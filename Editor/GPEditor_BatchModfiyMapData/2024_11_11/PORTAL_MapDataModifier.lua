
local MapDataModifierBase = require("Editor.GPEditor_BatchModfiyMapData.MapDataModifierBase")

local PORTAL_MapDataModifier = DefineClass("PORTAL_MapDataModifier", MapDataModifierBase)

PORTAL_MapDataModifier.Class2ActorType = {
	EP_PlanePortal = { EWActorType.PLANE_PORTAL, "EP_PlanePortalV2", 1 },
	EP_InteractivePortal = { EWActorType.INTERACTIVE_PORTAL, "EP_InteractivePortalV2", 1 }, -- DoorIn 2
	EP_InteractivePortal1 = { EWActorType.INTERACTIVE_PORTAL, "EP_InteractivePortalV2", 2 }, --DoorOut 3
}

--修改器初始
function PORTAL_MapDataModifier:Init()
    
end

--检查是否修改对应目标的场景物
--MapName Script\Data\Config\MapData 下的地图名称
-- return true 表示需要修改传递过来的场景物数据
function PORTAL_MapDataModifier:CanModify(EPActor, OldLuaData, MapName)
	return PORTAL_MapDataModifier.Class2ActorType[OldLuaData.Class] ~= nil
end

--执行修改
--修改方式为EPActor赋值, UE属性配置格式 例如: EPActor.ActorType = EWActorType.SCENEACTOR_MIN
function PORTAL_MapDataModifier:Modify(EPActor, OldLuaData, MapName)
	if OldLuaData.Class == "EP_PlanePortal" then
		if not next(OldLuaData.InteractorEvent.EventResponseGroup) then
			EPActor.SceneActorCommon.InitialState = 0
		else
			local UITemplateID = OldLuaData.EnterUITemplateID
			if UITemplateID == nil or UITemplateID == 0 then 
				UITemplateID = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_START.EventResponseList[1].ActionList[1].UITemplateID
			end
			EPActor.UITemplateID = UITemplateID

			local LeaveTemplateID = OldLuaData.LeaveUITemplateID
			if LeaveTemplateID == nil or LeaveTemplateID == 0 then
				LeaveTemplateID = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_START.EventResponseList[2].ActionList[1].UITemplateID
			end
			EPActor.LeaveTemplateID = LeaveTemplateID

			local PlaneID = OldLuaData.PlaneID
			if PlaneID == nil or PlaneID == 0 then
				PlaneID = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_SUCCESS.EventResponseList[1].ConditionList[1].PlaneID
			end
			EPActor.PlaneID = PlaneID

			EPActor.QuestID = OldLuaData.QuestID or 0
		end
	elseif OldLuaData.Class == "EP_InteractivePortal" or OldLuaData.Class == "EP_InteractivePortal1" then
		EPActor.SceneActorCommon.InsType = OldLuaData.Interactor.InsType.EnumValue
		EPActor.SceneActorCommon.BelongType = OldLuaData.Interactor.BelongType.EnumValue
		local WorldID = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_SUCCESS.EventResponseList[1].ActionList[1].TargetWorldID
		EPActor.WorldID = WorldID
		EPActor.RespawnPointID = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_SUCCESS.EventResponseList[1].ActionList[1].TargetRespawnPointID
		EPActor.bNotAdjustCamera = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_SUCCESS.EventResponseList[1].ActionList[1].bNotAdjustCamera or false
		EPActor.bStickGround = OldLuaData.InteractorEvent.EventResponseGroup.ON_INTERACT_SUCCESS.EventResponseList[1].ActionList[1].bStickGround or false
		-- 进门
		if OldLuaData.Class == "EP_InteractivePortal" then
			EPActor.TemplateID = 1
		-- 出门
		elseif OldLuaData.Class == "EP_InteractivePortal1" then
			EPActor.TemplateID = 2
		end
	else
		Log.ErrorFormat("PORTAL_MapData class error %s", OldLuaData.ID)
	end
	local ClassInfo = PORTAL_MapDataModifier.Class2ActorType[OldLuaData.Class]
	EPActor.ActorType = ClassInfo[1]
end

--保存前, 当前地图全部节点修改完毕后调用 做一些后处理
function PORTAL_MapDataModifier:PreSave(EPActor, MapName)

end

--保存通知, 当前场景物数据保存完毕后通知
function PORTAL_MapDataModifier:OnSave(EPActor, MapName)

end 

--全部处理完毕后通知一次
function PORTAL_MapDataModifier:OnSuccess()

end 

return PORTAL_MapDataModifier