---
--- Created by s<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
--- DateTime: 2025/6/12 16:38
---
local MapDataModifierBase = require("Editor.GPEditor_BatchModfiyMapData.MapDataModifierBase")

local BindViewController_MapDataModifier = DefineClass("BindViewController_MapDataModifier", MapDataModifierBase)

--修改器初始
function BindViewController_MapDataModifier:Init()

end

--检查是否修改对应目标的场景物
--MapName Script\Data\Config\MapData 下的地图名称
-- return true 表示需要修改传递过来的场景物数据
function BindViewController_MapDataModifier:CanModify(EPActor, OldLuaData, MapName)
    print("BindViewController_MapDataModifier:CanModify", EPActor.ID, OldLuaData.Class)
    return OldLuaData.Class == "EP_BindViewController" and (#OldLuaData.BindActors > 0)
end

--执行修改
--修改方式为EPActor赋值, UE属性配置格式 例如: EPActor.ActorType = EWActorType.SCENEACTOR_MIN
function BindViewController_MapDataModifier:Modify(EPActor, OldLuaData, MapName)
    print("BindViewController_MapDataModifier:Modify", EPActor.ID)
end

--保存前, 当前地图全部节点修改完毕后调用 做一些后处理
function BindViewController_MapDataModifier:PreSave(EPActor, MapName)
    print("BindViewController_MapDataModifier:PreSave", EPActor.ID)
end

--保存通知, 当前场景物数据保存完毕后通知
function BindViewController_MapDataModifier:OnSave(EPActor, MappName)
    print("BindViewController_MapDataModifier:OnSave", EPActor.ID)
end

return BindViewController_MapDataModifier
