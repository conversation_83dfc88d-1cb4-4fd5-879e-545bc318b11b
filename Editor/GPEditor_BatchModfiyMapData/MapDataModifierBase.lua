MapDataModifierBase = DefineClass("MapDataModifierBase")

--修改器初始
function MapDataModifierBase:Init()
    
end 

--检查是否修改对应目标的场景物
--MapName Script\Data\Config\MapData 下的地图名称
-- return true 表示需要修改传递过来的场景物数据
function MapDataModifierBase:CanModify(EPActor, OldLuaData, MapName)
    return false
end

--执行修改
--修改方式为EPActor赋值, UE属性配置格式 例如: EPActor.ActorType = EWActorType.SCENEACTOR_MIN
function MapDataModifierBase:Modify(EPActor, OldLuaData, MapName)
    
end

--保存前, 当前地图全部节点修改完毕后调用 做一些后处理
function MapDataModifierBase:PreSave(EPActor, MapName)
    
end

--保存通知, 当前场景物数据保存完毕后通知
function MapDataModifierBase:OnSave(EPActor, MapName)
    
end 

--全部处理完毕后通知一次(目前每张图已经有UI提示了, 暂不通知)
function MapDataModifierBase:OnSuccess()
    
end 

return MapDataModifierBase