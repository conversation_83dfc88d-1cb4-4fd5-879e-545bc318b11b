local MapDataTools= DefineClass("MapDataTools")

function MapDataTools:ctor()
    self:Reset()
end 

function MapDataTools:Reset()
    self.ModifierScriptList = {}
    self.EPActorRefOldLuaData = {}
end

function MapDataTools:OnLoadModifierScript(Path, Out_BatchModifyOverrideClassMap)
    if Path == nil then
        --Path = "Editor.GPEditor_BatchModfiyMapData.2024_11_11.BIND_VIEW_CONTROLLER_MapDataModifier"

        --Out_BatchModifyOverrideClassMap:Add("EP_BindViewController", "EP_BindViewControllerV2")
    end

    if Path then
        local ModifierIns = require(Path).new()
        if ModifierIns then
            table.insert(self.ModifierScriptList, ModifierIns)
        end
    end
end

function MapDataTools:OnExecute_ModifierScript(EPActor, OldLuaData, MapName)
    local bModify = false
    for i, ModifierIns in ipairs(self.ModifierScriptList) do
        if ModifierIns:CanModify(EPActor, OldLuaData, MapName) then
            ModifierIns:Modify(EPActor, OldLuaData, MapName)
            bModify = true

            self.EPActorRefOldLuaData[EPActor] = OldLuaData
        end
    end
    return bModify
end

function MapDataTools:OnPreSave_ModifierScript(EPActor, MapName)
    for i, ModifierIns in ipairs(self.ModifierScriptList) do
        local OldLuaData = self.EPActorRefOldLuaData[EPActor]
        if OldLuaData then
            if ModifierIns:CanModify(EPActor, OldLuaData, MapName) then
                ModifierIns:PreSave(EPActor, MapName)
            end
        end
    end
end

function MapDataTools:OnSave_ModifierScript(EPActor, MapName)
    for i, ModifierIns in ipairs(self.ModifierScriptList) do
        local OldLuaData = self.EPActorRefOldLuaData[EPActor]
        if OldLuaData then
            if ModifierIns:CanModify(EPActor, OldLuaData, MapName) then
                ModifierIns:OnSave(EPActor, MapName)
            end
        end
    end
end

function MapDataTools:OnSuccess()
    for i, ModifierIns in ipairs(self.ModifierScriptList) do
        ModifierIns:OnSuccess()
    end
end

return MapDataTools