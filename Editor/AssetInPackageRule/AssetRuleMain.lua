--  此脚本处理额外逻辑的引用入包
-- 	正常资源被策划配表
--  或者白名单
--  或者会被引用入包
--  对于一些特定字符串拼接 或者 配白名单比较难生效的方法提供一种自定义拼接字符串的入包方式，由具体的业务同学添加

local utils = require('Editor.AssetInPackageRule.Utils')

local AssetRuleMain = {}

OutputFileName =  "Content/Script/Editor/AssetInPackageRule/Output/AssetRules.lua"

function AssetRuleMain:Inner_OnStart()
	print ("--------------AssetRuleMain.Inner_OnStart------------")

	-- 初始化环境
    Game = {}
    Game.GameInstance = self
    Game.WorldContext = self:GetOwner()
    _G.ContextObject = Game.WorldContext

	require("Editor.EditorLuaEnvironment")
	InitEditorCommonManagers(Game, _G.ContextObject)

	totalResult = {}

	-- 将数据存入数组
	table.insert(totalResult, {name = "Map", value = require('Editor.AssetInPackageRule.MapInPackageRule').main()})
	table.insert(totalResult, {name = "SceneActor", value = require('Editor.AssetInPackageRule.SceneActorInPackageRule').main()})
	table.insert(totalResult, {name = "UI", value = require('Editor.AssetInPackageRule.UIInPackageRule').main()})
	table.insert(totalResult, {name = "Misc", value = require('Editor.AssetInPackageRule.MiscInPackageRule').main()})

	-- 直接按照插入顺序保存
	saveFilePath = import("KismetSystemLibrary").GetProjectDirectory() .. OutputFileName
	utils.saveListTableToConstLua(totalResult, saveFilePath)
end


function AssetRuleMain:Inner_OnShutdown()
	print ("--------------AssetRuleMain.Inner_OnShutdown------------")
end

return Class(nil, nil, AssetRuleMain)