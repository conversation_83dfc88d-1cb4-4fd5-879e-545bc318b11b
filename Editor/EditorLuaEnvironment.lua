_G.bUseKsbc = false
_G.GlobalLock = function() end
_G.GlobalUnlock = function() end
_G.NoCacheUI = true
_G.InvalidationBox = false

LuaCommonPrefix = LuaCommonPrefix or "Framework.Utils.LuaCommon."
language = "zhs"
unpack = table.unpack

_script = {}
_script.genIDCount = 0
_script.genIdForPureClientEntity = function ()
	_script.genIDCount = _script.genIDCount + 1
	return _script.genIDCount
end

function StringValid(Str)
	return Str and type(Str) == "string" and string.len(Str) > 0
end

slua.EditorLoadObject = slua.loadObject
slua.EditorLoadClass = slua.loadClass
slua.EditorLoadUI = slua.loadUI

require("Framework.DoraSDK.DefineClass")
require("Framework.C7Common.CommonDefine")
require("Framework.C7Common.C7Log")

function InitFakeEnvironment(Game)
	-- 假的SDK
	Game.FakeGameSDKClass = {
		__index = {
			setObject = function()
			end,
			enableAsGlobal = function()
			end,
		}
	}
	Game.GameSDK = {entityManager = {entities = {}}}
	setmetatable(Game.GameSDK, Game.FakeGameSDKClass)

	-- 假的UI管理器
	Game.FakeUIManagerClass = {
		__index = {
			GetInstance = function(V)
				return V.Instance
			end,
			OnIdle = function()
			end,
		}
	}

	-- 假的副本系统
	local FakeDungeonSystem = {
		__index = {
			IsInDungeon = function()
				return false
			end,
		}
	}
	Game.DungeonSystem = {}
	setmetatable(Game.DungeonSystem, FakeDungeonSystem)

	-- 假的设置管理器
	local FakeSettingsManager = {
		__index = {
			GetIniData = function()
				return 100
			end,
		}
	}
	Game.SettingsManager = {}
	Game.SettingsManager.QualityLevel = {
		low = 0,
		medium = 1,
		high = 2,
		epic = 3,
		cinematic = 4
	}
	setmetatable(Game.SettingsManager, FakeSettingsManager)

	-- 假的关卡管理器
	local FakeLevelManager = {
		__index = {
			GetCurrentLevelID = function()
				return 0
			end,

			GetNPCSpawnersInCurrentLevel = function()
				return {}
			end
		}
	}
	Game.LevelManager = {}
	setmetatable(Game.LevelManager, FakeLevelManager)

end




-- 这里有一个很trick的依赖
-- Fake环境--->先TableData --> 再GamePlay
InitFakeEnvironment(Game)

-- require("Shared.C7")
require("Data.Excel.SingleExcelEnum")
require("Shared.LogicEnum")
require("Framework.Library.Math3D")
require("Framework.Library.TableExtend")
require("Framework.Utils.StringExtend")
require("Framework.Utils.utf8")
require("Framework.Utils.PlatformUtil")
require("Framework.Utils.Functions")
require("Framework.Utils.GlobalFunctions")
require("Framework.Utils.LuaFunctionLibrary")
require("Framework.Utils.LuaCommon.Utils.Logger")
require("Framework.Utils.LuaCommon.Utils.DataStruct.Set")
require("Framework.Library.cache")
require("Framework.Manager.ManagerBase")
require("Framework.SystemBase.SystemBase")
require("Framework.SystemBase.SystemModelBase")
require("Framework.SystemBase.SystemSenderBase")
require("Framework.Utils.LuaCommon.Utils.CommonUtils")
require("Gameplay.CommonDefines.Enum")
Game.TableData = require("Data.Excel.TableData")
Game.TableDataManager = require("Framework.Utils.LuaCommon.Managers.TableDataManager").new()
require("Data.Excel.ExcelEnum")

-- 奇怪的Enum, Framework下有import 依赖
require"Framework.KGFramework.Require"


require("Framework.Ksbc.KsbcReader")
require("Framework.Library.time")
require("Shared.Const")
require("Shared.Utils")
require("Gameplay.3C.ClientCharacterHelper")
require("Gameplay.Combat.Comps.ClientTimelineComponent")
require("Shared.WorldActorDefine")
require("Gameplay.Combat.CombatEffect.CombatEffectUtils")
require("Framework.Entity.EntityBase")
require("Gameplay.NetEntities.LocalEntity.LocalAttachItem")
require("Gameplay.NetEntities.LocalEntity.LocalWeaponItem")
kg_require("Gameplay.NetEntities.LocalEntity.LocalEntityBase")
kg_require("Gameplay.NetEntities.LocalEntity.LocalDisplayChar")
kg_require("Gameplay.NetEntities.LocalEntity.DialogueLocalEntity")
kg_require("Gameplay.NetEntities.LocalEntity.LocalCutSceneActor")
kg_require("Gameplay.NetEntities.LocalEntity.PreCreateCharacterEntity.BSAEPreview")
kg_require("Gameplay.NetEntities.LocalEntity.PreCreateCharacterEntity.CutSceneEntityInEditor")

function InitEditorSimplestEnvironment(Game, ContextObject)
	Game.TimeUtils = _G.TimeUtils
	Game.TimerManager = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerManager").new()
	-- ExcelEnum里依赖TableDataManager
	-- Game.Logger = Logger.new("Game")
	Game.EntityManager = require("Framework.Entity.EntityManager")
	Game.TableDataManager = require("Framework.Utils.LuaCommon.Managers.TableDataManager").new()
	Game.ProfilerInstrumentation = require("Tools.ProfilerInstrumentation.ProfilerInstrumentation").new()
	Game.bit = require("Framework.Utils.bit")
	Game.PlatformScalabilitySettings = Game.PlatformScalabilitySettings or kg_require("Gameplay.CommonDefines.PlatformScalabilitySettings")
end



function InitEditorCommonManagers(Game, WorldContext, NeedWaterWind)
	
	InitEditorSimplestEnvironment(Game, WorldContext)
	
	Game.GPManagers = {}
	Game.EventSystem = require("Framework.EventSystem.EventSystem").new()
	table.insert(Game.GPManagers, Game.EventSystem)
	Game.GlobalEventSystem = Game.GlobalEventSystem or kg_require("Framework.EventSystem.V2.EventSystemV2").new("GlobalEventSystem")
	table.insert(Game.GPManagers, Game.GlobalEventSystem)
	Game.UniqEventSystemMgr = Game.UniqEventSystemMgr or kg_require("Framework.EventSystem.V2.UniqEventSystemMgr").new()
	Game.FormulaManager = require("Gameplay.LogicSystem.Formula.FormulaManager")
	table.insert(Game.GPManagers, Game.FormulaManager)
	
	-- 开始C++管理器的创建
	Game.ObjectActorManager = require("Framework.C7Common.ObjectActorManager").new()
	table.insert(Game.GPManagers, Game.ObjectActorManager)

    Game.ObjectPoolManager = require("Framework.C7Common.ObjectPoolManager").new()
	table.insert(Game.GPManagers, Game.ObjectPoolManager)
    
	Game.AssetManager = require("Framework.C7Common.AssetManager").new()
	table.insert(Game.GPManagers, Game.AssetManager)

	Game.CppAssetManager = require("Framework.C7Common.CppAssetManager").new()
	table.insert(Game.GPManagers, Game.CppAssetManager)

	-- 工作代理管理器
	--Game.WorkProxyManager = import("WorkProxyManager")(WorldContext)
	--Game.GameInstance:CacheManager(Game.WorkProxyManager)
	--table.insert(Game.GPManagers, Game.WorkProxyManager)

	-- 材质管理器
	Game.MaterialManager = require("Gameplay.Managers.MaterialManager").new()
	table.insert(Game.GPManagers, Game.MaterialManager)

	-- UEActor管理器
	---@type UEActorManager
	Game.UEActorManager = require("Framework.C7Common.UEActorManager").new()
	table.insert(Game.GPManagers, Game.UEActorManager)

	-- 战斗管理器
	---@type BSManager
	Game.BSManager = require("Gameplay.BattleSystem.S_Manager").new()
	table.insert(Game.GPManagers, Game.BSManager)

	-- 创建真实的WorldManager
	Game.WorldManager = require("Gameplay.WorldManager.WorldManager").new()
	-- 替换技能编辑器中调用但不需要的方法
	Game.WorldManager.SetClipFilter = function() return 0 end
	Game.WorldManager.RegisterLODSignActor = function() return 0 end
	Game.WorldManager.UnRegisterLODSignActor = function() return end
	Game.WorldManager.ChangeEntityAoiLevel = function() return end
	Game.WorldManager.GetEntityCategoryHiddenState = function() return false end
	Game.WorldManager.InitEditorCommonManagers = function() return true end
	Game.WorldManager.HandleWorldCleanup = function() end
	-- 编辑器下world都是准备好的
	Game.WorldManager.IsWorldMapLoadComplete = function() return true end
	-- 之后若有报错可在上方添加fake func替代
	table.insert(Game.GPManagers, Game.WorldManager)

	Game.WorldDataManager = Game.WorldDataManager or require("Gameplay.WorldManager.WorldDataManager").new()
	table.insert(Game.GPManagers, Game.WorldDataManager)

	-- 效果管理器
	Game.EffectManager = require("Gameplay.Managers.EffectManager").new()
	table.insert(Game.GPManagers, Game.EffectManager)
	Game.EffectManager:DisableQualityLevelOffsetAndTransparencyScale()
    Game.EffectManager:SetEnablePriorityCulling(false)
    Game.EffectManager:SetIsInEditor(true)

	-- 战斗QTE管理器
	Game.BSQTEManager = Game.BSQTEManager or require("Gameplay.BattleSystem.S_QTEManager")
	table.insert(Game.GPManagers, Game.BSQTEManager)
	-- 结束C++管理器的创建

	-- 音频管理器
	Game.AkAudioManager = require("Gameplay.Managers.AkAudioManager")
	table.insert(Game.GPManagers, Game.AkAudioManager)
	-- 角色组装管理器相关
	Game.RoleCompositeMgr =  Game.RoleCompositeMgr or kg_require("Gameplay.3C.RoleComposite.RoleCompositeManager").new()
	table.insert(Game.GPManagers, Game.RoleCompositeMgr)
	
	Game.ActorAppearanceManager = Game.ActorAppearanceManager or kg_require("Gameplay.Managers.ActorAppearanceManager.ActorAppearanceManager").new()
	table.insert(Game.GPManagers, Game.ActorAppearanceManager)
	
	Game.CustomRoleSystem = require("Gameplay.LogicSystem.CustomRole.CustomRoleSystem").new()
	Game.OpenPanelCheckSystem = require("Gameplay.LogicSystem.OpenPanelCheck.OpenPanelCheckSystem").new()

	Game.PostProcessManager = import("PPManager")(WorldContext)
	Game.GameInstance:CacheManager(Game.PostProcessManager)
	table.insert(Game.GPManagers, Game.PostProcessManager)
	
	-- =============================UI相关============================================
	-- UIManager有Import依賴, 先进行NewUIManager处理
	Game.NewUIManager = kg_require("Framework.KGFramework.KGUI.Core.UIManager").new()
	table.insert(Game.GPManagers, Game.NewUIManager)
	
	_G.UIManager = nil
	require("Framework.UI.UIManager")
	Game.UIManager = UIManager:GetInstance()
	table.insert(Game.GPManagers, Game.UIManager)

	
	Game.UIConfig = Game.UIConfig or require("Data.Config.UI.UIConfig")

	Game.StatAtlasSystem = Game.StatAtlasSystem or require("Gameplay.StatAtlasSystem.StatAtlasSystem").new()
	table.insert(Game.GPManagers, Game.StatAtlasSystem)

	Game.ColorManager = Game.ColorManager or require("Gameplay.UI.ColorTable.ColorManager").new()
	table.insert(Game.GPManagers, Game.ColorManager)

	Game.CharacterLightManager = require("Gameplay.3C.CharacterLightManager").new()
	table.insert(Game.GPManagers, Game.CharacterLightManager)

	-- 战斗数据管理器
	Game.CombatDataManager = require("Gameplay.BattleSystem.CombatDataManager").new()
	table.insert(Game.GPManagers, Game.CombatDataManager)


	Game.DialogueManager = kg_require("Gameplay.DialogueSystem.DialogueManager").new()
	table.insert(Game.GPManagers, Game.DialogueManager)

	Game.SequenceManager = kg_require("Gameplay.CinematicSystem.SequenceManager").new()
	table.insert(Game.GPManagers, Game.SequenceManager)
	
    Game.DialogueManagerV2 = kg_require("Gameplay.DialogueV2.DialogueManagerV2").new()
    table.insert(Game.GPManagers, Game.DialogueManagerV2)

	Game.NPCManager = Game.NPCManager or require("Gameplay.LogicSystem.NPC.System.NPCManager").new()
	table.insert(Game.GPManagers, Game.NPCManager)

	Game.HUDInteractManager = Game.HUDInteractManager or
        kg_require("Gameplay.LogicSystem.HUD.HUD_Interact.HUDInteractManager").new()
    table.insert(Game.GPManagers, Game.HUDInteractManager)

	--设置预览风场水场(在ManangerInit之前),注意有些编辑的World是个空的，没有水体组件
	if NeedWaterWind then
		Game.WorldManager:SetNeedWaterWind(true)
	else
		Game.WorldManager:SetNeedWaterWind(false)
	end
	
	-- 初始化管理器
	local Size = #Game.GPManagers
	for i = 1, Size do
		local Manager = Game.GPManagers[i]
		if (Manager ~= nil) and (Manager.Init ~= nil) then
			Manager:Init()
		end
	end
	--Game.WorkProxyManager.bIsInEditor = true
	Game.BSManager.bIsInEditor = true
	Game.CombatDataManager.bIsInEditor = true

	--设置预览风场水场(在ManangerInit之前),注意有些编辑的World是个空的，没有水体组件
	if NeedWaterWind then
		local WorldID = Game.ObjectActorManager:GetIDByObject(WorldContext)
		Game.WorldManager:SetEditorPreviewWorld(WorldID)
	end

end

