
local AvatarCheckRules = {

    --------------------------- Character Check ---------------------------
    ["CharacterRules"] = {
        --- CharacterClass勾上后，对应赋值不能为空
        [1] = {
            bEnable = 1, -- 是否生效
            rule = [[(not Data:IsMemberOverride("CharacterClass")) or (not Data.CharacterClass:IsNull())]], -- 配置规则
            msg = [[CharacterClass勾上后，对应赋值为空]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            OutRepairParams = {"RepairObject.OverrideNames:Remove('CharacterClass')", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- BoneLinkInfo勾上后，数组不能为空，也不能和模板设置一样
        [2] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck2(Data, Rule, OutRepairParams)]], -- 配置规则
            msg = [[BoneLinkInfo勾上后，数组不能为空，也不能和模板设置一样]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = true, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            OutRepairParams = {"RepairObject.OverrideNames:Remove('BoneLinkInfo')", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- Material Overlay勾上后，对应赋值不能为空
        [3] = {
            bEnable = 1, -- 是否生效
            rule = [[(not Data:IsMemberOverride("MaterialOverlay")) or (#Data.MaterialOverlay:ToString() ~= 0)]], -- 配置规则
            msg = [[Material Overlay勾上后，对应赋值为空]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            -- OutRepairParams = {"RepairObject.OverrideNames:Remove('MaterialOverlay')", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- MeshType设置类型后，对应赋值不能为空
        [4] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck4(Data, Rule)]], -- 配置规则
            msg = [[MeshType设置类型后，对应赋值不能为空]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = true, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            -- OutRepairParams = {"(RepairObject.MeshType = EAvatarModelMeshType.Unset)", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- AnimLib勾上后，对应赋值不能为空，如果和模版设置一样也报错
        [5] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck5(Data, Rule)]], -- 配置规则
            msg = [[MAnimLib勾上后，对应赋值不能为空，如果和模版设置一样也报错]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = true, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            -- OutRepairParams = {"RepairObject.OverrideNames:Remove('AnimLibAsset')", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- Anim Data勾上后，对应赋值不能为空，Anim Layers Class 数组不能有空引用，如果和模版设置一样也报错
        [6] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck6(Data, Rule)]], -- 配置规则
            msg = [[Anim Data勾上后，对应赋值不能为空，Anim Layers Class 数组不能有空引用，如果和模版设置一样也报错]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = true, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            OutRepairParams = {"RepairObject.OverrideNames:Remove('AnimData')", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- CharacterClass设置后，MeshType必须是Unset
        [7] = {
            bEnable = 0, -- 是否生效
            rule = [[Checker:CharacterCheck7(Data, Rule)]], -- 配置规则
            msg = [[CharacterClass设置后，MeshType必须是Unset]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            -- OutRepairParams = {"(RepairObject.MeshType = EAvatarModelMeshType.Unset)", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- MaterialOverlay设置后, MeshType必须是SkeletalMesh
        [8] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck8(Data, Rule)]], -- 配置规则
            msg = [[MaterialOverlay设置后, MeshType必须是SkeletalMesh]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            -- OutRepairParams = {"(RepairObject.MeshType = EAvatarModelMeshType.SkeletalMesh)", ""}, -- 修复参数，key 为修复的配置规则
        },
        --- Effect勾上后，数组不能为空，且里面NS Effect的设置不能为空
        [9] = {
            bEnable = 1, -- 是否生效
            rule = [[Checker:CharacterCheck9(Data, Rule)]], -- 配置规则
            msg = [[Effect勾上后，数组不能为空，且里面NS Effect的设置不能为空]], -- 报错字段
            msgParams = {}, -- 报错字段format 参数名,
            bCustomMsg = true, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
            OutRepairParams = {"RepairObject.OverrideNames:Remove('Effect')", ""}, -- 修复参数，key 为修复的配置规则
        },
		--- 配置了SkeletalMesh后，AnimLibAsset AnimData需要配置
		[10] = {
			bEnable = 0, -- 是否生效
			rule = [[Checker:CharacterCheck10(Data, Rule)]], -- 配置规则
			msg = [[配置了SkeletalMesh后，AnimLibAsset AnimData需要配置]], -- 报错字段
			msgParams = {}, -- 报错字段format 参数名,
			bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
		},
		--- Avatar Preset，Skeletal Mesh，Static Mesh为空时或者只配置了StaticMesh，下面的AnimLibAsset AnimData需要勾选覆盖后将配置置空
		[11] = {
			bEnable = 0, -- 是否生效
			rule = [[Checker:CharacterCheck11(Data, Rule)]], -- 配置规则
			msg = [[Avatar Preset，Skeletal Mesh，Static Mesh为空时或者只配置了StaticMesh，下面的AnimLibAsset AnimData需要勾选覆盖后将配置置空]], -- 报错字段
			msgParams = {}, -- 报错字段format 参数名,
			bCustomMsg = false, -- 是否需要在定制函数内处理报错数据拼装和上传, 如果是配置函数, 一般不需要, 大多数定制函数也不需要, 只有少部分定制函数有这类需求
		},
    },

    -------------------------- Weapon Check -----------------------------
    ["WeaponRules"] = {
    },


    ------------------------- AttachItem Check --------------------------------
    ["AttachItemRules"] = {
    },

}


return AvatarCheckRules