---
--- Created by he<PERSON><PERSON>.
--- DateTime: 2025/3/10
---

require("Framework.Ksbc.KsbcReader")
require("Framework.DoraSDK.DefineClass")
require("Framework.Utils.LuaFunctionLibrary")
require("Framework.Utils.StringExtend")
require("Framework.Library.Math3D")

local Json = require "Framework.Library.json"
local AvatarChecker = require "Editor.ResourceCheck.Checker.AvatarChecker"

local ResourceCheckLuaObj = {}

function ResourceCheckLuaObj:Inner_OnStart()
	-- refer to StarMisteryEditor:StartGameLua
    Game = {}

    _G.bUseKsbc = false
    Game.TableData = require("Data.Excel.TableData")
    
    require("Framework.C7Common.CommonDefine")
    require("Framework.C7Common.C7Log")
    _G.GlobalLock = function() end
    _G.GlobalUnlock = function() end
    require("Framework.DoraSDK.DefineClass")
    -- require("Shared.C7")
    require("Framework.Utils.LuaCommon.Utils.DataStruct.Set")
    LuaCommonPrefix = LuaCommonPrefix or "Framework.Utils.LuaCommon."
    Game.TableDataManager = require("Framework.Utils.LuaCommon.Managers.TableDataManager").new()
    require("Data.Excel.ExcelEnum")
    require("Data.Excel.SingleExcelEnum")
    require("Shared.LogicEnum")
    require("Framework.Library.Math3D")
    require("Framework.Library.TableExtend")
    unpack = table.unpack
    require("Framework.Utils.StringExtend")
    require("Framework.Utils.utf8")
    require("Framework.Utils.PlatformUtil")
    require("Framework.Utils.Functions")
    require("Framework.Utils.GlobalFunctions")
    require("Framework.Utils.LuaFunctionLibrary")
    require("Framework.Utils.LuaCommon.Utils.Logger")
    require("Framework.Library.cache")
    require("Framework.Library.time")
    require("Framework.Manager.ManagerBase")
    require("Framework.SystemBase.SystemBase")
    require("Framework.SystemBase.SystemModelBase")
    require("Framework.SystemBase.SystemSenderBase")


    language = "zhs"
    Game.TimerManager = kg_require("Framework.KGFramework.KGCore.TimerManager.TimerManager").new()
    
    -- Game.Logger = Logger.new("Game")

	Log.Debug("[ResourceCheckLuaObj] Inner_OnStart End.")
	
	-- package.cpath = package.cpath .. ';C:/Users/<USER>/AppData/Roaming/JetBrains/Rider2023.2/plugins/EmmyLua/debugger/emmy/windows/x64/?.dll'
	-- local dbg = require('emmy_core')
	-- dbg.tcpListen('localhost', 9966)
end

function ResourceCheckLuaObj:DoDataCheck(CheckObjects)
	Log.Debug("[ResourceCheckLuaObj] DataCheck Start.")

	local OutRepairParams = slua.Map(import("EPropertyClass").Str, import("EPropertyClass").Str)

	-- unreal class
	local UAvatarModelAsset = import("AvatarModelAsset")
	local UAvatarWeaponModelAsset = import("AvatarWeaponModelAsset")

	local ObjectsTable = CheckObjects:ToTable()
	for j = 1, #ObjectsTable do
		local Object = ObjectsTable[j]
		-- 根据类型判断对每个 Object 使用哪个 Checker 去检查
		if Object:IsA(UAvatarModelAsset) then
			return AvatarChecker:DoCheck(CheckObjects, nil, nil, OutRepairParams)
		end
	end
end

function ResourceCheckLuaObj:DoDataRepair(RepairObject, Arguments)
	Log.Debug("[ResourceCheckLuaObj] DataRepair Start.")

	-- unreal class
	local UAvatarModelAsset = import("AvatarModelAsset")
	local UAvatarWeaponModelAsset = import("AvatarWeaponModelAsset")

	-- 根据类型判断对每个 Object 使用哪个 Checker 去修复
	if RepairObject:IsA(UAvatarModelAsset) then
		return AvatarChecker:DoRepair(RepairObject, Arguments)
	end

	return false
end


return Class(nil, nil, ResourceCheckLuaObj)