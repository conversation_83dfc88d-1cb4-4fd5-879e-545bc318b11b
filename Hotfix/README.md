

## 规范

所有客户端Hotfix文件置于本目录下，每次修改创建一个新的文件，文件名统一为`hotfix_{P4账号名}_{Redmine单号}.lua`（不包含{}）。

**Hotfix文件需要保证在重复执行的场景下，逻辑依然正常**。若要求仅执行一次，则需要在实现时增加相应的标记和判断。

## 自测

完成Hotfix文件的编写后，修改`manifest.json`文件，**注意该文件的修改不要提交**。

JSON文件的格式如下：

```json
[
    {
        "FileName": "hotfix_litianhao07_000001",
        "Version": 11,    // 可选参数：如果客户端patch版本号大于Version，该客户端hotfix将无法生效
        "EffectServers": ["c7_dev"] // 可选参数：hotfix生效的服务器，里面填namespace。为空或不填时代表在所有服务器生效
    },
    {
        "FileName": "hotfix_shenxudong05_000010"
    }
]
```

新编写的hotfix文件按上述格式，在数组的末尾增加一项。

windows本地执行hotfix：

```shell
# Server/shell/win
./reload.bat "client_hotfix"
```

linux本地执行Hotfix：

```shell
# Server/shell/linux
./reload.sh "client_hotfix"
```

## 客户端hotfix编写规范

#### 普通代码Hotfix模板

```lua
-- 直接编写需要执行的hotfix代码就行
```

#### 网络相关Component Hotfix模板

>  由于Entity继承Component的时候，会平铺开Component里的Function，所以Hotfix的时候不能直接替换Component的Function,需要调用 HotfixUtils.NetComponentHook 进行替换

```lua
-------------要修复的Component名字和方法名---------------
local componentName = "WorldBossComponent"
local funcName  = "OnMsgWorldBossDamageStatisticsOpen" 

Game.HotfixUtils.HotfixComponentFunction(componentName,funcName,function(self,activityId, statistics)
    print("TestOnMsgWorldBossDamageStatisticsOpen")
end)
```

```lua
-------------要修复的Component名字和方法名,__component_EnterWorld__类型方法需要填写全名---------------
local componentName = "BuffComponentNew"
local funcName = "__component_EnterWorld__"
Game.HotfixUtils.HotfixComponentFunction(componentName,funcName,function(self)
    print("==============11111111111111111")
end)
```

#### WebHotfix模板

>  登录服务器流程前出现bug的话，无法走Hotfix，需要走WebHotfix

```lua
-- webhotfix init

--自定义内容，webhotfix代码获取到后会立即执行
print("wanghuihui_webhotfix")

--固定函数名，请勿修改。webhotfix阶段完毕后会立即执行
function _OnLuaStarted_()
    print("_OnLuaStarted_ webhotfix")
end

--固定函数名，请勿修改。patch更新完毕后会立即执行
function _OnPatched_()
    print("_OnPatched_ webhotfix")
end

--固定函数名，请勿修改。游戏逻辑初始化(Main函数执行)完毕后会立即执行
function _OnAllManagerInited_()
    print("_OnAllManagerInited_ webhotfix")
end
```

#### LuaOverrider Hotfix

```lua
--以GameInstance.lua举例

local GameInstance = require("Gameplay.Launch.GameInstance") --必须使用require

--如果要hotfix的lua脚本已经被实例化绑定过，需要找到对应的UObject对象（或者UObject绑定的luatable对象），清理Function缓存。否则hotfix的函数不会生效
--反之不需要这两步
slua.luaOverriderRemoveFuncCache(Game.GameInstance,"OnLuaShowGM")
rawset(Game.GameInstance,"OnLuaShowGM", nil) --需要把table上的缓存也清了，否则无法在lua侧调用

Game.HotfixUtils.LuaOverriderHotfix(GameInstance,"OnLuaShowGM",function(self)
    print("111")
end) --调用这个接口替换方法Game.HotfixUtils.LuaOverriderHotfix
```

#### ksbc数据hotfix

```lua
-- 以LevelMapData为例

local levelMapData = Game.TableData.GetLevelMapDataRow(5209996)
if levelMapData then
    levelMapData.LevelName = "{TestName}"
end
```

表格hotfix可以用工具生成，工具使用文档见 https://docs.corp.kuaishou.com/k/home/<USER>/fcACHzcQELpfkQUM-0Nm3ewYF
