local result = Game.ActorAppearanceManager.AvatarModelLib

local startTime = os.time()

local ModelLibData = nil
local mt = getmetatable(result)
local indexFunc = mt.__index
for i = 1, math.huge do
    local name, val = debug.getupvalue(indexFunc, i)
    if not name then break end
    if name == "Data" then
		ModelLibData = val
        break
    end
end

---@type table<string, table<string, AvatarPartData>>
local AvatarModelPartLib = kg_require("Data.Config.Model.AvatarModelPartLib")

for _, model in pairs(ModelLibData) do
	if model.AvatarPreset then
		local presetData = model.AvatarPreset
		for _, partData in pairs(presetData.BodyPartList) do
			if partData.Type == 1 then
				presetData.MakeupData = AvatarModelPartLib.MakeupData[partData.MakeupID]
			end
		end
	end
end

local endTime = os.time()
Log.Debug("[AvatarModelLib]", "hotfix time: " .. endTime - startTime)