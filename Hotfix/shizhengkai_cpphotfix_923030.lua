-- UAnimNotifyState_C7MotionWarping::NotifyBegin
-- 引入必备的库
require "Framework.C7Common.C7Log"
require "Framework.Library.CppReflection"

-- 类名，固定是cpp_XXX
local hotfixClassName = "cpp_UAnimNotifyState_C7MotionWarping"
-- 函数名，固定是%InjectLable%_BEFORE
local injectFuncName = "NotifyBegin_USkeletalMeshComponentPTR_UAnimSequenceBasePTR_float_FAnimNotifyEventReferenceCREF" .. "_" .. "BEFORE"
-- 删掉上次注入的
slua.removeFunctionHotfix(hotfixClassName, injectFuncName)
-- 注入
-- 函数参数是self+原函数参数
slua.addFunctionHotfix(hotfixClassName, injectFuncName, function(this, MeshComp, Animation, TotalDuration, EventReference)
    print("szk hotfix test, Success Enter!")
    if this.bUsingLocoInput == true and this.LocoInputQueryTime <= 0.0 then
        this.LocoInputQueryTime = 0.05
        print("szk hotfix test, Help Avoid a Crash 1!")
        return false
    end

    local AnimInst = MeshComp:GetAnimInstance()
    if AnimInst == nil then
        return true
    end

    local Character = AnimInst:TryGetPawnOwner()
    if Character == nil then
        return true
    end

    local RoleMoveComp = Character:GetMovementComponent()
    if RoleMoveComp == nil then
        return true
    end

    local testToken = RoleMoveComp:ObtainMoveCorrector(2, 4)
    if testToken <= 0 then
        print("szk hotfix test, Help Avoid a Crash 2!")
        return true
    end

    RoleMoveComp:ReleaseMoveCorrector(testToken, 4)
    print("szk hotfix test, Success Leave!")
    return false
end)
