
local rpc_deliver = kg_require("Framework.SystemBase.RPCDeliver")

---@class SystemReceiverBase
---@field Methods table
---@field EntityMethods table
local SystemReceiverBase = DefineClass("SystemReceiverBase")

function SystemReceiverBase:ctor(system)
    self.system = system
    self:Init()
end

function SystemReceiverBase:Init()
    for methodName in pairs(self.Methods or {}) do
        rpc_deliver.BindMethod(self, methodName)
    end
    for methodName in pairs(self.EntityMethods or {}) do
        rpc_deliver.BindEntityMethod(self, methodName)
    end
end

function SystemReceiverBase:UnInit()
    for methodName in pairs(self.Methods or {}) do
        rpc_deliver.UnBindMethod(self, methodName)
    end
    for methodName in pairs(self.EntityMethods or {}) do
        rpc_deliver.UnBindEntityMethod(self, methodName)
    end
end

-- ---主角的Entity RPC函数用Methods
-- function SystemReceiverBase.Methods:TestA()

-- end

-- ---非主角的Entity RPC函数用EntityMethods
-- ---@param entityId number
-- function SystemReceiverBase.EntityMethods:TestB(entityId)

-- end