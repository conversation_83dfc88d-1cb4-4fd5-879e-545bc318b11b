local IListAnimationPlayer = DefineClass("IListAnimationPlayer")


function IListAnimationPlayer:PlayListAnimation(cellDatas, aniData, callback)
    
end

function IListAnimationPlayer:StopListAnimation(startOrend)
    
end

function IListAnimationPlayer:IsAnimationPlaying()
    
end

function IListAnimationPlayer:onRefreshItem(index, bSelected)
    
end

function IListAnimationPlayer:onSetCell(index)
    
end
return IListAnimationPlayer