local EventDefine = kg_require("Framework.EventSystem.V2.EventDefine")
---这里区分了EEventTypesV2与DefineEvents，是为了减少一层查询
---下面annotation一致，是为了能够直接使用点号提示
---@class DefineEvents
EEventTypesV2 = EEventTypesV2 or {}
---@class DefineEvents
DefineEvents = DefineEvents or EventDefine.Define("DefineEvents", EEventTypesV2)

---事件的定义都放在这下面
DefineEvents.TEST_HELLO_WORLD = ""
DefineEvents.QUEST_ON_QUEST_FINISHED = ""


DefineEvents.ON_SELF_BLOCK_VOICE_CHANGED = ""

DefineEvents.ROLE_ON_BORN = ""