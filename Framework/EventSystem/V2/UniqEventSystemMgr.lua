---uniq事件的管理者，设计这个目的是为了解决全局事件的M*N查询低效问题，同时又不想写过多的逻辑来管理
---每个uid都对应一个EventSystem
---@class UniqEventSystemMgr: LuaClass
local UniqEventSystemMgr = DefineSingletonClass("UniqEventSystemMgr")
local EventSystemV2 = kg_require("Framework.EventSystem.V2.EventSystemV2")

UniqEventSystemMgr.MaxCacheCnt = 100

function UniqEventSystemMgr:ctor()
	---@type table<number, EventSystemV2>
	self.uniqEventSystems = {}
	---@type table<number, EventSystemV2>
	self.cachedEventSystems = {}
	self.curCacheCnt = 0
end

function UniqEventSystemMgr:dtor()
	self:dumpLeakEvents()
	self.uniqEventSystems = nil
	self.cachedEventSystems = nil
end

function UniqEventSystemMgr:dumpLeakEvents()
	for _, eventSystem in pairs(self.uniqEventSystems) do
		eventSystem:delete()
	end
end

---@param eventSystem EventSystemV2
function UniqEventSystemMgr:returnToCachedEventSystems(eventSystem)
	if self.curCacheCnt > UniqEventSystemMgr.MaxCacheCnt then return end
	table.insert(self.cachedEventSystems, eventSystem)
	self.curCacheCnt = self.curCacheCnt + 1
end

---@private
function UniqEventSystemMgr:getFromCachedEventSystems(uid)
	local eventSystem = table.remove(self.cachedEventSystems)
	if eventSystem == nil then
		eventSystem = EventSystemV2.new(uid, false)
	else
		eventSystem:OnGetFromPool(uid)
		self.curCacheCnt = self.curCacheCnt - 1
	end
	return eventSystem
end

function UniqEventSystemMgr:AddListener(uid, eventType, callbackName, target)
	local entityEventSystem = self.uniqEventSystems[uid]
	if entityEventSystem == nil then
		entityEventSystem = self:getFromCachedEventSystems(uid)
		self.uniqEventSystems[uid] = entityEventSystem
	end
	entityEventSystem:AddListener(eventType, callbackName, target)
end

function UniqEventSystemMgr:RemoveListener(uid, eventType, callbackName, target)
	local entityEventSystem = self.uniqEventSystems[uid]
	if entityEventSystem ~= nil then
		entityEventSystem:RemoveListener(eventType, callbackName, target)
		if entityEventSystem:CheckEventsEmpty() then
			self:RemoveUniqEventSystem(uid)
		end
	end
end

---Unique的事件拥有者在自己销毁的时候，调用这个方法进行移除
function UniqEventSystemMgr:RemoveUniqEventSystem(uid)
	local entityEventSystem = self.uniqEventSystems[uid]
	if entityEventSystem ~= nil then
		self:returnToCachedEventSystems(entityEventSystem)
		self.uniqEventSystems[uid] = nil
	end
end

---移除某个Target上所有的监听
---注意！！！这个接口性能有问题，慎用，如果用的多，后续这里会提供一个target与eventSystem的映射表
---@protected
--function UniqEventSystemMgr:RemoveTargetAllListeners(target)
--	for uid, eventSystem in pairs(self.uniqEventSystems) do
--		eventSystem:RemoveTargetAllListeners(target)
--		if eventSystem:CheckEventsEmpty() then
--			self:RemoveUniqEventSystem(uid)
--		end
--	end
--end

function UniqEventSystemMgr:Publish(uid, eventType, ...)
	local entityEventSystem = self.uniqEventSystems[uid]
	if entityEventSystem ~= nil then
		entityEventSystem:Publish(eventType, ...)
	end
end

---亚哥说想要这个接口，发送给指定target以及全局
function UniqEventSystemMgr:PublishTargetAndGlobal(uid, eventType, ...)
	self:Publish(uid, eventType, ...)
	Game.GlobalEventSystem:Publish(eventType, ...)
end

return UniqEventSystemMgr
