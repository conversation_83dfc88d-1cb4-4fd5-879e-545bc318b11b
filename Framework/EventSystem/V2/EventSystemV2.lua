---V2版本的事件系统，将Dispatcher的逻辑直接耦合进来，不用再多一层调用
---同时，这里不再是singleton，如果其他系统需要使用该事件系统，也可以自己new一个
---注意：!!!这里针对同一个eventType的callback不进行保序!!!
---@class EventSystemV2: LuaClass
local EventSystemV2 = DefineClass("EventSystemV2")

require "Gameplay.CommonDefines.EventTypesV2"
local EventDefine = kg_require("Framework.EventSystem.V2.EventDefine")

-- 弱引用标记，主要是会对target存在引用，防止泄露，但增加了一层调用，不过效率差别不大
local mtWeakKeyTable = {   -- luacheck: ignore
	__mode = "k",
}

---占位的callback，主要为了解决gc问题
EventSystemV2.DummyCallback = -1
-- 最大派发次数，大于该值认为死循环
EventSystemV2.MAX_DISPATCH_COUNT = 20
---单个事件的最大的callback数量
EventSystemV2.MAX_CALLBACK_CNT_PER_EVENT = 10
---后续处理的函数映射
EventSystemV2.PENDING_HANDLE_FUNC_MAP = {
	ADD = "AddListener",
	REMOVE = "RemoveListener",
}

function EventSystemV2:ctor(name, initAllEvents)
	self.name = name
	self.eventCallbackDict = {}
	self.targetRegisterEventTypes = {}
	setmetatable(self.targetRegisterEventTypes, mtWeakKeyTable)
	self.lockEventTypes = {}
	self.pendingProcessEventTypes = {}
	self.toRemoveTargetEventTypes = {}
	if initAllEvents ~= false then
		self:initAllEventTypes()
	end
end

function EventSystemV2:dtor(dumpLeak)
	if dumpLeak ~= false then
		self:dumpLeakEvents()	
	end
	self:reset()
end

function EventSystemV2:reset()
	table.clear(self.eventCallbackDict)
	table.clear(self.targetRegisterEventTypes)
	table.clear(self.lockEventTypes)
	table.clear(self.pendingProcessEventTypes)
	table.clear(self.toRemoveTargetEventTypes)
end

function EventSystemV2:OnGetFromPool(name)
	self.name = name
	self:reset()
end

---初始化所有的eventTypes
---@private
function EventSystemV2:initAllEventTypes()
	local maxCnt = DefineEvents.GetMaxValue()
	for i = 1, maxCnt do
		self.eventCallbackDict[i] = EventSystemV2.DummyCallback
	end
end

---@private
function EventSystemV2:dumpLeakEvents()
	if not SHIPPING_MODE then
		for eventType, callbackInfo in pairs(self.eventCallbackDict) do
			if callbackInfo ~= EventSystemV2.DummyCallback then
				for target, callbackName in pairs(callbackInfo) do
					Log.DebugErrorFormat("[EventSystemV2:dumpLeakEvents] 存在泄露，eventType:%s, callbackName:%s, target:%s", self:GetEventName(eventType), callbackName, target.__cname or "")
				end	
			end
		end
	end
end

---@private
function EventSystemV2:checkEventCallbackNoExist(eventType)
	local callbacks = self.eventCallbackDict[eventType]
	return callbacks == nil or callbacks == EventSystemV2.DummyCallback
end

---@private
function EventSystemV2:addIntoPendingQueues(callbackType, eventType, callbackName, target)
	self.pendingProcessEventTypes[#self.pendingProcessEventTypes + 1] = {callbackType, eventType, callbackName, target}
end

---@private
function EventSystemV2:processPendingQueues()
	for _, processEventType in ipairs(self.pendingProcessEventTypes) do
		local callbackType, eventType, callbackName, target = processEventType[1], processEventType[2], processEventType[3], processEventType[4]
		local func = self[callbackType]
		xpcall(func, _G.CallBackError, self, eventType, callbackName, target)
	end
	table.clear(self.pendingProcessEventTypes)
end

---@private
function EventSystemV2:checkEventTypeLocked(eventType)
	local eventTypeLockCnt = self.lockEventTypes[eventType]
	return eventTypeLockCnt ~= nil and eventTypeLockCnt > 0
end

---@private
function EventSystemV2:detectCallbackCntPerEventTypeExceed(eventType)
	if not SHIPPING_MODE then
		local exceed = table.count(self.eventCallbackDict[eventType]) >= EventSystemV2.MAX_CALLBACK_CNT_PER_EVENT
		if exceed then
			if not DefineEventsDetectWhiteList[eventType] then
				Log.DebugErrorFormat("[EventSystemV2:detectCallbackCntPerEventTypeExceed] 超过最大callback数量，eventType:%s", self:GetEventName(eventType))	
			end
		end
	end
end

function EventSystemV2:BatchAddTargetListeners(type2callbackNames, target)
	for eventType, callbackName in pairs(type2callbackNames) do
		self:AddListener(eventType, callbackName, target)
	end
end

function EventSystemV2:AddListener(eventType, callbackName, target)
	if self:checkEventTypeLocked(eventType) then
		self:addIntoPendingQueues(EventSystemV2.PENDING_HANDLE_FUNC_MAP.ADD, eventType, callbackName, target)
		return
	end
	if self:checkEventCallbackNoExist(eventType) then
		local targetListeners = {}
		setmetatable(targetListeners, mtWeakKeyTable)
		self.eventCallbackDict[eventType] = targetListeners
	end
	if self.eventCallbackDict[eventType][target] then
		---重复注册了，抛出错误
		Log.ErrorFormat("[EventSystemV2:AddListener] 重复注册了，eventType:%s, callbackName:%s, target: %s", self:GetEventName(eventType), callbackName, target.__cname or "")
		return
	end
	self.eventCallbackDict[eventType][target] = callbackName
	if self.targetRegisterEventTypes[target] == nil then
		self.targetRegisterEventTypes[target] = {}
	end
	self.targetRegisterEventTypes[target][eventType] = true
	self:detectCallbackCntPerEventTypeExceed(eventType)
end

function EventSystemV2:RemoveListener(eventType, callbackName, target)
	if self:checkEventCallbackNoExist(eventType) then return end
	if self.eventCallbackDict[eventType][target] ~= callbackName then return end
	if self:checkEventTypeLocked(eventType) then
		self:addIntoPendingQueues(EventSystemV2.PENDING_HANDLE_FUNC_MAP.REMOVE, eventType, callbackName, target)
		return
	end
	self:removeEventListenerInternal(eventType, target)
end

function EventSystemV2:RemoveTargetAllListeners(target)
	local registerEventTypes = self.targetRegisterEventTypes[target]
	if registerEventTypes == nil then return end
	table.clear(self.toRemoveTargetEventTypes)
	for eventType, _ in pairs(registerEventTypes) do
		if self:checkEventTypeLocked(eventType) then
			local callbackName = self.eventCallbackDict[eventType][target]
			self:addIntoPendingQueues(EventSystemV2.PENDING_HANDLE_FUNC_MAP.REMOVE, eventType, callbackName, target)
		else
			table.insert(self.toRemoveTargetEventTypes, eventType)
		end
	end
	for _, eventType in ipairs(self.toRemoveTargetEventTypes) do
		self:removeEventListenerInternal(eventType, target)
	end
end

---@pirvate
function EventSystemV2:removeEventListenerInternal(eventType, target)
	self.eventCallbackDict[eventType][target] = nil
	self.targetRegisterEventTypes[target][eventType] = nil
end

---检查事件是否为空，给UniqEventSystemMgr调用的
function EventSystemV2:CheckEventsEmpty()
	return next(self.targetRegisterEventTypes) == nil
end

---@private
function EventSystemV2:detectPublishCntPerCycleExceed(eventType)
	if self.lockEventTypes[eventType] and self.lockEventTypes[eventType] > EventSystemV2.MAX_DISPATCH_COUNT then
		Log.ErrorFormat("EventSystemV2:Publish cycle call，eventType:%s", self:GetEventName(eventType))
		return true
	end
	return false
end

---这里需要注意的是，publish的过程中，又有Add、Remove
---为了防止死锁，如果在publish的过程中，再次发生了相同eventType的调用，则记录起来，如果次数超过一定量，直接报错，修改逻辑
function EventSystemV2:Publish(eventType, ...)
	if self:checkEventCallbackNoExist(eventType) then return end
	if self:detectPublishCntPerCycleExceed(eventType) then return end
	
	local curLockEventCnt = self.lockEventTypes[eventType] or 0
	self.lockEventTypes[eventType] = curLockEventCnt + 1
	self:publishInternal(eventType, ...)
	self.lockEventTypes[eventType] = curLockEventCnt
	self:processPendingQueues()
end

---@private
function EventSystemV2:publishInternal(eventType, ...)
	local callbacks = self.eventCallbackDict[eventType]
	for target, callbackName in pairs(callbacks) do
		local func = target[callbackName]
		if func then
			xpcall(func, _G.CallBackError, target, ...)
		else
			Log.ErrorFormat("[EventSystemV2:publishInternal] 找不到对应的callback，eventType:%s, callbackName:%s, target:%s, isDestroyed:%s",
				self:GetEventName(eventType), callbackName, target.__cname or "", target.isDestroyed or false)
		end
	end
end

function EventSystemV2:GetEventName(eventType)
	return EventDefine.GetEventName(eventType)
end

return EventSystemV2
