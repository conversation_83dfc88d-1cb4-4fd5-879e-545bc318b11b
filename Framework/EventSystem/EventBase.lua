---@class EventBase
---@field events table<string,string|function|table<string|function,string>>
local EventBase = DefineClass("EventBase")

function EventBase:ctor()
    self.events = nil
	self.eventsV2 = {}
end

function EventBase:GetBindMap(class, map)
    if class.__supers then
        for _, super in ipairs(class.__supers) do
            self:GetBindMap(super, map)
        end
    end
    if class.eventBindMap then
        for key, event in pairs(class.eventBindMap) do
            map[key] = event
        end
    end
    return map
end

---@public BatchAddListener 批量注册事件监听
function EventBase:BatchAddListener()
    local eventDict = self:GetBindMap(self, self.eventBindMap)
    if not eventDict then
        return
    end
    for eventType, value in pairs(eventDict) do
		if type(eventType) == "number" then
			goto continue
		end
        if type(value) == "table" then
            if type(value[2]) == "function" then
                local result, behavior = xpcall(value[2], _G.CallBackError)
                if result then
                    self:AddListener(eventType, value[1], behavior)
                end
            else
                self:AddListener(eventType, value[1], value[2])
            end
        else
            self:AddListener(eventType, value)
        end
		:: continue ::
    end
	self:CompatibleBatchAddListener(eventDict)
end

---@public RemoveAllListener 批量移除事件监听
---@param forceRemove boolean 是否强制强制移除（防止移除过程中收到事件）
function EventBase:RemoveAllListener(forceRemove)
    Game.EventSystem:RemoveObjListeners(self, forceRemove)
    if self.events then
        table.clear(self.events)
    end
	self:RemoveAllListenerV2()
end

---@public AddListener 注册事件监听
---@param eventType string 事件类型
---@param callBackFunc string|function  事件回调函数（或回调函数名称）
---@param behavior string entityId
function EventBase:AddListener(eventType, callBackFunc, behavior)
    if (eventType == nil or (type(callBackFunc) == "string" and self[callBackFunc] == nil or callBackFunc == nil)) then
        Log.Warning("EventSystem AddListener Invalid Params ", eventType ~= nil and eventType or "EventType is nil")
        return
    end

    self.events = self.events or {}
    if behavior then
        self.events[eventType] = self.events[eventType] or {}
        if self.events[eventType][behavior] then
            --Log.Error("AddListener Error: already register this eventType. ClassName:", self.__cname, " EventType:", eventType)
            return
        end
    elseif self.events[eventType] then
        --Log.Error("AddListener Error: already register this eventType. ClassName:", self.__cname, " EventType:", eventType)
        return
    end
    Game.EventSystem:AddListener(eventType, self, callBackFunc, behavior)
    if behavior then
        self.events[eventType][behavior] = callBackFunc
    else
        self.events[eventType] = callBackFunc
    end
end

---@public RemoveListener 移除事件监听
---@param eventType string 事件类型
---@param behavior string entityId
function EventBase:RemoveListener(eventType, behavior)
    if self.events and self.events[eventType] then
        local eventValue = self.events[eventType]
        if behavior and eventValue then
            eventValue = self.events[eventType][behavior]
        end
        Game.EventSystem:RemoveListenerFromType(eventType, self, eventValue, behavior)
        if eventValue then
            if behavior then
                self.events[eventType][behavior] = nil
            else
                self.events[eventType] = nil
            end
        end
    end
end

---@public OnDestroy
function EventBase:OnDestroy()
    self:RemoveAllListener(true)
end

--region EventSystemV2
function EventBase:checkEventTypeAndCallbackInValid(eventType, callbackName, bLog)
	if type(eventType) == "string" then
		if bLog then
			Log.ErrorFormat("EventSystem AddListenerV2 Invalid, eventType: %s must be int", eventType)	
		end
		return true
	end
	if type(callbackName) ~= "string" then
		if bLog then
			Log.ErrorFormat("EventSystem AddListenerV2 Invalid, callbackName must be string, eventType: %s",
				Game.GlobalEventSystem:GetEventName(eventType))	
		end
		return true
	end
	return false
end

function EventBase:AddListenerV2(eventType, callbackName)
	--只需要在Add的时候检查一下，因为只要Add的时候知道传string，那么remove的时候一定也知道
	if self:checkEventTypeAndCallbackInValid(eventType, callbackName, true) then return end
	self:doAddListenerV2(eventType, callbackName)
end

function EventBase:doAddListenerV2(eventType, callbackName)
	self.eventsV2[eventType] = callbackName
	Game.GlobalEventSystem:AddListener(eventType, callbackName, self)
end

function EventBase:RemoveListenerV2(eventType, callbackName)
	if self.eventsV2[eventType] == callbackName then
		self.eventsV2[eventType] = nil
		Game.GlobalEventSystem:RemoveListener(eventType, callbackName, self)
	end
end

function EventBase:CompatibleBatchAddListener(eventType2CallbackNames)
	for eventType, callbackName in pairs(eventType2CallbackNames) do
		if not self:checkEventTypeAndCallbackInValid(eventType, callbackName, false) then 
			self:doAddListenerV2(eventType, callbackName)
		end
	end
end

function EventBase:BatchAddListenerV2(eventType2CallbackNames)
	for eventType, callbackName in pairs(eventType2CallbackNames) do
		self.eventsV2[eventType] = callbackName
	end
	Game.GlobalEventSystem:BatchAddTargetListeners(eventType2CallbackNames, self)
end

function EventBase:RemoveAllListenerV2()
	Game.GlobalEventSystem:RemoveTargetAllListeners(self)
	table.clear(self.eventsV2)
end
--endregion

return EventBase