---从sLuaUnReal中的LuaClass.inl复制过来，主要是为了热更方便，去掉缓存
local setmetatable = setmetatable
local getmetatable = getmetatable
local rawset = rawset
local CBase

--没有UClass的缓存
__EN_UCLASS_CACHE_ = false

function Class(base, static, classImplement)
	if base == nil then
		base = CBase
	end
	classImplement = classImplement or {}
	classImplement.__super_impl = base.__inner_impl
	classImplement.__super = base

	local base_mt = getmetatable(base)
	local class = static or {}
	class.__inner_impl = classImplement

	local class_index, class_newindex
	if __EN_UCLASS_CACHE_ then
		class_index = function (t, k, cache)
			local impl = classImplement
			local ret
			while impl do
				ret = impl[k]
				if ret ~= nil then
					if cache ~= false then
						rawset(t, k, ret)
					end
					return ret
				end
				impl = impl.__super_impl
			end
			return nil
		end
	else
		class_index = function (t, k, cache)
			local impl = classImplement
			local ret
			while impl do
				ret = impl[k]
				if ret ~= nil then
					-- 去掉缓存
					--if cache ~= false then
					--	rawset(t, k, ret)
					--end
					return ret
				end
				impl = impl.__super_impl
			end
			return nil
		end
	end

	class_newindex = function (t, k, v)
		error(string.format("Prevent __newindex with class! k=%s", k))
	end

	local instance_metatable =
	{
		__index = class_index,
	}

	local function recursiveCtor(r, impl, ...)
		local superImpl = impl.__super_impl
		if superImpl then
			recursiveCtor(r, superImpl, ...)
		end
		local ctor = impl.ctor
		if ctor then
			ctor(r, ...)
		end
	end

	setmetatable(class,
		{
			__index = class_index,

			__newindex = class_newindex,

			__call = function(_, ...)
				local r = {}
				setmetatable(r, instance_metatable)
				recursiveCtor(r, classImplement, ...)
				return r
			end,
		}
	)
	return class
end

local base = {}
setmetatable(base, {__call = function () return {} end})

CBase = Class(base, nil, nil)
