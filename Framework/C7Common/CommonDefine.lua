
local stringSub = string.sub

-- region 全局锁相关函数
function GlobalLock(t)
    local mt = getmetatable(t) or {}
    mt.__newindex = function(table, k, v)
        if (k ~= "_" and stringSub(k, 1, 2) ~= "__") then
            -- 后续再改成error，这里换成LuaCLogger，因为Log可能没有初始化
			LuaCLogger.DebugLogFmt("GLOBALS are locked -- %s must be declared local or prefix with '__' for globals.", k)
			rawset(table, k, v)
        else
            rawset(table, k, v)
        end
    end
    setmetatable(t, mt)

    __G_LOCK = true -- luacheck: ignore
end

function GlobalUnlock(t)
    local mt = getmetatable(t) or {}
    mt.__newindex = rawset
    setmetatable(t, mt)

    __G_LOCK = false  -- luacheck: ignore
end
-- endregion

-- region
-- 只在编辑器下锁住
if _G.UE_EDITOR then
	local rawRequire = require
	
	function LockRequire()
		_G.require = function(modName)
			local mod = package.loaded[modName]
			if mod then
				return mod
			else
				local module = rawRequire(modName)
				-- 过滤一下cpp require的
				if type(module) == "table" and module.__inner_impl == nil and (not Game.RefreshScript) then
					LuaCLogger.DebugErrorFmt("【！！！不要慌，不阻塞功能，请无视它！！！】 [LockRequire] donot use require, please use kg_require, modName: %s", modName)  -- luacheck:ignore
				end
				return module
			end
		end
	end

	function UnlockRequire()
		_G.require = rawRequire
	end
else
	function LockRequire() end
	function UnlockRequire() end
end
-- endregion