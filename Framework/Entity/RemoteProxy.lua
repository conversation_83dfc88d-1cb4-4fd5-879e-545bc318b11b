---@class OnlineRemoteProxy
OnlineRemoteProxy = DefineClass("OnlineRemoteProxy")
function OnlineRemoteProxy:ctor(entity)
    local metatable = {
        __index = function(_, rpcName)
            local func = function(arg0, ...)
                if arg0 == self or arg0 == nil then
                    entity:call_logic(rpcName, ...)
                else
                    entity:call_logic(rpcName, arg0, ...)
                end
            end
            rawset(self, rpcName, func)
            return func
        end
    }
    setmetatable(self, metatable)
end

---@class DummyRemoteProxy
DummyRemoteProxy = DefineClass("DummyRemoteProxy")
function DummyRemoteProxy:ctor()
    local metatable = {
        __index = function(_, rpcName)
            local func = function(...)
            end
            rawset(self, rpcName, func)
            return func
        end
    }
    setmetatable(self, metatable)
end
