local Const = kg_require("Shared.Const")

---@class EntityManager
EntityManager = DefineClass("EntityManager")

EEntityType = {
    MainPlayer = "MainPlayer", --主角
    AvatarActor = "AvatarActor", --第三方玩家
    NpcActor = "NpcActor", --NPC及怪物
}

EntityManager.EntitysCountChangedEventName = "EntitysCountChangedEvent"

function EntityManager.Instance()
    if EntityManager.instance == nil then
        EntityManager.instance = EntityManager.new()
    end
    return EntityManager.instance
end

function EntityManager:ctor()
    self.entityClsMap = {}
    self.briefEntityClsMap = {}
	--self.entities = Game.GameSDK.entityManager.entities
	self.entities = {}			-- 普通entity,int id做key
	self.entitiesStrKey = {}	-- 普通entity，string做key，后续删除
	self.briefEntities = {}		-- brief entity
    self.briefEntitiesStrKey = {}	-- 普通entity，string做key, 目前很多的业务逻辑都是基于Entity的eid(服务器有的地方叫uid)，这里只能兼容，性能有需求的用uid(服务器叫int_id) @hujianglong

	-- 包含local entity
    self.allEntities = {}
    self.allEntityMap = {}
    self.allEntityNum = {}
	
	-- 保存所有的scene actor
	self.sceneActorMap = {}
	-- 保存所有的avatar，包括brief avatar
	self.allAvatarMap = {}

	self.localEntityClsMap = {}
	self.localEntities = {}
	self.localEntityCountMap = {}
	self.avatarCount = 0
	self.npcCount = 0
	self.briefCount = 0
end

function EntityManager:dtor()
    self.entityClsMap = {}
    self.briefEntityClsMap = {}
    self:clear()
    
    self.allEntities = {}
    self.allEntityMap = {}
    self.allEntityNum = {}
	self.sceneActorMap = {}
	self.allAvatarMap = {}

	self.localEntityClsMap = {}
	self.localEntities = {}
end

function EntityManager:ClearEntity()
	local toDestroyEntities = {}
    for _, v in pairs(self.entities) do
        if v ~= Game.me then  --MainPlayer始终不主动销毁，获取到新的后再销毁老的
			table.insert(toDestroyEntities, v)
        end
    end

	for _, v in pairs(self.briefEntities) do
		table.insert(toDestroyEntities, v)
	end

	for _, entity in pairs(toDestroyEntities) do
		entity:destroy()
	end
end

function EntityManager:ClearLocalEntity()
	local deleteIDList = {}
	for k, _ in pairs(self.localEntities) do
		table.insert(deleteIDList, k)
	end
	for _, entityID in ipairs(deleteIDList) do
		local entity = self:getEntity(entityID)
		if entity then
			entity:destroy()
		end
	end
end

function EntityManager:registerEntity(entityName, entityCls, bBriefEntity)
    if bBriefEntity then
        self.briefEntityClsMap[entityName] = entityCls
    else
        self.entityClsMap[entityName] = entityCls
    end
end

function EntityManager:getEntitiesByType(EntityType)
    return  self.allEntities[EntityType]
end

function EntityManager:getEntityCharacterList()
    return self.allEntities["AvatarActor"]
end

function EntityManager:getEntity(entityId)
    local entity = self.entities[entityId]
    if entity then
        return entity
    end
	entity = self.entitiesStrKey[entityId]
	if entity then
		return entity
	end
    entity = self.localEntities[entityId]
    if entity then
        return entity
    end
	local iid = tonumber(entityId)
	if iid then
		entity = self.localEntities[iid]
		if entity then
			return entity
		end
	end
    -- 兼容下整型id获取
    return self:GetEntityByIntID(entityId)
end

-- C7 CODE ADD <NAME_EMAIL>
-- 兼容接口

function EntityManager:GetEntityEIDByUID(uid)
    local ent = self:getEntity(uid)
    return ent and ent.eid or nil
end

-- C7 CODE ADD <NAME_EMAIL>

function EntityManager:getEntityCls(entityType, bBriefEntity)
    if bBriefEntity then
        return self.briefEntityClsMap[entityType]
    else
        return self.entityClsMap[entityType]
    end
end

function EntityManager:getEntityClsMap()
    return self.entityClsMap
end

-- 按类型添加entity
function EntityManager:AddEntity(entity, is_brief)
	if is_brief then
		self.briefEntities[entity:uid()] = entity
        self.briefEntitiesStrKey[entity.eid] = entity
		self.briefCount = self.briefCount + 1
	else
		self.entities[entity:uid()] = entity
		self.entitiesStrKey[entity.eid] = entity

		if not self.allEntities[entity.ENTITY_TYPE] then
			self.allEntities[entity.ENTITY_TYPE] = {}
		end
		self.allEntities[entity.ENTITY_TYPE][entity:uid()] = entity
		self.allEntityMap[entity:uid()] = entity
		if entity.ENTITY_TYPE == "AvatarActor" then
			self.avatarCount = self.avatarCount + 1
		elseif entity.ENTITY_TYPE == "NpcActor" then
			self.npcCount = self.npcCount + 1
		end
	end
	if entity.ENTITY_TYPE == "AvatarActor" then
		self.allAvatarMap[entity:uid()] = entity
	end
	
    self:ModifyEntitysNum(entity.ENTITY_TYPE, 1)
end

-- 按类型移除entity
function EntityManager:RemoveEntity(entId, intId, is_brief)
	local entityType
	if is_brief then
		local briefEntity = self.briefEntities[intId]
		entityType = briefEntity.class.ENTITY_TYPE -- 正常流程肯定能取到，取不到报错抛出问题
		self.briefEntities[intId] = nil
		self.briefEntitiesStrKey[entId] = nil
		self.briefCount = self.briefCount - 1
	else
		local entity = self.entities[intId]
		entityType = entity.class.ENTITY_TYPE  -- 正常流程肯定能取到，取不到报错抛出问题
		self.entities[intId] = nil
		self.entitiesStrKey[entId] = nil
		if entityType and self.allEntities[entityType] then
			self.allEntities[entityType][intId] = nil
			self.allEntityMap[intId] = nil
		end
		if entity.ENTITY_TYPE == "AvatarActor" then
			self.avatarCount = self.avatarCount - 1
		elseif entity.ENTITY_TYPE == "NpcActor" then
			self.npcCount = self.npcCount - 1
		end
	end
	if entityType == "AvatarActor" then
		self.allAvatarMap[intId] = nil
	end

	self:ModifyEntitysNum(entityType, -1)
end

----------------------------------------------------------- Local Entity Start -----------------------------------------------------

function EntityManager:registerLocalEntity(entityName, entityCls)
	self.localEntityClsMap[entityName] = entityCls
end

function EntityManager:GetLocalEntityMap()
	return self.localEntities
end

function EntityManager:GetLocalEntity(uid)
	return self.localEntities[uid]
end

function EntityManager:IsLocalEntity(entityId)
	return self.localEntities[entityId] ~= nil
end

--- 创建entity接口
--- props: 初始化属性，会直接将该table作为entity本身
--- initData: 额外初始化数据，可以将外部数据传入，在Init接口中按需取用该数据
function EntityManager:CreateLocalEntity(entityName, props, initData)
	local entityCls = self.localEntityClsMap[entityName]
	if not entityCls then
		DebugLogError(string.format("EntityManager createEntity entityName error, %s", entityName))
		return
	end
	local entityID = _script.genIdForPureClientEntity()
	local entity = entityCls.new(entityID, props)
	self.localEntities[entity:uid()] = entity
	self:AddLocalEntity(entity)
	-- Local entity，不是真正的entity，需要初始化一些默认prop
	entity:call_components(Const.CALL_ENTITY_COMPONENT_STAGE.PropInit)
	if entity.Init then
		entity:Init(initData)
	end
	if entity.LoadActor then
		entity:LoadActor()
	end
	
	if entityName == "ActorBase" then
		DebugLogError(string.format("EntityManager CreateLocalEntity Use ActorBase entityID:%s,uid:%s ", entityID, entity:uid()))
	end
	
	return entity
end

function EntityManager:AddLocalEntity(entity)
	if not self.allEntities[entity.ENTITY_TYPE] then
		self.allEntities[entity.ENTITY_TYPE] = {}
	end
	self.allEntities[entity.ENTITY_TYPE][entity:uid()] = entity
	self.allEntityMap[entity:uid()] = entity
	self.localEntityCountMap[entity.ENTITY_TYPE] = (self.localEntityCountMap[entity.ENTITY_TYPE] or 0) + 1
end

-- 传入整型ID
function EntityManager:RemoveLocalEntity(entityID)
	local entity = self.localEntities[entityID]
	if not entity then
		return
	end
	local entityType = entity.ENTITY_TYPE
	self.localEntities[entityID] = nil
	if not self.allEntities[entityType] then
		return
	end
	self.allEntities[entityType][entityID] = nil
	self.allEntityMap[entityID] = nil
	self.localEntityCountMap[entity.ENTITY_TYPE] = self.localEntityCountMap[entity.ENTITY_TYPE] - 1
end

----------------------------------------------------------- Local Entity End -----------------------------------------------------


function EntityManager:GetEntityByIntID(iid)
    local entity = self.allEntityMap[iid]
    if entity then
        return entity
    end
    return self.localEntities[tostring(iid)]
end

function EntityManager:IsAvatar(entity)
    return entity.ENTITY_TYPE == "AvatarActor" or entity.ENTITY_TYPE == "MainPlayer"
end

function EntityManager:IsAOIAvatar(entity)
	return entity.ENTITY_TYPE == "AvatarActor"
end

function EntityManager:IsNpc(entity)
    return entity.ENTITY_TYPE == "NpcActor"
end

function EntityManager:IsLocalPerformPetEntity(entity)
	return entity.ENTITY_TYPE == "LocalPerformPetEntity"
end

function EntityManager:ModifyEntitysNum(EType, AddNum)
    local _Num = self.allEntityNum[EType]
    if _Num == nil then
        _Num = 0
    end

    _Num  = _Num + AddNum
    if _Num < 0 then
        _Num = 0
    end

    self.allEntityNum[EType] = _Num

	Game.GlobalEventSystem:Publish(_G.EEventTypesV2.ENTITY_COUNT_CHANGED, EType, _Num)
end

function EntityManager:GetEntitiesCountByType(EntityType)
	if self.allEntities[EntityType] then
		return table.count(self.allEntities[EntityType])
	end
	return 0
end

-- 只供aoi分级访问，获取所有的avatar，包括brief avatar
function EntityManager:GetAllAvatarsWithBrief()
	return self.allAvatarMap
end

-- 兼容查询brief entity actor
function EntityManager:getEntityWithBrief(entityId)

    local entity = self.briefEntities[entityId]
    if entity then
        return entity
    end
	entity = self.briefEntitiesStrKey[entityId]
	if entity then
		return entity
	end
	return self:getEntity(entityId)
end

-- 根据场编的唯一id获取entity，后续再优化获取性能
function EntityManager:getEntityByInsID(insID)
	-- 获取npc
	for _, entity in pairs(self.entities) do
		if entity.InstanceID == insID then
			return entity
		end
	end
	-- 获取场景物件
	for _, entity in pairs(self.localEntities) do
		if entity.InsID == insID then
			return entity
		end
	end
	return nil
end

return EntityManager.Instance()
