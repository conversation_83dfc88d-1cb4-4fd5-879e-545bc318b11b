---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by Administrator.
--- DateTime: 2022/1/13 17:32
---

---@class Set
Set = DefineClass("Set")

---@overload fun(): nil
---@param another Set
function Set:ctor(another)
    self.__count = -1
    self.data = {}
    if another ~= nil then
        self:Update(another)
    end
end

function Set:dtor()
    self.data = nil
    self.__count = 0
end

--- Add an element to a set.
---@param element any
function Set:Add(element)
    self.data[element] = true
    self.__count = -1
end

--- Remove all elements from this set.
function Set:Clear()
    self.data = {}
    self.__count = 0
end

--- Return the element count of this set.
function Set:Count()
    if self.__count == -1 then
        self.__count = #self:ToList()
    end
    return self.__count
end

--- Report whether element in this set.
---@param element any
---@return boolean
function Set:Contains(element)
    return self.data[element] == true
end

--- Return a shallow copy of a set.
---@return Set
function Set:Copy()
    return Set.new(self)
end

--- Return the difference of two or more sets as a new set.
---@param another Set
---@return Set
function Set:Difference(another, ...)
    local ret = Set.new(self)
    for _, other in ipairs({another, unpack({...})}) do
        for element, _ in pairs(other.data) do
            ret:Remove(element)
        end
    end
    return ret
end

--- Remove all elements of another set from this set.
---@param another Set
function Set:DifferenceUpdate(another)
    for element, _ in pairs(another.data) do
        self:Remove(element)
    end
end

--- Return the intersection of two sets as a new set.
---@return Set
function Set:Intersection(another)
    local ret = self:Clone()
    ret:Update(another)
    return ret
end

--- Report whether another set contains this set.
---@param another Set
---@return boolean
function Set:IsSubSet(another)
    for element, _ in pairs(self.data) do
        if not another:Contains(element) then
            return false
        end
    end
    return true
end

--- Report whether this set contains another set.
---@param another Set
---@return boolean
function Set:IsSuperSet(another)
    for element, _ in pairs(another.data) do
        if not self:Contains(element) then
            return false
        end
    end
    return true
end

--- Remove an element from a set.
function Set:Remove(element)
    self.data[element] = nil
    self.__count = -1
end

--- Return all elements as list
---@return number[]|string[]
function Set:ToList()
    local data = {}
    for element, _ in pairs(self.data) do
        data[#data + 1] = element
    end
    return data
end

--- Return the union of sets as a new set.
---@param another Set
---@return Set
function Set:Union(another)
    local ret = Set.new()
    for element, _ in pairs(another.data) do
        if self:Contains(element) then
            ret:Add(element)
        end
    end
    return ret
end

---@param another Set
function Set:Update(another)
    if type(another) ~= "table" then return end
    if another.__cname == "Set" then
        another = another:ToList()
    end
    for _, element in ipairs(another) do
        self.data[element] = true
    end
    self.__count = -1
end

return Set
