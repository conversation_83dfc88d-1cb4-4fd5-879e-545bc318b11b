--[[
  Lua日志规范: https://docs.corp.kuaishou.com/k/home/<USER>/fcACoVwyEZY44odMI9LKrFHFR#section=h.euh12pt9i0fr
]]

-- luacheck: push ignore
LogLevel = LogLevel or {
	DebugLog = 1,
	DebugWarning = 2,
	DebugError = 3,
	Log = 4,
	Warning = 5,
	Error = 6,
}

Log = Log or {}
Log.Level = LogLevel.DebugLog
--发布版本关闭Debug日志
if SHIPPING_MODE then
	Log.Level = LogLevel.Log
end

local KGLOG = LuaCLogger
--init clog levels
KGLOG.SetGameLogLevel(Log.Level)
KGLOG.SetLogOnScreenLevel(LogLevel.Error)

--客户端Debug日志(发布版本不打印)
Log.Debug = KGLOG.DebugLog
Log.DebugFormat = KGLOG.DebugLogFmt
Log.DebugWarning = KGLOG.DebugWarning
Log.DebugWarningFormat = KGLOG.DebugWarningFmt
Log.DebugError = KGLOG.DebugError
Log.DebugErrorFormat = KGLOG.DebugErrorFmt
Log.DebugPrefixLogFmt = KGLOG.DebugPrefixLogFmt
Log.DebugPrefixWarningFmt = KGLOG.DebugPrefixWarningFmt
Log.DebugPrefixErrorFmt = KGLOG.DebugPrefixErrorFmt

--客户端Release日志
Log.Info = KGLOG.Log
Log.InfoFormat = KGLOG.LogFmt
Log.Warning = KGLOG.Warning
Log.WarningFormat = KGLOG.WarningFmt
Log.Error = KGLOG.Error
Log.ErrorFormat = KGLOG.ErrorFmt
Log.PrefixLogFmt = KGLOG.PrefixLogFmt
Log.PrefixWarningFmt = KGLOG.PrefixWarningFmt
Log.PrefixErrorFmt = KGLOG.PrefixErrorFmt
Log.PrefixFatalFmt = KGLOG.PrefixFatalFmt

-- 客户端和服务器的Log接口保持一致，用于Shared目录中的代码
LOG_DEBUG = Log.Debug
LOG_DEBUG_FMT = Log.DebugFormat
LOG_INFO = Log.Info
LOG_INFO_FMT = Log.InfoFormat
LOG_WARN = Log.Warning
LOG_WARN_FMT = Log.WarningFormat
LOG_ERROR = Log.Error
LOG_ERROR_FMT = Log.ErrorFormat

---@class Logger
DefineClass("Logger")

--------------带有prefix的Debug日志(发布版不会打印self.Logger.xxx的日志)------------
function Logger:ctor(prefix)
	self.prefix = prefix or ""
end

function Logger:SetPrefix(prefix)
	self.prefix = prefix
end

function Logger:Debug(msg, ...)
end

function Logger:DebugWarning(msg, ...)

end

function Logger:DebugError(msg, ...)
end


local function DumpTable(Table, Dump_metatable, Max_level)
	if not Table then
		return
	end
	local lookup_table = {}

	local rep = string.rep

	Dump_metatable = Dump_metatable or false

	Max_level = Max_level or 1

	local function _dump(t, level)
		local str = "\n" .. rep("\t", level) .. "{\n"

		for k, v in pairs(t) do
			local k_is_str = type(k) == "string" and 1 or 0

			local v_is_str = type(v) == "string" and 1 or 0

			str =
			str ..
				rep("\t", level + 1) ..
				"[" .. rep('"', k_is_str) .. (tostring(k) or type(k)) .. rep('"', k_is_str) .. "]" .. " = "

			if type(v) == "table" then
				if not lookup_table[v] and ((not Max_level) or level < Max_level) then
					lookup_table[v] = true

					str = str .. _dump(v, level + 1) .. "\n"
				else
					str = str .. (tostring(v) or type(v)) .. ",\n"
				end
			else
				str = str .. rep('"', v_is_str) .. (tostring(v) or type(v)) .. rep('"', v_is_str) .. ",\n"
			end
		end

		if Dump_metatable then
			local mt = getmetatable(t)

			if mt ~= nil and type(mt) == "table" then
				str = str .. rep("\t", level + 1) .. '["__metatable"]' .. " = "

				if not lookup_table[mt] and ((not Max_level) or level < Max_level) then
					lookup_table[mt] = true

					str = str .. _dump(mt, level + 1) .. "\n"
				end
			end
		end

		str = str .. rep("\t", level) .. "},"

		return str
	end

	print(string.sub(_dump(Table, 0), 1, -2))
end

Log.Dump = function(Table, Dump_metatable, Max_level)
	if Log.Level > LogLevel.DebugError then
		return
	end
	DumpTable(Table, Dump_metatable, Max_level)
end

--------------带有prefix的Debug日志(发布版不会打印self.Logger.xxx的日志) End------------


-- 游戏开始时输出当前日志等级
local FinishInit = function()
	for key, v in pairs(LogLevel) do
		if v == Log.Level then
			local sTable = {}
			sTable[#sTable + 1] = "Current LogLevel is: "
			sTable[#sTable + 1] = tostring(key)
			ReleaseLog(table.concat(sTable))
			break
		end
	end
end
FinishInit()

-- luacheck: pop

