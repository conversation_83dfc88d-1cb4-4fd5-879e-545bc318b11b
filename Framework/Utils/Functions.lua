function traceback(msg)
    msg = debug.traceback(msg, 2)
    return msg
end

function LuaGC()
    local StartCount = collectgarbage("count")
    Log.InfoFormat("collectgarbage : Begin gc count = %s mb", StartCount/1024)
    collectgarbage("collect")
	local EndCount = collectgarbage("count")
    Log.InfoFormat("collectgarbage : End gc count = %s  free = %s mb",EndCount/1024, (StartCount - EndCount)/1024)
end

--------------------------------------------------
-- Util functions about table
function RemoveTableItem(list, item, removeAll)
    local rmCount = 0

    for i = 1, #list do
        if list[i - rmCount] == item then
            table.remove(list, i - rmCount)

            if removeAll then
                rmCount = rmCount + 1
            else
                break
            end
        end
    end
end

-- function table.equal(a, b)
--     if a == nil then
--         return b == nil
--     end
--     if b == nil then
--         return false
--     end
--     if #a ~= #b then
--         return false
--     end
--     for i = 1, #a do
--         if a[i] ~= b[i] then
--             return false
--         end
--     end
--     return true
-- end

-- From http://lua-users.org/wiki/TableUtils
function table.val_to_str(v)
    if "string" == type(v) then
        v = string.gsub(v, "\n", "\\n")
        if string.match(string.gsub(v, "[^'\"]", ""), '^"+$') then
            return "'" .. v .. "'"
        end
        return '"' .. string.gsub(v, '"', '\\"') .. '"'
    else
        return "table" == type(v) and table.tostring(v) or tostring(v)
    end
end

function table.key_to_str(k)
    if "string" == type(k) and string.match(k, "^[_%a][_%a%d]*$") then
        return k
    else
        return "[" .. table.val_to_str(k) .. "]"
    end
end

function PrintLua(name, lib)
    local m
    lib = lib or _G

    for w in string.gmatch(name, "%w+") do
        lib = lib[w]
    end

    m = lib
    if (m == nil) then
        Log.InfoFormat("Lua Module %s not exists", name)
        return
    end

    Log.InfoFormat("-----------------Dump Table %s-----------------", name)
    if (type(m) == "table") then
        for k, v in pairs(m) do
            Log.InfoFormat("Key: %s, Value: %s", k, v)
        end
    end

    local meta = getmetatable(m)
    Log.InfoFormat("-----------------Dump meta %s-----------------", name)

    while meta ~= nil and meta ~= m do
        for k, v in pairs(meta) do
            if k ~= nil then
                Log.InfoFormat("Key: %s, Value: %s", k, v)
            end
        end

        meta = getmetatable(meta)
    end

    Log.Info("-----------------Dump meta Over-----------------")
    Log.Info("-----------------Dump Table Over-----------------")
end

--------------------------------------------------
-- Util functions about string
local function chsize(char)
    local arr = { 0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc }

    if not char then
        return 0
    else
        for i = #arr, 1, -1 do
            if char >= arr[i] then
                return i
            end
        end
    end
end

function GetDir(path)
    return string.match(path, ".*/")
end

function GetFileName(path)
    return string.match(path, ".*/(.*)")
end

-- function IsNullUserData(data)
--     if not data or data == C7.cjson.null or data == "" then
--         return true
--     end
--     return false
-- end

-- isnan
function isnan(number)
    return not (number == number)
end

function isinf(number)
    return number == math.huge or number == -math.huge
end

function math.clamp(val, lower, upper)
    assert(val and lower and upper, "any parameter is nil")
    if lower > upper then
        lower, upper = upper, lower
    end
    return math.max(lower, math.min(upper, val))
end

function math.round(value)
    return value >= 0 and math.floor(value + 0.5) or math.ceil(value - 0.5)
end

function IsValid_L(Obj)
    if Obj == nil then
        return false
    end
    return slua.isValid(Obj)
end

KG_INVALID_ID = KG_INVALID_ID or 0
KG_INVALID_ACTOR_ID = KG_INVALID_ACTOR_ID or 0
KG_INVALID_ENTITY_ID = KG_INVALID_ENTITY_ID or 0

function IsValidID(id)
    return type(id) == "number" and id ~= KG_INVALID_ID
end

function ensure(condition)
	if not condition then
		-- add break pointer here
		xpcall(function() error("lua ensure failed", 4) end, _G.CallBackError)
	end
	return condition
end