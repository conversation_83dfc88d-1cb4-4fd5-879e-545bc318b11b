local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class UIComCheckBoxItem : UIListItem
---@field view UIComCheckBoxItemBlueprint
local UIComCheckBoxItem = DefineClass("UIComCheckBoxItem", UIListItem)

UIComCheckBoxItem.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComCheckBoxItem:OnCreate()
	self:InitWidget()
	self:InitUIData()
	self:InitUIComponent()
	self:InitUIEvent()
	self:InitUIView()
end

function UIComCheckBoxItem:InitWidget()
	self.text_Content = self.view.Text_Content
end

---初始化数据
function UIComCheckBoxItem:InitUIData()
end

--- UI组件初始化，此处为自动生成
function UIComCheckBoxItem:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComCheckBoxItem:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComCheckBoxItem:InitUIView()
end

---@public Refresh 刷新数据
function UIComCheckBoxItem:OnRefresh(data)
	self:SetName(data.name)
end

function UIComCheckBoxItem:UpdateSelectionState(selected)
	self.userWidget:BP_SetChecked(selected)
end

---设置名称
function UIComCheckBoxItem:SetName(name)
	if self.text_Content then
		self.text_Content:SetText(name or "")
	end
end

---设置是否有文本
---@param hasText boolean
function UIComCheckBoxItem:SetHasText(hasText)
	self.userWidget:BP_SetHasText(hasText)
end

---设置类型（暗、白）
---@param isLight boolean
function UIComCheckBoxItem:SetType(isLight)
	self.userWidget:BP_SetType(isLight)
end

---设置有效性
---@param disable boolean
function UIComCheckBoxItem:SetDisable(disable)
	self.userWidget:BP_SetDisable(disable)
end

return UIComCheckBoxItem
