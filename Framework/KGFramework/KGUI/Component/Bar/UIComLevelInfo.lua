local ESlateVisibility = import("ESlateVisibility")
local UIComBar = kg_require("Framework.KGFramework.KGUI.Component.Bar.UIComBar")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComLevelInfo : UIComponent
local UIComLevelInfo = DefineClass("UIComLevelInfo", UIComponent)

-- 默认分数格式化形式
UIComLevelInfo.DefaultScoreFormat = "%s/%s"

--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComLevelInfo:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComLevelInfo:InitUIData()
    ---tips 按钮点击事件
    self.tipsId = nil
    ---@type LuaDelegate<fun()>
    self.onTipClickEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function UIComLevelInfo:InitUIComponent()
    ---@type UIComBar
    self.ComBarCom = self:CreateComponent(self.view.ComBar, UIComBar)
end

---UI事件在这里注册，此处为自动生成
function UIComLevelInfo:InitUIEvent()
    if self.view.Btn_ClickArea then
        self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_Tips_Clicked")
    end
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComLevelInfo:InitUIView()
end

---@public
---@param curScore number 当前值
---@param targetScore number 目标值
---@param scoreTextFormat string 自定义数据格式化形式
---@param titleText string 标题文案
---@param levelText string 等级文案
function UIComLevelInfo:Refresh(curScore, targetScore, scoreTextFormat, titleText, levelText)
    scoreTextFormat = scoreTextFormat or self.DefaultScoreFormat
    local expText = string.format(scoreTextFormat, curScore, targetScore)
    self.view.Text_Exp:SetText(expText)
    self:RefreshBar(curScore, targetScore)

    self:SetTitle(titleText)
    self:SetLevel(levelText)
    if self.view.Btn_Tips then
        self.view.Btn_Tips:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function UIComLevelInfo:RefreshBar(curScore, targetScore)
    local percent
    if targetScore == 0 then
        percent = 1
        Log.Error("targetScore should not be set zero!")
    else
        percent = curScore / targetScore
    end
    self.ComBarCom:Refresh(percent)
end

function UIComLevelInfo:SetTitle(titleText)
    if self.view.Text_Title and titleText then
        self.view.Text_Title:SetText(titleText)
    end
end

function UIComLevelInfo:SetLevel(levelText)
    if self.view.Text_Level and levelText then
        self.view.Text_Level:SetText(levelText)
    end
end

---@public
---显示tips按钮
---@param tipsId int TipsData里的ID
function UIComLevelInfo:EnableShowTips(tipsId)
    self.tipsId = tipsId
    self.view.Btn_Tips:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

--- 此处为自动生成
function UIComLevelInfo:on_Btn_Tips_Clicked()
    if self.onTipClickEvent:IsBind() then
        self.onTipClickEvent:Execute()
    elseif self.tipsId then
        Game.TipsSystem:ShowTips(self.tipsId, self.view.Btn_Tips:GetCachedGeometry())
    end
end

return UIComLevelInfo
