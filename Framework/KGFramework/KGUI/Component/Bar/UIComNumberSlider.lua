local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComNumberSlider : UIComponent
---@field view UIComNumberSliderBlueprint
local UIComNumberSlider = DefineClass("UIComNumberSlider", UIComponent)

UIComNumberSlider.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComNumberSlider:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComNumberSlider:InitUIData()
    self.bNearest = false --是否开启近似值，去除小数保留整数
	self.bShowCalculatorPanel = nil	--是否显示计算器弹窗
    self.span = nil --递增值声明
    ---@type number
    self.min = 0
    ---@type number
    self.max = 100000000
	---@public 监听数值变化
    ---@type LuaDelegate<fun(value:number)>
    self.onValueChange = LuaDelegate.new()
	---@public 提交最终数值
	---@type LuaDelegate<fun(value:number)>
	self.onValueCommit = LuaDelegate.new()

    ---@public 当前数值
	---@type number
	self.curValue = 0

    ---@public 点击输入
	---@type LuaDelegate<fun()>
	self.onClickInput = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function UIComNumberSlider:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComNumberSlider:InitUIEvent()
    if self.view.Btn_MaxValue then
        self:AddUIEvent(self.view.Btn_MaxValue.OnClicked, "on_Btn_MaxValue_Clicked")
    end
    if self.view.Btn_Add then
        self:AddUIEvent(self.view.Btn_Add.OnClicked, "on_Btn_Add_Clicked")
    end
    if self.view.Btn_Input then
        self:AddUIEvent(self.view.Btn_Input.OnClicked, "on_Btn_Input_Clicked")
    end
    if self.view.Btn_Delete then
        self:AddUIEvent(self.view.Btn_Delete.OnClicked, "on_Btn_Delete_Clicked")
    end
    if self.view.Slider then
        self:AddUIEvent(self.view.Slider.OnValueChanged, "on_Slider_ValueChanged")
		self:AddUIEvent(self.view.Slider.OnMouseCaptureEnd, "on_Slider_ValueCommitted")
    end
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComNumberSlider:InitUIView()
end

---组件刷新统一入口
---@param cur number 当前值
---@param min number 最小值
---@param max number 最大值
---@param span number 递增值
---@param bShowCalculatorPopup boolean 点击输入框是否自动显示小键盘计算器,默认为true
function UIComNumberSlider:Refresh(cur, min, max, span, bShowCalculatorPopup)
    self:setValueInternal(cur, false)
    self.span = span or max/10
    self:SetMaxValue(max)
    self:SetMinValue(min)
    self:UpdateView()

	self.bShowCalculatorPanel = (bShowCalculatorPopup == nil) and true or bShowCalculatorPopup
end

function UIComNumberSlider:UpdateView()
    if self.view.Text_CurrentValue then
        self.view.Text_CurrentValue:SetText(self.curValue)
    end
    if self.view.ProgressBar then
        self.view.ProgressBar:SetPercent((self.curValue-self.min)/(self.max-self.min))
    end
    if self.view.Slider then
        self.view.Slider:SetValueWithoutNotify(self.curValue)
    end
end

function UIComNumberSlider:SetMaxValue(max)
    if self.view.Slider then
        self.view.Slider:SetMaxValue(max)
    end
    self.max = max or 100000000
end

--是否开启近似值，去除小数保留整数
function UIComNumberSlider:SetNearest(enable)
    self.bNearest = enable
end

function UIComNumberSlider:SetMinValue(min)
    if self.view.Slider then
        self.view.Slider:SetMinValue(min)
    end
    self.min = min or 0
end

function UIComNumberSlider:GetValue()
    return self.curValue
end

function UIComNumberSlider:SetValue(value)
	if value < self.min then
		value = self.min
	elseif value > self.max then
		value = self.max
	end
	self.curValue = value
	self:UpdateView()
end

--- 此处为自动生成
function UIComNumberSlider:on_Btn_MaxValue_Clicked()
    self:setValueInternal(self.max, true)
    self:UpdateView()
end

--- 此处为自动生成
function UIComNumberSlider:on_Btn_Add_Clicked()
    if self.curValue >= self.max then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MAX)
        return
    end
    local tempValue = self.curValue + self.span
    if tempValue > self.max then
        tempValue = self.max
    end
    self:setValueInternal(tempValue, true)
    self:UpdateView()
end

--- 此处为自动生成
function UIComNumberSlider:on_Btn_Input_Clicked()
    self.onClickInput:Execute()

	if self.bShowCalculatorPanel then
		local viewportPosition, anchors = Game.UITipsPosAutoFollowUtils.GetPanelAdaptPosFollowWidget(self.userWidget)
		Game.NewUIManager:OpenPanel(UIPanelConfig.ComNumInput_Panel, {
			SelectableMaximum = self.max,
			SelectableMinimum = self.min,
			FinishCallback = function(State, InputNum)
				self:FinishCallback(State, InputNum)
			end,
			viewportPosition = viewportPosition,
			anchors = anchors,
		})
	end
end

function UIComNumberSlider:FinishCallback(success, value)
	if success then
		self:setValueInternal(value, true)
		self:UpdateView()
	end
end

--- 此处为自动生成
function UIComNumberSlider:on_Btn_Delete_Clicked()
    if self.curValue <= self.min then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MIN)
        return
    end
    local tempValue = self.curValue - self.span
    if tempValue < self.min then
        tempValue = self.min
    end
    self:setValueInternal(tempValue, true)
    self:UpdateView()
end

--- 此处为自动生成
---@param value float
function UIComNumberSlider:on_Slider_ValueChanged(value)
	if self.bNearest == true then
		value = math.floor(value)
	end
    self:setValueInternal(value, true)
    self:UpdateView()
end

function UIComNumberSlider:on_Slider_ValueCommitted()
	self.onValueCommit:Execute(self.curValue)
end

function UIComNumberSlider:setValueInternal(value, bNotify)
    self.curValue = value
    if bNotify then
        self.onValueChange:Execute(value)
    end
end
return UIComNumberSlider
