local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComPageNum : UIComponent
---@field view UIComPageNumBlueprint
local UIComPageNum = DefineClass("UIComPageNum", UIComponent)

UIComPageNum.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComPageNum:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComPageNum:InitUIData()
    ---@public 分页发生变化
    ---@type LuaDelegate<fun(index:number)>
    self.onPageChange = LuaDelegate.new()
    
    ---@type number 当前页码
    self.curValue = 0
    ---@type number 最大页码
    self.max = 0
end

--- UI组件初始化，此处为自动生成
function UIComPageNum:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComPageNum:InitUIEvent()
    self:AddUIEvent(self.view.Btn_Right.OnClicked, "on_Btn_Right_Clicked")
    self:AddUIEvent(self.view.Btn_Left.OnClicked, "on_Btn_Left_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComPageNum:InitUIView()
end

---组件刷新统一入口
function UIComPageNum:Refresh(cur, max, bNotify)
    self.max = max
    self:setValueInternal(cur, bNotify)
    self:UpdateView()
end

function UIComPageNum:UpdateView()
    self.view.Text_Number:SetText(string.format("%s/%s", self.curValue, self.max))
end

--- 此处为自动生成
function UIComPageNum:on_Btn_Right_Clicked()
    if self.curValue >= self.max then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MAX)
        return
    end
    local tempValue = self.curValue + 1
    self:setValueInternal(tempValue, true)
    self:UpdateView()
end

--- 此处为自动生成
function UIComPageNum:on_Btn_Left_Clicked()
    if self.curValue <= 1 then
        Game.ReminderManager:AddReminderById(Enum.EReminderTextData.PICK_CHEST_MIN)
        return
    end
    local tempValue = self.curValue - 1
    self:setValueInternal(tempValue, true)
    self:UpdateView()
end

function UIComPageNum:setValueInternal(value, bNotify)
    self.curValue = value
    if bNotify then
        self.onPageChange:Execute(value)
    end
end
return UIComPageNum
