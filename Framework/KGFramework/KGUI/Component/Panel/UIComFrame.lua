local UIComBackTitle = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComBackTitle")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComFrame : UIComponent
---@field view ComFrameBlueprint
local UIComFrame = DefineClass("UIComFrame", UIComponent)

UIComFrame.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComFrame:OnCreate()
    self:InitUIComponent()
    self:InitUIData()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComFrame:InitUIData()
    ---预关闭界面事件，可以在里面处理关闭前的逻辑检查，返回ture就会执行关闭false不执行
    ---@type LuaDelegate<fun():bool>
    self.onPreCloseEvent = self.WBP_ComBackTitleCom.onPreCloseEvent

    ---tips 按钮点击事件
    ---@type LuaDelegate<fun()>
    self.onTipClickEvent = self.WBP_ComBackTitleCom.onTipClickEvent
end

function UIComFrame:InitUIComponent()
    ---@type UIComBackTitle
    self.WBP_ComBackTitleCom = self:CreateComponent(self.view.WBP_ComBackTitle, UIComBackTitle)
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComFrame:InitUIView()
    self:InitCurrencyWidget()
end

---组件刷新统一入口
function UIComFrame:Refresh(titleName, tipsId)
    self:SetTitleButtonCom(titleName, tipsId)
end

function UIComFrame:SetTitleButtonCom(titleName, tipsId)
    self.WBP_ComBackTitleCom:Refresh(titleName, tipsId)
end

function UIComFrame:InitCurrencyWidget()
    local moneyType = Game.NewUIManager:GetUIConfig(self.uid).moneyType
    if moneyType then
        self:OpenComponent(UICellConfig.UICurrentcyWidget, self.view.NS_Money, moneyType)
    end
end

function UIComFrame:GetTipsBtnGeometry()
    return self.WBP_ComBackTitleCom:GetTipsBtnGeometry()
end
return UIComFrame