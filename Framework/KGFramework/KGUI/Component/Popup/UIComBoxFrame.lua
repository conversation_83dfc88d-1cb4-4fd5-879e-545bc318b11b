local UIComMask = kg_require("Framework.KGFramework.KGUI.Component.BackGround.UIComMask")
local UIComButton = kg_require("Framework.KGFramework.KGUI.Component.Button.UIComButton")
local UIComDiyTitle = kg_require("Framework.KGFramework.KGUI.Component.Tools.UIComDiyTitle")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComBoxFrame : UIComponent
---@field view UIComBoxFrameBlueprint
local UIComBoxFrame = DefineClass("UIComBoxFrame", UIComponent)

UIComBoxFrame.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComBoxFrame:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComBoxFrame:InitUIData()
    ---预关闭界面事件，可以在里面处理关闭前的逻辑检查，返回ture就会执行关闭false不执行
    ---@type LuaDelegate<fun():bool>
    self.onPreCloseEvent = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function UIComBoxFrame:InitUIComponent()
    ---@type UIComMask
    self.WBP_ComMaskCom = self:CreateComponent(self.view.WBP_ComMask, UIComMask)
    ---@type UIComButton
    self.WBP_ComTitle_WBP_ComBtnCloseCom = self:CreateComponent(self.view.WBP_ComTitle.WBP_ComBtnClose, UIComButton)
    ---@type UIComDiyTitle
    self.WBP_ComTitle_WBP_ComFirstBigTextCom = self:CreateComponent(self.view.WBP_ComTitle.WBP_ComFirstBigText, UIComDiyTitle)
end

---UI事件在这里注册，此处为自动生成
function UIComBoxFrame:InitUIEvent()
    self:AddUIEvent(self.WBP_ComTitle_WBP_ComBtnCloseCom.onClickEvent, "on_WBP_ComTitle_WBP_ComBtnCloseCom_ClickEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComBoxFrame:InitUIView()
end

---组件刷新统一入口
function UIComBoxFrame:Refresh(title, hideClose)
    self.WBP_ComTitle_WBP_ComFirstBigTextCom:Refresh(title)
	self.WBP_ComTitle_WBP_ComBtnCloseCom:SetVisible(not hideClose)
end

--- 此处为自动生成
function UIComBoxFrame:on_WBP_ComTitle_WBP_ComBtnCloseCom_ClickEvent()
    if self.onPreCloseEvent:IsBind() then
        if self.onPreCloseEvent:Execute() then
            Game.NewUIManager:ClosePanel(self.uid)
        end
    else
        Game.NewUIManager:ClosePanel(self.uid)
    end
end

return UIComBoxFrame
