local UITreeItem = kg_require("Framework.KGFramework.KGUI.Component.UITreeView.UITreeItem")
---@class UIComAccordionListTab : UIComponent
---@field view UIComAccordionListTabBlueprint
local UIComAccordionListTab = DefineClass("UIComAccordionListTab", UITreeItem)

function UIComAccordionListTab:ctor()
    self.path = nil  -- 按规范定义路径变量
end

--- UI组件初始化，此处为自动生成
---@param data UITreeViewChildData
function UIComAccordionListTab:OnRefresh(data, otherInfo)
    local tabData = data.tabData
    self:SetName(tabData.name)
    self:SetDecorateName(tabData.decorateName)
    self:SetIcon(tabData.iconPath)
    self:SetBkg(tabData.bkgPath)
    self:SetExtraDesc(tabData.extraDesc)
    self:SetRedPoint(tabData.redPointId, tabData.redPointSuff)
    self:RefreshTabIsLeafNode()
end

function UIComAccordionListTab:SetRedPoint(redPointId, redPointSuff)
    if redPointId then
        Game.RedPointSystem:RegisterRedPoint(self:GetBelongPanel(), self.userWidget,redPointId, redPointSuff)
    end
end

function UIComAccordionListTab:SetName(name)
    if self.view.Text_Name then
        self.view.Text_Name:SetText(name or "")
    end
end

function UIComAccordionListTab:SetDecorateName(decorateName)
    if self.view.Text_Leon then
        self.view.Text_Leon:SetText(decorateName or "")
    end
end

function UIComAccordionListTab:SetExtraDesc(desc)
    if self.view.Text_ExtraDesc then
        self.view.Text_ExtraDesc:SetText(desc or "")
    end
end

function UIComAccordionListTab:SetIcon(iconPath)
    if self.view.Img_Icon and iconPath then
        self:SetImage(self.view.Img_Icon, iconPath)
    end
end

function UIComAccordionListTab:SetBkg(bkgPath)
    if self.view.Img_Bkg and bkgPath then
        self:SetImage(self.view.Img_Bkg, bkgPath)
    end
end

function UIComAccordionListTab:RefreshTabIsLeafNode()
    if self.userWidget.BP_SetArrow then
        self.userWidget:BP_SetArrow(self:HasChild())
    end
end

---@public
---设置数据
---@param index int 序号
function UIComAccordionListTab:RefreshInternal(data, otherInfo)
	self.data = data or {}
	self.otherInfo = otherInfo or self.otherInfo
	self:OnRefresh(data, otherInfo)
	if self:HasChild() then
		self:UpdateExpansionState(self:IsExpansion())
	else
		self:UpdateSelectionState(self:IsSelected())
	end
end

function UIComAccordionListTab:OnClickBtnInternal()
    if self:HasChild() then
        self.parentComponent:processListExpansionInternal(self.index, self.path)
    elseif self:IsFirstNode() then
        self.parentComponent:CollapseLastItem()
        self.parentComponent:processListSelectInternal(self.index, self.path)
    else
        self.parentComponent:processListSelectInternal(self.index, self.path)
    end
end

return UIComAccordionListTab
