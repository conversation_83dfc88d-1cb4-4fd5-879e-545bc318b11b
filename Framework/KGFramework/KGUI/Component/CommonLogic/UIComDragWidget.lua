local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

---@class UIComDragWidget : UIComponent
---@field view UIDragBlueprint
local UIComDragWidget = DefineClass("UIComDragWidget", UIComponent)

UIComDragWidget.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComDragWidget:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComDragWidget:InitUIData()
    ---按钮点击事件
    ---@type LuaDelegate<fun()>
    self.onClickEvent = LuaDelegate.new()
    ---按钮拖拽进入事件
    ---@type LuaDelegate<fun(myGeometry:table, inPointerEvent:table)>
    self.onDragEnterEvent = LuaDelegate.new()
    ---按钮拖拽离开事件
    ---@type LuaDelegate<fun(inDragDropOperation:table)>
    self.onDragLeaveEvent = LuaDelegate.new()
    
    ---@type Vector2D 前帧鼠标位置
    self._prevMousePos = nil
    ---@type Vector2D 原始节点位置
    self._prevPos = nil
    ---按钮拖拽取消事件
    ---@type LuaDelegate<fun(inPointerEvent:table, inDragDropOperation:table)>
    self.onDragCancelEvent = LuaDelegate.new()
    ---按钮拖拽检测事件
    ---@type LuaDelegate<fun(inPointerEvent:table, inDragDropOperation:table)>
    self.onDragDetectedEvent = LuaDelegate.new()
    ---按钮拖拽释放事件
    ---@type LuaDelegate<fun(inPointerEvent:table, inDragDropOperation:table)>
    self.onDragDropEvent = LuaDelegate.new()
    self._bDragSource = false
    ---@type UUserWidget
    self._sourceWidget = nil
    self._dragWidget = nil
end

--- UI组件初始化，此处为自动生成
function UIComDragWidget:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComDragWidget:InitUIEvent()
    self:AddUIEvent(self.widget.OnClicked, "onKGHotAreaClicked")
    self:AddUIEvent(self.widget.OnDragDetectedEvent, "onKGHotAreaDragDetectedEvent")
    self:AddUIEvent(self.widget.OnDragEnterEvent, "onKGHotAreaDragEnterEvent")
    self:AddUIEvent(self.widget.OnDragLeaveEvent, "onKGHotAreaDragLeaveEvent")
    self:AddUIEvent(self.widget.OnDragOverEvent, "onKGHotAreaDragOverEvent")
    self:AddUIEvent(self.widget.OnDropEvent, "onKGHotAreaDropEvent")
    self:AddUIEvent(self.widget.OnDragCancelEvent, "onKGHotAreaDragCancelEvent")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComDragWidget:InitUIView()
end

---面板打开的时候触发
function UIComDragWidget:Refresh()
end

---@param widget UUserWidget 原始拖拽的节点
---@param bDragSource boolean 是否托拽原始节点
function UIComDragWidget:SetDragSource(widget, bDragSource)
    self._bDragSource = bDragSource
    self._sourceWidget = widget
    if not self._dragWidget then
        self._dragWidget =  UE.UIFunctionLibrary.InstanceBP(widget)
    end
    self.widget:SetDragSourceWidget(self._dragWidget)
end

function UIComDragWidget:GetDragWidget()
    return self._dragWidget
end

---@param inPointerEvent FPointerEvent
function UIComDragWidget:updateSourceWidgetPostion(inPointerEvent)
    local mousePos =  UE.KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
    local Delta =  mousePos - self._prevMousePos
    local ViewportScale = UE.WidgetLayoutLibrary.GetViewportScale(_G.GetContextObject())
    local TargetPos = self._prevPos + Delta/ViewportScale
    self._sourceWidget.Slot:SetPosition(TargetPos)
end

--- 此处为自动生成
function UIComDragWidget:onKGHotAreaClicked()
    self.onClickEvent:Execute()
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inPointerEvent FPointerEvent
function UIComDragWidget:onKGHotAreaDragDetectedEvent(myGeometry, inPointerEvent)
    if self._bDragSource then
        self._prevPos =  self._sourceWidget.Slot:GetPosition()
        self.sourceWidgetVisubility = self._sourceWidget.Visibility
        self._sourceWidget:SetVisibility(UE.ESlateVisibility.Collapsed)
        self._prevMousePos =  UE.KismetInputLibrary.PointerEvent_GetScreenSpacePosition(inPointerEvent)
    end
    self.onDragDetectedEvent:Execute(myGeometry, inPointerEvent)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inDragDropOperation UDragDropOperation
function UIComDragWidget:onKGHotAreaDragEnterEvent(myGeometry, inDragDropOperation)
    self.onDragEnterEvent:Execute(myGeometry, inDragDropOperation)
end

--- 此处为自动生成
---@param inDragDropOperation UDragDropOperation
function UIComDragWidget:onKGHotAreaDragLeaveEvent(inDragDropOperation)
    self.onDragLeaveEvent:Execute(inDragDropOperation)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inDragDropOperation UDragDropOperation

function UIComDragWidget:onKGHotAreaDragOverEvent(myGeometry, inDragDropOperation)
end

--- 此处为自动生成
---@param myGeometry FGeometry
---@param inDragDropOperation UDragDropOperation
function UIComDragWidget:onKGHotAreaDropEvent(myGeometry, inDragDropOperation)
    self.onDragDropEvent:Execute(myGeometry, inDragDropOperation)
end

--- 此处为自动生成
---@param inPointerEvent FPointerEvent
---@param inDragDropOperation UDragDropOperation
function UIComDragWidget:onKGHotAreaDragCancelEvent(inPointerEvent, inDragDropOperation)
    if self._bDragSource then
        self._sourceWidget:SetVisibility(self.sourceWidgetVisubility)
        self:updateSourceWidgetPostion(inPointerEvent)
    end
    self.onDragCancelEvent:Execute(inPointerEvent, inDragDropOperation)
end

