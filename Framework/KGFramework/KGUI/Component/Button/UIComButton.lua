local ESlateVisibility = import("ESlateVisibility")
local LuaDelegate = kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")

---@class UIComButton : UIComponent
---@field view UIComButtonBlueprint
local UIComButton = DefineClass("UIComButton", UIListItem)

UIComButton.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComButton:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComButton:InitUIData()
    ---按钮点击事件
    ---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onClickEvent = LuaDelegate.new()
    ---按钮右击事件
    ---@type LuaDelegate<fun()>
    self.onRightClickedEvent = self.view.Btn_ClickArea.OnRightClicked
    ---按钮按下事件
    ---@type LuaDelegate<fun()>
    self.onPressedEvent = self.view.Btn_ClickArea.OnPressed
    ---按钮抬起事件
    ---@type LuaDelegate<fun()>
    self.onReleasedEvent = self.view.Btn_ClickArea.OnReleased
    ---按钮Hover事件
    ---@type LuaDelegate<fun()>
    self.onHoveredEvent = self.view.Btn_ClickArea.OnHovered
    ---按钮UnHover事件
    ---@type LuaDelegate<fun()>
    self.onUnhoveredEvent = self.view.Btn_ClickArea.OnUnhovered
    ---按钮长按下事件
    ---@type LuaDelegate<fun()>
    self.onLongPressedEvent = self.view.Btn_ClickArea.OnLongPressed
    ---按钮长按抬起事件
    ---@type LuaDelegate<fun()>
    self.onLongReleasedEvent = self.view.Btn_ClickArea.OnLongReleased
end

function UIComButton:dtor()
    self.onRightClickedEvent = nil
    self.onPressedEvent = nil
    self.onReleasedEvent = nil
    self.onHoveredEvent = nil
    self.onUnhoveredEvent = nil
end

--- UI组件初始化，此处为自动生成
function UIComButton:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComButton:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComButton:InitUIView()
end

---组件刷新统一入口
---@param name string 按钮名称
---@param iconPath string 按钮图标
function UIComButton:Refresh(name, iconPath)
    self:SetName(name)
    self:SetIcon(iconPath)
end

function UIComButton:OnRefresh(data)
    if data then
        self:Refresh(data.name, data.icon)
    end
end

function UIComButton:SetName(name)
    if self.view.Text_Name and name then
        self.view.Text_Name:SetText(name)
    end
end

function UIComButton:SetIcon(iconPath)
    if self.view.Img_Icon and iconPath then
        self:SetImage(self.view.Img_Icon, iconPath)
    end
end

function UIComButton:SetItemIcon(itemId)
    local iconPath = Game.UIIconUtils.GetIconByItemId(itemId)
    if self.view.Img_Currency and iconPath then
        self:SetImage(self.view.Img_Currency, iconPath)
    end
end

function UIComButton:SetExtraText(text)
    if self.view.Text_Time and text then
        self.view.Text_Time:SetText(text)
        self.view.Text_Time:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function UIComButton:SetType(type)
    self.userWidget:BP_SetType(type)
end

function UIComButton:SetDisable(disable)
    self.userWidget:BP_SetDisable(disable)
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_Clicked()
    self.onClickEvent:Execute()
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_RightClicked()
    self.onRightClickedEvent:Execute()
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_Pressed()
    self.onPressedEvent:Execute()
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_Released()
    self.onReleasedEvent:Execute()
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_Hovered()
    self.onHoveredEvent:Execute()
end

--- 此处为自动生成
function UIComButton:on_Btn_ClickArea_Unhovered()
    self.onUnhoveredEvent:Execute()
end

return UIComButton
