local UIListView = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListView")
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local UIUtil = kg_require("Framework.Utils.UIUtil")
---@class UIComDropDown : UIComponent
---@field view UIPullDownListBlueprint
local UIComDropDown = DefineClass("UIComDropDown", UIComponent)

---@public NewOptionData 
---@param name string
---@return UITabData
function UIComDropDown.NewOptionData(name)
	local data = {name = name}
	return data
end

---@private 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComDropDown:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---@private 初始化数据
function UIComDropDown:InitUIData()
	self.smartPositionArea = self.view.KGSmartPositioningArea
    ---@type UITabData
    self.curSelectData = nil --当前选择的数据
	self.optionsData = {}	--选项列表数据
	self.curSelectIndex = nil --当前选择的Index
	self.bOptionShow = false	--当前选项列表是否打开
	---@public 监听选项Item点击事件
    ---@type LuaDelegate<fun(index:number,data:UITabData)>
    self.onItemSelected = LuaDelegate.new()
    ---@public 选项检查
    ---@type LuaDelegate<fun(index:number):bool>
    self.onCheckItemCanSelect = LuaDelegate.new()
end

---@private UI组件初始化，此处为自动生成
function UIComDropDown:InitUIComponent()
    ---@type UIListView
    self.KGListViewCom = self:CreateComponent(self.view.KGListView, UIListView)
end

---@private  UI事件在这里注册，此处为自动生成
function UIComDropDown:InitUIEvent()
    self:AddUIEvent(self.view.Btn_ClickArea.OnClicked, "on_Btn_ClickArea_Clicked")
    self:AddUIEvent(self.KGListViewCom.onItemSelected, "on_KGListViewCom_ItemSelected")
    self:AddUIEvent(self.KGListViewCom.onCheckItemCanSelect, "on_KGListViewCom_CheckItemCanSelect")
end

---@private 初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComDropDown:InitUIView()
	self:setArrowState(false)
end

function UIComDropDown:OnOpen()
	Game.UIInputProcessorManager:BindMouseButtonUpEvent(self, "OnMouseButtonUp")
end

function UIComDropDown:OnClose()
	self.view.Canvas_Content:AddChildToCanvas(self.smartPositionArea)
	Game.UIInputProcessorManager:UnBindMouseButtonUpEvent(self)
end

function UIComDropDown:OnDestroy()
	self.smartPositionArea = nil
end
---@public Refresh 刷新数据
---@param optionsData UITabData
---@param index number 默认选中第几个（不填默认为1，-1表示不选中）
---@param bNotify boolean 是否广播事件
function UIComDropDown:Refresh(optionsData, index, bNotify)
	self.optionsData = optionsData
	self:SetSelectedIndex(index)
    if index ~= -1 and bNotify then
        self.onItemSelected:Execute(index, self.curSelectData)
    end
end

---@public SetSelectedIndex 设置选中
---@param index number? 选中第几个（不填默认为1，-1表示不选中）
function UIComDropDown:SetSelectedIndex(index)
	if index == -1 then
		self.curSelectData, self.curSelectIndex = nil, nil
	else
		index = index or 1
		self.curSelectData = self.optionsData[index]
		self.curSelectIndex = index
	end
	self:refreshOptionBtn()
end

function UIComDropDown:reverseDropDownBtn()
    self.bOptionShow = not self.bOptionShow
	self:setArrowState(self.bOptionShow)
	self:updateOptionBoxOrder()
	if self.bOptionShow then
		self:refreshOptionsList()
	else
		self.KGListViewCom:Clear()
	end
end

function UIComDropDown:refreshOptionBtn()
	if self.view.Img_Icon and self.curSelectData and string.notNilOrEmpty(self.curSelectData.iconPath) then
		self:SetImage(self.view.Img_Icon, self.curSelectData.iconPath)
	end
	if self.view.Text_Content then
		self.view.Text_Content:SetText((self.curSelectData and self.curSelectData.name) and self.curSelectData.name or "")
	end
end

---@private refreshOptionsList 刷新选项列表UI
function UIComDropDown:refreshOptionsList()
	self.KGListViewCom:Refresh(self.optionsData)
	if self.curSelectIndex then
		self.KGListViewCom:SetSelectedItemByIndex(self.curSelectIndex, true, true)
	end
end

---@private setArrowState 设置标题箭头方向
---@param bOpen boolean
function UIComDropDown:setArrowState(bOpen)
	self.userWidget:BP_SetFoldExpand(bOpen)
end

---@private updateOptionBoxPos 设置下拉列表位置
function UIComDropDown:updateOptionBoxOrder()
	if self.bOptionShow then
		Game.NewUIManager:AddPanelToRoot(self.smartPositionArea, true)
		self.smartPositionArea.Slot:SetZOrder(self:getParentOrder() + 1)
		local NewAnchors = UE.Anchors()
		NewAnchors.Minimum = FVector2D(0, 0)
		NewAnchors.Maximum = FVector2D(0, 0)
		self.smartPositionArea.Slot:SetAnchors(NewAnchors)
		self.smartPositionArea.Slot:SetAlignment(FVector2D(0, 0))
		UIUtil.AdaptRelatedUIPos(self.view.SB_Content, self.smartPositionArea)
	else
		self.view.Canvas_Content:AddChildToCanvas(self.smartPositionArea)
	end
end

---@private getParentOrder
function UIComDropDown:getParentOrder()
	local panel = self:GetBelongPanel()
	return panel:GetCanvasOrder()
end

--- 此处为自动生成
function UIComDropDown:on_Btn_ClickArea_Clicked()
    self:reverseDropDownBtn()
end

--- 此处为自动生成
---@param index number
---@param data table
function UIComDropDown:on_KGListViewCom_ItemSelected(index, data)
	self.curSelectIndex = index
    self.curSelectData = data
    self:refreshOptionBtn()
    self:reverseDropDownBtn()
	self.onItemSelected:Execute(index, data)
end

---@param mouseEvent MouseEvent
function UIComDropDown:OnMouseButtonUp(mouseEvent)
	local screenPos = UE.KismetInputLibrary.PointerEvent_GetScreenSpacePosition(mouseEvent)
	local rect = {left = 0, top = 0, right = 0, bottom = 0}
	local left, right, top, bottom = UE.UIFunctionLibrary.GetRenderingBoundingRect(self.view.KGListView)
    rect.left = left
    rect.right = right
    rect.top = top
    rect.bottom = bottom
	left, right, top, bottom = UE.UIFunctionLibrary.GetRenderingBoundingRect(self.smartPositionArea)
	rect.left = math.min(left, rect.left)
    rect.right = math.max(right, rect.right)
    rect.top = math.min(top, rect.top)
    rect.bottom = math.max(bottom, rect.bottom)
	
	local isInRect = self:IsInRect(screenPos.X, screenPos.Y, rect)
	if isInRect or self.bOptionShow then
		Game.TabClose:IgnoreNextMouseUpEvent("UIComDropDown:OnMouseButtonUp")
	end
	if self:IsValidRect(rect) and not isInRect then
		if self.bOptionShow then
			self:reverseDropDownBtn()
		end
	end
	return false
end

---@private
---@param rect {left:number, top:number, right:number, bottom:number}
---@return boolean
function UIComDropDown:IsValidRect(rect)   
    return rect and rect.left < rect.right and rect.top < rect.bottom
end

---@privte
---@param ptX number
---@param ptY number
---@param rect {left:number, top:number, right:number, bottom:number}
function UIComDropDown:IsInRect(ptX, ptY, rect)
    return rect.left <= ptX and ptX <= rect.right and rect.top <= ptY and ptY <= rect.bottom
end

--- 此处为自动生成
---@param index number
---@return bool
function UIComDropDown:on_KGListViewCom_CheckItemCanSelect(index)
	if self.onCheckItemCanSelect:IsBind() then
		local canSelect = self.onCheckItemCanSelect:Execute(index)
		if not canSelect then
			self:reverseDropDownBtn()
		end
		return canSelect
	else
		return true
	end
end

return UIComDropDown
