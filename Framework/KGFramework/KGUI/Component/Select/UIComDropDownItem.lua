local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
---@class UIComDropDownItem : UIListItem
---@field view UIPullDownListItemBlueprint
local UIComDropDownItem = DefineClass("UIComDropDownItem", UIListItem)

--- UI组件初始化，此处为自动生成
---@param data UITabData
function UIComDropDownItem:OnRefresh(data, otherInfo)
	self:SetName(data.name)
	self:SetIcon(data.iconPath)
end

function UIComDropDownItem:SetName(name)
    if self.view.Text_Content then
        self.view.Text_Content:SetText(name or "")
    end
end

function UIComDropDownItem:SetIcon(iconPath)
    if self.view.Img_Icon and string.notNilOrEmpty(iconPath) then
        self:SetImage(self.view.Img_Icon, iconPath)
    end
end

return UIComDropDownItem
