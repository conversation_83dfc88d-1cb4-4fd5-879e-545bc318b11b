local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
local ESelectionMode = import("ESelectionMode")

---@class UIIrregularListView : UIComponent
---@field widget UKGIrregularListView
local UIIrregularListView = DefineClass("UIIrregularListView", UIComponent)

--region Public

---获取列表原始数据
---@return table
function UIIrregularListView:GetData()
    return self._datas 
end

---@public
---通过序号获取Item的UI组件
---@param index int 序号
---@return UIListItem
function UIIrregularListView:GetItemByIndex(index)
    local item = self._itemShowIndexMap[index]
    return item
end

---@public
---通过key获取Item的UI组件，遍历当前存在的所有UI组件，返回第一个数据的key字段值为value的UI组件。
---
---例如：列表数据为 {{id=1001,name="apple"},{id=1002,name="banana"},{id=1005,name="orange"}}。那么key可以为"id"，此时value可以是1001、1002、1005。
---@param key string 字段名
---@param value any 值
---@return UIListItem
function UIIrregularListView:GetItemByKey(key, value)
    for k, v in pairs(self._itemShowIndexMap) do
        if v.data[key] == value then
            return v
        end
    end
    return nil
end

---@public
---使用新的数据刷新列表
---@param _datas table 列表原始数据
---@param index? int 从列表第几个下标开始刷新（默认值：1。注意Lua数组起始位是1）
---@param otherInfo? table | nil 列表里每个Item的补充数据，解决列表数据不全的问题（默认值：nil）
function UIIrregularListView:Refresh(datas, index, otherInfo)
    table.clear(self._curSelectIndexs)
    index = index or 1
    local oldListData = self._datas
    self._datas = datas
    local oldCount = oldListData == nil and 0 or #oldListData
    local newCount = datas == nil and 0 or #datas
    if oldCount ~= newCount or true then
        local placeholderItems = import("UIFunctionLibrary").GetListObject(self.listView, newCount)
        self.listView:SetListItems(placeholderItems)
        placeholderItems:Clear()
    else
        self.listView:RequestRefresh()
    end
    self.listView:ScrollToIndex(index - 1, 0)
end

---@public
---使用旧的数据刷新列表
function UIIrregularListView:RefreshItems()
    for k, item in pairs(self._itemShowIndexMap) do
        item:OnRefresh(self._datas[k])
    end
end

---@public
---新增数据数量（只能是在数组后面追加）
function UIIrregularListView:NotifyAddData(count)
    Log.Error("not implemented error!")
end

---@public
---@param index int 序号（lua数组起始为1）
---插入数据（每次一个数据）
function UIIrregularListView:NotifyInsertData(index)
    Log.Error("not implemented error!")
end

---@public
---@param index int 序号（lua数组起始为1）
---删除数据（每次一个数据）
function UIIrregularListView:NotifyRemoveData(index)
    Log.Error("not implemented error!")
end

---@public
---通过序号触发Item的刷新
---@param index int 序号
function UIIrregularListView:RefreshItemByIndex(index)
    local item = self:GetItemByIndex(index)
    local data = self:GetChildData(index)
    if item and data then
        item:RefreshInternal(data, self._otherInfo)
    end
end

---@public
---通过Item的字段名和值匹配触发Item的刷新（匹配方式参考`GetItemByKey`的描述）
---@param key string 字段名
---@param value any 值
---@param data any 数据
function UIIrregularListView:RefreshItemByKey(key, value, data)
    local item = self:GetItemByKey(key, value)
    if item and data then
        item:RefreshInternal(data, self._otherInfo)
    end
end

---@public
---获取选中的序号
---@return int
function UIIrregularListView:GetChildData(index)
    return self._datas[index]
end

---@public
---清空列表数据
function UIIrregularListView:Clear()
    self._datas = nil
    self._otherInfo = nil
    local placeholderItems = import("UIFunctionLibrary").GetListObject(self.listView, 0)
    self.listView:SetListItems(placeholderItems)
    placeholderItems:Clear()
end

---@public
---跳转到指定序号
---@param index int 指定序号（注意Lua数组起始位是1）
function UIIrregularListView:ScrollToItemByIndex(index)
    index = index or 1
    self.listView:ScrollToIndex(index - 1)
end

---@public
---通过序号触发Item选中
---@param index int 指定序号
---@param selected boolean 是否选择
---@param bNotNotify boolean|nil 是否禁止通知回调
function UIIrregularListView:SetSelectedItemByIndex(index, selected, bNotNotify)
	self._bForbitNotify = not not bNotNotify
    self.listView:SetItemSelection(self:getObjectByCppIndex(index - 1), selected)
	self._bForbitNotify = false
end

---@public
---通过序号触发Item选中
---@param indexs number[] 指定序号
---@param selected boolean 是否选择
---@param bNotNotify boolean|nil 是否禁止通知回调
function UIIrregularListView:SetSelectedItemByIndexs(indexs, selected, bNotNotify)
    self._bForbitNotify = not not bNotNotify
    for _, index in ipairs(indexs) do
		self.listView:SetItemSelection(self:getObjectByCppIndex(index - 1), selected)
    end
    self._bForbitNotify = false
end

---@public
---设置选择模式
---@param mode ESelectionMode
function UIIrregularListView:SetSelectionMode(mode)
    self.listView:SetSelectionMode(mode)
end

---@public
---获取当前选择模式
function UIIrregularListView:GetSelectionMode()
    return self.listView:GetSelectionMode()
end

---@public
---设置点击CD，单位毫秒（只影响手动点击）
---@param clickCD number
function UIIrregularListView:SetClickCD(clickCD)
	self._clickCD = clickCD
end

---@public
---清空全部选中
function UIIrregularListView:ClearSelection()
    table.clear(self._curSelectIndexs)
    self.listView:ClearSelection()
end

---@public
---获取单选的当前序号
---@return int
function UIIrregularListView:GetSelectedItemIndex()
    local _, value = next(self._curSelectIndexs)
    return value
end

---@public
---获取选中的序号集合
---@return table<int, bool> 其中Key为序号，Value恒定为true
function UIIrregularListView:GetSelectedItemIndexes()
    return self._curSelectIndexs
end

---@public
---获取选中的item数量
---@return int
function UIIrregularListView:GetSelectedItemNum()
	return table.count(self._curSelectIndexs)
end

---@public
---判断指定序号的Item是否被选中
---@param index int 序号
---@return bool
function UIIrregularListView:IsSelectedByIndex(index)
    return table.contains(self._curSelectIndexs, index)
end

---@public
---获取当前UI显示出来的最后一个Item的索引
---@return int
function UIIrregularListView:GetBottomIndex()
    local maxIndex = 1
    for index, _ in pairs(self._itemShowIndexMap) do
        maxIndex = math.max(maxIndex, index)
    end
    return maxIndex
end

function UIIrregularListView:NodeIsLastChild(index)
    return index == #self._datas
end

--endregion

--region Private

---@private
function UIIrregularListView:OnCreate()
    self:ReDefineWidget()
    self:InitUIData()
    self:InitUIEvent()
    self:InitUIView()
end

function UIIrregularListView:ReDefineWidget()
    self.listView = self.widget
end

---@private
function UIIrregularListView:InitUIData()
    ---@private
    ---@type table<int, UIListItem>
    self._itemShowIndexMap = {}

    ---@private
    ---@type table<UWidget, UIListItem>
    self._itemShowWidgetMap = {}

    ---@private
    ---@type table<UWidget, UIListItem>
    self._localCache = {}

    ---@private
    ---@type table<UWidget, UIListItem>
    self._itemComponentLookup = {}

    ---@private
    ---@type any
    self._datas = nil

    ---@private
    ---@type table<int>
    self._curSelectIndexs = {}

	---@private
	---点击CD，默认为0，无CD
	self._clickCD = 0
	---@private
	---最新一次点击时间
	self._latestClickTime = 0

	---@private
	self._bForbitNotify = false
    self:InitExtraEvent()
end

function UIIrregularListView:InitExtraEvent()

    ---监听Item选中状态变化回调
    ---@type LuaMulticastDelegate<fun(index:number, selected:bool)>
    self.onItemSelectionChanged = LuaMulticastDelegate.new()

    ---监听Item选中
    ---@type LuaMulticastDelegate<fun(index:number, data:table)>
    self.onItemSelected = LuaMulticastDelegate.new()

    ---监听Item点击事件
    ---@type LuaMulticastDelegate<fun(index:number, data:table)>
    self.onItemClicked = LuaMulticastDelegate.new()
    ---@public 选项检查
    ---@type LuaDelegate<fun(index:number):bool>
    self.onCheckItemCanSelect = LuaDelegate.new()
end

---@private
function UIIrregularListView:dtor()
end

---@private
function UIIrregularListView:InitUIEvent()
    self:AddUIEvent(self.listView.EntryWidgetPool.OnCustomizeGetOrCreateInstance, "onCustomizeGetOrCreateInstance")
    self:AddUIEvent(self.listView.EntryWidgetPool.OnCustomizeReleaseInstance, "onCustomizeReleaseInstance")
    self:AddUIEvent(self.listView.OnItemSelectionChanged, "onItemSelectionChangedInternal")
    self:AddUIEvent(self.listView.OnEntryInitialized, "onEntryInitialized")
    self:AddUIEvent(self.listView.OnEntryReleased, "onEntryReleased")
end

---@private
function UIIrregularListView:getCppIndexByObject(object)
    for index, v in pairs(self.listView.ListItems) do
        if v == object then
            return index
        end
    end
    return -1
end

---@private
function UIIrregularListView:getObjectByCppIndex(index)
    return self.listView.ListItems:Get(index)
end

---@private
function UIIrregularListView:onItemSelectionChangedInternal(object, bSelect)
    local index = self:getCppIndexByObject(object)
    index = index + 1
    local item = self:GetItemByIndex(index)
    if item then
        item:UpdateSelectionState(bSelect)
    end
    if bSelect then
        self._curSelectIndexs[#self._curSelectIndexs + 1] = index
    else
        table.removeItem(self._curSelectIndexs, index)
    end
    if self._bForbitNotify then
        return
    end
    self.onItemSelectionChanged:Broadcast(index, bSelect)
    if bSelect then
        self.onItemSelected:Broadcast(index, self._datas[index])
    end
end

---@private
function UIIrregularListView:onEntryInitialized(object, userWidget)
    local index = self:getCppIndexByObject(object)
    index = index + 1
    local item = self:addItem(index, userWidget)
    if item and self._datas[index] then
        item:SetIndex(index)
        item:RefreshInternal(self._datas[index], self._otherInfo)
    end
end

---@private
function UIIrregularListView:onEntryReleased(userWidget)
    --- 该函数的执行时序晚于onCustomizeReleaseInstance，而且在列表父节点RemoveFromParent时子项直接被忽略，不会走这里的回调。
    self:releasedItem(userWidget)
end

---@private
function UIIrregularListView:createInstance(widgetType)
    return UE.WidgetBlueprintLibrary.Create(_G.GetContextObject(), widgetType)
end

---@private
function UIIrregularListView:onCustomizeGetOrCreateInstance(widgetType)
    for userWidget, itemComponent in pairs(self._localCache) do
        if userWidget:GetClass() == widgetType then
            Log.DebugFormat("UIFrame: onCustomizeGetOrCreateInstance reuse %s", userWidget)
            return userWidget
        end
    end
    local userWidget = self:createInstance(widgetType)
    Log.DebugFormat("UIFrame: onCustomizeGetOrCreateInstance create %s", userWidget)
    return userWidget
end

---@private
function UIIrregularListView:onCustomizeReleaseInstance(userWidget)
    Log.DebugFormat("UIFrame: onCustomizeReleaseInstance %s", userWidget)
    self:releasedItem(userWidget)
    return true
end

---@private
function UIIrregularListView:addItem(index, userWidget)
    local class = nil
    -- if self.onGetEntryLuaClass:IsBind() then
    --     class = self.onGetEntryLuaClass:Execute(index)
    -- end
    local item = self:getItemFromPool(userWidget, class)
    self._itemShowIndexMap[index] = item
    self._itemShowWidgetMap[userWidget] = item
    return item
end

---@private
function UIIrregularListView:releasedItem(userWidget)
    local item = self._itemShowWidgetMap[userWidget]
    if not item then
        return
    end
    item:OnReleased()
    item:Hide()
    self._itemShowWidgetMap[userWidget] = nil
    if self._itemShowIndexMap[item.index] then 
        if self._itemShowIndexMap[item.index].userWidget == userWidget then
            self._itemShowIndexMap[item.index] = nil
        end
    end
    self._localCache[userWidget] = self._itemComponentLookup[userWidget]
end

---@private
function UIIrregularListView:getItemFromPool(userWidget, class)
    local item = self._localCache[userWidget]
    if item then
        item:Show()
        self._localCache[userWidget] = nil
    else
        item = self:CreateComponent(userWidget, class)
        if item then
            item:InitDefaultClickEvent()
            item:InitDefaultButtonClickMethod(UE.EButtonClickMethod.PreciseClick)
            item:UpdateObjectNum(userWidget.UObjectNum)
            self._itemComponentLookup[userWidget] = item
        end
    end
    return item
end

function UIIrregularListView:processListSelectInternal(index)
    local selected = self:IsSelectedByIndex(index)
    local curMode = self:GetSelectionMode()
    if curMode == ESelectionMode.Single then
        if not selected then
            self:SetSelectedItemByIndex(index, not selected)
        end
    elseif curMode == ESelectionMode.SingleToggle then
		self:SetSelectedItemByIndex(index, not selected)
	elseif curMode == ESelectionMode.Multi then
		local selectedNum = self:GetSelectedItemNum()
		if not selected and self._curMultiModeMaxSelectionNum>0 and selectedNum >= self._curMultiModeMaxSelectionNum then
			if self._curMultiModeMaxSelectionMode == Enum.EListMultiModeMaxSelectionMode.CantSelect then
				return
			elseif self._curMultiModeMaxSelectionMode == Enum.EListMultiModeMaxSelectionMode.CancelEarliestSelect then
				self:SetSelectedItemByIndex(self._curSelectIndexs[1], false)
			end
		end

		self:SetSelectedItemByIndex(index, not selected)
    end
end

---@private
function UIIrregularListView:processListClickInternal(index, data)
    self.onItemClicked:Broadcast(index, data)
end

function UIIrregularListView:CanSelectItem(index)
    if self.onCheckItemCanSelect:IsBind() then
        if index then
            return self.onCheckItemCanSelect:Execute(index)
        end
    end
    return true
end

---@private method 刷新最新一次点击时间
function UIIrregularListView:refreshLatestClickTime()
	self._latestClickTime = _G._now()
end

---@private method 是否在点击CD中
function UIIrregularListView:isInClickCD()
	if self._clickCD <= 0 then
		return false
	end

	if self._latestClickTime == 0 then
		return false
	end

	if (_G._now() - self._latestClickTime) >= self._clickCD then
		return false
	end

	return true
end

--endregion

return UIIrregularListView