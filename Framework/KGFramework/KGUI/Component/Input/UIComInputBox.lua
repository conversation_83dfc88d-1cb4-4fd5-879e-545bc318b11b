local StringConst = require "Data.Config.StringConst.StringConst"
local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")
---@class UIComInputBox : UIComponent
---@field view UIComInputBoxBlueprint
local UIComInputBox = DefineClass("UIComInputBox", UIComponent)

UIComInputBox.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComInputBox:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComInputBox:InitUIData()
    self.textLimit = 0 --限制输入文本数量
    ---输入文本超过限制事件
    ---@type LuaDelegate<fun()>
    self.onLimitOver = LuaDelegate.new()

    ---输入文本发生变化事件
    ---@type LuaDelegate<fun(text:string)>
    self.onTextChanged = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function UIComInputBox:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function UIComInputBox:InitUIEvent()
    if self.view.Btn_Clear then
        self:AddUIEvent(self.view.Btn_Clear.OnClicked, "on_Btn_Clear_Clicked")
    end
    self:AddUIEvent(self.view.EditText_Input.OnTextLimitOver, "on_EditText_Input_TextLimitOver")
    self:AddUIEvent(self.view.EditText_Input.OnTextChanged, "on_EditText_Input_TextChanged")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComInputBox:InitUIView()
end

function UIComInputBox:updateClearBtnVisible()
    if not self.view.Canvas_Clear then return end
    local content = self.view.EditText_Input:GetText()
    self:SetWidgetVisible(self.view.Canvas_Clear, string.notNilOrEmpty(content))
end

---组件刷新统一入口
function UIComInputBox:Refresh(text, hintText, limitNum, bNotifyTextChanged)
    self:SetHintText(hintText)
    self:SetText(text, bNotifyTextChanged)
    self:SetLimitLength(limitNum)
    self:updateClearBtnVisible()
end

function UIComInputBox:SetEnable(bEnable)
    self.view.EditText_Input:SetIsEnabled(bEnable)
    if not bEnable and self.view.Canvas_Clear then
        self:SetWidgetVisible(self.view.Canvas_Clear, false)
    end
end

function UIComInputBox:GetText()
    return self.view.EditText_Input:GetText()
end

function UIComInputBox:SetLimitLength(num)
    self.textLimit = num or 0
    self.view.EditText_Input:SetLimitLength(num)
    self:updateLimtTextVisible()
end

function UIComInputBox:SetHintText(text)
    self.view.EditText_Input:SetHintText(text or "")
end

function UIComInputBox:SetText(text, bNotifyTextChanged)
    self.view.EditText_Input:SetText(text or "")
    if bNotifyTextChanged then  
        self:on_EditText_Input_TextChanged(text or "")
    end
end

function UIComInputBox:updateLimtTextVisible()
    if self.view.Text_NumberLimit then
        self.view.Text_NumberLimit:SetText(string.format(StringConst.Get("EDITOR_TEXT_REMAINDER_LENGTH"), self.textLimit - self.view.EditText_Input:GetRemainderLimitLength(), self.textLimit))
        self:SetWidgetVisible(self.view.Text_NumberLimit, self.textLimit > 0)
    end
end

--- 此处为自动生成
function UIComInputBox:on_EditText_Input_TextLimitOver()
    self.onLimitOver:Execute()
end

--- 此处为自动生成
---@param text FText
function UIComInputBox:on_EditText_Input_TextChanged(text)
    self:updateClearBtnVisible()
    self.onTextChanged:Execute(text)
    if self.textLimit > 0 and self.view.Text_NumberLimit then
        self.view.Text_NumberLimit:SetText(string.format(StringConst.Get("EDITOR_TEXT_REMAINDER_LENGTH"), self.textLimit - self.view.EditText_Input:GetRemainderLimitLength(), self.textLimit))
    end
end

--- 此处为自动生成
function UIComInputBox:on_Btn_Clear_Clicked()
    self:SetText("")
    self:on_EditText_Input_TextChanged("")
end

return UIComInputBox
