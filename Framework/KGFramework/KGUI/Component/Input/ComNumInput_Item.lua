local UIListItem = kg_require("Framework.KGFramework.KGUI.Component.UIListView.UIListItem")
local StringConst = require "Data.Config.StringConst.StringConst"
local EUMGSequencePlayMode = import("EUMGSequencePlayMode")
---@class ComNumInput_Item : UIListItem
---@field view ComNumInput_ItemBlueprint
local ComNumInput_Item = DefineClass("ComNumInput_Item", UIListItem)

ComNumInput_Item.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function ComNumInput_Item:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function ComNumInput_Item:InitUIData()
	self.number = nil  ---这个块对应的数字
end

--- UI组件初始化，此处为自动生成
function ComNumInput_Item:InitUIComponent()
end

---UI事件在这里注册，此处为自动生成
function ComNumInput_Item:InitUIEvent()
    self:AddUIEvent(self.view.Button.OnClicked, "on_Button_Clicked")
    self:AddUIEvent(self.view.Button.OnHovered, "on_Button_Hovered")
    self:AddUIEvent(self.view.Button.OnUnhovered, "on_Button_Unhovered")
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function ComNumInput_Item:InitUIView()
end

---面板打开的时候触发
---@param number number 这个块对应的数字 -2表示回退键(←) -1表示确认键（√） 0-9表示数字键
function ComNumInput_Item:OnRefresh(number)
	self.number = number
	if number == -2 then
		self.view.TextKey:SetText(StringConst.Get("MAIL_DELETE"))
	elseif number == -1 then
		self.view.TextKey:SetText(StringConst.Get("CONFIRM"))
	else
		self.view.TextKey:SetText(number)
	end
end

--- 此处为自动生成
function ComNumInput_Item:on_Button_Clicked()
	self:PlayAnimation(self.userWidget.Ani_Press, nil, self.userWidget)
	if self.number == -2 then
		self:GetBelongPanel():OnClickBackButton()
	elseif self.number == -1 then
		self:GetBelongPanel():OnClickOKButton()
	else
		self:GetBelongPanel():OnClickNumberBtn(self.number)
	end
end

--- 此处为自动生成
function ComNumInput_Item:on_Button_Hovered()
	self:PlayAnimation(self.userWidget.Ani_Hover, nil, self.userWidget)
end

--- 此处为自动生成
function ComNumInput_Item:on_Button_Unhovered()
	self:PlayAnimation(self.userWidget.Ani_Hover, nil, self.userWidget, 0, 1, EUMGSequencePlayMode.Reverse)
end

return ComNumInput_Item
