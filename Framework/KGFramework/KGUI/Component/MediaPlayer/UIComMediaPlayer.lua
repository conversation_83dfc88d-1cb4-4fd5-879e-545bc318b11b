local UIComponent = kg_require("Framework.KGFramework.KGUI.Core.UIComponent")

local UIFunctionLibrary = import("UIFunctionLibrary")

---@class UIComMediaPlayer : UIComponent
---@field view UIComMediaPlayerBlueprint
local UIComMediaPlayer = DefineClass("UIComMediaPlayer", UIComponent)

UIComMediaPlayer.eventBindMap = {
}
--- 面板或者组件第一次创建的时候会触发，整个生命周期触发一次
function UIComMediaPlayer:OnCreate()
    self:InitUIData()
    self:InitUIComponent()
    self:InitUIEvent()
    self:InitUIView()
end

---初始化数据
function UIComMediaPlayer:InitUIData()
     ---@type string 音频事件名
    self.akEventName = ""
    ---@type number 音频播放标识
    self.playingID = 0
    ---@type function 视频结束的回调
    --self.onMediaReachedEnd = nil
	
	---@type LuaDelegate<fun()>AutoBoundWidgetEvent
    self.onMediaReachedEnd = LuaDelegate.new()
end

--- UI组件初始化，此处为自动生成
function UIComMediaPlayer:InitUIComponent()
    self:AddUIEvent(self.view.BinkMediaPlayer.OnMediaReachedEnd, "on_BinkMediaPlayer_OnMediaReachedEnd")
end

---UI事件在这里注册，此处为自动生成
function UIComMediaPlayer:InitUIEvent()
end

---初始化UI基础逻辑，这里尽量避免出现和数据相关的业务逻辑调用
function UIComMediaPlayer:InitUIView()
end

---组件刷新统一入口
---@param path string 视频路径
---@param bLooping boolean 是否循环播放
---@param reachedEndFunc function 视频结束的回调
function UIComMediaPlayer:Refresh(path, bLooping, reachedEndFunc)
    if path then
		self.path = path
        self.view.BinkMediaPlayer:OpenUrl(path)
    end
    if bLooping then
        self.view.BinkMediaPlayer:SetLooping(bLooping)
    end
    if reachedEndFunc then
        self.onMediaReachedEnd = reachedEndFunc
    end
end

---@private 设置视频结束的回调
function UIComMediaPlayer:SetMediaReachedEndCallBack(func)
    self.onMediaReachedEnd = func
end

---@private 清除视频结束的回调
function UIComMediaPlayer:ClearMediaReachedEndCallBack()
    self.onMediaReachedEnd = nil
end

---@private 视频结束
function UIComMediaPlayer:on_BinkMediaPlayer_OnMediaReachedEnd()
    if self.onMediaReachedEnd then
        xpcall(self.onMediaReachedEnd, _G.CallBackError)
    end

	self.onMediaReachedEnd:Execute()

    -- 暂停播放音频
	self:PauseAudio()
end

---@private 设置图片的可见性
---@param visibility ESlateVisibility 可见性
function UIComMediaPlayer:SetImageVisibility(visibility)
    self.view.MediaImage:SetVisibility(visibility)
end

---@private 打开视频
---@param path string 视频路径
function UIComMediaPlayer:OpenUrl(path)
	self.path = path
    self.view.BinkMediaPlayer:OpenUrl(path)
	self:SetAudio()
	--self:SetAudio("Play_UI_Card_Draw")
end

---@private 关闭视频
function UIComMediaPlayer:CloseUrl()
    self.view.BinkMediaPlayer:CloseUrl()
end

---@private 播放视频
function UIComMediaPlayer:Play()
    self.view.BinkMediaPlayer:Play()

    -- 同步播放音频
	self:RewindAudio()
end

---@private 暂停视频
function UIComMediaPlayer:Pause()
    self.view.BinkMediaPlayer:Pause()
	-- 同步音频
	self:PauseAudio()
end

---@private 停止视频
function UIComMediaPlayer:StopMedia()
	self.view.BinkMediaPlayer:Stop()
end

---@private 倒退视频
function UIComMediaPlayer:Rewind()
    self.view.BinkMediaPlayer:Rewind()
	self:RewindAudio()
end

---@private 按时间倒退视频
---@param time number 时间
function UIComMediaPlayer:SeekByTime(time)
	--Log.DebugFormat("Seek BMK goto time: ".. time)
	self.view.BinkMediaPlayer:SeekByTime(time*1000)

	-- 同步播放音频
	self:SeekAudio(time)
	if not self.view.BinkMediaPlayer:IsPaused() then
		self:ResumeAudio()
	end
end

---@private 按百分比倒退视频
---@param percent number 百分比
function UIComMediaPlayer:SeekByPercent(percent)
	--Log.DebugFormat("Seek BMK goto percent: ".. percent)
	self.view.BinkMediaPlayer:SeekByPercent(percent)

	-- 同步播放音频
	self:SeekAudio(percent * self:GetDuration())
	if not self.view.BinkMediaPlayer:IsPaused() then
		self:ResumeAudio()
	end
end

---@private 设置循环播放
---@param bLooping boolean 是否循环播放
function UIComMediaPlayer:SetLooping(bLooping)
    self.view.BinkMediaPlayer:SetLooping(bLooping)
end

---@private 设置音量
---@param rate number 音量
function UIComMediaPlayer:SetVolume(rate)
    self.view.BinkMediaPlayer:SetVolume(rate)
end

---@private 设置播放速度
---@param rate number 播放速度
function UIComMediaPlayer:SetRate(rate)
    self.view.BinkMediaPlayer:SetRate(rate)
end

---@private 获取视频时长
function UIComMediaPlayer:GetDuration()
	return UIFunctionLibrary.GetFTimespanTotalSeconds(self.view.BinkMediaPlayer:GetDuration())
end

---@private 获取视频当前播放时间
function UIComMediaPlayer:GetTime()
	return UIFunctionLibrary.GetFTimespanTotalSeconds(self.view.BinkMediaPlayer:GetTime())
end

---@private 设置音频事件名
---@param eventName string 音频事件名
function UIComMediaPlayer:SetAudio(eventName)
	if eventName then
		self.akEventName = eventName
		return
	end

	-- 拼接事件名
	local arr = string.split(self.path, "/")
	local assetName = arr[#arr]
	arr = string.split(assetName, ".")
	if (arr[1] ~= nil) and (arr[1] ~= "") then
		self.akEventName = "Play_" .. arr[1]
	end
end

---@private 重置音频
function UIComMediaPlayer:RewindAudio()
	if self.playingID == 0 then
		if self.akEventName ~= "" then
			Log.DebugFormat("[Play] media with audio %s", self.akEventName)
			if Game.me then
				self.playingID = Game.me:AkPostEventOnActor(self.akEventName)
			else
				self.playingID = Game.AkAudioManager:PostEvent2D(self.akEventName)
			end
		end
	else
		local curTime = self:GetTime()
		if curTime <= 0 or curTime >= self:GetDuration() then
			self:SeekAudio(0)
		end
		if not self.view.BinkMediaPlayer:IsPaused() then
			self:ResumeAudio()
		end
	end
end

---@private 停止音频
function UIComMediaPlayer:StopAudio()
	if self.playingID ~= 0 then
		--if Game.me then
		--	Game.me:AkStopEvent(self.playingID)
		--else
		Game.AkAudioManager:StopEvent(self.playingID)
		--end
		self.playingID = 0
	end
end

---@private 按时间倒退音频
---@param time number 时间
function UIComMediaPlayer:SeekAudio(time)
	if self.akEventName ~= "" and self.playingID ~= 0 then
		-- 处理一下音频和视频长度不同的问题
		local audioDuration = Game.AkAudioManager:GetEventDuration(self.akEventName)
		local audioPercent = time / audioDuration
		Log.DebugFormat("[Play] seek audio %s goto percent %f", self.akEventName, audioPercent)
        Game.me:AkSeekOnEvent(self.akEventName, audioPercent, self.playingID)
	end
end

---@private 暂停音频
function UIComMediaPlayer:PauseAudio()
	if self.playingID ~= 0 then
		Game.AkAudioManager:PauseEvent(self.playingID)
	end
end

---@private 恢复音频
function UIComMediaPlayer:ResumeAudio()
	if self.playingID ~= 0 then
		Game.AkAudioManager:ResumeEvent(self.playingID)
		return
	end
end

---@private 停止所有
function UIComMediaPlayer:StopAll()
	self:StopMedia()
	self:StopAudio()
end

---@private 关闭
function UIComMediaPlayer:OnClose()
	self:StopAll()
end

return UIComMediaPlayer
