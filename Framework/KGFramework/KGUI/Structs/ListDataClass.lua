---@class UITabData
---@field public name string  名称
---@field public decorateName string  装饰用的名字
---@field public uiType int UI样式
---@field public subTitle int 副标题样式
---@field public iconPath string 图标路径
---@field public bkgPath string 背景路径
---@field public extraDesc string 扩展文本
---@field public isLock boolean 是否解锁
---@field public isEnable boolean 是否激活
---@field public otherInfo any 附加信息
---@field public redPointId string 红点Id
---@field public redPointSuff string 红点的后缀
---@field public onLockCallback function lock时点击回调
---@field public selectedIconPath string 选中的图标路径

---@class UITreeViewChildData
---@field tabData table
---@field children UITreeViewChildData[]


---@class MsgBoxCurrencyInfo messagebox用的显示道具类信息(也可以是非道具类)
---@field public currencyId number 货币id
---@field public num number 数量

---@class MessageBoxInfo
---@field public ui string 弹窗类型
---@field public messageId number
---@field public title string 标题
---@field public content string 内容 正文
---@field public content2 string 内容2 下面文字
---@field public leftBtnText string 左边按钮文本
---@field public rightBtnText string 右边按钮文本
---@field public leftBtnEvent function 左边按钮事件
---@field public rightBtnEvent function 右边按钮事件 
---@field public onCloseEvent function 关闭事件
---@field public onClickCloseEvent function 点击关闭的事件
---@field public checkName string 勾选框文本
---@field public showCheck boolean 是否显示勾选框
---@field public bCheckBox boolean 勾选框值
---@field public checkBoxEvent function 勾选框事件
---@field public leftBtnTimer number 左边按钮倒计时
---@field public rightBtnTimer number 右边按钮倒计时
---@field public item ItemRewardNewParam 展示目标物品
---@field public currencyInfos MsgBoxCurrencyInfo[] 货币列表
---@field public inputParam MessageBoxInputParam 输入框参数
