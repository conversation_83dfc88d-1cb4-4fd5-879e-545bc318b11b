local timerAdapter = require("Framework.KGFramework.KGCore.TimerManager.TimerAdapter")
local GameplayStatics = import("GameplayStatics")

---@class TimerBase: LuaClass
local TimerBase = DefineClass("TimerBase")


function TimerBase:ctor()
	-- 暂时还需要规避define的一些时序问题，在构造的时候判断当前是否有client sdk api支持
	self._hasClientSdk = timerAdapter.HasClientSdk()
end


function TimerBase:dtor()
	self:DelAllTimer()
end


---@param delay number timer周期，单位秒
---@param invokeNum integer 1 表示一次性timer，-1表示无限循环的timer，>0 表示回调固定次数的timer
---@param callback function | string 函数或者方法名字，如果传入的是方法名字，那么表示回调对象方法 self:callback(...)，传入的是函数的话则回调callback(...)
---最后还可以传入一些回调参数，最多5个，会在timer回调的时候作为callback的参数
---note: entity销毁的时候会将其挂起的所有的timer都销毁
function TimerBase:AddTimer(delay, invokeNum, callback, ...)
	local isRepeat = (invokeNum ~= 1)
	if self._hasClientSdk then
		return _script.addTimerWithInvokeNum(delay, isRepeat, invokeNum, self, true, callback, ...)
	else
		return timerAdapter.PureLuaAddTimerWithInvokeNum(delay, isRepeat, invokeNum, self, true, callback, ...)
	end
end


--- 基于game time的timer，AddTimer使用的是绝对时间来判断是否触发，
--- game time则会剔除游戏暂停影响，例如第0秒的时候挂起了一个3秒的timer，然后游戏暂停了2秒，那么其最终会在第5秒的时候回调
function TimerBase:AddGameTimeTimer(delay, invokeNum, callback, ...)
	local isRepeat = (invokeNum ~= 1)
	if self._hasClientSdk then
		-- TODO(fanjinsong) 经过讨论，觉得只保留绝对时间的即可，先暂时把这个干了，看看反馈
		return _script.addTimerWithInvokeNum(delay, isRepeat, invokeNum, self, true, callback, ...)
		
		--local gameTime = GameplayStatics.GetTimeSeconds(Game.WorldContext)
		--return _script.addGameTimeTimerWithInvokeNum(gameTime, delay, isRepeat, invokeNum, self, true, callback, ...)
	else
		return timerAdapter.PureLuaAddGameTimeTimerWithInvokeNum(delay, isRepeat, invokeNum, self, true, callback, ...)
	end
end



---@param timerId integer AddTimer方法返回的id
function TimerBase:DelTimer(timerId)
	if timerId == nil then
		return
	end
	if self._hasClientSdk then
		_script.delTimer(timerId)
	else
		timerAdapter.PureLuaDelTimer(timerId)
	end
end


---一次性删除当前对象挂起的所有timer实例
function TimerBase:DelAllTimer()
	if self._hasClientSdk then
		_script.delOwnerAllTimer(self)
	end
end


return TimerBase
	