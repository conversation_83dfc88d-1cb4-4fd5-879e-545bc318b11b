<root>
    <Properties>
	</Properties>

    <Implements>
        <Interface> Service </Interface>
    </Implements>

    <ServerMethods>     
        <!-- 进入单人匹配 -->
        <SSReqTeamSingleMatch>
            <!--1 logicServerID -->
            <Arg>int</Arg>        
            <!--2 targetID -->
            <Arg>int</Arg>
            <!--3 AvatarInfo -->            
            <Arg>AvataTeamMatchInfo</Arg>              
        </SSReqTeamSingleMatch>
        <!-- 取消单人匹配 -->
        <SSReqTeamCancelSingleMatch>
            <!--1 logicServerID -->
            <Arg>int</Arg>        
            <!--2 targetID -->
            <Arg>int</Arg>
            <!--3 id -->            
            <Arg>string</Arg>
            <!--4 mailbox -->            
            <Arg>mailbox</Arg>                            
        </SSReqTeamCancelSingleMatch>  
        <!-- 进入队伍匹配 -->
        <SSReqTeamMatch>   
            <!--1 logicServerID -->
            <Arg>int</Arg>                  
            <!--2 teamInfo -->
            <Arg>TeamMatchInfo</Arg>     
        </SSReqTeamMatch>
        <!-- 退出队伍匹配 -->
        <SSReqTeamCancelMatch>
            <!--1 logicServerID -->
            <Arg>int</Arg>        
            <!--2 teamID --> 
            <Arg>TEAM_ID</Arg>     
            <!--3 targetID --> 
            <Arg>int</Arg>  
            <!--4 mailbox -->            
            <Arg>mailbox</Arg>  
            <!--5 ntfMailbox -->            
            <Arg>none</Arg>                                                     
        </SSReqTeamCancelMatch> 
        <!-- 更新队伍匹配信息 -->
        <SSReqTeamMatchUpdateInfo>
            <!--1 logicServerID -->
            <Arg>int</Arg>        
            <!--2 teamInfo -->
            <Arg>TeamMatchInfo</Arg>            
        </SSReqTeamMatchUpdateInfo> 
        <!-- 队伍匹配撮合结果确认 -->
        <SSReqTeamMatchResultComfirm>
            <!--1 logicServerID -->
            <Arg>int</Arg>            
            <!--2 teamID -->
            <Arg>TEAM_ID</Arg>
            <!--3 targetID -->
            <Arg>int</Arg>   
            <!--4 avatarID -->
            <Arg>string</Arg>
            <!--5 reEnterMatch -->
            <Arg>bool</Arg>                                    
            <!--6 avatarInfo -->
            <Arg>none</Arg>                                         
        </SSReqTeamMatchResultComfirm>
        <!-- 单人匹配信息更新 -->
        <SSReqTeamUpdateSingleMatchInfo>  
            <!--1 logicServerID -->
            <Arg>int</Arg>                    
            <!--2 targetID -->
            <Arg>int</Arg>            
            <!--3 avatarID -->
            <Arg>string</Arg>
            <!--4 zhanli -->
            <Arg>int</Arg>                                         
        </SSReqTeamUpdateSingleMatchInfo>
        <!-- 预确认队伍组队 -->
        <SSOnMsgTeamPreEnterResult>
            <!--1 logicServerID -->
            <Arg>int</Arg>           
            <!--2 result -->
            <Arg>bool</Arg>                  
            <!--3 teamID -->
            <Arg>TEAM_ID</Arg>
            <!--4 targetID -->
            <Arg>int</Arg>
            <!--5 avatarID -->
            <Arg>string</Arg>      
            <!--6 teamActorInfo -->
            <Arg>none</Arg>
        </SSOnMsgTeamPreEnterResult>   
        <!-- 打印队伍匹配池 -->
        <SSReqServerPrintMatchPool>
            <!--1 ListInt -->
            <Arg>ListInt</Arg>
            <!--2 logicServerID -->
            <Arg>int</Arg>
        </SSReqServerPrintMatchPool>            
    </ServerMethods>
</root>