<root>
    <Implements>
        <Interface> Service </Interface>
    </Implements>

    <Properties>

    </Properties>

    <ServerMethods>
        <CheckNeedReplaceTips>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> mailbox </Arg> <!--2 AccountMB -->
        </CheckNeedReplaceTips>
        <CheckAndSetAccount>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> mailbox </Arg> <!--2 AccountMB -->
            <Arg> string </Arg>  <!--3 Platform -->          
        </CheckAndSetAccount>
        <OnMsgReconnectTimeOut>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> string </Arg> <!--2 AvatarActorID -->
            <Arg> mailbox </Arg> <!--3 AccountMB -->
        </OnMsgReconnectTimeOut>
        <OnMsgAccoutClose>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> mailbox </Arg> <!--2 AccountMB -->
            <Arg> int </Arg> <!--3 LogoutCode -->
        </OnMsgAccoutClose>
        <CheckEnterGame>
            <Arg> string </Arg>  <!--1 AccountID -->
            <Arg> string </Arg>  <!--2 AvatarId -->            
            <Arg> mailbox </Arg> <!--3 AccountMB -->
        </CheckEnterGame>
        <CheckCreateRole>
            <Arg> string </Arg>  <!--1 AccountID -->
            <Arg> string </Arg>  <!--2 AvatarId -->            
        </CheckCreateRole>
        <AvatarConnectCheckAndUpdate>
            <Arg> mailbox </Arg> <!--1 AvatarMailbox -->            
            <Arg> bool </Arg> <!--2 bReconnect -->            
            <Arg> string </Arg> <!--3 logicID -->
        </AvatarConnectCheckAndUpdate>
        <CheckBackToSelectRoleUI>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> mailbox </Arg> <!--1 AccountMB -->                  
            <Arg> string </Arg> <!--3 LogOutAvartar -->
        </CheckBackToSelectRoleUI>
        <CheckBackToSelectRoleUIDone>
            <Arg> string </Arg> <!--1 AccountID -->
            <Arg> mailbox </Arg> <!--1 AccountMB -->                  
            <Arg> string </Arg> <!--3 LogOutAvartar -->
        </CheckBackToSelectRoleUIDone>
        <NotifyAvatarLogoutEnd>
            <Arg> string </Arg> <!--1 accountID -->            
            <Arg> string </Arg> <!--2 avatarID -->            
            <Arg> string </Arg> <!--3 processID -->            
            <Arg> bool </Arg> <!--4 bProcessRemoved -->            
            <Arg> bool </Arg><!--5 bServiceRemoved -->
        </NotifyAvatarLogoutEnd>
        <OnMsgChangeAvatarConnectState>           
            <Arg> string </Arg> <!--1 AvatarID -->                        
            <Arg> bool </Arg> <!--2 bConnect -->            
        </OnMsgChangeAvatarConnectState>    

        <SOnMsgUploadOnlineInfo>
            <Arg> mailbox </Arg>
        </SOnMsgUploadOnlineInfo>

        <SOnMsgUploadOnlineInfoAfterShutDown>
            <Arg> mailbox </Arg>
            <Arg> mailbox </Arg>
        </SOnMsgUploadOnlineInfoAfterShutDown>

        <SRegisterOnlineInfo>
            <Arg> string </Arg>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
        </SRegisterOnlineInfo> 

        <SOnMsgOnlineServiceCloseSuccess>
            <Arg> int </Arg>
        </SOnMsgOnlineServiceCloseSuccess>   

        <NotifyAvatarLogoutStart>
            <!--1 accountID -->
            <Arg> string </Arg>
            <!--2 avatarID -->
            <Arg> string </Arg>
            <!--3 bFromService -->
            <Arg> bool </Arg>
            <!--4 bProcessRemoved -->
            <Arg> bool </Arg>
            <!--5 bServiceRemoved -->
            <Arg> bool </Arg>
        </NotifyAvatarLogoutStart>
        <ClearLogin>
            <!--1 accountID -->
            <Arg> string </Arg>
        </ClearLogin>

        <ReqStartMigrate>
            <!--1 avatarID -->
            <Arg> string </Arg>
            <!--2 migrateSeqNo -->
            <Arg> int </Arg>
        </ReqStartMigrate>

        <ReqMigrateFinish>
            <!--1 accountID -->
            <Arg> string </Arg>
            <!--2 avatarID -->
            <Arg> string </Arg>
            <!--3 tarProcessID -->
            <Arg> string </Arg>
            <!--4 migrateSeqNo -->
            <Arg> int </Arg>
        </ReqMigrateFinish>

        <ReqMigrateFail>
            <!--1 avatarID -->
            <Arg> string </Arg>
            <!--2 curProcessID -->
            <Arg> string </Arg>
            <!--3 migrateSeqNo -->
            <Arg> int </Arg>
        </ReqMigrateFail>

        <OnReloadOnlineInfo>
             <!--1 logicProcessId -->
            <Arg> string </Arg>
            <!--2 AvatarIDs -->
            <Arg> AvatarIDs </Arg>
        </OnReloadOnlineInfo>
    
        <TestOnlineUserCount>
            <!--1 callback mailbox -->
            <Arg> mailbox </Arg>
            <!--2 callback uuid -->
            <Arg> int </Arg>
            <!--3 requestMethod -->
            <Arg> none </Arg>
            <!--4 params -->
            <Arg> none </Arg>
        </TestOnlineUserCount>
        <OnlineAvatarBroadcastCall>
            <!-- rpcName -->
            <Arg> string </Arg>
            <!-- args -->
            <Arg> VList </Arg>
        </OnlineAvatarBroadcastCall>
        <OnlineAvatarBroadcastSync>
            <!-- rpcName -->
            <Arg> string </Arg>
            <!-- args -->
            <Arg> VList </Arg>
        </OnlineAvatarBroadcastSync>

        <SSKDIPReqQueryServerNumber>
            <Arg> string </Arg>
            <Arg> int </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> none </Arg>
        </SSKDIPReqQueryServerNumber>

        <ReqServerGenerateShortUid>
            <!--1 Mailbox -->
            <Arg> mailbox </Arg>
        </ReqServerGenerateShortUid>

        <SSReqGMFindOnlinePlayer>
            <!--1 count-->
            <Arg> int </Arg>
        </SSReqGMFindOnlinePlayer>
        
        <InstantCMD>
            <Arg> bool </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> METHOD_ARGS </Arg>
        </InstantCMD>
        
        <AsyncInstantCMD>
            <Arg> bool </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> METHOD_ARGS </Arg>
        </AsyncInstantCMD>
    </ServerMethods>
</root>