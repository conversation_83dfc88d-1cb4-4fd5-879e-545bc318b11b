<?xml version="1.0" ?>
<root>
	<Properties>
		<planeID Type="int" Flags="OWN_CLIENT" Default="0"  Persistent="false" />
		<!-- 当前位面对应的任务ID -->
		<planeTaskID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="false" />
	</Properties>
	<Implements/>
    <ClientMethods>
		<OnNotifyMissionGroupChange>
			<!-- PlayerID -->
			<Arg> string </Arg>
			<!-- MissionGroupType -->
			<Arg> int </Arg>
			<!-- MissionGroupOwner -->
			<Arg> string </Arg>
		</OnNotifyMissionGroupChange>

	</ClientMethods>
	<ServerMethods>
		<OnMsgPlaneSpaceReady>
			<!-- error code -->
			<Arg> int </Arg>
			<!-- plane space mailbox -->
			<Arg> mailbox </Arg>
			<!-- exParam -->
            <Arg> none </Arg>
		</OnMsgPlaneSpaceReady>
	</ServerMethods>
</root>