<?xml version="1.0" ?>
<root>
    <Properties>
		<SpaceRemainTime Type="float" Flags="ALL_CLIENTS" Default="-1.0" Persistent="false"/>
		<SpaceMechanismID Type="float" Flags="ALL_CLIENTS" Default="-1.0"  Persistent="false"/>
		<SpaceRageInfoDict Type="dict" Key ="string" Value="SPACE_REMAIN_TIME_DEFINE" Flags="ALL_CLIENTS" Default="{}" Persistent="false"/>
	</Properties>

	<Implements>
	</Implements>

	<ClientMethods>
	</ClientMethods>

    <ServerMethods>
    </ServerMethods>
</root>
