<?xml version="1.0" ?>
<root>
    <Properties>
        <!-- Pvp 匹配相关 -->
        <PvpMatchInfo Type="PVPCurrentMatchInfo" Flags="OWN_CLIENT" /> <!-- 当前匹配目标-->
        <MatchCurrentMatchType Type="int" Flags="OWN_CLIENT" Default="0" />
        <PvpNewMatchInfo Type="PVP_TYPE_MATCH_INFO" Flags="OWN_CLIENT" Persistent="false"/> <!-- 多匹配目标，后续替代当前目标 -->
        <bGmWarmMatch Type="bool" Default="false"/> <!-- 是否是GM开启福利局 -->
        
        <!--Pve 匹配相关 --> 
        <isInTeamMatch Type="int" Flags="OWN_CLIENT" Default="0"/>        
        <teamMatchType Type="int" Flags="SERVER_ONLY" Default="0"/> <!-- 没走team广播流程 -->        
        <isInSingleMatch Type="int" Flags="OWN_CLIENT" Default="0"/>        
        <singleMatchInfoList Type="AvatarActorSingleMatchInfoList" Flags="OWN_CLIENT"/> <!-- 多目标匹配信息 -->
        <singleMatchPendingList Type="DictIntInt" Flags="SERVER_ONLY"/> <!-- 在途目标匹配，用于全部取消 -->
        <singleMatchResultInfo Type="AvataSingleMatchResultInfo" Flags="SERVER_ONLY"/> <!-- 进队状态锁 -->
	</Properties>
	<Implements/>
	<ClientMethods>
        <RetPVPMatch>
            <!--1 result -->
            <Arg> Result </Arg>
        </RetPVPMatch>
        <RetCancelPVPMatch>
            <!--1 result -->
            <Arg> Result </Arg>
        </RetCancelPVPMatch>
        <OnMsgConfirmStateChange>
            <!--1 bCancle -->
            <Arg> bool </Arg>
            <!--1 bFirst -->
            <Arg> bool </Arg>
            <!--3 ConfirmInfoList -->
            <Arg> ConfirmInfoList </Arg>
        </OnMsgConfirmStateChange>
        <OnMsgSyncMatchResult>
            <!--1 matchType -->
            <Arg> int </Arg>
            <!--2 status -->
            <Arg> int </Arg>
            <!--3 confirmEndTime -->
            <Arg> int </Arg>            
            <!--4 MatchedResult -->
            <Arg> PVP_MATCH_BATTLE_INFO </Arg>
        </OnMsgSyncMatchResult>  
        <OnMsgMatchConfirmStateChange>
            <!--1 camp -->
            <Arg> int </Arg>
            <!--2 memberId -->
            <Arg> string </Arg>
            <!--3 prepared -->
            <Arg> bool </Arg>
        </OnMsgMatchConfirmStateChange>
        <OnMsgAutoStartNextPVPMatchCancel>  <!-- 取消自动开始下一场匹配 -->
            <Arg> int </Arg>        <!--1 MatchType -->
        </OnMsgAutoStartNextPVPMatchCancel>
	</ClientMethods>
    <!-- Exposed 待处理 -->
	<ServerMethods>
        <ReqStartPVPMatch> <Exposed/>
            <!--1 MatchType -->
            <Arg> int </Arg>
        </ReqStartPVPMatch>
        <ReqCancelPVPMatch> <Exposed/>
            <!--1 MatchType -->
            <Arg> int </Arg>
        </ReqCancelPVPMatch>
        <ReqConfirmEnter> <Exposed/>
            <!--1 bAgree -->
            <Arg> bool </Arg>
        </ReqConfirmEnter>

        <OnMsgServerStartPVPMatchRet>
            <!--1 ret -->
            <Arg> int </Arg>        
            <!--2 matchType -->
            <Arg> int </Arg>
            <!--3 matchStatus -->
            <Arg> int </Arg>             
        </OnMsgServerStartPVPMatchRet>        
        <OnMsgServerSyncPvpMatchInfo>
            <!--1 matchType -->
            <Arg> int </Arg>
            <!--2 status -->
            <Arg> int </Arg>
            <!--3 campID -->
            <Arg> int </Arg>
            <!--4 confirmEndTime -->
            <Arg> int </Arg>                                    
            <!--5 MatchedResult -->
            <Arg> PVP_MATCH_BATTLE_INFO </Arg>
        </OnMsgServerSyncPvpMatchInfo> 
        <SSOnMsgPvpConfirmStateChange>
            <!--1 CampID -->
            <Arg> int </Arg>                
            <!--2 AvatarID -->
            <Arg> string </Arg>        
            <!--3 bAgree -->
            <Arg> bool </Arg>
        </SSOnMsgPvpConfirmStateChange>

        <SSOnMsgStartTeamPvpBattle>     <!-- 开始组队战斗通知 -->
            <Arg> PVP_MATCH_BATTLE_INFO </Arg>      <!--1 MatchedResult -->
        </SSOnMsgStartTeamPvpBattle>

        <SSOnMsgStartGroupPvpBattle>     <!-- 开始团队战斗通知 -->
            <Arg> PVP_MATCH_BATTLE_INFO </Arg>      <!--1 MatchedResult -->
        </SSOnMsgStartGroupPvpBattle>

        <SSOnMsgCancelPvpMatch>     <!-- 取消匹配通知 -->
            <Arg> int </Arg>            <!--1 matchType -->
            <Arg> string </Arg>         <!--2 matchID -->
            <Arg> DictStrBool </Arg>       <!--3 rematch teams or single player -->
        </SSOnMsgCancelPvpMatch>

        <SSOnMsgPvpMatchStateChange>
            <!--1 id -->
            <Arg> string </Arg>          
            <!--2 MatchType -->
            <Arg> int </Arg>          
            <!--3 MatchState -->
            <Arg> int </Arg>
            <!--3 MatchTime -->
            <Arg> int </Arg>            
        </SSOnMsgPvpMatchStateChange>
        <SSOnMsgConfirmPvpBattle>
            <!--1 id -->
            <Arg> string </Arg>         
            <!--2 MatchType -->
            <Arg> int </Arg>          
            <!--3 matchStatus -->
            <Arg> int </Arg>
            <!--4 ConfirmEndTime -->
            <Arg> int </Arg>                    
            <!--5 PVP_MATCH_RESULT -->
            <Arg> PVP_MATCH_RESULT </Arg>
            <!--6 delayPrepareTime -->
            <Arg> ENTITY_ID_FLOAT </Arg>
        </SSOnMsgConfirmPvpBattle>
        <SSOnMsgCancelMatchSanction>   <!-- 取消匹配惩罚 -->
            <Arg> int </Arg>    <!--1 matchType -->
        </SSOnMsgCancelMatchSanction>
        <ReqCancelStartNextPVPMatch> <Exposed/>   <!-- 取消自动开始下一场匹配 -->
            <Arg> int </Arg>    <!--1 MatchType -->
        </ReqCancelStartNextPVPMatch>

        <SSOnTeamMemberCancelStartNextPVPMatch>  <!-- 队友取消自动开始下一场匹配 -->
            <Arg> int </Arg>    <!--1 MatchType -->
        </SSOnTeamMemberCancelStartNextPVPMatch>
	</ServerMethods>
</root>
