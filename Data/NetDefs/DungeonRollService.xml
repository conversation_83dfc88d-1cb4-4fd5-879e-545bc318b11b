<root>
    <Implements>
        <Interface> Service </Interface>
    </Implements>

    <Properties>
	</Properties>
    <ServerMethods>
        <ReqServerDungeonRollRoll>
            <!--1 RollIDs -->
            <Arg> DungeonRollRollIDs </Arg>
            <!--2 AvatarID -->
            <Arg> string </Arg>
        </ReqServerDungeonRollRoll>

        <!-- 运营GM Start -->
        <ReqServerDungeonRollSetPoint>
            <!--1 RollID -->
            <Arg> string </Arg>
            <!--2 point -->
            <Arg> int </Arg>
            <!--3 AvatarID -->
            <Arg> string </Arg>
        </ReqServerDungeonRollSetPoint>
        <!-- 运营GM End -->

        <SOnMsgNotifyRoll>
            <!--1 RollID -->
            <Arg> string </Arg>
            <!--2 GoodsInfos -->
            <Arg> DungeonGoodsInfos </Arg>
            <!--3 Bidders  -->
            <Arg> DungeonRollBidders </Arg>         
            <!--4 SpaceMb  -->
            <Arg> mailbox </Arg>          
        </SOnMsgNotifyRoll>

        <RetHandleDungeonRollResult>
            <!--1 RollID -->
            <Arg> string </Arg>
            <!--2 AvatarID -->
            <Arg> string </Arg>
            <!--3 GoodsIDs-->
            <Arg> DictStrStr </Arg>
        </RetHandleDungeonRollResult>

        <ReqServerUpdateDungeonRollMbs>
            <!--1 RollIDs -->
            <Arg> RollIDs </Arg>
            <!--2 AvatarMb -->
            <Arg> mailbox </Arg>      
        </ReqServerUpdateDungeonRollMbs>

    </ServerMethods>
</root>