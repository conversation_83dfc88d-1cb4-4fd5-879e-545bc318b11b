<?xml version="1.0" ?>
<root>
	<Properties>
		<CurSpeed Type="int"   Flags="ALL_CLIENTS" Default="0.0" BriefProp="true"/>
		<bJump Type="BOOL" Persistent="false"/>   <!--跳跃状态-->
		<SpeedUsePosture Type="int" Default="-1"/>
		<bListenTeleportEnd Type="BOOL" Flags="OWN_CLIENT" Default="false" />
	</Properties>

	<Implements/>
	<ClientMethods>
		<RetMoveNew>
			<Arg> int </Arg> <!--1 result -->
			<Arg> Vector3 </Arg><!--2 CorrectPos -->
		</RetMoveNew>

		<OnMsgMoveUpdateBroadCast>
			<!--1 predictMoveParams -->
			<Arg> PredictMoveParams </Arg>
            <!--2 specialMoveType -->
            <Arg> int </Arg>
		</OnMsgMoveUpdateBroadCast>
		<OnMsgMoveBroadCastNew>
			<Arg> Vector3 </Arg><!--1 CurPos -->
            <Arg> int </Arg><!--2 TimeStamp -->
			<Arg> int </Arg><!--3 SyncInfos -->
		</OnMsgMoveBroadCastNew>

		<OnMsgBriefMoveNotify>
			<!--1 pos -->
			<Arg> Vector3 </Arg>
			<!--2 syncInfo -->
			<Arg> int </Arg>
			<!--3 TimeStamp -->
			<Arg> int </Arg>
		</OnMsgBriefMoveNotify>

		<OnTeleportInternal>
			<!--1 X -->
            <Arg> float </Arg>
            <!--2 Y -->
            <Arg> float </Arg>
            <!--3 Z -->
            <Arg> float </Arg>
            <!--4 Yaw -->
            <Arg> float </Arg>
            <!--5 TeleportType -->
            <Arg> int </Arg>
            <!--6 Reason -->
            <Arg> int </Arg>
            <!--7 Sequence -->
            <Arg> int </Arg>
			<!--8 Source-->
			<Arg> int </Arg>
		</OnTeleportInternal>

		<OnMsgTeleport>
			<Arg> int </Arg>    <!--1 TeleportType-->			
			<Arg> Vector3 </Arg><!--2 Pos -->
			<Arg> float </Arg>  <!--3 Rotation Yaw-->
		</OnMsgTeleport>
		<OnMsgSetPosition>
			<!--1 reason -->
			<Arg> int </Arg>
			<!--2 pos -->
			<Arg> PVector3 </Arg>
			<!--3 rot -->
			<Arg> PVector3 </Arg>
		</OnMsgSetPosition>

		<OnMsgStartRootMotion>
			<!--1 SyncSign -->
			<Arg> int </Arg>
			<!--2 Start -->
            <Arg> PVector3 </Arg>
			<!--3 Target -->
            <Arg> PVector3 </Arg>
			<!--4 Duration -->
            <Arg> float </Arg>
			<!--5 StickGround -->
            <Arg> bool </Arg>
			<!--6 CurveGUID -->
			<Arg> int </Arg>
			<!--7 InScale -->
			<Arg> float </Arg>
		</OnMsgStartRootMotion>
		<OnMsgStartServerRootMotion><EnableNilArgs/>
			<!--1 SyncSign -->
			<Arg> int </Arg>
			<!--2 Start -->
            <Arg> PVector3 </Arg>
			<!--3 Target -->
            <Arg> PVector3 </Arg>
			<!--4 Duration -->
            <Arg> float </Arg>
			<!--5 MinS -->
            <Arg> float </Arg>
			<!--6 MaxS -->
            <Arg> float </Arg>
			<!--7 StickGround -->
            <Arg> bool </Arg>
			<!--8 CurveGUID -->
			<Arg> int </Arg>
			<!--9 InScale -->
			<Arg> float </Arg>
		</OnMsgStartServerRootMotion>
		<OnMsgFinishRootMotion>
			<!--1 SyncSign -->
			<Arg> int </Arg>
			<!--2 Reason -->
			<Arg> int </Arg>
		</OnMsgFinishRootMotion>
		<OnMsgStartServerPhysicFall>
			<!--1 EndPosition -->
            <Arg> PVector3 </Arg>
			<!--2 Duration -->
            <Arg> float </Arg>
			<!--3 Maxheight -->
			<Arg> float </Arg>
		</OnMsgStartServerPhysicFall>

		<OnMsgRebuildGravitationalField>
			<!-- GravitationalField List -->
			<Arg> GravitationFieldList </Arg>
		</OnMsgRebuildGravitationalField>
		<OnMsgEnterGravitationalField>
			<!-- GravitationalField Info -->
			<Arg> GravitationFieldInfo </Arg>
		</OnMsgEnterGravitationalField>
		<OnMsgLeaveGravitationalField>
			<!-- GravitationalFieldID -->
			<Arg> int </Arg>
		</OnMsgLeaveGravitationalField>

		<OnJumpToPoint>
			<!--1 X -->
            <Arg> float </Arg>
            <!--2 Y -->
            <Arg> float </Arg>
            <!--3 Z -->
            <Arg> float </Arg>
            <!--4 Yaw -->
            <Arg> float </Arg>
			<!--5 Duration -->
            <Arg> float </Arg>
			<!--6 Maxheight -->
			<Arg> float </Arg>
		</OnJumpToPoint>
	</ClientMethods>
	<ServerMethods>
		<TeleportInternalAck> <Exposed/>
			<!--1 Sequence -->
            <Arg> int </Arg>
		</TeleportInternalAck>
		
		<ReqMoveNew> <Exposed/>
			<Arg> Vector3 </Arg> <!--1 CurPos -->
			<Arg> int </Arg>     <!--2 TimeStamp -->
			<Arg> int </Arg>     <!--3 SyncInfos -->
		</ReqMoveNew>

		<ReqStartRootMotion> <Exposed/>
			<!--1 SyncSign -->
			<Arg> int </Arg>
			<!--2 Start -->
            <Arg> PVector3 </Arg>
			<!--3 Target -->
            <Arg> PVector3 </Arg>
			<!--4 Duration -->
            <Arg> float </Arg>
			<!--5 NeedRPC -->
            <Arg> bool </Arg>
			<!--6 StickGround -->
            <Arg> bool </Arg>
			<!--7 CurveGUID -->
			<Arg> int </Arg>
			<!--8 InScale -->
			<Arg> float </Arg>
		</ReqStartRootMotion>
		<ReqCancelRootMotion><Exposed/>
			<!--1 SyncSign -->
			<Arg> int </Arg>
		</ReqCancelRootMotion>

		<ClientNotifyMoveModeChanged> <Exposed/>
			<Arg> int </Arg> <!--1 NewMoveMode -->
		</ClientNotifyMoveModeChanged>

		<!-- Far类型传送触发loading，需要通知服务器loading结束 -->
		<ClientNotifySceneLoaded> <Exposed/>
			<!--1 Sequence -->
            <Arg> int </Arg>
		</ClientNotifySceneLoaded>
	</ServerMethods>
</root>
