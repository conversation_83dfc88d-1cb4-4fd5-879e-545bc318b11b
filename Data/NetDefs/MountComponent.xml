<?xml version="1.0" ?>
<root>
    <Properties>
		<MountState Type="int" Default="0" Flags="ALL_CLIENTS|GHOST" />
		<MountConfigID Type="int" Default="1" Flags="ALL_CLIENTS|GHOST" Persistent="true"/>
		<MountMaxSeatNum Type="int" Default="1" Flags="SERVER_ONLY" Persistent="true"/>
		<MountPassengerDict Type="DictIntInt" Flags="ALL_CLIENTS|GHOST" />

		<MountDriverEId Type="int" Default="0" Flags="ALL_CLIENTS|GHOST" />
		<MountSeatIndex Type="int" Default="0" Flags="ALL_CLIENTS|GHOST" />
		<forbiddenMount Type="int" Default="0" Flags="ALL_CLIENTS|GHOST" />
	</Properties>

	<Implements>
	</Implements>

	<ClientMethods>
		<OnMsgGetOnSelfMount> 
			<!-- bPlayGetOnAnim -->
			<Arg> bool </Arg>
		</OnMsgGetOnSelfMount>
		<OnMsgGetOffSelfMount> 
			<!-- bPlayGetOffAnim -->
			<Arg> bool </Arg>
		</OnMsgGetOffSelfMount>

		<OnMsgGetOnOtherMount> </OnMsgGetOnOtherMount>
		<OnMsgGetOffOtherMount> </OnMsgGetOffOtherMount>

		<OnMsgMountInvite>
			<!-- drvier EID -->
			<Arg> int </Arg>
		</OnMsgMountInvite>

		<OnMsgMountSkillEnterCD>
			<!--1 skillID -->
			<Arg> int </Arg>						
			<!--2 totalCoolDown-->
			<Arg> float </Arg>	
			<!--3 CDEndTimestamp-->
			<Arg> int </Arg>
		</OnMsgMountSkillEnterCD>
	</ClientMethods>

    <ServerMethods>
		<ReqGetOnSelfMount> <Exposed/>
			<!-- bPlayGetOnAnim -->
			<Arg> bool </Arg>
		</ReqGetOnSelfMount>
		<ReqGetOffSelfMount> <Exposed/>
			<!-- bPlayGetOffAnim -->
			<Arg> bool </Arg>
		</ReqGetOffSelfMount>

		<ReqGetOffOtherMount> <Exposed/>
		</ReqGetOffOtherMount>

		<ReqInviteOnMount> <Exposed/>
			<!-- Passenger EID -->
			<Arg> int </Arg>
		</ReqInviteOnMount>

		<ReqAcceptMountInvite> <Exposed/>
			<!-- driver EID -->
			<Arg> int </Arg>
		</ReqAcceptMountInvite>

		<ReqRefuseMountInvite> <Exposed/>
			<!-- driver EID -->
			<Arg> int </Arg>
		</ReqRefuseMountInvite>

		<ReqCastMountSkill>	<Exposed/>
			<!-- skillID -->
			<Arg> int </Arg>
		</ReqCastMountSkill>

		<ReqGMGetOnSelfMount> <Exposed/>
			<!--  MountID -->
			<Arg> int </Arg>
		</ReqGMGetOnSelfMount>
    </ServerMethods>
</root>
