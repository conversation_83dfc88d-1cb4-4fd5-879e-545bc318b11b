<?xml version="1.0" ?>
<root>
    <Properties>
    </Properties>

    <Implements>
        <Interface> StateConflictComponentBase </Interface>
    </Implements>

    <ClientMethods>
        <OnMsgSCExecuteAction> <EnableNilArgs/>
            <!-- 1 actionID -->
            <Arg> int </Arg>
            <Arg> bool </Arg>
        </OnMsgSCExecuteAction>
    </ClientMethods>
    <ServerMethods>
        <!-- 状态移除 -->
        <ReqSCRemove> <Exposed/> 
            <!-- 1 actionID -->
            <Arg> int </Arg>
        </ReqSCRemove>

        <ReqSCSet> <Exposed/> 
            <!-- 1 actionID -->
            <Arg> int </Arg>
        </ReqSCSet>
    </ServerMethods>
</root>