<root>
	<Properties>
		<seasonID Type="UINT" Flags="SERVER_ONLY" Persistent="true" />
		<stage Type="UINT" Flags="SERVER_ONLY" Persistent="true" />
		<signUpLockTimestamp Type="UINT" Flags="SERVER_ONLY" Persistent="true" />
		<regionTroopLists Type="CHAMPION_REGION_TROOP_DICT" Flags="SERVER_ONLY" Persistent="true"/>
		<regionBattleInfoMap Type="CHAMPION_REGION_INFO_MAP" Flags="SERVER_ONLY" Persistent="true"/>
	</Properties>

	<Implements>
		<Interface> Service </Interface>
		<Interface> RefreshComponent </Interface>
		<Interface> EliminationComponent </Interface>
	</Implements>

	<ServerMethods>
		<SSReqReportChampionSignUpTroop>
		    <Arg> CHAMPION_TROOP_INFO_LIST </Arg>
		</SSReqReportChampionSignUpTroop>
		
		<SSReqReportChampionSignUpTroopFinish>
		</SSReqReportChampionSignUpTroopFinish>
		
		<SSOnMsgSyncTroopInfo>
			<Arg> CHAMPION_TROOP_ID </Arg>
			<Arg> CHAMPION_TROOP_DB_INFO </Arg>
		</SSOnMsgSyncTroopInfo>

		<SSOnMsgPrepareArenaCreated>
			<Arg> WORLD_ID </Arg>
			<Arg> mailbox </Arg>
		</SSOnMsgPrepareArenaCreated>

		<SSOnMsgPlayerEnterPrepareArena>
			<Arg> CHAMPION_ROLE_ID </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
			<Arg> WORLD_ID </Arg>
		</SSOnMsgPlayerEnterPrepareArena>

		<SSOnMsgPlayerLeavePrepareArena>
			<Arg> CHAMPION_ROLE_ID </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
			<Arg> WORLD_ID </Arg>
		</SSOnMsgPlayerLeavePrepareArena>
		
		<SSReqEnterPrepareArena>
			<Arg> mailbox </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
		</SSReqEnterPrepareArena>

        <SSGroupBattleWorldCreated>
			<Arg> UINT </Arg>
			<Arg> WORLD_ID </Arg>
			<Arg> mailbox </Arg>
        </SSGroupBattleWorldCreated>
        
		<SSGroupBattleWorldRecycle>
			<Arg> UINT </Arg>
			<Arg> WORLD_ID </Arg>
			<Arg> UINT </Arg>
		</SSGroupBattleWorldRecycle>
		
		<SSGroupBattleBattleResult>
			<Arg> WORLD_ID </Arg>
			<Arg> UINT </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
			<Arg> UINT </Arg>
			<Arg> UINT </Arg>
			<Arg> none </Arg>
		</SSGroupBattleBattleResult>

		<SSReqGetChampionGroupBattleRanklist>
			<Arg> mailbox </Arg>
			<Arg> UINT </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
		</SSReqGetChampionGroupBattleRanklist>
        
		<SSReqGetEliminationBracket>
			<Arg> mailbox </Arg>
			<Arg> UINT </Arg>
			<Arg> UINT </Arg>
		</SSReqGetEliminationBracket>
        
		<SSReqChampionGetProgress>
            <Arg> mailbox </Arg>
			<Arg> UINT </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
		</SSReqChampionGetProgress>

		<SSReqSupportEliminationTroop>
			<Arg> UINT </Arg>
			<Arg> CHAMPION_TROOP_ID </Arg>
			<Arg> UINT </Arg>
		</SSReqSupportEliminationTroop>
		
		<SSReqGetEliminationSupportInfo>
			<Arg> UINT </Arg>
			<Arg> UINT </Arg>
		</SSReqGetEliminationSupportInfo>
	</ServerMethods>
</root>
