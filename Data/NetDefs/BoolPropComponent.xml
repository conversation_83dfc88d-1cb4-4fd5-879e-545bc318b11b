<root>
    <Properties>
        <boolProps Type="BIT_FLAGS" /><!--线上玩家bool属性-->
        <storeBoolProps Type="BIT_FLAGS" Persistent="true" /><!--需要持久化玩家bool属性-->
        <hasInitBoolProps Type="BIT_FLAGS" Persistent="true" /> <!-- 是否初始化过默认值为true的flag -->
    </Properties>

    <ClientMethods>
        <onAllBoolProp>
            <Arg> BIT_FLAGS </Arg>
        </onAllBoolProp>

        <onSetBoolProp>
            <Arg> UINT </Arg>
            <Arg> BOOL </Arg>
        </onSetBoolProp>

        <onBatchSetBoolProps>
            <Arg> KEY_BOOL </Arg>
        </onBatchSetBoolProps>
    </ClientMethods>

    <ServerMethods>
        <clientSetBoolProp> <Exposed /><!--客户端设置bool属性-->
            <Arg> UINT </Arg>
            <Arg> BOOL </Arg>
        </clientSetBoolProp>

        <setBoolProp> <!--服务端设置bool属性-->
            <Arg> UINT </Arg>
            <Arg> BOOL </Arg>
            <Arg> BOOL </Arg>
        </setBoolProp>

        <clientBatchSetBoolProps> <Exposed /><!--客户端批量设置bool属性-->
            <Arg> KEY_BOOL </Arg>
        </clientBatchSetBoolProps>
    </ServerMethods>
</root>