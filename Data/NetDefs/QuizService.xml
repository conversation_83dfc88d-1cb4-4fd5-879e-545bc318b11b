<root>
    <Implements>
        <Interface>Service</Interface>
        <Interface>RefreshComponent</Interface>
    </Implements>
    <Properties>
        <lastQuizBankRefreshTimeStamp Type="int" Flags="SERVER_ONLY"  Persistent="true"/>  <!-- 上次题库的刷新时间戳 -->
        <dailyWorldQuizIdList Type="list" Element="int" Flags="SERVER_ONLY"  Persistent="true"/>      <!-- 世界频道答题每日题库  -->
        <gmRefreshQuizStatus Type="int" Flags="SERVER_ONLY"  Persistent="true"/> <!-- gm刷新题库状态 1:需要清空gm对原有流程的影响 -->
    </Properties>
    <ServerMethods>

        <SSReqAnswerWorldQuizCorrect>
            <Arg> int </Arg>
            <!--avatarId-->
            <Arg> string </Arg>
            <!-- quizType -->
            <Arg> int </Arg>
        </SSReqAnswerWorldQuizCorrect>

    </ServerMethods>
</root>
