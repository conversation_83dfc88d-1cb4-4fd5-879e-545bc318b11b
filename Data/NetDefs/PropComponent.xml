<?xml version="1.0" ?>
<root>
    <Properties>
        <!-- dict<propName, operateId> -->
        <coverSetPropRecord Type="dict" Key="string" Value="int" Flags="SERVER_ONLY" />

        <!-- dict<operateId, extraHurtMultiId> -->
        <extraHurtMultiList Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" />
    </Properties>

    <Implements>
    </Implements>

    <ClientMethods>
    </ClientMethods>

    <ServerMethods>

    </ServerMethods>
</root>