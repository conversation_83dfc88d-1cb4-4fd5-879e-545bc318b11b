<?xml version="1.0" ?>
<root>
    <Properties>
        <!-- dict<skillId, skillLvl> -->
        <PassiveSkillList Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" />
        <!-- dict<skillId, PSEffectBlackboard> -->
        <PSEffectBlackBoardDict Type="dict" Key="int" Value="PSEffectBlackboard" Flags="SERVER_ONLY" />
        <!-- dict<skillId, PSEffectIDList> -->
        <PSEffectDict Type="dict" Key="int" Value="PSEffectIDList" Flags="SERVER_ONLY" />
        <!-- dict<skillId, skillLvl> -->
        <ExtraPassiveSkillList Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" />
    </Properties>

    <Implements/>
    <ClientMethods>
    </ClientMethods>

    <ServerMethods>
    </ServerMethods>
</root>