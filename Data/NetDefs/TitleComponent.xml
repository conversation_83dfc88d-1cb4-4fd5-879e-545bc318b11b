<?xml version="1.0" ?>
<root>
    <Properties>
        <curTitleInfos Type="CurTitleInfoDict" Flags="ALL_CLIENTS" Persistent="true" /> <!-- 当前佩戴的称号与头衔，key为title类型，1为称号，2为头衔，val为具体信息，由于需要被其他玩家看见所以是ALL_CLIENTS -->
        <titleInfos Type="dict" Key="int" Value="TitleInfoDict" Flags="OWN_CLIENT" Persistent="true" /> <!-- 拥有的称号与头衔，key为title类型，1为称号，2为头衔，val为拥有的所有title的具体信息 -->
    </Properties>

	<ClientMethods>
        <retPutOnTitle>
            <!--1 result -->
            <Arg> int </Arg>
            <!--1 称号类型 -->
            <Arg> int </Arg>
        </retPutOnTitle>

        <retPutOffTitle>
            <!--1 result -->
            <Arg> int </Arg>
            <!--1 称号类型 -->
            <Arg> int </Arg>
        </retPutOffTitle>

        <titleUpdate>
            <!--1 更新的称号类型 -->
            <Arg> int </Arg>
        </titleUpdate>
	</ClientMethods>

    <ServerMethods>
        <reqPutOnTitle CD="0.5"> <Exposed />
            <!-- 1 要换上的称号ID -->
            <Arg> int </Arg>
            <!--1 称号类型 -->
            <Arg> int </Arg>
        </reqPutOnTitle>

        <reqPutOffTitle CD="0.5"> <Exposed />
            <!--1 称号类型 -->
            <Arg> int </Arg>
        </reqPutOffTitle>
    </ServerMethods>
</root>
