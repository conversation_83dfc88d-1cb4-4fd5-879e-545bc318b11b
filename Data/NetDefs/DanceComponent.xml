<?xml version="1.0" ?>
<root>
    <Properties>
		<danceMusicID Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false" />  <!-- 舞曲id -->
		<danceActivityMode Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false" />  <!-- 舞曲模式（单人、双人、多人） -->
		<danceStartTime Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false" />	<!-- 舞曲开始时间(ms) -->
		<danceLeaderID Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false" />  <!-- 舞曲发起者id -->
		<danceMembers Type="DictIntBool" Flags="OWN_CLIENT" Persistent="false" />
		<danceStageID Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false" />  <!-- 舞台id -->
	</Properties>

	<Implements>
	</Implements>

	<ClientMethods>
		<RetStartSingleDance>
			<!-- Result -->
			<Arg> Result </Arg>
			<!-- MusicID -->
			<Arg> int </Arg>
		</RetStartSingleDance>
		<OnMsgStartSinglePerformance>
			<!-- MusicID -->
			<Arg> int </Arg>
		</OnMsgStartSinglePerformance>

		<OnMsgStopSinglePerformance>
		</OnMsgStopSinglePerformance>
		<RetInviteDoubleDance>
			<!-- Result -->
			<Arg> Result </Arg>
			<!-- target -->
			<Arg> int </Arg>
		</RetInviteDoubleDance>
		<OnMsgInviteDoubleDance>
			<!-- target -->
			<Arg> int </Arg>
		</OnMsgInviteDoubleDance>
		<RetAcceptDoubleDance>
			<!-- target -->
			<Arg> int </Arg>
		</RetAcceptDoubleDance>
		<OnMsgEnterDoubleDance>
		</OnMsgEnterDoubleDance>

		<OnMsgRefuseDoubleDance>
			<!-- member -->
			<Arg> int </Arg>
		</OnMsgRefuseDoubleDance>

		<RetStartDoubleDance>
			<!-- Result -->
			<Arg> Result </Arg>
		</RetStartDoubleDance>

		<OnMsgStartDoubleDance>
			<!-- MusicID -->
			<Arg> int </Arg>
		</OnMsgStartDoubleDance>

		<OnMsgStartDoublePerformance>
			<!-- MusicID -->
			<Arg> int </Arg>
			<!-- agent -->
			<Arg> int </Arg>
			<!-- target -->
			<Arg> int </Arg>
		</OnMsgStartDoublePerformance>

		<OnMsgStopDoublePerformance>
			<!-- agent -->
			<Arg> int </Arg>
			<!-- target -->
			<Arg> int </Arg>
		</OnMsgStopDoublePerformance>

		<RetInviteMultiDance>
			<!-- Result -->
			<Arg> Result </Arg>
		</RetInviteMultiDance>

		<OnMsgInviteMultiDance>
			<!-- inviter -->
			<Arg> int </Arg>
		</OnMsgInviteMultiDance>

		<OnMsgLeaveBasicDance>
			<!-- member -->
			<Arg> int </Arg>
		</OnMsgLeaveBasicDance>
		<RetSwitchDanceStage>
			<!-- Result -->
			<Arg> Result </Arg>
		</RetSwitchDanceStage>

		<OnMsgStartMultiDance>
		</OnMsgStartMultiDance>

		<OnMsgInterruptDance>
		</OnMsgInterruptDance>
	</ClientMethods>

    <ServerMethods>
		<ReqStartSingleDance><Exposed/>
			<!-- MusicID -->
			<Arg> int </Arg>
		</ReqStartSingleDance>

		<ReqInviteDoubleDance><Exposed/>	
			<!-- target -->
			<Arg> int </Arg>
		</ReqInviteDoubleDance>

		<ReqAcceptDoubleDance><Exposed/>
			<!-- target -->
			<Arg> int </Arg>
		</ReqAcceptDoubleDance>

		<ReqRefuseDoubleDance><Exposed/>
			<!-- target -->
			<Arg> int </Arg>
		</ReqRefuseDoubleDance>

		<ReqStartDoubleDance><Exposed/>
			<!-- MusicID -->
			<Arg> int </Arg>
		</ReqStartDoubleDance>

		<ReqInviteMultiDance><Exposed/>
		</ReqInviteMultiDance>

		<ReqAcceptMultiDance><Exposed/>
		</ReqAcceptMultiDance>

		<ReqRefuseMultiDance><Exposed/>
		</ReqRefuseMultiDance>

		<ReqStartMultiDance><Exposed/>
			<!-- MusicID -->
			<Arg> int </Arg>
		</ReqStartMultiDance>

		<ReqLeaveDanceActivity><Exposed/>
		</ReqLeaveDanceActivity>

		<ReqSwitchDanceStage CD="2"><Exposed/>
            <!-- 舞台id -->
            <Arg> int </Arg>
        </ReqSwitchDanceStage>

		<ReqStartSinglePerformance><Exposed/>
			<!-- MusicID -->
			<Arg> int </Arg>
		</ReqStartSinglePerformance>

		<ReqStartDoublePerformance><Exposed/>
			<!-- MusicID -->
			<Arg> int </Arg>
			<!-- agent -->
			<Arg> int </Arg>
			<!-- target -->
			<Arg> int </Arg>
		</ReqStartDoublePerformance>

		<ReqStopDoublePerformance><Exposed/>
			<!-- agent -->
			<Arg> int </Arg>
			<!-- target -->
			<Arg> int </Arg>
		</ReqStopDoublePerformance>

		<ReqStopSinglePerformance><Exposed/>
		</ReqStopSinglePerformance>

    </ServerMethods>
</root>