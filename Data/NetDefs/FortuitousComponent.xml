<?xml version="1.0" ?>
<root>
	<Properties>
        <FortuitousList Type="dict" Key="int" Value="FortuitousInfo" Flags="SERVER_ONLY" Persistent="true"/>
        <FinishedFortuitousList Type="dict" Key="int" Value="bool" Flags="OWN_CLIENT" Persistent="true"/>
	</Properties>
	<Implements/>
	<ClientMethods>
        <OnMsgStartFortuitous> 
			<!--1 FortuitousID -->
			<Arg> int </Arg>
		</OnMsgStartFortuitous>

        <OnMsgFinishedFortuitous> 
			<!--1 FortuitousID -->
			<Arg> int </Arg>
			<!--1 RingID -->
			<Arg> int </Arg>
		</OnMsgFinishedFortuitous>

        <RetClientTriggerFortuitous> 
			<!--1 Result -->
			<Arg> Result </Arg>
            <!--1 FortuitousID -->
            <Arg> int </Arg>
		</RetClientTriggerFortuitous>
	</ClientMethods>
	<ServerMethods>
        <ReqClientTriggerFortuitous> <Exposed/>
			<!--1 FortuitousID -->
			<Arg> int </Arg>
		</ReqClientTriggerFortuitous>
	</ServerMethods>
</root>
