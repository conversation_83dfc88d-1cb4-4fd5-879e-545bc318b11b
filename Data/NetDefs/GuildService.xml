<root>
    <Implements>
        <Interface> Service </Interface>
        <!-- <Interface> iActivity </Interface> -->
        <Interface> RefreshComponent </Interface>
    </Implements>

    <Properties>
        <!-- <guildLastId Type="UINT" Persistent="true" Default="0" /> -->
        <!--offlineKick Type="ENTITY_ID_BOOL" Persistent="true" /-->
        <!-- voyageTaskVersion Type="UINT" Persistent="true" /> --> <!-- 远航任务版本号 -->
        <!-- <guildVoyageTask Type="VOYAGE_TASK_GROUP_ID_LIST" Persistent="true" /> --> <!-- 公会远航任务简要信息 -->
        <!-- <personalVoyageTask Type="VOYAGE_TASK_GROUP_ID_LIST" Persistent="true" /> --> <!-- 个人远航任务简要信息 -->
        <recommendInfos Type="GUILD_RECOMMEND_INFOS" Persistent="true" />

        <guildLeagueSeasonInfo Type="GUILD_LEAGUE_SEASON_INFO" Persistent="true" />
        <guildLeagueRoundInfo Type="GUILD_LEAGUE_ROUND_INFO" Persistent="true" />
        <zoneIndex2GameInfos Type="GUILD_LEAGUE_ZONE_GAME_INFOS" Persistent="true" />
        <!-- <guildTalentShowCalcTime Type="LONGTIME" Persistent="true" />  云垂少女星发奖时间戳 
        <guildTalentShowClearTime Type="LONGTIME" Persistent="true" /> 云垂少女星清榜时间戳 
        <guildTalentShowGroupCalcTime Type="LONGTIME" Persistent="true" /> 云垂少女星成团时间戳
        <guildTalentShowHistoryRecords Type="array" Element="GUILD_TALENT_SHOW_GROUP_RECORD" Persistent="true" /> 云垂少女星往届成团成员信息
        <guildTalentShowCurrentRecord Type="GUILD_TALENT_SHOW_GROUP_RECORD" Persistent="true" /> 云垂少女星本届成团成员信息  -->
        <!-- <applyRecords Type="ENTITY_ID_TIME" Persistent="true"/>
        <recommendSystemGuildRecord Type="ENTITY_ID_TIME" Persistent="true"/>
        <guildLeagueInited Type="BOOL" Default="false" Persistent="true" />
        <lastGuildList Type="GUILD_LEAGUE_INFO_LIST" Persistent="true" />
        <topGuildList Type="GUILD_LEAGUE_INFO_LIST" Persistent="true" />
        <leagueOpenTimes Type="UINT" Persistent="true" /> -->
        <!-- <highLeagueMatchInfo Type="GUILD_LEAGUE_MATCH_INFO" Persistent="true" />
        <commonLeagueMatchInfo Type="GUILD_LEAGUE_MATCH_INFO" Persistent="true" /> -->
    </Properties>

    <ServerMethods>
        <!--进入公会场景-->
        <SSEnterGuildStation>
            <Arg> mailbox </Arg>        <!--玩家的mailbox-->
            <Arg> ENTITY_ID </Arg>      <!--公会的ID-->
        </SSEnterGuildStation>

        <!--玩家删除角色的通知-->
        <SSOnMsgGuildOnDeleteRole>
            <Arg> ENTITY_ID </Arg>      <!--玩家的ID-->
        </SSOnMsgGuildOnDeleteRole>

        <getGuildRecommendInfo>
            <Arg> mailbox </Arg>
            <Arg> bool </Arg>
        </getGuildRecommendInfo>

        <SPullRankInfo>
            <Arg> string </Arg>
        </SPullRankInfo>

        <registerGuildProcess>
            <Arg> PID </Arg>
            <Arg> bool </Arg>
            <Arg> bool </Arg>
        </registerGuildProcess>

        <SRefreshWhisperGuildResponseRecruitInfo>
            <Arg> DictStrBool </Arg>
        </SRefreshWhisperGuildResponseRecruitInfo>

        <reportGuildRecommendInfo>
            <Arg> string </Arg>
            <Arg> int </Arg>
        </reportGuildRecommendInfo>

        <playerOnline>
            <Arg> mailbox </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> none </Arg>
        </playerOnline>

        <tryJoinGuild>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
        </tryJoinGuild>

        <clearJoinGuildInfo>
            <Arg> string </Arg>
            <Arg> string </Arg>
        </clearJoinGuildInfo>

        <playerOnlineServer>
            <Arg> mailbox </Arg>
            <Arg> none </Arg>
        </playerOnlineServer>

        <createGuild>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> none </Arg>
            <Arg> string </Arg>    <!-- 货币加锁信息 -->
            <Arg> UINT </Arg> <!--类型(const.GUILD_CREATE_TYPE)-->
            <Arg> UINT </Arg> <!--类型(const.GUILD_TYPE)-->
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </createGuild>

        <responseGuild>
            <Arg> mailbox </Arg>
            <Arg> GUILD_ID </Arg>
            <Arg> none </Arg>
        </responseGuild>

        <registerSecondInit>
            <Arg> string </Arg>
        </registerSecondInit>

        <registerGuild>
            <Arg> mailbox </Arg>
            <Arg> BOOL </Arg>
            <Arg> GUILD_SIMPLE_INFO </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
            <Arg> BOOL </Arg>
        </registerGuild>

        <unregisterGuild>
            <Arg> GUILD_ID </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
        </unregisterGuild>

        <onCreateGuild>
            <Arg> mailbox </Arg>
            <Arg> GUILD_SIMPLE_INFO </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
        </onCreateGuild>

        <onMergeGuild>
            <Arg> GUILD_ID </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
            <Arg> none </Arg>
            <Arg> string </Arg>
        </onMergeGuild>

        <createGuildFinish>
            <Arg> GUILD_SIMPLE_INFO </Arg>
        </createGuildFinish>

        <createGuildFail>
            <Arg> GUILD_ID </Arg>
            <Arg> none </Arg>
        </createGuildFail>

        <getPreGuilds>
            <Arg> mailbox </Arg>
        </getPreGuilds>

        <getGuilds>
            <Arg> mailbox </Arg>
            <Arg> GUILD_APPLY_MAP </Arg>
        </getGuilds>

        <batchApplyGuild>
            <Arg> mailbox </Arg>
            <Arg> GUILD_APPLY </Arg>
            <Arg> ListInt </Arg>
        </batchApplyGuild>

        <checkPlayerInGuild>
            <Arg> mailbox </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
        </checkPlayerInGuild>

        <getPlayerGuildId>
            <Arg> mailbox </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
        </getPlayerGuildId>

        <syncGuildInfo>
            <Arg> GUILD_ID </Arg>
            <Arg> none </Arg>
        </syncGuildInfo>

        <playerJoinGuild>
            <Arg> GUILD_ID </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> none </Arg>
        </playerJoinGuild>

        <playerQuitGuild>
            <Arg> GUILD_ID </Arg>
            <Arg> none </Arg>
            <Arg> ENTITY_ID </Arg>
        </playerQuitGuild>

        <SSendGuildRankRewards>
            <Arg> string </Arg>
            <Arg> int </Arg>
            <Arg> DictStrInt </Arg>
        </SSendGuildRankRewards>

        <callAllGuild>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
            <Arg> none </Arg>
        </callAllGuild>

        <callGuildByPlayerGbId>
            <Arg> ENTITY_ID </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
        </callGuildByPlayerGbId>

        <callGuildByGuildIdWithCallback>
            <Arg> GUILD_ID </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg> <!--  失败回调  -->
            <Arg> none </Arg> <!--  失败回调参数  -->
        </callGuildByGuildIdWithCallback>

        <callGuildByGuildId>
            <Arg> GUILD_ID </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
        </callGuildByGuildId>

        <callGuildByShortId>
            <Arg> UINT </Arg>
            <Arg> METHOD_NAME </Arg> <!--  methodName  -->
            <Arg> none </Arg> <!--  method      args -->
        </callGuildByShortId>

        <!--修改公会名的请求-->
        <SSReqChangeGuildName>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>  
        </SSReqChangeGuildName>

        <!--根据公会的shortID获取公会名-->
        <SSReqGetGuildNameByShortID>
            <Arg> mailbox </Arg>        <!-- mailbox -->
            <Arg> int </Arg>            <!-- shortID -->
        </SSReqGetGuildNameByShortID>

        <!--根据guildID获取公会名-->
        <SSReqGetGuildNameByGuildID>
            <Arg> mailbox </Arg>        <!-- mailbox -->
            <Arg> GUILD_ID </Arg>       <!-- guildID -->
        </SSReqGetGuildNameByGuildID>


        <onChangeGuildType>
            <Arg> GUILD_ID </Arg>
            <Arg> int </Arg>
        </onChangeGuildType>

        <onChangeGuildName>
            <Arg> GUILD_ID </Arg>
            <Arg> string </Arg>
        </onChangeGuildName>

        <useShortId>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> BOOL </Arg>
        </useShortId>

        <readyMerge>
            <Arg> GUILD_ID </Arg>
            <Arg> mailbox </Arg>
            <Arg> none </Arg>
        </readyMerge>

        <batchKickMembers>
            <Arg> GUILD_ID </Arg>
            <Arg> none </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
        </batchKickMembers>

        <!-- 运营GM Start -->
        <getGuildIdByName>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </getGuildIdByName>

        <SSReqKDIPDisbandGuildByID>
            <Arg> none </Arg> <!-- 公会列表 -->
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> none </Arg> <!-- handle信息 -->
        </SSReqKDIPDisbandGuildByID>

        <renameGuildById>
            <Arg> UINT </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </renameGuildById>

        <kickGuildMemberById>
            <Arg> UINT </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </kickGuildMemberById>

        <setGuildLvById>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </setGuildLvById>

        <setGuildDeclaration>
            <Arg> UINT </Arg>
            <Arg> string </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </setGuildDeclaration>

        <playerLoginInStation>
            <Arg> ENTITY_ID </Arg>
        </playerLoginInStation>

        <getGuildDeclaration>
            <Arg> UINT </Arg>
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> UINT </Arg>
        </getGuildDeclaration>
        <!-- 运营GM End -->

        <forceEnterGuild>
            <Arg> mailbox </Arg>
            <Arg> none </Arg>
            <Arg> GUILD_ID </Arg>
        </forceEnterGuild>

        <callGuildByGuildIdForGMTool>
            <Arg> GUILD_ID </Arg>
            <Arg> METHOD_NAME </Arg>        <!--  methodName  -->
            <Arg> UINT </Arg>               <!--  method args -->
        </callGuildByGuildIdForGMTool>

        <sendGuildRankListReward> <!-- 给公会结算排行榜奖励 -->
            <Arg> GUILD_ID </Arg> <!-- 公会id -->
            <Arg> UINT </Arg> <!-- 排行榜id -->
            <Arg> UINT </Arg> <!-- 排行榜名次 -->
            <Arg> none </Arg> <!-- rankInfo -->
            <Arg> NUID </Arg> <!-- opNUID -->
        </sendGuildRankListReward>

        <onRegRankListCallback>
            <Arg> UINT </Arg> <!-- rankId -->
            <Arg> UINT </Arg> <!-- callbackId -->
        </onRegRankListCallback>

        <onRankListRefresh>
            <Arg> UINT </Arg> <!-- rankId -->
            <Arg> none </Arg> <!-- displayInfo -->
        </onRankListRefresh>

        <onGetGuildTalentShowGroupGenderInfo>
            <Arg> none </Arg> <!-- playerBasicInfo -->
            <Arg> UINT </Arg> <!-- sessionId -->
        </onGetGuildTalentShowGroupGenderInfo>

        <!-- <onCollectGuildTalentShowGroupInfo>
            <Arg> GUILD_TALENT_SHOW_GROUP_PLAYER_INFO </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> UINT </Arg>  rank 
        </onCollectGuildTalentShowGroupInfo> -->

        <!-- <getGuildTalentShowGroupInfo>
            <Arg> UINT </Arg> 上几届 0代表本届 
            <Arg> mailbox </Arg>
        </getGuildTalentShowGroupInfo> -->

        <gmGetAllGuildSimpleInfo>
            <Arg> UINT </Arg>
        </gmGetAllGuildSimpleInfo>

        <getGuildsByFilter>
            <Arg> none </Arg>  <!-- filter k- v-->
            <Arg> mailbox </Arg>  <!-- 玩家mailbox -->
            <Arg> METHOD_NAME </Arg>
            <Arg> none </Arg>
        </getGuildsByFilter>

        <gmOpenGuildLeague>
            <Arg> int </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> BOOL </Arg>
            <Arg> string </Arg>
        </gmOpenGuildLeague>

        <gmCloseGuildLeague>
        </gmCloseGuildLeague>

        <gmEnterGuildLeagueForStress>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
        </gmEnterGuildLeagueForStress>

        <searchMatchGuilds>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
            <Arg> int </Arg>
            <Arg> bool </Arg>
            <Arg> ListInt </Arg>
        </searchMatchGuilds>

        <gmSettleGuildLeague>
		</gmSettleGuildLeague>

        <gmGetAllGuildId2Name>
            <Arg> UINT </Arg>
        </gmGetAllGuildId2Name>

        <gmRandomEnterGuild>
            <Arg> mailbox </Arg>
            <Arg> none </Arg>
        </gmRandomEnterGuild>

        <enterGuildLeagueSpace>
            <Arg> mailbox </Arg>
        </enterGuildLeagueSpace>

        <SOnGuildLeagueWorldCreate>
            <Arg> int </Arg>
            <Arg> mailbox </Arg>
            <Arg> int </Arg>
            <Arg> ListStr </Arg>
        </SOnGuildLeagueWorldCreate>

        <SOnGuildLeagueWorldStart>
            <Arg> mailbox </Arg>
            <Arg> ListStr </Arg>
        </SOnGuildLeagueWorldStart>

        <SOnGuildLeagueWorldOver>
            <Arg> int </Arg>
            <Arg> ListInt </Arg>
            <Arg> int </Arg>
        </SOnGuildLeagueWorldOver>

        <SCheckCanDisbandGuild>
            <Arg> mailbox </Arg>
            <Arg> mailbox </Arg>
        </SCheckCanDisbandGuild>

        <SOnGuildLeagueWorldDestroy>
            <Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> ListStr </Arg>
        </SOnGuildLeagueWorldDestroy>

        <gmRefreshGuildVoyageTasks> <!-- gm刷新当周远航任务 -->
        </gmRefreshGuildVoyageTasks>

        <gmSetGuildVoyageTasks> <!-- gm设置指定周的远航任务 -->
            <Arg> UINT </Arg> <!-- 周数 -->
        </gmSetGuildVoyageTasks>

        <recordGuildApply>
            <Arg> ENTITY_ID </Arg>
            <Arg> LONGTIME </Arg>
        </recordGuildApply>

        <removeGuildApply>
            <Arg> ENTITY_ID </Arg>
        </removeGuildApply>

        <applySystemGuild>
            <Arg> UINT </Arg>
            <Arg> mailbox </Arg>
            <Arg> GUILD_APPLY </Arg>
        </applySystemGuild>

        <guildMemberKicked>
            <Arg> ENTITY_ID </Arg>
        </guildMemberKicked>

        <!-- <getHegemonyGuildInfo> 获取公会争霸公会数据 
            <Arg> GUILD_ID_LIST </Arg> 公会id列表 
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
        </getHegemonyGuildInfo> -->
<!-- 
        <getHegemonyGuildMemberGbIdList> 获取公会争霸奖励公会成员信息 
            <Arg> UINT </Arg> 活动id
            <Arg> HEGEMONY_GUILD_RANK_INFO </Arg>  公会排名信息 
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> none </Arg>
        </getHegemonyGuildMemberGbIdList> -->

        <!-- <onQueryGuildLeagueInfo>  查询公会联赛信息回调 
            <Arg> UINT </Arg>     公会id 
            <Arg> number </Arg>   公会奖章数量 
            <Arg> number </Arg>  公会7日总战力 
            <Arg> string </Arg>  公会名称 
            <Arg> GUILD_LEAGUE_STATUS_RECORD </Arg> 最近5场的胜负情况 
            <Arg> string </Arg>  会长角色名 
        </onQueryGuildLeagueInfo> -->

        <getGuildLeagueList>
            <Arg> mailbox </Arg>
        </getGuildLeagueList>

        <recordGuildLeagueMatchResult>  <!-- 战斗结算时根据双方胜负结果进行结算 -->
            <Arg> UINT </Arg>           <!-- guildId -->
            <Arg> BOOL </Arg>           <!-- isWin -->
            <Arg> UINT </Arg>           <!-- round -->
            <Arg> BOOL </Arg>           <!-- 是否轮空 -->
        </recordGuildLeagueMatchResult>

        <getLeagueMatchInfoByIndex>     <!-- 根据组别id和组获取对局信息 -->
            <Arg> mailbox </Arg>        <!-- srcBox -->
            <Arg> BOOL </Arg>           <!-- 是否在青龙组 -->
            <Arg> UINT </Arg>           <!-- 组内index -->
        </getLeagueMatchInfoByIndex>

        <getMyLeagueMatchInfo>          <!-- 查看指定公会id的对局信息 -->
            <Arg> mailbox </Arg>        <!-- srcBox -->
            <Arg> UINT </Arg>           <!-- guildId -->
        </getMyLeagueMatchInfo>

        <updateLeagueMatchResult>       <!-- 对战后胜利方通知stub更新赛程记录 -->
            <Arg> UINT </Arg>           <!-- guildId -->
            <Arg> BOOL </Arg>           <!-- isWin -->
            <Arg> UINT </Arg>           <!-- round -->
        </updateLeagueMatchResult>

        <registerLeagueSpace>
            <Arg> UINT </Arg>
            <Arg> none </Arg>
            <Arg> BOOL </Arg>
            <Arg> mailbox </Arg>
        </registerLeagueSpace>

        <modifyGuildLeagueOpenTimes>
            <Arg> UINT </Arg>
        </modifyGuildLeagueOpenTimes>

        <queryGuildLeagueOpenTimes>
            <Arg> mailbox </Arg>
        </queryGuildLeagueOpenTimes>

        <getLeagueMainSpaceId>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> ENTITY_ID </Arg>
        </getLeagueMainSpaceId>

        <getLeagueSubSpaceId>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> ENTITY_ID </Arg>
        </getLeagueSubSpaceId>

        <reportLeagueResult>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> none </Arg>
            <Arg> none </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </reportLeagueResult>

        <modifyLeagueFieldPlayerCount>  <!-- 修改战场内人数 -->
            <Arg> UINT </Arg>           <!-- 公会id -->
            <Arg> ENTITY_ID </Arg>           <!-- playerGbId -->
            <Arg> BOOL </Arg>           <!-- isMainStage -->
            <Arg> BOOL </Arg>           <!-- true:进入场景 false:离开场景 -->
        </modifyLeagueFieldPlayerCount>

        <SPullAvatarGuildPageName>
            <Arg> RANK_PULL_GUILD_NAME </Arg> 
        </SPullAvatarGuildPageName>

        <gmResetGuildLeagueInitedStatus>
        </gmResetGuildLeagueInitedStatus>

        <gmForceFinishLeagueBattle>
        </gmForceFinishLeagueBattle>

        <gmStartLeagueBattle>
        </gmStartLeagueBattle>

        <gmDisbandAllGuild>
        </gmDisbandAllGuild>

        <SSKDIPQueryGuildInfo>
            <Arg> none </Arg>   <!-- 过滤条件 -->
            <Arg> mailbox </Arg>
            <Arg> METHOD_NAME </Arg>
            <Arg> none </Arg>
        </SSKDIPQueryGuildInfo>

        <SSRetKDIPQueryGuildInfo>
            <Arg> string </Arg>   <!-- KDIP的流水号 -->
            <Arg> none </Arg> <!-- 公会信息返回 -->
        </SSRetKDIPQueryGuildInfo>

        <agreeGuildAllApplys>
            <Arg> mailbox </Arg>
            <Arg> mailbox </Arg>
            <Arg> ListStr </Arg>
        </agreeGuildAllApplys>
        <checkTwoPlayerInSameGuild>
            <Arg> mailbox </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> ENTITY_ID </Arg>
            <Arg> string </Arg>
        </checkTwoPlayerInSameGuild>

        <gmTestGuildLeagueFakeStart>
        </gmTestGuildLeagueFakeStart>

        <gmTestGuildLeagueSettlement>
        </gmTestGuildLeagueSettlement>

        <gmTestGuildLeagueStepByStep>
        </gmTestGuildLeagueStepByStep>

        <SReqSyncGuildLeagueInfo>
            <Arg> mailbox </Arg>
        </SReqSyncGuildLeagueInfo>

        <SReqSyncGuildLeaguePreZoneID>
            <Arg> mailbox </Arg>
        </SReqSyncGuildLeaguePreZoneID>

        <SReqSyncGuildLeagueGroupGameInfo>
            <Arg> mailbox </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
        </SReqSyncGuildLeagueGroupGameInfo>

        <gmCreateNGuild>
            <Arg> int </Arg>
        </gmCreateNGuild>

        <gmClearGuildLeagueSeasonInfo>
        </gmClearGuildLeagueSeasonInfo>
    </ServerMethods>
</root>
