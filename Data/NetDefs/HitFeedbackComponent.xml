<?xml version="1.0" ?>
<root>
	<Implements/>
	<ClientMethods>

		<OnMsgHitFeedback> <EnableNilArgs/>
			<!--1 InstigatorID -->
			<Arg> string </Arg>
			<!--2 StaggerState -->
			<Arg> int </Arg>
			<!--3 LifeInfo 倒地/地面状态时的硬直时长 -->
			<Arg> HIT_FEEDBACK_LIFE </Arg>
			<!--4 target_yaw -->
			<Arg> float </Arg>
			<!--5 HF_MOVE_INFO_LIST -->
			<Arg> HF_MOVE_INFO_LIST </Arg>
			<!--6 staggerParamList 客户端需要的表参数 -->
			<Arg> ListFloat </Arg>
		</OnMsgHitFeedback>

		<OnMsgRecoverFromStagger> <EnableNilArgs/>
			<!--1 forceStop -->
			<Arg> bool </Arg>
		</OnMsgRecoverFromStagger>

	</ClientMethods>
	<ServerMethods/>
</root>
