<root>
    <Properties>
        <lastSortWareHouse Type="UINT" />
        <wareHouse Type="WARE_HOUSE" Persistent="true"/>
    </Properties>

    <ClientMethods>
        <onGetWareHouseInfo>
            <Arg> WARE_HOUSE </Arg> <!-- 解锁信息 -->
        </onGetWareHouseInfo>

        <onGetWareHousePageInfo>
            <Arg> UINT_MAP </Arg> <!-- 解锁信息 -->
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> INV_SLOT_INFO </Arg>
            <Arg> MARK_MAP </Arg> <!-- vip解锁信息 -->
        </onGetWareHousePageInfo>

        <onSortWareHousePage>
            <Arg> UINT_MAP </Arg> <!-- 仓库页 -->
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> INV_SLOT_INFO </Arg>
            <Arg> MARK_MAP </Arg> <!-- vip解锁信息 -->
        </onSortWareHousePage>

        <onChangeWareHouse>
            <Arg> UINT </Arg> <!-- error code -->
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> INV_SLOT_COUNT_MAP </Arg> <!-- 格子中物品数量变化 -->
            <Arg> INV_SLOT_VAL_MAP </Arg> <!-- 格子中物品内容变化 -->
        </onChangeWareHouse>

        <onUnlockWareHousePage>
            <Arg> UINT </Arg> <!-- 仓库分页id -->
        </onUnlockWareHousePage>

        <onUnlockWareHouseSlot>
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> UINT </Arg> <!-- 解锁id -->
            <Arg> UINT </Arg> <!-- 解锁后的格子数 -->
        </onUnlockWareHouseSlot>
    </ClientMethods>

    <ServerMethods>
        <getWareHousePageInfo> <Exposed />
            <Arg> UINT </Arg> <!-- 仓库分页id -->
        </getWareHousePageInfo>

        <storeToWareHouse CD="0.17"> <Exposed />
            <Arg> UINT </Arg> <!-- 背包id -->
            <Arg> SLOT_INDEX </Arg> <!-- 背包格子index -->
            <Arg> UUID </Arg> <!-- 存放的物品gbId(用于校验) -->
            <Arg> UINT </Arg> <!-- 仓库分页id -->
        </storeToWareHouse>

        <takeFromWareHouse CD="0.17"> <Exposed />
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> SLOT_INDEX </Arg> <!-- 分页内格子index -->
            <Arg> UUID </Arg> <!-- 取出的物品gbId(用于校验) -->
        </takeFromWareHouse>

        <unlockWareHousePage> <Exposed />
            <Arg> UINT </Arg> <!-- 仓库分页id -->
        </unlockWareHousePage>

        <unlockWareHouseSlot> <Exposed />
            <Arg> UINT </Arg> <!-- 仓库分页id -->
            <Arg> UINT </Arg> <!-- 解锁id -->
        </unlockWareHouseSlot>

        <sortWareHousePage CD="4"> <Exposed />
            <Arg> UINT </Arg> <!-- 仓库分页id -->
        </sortWareHousePage>

        <gmDelWareHouseItem>
            <Arg> UINT </Arg> <!-- handler -->
            <Arg> UINT </Arg> <!-- itemId -->
            <Arg> UUID </Arg> <!-- itemGbId -->
            <Arg> UINT </Arg> <!-- count -->
            <Arg> UINT </Arg> <!-- bindType -->
        </gmDelWareHouseItem>
    </ServerMethods>
</root>
