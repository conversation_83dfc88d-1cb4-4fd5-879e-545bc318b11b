<root>
    <Properties>
    </Properties>

    <Implements>
    </Implements>

    <ClientMethods>
        <OnMsgCommonInteractorCreate>                   <!-- 通用交互物创建 -->
            <Arg> COMMON_INTERACTOR_SYNC_ATTR </Arg>    <!-- 交互物信息 -->
        </OnMsgCommonInteractorCreate>

        <OnMsgCommonInteractorDestroy>                  <!-- 通用交互物销毁 -->
            <Arg> int </Arg>                            <!-- 交互物实例ID-->
        </OnMsgCommonInteractorDestroy>

        <OnMsgCommonInteractorAttrSync>                 <!-- 通用交互物属性同步 -->
            <Arg> COMMON_INTERACTOR_SYNC_ATTR </Arg>    <!-- 交互物信息 -->
        </OnMsgCommonInteractorAttrSync>

        <OnMsgStartCommonInteract>              <!-- 通用交互开始广播通知 -->
            <Arg> int </Arg>                    <!-- 交互开始类型,参考COMMON_INTERACTOR_START_TYPE -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> string </Arg>                 <!-- 交互对象 -->
            <Arg> int </Arg>                    <!-- 按钮ID,参考ReqStartCommonInteract中的说明-->
        </OnMsgStartCommonInteract>

        <OnMsgCommonInteractorResult>           <!-- 通用交互结果广播通知 -->
            <Arg> int </Arg>                    <!-- 交互开始类型,参考COMMON_INTERACTOR_START_TYPE -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> int </Arg>                    <!-- 通用交互结果,参考COMMON_INTERACTOR_RESULT -->
            <Arg> int </Arg>                    <!-- 按钮ID,参考ReqStartCommonInteract中的说明 -->
            <Arg> string </Arg>                 <!-- 交互对象 -->
        </OnMsgCommonInteractorResult>

        <OnMsgCommonInteractorReceiveMsg>       <!-- 收到msg通知 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> string </Arg>                 <!-- msg -->
        </OnMsgCommonInteractorReceiveMsg>

        <OnMsgCommonInteractorStartRotate>      <!-- 开始旋转通知 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> float </Arg>                  <!-- 目标角度Yaw -->
            <Arg> float </Arg>                  <!-- 旋转时长 -->
        </OnMsgCommonInteractorStartRotate>

        <OnMsgCommonInteractorAddButton>        <!-- 添加按钮通知 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> int </Arg>                    <!-- 按钮ID,参考ReqStartCommonInteract中的说明 -->
        </OnMsgCommonInteractorAddButton>

        <OnMsgCommonInteractorRemoveButton>     <!-- 移除按钮通知 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> ListInt </Arg>                <!-- 按钮ID列表,参考ReqStartCommonInteract中的说明 -->
        </OnMsgCommonInteractorRemoveButton>

        <OnMsgCommonInteractorMirrorMove>       <!-- 镜子移动通知 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> PVector2 </Arg>               <!-- 目标位置 -->
            <Arg> float </Arg>                  <!-- 移动时长,秒 -->
        </OnMsgCommonInteractorMirrorMove>

        <OnMsgCommonInteractorActionBegin>      <!-- 服务器广播客户端action开始执行 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> string </Arg>                 <!-- 执行玩家ID -->
            <Arg> int </Arg>                    <!-- ActionID -->
        </OnMsgCommonInteractorActionBegin>

        <OnMsgCommonInteractorActionFinish>     <!-- 服务器通知客户端action结束执行 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> int </Arg>                    <!-- ActionID -->
        </OnMsgCommonInteractorActionFinish>

        <OnMsgCommonInteractorUpdateAttackCnt>  <!-- 更新受击次数 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
            <Arg> int </Arg>                    <!-- 受击次数 -->
        </OnMsgCommonInteractorUpdateAttackCnt>

        <OnMsgCommonInteractorTriggerDestroy>   <!-- 通知客户端销毁事件触发 -->
            <Arg> int </Arg>                    <!-- 通用交互物实例ID -->
        </OnMsgCommonInteractorTriggerDestroy>
    </ClientMethods>

    <ServerMethods>
    </ServerMethods>
</root>
