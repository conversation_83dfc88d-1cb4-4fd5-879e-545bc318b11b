<?xml version="1.0" ?>
<root>
    <Properties>
        <WorldReturnInfo Type="WorldSimpleInfo" Flags="OWN_CLIENT" />
        <ReturnWorldEntityID Type="ENTITY_ID" Flags="OWN_CLIENT" Default="" Persistent="true"/>
        <ReturnWorldTemplateID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>
        <teleportStatus Type="UINT" Default="0" /> <!-- 传送状态 -->
        <TracingInfo Type="TRACING_INFO_DEFINE" Persistent="true" />
        <TracingMapTagID Type="UINT" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <TracingQuestRingID Type="UINT" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <TracingCustomID Type="string" Default="" Flags="OWN_CLIENT" Persistent="true" />
        <TracingCustomList Type="CUSTOM_TRACING_ITEM_DEFINE" Flags="OWN_CLIENT" Persistent="true"/>
        <MapTagHideInfo Type="TRACING_MAP_TAG_HIDE_INFO" Flags="OWN_CLIENT" Persistent="true" />
        <MapTagClassHideInfo Type="TRACING_MAP_TAG_CLASS_HIDE_INFO" Flags="OWN_CLIENT" Persistent="true" />
    </Properties>
    <Implements/>
    <ClientMethods>
       <RetLineList>
            <!--1 result -->
            <Arg> int </Arg>
            <!--2 MapLineInfo -->
            <Arg> SceneInfo </Arg>
        </RetLineList>
        <RetToUserLine>
            <Arg> int </Arg>    <!-- result -->
            <Arg> string </Arg> <!-- AvtarID -->
            <Arg> int </Arg>    <!-- 切分线枚举 -->
        </RetToUserLine>
        <RetToWorld>
            <!--1 result -->
            <Arg> Result </Arg>
        </RetToWorld>

        <RetCustomTracingAdd>
            <!--1 result -->
            <Arg> int </Arg>
            <!--2 uniqID -->
            <Arg> string </Arg>
        </RetCustomTracingAdd>

        <RetCustomTracingUpdate>
            <!--1 result -->
            <Arg> int </Arg>
            <!--2 uniqID -->
            <Arg> string </Arg>
        </RetCustomTracingUpdate>

        <RetSetTracing>
            <!-- tracing type -->
            <Arg> int </Arg>
            <!-- tracing value -->
            <Arg> string </Arg>
        </RetSetTracing>

        <RetRemoveTracing>
            <!-- tracing type -->
            <Arg> int </Arg>
            <!-- tracing value -->
            <Arg> string </Arg>
        </RetRemoveTracing>

        <RetSyncTracingInfo>
            <Arg> TRACING_INFO_DEFINE </Arg>
        </RetSyncTracingInfo>
    </ClientMethods>
    <ServerMethods>
        <ReqLineList CD="1"> <Exposed/>
            <Arg> int </Arg>
        </ReqLineList>
        <ReqSwitchLine CD="1"> <Exposed/>
            <!-- WorldID -->
            <Arg> int </Arg>
        </ReqSwitchLine>
        <ReqToUserLine CD="1"> <Exposed/>
            <Arg> string </Arg> <!-- AvtarID -->
            <Arg> int </Arg>    <!-- 切分线枚举 -->
            <Arg> bool </Arg>   <!-- 是否二次确认 -->
        </ReqToUserLine>
        <ReqQuitWorld CD="1"> <Exposed/>
            <!-- bTemporary-->
            <Arg> bool </Arg>
        </ReqQuitWorld>

        <RetLineListServer>
            <!--1 RetCode -->
            <Arg> int </Arg>
            <!--2 SceneInfo -->
            <Arg> SceneInfo </Arg>
        </RetLineListServer>
        <OnCaptainLeaveDungeon>
        </OnCaptainLeaveDungeon>
        <OnCaptainSwitchLineServer>
            <!--1 WorldID -->
            <Arg> int </Arg>
        </OnCaptainSwitchLineServer>
        <OnCaptainReturnToDungeon>
        </OnCaptainReturnToDungeon>
        <SSReqGetLineSpaceIDServer>
            <Arg> mailbox </Arg>    <!-- 请求者 mailbox -->
            <Arg> int </Arg>       <!-- UserLineType -->
            <Arg> bool </Arg>       <!-- 请求者 是否在大世界 -->
            <Arg> bool </Arg>        <!-- 请求者二次确认 -->
        </SSReqGetLineSpaceIDServer>
        <RetGetLineSpaceIDServer>
            <Arg> int </Arg>    <!-- ErrorCode -->
            <Arg> int </Arg>    <!-- LineSpaceID -->
            <Arg> string </Arg> <!-- 目标玩家 entityID -->
            <Arg> int </Arg>       <!-- UserLineType -->
        </RetGetLineSpaceIDServer>

        <SSTeleportToActivity>
            <Arg> int </Arg> <!-- worldID -->
            <Arg> int </Arg> <!-- regionIndex -->
            <Arg> mailbox </Arg> <!-- worldBox -->
        </SSTeleportToActivity>

        <SRetEnterWorld>
            <Arg> int </Arg> <!-- worldID -->
            <Arg> int </Arg> <!-- regionIndex -->
            <Arg> mailbox </Arg> <!-- worldBox -->
        </SRetEnterWorld>

        <SApplyEnterWorldResult>
            <Arg> int </Arg> <!-- worldID -->
            <Arg> string </Arg> <!-- worldEntityID -->
            <Arg> string </Arg> <!-- tarPid -->
            <Arg> int </Arg> <!-- regionIndex -->
            <Arg> int </Arg> <!-- reminder id -->
            <Arg> VList </Arg>
        </SApplyEnterWorldResult>

        <SRetEnterWorldFail>
            <Arg> int </Arg> <!-- worldID -->
            <Arg> string </Arg> <!-- worldEntityID -->
            <Arg> int </Arg> <!-- regionIndex -->
            <Arg> int </Arg> <!-- remindID -->
        </SRetEnterWorldFail>

        <RetGMAvatarQueryUserMB>
            <Arg> int </Arg>
            <Arg> mailbox </Arg>
        </RetGMAvatarQueryUserMB>

        <ReqGetTargetProcessID>
            <Arg> mailbox </Arg>
        </ReqGetTargetProcessID>

        <RetGetTargetProcessID>
            <Arg> string </Arg>
        </RetGetTargetProcessID>

        <SRetReturnWorld>
            <Arg> ENTITY_ID </Arg> <!-- worldEntityID -->
            <Arg> int </Arg> <!-- ErrorCode -->
            <Arg> mailbox </Arg> <!-- mailbox -->
        </SRetReturnWorld>

        <SApplyReturnWorldFail>
            <Arg> ENTITY_ID </Arg> <!-- worldEntityID -->
            <Arg> int </Arg> <!-- ErrorCode -->
            <Arg> BOOL </Arg> <!-- 保留返回状态 -->
        </SApplyReturnWorldFail>

        <SApplyReturnWorldSuccess>
            <Arg> int </Arg> <!-- worldID -->
            <Arg> mailbox </Arg> <!-- worldBox -->
        </SApplyReturnWorldSuccess>

        <SyncWorldReturnInfo>
            <Arg> WorldSimpleInfo </Arg>
        </SyncWorldReturnInfo>

        <ClearReturnState>
            <Arg> ENTITY_ID </Arg>
        </ClearReturnState>

        <SForceOffload>
            <Arg> int </Arg>
            <Arg> string </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> bool </Arg>
        </SForceOffload>

        <!-- 设置当前追踪的MapTag.ID -->
        <ReqSetTracingMapTag> <Exposed/>
            <!-- MapTag.ID -->
            <Arg> int </Arg>
        </ReqSetTracingMapTag>

        <!-- 设置当前追踪自定义Tag -->
        <ReqSetTracingCustomID> <Exposed/>
            <!-- CUSTOM_TRACING_ITEM.ID -->
            <Arg> string </Arg>
        </ReqSetTracingCustomID>

        <!-- 自定义追踪标签添加 -->
        <ReqCustomTracingAdd> <Exposed/>
            <!-- CustomTag.ID: icon ID -->
            <Arg> int </Arg>
            <!-- mapID -->
            <Arg> int </Arg>
            <!-- text -->
            <Arg> string </Arg>
            <!-- X -->
            <Arg> float </Arg>
            <!-- Y -->
            <Arg> float </Arg>
        </ReqCustomTracingAdd>

        <!-- 自定义追踪标签修改 -->
        <ReqCustomTracingUpdate> <Exposed/>
            <!-- uniqID: 在添加时由服务器生成 -->
            <Arg> string </Arg>
            <!-- text -->
            <Arg> string </Arg>
            <!-- CustomTag.ID : icon ID -->
            <Arg> int </Arg>
        </ReqCustomTracingUpdate>

        <!-- 自定义追踪标签移除 -->
        <ReqCustomTracingRemove> <Exposed/>
            <!-- uniqID -->
            <Arg> string </Arg>
        </ReqCustomTracingRemove>

        <!-- 设置追踪 -->
        <ReqSetTracing> <Exposed/>
            <!-- tracing type -->
            <Arg> int </Arg>
            <!-- tracing value -->
            <Arg> string </Arg>
        </ReqSetTracing>
		<!-- 同步追踪信息-->
		<ReqSyncTracingInfo> <Exposed/>
		</ReqSyncTracingInfo>
		
        <ReqRemoveTracing> <Exposed />
            <!-- tracing type -->
            <Arg> int </Arg>
        </ReqRemoveTracing>

        <!-- 设置map tag 显示隐藏 隐藏为true 取消隐藏为false -->
        <ReqSetMapTagHide> <Exposed/>
            <!-- map tag增量隐藏 -->
            <Arg> DictIntBool </Arg>
            <!-- map tag class增量隐藏 -->
            <Arg> DictIntBool </Arg>
            <!-- 自定义增量隐藏 -->
            <Arg> DictStrBool </Arg>
            <!-- 地图mapID -->
            <Arg> int </Arg>
        </ReqSetMapTagHide>

    </ServerMethods>
</root>
