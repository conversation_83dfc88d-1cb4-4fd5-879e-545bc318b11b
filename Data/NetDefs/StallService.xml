<root>
    <Implements>
        <Interface> Service </Interface>
        <Interface> RefreshComponent </Interface>
    </Implements>

    <Properties>
        <overTimeReturnTaxList Type = "dict" Key="string" Value="UINT" Persistent="true" />      <!-- 超时订单返回押金 -->
    </Properties>

    <ServerMethods>
        <getSellingItemInfo>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>       <!-- itemId -->
            <Arg> UINT </Arg>       <!-- index -->
            <Arg> UUID </Arg>       <!-- item gbId -->
            <Arg> UINT </Arg>       <!-- number -->
        </getSellingItemInfo>

        <buyItem>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>       <!-- player gbId -->
            <Arg> UINT </Arg>       <!-- itemId -->
            <Arg> UINT </Arg>       <!-- index -->
            <Arg> UUID </Arg>       <!-- item gbId -->
            <Arg> UINT </Arg>       <!-- number -->
            <Arg> UINT </Arg>       <!-- price -->
            <Arg> LOCK_MONEY_INFO </Arg>    <!-- 货币加锁信息 -->
        </buyItem>
        <buyItemNew>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>       <!-- orderId -->
            <Arg> NUID </Arg>       <!-- player gbId -->
        </buyItemNew>
        <doBatchBuyStallItems>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>       <!-- orderId -->
            <Arg> NUID </Arg>       <!-- player gbId -->
        </doBatchBuyStallItems>

        <buyStallItemsByList>
            <Arg> string </Arg>                   <!-- orderId -->
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>                     <!-- player gbId -->
            <Arg> UINT </Arg>                     <!-- itemId -->
            <Arg> UINT </Arg>                     <!-- 购买货物总价 -->
            <Arg> TRADE_GOODS_ITEM_LIST </Arg>    <!-- 需要购买的货物物品列表 -->
            <Arg> METHOD_NAME </Arg>              <!-- 回调函数 -->
            <Arg> string </Arg>                   <!-- 业务回调 callback name -->
            <Arg> ARRAY_STRING </Arg>             <!-- 业务自定义数据列表(字符串) -->
        </buyStallItemsByList>

        <putStallItemRequest>
            <Arg> mailbox </Arg>
            <Arg> string </Arg>
            <Arg> string </Arg>
        </putStallItemRequest>

        <playerAddStallItem>
            <Arg> NUID </Arg>
            <Arg> INV_SLOT_VAL </Arg>
            <Arg> UINT </Arg>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> STALL_SELLER_INFO </Arg>  <!-- 附带日志信息 -->
        </playerAddStallItem>

        <getPlayerStallItemInfo>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> UUID </Arg>
            <!-- 业务回调 callback name -->
            <Arg> string </Arg>
            <!-- 业务自定义数据列表(字符串) -->
            <Arg> ARRAY_STRING </Arg>
        </getPlayerStallItemInfo>

        <takeBackStallItem>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> UUID </Arg>
            <Arg> string </Arg>
        </takeBackStallItem>

        <takeBackSoldItemMoney>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </takeBackSoldItemMoney>

        <getPlayerStallInfo>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> table </Arg>
        </getPlayerStallInfo>

        <updateStallInfo>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </updateStallInfo>

        <getPlayerSellRecord>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </getPlayerSellRecord>

        <getItemsInfoByItemIdList>            <!-- 获取指定物品概要信息(如出售数量，是否关注，具体看回包) -->
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>                  <!-- 玩家gbId -->
            <Arg> ARRAY_UINT </Arg>            <!-- 道具id数组 -->
        </getItemsInfoByItemIdList>

        <getPublicityItemsInfoByItemId>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>           <!-- itemId -->
            <Arg> number </Arg>         <!-- begin index -->
            <Arg> number </Arg>         <!-- end index -->
            <Arg> string </Arg>         <!-- callback name -->
            <Arg> BOOL </Arg>           <!-- callback is client -->
            <Arg> UINT </Arg>           <!-- 排序类型 STALL_SORT_TYPE -->
        </getPublicityItemsInfoByItemId>

        <getSellingItemsInfoByItemId>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>           <!-- itemId -->
            <Arg> number </Arg>         <!-- begin index -->
            <Arg> number </Arg>         <!-- end index -->
            <Arg> string </Arg>         <!-- callback name -->
            <Arg> BOOL </Arg>           <!-- callback is client -->
            <Arg> METHOD_ARGS </Arg>    <!-- 回调函数附带参数 -->
            <Arg> number </Arg>         <!-- 价格上限 -->
            <Arg> UINT </Arg>           <!-- 排序类型 STALL_SORT_TYPE -->
            <Arg> string </Arg>         <!-- 业务回调 callback name -->
            <Arg> ARRAY_STRING </Arg>   <!-- 业务自定义数据列表(字符串) -->
        </getSellingItemsInfoByItemId>

        <batchBuyGetSatisfyStallItems>  <!-- 批量购买获取满足条件的道具列表 -->
            <Arg> mailbox </Arg>
            <Arg> string </Arg>         <!-- orderId -->
            <Arg> string </Arg>         <!-- 玩家id -->
            <Arg> UINT </Arg>           <!-- itemId -->
            <Arg> number </Arg>         <!-- number -->
            <Arg> number </Arg>         <!-- priceLimit -->
            <Arg> string </Arg>         <!-- callback name -->
            <Arg> string </Arg>         <!-- 业务回调 callback name -->
            <Arg> ARRAY_STRING </Arg>   <!-- 业务自定义数据列表(字符串) -->
        </batchBuyGetSatisfyStallItems>

        <getPublicityItemsInfoBySecondaryType>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </getPublicityItemsInfoBySecondaryType>

        <getSellingItemsInfoBySecondaryType>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </getSellingItemsInfoBySecondaryType>

        <getAllItemsInfoBySecondaryType>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> NUID </Arg>
        </getAllItemsInfoBySecondaryType>

        <followItem>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> UUID </Arg>
            <Arg> BOOL </Arg>
            <Arg> STALL_FOLLOW_ITEM_PLAYER_INFO </Arg>
        </followItem>

        <cancelFollowItem>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> UUID </Arg>
        </cancelFollowItem>

        <followItemType>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg>
            <Arg> STALL_FOLLOW_ITEM_PLAYER_INFO </Arg>
        </followItemType>

        <cancelFollowItemType>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
            <Arg> UINT </Arg>
        </cancelFollowItemType>

        <getFollowItems>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </getFollowItems>

        <refresh>
        </refresh>

        <gmEndPublicity>
        </gmEndPublicity>

        <gmChangePriceCycleTime>
            <Arg> UINT </Arg>
        </gmChangePriceCycleTime>

        <gmResetRecPrice>
        </gmResetRecPrice>

        <gmClearAllSellingItems>
        </gmClearAllSellingItems>

        <mgrFlipPublicityItem>
            <Arg> mailbox </Arg>
            <Arg> number </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </mgrFlipPublicityItem>

        <gmRemoveStallItem>
            <Arg> UINT </Arg>           <!-- gm回调句柄 -->
            <Arg> NUID </Arg>           <!-- 玩家gbId -->
            <Arg> UUID </Arg>           <!-- 物品gbId -->
            <Arg> UINT </Arg>           <!-- 物品itemId -->
            <Arg> BOOL </Arg>           <!-- 是否返还物品 -->
            <Arg> UINT </Arg>           <!-- hostId -->
        </gmRemoveStallItem>

        <mgrGetStallItemInfo>              <!-- 获取摆摊道具的index等信息 -->
            <Arg> mailbox </Arg>        <!-- 玩家mailBox -->
            <Arg> UINT </Arg>           <!-- itemId -->
            <Arg> UUID </Arg>           <!-- 道具gbId -->
        </mgrGetStallItemInfo>

        <mgrSearchStallEquipmentItems>  <!-- 摆摊装备高级搜索 -->
            <Arg> mailbox </Arg>        <!-- 玩家mailBox -->
            <Arg> STALL_EQUIP_ITEM_SEARCH_PARAM </Arg>  <!-- 搜索参数 -->
            <Arg> BOOL </Arg>           <!-- 是否公示期 -->
            <Arg> number </Arg>         <!-- beginIndex(include) -->
            <Arg> number </Arg>         <!-- endIndex(include) -->
        </mgrSearchStallEquipmentItems>

        <mgrGetStallMoneyInfo>
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </mgrGetStallMoneyInfo>

        <getStallCensorInfo>    <!-- 获取玩家审核中的出售记录 -->
            <Arg> mailbox </Arg>
            <Arg> NUID </Arg>
        </getStallCensorInfo>

        <gmOffShelfPlayerItem>          <!-- 因封禁下架玩家所有道具 -->
            <Arg> NUID </Arg>           <!-- 玩家gbId -->
            <Arg> UINT </Arg>           <!-- 封禁邮件ID -->
            <Arg> NUID </Arg>           <!-- opNUID -->
        </gmOffShelfPlayerItem>

        <stepSysPurchase>       <!-- 分帧进行系统购买 -->
            <Arg> UINT </Arg>           <!-- itemId -->
            <Arg> UINT </Arg>           <!-- 购买数目 -->
            <Arg> NUID </Arg>           <!-- opNUID -->
        </stepSysPurchase>

        <gmLockMoneyWithStallMoneyFirst>    <!-- 冻结货币优先冻结摆摊提取货币 -->
            <Arg> UINT </Arg>           <!-- gm回调句柄 -->
            <Arg> NUID </Arg>           <!-- 玩家gbId -->
            <Arg> UINT </Arg>           <!-- 货币类型 -->
            <Arg> UINT </Arg>           <!-- 冻结秒数 -->
            <Arg> UINT </Arg>           <!-- 冻结金额 -->
            <Arg> UINT </Arg>           <!-- 保释金 -->
            <Arg> string </Arg>         <!-- 封禁原因 -->
            <Arg> UINT </Arg>           <!-- 锁定类型 -->
        </gmLockMoneyWithStallMoneyFirst>

        <gmReleaseStallLockedMoney>    <!-- 解冻货币时释放摆摊冻结货币 -->
            <Arg> NUID </Arg>           <!-- 玩家gbId -->
        </gmReleaseStallLockedMoney>

        <onSoldTradeDirectlyItem>
            <Arg> NUID </Arg>           <!-- sellerGbId -->
            <Arg> NUID </Arg>           <!-- buyerGbId -->
            <Arg> INV_SLOT_VAL </Arg>   <!-- soldItem -->
            <Arg> UINT </Arg>           <!-- 单价 -->
            <Arg> mailbox </Arg>        <!-- seller mailBox -->
            <Arg> NUID </Arg>           <!-- opNUID -->
            <Arg> BOOL </Arg>           <!-- 是否需要审核 -->
            <Arg> string </Arg>         <!-- buyer name -->
        </onSoldTradeDirectlyItem>

        <gmGetSellingItemStatistics>    <!-- 获取摆摊统计数据 -->
            <Arg> table </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </gmGetSellingItemStatistics>

        <gmStopAllAutoSysReplenish>
        </gmStopAllAutoSysReplenish>

        <rePutStallItem>
            <!--1 订单ID -->
            <Arg> string </Arg>
            <!--2 mailbox -->
            <Arg> mailbox </Arg>
            <!--3 玩家id -->
            <Arg> NUID </Arg>
            <!--4 道具gbId -->
            <Arg> UUID </Arg>
            <!--5 price -->
            <Arg> UINT </Arg>
            <!--6 出售者基础信息 -->
            <Arg> STALL_SELLER_INFO </Arg>  <!-- 附带日志信息 -->
        </rePutStallItem>
        <getSellingItemsListByItemId>   <!-- 拉取指定道具在售数量和价格信息 -->
            <Arg> mailbox </Arg>
            <!-- itemId -->
            <Arg> UINT </Arg>
            <!-- itemNum -->
            <Arg> UINT </Arg>
            <!-- callback name -->
            <Arg> string </Arg>
            <!-- 业务回调 callback name -->
            <Arg> string </Arg>
            <!-- 回调带回来的参数信息 -->
            <Arg> ARRAY_STRING </Arg>
        </getSellingItemsListByItemId>
        <gmStallSystemBuy>
            <Arg> UINT </Arg>           <!-- itemId -->
        </gmStallSystemBuy>
        <gmStallSystemSell>
            <Arg> UINT </Arg>           <!-- itemId -->
        </gmStallSystemSell>
        <SSReqGMGetInfoAndShow>                 <!-- 获取交易所数据(聊天频道打印) -->
            <!--1 avatarId -->
            <Arg> UUID </Arg>
            <!--2 itemId -->
            <Arg> UINT </Arg>
        </SSReqGMGetInfoAndShow>
    </ServerMethods>
</root>