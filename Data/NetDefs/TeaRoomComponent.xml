<?xml version="1.0" ?>
<root>
    <Properties>
        <teaRoomID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <teaRoomFavoritesCount Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/>
        <teaRoomFavorites Type="DictIntUInt" Flags="SERVER_ONLY" Persistent="true" Default="nil"/> <!-- [key: 玩家收藏的房间, value: 创建房间的时间戳] -->
        <favoriteRoomStartTime Type="int" Flags="SERVER_ONLY" Default="0"/> <!-- 收藏房间的起始时间戳(秒) -->
        <teaRoomFavoriteInfos Type="TEA_ROOM_INFO_LIST" Flags="SERVER_ONLY" Persistent="false" Default="nil"/>
        <isParty Type="bool" Flags="SERVER_ONLY" Default="false"/> <!-- 当前房间是否为派对房间 -->
    </Properties>
    <Implements/>

    <ClientMethods>
        <!-- 更新茶壶系统入口界面的房间列表 -->
        <UpdateTeaRoomRoomList>
            <!-- 房间信息列表 -->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
        </UpdateTeaRoomRoomList>
        <!-- 进入房间的返回询问离开房间 -->
        <OnMsgEnterRoomToLeaveRoom>
            <!-- 原房间ID -->
            <Arg>int</Arg>
            <!-- 目标房间ID -->
            <Arg>int</Arg>
            <!-- 是否为派对房间 -->
            <Arg>bool</Arg>
        </OnMsgEnterRoomToLeaveRoom>
        <!-- 成功进入房间 -->
        <OnMsgEnterRoom>
            <!-- 房间详细信息 -->
            <Arg>TEA_ROOM_DETAILED_INFO</Arg>
            <!-- 是否为收藏房间 -->
            <Arg>bool</Arg>
        </OnMsgEnterRoom>
        <!-- 通知房间其他成员玩家进入房间 -->
        <OnMsgNotifyMemberEnterRoom>
            <!-- 进入房间的ID -->
            <Arg>int</Arg>
            <!-- 进入玩家的信息 -->
            <Arg>TEA_ROOM_MEMBER_INFO</Arg>
            <!-- 进房后的热度值 -->
            <Arg>int</Arg>
            <!-- 进房后的房间人数 -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberEnterRoom>
        <!-- 玩家离开房间 -->
        <OnMsgLeaveRoom>
            <!-- 目标房间ID -->
            <Arg>int</Arg>
        </OnMsgLeaveRoom>
        <!-- 通知房间其他成员玩家离开房间 -->
        <OnMsgNotifyMemberLeaveRoom>
            <!-- 进入房间的ID -->
            <Arg>int</Arg>
            <!-- 退出玩家的ID -->
            <Arg>string</Arg>
            <!-- 退出玩家的ShortUid -->
            <Arg>int</Arg>
            <!-- 退房后的热度值 -->
            <Arg>int</Arg>
            <!-- 退房后的房间人数 -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberLeaveRoom>
        <!-- 成功创建房间 -->
        <OnMsgCreateRoom>
            <!-- 房间名称 -->
            <Arg>string</Arg>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 是否分享到世界频道 -->
            <Arg>bool</Arg>
            <!-- 是否分享到朋友圈 -->
            <Arg>bool</Arg>
        </OnMsgCreateRoom>
        <OnMsgReqTeaRoomMessageTop>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 置顶消息内容 -->
            <Arg>string</Arg>
            <!-- 置顶消息作者 -->
            <Arg>string</Arg>
            <!-- 置顶消息ID -->
            <Arg>string</Arg>
        </OnMsgReqTeaRoomMessageTop>
        <OnMsgReqTeaRoomCancelMessageTop>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgReqTeaRoomCancelMessageTop>
        <OnMsgNotifyMemberGiveGift>
            <!-- 赠礼信息 -->
            <Arg>TEAROOM_GIVE_GIFT_INFO</Arg>
        </OnMsgNotifyMemberGiveGift>
        <OnMsgTeaRoomReconnect>
            <!-- 房间详细信息 -->
            <Arg>TEA_ROOM_DETAILED_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg> bool </Arg>
            <!-- 是否为收藏房间 -->
            <Arg> bool </Arg>
        </OnMsgTeaRoomReconnect>
        <!-- 通知房间成员变成全员自由发言 -->
        <OnMsgNotifySpeechFree>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgNotifySpeechFree>
        <!-- 通知房间成员玩家的发言状态改变 -->
        <OnMsgNotifyMemberSpeechState>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 玩家的信息 -->
            <Arg>TEA_ROOM_MEMBER_INFO</Arg>
        </OnMsgNotifyMemberSpeechState>
        <!-- 通知成员房间成员列表信息 -->
        <OnMsgNotifyMemberInfoList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员信息列表 -->
            <Arg>TEA_ROOM_MEMBER_INFO_LIST</Arg>
        </OnMsgNotifyMemberInfoList>
        <!-- 设置房间发言权限的回调 -->
        <OnMsgSetTeaRoomMemberPrivilegeCallback>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 上麦的成员列表 -->
            <Arg>ARRAY_STRING</Arg>
            <!-- 被下的成员列表 -->
            <Arg>ARRAY_STRING</Arg>
        </OnMsgSetTeaRoomMemberPrivilegeCallback>
        <!-- 通知所有成员新的麦序 -->
         <OnMsgNotifyAllMemberOrderList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 麦序列表 -->
            <Arg>ListStr</Arg>
        </OnMsgNotifyAllMemberOrderList>
        <!-- 通知所有房间成员房主变更 -->
        <OnMsgNotifyModifyMaster>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房主的信息 -->
            <Arg>TEA_ROOM_MEMBER_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg>bool</Arg>
        </OnMsgNotifyModifyMaster>
        <!-- 通知房主上麦申请 -->
        <OnMsgApplicationInfo2Master>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 个人申请信息 -->
            <Arg>APPLICATION_INFO</Arg>
        </OnMsgApplicationInfo2Master>
        <!-- 同步房主上麦申请列表 -->
        <OnMsgApplicationList2Master>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 申请列表信息 -->
            <Arg>APPLICATION_INFO_LIST</Arg>
        </OnMsgApplicationList2Master>
        <!-- 同步房主黑名单列表 -->
        <OnMsgBlackList2Master>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 黑名单 -->
            <Arg>TEAROOM_BLACK_LIST</Arg>
        </OnMsgBlackList2Master>
        <!-- 通知房主将成员拉入黑名单 -->
        <OnMsgNotifyMasterAddBlackList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家黑名单信息 -->
            <Arg>MemberShowInfo</Arg>
        </OnMsgNotifyMasterAddBlackList>
        <!-- 通知房主将成员从黑名单中删除 -->
        <OnMsgNotifyMasterDeleteBlackList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家ID -->
            <Arg>string</Arg>
        </OnMsgNotifyMasterDeleteBlackList>
        <!-- 通知所有成员房间名字修改 -->
        <OnMsgNotifyModifyRoomName>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间名称 -->
            <Arg>string</Arg>
        </OnMsgNotifyModifyRoomName>
        <!-- 通知所有成员房间类型修改 -->
        <OnMsgNotifyModifyRoomType>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间类型 -->
            <Arg>int</Arg>
        </OnMsgNotifyModifyRoomType>
        <!-- 通知所有成员房间热度值 -->
        <OnMsgNotifyModifyRoomHotValue>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间热度 -->
            <Arg>int</Arg>
            <!-- 房间Tag -->
            <Arg>int</Arg>
        </OnMsgNotifyModifyRoomHotValue>
        <!-- 通知所有成员某玩家的禁言属性 -->
        <OnMsgNotifyGagMessage>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 被禁言玩家的ID -->
            <Arg>string</Arg>
            <!-- 禁言属性 -->
            <Arg>bool</Arg>
        </OnMsgNotifyGagMessage>
        <!-- 通知房主删除申请列表中的玩家信息 -->
        <OnMsgDeleteApplicationInfos>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 申请列表中的玩家ID -->
            <Arg>ARRAY_STRING</Arg>
        </OnMsgDeleteApplicationInfos>
        <!-- 通知所有成员玩家升级 -->
        <OnMsgNotifyMemberLevelup>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家的ID -->
            <Arg>string</Arg>
            <!-- 等级 -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberLevelup>
        <!-- 通知所有成员玩家改名 -->
        <OnMsgNotifyMemberNewName>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家的ID -->
            <Arg>string</Arg>
            <!-- 名字 -->
            <Arg>string</Arg>
        </OnMsgNotifyMemberNewName>
        <!-- 通知玩家是否为房主-->
        <OnMsgIsTeaRoomMaster>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 是否为房主 -->
            <Arg>bool</Arg>
        </OnMsgIsTeaRoomMaster>
         <!-- 通知玩家所有的茶壶信息-->
        <OnMsgGetAllTeaRoomInfo>
            <Arg>TEA_ROOM_ALL_INFO</Arg>
        </OnMsgGetAllTeaRoomInfo>
        <!-- 通知客户端收藏房间成功 -->
        <OnMsgFavoriteTeaRoomCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgFavoriteTeaRoomCallBack>
        <!-- 获取收藏夹 -->
        <OnMsgGetChatRoomFavoritesCallBack>
            <!-- 普通聊天室信息-->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
            <!-- 派对房间信息 -->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
            <!-- 是否删除收藏夹 -->
            <Arg>bool</Arg>
        </OnMsgGetChatRoomFavoritesCallBack>
        <!-- 通知客户端取消收藏房间成功 -->
        <OnMsgCancelTeaRoomFavoriteCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgCancelTeaRoomFavoriteCallBack>
        <!-- 查找成员名字 -->
        <OnMsgTeaRoomFindMemberName>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 搜索内容的shortUid列表 -->
            <Arg>ListInt</Arg>
        </OnMsgTeaRoomFindMemberName>

    </ClientMethods>

    <ServerMethods>
        <!-- 创建房间 -->
        <ReqCreateTeaRoom CD="1"> <Exposed/>
            <!-- 房间名称 -->
            <Arg>string</Arg>
            <!-- 房间类型 -->
            <Arg>int</Arg>
            <!-- 房间标签 -->
            <Arg>int</Arg>
            <!-- 是否分享到世界频道 -->
            <Arg>bool</Arg>
            <!-- 是否分享到朋友圈 -->
            <Arg>bool</Arg>
        </ReqCreateTeaRoom>
        <!-- 进入房间 -->
        <ReqEnterTeaRoom CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqEnterTeaRoom>
        <!-- 快速加入 -->
        <ReqEnterQuickly CD="1"> <Exposed/>
        </ReqEnterQuickly>
        <!-- 离开并进入新的房间 -->
        <ReqLeaveAndEnterRoom CD="1"> <Exposed/>
            <!-- 离开房间ID -->
            <Arg>int</Arg>
            <!-- 加入房间ID -->
            <Arg>int</Arg>
            <!--新房间是否为派对 -->
            <Arg>bool</Arg>
        </ReqLeaveAndEnterRoom>
        <!-- 离开房间 -->
        <ReqLeaveTeaRoom CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqLeaveTeaRoom>
        <!-- 查找房间 -->
        <ReqSearchTeaRoom CD="1"> <Exposed/>
             <!-- 搜索字符 -->
            <Arg>string</Arg>
             <!-- 是否查看本服 false为全服 true为本服 -->
            <Arg>bool</Arg>
            <!-- 筛选列表 默认全选 -->
            <Arg>ARRAY_BOOL</Arg>
        </ReqSearchTeaRoom>

        <SSOnMsgEnterTeaRoomCallBack>
            <!-- 新房间ID -->
            <Arg>int</Arg>
            <!-- 是否进入房间成功 -->
            <Arg>bool</Arg>
        </SSOnMsgEnterTeaRoomCallBack>

        <SSOnMsgCacheServiceGetRoomIDCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 建房时的房间信息 -->
            <Arg>CREATE_CHAT_ROOM_INFO</Arg>
            <!-- 建房的顺序 -->
            <Arg>int</Arg>
        </SSOnMsgCacheServiceGetRoomIDCallBack>

        <SSOnMsgCreateTeaRoomCallBack>
            <!-- 是否创建房间成功 -->
            <Arg>bool</Arg>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </SSOnMsgCreateTeaRoomCallBack>

        <SSOnMsgLeaveTeaRoomCallback>
            <!-- 退出房间ID -->
            <Arg>int</Arg>
            <!-- 进入房间ID -->
            <Arg>int</Arg>
            <!-- 是否随机进入一个新的房间 -->
            <Arg>bool</Arg>
            <!--新房间是否为派对 -->
            <Arg>bool</Arg>
        </SSOnMsgLeaveTeaRoomCallback>
        <!-- 修改房间的名字 -->
        <ReqModifyTeaRoomName CD="1"> <Exposed/>
             <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房间名字 -->
            <Arg>string</Arg>
        </ReqModifyTeaRoomName>
        <!-- 修改房间的类型 -->
        <ReqModifyTeaRoomType CD="1"> <Exposed/>
             <!-- 房间ID -->
            <Arg>int</Arg>
             <!-- 房间类型 -->
            <Arg>int</Arg>
        </ReqModifyTeaRoomType>
        <!-- 成员申请上麦 -->
        <ReqTeaRoomApplyOpenMic CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 申请理由 -->
            <Arg>string</Arg>
        </ReqTeaRoomApplyOpenMic>
        <!-- 成员修改发言状态 -->
        <ReqTeaRoomMemberModifySpeechState CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言状态 -->
            <Arg>int</Arg>
        </ReqTeaRoomMemberModifySpeechState>
        <!-- 房主设置全员自由发言 -->
        <ReqSetTeaRoomSpeechFree CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqSetTeaRoomSpeechFree>
        <!-- 房主设置玩家的发言权限 -->
        <ReqSetTeaRoomMemberPrivilege CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言权限的列表 entityID -> bool -->
            <Arg>DictStrBool</Arg>
        </ReqSetTeaRoomMemberPrivilege>
        <!-- 房主设置玩家的发言顺序 -->
        <ReqSetTeaRoomMemberOrder CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言顺序的列表 -->
            <Arg>ListStr</Arg>
        </ReqSetTeaRoomMemberOrder>
        <ReqTransferTeaRoomMaster CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>

        </ReqTransferTeaRoomMaster>

        <ReqTeaRoomPullBlacklist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqTeaRoomPullBlacklist>
        <ReqTeaRoomCancelBlacklist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqTeaRoomCancelBlacklist>
        <ReqTeaRoomPullGaglist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqTeaRoomPullGaglist>
        <ReqTeaRoomCancelGaglist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqTeaRoomCancelGaglist>
        <ReqTeaRoomMessageTop CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 文本消息 -->
            <Arg>string</Arg>
            <!-- 文本消息作者ID -->
            <Arg>string</Arg>
            <!-- 文本消息ID -->
            <Arg>string</Arg>
        </ReqTeaRoomMessageTop>
        <ReqTeaRoomCancelMessageTop CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqTeaRoomCancelMessageTop>
        <!-- 茶壶赠礼 -->
        <ReqTeaRoomGiveGift CD="0.17"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 赠礼对象ID -->
            <Arg> ENTITY_ID </Arg>
            <!-- 礼物数量 -->
            <Arg> UINT </Arg>
            <!-- GiftID -->
            <Arg> UINT </Arg>
        </ReqTeaRoomGiveGift>
        <!-- 房主拉取黑名单列表 -->
        <ReqTeaRoomGetBlackList> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqTeaRoomGetBlackList>
        <!-- 房主拉取申请列表 -->
        <ReqTeaRoomGetApplicationList> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqTeaRoomGetApplicationList>
        <!-- 获取指定id列表的玩家信息 -->
        <ReqTeaRoomGetMemberInfo> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家列表 -->
            <Arg>ARRAY_STRING</Arg>
        </ReqTeaRoomGetMemberInfo>
        <!-- 获取指定shortUid列表的玩家信息 -->
        <ReqTeaRoomGetMemberInfoByShortUid> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家列表 -->
            <Arg>ARRAY_UINT</Arg>
        </ReqTeaRoomGetMemberInfoByShortUid>
        <!-- 房主清理申请列表的内容 -->
        <ReqTeaRoomCleanApplicationList> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 同意玩家ID列表 -->
            <Arg>ARRAY_STRING</Arg>
            <!-- 拒绝玩家ID列表 -->
            <Arg>ARRAY_STRING</Arg>
        </ReqTeaRoomCleanApplicationList>
        <!-- 麦序模式下  玩家下麦操作 -->
        <ReqTeaRoomMemberCloseMic> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqTeaRoomMemberCloseMic>
        <ReqIsTeaRoomMaster> <Exposed/>
        </ReqIsTeaRoomMaster>
        <ReqGetAllTeaRoomInfo> <Exposed/>
        </ReqGetAllTeaRoomInfo>
        <!-- 收藏房间 -->
        <ReqFavoriteTeaRoom CD="1"> <Exposed/>
        </ReqFavoriteTeaRoom>

        <SSOnMsgFavoriteTeaRoomCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房间version -->
            <Arg>int</Arg>
        </SSOnMsgFavoriteTeaRoomCallBack>

        <SSOnMsgUpdateTeaRoomFavoritesCallBack>
            <!-- 收藏数量 -->
            <Arg>int</Arg>
            <!-- 新的收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!-- 新的收藏房间信息 -->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
            <!-- 是否删除收藏夹 -->
            <Arg>bool</Arg>
        </SSOnMsgUpdateTeaRoomFavoritesCallBack>

        <SSOnMsgCheckTeaRoomFavoritesCallBack>
            <!-- 收藏数量 -->
            <Arg>int</Arg>
            <!-- 新的收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!-- 加入房间ID -->
        </SSOnMsgCheckTeaRoomFavoritesCallBack>

        <!-- 取消普通聊天室收藏 -->
        <ReqCancelTeaRoomFavorite CD="1"> <Exposed/>
        </ReqCancelTeaRoomFavorite>

        <!-- 删除收藏夹内容-->
        <ReqDeleteFavorites CD="1"> <Exposed/>
            <!-- 普通聊天室收藏夹 -->
            <Arg>ListInt</Arg>
            <!-- 派对收藏夹 -->
            <Arg>ListInt</Arg>
        </ReqDeleteFavorites>
        
        <!-- 获取收藏房间列表 -->
        <ReqGetChatRoomFavorites CD="1"> <Exposed/>
        </ReqGetChatRoomFavorites>

        <!-- 查找玩家名字-->
        <ReqTeaRoomFindMemberName CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 模式串 -->
            <Arg>string</Arg>
        </ReqTeaRoomFindMemberName>
    </ServerMethods>
</root>

