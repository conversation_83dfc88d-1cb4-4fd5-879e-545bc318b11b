<?xml version="1.0" ?>
<root>
    <Properties>
        <!--成就配置表的版本号，用于优化每次登录都要遍历配置表注册监听-->
        <achievementMd5 Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <!-- 正在进行的成就，key: 成就编号，value: 更新的时间戳，客户端用于排序 -->
        <achievements Type="DictIntInt" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!-- 已完成的成就，key: 成就编号，value: 完成时间戳（未领取奖励的完成时间戳是奇数，已领取的是偶数）-->
        <finishedAchievements Type="DictIntInt" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!-- 关注的成就，key: 成就编号，value: 关注的时间戳 -->
        <collectAchievements Type="DictIntInt" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!--成就的大类型信息（大类型的积分和奖励位标识）-->
        <achievementTypeInfo Type="ACHIEVEMENT_TYPE_INFO_MAP" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
    </Properties>

    <Implements/>
    <ClientMethods>
        <RetCollectAchievement>
            <Arg> int </Arg>      <!-- error code -->
            <Arg> int </Arg>      <!-- 成就编号 -->
            <Arg> int </Arg>      <!-- 大于0为收藏时间，等于0为取消收藏 -->
        </RetCollectAchievement>
        
        <RetRewardAchievementLevelOne>
            <Arg> int </Arg>      <!-- error code -->
            <Arg> int </Arg>      <!-- 成就类型 -->
            <Arg> ACHIEVEMENT_TYPE_INFO </Arg>  <!-- 成就类型对应的信息（积分和奖励标识） -->
        </RetRewardAchievementLevelOne>
        
        <RetRewardAchievementLevelAll>
            <Arg> int </Arg>      <!-- error code -->
            <Arg> int </Arg>      <!-- 成就类型 -->
            <Arg> ACHIEVEMENT_TYPE_INFO </Arg>  <!-- 成就类型对应的信息（积分和奖励标识） -->
        </RetRewardAchievementLevelAll>
        
        <RetRewardAchievementOne>
            <Arg> int </Arg>             <!-- error code -->
            <Arg> DictIntInt </Arg>      <!-- key: 成就编号, value: 完成时间，如果为奇数，则代表已完成未领取奖励，客户端要显示reminder，为偶数则代表已领取奖励 -->
        </RetRewardAchievementOne>
        
        <RetRewardAchievementAll>
            <Arg> int </Arg>             <!-- error code -->
            <Arg> DictIntInt </Arg>      <!-- key: 成就编号, value: 完成时间，如果为奇数，则代表已完成未领取奖励，客户端要显示reminder，为偶数则代表已领取奖励 -->
        </RetRewardAchievementAll>

        <!--同步成就的所有属性-->
        <OnMsgSyncAchievementInfo>
            <Arg> DictIntInt </Arg>                 <!-- 正在进行的成就的条件更新时间 -->
            <Arg> DictIntInt </Arg>                 <!-- 已完成的成就 -->
            <Arg> DictIntInt </Arg>                 <!-- 关注的成就 -->
            <Arg> ACHIEVEMENT_TYPE_INFO_MAP </Arg>  <!-- 成就的大类型信息 -->
        </OnMsgSyncAchievementInfo>

        <!-- 同步某个已完成的成就 -->
        <OnMsgFinishedAchievementUpdate>
            <Arg> int </Arg>             <!-- 成就编号 -->
            <Arg> int </Arg>             <!-- 完成时间，如果为奇数，则代表已完成未领取奖励，客户端要显示reminder，为偶数则代表已领取奖励 -->
        </OnMsgFinishedAchievementUpdate>

        <!-- 同步大类型信息 -->
        <OnMsgAchievementTypeUpdate>
            <Arg> int </Arg>                    <!-- 成就类型 -->
            <Arg> ACHIEVEMENT_TYPE_INFO </Arg>  <!-- 成就类型对应的信息（积分和奖励标识） -->
        </OnMsgAchievementTypeUpdate>

        <!-- 同步某个正在进行的成就条件的更新时间 -->
        <OnMsgAchievementTimeUpdate>
            <Arg> int </Arg>      <!-- 成就编号 -->
            <Arg> int </Arg>      <!-- 条件的变更时间 -->
        </OnMsgAchievementTimeUpdate>
    </ClientMethods>

    <ServerMethods>
        <!--收藏成就-->
        <ReqCollectAchievement> <Exposed/>
            <Arg> int </Arg>      <!-- 成就编号 -->
            <Arg> bool </Arg>     <!-- 其中true代表收藏，false代表取消收藏 -->
        </ReqCollectAchievement>

        <!--领取大类新的某个奖励-->
        <ReqRewardAchievementLevelOne> <Exposed/>
            <Arg> int </Arg>      <!-- 大类型 -->
            <Arg> int </Arg>      <!-- 第几个奖励 -->
        </ReqRewardAchievementLevelOne>

        <!--一键领取大类型的等级奖励-->
        <ReqRewardAchievementLevelAll> <Exposed/>
            <Arg> int </Arg>      <!-- 大类型 -->
        </ReqRewardAchievementLevelAll>

        <!--领取某个已完成的成就奖励-->
        <ReqRewardAchievementOne> <Exposed/>
            <Arg> int </Arg>      <!-- 成就编号 -->
        </ReqRewardAchievementOne>

        <!--领取某个大类型下的所有已完成的成就奖励-->
        <ReqRewardAchievementAll> <Exposed/>
            <Arg> int </Arg>      <!-- 大类型 -->
        </ReqRewardAchievementAll>
    </ServerMethods>
</root>