<?xml version="1.0" ?>
<root>
    <Properties>
        <!--<refreshShopsInfo Type="REFRESH_SHOP_DATA_MAP" Flags="SERVER_ONLY" Persistent="true" /> -->                   <!-- 随机商店商品 -->
        <!-- <refreshShopCellRefreshTimes Type="dict" Key="UINT" Value="UINT" Persistent="true" />-->  <!-- 特殊商品周期内已刷新次数 -->
        <!--<refreshShopDayManualTimes Type="dict" Key="UINT" Value="UINT" Flags="SERVER_ONLY" Persistent="true" />-->    <!-- 随机商店手动刷新次数 -->
    </Properties>

    <Implements/>
    <ClientMethods>
        <OnMsgRefreshShopItemsInfoSync>               <!-- 获取随机商店列表 -->
            <Arg> REFRESH_SHOP_DATA </Arg>            <!-- 商店具体信息 -->
        </OnMsgRefreshShopItemsInfoSync>

        <RetBuyRefreshShopItem>                       <!-- 购买随机商店道具 -->
            <Arg> UINT </Arg>                         <!-- cellId -->
            <Arg> UINT </Arg>                         <!-- shopId -->
            <Arg> UINT </Arg>                         <!-- 消耗的货币数量 -->
        </RetBuyRefreshShopItem>
    </ClientMethods>

    <!-- Exposed 待处理 -->
    <ServerMethods>
        <ReqBuyRefreshShopItem CD="0.17"> <Exposed/>            <!-- 购买随机商店道具 -->
            <Arg> UINT </Arg>                         <!-- cellId -->
            <Arg> UINT </Arg>                         <!-- 刷新商店的版本号 -->
        </ReqBuyRefreshShopItem>

        <ReqUpdateRefreshShopItemsInfo> <Exposed/>    <!-- 获取随机商店列表 -->
            <Arg> UINT </Arg>                         <!-- shopId -->
        </ReqUpdateRefreshShopItemsInfo>

        <ReqRefreshShopAtOnce CD="1"> <Exposed/>             <!-- 立即刷新随机商店 -->
            <Arg> UINT </Arg>                         <!-- shopId -->
        </ReqRefreshShopAtOnce>
    </ServerMethods>
</root>