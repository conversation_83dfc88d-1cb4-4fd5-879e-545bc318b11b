<?xml version="1.0" ?>
<root>
    <Properties>
		<buffInstDict Type="BuffInstDict" Flags="SERVER_ONLY" />
		<buffGroupDict Type="BuffGroupDict" Flags="SERVER_ONLY" Default="nil"/>
		<shieldBuffList Type="BuffShieldList" Flags="SERVER_ONLY" Default="nil"/>
        <buffOverrideDict Type="BuffOverrideDict" Flags="SERVER_ONLY" Default="nil"/>
		<offlineKeepBuffs Type="BuffInstDict" Flags="SERVER_ONLY" Persistent="true" Default="nil"/>
		<balanceKeepedBuffs Type="BuffInstDict" Flags="SERVER_ONLY" Default="nil"/>
		<immuneBuffTags Type="DictIntInt" Flags="SERVER_ONLY"/>
		<buffInstsAsInstigator Type="BuffTraceInfoList" Flags="SERVER_ONLY" Default="nil"/>
		<addOnceBuffList Type="DictIntInt" Flags="SERVER_ONLY" Default="nil"/>
	</Properties>
	
	<Implements/>
	<ClientMethods>
		<OnMsgRebuildBuffNew>
			<!--1 RebuildBuffs -->
			<Arg> BuffSyncList </Arg>
		</OnMsgRebuildBuffNew>
		<OnMsgRebuildOthersBuffNew>
			<!--1 OtherAvatarID-->
            <Arg> string</Arg>
			<!--2 RebuildBuffs -->
			<Arg> BuffSyncList </Arg>
		</OnMsgRebuildOthersBuffNew>
		<OnMsgAddBuffNew> <EnableNilArgs/>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 triggerID -->
			<Arg> int </Arg>
			<!--4 level -->
			<Arg> int </Arg>
			<!--5 layer -->
			<Arg> int </Arg>
			<!--6 maxLayer -->
			<Arg> int </Arg>
			<!--7 totalLife -->
			<Arg> float </Arg>
			<!--8 totalStartTimeStamp -->
			<Arg> int </Arg>
			<!--9 linkedTargetId -->
			<Arg> int </Arg>
			<!--10 rootSkillID -->
			<Arg> int </Arg>
		</OnMsgAddBuffNew>
		<OnMsgRemoveBuffNew>
			<!--1 buffID -->
			<Arg> int </Arg>
            <!--2 instigatorID -->
            <Arg> int </Arg>
			<!--3 reason -->
			<Arg> int </Arg>
		</OnMsgRemoveBuffNew>
		<OnMsgBuffLayerChangeNew>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 oldValue -->
			<Arg> int </Arg>
			<!--4 newValue -->
			<Arg> int </Arg>
		</OnMsgBuffLayerChangeNew>
		<OnMsgBuffLifeChangeNew>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 newValue -->
			<Arg> float </Arg>
		</OnMsgBuffLifeChangeNew>
		<OnMsgBuffTotalLifeChangeNew>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 oldValue -->
			<Arg> float </Arg>
			<!--4 newValue -->
			<Arg> float </Arg>
			<!--5 totalStartTimeStamp -->
			<Arg> int </Arg>
		</OnMsgBuffTotalLifeChangeNew>
		<OnMsgBuffLevelChange>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 oldValue -->
			<Arg> int </Arg>
			<!--4 newValue -->
			<Arg> int </Arg>
		</OnMsgBuffLevelChange>
		<OnMsgBuffChangeLinkedTarget>
			<!--1 buffID -->
			<Arg> int </Arg>
			<!--2 instigatorID -->
			<Arg> int </Arg>
			<!--3 linkTargetID -->
			<Arg> int </Arg>
		</OnMsgBuffChangeLinkedTarget>

		<OnMsgSyncTargetBuffDebugInfo>
			<Arg> DictIntStr </Arg>
		</OnMsgSyncTargetBuffDebugInfo>
	</ClientMethods>

    <ServerMethods>
		<ReqSyncTargetBuffDebugInfo> <Exposed />
			<Arg> string </Arg>
		</ReqSyncTargetBuffDebugInfo>
		<ReqCameraShotContainObj> <Exposed />
		</ReqCameraShotContainObj>
    </ServerMethods>
</root>
