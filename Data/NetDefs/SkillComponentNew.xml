<?xml version="1.0" ?>
<root>
    <Properties>
		<skillCDInfoDict Type="SkillCDInfoDictNew" Flags="SERVER_ONLY" />
		<skillChargeInfoDict Type="SkillChargeInfoDict" Flags="SERVER_ONLY" Default="nil"/>
		<skillGroupCDDict Type="SkillCDInfoDictNew" Flags="SERVER_ONLY" Default="nil"/>
		<offlineKeepSkillInfo Type="OfflineKeepSkillInfo" Flags="SERVER_ONLY" Persistent="true" Default="nil"/>
		<skillModifyDict Type="SkillModifyDict" Flags="SERVER_ONLY" Default="nil"/>
		<skillOverrideDict Type="SkillOverrideDict" Flags="SERVER_ONLY" Default="nil"/>
		<disableSkillTypes Type="dict" Key="int" Value="DictIntInt" Flags="SERVER_ONLY" ActorAttr="true" />
		<disableAllSkillTypeCount Type="int" Flags="SERVER_ONLY" Default="nil" ActorAttr="true" />
		<disableSkillIDs Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Default="nil" ActorAttr="true" />
		<disableSkillIDWhiteList Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Default="nil" ActorAttr="true" />
		<disableSkillTags Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Default="nil" ActorAttr="true" />
	</Properties>
	
	<Implements/>
	<ClientMethods>
		<RetCastSkillFailNew>
			<!--1 retCode -->
			<Arg> int </Arg>
			<!--2 skillID -->
			<Arg> int </Arg>
			<!--3 insID -->
			<Arg> int </Arg>
		</RetCastSkillFailNew>
		<OnMsgCastSkillNew> <EnableNilArgs/>
			<!--1 skillID -->
			<Arg> int </Arg>
			<!--2 lockTarget -->
			<Arg> int </Arg>
			<!--3 inputPos -->
			<Arg> PVector3 </Arg>
			<!--4 inputDir -->
			<Arg> PRotator </Arg>
			<!--5 stateID -->
			<Arg> int </Arg>
			<!--6 insID -->
			<Arg> int </Arg>
		</OnMsgCastSkillNew>
		<RetCastSkillSuccessNew> <EnableNilArgs/>
			<!--1 skillID -->
			<Arg> int </Arg>
			<!--2 insID -->
			<Arg> int </Arg>
			<!--3 stateID -->
			<Arg> int </Arg>
		</RetCastSkillSuccessNew>
		<OnMsgStopSkill>
			<!--1 skillID -->
			<Arg> int </Arg>	
			<!--2 reason -->
			<Arg> int </Arg>
			<!--3 insID -->
			<Arg> int </Arg>
		</OnMsgStopSkill>
		<OnMsgSkillEnterCDNew>
			<!--1 skillID -->
			<Arg> int </Arg>						
			<!--2 totalCoolDown-->
			<Arg> float </Arg>	
			<!--3 CDEndTimestamp-->
			<Arg> int </Arg>
		</OnMsgSkillEnterCDNew>
		<OnMsgSkillEnterGCD>
			<!--1 groupID -->
			<Arg>int</Arg>
			<!--2 groupCoolDowm -->
			<Arg>float</Arg>
			<!--3 CDEndTimestamp -->
			<Arg>int</Arg>
		</OnMsgSkillEnterGCD>
		<ConsumeCharge>
			<!--1 skillID-->
			<Arg>int</Arg>
			<!--2 chargeStartTimeStamp-->
			<Arg>int</Arg>
			<!--3 chargeNum-->
			<Arg>int</Arg>
		</ConsumeCharge>
		<OnMsgSkillReviveConfirmReq>
			<!--1 srcEntityId -->
			<Arg> string </Arg>
			<!--2 reason -->
			<Arg> int </Arg>
		</OnMsgSkillReviveConfirmReq>
		<OnMsgReviveConfirmRemove/>

		<OnMsgLaserState>
			<!-- bClose -->
			<Arg> bool </Arg>
		</OnMsgLaserState>

	</ClientMethods>

    <ServerMethods>
		<ReqCastSkillNew> <Exposed/> <EnableNilArgs/>
			<!--1 skillID -->
			<Arg> int </Arg>
			<!--2 lockTarget -->
			<Arg> int </Arg>
			<!--3 inputPos -->
			<Arg> PVector3 </Arg>
			<!--4 inputDir -->
			<Arg> PRotator </Arg>
			<!--5 insID -->
			<Arg> int </Arg>
		</ReqCastSkillNew>
		<ReqBreakSkill> <Exposed/> <EnableNilArgs/>
			<!--1 skillID -->
			<Arg> int </Arg>
			<!--2 runningTime -->
			<Arg> float </Arg>
			<!--3 reason -->
			<Arg> int </Arg>
		</ReqBreakSkill>

		<ReqConfirmSkillRevive> <Exposed/>
			<!--1 confirm -->
			<Arg> bool </Arg>
			<!--2 srcEntityId -->
			<Arg> string </Arg>
			<!--3 reason -->
			<Arg> int </Arg>
		</ReqConfirmSkillRevive>
    </ServerMethods>
</root>