<root>
    <Properties>
        <country Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <province Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <city Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <reportRankInfoTimers Type="DictIntInt" />
        <lastReportRankInfoTimes Type="DictIntInt" />
        <reportRankInfos Type="RANK_REPORT_INFOS" />
        <forbidReportRank Type="bool" Default="false"/>
    </Properties>

    <ServerMethods>
        <ReqSyncRankInfo>  <Exposed/>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
		</ReqSyncRankInfo>
    </ServerMethods>

    <ClientMethods>
        <RetSyncRankInfo>
			<Arg> int </Arg>
			<Arg> RANK_SYNC_INFOS </Arg>
            <Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> ListStr </Arg>
            <Arg> int </Arg>
            <Arg> string </Arg>
		</RetSyncRankInfo>
    </ClientMethods>
 </root>