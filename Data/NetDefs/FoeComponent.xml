<root>
    <Properties>
       <foeMap Type="SIMPLE_FOE_MAP" Persistent="true"/>
       <hereditaryFoeMap Type="SIMPLE_FOE_MAP" Persistent="true"/>
       <shuraTargetMap Type="dict" Key="ENTITY_ID" Value="ENTITY_ID" Flags="OWN_CLIENT" Persistent="true"/>
       <lastBeShuraTime Type="LONGTIME" Persistent="true" /> <!-- 最近一次被碎心时间 -->
       <shuraCount Type="UINT" Persistent="true"/> <!-- 碎心他人次数 -->
       <beShuraCount Type="UINT" Persistent="true"/> <!-- 当日被碎心次数 -->
       <shuraRepairProgress Type="UINT" Flags="OWN_CLIENT" Persistent="true"/> <!-- 碎心修复进度 -->
       <shuraGbIdMap Type="DictStrBool" Persistent="true"/> <!-- 碎心玩家列表 -->
       <shuraProtectTime Type="LONGTIME" Persistent="true" Flags="OWN_CLIENT"/> <!-- 碎心挑战保护时间（到期时间） -->
    </Properties>

    <ClientMethods>
        <onGetFoeList>
            <Arg> FOE_LIST </Arg> <!-- 仇人列表 -->
            <Arg> FOE_LIST </Arg> <!-- 宿敌列表 -->
        </onGetFoeList>

        <onGetFoeListGM>
            <Arg> SIMPLE_FOE_MAP </Arg> <!-- 仇人列表 -->
            <Arg> SIMPLE_FOE_MAP </Arg> <!-- 宿敌列表 -->
        </onGetFoeListGM>

        <onRemoveHereditaryFoe>
            <Arg> ENTITY_ID </Arg>
            <Arg> ENTITY_ID </Arg>
        </onRemoveHereditaryFoe>

        <onRemoveFoe>
            <Arg> ENTITY_ID </Arg>
            <Arg> ENTITY_ID </Arg>
        </onRemoveFoe>

        <onAddHereditaryFoe>
            <Arg> FOE_PLAYER_INFO </Arg>
            <Arg> ENTITY_ID </Arg>
        </onAddHereditaryFoe>

        <onAddFoe>
            <Arg> FOE_PLAYER_INFO </Arg>
            <Arg> ENTITY_ID </Arg>
        </onAddFoe>

        <onChangeHereditaryFoeToFoe>
            <Arg> FOE_PLAYER_INFO </Arg>
            <Arg> ENTITY_ID </Arg>
        </onChangeHereditaryFoeToFoe>

        <onSyncFoeInfo> <!-- 增量下发 -->
            <Arg> FOE_PLAYER_INFO </Arg>
        </onSyncFoeInfo>
    </ClientMethods>

    <ServerMethods>
        <addFoe> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </addFoe>

        <addHereditaryFoe> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </addHereditaryFoe>

        <changeHereditaryFoeToFoe> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </changeHereditaryFoeToFoe>

        <removeHereditaryFoe> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </removeHereditaryFoe>

        <removeFoe> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </removeFoe>

        <onGetFoePlayerInfo>
            <Arg> none </Arg>
            <Arg> string </Arg> <!-- callback -->
        </onGetFoePlayerInfo>

        <onGetFoePlayerListInfo>
            <Arg> none </Arg>
        </onGetFoePlayerListInfo>

        <onRefreshFoeInfo>
            <Arg> ENTITY_ID </Arg>
            <Arg> FOE_PLAYER_INFO </Arg>
        </onRefreshFoeInfo>
    </ServerMethods>
</root>