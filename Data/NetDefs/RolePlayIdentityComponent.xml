<?xml version="1.0" ?>
<root>
	<Properties>
		<!-- 身份信息（等级，经验） -->
        <rolePlayIdentity Type="ROLEPLAY_IDENTITY_DEFINE" Flags="OWN_INITIAL_ONLY" Persistent="true" />
		<!--解锁身份信息，格式为{key: 身份, value: 已解锁的最高等级}-->
        <rolePlayUnlockIdentity Type="DictIntInt" Flags="OWN_INITIAL_ONLY" Persistent="true" />
		<!--上次扮演的身份-->
		<lastRPIdentity Type="int" Default="0" Flags="OWN_INITIAL_ONLY" Persistent="true" />
	</Properties>

	<Implements/>

	<ClientMethods>
		<!--领取身份等级奖励的响应-->
		<RetRolePlayIdentityLevelReward>
			<Arg> int </Arg>						<!--身份编号-->
			<Arg> int </Arg>						<!--等级-->
		</RetRolePlayIdentityLevelReward>

		<!--某个身份的升级响应-->
		<RetRolePlayIdentityUpLevel>
			<Arg> int </Arg>						<!--身份编号-->
			<Arg> int </Arg>						<!--身份等级-->
		</RetRolePlayIdentityUpLevel>

		<!--开始扮演的响应-->
		<RetRolePlayStart>
			<Arg> int </Arg>						<!--身份编号-->
		</RetRolePlayStart>

		<!--同步变更的身份信息-->
		<OnMsgSyncRolePlayIdentity>
			<Arg> ROLEPLAY_IDENTITY_DEFINE </Arg>	<!--身份信息-->
		</OnMsgSyncRolePlayIdentity>

		<!--同步变更的身份信息-->
		<OnMsgSyncRolePlayUnlockIdentity>
			<Arg> int </Arg>						<!--身份编号-->
			<Arg> int </Arg>						<!--解锁的等级-->
		</OnMsgSyncRolePlayUnlockIdentity>
	</ClientMethods>

	<ServerMethods>
		<!--领取身份等级奖励的请求-->
		<ReqRolePlayIdentityLevelReward> <Exposed/>
			<Arg> int </Arg>						<!--身份编号-->
			<Arg> int </Arg>						<!--等级-->
		</ReqRolePlayIdentityLevelReward>

		<!--某个身份的升级请求-->
		<ReqRolePlayIdentityUpLevel> <Exposed/>
			<Arg> int </Arg>						<!--身份编号-->
		</ReqRolePlayIdentityUpLevel>

		<!--开始扮演的请求-->
		<ReqRolePlayStart> <Exposed/>
			<Arg> int </Arg>						<!--身份编号-->
		</ReqRolePlayStart>
	</ServerMethods>
</root>
