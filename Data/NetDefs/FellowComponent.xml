<?xml version="1.0" ?>
<root>
	<Properties>
		<fellowBag Type="FellowBag" Flags="SERVER_ONLY" Persistent="true" /><!-- key:ConfigID 表格配置ID-->
		<fellowGachaStateCount Type="DictStrInt" Flags="OWN_CLIENT" Persistent="true" /><!-- 伙伴抽卡，未命中次数的记录-->
		<fellowGachaStateWeight Type="DictStrInt" Flags="SERVER_ONLY" Persistent="true" /><!-- 伙伴抽卡，对应类型的累积权重-->
		<fellowGachaFreeState Type="DictIntInt" Flags="OWN_CLIENT" Persistent="true" /><!-- 伙伴抽卡，免费单抽 卡池ID 是否已经单抽-->
		<fellowGachaDailyCount Type="int" Flags="OWN_CLIENT" Persistent="true" /><!-- 伙伴抽卡次数-->
		<fellowsPropTransInfo Type="FellowsPropTransInfo" Flags="OWN_CLIENT" Persistent="FALSE" /><!-- 伙伴属性转换到玩家身上的数据记录-->
		<fellowCE Type="int" Flags="SERVER_ONLY" Persistent="FALSE" /><!-- 伙伴加成到玩家身上的战力 -->
		<fellowFriendAssistMap Type="fellowFriendAssistMap" Flags="SERVER_ONLY" Persistent="true" /><!-- key:slot -->
	</Properties>

	<Implements/>
	<ClientMethods>
        <OnMsgSyncAllFellows> <!--全量同步，上线 -->
			<Arg> FellowBag </Arg><!--1 FellowBag -->
		</OnMsgSyncAllFellows>

		<OnMsgSyncAllFriendAssistFellows> <!--全量同步，上线 -->
			<Arg> fellowFriendAssistMap </Arg><!--1 fellowFriendAssistMap -->
		</OnMsgSyncAllFriendAssistFellows>

        <OnMsgAddFellows> <!--增，新增伙伴 -->
			<Arg> FellowList </Arg><!--1 Fellows -->
		</OnMsgAddFellows>

        <OnMsgDeleteFellows> <!--删，删除伙伴 -->
			<Arg> ListInt </Arg><!--1 FellowConfigIDs -->
		</OnMsgDeleteFellows>

        <OnMsgUpdateFellows> <!--改，部分伙伴的信息改变同步 -->
			<Arg> FellowList </Arg><!--1 Fellows -->
		</OnMsgUpdateFellows>

		<OnMsgUpdateFriendAssistFellows> <!--改，部分好友支援伙伴的信息改变同步 -->
			<Arg> int </Arg>                      <!--1 支援slot -->
			<Arg> fellowFriendAssistInfo </Arg>   <!--2 支援详细信息 -->
		</OnMsgUpdateFriendAssistFellows>

		<RetCombineFellow>
			<Arg> Result </Arg>
        </RetCombineFellow>

		<RetFellowJoinCombat>
			<Arg> Result </Arg>
        </RetFellowJoinCombat>

		<RetFellowAssistCombat>
			<Arg> Result </Arg>
        </RetFellowAssistCombat>

		<RetFellowLevelUp>
			<Arg> Result </Arg>
        </RetFellowLevelUp>

		<RetFellowStarLevelUp> <!-- 请求突破 Ret -->
			<Arg> Result </Arg>
        </RetFellowStarLevelUp>

		<RetFellowGacha><!-- 伙伴抽卡 -->
			<Arg> Result </Arg>
			<Arg> FellowGachaRewardList </Arg> <!--2 GachaReward -->
        </RetFellowGacha>

		<RetFellowGachaRecord><!-- 抽卡历史记录 -->
			<Arg> Result </Arg>
			<Arg> FellowGachaRecordInfo </Arg> <!--2 RecordInfo -->
        </RetFellowGachaRecord>

		<RetFellowFriendAssistQuery> <!-- 好友出战伙伴查询 -->
			<Arg> Result </Arg>
			<Arg> FriendAssistFellowBagMap </Arg>
        </RetFellowFriendAssistQuery>

		<RetFellowFriendAssistCombat> <Exposed/>  <!-- 好友伙伴支援 -->
			<Arg> Result </Arg>
        </RetFellowFriendAssistCombat>
	</ClientMethods>

	<ServerMethods>
		<ReqCombineFellow> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
        </ReqCombineFellow>

		<ReqFellowJoinCombat  CD="0.17"> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> bool </Arg><!--2 bCancle -->
			<Arg> int </Arg><!--1 pos -->
        </ReqFellowJoinCombat>

		<ReqFellowAssistCombat  CD="0.17"> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> bool </Arg><!--2 bCancle -->
			<Arg> int </Arg><!--1 pos -->
        </ReqFellowAssistCombat>

		<ReqFellowLevelUp CD="0.17"> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> bool </Arg><!--2 bAuto -->
			<Arg> DictIntInt </Arg> <!--UseItems[ItemID, ItemNum] -->
        </ReqFellowLevelUp>

		<ReqFellowStarLevelUp CD="0.17"> <Exposed/><!-- 请求突破 -->
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> int </Arg><!--2 STAR_LEVEL_UP_TYPE -->
			<Arg> bool </Arg><!--3 CanReplace -->
        </ReqFellowStarLevelUp>

		<ReqFellowGacha CD="1"> <Exposed/><!-- 伙伴抽卡 -->
			<Arg> int </Arg><!--1 Gacha ConfigID -->
			<Arg> int </Arg><!--2 伙伴抽卡类型 see FellowGachaConstInt -->
        </ReqFellowGacha>

		<ReqFellowGachaRecord CD="0.1"> <Exposed/><!-- 抽卡记录 -->
			<Arg> string </Arg><!--1 PageLastRecord -->
			<Arg> int </Arg><!--2 GachaType-->
        </ReqFellowGachaRecord>

		<ReqFellowFriendAssistQuery> <Exposed/><!-- 好友出战伙伴查询 -->
        </ReqFellowFriendAssistQuery>

		<onServerFellowFriendAssistQuery>
			<Arg> none </Arg>
		</onServerFellowFriendAssistQuery>

		<ReqFellowFriendAssistCombat> <Exposed/>  <!-- 好友伙伴支援 -->
			<Arg> int </Arg>                      <!--1 支援slot -->
			<Arg> bool </Arg>                     <!--2 bCancle true为下阵 false为上阵 -->
			<Arg> ENTITY_ID </Arg>                <!--3 好友ID -->
			<Arg> int </Arg>                      <!--4 ConfigID -->
        </ReqFellowFriendAssistCombat>

		<onServerFellowFriendAssistCombat>
			<Arg> none </Arg>
			<Arg> int </Arg>                      <!--1 支援slot -->
			<Arg> bool </Arg>                     <!--2 bCancle true为下阵 false为上阵 -->
			<Arg> ENTITY_ID </Arg>                <!--3 好友ID -->
			<Arg> int </Arg>                      <!--4 ConfigID -->
		</onServerFellowFriendAssistCombat>
	</ServerMethods>
</root>

