<?xml version="1.0" ?>
<root>
    <Properties>
        <NpcSPInteractCD Type="dict" Key="string" Value="DictIntInt" Flags="SERVER_ONLY" Persistent="true"/>
        <bEnableNpcButtonInteract Type="bool" Flags="OWN_CLIENT" Default="true" Persistent="false"/>
    </Properties>
	<Implements/>
	<ClientMethods>
        <RetNpcSPInteract>
			<Arg> Result </Arg>
			<!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 buttonID -->
            <Arg>int</Arg>
            <!--3 bSuccess -->
            <Arg>bool</Arg>
		</RetNpcSPInteract>
        <RetNpcAskPrice>
			<Arg> Result </Arg>
			<!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 price -->
            <Arg>int</Arg>
		</RetNpcAskPrice>
        <RetSetNpcMood>
			<Arg> Result </Arg>
			<!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 mood -->
            <Arg>int</Arg>
		</RetSetNpcMood>
        <RetAddNpcMood>
			<Arg> Result </Arg>
			<!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 mood -->
            <Arg>int</Arg>
		</RetAddNpcMood>
	</ClientMethods>
	<!-- Exposed 待处理 -->
	<ServerMethods>
        <ReqNpcSPInteract> <Exposed/>
            <!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 buttonID -->
            <Arg>int</Arg>
            <!--3 params -->
            <Arg>ReqNpcSPInteractParams</Arg>
        </ReqNpcSPInteract>
        <ReqNpcAskPrice> <Exposed/>
            <!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 price -->
            <Arg>int</Arg>
        </ReqNpcAskPrice>
        <ReqSetNpcMood> <Exposed/>
            <!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 mood -->
            <Arg>int</Arg>
        </ReqSetNpcMood>
        <ReqAddNpcMood> <Exposed/>
            <!--1 npcUid -->
            <Arg>string</Arg>
            <!--2 deltaMood -->
            <Arg>int</Arg>
        </ReqAddNpcMood>
        <ReqSpecialInteractiveKey> <Exposed/>
            <!--1 npcEntityID -->
            <Arg>string</Arg>
            <!--2 InteractorUIID -->
            <Arg>int</Arg>
        </ReqSpecialInteractiveKey>
	</ServerMethods>
</root>
