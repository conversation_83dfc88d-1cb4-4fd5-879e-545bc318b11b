<root>
    <Implements>
    </Implements>
	
    <Properties>
	</Properties>

	<ClientMethods>
    </ClientMethods>

    <ServerMethods>
		<SSReqJoinGroup>	<!-- 申请加入团队 -->
			<Arg> ENTITY_ID </Arg>	<!--1 avatarID -->
			<Arg> GROUP_ID </Arg>	<!--2 groupID -->
		</SSReqJoinGroup>
		
		<SSReqJoinGroupFail>	<!-- 加入团队意外失败 -->
			<Arg> ENTITY_ID </Arg>	<!--1 avatarID -->
			<Arg> GROUP_ID </Arg>	<!--2 groupID -->
		</SSReqJoinGroupFail>
		
		<SSReqLeaveGroup>	<!-- 退出团队附带队伍成员 -->
			<Arg> ENTITY_ID </Arg>		<!--1 avatarID -->
			<Arg> GROUP_ID </Arg>		<!--2 groupID -->
			<Arg> ENTITY_ID_LIST </Arg>	<!--3 memberList -->
			<Arg> bool </Arg>			<!--4 bGroupTeamQuit -->
		</SSReqLeaveGroup>
		
		<SSReqDisbandWholeGroup>	<!-- 解散团队 -->
			<Arg> GROUP_ID </Arg>	<!--1 groupID -->
		</SSReqDisbandWholeGroup>
		
		<SSOnMsgMangerGroupLeaderChange>	<!-- 团队队长变更 -->
			<Arg> GROUP_ID </Arg>	<!--1 groupID -->
			<Arg> ENTITY_ID </Arg>	<!--2 newLeaderID -->
		</SSOnMsgMangerGroupLeaderChange>
		
		<SSReqChangeMemberInDifferentGroup>		<!-- 转移团队成员到另一个团队(SrcGroupId, DstGroupId, SrcSlotInfo, DstSlotInfo) -->
			<Arg> GROUP_ID </Arg>				<!--1 srcGroupID -->
			<Arg> GROUP_ID </Arg>				<!--2 tarGroupID -->
			<Arg> GroupTeamMemberSlot </Arg> 	<!--3 src slot -->
			<Arg> GroupTeamMemberSlot </Arg> 	<!--4 dst slot -->
		</SSReqChangeMemberInDifferentGroup>
		
		<SSReqAddGroupToLeague>	<!-- 添加团队到联盟 -->
			<Arg> GROUP_ID </Arg>	<!--1 srcGroupID -->
			<Arg> GROUP_ID </Arg>	<!--2 tarGroupID -->
			<Arg> GroupClientInfo </Arg>	<!--3 groupInfo for client -->
		</SSReqAddGroupToLeague>
		
		<SSReqRemoveGroupFromLeague>	<!-- 从联盟移除团队 -->
			<Arg> GROUP_ID </Arg>	<!--1 LeagueID -->
			<Arg> GROUP_ID </Arg>	<!--2 GroupID -->
		</SSReqRemoveGroupFromLeague>

		<SSReqBatchBuildBattleGroup>	<!-- 批量创建战斗团队 -->
			<Arg>BATCH_CREATE_TEAM_INFO_MAP</Arg> <!--队伍成员信息 -->
		</SSReqBatchBuildBattleGroup>
    </ServerMethods>
</root>
