<?xml version="1.0" ?>
<root>
    <Implements/>

    <Properties>
        <DungeonEntityID Type="string" Flags="OWN_CLIENT" Default="" Persistent="true"/>
        <DungeonFirstPassageStageInfos Type="DUNGEON_FIRST_PASSAGE_STAGE_INFOS" Persistent="true"/>
        <DungeonTemplateID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>
        <DungeonReadinessPanelState Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>
        <ExitDungeonBtnState Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>
        <LastTinggenDungeonID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>

        <DungeonFirstCompleteTSs Type="DictIntInt" Persistent="true"/>
    </Properties>

    <ClientMethods>
        <RetOpenDungeon>
            <!--1 result -->
            <Arg>Result</Arg>
            <!--2 NonCompliantMemberList -->
            <Arg>ListStr</Arg>
        </RetOpenDungeon>
        <RetCloseDungeon>
            <!--1 result -->
            <Arg>Result</Arg>
        </RetCloseDungeon>
        <RetLeaveDungeon>
            <!--1 result -->
            <Arg>Result</Arg>
        </RetLeaveDungeon>
        <RetChallengeDungeon>
            <!--1 result -->
            <Arg>Result</Arg>
        </RetChallengeDungeon>
        <OnMsgDungeonReadinessCheck>
            <Arg>int</Arg>
            <Arg>int</Arg>
            <Arg>string</Arg>
            <Arg>int</Arg>
        </OnMsgDungeonReadinessCheck>
        <RetHandleReadinessCheck>
            <Arg>Result</Arg>
        </RetHandleReadinessCheck>
        <OnMsgLike>
            <Arg>string</Arg>
            <Arg>string</Arg>
        </OnMsgLike>
        <RetLike>
            <Arg>Result</Arg>
            <Arg>string</Arg>
        </RetLike>
        <RetReturnDungeon>
            <Arg>int</Arg>
        </RetReturnDungeon>
        <OnMsgNotifyReturnDungeon>
            <!--RemainTime-->
            <Arg>int</Arg>
        </OnMsgNotifyReturnDungeon>
        <OnMsgDungeonStageSettlement>
            <!--1 stageId -->
            <Arg>int</Arg>
            <!--2 isSucceed -->
            <Arg>bool</Arg>
        </OnMsgDungeonStageSettlement>
        <OnMsgDungeonSettlement>
            <!--1 dungeonId -->
            <Arg>int</Arg>
            <!--2 settlementTime -->
            <Arg>int</Arg>
            <!--3 isSucceed -->
            <Arg>bool</Arg>
            <!--4 firstreward -->
            <Arg>none</Arg>
            <!--5 reward -->
            <Arg>none</Arg>
            <!--6 title-->
            <Arg>DungeonTitles</Arg>
            <!--7 bLimitStage -->
            <Arg>bool</Arg>
            <!--8 mvpID-->
            <Arg>string</Arg>
            <!--9 LevelMapID -->
            <Arg>int</Arg>
        </OnMsgDungeonSettlement>
        <OnMsgDungeonLevelChange>
            <!--1 levelMapID -->
            <Arg>int</Arg>
        </OnMsgDungeonLevelChange>
        <OnMsgDungeonExitRemainUpdate>
            <!--DungeonExitRemain-->
            <Arg>int</Arg>
        </OnMsgDungeonExitRemainUpdate>
        <OnMsgDungeonChallengeRemain>
            <!--1 DungeonChallengeRemain-->
            <Arg>int</Arg>
        </OnMsgDungeonChallengeRemain>

        <RetGetDungeonFirstPassageRecord>
            <Arg> int </Arg>
            <Arg> DUNGEON_FIRST_PASSAGE_RECORD </Arg>
        </RetGetDungeonFirstPassageRecord>
    </ClientMethods>

    <!-- Exposed 待处理 -->
    <ServerMethods>
        <ReqGetDungeonFirstPassageRecord> <Exposed/>
            <Arg>int</Arg>
        </ReqGetDungeonFirstPassageRecord>

        <ReqSetDungeonReadinessPanel> <Exposed/>
            <Arg>int</Arg>
        </ReqSetDungeonReadinessPanel>
        <RetReturnDungeonCheck> <Exposed/>
            <Arg>int</Arg>
        </RetReturnDungeonCheck>
        <SOnMsgNotifyReturnDungeon>
            <!--RemainTime-->
            <Arg>int</Arg>
        </SOnMsgNotifyReturnDungeon>
        <SOnMsgReportDungeonSAData>
            <Arg>string</Arg>
            <Arg>int</Arg>
            <Arg>ListInt</Arg>
            <Arg>int</Arg>
        </SOnMsgReportDungeonSAData>
        <SOnMsgClearTeamDungeonState/>
        <ReqReturnDungeon> <Exposed/>
        </ReqReturnDungeon>
        <ReqDungeonSettlement> <Exposed/>
        </ReqDungeonSettlement>
        <ReqHandleReadinessCheck> <Exposed/>
            <Arg>int</Arg>
        </ReqHandleReadinessCheck>
        <ReqOpenDungeon> <Exposed/>
            <!--1 dungeonID -->
            <Arg>int</Arg>
            <!--2 dungeonMode -->
            <Arg>int</Arg>
        </ReqOpenDungeon>
        <RetCreateDungeonInst> <Exposed/>
            <Arg>Result</Arg>
            <Arg>mailbox</Arg>
        </RetCreateDungeonInst>
        <OnMsgDungeonInstReady> <Exposed/>
            <Arg>Result</Arg>
            <!--2 DungeonInstMailBox -->
            <Arg>mailbox</Arg>
            <!--3 NeedNotifyMembers -->
            <Arg>bool</Arg>
        </OnMsgDungeonInstReady>
        <ReqChallengeDungeon CD="1"> <Exposed/>
            <!--1 challengeType -->
            <Arg>int</Arg>
        </ReqChallengeDungeon>
        <ReqLeaveDungeon CD="1"> <Exposed/>
            <Arg>bool</Arg>
        </ReqLeaveDungeon>
        <OnMsgServerDungeonReadinessCheck>
            <Arg>int</Arg>
            <Arg>int</Arg>
            <Arg>string</Arg>
            <Arg>int</Arg>
        </OnMsgServerDungeonReadinessCheck>
        <OnMsgServerMemberReadyForDungeon>
            <Arg>int</Arg>
        </OnMsgServerMemberReadyForDungeon>
        <OnMsgServerGroupReadyForDungeon>
            <Arg>int</Arg>
            <Arg>ListInt</Arg>
            <Arg>MailBoxList</Arg>
            <Arg>GROUP_ID</Arg>
        </OnMsgServerGroupReadyForDungeon>
        <OnMsgServerSyncDungeonMailbox>
            <Arg>mailbox</Arg>
        </OnMsgServerSyncDungeonMailbox>
        <ReqLike> <Exposed/>
            <Arg>string</Arg>
        </ReqLike>
        <OnMsgNotifyLike>
            <Arg>string</Arg>
        </OnMsgNotifyLike>
    </ServerMethods>
</root>
