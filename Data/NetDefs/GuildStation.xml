<root>
    <Properties>
    </Properties>
    <Implements>
        <Interface>World</Interface>
    </Implements>

    <ClientMethods>

    </ClientMethods>

    <ServerMethods>
        <onGuildLevelChange>
            <Arg> UINT </Arg>
        </onGuildLevelChange>

        <systemOpenActivity>
            <Arg> UINT </Arg>
            <Arg> LONGTIME </Arg>
            <Arg> LONGTIME </Arg>
        </systemOpenActivity>

        <systemCloseActivity>
            <Arg> UINT </Arg>
        </systemCloseActivity>

        <manualOpenGuildChallenge>
            <Arg> UINT </Arg> <!-- 活动Id -->
            <Arg> UINT </Arg> <!-- 场景id -->
            <Arg> mailbox </Arg> <!-- 发起玩家box -->
            <Arg> ENTITY_ID </Arg> <!-- 发起玩家id -->
            <Arg> UINT </Arg> <!-- 难度 -->
        </manualOpenGuildChallenge>

        <manualOpenGuildTalentShow>
            <Arg> UINT </Arg> <!-- 活动Id -->
            <Arg> UINT </Arg> <!-- 场景id -->
            <Arg> mailbox </Arg> <!-- 发起玩家box -->
            <Arg> ENTITY_ID </Arg> <!-- 发起玩家id -->
            <Arg> ROLE_MODEL_LIST </Arg> <!-- pick玩家外观信息 -->
        </manualOpenGuildTalentShow>

        <manualCloseGuildActivity>
            <Arg> UINT </Arg> <!-- 活动Id -->
            <Arg> UINT </Arg> <!-- 场景id -->
            <Arg> ENTITY_ID </Arg> <!-- 发起玩家id -->
        </manualCloseGuildActivity>

        <enterGuildScene>
            <Arg> UINT </Arg>
            <Arg> mailbox </Arg>
        </enterGuildScene>

        <updateGuildMembers>
            <Arg> table </Arg>
        </updateGuildMembers>

        <addBabyPrototype>
            <Arg> ENTITY_ID </Arg>
        </addBabyPrototype>

        <removeBabyPrototype>
            <Arg> ENTITY_ID </Arg>
        </removeBabyPrototype>

        <onDayRefresh>
        </onDayRefresh>

        <modifyGuildPresidentStatue> <!-- 修改公会会长雕像参数 -->
            <Arg> GUILD_STATION_PRESIDENT_STATUE </Arg>
        </modifyGuildPresidentStatue>

        <changeGuildPresident>
            <Arg> ENTITY_ID </Arg>
            <Arg> ROLENAME </Arg>
        </changeGuildPresident>

        <updateGuildBadgeFrame>
            <Arg> UINT </Arg>
        </updateGuildBadgeFrame>

        <getGuildRobberNumArg>
            <Arg> mailbox </Arg>
        </getGuildRobberNumArg>

        <updateWeekRankTopStatus>
            <Arg> ENTITY_ID </Arg> <!-- 每周公会贡献第一 -->
            <Arg> ENTITY_ID </Arg> <!-- 每周公会发言第一 -->
        </updateWeekRankTopStatus>

        <letPlayerBackToGuildChallengeMainStage> <!-- 公会驻地通知公会副本主场景拉玩家 -->
            <Arg> UINT </Arg>       <!-- 活动id -->
            <Arg> mailbox </Arg>    <!-- 玩家的mailbox -->
        </letPlayerBackToGuildChallengeMainStage>

        <queryGuildActivityStatus>  <!-- 查询活动场景状态 -->
            <Arg> UINT </Arg>       <!-- activityId -->
            <Arg> mailbox </Arg>    <!-- 查询者mailbox -->
        </queryGuildActivityStatus>

        <removeActivityFlag>        <!-- 移除公会驻地活动中标记 -->
            <Arg> UINT </Arg>
        </removeActivityFlag>

        <openGuildActivity>
            <Arg> int </Arg> 
            <Arg> int </Arg> 
            <Arg> int </Arg>
        </openGuildActivity>

        <closeGuildActivity>
            <Arg> int </Arg> 
        </closeGuildActivity>

        <openGuildActivities>
            <Arg> GuildActivityInfos </Arg> 
        </openGuildActivities>
    </ServerMethods>
</root>
