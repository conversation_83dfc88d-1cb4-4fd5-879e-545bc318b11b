<?xml version="1.0" ?>
<root>
    <Properties>
        <moneySellOrderListSize Type="int"  Default="0" Flags="SERVER_ONLY"  Persistent="false" />                                   <!-- 出售订单数量  -->
        <takeBackMoneyTimestamp Type="int"  Default="nil" Flags="SERVER_ONLY"  Persistent="true" />                                  <!-- 下架时间戳  -->

        <moneyBuyHistory Type="list" Element="MoneyBuyHistory"  Default="nil" Flags="SERVER_ONLY" Persistent="true" />                             <!-- 出售记录  -->
        <moneySellHistory Type="list" Element="MoneySellHistory" Default="nil" Flags="SERVER_ONLY" Persistent="true" />                           <!-- 购买记录  -->
    </Properties>
	<Implements/>
    <ServerMethods>
        <!-- 客户端通信协议 开始 -->
        <ReqSellMoney> <Exposed/>                       <!-- 出售请求 -->
            <!--1 SellRate -->
            <Arg> int </Arg>
            <!--2 SellNum -->
            <Arg> int </Arg>
        </ReqSellMoney>
        <ReqBuyMoney> <Exposed/>                        <!-- 购买请求 -->
            <!--1 MoneyRateBuyList -->
            <Arg> MoneyRateBuyList </Arg>
        </ReqBuyMoney>
        <RetTakeBackMoney CD="3"> <Exposed/>                   <!-- 下架请求 -->
            <!--1 opNUID -->
            <Arg> UUID </Arg>
        </RetTakeBackMoney>
        <ReqGetMoneySellList> <Exposed/>                <!-- 获取当前在售的货币信息列表 -->
            <!--1 refill == 1 重新全量拉取数据(如新打开界面时用)  refill == 0 出售列表信息有变动才回包(如停留在界面定时拉取时使用)-->
            <Arg> int </Arg>
        </ReqGetMoneySellList>
        <ReqGetOwnerSellMoneyInfoList> <Exposed/>       <!-- 获取自己当前在售的货币信息列表 回包 RetGetOwnerSellMoneyInfo-->
        </ReqGetOwnerSellMoneyInfoList>
        <RepGetOwnerSellHistory> <Exposed/>             <!-- 获取自己出售货币记录 回包 RetGetOwnerSellHistory-->
        </RepGetOwnerSellHistory>
        <ReqGetOwnerBuyHistory> <Exposed/>              <!-- 获取自己购买货币记录 回包 RetGetOwnerBuyHistory-->
        </ReqGetOwnerBuyHistory>
        <!-- 客户端通信协议 结束 -->


        <!-- 服务器节点间通信协议 开始 -->
        <SSOnMsgMoneySellSuccess>
            <!--1 opNUID -->
            <Arg> UUID </Arg>
            <!--2 sellRate -->
            <Arg> int </Arg>
            <!--3 sellSuccessNum -->
            <Arg> int </Arg>
            <!--4 curTime -->
            <Arg> int </Arg>
        </SSOnMsgMoneySellSuccess>
        <SSOnMsgTimerOutTakeBackOrder>
            <!--1 opNUID -->
            <Arg> UUID </Arg>
            <!--2 sellNum -->
            <Arg> int </Arg>
        </SSOnMsgTimerOutTakeBackOrder>
        <SSRetSellMoney>                                 <!--出售货币回包结果 -->
            <!--1 errorCode -->
            <Arg> int </Arg>
            <!--2 orderID -->
            <Arg> UUID </Arg>
            <!--3 出售货币简要信息 -->
            <Arg> MoneySellSimpleInfo </Arg>
        </SSRetSellMoney>

        <!-- 服务器节点间通信协议 结束 -->

    </ServerMethods>
    <ClientMethods>
        <RetSellMoney>                                 <!-- 出售请求结果 -->
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 MoneySellList result 为0成功的时候才有效, 不为0时这里为空table{} -->
            <Arg> MoneySellList </Arg>
            <!--3 出售商品信息 -->
            <Arg> OwnerSellMoneyInfo </Arg>
        </RetSellMoney>
        <RetBuyMoney>                                  <!-- 购买请求结果 -->
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 MoneySellList result 为0成功的时候才有效, 不为0时这里为空table{} -->
            <Arg> MoneySellList </Arg>
        </RetBuyMoney>
        <RetTakeBackMoney>                             <!-- 下架请求 回包结果-->
            <!--1 Result -->
            <Arg> Result </Arg>
            <!--2 opNUID -->
            <Arg> UUID </Arg>
            <!--3 下架时间戳，>0时，超过这个时间才能下架  -->
            <Arg> int </Arg>
        </RetTakeBackMoney>
        <RetGetMoneySellList>                          <!-- 返回当前在售的货币信息列表 -->
            <!--1 MoneySellList -->
            <Arg> MoneySellList </Arg>
        </RetGetMoneySellList>
        <RetGetOwnerSellMoneyInfo>                       <!-- 返回自己出售的货币信息列表 -->
            <Arg> OwnerSellMoneyInfoList </Arg>
            <!--2 下架时间戳，>0时，超过这个时间才能下架  -->
            <Arg> int </Arg>
        </RetGetOwnerSellMoneyInfo>
        <OnSellMoneySuccessNotify>                          <!-- 成功出售商品通知 -->
            <!--1 opNUID -->
            <Arg> UUID </Arg>
            <!--2 sellRate 出售汇率-->
            <Arg> int </Arg>
            <!--3 sellSuccessNum 成功出售数量-->
            <Arg> int </Arg>
        </OnSellMoneySuccessNotify>
        <RetGetOwnerSellHistory>               <!-- 返回自己出售货币记录 -->
            <Arg> MoneySellHistoryList </Arg>
        </RetGetOwnerSellHistory>
        <RetGetOwnerBuyHistory>                <!-- 返回自己购买货币记录 -->
            <Arg> MoneyBuyHistoryList </Arg>
        </RetGetOwnerBuyHistory>
        <OnMsgTimerOutTakeBackMoney>                   <!-- 超时下架出售货币 -->
            <!--1 opNUID -->
            <Arg> UUID </Arg>
        </OnMsgTimerOutTakeBackMoney>

    </ClientMethods>
</root>
