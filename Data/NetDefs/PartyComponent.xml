<?xml version="1.0" ?>
<root>
    <Properties>
        <persistentPartyID Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/> <!-- 玩家的常驻房间 可能和当前派对房间ID重复 -->
        <persistentPartyVersion Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/> <!-- 玩家的常驻房间的version -->
        <partyFavoritesCount Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/>
        <partyFavorites Type="DictIntUInt" Flags="SERVER_ONLY" Persistent="true" Default="nil"/> <!-- [key: 玩家收藏的房间, value: 创建房间的时间戳] -->
        <partyFavoriteInfos Type="TEA_ROOM_INFO_LIST" Flags="SERVER_ONLY" Persistent="false" Default="nil"/>
    </Properties>
    <Implements/>

    <ClientMethods>
        <!-- 成功创建派对 -->
        <OnMsgCreateParty>
            <!-- 房间名称 -->
            <Arg>string</Arg>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 是否分享到世界频道 -->
            <Arg>bool</Arg>
            <!-- 是否分享到朋友圈 -->
            <Arg>bool</Arg>
        </OnMsgCreateParty>
        <!-- 进入派对 -->
        <OnMsgEnterParty>
            <!-- 房间详细信息 -->
            <Arg>PARTY_DETAILED_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg> bool </Arg>
            <!-- 是否为收藏房间 -->
            <Arg>bool</Arg>
        </OnMsgEnterParty>
        <!-- 通知房间其他成员玩家离开房间 -->
        <OnMsgNotifyMemberLeaveParty>
            <!-- 进入房间的ID -->
            <Arg>int</Arg>
            <!-- 退出玩家的ID -->
            <Arg>string</Arg>
            <!-- 退出玩家的ShortUid -->
            <Arg>int</Arg>
            <!-- 退房后的热度值 -->
            <Arg>int</Arg>
            <!-- 退房后的房间人数 -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberLeaveParty>
        <!-- 玩家离开房间 -->
        <OnMsgLeaveParty>
            <!-- 目标房间ID -->
            <Arg>int</Arg>
        </OnMsgLeaveParty>
        <!-- 通知房间其他成员玩家进入房间 -->
        <OnMsgNotifyMemberEnterParty>
            <!-- 进入房间的ID -->
            <Arg>int</Arg>
            <!-- 进入玩家的信息 -->
            <Arg>PARTY_ROOM_MEMBER_INFO</Arg>
            <!-- 进房后的热度值 -->
            <Arg>int</Arg>
            <!-- 进房后的房间人数 -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberEnterParty>
        
        <!-- 通知所有派对成员房主变更 -->
        <OnMsgPartyNotifyModifyMaster>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房主的信息 -->
            <Arg>PARTY_ROOM_MEMBER_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg>bool</Arg>
        </OnMsgPartyNotifyModifyMaster>
        <!-- 获取常驻房间到期时间的回调 -->
        <OnMsgGetPersistentPartyDieTimeCallBack>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--到期时间 -->
            <Arg>int</Arg>
        </OnMsgGetPersistentPartyDieTimeCallBack>
        <!-- 断线重新 -->
        <OnMsgPartyReconnect>
            <!-- 房间详细信息 -->
            <Arg>PARTY_DETAILED_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg> bool </Arg>
            <!-- 是否为收藏房间 -->
            <Arg> bool </Arg>
        </OnMsgPartyReconnect>
        <!-- 通知常驻房间的成员房主上线 -->
        <OnMsgNotifyMembersMasterLogin>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房主信息 -->
            <Arg> PARTY_ROOM_MEMBER_INFO </Arg>
        </OnMsgNotifyMembersMasterLogin>
        <!-- 通知房间成员房主下线 -->
        <OnMsgNotifyMemberMasterOffline>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房主ID -->
            <Arg> string </Arg>
        </OnMsgNotifyMemberMasterOffline>
        <!-- 通知房间成员房主被转让 -->
        <OnMsgNotifyModifyPartyMaster>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房主的信息 -->
            <Arg>PARTY_ROOM_MEMBER_INFO</Arg>
            <!-- 申请列表是否为空 -->
            <Arg>bool</Arg>
        </OnMsgNotifyModifyPartyMaster>
        <!-- 通知房间成员新的麦序 -->
        <OnMsgNotifyPartyMemberOrderList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 麦序列表 -->
            <Arg> ListStr </Arg>
        </OnMsgNotifyPartyMemberOrderList>
        <!-- 通知房间成员禁言信息 -->
        <OnMsgNotifyPartyGagMessage>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 被禁言玩家的ID -->
            <Arg>string</Arg>
            <!-- 禁言属性 -->
            <Arg>bool</Arg>
        </OnMsgNotifyPartyGagMessage>
        <!-- 通知房主常驻房间续费成功 -->
        <OnMsgNotifyMasterUpdatePersistentSucc>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 到期时间 -->
            <Arg>int</Arg>
        </OnMsgNotifyMasterUpdatePersistentSucc>
        <!-- 通知房间成员常驻状态变化 -->
        <OnMsgNotifyMemberPersistentPartyChange>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 是否为常驻房间 -->
            <Arg>bool</Arg>
            <!-- 房间Tag -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberPersistentPartyChange>
        <!-- 通知房间成员解散常驻房间 -->
        <OnMsgNotifyMemberPersistentPartyDismantle>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgNotifyMemberPersistentPartyDismantle>
        <!-- 通知客户端收藏房间成功 -->
        <OnMsgFavoritePartyCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgFavoritePartyCallBack>
        <!-- 通知客户端取消收藏房间成功 -->
        <OnMsgCancelPartyFavoriteCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgCancelPartyFavoriteCallBack>
        <!-- 更新房间列表 -->
        <OnMsgUpdatePartyList>
            <!-- 房间信息列表 -->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
        </OnMsgUpdatePartyList>
        <!-- 更新房间列表 -->
        <OnMsgNotifyModifyPartyHotValue>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间热度 -->
            <Arg>int</Arg>
            <!-- 房间Tag -->
            <Arg>int</Arg>
        </OnMsgNotifyModifyPartyHotValue>
        <!-- 修改房间名字 -->
        <OnMsgNotifyModifyPartyName>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间名字 -->
            <Arg>string</Arg>
        </OnMsgNotifyModifyPartyName>
        <!-- 通知房主将成员拉入黑名单 -->
        <OnMsgNotifyPartyMasterAddBlackList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家黑名单信息 -->
            <Arg>MemberShowInfo</Arg>
        </OnMsgNotifyPartyMasterAddBlackList>
        <!-- 通知房主将成员从黑名单中删除 -->
        <OnMsgNotifyPartyMasterDeleteBlackList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家ID -->
            <Arg>string</Arg>
        </OnMsgNotifyPartyMasterDeleteBlackList>
        <!-- 获取常驻房间信息的回调  roomid=0表示没有 -->
        <OnMsgGetPersistentPartyInfoCallback>
            <!-- 房间信息 -->
            <Arg>TEA_ROOM_INFO</Arg>
        </OnMsgGetPersistentPartyInfoCallback>

        <OnMsgPartyMessageTop>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 置顶消息内容 -->
            <Arg>string</Arg>
            <!-- 置顶消息作者 -->
            <Arg>string</Arg>
            <!-- 置顶消息ID -->
            <Arg>string</Arg>
        </OnMsgPartyMessageTop>
        <OnMsgPartyCancelMessageTop>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgPartyCancelMessageTop>

        <OnMsgNotifyPartyMemberGiveGift>
            <!-- 赠礼信息 -->
            <Arg>TEAROOM_GIVE_GIFT_INFO</Arg>
        </OnMsgNotifyPartyMemberGiveGift>
        <OnMsgNotifyModifyPartyType>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 新房间类型 -->
            <Arg>int</Arg>
        </OnMsgNotifyModifyPartyType>

        <!-- 设置房间发言权限的回调 -->
        <OnMsgSetPartyMemberPrivilegeCallback>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 上麦的成员列表 -->
            <Arg>ARRAY_STRING</Arg>
            <!-- 被下的成员列表 -->
            <Arg>ARRAY_STRING</Arg>
        </OnMsgSetPartyMemberPrivilegeCallback>
        <!-- 通知所有成员新的麦序 -->
         <OnMsgNotifyAllPartyMemberOrderList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 麦序列表 -->
            <Arg>ListStr</Arg>
        </OnMsgNotifyAllPartyMemberOrderList>
        <!-- 通知房间成员变成全员自由发言 -->
        <OnMsgNotifyPartySpeechFree>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </OnMsgNotifyPartySpeechFree>
        <!-- 通知所有成员玩家升级 -->
        <OnMsgNotifyPartyMemberLevelup>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家的ID -->
            <Arg>string</Arg>
            <!-- 等级 -->
            <Arg>int</Arg>
        </OnMsgNotifyPartyMemberLevelup>
        <!-- 通知所有成员玩家改名 -->
        <OnMsgNotifyPartyMemberNewName>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家的ID -->
            <Arg>string</Arg>
            <!-- 名字 -->
            <Arg>string</Arg>
        </OnMsgNotifyPartyMemberNewName>
        <!-- 通知房主上麦申请 -->
        <OnMsgApplicationInfo2PartyMaster>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 个人申请信息 -->
            <Arg>APPLICATION_INFO</Arg>
        </OnMsgApplicationInfo2PartyMaster>
        <!-- 通知房主删除申请列表中的玩家信息 -->
        <OnMsgDeletePartyApplicationInfos>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 申请列表中的玩家ID -->
            <Arg>ARRAY_STRING</Arg>
        </OnMsgDeletePartyApplicationInfos>
        <!-- 通知房间成员玩家的发言状态改变 -->
        <OnMsgNotifyPartyMemberSpeechState>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 玩家的信息 -->
            <Arg>PARTY_ROOM_MEMBER_INFO</Arg>
        </OnMsgNotifyPartyMemberSpeechState>
        <!-- 通知房间成员标签名字修改 -->
        <OnMsgNotifyPartyModifyIdentityName>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 标签ID -->
            <Arg>int</Arg>
            <!-- 标签名称 -->
            <Arg>string</Arg>
        </OnMsgNotifyPartyModifyIdentityName>
        <OnMsgPartyAssignIdentity2Avatar>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 标签ID -->
            <Arg>int</Arg>
            <!-- 成员信息 -->
            <Arg>PARTY_MEMBER_INFO_LIST</Arg>
        </OnMsgPartyAssignIdentity2Avatar>
        <!-- 查找成员名字 -->
        <OnMsgPartyFindMemberName>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 搜索内容的shortUid列表 -->
            <Arg>ListInt</Arg>
        </OnMsgPartyFindMemberName>
        <!-- 成员移除标签 -->
        <OnMsgRemoveIdentification>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 玩家ID -->
            <Arg>string</Arg>
            <!-- identityID -->
            <Arg>int</Arg>
        </OnMsgRemoveIdentification>
        <!-- 修改标签状态 -->
        <OnMsgPartyModifyIdentityState>
            <!-- 房间的ID -->
            <Arg>int</Arg>
            <!-- 玩家ID -->
            <Arg>string</Arg>
            <!-- identityID -->
            <Arg>int</Arg>
            <!-- identityState -->
            <Arg>int</Arg>
        </OnMsgPartyModifyIdentityState>
        <!-- 通知成员房间成员列表信息 -->
        <OnMsgNotifyPartyMemberInfoList>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员信息列表 -->
            <Arg>PARTY_MEMBER_INFO_LIST</Arg>
        </OnMsgNotifyPartyMemberInfoList>
    </ClientMethods>

    <ServerMethods>
        <!-- 创建房间 -->
        <ReqCreateParty CD="1"> <Exposed/>
            <!-- 房间名称 -->
            <Arg>string</Arg>
            <!-- 房间类型 -->
            <Arg>int</Arg>
            <!-- 房间标签 -->
            <Arg>int</Arg>
            <!-- 是否分享到世界频道 -->
            <Arg>bool</Arg>
            <!-- 是否分享到朋友圈 -->
            <Arg>bool</Arg>
        </ReqCreateParty>
        <!-- 获取派对ID的回调 -->
        <SSOnMsgCacheServiceGetPartyIDCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 建房时的房间信息 -->
            <Arg>CREATE_CHAT_ROOM_INFO</Arg>
            <!-- 建房的顺序 -->
            <Arg>int</Arg>
        </SSOnMsgCacheServiceGetPartyIDCallBack>
        <SSOnMsgCreatePartyCallBack>
            <!-- 是否创建房间成功 -->
            <Arg>bool</Arg>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </SSOnMsgCreatePartyCallBack>

        <!-- 进入派对 -->
        <ReqEnterParty CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqEnterParty>

        <!-- 离开房间 -->
        <ReqLeaveParty CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqLeaveParty>
        <SSOnMsgEnterPartyCallBack>
            <!-- 新房间ID -->
            <Arg>int</Arg>
            <!-- 是否进入房间成功 -->
            <Arg>bool</Arg>
        </SSOnMsgEnterPartyCallBack>

         <SSOnMsgLeavePartyCallback>
            <!-- 退出房间ID -->
            <Arg>int</Arg>
            <!-- 进入房间ID -->
            <Arg>int</Arg>
            <!-- 是否随机进入一个新的房间 -->
            <Arg>bool</Arg>
            <!--新房间是否为派对 -->
            <Arg>bool</Arg>
        </SSOnMsgLeavePartyCallback>
        <!-- 常驻房间到期的回调 -->
        <SSOnMsgPersistentPartyDieCallBack> 
            <!-- 进入房间ID -->
            <Arg>int</Arg>
        </SSOnMsgPersistentPartyDieCallBack>
        <!-- 升级为常驻房间 -->
        <ReqUpdatePersisentParty CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqUpdatePersisentParty>
        <!-- 获取最新的常驻房间到期时间 -->
        <ReqGetPersistentPartyDieTime CD="1"> <Exposed/>
        </ReqGetPersistentPartyDieTime>
        <!-- 常驻房间到期的回调 -->
        <SSOnMsgCheckPersisentPartyTimeCallback> 
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 是否可以开通常驻房间 -->
            <Arg>bool</Arg>
        </SSOnMsgCheckPersisentPartyTimeCallback>
        <!-- 通知玩家常驻房间续费成功 -->
        <SSOnMsgNotifyMasterUpdatePersist> 
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房间版本 -->
            <Arg>int</Arg>
        </SSOnMsgNotifyMasterUpdatePersist>
        <!-- 转让房主 -->
        <ReqTransferPartyMaster> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房主ID -->
            <Arg>string</Arg>
        </ReqTransferPartyMaster>
        <!-- 解散常驻房间 -->
        <ReqDismantlePersistentParty> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqDismantlePersistentParty>
        <!-- 收藏派对房间 -->
        <ReqFavoriteParty> <Exposed/>
        </ReqFavoriteParty>
        <!-- 收藏派对房间回调 -->
        <SSOnMsgFavoritePartyCallBack>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房间version -->
            <Arg>int</Arg>
        </SSOnMsgFavoritePartyCallBack>
        <!-- 检查派对收藏房间回调 -->
        <SSOnMsgCheckPartyFavoritesCallBack>
            <!-- 收藏数量 -->
            <Arg>int</Arg>
            <!-- 新的收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!-- 加入房间ID -->
        </SSOnMsgCheckPartyFavoritesCallBack>
        <!-- 取消收藏派对房间 -->
        <ReqCancelPartyFavorite> <Exposed/>
        </ReqCancelPartyFavorite>

        <SSOnMsgUpdatePartyFavoritesCallBack>
            <!-- 收藏数量 -->
            <Arg>int</Arg>
            <!-- 新的收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!-- 新的收藏房间信息 -->
            <Arg>TEA_ROOM_INFO_LIST</Arg>
            <!-- 是否删除收藏夹 -->
            <Arg>bool</Arg>
        </SSOnMsgUpdatePartyFavoritesCallBack>
        <!-- 查找房间 -->
        <ReqSearchParty CD="1"> <Exposed/>
             <!-- 搜索字符 -->
            <Arg>string</Arg>
             <!-- 是否查看本服 false为全服 true为本服 -->
            <Arg>bool</Arg>
            <!-- 筛选列表 默认全选 -->
            <Arg>ARRAY_BOOL</Arg>
        </ReqSearchParty>

        <!-- 修改房间的名字 -->
        <ReqModifyPartyName CD="1"> <Exposed/>
             <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 房间名字 -->
            <Arg>string</Arg>
        </ReqModifyPartyName>

        <ReqPartyPullBlacklist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqPartyPullBlacklist>
        <ReqPartyCancelBlacklist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqPartyCancelBlacklist>
        <!-- 快速进房 -->
        <ReqEnterPartyQuickly CD="1"> <Exposed/>
        </ReqEnterPartyQuickly>
        <!-- 获取常驻房间信息 -->
        <ReqGetPersistentPartyInfo CD="1"> <Exposed/>
        </ReqGetPersistentPartyInfo>
        
        <ReqPartyMessageTop CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 文本消息 -->
            <Arg>string</Arg>
            <!-- 文本消息作者ID -->
            <Arg>string</Arg>
            <!-- 文本消息ID -->
            <Arg>string</Arg>
        </ReqPartyMessageTop>
        <ReqPartyCancelMessageTop CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqPartyCancelMessageTop>
        <!-- 茶壶赠礼 -->
        <ReqPartyGiveGift CD="0.17"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 赠礼对象ID -->
            <Arg> ENTITY_ID </Arg>
            <!-- 礼物数量 -->
            <Arg> UINT </Arg>
            <!-- GiftID -->
            <Arg> UINT </Arg>
        </ReqPartyGiveGift>
        <!-- 房主设置玩家的发言权限 -->
        <ReqSetPartyMemberPrivilege CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言权限的列表 entityID -> bool -->
            <Arg>DictStrBool</Arg>
        </ReqSetPartyMemberPrivilege>
        <!-- 房主设置玩家的发言顺序 -->
        <ReqSetPartyMemberOrder CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言顺序的列表 -->
            <Arg>ListStr</Arg>
        </ReqSetPartyMemberOrder>
         <!-- 房主设置全员自由发言 -->
        <ReqSetPartySpeechFree CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqSetPartySpeechFree>
        <ReqPartyPullGaglist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqPartyPullGaglist>
        <ReqPartyCancelGaglist> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 成员的ID -->
            <Arg>string</Arg>
        </ReqPartyCancelGaglist>
        <!-- 成员申请上麦 -->
        <ReqPartyApplyOpenMic CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 申请理由 -->
            <Arg>string</Arg>
        </ReqPartyApplyOpenMic>
        <!-- 房主清理申请列表的内容 -->
        <ReqPartyCleanApplicationList> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 同意玩家ID列表 -->
            <Arg>ARRAY_STRING</Arg>
            <!-- 拒绝玩家ID列表 -->
            <Arg>ARRAY_STRING</Arg>
        </ReqPartyCleanApplicationList>
        <!-- 成员修改发言状态 -->
        <ReqPartyMemberModifySpeechState CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 发言状态 -->
            <Arg>int</Arg>
        </ReqPartyMemberModifySpeechState>
        <!-- 房主修改身份标签的名称 -->
        <ReqPartyModifyIdentityName> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 身份标签ID -->
            <Arg>int</Arg>
            <!-- 身份标签名字 -->
            <Arg>string</Arg>
        </ReqPartyModifyIdentityName>
        <!-- 房主分配身份标签 -->
        <ReqPartyAssignIdentity2Avatar> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 身份标签ID -->
            <Arg>int</Arg>
            <!-- 玩家ID -->
            <Arg>ListStr</Arg>
        </ReqPartyAssignIdentity2Avatar>
        <!-- 查找玩家名字-->
        <ReqPartyFindMemberName CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 模式串 -->
            <Arg>string</Arg>
        </ReqPartyFindMemberName>
        <!-- 玩家移除身份标签-->
        <ReqRemoveIdentification CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 身份ID -->
            <Arg>int</Arg>
        </ReqRemoveIdentification>
        <!-- 成员身份状态调整-->
        <ReqPartyModifyIdentityState CD="1"> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 身份ID -->
            <Arg>int</Arg>
            <!-- 身份状态 -->
            <Arg>int</Arg>
        </ReqPartyModifyIdentityState>
        <!-- 获取指定id列表的玩家信息 -->
        <ReqPartyGetMemberInfo> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家列表 -->
            <Arg>ARRAY_STRING</Arg>
        </ReqPartyGetMemberInfo>
        <!-- 获取指定shortUid列表的玩家信息 -->
        <ReqPartyGetMemberInfoByShortUid> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
            <!-- 玩家列表 -->
            <Arg>ARRAY_UINT</Arg>
        </ReqPartyGetMemberInfoByShortUid>
        <!-- 麦序模式下  玩家下麦操作 -->
        <ReqPartyMemberCloseMic> <Exposed/>
            <!-- 房间ID -->
            <Arg>int</Arg>
        </ReqPartyMemberCloseMic>
    </ServerMethods>
</root>