<?xml version="1.0" ?>
<root>
    <Properties>
        <!-- 已获取的收藏品基础信息 key:收藏品id,value:获取时间.参考了成就的设计,如果时间为奇数表示奖励未领取,偶数表示奖励已领取 -->
        <CollectiblesInfo Type="COLLECTIBLESINFODICT" Flags="SERVER_ONLY" Persistent="true"/>
        <!-- 已获取的收藏品额外信息-->
        <CollectiblesExternInfo Type="COLLECTIBLESEXTERNINFODICT" Flags="SERVER_ONLY" Persistent="true"/>
        <!-- 收藏品类别积分/奖励信息 -->
        <CollectiblesRewardDict Type="COLLECTIBLESREWARDDICT" Flags="SERVER_ONLY" Persistent="true"/>
    </Properties>

    <Implements/>

    <ClientMethods>
        <RetGetCollectiblesInfo>   <!-- 获取收藏品全量信息回复 -->
            <Arg>COLLECTIBLESINFODICT</Arg>
            <Arg>COLLECTIBLESEXTERNINFODICT</Arg>
            <Arg>COLLECTIBLESREWARDDICT</Arg>
        </RetGetCollectiblesInfo>

        <OnMsgGetCollectiblesNotify>  <!-- 玩家获得收藏品通知 -->
            <!-- 获得的收藏品 -->
            <Arg>COLLECTIBLESINFODICT</Arg>
            <!-- 收藏品大类积分 -->
            <Arg>CollectiblesScoreDict</Arg>
        </OnMsgGetCollectiblesNotify>

        <RetGetCollectiblesLevelReward>    <!-- 领取等级奖励回复 -->
            <!-- CollectiblesType 收藏品大类 -->
            <Arg>int</Arg>
            <!-- isGetAll 是否全部领取 -->
            <Arg>bool</Arg>
            <!-- Level 领取指定等级的奖励,仅IsGetAll为false时有效 -->
            <Arg>int</Arg>
            <!-- result 结果 -->
			<Arg>UINT</Arg>
        </RetGetCollectiblesLevelReward>

        <RetModifyCollectiblesDesc>     <!-- 修改收藏品描述回复 -->
            <!-- CollectibleID 收藏品id -->
            <Arg>int</Arg>
            <!-- Desc 收藏品描述 -->
            <Arg>string</Arg>
            <!-- result 结果 -->
			<Arg>UINT</Arg>
        </RetModifyCollectiblesDesc>

        <RetGetCollectiblesReward>      <!-- 领取收藏品奖励回复 -->
            <!-- CollectiblesID 收藏品ID -->
            <Arg>int</Arg>
            <!-- result 结果 -->
			<Arg>UINT</Arg>
        </RetGetCollectiblesReward>

        <RetGetAllCollectiblesReward>   <!-- 一键领取收藏品奖励回复 -->
            <!-- CollectiblesType 收藏品大类 -->
            <Arg>int</Arg>
            <!-- result 结果 -->
			<Arg>UINT</Arg>
            <!-- 领取奖励的收藏品信息 -->
            <Arg>COLLECTIBLESINFODICT</Arg>
        </RetGetAllCollectiblesReward>
    </ClientMethods>

    <ServerMethods>
        <ReqGetCollectiblesInfo> <Exposed/>     <!-- 获取收藏品全量信息请求 -->
        </ReqGetCollectiblesInfo>

        <ReqGetCollectiblesLevelReward> <Exposed/>   <!-- 领取等级奖励请求 -->
            <!-- CollectiblesType 收藏品大类 -->
            <Arg>int</Arg>
            <!-- IsGetAll 是否全部领取 -->
            <Arg>bool</Arg>
            <!-- Level 领取指定等级的奖励,仅IsGetAll为false时有效 -->
            <Arg>int</Arg>
        </ReqGetCollectiblesLevelReward>

        <ReqModifyCollectiblesDesc> <Exposed/>    <!-- 修改收藏品描述请求 -->
            <!-- CollectibleID 收藏品ID -->
            <Arg>int</Arg>
            <!-- Desc 收藏品描述 -->
            <Arg>string</Arg>
        </ReqModifyCollectiblesDesc>

        <ReqGetCollectiblesReward> <Exposed/>    <!-- 领取收藏品奖励请求 -->
            <!-- CollectiblesID 收藏品ID -->
            <Arg>int</Arg>
        </ReqGetCollectiblesReward>

        <ReqGetAllCollectiblesReward> <Exposed/> <!-- 一键领取收藏品奖励请求 -->
            <!-- CollectiblesType 收藏品大类 -->
            <Arg>int</Arg>
        </ReqGetAllCollectiblesReward>
    </ServerMethods>
</root>
