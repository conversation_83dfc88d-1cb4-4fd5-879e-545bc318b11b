<root>
    <Properties>
        <popularity Type="UINT" Persistent="true" Default="0"/>             <!-- 人气值 -->
        <liberality Type="UINT" Persistent="true" Default="0"/>             <!-- 豪气值 -->
        <popularityWeekly Type="UINT" Persistent="true" Default="0"/>       <!-- 每周人气值 -->
        <popularitySource Type="UINT_MAP" Persistent="true" />        <!-- 各类型礼物人气值 -->
        <giftContribution Type="UINT" Persistent="true" Default="0"/>       <!-- 贡献值 -->
        <giftContributionWeekly Type="UINT" Persistent="true" Default="0"/> <!-- 每周贡献值 -->
        <giveGiftCount Type="UINT" Persistent="true" /> <!-- 送礼计数 -->
        <recvGiftCount Type="UINT" Persistent="true" /> <!-- 收礼计数 -->
        <giveGiftRecordList Type="GIFT_RECORD_LIST" Persistent="true"/>     <!-- 送礼记录  -->
        <recvGiftRecordList Type="GIFT_RECORD_LIST" Persistent="true"/>     <!-- 收礼记录  -->
        <dailyGiftExtraAttraction Type="UINT" Flags="OWN_CLIENT" Persistent="true" /> <!-- 每日额外加成好感度数值 -->
    </Properties>

    <ClientMethods>
        <onSyncGiftExtraAttractionTime>
            <Arg> LONGTIME </Arg>
        </onSyncGiftExtraAttractionTime>

        <onGetPlayerGiftRecord>
            <Arg> UINT </Arg> <!--recordType 见 const.GIFT_RECORD_TYPE_xxx-->
            <Arg> ENTITY_ID </Arg> <!--tgtGbId-->
            <Arg> GIFT_RECORD_LIST </Arg> <!--记录列表-->
        </onGetPlayerGiftRecord>

        <recvGiftEffect >
            <Arg> UINT </Arg>             <!--giftId-->
            <Arg> UINT </Arg>             <!--本次送的giftCnt-->
        </recvGiftEffect>

        <recvGiftScreenEffect>
            <Arg> UINT </Arg>             <!--giftId-->
            <Arg> UINT </Arg>             <!--本次送的giftCnt-->
        </recvGiftScreenEffect>

        <onGiveGiftSuccess>
            <Arg> ENTITY_ID </Arg>           <!--tgtGbId-->
            <Arg> UINT </Arg>           <!--giftId-->
            <Arg> UINT </Arg>           <!--giftCnt-->
            <Arg> string </Arg>         <!--tgtRoleName-->
        </onGiveGiftSuccess>

        <onRecvGift>
            <Arg> ENTITY_ID </Arg>           <!--srcGbId-->
            <Arg> ROLENAME </Arg>       <!--srcRolename-->
            <Arg> UINT </Arg>           <!--giftId-->
            <Arg> UINT </Arg>           <!--giftCnt-->
            <Arg> UINT </Arg>           <!--popularityAdd-->
        </onRecvGift>

        <recvGiftBulletin>             <!--收礼跑马灯-->
            <Arg> ENTITY_ID </Arg>           <!--送礼者gbId-->
            <Arg> ENTITY_ID </Arg>           <!--收礼者gbId-->
            <Arg> UINT </Arg>           <!--giftId-->
            <Arg> UINT </Arg>           <!--giftCnt-->
            <Arg> string </Arg>         <!--senderRoleName-->
            <Arg> string </Arg>         <!--recvRoleName-->
            <Arg> UINT </Arg>           <!-- 送礼者hostId -->
            <Arg> UINT </Arg>           <!-- 收礼者hostId -->
        </recvGiftBulletin>

        <onGetPlayerRecvGiftRankList>
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
            <Arg> GIFT_RANK_PLAYER_INFO_LIST </Arg>
        </onGetPlayerRecvGiftRankList>

        <onGetPlayerFansRankList>
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
            <Arg> GIFT_RANK_PLAYER_INFO_LIST </Arg>
        </onGetPlayerFansRankList>
    </ClientMethods>

    <ServerMethods>
        <ReqGiveGift CD="0.17"> <Exposed />
            <Arg> ENTITY_ID </Arg>           <!--to gbId-->
            <Arg> UINT </Arg>           <!--giftId-->
            <Arg> UINT </Arg>           <!--giftCnt-->
        </ReqGiveGift>

        <!-- <getPlayerGiftRecord> <Exposed />
            <Arg> UINT </Arg> recordType 见 const.GIFT_RECORD_TYPE_xxx
            <Arg> ENTITY_ID </Arg> tgtGbId
        </getPlayerGiftRecord> -->

        <addToThankList> <Exposed />
            <Arg> ENTITY_ID </Arg>           <!--to gbId-->
            <Arg> UINT </Arg>           <!--grade-->
            <Arg> MAIL_ID </Arg>        <!--mailId-->
        </addToThankList>

        <replyThanks> <Exposed />       <!--答谢送礼者-->
            <Arg> ENTITY_ID </Arg>           <!--to gbId-->
            <Arg> MAIL_ID </Arg>        <!--mailId-->
        </replyThanks>

        <getPlayerRecvGiftRankList> <Exposed />  <!--获取目标玩家收礼排行榜-->
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
        </getPlayerRecvGiftRankList>

        <getPlayerFansRankList> <Exposed />  <!-- 获取目标玩家粉丝排行榜 -->
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
        </getPlayerFansRankList>

        <doGiveGift>
            <Arg> none </Arg>          <!--tgtRoleBrief-->
            <Arg> none </Arg>          <!--extArgs-->
        </doGiveGift>

        <onOtherGetGiftRecord>
            <Arg> mailbox </Arg>
            <Arg> UINT </Arg> <!-- recordType -->
        </onOtherGetGiftRecord>

        <onGetGiftRecordFromCache>
            <Arg> none </Arg> <!-- cahce info -->
            <Arg> UINT </Arg> <!-- recordType -->
            <Arg> ENTITY_ID </Arg> <!--tgtGbId-->
        </onGetGiftRecordFromCache>

        <onRecvGift>
            <Arg> none </Arg>           <!--收礼信息列表-->
        </onRecvGift>

        <onGetPlayerRecvGiftRankList>
            <Arg> GIFT_RANK_PLAYER_INFO_LIST </Arg>
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
        </onGetPlayerRecvGiftRankList>

        <onGiveGift> <!-- 获取跨服收礼玩家信息回调 -->
            <Arg> none </Arg>  <!-- playerBasicInfoList -->
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
            <Arg> UINT </Arg> <!-- giftId -->
            <Arg> UINT </Arg> <!-- giftCnt -->
        </onGiveGift>

        <onGetPlayerFansRankList>
            <Arg> GIFT_RANK_PLAYER_INFO_LIST </Arg>
            <Arg> ENTITY_ID </Arg> <!-- tgtGbId -->
        </onGetPlayerFansRankList>

    </ServerMethods>
</root>
