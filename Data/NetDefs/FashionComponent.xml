<?xml version="1.0" ?>
<root>
	<Properties>
		<fashionValue Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true"/>
		<fashionTreasure Type="DictIntBool" Default="{}" Flags="OWN_CLIENT" Persistent="true"/>
		<playerWardrobe Type="PLAYER_WARDROBE_DEFINE" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <playerAppearance Type="PLAYER_APPEARANCE" Default="{}" Flags="ALL_CLIENTS" Persistent="false"/>
		<playerCompliantAppearance Type="PLAYER_APPEARANCE" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
		<playerAppearanceDirty Type="bool" Default="false" Flags="SERVER_ONLY" Persistent="true"/>
		<isWearingTemporaryAppearance Type="bool" Default="false" Flags="OWN_CLIENT" Persistent="true"/>
		<playerCachedAppearance Type="APPEARANCE_ID_LIST" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
		<playerAppearanceStrategy Type="PLAYER_APPEARANCE_STRATEGY_DICT" Flags="SERVER_ONLY" Persistent="true"/> 
		<currentStrategy Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
		<showWeapon Type="bool" Default="true" Flags="OWN_CLIENT" Persistent="true"/>
		<showAccessory Type="bool" Default="true" Flags="OWN_CLIENT" Persistent="true"/>
		<hasNewAppearanceComp Type="bool" Default="false" Flags="OWN_CLIENT" Persistent="true"/>
		<portrait Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>							<!-- 玩家头像 -->
		<portraitFrame Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>						<!-- 玩家头像框 -->
		<unlockedPortrait Type="DictIntBool" Default="{}" Flags="OWN_CLIENT" Persistent="true"/>			<!-- 已解锁的头像 -->
		<unlockedPortraitFrame Type="DictIntBool" Default="{}" Flags="OWN_CLIENT" Persistent="true"/>		<!-- 已解锁的头像框 -->
	</Properties>
	<Implements/>
	<ClientMethods>
		<RetGetWholeWardrobeData>
            <Arg> PLAYER_WARDROBE_DEFINE </Arg> 
			<Arg> APPEARANCE_STAIN_TO_STRATEGY </Arg> <!--外观染色槽位到预制方案槽位的反向索引-->
        </RetGetWholeWardrobeData>

		<RetWholeAppearanceStategyData>
			<Arg> PLAYER_APPEARANCE_STRATEGY_DICT </Arg>
		</RetWholeAppearanceStategyData>
		
		<RetChangeAppearanceNewFlag>
			<Arg> APPEARANCE_SUBTYPE </Arg>
		</RetChangeAppearanceNewFlag>

		<RetCollectAppearanceComp>
            <Arg> APPEARANCE_ID_LIST </Arg>    
        </RetCollectAppearanceComp>

        <RetCancelCollectAppearanceComp> 
            <Arg> APPEARANCE_ID_LIST </Arg>    
        </RetCancelCollectAppearanceComp>

		<RetBuyAppearanceComp> 	<!--批量购买外观部件-->
			<Arg> PLAYER_WARDROBE_CELL_DICT </Arg> <!--购买成功的外观的衣柜信息-->  
		</RetBuyAppearanceComp>

        <RetSaveWardrobeCell>	<!--保存单个衣柜单元-->
			<Arg> APPEARANCE_ID </Arg> 	
			<Arg> PLAYER_WARDROBE_CELL </Arg>
		</RetSaveWardrobeCell>

		<RetSetCurWearStain> 	<!--应用已保存的染色方案-->
			<Arg> APPEARANCE_ID </Arg> 	
			<Arg> int </Arg> 	<!--stainSlot, 默认染色传0-->
		</RetSetCurWearStain>

		<RetSaveAppearanceStrategy> <!-- 保存预设方案-->
			<Arg> int </Arg>	<!-- 第几个方案，slot-->
			<Arg> PLAYER_APPEARANCE_STRATEGY </Arg>	<!--预设方案数据-->
		</RetSaveAppearanceStrategy>
		
		<RetChangeAppearanceStrategyName>
			<Arg> int </Arg>	<!-- 第几个方案，slot-->
			<Arg> string </Arg>	<!-- 方案的名字 -->
		</RetChangeAppearanceStrategyName>

		<RetChangePortrait>		  	<!--更换头像回复-->
			<Arg> bool </Arg>   	<!-- 结果 -->
			<Arg> int </Arg>   		<!-- PortraitID -->
		</RetChangePortrait>

		<RetChangePortraitFrame>  	<!--更换头像框回复-->
			<Arg> bool </Arg>   	<!-- 结果 -->
			<Arg> int </Arg>   		<!-- PortraitFrameID -->
		</RetChangePortraitFrame>

	</ClientMethods>
	<ServerMethods>
		<ReqWholeWardrobeData CD="1"> <Exposed/> <!--获取全量衣橱数据-->
		</ReqWholeWardrobeData>

		<ReqWholeAppearanceStategyData CD="1"> <Exposed/> <!--获取全量预设方案数据-->
		</ReqWholeAppearanceStategyData>
		
		<ReqChangeAppearanceNewFlag CD="0.1"> <Exposed/> 
			<Arg> APPEARANCE_SUBTYPE </Arg>
		</ReqChangeAppearanceNewFlag>

        <ReqCollectAppearanceComp CD="0.2"> <Exposed/> <!--收藏外观-->
            <Arg> APPEARANCE_ID </Arg>    
        </ReqCollectAppearanceComp>

        <ReqCancelCollectAppearanceComp CD="0.2"> <Exposed/> <!--取消收藏外观-->
            <Arg> APPEARANCE_ID </Arg>    
        </ReqCancelCollectAppearanceComp>

		<ReqSaveClientAppearanceTimed CD="1"> <Exposed/> <!--定时批量穿戴外观部件-->
            <Arg> APPEARANCE_ID_LIST </Arg> 
		</ReqSaveClientAppearanceTimed>

        <ReqSaveClientAppearance CD="1"> <Exposed/> <!--批量穿戴外观部件-->
            <Arg> APPEARANCE_ID_LIST </Arg> 
		</ReqSaveClientAppearance>

        <ReqBuyAppearanceComp CD="1"> <Exposed/> <!--批量购买外观部件-->
            <Arg> APPEARANCE_ID_LIST </Arg>    
		</ReqBuyAppearanceComp>

        <ReqSaveStain CD="1"><Exposed/> <!--保存普通染色方案-->
			<Arg> APPEARANCE_ID </Arg> 	
			<Arg> int </Arg> 	<!--stainSlot-->
			<Arg> FASHION_STAIN_INFO </Arg> 
			<Arg> string </Arg> <!--pictureID-->
		</ReqSaveStain>

		<ReqSaveAdvancedStain CD="1"><Exposed/> <!--保存高级染色方案-->
			<Arg> APPEARANCE_ID </Arg> 	
			<Arg> int </Arg> 	<!--stainSlot-->
			<Arg> FASHION_ADVANCED_STAIN_INFO </Arg> 
			<Arg> string </Arg> <!--pictureID-->
		</ReqSaveAdvancedStain>

        <ReqSetCurWearStain CD="0.5"> <Exposed/> <EnableNilArgs/> <!--应用已保存的染色方案-->
			<Arg> APPEARANCE_ID </Arg> 
			<Arg> int </Arg> 	<!--stainSlot-->
		</ReqSetCurWearStain>

        <ReqSaveFinetuneInfo CD="0.5"> <Exposed/> <EnableNilArgs/> <!--保存微调信息-->
            <Arg> APPEARANCE_ID </Arg>    	
            <Arg> PVector3 </Arg> 	<!--位置-->
			<Arg> PRotator </Arg> 	<!--旋转-->
			<Arg> float </Arg> 		<!--缩放-->
            <Arg> int </Arg> 		<!--slotID-->
		</ReqSaveFinetuneInfo>

		<ReqSaveAppearanceStrategy CD="0.5"> <Exposed/> <!-- 保存预设方案-->
			<Arg> int </Arg>	<!-- 第几个方案，slot-->
			<Arg> string </Arg>	<!--方案的拍照缩略图-->
		</ReqSaveAppearanceStrategy>

		<ReqChangeAppearanceStrategyName CD="0.5"> <Exposed/> <!--给预设方案起名字-->
			<Arg> int </Arg>	<!-- 第几个方案，slot-->
			<Arg> string </Arg>	<!-- 方案的名字 -->
		</ReqChangeAppearanceStrategyName>
		
		<ReqUseAppearanceStrategy CD="0.5"> <Exposed/><!--使用玩家的预设方案-->
			<Arg> int </Arg>	<!--方案的slotid-->
		</ReqUseAppearanceStrategy>
		
		<ReqSetWeaponShowStatus CD="0.17"><Exposed/><!--改变武器显隐-->
			<Arg> bool </Arg>
		</ReqSetWeaponShowStatus>

		<ReqSetAccessoryShowStatus CD="0.17"><Exposed/><!--改变饰品显隐-->
			<Arg> bool </Arg>
		</ReqSetAccessoryShowStatus>

		<ReqGetFashionTreasure CD="0.5"> <Exposed/> <EnableNilArgs/> <!--领取风尚值的奖励-->
			<Arg> int </Arg>	<!--FashionLevel里的uniqueID-->
		</ReqGetFashionTreasure>

		<ReqChangePortrait> <Exposed/>  		<!--更换头像请求-->
			<Arg> int </Arg>					<!--portraitID-->
		</ReqChangePortrait>

		<ReqChangePortraitFrame> <Exposed/>  	<!--更换头像框请求-->
			<Arg> int </Arg>					<!--portraitFrameID-->
		</ReqChangePortraitFrame>
	</ServerMethods>
</root>