<?xml version="1.0" ?>
<root>
    <Properties>
		<MoveStateNew Type="int" Flags="ALL_CLIENTS" Default="1" />
		<bCurveMove Type="bool" Flags="ALL_CLIENTS" Default="false" />
		<CurvePathIndex Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<CurveMoveSpeed Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<CurveMovePathID Type="string" Flags="ALL_CLIENTS" Default=""/>
		<CurvePatrolType Type="int" Flags="ALL_CLIENTS" Default="0"/>
	</Properties>

	<Implements>
	</Implements>

	<ClientMethods>
		<OnMsgCurveToPoint>
			<Arg> int </Arg>
		</OnMsgCurveToPoint>

		<OnMsgCurveReachPoint>
			<Arg> int </Arg>
		</OnMsgCurveReachPoint>

		<OnMsgCurvePauseMove>
		</OnMsgCurvePauseMove>

		<OnMsgStartCurveMove>
		</OnMsgStartCurveMove>

		<OnMsgStopCurveMove>
		</OnMsgStopCurveMove>
		
		<OnMsgCurveResumeMove>
			<Arg> int </Arg>
		</OnMsgCurveResumeMove>
	</ClientMethods>
</root>
