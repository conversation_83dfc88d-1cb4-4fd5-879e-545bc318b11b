<?xml version="1.0" ?>
<root HasActor="true">
    <Properties>
		<!-- 一些关键的信息, aoi等级为2的时候也会同步 -->
		<TemplateID Type="int" Flags="ALL_CLIENTS" Default="0" BriefProp="true"/>
		<Level Type="int" Flags="ALL_CLIENTS" Default="0" BriefProp="true" ActorAttr="true" />
		<NpcType Type="int" Flags="ALL_CLIENTS|GHOST" Default="0" BriefProp="true"/>
		<BossType Type="int" Flags="ALL_CLIENTS|GHOST" Default="0" BriefProp="true" />
		<bBornState Type="bool" Flags="ALL_CLIENTS|GHOST" Default="false" BriefProp="true" />

		<IsPet Type="bool" Flags="SERVER_ONLY"/>
		<MonsterID Type="int" Flags="SERVER_ONLY" Default="0"/>
		<prevHp Type="int" Flags="SERVER_ONLY" Default="-1"/>
		<SummonerID Type="int" Flags="SERVER_ONLY" Default="nil"/>
		<SummonTID Type="int" Flags="SERVER_ONLY" Default="nil"/>
		<SimpleAIID Type="int" Flags="SERVER_ONLY" Default="0"/>
		<npcSpawnerEntityID Type="string" Flags="SERVER_ONLY" Default=""/>
		<BelongUser Type="string" Flags="SERVER_ONLY" Default=""/>
		<ElementEffectMap Type="DictIntInt" Flags="SERVER_ONLY" />
		
		<RaceType Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<WayPointPath Type="string" Flags="ALL_CLIENTS" Default=""/>
		<SummonLevel Type="int" Flags="ALL_CLIENTS" Default="1"/>
		<IsSummoned Type="bool" Flags="ALL_CLIENTS" Default="false"/>
		<CtxParamTable Type="CtxParamTable" Flags="ALL_CLIENTS"/>
		<spawnerID Type="string" Flags="ALL_CLIENTS" Default=""/>
		<groupMemberIdx Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<bUseSpiritualVision Type="bool" Flags="ALL_CLIENTS"/>
		<bOnlyInSpiritualVision Type="bool" Flags="ALL_CLIENTS"/>
		<FellowID Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<InstanceID Type="string" Flags="ALL_CLIENTS" Default=""/>
		<ExploreElementEvent Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<State Type="int" Flags="ALL_CLIENTS" Default="1"/>
		<DisplayName Type="string" Flags="ALL_CLIENTS" Default=""/>
		<ExploreElementID Type="int" Flags="ALL_CLIENTS" Default="0"/>
		<BossIdlePosture Type="int" Default="0" Flags="ALL_INITIAL_ONLY" />
		<trapExcelIDDict Type="DictIntBool" Default="nil" Flags="ALL_CLIENTS"/>
		<trapWarningValue Type="float" Flags="ALL_CLIENTS"/>
		<trapGroupID Type="int" Default="nil" Flags="ALL_CLIENTS"/>
		<trapIDDict Type="DictIntBool" Default="nil" Flags="SERVER_ONLY"/>
		<trapID2trapExcelID Type="DictIntInt" Default="nil" Flags="SERVER_ONLY"/>
		<trapExcelID2trapID Type="DictIntInt" Default="nil" Flags="SERVER_ONLY"/>
		<trapID2QuestID Type="DictIntInt" Default="nil" Flags="SERVER_ONLY"/>
		<trapIDEnterCount Type="dict" Key="int" Value="DictStrUInt" Flags="SERVER_ONLY"/>
		<trapIDRemovePlayer Type="dict" Key="int" Value="DictStrBool" Flags="SERVER_ONLY"/>
		<interactiveState Type="int" Default="2" Flags="ALL_CLIENTS" />
		<InteractorUIIDMap Type="SPECIAL_INTERACTIVE_KEY_INFO" Flags="ALL_CLIENTS"/>
		<InteractorDistance Type="int" Default="0" Flags="ALL_CLIENTS" />
		<catchSchedule Type="float" Default="0" Flags="ALL_CLIENTS"/>
		<bAutoGaze Type="BOOL" Default="false" Flags="ALL_CLIENTS" />
		<!-- npc 运行时配置, 流程图设置参数覆盖使用 -->
		<runtimeSettings Type="NPC_RUNTIME_SETTINGS" Flags="ALL_CLIENTS" Default="nil"/>
    </Properties>
	<Implements>
		<Interface>ActorBase</Interface>
		<Interface>PropComponent</Interface>
		<Interface>FightPropComponent</Interface>
		<Interface>FStatePropComponent</Interface>
		<Interface>BuffComponentNew</Interface>
		<Interface>MoveComponent</Interface>
		<Interface>CurveMoveComponent</Interface>
		<Interface>TakeDamageComponent</Interface>
		<Interface>HitFeedbackComponent</Interface>
		<Interface>DebugComponent</Interface>
		<Interface>DamageTrackComponent</Interface>
		<Interface>NpcAggroComponent</Interface>
        <Interface>NpcAsideTalkComponent</Interface>
		<Interface>StateComponent</Interface>
		<Interface>BreakDefenseComponent</Interface>
		<Interface>ParallelBehaviorControlCompBase</Interface>
		<Interface>ComplexLocomotionControlComponent</Interface>
        <Interface>NpcViewControlComponent</Interface>
		<Interface>CombatEffectComponent</Interface>
		<Interface>VehicleComponent</Interface>
		<Interface>WeaponItemComponent</Interface>
		<Interface>LUnitProxyComponent</Interface>
		<Interface>NpcInteractorComponent</Interface>
		<Interface>SummonComponent</Interface>
		<Interface>SkillComponentNew</Interface>
		<Interface>AbilityComponent</Interface>
		<Interface>SystemActionComponent</Interface>
        <Interface>StateConflictComponentBase</Interface>
		<Interface>FlowchartActionComponent</Interface>
		<Interface>MorphComponent</Interface>
		<Interface>GazeControlComponent</Interface>
		<Interface>AnimControlComponent</Interface>
		<Interface>BehaviorMoveComponent</Interface>
	</Implements>

    <ClientMethods>
        <OnMsgEntityDead>
            <!--1 reason -->
            <Arg> int </Arg>
			<!--2 KillerID -->
            <Arg>string</Arg>
        </OnMsgEntityDead>
		<OnMsgWayPointEvent>
            <!--1 reason -->
            <Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> float </Arg>
            <Arg> string </Arg>
        </OnMsgWayPointEvent>
		<OnMsgEnableCurveMove>
			<Arg> bool </Arg>
			<Arg> string </Arg>
            <Arg> int </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
        </OnMsgEnableCurveMove>
		<OnMsgSyncCurveStateEvent>
            <Arg> int </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
        </OnMsgSyncCurveStateEvent>
		<OnMsgSyncCurvePos>
			<Arg> float </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
        </OnMsgSyncCurvePos>
		<OnMsgSyncCurveRotation>
			<Arg> float </Arg>
			<Arg> float </Arg>
			<Arg> float </Arg>
        </OnMsgSyncCurveRotation>
		<OnMsgBossMechanismCoolDown>
			<!--1 type -->
			<Arg> int </Arg>
			<!--2 instId -->
			<Arg> int </Arg>
			<!--3 duration -->
			<Arg> float </Arg>
			<!--4 leftTime -->
			<Arg> float </Arg>
			<!--2 paramList -->
			<Arg> VList </Arg>
		</OnMsgBossMechanismCoolDown>
		<OnMsgBossMechanismCoolDownFinish>
			<!--1 type -->
			<Arg> int </Arg>
			<!--2 instId -->
			<Arg> int </Arg>
		</OnMsgBossMechanismCoolDownFinish>
		<OnMsgArrodesPlayEmojiPreset>
			<!--1 PreSetID -->
			<Arg> int </Arg>
		</OnMsgArrodesPlayEmojiPreset>
		<RetShowAsideTalk>
			<!--1 asideID -->
			<Arg> int </Arg>
		</RetShowAsideTalk>
		<OnChangeBossIdlePosture>
			<Arg> int </Arg><!--1 Posture -->
		</OnChangeBossIdlePosture>

		<OnMsgNpcActorAddTrap>
			<Arg> ENTITY_ID </Arg>
			<Arg> int </Arg> <!--Trap ExcelID-->
		</OnMsgNpcActorAddTrap>

		<OnMsgNpcActorAddMultiTrap>
			<Arg> ENTITY_ID </Arg>
			<Arg> DictIntBool </Arg> <!--key: Trap ExcelID value:ignore-->
		</OnMsgNpcActorAddMultiTrap>

		<OnMsgNpcActorRemoveTrap>
			<Arg> ENTITY_ID </Arg>
			<Arg> int </Arg> <!--Trap ExcelID-->
		</OnMsgNpcActorRemoveTrap>

		<OnMsgNpcActorRemoveMultiTrap>
			<Arg> ENTITY_ID </Arg>
			<Arg> DictIntBool </Arg> <!--key: Trap ExcelID value:ignore-->
		</OnMsgNpcActorRemoveMultiTrap>

		<OnMsgStopGossip>
		</OnMsgStopGossip>

	</ClientMethods>
	<ServerMethods>
	</ServerMethods>
</root>
