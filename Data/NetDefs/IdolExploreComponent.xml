<root>
    <Properties>
        <IdolExploreStaus Type="int" Flags="OWN_CLIENT" Default="1"/>   <!--神像探索状态，1为未解锁，2,3为进行中，4为已完成-->
        <Idol3TriggerStatus Type="int" Flags="SERVER_ONLY" Default="1"/> <!--神像3的宝箱状态，1为未刷新，2为已刷新未被打开，3为已被打开-->
    </Properties>

    <ClientMethods>
        <OnMsgPlayIdol3Effect> <!-- 播放神像3特效 -->
        </OnMsgPlayIdol3Effect>
    </ClientMethods>

    <ServerMethods>
        <ReqChangeIdolExploreStatus> <Exposed /> <!-- 请求更新神像探索状态 -->
        </ReqChangeIdolExploreStatus>

        <ReqCompleteIdolExploration> <Exposed /> <!-- 请求完成神像探索 ,限神像2，4，6使用-->
            <!--1 神像实例id, -->
            <Arg> string </Arg>
        </ReqCompleteIdolExploration>

        <ReqTeleportToPlane> <Exposed /> <!-- 请求传送到位面 -->
            <!--1 神像实例id -->
            <Arg> string </Arg>
            <!--1 位面id -->
            <Arg> int </Arg>
        </ReqTeleportToPlane>

        <ReqInteractBallAddBuff> <Exposed /> <!-- 吃球请求加buff -->
        </ReqInteractBallAddBuff>

        <ReqGenIdol3Box> <Exposed /> <!-- 请求生成神像3宝箱 -->
        </ReqGenIdol3Box>
    </ServerMethods>
</root>