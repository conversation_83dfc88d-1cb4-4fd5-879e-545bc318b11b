<?xml version="1.0" ?>
<root>
	<Implements/>
	<ClientMethods>
 		<OnMsgDrawDebugLine>
            <!--1 lineStart -->
            <Arg> Vector3 </Arg>
            <!--2 lineEnd -->
            <Arg> Vector3 </Arg>
            <!--3 color -->
            <Arg> Vector3 </Arg>
            <!--4 duration -->
            <Arg> float </Arg>
            <!--5 thickness -->
            <Arg> float </Arg>
        </OnMsgDrawDebugLine>
        <OnMsgDrawDebugCircle>
            <!--1 center -->
            <Arg> Vector3 </Arg>
            <!--2 radius -->
            <Arg> float </Arg>
            <!--3 numSegments -->
            <Arg> int </Arg>
            <!--4 yAxis -->
            <Arg> Vector3 </Arg>
            <!--5 zAxis -->
            <Arg> Vector3 </Arg>
            <!--6 color -->
            <Arg> Vector3 </Arg>
            <!--7 duration -->
            <Arg> float </Arg>
            <!--8 thickness -->
            <Arg> float </Arg>
            <!--9 drawAxis -->
            <Arg> bool </Arg>
        </OnMsgDrawDebugCircle>
		<OnMsgDrawDebugPoint>
			<!--1 position -->
			<Arg> Vector3 </Arg>
			<!--2 size -->
			<Arg> float </Arg>
			<!--3 color -->
			<Arg> Vector3 </Arg>
			<!--4 duration -->
			<Arg> float </Arg>
		</OnMsgDrawDebugPoint>
		<OnMsgDrawDebugArrow>
			<!--1 lineStart -->
			<Arg> Vector3 </Arg>
			<!--2 lineEnd -->
			<Arg> Vector3 </Arg>
			<!--3 arrowSize -->
			<Arg> float </Arg>
			<!--4 color -->
			<Arg> Vector3 </Arg>
			<!--5 duration -->
			<Arg> float </Arg>
			<!--6 thickness -->
			<Arg> float </Arg>
		</OnMsgDrawDebugArrow>
		<OnMsgDrawDebugBox>
			<!--1 center -->
			<Arg> Vector3 </Arg>
			<!--2 extent -->
			<Arg> Vector3 </Arg>
			<!--3 rotation -->
			<Arg> Vector3 </Arg>
			<!--4 color -->
			<Arg> Vector3 </Arg>
			<!--5 duration -->
			<Arg> float </Arg>
			<!--6 thickness -->
			<Arg> float </Arg>
		</OnMsgDrawDebugBox>
        <OnMsgDrawDebugSphere>
            <!--1 center -->
            <Arg> Vector3 </Arg>
            <!--2 radius -->
            <Arg> float </Arg>
			<!--3 segments -->
			<Arg> int </Arg>
            <!--4 color -->
            <Arg> Vector3 </Arg>
            <!--5 duration -->
            <Arg> float </Arg>
            <!--6 thickness -->
            <Arg> float </Arg>
        </OnMsgDrawDebugSphere>
        <OnMsgDrawDebugCylinder>
            <!--1 centerStart -->
            <Arg> Vector3 </Arg>
            <!--2 centerEnd -->
            <Arg> Vector3 </Arg>
            <!--3 radius -->
            <Arg> float </Arg>
			<!--4 segments -->
			<Arg> int </Arg>
            <!--5 color -->
            <Arg> Vector3 </Arg>
            <!--6 duration -->
            <Arg> float </Arg>
            <!--7 thickness -->
            <Arg> float </Arg>
        </OnMsgDrawDebugCylinder>
        <OnMsgDrawDebugCone>
            <!--1 origin -->
            <Arg> Vector3 </Arg>
            <!--2 direction -->
            <Arg> Vector3 </Arg>
            <!--3 length -->
            <Arg> float </Arg>
            <!--4 angleWidth -->
            <Arg> float </Arg>
            <!--5 angleHeight -->
            <Arg> float </Arg>
			<!--6 numSides -->
			<Arg> int </Arg>
            <!--7 color -->
            <Arg> Vector3 </Arg>
            <!--8 duration -->
            <Arg> float </Arg>
            <!--9 thickness -->
            <Arg> float </Arg>
        </OnMsgDrawDebugCone>
		<OnMsgDrawDebugCapsule>
			<!--1 center -->
			<Arg> Vector3 </Arg>
			<!--2 halfHeight -->
			<Arg> float </Arg>
			<!--3 radius -->
			<Arg> float </Arg>
			<!--4 rotation -->
			<Arg> Vector3 </Arg>
			<!--5 color -->
			<Arg> Vector3 </Arg>
			<!--6 duration -->
			<Arg> float </Arg>
			<!--7 thickness -->
			<Arg> float </Arg>
		</OnMsgDrawDebugCapsule>
		<OnMsgDrawDebugFan>
			<!--1 center -->
			<Arg> Vector3 </Arg>
			<!--2 innerRadius -->
			<Arg> float </Arg>
			<!--3 outerRadius -->
			<Arg> float </Arg>
			<!--4 segments -->
			<Arg> int </Arg>
			<!--5 facing -->
			<Arg> float </Arg>
			<!--6 halfAngle -->
			<Arg> float </Arg>
			<!--7 color -->
			<Arg> Vector3 </Arg>
			<!--8 duration -->
			<Arg> float </Arg>
			<!--9 thickness -->
			<Arg> float </Arg>
		</OnMsgDrawDebugFan>
		<OnMsgDrawDebugFan3d>
			<!--1 center -->
			<Arg> Vector3 </Arg>
			<!--2 innerRadius -->
			<Arg> float </Arg>
			<!--3 outerRadius -->
			<Arg> float </Arg>
			<!--4 segments -->
			<Arg> int </Arg>
			<!--5 rotation -->
			<Arg> Vector3 </Arg>
			<!--6 halfAngle -->
			<Arg> float </Arg>
			<!--7 height -->
			<Arg> float </Arg>
			<!--8 color -->
			<Arg> Vector3 </Arg>
			<!--9 duration -->
			<Arg> float </Arg>
			<!--10 thickness -->
			<Arg> float </Arg>
		</OnMsgDrawDebugFan3d>

		<OnMsgDrawTrapOnActor><EnableNilArgs/>
			<!--1 trapID -->
			<Arg> int </Arg>
			<!--2 bDel 是否删除 -->
			<Arg> bool </Arg>
			<!-- trapInfo -->
			<Arg> DEBUG_TRAP_INFO </Arg>
		</OnMsgDrawTrapOnActor>

		<OnMsgDrawDebugString>
			<!--1 location -->
			<Arg> Vector3 </Arg>
			<!--2 text -->
			<Arg> string </Arg>
			<!--4 color -->
			<Arg> Vector3 </Arg>
			<!--5 duration -->
			<Arg> float </Arg>
		</OnMsgDrawDebugString>
		<OnMsgDeletePlaneConfAndGetApi>
		</OnMsgDeletePlaneConfAndGetApi>
		<OnMsgSyncDebugMsg> <EnableNilArgs/>
			<!--1 msgTitle -->
			<Arg> string </Arg>
			<!--2 msgContent -->
			<Arg> object </Arg>
			<!--3 bCompactPrint -->
			<Arg> bool </Arg>
		</OnMsgSyncDebugMsg>

		<RetGMSwitchLineAI>
            <Arg>int</Arg>
        </RetGMSwitchLineAI>
        <RetGMSwitchPlayerLogicAI>
            <Arg>string</Arg>
        </RetGMSwitchPlayerLogicAI>

        <RetAttachAINpcInfo>
            <Arg>DictStrStr</Arg>
			<Arg>string</Arg>
        </RetAttachAINpcInfo>

		<OnMsgSyncAttackTaskMsg>
            <Arg>int</Arg>
        </OnMsgSyncAttackTaskMsg>
		<OnReceiveReloadMapDebug>
			<!--1 ldName = "LV_Rose_School_P" -->
			<Arg>string</Arg>
			<!--2 addOrModifyInstConfStr =  "instConf;instConf" -->
			<Arg>string</Arg>
			<!--3 deleteIdListStr = "2952881097;2611031429" -->
			<Arg>string</Arg>
		</OnReceiveReloadMapDebug>
		

        <RetAIBTDebugData>
            <Arg>string</Arg>
            <Arg>string</Arg>
            <Arg>IntList</Arg>
			<Arg>DictStrStr</Arg>
        </RetAIBTDebugData>
        <OnReceiveServerTraceback>
			<Arg> string </Arg>
			<Arg> int </Arg>
		</OnReceiveServerTraceback>
		<RetPushDataCenter>
		</RetPushDataCenter>
		<RetReadDataCenter>
		</RetReadDataCenter>
		<RetAddFriendTest>
        </RetAddFriendTest>
		<RetQueryRelationsTest>
        </RetQueryRelationsTest>
		<RetQueryRelationsTestSuccess>
        </RetQueryRelationsTestSuccess>

        <RetGMCollectEntityInfo>
            <Arg> ENTITY_ID </Arg>
            <Arg> ENTITY_DEBUG_INFO </Arg>
        </RetGMCollectEntityInfo>

		<RetServerVoxel>
            <!-- VoxelInfo -->
            <Arg> ListFloat </Arg>
			<!-- Duration -->
            <Arg> float </Arg>
        </RetServerVoxel>
    </ClientMethods>
	<ServerMethods>
        <ReqGMSwitchLineAI> <Exposed/>
            <Arg>int</Arg>
        </ReqGMSwitchLineAI>
        <ReqGMSwitchPlayerLogicAI> <Exposed/>
            <Arg>string</Arg>
        </ReqGMSwitchPlayerLogicAI>

		<ReqPushDataCenter> <Exposed/>
			<Arg> ListStr </Arg>
			<Arg>int</Arg>
        </ReqPushDataCenter>

		<ReqReadDataCenter> <Exposed/>
			<Arg> ListStr </Arg>
			<Arg>int</Arg>
        </ReqReadDataCenter>

		<ReqAddFriendTest> <Exposed/>
			<Arg> ListStr </Arg>
        </ReqAddFriendTest>

		<ReqSwitchAbilityMonitor> <Exposed/>
			<Arg> bool </Arg>
        </ReqSwitchAbilityMonitor>

		<ReqQueryRelationsTest> <Exposed/>
        </ReqQueryRelationsTest>

		<onGMModifyAttractionRequest>
            <Arg> none </Arg>
            <Arg> UINT </Arg>
        </onGMModifyAttractionRequest>
		
        <ReqGMCollecEntityInfo> <Exposed/>
            <Arg> ENTITY_ID </Arg>
        </ReqGMCollecEntityInfo>

		<ReqServerVoxel> <Exposed/>
            <!-- X -->
            <Arg> float </Arg>
            <!-- Y -->
            <Arg> float </Arg>
            <!-- Range -->
            <Arg> float </Arg>
			<!-- Duration -->
            <Arg> float </Arg>
        </ReqServerVoxel>
	</ServerMethods>
</root>
