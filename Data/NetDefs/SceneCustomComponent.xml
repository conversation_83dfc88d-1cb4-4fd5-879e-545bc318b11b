<?xml version="1.0" ?>
<root>
	<Properties>
        <playerSceneCustomSlot Type="int" Flags="OWN_CLIENT" Persistent="true"/>
        <playerSceneCustom Type="SCENE_CUSTOM_STRATEGY" Flags="OWN_CLIENT" Persistent="true"/>
        <sceneCustomStrategyDict Type="SCENE_CUSTOM_STRATEGY_DICT" Flags="SERVER_ONLY" Persistent="true"/>
        <sceneCustomItemWardrobe Type="SCENE_CUSTOM_ITEM_WARDROBE" Flags="SERVER_ONLY" Persistent="true"/>
	</Properties>
	<ClientMethods>
        <RetGetAllSceneCustomStrategyBriefInfo>
            <Arg> SCENE_CUSTOM_STRATEGY_BRIEF_INFO_DICT </Arg>
        </RetGetAllSceneCustomStrategyBriefInfo>

        <RetGetOwnedSceneItem> 
            <Arg> SCENE_CUSTOM_ITEM_WARDROBE </Arg>
        </RetGetOwnedSceneItem>

        <RetChangePlayerSceneCustomStrategyName>
            <Arg> string </Arg>
        </RetChangePlayerSceneCustomStrategyName>

        <RetChangePlayerSceneCustomStrategyPicture> 
            <Arg> string </Arg>
        </RetChangePlayerSceneCustomStrategyPicture>
	</ClientMethods>
	<ServerMethods>
        <ReqGetAllSceneCustomStrategyBriefInfo CD="1"> <Exposed/> 
        </ReqGetAllSceneCustomStrategyBriefInfo>

        <ReqGetOwnedSceneItem CD="1"> <Exposed/> 
        </ReqGetOwnedSceneItem>

        <ReqSetPlayerSceneCustom CD="1"> <Exposed/> 
            <Arg> int </Arg>
        </ReqSetPlayerSceneCustom>

        <ReqSavePlayerSceneCustomStrategy CD="1"> <Exposed/> 
            <Arg> SCENE_CUSTOM_STRATEGY </Arg>
        </ReqSavePlayerSceneCustomStrategy>

        <ReqChangePlayerSceneCustomStrategyName CD="1"> <Exposed/> 
            <Arg> string </Arg>
        </ReqChangePlayerSceneCustomStrategyName>

        <ReqChangePlayerSceneCustomStrategyPicture CD="1"> <Exposed/> 
            <Arg> string </Arg>
        </ReqChangePlayerSceneCustomStrategyPicture>
	</ServerMethods>
</root>