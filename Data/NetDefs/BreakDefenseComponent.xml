<root>
    <Properties>
        <MaxSanValue Type="int" Flags="ALL_CLIENTS" Default="0"/> <!--最大破防值-->
        <CurSanValue Type="int" Flags="ALL_CLIENTS" Default="0"/> <!--当前破防值-->
        <bSanLocked Type="bool" Flags="ALL_CLIENTS" Default="false"/> <!--破防条是否被锁定-->
        <SanFullTime Type="int" Flags="ALL_CLIENTS" Default="0"/> <!--破防之后，破防条复原结束的时间戳-->
        <SanStartTime Type="int" Flags="ALL_CLIENTS" Default="0"/> <!--破防之后，破防值开始回复的时间戳-->
    </Properties>

    <ClientMethods>
    </ClientMethods>

    <ServerMethods> 
    </ServerMethods>
</root>