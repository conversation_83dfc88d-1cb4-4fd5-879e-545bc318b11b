<root>
    <Properties>

    </Properties>

    <ClientMethods>

        <!-- 玩家登陆时同步当前活动信息 -->
        <onGeActivityInfo>
            <Arg> ACTIVITY_STATUS_INFO_DICT </Arg>   <!-- 所有活动的id，状态，及当前状态结束时间 -->
        </onGeActivityInfo>

        <!-- 活动状态变化 -->
        <onChangeActivityStatus>
            <Arg> int </Arg>    <!-- activityId -->
            <Arg> int </Arg>    <!-- 活动状态 2：预告 3：开启 4：关闭 -->
            <Arg> int </Arg>    <!-- 当前状态结束的时间戳 -->
            <Arg> int </Arg>    <!-- 状态开启时间戳 -->
        </onChangeActivityStatus>

        <!-- 玩家登陆时同步当前答题信息 -->
        <onGetWorldChannelQuizInfo>
            <Arg> int </Arg>   <!-- quizId -->
            <Arg> int </Arg>   <!-- 活动当前状态 Enum.EACTIVITY_STATUS 2：预告中 3：开启中 其他状态可忽略-->
            <Arg> int </Arg>    <!-- 当前状态结束的时间戳 -->
        </onGetWorldChannelQuizInfo>

        <!-- 世界频道答题状态变化 -->
        <onChangeWorldChannelQuizStatus>
            <Arg> int </Arg>   <!-- quizId -->
            <Arg> int </Arg>   <!-- 活动状态 2：预告 3：开启 4：关闭 -->
            <Arg> int </Arg>    <!-- 当前状态结束的时间戳 -->
        </onChangeWorldChannelQuizStatus>

        <!-- 同步是否有人答对了题目 -->
        <syncIsQuizSolved>
            <Arg> bool </Arg>
        </syncIsQuizSolved>

    </ClientMethods>

    <ServerMethods>
    
        <SSReqSendTopQuizReward>
        </SSReqSendTopQuizReward>

        <SSReqSendGeneralQuizReward>
            <Arg> int </Arg>
        </SSReqSendGeneralQuizReward>

    </ServerMethods>
</root>