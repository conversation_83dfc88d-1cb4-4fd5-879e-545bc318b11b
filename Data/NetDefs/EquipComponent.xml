<root>
    <Properties>
        <equipmentSlotInfo Type="INV_SLOT_INFO" Persistent="true" />                                     <!-- 身上装备槽 -->
        <equipmentBodyInfo Type="EQUIP_BODY_INFO" Persistent="true" />                                   <!-- 新部位信息(部位强化、高级属性、套装) -->
        <equipmentBatch Type="int" Default="1" Flags="OWN_CLIENT" Persistent="true" />                   <!-- 目前和赛年一致 -->
        <curEquipPlanId Type="int" Default="1" Flags="OWN_CLIENT" Persistent="true" />
        <equipPlans Type="EQUIP_PLANS" Default="{}" Flags="OWN_CLIENT" Persistent="true" />
        <equipGbId2PlanIds Type="dict" Key="string" Value="DictIntInt" Default="{}" />
		<randomClassLockInfo Type="dict" Key="int" Value="RANDOM_CLASS_LOCK_INFO" Flags="OWN_CLIENT"  /> <!-- 随机词条库的职业锁定信息 todo: 客户端修改后删除 -->
        <seasonUnlockSlots Type="dict" Key="int" Value="bool" Flags="OWN_CLIENT" /> <!-- 赛季解锁的槽位 -->
        <equipSlotsSeasonID Type="int" Default="0" Flags="OWN_CLIENT" /> <!-- 当前赛季ID -->
        <equipSuitActiveInfo Type="dict" Key="int" Value="int" Flags="OWN_CLIENT"  /> <!-- 部位套装激活信息(key是套装id, value是激活到第几层) -->
        <equipSuitMark Type="int" Default="0" /> <!-- 部位套装战力 -->
        <equipUniqueActiveInfo Type="dict" Key="int" Value="KEY_BOOL" Flags="OWN_CLIENT" /> <!-- 独珍装备激活信息(key是独珍id, value已经激活的trigger) -->
        <equipFightPropValueBySource Type="EQUIP_FIGHT_PROP_VALUE_BY_SOURCE" Flags="SERVER_ONLY" /> <!-- 装备战斗属性值(按来源分组) -->
        <equipPassiveSkillBySource Type="EQUIP_PASSIVE_SKILL_BY_SOURCE" Flags="SERVER_ONLY" /> <!-- 装备被动技能(按来源分组) -->
    </Properties>

    <ClientMethods>
        <!-- 新装备 -->

        <!-- 装备栏变化 -->
        <onMsgEquipmentbarChange>
            <!-- 格子中物品数量变化 -->
            <Arg>INV_SLOT_COUNT_MAP</Arg>
            <!-- 格子中物品内容变化 -->
            <Arg>INV_SLOT_VAL_MAP</Arg>
            <!-- 词条有变化的gbid -->
            <Arg> ListStr </Arg>
        </onMsgEquipmentbarChange>

        <onEquipmentExchangeSucc>
            <Arg> UINT </Arg>       <!-- 背包的ID -->
            <Arg> SLOT_INDEX </Arg> <!-- 背包的index -->
            <Arg> SLOT_INDEX </Arg> <!-- 装备栏的index -->
        </onEquipmentExchangeSucc>

        <onEquipSnapInfo>
            <Arg> INV_SLOT_VAL </Arg>
        </onEquipSnapInfo>

        <!-- 装备同步 -->
        <OnMsgEquipmentSlotInfoSync>
            <!-- 装备栏信息 -->
            <Arg>INV_SLOT_INFO</Arg>
        </OnMsgEquipmentSlotInfoSync>

        <!-- 装备信息变更同步 增量 -->
        <OnMsgEquipChange>
            <!-- channel -->
            <Arg> UINT </Arg>
            <!-- 道具的slot -->
            <Arg> UINT </Arg>
            <!-- 道具的uid -->
            <Arg> UUID </Arg>
            <!-- 装备信息 -->
            <Arg> INV_SLOT_VAL </Arg>
        </OnMsgEquipChange>

        <!-- 部位信息同步 全量 -->
        <OnMsgEquipmentBodyInfoSync>
            <!-- 装备栏信息 -->
            <Arg>EQUIP_BODY_INFO</Arg>
        </OnMsgEquipmentBodyInfoSync>

        <!-- 部位套裝激活 增量 -->
        <OnMsgEquipmentBodySuitActive>
            <!-- type -->
            <Arg> UINT </Arg>
            <!-- EQUIPMENT_BODY_SUIT_SLOT_INFO -->
            <Arg> EQUIPMENT_BODY_SUIT_SLOT_INFO </Arg>
        </OnMsgEquipmentBodySuitActive>

        <!-- 部位强化激活 -->
        <OnMsgEquipmentBodyEnhanceActivate>
            <!-- slot -->
            <Arg> UINT </Arg>
            <!-- stage -->
            <Arg> UINT </Arg>
            <!-- EQUIPMENT_BODY_ENHANCE_SLOT_INFO -->
            <Arg> EQUIPMENT_BODY_ENHANCE_SLOT_INFO </Arg>
        </OnMsgEquipmentBodyEnhanceActivate>

        <!-- 部位强化精炼 -->
        <OnMsgEquipmentBodyEnhanceRefine>
            <!-- multi -->
            <Arg> float </Arg>
            <!-- slot -->
            <Arg> UINT </Arg>
            <!-- stage -->
            <Arg> UINT </Arg>
            <!-- EQUIPMENT_BODY_ENHANCE_SLOT_INFO -->
            <Arg> EQUIPMENT_BODY_ENHANCE_SLOT_INFO </Arg>
        </OnMsgEquipmentBodyEnhanceRefine>

        <!-- 装备强化精炼重置 -->
        <OnMsgEquipmentEnhanceInfoReset>
        </OnMsgEquipmentEnhanceInfoReset>

        <!-- 随机词条库同步 增量 -->
        <OnMsgRandomGroupStockSlotsInfoChange>
            <!-- slot -->
            <Arg> UINT </Arg>
            <!-- adjustTotalUpCount -->
            <Arg> int </Arg> 
            <!-- adjustTotalDownCount -->
            <Arg> UINT </Arg>
            <!-- randomGroupId -->
            <Arg> UINT </Arg>            
            <!-- randomGroupInfo -->
            <Arg> EQUIP_BODY_RANDOM_GROUP_INFO </Arg>
        </OnMsgRandomGroupStockSlotsInfoChange>

        <!-- 随机词条库 单词条重置 -->
        <OnMsgEquipRandomGroupReset>
            <!-- slot -->
            <Arg> UINT </Arg>
            <!-- randomGroupId -->
            <Arg> UINT </Arg> 
        </OnMsgEquipRandomGroupReset>

        <!-- 随机词条库 槽位重置 -->
        <OnMsgEquipRandomSlotReset>
            <!-- slot -->
            <Arg> UINT </Arg>
        </OnMsgEquipRandomSlotReset>

        <!-- 切装备方案 -->
        <RetSwitchEquipPlan>
            <!-- Result -->
            <Arg> Result </Arg>
            <!-- 方案ID -->
            <Arg> int </Arg>
        </RetSwitchEquipPlan>

        <RetChangeEquipPlanName>
            <!-- Result -->
             <Arg> Result </Arg>
             <!-- 方案ID -->
              <Arg> int </Arg>
        </RetChangeEquipPlanName>

        <!-- 装备穿戴 -->
        <RetEquipPutOn>
            <!-- Result -->
            <Arg> Result </Arg>
            <!-- 背包的ID -->
            <Arg> UINT </Arg>
            <!-- 物品在背包的index -->
            <Arg> SLOT_INDEX </Arg>
            <!-- 物品的uid -->
            <Arg> UUID </Arg>
            <!-- 穿戴部位的slot -->
            <Arg> UINT </Arg>
            <!-- 穿脱类型 -->
            <Arg> UINT </Arg>
        </RetEquipPutOn>

        <!-- 装备脱卸 -->
        <RetEquipPutOff>
            <!-- Result -->
            <Arg> Result </Arg>
            <!-- 装备类型 -->
            <Arg>SLOT_INDEX</Arg>
            <!-- 物品的uid -->
            <Arg>UUID</Arg>
        </RetEquipPutOff>


        <!-- 装备强化，装备部位激活 -->
        <RetEquipBodyEnhanceActivate>
            <!-- Result -->
            <Arg> Result </Arg>
            <!-- 装备的slot -->
            <Arg> UINT </Arg>
            <!-- 装备的uid -->
            <Arg> UUID </Arg>
            <!-- 部位的stage -->
            <Arg> UINT </Arg>
        </RetEquipBodyEnhanceActivate>

        <!-- 装备强化，装备部位激活 -->
        <RetEquipBodyEnhanceRefine>
            <!-- Result -->
            <Arg> Result </Arg>
            <!-- 装备的slot -->
            <Arg> UINT </Arg>
            <!-- 装备的uid -->
            <Arg> UUID </Arg>
            <!-- 部位的stage -->
            <Arg> UINT </Arg>
            <!-- 强化道具id -->
            <Arg> UINT </Arg>
        </RetEquipBodyEnhanceRefine>

        <!-- 装备重塑 -->
        <RetEquipmentReform>
            <Arg> Result </Arg> <!-- Result -->
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> EQUIPMENT_RANDOM_PROP_INFO </Arg> <!-- 重塑后的随机属性信息 -->
        </RetEquipmentReform>

        <!-- 获取最佳重塑方案列表 -->
        <RetGetBestReformList>
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> EQUIPMENT_RANDOM_PROP_INFO_LIST </Arg> <!-- 最佳重塑方案列表 -->
        </RetGetBestReformList>

        <!-- 装备重塑保存当前方案 -->
        <RetSaveCurrentEquipmentReform>
            <Arg> Result </Arg> <!-- Result -->
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 方案index -->
            <Arg> EQUIPMENT_RANDOM_PROP_INFO_LIST </Arg> <!-- 重塑后的随机属性信息 -->
        </RetSaveCurrentEquipmentReform>

        <!-- 装备重塑应用最佳方案 -->
        <RetEquipmentApplyBestReform>
            <Arg> Result </Arg> <!-- Result -->
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 方案index -->
            <Arg> EQUIPMENT_RANDOM_PROP_INFO </Arg> <!-- 重塑后的随机属性信息 -->
        </RetEquipmentApplyBestReform>

        <!-- 查看记忆空间 -->
        <RetGetReformMemerySpace>
            <Arg> EQUIPMENT_REFORM_MEMERY_SPACE_BY_SLOT </Arg>  <!-- 装备的记忆空间信息 -->
        </RetGetReformMemerySpace>

        <!-- 置换记忆空间中的随机属性 -->
        <RetReplaceOneRandomPropFromMemerySpace>
            <Arg> Result </Arg> <!-- Result -->
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 属性类型 -->
            <Arg> string </Arg> <!-- 记忆空间中的key -->
            <Arg> int </Arg>    <!-- 随机属性index -->
            <Arg> int</Arg>     <!-- 新的随机属性id -->
        </RetReplaceOneRandomPropFromMemerySpace>

        <!-- 一键置换记忆空间中的随机属性 -->
        <RetAutoReplaceRandomPropFromMemerySpace>
            <Arg> Result </Arg> <!-- Result -->
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> EQUIPMENT_RANDOM_PROP_INFO </Arg> <!-- 置换后的随机属性信息 -->
        </RetAutoReplaceRandomPropFromMemerySpace>
    </ClientMethods>

    <ServerMethods>
        <!-- 新装备 -->
        
        <!-- 切装备方案 -->
        <ReqSwitchEquipPlan CD="1"> <Exposed />
            <!-- 方案ID -->
            <Arg> UINT </Arg>
            <!-- invID -->
            <Arg> UINT</Arg>
            <!-- 装备方案中需要新穿的装备 key 装备槽位 value 背包中的index,物品id -->
            <Arg> EQUIP_PLAN_INV_EQUIPS </Arg>
            <!-- 已经在身上 但槽位需要变动的装备 -->
            <Arg> DictIntInt </Arg>
            <!-- 已经在身上不需要变动的装备 key 装备槽位 value 物品id -->
            <Arg> DictIntStr </Arg>
        </ReqSwitchEquipPlan>

        <ReqChangeEquipPlanName> <Exposed />
            <!-- 方案ID -->
            <Arg> UINT </Arg>
            <!-- name -->
             <Arg> string </Arg>
        </ReqChangeEquipPlanName>

        <!-- 装备穿戴 -->
        <ReqEquipPutOn CD="0.01"> <Exposed />
            <!-- 背包的ID -->
            <Arg>UINT</Arg>
            <!-- 物品在背包的index -->
            <Arg>SLOT_INDEX</Arg>
            <!-- 物品的uid -->
            <Arg>UUID</Arg>
            <!-- 穿戴部位的slot -->
            <Arg> UINT </Arg>
            <!-- 穿脱类型 -->
            <Arg>UINT</Arg>
        </ReqEquipPutOn>

        <!-- 装备脱卸 -->
        <ReqEquipPutOff CD="0.17"> <Exposed />
            <!-- 装备类型 -->
            <Arg>SLOT_INDEX</Arg>
            <!-- 物品的uid -->
            <Arg>UUID</Arg>
        </ReqEquipPutOff>

        <!-- 装备强化 -->
        <ReqEquipBodyEnhanceRefine CD="0.1"><Exposed />
            <!-- 装备的slot -->
            <Arg> UINT </Arg>
            <!-- 装备的uid -->
            <Arg> UUID </Arg>
            <!-- 部位的stage -->
            <Arg> UINT </Arg>
            <!-- 强化道具id -->
            <Arg> UINT </Arg>
        </ReqEquipBodyEnhanceRefine>

        <!-- 装备重塑 -->
        <ReqEquipmentReform CD="0.1"><Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
        </ReqEquipmentReform>

        <ReqGetBestReformList CD="0.3"><Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
        </ReqGetBestReformList>

        <ReqSaveCurrentEquipmentReform CD="0.1"><Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 方案index -->
        </ReqSaveCurrentEquipmentReform>

        <!-- 装备重塑应用最佳方案 -->
        <ReqEquipmentApplyBestReform CD="0.1"><Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 方案index -->
        </ReqEquipmentApplyBestReform>

        <!-- 查看记忆空间 -->
        <ReqGetReformMemerySpace CD="1">   <Exposed />
        </ReqGetReformMemerySpace>

        <!-- 置换记忆空间中的随机属性 -->
        <ReqReplaceOneRandomPropFromMemerySpace>  <Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
            <Arg> UINT </Arg>   <!-- 属性类型 -->
            <Arg> string </Arg> <!-- 记忆空间中的key -->
            <Arg> int </Arg>    <!-- 随机属性index -->
            <Arg> int</Arg>     <!-- 随机属性id -->
        </ReqReplaceOneRandomPropFromMemerySpace>

        <!-- 一键置换记忆空间中的随机属性 -->
        <ReqAutoReplaceRandomPropFromMemerySpace>  <Exposed />
            <Arg> UINT </Arg>   <!-- 装备的slot -->
            <Arg> UUID </Arg>   <!-- 装备的uid -->
        </ReqAutoReplaceRandomPropFromMemerySpace>
    </ServerMethods>
</root>
