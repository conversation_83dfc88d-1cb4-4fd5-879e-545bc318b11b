<?xml version="1.0" ?>
<root>
    <Properties>
		<FirstPayInfos Type="DictIntInt" Flags="OWN_CLIENT" Persistent="true" /><!-- 记录已经获得首冲奖励的货物ID-->
		<isSdkLogin Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="false"/>
		<channelId Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
        <packageChannel Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>                
        <deviceId Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
		<appVersion Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
		<os Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
		<ip Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>		
		<addMoneyStatis Type="DictIntFloat" Flags="SERVER_ONLY" Persistent = "false"/>
		<subMoneyStatis Type="DictIntFloat" Flags="SERVER_ONLY" Persistent = "false"/>
		<loginTotalExp Type="UINT" Flags="SERVER_ONLY" Persistent = "false"/>			
	</Properties>

	<Implements/>
	<ClientMethods>
		<RetPaySign>
			<Arg> Result </Arg><!--1 result -->
			<Arg> string </Arg><!--2 Sign -->
			<Arg> string </Arg><!--4 PayNotifyUrl -->
			<Arg> string </Arg><!--2 ThirdPartyTradeNo -->
		</RetPaySign>

		<RetAddProduct> <Exposed/>
			<Arg> Result </Arg><!--1 result -->
			<Arg> int </Arg><!--1 ProductID -->
			<Arg> bool </Arg><!--1 bFirst -->
		</RetAddProduct>
	</ClientMethods>
	<ServerMethods>
		<ReqPaySign> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> string </Arg><!--2 ProductID -->
			<Arg> int </Arg><!--3 ServerID -->
		</ReqPaySign>

		<ReqAddProduct> <Exposed/>
			<Arg> int </Arg><!--1 ConfigID -->
			<Arg> string </Arg><!--1 ProductID -->
		</ReqAddProduct>
	</ServerMethods>
</root>