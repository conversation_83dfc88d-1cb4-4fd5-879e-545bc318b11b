<?xml version="1.0" ?>
<root>
    <Properties>
        <championTroopID Type="CHAMPION_TROOP_ID" Flags="OWN_CLIENT" Persistent="true"/>
        <championTroopInfo Type="CHAMPION_TROOP_BRIEF_INFO" Flags="SERVER_ONLY"/>
    </Properties>

    <ClientMethods>
        <OnMsgCreateChampionTroop>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </OnMsgCreateChampionTroop>

        <OnMsgJoinChampionTroop>
            <Arg> CHAMPION_TROOP_BRIEF_INFO </Arg>
        </OnMsgJoinChampionTroop>

        <OnMsgQuitChampionTroop>
            <Arg> CHAMPION_TROOP_ID </Arg>
        </OnMsgQuitChampionTroop>
        
        <OnMsgDisbandChampionTroop>
            <Arg> CHAMPION_TROOP_ID </Arg>
        </OnMsgDisbandChampionTroop>

        <RetChampionPrepareArenaInfo>
            <Arg> UINT </Arg> <!--赛区id-->
            <Arg> UINT </Arg> <!--大会阶段-->
            <Arg> CHAMPION_GROUP_BATTLE_SCHEDULE_INFO </Arg> <!--小组赛阶段信息-->
            <Arg> CHAMPION_GROUP_BATTLE_SCHEDULE_INFO </Arg> <!--淘汰赛阶段信息-->
        </RetChampionPrepareArenaInfo>
        
        <OnMsgGetChampionTroopDetail>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </OnMsgGetChampionTroopDetail>
        
        <OnMsgGetChampionTroopBrief>
            <Arg> CHAMPION_TROOP_BRIEF_INFO </Arg>
        </OnMsgGetChampionTroopBrief>
        
        <RetGetChampionGroupBattleMatches>
            <Arg> UINT </Arg> <!--赛区ID-->
            <Arg> CHAMPION_GROUP_BATTLE_MULTI_SHOW_LIST </Arg> <!--对应赛区的所有对局 roundIndex -> matches -->
        </RetGetChampionGroupBattleMatches>
        
        <RetGetChampionGroupBattlePersonalMatches>
            <Arg> UINT </Arg> <!--赛区ID-->
            <Arg> CHAMPION_GROUP_BATTLE_MATCH_INFO_MAP </Arg> <!--对应赛区的所有对局 roundIndex -> match -->
        </RetGetChampionGroupBattlePersonalMatches>
        
        <RetModifyChampionTroopName>
            <Arg> string </Arg>
        </RetModifyChampionTroopName>
        
        <RetChampionChangeTroopLeader>
            <Arg> CHAMPION_ROLE_ID </Arg>
        </RetChampionChangeTroopLeader>
        
        <RetSignUpChampionTroop>
        </RetSignUpChampionTroop>
        
        <RetGetChampionGroupBattleRanklist>
            <Arg> UINT </Arg> <!--赛区ID-->
            <Arg> CHAMPION_GROUP_BATTLE_RANKLIST </Arg>  <!--排行榜-->
            <Arg> CHAMPION_GROUP_BATTLE_RANK_ITEM </Arg> <!--所在战队的信息（如果不在对应赛区则为默认值）-->
        </RetGetChampionGroupBattleRanklist>
        
        <RetGetEliminationBracket>
            <Arg> UINT </Arg>  <!--赛区ID-->
            <Arg> ROUND_BRACKET_RESULT_DICT </Arg>   <!--上几轮的赛程结果-->
            <Arg> BRACKET_ID_2_INFO </Arg> <!--本轮的赛程进度-->
        </RetGetEliminationBracket>
        
        <RetChampionDistribubeRegionFinish> <!--积分赛即将开始弹窗-->
        </RetChampionDistribubeRegionFinish>
        
        <RetOnChampionTroopUpdated>  <!--刷新信息-->
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </RetOnChampionTroopUpdated>
        
        <OnMsgChampionBeinviteJoinTroop>  <!--战队邀请弹窗-->
            <Arg> CHAMPION_ROLE_ID </Arg>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> ROLE_BRIEF </Arg>
        </OnMsgChampionBeinviteJoinTroop>
        
        <RetChampionInviteJoinTroop> <!--战队邀请操作回调-->
            <Arg> CHAMPION_ROLE_ID </Arg>
        </RetChampionInviteJoinTroop>
        
        <RetChampionTroopMemberInfo>
            <Arg> CHAMPION_MULTI_TROOP_MEMBER_MAP </Arg>
        </RetChampionTroopMemberInfo>
        
        <RetChampionGetProgress>
            <Arg> UINT </Arg> <!--目前阶段,  championConsts.CHAMPION_ACTIVITY_STAGE-->
            <Arg> UINT </Arg> <!--战区ID-->
            <Arg> CHAMPION_REGION_BATTLE_INFO </Arg>
            <Arg> BOOL </Arg> <!-- 是否自己的战区 -->
        </RetChampionGetProgress>
        
        <RetKickChampionTroopMember> <!--踢人回调-->
            <Arg> CHAMPION_ROLE_ID </Arg>
        </RetKickChampionTroopMember>
        
        <RetSupportChampionTroop>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> UINT </Arg>
            <Arg> UINT </Arg>
        </RetSupportChampionTroop>
        
        <OnMsgChampionChangeTroopLeader>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> CHAMPION_ROLE_ID </Arg>
        </OnMsgChampionChangeTroopLeader>
        
        <RetGetChampionAvaliableRegions>
          <Arg> UINT </Arg> <!--赛区数量-->
        </RetGetChampionAvaliableRegions>
        
        <OnMsgChampionAdvanceResult> <!--小组赛晋级结果-->
            <Arg> UINT </Arg>
            <Arg> CHAMPION_TROOP_ID_LIST </Arg>
        </OnMsgChampionAdvanceResult>
        
        <RetGetEliminationSupportInfo>
            <Arg> UINT </Arg> <!--赛区ID-->
            <Arg> CHAMPION_ELIMINATION_SUPPORT_RANKLIST </Arg>  <!--排行榜-->
            <Arg> UINT </Arg>  <!--剩余票数-->
            <Arg> CHAMPION_TROOP_ID_LIST </Arg>  <!--已助力战队-->
        </RetGetEliminationSupportInfo>
    </ClientMethods>

    <ServerMethods>
        <ReqCreateChampionTroop> <!--创建战队-->
            <Exposed />
            <Arg> BOOL </Arg> <!--是否客户端自动创建-->
            <Arg> string </Arg> <!--队伍名称-->
        </ReqCreateChampionTroop>

        <ReqJoinChampionTroop> <!--加入战队-->
            <Exposed />
            <Arg> CHAMPION_TROOP_ID </Arg>
        </ReqJoinChampionTroop>

        <ReqQuitChampionTroop> <!--退出战队-->
            <Exposed />
        </ReqQuitChampionTroop>

        <ReqDisbandChampionTroop> <!--解散战队-->
            <Exposed />
        </ReqDisbandChampionTroop>

        <ReqSignUpChampion> <!--报名战队-->
            <Exposed />
        </ReqSignUpChampion>

        <ReqGetChampionTroopDetail> <!--获取战队信息-->
            <Exposed />
        </ReqGetChampionTroopDetail>

        <ReqGetChampionTroopBrief> <!--获取简单战队信息-->
            <Exposed />
        </ReqGetChampionTroopBrief>
        
        <ReqChampionPrepareArenaInfo> <!--获取准备场信息-->
            <Exposed />
        </ReqChampionPrepareArenaInfo>
        
        <ReqChampionGroupBattleMatches> <!--获取小组赛对局信息-->
            <Exposed />
            <Arg> UINT </Arg> <!--赛区ID(填0为所在战队赛区，无战队则默认1赛区，填合法赛区ID则为指定赛区数据)-->
            <Arg>UINT_MAP</Arg> <!-- roundIndex -> startIndex -->
        </ReqChampionGroupBattleMatches>

        <ReqChampionGroupBattlePersonalMatches> <!--获取小组赛个人战队信息（无战队不会返回）-->
            <Exposed />
            <Arg> CHAMPION_TROOP_ID </Arg>
        </ReqChampionGroupBattlePersonalMatches>
        
        <ReqChampionModifyTroopName> <!--战队改名-->
            <Exposed />
            <Arg> string </Arg>
        </ReqChampionModifyTroopName>
        
        <ReqChampionChangeTroopLeader> <!--转让队长-->
            <Exposed />
            <Arg> CHAMPION_ROLE_ID </Arg>
        </ReqChampionChangeTroopLeader>
        
        <ReqChampionGroupBattleRanklist> <!--小组赛排行榜-->
            <Exposed />
            <Arg> UINT </Arg> <!--赛区ID (为0时自动寻找战队对应的赛区)-->
        </ReqChampionGroupBattleRanklist>

        <ReqChampionGetEliminationBracket>  <!--淘汰赛赛程-->
            <Exposed />
            <Arg> UINT </Arg>
        </ReqChampionGetEliminationBracket>

        <ReqChampionInviteJoinTroop>  <!--邀请加入战队-->
            <Exposed />
            <Arg> CHAMPION_ROLE_ID </Arg>
        </ReqChampionInviteJoinTroop>
        
        <ReqChampionGetOtherTroopMemberInfo>
            <Exposed />
            <Arg> CHAMPION_TROOP_ID_LIST </Arg>
        </ReqChampionGetOtherTroopMemberInfo>
        
        <ReqChampionGetProgress>   <!--获取当前进度-->
            <Exposed />
            <Arg> UINT </Arg>
        </ReqChampionGetProgress>

        <ReqQuitToChampionPrepareWorld>
            <Exposed />
        </ReqQuitToChampionPrepareWorld>

        <ReqKickChampionTroopMember>
            <Exposed />
            <Arg> CHAMPION_ROLE_ID </Arg>
        </ReqKickChampionTroopMember>
        
        <ReqGetEliminationSupportInfo>
            <Exposed />
            <Arg> UINT </Arg> <!--赛区id-->
        </ReqGetEliminationSupportInfo>
        
        <ReqSupportChampionTroop>
          <Exposed />
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> UINT </Arg> <!--投票数量-->
        </ReqSupportChampionTroop>
        
        <ReqGetChampionAvaliableRegions>
          <Exposed />
        </ReqGetChampionAvaliableRegions>
        
        <SSRetChampionTroopPlayerLogin>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </SSRetChampionTroopPlayerLogin>

        <SSRetCreateTroopID>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> string </Arg>
        </SSRetCreateTroopID>

        <SSRetChampionTroopCreate>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </SSRetChampionTroopCreate>

        <SSRetJoinChampionTroop>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </SSRetJoinChampionTroop>
        
        <SSRetQuitChampionTroop>
            <Arg> CHAMPION_TROOP_ID </Arg>
        </SSRetQuitChampionTroop>
        
        <SSOnMsgDisbandChampionTroop>
            <Arg> CHAMPION_TROOP_ID </Arg>
        </SSOnMsgDisbandChampionTroop>
        
        <SSRetEnterChampionPrepareArena>
        </SSRetEnterChampionPrepareArena>
        
        <SSRetSignUpChampionTroop>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </SSRetSignUpChampionTroop>

        <SSOnMsgUpdateChampionTroop>
            <Arg> CHAMPION_TROOP_INFO </Arg>
        </SSOnMsgUpdateChampionTroop>
        
        <SSOnMsgModifyChampionTroopName>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> string </Arg>
        </SSOnMsgModifyChampionTroopName>
        
        <SSOnMsgChampionChangeTroopLeader>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> CHAMPION_ROLE_ID </Arg>
        </SSOnMsgChampionChangeTroopLeader>
        
        <SSOnMsgBeinviteJoinTroop>
            <Arg> CHAMPION_ROLE_ID </Arg>
            <Arg> CHAMPION_TROOP_ID </Arg>
            <Arg> ROLE_BRIEF </Arg>
        </SSOnMsgBeinviteJoinTroop>
        
        <SSOnMsgChampionTroopDistributeRegion>
          <Arg> UINT </Arg>
        </SSOnMsgChampionTroopDistributeRegion>
    </ServerMethods>
</root>
