<?xml version="1.0" ?>
<root>
	<Properties>
        <!-- 任务位面存档数据缓存 存储时会先存到这里，独立写库成功后清除 -->
		<questPlaneArchive Type="QUEST_TOTAL_PLANE_ARCHIVE" Flags="SERVER_ONLY" Persistent="true"/>
		<questPlaneArchiveBackup Type="QUEST_TOTAL_PLANE_ARCHIVE" Flags="SERVER_ONLY" Persistent="true"/>
		<questPlanePosition Type="dict" Key="int" Value="ListFloat" Flags="SERVER_ONLY" Persistent="true" />
		<questPlaneYaw Type="dict" Key="int" Value="float" Flags="SERVER_ONLY" Persistent="true" />
		<!-- 任务位面存档标记 -->
		<questPlaneArchiveFlags Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true"/>
	</Properties>
</root>
