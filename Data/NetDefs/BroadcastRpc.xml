<?xml version="1.0" ?>
<root>
	<Implements>
	</Implements>

	<ClientMethods>
		<onReceiveGuildNightInvitation>  <!--公会晚会邀请-->
		</onReceiveGuildNightInvitation>
		<OnMsgUpdateWorldBidItemInfo>	<!--世界拍卖用-->
			<Arg> BID_ITEM_SIMPLE_INFO </Arg>
		</OnMsgUpdateWorldBidItemInfo>

		<OnMsgWorldBidInfoNotice>	<!--世界拍卖用-->
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
		</OnMsgWorldBidInfoNotice>

		<OnMsgGenReminder>	<!--世界boss活动用-->
			<Arg> int </Arg>
            <Arg> VList </Arg>
		</OnMsgGenReminder>

		<OnMsgChannelChat>
			<Arg> string </Arg>
			<Arg> CHAT_INFO </Arg>
		</OnMsgChannelChat>

		<!--  匿名聊天频道揭露信息新增或者更新 -->
        <OnMsgChatExposeUpdate>
            <Arg> CHAT_EXPOSE_INFO </Arg>
			<!-- userNameID, 为0的情况表示实名揭露, 使用下面的userRealName -->
            <Arg> int </Arg>
            <!-- userRealName -->
            <Arg> string </Arg>
        </OnMsgChatExposeUpdate>

		<!-- 匿名聊天频道有用户进入神隐状态 -->
		<OnMsgChatUserStartShield>
			<!-- userID -->
			<Arg> string </Arg>
			<!-- expire time: unixtimestamp second -->
			<Arg> LONGTIME </Arg>
		</OnMsgChatUserStartShield>

		<!-- 通知客户端清除本地下载好的res_id对应的图片 -->
        <OnClearPictureCache>
            <Arg> string </Arg>  <!-- res_id（资源id） -->
        </OnClearPictureCache>

		<!--服务器数据版本不一致提示-->
		<OnMsgDatabaseVersionWarning>
			<!--1 code version -->
			<Arg> string </Arg>
			<!--2 db version -->
			<Arg> string </Arg>
		</OnMsgDatabaseVersionWarning>

	</ClientMethods>

    <ServerMethods>
		<BroadcastRpcWithCallback>
			<Arg> mailbox </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
		</BroadcastRpcWithCallback>

		<BroadcastAvatarActorRPC>
			<Arg> int </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
		</BroadcastAvatarActorRPC>

		<OnNewGlobalMail>   <!--邮件通知用-->
			<Arg> int </Arg>
		</OnNewGlobalMail>

		<CreateWorldFromRemote>	 <!--创建场景用-->
			<Arg> int </Arg>
            <Arg> int </Arg>
            <Arg> string </Arg>
            <Arg> ApplyInfo </Arg>
		</CreateWorldFromRemote>

		<createGuild>   <!--公会用-->
			<Arg> string </Arg>
			<Arg> int </Arg>
            <Arg> mailbox </Arg>
			<Arg> int </Arg>
			<Arg> bool </Arg>
		</createGuild>

		<buildGuild>   <!--公会用-->
			<Arg> CREATE_GUILD_INFO </Arg>
            <Arg> mailbox </Arg>
			<Arg> string </Arg>
			<Arg> bool </Arg>
		</buildGuild>

		<CreateWorldFromCM>
			<Arg> int </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
		</CreateWorldFromCM>

		<OnReceiveServerTraceback>   <!--报警机器人用-->
			<Arg> string </Arg>
		</OnReceiveServerTraceback>

		<OnSwitchServerErrorLogToClient>	<!--报警机器人用-->
			<Arg> bool </Arg>
		</OnSwitchServerErrorLogToClient>

		<GMSetGuildLeagueMaxPlayer>	<!--公会联赛GM用-->
			<Arg> int </Arg>
		</GMSetGuildLeagueMaxPlayer>

		<GMSetDungeonMinPlayerLimit>	<!--设置副本人数GM用-->
			<Arg> int </Arg>
		</GMSetDungeonMinPlayerLimit>

		<broadcastLogicServerMirrorTable> <!--逻辑服全局缓存-->
			<!--1 serverID-->
			<Arg> int </Arg>
			<!--2 tableName-->
			<Arg> string </Arg>
			<!--3 tableDict-->
			<Arg> none </Arg>
		</broadcastLogicServerMirrorTable>

		<broadcastLSMMultiTable>
			<!--1 serverID-->
			<Arg> int </Arg>
			<!--2 tableName2tableDict-->
			<Arg> DictStrNone </Arg>
		</broadcastLSMMultiTable>

		<broadcastGlobalMirrorTable>  <!--全局缓存-->
			<!--1 tableName-->		
			<Arg> string </Arg>
			<!--2 tableDict-->
			<Arg> none </Arg>
		</broadcastGlobalMirrorTable>

		<broadcastTriggerMirrorDataSyncToAllLogic>   <!--镜像数据用-->
			<Arg> string </Arg>
		</broadcastTriggerMirrorDataSyncToAllLogic>

		<broadcastMirrorDataSync>   <!--镜像数据用-->
			<!--1 serverID-->
			<Arg> string </Arg>
			<!--2 tableDict-->					
			<Arg> none </Arg>
		</broadcastMirrorDataSync>

		<SetLuaCallTimeout>	  <!--设置lua超时时间，GM用-->
			<Arg> int </Arg>
		</SetLuaCallTimeout>

		<broadcastActivityStatus>  <!--活动系统用-->
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
		</broadcastActivityStatus>

		<FriendServiceRestart>  <!--FriendService重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg> <!--shardIndex-->
			<Arg> int </Arg> <!--service Term-->
			<Arg> int </Arg> <!-- status 启动阶段-->
		</FriendServiceRestart>

		<OnlineServiceRestart>  <!--OnlineService重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
		</OnlineServiceRestart>

		<OnlineServiceCloseSuccess>  <!--OnlineService关服用-->
		</OnlineServiceCloseSuccess>

		<UpdateTeamPvPSeason>  <!-- pvp赛季用-->
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> bool </Arg>
		</UpdateTeamPvPSeason>

		<TeamMatchServiceRestart>  <!--TeamMatchService重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg>			
		</TeamMatchServiceRestart>

		<TeamServiceRestart>  <!--TeamService重启用-->
			<Arg> mailbox </Arg>
		</TeamServiceRestart>

		<TeamManagerServiceRestart>  <!--TeamManagerService重启用-->
			<Arg> mailbox </Arg>
		</TeamManagerServiceRestart>

		<BroadcastWorldServiceRestart>  <!--WorldService重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg>
		</BroadcastWorldServiceRestart>

		<BroadcastGuildServiceInit>  <!--GuildService初始化-->
			<Arg> mailbox </Arg>
		</BroadcastGuildServiceInit>

		<BroadcastGuildServiceReady>  <!--GuildService启动成功-->
			<Arg> mailbox </Arg>
		</BroadcastGuildServiceReady>

		<onSyncItemSnapShotInfo>  <!--道具镜像-->
			<Arg> int </Arg>
			<Arg> SNAP_SHOT_INFO_MAP </Arg>
		</onSyncItemSnapShotInfo>

		<broadcastWorldChannelQuizStatus>  <!--答题状态变更-->
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> bool </Arg>
		</broadcastWorldChannelQuizStatus>

		<sendWorldChannelQuizReward>  <!--答题奖励-->
			<Arg> int </Arg>
		</sendWorldChannelQuizReward>

		<broadcastIsTopQuizRewardQuotaFull>  <!--前几名是否已产生-->
			<Arg> int </Arg>
			<Arg> bool </Arg>
		</broadcastIsTopQuizRewardQuotaFull>

		<GMResetActivity>
			<Arg> int </Arg>
			<Arg> string </Arg>
			<Arg> bool </Arg>
		</GMResetActivity>

		<GMCloseActivity>
			<Arg> int </Arg>
			<Arg> bool </Arg>
		</GMCloseActivity>

		<GMReverActivity>
			<Arg> int </Arg>
		</GMReverActivity>

		<RouterServiceRestart>  <!--RouterService重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
		</RouterServiceRestart>

		<OnModifyAccountSwitches>  <!--个人账号的开关用-->
			<Arg> string </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
		</OnModifyAccountSwitches>

		<OnReceiveSwitches>  <!--模块开关用-->
			<Arg> mailbox </Arg>
			<Arg> DictIntBool </Arg>
			<Arg> int </Arg>
		</OnReceiveSwitches>

		<SOnMsgSyncSwitchInfo>  <!--模块开关用-->
			<Arg> DictIntBool </Arg>
			<Arg> int </Arg>
		</SOnMsgSyncSwitchInfo>

		<OnModifySwitches>  <!--模块开关用-->
			<Arg> mailbox </Arg>
			<Arg> DictIntBool </Arg>
			<Arg> int </Arg>
		</OnModifySwitches>

		<OnKillOutAllAvatars>  <!--服务器踢人-->
			<Arg> int </Arg> <!--错误码-->
			<Arg> string </Arg> <!--踢人消息-->
		</OnKillOutAllAvatars>

		<OnKillOutSaveErrorAvatars>  <!--踢掉进程存盘失败的玩家-->

		</OnKillOutSaveErrorAvatars>

		<BroadcastSendGMMarquee> <!--发送跑马灯-->
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> int </Arg>
			<Arg> string </Arg>
			<Arg> float </Arg>
			<Arg> int </Arg>
		</BroadcastSendGMMarquee>

		<BroadcastDeleteGMMarquee> <!--停止跑马灯-->
			<Arg> int </Arg>
		</BroadcastDeleteGMMarquee>

		<OnSetServerGlobalConfig> <!--修改GM的配置文件文件-->
			<Arg> ListStr </Arg>
			<Arg> string </Arg>
			<Arg> int </Arg>  <!--只有一处用到 先申明成int-->
		</OnSetServerGlobalConfig>

		<broadcastNewHotfixVersion> <!--hotfix用-->
			<Arg> string </Arg>
			<Arg> int </Arg> 
		</broadcastNewHotfixVersion>

		<BroadcastLoadBalanceServiceRestart> <!--LoadBalanceService重启用-->
			<Arg> mailbox </Arg> 
		</BroadcastLoadBalanceServiceRestart>

		<BroadcastLoadBalanceServiceInit> <!--LoadBalanceService初始化用-->
			<Arg> mailbox </Arg> 
		</BroadcastLoadBalanceServiceInit>

		<NotifyRankServiceInit> <!--RankService初始化用-->
		</NotifyRankServiceInit>

		<SOnMsgSyncRankInfo> <!--同步排行榜信息用-->
			<Arg> int </Arg> 		
			<Arg> int </Arg> 
			<Arg> int </Arg>
			<Arg> string </Arg> 
			<Arg> ListListStr </Arg> 
			<Arg> DictStrInt </Arg> 
			<Arg> ListListStr </Arg> 
		</SOnMsgSyncRankInfo>

		<SOnMsgSyncRankInfos> <!--同步排行榜信息用-->
			<Arg> int </Arg> 		
			<Arg> int </Arg> 
			<Arg> int </Arg>
			<Arg> RANK_INFOS </Arg> 
		</SOnMsgSyncRankInfos>

		<reloadScript> <!--reload用-->
			<Arg> string </Arg> 
		</reloadScript>

		<GMAddForbidFlowchart>	<!-- 封禁流程图 -->
			<Arg> int </Arg>
			<Arg> string </Arg> 
		</GMAddForbidFlowchart>

		<GMDelForbidFlowchart> <!-- 解禁流程图 -->
			<Arg> int </Arg>
			<Arg> string </Arg> 
		</GMDelForbidFlowchart>

		<BroadcastClimateServiceInit>  <!--天气信息同步-->
			<!-- 所有区域天气 -->
			<Arg> RegionClimateInfoList </Arg>
		</BroadcastClimateServiceInit>

		<OnRetClimateServiceInit>  <!--用于进程重启时天气数据的恢复-->
			<!-- 所有区域天气 -->
			<Arg> RegionClimateInfoList </Arg>
		</OnRetClimateServiceInit>

		<OnClimateChange> <!--区域天气变更-->
			<!-- regionID -->
            <Arg> int </Arg>
            <!-- climateID -->
            <Arg> int </Arg>
		</OnClimateChange>
		
		<syncLoginQueueInfos>   <!--登录排队用-->
			<Arg> LOGIN_QUEUE_INFOS </Arg>
            <Arg> int </Arg>
		</syncLoginQueueInfos>
        
		<!--预留接口-->
		<BroadcastAnyRpc>
			<Arg> string </Arg>
		</BroadcastAnyRpc>

		<TelnetCommand>
			<Arg> mailbox </Arg>
			<Arg> int </Arg>
			<Arg> string </Arg>
		</TelnetCommand>

		<DoRemoteDebugCmd>	<!--远程调试用-->
			<Arg> int </Arg>
			<Arg> string </Arg>
		</DoRemoteDebugCmd>

		<GmSetServerTime>	<!--设置服务器时间-->
			<Arg> LONGTIME </Arg>  <!--服务器时间-->
			<Arg> bool </Arg>	   <!--是否重置-->
		</GmSetServerTime>

		<GmSetServerTimeShort>	<!--设置服务器时间(短时间修改)-->
			<Arg> LONGTIME </Arg>  <!--服务器时间-->
			<Arg> bool </Arg>	   <!--是否重置-->
		</GmSetServerTimeShort>

		<LoginQueueServiceRestart>  <!--LoginQueueServiceRestart重启用-->
			<Arg> mailbox </Arg>
			<Arg> int </Arg>
		</LoginQueueServiceRestart>

		<BroadcastExecGMFuncCodeAllLogic> 
			<Arg> string </Arg>
		</BroadcastExecGMFuncCodeAllLogic>

		<BroadcastExecGMFuncCodeOneLogic> 
			<Arg> int </Arg>
			<Arg> string </Arg>
		</BroadcastExecGMFuncCodeOneLogic>

		<BroadcastTempInsertClientHotfix> 
			<Arg> string </Arg>
		</BroadcastTempInsertClientHotfix>
        
        <GMGetEntityProperty>
			<Arg> mailbox </Arg>
			<Arg> none </Arg>
            <Arg> ENTITY_ID </Arg>
			<Arg> string </Arg>
			<Arg> string </Arg>
        </GMGetEntityProperty>
    </ServerMethods>
</root>