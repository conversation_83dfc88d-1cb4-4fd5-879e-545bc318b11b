<?xml version="1.0" ?>
<root>
    <Properties>
        <LimitGoodsBuyInfo Type="LimitGoodsInfoDict" Flags="SERVER_ONLY" Persistent="true"/>
        <DiscountGoodsBuyInfo Type="LimitGoodsInfoDict" Flags="SERVER_ONLY" Persistent="true"/>        
        <LastGCBuyInfoTime Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/>
	</Properties>
	<Implements/>
	<ClientMethods>
        <RetBuyGoods>
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 SerialNo -->
            <Arg> string </Arg>
            <!--3 GoodsID-->
            <Arg> int </Arg>
        </RetBuyGoods>

        <RetShopQueryGoodsLimitInfo>
            <!--1 result -->
            <Arg> Result </Arg>      
            <!--2 limitInfo -->
            <Arg> DictIntInt </Arg>
        </RetShopQueryGoodsLimitInfo>

        <OnMsgUpdateLimitItemInfo>
            <!--1 LimitItemInfos -->
            <Arg> LimitGoodsInfoDict </Arg>
            <!--2 DiscountGoodsBuyInfo -->
            <Arg> LimitGoodsInfoDict </Arg>            
            <!--3 bAllSync -->
            <Arg> bool </Arg>
        </OnMsgUpdateLimitItemInfo>
        <RetBuyMultiGoods>
            <Arg> MULTI_BUY_GOODS_RESULT_DEFINE </Arg>
        </RetBuyMultiGoods>
	</ClientMethods>
	<ServerMethods>
        <ReqBuyGoods> <Exposed/>
            <!--1 GoodsID -->
            <Arg> int </Arg>
            <!--2 ShopID -->
            <Arg> int </Arg>
            <!--3 Count -->
            <Arg> int </Arg>
            <!--4 SerialNo -->
            <Arg> string </Arg>
            <!--5 DiscountPrice -->
            <Arg> int </Arg>
        </ReqBuyGoods>

        <ReqBuyMultiGoods CD="0.3"> <Exposed/> <!-- 不支持全服限购的，全服限购的会直接判定购买失败，全服限购请单独走ReqBuyGoods-->
            <Arg> MULTI_BUY_GOODS_DEFINE </Arg>
        </ReqBuyMultiGoods>

        <ReqShopQueryGoodsLimitInfo  CD="0.3"> <Exposed/>
        </ReqShopQueryGoodsLimitInfo>

        <SSRetShopBuyServerGoods>
            <!--buyCount-->        
            <Arg> UINT </Arg>
        </SSRetShopBuyServerGoods> 

        <OnRetServerQueryServerGoodsLimitInfo>
            <!--limitInfo-->        
            <Arg> DictIntInt </Arg>
        </OnRetServerQueryServerGoodsLimitInfo> 
       
	</ServerMethods>
</root>
