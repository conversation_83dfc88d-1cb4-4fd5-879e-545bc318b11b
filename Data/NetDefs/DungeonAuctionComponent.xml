<?xml version="1.0" ?>
<root>
    <Properties>
		<DungeonAuctionOrderHasInit Type="bool" Default="false" Flags="SERVER_ONLY" Persistent="true"/>
		<MaxTsDungeonAuctionOrder Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
		<!--拍卖的各个物品的详细信息-->
		<DungeonAuctions Type="DungeonAuctions" Flags="SERVER_ONLY" Persistent="true"/>
		<!--自己参与的拍卖对应的出价-->
		<DungeonAuctionOrders Type="DungeonAuctionOrders" Flags="SERVER_ONLY" Persistent="true"/>
		<!--此属性废弃了 xietieyong-->
		<DungeonAuctionEstimations Type="DungeonAuctionEstimationInfos" Flags="OWN_CLIENT" Persistent="true"/>
	</Properties>

	<Implements>
	</Implements>

	<ClientMethods>
		<RetDungeonAuctionBid>		
			<!--1 Result -->
			<Arg> int </Arg>
			<!--2 AuctionID -->
			<Arg> string </Arg>
			<!--3 GoodsID -->
			<Arg> string </Arg>
		</RetDungeonAuctionBid>

		<OnMsgDungeonAuctionRefreshState>		
			<!--1 SyncInfos -->
			<Arg> DungeonAuctionSyncStateInfos </Arg>		
		</OnMsgDungeonAuctionRefreshState>

		<OnMsgDungeonAuctionRefresh>		
			<!--1 SyncInfos -->
			<Arg> DungeonAuctionSyncClientInfos </Arg>		
		</OnMsgDungeonAuctionRefresh>

		<OnMsgSyncDungeonAuctionRemove>
			<Arg> ListStr </Arg>
		</OnMsgSyncDungeonAuctionRemove>

		<RetDungeonAuctionGiveUpBid>
			<!--1 Result -->
			<Arg> int </Arg>	
			<!--2 AuctionID -->
			<Arg> string </Arg>
			<!--3 GoodsID -->
			<Arg> string </Arg>
			<!--4 bGiveUp -->
			<Arg> bool </Arg>
		</RetDungeonAuctionGiveUpBid>

		<OnMsgNotifyGiveUpBid>
			<!--1 GoodsID -->
			<Arg> string </Arg>
			<!--2 AvatarName -->
			<Arg> string </Arg>
		</OnMsgNotifyGiveUpBid>
	</ClientMethods>

    <ServerMethods>
		<SOnMsgNotifyGiveUpBid>
			<!--1 GoodsID -->
			<Arg> string </Arg>
			<!--2 AvatarName -->
			<Arg> string </Arg>
		</SOnMsgNotifyGiveUpBid>

		<ReqDungeonAuctionBid CD="0.17"> <Exposed/>		
			<!--1 AuctionID -->
			<Arg> string </Arg>
			<!--2 GoodsID -->
			<Arg> string </Arg>
			<!--3 MoneyType -->
			<Arg> int </Arg>	
			<!--4 MoneyCount -->
			<Arg> int </Arg>	
		</ReqDungeonAuctionBid>

		<ReqDungeonAuctionGiveUpBid CD="0.17"> <Exposed/>	
			<!--1 AuctionID -->
			<Arg> string </Arg>
			<!--2 GoodsID -->
			<Arg> string </Arg>
			<!--3 bGiveUp -->
			<Arg> bool </Arg>
		</ReqDungeonAuctionGiveUpBid>

		<SOnMsgHandleDungeonAuctionOrder>		
			<!--1 AuctionID -->
			<Arg> string </Arg>
			<!--2 GoodsID -->
			<Arg> string </Arg>
			<!--3 OrderID -->
			<Arg> string </Arg>	
			<!--4 bSuccess -->
			<Arg> bool </Arg>	
			<!--5 bEstimate -->
			<Arg> bool </Arg>	
		</SOnMsgHandleDungeonAuctionOrder>

		<SOnMsgHandleDungeonAuctionDividend>		
			<!--1 AuctionID -->
			<Arg> string </Arg>
			<!--2 GoodsID -->
			<Arg> string </Arg>
			<!--3 MoneyType -->
			<Arg> int </Arg>	
			<!--4 MoneyCount -->
			<Arg> int </Arg>	
		</SOnMsgHandleDungeonAuctionDividend>

		<SOnMsgDungeonAuctionStart>
			<!--1 AuctionID -->
			<Arg> string </Arg>
			<!--2 StartTS -->
			<Arg> int </Arg>
			<!--3 Queue -->
			<Arg> DungeonAuctionQueue </Arg>
		</SOnMsgDungeonAuctionStart>

		<SOnMsgHandleDungeonAuctionChange>
			<!--1 SeqNo -->
			<Arg> int </Arg>
			<!--2 CurIndex -->
			<Arg> int </Arg>
			<!--3 AuctionID -->
			<Arg> string </Arg>
			<!--4 GoodsID -->
			<Arg> GoodsIDs </Arg>	
			<!--5 SyncInfo -->
			<Arg> DungeonAuctionSyncInfos </Arg>	
		</SOnMsgHandleDungeonAuctionChange>
    </ServerMethods>
</root>
