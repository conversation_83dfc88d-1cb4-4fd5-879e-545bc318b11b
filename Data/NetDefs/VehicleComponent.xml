<?xml version="1.0" ?>
<root>
    <Properties>
        <SeatInfo Type="DictIntStr" Flags="ALL_CLIENTS"/>
		<SeatCount Type="int" Default="0" Flags="ALL_CLIENTS"/>
		<bAllowGetOn Type="bool" Default="true" Flags="ALL_CLIENTS"/>
		<bAllowGetOff Type="bool" Default="true" Flags="ALL_CLIENTS"/>
		<bAllowChangeSeat Type="bool" Default="true" Flags="ALL_CLIENTS"/>
		<bAllowAccelerate Type="bool" Default="false" Flags="ALL_CLIENTS"/>
		<AccelerateDuration Type="int" Default="0" Flags="ALL_CLIENTS"/>
		<AccelerateCD Type="int" Default="0" Flags="ALL_CLIENTS"/>
		<AccelerateDurationTimeStamp Type="int" Default="0" Flags="ALL_CLIENTS"/>
		<AccelerateCDTimeStamp Type="int" Default="0" Flags="ALL_CLIENTS"/>
		<GameplayType Type="int" Default="0" Flags="ALL_CLIENTS"/>
    </Properties>
	<Implements/>
	<ClientMethods>
	</ClientMethods>
	<!-- Exposed 待处理 -->
	<ServerMethods>
	</ServerMethods>
</root>
