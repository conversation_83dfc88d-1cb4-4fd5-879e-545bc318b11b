<?xml version="1.0" ?>
<root>
    <Properties>
    </Properties>
    <Implements/>
    <ClientMethods>
        <RetDungeonBattleStatistics>
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 SeqId -->
            <Arg> int </Arg>
            <!--3 DetailStatistics -->
            <Arg> BattleStatisticsDetail </Arg>
        </RetDungeonBattleStatistics>
        <RetMonsterBattleStatistics>
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 SeqId -->
            <Arg> int </Arg>
            <!--3 DetailStatistics -->
            <Arg> BattleStatisticsDetail </Arg>
        </RetMonsterBattleStatistics>
        <OnMsgUpdateDungeonBattleStatistics>
            <!--1 DetailStatistics -->
            <Arg> BattleStatisticsDetail </Arg>
            <!--2 isSpecialBoss -->
            <Arg> bool </Arg>
        </OnMsgUpdateDungeonBattleStatistics>
        <OnMsgUpdateDungeonTeamPlayerBattleStatistics>
            <!--1 UpdatedTeamPlayerStatistics -->
            <Arg> UpdatedTeamPlayerBattleStatistics </Arg>
            <!--2 isSpecialBoss -->
            <Arg> bool </Arg>
        </OnMsgUpdateDungeonTeamPlayerBattleStatistics>
        <RetAggroInfoList>
            <!--1 result -->
            <Arg> Result </Arg>
            <!--2 SeqId -->
            <Arg> int </Arg>
            <!--3 TargetNpcId -->
            <Arg> string </Arg>
            <!--4 AggroInfos -->
            <Arg> AggroInfoDict </Arg>
        </RetAggroInfoList>
        <OnMsgTakeLastAttackDamage>
            <!--1 killer templateId-->
            <Arg> UINT </Arg>
            <!--2 killer name-->
            <Arg> string </Arg>
            <!--3 skill Id -->
            <Arg> UINT </Arg>
            <!--4 realTotalDamage-->
            <Arg> float </Arg>
        </OnMsgTakeLastAttackDamage>
        <OnLeaveStatistics>
        </OnLeaveStatistics>
    </ClientMethods>
    <!-- Exposed 待处理 -->
    <ServerMethods>
        <ReqDungeonBattleStatistics CD="0.5" > <Exposed/>
            <!--1 SeqId -->
            <Arg> int </Arg>
        </ReqDungeonBattleStatistics>
        <ReqMonsterBattleStatistics CD="0.5" > <Exposed/>
            <!--1 SeqId -->
            <Arg> int </Arg>
            <!--2 MonsterNpcId -->
            <Arg> string </Arg>
        </ReqMonsterBattleStatistics>
        <ReqAggroInfoList> <Exposed/>
            <!--1 SeqId -->
            <Arg> int </Arg>
            <!--2 TargetNpcId -->
            <Arg> ENTITY_ID </Arg>
        </ReqAggroInfoList>
    </ServerMethods>
</root>
