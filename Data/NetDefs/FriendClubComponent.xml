<root>
    <Properties>
        <friendClubSetting Type="FRIEND_CLUB_SETTING_INFO_MAP"  Persistent="true" />    <!-- 群组设置信息 -->
        <isClubLoaded Type="bool" Default="false" Flags="SERVER_ONLY" Persistent="false"/>
        <clubsNoDisturb Type="DictStrBool" Flags="OWN_CLIENT" Persistent="true"/>
    </Properties>

    <ClientMethods>
        <s2cGetFriendClubInfo>                  <!-- 获取群组信息(全量更新) -->
            <Arg> FRIEND_CLUB_INFO_LIST </Arg>
        </s2cGetFriendClubInfo>

        <s2cSendOneFriendClubInfo>              <!-- 群组信息(增量更新) -->
            <Arg> FRIEND_CLUB_INFO </Arg>
        </s2cSendOneFriendClubInfo>

        <s2cGetFriendClubSettingInfo>           <!-- 获取群组设置信息(全量更新) -->
            <Arg> FRIEND_CLUB_SETTING_INFO_MAP </Arg>
        </s2cGetFriendClubSettingInfo>

        <s2cSendOneFriendClubSettingInfo>         <!-- 群组设置信息(增量更新) -->
            <Arg> string </Arg>                   <!-- 群组Id -->
            <Arg> FRIEND_CLUB_SETTING_INFO </Arg>
        </s2cSendOneFriendClubSettingInfo>

        <s2cGetFriendClubMemberInfo>            <!-- 获取成员信息 -->
            <Arg> ROLE_BRIEF_MAP </Arg>         <!-- 玩家信息列表 -->
        </s2cGetFriendClubMemberInfo>

        <s2cleaveFriendClub>                    <!-- 离开群组(被踢、退群、解散) -->
            <Arg> string </Arg>                 <!-- 群组Id -->
        </s2cleaveFriendClub>

        <s2cGetFriendClubChatInfo>              <!-- 获取聊天记录 -->
            <Arg> string </Arg>                 <!-- 群组Id -->
            <Arg> CHAT_INFO_LIST </Arg>         <!-- 聊天记录 -->
            <Arg> NUID </Arg>                   <!-- opNUID -->
        </s2cGetFriendClubChatInfo>

        <s2cFriendClubChat>                     <!-- 聊天 -->
            <Arg> string </Arg>                 <!-- 群组Id -->
            <Arg> CHAT_INFO </Arg>              <!-- 聊天内容 -->
        </s2cFriendClubChat>

        <s2cSetFriendClubBanId>                 <!-- 消息屏蔽群组设置 -->
            <Arg> DictStrBool </Arg>            <!-- clubId - true -->
        </s2cSetFriendClubBanId>

        <s2cClearFriendClubChatRecord>          <!-- 清空聊天记录 -->
            <Arg> string </Arg>                 <!-- 群组Id -->
            <Arg> NUID </Arg>                   <!-- opNUID -->
        </s2cClearFriendClubChatRecord>

        <OnMsgSyncTopClubMap>                      <!-- 全量同步群聊置顶 -->
            <Arg> TOP_CLUB_MAP </Arg>
        </OnMsgSyncTopClubMap>
        <onSyncTopClubMap>                      <!-- 全量同步群聊置顶 -->
            <Arg> TOP_CLUB_MAP </Arg>
        </onSyncTopClubMap>

        <onTopClubMapChange>                    <!-- 增量同步群聊置顶 -->
            <Arg> string </Arg>                 <!-- 群组Id -->
            <Arg> LONGTIME </Arg>               <!-- 单位毫秒 0表示取消置顶 -->
        </onTopClubMapChange>

        <RetSetClubNoDisturb>
            <Arg> Result </Arg>
        </RetSetClubNoDisturb>

        <OnMsgClubsNoDisturbChange>
            <Arg> string </Arg>
            <Arg> bool </Arg>
        </OnMsgClubsNoDisturbChange>
    </ClientMethods>

    <ServerMethods>
        <c2sBuildFriendClub CD="1"> <Exposed />          <!-- 创建好友群组 done -->
            <Arg> string </Arg>                          <!-- 群组名 -->
            <Arg> string </Arg>                          <!-- 群组公告 -->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- 成员(不包括自己) -->
        </c2sBuildFriendClub>

        <c2sInviteFriendClubMember> <Exposed />          <!-- 邀请加入群组 done -->
            <Arg> string </Arg>                          <!-- 群组id -->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- 被邀请成员 -->
        </c2sInviteFriendClubMember>

        <c2sKickFriendClubMember CD="0.5"> <Exposed />   <!-- 踢群组成员 done -->
            <Arg> string </Arg>                          <!-- 群组id -->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- 被踢成员 -->
        </c2sKickFriendClubMember>

        <c2sDissolveFriendClub CD="3"> <Exposed />       <!-- 解散群组  done -->
            <Arg> string </Arg>                            <!-- 群组id -->
        </c2sDissolveFriendClub>

        <c2sQuitFriendClub CD="3"> <Exposed />           <!-- 退出群组 done -->
            <Arg> string </Arg>                            <!-- 群组id -->
        </c2sQuitFriendClub>

        <c2sEditFriendClubInfo CD="3"> <Exposed />       <!-- 编辑群组信息 done -->
            <Arg> string </Arg>                            <!-- 群组id -->
            <Arg> FRIEND_CLUB_EDIT_FIELDS </Arg>         <!-- 修改的字段 -->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- 拉的人-->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- 踢的人-->
        </c2sEditFriendClubInfo>

        <c2sGetFriendClubInfo> <Exposed />               <!-- 获取群组信息(不包括聊天) done -->
        </c2sGetFriendClubInfo>

        <c2sGetFriendClubChatInfo> <Exposed />           <!-- 获取聊天记录 done -->
            <Arg> string </Arg>                            <!-- 群组Id -->
            <Arg> NUID </Arg>                            <!-- opNUID -->
        </c2sGetFriendClubChatInfo>

        <c2sFriendClubChat CD="1"> <Exposed/>            <!-- 聊天 done -->
            <Arg> string </Arg>                            <!-- 群组Id -->
            <Arg> CHAT_INFO </Arg>                       <!-- 聊天内容 -->
        </c2sFriendClubChat>

        <c2sGetFriendClubMemberInfo> <Exposed/>          <!-- 获取成员信息(单次不超过群组人数上限) done -->
            <Arg> ENTITY_ID_LIST </Arg>                  <!-- gbIdList -->
        </c2sGetFriendClubMemberInfo>

        <c2sSetFriendClubBanId CD="1"> <Exposed />       <!-- 消息屏蔽群组设置 done -->
            <Arg> string </Arg>                            <!-- 群组Id -->
            <Arg> BOOL </Arg>                            <!-- 是否屏蔽 -->
        </c2sSetFriendClubBanId>

        <c2sSetFriendClubBulletScreen CD="1"> <Exposed /> <!-- 弹幕群组设置 done -->
            <Arg> string </Arg>                             <!-- 群组Id -->
            <Arg> BOOL </Arg>                             <!-- 是否开启弹幕 -->
        </c2sSetFriendClubBulletScreen>

        <c2sClearFriendClubChatRecord CD="1"> <Exposed /> <!-- 清空聊条记录 done -->
            <Arg> string </Arg>                             <!-- 群组Id -->
            <Arg> NUID </Arg>                             <!-- opNUID -->
        </c2sClearFriendClubChatRecord>

        <doSetTopClub> <Exposed/>               <!-- 设置群聊置顶 -->
            <Arg> string </Arg>                   <!-- 群组Id -->
        </doSetTopClub>

        <doCancelTopClub> <Exposed/>            <!-- 取消群聊置顶 -->
            <Arg> string </Arg>                   <!-- 群组Id -->
        </doCancelTopClub>

        <s2sOnSetFriendClubBanId>              <!-- 消息屏蔽群组设置 stub的回调 -->
            <Arg> string </Arg>                  <!-- 群组Id -->
            <Arg> BOOL </Arg>                  <!-- 是否屏蔽 -->
        </s2sOnSetFriendClubBanId>

        <s2sSetFriendClubBulletScreen>         <!-- 弹幕群组设置 stub的回调 -->
            <Arg> string </Arg>                  <!-- 群组Id -->
            <Arg> BOOL </Arg>                  <!-- 是否开启弹幕 -->
        </s2sSetFriendClubBulletScreen>

        <s2sOnClearFriendClubChatRecord>       <!-- 清空聊条记录 stub的回调 -->
            <Arg> string </Arg>                  <!-- 群组Id -->
            <Arg> NUID </Arg>                  <!-- opNUID -->
        </s2sOnClearFriendClubChatRecord>

        <s2sOnSetTopClub>                      <!-- 置顶群聊的回调 -->
            <Arg> string </Arg>                  <!-- 群组Id -->
        </s2sOnSetTopClub>

        <s2sOnCancelTopClub>                   <!-- 取消置顶群聊的回调 -->
            <Arg> string </Arg>                  <!-- 群组Id -->
        </s2sOnCancelTopClub>

        <ReqSetClubNoDisturb CD="0.17"><Exposed/> <!--群聊免打扰-->
            <Arg> string </Arg> <!-- 群组Id -->
            <Arg> bool </Arg> <!-- 是否免打扰 -->
        </ReqSetClubNoDisturb>

        <SSReqCreateOneFriendClub>
            <Arg> FRIEND_CLUB_INFO </Arg>
        </SSReqCreateOneFriendClub>

        <SSReqFriendClubMemberChange>
            <Arg> FRIEND_CLUB_INFO </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
            <Arg> bool </Arg>
        </SSReqFriendClubMemberChange>

        <SSReqLeaveOneFriendClub>
            <Arg> string </Arg>
        </SSReqLeaveOneFriendClub>

        <SSReqFriendClubReportSAData>
            <Arg> int </Arg>
            <Arg> string </Arg>
            <Arg> ENTITY_ID_LIST </Arg>
        </SSReqFriendClubReportSAData>

        <RPCTest>
            <Arg> FRIEND_CLUB_INFO </Arg>
        </RPCTest>
    </ServerMethods>
</root>
