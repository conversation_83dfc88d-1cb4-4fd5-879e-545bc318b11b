<?xml version="1.0" ?>
<root>
    <Properties>
        <ZhanLi Type="int" Flags="OWN_CLIENT" Default="0"/>
        <MaxZhanli Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>        
        <ZhanliDetail Type="DictIntInt" Flags="OWN_CLIENT"/>
    </Properties>
	<Implements/>
	<ClientMethods>
        <OnMsgZhanLiChangeDisplay>
            <!--1 preZhanLi -->
            <Arg> int </Arg>
            <!--2 curZhanLi -->
            <Arg> int </Arg>
            <!--3 preAttrDetail -->
            <Arg> DictStrFloat </Arg>
            <!--4 curAttrDetail -->
            <Arg> DictStrFloat </Arg>
        </OnMsgZhanLiChangeDisplay>
	</ClientMethods>
	<!-- Exposed 待处理 -->
	<ServerMethods>
	</ServerMethods>
</root>
