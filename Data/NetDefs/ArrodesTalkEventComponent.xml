<?xml version="1.0" ?>
<root>
    <Properties>
        <playerAskQuestionGroup Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <lockedPlayerAskQuestions Type="DictIntDictIntBool" Flags="OWN_CLIENT" Persistent="true"/>
        <lockedArrodesAskQuestions Type="DictIntDictIntBool" Flags="OWN_CLIENT" Persistent="true"/>
        <awardedPlayerAnswers Type="DictIntBool" Flags="SERVER_ONLY" Persistent="true"/>
    </Properties>
    <ClientMethods>
    </ClientMethods>
    <ServerMethods>
        <ReqPlayerAskQuestionEnd> <Exposed/>
            <Arg> int </Arg>    <!--题组id-->
            <Arg> int </Arg>    <!--题目id-->
        </ReqPlayerAskQuestionEnd>
        <ReqArrodesAskQuestionEnd> <Exposed/>
            <Arg> int </Arg>    <!--题组id-->
            <Arg> int </Arg>    <!--题目id-->
            <Arg> int </Arg>    <!--答案id-->
        </ReqArrodesAskQuestionEnd> 
    </ServerMethods>
</root>