<?xml version="1.0" ?>
<root>
    <Properties>
        <!--同步朋友圈的定时器-->
		<momentsSyncTimer Type="float" Flags="SERVER_ONLY" Default="0" Persistent="false"/>
        <!--朋友圈接口要用的token-->
		<momentsToken Type="string" Flags="OWN_CLIENT" Default="" Persistent="false"/>
    </Properties>

    <Implements/>

    <ClientMethods>
        <!--同步各种微服务（图片审核和朋友圈）的url-->
        <OnMsgSyncMicroServiceUrl>
            <Arg> string </Arg>     <!-- 图片审核的host -->
            <Arg> int </Arg>        <!-- 图片审核的port -->
            <Arg> string </Arg>     <!-- 朋友圈的host -->
            <Arg> int </Arg>        <!-- 朋友圈的port -->
        </OnMsgSyncMicroServiceUrl>
    </ClientMethods>

    <ServerMethods/>
</root>