<root>
    <Properties>
        <totalTarotTasksInfo Type="SCHEDULE_TASKS_INFO" Persistent="true" /> <!-- 每日启示所有任务 -->
        <screenTarotTasksInfo Type="SCHEDULE_TASKS_INFO" Persistent="true" /> <!-- 每日启示当前每日任务 -->
        <tarotProgressInfo Type="NEWBIE_PROGRESS_INFO" Persistent="true" /> <!-- 手册总进度 -->
        <poolTarotTasks Type="PARAM2TASKS" Flags="SERVER_ONLY" Persistent="true" />           <!-- 可刷新的存在于卡池中的任务id -->
        <fixedTarotTasks Type="PARAM2TASKS" Flags="SERVER_ONLY" Persistent="true" />          <!-- 已固定不可继续参与刷新的任务 -->
        <fixedTarotTaskNum Type="int" Flags="SERVER_ONLY" Persistent="true" />              <!-- 已固定的任务数量 -->
        <baseTarotTaskTypeNum Type="DictIntInt" Flags="SERVER_ONLY" Persistent="true" />    <!-- 记录固定任务里有多少个已完成的任务 -->
        <randomTarotTaskTypeNum Type="DictIntInt" Flags="SERVER_ONLY" Persistent="true" />  <!-- 记录补充任务里有多少个未完成的任务 -->
        <stId2TarotTasks Type="PARAM2TASKS" Persistent="true" />            <!-- 统计值到每日任务的映射 -->
        <revelationTask Type="REVELATION_TASK_INFO" Persistent="true" />    <!-- 命运启示任务 -->
        <isRevelationRefresh Type="bool" Persistent="true" Flags="OWN_CLIENT" />                 <!-- 是否要刷命运启示 -->
        <isScheduleFirstOpen Type="bool" Persistent="true" Default="true" Flags="OWN_CLIENT" />
    </Properties>

    <ClientMethods>
        <onGetTarotTasksInfo>          <!-- 全量更新 登录获取扮演手册所有相关数据 -->
            <Arg> NEWBIE_PROGRESS_INFO </Arg>   <!-- 进度信息 -->
            <!-- <Arg> NEWBIE _TASKS_INFO </Arg>      所有塔罗任务 -->
            <Arg> SCHEDULE_TASKS_INFO </Arg>      <!-- 每日塔罗任务 -->
        </onGetTarotTasksInfo>

        <onUpdateTarotProgressInfo>    <!-- 更新总体进度状态 -->
            <Arg> NEWBIE_PROGRESS_INFO </Arg>
        </onUpdateTarotProgressInfo>

        <onUpdateTarotTaskInfo>        <!-- 更新单条任务内容状态 -->
            <Arg> SCHEDULE_TASK_INFO </Arg>
        </onUpdateTarotTaskInfo>

        <!--  更新所有的每日任务 -->
        <onUpdateDailyTarotTasksInfo>
            <Arg> SCHEDULE_TASKS_INFO </Arg>    <!-- 每日塔罗任务 -->
        </onUpdateDailyTarotTasksInfo>
        
        <!-- <retManualRefreshScheduleDailyTasks>
            <Arg> int </Arg>    
        </retManualRefreshScheduleDailyTasks> -->
        
        <!-- 领取塔罗牌活跃度 -->
        <retGetTarotTaskReward>
			<!-- taskId -->
			<Arg> int </Arg>
            <!-- error code -->
            <Arg> int </Arg>
        </retGetTarotTaskReward>

        <!-- 领取活跃度奖励 -->
        <retGetTarotStageReward>
		    <!-- stageId -->
		    <Arg> int </Arg>
            <!-- error code -->
            <Arg> int </Arg>
        </retGetTarotStageReward>

        <!-- 命运启示刷新状态同步 -->
        <syncFateRevelationTaskInfo>
            <Arg> REVELATION_TASK_INFO </Arg> 
        </syncFateRevelationTaskInfo>

        <retGetFateRevelationReward>
            <Arg> int </Arg> <!-- error code -->
        </retGetFateRevelationReward>

        <retCheckIsScheduleFirstOpen>
            <Arg> bool </Arg> 
        </retCheckIsScheduleFirstOpen>

        <syncIsScheduleFirstOpen>
            <Arg> bool </Arg> 
        </syncIsScheduleFirstOpen>
        
    </ClientMethods>

    <ServerMethods>
        <!-- 重新占卜 -->
        <reqManualRefreshScheduleDailyTasks> <Exposed/>
        </reqManualRefreshScheduleDailyTasks>

        <!-- 领取塔罗牌活跃度 -->
        <reqGetTarotTaskReward CD="0.1"> <Exposed/>
			<!-- taskId -->
			<Arg> int </Arg>
        </reqGetTarotTaskReward>

        <!-- 领取活跃度奖励 -->
        <reqGetTarotStageReward CD="0.17"> <Exposed/>
		    <!-- stageId -->
		    <Arg> int </Arg>
        </reqGetTarotStageReward>

        <!-- 领取命运启示奖励 -->
        <reqGetFateRevelationReward CD="0.1"> <Exposed/>
            <Arg> int </Arg>
        </reqGetFateRevelationReward>

        <reqCheckIsScheduleFirstOpen> <Exposed/>
        </reqCheckIsScheduleFirstOpen>


    </ServerMethods>
</root>