<root>
    <Properties>
    </Properties>

    <Implements>
        <Interface>Service</Interface>
    </Implements>

    <ClientMethods>


    </ClientMethods>

    <ServerMethods>
        <SSOnMsgGetPartyID>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!-- 建房时的房间信息 -->
            <Arg>CREATE_CHAT_ROOM_INFO</Arg>
        </SSOnMsgGetPartyID>

        <SSOnMsgCreateParty>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!--roomID -->
            <Arg>int</Arg>
            <!-- 建房时的房间信息 -->
            <Arg>CREATE_CHAT_ROOM_INFO</Arg>
            <!-- 建房的顺序 -->
            <Arg>int</Arg>
        </SSOnMsgCreateParty>
        <SSOnMsgFindAndEnterRandomAvailableParty>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!-- roomID -->
            <Arg>int</Arg>
        </SSOnMsgFindAndEnterRandomAvailableParty>
        <SSOnMsgDeletePartyBriefInfo>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--房主ID -->
            <Arg>string</Arg>
        </SSOnMsgDeletePartyBriefInfo>
        <SSOnMsgModifyPartyInfo>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--成员数量 -->
            <Arg>int</Arg>
            <!-- 房间人数是否未满 -->
            <Arg>bool</Arg>
        </SSOnMsgModifyPartyInfo>
        <SSOnMsgPersistentPartyChange>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--是否为常驻房间 -->
            <Arg>bool</Arg>
            <!--房主ID -->
            <Arg>string</Arg>
        </SSOnMsgPersistentPartyChange>
        <SSOnMsgCheckPartyFavorites>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!--收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!--roomID -->
            <Arg>int</Arg>
        </SSOnMsgCheckPartyFavorites>
        <SSOnMsgUpdatePartyFavorites>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!--收藏夹 -->
            <Arg>DictIntUInt</Arg>
            <!-- 是否删除收藏夹 -->
            <Arg>bool</Arg>
        </SSOnMsgUpdatePartyFavorites>
        
        <SSOnMsgSearchParty>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!--模式串 -->
            <Arg>string</Arg>
            <!-- 服务器ID -->
            <Arg>int</Arg>
            <!--是否只有当前服务器 -->
            <Arg>bool</Arg>
            <!--筛选列表 -->
            <Arg>ARRAY_BOOL</Arg>
        </SSOnMsgSearchParty>

        <BatchModifyPartyGiftHotValue>
            <!-- 房间ID -> 房间礼物热度值的Map -->
            <Arg>UINT_MAP</Arg>
        </BatchModifyPartyGiftHotValue>

        <SSOnMsgModifyPartyName>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--房间名称 -->
            <Arg>string</Arg>
        </SSOnMsgModifyPartyName>
        <SSOnMsgModifyPartyType>
            <!--房间ID -->
            <Arg>int</Arg>
            <!--房间类型 -->
            <Arg>int</Arg>
        </SSOnMsgModifyPartyType>
        <SSOnMsgDismantleAllParty>
        </SSOnMsgDismantleAllParty>
        <SSOnMsgFindPartyIDOfRoomName>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!-- roomName -->
            <Arg>string</Arg>
        </SSOnMsgFindPartyIDOfRoomName>
        <SSOnMsgDismantlePartyOnDeleteRole>
            <!-- roomID -->
            <Arg>string</Arg>
        </SSOnMsgDismantlePartyOnDeleteRole>
        <GMRandomlyCreateParty>
            <!--mailbox -->
            <Arg>mailbox</Arg>
            <!-- IsPersistent -->
            <Arg>bool</Arg>
            <!-- 建房时的房间信息 -->
            <Arg>CREATE_CHAT_ROOM_INFO</Arg>
            <!-- 房间热度 -->
            <Arg>int</Arg>
            <!--房间人数 -->
            <Arg>int</Arg>
        </GMRandomlyCreateParty>
    </ServerMethods>
</root>