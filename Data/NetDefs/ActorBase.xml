<?xml version="1.0" ?>
<root>
    <Properties>
        <CanTakeDamage Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <chrTypeMask Type="int" Flags="ALL_CLIENTS|GHOST" Default="0"/>

        <!-- 一些关键状态 -->
        <IsDead Type="bool" Flags="ALL_CLIENTS" Default="false" Persistent="true" BriefProp="true"/>
        <InBattle Type="bool" Flags="ALL_CLIENTS" Default="false" BriefProp="true"/>
        <InBattleTime Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <OutBattleTime Type="int" Flags="SERVER_ONLY" Default="nil"/>

        <ModifyIDCreater Type="int" Flags="SERVER_ONLY" Default="0"/>
        <ModifyIDList Type="DictIntInt" Flags="SERVER_ONLY"/>
        <bSkillAgent Type="bool" Flags="SERVER_ONLY" Default="false"/>

        <!-- 阵营关系相关的属性 -->
        <Camp Type="int" Flags="ALL_CLIENTS" Default="0" BriefProp="true"/>
        <InitCamp Type="int" Flags="SERVER_ONLY" Default="nil"/>                                  <!--默认阵营（回退的默认阵营）-->
        <DynamicCampRule Type="int" Flags="ALL_CLIENTS" Default="nil" BriefProp="true"/>          <!--阵营判断规则类型（暂时只有红名，预留类型字段）-->      
        <DynamicCampRelationship Type="dict" Key="int" Flags="ALL_CLIENTS" Value="UINT" Default="nil" BriefProp="true"/>                        <!--动态阵营关系-->
        <ObserverType Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="false" BriefProp="true"/>
        <BattleZoneID Type="string" Flags="ALL_CLIENTS" Default="" Persistent="false" BriefProp="true"/>
        <FinalOwnerID Type="string" Flags="ALL_CLIENTS|GHOST" Default="" BriefProp="true"/>

        <MissionGroup Type="string" Flags="ALL_CLIENTS" Default="" />
        <MissionGroupType Type="int" Flags="SERVER_ONLY | GHOST" Default="0" />
        <MissionGroupOwner Type="string" Flags="SERVER_ONLY | GHOST" Default="" />


        <__timer_info Type="TIMER_INFO" />
		<__timer_const_to_id Type="INT_MAP" />
		<BroadcastRecords Type="BroadcastRecords"/>
		<migrateServiceCallback Type="dict" Key="int" Value="MSCallBack" Flags="SERVER_ONLY"/>
        <migrateMultiServiceCallback Type="dict" Key="int" Value="MMSCallBack" Flags="SERVER_ONLY"/>
    </Properties>
    <Implements>
        <Interface>EntityBase</Interface>
    </Implements>
    <ClientMethods>
        <OnMsgObserverTypeChange>
            <!--1 NewType -->
            <Arg> int </Arg>
        </OnMsgObserverTypeChange>

        <OnMsgShowTalkBubble>
            <!--1 NewType -->
            <Arg> int </Arg>
            <Arg> VList </Arg>
            <Arg> float </Arg>
        </OnMsgShowTalkBubble>

        <OnMsgForwardAvatarRpc>
            <!--1 rpcName -->
            <Arg>string</Arg>
            <!--2 rpcArgs -->
            <Arg>VList</Arg>
        </OnMsgForwardAvatarRpc>

        <OnMsgRotateToAng>
            <!-- angle -->
            <Arg> int </Arg>
            <!-- rotateTime -->
            <Arg> float </Arg>
            <!-- bShieldAnimation -->
            <Arg> bool </Arg>
        </OnMsgRotateToAng>

        <OnMsgRotateToEntity>
            <!-- targetID -->
            <Arg> string </Arg>
            <!-- rotateTime -->
            <Arg> float </Arg>
            <!-- bShieldAnimation -->
            <Arg> bool </Arg>
        </OnMsgRotateToEntity>

        <OnMsgRotateToLoc>
            <!-- pos -->
            <Arg> Vector3 </Arg>
            <!-- rotateTime -->
            <Arg> float </Arg>
            <!-- bShieldAnimation -->
            <Arg> bool </Arg>
        </OnMsgRotateToLoc>

		<OnMsgStopRotate>
			<!--rotateToTargetYaw-->
			<Arg> bool </Arg>
		</OnMsgStopRotate>

        <OnMsgGazeToSpawner>
            <!-- SpawnerID -->
            <Arg> string </Arg>
            <!-- SocketName -->
            <Arg> string </Arg>
        </OnMsgGazeToSpawner>

        <OnMsgGazeToEntity>
            <!-- EntityID -->
            <Arg> string </Arg>
            <!-- SocketName -->
            <Arg> string </Arg>
        </OnMsgGazeToEntity>

        <OnMsgGazeToLoc>
            <!-- pos -->
            <Arg> Vector3 </Arg>
        </OnMsgGazeToLoc>

        <OnMsgGazeBack>
            <!-- BeInstantRecover -->
            <Arg> bool </Arg>
        </OnMsgGazeBack>

        <OnMsgGazeBackDelay>
            <!-- BeInstantRecover -->
            <Arg> bool </Arg>
            <Arg> float </Arg>
        </OnMsgGazeBackDelay>

        <OnMsgPlayEntityEffect>
            <!-- 1 uniqueID -->
            <Arg>string</Arg>
            <!-- 2 effectID -->
            <Arg>int</Arg>
            <!-- 3 delayTime -->
            <Arg>float</Arg>
            <!-- 4 duration -->
            <Arg>float</Arg>
        </OnMsgPlayEntityEffect>

        <OnMsgPlayEntityAudio>
            <!-- 1 AudioPath -->
            <Arg>string</Arg>
        </OnMsgPlayEntityAudio>

        <OnMsgDelayPlayEntityAudio>
            <!-- 1 AudioPath -->
            <Arg>string</Arg>
            <Arg>float</Arg>
        </OnMsgDelayPlayEntityAudio>

        <OnMsgDelayStopEntityAudio>
            <!-- 1 AudioPath -->
            <Arg>string</Arg>
            <Arg>float</Arg>
        </OnMsgDelayStopEntityAudio>


        <OnMsgStopEntityAudio>
            <!-- 1 AudioPath -->
            <Arg>string</Arg>
        </OnMsgStopEntityAudio>


        <OnMsgRemoveEntityEffect>
            <!-- 1 uniqueID -->
            <Arg>string</Arg>
        </OnMsgRemoveEntityEffect>


    </ClientMethods>

    <ServerMethods>

    </ServerMethods>
</root>