<?xml version="1.0" ?>
<root>
    <GLOBAL_EMPTY_TABLE Type="dict" Key="int" Value="int" />  <!--其他属性不要用GLOBAL_EMPTY_TABLE这个类型-->

    <BOOL Type="bool"/>
    <UINT Type="int"/>
    <INT Type="int"/>
    <GBID Type="UINT" />
    <NUID Type="string" />
    <UUID Type="NUID" />
    <ENTITY_ID Type="string"/>
    <SPACE_ID Type="int"/>
    <WORLD_ID Type="int"/>
    <SCENE_ID Type="int"/>
    <OBJECT_ID Type="UINT" />
    <number Type="int"/>
    <LONGTIME Type="int" />
    <STATUS Type="UINT" />
    <SCHOOL Type="STATUS" />
    <ROLENAME Type="string" />
    <PID Type="string"/>
    <LEVEL Type="UINT" />
    <table Type="dict" Key="UINT" Value="none" />
    <KEY_NUMBER Type="dict" Key="UINT" Value="number" />
    <UINT_MAP Type="dict" Key="UINT" Value="UINT" />
    <INT_MAP Type="dict" Key="int" Value="int" />
    <ARRAY_BOOL Type="list" Element="BOOL" />
    <ARRAY_NUMBER Type="list" Element="int" />
    <ARRAY_UINT Type="list" Element="UINT" />
    <ARRAY_STRING Type="list" Element="string" />
    <ARRAY_ENTITY_ID Type="list" Element="ENTITY_ID" />
    <ARRAY_ARRAY_UINT Type="list" Element="ARRAY_UINT" />
    <BIT_FLAGS Type="dict" Key="OBJECT_ID" Value="int"/>
    <FloatList Type="list" Element="float"/>
    <IntList Type="list" Element="int"/>
    <StringList Type="list" Element="string"/>
    <DictIntInt Type="dict" Key="int" Value="int"/>
    <DictIntUInt Type="dict" Key="int" Value="UINT"/>
    <DictIntFloat Type="dict" Key="int" Value="float"/>
    <DictIntBool Type="dict" Key="int" Value="bool"/>
    <DictIntStr Type="dict" Key="int" Value="string"/>
    <DictStrFloat Type="dict" Key="string" Value="float"/>
    <DictIntDictIntBool Type="dict" Key="int" Value="DictIntBool"/>
    <DictIntDictIntInt Type="dict" Key="int" Value="DictIntInt"/>
    <GroupBlockVoices Type="dict" Key="string" Value="bool"/>
    <DictStrBool Type="dict" Key="string" Value="bool"/>
    <DictStrInt Type="dict" Key="string" Value="int"/>
    <DictStrStr Type="dict" Key="string" Value="string"/>
    <DictStrUInt Type="dict" Key="string" Value="UINT"/>
    <DictStrDictStrUInt Type="dict" Key="string" Value="DictStrUInt"/>
    <DictStrNone Type="dict" Key="string" Value="none"/>
    <DictAttr2 Type="dict" Key="int" Value="string"/>
    <DictNoneInt Type="dict" Key="none" Value="int"/>
    <ListFloat Type="list" Element="float"/>
    <ListInt Type="list" Element="int"/>
    <ListStr Type="list" Element="string"/>
    <ListListStr Type="list" Element="ListStr"/>
	<ListDictInt Type="list" Element="DictIntInt"/>
    <MailBoxList Type="list" Element="mailbox"/>
    <MailBoxDict Type="dict" Key="string" Value="MailBoxList"/>
    <MailBoxOnlyDict Type="dict" Key="mailbox" Value="UINT"/>
    <ListAttr1 Type="list" Element="int"/>
    <GUILD_ID Type="string"/>
	<GUILD_ID_INT Type="UINT" />
    <GUILD_ROLE Type="UINT" />  <!--公会职位-->
    <METHOD_NAME Type="string" />
    <METHOD_ARGS Type="dict" Key="none" Value="none"/>
    <ENTITY_ID_UINT Type="dict" Key="ENTITY_ID" Value="UINT" />
    <ENTITY_ID_BOOL Type="dict" Key="ENTITY_ID" Value="BOOL" />
    <ENTITY_ID_TIME Type="dict" Key="ENTITY_ID" Value="LONGTIME"/>
    <ENTITY_ID_FLOAT Type="dict" Key="ENTITY_ID" Value="float" />
    <UINT_ENTITY_ID Type="dict" Key="UINT" Value="ENTITY_ID"/>
    <SCHOOL_2_ROLENAME Type="dict" Key="SCHOOL" Value="ROLENAME" />
    <MARK_MAP Type="dict" Key="none" Value="BOOL">
    </MARK_MAP>
    <COLOR Type="tuple">
        <R Type="float" />
        <G Type="float" />
        <B Type="float" />
        <A Type="float" />
    </COLOR>

    <INSTANCE_ID Type="string"/>
    <TEAM_ID Type="int"/>
    <GROUP_ID Type="int"/>    
    <TEAROOM_ID Type="int"/>
    <LIST_TEAM_ID Type="list" Element="TEAM_ID"/>    
    <LIST_GROUP_ID Type="list" Element="GROUP_ID"/>

    <TIMER_INFO Type="dict" Key="int" Value="none" />
    <KEY_LONGTIME Type="dict" Key="UINT" Value="LONGTIME" />
    <KEY_BOOL Type="dict" Key="UINT" Value="BOOL" />
    <Vector3 Type="tuple">
        <x Type="float" Flags="SERVER_ONLY" Default="0.0"/>
        <y Type="float" Flags="SERVER_ONLY" Default="0.0"/>
        <z Type="float" Flags="SERVER_ONLY" Default="0.0"/>
    </Vector3>
    <TeamVector3 Type="tuple">
        <x Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <y Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <z Type="float" Flags="OWN_CLIENT" Default="0.0"/>
    </TeamVector3>

    <PVector2 Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </PVector2>
    <PVector2List Type="list" Element="PVector2"/>
    <PVector3 Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Z Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </PVector3>

    <PVector4 Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Z Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <W Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </PVector4>
    <PRotator Type="tuple">
        <Pitch Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Yaw Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Roll Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </PRotator>
    <PQuat Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Z Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <W Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </PQuat>
    <PTransform Type="tuple">
        <Rotation Type="PQuat" Flags="ALL_CLIENTS"/>
        <Translation Type="PVector3" Flags="ALL_CLIENTS"/>
        <Scale3D Type="PVector3" Flags="ALL_CLIENTS"/>
    </PTransform>

    <SWITCH_SYNC_MAP Type="dict" Key="int" Value="string"/>
    <SWITCH_SYNC_MAP_BY_SERVER Type="dict" Key="int" Value="SWITCH_SYNC_MAP"/>
    <SWITCH_UPDATE_MAP Type="dict" Key="string" Value="string"/>

    <Result Type="tuple">
        <Code Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Trace Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <Content Type="string" Flags="ALL_CLIENTS" Default="nil"/>
    </Result>

    <CtxParamTable Type="tuple">
        <OwnerID Type="string" Flags="ALL_CLIENTS"/>
        <ConfigID Type="int" Flags="ALL_CLIENTS"/>
    </CtxParamTable>

    <MSCallBack Type="tuple">
        <callTime Type="int" Flags="SERVER_ONLY"/>
        <timeout Type="int" Flags="SERVER_ONLY"/>
        <callback Type="string" Flags="SERVER_ONLY"/>
		<userdata Type="none" Flags="SERVER_ONLY"/>
        <serviceId Type="string" Flags="SERVER_ONLY"/>
        <serviceName Type="string" Flags="SERVER_ONLY"/>
        <shardIndex Type="int" Flags="SERVER_ONLY" Default="-1"/>
        <logicServerID Type="int" Flags="SERVER_ONLY"/>
    </MSCallBack>
    <MMSCallBack Type="tuple">
        <callbackInfo Type="none" Flags="SERVER_ONLY"/>
        <callbackCount Type="int" Flags="SERVER_ONLY"/>
        <callbackMaxCount Type="int" Flags="SERVER_ONLY"/>
        <callback Type="string" Flags="SERVER_ONLY"/>
		<userdata Type="none" Flags="SERVER_ONLY"/>
        <timeoutTimerID Type="int" Flags="SERVER_ONLY"/>
    </MMSCallBack>

    <IntCampOverrideInfo Type="tuple">
        <SelfValue Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <TargetValue Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <OverrideRelation Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Priority Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Type Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </IntCampOverrideInfo>
    <StringCampOverrideInfo Type="tuple">
        <SelfValue Type="string" Flags="ALL_CLIENTS" Default="0"/>
        <TargetValue Type="string" Flags="ALL_CLIENTS" Default="0"/>
        <OverrideRelation Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Priority Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Type Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </StringCampOverrideInfo>

    <LoginArg Type="tuple">
        <os Type="string" Flags="SERVER_ONLY" Default=""/>
        <serverId Type="string" Flags="SERVER_ONLY" Default=""/>
        <clientVersion Type="string" Flags="SERVER_ONLY" Default=""/>
        <channel Type="string" Flags="SERVER_ONLY" Default=""/>
        <packageChannel Type="string" Flags="SERVER_ONLY" Default=""/>
        <deviceId Type="string" Flags="SERVER_ONLY" Default=""/>
        <model Type="string" Flags="SERVER_ONLY" Default=""/>
        <ip Type="string" Flags="SERVER_ONLY" Default=""/>
        <timeOffset Type="string" Flags="SERVER_ONLY" Default=""/>
        <country Type="string" Flags="SERVER_ONLY" Default=""/>
        <province Type="string" Flags="SERVER_ONLY" Default=""/>
        <city Type="string" Flags="SERVER_ONLY" Default=""/>
        <patchP4Version Type="int" Flags="SERVER_ONLY" Default="0"/>
    </LoginArg>

    <ActorSkillEntryInfo Type="tuple">
        <SkillID Type="int" Flags="OWN_CLIENT" Default="0" />
        <SkillLvl Type="int" Flags="OWN_CLIENT" Default="0" />
        <SkillSlot Type="int" Flags="OWN_CLIENT" Default="0" />
        <SkillUnlocked Type="int" Flags="OWN_CLIENT" Default="0" />
    </ActorSkillEntryInfo>
    <ActorSkillDict Type="dict" Key="int" Value="ActorSkillEntryInfo"/>

    <UnlockedSkillEntryInfo ImplClass="UnlockedSkillEntryInfo" Type="tuple">
        <SkillID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <SkillLvl Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <SkillExtraLvl Type="int" Flags="OWN_CLIENT" Default="0" Persistent="false"/>
    </UnlockedSkillEntryInfo>
    <UnlockedSkillDict ImplClass="UnlockedSkillDict" Type="dict" Key="int" Value="UnlockedSkillEntryInfo"/>

    <SkillSchemeEntryInfo Type="tuple">
        <SchemeID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <DefaultSchemeTid Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>   <!--已迭代 待删除 TODO-->
        <CustomName Type="string" Flags="OWN_CLIENT" Default="" Persistent="true"/>
        <SkillSlotDict Type="dict" Key="int" Value="int" Flags="OWN_CLIENT" Persistent="true"/>
        <PassiveSkillSlotDict Type="dict" Key="int" Value="int" Flags="OWN_CLIENT" Persistent="true"/>
    </SkillSchemeEntryInfo>
    <SkillSchemeDict Type="dict" Key="int" Value="SkillSchemeEntryInfo"/>

    <PredictMoveParams Type="tuple">
        <tickCount Type="int" Flags="SERVER_ONLY" Default="0"/>
        <moveSyncType Type="int" Flags="SERVER_ONLY" Default="0"/>
        <moveMode Type="int" Flags="SERVER_ONLY" Default="0"/>
        <currentLocation Type="Vector3" Flags="SERVER_ONLY"/>
        <predictRotation Type="float" Flags="SERVER_ONLY" Default="0.0"/>
        <acceleration Type="Vector3" Flags="SERVER_ONLY"/>
        <velocity Type="Vector3" Flags="SERVER_ONLY"/>
        <extraMask Type="int" Flags="SERVER_ONLY" Default="0"/>
        <spaceId Type="string" Flags="SERVER_ONLY" Default=""/>
    </PredictMoveParams>

    <IdleParams Type="tuple">
        <idleTime Type="float" Flags="SERVER_ONLY" Default="0.0"/>
    </IdleParams>
    <JumpParams Type="tuple">
        <startLocation Type="Vector3" Flags="SERVER_ONLY"/>
        <jumpFace Type="Vector3" Flags="SERVER_ONLY"/>
    </JumpParams>
    <AOIStateData Type="tuple">
        <state Type="int" Flags="SERVER_ONLY" Default="0"/>
        <idleData Type="IdleParams" Flags="SERVER_ONLY"/>
        <jumpData Type="JumpParams" Flags="SERVER_ONLY"/>
    </AOIStateData>

    <AttributeIntList Type="list" Element="int"/>
    <AttrResumeRowInfo Type="tuple">
        <TargetAttrName Type="string" Flags="SERVER_ONLY" Default=""/>
        <SourceAttrName Type="string" Flags="SERVER_ONLY" Default=""/>
        <ResumeType Type="int" Flags="SERVER_ONLY" Default="0"/>
        <Value Type="float" Flags="SERVER_ONLY" Default="0.0"/>
    </AttrResumeRowInfo>
    <AttrResumeInfo Type="list" Element="AttrResumeRowInfo"/>
    <AvatarActorBagBatchResolveList Type="list" Element="string"/>

    <AvataSingleMatchResultInfo Type="tuple">
        <teamID Type="TEAM_ID" Flags="SERVER_ONLY" Default="0"/>
        <target Type="int" Flags="SERVER_ONLY" Default="0"/>
        <matchStartTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <isEnteringTeam Type="bool" Flags="SERVER_ONLY" Default="false"/>  <!-- 匹配成功正在加入队伍 -->
    </AvataSingleMatchResultInfo>

    <AvataTeamMatchInfo Type="tuple">
        <id Type="string" Flags="SERVER_ONLY" Default=""/>
        <maxZhanli Type="int" Flags="SERVER_ONLY" Default="0"/>
        <position Type="int" Flags="SERVER_ONLY" Default="0"/>
        <name Type="string" Flags="SERVER_ONLY" Default=""/>
        <mailbox Type="mailbox" Flags="SERVER_ONLY"/>
    </AvataTeamMatchInfo>
    <TeamMatchMemberInfo Type="tuple">
        <id Type="string" Flags="SERVER_ONLY" Default=""/>
        <name Type="string" Flags="SERVER_ONLY" Default=""/>
        <profession Type="int" Flags="SERVER_ONLY" Default="0"/>
        <position Type="int" Flags="SERVER_ONLY" Default="0"/>
        <zhanli Type="int" Flags="SERVER_ONLY" Default="0"/>
        <level Type="int" Flags="SERVER_ONLY" Default="0"/>
        <isCaptain Type="int" Flags="SERVER_ONLY" Default="0"/>
    </TeamMatchMemberInfo>
    <TeamMatchInfo Type="tuple">
        <id Type="TEAM_ID" Flags="SERVER_ONLY" Default="0"/>
        <targetID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <mailbox Type="mailbox" Flags="SERVER_ONLY"/>
        <count Type="int" Flags="SERVER_ONLY" Default="0"/>
        <members Type="list" Element="TeamMatchMemberInfo"/>
    </TeamMatchInfo>

    <AvatarActorTeamMemberInfo Type="tuple">
        <memberID Type="string" Flags="OWN_CLIENT" Default=""/>
        <level Type="int" Flags="OWN_CLIENT"/>
        <profession Type="int" Flags="OWN_CLIENT"/>
    </AvatarActorTeamMemberInfo>
    <AvatarActorTeamMemberInfoList Type="list" Element="AvatarActorTeamMemberInfo"/>
    <AvatarActorInviteInfo Type="tuple">
        <teamID Type="TEAM_ID" Flags="SERVER_ONLY" Default="0"/>
        <captainID Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainName Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainProfession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <captainLevel Type="int" Flags="OWN_CLIENT" Default="0"/>
        <inviterID Type="string" Flags="OWN_CLIENT" Default=""/>
        <teamMemberInfo Type="AvatarActorTeamMemberInfoList" Flags="OWN_CLIENT"/>
        <initiatorID Type="string" Flags="SERVER_ONLY" Default=""/>
        <initiatorName Type="string" Flags="OWN_CLIENT" Default=""/>
        <createTime Type="int" Flags="OWN_CLIENT" Default="0"/>
        <targetID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <positionNeed Type="ListAttr1" Flags="OWN_CLIENT" Default="{}"/>
        <zhanliNeed Type="int" Flags="OWN_CLIENT" Default="0"/>
        <description Type="string" Flags="OWN_CLIENT" Default=""/>
        <isInActivity Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <logicServerID Type="int" Flags="OWN_CLIENT" Default="0"/>
    </AvatarActorInviteInfo>
    <AvatarActorInvitedInfoList Type="list" Element="AvatarActorInviteInfo"/>
    <AvatarActorSingleMatchInfo Type="tuple">
        <singleTargetID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <matchStartTime Type="int" Flags="OWN_CLIENT" Default="0"/>
    </AvatarActorSingleMatchInfo>
    <AvatarActorSingleMatchInfoList Type="list" Element="AvatarActorSingleMatchInfo"/>
    <AvatarActorTeamSpaceInfo Type="tuple">
        <isInDungeon Type="int" Default="0"/>
        <mapInstID Type="string" Default=""/>
        <mapInstType Type="int" Default="0"/>
        <worldID Type="int" Default="0"/>
        <worldType Type="int" Default="0"/>
    </AvatarActorTeamSpaceInfo>
    <TeamApplyInfo Type="tuple">
        <id Type="string" Flags="OWN_CLIENT" Default=""/>
        <name Type="string" Flags="OWN_CLIENT" Default=""/>
        <profession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <level Type="int" Flags="OWN_CLIENT" Default="0"/>
        <applicateTime Type="int" Flags="OWN_CLIENT" Default="0"/>
        <zhanli Type="int" Flags="OWN_CLIENT" Default="0"/>
        <mailbox Type="mailbox" Flags="SERVER_ONLY" Default=""/>
        <description Type="string" Flags="OWN_CLIENT" Default=""/>
        <isPreApply Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <reason Type="int" Flags="SERVER_ONLY" />
        <logicServerID Type="int" Flags="SERVER_ONLY" />
        <isBot Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <bIsRescue Type="bool" Flags="SERVER_ONLY" Default="false"/>
    </TeamApplyInfo>
    <AvatarActorTeamApplicatorList Type="dict" Key="string" Value="TeamApplyInfo"/>
    <AvatarActorTeamCombineInfo Type="tuple">
        <teamID Type="TEAM_ID" Flags="OWN_CLIENT" Default="0"/>
        <captainID Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainName Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainProfession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <captainLevel Type="int" Flags="OWN_CLIENT" Default="0"/>
        <memberID Type="string" Flags="OWN_CLIENT" Default=""/>
        <memberSize Type="int" Flags="OWN_CLIENT" Default="0"/>
        <target Type="int" Flags="OWN_CLIENT" Default="0"/>
        <targetID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <applicateTime Type="int" Flags="OWN_CLIENT" Default="0"/>
        <teamMemberInfo Type="AvatarActorTeamMemberInfoList" Flags="OWN_CLIENT"/>
        <positionNeed Type="ListAttr1" Flags="OWN_CLIENT" />
    </AvatarActorTeamCombineInfo>
    <AvatarActorTeamCombineList Type="dict" Key="string" Value="AvatarActorTeamCombineInfo"/>
    <AvatarActorTeamInviteApplyCombineInfo Type="tuple">
        <InviteInfo Type="AvatarActorInviteInfo" Flags="SERVER_ONLY" Default="nil"/>
        <ApplicatorInfo Type="TeamApplyInfo" Flags="SERVER_ONLY" Default="nil"/>
        <CombineInfo Type="AvatarActorTeamCombineInfo" Flags="SERVER_ONLY" Default="nil"/>
    </AvatarActorTeamInviteApplyCombineInfo>
    <AvatarActorTeamCollectList Type="list" Element="int"/>
    <AvatarActorTeamPositionNeedList Type="list" Element="int"/>
    <AvatarActorTeamInfo Type="tuple">
        <id Type="string" Flags="OWN_CLIENT" Default=""/>
        <botID Type="int" Flags="OWN_CLIENT" Default="nil"/>
        <name Type="string" Flags="OWN_CLIENT" Default=""/>
        <shortUid Type="int" Flags="OWN_CLIENT" />
        <sex Type="int" Flags="OWN_CLIENT" Default="0"/>
        <profession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <level Type="int" Flags="OWN_CLIENT" Default="0"/>
        <isCaptain Type="int" Flags="OWN_CLIENT" Default="0"/>
        <isOnline Type="int" Flags="OWN_CLIENT" Default="1"/>
        <hp Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <maxHp Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <shieldAll Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <isDead Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <mailbox Type="mailbox" Flags="SERVER_ONLY" Default=""/>
        <location Type="TeamVector3" Flags="OWN_CLIENT"/>
        <mapInstID Type="string" Flags="OWN_CLIENT" Default="0"/>
        <mapInstType Type="int" Flags="OWN_CLIENT" Default="0"/>
        <worldID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <planeID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <planeType Type="int" Flags="OWN_CLIENT" Default="0"/>
        <isInDungeon Type="int" Flags="OWN_CLIENT" Default="0"/>
        <enterTime Type="int" Flags="OWN_CLIENT" Default="0"/>
        <bInBattle Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <bFollowing Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <bTargetByBoss Type="bool" Flags="OWN_CLIENT" Default="false"/>
        <combat3V3Lock Type="bool" Default="false"/>
        <dungeonLock Type="bool" Default="false"/>
        <voiceState Type="int" Flags="OWN_CLIENT" Default="1"/>
        <memberFlag Type="int" Flags="OWN_CLIENT" Default="0"/>
        <reason Type="int" Flags="SERVER_ONLY" />
        <logicServerID Type="int" Flags="SERVER_ONLY" />
        <team3v3RankId Type="int" Flags="OWN_CLIENT" Default="0"/>
        <team5v5RankId Type="int" Flags="OWN_CLIENT" Default="0"/>
        <zhanli Type="int" Flags="OWN_CLIENT" Default="0"/>
        <guildId Type="string" Flags="OWN_CLIENT" Default=""/>
        <professionStateID Type="int" Flags="OWN_CLIENT" Default="1"/>
        <teamJoinAnimation Type="int" Flags="OWN_CLIENT" Default= "nil"/>	<!-- 队伍加入动效 -->
		<teamNameplate Type="int" Flags="OWN_CLIENT" Default= "nil"/>		<!-- 组队名字底框 -->
    </AvatarActorTeamInfo>
    <AvatarActorTeamInfoList Type="dict" Key="string" Value="AvatarActorTeamInfo"/>
    <AvatarActorTeamFullInfo Type="tuple">
        <teamID Type="TEAM_ID" Flags="OWN_CLIENT" Default="0"/>
        <teamIntID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <targetID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <captainID Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainName Type="string" Flags="OWN_CLIENT" Default=""/>
        <captainProfession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <captainLevel Type="int" Flags="OWN_CLIENT" Default="0"/>
        <captainMailbox Type="mailbox" Flags="OWN_CLIENT" Default="0"/>
        <initiatorID Type="string" Flags="OWN_CLIENT" Default=""/>
        <professionNeed Type="AvatarActorTeamPositionNeedList" Flags="OWN_CLIENT" Default="{}"/>
        <createTime Type="int" Flags="OWN_CLIENT" Default="0"/>
        <teamMemberIDList Type="AvatarActorTeamInfoList" Flags="OWN_CLIENT"/>
        <memberSize Type="int" Flags="OWN_CLIENT" Default="0"/>
        <zhanliNeed Type="int" Flags="OWN_CLIENT" Default="0"/>
    </AvatarActorTeamFullInfo>
    <InviteCDInfo Type="tuple"> <!--邀请CD信息-->
        <expirationDuration Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!--被拒绝cd过期时间-->
        <refuseTimes Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!--拒绝次数-->
        <ignoreTime Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!--忽略时间-->
        <inviteTime Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!-- 邀请开始时间-->
        <bIsMax Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="true"/> <!--是否达到最大次数-->
    </InviteCDInfo>

    <recentlyTeamMemberInfo Type="tuple">
        <recentlyTeamMembers Type="DictStrInt" Flags="OWN_CLIENT" Persistent="true"/>
        <innerCount Type="int" Flags="SERVER_ONLY" Default="1" Persistent="true"/>
    </recentlyTeamMemberInfo>

    <spaceFlagInfo Type="tuple">
        <spaceId Type="string" Default="nil" Flags="OWN_CLIENT"/>
        <pos Type="PVector3" Default="nil" Flags="OWN_CLIENT"/>
    </spaceFlagInfo>
    <AvatarActorSpaceFlag Type="dict" Key="int" Value="spaceFlagInfo" Flags="OWN_CLIENT"/>

    <AvatarActorBasicInfo Type="tuple">
        <id Type="string" Flags="SERVER_ONLY"/>
        <name Type="string" Flags="SERVER_ONLY"/>
        <profession Type="int" Flags="SERVER_ONLY"/>
    </AvatarActorBasicInfo>

    <SYNC_PALYER_FREQUENT_INFO Type="tuple">
        <state Type="int" />
        <teamId Type="TEAM_ID" />
        <rolename Type="string" />
        <school Type="SCHOOL" />
        <lv Type="int" />
        <teamMemberSize Type="UINT" />
        <groupMemberSize Type="UINT" />
    </SYNC_PALYER_FREQUENT_INFO>
    <SYNC_PLAYER_FREQUENT_MAP Type="dict" Key="ENTITY_ID" Value="SYNC_PALYER_FREQUENT_INFO" />

    <ACHIEVEMENT_TYPE_INFO Type="tuple">
        <token Type="int" Flags="OWN_INITIAL_ONLY" Default="0" Persistent="true"/>       <!--当前总积分-->
        <rewardFlag Type="int" Flags="OWN_INITIAL_ONLY" Default="0" Persistent="true"/>   <!--位标识，哪些等级已领取了奖励-->
    </ACHIEVEMENT_TYPE_INFO>
    <ACHIEVEMENT_TYPE_INFO_MAP Type="dict" Key="int" Value="ACHIEVEMENT_TYPE_INFO"/>

    <LoginDebugInfo Type="tuple">
        <serverVersion Type="string" Flags="OWN_CLIENT" Default=""/>
        <clientVersion Type="string" Flags="OWN_CLIENT" Default=""/>
        <minVersion Type="string" Flags="OWN_CLIENT" Default=""/>
        <maxVersion Type="string" Flags="OWN_CLIENT" Default=""/>
        <processName Type="string" Flags="OWN_CLIENT" Default=""/>
    </LoginDebugInfo>

    <TaskTargetItem Type="tuple">
        <Count Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <Ok Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
    </TaskTargetItem>
    <TaskTargetList Type="dict" Key="int" Value="TaskTargetItem"/>
    <TaskListItem Type="tuple">
        <TaskID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <Status Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <Targets Type="TaskTargetList" Flags="OWN_CLIENT" Persistent="true"/>
        <CreatedAt Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <StartAt Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <FailCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
    </TaskListItem>
    <AvatarActorTasks Type="dict" Key="int" Value="TaskListItem"/>
    <!-- ID/ByTaskIndex 后续废弃 -->
    <TASK_SPAWN_LIST_ITEM Type="tuple">
        <ID Type="string" Flags="SERVER_ONLY" Default="" Persistent="true" />
        <ByTaskID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <ByTaskIndex Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <SpawnType Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
    </TASK_SPAWN_LIST_ITEM>
    <TASK_SPAWN_LIST Type="dict" Key="string" Value="TASK_SPAWN_LIST_ITEM" Flags="SERVER_ONLY" Persistent="true"/>
    <TASK_TALK_LIST_ITEM Type="tuple">
        <SpawmID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
    </TASK_TALK_LIST_ITEM>

    <TASK_DIALOGID_LIST_MAP Type="dict" Key="int" Value="int"/>

    <TASK_ACTION_MARK_LIST_ITEM Type="tuple">
        <eventType Type="int" Flags="SERVER_ONLY"  Persistent="true" />
        <eventUniqueID Type="string" Flags="SERVER_ONLY" Persistent="true" />
    </TASK_ACTION_MARK_LIST_ITEM>

    <TASK_EVENT_FLOW_ITEM Type="tuple">
        <eventIdx Type="int" Flags="SERVER_ONLY"  Persistent="true" />
        <eventType Type="int" Flags="SERVER_ONLY" Persistent="true" />
        <eventStatus Type="int" Flags="SERVER_ONLY" Persistent="true" />
        <eventFinishNumber Type="int" Flags="SERVER_ONLY" Persistent="true" />
        <eventNowUniqueID Type="string" Flags="SERVER_ONLY" Persistent="true"/>
    </TASK_EVENT_FLOW_ITEM>

    <TASK_FLOW_LIST_ITEM Type="dict" Key="int" Value="TASK_EVENT_FLOW_ITEM" Flags="SERVER_ONLY" Persistent="true"/>

    <TASK_SPAWN_MANAGER_LIST Type="dict" Key="string" Value="bool" Flags="SERVER_ONLY" Persistent="true"/>
    <TASK_ACTION_MARK_LIST Type="dict"  Key="string" Value="TASK_ACTION_MARK_LIST_ITEM" Flags="SERVER_ONLY"  Persistent="true"/>

    <QUEST_SPAWN_LIST_ITEM Type="tuple">
        <CreateQuestID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <CreateType Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
    </QUEST_SPAWN_LIST_ITEM>
    <QUEST_SPAWN_LIST Type="dict" Key="string" Value="QUEST_SPAWN_LIST_ITEM" Flags="SERVER_ONLY" Persistent="true"/>

    <!-- 寻找正确NPC存储数据，甲哥增加的，后续看一下逻辑是否能够优化或去掉 -->
    <QUEST_EXTRA_DATA_ITEM Type="tuple">
        <QuestID Type="int" Persistent="true" />
        <Idx Type="int" Persistent="true" />
        <AutoRemove Type="bool" Persistent="true" />
        <DataType Type="int" Persistent="true" />
        <FindCorrentNpc Type="dict" Key="string" Value="int" Persistent="true" />
    </QUEST_EXTRA_DATA_ITEM>

    <QUEST_JUMP_EXTRA_DATA Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true"/>

    <QuestTargetInfo Type="tuple">
        <FinishCount Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <IsFinished Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="true"/>
    </QuestTargetInfo>
    <QuestTargetIndexMap Type="dict" Key="int" Value="QuestTargetInfo" Flags="SERVER_ONLY" Persistent="true"/>

    <QuestInfo Type="tuple">
        <NowRingQuestID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <RingStatus Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <NowQuestTargets Type="QuestTargetIndexMap" Flags="SERVER_ONLY" Persistent="true"/>
        <RingStartAt Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <RingFailCount Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <RingFinishedMap Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true" />
        <RingSubTargetFinishedMap Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true" />
        <!-- 任务版本 与Ring配置中的Version进行对比 不一致时自动放弃 -->
        <Version Type="int" Flags="SERVER_ONLY" Default="nil" Persistent="true"/>
        <SavePointQuestID Type="int" Flags="SERVER_ONLY" Default="nil" Persistent="true"/>
        <NowPlaneID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <GetRewardList Type="dict" Key="int" Value="bool" Default="nil" Flags="SERVER_ONLY" Persistent="true" />
    </QuestInfo>
    <QuestList Type="dict" Key="int" Value="QuestInfo"/>

    <!-- 简化版的已完成任务Ring存盘数据 -->
    <QUEST_FINISHED_INFO Type="tuple">
        <RingStartAt Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <RingFinishedMap Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true" />
        <RingSubTargetFinishedMap Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true" />
    </QUEST_FINISHED_INFO>
    <QUEST_FINISHED_INFO_DICT Type="dict" Key="int" Value="QUEST_FINISHED_INFO"/>


    <TASK_PLANE_CLIMITEID_ITEM Type="tuple">
        <PlaneID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <ClimateID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
    </TASK_PLANE_CLIMITEID_ITEM>
    <TASK_ACTION_DATA_ITEM Type="tuple">
        <TaskID Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <EventType Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <EventIdx Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <Status Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <UniqueID Type="string" Flags="OWN_CLIENT" Default="" Persistent="true"/>
    </TASK_ACTION_DATA_ITEM>

    <BLACK_BG_WORD_INFO Type="tuple">
        <Desc Type="string" Flags="SERVER_ONLY" Default="" />
        <Delay Type="float" Flags="SERVER_ONLY" Default="0"/>
    </BLACK_BG_WORD_INFO>

    <BLACK_BG_WORD_INFO_LIST Type="list" Element="BLACK_BG_WORD_INFO"/>

    <TASK_ACTIONS_DICT Type="dict" Key="string" Value="TASK_ACTION_DATA_ITEM" Flags="SERVER_ONLY" Persistent="true"/>

    <TaskActionDataItemList Type="list" Element="TASK_ACTION_DATA_ITEM"/>

    <TASK_SET_SCENE_TIME_ITEM Type="tuple">
        <Hour Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <Minute Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
    </TASK_SET_SCENE_TIME_ITEM>



    <TASK_CREATE_SPAWN_ITEM Type="tuple">
        <SpawnType Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <SpawnID Type="string" Flags="SERVER_ONLY" Default="" Persistent="true" />
    </TASK_CREATE_SPAWN_ITEM>

    <TaskCreateSpawnList Type="list" Element="TASK_CREATE_SPAWN_ITEM"/>
    <EntityIdList Type="list" Element="string"/>

    <SectionInfo Type="tuple" Persistent="true">
        <RunTime Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <LoopTime Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
    </SectionInfo>
    <SectionInfoDict Type="dict" Key="int" Value="SectionInfo" Persistent="true"/>
    <TaskRTInfo Type="tuple" Persistent="true">
        <ID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <RunTime Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
    </TaskRTInfo>
    <TaskRTInfoList Type="list" Element="TaskRTInfo" Persistent="true"/>

    <TASK_SNAPSHOT_ITEM_INST Type="tuple" Persistent="true">
        <RingID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <TaskID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />
        <Info Type="string" Flags="SERVER_ONLY" Default="" Persistent="true" />
        <Data Type="string" Flags="SERVER_ONLY" Default="" Persistent="true" />
        <IsPublic Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="true" />
        <InstanceID Type="string" Flags="SERVER_ONLY" Default="nil" Persistent="true" />
    </TASK_SNAPSHOT_ITEM_INST>
    <TASK_SNAPSHOT_ITEM_TASK Type="dict" Key="string" Value="TASK_SNAPSHOT_ITEM_INST"  Flags="SERVER_ONLY" Persistent="true" />
    <TASK_SNAPSHOT_ITEM_RING Type="dict" Key="int" Value="TASK_SNAPSHOT_ITEM_TASK"  Flags="SERVER_ONLY" Persistent="true" />
    <TASK_SNAPSHOT_ITEM_PLANE Type="dict" Key="int" Value="TASK_SNAPSHOT_ITEM_RING"  Flags="SERVER_ONLY" Persistent="true" />
	
    <SkillCastExtraClientData Type="tuple">
        <LockPartIDs Type="EntityIdList" Flags="ALL_CLIENTS"/>
        <Transform Type="PTransform" Flags="ALL_CLIENTS"/>
    </SkillCastExtraClientData>

    <LocoGroupExtraClientData Type="tuple">
        <WaterHeight Type="float" Flags="OWN_CLIENT" />
    </LocoGroupExtraClientData>

    <DiceCheckExtraParam Type="tuple">
        <Type Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <InsID Type="string" Default="nil" Flags="SERVER_ONLY"/>
    </DiceCheckExtraParam>

    <DICE_RESULT_CACHE Type="tuple">
        <canReDice Type="bool" Default="false" Flags="SERVER_ONLY"/>
        <opNUID Type="string" Default="string" Flags="SERVER_ONLY"/>
        <diceID Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <diceResult Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <finalValue Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <extraParam Type="DiceCheckExtraParam" Default="nil" Flags="SERVER_ONLY"/>
        <context Type="none" Default="nil" Flags="SERVER_ONLY"/>
    </DICE_RESULT_CACHE>

    <DICE_BUFF_EFFECT Type="tuple">
        <minValue Type="int" Default="0" Flags="SERVER_ONLY" Persistent="false"/>
        <maxValue Type="int" Default="0" Flags="SERVER_ONLY" Persistent="false"/>
    </DICE_BUFF_EFFECT>
    <DICE_BUFF_INFO Type="tuple">
        <diceBuffEffect Type="dict" Key="int" Value="DICE_BUFF_EFFECT" Flags="SERVER_ONLY" Persistent="false"/> <!--key: diceCheckProperty-->
        <buffLayer Type="int" Default="0" Flags="SERVER_ONLY" Persistent="false"/>
    </DICE_BUFF_INFO>
    <DICE_BUFF_INFO_DICT Type="dict" Key="int" Value="DICE_BUFF_INFO" Flags="SERVER_ONLY" Persistent="false"/> <!--key: diceBuffID-->

    <PROP_BUFF_INFO Type="tuple">
        <propBuffEffect Type="DictIntInt" Flags="SERVER_ONLY" Persistent="false"/><!--key: roleplay CommonProp-->
        <buffLayer Type="int" Default="0" Flags="SERVER_ONLY" Persistent="false"/>
    </PROP_BUFF_INFO>
    <PROP_BUFF_INFO_DICT Type="dict" Key="int" Value="PROP_BUFF_INFO" Flags="SERVER_ONLY" Persistent="false"/><!--key: roleplayCommonPropBuffID-->

    <DICE_RESULT_DEFINE Type="tuple">
        <diceID Type="int"/>
        <opNUID Type="string"/>
        <originValueDict Type="DictIntInt"/>
        <commonPropAddDict Type="DictIntInt"/>
        <dicePropAddDict Type="DictIntInt"/>
        <finalValue Type="int"/>
        <finalResult Type="int"/>
        <canReDice Type="bool"/>
        <passCD Type="bool"/>
        <nextTime Type="int"/>
        <extraParam Type="DiceCheckExtraParam"/>
    </DICE_RESULT_DEFINE>

    <SkillCastExtraData Type="tuple">
        <LockPartIDs Type="EntityIdList" Flags="ALL_CLIENTS"/>
        <Transform Type="PTransform" Flags="ALL_CLIENTS"/>
        <STLocation Type="PVector3" Flags="ALL_CLIENTS"/>
        <STYaw Type="float" Flags="ALL_CLIENTS"/>
        <ItemID Type="int" Flags="ALL_CLIENTS"/>
    </SkillCastExtraData>
    <SkillCastMsg Type="tuple">
        <SkillID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <InsID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Level Type="int" Flags="ALL_CLIENTS" Default="1"/>
        <Timestamp Type="int" Flags="ALL_CLIENTS" Default="0.0"/>
        <InstigatorID Type="string" Flags="ALL_CLIENTS" Default="0.0"/>
        <TriggerID Type="string" Flags="ALL_CLIENTS" Default="0.0"/>
        <ExtraData Type="SkillCastExtraData" Flags="ALL_CLIENTS"/>
    </SkillCastMsg>

    <SISyncInfo Type="tuple">
        <CastMsg Type="SkillCastMsg" Flags="ALL_CLIENTS"/>
        <SectionInfos Type="SectionInfoDict" Flags="ALL_CLIENTS"/>
        <RTaskInfos Type="TaskRTInfoList" Flags="ALL_CLIENTS"/>
        <ETaskIDs Type="IntList" Flags="ALL_CLIENTS"/>
    </SISyncInfo>
    <SISyncInfoDict Type="dict" Key="int" Value="SISyncInfo"/>

    <SkillCoolDownMsg Type="tuple">
        <SkillID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <CurCoolDown Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <TotalCoolDown Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <CurPenaltyCoolDown Type="float" Flags="OWN_CLIENT" Default="0.0"/>
        <CurRechargeTimes Type="int" Flags="OWN_CLIENT" Default="0"/>
        <TotalRechargeTimes Type="int" Flags="OWN_CLIENT" Default="0"/>
        <Timestamp Type="int" Flags="OWN_CLIENT" Default="0"/>
    </SkillCoolDownMsg>

    <SkillCDInfo Type="tuple">
        <CurCoolDown Type="float" Flags="OWN_CLIENT" Default="0.0" Persistent="true" />
        <CurPenaltyCoolDown Type="float" Flags="OWN_CLIENT" Default="0.0" Persistent="true"/>
        <CurRechargeTimes Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
    </SkillCDInfo>
    <SkillCDInfoDict Type="dict" Key="int" Value="SkillCDInfo"/>

    <SkillCDInfoNew Type="tuple">
        <CDTime Type="float" Flags="OWN_CLIENT" Default="0.0" />  <!--1.服务器用于缓存计算结果 2.客户端用于本地数据操作-->
        <CDEndTimestamp Type="int" Flags="OWN_CLIENT" Default="0" /> <!--1.服务器用于缓存计算结果 2.客户端用于本地数据操作-->
        <CDTime_N Type="float" Flags="OWN_CLIENT" Default="0.0" Persistent="true"/> <!--服务器用，客户端不用-->
        <CDTime_P Type="float" Flags="OWN_CLIENT" Default="0.0" Persistent="true"/> <!--服务器用，客户端不用-->
        <LastTriggerTimestamp Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/> <!--服务器用，客户端不用-->
    </SkillCDInfoNew>
    <SkillCDInfoDictNew Type="dict" Key="int" Value="SkillCDInfoNew"/>
    <SkillChargeInfo Type="tuple">
        <chargeNum Type="int" Flags="OWN_CLIENT" Default="0" />
        <chargeStartTimeStamp Type="int" Flags="OWN_CLIENT" Default="0" />
    </SkillChargeInfo>
    <SkillChargeInfoDict Type="dict" Key="int" Value="SkillChargeInfo"/>
    <OfflineKeepSkillInfo Type="tuple" Persistent="true">
        <keepSkillCDInfoDict Type="dict" Key="int" Value="SkillCDInfoNew" Persistent="true" Default="nil"/>
        <keepSkillGroupCDInfoDict Type="dict" Key="int" Value="SkillCDInfoNew" Persistent="true" Default="nil"/>
        <!--keepSkillChargeInfoDict Type="dict" Key="int" Value="SkillChargeInfo" Persistent="true" Default="nil" /-->
    </OfflineKeepSkillInfo>
    <SkillInst Type="tuple">
        <skillID Type="int" Default="0" />
        <insID Type="int" Default="0"/>
        <level Type="int" Default="0" />
        <startTimestamp Type="int" Default="0" />
        <lockTarget Type="int" Default="nil"/>
        <inputPos Type="PVector3" Default="nil"/>
        <inputDir Type="PVector3" Default="nil"/>
        <stateID Type="int" Default="nil"/>
    </SkillInst>
    <SkillInstDict Type="dict" Key="int" Value="SkillInst"/>
    <SkillModifyInfo Type="tuple">
        <value Type="float" Flags="SERVER_ONLY" Default="nil"/>
        <discountValue Type="float" Flags="SERVER_ONLY" Default="nil"/>
    </SkillModifyInfo>
    <SkillModifyList Type="dict" Key="int" Value="SkillModifyInfo" Flags="SERVER_ONLY" />
    <SkillModifyDict Type="dict" Key="string" Value="SkillModifyList" Flags="SERVER_ONLY" />
    <SkillOverrideInfo Type = "tuple">
        <CD Type="float" Flags="SERVER_ONLY" Default = "nil"/>
    </SkillOverrideInfo>
    <SkillOverrideDict  Type="dict" Key="int" Value="SkillOverrideInfo" />

    <BuffAddMsg Type="tuple">
        <BuffID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <InsID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <Level Type="int" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
        <Timestamp Type="int" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <TotalLife Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <CurrentLife Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <MaxLayer Type="int" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
        <Layer Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <InstigatorID Type="string" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <TriggerID Type="string" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <LifeStartTimestamp Type="int" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <SourceSkillID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
    </BuffAddMsg>

    <BISyncInfo Type="tuple">
        <AddMsg Type="BuffAddMsg" Flags="ALL_CLIENTS" Persistent="true"/>
        <SectionInfos Type="SectionInfoDict" Flags="ALL_CLIENTS" Persistent="true"/>
        <RTaskInfos Type="TaskRTInfoList" Flags="ALL_CLIENTS" Persistent="true"/>
        <ETaskIDs Type="IntList" Flags="ALL_CLIENTS" Persistent="true"/>
    </BISyncInfo>
    <BISyncInfoDict Type="dict" Key="int" Value="BISyncInfo" Persistent="true"/>

    <BuffReferenceInfo Type="tuple">
        <level Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <layer Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <totalLife Type="float" Flags="ALL_CLIENTS" Persistent="true" Default="0.0"/>
        <totalStartTimeStamp Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
    </BuffReferenceInfo>
    <BUFF_COVER_SET_PROP_MODE_C_INFO Type="tuple">
        <setValue Type="float"/>
        <operateId Type="int"/>
    </BUFF_COVER_SET_PROP_MODE_C_INFO>
    <changePropDataType Type="tuple">
        <propModifyList Type="dict" Key="string" Value="float" Default="nil"/>
        <coverSetPropModeCMList Type="dict" Key="string" Value="BUFF_COVER_SET_PROP_MODE_C_INFO" Default="nil"/>
    </changePropDataType>
    <BuffInst Type="tuple" ImplClass="Buff">
        <ownerID Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <instigatorID Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <triggerID Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <buffID Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <insID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <level Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <layer Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <totalLife Type="float" Flags="ALL_CLIENTS" Persistent="true" Default="0.0"/>
        <totalStartTimeStamp Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <initialTimeStamp Type="int" Flags="ALL_CLIENTS" Persistent="true" Default="0"/>
        <totalLifeTimer Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <effectIdList Type="IntList" Flags="SERVER_ONLY" Default="nil"/>
        <maxLayer Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0"/>
        <shieldInfoDetail Type="dict" Key="string" Value ="float" Flags="SERVER_ONLY" Default="nil"/>
        <shieldCostTotal Type="float" Flags="SERVER_ONLY" Persistent="true" Default="nil"/>
        <instigatorDistanceTimer Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <bLayerEffected Type="bool" Flags="SERVER_ONLY" Persistent="true" Default="nil"/>
        <linkedTargetId Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <rootSkillID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <rootBuffID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <changePropData Type="changePropDataType" Flags="SERVER_ONLY" Default="nil"/>
        <battleButtonID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <isDestroyed Type="bool" Flags="SERVER_ONLY" Default="nil"/>
    </BuffInst>
    <BuffOverrideInfo Type = "tuple">
        <MaxLayers Type="int" Flags="SERVER_ONLY" Default = "nil"/>
    </BuffOverrideInfo>

    <BuffTraceInfo Type = "tuple">
        <buffOwnerID Type="int" Flags="SERVER_ONLY" Default = "nil" />
        <buffID Type="int" Flags="SERVER_ONLY" Default = "nil" />
        <insID Type="int" Flags="SERVER_ONLY" Default="nil"/>
    </BuffTraceInfo>
    <BuffInstInstigatorDict Type="dict" Key="int" Value="BuffInst"/>
    <BuffInstDict Type="dict" Key="int" Value="BuffInstInstigatorDict" />
    <BuffOverrideDict Type="dict" Key="int" Value="BuffOverrideInfo" />
    <BuffTraceInfoList Type="list" Element="BuffTraceInfo"/>

    <EndureInfo Type="dict" Key="int" Value="int"/>
    <EndureInfoDict Type="dict" Key="int" Value="EndureInfo" />
    <BuffGroupInfo Type="tuple">
        <ID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <instigatorID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <priority Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <timestamp Type="int" Flags="SERVER_ONLY" Default="nil"/>
    </BuffGroupInfo>
    <BuffGroupList Type="list" Element="BuffGroupInfo"/>
    <BuffGroupDict Type="dict" Key="int" Value="BuffGroupList"/>
    <BuffSyncList Type="list" Element="BuffInst"/>
    <BuffShieldInfo Type="tuple">
        <buffID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <instigatorID Type="int" Flags="SERVER_ONLY" Default="0"/>
    </BuffShieldInfo>
    <BuffShieldList Type="list" Element="BuffShieldInfo"/>
    <TeamAuraInst Type="tuple" ImplClass="LUnitTeamAuraNEntity">
        <OwnerID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <TemplateID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <UnitLevel Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <RootSkillID Type="int" Flags="SERVER_ONLY" Default="nil"/>
    </TeamAuraInst>
    <!-- passiveskill begin -->
    <PSTriggerSkillInfo Type="tuple">
        <skillId Type="int" Flags="SERVER_ONLY" />
        <skillLevel Type="int" Flags="SERVER_ONLY" />
        <eventIndex Type="int" Flags="SERVER_ONLY" />
        <isInTrigger Type="bool" Flags="SERVER_ONLY" />
    </PSTriggerSkillInfo>
    <PSTriggerSkillInfoDict Type="dict" Key="int" Value="PSTriggerSkillInfo"/>
    <PSTriggerDict Type="dict" Key="string" Value="PSTriggerSkillInfoDict"/>

    <PSTriggeredSkillIDList Type="dict" Key="int" Value="int"/>

    <PSTriggeredBlackboardDict Type="dict" Key="int" Value="none"/>
    <!-- passiveskill end -->

    <!-- passive skill new begin -->
    <PSEffectBlackboard Type="tuple">
        <instigatorID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <level Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <skillID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <rootSkillID Type="int" Flags="SERVER_ONLY" Default="nil"/>
    </PSEffectBlackboard>
    <PSEffectIDList Type="list" Element="int" Flags="SERVER_ONLY" />
    <!-- passive skill end begin -->

    <RebuildAttrInfo Type="tuple">
        <skillInstDict Type="SkillInstDict" Default = "nil"/>
        <skillCDInfoDict Type="SkillCDInfoDictNew" Default = "nil"/>
        <skillChargeInfoDict Type="SkillChargeInfoDict" Default = "nil"/>
        <skillGroupCDDict Type="SkillCDInfoDictNew" Default = "nil"/>
        <buffInfo Type="BuffSyncList" Default = "nil"/>
    </RebuildAttrInfo>

    <!-- fightProp begin -->
    <FightPropSubSrcPartDict Type="dict" Key="string" Value="float"/>
    <FightPropSubSrcDict Type="dict" Key="int" Value="FightPropSubSrcPartDict"/>
    <!-- fightProp end -->
    <BuffStateInfo Type="tuple">
        <InstigatorIDs Type="DictIntInt" Flags="ALL_CLIENTS"/>
        <bIsActive Type="bool" Flags="ALL_CLIENTS" Default="false"/>
        <addTs Type="int" Flags="ALL_CLIENTS" Default="nil"/>
    </BuffStateInfo>

    <AvatarIDs Type="list" Element="string"/>
    <AvatarItemInfo Type="tuple">
        <Tid Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <Uid Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <StackCount Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <ExpireTime Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <UsedCount Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <IsBind Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <IsNew Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </AvatarItemInfo>
    <AvatarItemList Type="dict" Key="string" Value="AvatarItemInfo"/>
    <AvatarActorBagSetNewList Type="dict" Key="string" Value="int"/>
    <AvatarActorBagDelList Type="list" Element="string"/>

    <!-- equip begin -->
    <AvatarActorEquipmentEntryInfoPropDict Type="dict" Key="string" Value="float"/>
    <AvatarActorEquipmentEntryInfo Type="tuple">
        <entryID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <propType Type="string" Flags="ALL_CLIENTS" Default="" Persistent="true"/>
        <prop Type="AvatarActorEquipmentEntryInfoPropDict" Flags="ALL_CLIENTS" Persistent="true"/>
        <step Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <index Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <isLocked Type="bool" Flags="ALL_CLIENTS" Default="false" Persistent="true"/>
    </AvatarActorEquipmentEntryInfo>
    <AvatarActorEquipmentEntryList Type="list" Element="AvatarActorEquipmentEntryInfo"/>
    <AvatarActorEquipmentEntryOptionalEntryInfo Type="tuple">
        <index Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <entryList  Type="AvatarActorEquipmentEntryList" Flags="ALL_CLIENTS" Persistent="true"/>
    </AvatarActorEquipmentEntryOptionalEntryInfo>
    <AvatarActorEquipmentEntryOptionalList Type="list" Element="AvatarActorEquipmentEntryOptionalEntryInfo"/>
    <AvatarActorEquipmentEntryOptionalInfo Type="tuple">
        <operate  Type="int" Flags="ALL_CLIENTS"  Default="0" Persistent="true"/>
        <entrylist  Type="AvatarActorEquipmentEntryOptionalList" Flags="ALL_CLIENTS"  Default="{}" Persistent="true"/>
    </AvatarActorEquipmentEntryOptionalInfo>
    <AvatarActorEquipmentPropDict Type="dict" Key="string" Value="int"/>
    <AvatarActorEquipmentInfo Type="tuple">
        <Tid Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <Uid Type="string" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <StackCount Type="int" Flags="ALL_CLIENTS" Persistent="true"/>
        <ExpireTime Type="int" Flags="ALL_CLIENTS" Persistent="true"/>
        <UsedCount Type="int" Flags="ALL_CLIENTS" Persistent="true"/>
        <IsBind Type="int" Flags="ALL_CLIENTS" Persistent="true"/>
        <IsNew Type="int" Flags="ALL_CLIENTS" Persistent="true"/>
        <level Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <prop Type="AvatarActorEquipmentPropDict" Flags="ALL_CLIENTS" Persistent="true"/>
        <entry Type="AvatarActorEquipmentEntryList" Flags="ALL_CLIENTS" Persistent="true"/>
        <enabled Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
		<optionalEntry Type="AvatarActorEquipmentEntryOptionalInfo" Flags="OWN_CLIENT" Persistent="true"/>
    </AvatarActorEquipmentInfo>
    <AvatarActorEquipmentList Type="dict" Key="int" Value="AvatarActorEquipmentInfo"/>
    <AvatarActorBagEquipmentList Type="dict" Key="string" Value="AvatarActorEquipmentInfo"/>

    <AvatarActorBagExtraChangeInfo Type="tuple">
        <ChangeInfo Type="dict" Key="string" Value="int"/>
        <SpecItems Type="dict" Key="int" Value="int"/>
        <bConsumeLimitSuc Type="bool"/>
    </AvatarActorBagExtraChangeInfo>

    <AvatarActorBagChangeInfoOrderList Type="list" Element="string"/>

    <AvatarActorKeyLists Type="list" Element="int"/>
    <AvatarActorBagDelLists Type="list" Element="AvatarActorBagDelList"/>
    <AvatarActorBagChangeList Type="tuple">
        <Type Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <Items Type="AvatarItemList" Flags="SERVER_ONLY" Persistent="true"/>
        <Equips Type="AvatarActorBagEquipmentList" Flags="SERVER_ONLY" Persistent="true"/>
    </AvatarActorBagChangeList>
    <AvatarActorBagChangeLists Type="list" Element="AvatarActorBagChangeList"/>
    <AvatarActorBagInfoList Type="tuple">
        <Type Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <CurrentCount Type="int" Flags="SERVER_ONLY"/>
        <Size Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <Capacity Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <Items Type="AvatarItemList" Flags="SERVER_ONLY" Persistent="true"/>
        <Equips Type="AvatarActorBagEquipmentList" Flags="SERVER_ONLY" Persistent="true"/>
        <SlotInfo Type="DictStrInt" Flags="OWN_CLIENT" Persistent="true"/>
    </AvatarActorBagInfoList>
    <AvatarActorWalletList Type="dict" Key="int" Value="int"/>
    <AvatarActorBagList Type="dict" Key="int" Value="AvatarActorBagInfoList"/>
    <AvatarActorBagTimeoutItemInfo Type="tuple">
        <Type Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <Uid Type="string" Flags="SERVER_ONLY" Persistent="true"/>
        <Time Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </AvatarActorBagTimeoutItemInfo>
    <AvatarActorBagTimeoutItemList Type="dict" Key="string" Value="AvatarActorBagTimeoutItemInfo"/>
    <AvatarActorBagUseItemLimitList Type="dict" Key="int" Value="int"/>
    <AvatarActorBagUseItemLimitInfo Type="tuple">
        <count Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <refreshTime Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </AvatarActorBagUseItemLimitInfo>
    <AvatarActorBagUseItemLimitInfoList Type="dict" Key="int" Value="AvatarActorBagUseItemLimitInfo"/>
    <AvatarActorBagTimeoutInfo Type="tuple">
        <CurrentItem Type="AvatarActorBagTimeoutItemInfo" Flags="SERVER_ONLY" Persistent="true"/>
        <Info Type="AvatarActorBagTimeoutItemList" Flags="SERVER_ONLY" Persistent="true"/>
        <UseItemTimeLimit Type="AvatarActorBagUseItemLimitInfoList" Flags="SERVER_ONLY" Persistent="true"/>
    </AvatarActorBagTimeoutInfo>
    <AvatarActorMemberLevelList Type="list" Element="int"/>
    <AvatarActorMemberProfessionList Type="list" Element="int"/>
    <TupleAttr1 Type="tuple">
        <hp Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <pp Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <mp Type="string" Flags="ALL_CLIENTS" Default="" Persistent="true"/>
    </TupleAttr1>
    <TupleAttr2 Type="tuple">
        <sub1 Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <sub2 Type="TupleAttr1" Flags="OWN_CLIENT"/>
    </TupleAttr2>
    <DictAttr1 Type="dict" Key="string" Value="TupleAttr1"/>
    <ListAttr2 Type="list" Element="DictAttr2"/>
    <ListAttr3 Type="list" Element="TupleAttr1"/>
    <LogicConfigInfo Type="tuple">
        <IsSkillHostByClient Type="int" Flags="SERVER_ONLY" Default="1"/>
    </LogicConfigInfo>
    <MoveFrame Type="tuple">
        <timestamp Type="float" Flags="SERVER_ONLY" Default="0.0"/>
        <moveMode Type="int" Flags="SERVER_ONLY" Default="0"/>
        <location Type="Vector3" Flags="SERVER_ONLY"/>
        <rotation Type="Vector3" Flags="SERVER_ONLY"/>
        <velocity Type="Vector3" Flags="SERVER_ONLY"/>
    </MoveFrame>

    <EquipBodySlotStage Type="tuple">
        <level Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <percent Type="float" Flags="SERVER_ONLY" Default="0.0" Persistent="true"/>
    </EquipBodySlotStage>
    <EquipBodySlotStageInfo Type="dict" Key="int" Value="EquipBodySlotStage"/>
    <EquipBodyEnhanceSlot Type="tuple">
        <stages Type="EquipBodySlotStageInfo" Flags="SERVER_ONLY" Persistent="true"/>
        <stagesEffect Type="int" Flags="SERVER_ONLY" Default="0"/>
    </EquipBodyEnhanceSlot>
    <EquipBodyEnhanceSlotInfo Type="dict" Key="int" Value="EquipBodyEnhanceSlot"/>

    <EquipBodyEnhanceSuit Type="tuple">
        <level Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <effect Type="int" Flags="SERVER_ONLY" Default="0" />
    </EquipBodyEnhanceSuit>
    <EquipBodyEnhanceSuitInfo Type="dict" Key="int" Value="EquipBodyEnhanceSuit"/>
    <EQUIP_BODY_MARK_INFO Type="tuple">
        <slots Type="DictIntInt" />
        <suit Type="UINT" Default="0" />
    </EQUIP_BODY_MARK_INFO>
    <EQUIP_BODY_ENHANCE_INFO Type="tuple">
        <slots Type="EquipBodyEnhanceSlotInfo" Flags="SERVER_ONLY" Persistent="true"/>
        <suit  Type="EquipBodyEnhanceSuitInfo" Flags="SERVER_ONLY" Persistent="true"/>
        <marks Type="EQUIP_BODY_MARK_INFO" />
    </EQUIP_BODY_ENHANCE_INFO>

    <EQUIPMENT_BODY_ENHANCE_SLOT_STAGE Type="tuple">
        <level Type="int" Default="0" Persistent="true"/>
        <percent Type="float" Default="0.0" Persistent="true"/>
        <singleFailCnt Type="int" Flags="SERVER_ONLY" Default="0"/>
        <totalFailCnt Type="int" Flags="SERVER_ONLY" Default="0"/>
    </EQUIPMENT_BODY_ENHANCE_SLOT_STAGE>
    <EQUIPMENT_BODY_ENHANCE_SLOT_STAGE_INFO Type="dict" Key="int" Value="EQUIPMENT_BODY_ENHANCE_SLOT_STAGE"/>
    <EQUIPMENT_BODY_ENHANCE_SLOT_INFO Type="tuple">
        <stages Type="EQUIPMENT_BODY_ENHANCE_SLOT_STAGE_INFO" Flags="SERVER_ONLY" Persistent="true"/>
        <stagesEffect Type="int" Flags="SERVER_ONLY" Default="0"/>
        <mark Type="int" Flags="SERVER_ONLY" Default="0"/>
    </EQUIPMENT_BODY_ENHANCE_SLOT_INFO>
    <EQUIPMENT_BODY_ENHANCE_SLOTS_INFO Type="dict" Key="int" Value="EQUIPMENT_BODY_ENHANCE_SLOT_INFO"/>
    <EQUIPMENT_BODY_ENHANCE_INFO Type="tuple">
        <slots Type="EQUIPMENT_BODY_ENHANCE_SLOTS_INFO" Persistent="true"/>
    </EQUIPMENT_BODY_ENHANCE_INFO>

    <EQUIPMENT_BODY_SUIT_SLOT_INFO Type="tuple">
        <level Type="int" Default="0" Persistent="true"/>
        <effect Type="int" Default="0" />
    </EQUIPMENT_BODY_SUIT_SLOT_INFO>
    <EQUIPMENT_BODY_SUIT_SLOTS_INFO Type="dict" Key="int" Value="EQUIPMENT_BODY_SUIT_SLOT_INFO"/>
    <EQUIPMENT_BODY_SUIT_INFO Type="tuple">
        <suit Type="EQUIPMENT_BODY_SUIT_SLOTS_INFO" Flags="SERVER_ONLY" Persistent="true"/>
        <suitMark Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true"/>
        <mark Type="int" Flags="SERVER_ONLY" Default="0"/> <!-- 战力稳定后废弃该字段 -->
        <totalMark Type="int" Flags="SERVER_ONLY" Default="0"/>
    </EQUIPMENT_BODY_SUIT_INFO>

    <EQUIPMENT_BODY_COMMON_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />            <!-- 随机固定词条、随机个数加成后的基础属性值, 客户端显示 -->
        <realValue Type="number" Persistent="true" />        <!-- 真实的基础属性值, 用于数值计算 -->
        <baseValue Type="number" Persistent="true" />        <!-- 基础属性基础值 -->
    </EQUIPMENT_BODY_COMMON_PROP>
    <EQUIPMENT_BODY_ADVANCE_PROP_SLOTS_INFO Type="tuple">
        <commonProps Type="list" Element="EQUIPMENT_BODY_COMMON_PROP" Persistent="true" />          <!-- 装备通用属性 -->
        <advanceProps Type="list" Element="UINT" Persistent="true" />                               <!-- 装备高级属性 -->
        <mark Type="int" Default="0"/>
    </EQUIPMENT_BODY_ADVANCE_PROP_SLOTS_INFO>
    <EQUIPMENT_BODY_ADVANCE_PROP_INFO Type="tuple">
        <slots Type="dict" Key="int" Value="EQUIPMENT_BODY_ADVANCE_PROP_SLOTS_INFO" Persistent="true"/>
    </EQUIPMENT_BODY_ADVANCE_PROP_INFO>

    <EQUIPMENT_RANDOM_PROP_INFO Type="tuple">
        <randomProps Type="list" Element="UINT" Persistent="true" />                                <!-- 装备随机属性,词条 -->
        <convergenceID Type="UINT" Persistent="true" Default="nil" />                                 <!-- 灵性聚合 -->
        <mark Type="UINT" Persistent="true" Default="0" />                                          <!-- 装备随机属性得分 -->
    </EQUIPMENT_RANDOM_PROP_INFO>

    <EQUIPMENT_RANDOM_PROP_INFO_LIST Type="list" Element="EQUIPMENT_RANDOM_PROP_INFO" Persistent="true" /> <!-- 装备随机属性列表 -->

    <EQUIPMENT_REFORM_MEMERY_SPACE Type="dict" Key="string" Value="int" Persistent="true" /> <!-- 单个槽位的重塑记忆空间 -->

    <EQUIPMENT_REFORM_MEMERY_SPACE_BY_TYPE Type="dict" Key="int" Value="EQUIPMENT_REFORM_MEMERY_SPACE" Persistent="true"/> <!-- 每个槽位的重塑记忆空间，按类型分组 -->

    <EQUIPMENT_REFORM_MEMERY_SPACE_BY_SLOT Type="dict" Key="int" Value="EQUIPMENT_REFORM_MEMERY_SPACE_BY_TYPE" Persistent="true"/> <!-- 所有槽位的重塑记忆空间，按槽位分组 -->

    <EQUIPMENT_BODY_REFORM_INFO Type="tuple">  <!-- 重塑信息 -->
        <reformCountBySlot Type="dict" Key="int" Value="int" Persistent="true"/>  <!-- 每个槽位重塑次数 -->
        <bestReformsBySlot Type="dict" Key="int" Value="EQUIPMENT_RANDOM_PROP_INFO_LIST" Persistent="true"/> <!-- 每个槽位的最佳重塑结果 -->
        <reformMemerySpaceBySlot Type="EQUIPMENT_REFORM_MEMERY_SPACE_BY_SLOT" Persistent="true"/> <!-- 每个槽位的重塑记忆空间 -->
    </EQUIPMENT_BODY_REFORM_INFO>

    <SLOT_WORD_SPACE Type="dict" Key="int" Value="DictIntInt" Persistent="true"/>		<!-- 槽位灵质空间，记录当前槽位某个group下的评分最高词条 -->
    <EQUIP_BODY_INFO Type="tuple">
        <enhanceInfo Type="EQUIPMENT_BODY_ENHANCE_INFO" Persistent="true"/>
        <suitInfo  Type="EQUIPMENT_BODY_SUIT_INFO" Persistent="true"/>
        <reformInfo Type="EQUIPMENT_BODY_REFORM_INFO" Persistent="true"/>  <!-- 重塑信息 -->
        <advancePropInfo Type="EQUIPMENT_BODY_ADVANCE_PROP_INFO" />  <!-- 高级词条 todo: 客户端修改后删除 -->
		<slotsWordSpace Type="SLOT_WORD_SPACE"/>		    <!-- 固定词条的词条空间 todo: 客户端修改后删除 -->
    </EQUIP_BODY_INFO>

    <EQUIP_PLAN_INV_EQUIP Type="tuple">
        <invSlotIndex Type="int" />
        <gbId Type="UUID" />
    </EQUIP_PLAN_INV_EQUIP>

    <EQUIP_PLAN_INV_EQUIPS Type="dict" Key="int" Value="EQUIP_PLAN_INV_EQUIP" />

    <EQUIP_PLAN Type="tuple">
        <name Type="string" Flags="OWN_CLIENT" Persistent="true" />
        <plan Type="DictIntStr" Flags="OWN_CLIENT" Persistent="true" />
    </EQUIP_PLAN>

    <EQUIP_PLANS Type="dict" Key="int" Value="EQUIP_PLAN" />

    <OPERATE_COUNT_LIMIT Type="tuple">
        <atkRandomPropEnhance Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <defAdvancePropExchange  Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </OPERATE_COUNT_LIMIT>

    <EQUIP_BODY_RANDOM_GROUP_INFO Type="tuple">
        <baseValue Type="int" Persistent="true"/>
        <value Type="int" Persistent="true"/>
        <adjustCount Type="int" Persistent="true"/>
    </EQUIP_BODY_RANDOM_GROUP_INFO>

    <EQUIP_BODY_RANDOM_GROUP_STOCK Type="dict" Key="int" Value="EQUIP_BODY_RANDOM_GROUP_INFO" Persistent="true"/>

    <EQUIP_BODY_RANDOM_GROUP_STOCK_SLOT_INFO Type="tuple">
        <stockInfo Type="EQUIP_BODY_RANDOM_GROUP_STOCK" Persistent="true"/>
        <adjustTotalUpCount Type="int" Persistent="true"/>
        <adjustTotalDownCount Type="int" Persistent="true"/>
    </EQUIP_BODY_RANDOM_GROUP_STOCK_SLOT_INFO>

    <EQUIP_BODY_RANDOM_GROUP_STOCK_SLOTS_INFO  Type="dict" Key="int" Value="EQUIP_BODY_RANDOM_GROUP_STOCK_SLOT_INFO" Persistent="true"/>

    <EquipGrowConsumeItemsInfo Type="DictIntInt"/>

    <DamageContextNew Type="tuple">
        <AssetIDWithType Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <ActionUniqueID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <InstigatorID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <AttackerID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <DefenderID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <BitFlags Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <HpDelta Type="float" Flags="ALL_CLIENTS" Default="0.0"/>       <!--实际血量变化值-->
        <ShieldCost Type="float" Flags="ALL_CLIENTS" Default="0.0"/>    <!--护盾扣除值-->
        <EleEffectId Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <DamageType Type="int" Flags="ALL_CLIENTS" Default="0" />
        <DamageValue Type="int" Flags="ALL_CLIENTS" Default="0" />      <!--当次伤害值-->
    </DamageContextNew>

    <CounterAttackInfo Type="tuple">
        <CounterAttackID Type="int" Flags="SERVER_ONLY"/>
        <SourceAbilityID Type="int" Flags="SERVER_ONLY"/>
        <InstigatorID Type="int" Flags="SERVER_ONLY"/>
        <bBuffOrPassive Type="bool" Flags="SERVER_ONLY"/>
        <ParamIndex Type="list" Element="int" Flags="SERVER_ONLY"/>
    </CounterAttackInfo>

    <BeatenContext Type="tuple">
        <HitSrcInsIdWithType Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <ActionUniqueID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <InstigatorID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <AttackerID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <DefenderID Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </BeatenContext>

    <DROP_LIMIT_INFO Type="tuple" Flags="OWN_CLIENT" Persistent="true">
        <!--上次的刷新时间, 偶数代表未经历过刷新，奇数代表经历过刷新-->
        <ts Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--本周期已使用的次数（包括周期新增次数和初始次数）, 刷新的时候会参与计算上周期的剩余可用次数-->
        <used Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--上周期剩余的次数-->
        <lastCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
    </DROP_LIMIT_INFO>
    <DROP_LIMIT_INFO_MAP Type="dict" Key="int" Value="DROP_LIMIT_INFO"/>

    <PRIORITY_DROP_LIMIT Type="tuple" Flags="OWN_CLIENT" Persistent="true">
        <!--本周期已使用的次数(仅周期新增次数，不包括初始次数), 刷新的时候会参与计算上周期的剩余可用次数-->
        <used Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--本周期已使用的初始次数,仅用于客户端展示 XX / YY，不参与剩余可用次数计算-->
        <initUsed Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--总可使用的初始次数-->
        <initTotal Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--上周期剩余的次数-->
        <lastCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
    </PRIORITY_DROP_LIMIT>
    <GROUP_PRIORITY_DROP_LIMIT Type="tuple" Flags="OWN_CLIENT" Persistent="true">
        <!--上次的刷新时间, 偶数代表未经历过刷新，奇数代表经历过刷新-->
        <ts Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--各个优先级的限次信息，key为优先级，value为优先级对应限次信息-->
        <limits Type="dict" Key="int" Value="PRIORITY_DROP_LIMIT" Flags="OWN_CLIENT" Persistent="true"/>
    </GROUP_PRIORITY_DROP_LIMIT>
    <GROUP_PRIORITY_DROP_LIMIT_MAP Type="dict" Key="int" Value="GROUP_PRIORITY_DROP_LIMIT"/>

    <EXTRA_DROP_LIMIT_INFO Type="tuple" Flags="OWN_CLIENT" Persistent="true">
        <!--已使用的可共享的次数-->
        <shareUsedCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--可使用的共享总次数-->
        <shareTotalCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--已使用的不共享的次数-->
        <usedCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
        <!--可使用的不共享的总次数-->
        <totalCount Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true"/>
    </EXTRA_DROP_LIMIT_INFO>
    <!--各个优先级的限次信息，key为优先级，value为优先级对应限次信息-->
    <EXTRA_GROUP_PRIORITY_DROP_LIMIT Type="dict" Key="int" Value="EXTRA_DROP_LIMIT_INFO"/>
    <EXTRA_GROUP_PRIORITY_DROP_LIMIT_MAP Type="dict" Key="int" Value="EXTRA_GROUP_PRIORITY_DROP_LIMIT"/>

    <Md5Table Type="dict" Key="string" Value="string"/>
    <PlayerOnlineInfo Type="tuple">
        <EntityID Type="string" Flags="OWN_CLIENT" Default=""/>
        <Nickname Type="string" Flags="OWN_CLIENT" Default=""/>
        <Profession Type="int" Flags="OWN_CLIENT" Default="0"/>
        <Lv Type="int" Flags="OWN_CLIENT" Default="0"/>
        <Pos Type="PVector3" Flags="OWN_CLIENT" />
    </PlayerOnlineInfo>
    <PlayerOnlineList Type="list" Element="PlayerOnlineInfo"/>
    <NearbyTeamList Type="list" Element="AvatarActorInviteInfo"/>
    <BatchQueryTeamInfoList Type="list" Element="AvatarActorInviteInfo"/>

    <ENTITY_DEBUG_INFO Type="tuple">
        <FlowchartPath Type="StringList" Default="{}"/>
        <SpaceFlowchartPath Type="StringList" Default="{}"/>
        <SCState Type="DictIntBool" Default="{}"/>
    </ENTITY_DEBUG_INFO>

    <CreateSpaceInfo Type="tuple">
        <MapID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <Num Type="int" Flags="SERVER_ONLY" Default="0"/>
        <MapType Type="int" Flags="SERVER_ONLY" Default="0"/>
        <AvatarMB Type="mailbox" Flags="SERVER_ONLY" Default=""/>
    </CreateSpaceInfo>
    <CreateSpaceInfoList Type="list" Element="CreateSpaceInfo"/>

    <LineInfo Type="tuple">
        <EntityID Type="string" Flags="SERVER_ONLY" Default=""/>
        <WorldID Type="WORLD_ID" Flags="SERVER_ONLY" Default="0"/>
        <PlayerNum Type="int" Flags="SERVER_ONLY" Default="0"/>
        <LineType Type="UINT" Flags="SERVER_ONLY" Default="0"/>
    </LineInfo>
    <SceneInfo Type="tuple">
        <MapID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <HardCapacity Type="int" Flags="SERVER_ONLY" Default="0"/>
        <SoftCapacity Type="int" Flags="SERVER_ONLY" Default="0"/>
        <LineInfoList Type="list" Element="LineInfo"/>
    </SceneInfo>
    <SpaceMgrInfo Type="tuple">
        <MailBox Type="mailbox" Flags="SERVER_ONLY" Default=""/>
        <IP Type="string" Flags="SERVER_ONLY" Default=""/>
        <PlayerNum Type="int" Flags="SERVER_ONLY" Default="0"/>
        <SceneInfoList Type="list" Element="SceneInfo"/>
    </SpaceMgrInfo>
    <!-- mail service -->
    <!-- mail head -->
    <MailHead Type="tuple">
        <ID Type="string" Flags="OWN_CLIENT" Default=""/>
        <TemplateID Type="int" Flags="OWN_CLIENT" Default="0"/>
        <Title Type="string" Flags="OWN_CLIENT" Default=""/>
        <Author Type="string" Flags="OWN_CLIENT" Default=""/>
        <AuthorIMG Type="string" Flags="OWN_CLIENT" Default=""/>
        <Time Type="int" Flags="OWN_CLIENT" Default="0"/>
        <Expire Type="int" Flags="OWN_CLIENT" Default="0"/>
        <IsRead Type="int" Flags="OWN_CLIENT" Default="0"/>
        <AttachmentStatus Type="int" Flags="OWN_CLIENT" Default="0"/>
    </MailHead>
    <!-- mail attachment normal item -->
    <MailAttachmentItem Type="tuple">
        <tid Type="int" Flags="OWN_CLIENT" Default="0"/>
        <count Type="int" Flags="OWN_CLIENT" Default="0"/>
        <isBind Type="int" Flags="OWN_CLIENT" Persistent="true"/>
    </MailAttachmentItem>
    <!-- mail attachment -->
    <MailAttachment Type="tuple">
        <Items Type="list" Element="MailAttachmentItem" Flags="OWN_CLIENT"/>
        <Equips Type="list" Element="AvatarActorEquipmentInfo" Flags="OWN_CLIENT"/>
    </MailAttachment>
    <!-- mail content -->
    <MailContent Type="tuple">
        <ID Type="string" Flags="OWN_CLIENT" Default=""/>
        <Content Type="string" Flags="OWN_CLIENT" Default=""/>
        <Attachment Type="MailAttachment" Flags="OWN_CLIENT"/>
    </MailContent>
    <MailList Type="list" Element="MailHead"></MailList>
    <UserMailList Type="dict" Key="string" Value="MailList"></UserMailList>
    <MailAttachmentsByID Type="dict" Key="string" Value="MailAttachment"></MailAttachmentsByID>
    <VDict Type="dict" Key="none" Value="none"/>
    <VList Type="list" Element="none"/>
    <VTuple Type="tuple"/>
    <DropItemInfo Type="tuple">
        <LifeTime Type="int" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
        <DropItemID Type="int" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
        <StartPosition Type="list" Element="float" Flags="ALL_CLIENTS"/>
        <Count Type="int" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
        <DelayTime Type="float" Flags="ALL_CLIENTS" Default="1" Persistent="true"/>
    </DropItemInfo>

    <CurPartners Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" Persistent="true"/>
    <PartnerInfo Type="tuple">
        <Level Type="int" Flags="SERVER_ONLY" Default="1" Persistent="true"/>
    </PartnerInfo>
    <PartnerInfos Type="dict" Key="int" Value="PartnerInfo" Flags="SERVER_ONLY" Persistent="true"/>
    <PartnerDetail Type="tuple">
        <TestAttr Type="int" Flags="SERVER_ONLY" Default="1" Persistent="true"/>
    </PartnerDetail>
    <PartnerDetails Type="dict" Key="int" Value="PartnerDetail" Flags="SERVER_ONLY" Persistent="true"/>

    <DungeonTitle Type="dict" Key="int" Value="string"/>
    <DungeonTitles Type="dict" Key="string" Value="DungeonTitle"/>

    <ChatChannelSetting Type="list" Element="int" Flags="SERVER_ONLY" Persistent="true"/>
    <LastChatTimestamps Type="dict" Key="int" Value="int" Flags="SERVER_ONLY" />

    <UnreadInfos Type="dict" Key="string" Value="int" Flags="SERVER_ONLY" />

    <ChatHead Type="tuple">
        <AvatarID Type="string" Flags="SERVER_ONLY" Default=""/>
        <IconID Type="int" Flags="SERVER_ONLY" Default="0" />
        <ProfessionID Type="int" Flags="SERVER_ONLY" Default="0" />
        <Level Type="int" Flags="SERVER_ONLY" Default="0" />
        <Nickname Type="string" Flags="SERVER_ONLY" Default="" />
    </ChatHead>

    <PrivateChatHead Type="tuple" Inherit="ChatHead">
        <GameMode Type="int" Flags="SERVER_ONLY" Default="0"/>
        <LastChatTime Type="int" Flags="SERVER_ONLY" Default="0" />
        <LastLogoutTime Type="int" Flags="SERVER_ONLY" Default="0" />
        <TeamMemberCount Type="int" Flags="SERVER_ONLY" Default="0" />
        <bTop Type="bool" Flags="SERVER_ONLY" Default="false" />
        <bBlock Type="bool" Flags="SERVER_ONLY" Default="false" />
    </PrivateChatHead>
    <PrivateChatHeads Type="dict" Key="string" Value="PrivateChatHead" Flags="SERVER_ONLY" />

    <PlayerChatInfo Type="tuple" Inherit="ChatHead">
        <Content Type="string" Flags="SERVER_ONLY" Default="" />
        <Timestamp Type="int" Flags="SERVER_ONLY" Default="0" />
    </PlayerChatInfo>
    <PlayerChatInfos Type="list" Element="PlayerChatInfo" Flags="SERVER_ONLY" />

    <PrivateChatInfo Type="tuple">
        <SenderID Type="string" Flags="SERVER_ONLY" Default="" />
        <Content Type="string" Flags="SERVER_ONLY" Default="" />
        <Timestamp Type="int" Flags="SERVER_ONLY" Default="0" />
    </PrivateChatInfo>
    <PrivateChatInfos Type="list" Element="PrivateChatInfo" Flags="SERVER_ONLY" />

    <GroupChatInviteeInfo Type="tuple" Inherit="ChatHead">
    </GroupChatInviteeInfo>
    <GroupChatInviteeInfos Type="list" Element="GroupChatInviteeInfo" Flags="SERVER_ONLY" />

    <GroupChatHead Type="tuple">
        <GroupID Type="string" Flags="SERVER_ONLY" Default=""/>
        <IconID Type="int" Flags="SERVER_ONLY" Default="0" />
        <Nickname Type="string" Flags="SERVER_ONLY" Default="" />
        <bTop Type="bool" Flags="SERVER_ONLY" Default="false" />
        <bBlock Type="bool" Flags="SERVER_ONLY" Default="false" />
    </GroupChatHead>
    <GroupChatHeads Type="list" Element="GroupChatHead" Flags="SERVER_ONLY" />
    <ChatIDs Type="dict" Key="string" Value="int" Flags="SERVER_ONLY" Persistent="true" />

    <BATCH_CREATE_TEAM_INFO Type="tuple">
        <teamID Type="TEAM_ID" Flags="SERVER_ONLY" Default="nil"/>
        <captainID Type="string" Flags="SERVER_ONLY" Default="nil"/>
        <membersList Type="ARRAY_ENTITY_ID" Flags="SERVER_ONLY"/>
        <botIdMap Type="ENTITY_ID_BOOL" Flags="SERVER_ONLY" Default="nil"/>
    </BATCH_CREATE_TEAM_INFO>
    <BATCH_CREATE_TEAM_INFO_MAP Type="dict" Key="int" Value="BATCH_CREATE_TEAM_INFO" Flags="SERVER_ONLY"/>

    <PVPMailboxs Type="list" Element="mailbox"/>
    <PvpMatchedMemberInfo Type="tuple">
        <id Type="string" Flags="SERVER_ONLY" Default=""/>
        <name Type="string" Flags="SERVER_ONLY" Default=""/>
        <sex Type="int" Flags="SERVER_ONLY" Default="0"/>
        <profession Type="int" Flags="SERVER_ONLY" Default="0"/>
        <level Type="int" Flags="SERVER_ONLY" Default="0"/>
        <zhanLi Type="int" Flags="SERVER_ONLY" Default="0"/>
        <isCaptain Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <rankId Type="int" Flags="SERVER_ONLY" Default="0"/>
        <rankPoints Type="int" Flags="SERVER_ONLY" Default="0"/>
        <protectedPoints Type="int" Flags="SERVER_ONLY" Default="0"/>
        <botID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <isRobot Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <prepared Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <mailbox Type="none" Flags="SERVER_ONLY"/>
        <recoverTeamID Type="TEAM_ID" Flags="SERVER_ONLY" Default="nil"/>
        <bRecoverCaptain Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <bGmWarmMatch Type="bool" Flags="SERVER_ONLY" Default="false"/>
    </PvpMatchedMemberInfo>
    <PVP_MATCH_CAMP_INFO Type="tuple">
        <teamID Type="TEAM_ID" Flags="SERVER_ONLY" Default="nil"/>
        <groupID Type="GROUP_ID" Flags="SERVER_ONLY" Default="nil"/>
        <captainID Type="string" Flags="SERVER_ONLY" Default="nil"/>
        <members Type="list" Element="PvpMatchedMemberInfo" Flags="SERVER_ONLY"/>
    </PVP_MATCH_CAMP_INFO>
    <PVP_TYPE_MATCH_INFO Type="tuple">
        <type Type="int" Flags="SERVER_ONLY" Persistent="false" Default="0"/>
        <index Type="int" Flags="SERVER_ONLY" Persistent="false" Default="0"/>
        <prepared Type="bool" Flags="SERVER_ONLY" Persistent="false" Default="false"/>
        <campID Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <state  Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <confirmEndTime Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <startTime Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
    </PVP_TYPE_MATCH_INFO>
    <PVP_MATCH_INFO Type="tuple">
        <id Type="string" Flags="SERVER_ONLY" Default=""/>
        <type Type="int" Flags="SERVER_ONLY" Default="0"/>
        <isTeam Type="bool" Flags="SERVER_ONLY" Default="false"/>
        <mailbox Type="mailbox" Flags="SERVER_ONLY"/>
        <count Type="int" Flags="SERVER_ONLY" Default="0"/>
        <consecutiveFailCnt Type="int" Flags="SERVER_ONLY" Default="0"/>
        <members Type="list" Element="PvpMatchedMemberInfo" Flags="SERVER_ONLY" Default="nil"/>
    </PVP_MATCH_INFO>
    <PVP_CONFIRM_INFO Type="tuple">
        <matchID Type="string" Flags="SERVER_ONLY" Default=""/>
        <campID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <avatarID Type="string" Flags="SERVER_ONLY" Default=""/>
        <bAgree Type="bool" Flags="SERVER_ONLY" Default="false"/>
    </PVP_CONFIRM_INFO>

    <PVPCurrentMatchInfo Type="tuple">
        <PvpMatchID Type="string" Flags="SERVER_ONLY" Persistent="false" Default=""/>
        <PvpMatchIDPrepared Type="bool" Flags="SERVER_ONLY" Persistent="false" Default="false"/>
        <PvpMatchCampID Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <PvpMatchType Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <PvpMatchState  Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <PvpMatchConfirmEndTime Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <PvpMatchStartTime Type="int" Flags="OWN_CLIENT" Persistent="false" Default="0"/>
        <PvpStateEndTime Type="int" Flags="SERVER_ONLY" Persistent="false" Default="nil"/>
    </PVPCurrentMatchInfo>
    <PVP_MATCH_RESULT Type="dict" Key="int" Value="PVP_MATCH_CAMP_INFO" Flags="SERVER_ONLY"/>
    <PVP_MATCH_BATTLE_INFO Type="tuple">
        <id Type="string" Flags="SERVER_ONLY" Default=""/>
        <matchType Type="int" Flags="SERVER_ONLY" Default="0"/>
        <info Type="PVP_MATCH_RESULT" Flags="SERVER_ONLY"/>
        <delayPrepareTime Type="ENTITY_ID_FLOAT" /> <!-- 延迟确认时间 -->
    </PVP_MATCH_BATTLE_INFO>

    <ReviveRecord Type="tuple">
        <AllowTimestamp Type="int" Flags="OWN_CLIENT" Persistent="true"/> <!-- 下次允许复活时间戳 -->
        <AutoReviveTime Type="int" Flags="OWN_CLIENT" Persistent="true"/> <!-- 自动复活的时间戳 -->
        <LastTimestamp Type="int" Flags="OWN_CLIENT" Persistent="true"/> <!-- 上次复活时间戳 -->
        <CDTimes Type="int" Flags="OWN_CLIENT" Persistent="true"/> <!-- cd累计次数 -->
    </ReviveRecord>

    <ReviveRecords Type="dict" Key="int" Value="ReviveRecord" Flags="OWN_CLIENT" Persistent="true"/>

    <PVP_PLAYER_SEASON_SCORE_MAP Type="dict" Key="ENTITY_ID" Value="int" Persistent="true"/> <!-- PVP玩家赛季分数, key为entityID, value是分数 -->
    <MATCH_TYPE_PVP_PLAYER_SEASON_SCORE_MAP Type="dict" Key="int" Value="PVP_PLAYER_SEASON_SCORE_MAP" Persistent="true"/> <!-- PVP玩家赛季分数, key为matchType -->
    <ALL_SERVER_PVP_PLAYER_SEASON_SCORE_MAP Type="dict" Key="int" Value="MATCH_TYPE_PVP_PLAYER_SEASON_SCORE_MAP" Persistent="true"/>       <!-- 所有服务器的PVP玩家赛季分数, key为logicServerID -->
    <!-- SceneObject 相关 Start -->
    <SCENE_OBJECT_OVERRIDE_CONF Type="tuple">
        <State Type="int" Default="nil"/>
        <Position Type="ListFloat" Default="nil"/>
        <!-- 文字板相关动态设置 -->
        <IsFadeIn Type="bool" Default="nil"/>
        <TransitionDuration Type="float" Default="nil"/>
		<!-- 体积雾相关配置-->
		<ModifyType Type="int" Default="nil" />
		<Density Type="float" Default="nil" />
		<Scale Type="float" Default="nil" />
		<TransitionTime Type="float" Default="nil" />
    </SCENE_OBJECT_OVERRIDE_CONF>

    <SCENE_OBJECT_CONF_MAP Type="dict" Key="int" Value="SCENE_OBJECT_OVERRIDE_CONF" />
    <SCENE_OBJECT_INSTANCE_ID_LIST Type="list" Element="INSTANCE_ID" />
    <SCENE_OBJECT_ID_LIST Type="list" Element="int" />
    <!-- SceneObject 相关 End -->

    <SceneActorOverrideConfInfo Type="tuple">
        <UITemplate Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <PlaneID Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <InteractorRadius Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <Text Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <Color Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <QuestID Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <CurSpiritualVisionID Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <UIShowText Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <TargetIndex Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <InteractiveFaction Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <DisplayName Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <HeadTitle Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <IsFadeIn Type="bool" Flags="ALL_CLIENTS" Default="nil"/>
        <TransitionDuration Type="float" Flags="ALL_CLIENTS" Default="nil"/>
        <bSeverControlVisible Type="bool" Flags="ALL_CLIENTS" Default="nil"/>
    </SceneActorOverrideConfInfo>

    <SCENE_ACTOR_SEAT_INFO Type="dict" Key="int" Value="string" Flags="ALL_CLIENTS"/>

    <SceneActorElemInfo Type="DictIntInt" Flags="ALL_CLIENTS"/>
    <SceneActorInfoChildrenSync Type="tuple">
        <InsID Type="string" Flags="ALL_CLIENTS" Default="nil"/>
        <ActorType Type="int" Flags="ALL_CLIENTS" Default="nil"/>
        <Position Type="list" Element="float" Flags="ALL_CLIENTS" Default="nil"/>
        <Rotation Type="list" Element="float" Flags="ALL_CLIENTS" Default="nil"/>
        <DropItem Type="DropItemInfo" Flags="ALL_CLIENTS" Default="nil"/>
    </SceneActorInfoChildrenSync>
    <SceneActorInfoSync Type="tuple">
        <!--通用Actor属性start-->
        <InsID Type="string" Flags="SERVER_ONLY" Default="nil"/>
        <SceneActorState Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <SubState Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <ActorType Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <Position Type="list" Element="float" Flags="SERVER_ONLY" Default="nil"/>
        <Rotation Type="list" Element="float" Flags="SERVER_ONLY" Default="nil"/>
        <TemplateID Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <BelongType Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <LastInteractTime Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <CdExpiredTime Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <ElemInfo Type="SceneActorElemInfo" Flags="SERVER_ONLY" Default="nil"/>
        <ShowVisibleTime Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <DropItem Type="DropItemInfo" Flags="SERVER_ONLY" Default="nil"/>
        <OverrideConf Type="SceneActorOverrideConfInfo" Flags="SERVER_ONLY" Default="nil"/>
        <Children Type="dict" Key="string" Value="SceneActorInfoChildrenSync" Flags="SERVER_ONLY" Default="nil"/>
        <ElemSpreadInfo Type="DictIntInt" Flags="SERVER_ONLY" Default="nil"/>
        <!--通用Actor属性end-->
        <!--老交互物属性start-->
        <InsType Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <RewardCountInfo Type="DictIntInt" Flags="SERVER_ONLY" Default="nil" />
        <GroupID Type="string" Flags="SERVER_ONLY" Default="nil"/>
        <!--老交互物属性end-->
        <SeatInfo Type="SCENE_ACTOR_SEAT_INFO" Flags="SERVER_ONLY" Default="nil" />
        <Summoner Type="string" Flags="SERVER_ONLY" Default="nil"/>
    </SceneActorInfoSync>

    <SpaceInteractorInfoSync Type="dict" Key="string" Value="SceneActorInfoSync" Flags="SERVER_ONLY" Default="" />
    <InteractorInfo Type="tuple">
        <InsID Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <ActorType Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <InsType Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <BelongType Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <TemplateID Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <Position Type="list" Element="float" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <Rotation Type="list" Element="float" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <!--交互组的InsID, 当玩家进入场景将交互物创建出来后, 根据其所属的GroupInsID做筛选, 再建一个GroupIns出来-->
        <!--prefab组ID-->
        <GroupID Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <SourceID Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <OverrideConf Type="SceneActorOverrideConfInfo" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <RewardCountInfo Type="DictIntInt" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <SceneActorState Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <SubState Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <LastInteractTime Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <CdExpiredTime Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <ElemInfo Type="SceneActorElemInfo" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <ShowVisibleTime Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <ElemSpreadInfo Type="DictIntInt" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
    </InteractorInfo>

    <SpaceInteractorInfo Type="dict" Key="string" Value="InteractorInfo" Flags="ALL_CLIENTS" Default="" Persistent="true"/>

    <InitiateInteractExParam Type="tuple">
        <TargetSubState Type="int" Flags="SERVER_ONLY" Default="nil" />
    </InitiateInteractExParam>

    <InteractorReSpawnInfo Type="tuple">
        <TemplateInsID Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <RespawnTime Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
    </InteractorReSpawnInfo>
    <InteractorMapReSpawnInfo Type="dict" Key="string" Value="InteractorReSpawnInfo" Flags="SERVER_ONLY" Default="" Persistent="true"/>

    <InteractorInCDInfoMap Type="dict" Key="string" Value="DictIntBool" Flags="SERVER_ONLY" Default="" Persistent="true"/>

    <PersistInteractorCacheInfo Type="dict" Key="string" Value="ListStr" Flags="SERVER_ONLY" Default="" Persistent="true"/>
    <PersistInteractorCacheInfoMap Type="dict" Key="string" Value="PersistInteractorCacheInfo" Flags="SERVER_ONLY" Default="" Persistent="true"/>

    <InteractorEventSyncParam Type="tuple">
        <Position Type="list" Element="float" Flags="SERVER_ONLY" Persistent="true"/>
        <InitiatorID Type="string" Flags="SERVER_ONLY" Persistent="true"/>
    </InteractorEventSyncParam>
    <ConfirmInfo Type="tuple">
        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
        <CampIndex Type="int" Flags="SERVER_ONLY" Default="0" Persistent="false"/>
        <bConfirmed Type="bool" Flags="SERVER_ONLY" Persistent="false"/>
    </ConfirmInfo>
    <ConfirmInfoList Type="list" Element="ConfirmInfo" Flags="SERVER_ONLY" Persistent="false"/>
    <UserHUDSnapshot Type="tuple">
        <AvatarActorID Type="string" Flags="ALL_CLIENTS" Default="" Persistent="false"/>
        <CampID Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="false"/>
        <bInCurSpace Type="bool" Flags="ALL_CLIENTS" Persistent="false"/>
        <bDead Type="bool" Flags="ALL_CLIENTS" Persistent="false"/>
        <Profession Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="false"/>
        <Name Type="string" Flags="ALL_CLIENTS" Default="" Persistent="false"/>
    </UserHUDSnapshot>
    <HUDSnapShotDict Type="dict" Key="string" Value="UserHUDSnapshot" Flags="ALL_CLIENTS" Persistent="false"/>
    <CombatStatData Type="tuple">
        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>
        <KillPlayerNum Type="int" Flags="SERVER_ONLY" Default="0" Persistent="false"/>
        <OutDamageNum Type="int" Flags="SERVER_ONLY"  Default="0" Persistent="false"/>
        <InDamageNum  Type="int" Flags="SERVER_ONLY"  Default="0" Persistent="false"/>
        <HealNum  Type="int" Flags="SERVER_ONLY"  Default="0" Persistent="false"/>
    </CombatStatData>
    <CombatStatDataDict Type="dict"  Key="string" Value="CombatStatData"/>
    <TitleInfo Type="tuple">
        <titleId Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <customName Type="string" Flags="ALL_CLIENTS" Default="" Persistent="true"/>
        <expireTs Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
        <obtainTs Type="int" Flags="ALL_CLIENTS" Default="0" Persistent="true"/>
    </TitleInfo>
    <CurTitleInfoDict Type="dict" Key="int" Value="TitleInfo" Flags="ALL_CLIENTS" Persistent="true"/>
    <TitleInfoDict Type="dict" Key="int" Value="TitleInfo" Flags="OWN_CLIENT" Persistent="true"/>
    
<!--    <TitleInfo Type="tuple">-->
<!--        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" Persistent="false"/>-->
<!--        <TitleID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="false"/>-->
<!--    </TitleInfo>-->
<!--    <TitleInfoList Type="list" Element="TitleInfo"/>-->
<!--    <TitleInfoDict Type="dict"  Key="string" Value="TitleInfoList"/>-->

    <TeamAvatarKeyChangeInfo Type="dict"  Key="string" Value="none"/>

    <TeamAvatarBriefInfo Type="tuple">
        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" />
        <Name Type="string" Flags="SERVER_ONLY" Default="" />
        <ProfessionId Type="int" Flags="SERVER_ONLY" Default="0" />
        <EnterTeamTime Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <PlayerType Type="int" Flags="SERVER_ONLY" Default="0" />
        <FellowID Type="int" Flags="SERVER_ONLY" Default="0" />
    </TeamAvatarBriefInfo>
    <TeamAvatarBriefInfoDict Type="dict"  Key="string" Value="TeamAvatarBriefInfo"/>
    <BattleStatistics Type="tuple">
        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" />
        <PlayerType Type="int" Flags="SERVER_ONLY" Default="0" />
        <IsLeaved Type="bool" Flags="SERVER_ONLY" Default="false" />
        <BattleTimeLength Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <TotalDamage Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <TotalHealing Type="float" Flags="SERVER_ONLY"  Default="0.0" />
        <TotalTakeDamage  Type="float" Flags="SERVER_ONLY"  Default="0.0" />
        <PlayerLastHitCount Type="int" Flags="SERVER_ONLY"  Default="0" />
    </BattleStatistics>
    <BattleStatisticsDict Type="dict"  Key="string" Value="BattleStatistics"/>
    <UpdatedTeamPlayerBattleStatistics Type="tuple">
        <BattleTimeLength Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <TeamStatistics Type="BattleStatisticsDict" Flags="SERVER_ONLY" />
    </UpdatedTeamPlayerBattleStatistics>
    <SkillStatistics Type="tuple">
        <SkillId Type="int" Flags="SERVER_ONLY"  Default="0" />
        <SkillType Type="int" Flags="SERVER_ONLY"  Default="0" />
        <CastTimes Type="int" Flags="SERVER_ONLY"  Default="0" />          <!--技能释放总次数（治疗+伤害）-->
        <TotalDamage Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <TotalHealing Type="float" Flags="SERVER_ONLY"  Default="0.0" />
        <DamageCritTimes Type="int" Flags="SERVER_ONLY"  Default="0" />    <!--技能伤害暴击次数-->
        <HealCritTimes Type="int" Flags="SERVER_ONLY"  Default="0" />      <!--技能治疗暴击次数-->
        <HealTimes Type="int" Flags="SERVER_ONLY"  Default="0" />          <!--技能治疗效果释放总次数-->
        <DamageTimes Type="int" Flags="SERVER_ONLY"  Default="0" />        <!--技能伤害效果释放总次数-->
    </SkillStatistics>
    <SkillStatisticsDict Type="dict"  Key="int" Value="SkillStatistics"/>
    <BattleStatisticsDetail Type="tuple">
        <BattleID Type="string" Flags="SERVER_ONLY" Default="" />
        <StartTime Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <BattleName Type="string" Flags="SERVER_ONLY" Default="" />
        <ShowInHistory Type="bool" Flags="SERVER_ONLY" Default="true" />
        <BattleTimeLength Type="float" Flags="SERVER_ONLY" Default="0.0" />
        <IsBattleOver Type="bool" Flags="SERVER_ONLY" Default="false" />
        <IsFullStatistics Type="bool" Flags="SERVER_ONLY" Default="false" />
        <TeamStatistics Type="BattleStatisticsDict" Flags="SERVER_ONLY" />
        <SkillStatistics Type="SkillStatisticsDict" Flags="SERVER_ONLY" />
        <LeavedAvatarInfos Type="TeamAvatarBriefInfoDict" Flags="SERVER_ONLY" />
    </BattleStatisticsDetail>
    <CombatScoreInfo Type="tuple">
        <Zhanli Type="int" Flags="SERVER_ONLY" Default="0" />
        <ZhanliDetail Type="DictIntInt" Flags="SERVER_ONLY"/>
    </CombatScoreInfo>

    <AggroInfo Type="tuple">
        <AvatarActorID Type="string" Flags="SERVER_ONLY" Default="" />
        <AggroValue Type="int" Flags="SERVER_ONLY"  Default="0" />
    </AggroInfo>
    <AggroInfoDict Type="dict"  Key="string" Value="AggroInfo"/>

    <PositionSaveInfo Type="tuple">
        <Position Type="list" Element="float" Flags="SERVER_ONLY" Persistent="true"/>
        <Rotation Type="list" Element="float" Flags="SERVER_ONLY" Persistent="true"/>
        <MapID Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <SpaceType Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <SpaceEntityID Type="ENTITY_ID" Flags="SERVER_ONLY" Persistent="true"/>
        <DungeonTemplateID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <WorldTemplateID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <WorldID Type="WORLD_ID" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <ApplyTemplateID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <LineType Type="UINT" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
        <!-- 主线序章特殊位面记录，主线前几章要玩家登录后直接进入位面，不返回大世界 -->
        <QuestEnterPlane Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <!-- 公会场景下线后重新登录还在公会场景 -->
        <GuildID Type="ENTITY_ID" Flags="SERVER_ONLY" Persistent="true"/>
    </PositionSaveInfo>

    <LimitGoodsInfo Type="tuple">
        <BuyCount Type="int" Flags="SERVER_ONLY" Persistent="true" Default="0" />
        <LastBuyTime Type="int" Flags="SERVER_ONLY"  Persistent="true" Default="0" />
    </LimitGoodsInfo>
    <LimitGoodsInfoDict Type="dict"  Key="int" Value="LimitGoodsInfo"  Persistent="true"/>
    <FriendInfo Type="tuple">
        <IconID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <ProfessionID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <Level Type="int" Flags="SERVER_ONLY" Default="1"/>
        <Nickname Type="string" Flags="SERVER_ONLY" Default=""/>
        <Remark Type="string" Flags="SERVER_ONLY" Default=""/>
        <LastLogoutTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <GameMode Type="int" Flags="SERVER_ONLY" Default="0"/>
        <TeamMemberCount Type="int" Flags="SERVER_ONLY" Default="0"/>
        <GroupID Type="string" Flags="SERVER_ONLY" Default=""/>
        <Friendship Type="int" Flags="SERVER_ONLY" Default="0"/>
    </FriendInfo>
    <FriendInfoDict Type="dict" Key="string" Value="FriendInfo"/>
    <FriendApplyInfo Type="tuple">
        <IconID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <ProfessionID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <Level Type="int" Flags="SERVER_ONLY" Default="1"/>
        <Nickname Type="string" Flags="SERVER_ONLY" Default=""/>
        <OperateTime Type="int" Flags="SERVER_ONLY" Default="0"/>
    </FriendApplyInfo>
    <FriendApplyInfoDict Type="dict" Key="string" Value="FriendApplyInfo"/>
    <BlackInfo Type="tuple">
        <IconID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <ProfessionID Type="int" Flags="SERVER_ONLY" Default="0"/>
        <Level Type="int" Flags="SERVER_ONLY" Default="1"/>
        <Nickname Type="string" Flags="SERVER_ONLY" Default=""/>
        <OperateTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <LastLogoutTime Type="int" Flags="SERVER_ONLY" Default="0"/>
    </BlackInfo>
    <BlackInfoDict Type="dict" Key="string" Value="BlackInfo"/>
    <FriendGroupInfo Type="tuple">
        <GroupID Type="string" Flags="SERVER_ONLY" Default=""/>
        <GroupName Type="string" Flags="SERVER_ONLY" Default=""/>
        <CreateTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <SortTag Type="int" Flags="SERVER_ONLY" Default="0"/>
    </FriendGroupInfo>
    <FriendGroupInfoDict Type="dict" Key="string" Value="FriendGroupInfo"/>
    <CameraModeInfo Type="tuple">
        <bEnable Type="bool" Flags="SERVER_ONLY" />
        <CameraInsName Type="string" Flags="SERVER_ONLY" />
        <bCustomModify Type="bool" Flags="SERVER_ONLY" />
        <BlendTime Type="float" Flags="SERVER_ONLY" />
        <ZoomMinLen Type="int" Flags="SERVER_ONLY" />
        <ZoomMaxLen Type="int" Flags="SERVER_ONLY" />
        <InitZoomLen Type="int" Flags="SERVER_ONLY" />
        <bEnableCameraRotate Type="bool" Flags="SERVER_ONLY" />
        <bEnablePlayerMoveWhileBlend Type="bool" Flags="SERVER_ONLY" />
        <bEnablePlayerMoveDuringCamera Type="bool" Flags="SERVER_ONLY" />
        <bEnablePlayerSkillWhileBlend Type="bool" Flags="SERVER_ONLY" />
        <bEnablePlayerSkillDuringCamera Type="bool" Flags="SERVER_ONLY" />
        <BlendCurve Type="int" Flags="SERVER_ONLY" />
    </CameraModeInfo>
    <CrossSpaceCutSceneInfo Type="tuple">
        <LeaveSpaceCutScene Type="int"/>
        <LoadingID Type="int"/>
        <EnterNewSpaceCutScene Type="int"/>
    </CrossSpaceCutSceneInfo>
    <CreateDungeonExParam Type="tuple">
        <CampInfo Type="MailBoxDict"/>
        <DynamicLevelParam Type="ListInt"/>
    </CreateDungeonExParam>

    <WORLD_REGISTER_INFO Type="tuple">
        <worldID Type="int"/>
        <worldBox Type="mailbox"/>
        <status Type="int"/>
        <playerIDs Type="ListStr"/>
        <LineType Type="UINT"/>
        <bUseServerBigWorld Type="bool"/>
    </WORLD_REGISTER_INFO>
    <WORLD_REGISTER_INFOS Type="list" Element="WORLD_REGISTER_INFO"/>

    <GUILD_LEAGUE_SYNC_GUILD_INFO Type="tuple">
        <name Type="string" Default="" Flags="ALL_CLIENTS"/>
        <badgeIndex Type="int" Default="1" Flags="ALL_CLIENTS"/>
        <badgeFrameId Type="int" Default="1" Flags="ALL_CLIENTS"/>
    </GUILD_LEAGUE_SYNC_GUILD_INFO>

    <GUILD_LEAGUE_SYNC_SELF_GAME_INFO Type="tuple">
        <groupInfo Type="list" Element="GUILD_LEAGUE_SYNC_GUILD_INFO" />
        <selfIndex Type="int" />
        <zoneIndex Type="int" />
        <groupType Type="int" />
        <groupIndex Type="int" />
        <result Type="int" />
    </GUILD_LEAGUE_SYNC_SELF_GAME_INFO>

    <!--2个公会的单场对局信息及结果-->
    <GUILD_LEAGUE_GAME_INFO Type="tuple">
        <!--对战的双方公会索引（组内公会排名）-->
        <guildIndex Type="list" Element="int" Persistent="true"/>
        <!--对战结果，参考GUILD_LEAGUE_GAME_RESULT-->
        <result Type="int" Persistent="true"/>
    </GUILD_LEAGUE_GAME_INFO>
    <!--某个场次的组内当前各场（如固定组和特殊组有4场对战，匹配组N/2场对战）对局信息及结果-->
    <GUILD_LEAGUE_ROUND_GAME_INFO Type="list" Element="GUILD_LEAGUE_GAME_INFO" Persistent="true"/>
    <!--某个轮次的3场次(每轮有3场次，单周2场，双周1场)的对局信息及结果-->
    <GUILD_LEAGUE_ROUNDS_GAME_INFO Type="list" Element="GUILD_LEAGUE_ROUND_GAME_INFO" Persistent="true"/>

    <GUILD_LEAGUE_SYNC_GROUP_GAME_INFO Type="tuple">
        <groupInfo Type="list" Element="GUILD_LEAGUE_SYNC_GUILD_INFO" />
        <roundInfos Type="GUILD_LEAGUE_ROUNDS_GAME_INFO" />
    </GUILD_LEAGUE_SYNC_GROUP_GAME_INFO>

    <GUILD_LEAGUE_GUILD_INFO Type="tuple">
        <!--公会ID-->
        <guildId Type="string" Persistent="true"/>
        <!--胜利次数-->
        <wins Type="int" Persistent="true"/>
        <!--失败次数-->
        <loses Type="int" Persistent="true"/>
    </GUILD_LEAGUE_GUILD_INFO>
    <GUILD_LEAGUE_GROUP_GAME_INFO Type="tuple">
        <!--小组里面各个公会的胜利和失败次数-->
        <groupInfo Type="list" Element="GUILD_LEAGUE_GUILD_INFO" Default="nil" Persistent="true"/>
        <!--某个轮次的3场次(每轮有3场次，单周2场，双周1场)的对局信息及结果-->
        <roundInfos Type="GUILD_LEAGUE_ROUNDS_GAME_INFO" Default="nil" Persistent="true"/>
    </GUILD_LEAGUE_GROUP_GAME_INFO>

    <GUILD_LEAGUE_ZONE_GAME_INFO Type="tuple">
        <!--固定组，N个组，每个组下有8个公会-->
        <Fix Type="list" Element="GUILD_LEAGUE_GROUP_GAME_INFO" Persistent="true"/>
        <!--特殊组，1个组，组里面有8个公会-->
        <Special Type="GUILD_LEAGUE_GROUP_GAME_INFO" Persistent="true"/>
        <!--匹配组，1个组，组里面有N个公会-->
        <Match Type="GUILD_LEAGUE_GROUP_GAME_INFO" Persistent="true"/>
    </GUILD_LEAGUE_ZONE_GAME_INFO>

    <!--各个分区的对战信息-->
    <GUILD_LEAGUE_ZONE_GAME_INFOS Type="list" Element="GUILD_LEAGUE_ZONE_GAME_INFO" Persistent="true"/>

    <GUILD_LEAGUE_SEASON_INFO Type="tuple">
        <!--赛季编号-->
        <id Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <!--赛季状态，参考GUILD_LEAGUE_SEASON_STATUS-->
        <status Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <!--切换赛季的开始时间，用于计算是单周还是双周，单周2场对战，双周1场对战-->
        <weekTS Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </GUILD_LEAGUE_SEASON_INFO>

    <GUILD_LEAGUE_ROUND_INFO Type="tuple">
        <!--场次-->
        <round Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <!--当前轮次的状态，参考GUILD_LEAGUE_ROUND_STATUS-->
        <status Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </GUILD_LEAGUE_ROUND_INFO>

    <!-- 结算信息 start -->
    <GUILD_LEAGUE_AVATAR_STAT Type="tuple">
        <Damage Type="float" Default="0" /> <!-- 伤害 -->
        <TakeDamage Type="float" Default="0" /> <!-- 承伤 -->
        <Heal Type="float" Default="0" /> <!-- 治疗 -->
        <Kill Type="int" Default="0" /> <!-- 击杀 -->
        <Assist Type="int" Default="0" /> <!-- 助攻 -->
        <Death Type="int" Default="0" /> <!-- 死亡 -->
        <Resource Type="int" Default="0" /> <!-- 资源点 -->
        <Revive Type="int" Default="0" /> <!-- 复活次数 -->
        <DamageTower Type="int" Default="0" /> <!-- 对塔的伤害 -->
    </GUILD_LEAGUE_AVATAR_STAT>
    <GUILD_LEAGUE_AVATAR_STATS Type="dict" Key="string" Value="GUILD_LEAGUE_AVATAR_STAT" />

    <GUILD_LEAGUE_CAMP_EXTRA_RESOURCE Type="dict" Key="int" Value="bool" />

    <GUILD_LEAGUE_AVATAR_FIX_STAT Type="tuple">
        <Name Type="string" Default="" Flags="ALL_CLIENTS"/> <!-- 名字 -->
        <Profession Type="int" Default="0" Flags="ALL_CLIENTS"/> <!-- 职业 -->
    </GUILD_LEAGUE_AVATAR_FIX_STAT>
    <GUILD_LEAGUE_AVATAR_FIX_CAMP_STATS Type="dict" Key="string" Value="GUILD_LEAGUE_AVATAR_FIX_STAT" />
    <GUILD_LEAGUE_AVATAR_FIX_STATS Type="dict" Key="int" Value="GUILD_LEAGUE_AVATAR_FIX_CAMP_STATS" />

    <GUILD_LEAGUE_CAMP_INFO Type="tuple">
        <id Type="string" Default="" Flags="ALL_CLIENTS"/>    <!-- 公会ID -->
        <name Type="string" Default="" Flags="ALL_CLIENTS"/>  <!-- 公会名 -->
        <badgeIndex Type="int" Default="1" Flags="ALL_CLIENTS"/>    <!-- 第几个字 -->
        <badgeFrameId Type="int" Default="1" Flags="ALL_CLIENTS"/>  <!-- 徽章框ID -->
    </GUILD_LEAGUE_CAMP_INFO>

    <GUILD_LEAGUE_CAMP_INFOS Type="dict" Key="int" Value="GUILD_LEAGUE_CAMP_INFO" Flags="ALL_CLIENTS"/>
    <GUILD_LEAGUE_CAMP_INFO_LIST Type="list" Element="GUILD_LEAGUE_CAMP_INFO" />

    <GUILD_LEAGUE_CAMP_RESULT Type="tuple">
        <id Type="string" Default=""/>          <!-- 公会ID -->
        <name Type="string" Default=""/>        <!-- 公会名 -->
        <badgeIndex Type="int" Default="1"/>    <!-- 第几个字 -->
        <badgeFrameId Type="int" Default="1"/>  <!-- 徽章框ID -->
        <areaNum Type="int" Default="0"/>       <!--占领的祭坛数-->
        <hp Type="int" Default="0"/>            <!--血量-->
    </GUILD_LEAGUE_CAMP_RESULT>
    <GUILD_LEAGUE_CAMP_RESULT_INFOS Type="dict" Key="int" Value="GUILD_LEAGUE_CAMP_RESULT"/>

    <GUILD_LEAGUE_RESOURCES Type="dict" Key="int" Value="int" Flags="ALL_CLIENTS"/>
    <GUILD_LEAGUE_TOTAL_ADD_RESOURCES Type="dict" Key="int" Value="int" Flags="ALL_CLIENTS"/>

    <GUILD_LEAGUE_SETTLEMENT_INFO Type="tuple">
        <avatarInfos Type="GUILD_LEAGUE_AVATAR_FIX_STATS"/>     <!--阵营双方的玩家信息（如玩家名字，职业）-->
        <avatarStats Type="GUILD_LEAGUE_AVATAR_STATS"/>         <!--玩家统计信息（如击杀，输出，治疗等）-->
        <campInfos Type="GUILD_LEAGUE_CAMP_RESULT_INFOS"/>      <!--对战双方的公会信息(如公会名，公会徽章)-->
        <winCamp Type="int"/>                                   <!--胜利的阵营-->
        <ts Type="UINT"/>                                       <!--比赛结束的时间-->
    </GUILD_LEAGUE_SETTLEMENT_INFO>
    <GUILD_LEAGUE_SETTLEMENT_LIST Type="list" Element="GUILD_LEAGUE_SETTLEMENT_INFO"/>

    <GUILD_LEAGUE_POSITION_INFOS Type="list" Element="ListInt"/>
    <GUILD_LEAGUE_MAP_INFO Type="tuple">
        <Enemy Type="GUILD_LEAGUE_POSITION_INFOS" />
        <Friend Type="GUILD_LEAGUE_POSITION_INFOS" />
    </GUILD_LEAGUE_MAP_INFO>
    <!-- 结算信息 end -->
    <SINGLE_DUNGEON_BOT Type="tuple">
        <BotID Type="int"/>
        <Level Type="int"/>
        <ZhanLi Type="number"/>
        <Camp Type="number"/>
    </SINGLE_DUNGEON_BOT>
    <DungeonBot Type="tuple">
        <BotList Type="list" Element="SINGLE_DUNGEON_BOT"/>
        <BotLeaderID Type="ENTITY_ID"/>
        <Camp Type="int"/>
        <TeamType Type="int"/>
        <TeamID Type="TEAM_ID"/>
        <GroupID Type="GROUP_ID"/>
        <LogicServerID Type="int"/>
        <SrcLevel Type="int"/>
        <SrcZhanLi Type="number"/>
    </DungeonBot>

    <!-- 传送参数 -->
    <ApplyInfo Type="tuple">
        <ProcessID Type="PID"/>
        <PriorityWorldID Type="WORLD_ID"/>
        <ApplyTemplateID Type="int"/>
        <MapIDs Type="ARRAY_UINT"/>
        <TargetLogicID Type="string"/>
        <CreatorBox Type="mailbox"/>
        <MemberBoxList Type="MailBoxList"/>
        <CampInfo Type="dict" Key="string" Value="MailBoxList"/>
        <DynamicLevelParam Type="IntList"/>
        <TemporaryLeave Type="bool"/> <!-- 这次传送是不是暂离性质的 -->
        <GuildInfo Type="none"/>
        <bForceNewLine Type="bool"/>
        <bGMSpace Type="bool"/>
        <bGMLaType Type="int"/> <!-- GM用，场景仅创建LA，或者编辑模式 -->
        <ActivityInfo Type="none"/> <!-- 玩法数据，比较灵活 -->
        <DungeonBotInfo Type="DungeonBot"/>
        <!-- plane -->
        <PlaneID Type="int" Default="0"/>
        <PlaneOptions Type="none" />
        <TowerLevel Type="int"/>
        <DungeonType Type="int"/>
        <GroupId Type="GROUP_ID"/>
        <LeagueGuildInfos Type="GUILD_LEAGUE_CAMP_INFO_LIST"/>
        <Guild2Mailbox Type="dict" Key="string" Value="mailbox"/>
        <GuildLeagueGroupInfo Type="ListInt"/>
        <GuildLeagueMatchId Type="int"/>
        <HomelandOwner Type="string"/>
        <HomelandType Type="int"/>
        <HomelandLevel Type="int"/>
        <LogicServerID Type="int"/>
        <BigWorldLogicInfo Type="ListStr"/>
        <LineType Type="UINT" Default="0"/> <!-- 安全/战斗分线 -->
        <DungeonMode Type="UINT" Default="nil"/>
        <UseKwAITraining Type="bool" Default="nil"/>
    </ApplyInfo>

    <WorldSimpleInfo Type="tuple" ImplClass="WorldReturnInfo">
        <EntityID Type="ENTITY_ID" Default="" Persistent="true"/>
        <WorldType Type="int" Persistent="true"/>
        <WorldID Type="int" Persistent="true"/>
        <ApplyTemplateID Type="int" Persistent="true"/>
    </WorldSimpleInfo>

    <ApplyWorldEnterInfo Type="tuple">
        <GuildID Type="ENTITY_ID"/>
        <Profession Type="int" Default="nil"/>
        <Sex Type="int" Default="nil"/>
        <fromGM Type="bool"/>
    </ApplyWorldEnterInfo>

    <!--交互组内每个交互物的信息-->
    <InteractorGroupMemberInfo Type="tuple">
        <InsID Type="string" Default="" Flags="ALL_CLIENTS"/>
        <InsType Type="int" Default="1" Flags="ALL_CLIENTS"/>
        <BelongType Type="int" Default="1" Flags="ALL_CLIENTS"/>
        <State Type="string" Default="" Flags="ALL_CLIENTS"/>
        <TemplateID Type="int" Default="0" Flags="ALL_CLIENTS"/>
        <TemplateType Type="string" Default="" Flags="ALL_CLIENTS"/>
        <DropItem Type="DropItemInfo" Flags="ALL_CLIENTS"/>
        <AuctionLock Type="bool" Default="false" Flags="ALL_CLIENTS"/>
        <RollLock Type="bool" Default="false" Flags="ALL_CLIENTS"/>
    </InteractorGroupMemberInfo>
    <GroupMemberInfos Type="dict" Key="string" Value="InteractorGroupMemberInfo" Flags="ALL_CLIENTS"/>
    <!-- inventoryInfo -->

    <FellowInfo Type="tuple"> <!-- 伙伴信息-->
        <ConfigID Type="UINT" Persistent="true"/>
        <Level Type="UINT" Persistent="true"/>
        <Exp Type="UINT" Persistent="true"/>
        <FirstStarUpLevel Type="UINT" Persistent="true"/><!-- 大突破等级，起始等级1-->
        <SecondStarUpLevel Type="UINT" Persistent="true"/><!-- 小突破等级，起始等级0-->
        <GetTime Type="UINT" Persistent="true"/>    <!-- 获取时间戳，毫秒-->
        <CombatType Type="UINT" Persistent="true"/> <!-- 出战类型 -->
        <CombatSlot Type="UINT" Persistent="true"/>       <!-- 出战Slot-->
    </FellowInfo>
    <FellowBag Type="dict" Key="OBJECT_ID" Value="FellowInfo"/><!-- key:ConfigID 表格配置ID-->
    <FriendAssistFellowBagMap Type="dict" Key="ENTITY_ID" Value="FellowBag"/>
    <fellowFriendAssistInfo Type="tuple"> <!-- 伙伴好友助战-->
        <OwnerID Type="ENTITY_ID" Default="nil" Persistent="true"/>
        <FellowInfo Type="FellowInfo" Default="nil" Persistent="true"/>
    </fellowFriendAssistInfo>
    <fellowFriendAssistMap Type="dict" Key="int" Value="fellowFriendAssistInfo"/>
    <FellowList Type="list" Element="FellowInfo"/><!--增、删、改与客户端同步的数据结构-->
    <FellowsPropTransInfoItem Type="tuple">
        <AddProp Type="DictStrFloat" Persistent="false"/>
        <AddCE Type="int" Persistent="false"/>
    </FellowsPropTransInfoItem>
    <FellowsPropTransInfo Type="dict" Key="OBJECT_ID" Value="FellowsPropTransInfoItem"/>
    <!-- 伙伴抽卡奖励信息-->
    <FellowGachaReward Type="tuple">
        <CardID Type="UINT" Persistent="false"/><!-- 奖励展示在哪张卡上 10连抽用到-->
        <RewardID Type="UINT" Persistent="false"/>
        <Num Type="UINT" Persistent="false"/>
        <IsFellow Type="UINT" Persistent="false"/>
        <SourceFellow Type="UINT" Persistent="false"/>
    </FellowGachaReward>
    <FellowGachaRewardList Type="list" Element="FellowGachaReward"/>
    <!-- 伙伴抽卡记录-->
    <FellowGachaRecord Type="tuple">
        <RewardID Type="UINT" Persistent="false"/>
        <Num Type="UINT" Persistent="false"/>
        <IsFellow Type="UINT" Persistent="false"/>
        <Time Type="UINT" Persistent="false"/>
         <SourceFellow Type="UINT" Persistent="false"/>
        <!--GachaID Type="UINT" Persistent="false"/--> <!-- 来自那个卡池-->
    </FellowGachaRecord>
    <FellowGachaRecordInfo Type="tuple">
        <RecordsTable Type="list" Element="FellowGachaRecord"/>
        <PageLastRecord Type="string" Persistent="false"/>
        <GachaClassify Type="UINT" Persistent="false"/>
        <TotalRecords Type="UINT" Persistent="false"/>
    </FellowGachaRecordInfo>


    <MultiPvpCampInfo Type="tuple">
        <PlayerNum Type="int" Default="0" Flags="ALL_CLIENTS"/>
        <Damage Type="int" Default="0" Flags="ALL_CLIENTS"/>
    </MultiPvpCampInfo>
    <MultiPvpCampInfoDict Type="dict" Key="int" Value="MultiPvpCampInfo" Flags="ALL_CLIENTS" Persistent="false"/>

    <PET_EQUIP_SKILL_SLOT_INFO Type="tuple">
        <skillId Type="int" Persistent="true"/>
        <isActive Type="bool" Persistent="true"/>
    </PET_EQUIP_SKILL_SLOT_INFO>
    <HOLD_ITEMS_COUNT Type="dict" Key="OBJECT_ID" Value="UINT"/>
    <INV_ITEM_COUNT Type="dict" Key="UINT" Value="UINT_MAP"/>
    <PET_EQUIP_SKILL_INFO Type="tuple">
        <petEquipId Type="UINT" Persistent="true"/>
        <data Type="list" Element="PET_EQUIP_SKILL_SLOT_INFO" Persistent="true"/>
        <maxSlot Type="UINT" Persistent="true"/>
    </PET_EQUIP_SKILL_INFO>

    <LOCK_MONEY_INFO Type="tuple">   <!-- 货币加锁信息 -->
        <opNUID Type="NUID" />
        <moneyType Type="UINT" />
        <amount Type="UINT" />
        <sourceName Type="string" />
    </LOCK_MONEY_INFO>

    <EQUIP_ATTACK_BASE_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />            <!-- 随机固定词条、随机个数加成后的基础属性值, 客户端显示 -->
        <realValue Type="number" Persistent="true" />        <!-- 真实的基础属性值, 用于数值计算 -->
        <maxValue Type="number" Persistent="true" />         <!-- 随机固定词条、随机个数加成后的基础属性最大值, 客户端显示 -->
        <baseValue Type="number" Persistent="true" />        <!-- 基础属性基础值 -->
        <maxBaseValue Type="number" Persistent="true" />     <!-- 基础属性基础最大值 -->
        <propRate Type="float" Persistent="true" />          <!-- 随机固定词条加成的比例系数 -->
    </EQUIP_ATTACK_BASE_PROP>
    <EQUIP_EXTRA_RANDOM_PROP Type="tuple">
        <index Type="UINT" Persistent="true" />
        <randomProps Type="list" Element="UINT" Persistent="true" />           <!-- 装备随机属性,词条 -->
        <mark Type="UINT" Persistent="true" />                                 <!-- 随机属性平分 -->
    </EQUIP_EXTRA_RANDOM_PROP>
    <EQUIP_EXTRA_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />
        <index Type="UINT" Persistent="true" />
        <poolId Type="UINT" Persistent="true" />
        <rarity Type="UINT" Persistent="true" />
        <propCE Type="number" Persistent="true" />
        <modified Type="BOOL" Persistent="true" />
    </EQUIP_EXTRA_PROP>
    <EQUIP_SPECIAL_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <rarity Type="UINT" Persistent="true" />
        <modified Type="BOOL" Persistent="true" />
        <propCE Type="number" Persistent="true" />
        <index Type="UINT" Persistent="true" />
        <poolId Type="UINT" Persistent="true" />
    </EQUIP_SPECIAL_PROP>
    <EQUIP_RANDOM_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />
        <rarity Type="UINT" Persistent="true" />
    </EQUIP_RANDOM_PROP>
    <PET_EQUIP_RANDOM_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />
        <normalizeCE Type="number" Persistent="true" />
    </PET_EQUIP_RANDOM_PROP>
    <SOUL_POWER_PROP Type="tuple">
        <powerId Type="OBJECT_ID" Persistent="true" />
        <value Type="number" Persistent="true" />
        <normalizeCE Type="number" Persistent="true" />
    </SOUL_POWER_PROP>
    <EXTRA_PROP_SHOW_LIST Type="list" Element="UINT" />

    <EQUIP_GEM_BASE_INFO Type="tuple">
        <gemId Type="UINT" Persistent="true" /> <!-- 纹印id -->
        <gemLv Type="UINT" Persistent="true" /> <!-- 当前纹印等级 -->
    </EQUIP_GEM_BASE_INFO>
    <EQUIP_GEM_INFO Type="tuple">
        <baseInfo Type="dict" Key="UINT" Value="EQUIP_GEM_BASE_INFO" Persistent="true" /> <!-- 纹印详细信息 -->
        <runeQuality Type="UINT" Persistent="true" /> <!-- 当前可装备符文最高品质 -->
        <runeId Type="UINT" Persistent="true" /> <!-- 符文id -->
        <runeLv Type="UINT" Persistent="true" /> <!-- 符文激活等级 -->
        <gemMaxLvAdd Type="UINT" Persistent="true" Default="0" /> <!-- 特殊属性增加的纹印等级上限 -->
        <gemPropAdd Type="number" Persistent="true" Default="0" /> <!-- 特殊属性额外增加纹印属性百分比 -->
    </EQUIP_GEM_INFO>

    <UNAPPLIED_REFINE_PROP Type="tuple">
        <baseProps Type="list" Element="EQUIP_ATTACK_BASE_PROP" Persistent="true" /> <!-- 重铸未应用的基础属性 -->
        <basePropCE Type="number" Persistent="true" /> <!-- 重铸未应用基础属性的战力 -->
        <extraProps Type="list" Element="EQUIP_EXTRA_PROP" Persistent="true" /> <!-- 重铸未应用的额外固定属性 -->
        <extraPropCE Type="number" Persistent="true" /> <!-- 重铸未应用额外固���属性的战力 -->
        <extraSpecialProps Type="list" Element="EQUIP_SPECIAL_PROP" Persistent="true" />   <!-- 重铸未应用装备额外固定特殊属性,蓝字前几条 -->
        <randomProps Type="list" Element="EQUIP_RANDOM_PROP" Persistent="true" /> <!-- 重铸未应用的随机属性 -->
        <randomPropCE Type="number" Persistent="true" /> <!-- 重铸未应用随机属性的战力 -->
        <randomSpecialProps Type="list" Element="EQUIP_SPECIAL_PROP" Persistent="true" /> <!-- 重铸未应用的随机特殊属性 -->
    </UNAPPLIED_REFINE_PROP>
    <EQUIP_EXTRA_EXCHANGE_PROP Type="tuple">
        <index Type="UINT" Persistent="true" /> <!-- 替换装备数据结构上的属性索引 -->
        <extraProp Type="EQUIP_EXTRA_PROP" Persistent="true" />
        <extraSpecialProp Type="EQUIP_SPECIAL_PROP" Persistent="true" />
    </EQUIP_EXTRA_EXCHANGE_PROP>
    <EXCHANGE_MATERIAL_INFO Type="tuple">
        <index Type="UINT" Persistent="true" /> <!-- 替换材料数据结构上被选择的属性索引 -->
        <itemId Type="OBJECT_ID" Persistent="true" />
        <quality Type="UINT" Persistent="true" />
        <extraProps Type="list" Element="EQUIP_EXTRA_PROP" Persistent="true" />
        <extraSpecialProps Type="list" Element="EQUIP_SPECIAL_PROP" Persistent="true" />
        <extraPropShowList Type="EXTRA_PROP_SHOW_LIST" Persistent="true" /> <!-- 替换材料额外属性显示顺序列表 -->
    </EXCHANGE_MATERIAL_INFO>

    <EQUIP_ENHANCE_INFO Type="tuple">
        <realEquipmentOrder Type="number" Persistent="true" /> <!-- 装备实际精炼品阶，受特效改变 -->
        <effectAddEnhanceLv Type="number" Persistent="true" /> <!-- 装备精炼等级特效加成，受特效改变 -->
        <enhSaturationList Type="ARRAY_NUMBER" Persistent="true" /> <!-- 装备各级精炼度信息 -->
        <enhCountList Type="ARRAY_UINT" Persistent="true" /> <!-- 装备各级精炼次数信息 -->
        <lastEnhanceUUID Type="UUID" Persistent="true" /> <!-- 装备最近一次精炼操作UUID -->
        <unappliedRefineProps Type="UNAPPLIED_REFINE_PROP" Persistent="true" /> <!-- 装���重铸未应用的属性信息 -->
        <refineCount Type="UINT" Persistent="true" /> <!-- 装备重铸次数 -->
        <bottomTimesList Type="ARRAY_UINT" Persistent="true" /> <!-- 装备重铸保底次数信息 -->
        <lastUnappliedRefineUUID Type="UUID" Persistent="true" /> <!-- 装备最近一次重铸操作的UUID -->
        <lastRefineUUID Type="UUID" Persistent="true" /> <!-- 装备最近一次应用重铸属性的UUID -->
    </EQUIP_ENHANCE_INFO>
    <EQUIP_PROP_EXCHANGE_INFO Type="tuple">
        <lastExtraPropExchangeUUID Type="UUID" Persistent="true" />
        <lastSpecialPropExchangeUUID Type="UUID" Persistent="true" />
        <exchangeScore Type="UINT" Persistent="true" />
        <exchangeOldPropInfo Type="EQUIP_EXTRA_EXCHANGE_PROP" Persistent="true" /> <!-- 替换装备被选择的旧属性 -->
        <exchangeNewPropInfo Type="EQUIP_EXTRA_EXCHANGE_PROP" Persistent="true" /> <!-- 替换装备被选择的新属性 -->
        <extraPropShowList Type="EXTRA_PROP_SHOW_LIST" Persistent="true" /> <!-- 替换前额外属性显示顺序列表 -->
        <exchangeMaterialInfo Type="EXCHANGE_MATERIAL_INFO" Persistent="true" /> <!-- 替换材料信息 -->
        <deltaCe Type="number" Persistent="true" /> <!-- 现在战力和替换前战力的差值 -->
    </EQUIP_PROP_EXCHANGE_INFO>
    <EXTRA_PROP_RESTORE_INFO Type="tuple">
        <extraProps Type="list" Element="EQUIP_EXTRA_PROP" Persistent="true" />   <!-- 装备额外固定属性,蓝字前几条 -->
        <extraSpecialProps Type="list" Element="EQUIP_SPECIAL_PROP" Persistent="true" />   <!-- 装备额外固定特殊属性,蓝字前几条 -->
        <extraPropShowList Type="EXTRA_PROP_SHOW_LIST" Persistent="true" /> <!-- 当前额外属性显示顺序列表 -->
    </EXTRA_PROP_RESTORE_INFO>

    <PURCHASE_INFO Type="tuple">
        <moneyType Type="UINT" Persistent="true" />
        <price Type="UINT" Persistent="true" />
        <freezeTime Type="UINT" Persistent="true" />
    </PURCHASE_INFO>
    <PET_EQUIP_PROP_INFO Type="tuple">
        <petEquipProps Type="list" Element="SOUL_POWER_PROP" Persistent="true" />  <!-- 英灵装备(元魂)随机出的属性 -->
        <fitness Type="number" Persistent="true" />
        <equipSkillInfo Type="PET_EQUIP_SKILL_INFO" Persistent="true" /> <!-- 英灵装备附加的技能 -->
        <currentCE Type="number" Persistent="true" /> <!-- 当前使用的属性带来的战力数值(技能加属性) -->
        <currentScore Type="number" Persistent="true" /> <!-- 魂器评分 独立于战力 -->
        <upgradeCount Type="UINT" Persistent="true" /> <!-- 升阶次数 默认为0 -->
        <increasePowerCount Type="UINT" Persistent="true" /> <!-- 数值补偿 加魂力次数 -->
        <increaseFitnessCount Type="UINT" Persistent="true" /> <!-- 数值补偿 加契合度次数 -->
        <totalRefineCount Type="UINT" Persistent="true" /> <!-- 当前元魂总淬炼次数(范围保护次数 不会重置) -->
        <resetRefineCount Type="UINT" Persistent="true" /> <!-- 本轮指定���护已淬炼次数(根据配置 可能会重置) -->
        <consumeItemInfo Type="UINT_MAP" Persistent="true" />   <!-- 记录淬炼使用的总道具数量 -->
        <isLocked Type="BOOL" Persistent="true" />               <!-- 魂器是否上锁 -->
        <protectedReplaceSkillSlotCounter Type="UINT" Persistent="true" />  <!-- 学技能时顶替掉旧技能连续计数器 -->
    </PET_EQUIP_PROP_INFO>

    <EQUIP_ATTACK_FIXED_PROP_INFO Type="tuple">
        <isLocked Type="bool" Persistent="true" />
        <propId Type="UINT" Persistent="true" /> <!-- 固定词条Id -->
    </EQUIP_ATTACK_FIXED_PROP_INFO>
    <EQUIP_MARK_INFO Type="tuple">
        <baseProps Type="UINT" Default="0" />
        <fixedProps Type="UINT" Default="0" />
        <randomProps Type="UINT" Default="0" />
    </EQUIP_MARK_INFO>
    <EQUIP_ATTACK_PROP_INFO Type="tuple">
        <baseProps Type="list" Element="EQUIP_ATTACK_BASE_PROP" Persistent="true" />            <!-- 装备基础属性 -->
        <fixedProps Type="list" Element="EQUIP_ATTACK_FIXED_PROP_INFO" Persistent="true" />     <!-- 装备固定属性 -->
        <randomProps Type="list" Element="UINT" Persistent="true" />                            <!-- 装备随机属性,词条 -->
        <extraRandomProps Type="list" Element="EQUIP_EXTRA_RANDOM_PROP" Persistent="true" />    <!-- 装备未生效随机属性，洗练词条 -->
        <randomEnhanceCount Type="UINT" Persistent="true" />                                    <!-- 洗练次数 -->
    </EQUIP_ATTACK_PROP_INFO>
    <EQUIP_PROP_ITEM_INFO Type="tuple">
        <equipId Type="UINT" Persistent="true" />                       <!-- 来源道具ID -->
        <propId Type="UINT" Persistent="true" />                        <!-- 来源词条ID -->
    </EQUIP_PROP_ITEM_INFO>
    <EQUIP_DEFENCE_BASE_PROP_INFO Type="tuple">
        <index Type="UINT" Persistent="true" />                         <!-- index -->
        <propId Type="UINT" Persistent="true" />                        <!-- 基础词条Id -->
        <baseValue Type="UINT" Persistent="true" />                     <!-- 基础属性值 -->
        <value Type="UINT" Persistent="true" />                         <!-- 属性值,用于客户端显示 -->
        <realValue Type="UINT" Persistent="true" />                     <!-- 实际属性值 -->
        <propRate Type="float" Persistent="true" />                     <!-- 固定词条加成的比例系数 -->
    </EQUIP_DEFENCE_BASE_PROP_INFO>
    <EQUIP_DEFENCE_FIXED_PROP_INFO Type="tuple">
        <index Type="UINT" Persistent="true" />                         <!-- index -->
        <propId Type="UINT" Persistent="true" />                        <!-- 固定词条Id -->
        <groupId Type="UINT" Persistent="true" />                       <!-- 固定词条组Id -->
        <process Type="UINT" Persistent="true" />                       <!-- 点亮进度 -->
    </EQUIP_DEFENCE_FIXED_PROP_INFO>
    <EQUIP_DEFENCE_PROP_INFO Type="tuple">                                                       <!-- 防御装信息 -->
        <level Type="UINT" Default="0" Persistent="true" />                                      <!-- 等级 -->
        <currentExp Type="UINT" Default="0" Persistent="true"/>                                  <!-- 当前等级经验 -->
        <needPromote Type="bool" Default="false" Persistent="true"/>                             <!-- 是否需要突破 -->
        <promoteCount Type="UINT" Default="0" Persistent="true"/>                                <!-- 突破次数 -->
        <baseProps Type="list" Element="EQUIP_DEFENCE_BASE_PROP_INFO" Persistent="true" />       <!-- 装备基础属性 -->
        <fixedProps Type="list" Element="EQUIP_DEFENCE_FIXED_PROP_INFO" Persistent="true" />     <!-- 装备固定属性 -->
    </EQUIP_DEFENCE_PROP_INFO>
    <SLOT_INDEX Type="UINT"/>
    <REMOVE_ITEM_SLOT_INFO Type="tuple">
        <index Type="UINT" Default="0" />
        <gbId Type="UUID"/>
    </REMOVE_ITEM_SLOT_INFO>
    <REMOVE_ITEMS_SLOT_INFO Type="list" Element="REMOVE_ITEM_SLOT_INFO" />       <!-- 删除物品信息 -->
    <EQUIP_PROP_INFO Type="tuple">                                               <!-- 装备属性 -->
        <equipAtkPropInfo   Type="EQUIP_ATTACK_PROP_INFO" Persistent="true" />   <!-- 攻击装属性 -->
        <equipDefPropInfo   Type="EQUIP_DEFENCE_PROP_INFO" Persistent="true" />  <!-- 防御装属性 -->
        <equipPropItemInfo  Type="EQUIP_PROP_ITEM_INFO" Persistent="true"/>      <!-- 特殊类型，词缀球包含信息 -->
        <marks Type="EQUIP_MARK_INFO" />                                         <!-- 装备评分 -->
    </EQUIP_PROP_INFO>

    <SEALED_RANDOM_ATTR Type="tuple">
        <propId Type="UINT" Persistent="true" />                      <!--词条id-->
        <attrLevel Type="UINT" Persistent="true" />                   <!--词条强化等级-->
        <refineRecord Type="IntList" Persistent="true" />             <!--洗练词条历史记录-->
    </SEALED_RANDOM_ATTR>

    <SEALED_PROP_INFO Type="tuple">
        <sealedName Type="string" Persistent="true" />                                  <!--封印物名称-->
        <sealedResonance Type="UINT" Persistent="true" />                               <!--封印物共鸣度-->
        <sealedLevel Type="UINT" Persistent="true" />                                   <!--封印物强化等级（联调后清掉）-->
        <sealedBreakthrough Type="UINT" Persistent="true" />                            <!--封印物突破等级-->
        <sealedNormalProp Type="DictIntInt" Persistent="true" />                        <!--封印物固定品质(正面和负面)-->
        <sealedRandomProp Type="IntList" Persistent="true" />                           <!--封印物随机品质（联调后清掉）-->
        <sealedRandomPropInfo Type="dict" Key="UINT" Value="SEALED_RANDOM_ATTR" Persistent="true" />         <!--封印物随机品质词条信息-->
        <isEquip Type="BOOL" Persistent="true" />                                       <!--是否装备-->
        <randomRefineRes Type="IntList" Persistent="true" />                            <!--上次洗练结果-->
        <refineTimes Type="UINT" Persistent="true" />                                   <!--洗练次数-->
        <lucky Type="UINT" Persistent="true" />                                         <!--幸运值-->
        <bLock Type="BOOL" Persistent="true" />                                         <!--锁定状态-->
    </SEALED_PROP_INFO>

    <XTRA_MAT_PROP_INFO Type="tuple">
        <nameId Type="UINT" Persistent="true" Flags="OWN_CLIENT" />                                  <!-- 非凡物资名称ID，服务器没有多语言表只能记录id，客户端再去获取 -->
        <randomProps Type="list" Element="UINT" Persistent="true" Flags="OWN_CLIENT" />              <!-- 非凡物随机属性,词条 -->
        <mark Type="UINT" Persistent="true" Default="0" Flags="OWN_CLIENT" />                        <!-- 非凡物质评分 -->
        <equippedSealedId Type="UINT" Persistent="true" Default="nil" Flags="OWN_CLIENT" />          <!-- 非凡物质装在哪个封印物上 -->
        <lock Type="BOOL" Persistent="true" Default="false" Flags="OWN_CLIENT" />                    <!-- 非凡物质是否被锁定（锁定时无法合成或分解） -->
    </XTRA_MAT_PROP_INFO>
    
    <!-- 新装备 begin -->
    <EQUIPMENT_BASE_PROP Type="tuple">
        <propId Type="OBJECT_ID" Persistent="true" />
        <value Type="float" Persistent="true" />            <!-- 随机固定词条、随机个数加成后的基础属性值, 客户端显示 -->
        <realValue Type="float" Persistent="true" />        <!-- 真实的基础属性值, 用于数值计算 -->
        <baseValue Type="float" Persistent="true" />        <!-- 基础属性基础值 -->
        <propRate Type="float" Persistent="true" />          <!-- 随机固定词条加成的比例系数 -->
    </EQUIPMENT_BASE_PROP>

    <EQUIPMENT_EXTRA_RANDOM_PROP Type="tuple">
        <index Type="UINT" Persistent="true" />
        <randomProps Type="list" Element="UINT" Persistent="true" />                                <!-- 装备随机属性,词条 -->
    </EQUIPMENT_EXTRA_RANDOM_PROP>

    <EQUIPMENT_BASE_PROP_INFO Type="tuple">
        <baseProps Type="list" Element="EQUIPMENT_BASE_PROP" Persistent="true" />                   <!-- 装备基础属性 -->
        <mark Type="UINT" Default="0" />                                                            <!-- 装备基础属性得分 -->
    </EQUIPMENT_BASE_PROP_INFO>

    <EQUIPMENT_FIXED_PROP_INFO Type="tuple">
        <fixedProps Type="list" Element="UINT" Persistent="true" />                                 <!-- 装备固定属性 -->
        <mark Type="UINT" Default="0" />                                                            <!-- 装备固定属性得分 -->
    </EQUIPMENT_FIXED_PROP_INFO>

    <EQUIP_FIGHT_PROP_VALUE_BY_SUB_SOURCE Type="dict" Key="int" Value="DictStrFloat" Persistent="true" /> <!-- 装备战斗属性,按子来源分组, 组内是<propName, propValue> -->
    <EQUIP_FIGHT_PROP_VALUE_BY_SOURCE Type="dict" Key="int" Value="EQUIP_FIGHT_PROP_VALUE_BY_SUB_SOURCE"/>        <!-- 装备战斗属性,按来源分组 -->

    <EQUIP_PASSIVE_SKILL_BY_SUB_SOURCE Type="dict" Key="int" Value="DictIntInt" Persistent="true" /> <!-- 装备被动技能,按子来源分组, 组内是<skillID, skillLevel> -->
    <EQUIP_PASSIVE_SKILL_BY_SOURCE Type="dict" Key="int" Value="EQUIP_PASSIVE_SKILL_BY_SUB_SOURCE" Persistent="true" /> <!-- 装备被动技能,按来源分组 -->

    <EQUIPMENT_PROP_INFO Type="tuple">                                                              <!-- 装备属性 -->
        <basePropInfo Type="EQUIPMENT_BASE_PROP_INFO" Persistent="true" />
        <fixedPropInfo Type="EQUIPMENT_FIXED_PROP_INFO" /> <!-- todo:等客户端清理后删除 -->
        <randomPropInfo Type="EQUIPMENT_RANDOM_PROP_INFO" Persistent="true" />
    </EQUIPMENT_PROP_INFO>
    <!-- 新装备 end -->
    <INV_SLOT_VAL Type="tuple">
        <index Type="SLOT_INDEX" Persistent="true" Flags="OWN_CLIENT" />
        <gbId Type="UUID" Persistent="true" Flags="OWN_CLIENT" />
        <itemId Type="OBJECT_ID" Persistent="true" Flags="OWN_CLIENT" />
        <count Type="UINT" Persistent="true" Flags="OWN_CLIENT" />
        <bound Type="BOOL" Default="true" Persistent="true" Flags="OWN_CLIENT" />
        <useTimes Type="UINT" Persistent="true" Flags="OWN_CLIENT" />
        <expiryTime Type="UINT" Persistent="true" Flags="OWN_CLIENT" />
        <expired Type="BOOL" Persistent="true" Flags="OWN_CLIENT" />
        <quality Type="UINT" Persistent="true" Flags="OWN_CLIENT" />
        <isDeleted Type="BOOL" Default="false" Persistent="true"/>
        <enabled Type="int" Flags="SERVER_ONLY" Default="0"/>
        <purchaseInfo Type="PURCHASE_INFO" Persistent="true" Flags="OWN_CLIENT"/>
        <equipmentPropInfo Type="EQUIPMENT_PROP_INFO" Persistent="true" Default="nil" Flags="OWN_CLIENT"/>          <!-- 新装备属性 -->
        <xtraMatPropInfo Type="XTRA_MAT_PROP_INFO" Persistent="true" Default="nil" Flags="OWN_CLIENT"/>                <!-- 非凡物质属性 -->
        <!--<sealedPropInfo Type="SEALED_PROP_INFO" Persistent="true" Default="nil"/>                老封印物属性(已废弃)-->
		<equipEnhanceInfo Type="ListInt" Persistent="true" Default="nil" Flags="OWN_CLIENT"/>                <!--圣膏属性-->
        <isNewTip Type="BOOL" Default="true" Persistent="true" Flags="OWN_CLIENT"/>
    </INV_SLOT_VAL>
    <INV_SLOT_VAL_LIST Type="list" Element="INV_SLOT_VAL" Persistent="true" />
    <EQUIPMENT_INFO_SLOTS Type="dict" Key="SLOT_INDEX" Value="INV_SLOT_VAL" />
    <DropPersistentData Type="dict" Key="string" Value="INV_SLOT_VAL_LIST" Persistent="true" />
    <INV_SLOT_INFO Type="tuple">
        <slotNumber Type="UINT" Persistent="true" />
        <unlockTimes Type="UINT" Persistent="true" />
        <bAutoDecompose Type="bool" Persistent="true" />		<!--老数据，开发完新版分解去除-->
        <slots Type="EQUIPMENT_INFO_SLOTS" Persistent="true" />
    </INV_SLOT_INFO>
	<RANDOM_CLASS_LOCK_INFO Type="tuple">
		<lockInfo Type="DictIntBool" Default="{}" Flags="OWN_CLIENT" Persistent="true" />	<!-- key为装备栏slotIndex，value中key是randomWordClass，value为是否禁锢 -->
		<lockSwitch Type="BOOL" Default="false" Flags="OWN_CLIENT" Persistent="true" />								<!-- 洗练时是否打开屏蔽-->
	</RANDOM_CLASS_LOCK_INFO>
    <INV_INFO Type="dict" Key="UINT" Value="INV_SLOT_INFO" />
    <DECOMPOSE_INFO Type="tuple">
        <bOpen Type="bool" Flags="OWN_CLIENT" Persistent="true" Default="nil"/>
        <autoDecomposeQuality Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true" Default="nil"/>	    <!--哪些品质的道具自动分解-->
    </DECOMPOSE_INFO>
	<DECOMPOSE_CONFIG Type="tuple">
		<bAutoDecompose Type="bool" Flags="OWN_CLIENT" Persistent="true" />
        <decomposeInfoMap Type="dict" Key="UINT" Value="DECOMPOSE_INFO" Flags="OWN_CLIENT" Persistent="true" />
	</DECOMPOSE_CONFIG>

    <DECOMPOSE_CONFIGS Type="dict" Key="UINT" Value="DECOMPOSE_CONFIG" Persistent="true" Flags="OWN_CLIENT"/>
    <SEALED_SLOT_INFO Type="tuple">
        <itemId Type="OBJECT_ID" Persistent="true" />
        <gbId Type="UUID" Persistent="true" />
    </SEALED_SLOT_INFO>
    <SEFIROT_CORE_MARKS Type="tuple">
        <coreBuff Type="UINT" />
        <sealedSlotInfo Type="DictIntInt" />
    </SEFIROT_CORE_MARKS>
    <SEALED_INFO Type="tuple">
        <sealedId Type="UINT" Flags="OWN_CLIENT" Persistent="true" />    <!--封印物id -->
        <gbId Type="UUID" Flags="OWN_CLIENT" Persistent="true" />       <!--封印物gbId -->
        <equippedSlot Type="UINT" Flags="OWN_CLIENT" Persistent="true" Default="nil"/>       <!--封印物装备槽位 -->
        <xtraMatIdx Type="UINT" Flags="OWN_CLIENT" Persistent="true" Default="nil"/>       <!--非凡物质槽 -->
        <invalidWordIndexMap Type="DictIntBool" Flags="OWN_CLIENT" />    <!--封印物屏蔽的非凡物质词条 -->
    </SEALED_INFO>

    <SEALED_INFOS Type="dict" Key="UINT" Value="SEALED_INFO" Persistent="true" Flags="OWN_CLIENT"/> <!--封印物数据结构-->

    <XMAT_FILTER_INFO Type="tuple">
        <qualityFilter Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true"/>    <!--品质filter -->
        <wordNumFilter Type="UINT" Flags="OWN_CLIENT" Persistent="true" Default="0"/>       <!--词条数filter -->
        <wordFilter Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true"/>       <!--词条filter -->
    </XMAT_FILTER_INFO>
    
    <SEALED_EXTRA_COMPLETED_CONDITIONS Type="DictIntBool" Default="{}" Persistent="true" Flags="SERVER_ONLY"/>

    <WORD_NUM_GUAR_INFO Type="tuple">
        <curNum Type="UINT" Persistent="true" Flags="SERVER_ONLY"/>       <!--词条数量当前保底计数（每合成一次+1，合成时每次词条数量大于等于保底词条数量时清零）-->
        <guarNum Type="UINT" Persistent="true" Flags="SERVER_ONLY"/>      <!--词条数量保底值（通过配置在一定范围内随机）-->
    </WORD_NUM_GUAR_INFO>
    
    <FUSE_GUARANTEE Type="tuple">   <!--非凡物质合成保底结构-->
        <specialCurNum Type="UINT" Persistent="true" Flags="SERVER_ONLY" Default="nil"/>       <!--特殊词条当前保底计数（每生成一条普通词条+1，每生成一条特殊词条清零）-->
        <specialGuarNum Type="UINT" Persistent="true" Flags="SERVER_ONLY" Default="nil"/>      <!--特殊词条保底值（通过配置在一定范围内随机）-->
        <rareSpecialCurNum Type="UINT" Persistent="true" Flags="SERVER_ONLY" Default="nil"/>   <!--高稀有特殊词条当前保底计数（每生成一条特殊词条+1，每生成一条高稀有特殊词条清零）-->
        <rareSpecialGuarNum Type="UINT" Persistent="true" Flags="SERVER_ONLY" Default="nil"/>  <!--高稀有特殊词条保底值（通过配置在一定范围内随机）-->
        <wordNumGuarInfos Type="dict" Key="UINT" Value="WORD_NUM_GUAR_INFO" Persistent="true" Flags="SERVER_ONLY" Default="nil"/> <!--以合成父代双方词条总数为key的词条总数保底数据-->
    </FUSE_GUARANTEE>

    <FUSE_PROGRESS_REWARD_COLLECT_INFO Type="tuple">
        <progressReward Type="DictIntBool" Persistent="true" Flags="OWN_CLIENT" Default="nil"/> <!--进度奖励-->
        <cycleRewardTimes Type="UINT" Persistent="true" Flags="OWN_CLIENT" Default="0"/> <!--循环奖励领取次数-->
    </FUSE_PROGRESS_REWARD_COLLECT_INFO>
    <FUSE_PROGRESS_REWARD_COLLECT_INFOS Type="dict" Key="UINT" Value="FUSE_PROGRESS_REWARD_COLLECT_INFO" Persistent="true" Flags="OWN_CLIENT"/> <!--合成进度奖励领取记录-->

    <SEFIROT_CORE_INFO Type="tuple">
        <coreID Type="UINT" Persistent="true" />                                                          <!--源质核心类型id-->
        <sumResonance Type="UINT" Persistent="false" />                                                   <!--源质核心总共鸣度-->
        <coreBuff Type="list" Element="UINT" Persistent="false" />                                        <!--源质核心加成效果-->
        <sealedSlotInfo Type="dict" Key="SLOT_INDEX" Value="SEALED_SLOT_INFO" Persistent="true" />        <!--封印物装备插槽-->
        <marks Type="SEFIROT_CORE_MARKS" />
    </SEFIROT_CORE_INFO>
    <INV_RED_POINT_RECORD Type="dict" Key="UINT" Value="ARRAY_UINT" />

    <QuickUseItemLists Type="dict" Key="UINT" Value="ARRAY_UINT" />

    <GroupRoleBrief Type="tuple">
        <uid Type="string" />
        <name Type="string" />
        <botID Type="int" Default="0"/>
        <logicServerID Type="int" Flags="SERVER_ONLY" />
        <lv Type="int" />
        <profession Type="int" />
        <sex Type="int" />
        <zhanLi Type="int" />
        <mailbox Type="mailbox" />
        <voiceState Type="int" Default="1" />
        <worldID Type="int" />
        <mapInstID Type="string" />
        <planeID Type="int" />
        <isOnline Type="int" />
        <hp Type="float" />
        <maxHp Type="float" />
        <isDead Type="bool" />
        <bTargetByBoss Type="bool" />
        <expireTime Type="int" />
        <description Type="string" />
        <dungeonLock Type="bool"/>
        <guildId Type="string"/>
        <professionStateID Type="int"/>
    </GroupRoleBrief>

    <GroupRoleBriefs Type="dict" Key="string" Value="GroupRoleBrief" />

    <GroupRoleApplyClient Type="GroupRoleBrief">
        <uid Type="string" />
        <name Type="string" />
        <lv Type="int" />
        <profession Type="int" />
        <sex Type="int" />
        <zhanLi Type="int" />
        <worldID Type="int" />
        <mapInstID Type="string" />
        <voiceState Type="int" Default="1"/>
        <planeID Type="int" />
        <isOnline Type="int" />
        <hp Type="float" />
        <maxHp Type="float" />
        <isDead Type="bool" />
        <bTargetByBoss Type="bool" />
        <expireTime Type="int" />
        <description Type="string" />
    </GroupRoleApplyClient>

    <GroupRoleApplysClient Type="dict" Key="string" Value="GroupRoleApplyClient" />


    <GroupMemberApplyBrief Type="tuple">
        <leaderInfo Type="GroupRoleBrief" Default="nil"/>
        <memberProfessions Type="list" Element="int" Default="nil"/>
        <expireTime Type="int" />
    </GroupMemberApplyBrief>

    <GroupMemberApplyBriefs Type="dict" Key="TEAM_ID" Value="GroupMemberApplyBrief" />


    <GroupLeagueInviteInfo Type="tuple">
        <groupID Type="GROUP_ID" />
        <name Type="string" />
        <lv Type="int" />
        <profession Type="int" />
        <expireTime Type="int" />
    </GroupLeagueInviteInfo>

    <GroupLeagueInviteInfos Type="dict" Key="GROUP_ID" Value="GroupLeagueInviteInfo" />

    <GroupApplyPosArgs Type="tuple">
        <uid Type="string" />
        <pageIndex Type="int" Default="1"/>
        <teamIndex Type="int" />
        <teamFollowNum Type="int" Default="nil" />  <!-- 跟随入团人数(不包括本人) -->
    </GroupApplyPosArgs>

    <GroupDetails Type="tuple">
        <targetID Type="int" Default="0" Flags="SERVER_ONLY" />
        <positionNeed Type="AvatarActorTeamPositionNeedList" Flags="SERVER_ONLY" />
        <zhanliNeed Type="int" Default="0" Flags="SERVER_ONLY" />
        <description Type="string" Default="" Flags="SERVER_ONLY" />
    </GroupDetails>

    <GroupBriefInfo Type="tuple">
        <leaderUid Type="string" />
        <leaderLv Type="int" />
        <leaderName Type="string" />
        <leaderProfession Type="int" />
        <groupID Type="GROUP_ID" />
        <details Type="GroupDetails" />
        <memberInfos Type="DictIntInt" />
    </GroupBriefInfo>
    <BatchQueryGroupInfoList Type="list" Element="GroupBriefInfo"/>

    <GroupTeamMemberVal Type="tuple">
        <uid Type="string" />
        <name Type="string" />
        <botID Type="int" Default="0"/>
        <logicServerID Type="int" Flags="SERVER_ONLY" />
        <lv Type="int" />
        <profession Type="int" />
        <sex Type="int" />
        <teamUUID Type="TEAM_ID" />
        <bTeamLeader Type="bool" />
        <teamIndex Type="int" />
        <memberIndex Type="int" />
        <confirmState Type="int" />
        <worldID Type="int" />
        <mapInstID Type="string" />
        <planeID Type="int" />
        <memberFlag Type="int" />
        <voiceState Type="int" Default="1"/>
        <bFollow Type="bool" />
        <isOnline Type="int" />
        <hp Type="float" />
        <maxHp Type="float" />
        <isDead Type="bool" />
        <bTargetByBoss Type="bool" />
        <guildId Type="string"/>
        <professionStateID Type="int"/>
    </GroupTeamMemberVal>

    <GroupLeaderInfo Type="tuple">
        <uid Type="string" />
        <name Type="string" />
        <lv Type="int" />
        <profession Type="int" />
    </GroupLeaderInfo>

    <GroupSyncInfo Type="tuple">
        <isOnline Type="int" Default="nil"/>
        <hp Type="float" Default="nil"/>
        <maxHp Type="float" Default="nil"/>
        <isDead Type="bool" Default="nil"/>
        <worldID Type="int" Default="nil"/>
        <dungeonLock Type="bool" Default="nil"/>
        <mapInstID Type="string" Default="nil"/>
        <planeID Type="int" Default="nil"/>
        <guildId Type="GUILD_ID" Default="nil"/>
    </GroupSyncInfo>

    <GroupTeamMemberList Type="list" Element="GroupTeamMemberVal" />

    <GroupTeamMembers Type="dict" Key="int" Value="GroupTeamMemberList" />

    <GroupApplyInfo Type="tuple">
        <uid Type="string" />
        <name Type="string" />
        <lv Type="int" />
        <profession Type="int" />
        <sex Type="int" />
    </GroupApplyInfo>

    <GroupTeamSlot Type="tuple">
        <teamUUID Type="TEAM_ID" />
        <teamIndex Type="int" />
        <groupIndex Type="int" Default="1"/>
    </GroupTeamSlot>

    <GROUP_MEMBER_POS_CHANGE_SLOT Type="tuple">
        <teamIndex Type="int" />
        <memberIndex Type="int" />
    </GROUP_MEMBER_POS_CHANGE_SLOT>

    <GROUP_MEMBER_POS_CHANGE_MAP Type="dict" Key="ENTITY_ID" Value="GROUP_MEMBER_POS_CHANGE_SLOT" />    <!-- key为entityID，value为变化后的位置 -->


    <AdminMailAttachItem Type="tuple">
        <tid Type="int" />
        <count Type="int" />
        <isBound Type="int" />
    </AdminMailAttachItem>
    <AdminMailAttachment Type="list" Element="AdminMailAttachItem" />

    <GroupTeamMemberSlot Type="tuple">
        <uid Type="string" />
        <groupIndex Type="int" Default="1"/>
        <teamUUID Type="TEAM_ID" />
        <teamIndex Type="int" />
        <memberIndex Type="int" />
    </GroupTeamMemberSlot>

    <GroupApplyInfos Type="dict" Key="string" Value="GroupApplyInfo" />

    <GroupID2Time Type="dict" Key="GROUP_ID" Value="int" />

    <GroupID2Int Type="dict" Key="GROUP_ID" Value="int" />

    <Uid2Bool Type="dict" Key="string" Value="bool" />

    <GroupClientInfo Type="tuple">
        <groupId Type="GROUP_ID" />
        <leader Type="string" />
        <managers Type="Uid2Bool" />
        <details Type="GroupDetails" />
        <groupMembers Type="dict" Key="int" Value="GroupTeamMembers" />
        <bAutoMatch Type="bool" />
        <bAutoAgree Type="bool" />
        <confirmStartTime Type="int" />
        <spaceFlag Type="AvatarActorSpaceFlag" />
    </GroupClientInfo>

    <GroupClientInfos Type="list" Element="GroupClientInfo" />

    <GoodsIDs Type="list" Element="string"/>
    <AuctionIDs Type="list" Element="string"/>
    <RollIDs Type="list" Element="string"/>

    <DungeonAuctionQueue Type="list" Element="string"/>
    <DungeonAuctionSyncInfo Type="tuple">
        <StartTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <State Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <MoneyType Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <MoneyCount Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <BidderID Type="string" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <BidderName Type="string" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <BidTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonAuctionSyncInfo>

    <DungeonAuctionSyncInfos Type="dict" Key="string" Value="DungeonAuctionSyncInfo"/>

    <DungeonAuctionBidder Type="tuple">
        <Mailbox Type="mailbox" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <Name Type="string" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <LogicServerID Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonAuctionBidder>
    <DungeonAuctionBidders Type="dict" Key="string" Value="DungeonAuctionBidder"/>

    <DungeonRollBidder Type="tuple">
        <Mailbox Type="mailbox" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <Name Type="string" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <LogicServerID Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonRollBidder>
    <DungeonRollBidders Type="dict" Key="string" Value="DungeonRollBidder"/>

    <DungeonGoodsInfos Type="dict" Key="string" Value="INV_SLOT_VAL"/>

    <DungeonAuctionInfo Type="tuple">
        <SeqNo Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <StartTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <ClearTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <Queue Type="DungeonAuctionQueue" Flags="SERVER_ONLY" Persistent="true"/>
        <CurIndex Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <GoodsInfos Type="DungeonGoodsInfos" Flags="SERVER_ONLY" Persistent="true"/>
        <SyncInfos Type="DungeonAuctionSyncInfos" Flags="SERVER_ONLY" Persistent="true"/>
        <bGiveUps Type="dict" Key="string" Value="bool" Flags="SERVER_ONLY" Persistent="true"/>
        <bDividends Type="dict" Key="string" Value="bool" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonAuctionInfo>
    <DungeonAuctions Type="dict" Key="string" Value="DungeonAuctionInfo"/>

    <DungeonAuctionSyncStateInfo Type="tuple">
        <CurIndex Type="int" Flags="SERVER_ONLY"/>
        <SyncInfos Type="dict" Key="string" Value= "DungeonAuctionSyncInfo" Flags="SERVER_ONLY"/>
    </DungeonAuctionSyncStateInfo>

    <DungeonAuctionSyncStateInfos Type="dict" Key="string" Value="DungeonAuctionSyncStateInfo"/>


    <DungeonAuctionSyncClientInfo Type="tuple">
        <StartTS Type="int" Default="0" Flags="SERVER_ONLY"/>
        <Queue Type="DungeonAuctionQueue" Flags="SERVER_ONLY"/>
        <CurIndex Type="int" Flags="SERVER_ONLY"/>
        <GoodsInfos Type="DungeonGoodsInfos" Flags="SERVER_ONLY"/>
        <SyncInfos Type="dict" Key="string" Value= "DungeonAuctionSyncInfo" Flags="SERVER_ONLY"/>
        <bGiveUps Type="dict" Key="string" Value= "bool" Flags="SERVER_ONLY"/>
    </DungeonAuctionSyncClientInfo>
    <DungeonAuctionSyncClientInfos Type= "dict" Key="string" Value="DungeonAuctionSyncClientInfo"/>

    <DungeonAuctionSyncStartInfos Type= "dict" Key="string" Value="int"/>

    <DungeonAuctionOrder Type="tuple">
        <CreateTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <MoneyType Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <MoneyCount Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonAuctionOrder>
    <DungeonAuctionOrderInfo Type= "dict" Key="string" Value="DungeonAuctionOrder"/>
    <DungeonAuctionOrderInfos Type= "dict" Key="string" Value="DungeonAuctionOrderInfo"/>
    <DungeonAuctionOrders Type= "dict" Key="string" Value="DungeonAuctionOrderInfos"/>

    <DungeonAuctionEstimation Type="tuple">
        <OrderID Type="string" Default="" Flags="SERVER_ONLY" Persistent="true"/>
        <MoneyType Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true"/>
        <MoneyCount Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true"/>
    </DungeonAuctionEstimation>
    <DungeonAuctionEstimationInfo Type= "dict" Key="string" Value="DungeonAuctionEstimation"/>
    <DungeonAuctionEstimationInfos Type="dict" Key="string" Value="DungeonAuctionEstimationInfo"/>

    <DungeonRollResult Type="tuple">
        <WinID Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <WinName Type="string" Flags="SERVER_ONLY" Default="" Persistent="true"/>
        <WinState Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <WinPoint Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonRollResult>
    <DungeonRollState Type="tuple">
        <State Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <Point Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <RollResult Type="DungeonRollResult" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonRollState>

    <DungeonRollSuccess Type="dict" Key="string" Value="bool"/>

    <DungeonRollStates Type="dict" Key="string" Value="DungeonRollState"/>
    <DungeonRollClientSyncStates Type="dict" Key="string" Value="DungeonRollStates"/>
    <DungeonRollInfo Type="tuple">
        <StartTS Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <GoodsInfos Type="DungeonGoodsInfos" Flags="SERVER_ONLY" Persistent="true"/>
        <RollStates Type="DungeonRollStates" Flags="SERVER_ONLY" Persistent="true"/>
        <RollSuccess Type="DungeonRollSuccess" Flags="SERVER_ONLY" Persistent="true"/>
    </DungeonRollInfo>
    <DungeonRollInfos Type="dict" Key="string" Value="DungeonRollInfo"/>

    <DungeonRollRollIDs Type="dict" Key="string" Value="DictStrInt"/>
    <DungeonRollClientSyncInfos Type="dict" Key="string" Value="DungeonRollInfo"/>

    <ID_LIST Type="list" Element="OBJECT_ID" />
    <ID_TIME Type="dict" Key="OBJECT_ID" Value="LONGTIME" />
    <GBID_LIST Type="list" Element="string" />
    <ENTITY_ID_LIST Type="list" Element="ENTITY_ID" />
    <GM_LOCK_MONEY Type="tuple">
        <time Type="LONGTIME" Persistent="true" />
        <amount Type="UINT" Persistent="true" />
        <bail Type="UINT" Persistent="true" />
        <reason Type="string" Persistent="true" />
        <startTime Type="LONGTIME" Persistent="true" />
        <tradeLock Type="BOOL" Persistent="true" />
        <lockTakeMoney Type="BOOL" Persistent="true" />
        <lockPutItem Type="BOOL" Persistent="true" />
    </GM_LOCK_MONEY>
    <GM_LOCK_MONEY_MAP Type="tuple">
        <boundMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <unboundMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <rmbMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <boundRmbMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <fellowMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <boundFellowMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <skillMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <guildMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <!--<battleFieldMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp0 Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp1 Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp2 Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp3 Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp4 Type="GM_LOCK_MONEY" Persistent="true" />
        <exploreExp5 Type="GM_LOCK_MONEY" Persistent="true" />
        <xiaValueMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <angrySquamaPoints Type="GM_LOCK_MONEY" Persistent="true" />
        <vipExp Type="GM_LOCK_MONEY" Persistent="true" />
        <climbTowerPoints Type="GM_LOCK_MONEY" Persistent="true" />
        <niceClothScore Type="GM_LOCK_MONEY" Persistent="true" />
        <territoryPoints Type="GM_LOCK_MONEY" Persistent="true" />
        <fairyParkPoints Type="GM_LOCK_MONEY" Persistent="true" />
        <petDrawNormalMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <petDrawRarityMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <runeMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <explorePoints Type="GM_LOCK_MONEY" Persistent="true" />
        <whaleCoin Type="GM_LOCK_MONEY" Persistent="true" />
        <giftPoints Type="GM_LOCK_MONEY" Persistent="true" />
        <petDrawElementMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <mapGameCoin Type="GM_LOCK_MONEY" Persistent="true" />
        <unlockAppearancePoints Type="GM_LOCK_MONEY" Persistent="true" />
        <battlePassExp Type="GM_LOCK_MONEY" Persistent="true" />
        <mentorMoney Type="GM_LOCK_MONEY" Persistent="true" />
        <springFestivalCoin Type="GM_LOCK_MONEY" Persistent="true" />
        <appearanceDrawCoin Type="GM_LOCK_MONEY" Persistent="true" />
        <appearanceScore Type="GM_LOCK_MONEY" Persistent="true" />
        <appearanceDrawBindCoin Type="GM_LOCK_MONEY" Persistent="true" /> -->
    </GM_LOCK_MONEY_MAP>
    <GM_BAIL_INFO Type="tuple">
        <time Type="LONGTIME" Persistent="true" />
        <bail Type="UINT" Persistent="true" />
        <reason Type="string" Persistent="true" />
    </GM_BAIL_INFO>
    <GM_BAIL_INFO_MAP Type="dict" Key="UINT" Value="GM_BAIL_INFO" />
    <MONEY_NEGATIVE_VALUE Type="tuple">
        <boundMoney Type="UINT" Persistent="true" />
        <unboundMoney Type="UINT" Persistent="true" />
        <rmbMoney Type="UINT" Persistent="true" />
        <boundRmbMoney Type="UINT" Persistent="true" />
    </MONEY_NEGATIVE_VALUE>
    <GM_CHAT_BAIL_NEED_PAY Type="tuple">
        <startTime Type="LONGTIME" Persistent="true" />
        <forbiddenTime Type="LONGTIME" Persistent="true" />
        <bail Type="UINT" Persistent="true" />
    </GM_CHAT_BAIL_NEED_PAY>

    <GM_ARG_INFO Type="ARRAY_STRING" />
    <GM_ARG_LIST Type="list" Element="GM_ARG_INFO" />
    <GM_DETAIL_INFO Type="tuple">
        <name Type="string" />
        <desc Type="string" />
        <args Type="GM_ARG_LIST" />
    </GM_DETAIL_INFO>
    <GM_KEY_TO_DETAIL Type="dict" Key="string" Value="GM_DETAIL_INFO" />
    <GM_CMD_MAP Type="dict" Key="string" Value="GM_KEY_TO_DETAIL" />

    <INV_CHANGE_SOURCE_INFO Type="tuple">
        <source Type="UINT" />
        <args Type="none" />
        <subSource Type="UINT" />
    </INV_CHANGE_SOURCE_INFO>
    <INV_CHANGE_SOURCE_INFO_MAP Type="dict" Key="UINT" Value="INV_CHANGE_SOURCE_INFO" />

    <INV_SLOT_COUNT_MAP Type="dict" Key="SLOT_INDEX" Value="UINT" />
    <INV_COUNT_MAP Type="dict" Key="UINT" Value="INV_SLOT_COUNT_MAP" />
    <INV_SLOT_VAL_MAP Type="dict" Key="SLOT_INDEX" Value="INV_SLOT_VAL" />
    <INV_VAL_MAP Type="dict" Key="UINT" Value="INV_SLOT_VAL_MAP" />
    <INV_PRICE_CHANGE_MAP Type="dict" Key="UINT" Value="UINT_MAP" />
    <PERCENTAGE Type="float" />

    <ITEM_USE_SHARE_TYPE_INFO Type="dict" Key="UINT" Value="DictIntInt" />
    <ITEM_SHARE_USE_GROUP Type="dict" Key="UINT" Value="ITEM_USE_SHARE_TYPE_INFO" />
    <ARRAY_UUID Type="list" Element="UUID" />
    <DECOMPOSE_ITEM_INFO Type="tuple">
        <itemId Type="OBJECT_ID" />
        <quality Type="UINT" />
        <number Type="UINT" />
        <enhanceLv Type="UINT" />
    </DECOMPOSE_ITEM_INFO>
    <ITEM_BIND_NUM_INFO Type="tuple">
        <boundNum Type="UINT" />
        <unboundNum Type="UINT" />
    </ITEM_BIND_NUM_INFO>
    <ITEM_ID_NUM_INFO Type="dict" Key="OBJECT_ID" Value="ITEM_BIND_NUM_INFO" />

    <ITEM_USE_EXTRA_DATA Type="tuple">
        <pickItems Type="list" Element="ListInt" />
    </ITEM_USE_EXTRA_DATA>

    <ITEM_LIST_INFO Type="dict" Key="SLOT_INDEX" Value="UUID" />

    <ITEM_INDEX_USE_INFO Type="tuple">
        <invId Type="UINT" />
        <index Type="SLOT_INDEX" />
        <gbId Type="UUID" />
        <number Type="UINT" />
    </ITEM_INDEX_USE_INFO>

    <ITEM_INDEX_USE_INFO_LIST Type="list" Element="ITEM_INDEX_USE_INFO" />

    <AVATAR_HEAD Type="tuple"> <!-- 玩家头像信息 -->
        <ID Type="string" Default=""/>
        <IconID Type="int" Default="0" />
        <ProfessionID Type="int" Default="0" />
        <Level Type="int" Default="0" />
        <Name Type="string" Default="" />
        <Sex Type="int" Default="0" />
    </AVATAR_HEAD>

    <CHAT_SEND_INFO Type="tuple">
        <sendNum Type="UINT" Flags="OWN_CLIENT" Persistent="true" />
        <lastSendTime Type="UINT" Flags="OWN_CLIENT" Persistent="true" />
    </CHAT_SEND_INFO>
    <CHAT_SEND_INFO_MAP Type="dict" Key="UINT" Value="CHAT_SEND_INFO" />
    <CHANNEL_AUTO_PLAY_VOICE_MAP Type="dict" Key="UINT" Value="BOOL" />
    <RECENT_CHAT_INFO_FOR_REPORT Type="tuple">
        <channelName Type="string" Persistent="true" />
        <text Type="string" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
    </RECENT_CHAT_INFO_FOR_REPORT>
    <RECENT_CHAT_INFO_FOR_REPORT_MAP Type="dict" Key="UINT" Value="RECENT_CHAT_INFO_FOR_REPORT" />

    <CHAT_TEAM_RECRUIT_INFO Type="tuple">
        <!-- <goal Type="UINT" /> -->
        <!-- <minLv Type="UINT" /> -->
        <!-- <maxLv Type="UINT" /> -->
        <teamUUID Type="string" />
        <teamMemberInfo Type="none" />
        <!-- <guildId Type="GUILD_ID" /> -->
    </CHAT_TEAM_RECRUIT_INFO>

    <CHAT_STICKER_INFO Type="tuple">
        <stickerType Type="string" Persistent="true" />
        <stickerValue Type="string" Persistent="true" />
    </CHAT_STICKER_INFO>

    <EMOJI_INFO Type="tuple">
        <id Type="int" Persistent="true" />
        <imgPath Type="string" Default="nil" Persistent="true" />
    </EMOJI_INFO>

    <CHAT_VOICE_INFO Type="tuple">
        <voiceKey Type="string" Persistent="true" />
        <voiceDuration Type="UINT" Persistent="true" />
        <isVoiceToText Type="BOOL" Persistent="true" />
        <isSecretRedpacket Type="BOOL" Default = "nil"  Persistent="true" />
    </CHAT_VOICE_INFO>

    <QUESTION_INFO Type="tuple">
        <id Type="UINT" Persistent="true" />
        <type Type="UINT" Persistent="true" />
        <groupId Type="UINT" Persistent="true" />
    </QUESTION_INFO>

    <SYSTEM_TEXT_INFO Type="tuple">
        <noticeId Type="UINT" Persistent="true"/>
        <noticeArgs Type="string" Persistent="true"/>
    </SYSTEM_TEXT_INFO>

    <ONE_TIME_ANNOUNCE_INFO Type="tuple">
        <priority Type="UINT" Persistent="true" />
        <school Type="UINT" Persistent="true" />
        <physique Type="UINT" Persistent="true" />
        <msgId Type="UINT" Persistent="true" />
    </ONE_TIME_ANNOUNCE_INFO>

    <TEAM_GROUP_RECRUIT_INFO Type="tuple">
        <goal Type="UINT" Persistent="true" />
        <minLv Type="UINT" Persistent="true" />
        <maxLv Type="UINT" Persistent="true" />
        <!-- <teamGroupId Type="TEAM_GROUP_ID" Persistent="true" /> -->
        <!-- <guildId Type="GUILD_ID" Persistent="true" /> -->
    </TEAM_GROUP_RECRUIT_INFO>

    <NPC_CHAT_CHOICE_INFO Type="tuple">
        <debateGroupId Type="UINT" Persistent="true" /> <!-- 辩论组Id -->
        <choice Type="UINT_MAP" Persistent="true" /> <!-- 当前辩论选择 -->
        <isTimeout Type="BOOL" Persistent="true" /> <!-- 私聊超时结束 -->
    </NPC_CHAT_CHOICE_INFO>

    <EXPLORE_COURSE_ASSIST_INFO Type="tuple">
        <courseId Type="UINT" Persistent="true" />
    </EXPLORE_COURSE_ASSIST_INFO>

    <LOUD_SPEAKER_INFO Type="tuple">
        <loudSpeakerId Type="UINT" Persistent="true" />
        <noticeId Type="UINT" Persistent="true"/>
        <noticeArgs Type="string" Persistent="true"/>
    </LOUD_SPEAKER_INFO>

    <COLOUR_FONT_INFO Type="tuple">
        <colourFontId Type="UINT" Persistent="true" />
    </COLOUR_FONT_INFO>

    <CHAT_CUSTOM_IMG Type="tuple">
        <id Type="string"  Persistent="true" />
        <res Type="string"  Persistent="true" />
        <status Type="int"  Persistent="true" />
        <updatedAt Type="LONGTIME"  Persistent="true" />
    </CHAT_CUSTOM_IMG>
    <CHAT_CUSTOM_IMG_LIST Type="list" Element="CHAT_CUSTOM_IMG" />

    <CHAT_SHARE_INTERFACE_INFO Type="tuple">
        <interfaceId Type="UINT" Persistent="true" />
        <endTime Type="LONGTIME" Persistent="true" />
        <curProgress Type="UINT" Persistent="true" />
        <totalProgress Type="UINT" Persistent="true" />
    </CHAT_SHARE_INTERFACE_INFO>

    <CHAT_FUTURE_INFO Type="tuple">
        <futureMessage Type="string" Default="" Persistent="true" />
    </CHAT_FUTURE_INFO>

    <CHAT_AFFIX_INFO  Type="tuple">
        <titleId Type="UINT" Persistent="true"  />
        <titleQuality Type="UINT" Persistent="true" />
        <titleArgs Type="string" Persistent="true" />
    </CHAT_AFFIX_INFO>

    <SENDER_INFO Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <entityId Type="ENTITY_ID" />
        <rolename Type="string" Default="nil" Persistent="true" />
        <photo Type="string" Default="nil" Persistent="true" />
        <school Type="UINT" Default="nil" Persistent="true" />
        <lv Type="UINT" Default="nil" Persistent="true" />
        <sex Type="UINT" Default="nil" Persistent="true" />
        <awakenId Type="UINT" Default="nil" Persistent="true" />
        <curHeadFrame Type="UINT" Default="nil" Persistent="true" />
        <curChatBubble Type="UINT" Default="nil" Persistent="true" />
        <curBadges Type="UINT_MAP" Default="nil" Persistent="true" />
        <!-- <decorations Type="ICON_DECORATIONS" Default="nil" Persistent="true" /> -->
        <affixInfo Type="CHAT_AFFIX_INFO" Default="nil" Persistent="true" />
        <physique Type="STATUS" Persistent="true" />
        <anonymityNameId Type="UINT" Persistent="true" />
        <anonymityImgId Type="UINT" Persistent="true" />
        <hostId Type="UINT" Default="nil" /> <!-- 如果是全服喇叭 传一下发送者所在区服ID -->
        <crossToHostId Type="UINT" Default="nil" /> <!-- 如果在中心服 传一下中心服ID -->
        <guildName Type="string" Default="nil" Persistent="true" /> <!-- 公会名字 -->
        <guildBadgeFrameId Type="UINT" Default="nil" Persistent="true" /> <!-- 公会旗帜 -->
        <npcId Type="UINT" Default="nil" Persistent="true" /> <!-- npcId -->
        <honorificInfo Type="TitleInfo" Default="nil" Persistent="true" /> <!-- 头衔信息 -->
        <portrait Type="int" Default="nil" Persistent="true"/>			<!-- 玩家头像 -->
		<portraitFrame Type="int" Default="nil" Persistent="true"/>	<!-- 玩家头像框 -->
        <chatBubble Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>	<!-- 聊天气泡 -->
    </SENDER_INFO>

	<RED_PACKET_RECEIVE_INFO Type="tuple">
		<rolename Type="string" Default="nil" Persistent="true" />
		<school Type="UINT" Default="nil" Persistent="true" />
		<count Type="UINT" Default="nil" Persistent="true" />		  <!-- 货币红包钱数 -->
		<goodsId Type="UINT" Default="nil" Persistent="true" />        <!-- 道具红包goodsId -->
        <time Type="LONGTIME" Default="nil" Persistent="true" />       <!-- 领取时间 -->
	</RED_PACKET_RECEIVE_INFO>

    <RED_PACKET_CURRENT_STATUS Type="tuple"> <!-- 红包当前领取状态 -->
        <receiveMoneyDict Type="dict" Key="ENTITY_ID" Value="RED_PACKET_RECEIVE_INFO" Default="nil" Persistent="true" />  <!-- 玩家已领取的红包数 -->
        <receiveGoodsDict Type="dict" Key="ENTITY_ID" Value="RED_PACKET_RECEIVE_INFO" Default="nil" Persistent="true" />  <!-- 玩家已领取的道具id -->
        <guildID Type="GUILD_ID" Default="nil" Persistent="true" /> <!-- 公会id -->
        <teamID Type="TEAM_ID" Default="nil" Persistent="true" /> <!-- 队伍id -->
        <groupID Type="GROUP_ID" Default="nil" Persistent="true" /> <!-- 组团id -->
        <teaRoomID Type="TEAROOM_ID" Default="0" Persistent="true" /> <!-- 茶壶房间id -->
        <packetTime Type="LONGTIME" Default="nil" Persistent="true" /> <!-- 红包创建时间 -->
        <senderInfo Type="SENDER_INFO" Default="nil" Persistent="true" />  <!-- 发红包人信息 -->
    </RED_PACKET_CURRENT_STATUS>

    <RED_PACKET_INFO Type="tuple"> <!-- 红包数据 -->
        <packetClass Type="UINT" Persistent="true" /> <!-- 红包大类(1=绑金礼金，2=道具礼金) -->
        <packetType Type="UINT" Persistent="true" /> <!-- 红包小类(1=拼手气礼金，2=密文礼金，3=密码礼金) -->
        <packetChannel Type="UINT" Persistent="true" /> <!-- 红包频道 -->
        <sourceType Type="UINT" Default="0" Persistent="true" /> <!-- 来源 -->
        <uuid Type="UUID" Persistent="true" /> <!-- 红包uuid(发红包不需要传) -->
        <moneyType Type="UINT" Default="0" Persistent="true" /> <!-- 慎重!!! 红包的货币类型 (用于系统发放), 玩家发放的只支持金镑, 不要支持其他的货币类型（会破坏经济系统）-->
        <moneyNum Type="UINT" Default="nil" Persistent="true" />   <!-- 红包金额(道具红包是道具等效金镑数目) -->
        <moneyMinNum Type="UINT" Default="nil" Persistent="true" />   <!-- 每个人领取红包的保底金额(仅限代币红包生效) -->
        <packNum Type="UINT" Default="nil" Persistent="true" />   <!-- 红包数目 -->
        <shopGoodsMap Type="UINT_MAP" Default="nil" Persistent="true" />   <!-- key:商品goodsId value:数目 -->
        <message Type="string" Default="nil" Persistent="true" />   <!-- 赠言 -->
        <emojiInfo Type="EMOJI_INFO" Default="nil" Persistent="true" />   <!-- 表情 -->
        <passwd Type="string" Default="nil" Persistent="true" />   <!-- 密码/密文 -->
        <currentStatus Type="RED_PACKET_CURRENT_STATUS" Default="nil" Persistent="true" />   <!-- 红包当前领取状态(发红包不需要传) -->
        <bIsFinished Type="BOOL" Default="nil" Persistent="true" /> <!-- 是否抢光(发红包不需要传) -->
        <limitUserIDs Type="ARRAY_UUID" Default="nil" Persistent="true" /> <!-- 允许领取的玩家ID -->
    </RED_PACKET_INFO>

    <RED_PACKET_INFO_LIST Type="list" Element="RED_PACKET_INFO" /> <!-- 红包数据列表 -->
    <RED_PACKET_INFO_DICT Type="dict" Key="UUID" Value="RED_PACKET_INFO" /> <!-- 红包数据dict, key为红包id, value为红包数据 -->

    <RED_PACKET_RECORD Type="tuple"> <!-- 红包记录 -->
        <packetClass Type="UINT" Persistent="true" />   <!-- 红包大类(1=绑金礼金，2=道具礼金) -->
        <packetType Type="UINT" Persistent="true" /> <!-- 红包小类(1=拼手气礼金，2=密文礼金，3=密码礼金) -->
        <time Type="LONGTIME" Persistent="true" />  <!-- 赠送或领取时间 -->
        <moneyNum Type="UINT" Default="nil" Persistent="true" />   <!-- 红包金额(赠送记录) -->
        <packNum Type="UINT" Persistent="true" />   <!-- 总个数(赠送记录) -->
        <receivedNum Type="UINT" Default="nil" Persistent="true" /> <!-- 已领取的人数(赠送记录) -->
        <bIsBest Type="BOOL" Default="nil" Persistent="true" /> <!-- 是否为最佳红包(领取记录) -->
        <receivePrice Type="UINT" Default="nil" Persistent="true" /> <!-- 已领取的金镑数目或道具价格(赠送记录) -->
		<rolename Type="string" Default="nil" Persistent="true" /> <!-- 领取人名字 -->
        <uuid Type="UUID" Persistent="true" /> <!-- 红包uuid -->
        <packetChannel Type="UINT" Persistent="true" /> <!-- 红包频道 -->
    </RED_PACKET_RECORD>

    <RED_PACKET_RECORD_DICT Type="dict" Key="UINT" Value="RED_PACKET_RECORD" />

    <RED_PACKET_RECORD_TYPE_OVERVIEW Type="tuple">     <!-- 红包数据类型总览 -->
        <totalAmount Type="UINT" Persistent="true" />       <!-- 收或发总金额 -->
        <totalCount Type="UINT" Persistent="true" />        <!-- 收或发总次数 -->
        <bestCount Type="UINT" Default="nil" Persistent="true" />         <!-- 手气最佳总数(领取记录) -->
        <receiveCount Type="UINT" Default="nil" Persistent="true" />      <!-- 已领取总数(赠送记录) -->
    </RED_PACKET_RECORD_TYPE_OVERVIEW>

    <RED_PACKET_RECORD_YEAR_OVERVIEW Type="dict" Key="UINT" Value="RED_PACKET_RECORD_TYPE_OVERVIEW" />    <!-- 红包数据赛年总览 key:类型, value:类型总览 -->

    <memberRecruitInfo Type="tuple">
        <lv Type="int" />
        <profession Type="int" />
        <enterTime Type="int" />
        <isCaptain Type="bool" />
    </memberRecruitInfo>

    <teamRecruitInfo Type="tuple">
        <teamUid Type="TEAM_ID" />
        <details Type="GroupDetails" />
        <memberInfos Type="dict" Key="string" Value="memberRecruitInfo" />
        <bInSeekRescue Type="bool" />
    </teamRecruitInfo>

    <groupRecruitInfo Type="tuple">
        <groupID Type="GROUP_ID" />
        <details Type="GroupDetails" />
        <memberInfos Type="DictIntInt" />
        <bInSeekRescue Type="bool" />
    </groupRecruitInfo>

    <GUILD_CHAT_TITLE Type="tuple">
        <id Type="int" Persistent="true" />
        <arg Type="string" Persistent="true" />
        <role Type="int" Persistent="true" />
    </GUILD_CHAT_TITLE>

    <guildResponseRecruitInfo Type="tuple">
        <id Type="string" Persistent="true" />
        <name Type="string" Persistent="true" />
        <memberNum Type="int" Persistent="true" />
        <guildType Type="int" Persistent="true" />
        <createTime Type="int" Persistent="true" />
        <badgeIndex Type="int" Persistent="true" />
        <badgeFrameId Type="int" Persistent="true" />
        <status Type="int" Persistent="true" />
    </guildResponseRecruitInfo>

    <guildJoinRecruitInfo Type="tuple">
        <id Type="string" />
        <name Type="string" />
        <lv Type="int" />
        <memberNum Type="int" />
        <maxMemberNum Type="int" />
        <badgeIndex Type="int" />
        <badgeFrameId Type="int" />
    </guildJoinRecruitInfo>

    <!-- 标签对应的用户列表, key 用户ID, Value 用户名称/匿名名称 -->
    <CHAT_EXPOSE_TAG_USER Type="tuple">
        <uid Type="string" />
        <nameID Type="UINT" />
        <realName Type="string" />
    </CHAT_EXPOSE_TAG_USER>
    <!-- 标签对应的用户列表, key 用户ID, Value 用户名称/匿名名称 -->
    <CHAT_EXPOSE_TAGS Type="dict" Key="string" Value="CHAT_EXPOSE_TAG_USER" />
    <!-- 揭露的信息 -->
    <CHAT_EXPOSE_INFO Type="tuple">
        <!--对应CHAT_INFO中的唯一ID-->
        <id Type="string" />
        <!--  点赞列表  element为用户ID -->
        <liked Type="UINT" />
        <!-- 揭露打的tag key为标签的ID -->
        <tags Type="dict" Key="UINT" Value="CHAT_EXPOSE_TAGS" />
        <!-- 是否已被揭露 -->
        <isExposed Type="BOOL" />
        <!-- 已被揭露的次数 -->
        <exposeCount Type="UINT" />
        <!-- 已被揭露的时间,用于计算置顶时间 -->
        <exposedTime Type="LONGTIME" />
        <!-- 创建时间 -->
        <createdAt Type="LONGTIME" />
    </CHAT_EXPOSE_INFO>

    <CHAT_ARGS_INFO Type="tuple">
        <teamRecruitInfo Type="teamRecruitInfo" Default="nil" Persistent="true" />
        <stickerInfo Type="CHAT_STICKER_INFO" Default="nil" Persistent="true" /> <!-- mark for deletion-->
        <voiceInfo Type="CHAT_VOICE_INFO" Default="nil" Persistent="true" />
        <redPacketInfo Type="RED_PACKET_INFO" Default="nil" Persistent="true" />
        <questionHelpInfo Type="QUESTION_INFO" Default="nil" Persistent="true" /> <!-- client unused -->
        <systemTextInfo Type="SYSTEM_TEXT_INFO" Default="nil" Persistent="true" />
        <groupRecruitInfo Type="groupRecruitInfo" Default="nil" Persistent="true" />
        <guildResponseRecruitInfo Type="guildResponseRecruitInfo" Default="nil" Persistent="true" />
        <guildJoinRecruitInfo Type="guildJoinRecruitInfo" Default="nil" Persistent="true" />
        <loudSpeakerInfo Type="LOUD_SPEAKER_INFO" Default="nil" Persistent="true" />
        <tailMessageText Type="string" Default="nil" Persistent="true" />
        <guildChatTitle Type="GUILD_CHAT_TITLE" Default="nil" Persistent="true" />
        <hrefArgs Type="DictIntStr" Default="nil" Persistent="true" />
        <equipIDs Type="ARRAY_STRING" Default="nil" Persistent="true" />  <!--聊天里发的装备唯一ID-->
        <anonymous Type="CHAT_EXPOSE_INFO" Default="nil" Persistent="true" /> <!-- 匿名聊天信息 -->
        <!-- <mapGameSummonArgs Type="MAP_GAME_SUMMON_ARGS" KeepNil="true" /> -->
    </CHAT_ARGS_INFO>
    <CHAT_INFO Type="tuple">
        <isShield Type="BOOL" Persistent="true" />
        <channelType Type="UINT" Persistent="true" />
        <messageType Type="UINT" Persistent="true" />
        <functionType Type="UINT" Persistent="true" />
        <messageText Type="string" Persistent="true" />
        <contentType Type="int" Default="0" Persistent="true" />
        <whisperReceiver Type="ENTITY_ID" Persistent="true" />
        <senderInfo Type="SENDER_INFO" Persistent="true" />
        <atPlayer Type="ENTITY_ID" Default="nil" Persistent="true" />
        <chatArgs Type="CHAT_ARGS_INFO" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
        <isAnonymity Type="BOOL" Default="nil" Persistent="true" /> <!-- 是否匿名 -->
        <isColourFont Type="BOOL" Default="nil" Persistent="true" />
        <opNUID Type="NUID" Default="" Persistent="true" />
        <id Type="string" Default="nil" Persistent="true" /> <!-- 聊天的唯一ID, 匿名聊天更新使用 -->
        <channelTypes Type="ARRAY_UINT" Default="nil" /> <!-- 群发多频道的频道类型列表 -->
        <whisperReceiverList Type="ENTITY_ID_LIST" Default="nil" /> <!-- 群发多频道的私聊接收者列表 -->
        <clubIdList Type="ARRAY_STRING" Default="nil" /> <!-- 群发多频道的群聊ID列表 -->
    </CHAT_INFO>
    <CHAT_INFO_LIST Type="list" Element="CHAT_INFO" />

    <SYNC_BOTHWAY_FRIEND_FREQUENT_INFO Type="tuple">
        <teamMemberSize Type="UINT" />
        <groupMemberSize Type="UINT" />
        <worldTemplateId Type="UINT" Default="nil" />              <!-- 当前World ID -->
        <worldReturnTemplateId Type="UINT" Default="nil" />        <!-- 当前暂离World ID -->
    </SYNC_BOTHWAY_FRIEND_FREQUENT_INFO>
    <SYNC_BOTHWAY_FRIEND_FREQUENT_MAP Type="dict" Key="ENTITY_ID" Value="SYNC_BOTHWAY_FRIEND_FREQUENT_INFO" />

    <ON_SYNC_PLAYER_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <specialGbId Type="GBID" />
        <isOffline Type="BOOL" />
        <rolename Type="string" />
        <school Type="int" />
        <lv Type="int" />
        <physique Type="STATUS" />
        <portrait Type="int" Default="nil"/>		<!-- 玩家头像 -->
		<portraitFrame Type="int" Default="nil"/>	<!-- 玩家头像框 -->
    </ON_SYNC_PLAYER_INFO>

    <PLAYER_EXERCISE Type="tuple">
        <lv Type="LEVEL" Persistent="true" />
        <exp Type="UINT" Persistent="true" />
    </PLAYER_EXERCISE>
    <PLAYER_EXERCISE_INFO Type="dict" Key="OBJECT_ID" Value="PLAYER_EXERCISE" />

    <NPC_ID Type="UINT" />
    <NPC_ID_LIST Type="list" Element="NPC_ID" />
    <NPC_APPLICATIONS Type="dict" Key="NPC_ID" Value="BOOL" />
    <NPC_APPLICATION_MAP Type="dict" Key="ENTITY_ID" Value="NPC_APPLICATIONS" />

    <APPLICATION_SOURCE_INFO Type="tuple">
        <sourceId Type="UINT" Persistent="true" />
        <subSourceId Type="UINT" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
    </APPLICATION_SOURCE_INFO>
    <APPLICATION_SOURCE_INFO_MAP Type="dict" Key="ENTITY_ID" Value="APPLICATION_SOURCE_INFO" />
    <GBID_2_BIT_FLAGS Type="dict" Key="ENTITY_ID" Value="BIT_FLAGS" />
    <SYNC_REFRESH_RECENT_WHISPER_INFO Type="tuple"> <!-- photo、lv、state有改动，后端主动推给前端 -->
        <lv Type="UINT" />
        <photo Type="string" />
        <state Type="UINT" />                        <!-- 删角、在线、离线、暂离 -->
        <offlineTime Type="LONGTIME" />              <!-- 下线时间 -->
        <rolename Type="string" />
        <guildId Type="GUILD_ID" />
        <guildName Type="string" />
        <worldTemplateId Type="UINT" />              <!-- ���前World ID -->
        <worldReturnTemplateId Type="UINT" />        <!-- 当前暂离World ID -->
    </SYNC_REFRESH_RECENT_WHISPER_INFO>

    <FRIEND_REFRESH_INFO Type="tuple">
        <rolename Type="string" Default="nil" /> <!-- 新角色名 -->
    </FRIEND_REFRESH_INFO>

    <ROLE_BRIEF Type="tuple">
        <id Type="ENTITY_ID" />
        <lv Type="LEVEL" />
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <hostId Type="UINT" Default="nil"/>
    </ROLE_BRIEF>

    <ROLE_BRIEF_LIST Type="list" Element="ROLE_BRIEF" />
    <ROLE_BRIEF_MAP Type="dict" Key="ENTITY_ID" Value="ROLE_BRIEF" />

    <FOE_PLAYER_INFO Type="tuple">
        <id Type="ENTITY_ID" Default="nil"/>
        <hostId Type="UINT" Default="nil"/>
        <rolename Type="ROLENAME" Default="nil"/>
        <school Type="SCHOOL" Default="nil"/>
        <!-- <awakenId Type="UINT" KeepNil="true"/> -->
        <lv Type="LEVEL" Default="nil"/>
        <recordTime Type="UINT" Default="nil"/>
        <!-- <guildName Type="string" KeepNil="true"/> -->
        <!-- <photo Type="string" KeepNil="true"/>
        <curHeadFrame Type="UINT" KeepNil="true"/>
        <physique Type="STATUS" KeepNil="true"/> -->
        <state Type="UINT" Default="nil"/>
        <hateValue Type="UINT" Default="nil"/>
    </FOE_PLAYER_INFO>
    <FOE_LIST Type="list" Element="FOE_PLAYER_INFO"/>
    <FOE_MAP Type="dict" Key="ENTITY_ID" Value="FOE_PLAYER_INFO"/>
    <SIMPLE_FOE_INFO Type="tuple">
        <hateValue Type="UINT" Persistent="true"/>
        <rolename Type="string" Persistent="true"/>
        <recordTime Type="UINT" Persistent="true" />
    </SIMPLE_FOE_INFO>
    <SIMPLE_FOE_MAP Type="dict" Key="ENTITY_ID" Value="SIMPLE_FOE_INFO"/>

    <FRIEND_INFO Type="tuple">
        <groupId Type="UINT" Persistent="true" />
        <attraction Type="UINT" />
        <attractionLevel Type="UINT" />
        <remark Type="string" Persistent="true" />
        <remindFlag Type="BIT_FLAGS" Persistent="true" /> <!--TODO@lyw: 这个有用吗？review一下之后确定-->
        <sourceId Type="UINT" Default="nil" Persistent="true" />
        <subSourceId Type="UINT" Default="nil" Persistent="true" />
        <time Type="UINT" Flags="SERVER_ONLY" Persistent="true" />
        <validState Type="UINT" Flags="SERVER_ONLY" Persistent="true" />
        <noDisturb Type="bool" Default="false" Persistent="true"/>
        <showStatus Type="int" Default="0" Persistent="true"/>
        <isSet Type="bool" Default="false" Persistent="true"/>
        <sex Type="int" Persistent="true"/>
        <setTime Type="int" Default="nil" Persistent="true"/>
    </FRIEND_INFO>
    <FRIEND_INFO_MAP Type="dict" Key="ENTITY_ID" Value="FRIEND_INFO" />

     <FRIEND_ATTRACTION_INFO Type="tuple">
        <attraction Type="UINT" Persistent="true" />  <!-- 玩家之间的好感度 -->
        <attractionLevel Type="UINT" Persistent="true" />  <!-- 玩家之间的好感度等级 -->
    </FRIEND_ATTRACTION_INFO>
    <FRIEND_ATTRACTION_INFO_MAP Type="dict" Key="ENTITY_ID" Value="FRIEND_ATTRACTION_INFO" />

    <NPC_FRIEND_INFO Type="tuple">
        <remark Type="string" Persistent="true" />
    </NPC_FRIEND_INFO>
    <NPC_FRIEND_INFO_MAP Type="dict" Key="UINT" Value="NPC_FRIEND_INFO" />

    <BLACK_INFO_DICT Type="dict" Key="ENTITY_ID" Value="bool" />
    <GROUP_INFO Type="tuple">
        <id Type="UINT" Persistent = "true" />
        <name Type="string" Persistent = "true" />
    </GROUP_INFO>
    <GROUP_INFO_LIST Type="list" Element="GROUP_INFO" />

    <RECENTLY_MEET_INFO Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <sourceId Type="UINT" Persistent="true" />
        <subSourceId Type="UINT" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
    </RECENTLY_MEET_INFO>
    <RECENTLY_MEET_INFO_LIST Type="list" Element="RECENTLY_MEET_INFO" />

     <RECENTLY_WHISPER_INFO Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
        <lastWhisper Type="CHAT_INFO" Persistent="true" /> <!-- 最后一次私聊信息 -->
    </RECENTLY_WHISPER_INFO>
    <RECENTLY_WHISPER_LIST Type="list" Element="RECENTLY_WHISPER_INFO" />

    <!-- <TOP_WHISPER_INFO Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
    </TOP_WHISPER_INFO> -->
    <TOP_WHISPER_MAP Type="dict" Key="ENTITY_ID" Value="LONGTIME" Persistent="true" />

    <!-- <TOP_CLUB_INFO Type="tuple">
        <clubId Type="ENTITY_ID" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
    </TOP_CLUB_INFO> -->
    <TOP_CLUB_MAP Type="dict" Key="string" Value="LONGTIME" Persistent="true" />

    <WHISPER_PUSH_SETTINGS Type="tuple">
        <needPush Type="BOOL" Persistent="true" />
        <attractionLevel Type="UINT" Persistent="true" />
        <timeStart Type="LONGTIME" Persistent="true" />
        <timeEnd Type="LONGTIME" Persistent="true" />
    </WHISPER_PUSH_SETTINGS>

    <FRIEND_CIRCLE_ROLE_INFO Type="tuple">
        <gender Type="UINT" Persistent="true" /> <!-- 性别 -->
        <birthday Type="UINT" Persistent="true" /> <!-- 生日 -->
        <curCity Type="UINT" Persistent="true" /> <!-- 城市 -->
        <curProvince Type="UINT" Persistent="true" /> <!-- 区 -->
        <signName Type="ARRAY_STRING" Persistent="true" /> <!-- 签名 -->
        <!-- <iconDecorations Type="ICON_DECORATIONS" Persistent="true"/> -->
        <tagList Type="ARRAY_UINT" Persistent="true" /> <!-- 标签 -->
        <university Type="string" /> <!-- 学校 -->
        <thanks Type="string" /> <!-- 感谢 -->
    </FRIEND_CIRCLE_ROLE_INFO>

    <FRIEND_IMPRINT_INFO  Type="tuple">
        <rolename Type="ROLENAME" Default="" Persistent="true"/>
        <days Type="UINT" Default="0" Persistent="true"/> <!-- 连续互发消息的天数 -->
        <toOhterTime Type="LONGTIME" Default="0" Persistent="true"/> <!-- 主动互动的时间 -->
        <fromOhterTime Type="LONGTIME" Default="0" Persistent="true"/> <!-- 被动互动的时间 -->
        <lastInteractionTime Type="LONGTIME" Default="0" Persistent="true" /> <!-- 上一次互动的时间 -->
    </FRIEND_IMPRINT_INFO>
    <FRIEND_IMPRINT_INFO_MAP Type="dict" Key="ENTITY_ID" Value="FRIEND_IMPRINT_INFO" />
    <FRIEND_IMPRINT_TYPE_TO_INFO_MAP Type="dict" Key="UINT" Value="FRIEND_IMPRINT_INFO_MAP"/>

    <!-- 更新互动印记 -->
    <FRIEND_IMPRINT_UPDATE_INFO Type="tuple">
        <tgtRolename Type="ROLENAME" /> <!-- 目标角色名 -->
        <time Type="LONGTIME" /> <!-- 更新时间 -->
        <isSender Type="BOOL" /> <!-- 是否是发起者 -->
    </FRIEND_IMPRINT_UPDATE_INFO>
    <FRIEND_IMPRINT_UPDATE_ARRAY Type="list" Element="FRIEND_IMPRINT_UPDATE_INFO" />
    <FRIEND_IMPRINT_UPDATE_MAP Type="dict" Key="ENTITY_ID" Value="FRIEND_IMPRINT_UPDATE_ARRAY" /> <!-- key id -->
    <FRIEND_IMPRINT_UPDATE_TYPE_TO_MAP Type="dict" Key="UINT" Value="FRIEND_IMPRINT_UPDATE_MAP" /> <!-- key imprintType -->


    <WHISPER_CONTINUE_INFO Type="tuple">
        <id Type="ENTITY_ID" Default="" Persistent="true" /> <!-- 连续最频繁的玩家id -->
        <gbId Type="ENTITY_ID" Default="" Persistent="true" /> <!-- 连续最频繁的玩家id -->
        <rolename Type="ROLENAME" Default="" Persistent="true" /> <!-- 连续最频繁的玩家角色名 -->
        <days Type="UINT" Default="0" Persistent="true" /> <!-- 连续最频繁的天数 -->
        <time Type="LONGTIME" Default="0" Persistent="true" /> <!-- 获得连续时的时间戳 -->
    </WHISPER_CONTINUE_INFO>

    <PLAYER_DETAIL_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <shortUid Type="UINT" Default="nil" />
        <hostId Type="UINT" />
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <awakenId Type="UINT" />
        <lv Type="LEVEL" />
        <guildName Type="string" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <friendCircleInfo Type="FRIEND_CIRCLE_ROLE_INFO" />
        <physique Type="STATUS" />
        <sex Type="int"/>
    </PLAYER_DETAIL_INFO>
    <PLAYER_DETAIL_INFO_LIST Type="list" Element="PLAYER_DETAIL_INFO" />

    <FRIEND_RECOMMEND_INFO Type="tuple">
        <playerDetailInfo Type="PLAYER_DETAIL_INFO" />
    </FRIEND_RECOMMEND_INFO>
    <FRIEND_RECOMMEND_INFO_LIST Type="list" Element="FRIEND_RECOMMEND_INFO" />

    <SYNC_APPLICATION_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <hostId Type="UINT" />
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <lv Type="LEVEL" />
        <physique Type="STATUS" />
        <label Type="table" />
        <signName Type="ARRAY_STRING" />
        <awakenId Type="UINT" />
        <sourceId Type="UINT" />
        <subSourceId Type="UINT" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <sex Type="int"/>
        <time Type="LONGTIME" /> <!-- 申请时间 -->
    </SYNC_APPLICATION_INFO>

    <SYNC_APPLICATION_INFO_LIST Type="list" Element="SYNC_APPLICATION_INFO" />

    <SYNC_RELATION_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <shortUid Type="UINT" Default="nil" />
        <groupId Type="UINT" Default="nil" />
        <attraction Type="UINT" Default="nil"/>
        <attractionLevel Type="UINT" Default="nil"/>
        <remark Type="string" Default="nil"/>
        <hostId Type="UINT" Default="nil" />
        <rolename Type="ROLENAME" Default="nil"/>
        <state Type="number" Default="nil" />
        <offlineTime Type="LONGTIME" Default="nil"/>     <!-- 下线时间 -->
        <lv Type="LEVEL" Default="nil"/>
        <school Type="SCHOOL" Default="nil"/>
        <signName Type="ARRAY_STRING" Default="nil"/>
        <guildId Type="GUILD_ID" Default="nil"/>
        <guildName Type="string" Default="nil"/>
        <curHeadFrame Type="UINT" Default="nil"/>
        <curChatBubble Type="UINT" Default="nil"/>
        <curBadges Type="UINT_MAP" Default="nil"/>
        <photo Type="string" Default="nil"/>
        <ZhanLi Type="UINT" Default="nil"/>
        <remindFlag Type="BIT_FLAGS" Default="nil"/>
        <imprintMap Type="UINT_MAP" Default="nil" /> <!-- 好友互动印记数据 -->
        <physique Type="STATUS" Default="nil"/>
        <sourceId Type="UINT" Default="nil"/> <!-- 好友来源渠道 -->
        <subSourceId Type="UINT" Default="nil"/>
        <worldTemplateId Type="UINT" Default="nil"/>        <!-- 当前World ID -->
        <worldReturnTemplateId Type="UINT" Default="nil"/>  <!-- 当前暂离World ID -->
        <noDisturb Type="bool" Default="nil"/>
        <showStatus Type="int" Default="nil"/>
        <isSet Type="bool" Default="nil"/>
        <sex Type="int" Default="nil"/>
        <setTime Type="int" Default="nil"/>
        <portrait Type="int"/>	    <!-- 玩家头像 -->
		<portraitFrame Type="int"/>	<!-- 玩家头像框 -->
    </SYNC_RELATION_INFO>

    <SYNC_RELATION_INFO_LIST Type="list" Element="SYNC_RELATION_INFO" />

    <SYNC_NPC_RELATION_INFO Type="tuple">
        <npcId Type="NPC_ID" />
        <groupId Type="UINT" Default="nil" />
        <favorability Type="number" />
        <favorabilityLv Type="UINT" />
        <remark Type="string" />
        <sourceId Type="UINT" /> <!-- 来源渠道 -->
    </SYNC_NPC_RELATION_INFO>
    <SYNC_NPC_RELATION_INFO_LIST Type="list" Element="SYNC_NPC_RELATION_INFO" />

    <SIMPLE_MEMBER_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
        <lv Type="LEVEL" />
        <school Type="SCHOOL" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
        <attraction Type="UINT" />
        <isClientHosting Type="BOOL" />
        <!-- <spaceId Type="SPACE_ID" /> -->
    </SIMPLE_MEMBER_INFO>
    <SIMPLE_MEMBER_INFO_LIST Type="list" Element="SIMPLE_MEMBER_INFO" />

    <HOME_INVITE_VISITOR_PROFILE Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
        <lv Type="LEVEL" />
        <school Type="SCHOOL" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
    </HOME_INVITE_VISITOR_PROFILE>
    <HOME_INVITE_VISITOR_PROFILE_LIST Type="list" Element="HOME_INVITE_VISITOR_PROFILE" />

    <HOME_VISIT_PLAYER_PROFILE Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
        <lv Type="LEVEL" />
        <school Type="SCHOOL" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
        <homeGbId Type="ENTITY_ID" />
        <homeAddress Type="UINT" />
    </HOME_VISIT_PLAYER_PROFILE>
    <HOME_VISIT_PLAYER_PROFILE_LIST Type="list" Element="HOME_VISIT_PLAYER_PROFILE" />

    <HOME_INIVITE_MEMBER_PROFILE Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
        <lv Type="LEVEL" />
        <school Type="SCHOOL" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
        <attractionLv Type="UINT" />
    </HOME_INIVITE_MEMBER_PROFILE>
    <HOME_INIVITE_MEMBER_PROFILE_LIST Type="list" Element="HOME_INIVITE_MEMBER_PROFILE" />

    <SYNC_RELATION_INC_DATA Type="tuple">
        <id Type="ENTITY_ID" />
        <lv Type="LEVEL" />
        <state Type="number" />
        <signName Type="ARRAY_STRING" />
    </SYNC_RELATION_INC_DATA>
    <SYNC_RELATION_INC_DATA_LIST Type="list" Element="SYNC_RELATION_INC_DATA" />

    <SYNC_RECENTLY_MEET_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <lv Type="LEVEL" />
        <sourceId Type="UINT" />
        <subSourceId Type="UINT" />
        <time Type="LONGTIME" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
    </SYNC_RECENTLY_MEET_INFO>

    <SYNC_RECENTLY_MEET_INFO_LIST Type="list" Element="SYNC_RECENTLY_MEET_INFO" />

    <WHISPER_MESSAGE Type="tuple">
        <isOffline Type="BOOL"/>
        <chatInfo Type="CHAT_INFO"/>
    </WHISPER_MESSAGE>

    <WHISPER_MESSAGE_LIST Type="list" Element="WHISPER_MESSAGE" />


    <WHISPER_OFFLINE_INFO Type="tuple">
        <id Type="ENTITY_ID"/>
        <count Type="UINT"/>
    </WHISPER_OFFLINE_INFO>
    <WHISPER_OFFLINE_INFO_LIST Type="list" Element="WHISPER_OFFLINE_INFO" />


    <FRIEND_SYSTEM_MESSAGE Type="tuple">
        <id Type="UINT"/>
        <time Type="LONGTIME"/>
        <args Type="none"/>
    </FRIEND_SYSTEM_MESSAGE>
    <FRIEND_SYSTEM_MESSAGE_LIST Type="list" Element="FRIEND_SYSTEM_MESSAGE" />

    <!-- 捏脸数据end-->
    <ROLE_MODEL Type="tuple">
        <id Type="ENTITY_ID" />
        <lv Type="LEVEL" Persistent="true" />
        <rolename Type="ROLENAME" Persistent="true" />
        <!-- <school Type="SCHOOL" Persistent="true" />
        <awakenId Type="UINT" Persistent="true" />
        <physique Type="STATUS" Persistent="true" />
        <face Type="STATUS" Persistent="true" />
        <hair Type="STATUS" Persistent="true" />
        <body Type="STATUS" Persistent="true" />
        <customFacialInfo Type="FACIAL_INFO" Persistent="true" />
        <customHairInfo Type="HAIR_INFO" Persistent="true" />
        <customSkinInfo Type="SKIN_INFO" Persistent="true" />
        <appearCustomInfo Type="APPEARANCE_CUSTOM_INFO" Persistent="true" />
        <currentAppearanceId Type="APPEAR_PARTS_ID" Persistent="true" />
        <combatEffectiveness Type="UINT" Persistent="true" />
        <curFatness Type="number" Persistent="true" />
        <curSkinBlackness Type="number" Persistent="true" />
        <currentFaceId Type="FACE_MAKEUP_PARTS_ID" Persistent="true" />
        <currentStylistData Type="APPEARANCE_STYLIST_INFO" Persistent="true" />
        <photo Type="string" Persistent="true" />
        <curHeadFrame Type="UINT" Persistent="true" /> -->
    </ROLE_MODEL>

    <ROLE_MODEL_MAP Type="dict" Key="ENTITY_ID" Value="ROLE_MODEL" />
    <ROLE_MODEL_LIST Type="list" Element="ROLE_MODEL" />

    <SYNC_RECENTLY_WHISPER_INFO Type="tuple">
        <relationInfo Type="SYNC_RELATION_INFO" />
        <time Type="LONGTIME" />
        <lastWhisper Type="CHAT_INFO" Default="nil"/> <!-- 最后一次私聊信息 -->
    </SYNC_RECENTLY_WHISPER_INFO>

    <SYNC_RECENTLY_NPC_WHISPER_INFO Type="tuple">
        <relationInfo Type="SYNC_NPC_RELATION_INFO" />
        <time Type="LONGTIME" />
    </SYNC_RECENTLY_NPC_WHISPER_INFO>

    <SYNC_RECENTLY_WHISPER_INFO_LIST Type="list" Element="SYNC_RECENTLY_WHISPER_INFO" />
    <SYNC_RECENTLY_NPC_WHISPER_INFO_LIST Type="list" Element="SYNC_RECENTLY_NPC_WHISPER_INFO" />

    <PLAYER_EXTRA_INFO Type="tuple">
        <id Type="ENTITY_ID" />                        <!-- id -->
        <!-- <guildName Type="string" />                 公会名
        <combatPetDetail Type="PET_MAP"/>           备战列表英灵数据
        <playerTitle Type="TITLE" />                称号
        <guildExerciseLv Type="UINT"/>              公会修炼等级
        <averExerciseValue Type="number"/>          个人修炼平均等级
        <treasureEffectiveness Type="UINT"/>        瑰宝值
        <realmId Type="REALM_ID" />                 -1:未突破 0:可以突破 >0: ���突���的最大境界值
        <maxConsolidateRealmId Type="REALM_ID" />   已巩固的最大境界
        <macroExp Type="EXPERIENCE" />              元神经验
        <macroLv Type="LEVEL" />                    元神等级
        <hierogramInfo Type="HIEROGRAM_BRIEF_LIST" /> 神格信息
        <settings Type="KEY_NUMBER" /> 玩家设置 -->
    </PLAYER_EXTRA_INFO>

    <FRIEND_RECOMMEND_OPERATION Type="tuple">
        <visitCircle Type="BOOL" />
        <addFriend Type="BOOL" />
        <nextPage Type="BOOL" />
    </FRIEND_RECOMMEND_OPERATION>
    <FRIEND_RECOMMEND_OPERATION_MAP Type="dict" Key="ENTITY_ID" Value="FRIEND_RECOMMEND_OPERATION" />

    <!-- 好友群组 start -->
    <FRIEND_CLUB_INFO Type="tuple">                <!-- 群组信息 -->
        <clubId Type="string" />                   <!-- 群组Id -->
        <owner Type="ENTITY_ID" />                 <!-- 群主 -->
        <name Type="string" />                     <!-- 群组名-->
        <notice Type="string" />                   <!-- 公告 -->
        <createTime Type="LONGTIME" />             <!-- 创建时间 -->
        <members Type="ENTITY_ID_LIST" />          <!-- 成员gbIdList -->
        <onlineCount Type="UINT" />                <!-- 在线人数 -->
        <type Type="UINT" />                       <!-- 群组类型 -->
    </FRIEND_CLUB_INFO>
    <FRIEND_CLUB_INFO_LIST Type="list" Element="FRIEND_CLUB_INFO" />

    <FRIEND_CLUB_SETTING_INFO Type="tuple">                       <!-- 群组个人设置信息 -->
        <isBan Type="BOOL"  Persistent="true"/>                   <!-- 是否屏蔽聊天-->
        <bulletScreen Type="BOOL"  Persistent="true"/>            <!-- 是否开启弹幕-->
        <recordUUID Type="UUID" Persistent="true"/>               <!-- 聊天记录的uuid 用于删除聊天记录的标记 -->
    </FRIEND_CLUB_SETTING_INFO>
    <FRIEND_CLUB_SETTING_INFO_MAP Type="dict" Key="string" Value="FRIEND_CLUB_SETTING_INFO" /> <!-- key为clubId-->

    <FRIEND_CLUB_EDIT_FIELDS Type="tuple">                   <!-- 修改群组的字段 -->
        <name Type="string" Default="nil" />   <!-- 群组名-->
        <notice Type="string" Default="nil" /> <!-- 公告 -->
    </FRIEND_CLUB_EDIT_FIELDS>

    <FRIEND_CLUB_MEMBER_INFO Type="tuple">
        <rolename Type="string" Default="nil" />
    </FRIEND_CLUB_MEMBER_INFO>
    <FRIEND_CLUB_MEMBER_MAP Type="dict" Key="string" Value="FRIEND_CLUB_MEMBER_INFO"/>
    <!-- 好友群组 end -->

    <GUILD_ACTIVITIES Type="dict" Key="UINT" Value="UINT" />

    <GUILD_ARCHIVE Type="tuple">
        <count Type="UINT" Persistent="true" /> <!-- 活动次数 -->
        <difficulty Type="UINT" Persistent="true" /> <!-- 通关难度 -->
    </GUILD_ARCHIVE>
    <GUILD_ARCHIVES Type="dict" Key="UINT" Value="GUILD_ARCHIVE"/>

    <TIME_AND_COUNT_INFO Type="tuple">
        <time Type="LONGTIME" Default="0" Persistent="true"/>
        <count Type="UINT" Default="0" Persistent="true"/>
    </TIME_AND_COUNT_INFO>
    <ACTIVITY_TYPE_TO_TIME_COUNT_INFO Type="dict" Key="UINT" Value="TIME_AND_COUNT_INFO" />

    <GOODS_AWARD Type="dict" Key="UINT" Value="UINT" /> <!-- INV_BOUND_TYPE : number -->

    <GOODS_AWARD_MAP Type="dict" Key="UINT" Value="GOODS_AWARD" /> <!-- itemId : GOODS_AWARD -->

    <GOODS Type="tuple">
        <id Type="UINT" Persistent="true" />       <!-- 商会任务配置表ID -->
        <status Type="UINT" Persistent="true" />   <!-- 状态 -->
        <award Type="GOODS_AWARD_MAP" Persistent="true" /> <!-- 奖励 -->
    </GOODS>

    <GOODS_MAP Type="dict" Key="UINT" Value="GOODS" />

    <GOODS_TIME_MAP Type="dict" Key="UINT" Value="UINT" />

    <GOODS_HELPER Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME" />
    </GOODS_HELPER>

    <GOODS_HELPER_MAP Type="dict" Key="UINT" Value="GOODS_HELPER" />

    <WARE_HOUSE Type="tuple">
        <unlockInfo Type="UINT_MAP" Persistent="true" /> <!-- 参考分页解锁信息 const.WARE_HOUSE_UNLOCK_TYPE -->
        <invInfo Type="INV_INFO" Persistent="true" /> <!-- 仓库分页存储 -->
    </WARE_HOUSE>

    <GUILD_SINGLE_DANCE_MEMBER Type="tuple">
        <distance Type="int" />
    </GUILD_SINGLE_DANCE_MEMBER>

    <GUILD_SINGLE_DANCE_MEMBER_DICT Type="dict" Key="string" Value="GUILD_SINGLE_DANCE_MEMBER" />

    <GUILD_DANCE_INFO Type="tuple">
        <teamId Type="string" Default="" Flags="OWN_CLIENT" />
        <partnerId Type="string" Default="" Flags="OWN_CLIENT" />
        <ownScoreDict Type="DictIntInt" Default="{}" Flags="OWN_CLIENT" />
        <partnerScoreDict Type="DictIntInt" Default="{}" Flags="OWN_CLIENT" />
        <ownComboCount Type="int" Default="0" Flags="OWN_CLIENT" />
        <partnerComboCount Type="int" Default="0" Flags="OWN_CLIENT" />
        <!-- 被助力次数 -->
        <poweredCount Type="int" Default="0" Flags="OWN_CLIENT" /> 
        <!-- 助力次数 -->
        <powerCount Type="int" Default="0" Flags="OWN_CLIENT" />
        <!-- 给队友佩戴胸花时加(组队后的双人动作) -->
        <bonus Type="BOOL" Default="false" Flags="OWN_CLIENT" />
        <!-- 是否主动邀请 -->
        <bInvite Type="BOOL" Default="false" Flags="OWN_CLIENT" />
        <!-- 撒花抽奖的标识 @todo 未何不存盘, 这岂不是可以刷奖励? -->
        <bLottery Type="BOOL" Default="false" />
        <!-- 单人独舞时是不是第一个跳 -->
        <bFirstDance Type="BOOL" Default="false" Flags="OWN_CLIENT" />
    </GUILD_DANCE_INFO>

    <GUILD_DANCE_MEMBER Type="tuple">
        <avatarId Type="string" Flags="ALL_CLIENTS" />
        <name Type="string" Flags="ALL_CLIENTS" />
        <profession Type="int" Flags="ALL_CLIENTS" />
        <level Type="int" Flags="ALL_CLIENTS" />
        <fashionValue Type="int" Flags="ALL_CLIENTS" />
        <sex Type="int" Flags="ALL_CLIENTS" />
        <!--是否邀请中的状态，发出邀请或收到邀请都会处于邀请中的状态-->
        <inviting Type="BOOL" Flags="ALL_CLIENTS" />
        <teamId Type="string" Default="" Flags="ALL_CLIENTS" />
    </GUILD_DANCE_MEMBER>

    <GUILD_DANCE_MEMBERS Type="dict" Key="ENTITY_ID" Value="GUILD_DANCE_MEMBER" />

    <GUILD_DANCE_TEAM Type="tuple">
        <teamId Type="string" Default="" Flags="ALL_CLIENTS"/>
        <members Type="list" Element="string" Flags="ALL_CLIENTS"/>
        <score Type="int" Default="0" Flags="ALL_CLIENTS"/>
    </GUILD_DANCE_TEAM>

    <GUILD_DANCE_TEAMS Type="dict" Key="string" Value="GUILD_DANCE_TEAM" />

    <GUILD_DANCE_TEAM_LIST Type="list" Element="GUILD_DANCE_TEAM" />

    <GUILD_DANCE_MEMBER_SCORE_DICT Type="dict" Key="string" Value="DictIntInt" />

    <GUILD_MEMBER Type="tuple">   <!--客户端公会成员数据结构-->
        <id Type="ENTITY_ID" Persistent="true" />
        <school Type="SCHOOL" />
        <rolename Type="ROLENAME" />
        <lv Type="LEVEL" />
        <roles Type="DictIntInt" />       <!--职位-->
        <groupID Type="int" />            <!--玩家所在的组，即第几组的组员或组长-->
        <curContribution Type="UINT" />   <!--当前贡献-->
        <weekContribution Type="UINT" />  <!--周贡献-->
        <contribution Type="UINT" />      <!--历史贡献-->
        <offlineTime Type="LONGTIME" />   <!--离线时间(0表示在线)-->
        <enterTime Type="LONGTIME" />     <!--入会时间-->
        <worldId Type="SPACE_ID" />       <!--场景ID-->
        <power Type="UINT" />
        <lastWeekWage Type="UINT" Persistent="true" />      <!--可领工资-->
        <wageFlag Type="BOOL" Persistent="true" />
        <dayMailNum Type="UINT" Persistent="true" />
        <teamLeaderName Type="string" />
        <groupLeaderName Type="string" />
    </GUILD_MEMBER>

    <GUILD_RECOMMEND_DETAIL_INFO Type="tuple">
        <id Type="string"/>
        <lv Type="int"/>
        <offlineTime Type="int"/>
        <rolename Type="string"/>
        <school Type="int"/>
        <worldId Type="int"/>
        <ZhanLi Type="int"/>
        <guildId Type="string"/>
    </GUILD_RECOMMEND_DETAIL_INFO>

    <GUILD_RECOMMEND_DETAIL_INFOS Type="list" Element="GUILD_RECOMMEND_DETAIL_INFO"/>

    <GUILD_RECOMMEND_INFO Type="tuple">
        <time Type="int" Persistent="true" />
        <point Type="int" Persistent="true" />
    </GUILD_RECOMMEND_INFO>

    <GUILD_RECOMMEND_INFOS Type="dict" Key="string" Value="GUILD_RECOMMEND_INFO" Persistent="true" />

    <LockedMoney Type="dict" Key="UINT" Value="UINT" Flags="OWN_CLIENT" Persistent="true" />

    <MONEY_WEEK_LIMIT_INFO Type="tuple">
        <weekPossessed Type="UINT" Persistent="true" Flags="OWN_CLIENT" />
        <prevAccuLimit Type="UINT" Persistent="true" Flags="OWN_CLIENT" />		<!-- 过往累积的上限值（当周的不计入） -->
    </MONEY_WEEK_LIMIT_INFO>

    <MONEY_WEEK_LIMIT_INFO_DICT Type="dict" Key="UINT" Value="MONEY_WEEK_LIMIT_INFO" />

    <COMPLETE_GUILD_MEMBER ImplClass="CompleteGuildMember" Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <school Type="SCHOOL" Persistent="true" />
        <power Type="int" Persistent="true" />
        <rolename Type="ROLENAME" Persistent="true" />
        <lv Type="LEVEL" Persistent="true" />
        <roles Type="DictIntInt" Persistent="true" />       <!--职位类型对应的职位-->
        <groupID Type="int" Persistent="true" />            <!--所在的组编号-->
        <curContribution Type="UINT" Persistent="true" />   <!--当前贡献-->
        <weekContribution Type="UINT" Persistent="true" />  <!--周贡献-->
        <contribution Type="UINT" Persistent="true" />      <!--历史贡献-->
        <offlineTime Type="LONGTIME" Persistent="true" />   <!--离线时间(0表示在线)-->
        <enterTime Type="LONGTIME" Persistent="true" />     <!--入会时间-->
        <worldId Type="WORLD_ID" Persistent="true" />       <!--场景ID-->
        <lastWeekWage Type="UINT" Persistent="true" />      <!--可领工资-->
        <curWeekWage Type="UINT" Persistent="true" />       <!--工资-->
        <mailbox Type="mailbox" />
        <isInTeam Type="BOOL" />
        <wageFlag Type="BOOL" Persistent="true" />
        <dayOnlineTime Type="UINT" Persistent="true" />     <!--日在线总时长-->
        <!--<activityCountInfo Type="ACTIVITY_TYPE_TO_TIME_COUNT_INFO" Persistent="true"/>    不同时间周期的活动计次 -->
        <!--<activitiesCount Type="UINT_MAP" Persistent="true" />  参与活动次数-->
        <dayGuildChatNum Type="UINT" Persistent="true" /> <!-- 每天公会聊天次数 -->
        <weekGuildChatNum Type="UINT" Persistent="true" /> <!-- 每周公会聊天次数 -->
        <chatTitle Type="GUILD_CHAT_TITLE" Persistent="true" />
        <dayMailNum Type="UINT" Persistent="true" />
        <dayFirstLoginTime Type="UINT" Persistent="true" />
        <lastEnterGuildLeague Type="UINT" Persistent="true" />
        <teamLeaderName Type="string" />
        <groupLeaderName Type="string" />
        <portrait Type="int" Persistent="true"/>	    <!-- 玩家头像 -->
		<portraitFrame Type="int" Persistent="true"/>	<!-- 玩家头像框 -->
    </COMPLETE_GUILD_MEMBER>

    <GUILD_MEMBERS Type="list" Element="GUILD_MEMBER" />

    <COMPLETE_GUILD_MEMBERS Type="list" Element="COMPLETE_GUILD_MEMBER" />

    <GUILD_TARGET_ACTIVITY_INFO Type="tuple">
        <opened Type="BOOL" Persistent="true" /> <!-- 活动是否开启 -->
        <contribution Type="UINT" Persistent="true" Default="0" /> <!-- 活动开启期间累积贡献 -->
    </GUILD_TARGET_ACTIVITY_INFO>

    <GUILD_PRESTIGE_HISTORY Type="tuple">
        <activityId Type="UINT" Persistent="true" />
        <prestige Type="UINT" Persistent="true" />
        <time Type="UINT" Persistent="true" />
    </GUILD_PRESTIGE_HISTORY>
    <GUILD_PRESTIGE_HISTORY_INFO Type="list" Element="GUILD_PRESTIGE_HISTORY" />

    <GUILD_RANK_ACTIVITY_INFO Type="tuple">
        <opened Type="BOOL" Persistent="true" /> <!-- 活动是否开启 -->
        <prestige Type="UINT" Persistent="true" Default="0" /> <!-- 活动开启期间累积声望 -->
        <inRank Type="BOOL" Persistent="true" /> <!-- 当前是否参与排行 -->
        <rankLevel Type="UINT" Persistent="true" /> <!-- 排行等级 -->
        <rankPercent Type="UINT" Persistent="true" /> <!-- 排行百分比 -->
    </GUILD_RANK_ACTIVITY_INFO>

    <GUILD_RANK_ACTIVITY_SHOW_INFO Type="tuple">
        <id Type="GUILD_ID" Persistent="true" />
        <name Type="string" Persistent="true" />
        <leaderName Type="ROLENAME" Persistent="true" />
    </GUILD_RANK_ACTIVITY_SHOW_INFO>
    <GUILD_RANK_ACTIVITY_SHOW_INFO_LIST Type="list" Element="GUILD_RANK_ACTIVITY_SHOW_INFO" />

    <GUILD_TIME_LIVE_TYPE Type="tuple">
        <type Type="int" Persistent="true" />
        <num Type="int" Persistent="true" />
    </GUILD_TIME_LIVE_TYPE>

    <GUILD_PROFESSION_INFO Type="tuple">
        <id Type="int" Persistent="true" />
        <num Type="int" Persistent="true" />
    </GUILD_PROFESSION_INFO>

    <GUILD_LIST_CLIENT_SYNC_INFO Type="tuple">
        <id Type="GUILD_ID" />   <!--唯一ID-->
        <shortId Type="UINT" />
        <guildType Type="UINT" />
        <name Type="string"/>
        <lv Type="LEVEL" />
        <memberNum Type="UINT" />
        <apprenticeNum Type="UINT" />
        <maxMemberNum Type="UINT" />
        <totalMaxMemberNum Type="UINT" />
        <badgeFrameId Type="UINT" />
        <badgeIndex Type="UINT" />
        <posterResID Type="string" Default="" Persistent="true"/>       <!-- 宣传图的资源ID, 必定是审核通过的-->
        <newPosterResID Type="string" Default="" Persistent="true"/>    <!-- 最新的宣传图的资源ID, 要么是审核中，要么是审核失败，不可能是审核通过-->
        <posterStatus Type="int" Default="0" Persistent="true"/>        <!-- 最新宣传图的审核状态，要么是审核中，要么是审核失败，不可能是审核通过-->
    </GUILD_LIST_CLIENT_SYNC_INFO>
    <GUILD_LIST_CLIENT_SYNC_INFOS Type="list" Element="GUILD_LIST_CLIENT_SYNC_INFO" />


    <PRE_GUILD_LIST_CLIENT_SYNC_INFO Type="tuple">
        <id Type="GUILD_ID" />   <!--唯一ID-->
        <shortId Type="UINT" />
        <guildType Type="UINT" />
        <name Type="string"/>
        <lv Type="LEVEL" />
        <memberNum Type="UINT" />
        <createType Type="UINT" />
        <createTime Type="LONGTIME" />
        <badgeFrameId Type="UINT" />
        <badgeIndex Type="UINT" />
    </PRE_GUILD_LIST_CLIENT_SYNC_INFO>
    <PRE_GUILD_LIST_CLIENT_SYNC_INFOS Type="list" Element="PRE_GUILD_LIST_CLIENT_SYNC_INFO" />

    <GUILD_SIMPLE_INFO ImplClass="GuildSimpleInfo" Type="tuple">
        <id Type="GUILD_ID" />   <!--唯一ID-->
        <shortId Type="UINT" />    <!--前端短编号-->
        <badgeIndex Type="UINT" />
        <guildType Type="UINT" />
        <guildBabyName Type="string" />
        <name Type="string"/>
        <lv Type="LEVEL" />
        <memberNum Type="UINT" />
        <apprenticeNum Type="UINT" />
        <leaderName Type="ROLENAME" />
        <leaderId Type="ENTITY_ID" />
        <createTime Type="LONGTIME" />
        <liveValue Type="UINT" />
        <isAutoAgree Type="BOOL" />
        <maxMemberNum Type="UINT" />
        <totalMaxMemberNum Type="UINT" />
        <timeLiveInfo Type="GUILD_TIME_LIVE_TYPE" />
        <professionInfo Type="GUILD_PROFESSION_INFO" />
        <badgeFrameId Type="UINT" /> <!--徽章框ID-->
        <createType Type="UINT" />  <!-- 创建类型(const.GUILD_CREATE_TYPE)-->
        <powerSum Type="UINT" /> <!-- 公会总评 -->
        <posterResID Type="string" Default="" Persistent="true"/>       <!-- 宣传图的资源ID, 必定是审核通过的-->
        <newPosterResID Type="string" Default="" Persistent="true"/>    <!-- 最新的宣传图的资源ID, 要么是审核中，要么是审核失败，不可能是审核通过-->
        <posterStatus Type="int" Default="0" Persistent="true"/>        <!-- 最新宣传图的审核状态，要么是审核中，要么是审核失败，不可能是审核通过-->
    </GUILD_SIMPLE_INFO>

    <GUILD_SIMPLE_INFOS Type="list" Element="GUILD_SIMPLE_INFO" />

    <GUILD_BUILDING Type="tuple">
        <lv Type="LEVEL" Persistent="true" />
        <srcName Type="string" Persistent="true" />
        <costFunds Type="int" Persistent="true" />
        <endTime Type="LONGTIME" Persistent="true" />
    </GUILD_BUILDING>

    <GUILD_BUILDINGS Type="dict" Key="UINT" Value="GUILD_BUILDING" />

    <CREATE_GUILD_INFO Type="tuple">
        <id Type="GUILD_ID" />
        <shortId Type="int" />
        <lv Type="int" Default="1"/>
        <name Type="string"/>
        <createTime Type="int" />
        <members Type="GUILD_MEMBERS" />
        <declaration Type="string" />
        <createFlag Type="bool" />
        <createType Type="int" />
        <guildType Type="int" />
        <badgeIndex Type="int" />
        <LogicServerID Type="int" />
        <badgeFrameId Type="int" />
    </CREATE_GUILD_INFO>

    <PRE_GUILD_INFO Type="tuple">
        <id Type="GUILD_ID" />   <!--唯一ID-->
        <shortId Type="UINT" />    <!--前端短编号-->
        <name Type="string"/>
        <memberNum Type="UINT" />
        <leaderId Type="ENTITY_ID" />       <!--创建者GbId-->
        <leaderName Type="ROLENAME" />   <!--创建者名字-->
        <outDateTime Type="LONGTIME" />  <!--过期时间戳-->
        <declaration Type="string" />    <!--公会宣言-->
        <members Type="GUILD_MEMBERS" />
        <guildType Type="int" />
        <createType Type="UINT" />  <!-- 创建类型(const.GUILD_CREATE_TYPE)-->
    </PRE_GUILD_INFO>

    <GUILD_PRINCIPAL_INFO Type="tuple">
        <id Type="GUILD_ID" />   <!--唯一ID-->
        <shortId Type="UINT" />    <!--前端短编号-->
        <guildType Type="UINT" />
        <guildBabyName Type="string" />
        <badgeIndex Type="UINT" />
        <posterResID Type="string"/>        <!-- 宣传图的资源ID, 必定是审核通过的-->
        <newPosterResID Type="string"/>     <!-- 最新的宣传图的资源ID, 要么是审核中，要么是审核失败，不可能是审核通过-->
        <posterStatus Type="int"/>          <!-- 最新宣传图的审核状态，要么是审核中，要么是审核失败，不可能是审核通过-->
        <name Type="string"/>               <!--公会名-->
        <lv Type="LEVEL" />                 <!--等级-->
        <leaderId Type="ENTITY_ID" />       <!--会长gbId-->
        <leaderName Type="ROLENAME" />      <!--会长角色名-->
        <leaderLv Type="int" />             <!--会长等级-->
        <leaderSchool Type="int" />         <!--会长职业和性别-->
        <onlineMemberNum Type="UINT" />     <!--在线成员数-->
        <memberNum Type="UINT" />           <!--成员数-->
        <onlineApprenticeNum Type="UINT" /> <!--在线学徒数-->
        <apprenticeNum Type="UINT" />       <!--学徒数-->
        <power Type="UINT" />               <!--公会战力-->
        <funds Type="UINT" />               <!--公会资金-->
        <liveValue Type="UINT" />           <!--活跃值-->
        <declaration Type="string" />       <!--公会宣言-->
        <bonus Type="UINT" />               <!--分红-->
        <buildings Type="GUILD_BUILDINGS" /><!--建筑信息-->
        <member Type="GUILD_MEMBER" />      <!--玩家自身的成员信息-->
        <setBadgeFrameCount Type="UINT" />  <!--修改公会徽章框的次数-->
        <unlockBadgeFrames Type="DictIntBool"/>
        <autoFormatFlag Type="BOOL" />      <!-- 是否自动转正学徒 -->
        <groupNames Type="DictIntStr" />
        <portrait Type="int"/>	            <!-- 玩家头像 -->
		<portraitFrame Type="int"/>	        <!-- 玩家头像框 -->
    </GUILD_PRINCIPAL_INFO>

    <GUILD_CHANGE_INFO Type="tuple">
        <badgeIndex Type="int" /> <!-- 徽章第几个文字 -->
        <frameId Type="int" /> <!-- 徽章样式Id -->
        <name Type="string" />  <!-- 公会名字 -->
        <type Type="int" />  <!-- 公会类型 -->
        <declaration Type="string" /> <!-- 公会宣言 -->
    </GUILD_CHANGE_INFO>

    <GUILD_MANAGER Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <rolename Type="ROLENAME"/>
        <roles Type="DictIntInt" />
        <school Type="SCHOOL" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
        <lv Type="LEVEL" />
        <isOnline Type="BOOL" />
        <portrait Type="int"/>	    <!-- 玩家头像 -->
		<portraitFrame Type="int"/>	<!-- 玩家头像框 -->
    </GUILD_MANAGER>

    <GUILD_MANAGERS Type="list" Element="GUILD_MANAGER" />

    <GUILD_APPLY Type="tuple">
        <id Type="ENTITY_ID" Persistent="true" />
        <school Type="SCHOOL" Persistent="true" />
        <rolename Type="ROLENAME" Persistent="true" />
        <lv Type="LEVEL" Persistent="true" />
        <power Type="UINT" Persistent="true" />
        <applyTime Type="LONGTIME" Persistent="true" />
        <reason Type="string" Persistent="true" />
        <joinReason Type="UINT" Persistent="true"/>
    </GUILD_APPLY>

    <GUILD_APPLYS Type="list" Element="GUILD_APPLY" />

    <CUSTOM_RIGHTS Type="dict" Key="UINT" Value="KEY_BOOL" />

    <GUILD_APPLY_MAP Type="dict" Key="GUILD_ID" Value="LONGTIME" />

    <STORE_CONTRIBUTION ImplClass="StoreContribution" Type="tuple">
        <weekContribution Type="UINT" Persistent="true" />  <!--周贡献-->
        <contribution Type="UINT" Persistent="true" />      <!--历史贡献-->
        <overTime Type="LONGTIME" Persistent="true" />
    </STORE_CONTRIBUTION>

    <STORE_CONTRIBUTION_MAP Type="dict" Key="ENTITY_ID" Value="STORE_CONTRIBUTION" />

    <GUILD_TASK_DATA ImplClass="GuildTaskData" Type="tuple">
        <conditions Type="dict" Key="UINT" Value="UINT" Persistent="true" />
        <modifyTime Type="UINT" Persistent="true" />
        <endTime Type="UINT" Persistent="true" />
    </GUILD_TASK_DATA>

    <GUILD_TASK_INFO ImplClass="GuildTaskInfo" Type="tuple">
        <refreshCount Type="UINT" Persistent="true" />
        <finishedConditions Type="ARRAY_UINT" Persistent="true" />
        <redPacketState Type="UINT" Persistent="true" />
        <redPacketCount Type="UINT" Persistent="true" />
        <taskDatas Type="dict" Key="OBJECT_ID" Value="GUILD_TASK_DATA" Persistent="true" />
    </GUILD_TASK_INFO>

    <GUILD_BABY_TASK_INFO Type="dict" Key="ENTITY_ID" Value="GUILD_TASK_INFO" />

    <GUILD_BABY_ROLE_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <school Type="UINT" />
        <curHeadFrame Type="UINT" />
        <physique Type="STATUS" />
        <isOnline Type="BOOL" />
        <redPacketState Type="UINT" />
        <isStick Type="BOOL" />
        <photo Type="string" />
        <taskInfo Type="GUILD_TASK_INFO" />
    </GUILD_BABY_ROLE_INFO>
    <GUILD_BABY_ROLE_INFO_LIST Type="list" Element="GUILD_BABY_ROLE_INFO" />

    <GUILD_ROBBER_NUM_INFO Type="list" Element="UINT" /> <!-- {npcId, num} -->
    <GUILD_ROBBER_NUM_INFO_LIST Type="list" Element="GUILD_ROBBER_NUM_INFO" />

    <GUILD_BID_TIMESTAMP Type="dict" Key="UINT" Value="UINT" />

    <GUILD_EVENT Type="tuple">
        <noticeId Type="UINT" Persistent="true"/>
        <args Type="string" Persistent="true"/>
        <time Type="LONGTIME" Persistent="true"/>
    </GUILD_EVENT>

    <GUILD_EVENTS Type="list" Element="GUILD_EVENT" Persistent="true"/>

    <GUILD_SUMMARY Type="tuple">
        <id Type="UINT"/>
        <type Type="UINT"/>
        <time Type="LONGTIME"/>
        <args Type="table"/>
    </GUILD_SUMMARY>
    <GUILD_SUMMARY_LIST Type="list" Element="GUILD_SUMMARY" />

    <GUILD_STATION_PRESIDENT_STATUE Type="tuple">
        <bodyId Type="UINT" Persistent="true" /> <!-- 胖瘦参数 -->
        <actionId Type="UINT" Persistent="true" /> <!-- 动作 -->
        <actionFrame Type="UINT" Persistent="true" /> <!-- 动作帧数 -->
    </GUILD_STATION_PRESIDENT_STATUE>

    <PLAYER_GUILD_BASE Type="tuple">
        <id Type="string" />
        <shortId Type="UINT"/>
        <name Type="string" />
        <lv Type="int" />
        <presidentName Type="string" />
        <badgeFrameId Type="int" />
        <status Type="int" />
        <roles Type="DictIntInt" />
        <groupID Type="int" />
        <guildBabyName Type="string" />
        <enterTime Type="LONGTIME" />
        <lastWeekWage Type="UINT" />
        <wageFlag Type="BOOL" />
        <dayMailNum Type="UINT" />
        <guildShopLv Type="UINT" />
        <badgeIndex Type="UINT" />
    </PLAYER_GUILD_BASE>

    <PLAYER_GUILD ImplClass="PlayerGuild" Type="tuple">
        <status Type="UINT" Persistent="true" />         <!--公会状态-->
        <roles Type="DictIntInt" Persistent="true" />
        <groupID Type="int" Persistent="true" />
        <guildBabyName Type="string" />    <!--职位名-->
        <signature Type="string" Persistent="true" />   <!--个性签名 富文本字符串-->
        <lastSignInTime Type="UINT" Persistent="true" />   <!--上次签到时间-->
        <enterTime Type="LONGTIME" />
        <isVoiceSignature Type="BOOL" Persistent="true" /> <!-- 公会签名是否是语音 -->
        <lastWeekWage Type="UINT" Persistent="true" />     <!--可领工资-->
        <wageFlag Type="BOOL" Persistent="true" />
        <dayMailNum Type="UINT" Persistent="true" />
        <guildShopLv Type="UINT" Persistent="true" />
    </PLAYER_GUILD>

    <GUILD_AUDIO_LISTENER Type="tuple">
        <id Type="ENTITY_ID" />
        <rolename Type="ROLENAME"/>
        <school Type="SCHOOL" />
        <awakenId Type="UINT" />
        <physique Type="STATUS" />
        <lv Type="LEVEL" />
    </GUILD_AUDIO_LISTENER>

    <GUILD_AUDIO_LISTENER_LIST Type="list" Element="GUILD_AUDIO_LISTENER" />

    <GUILD_SIGNATURE_INFO Type="tuple">
        <signature Type="string" Persistent="true" />   <!--个性签名 富文本字符串-->
        <lastSignInTime Type="UINT" Persistent="true" />   <!--上次签到时间-->
        <isVoiceSignature Type="BOOL" Persistent="true" /> <!-- 公会签名是否是语音 -->
    </GUILD_SIGNATURE_INFO>

    <VOYAGE_TASK_DETIAL Type="tuple">
        <status Type="UINT" Persistent="true" /> <!-- 任务状态, 见const.VOYAGE_TASK_STATUS -->
        <process Type="UINT" Persistent="true" /> <!-- 进度值 -->
        <medalNum Type="UINT" Persistent="true" /> <!-- 公会奖章数 -->
    </VOYAGE_TASK_DETIAL>
    <VOYAGE_TASK Type="dict" Key="UINT" Value="VOYAGE_TASK_DETIAL" /> <!-- 任务id => 任务详细信息 -->
    <VOYAGE_TASK_INFO Type="dict" Key="UINT" Value="VOYAGE_TASK" /> <!-- 任务组id => 任务信息 -->

    <VOYAGE_TASK_GROUP_ID_LIST Type="list" Element="UINT"/> <!-- 远航任务组信息 -->
    <VOYAGE_TOP_CONTRIBUTION_ROLE_BRIEF Type="tuple">
        <contribution Type="UINT" Persistent="true" /> <!-- 总贡献值 -->
        <id Type="ENTITY_ID" Persistent="true" /> <!-- 玩家gbId -->
        <time Type="LONGTIME" Persistent="true" /> <!-- 达成时间 -->
    </VOYAGE_TOP_CONTRIBUTION_ROLE_BRIEF>

    <GUILD_LEAGUE_STATUS_RECORD Type="list" Element="BOOL" /> <!-- 公会联赛 最近几场的胜负情况 -->
    <GUILD_LEAGUE_INFO  Type="tuple">
        <medalCount Type="UINT" Persistent="true" />
        <leaguePoint Type="number" Persistent="true" />
        <guildId Type="UINT" Persistent="true" />
        <guildName Type="string" Persistent="true" />
        <historyWinStatus Type="GUILD_LEAGUE_STATUS_RECORD" Persistent="true" />
        <guildCombat Type="number" Persistent="true" />
        <badgeFrameId Type="UINT" Persistent="true" />
        <rolename Type="string" Persistent="true" />
    </GUILD_LEAGUE_INFO>
    <GUILD_LEAGUE_INFO_LIST Type="list" Element="GUILD_LEAGUE_INFO" />

    <GUILD_RECOMMEND_INVITE_IDS ImplClass="GUILD_RECOMMEND_INVITE_IDS" Type="DictStrBool" Flags="OWN_CLIENT" Persistent="true"/>
    <GUILD_RESPONSE_INVITE_IDS ImplClass="GUILD_RESPONSE_INVITE_IDS" Type="DictStrBool" Flags="OWN_CLIENT" Persistent="true"/>

    <DUNGEON_FIRST_PASSAGE_STAGE_INFO Type="dict" Key="int" Value="bool" Persistent="true"/>
    <DUNGEON_FIRST_PASSAGE_STAGE_INFOS Type="dict" Key="int" Value="DUNGEON_FIRST_PASSAGE_STAGE_INFO" Persistent="true"/>

    <DUNGEON_FIRST_PASSAGE_RECORD Type="tuple">
        <time Type="UINT" Persistent="true" />
        <leaderID Type="string" Persistent="true" />
        <leaderName Type="string" Persistent="true" />
        <memberIDs Type="ListStr" Persistent="true" />
    </DUNGEON_FIRST_PASSAGE_RECORD>

    <DUNGEON_FIRST_PASSAGE_RECORDS Type="dict" Key="int" Value="DUNGEON_FIRST_PASSAGE_RECORD" Persistent="true"/>

    <GUILD_LEAGUE_OCCUPY_DETECT_PREPARE_INFO Type="tuple">
        <activatingIDs Type="ListStr" Flags="ALL_CLIENTS"/>
        <startTS Type="int" Flags="ALL_CLIENTS"/>
    </GUILD_LEAGUE_OCCUPY_DETECT_PREPARE_INFO>

    <GUILD_LEAGUE_LOCKING_TOWER_IDS Type="dict" Key="string" Value="string" />

    <SNAP_SHOT_INFO Type="tuple">
        <snapType Type="UINT" />
        <itemInfo Type="INV_SLOT_VAL" />
    </SNAP_SHOT_INFO>
    <SNAP_SHOT_INFO_MAP Type="dict" Key="UUID" Value="SNAP_SHOT_INFO" />

    <POSITION_ARRAY Type="list" Element="float" />

    <COMMAND_SYSTEM_GROUP_BRIEF Type="tuple">   <!-- 指挥系统团队组简要信息 -->
        <groupID Type="GROUP_ID" />   <!-- 团队ID -->
        <leaderName Type="string" />    <!-- 团长名字 -->
        <count Type="UINT" />           <!-- 团队人数 -->
    </COMMAND_SYSTEM_GROUP_BRIEF>

    <COMMAND_SYSTEM_GROUP_LIST Type="list" Element="COMMAND_SYSTEM_GROUP_BRIEF" />

    <GUILD_COMMAND_SYSTEM_INFO Type="tuple">        <!-- 公会指挥系统信息 -->
        <groupList Type="COMMAND_SYSTEM_GROUP_LIST" />  <!-- 团队列表 -->
        <playerNum Type="UINT" />   <!-- 总玩家数量 -->
    </GUILD_COMMAND_SYSTEM_INFO>

    <COMMAND_SYSTEM_MARK_INFO Type="tuple">        <!-- 公会指挥系统的标记 -->
        <index Type="int" Default="nil" Flags="ALL_CLIENTS" />            <!-- 标记索引 -->
        <groupID Type="GROUP_ID" Default="nil" Flags="ALL_CLIENTS" />   <!-- 团队ID(为空代表所有团队) -->
        <position Type="POSITION_ARRAY" Default="nil" Flags="ALL_CLIENTS" />     <!-- 标记位置 -->
        <customText Type="string" Default="nil" Flags="ALL_CLIENTS" />    <!-- 自定义文本 -->
    </COMMAND_SYSTEM_MARK_INFO>

    <COMMAND_SYSTEM_MARK_INFO_MAP Type="dict" Key="int" Value="COMMAND_SYSTEM_MARK_INFO" />  <!-- 公会指挥系统团队标记信息(key是markIndex) -->

    <COMMAND_SYSTEM_GROUP_MARK_INFO Type="dict" Key="GROUP_ID" Value="COMMAND_SYSTEM_MARK_INFO_MAP" />  <!-- 公会指挥系统标记信息信息(key是groupID, 0代表所有团队) -->

    <COMMAND_SYSTEM_GUILDS_MARK_INFO Type="dict" Key="ENTITY_ID" Value="COMMAND_SYSTEM_GROUP_MARK_INFO" />  <!-- 指挥系统所有公会标记信息 -->

    <COMMAND_SYSTEM_CUSTOM_TEXT_MAP Type="dict" Key="ENTITY_ID" Value="DictIntStr" />  <!-- 指挥系统自定义文本信息{guildID = {[markIndex] = text}} -->


    <!-- <TRIGGER_INFO_PARAMS Type="tuple">
        <TaskID Type="UINT" Persistent="true"/>
        <TargetIndex Type="UINT" Persistent="true"/>
        <InitCount Type="UINT" Persistent="true"/>
        <StartAt Type="UINT" Persistent="true"/>
        <Time Type="UINT" Persistent="true"/>
        <Count Type="UINT" Persistent="true"/>
        <SpawnerID Type="ARRAY_STRING" Persistent="true"/>
        <Percent Type="UINT" Persistent="true"/>
        <IDs Type="ARRAY_STRING" Persistent="true"/>
        <MapID Type="UINT" Persistent="true"/>
        <planeID Type="UINT" Persistent="true"/>
        <NPCID Type="UINT" Persistent="true"/>
        <TalkID Type="UINT" Persistent="true"/>
        <TalkNextID Type="UINT" Persistent="true"/>
        <TriggerID Type="string" Persistent="true"/>
    </TRIGGER_INFO_PARAMS> -->

    <TRIGGER_INFO Type="tuple">
        <id Type="string" Persistent="true"/>
        <target Type="UINT" Persistent="true"/>
        <current Type="UINT" Persistent="true"/>
        <taskID Type="int" Persistent="true" />
        <taskTargetType Type="int" Persistent="true" />
        <taskTargetIndex Type="int" Persistent="true" />
        <randomGroupId Type="int" Persistent="true" />
        <targetId Type="int" Persistent="true" />
    </TRIGGER_INFO>
    <TRIGGER_MAP_BY_EVENT Type="dict" Key="string" Value="TRIGGER_INFO" />

    <QUEST_TRIGGER_INFO Type="tuple">
        <CurrentFinished Type="UINT" Persistent="true"/>
        <QuestTargetType Type="int" Persistent="true" /> <!-- 标记类型，目标/失败条件-->
        <RandomGroupId Type="int" Persistent="true" /> <!-- 公会的暂时迁移过来-->
    </QUEST_TRIGGER_INFO>

    <QUEST_TRIGGER_INFO_TARGET_INDEX_MAP Type="dict" Key="int" Value="QUEST_TRIGGER_INFO" />

    <QUEST_TRIGGER_INFO_MAP Type="dict" Key="int" Value="QUEST_TRIGGER_INFO_TARGET_INDEX_MAP" />

    <!-- 后续挪到各自模块 DataTypes Start -->
    <DELAY_SYS_ACTION_QUEUE Type="dict" Key="int" Value="VList" />

    <!-- 局内状态 start -->
    <AVATAR_HEAD_ALL_CLIENTS Type="tuple">
        <ID Type="string" Default="" Flags="ALL_CLIENTS" />
        <IconID Type="int" Default="0" Flags="ALL_CLIENTS" />
        <ProfessionID Type="int" Default="0" Flags="ALL_CLIENTS" />
        <Level Type="int" Default="0" Flags="ALL_CLIENTS" />
        <Name Type="string" Default="" Flags="ALL_CLIENTS" />
        <Sex Type="int" Default="0" Flags="ALL_CLIENTS" />
    </AVATAR_HEAD_ALL_CLIENTS>

    <TEAM_ARENA_MEMBER Type="tuple" Inherit="AVATAR_HEAD_ALL_CLIENTS">
        <IsLeader Type="bool" Flags="ALL_CLIENTS" />  <!-- 是否是���长 -->
        <RankID Type="int" Flags="ALL_CLIENTS" />  <!-- 段位 -->
        <ZhanLi Type="int" Flags="ALL_CLIENTS" />  <!-- 战力 -->
        <Status Type="int" Flags="ALL_CLIENTS" />  <!-- 状态 PVP_INNER_MEMBER_STATUS 中的宏-->
        <HeadIconState Type="int" Flags="ALL_CLIENTS" />  <!-- 头像状态，播报系统触发-->
        <Badge Type="int" Flags="ALL_CLIENTS" />  <!-- 徽章 玩家局内表现突出点 根据配置优先显示-->
        <BotID Type="int" Flags="ALL_CLIENTS" Default="nil" />  <!-- 机器人ID，-1表示不是机器人 -->
    </TEAM_ARENA_MEMBER>
    <TEAM_ARENA_MEMBERS Type="list" Element="TEAM_ARENA_MEMBER" />

    <TEAM_ARENA_CAMP_INFO Type="tuple"> <!-- 战斗队伍信息 -->
        <TeamID Type="TEAM_ID" Flags="ALL_CLIENTS" />  <!-- 队伍ID -->
        <Name Type="string" Flags="ALL_CLIENTS" Default=""/>  <!-- 队伍ID -->
        <CampID Type="int" Flags="ALL_CLIENTS" />  <!-- 阵营ID -->
        <Members Type="TEAM_ARENA_MEMBERS" Flags="ALL_CLIENTS" /> <!-- 队伍成员 -->
    </TEAM_ARENA_CAMP_INFO>
    <TEAM_ARENA_CAMP_INFO_DICT Type="dict" Key="TEAM_ID" Value="TEAM_ARENA_CAMP_INFO" /> <!-- 比赛双方信息 -->
    <TEAM_ID_BOOL_MAP Type="dict" Key="TEAM_ID" Value="BOOL" />
    <!-- 局内状态 end -->

    <BroadcastRecord Type="tuple">
        <count Type="int" />
        <cbName Type="string" />
    </BroadcastRecord>
    <BroadcastRecords Type="dict" Key="string" Value="BroadcastRecord" />

    <!-- 结算信息 start -->
    <BRIEF_COMBAT_STATS Type="tuple">
        <Damage Type="int" Default="0" /> <!-- 伤害 -->
        <Heal Type="int" Default="0" /> <!-- 治疗 -->
        <Bear Type="int" Default="0" /> <!-- 承伤 -->
        <ReviveNum Type="int" Default="0" /> <!-- 复活 -->
        <KillNum Type="int" Default="0" /> <!-- 击杀 -->
        <DieNum Type="int" Default="0" /> <!-- 死亡 -->
        <AssistNum Type="int" Default="0" /> <!-- 助攻 -->
        <Control Type="number" Default="0" /> <!-- 控制时长 -->
        <Score Type="int" Default="0" /> <!-- 分数 -->
    </BRIEF_COMBAT_STATS>

    <AVATAR_COMBAT_STATS Type="dict" Key="string" Value="BRIEF_COMBAT_STATS" />
    <!-- 结算信息 end -->

    <BATTLE_NOTICE_AVATAR_BRIEF Type="tuple" Inherit="AVATAR_HEAD">
        <CampID Type="int" Default="0" />
        <!-- 玩法自定义字段 start -->
        <IsWanted Type="bool" Default="nil" />
        <!-- 玩法自定义字段 end -->
    </BATTLE_NOTICE_AVATAR_BRIEF>
    <BATTLE_NOTICE_AVATAR_BRIEF_LIST Type="list" Element="BATTLE_NOTICE_AVATAR_BRIEF" />

    <!-- 后续挪到各自模块 DataTypes End -->
    <TEAM_ARENA_MEMBER_DETAIL_INFO Type="tuple">
        <sealedInfos Type="SEALED_INFOS"/>             <!-- 封印物信息 -->
        <skillDict Type="ActorSkillDict"/>  <!-- 技能id->slot -->
    </TEAM_ARENA_MEMBER_DETAIL_INFO>
    <TEAM_ARENA_MEMBER_DETAIL_INFO_DICT Type="dict" Key="ENTITY_ID" Value="TEAM_ARENA_MEMBER_DETAIL_INFO"/>

    <LoseControlFlag Type="ENTITY_ID_BOOL" /> <!-- 用于客户端监听设置回调 -->

    <ELIMINATION_BRACKET_INFO Type="tuple">
        <groupID Type="UINT" />
        <bracketID Type="UINT" />
        <isWinnerBracket Type="bool" />
        <defender Type="UUID" Default="" Persistent="true"/>
        <defenderName Type="string" Default="nil" Persistent="true"/>
        <challenger Type="UUID" Default="" Persistent="true" />
        <challengerName Type="string" Default="nil" Persistent="true"/>
    </ELIMINATION_BRACKET_INFO>

    <DOUBLE_ELIMINATION_BRACKET_INFO Type="tuple" Inherit="ELIMINATION_BRACKET_INFO">
        <isChallengerWin Type="bool" Default="nil" Persistent="true"/>
        <teamId2Point Type="DictIntInt" Default="nil" Persistent="true"/> <!--比分-->
    </DOUBLE_ELIMINATION_BRACKET_INFO>
    <BRACKET_ID_2_INFO Type="dict" Key="UINT" Value="DOUBLE_ELIMINATION_BRACKET_INFO" />
    <ROUND_BRACKET_RESULT Type="list" Element="BRACKET_ID_2_INFO" /> <!--winerBracket, loerBracket-->
    <ROUND_BRACKET_RESULT_DICT Type="dict" Key="UINT" Value="ROUND_BRACKET_RESULT" /> <!-- roundId -> {winerBracket, loerBracket}-->

    <ELIMINATION_BASE_INFO Type="tuple">
        <groupID Type="UINT" Persistent="true"/>
        <round Type="UINT" Default="0" Persistent="true"/>
        <winnerBracket Type="BRACKET_ID_2_INFO" Default="{}" Persistent="true"/>
        <nextWinnerBracket Type="BRACKET_ID_2_INFO" Default="{}" Persistent="true"/>
        <round2BracketResults Type="ROUND_BRACKET_RESULT_DICT" Default="{}" Persistent="true"/>
    </ELIMINATION_BASE_INFO>

    <DOUBLE_ELIMINATION_INFO Type="tuple" Inherit="ELIMINATION_BASE_INFO">
        <loserBracket Type="BRACKET_ID_2_INFO" Default="{}" Persistent="true"/>
        <nextLoserBracket Type="BRACKET_ID_2_INFO" Default="{}" Persistent="true"/>
    </DOUBLE_ELIMINATION_INFO>

    <MIX_DOUBLE_ELIMINATION_INFO Type="tuple" Inherit="DOUBLE_ELIMINATION_INFO" ImplClass="MixDoubleElimination" />
    <MIX_DOUBLE_ELIMINATION_DICT Type="dict" Key="UINT" Value="MIX_DOUBLE_ELIMINATION_INFO" />

    <SINGLE_ELIMINATION_INFO Type="tuple" Inherit="ELIMINATION_BASE_INFO" ImplClass="SingleElimination" />
    <SINGLE_ELIMINATION_DICT Type="dict" Key="UINT" Value="SINGLE_ELIMINATION_INFO" />

    <!-- 后续记录一些战绩等 -->
    <ELIMINATION_BATTLE_RESULT_EXTRA Type="tuple">
        <groupID Type="UINT" />
        <bracketID Type="UINT" />
        <isWinnerBracket Type="bool" />
        <teamID2Point Type="DictIntInt" Default="nil"/> <!-- 本场胜点 -->
    </ELIMINATION_BATTLE_RESULT_EXTRA>

    <!-- 组队pvp每局结算信息 start -->
    <PVP_ARENA_MEMBER_CALC Type="tuple" Inherit="AVATAR_HEAD">
        <IsLeader Type="bool" />  <!-- 是否是队长 -->
        <PreRankID Type="int" Default="1" />  <!-- 开局段位 -->
        <PreRankPoints Type="int" Default="0" /> <!-- 开局胜点数 -->
        <ZhanLi Type="int" />  <!-- 战力 -->
        <Damage Type="int" Default="0" /> <!-- 伤害 -->
        <Heal Type="int" Default="0" /> <!-- 治疗 -->
        <Bear Type="int" Default="0" /> <!-- 承伤 -->
        <KillNum Type="int" Default="0" /> <!-- 击杀 -->
        <DieNum Type="int" Default="0" /> <!-- 死亡 -->
        <PreProtected Type="int" Default="0" /> <!-- 开局保护胜点 -->
        <GainPoints Type="int" Default="0" /> <!-- 获取胜点数 -->
        <GainProtected Type="int" Default="0" /> <!-- 获取保护胜点 -->
        <AssistNum Type="int" Default="0" /> <!-- 助攻 -->
        <Control Type="number" Default="0" /> <!-- 控制时长 -->
        <Score Type="int" Default="0" /> <!-- 分数 -->
        <Hp Type="int" Default="0" /> <!-- 剩余血量 -->
    </PVP_ARENA_MEMBER_CALC>
    <PVP_ARENA_MEMBERS_CALC Type="list" Element="PVP_ARENA_MEMBER_CALC" />
    <PVP_ARENA_BATTLE_TEAM_RESULT Type="tuple"> <!-- 单局比赛结果 -->
        <Result Type="int" /> <!-- 本局比赛结果（胜利为1或者失败为0）目前没有平局, 预留 -->
        <Mvp Type="ENTITY_ID" /> <!-- Mvp玩家id -->
        <StartTime Type="int" /> <!-- 对局开始时间 秒级时间戳-->
        <EndTime Type="int" /> <!-- 对局结束时间 秒级时间戳 -->
        <Members Type="PVP_ARENA_MEMBERS_CALC" /> <!-- 队伍成员结算信息 -->
        <Extra Type="none" /> <!-- 额���信息, 根据pvp类型自行定义 -->
        <Title Type="int" Default="0" />    <!-- 对局称号 -->
    </PVP_ARENA_BATTLE_TEAM_RESULT>
    <PVP_ARENA_BATTLE_ROUND_RESULT Type="dict" Key="TEAM_ID" Value="PVP_ARENA_BATTLE_TEAM_RESULT"/>
    <PVP_GROUP_BATTLE_ROUND_RESULT Type="dict" Key="int" Value="PVP_ARENA_BATTLE_TEAM_RESULT"/>
    <!-- 组队pvp每局结算信息 end -->

    <!-- new mail -->
    <MAIL_EXTRA_CONDITION Type="tuple">
        <startTime Type="UINT" Persistent="true"/>
        <registerTime Type="UINT" Persistent="true"/>
    </MAIL_EXTRA_CONDITION>

    <!-- 获取单个邮件内容的RPC用,包含reward信息 -->
    <MAIL_SYNC_INFO Type="tuple">
        <id Type="string" />
        <templateId Type="UINT" />
        <sender Type="string" />
        <senderId Type="UINT" />
        <senderPhoto Type="string" />
        <title Type="string" />
        <createTime Type="UINT" />
        <category Type="UINT" />
        <destroyTime Type="UINT" />
        <type Type="UINT" />
        <content Type="string" />
        <isRead Type="BOOL" />
        <isExtracted Type="BOOL" />
        <sourceExtraArgs Type="string" />

        <reward Type="INV_SLOT_VAL_LIST"/>
    </MAIL_SYNC_INFO>

    <!-- 邮件在玩家属性里的持久化存储 -->
    <MAIL_INFO Type="tuple">
        <id Type="string" Persistent="true"/>
        <templateId Type="UINT" Persistent="true"/>
        <sender Type="string" Persistent="true"/>
        <senderId Type="UINT" Persistent="true"/>
        <senderPhoto Type="string" Persistent="true" />
        <title Type="string" Persistent="true"/>
        <createTime Type="UINT" Persistent="true"/>
        <category Type="UINT" Persistent="true"/>
        <destroyTime Type="UINT" Persistent="true"/>
        <type Type="UINT" Persistent="true"/>
        <content Type="string" Persistent="true"/>
        <isRead Type="BOOL" Persistent="true"/>
        <isExtracted Type="BOOL" Persistent="true"/>
        <sourceId Type="UINT" Persistent="true"/>
        <sourceExtraArgs Type="string" Persistent="true"/>
        <extractCondition Type="MAIL_EXTRA_CONDITION" Persistent="true"/>
        <opNUID Type="NUID" Persistent="true"/>

        <reward Type="INV_SLOT_VAL_LIST" Default="nil"/>
    </MAIL_INFO>
    <MAIL_INFOS Type="dict" Key="string" Value="MAIL_INFO" Persistent="true"/>

    <!-- 发给客户端的邮件列表, 只包含摘要信��� 仅RPC -->
    <MAIL_BRIEF Type="tuple">
        <templateId Type="UINT"/>
        <title Type="string"/>
        <createTime Type="UINT"/>
        <destroyTime Type="UINT"/>
        <type Type="UINT"/>
        <isRead Type="BOOL"/>
        <sourceExtraArgs Type="string"/>
        <isExtracted Type="BOOL"/>
        <senderId Type="UINT"/>
        <sender Type="string"/>
        <senderPhoto Type="string" />
        <category Type="UINT"/>
    </MAIL_BRIEF>
    <MAIL_BRIEF_LIST Type="dict" Key="string" Value="MAIL_BRIEF" />

    <!-- 邮件收藏夹 -->
    <MAIL_FAV_INFO Type="tuple">
        <id Type="string"/>
        <templateId Type="UINT"/>
        <sender Type="string"/>
        <senderId Type="UINT"/>
        <title Type="string"/>
        <createTime Type="LONGTIME"/>
        <category Type="UINT"/>
        <content Type="string"/>
        <favTime Type="LONGTIME"/>
        <sourceExtraArgs Type="string" />
    </MAIL_FAV_INFO>
    <MAIL_FAV_LIST Type="list" Element="MAIL_FAV_INFO" />

    <MAIL_ID Type="NUID" />
    <MAIL_ID_LIST Type="list" Element="MAIL_ID" />

    <GUILD_ROLE_SLOT_INFO Type="dict" Key="int" Value="string" Persistent="true"/>
    <GUILD_ROLE_SLOT_INFOS Type="dict" Key="int" Value="GUILD_ROLE_SLOT_INFO" Persistent="true"/>
    <CLIENT_SET_GUILD_ROLE_SLOT_INFOS Type="dict" Key="int" Value="GUILD_ROLE_SLOT_INFOS" Persistent="true"/>

    <!-- 刷新商店相关 -->
    <REFRESH_SHOP_CELL Type="tuple">    <!-- 刷新商店单个商品信息 -->
        <price Type="UINT" Persistent="true" />                                        <!-- 价格 -->
        <order Type="UINT" Persistent="true" />                                        <!-- 显示顺序 -->
        <isBought Type="BOOL" Persistent="true" />                                     <!-- 是否已买 -->
    </REFRESH_SHOP_CELL>

    <REFRESH_SHOP_DATA Type="tuple">    <!-- 刷新商店信息 -->
        <shopId Type="UINT" Persistent="true" />                                       <!-- 商店ID -->
        <refreshTime Type="LONGTIME" Persistent="true" />                              <!-- 逻辑刷新时间 -->
        <refreshInterval Type="LONGTIME" Persistent="true" />                          <!-- 距离下次刷新的时间 -->
        <version Type="UINT" Persistent="true" />                                      <!-- 商店版本号 -->
        <manualTimes Type="UINT" Persistent="true" />                                  <!-- 手动刷新次数 -->
        <cells Type="dict" Key="UINT" Value="REFRESH_SHOP_CELL" Persistent="true" />   <!-- 所有商品信息 -->
    </REFRESH_SHOP_DATA>

    <REFRESH_SHOP_DATA_MAP Type="dict" Key="UINT" Value="REFRESH_SHOP_DATA" Persistent="true" />

    <GuildActivityInfos Type="dict" Key="UINT" Value="ListInt" />

    <GuildAnswerActivity Type="tuple">
        <stage Type="int" />
        <questIndex Type="int" />
        <stageStartTime Type="int" />
        <questID Type="int" />
        <questOptions Type="string" />
        <questCount Type="int" />
        <answerIndex Type="int" />
        <totalCount Type="int" />
        <correctCount Type="int" />
    </GuildAnswerActivity>

    <GIFT_RECORD Type="tuple">
        <startRecordTime Type="LONGTIME" Persistent="true" />
        <lastRecordTime Type="LONGTIME" Persistent="true" />
        <senderGbId Type="ENTITY_ID" Persistent="true" />
        <senderRoleName Type="ROLENAME" Persistent="true" />
        <senderLv Type="LEVEL" Persistent="true" />
        <senderSchool Type="SCHOOL" Persistent="true" />
        <senderPhysique Type="STATUS" Persistent="true" />
        <recvGbId Type="ENTITY_ID" Persistent="true" />
        <recvRoleName Type="ROLENAME" Persistent="true" />
        <recvLv Type="LEVEL" Persistent="true" />
        <recvSchool Type="SCHOOL" Persistent="true" />
        <recvPhysique Type="STATUS" Persistent="true" />
        <giftId Type="UINT" Persistent="true" />
        <giftCnt Type="UINT" Persistent="true" />
        <needPublish Type="BOOL" Default="nil" /> <!-- 是否需要发送跨服喇叭 不需要存档 -->
        <opNUID Type="UUID" Default="nil" /> <!-- 最后一次连击的opNUID -->
    </GIFT_RECORD>

    <GIFT_RECORD_LIST Type="list" Element="GIFT_RECORD" />

     <GIFT_RANK_INFO Type="tuple">
        <score Type="UINT" Persistent="true" />
        <time Type="UINT" Persistent="true" />
    </GIFT_RANK_INFO>

    <GIFT_RANK_PLAYER_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <score Type="UINT" />
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <physique Type="STATUS" />
        <photo Type="string" />
        <curHeadFrame Type="UINT" />
        <time Type="LONGTIME" />
    </GIFT_RANK_PLAYER_INFO>
    <GIFT_RANK_PLAYER_INFO_LIST Type="list" Element="GIFT_RANK_PLAYER_INFO" />

    <!-- <AvatarBattleStatistics Type="tuple">
        <nickName Type="string" />
        <battlePoint Type="UINT" />
        <rank Type="UINT" />
    </AvatarBattleStatistics>

    <TeamBattleStatistics Type="tuple">
        <teamCaptainName Type="string" />
        <battlePoint Type="UINT" />
        <rank Type="UINT" />
    </TeamBattleStatistics>

    <GuildBattleStatistics Type="tuple">
        <guildName Type="string" />
        <battlePoint Type="UINT" />
        <rank Type="UINT" />
    </GuildBattleStatistics>

    <AvatarRankStatistics Type="dict" Key="UINT" Value="AvatarBattleStatistics" />
    <TeamRankStatistics Type="dict" Key="UINT" Value="TeamBattleStatistics" />
    <GuildRankStatistics Type="dict" Key="UINT" Value="GuildBattleStatistics" /> -->


    <RegionClimateInfo Type="tuple">
        <regionID Type="int" />
        <climateID Type="int" />
    </RegionClimateInfo>
    <RegionClimateInfoList Type="list" Element="RegionClimateInfo"/>

    <ClimateIDList Type="list" Element="int"/>
    <Region2ClimateIDs Type="dict" Key="int" Value="ClimateIDList"/>

    <RED_POINT_INFO Type="tuple">
        <!-- 红点的类型 例如 任务、新人手册、装备等 要定义枚举值 -->
        <RedPointTriggerType Type="int" Persistent="true" />
        <!-- 红点类型下的参数 例如 任务类型传任务ID ，物品传物品ID 方便找到对应的红点 -->
        <TypePatamID Type="int" Persistent="true" />
        <!-- 红点展示的类型 -->
        <PointType Type="int" Persistent="true" />
    </RED_POINT_INFO>

    <RED_POINTS_INFO  Type="list" Element="RED_POINT_INFO"  />

    <FaceDataInfo Type="dict" Key="UUID" Value="DictStrStr"/>
    <CustomRoleFaceDataInfo Type="dict" Key="UUID" Value="string"/>

    <ACTIVITY_STATUS_INFO Type="tuple">
        <status Type="int" />
        <statusEndTimeStamp Type="int" />
        <statusStartTimeStamp Type="int" />
    </ACTIVITY_STATUS_INFO>

    <ACTIVITY_STATUS_INFO_DICT Type="dict" Key="int" Value="ACTIVITY_STATUS_INFO" />

    <!-- team pvp rank -->
    <TEAM_PVP_SEASON_STATS Type="tuple">
        <gameTimes Type="int" Default="0" Persistent="true" />
        <winTimes Type="int" Default="0" Persistent="true" />
        <killNum Type="int" Default="0" Persistent="true" />
        <dieNum Type="int" Default="0" Persistent="true" />
        <kd Type="float" Default="0" Persistent="true" />
        <curWinStreak Type="int" Default="0" Persistent="true" />
        <maxWinStreak Type="int" Default="0" Persistent="true" />
        <maxDamage Type="int" Default="0" Persistent="true" />
        <maxHeal Type="int" Default="0" Persistent="true" />
        <maxBear Type="int" Default="0" Persistent="true" />
        <maxRankId Type="int" Default="0" Persistent="true" />
        <mvpCnt Type="int" Default="0" Persistent="true" />
        <avgKill Type="float" Default="0" />
        <!-- <likes Type="int" Default="0" Persistent="true" /> -->
    </TEAM_PVP_SEASON_STATS>

    <TEAM_PVP_SEASON_STATS_DICT Type="dict" Key="int" Value="TEAM_PVP_SEASON_STATS" />

    <TEAM_PVP_MATCH_INFO Type="tuple">
        <rankVersion Type="int" Default="0" />
        <seasonStatsDict Type="TEAM_PVP_SEASON_STATS_DICT" Persistent="true" />
        <rankId Type="int" Flags="OWN_CLIENT" Default="1" />
        <rankPoints Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <maxRankId Type="int" Flags="OWN_CLIENT" Default="1" Persistent="true" />
        <protectedPoints Type="int" Flags="OWN_CLIENT" Default="0" Persistent="true" />
        <seasonId Type="int" Flags="OWN_CLIENT" Default="1" Persistent="true" />
        <rewardStatus Type="DictIntBool" Default="{}" Persistent="true" />
        <quitMatchCnt Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />     <!-- 连续退出匹配次数 -->
        <quitMatchPunishTime Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />     <!-- 连续退出匹配惩罚时间 -->
        <quitConfirmCnt Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />     <!-- 连续退出匹配次数 -->
        <quitConfirmPunishTime Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />     <!-- 连续退出匹配次数惩罚时间 -->
        <consecutiveFailCnt Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true" />      <!-- 连败次数 -->
    </TEAM_PVP_MATCH_INFO>

    <TEAM_PVP_RECORD_INFO Type="tuple">
        <IsWin Type="bool" Default="false" />
        <KillNum Type="int" Default="0" />
        <DieNum Type="int" Default="nil" />
        <Damage Type="int" Default="0" />
        <Bear Type="int" Default="0" />
        <Heal Type="int" Default="0" />
        <GainPoints Type="int" Default="0" />    <!-- 获取胜点数 -->
        <PreRankID Type="int" Default="1" />  <!-- 开局段位 -->
        <PreRankPoints Type="int" Default="0" /> <!-- 开局胜点数 -->
        <Score Type="int" Default="nil" /> <!-- 占点分数 -->
    </TEAM_PVP_RECORD_INFO>

    <TEAM_PVP_BRIEF_RECORD Type="tuple" >
        <BriefInfo Type="TEAM_PVP_RECORD_INFO" />
        <GameId Type="string" Default="" />
        <Time Type="int" Default="0" />
        <OccupyNum Type="int" Default="nil" />
        <CampScore Type="int" Default="nil" />
        <TimeLength Type="int" Default="nil" /> <!-- 战斗时长 -->
        <MainRoundIndex Type="int" Default="nil" /> <!-- 轮次（第x轮） -->
        <RoundIndex Type="int" Default="nil" /> <!-- 局内第X回合 -->
        <SubType Type="int" Default="nil" /> <!-- 子类型 -->
    </TEAM_PVP_BRIEF_RECORD>

    <TEAM_PVP_BRIEF_RECORD_DICT Type="dict" Key="int" Value="TEAM_PVP_BRIEF_RECORD" />

    <TEAM_PVP_MEMBER_DETAIL_RECORD Type="tuple">
        <Name Type="string" Default="" />
        <IsWin Type="bool" Default="false" />
        <KillNum Type="int" Default="0" />
        <DieNum Type="int" Default="nil" />
        <AssistNum Type="int" Default="nil" />
        <Damage Type="int" Default="0" />
        <Bear Type="int" Default="0" />
        <Heal Type="int" Default="0" />
        <CampId Type="TEAM_ID" Default="0" />
        <RankPoints Type="int" Default="0" />
        <GainPoints Type="int" Default="0" />
        <ProfessionID Type="int" Default="0" />
        <TitleID Type="int" Default="nil" />
        <Score Type="int" Default="nil" />
    </TEAM_PVP_MEMBER_DETAIL_RECORD>

    <TEAM_PVP_CAMP_EXTRA_INFO Type="tuple">
        <OccupyNum Type="int" Default="nil" />
        <CampScore Type="int" Default="nil" />
    </TEAM_PVP_CAMP_EXTRA_INFO>

    <TEAM_PVP_DETAIL_RECORD Type="tuple">
        <DetailInfo Type="dict" Key="string" Value="TEAM_PVP_MEMBER_DETAIL_RECORD" />
        <CampExtraInfo Type="dict" Key="string" Value="TEAM_PVP_CAMP_EXTRA_INFO" Default="nil" />
    </TEAM_PVP_DETAIL_RECORD>

    <!-- arbitrator -->
    <TRIAL_NPC_INFO Type="tuple">
        <npcID Type="string" Default="" Flags="OWN_CLIENT" />
        <templateID Type="int" Default="0" Flags="OWN_CLIENT" />
        <npcType Type="int" Default="0" Flags="OWN_CLIENT" />
        <stage Type="int" Default="0" Flags="OWN_CLIENT" />
        <pressure Type="int" Default="0" Flags="OWN_CLIENT" />
    </TRIAL_NPC_INFO>

    <!-- ScheduleTask-->
    <SCHEDULE_TASK_INFO Type="tuple">
        <taskId Type="int" Persistent="true" />
        <curValue Type="int" Persistent="true" />
        <type Type="int" Persistent="true" />
        <isDone Type="bool" Persistent="true" />
        <isRewarded Type="bool"  Persistent="true" />
    </SCHEDULE_TASK_INFO>

    <SCHEDULE_TASKS_INFO Type="dict" Key="int" Value="SCHEDULE_TASK_INFO" />

    <REVELATION_TASK_INFO Type="tuple">
        <taskId Type="int" Persistent="true" />
        <statisticsId Type="int" Persistent="true" />
        <curValue Type="int" Persistent="true" />
        <isDone Type="bool" Persistent="true" />
        <isRewarded Type="bool"  Persistent="true" />
    </REVELATION_TASK_INFO>

    <!-- Sequence -->
    <DIGESTION_INFO Type="tuple">
        <digestionId Type="int" Persistent="true" />
        <curValue Type="int" Persistent="true" />
        <isUnLocked Type="bool" Persistent="true" />
        <isDigested Type="bool" Persistent="true" /> <!-- 是否被消化 -->
    </DIGESTION_INFO>

    <DIGESTION_INFO_DICT Type="dict" Key="int" Value="DIGESTION_INFO" />

    <DIGESTION_CONDITION_INFO Type="tuple">
        <conditionId Type="int" Persistent="true" />
        <curValue Type="int" Persistent="true" />
        <isDone Type="bool" Persistent="true" />
    </DIGESTION_CONDITION_INFO>

    <DIGESTION_CONDITION_DICT Type="dict" Key="int" Value="DIGESTION_CONDITION_INFO" />

    <SEQUENCE_INFO Type="tuple">
        <seqId Type="int" Persistent="true" />
        <curValue Type="int" Persistent="true" />
        <status Type="int" Persistent="true" />
    </SEQUENCE_INFO>

    <SEQUENCE_INFO_DICT Type="dict" Key="int" Value="SEQUENCE_INFO" />

    <!-- NewBieTask(目前暂时废弃) -->
    <NEWBIE_TASK_LIST Type="list" Element="UINT" Persistent="true"/> <!-- 新人手册任务列表 -->

    <NEWBIE_TASK_INFO Type="tuple">
        <taskId Type="int" Persistent="true" Default="0"/>
        <curValue Type="int" Persistent="true" Default="0"/>
        <isUnlock Type="bool"  Persistent="true" Default="false"/>
        <isDone Type="bool" Persistent="true" Default="false"/>
        <isRewarded Type="bool"  Persistent="true" Default="false"/>
    </NEWBIE_TASK_INFO>

    <NEWBIE_TASKS_INFO Type="dict" Key="int" Value="NEWBIE_TASK_INFO" />

    <NEWBIE_PROGRESS_INFO Type="tuple">
        <progressValue Type="int"  Persistent="true"/>
        <rewardedBoxs Type="KEY_BOOL"  Persistent="true"/>
    </NEWBIE_PROGRESS_INFO>

    <PARAM2TASKS Type="dict" Key="int" Value="ListInt" />

    <!-- Statistics -->
    <StatisticsDictDataList Type="list" Element="DictStrInt" />
    <StatisticsDictData Type="dict" Key="int" Value="StatisticsDictDataList" />    <!-- 统计变量的伴生结构，key为变量类型分组，value为统计ID -->
    <WORLD_BOSS_LINE_INFO Type="tuple">
        <status Type="int"/>
        <hpRatio Type="float" />
        <avatarRatio Type="float" />
        <LineType Type="int" />
    </WORLD_BOSS_LINE_INFO>
    <WORLD_BOSS_LINE_INFO_DICT Type="dict" Key="int" Value="WORLD_BOSS_LINE_INFO" />
    <WORLD_BOSS_RANK_DATA Type="tuple">
        <name Type="string" />
        <totalDamage Type="float" />
        <rank Type="int" />
        <rankType Type="int"/>
        <updateTime Type="float" />
        <teamId Type="TEAM_ID"/>
        <entityId Type="ENTITY_ID"/>
    </WORLD_BOSS_RANK_DATA>
    <WORLD_BOSS_RANK_DATA_ARRAY Type="list" Element="WORLD_BOSS_RANK_DATA" />

    <ActivityRefreshInfo Type="tuple">
        <id Type="int" />
        <type Type="int" />
        <startTime Type="float" />
        <endTime Type="int" />
    </ActivityRefreshInfo>

    <NpcAsideTalkInfo Type="tuple">
        <TargetID Type="string" Flags="ALL_CLIENTS" Default=""/>
        <bFollow Type="bool" Flags="ALL_CLIENTS" Default="false"/>
        <bEnable Type="bool" Flags="ALL_CLIENTS" Default="false"/>
        <AlertDistance Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </NpcAsideTalkInfo>
    <MoneySellSimpleInfo Type="tuple">
        <createTime Type="int" Persistent="true" />
        <sellRate Type="int" Persistent="true" />
        <sellNum Type="int" Persistent="true" />
    </MoneySellSimpleInfo>
    <MoneyRateBuyList Type="dict" Key="int" Value="int"/>
    <MoneyBuyHistory Type="tuple" >
        <buyTime Type="int" Persistent="true" />
        <buyRate Type="int" Persistent="true" />
        <buyMoneyNum Type="int" Persistent="true" />
    </MoneyBuyHistory>
    <MoneyBuyHistoryList Type="list" Element="MoneyBuyHistory"/>  <!-- 货币购买历史记录 -->
    <MoneySellHistory Type="tuple">
        <sellTime Type="int" Persistent="true" />
        <sellMoney Type="int" Persistent="true" />
        <sellRate Type="int" Persistent="true" />
        <sellSuccessTime Type="int" Persistent="true" />
    </MoneySellHistory>
    <MoneySellHistoryList Type="list" Element="MoneySellHistory"/>  <!-- 货币出售历史记录 -->
    <MoneySellInfo Type="tuple">
        <createTime Type="int" Persistent="true" />
        <sellRate Type="int" Persistent="true" />
        <sellNum Type="int" Persistent="true" />
        <avatarID Type="string" />
        <orderID Type="UUID" Persistent="true" />   <!-- 订单id -->
    </MoneySellInfo>
    <MoneySellList Type="list" Element="MoneySellInfo"/>
    <OwnerSellMoneyInfo Type="tuple">
        <createTime Type="int" Persistent="true" />
        <sellRate Type="int" Persistent="true" />
        <sellNum Type="int" Persistent="true" />        <!-- 当前在售数量 -->
        <totalSellNum Type="int" Persistent="true" />   <!-- 总出售数量 -->
        <orderID Type="UUID" Persistent="true" />       <!-- 订单id -->
    </OwnerSellMoneyInfo>
    <OwnerSellMoneyInfoList Type="dict" Key="UUID" Value="OwnerSellMoneyInfo"/>
    <TowerClimbSpawnerInfo Type="tuple">
        <Count Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </TowerClimbSpawnerInfo>
    <DynamicTaskInfo Type="tuple">
        <TowerClimbSpawner Type="TowerClimbSpawnerInfo" Flags="ALL_CLIENTS"/>
        <TowerClimbSurviveTime Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <TowerClimbFailTime Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </DynamicTaskInfo>
    <TowerClimbFellowInfo Type="tuple">
        <FellowID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <FellowEID Type="string" Flags="ALL_CLIENTS" Default="0"/>
    </TowerClimbFellowInfo>
    <FellowDict Type="dict" Key="int" Value="TowerClimbFellowInfo" Flags="ALL_CLIENTS"/>

    <!-- 拍卖相关  开始 -->
    <BID_ITEM Type="tuple">
        <gbId Type="GBID" />      <!-- 拥有该物品竞拍最高价的人的gbId -->
        <rolename Type="string" />  <!-- 拥有该物品竞拍最高价的人的名字 -->
        <status Type="UINT" />      <!-- 该物品的状态(已售出，已结束，竞价中) -->
        <item Type="INV_SLOT_VAL" Persistent="true" /> <!-- 该物品信息 -->
        <bidPrice Type="UINT" Persistent="true" />    <!-- 当前竞拍价格 -->
        <bidItemType Type="UINT" /> <!-- 本物品拍卖的类型 -->
        <endTime Type="UINT" />     <!-- 本物品���束拍卖时间 -->
        <extraFlag Type="BOOL" Persistent="true" />     <!-- 是否是运营额外拍卖品 -->
        <index Type="UINT" />     <!-- 拍卖列表中的索引 -->
    </BID_ITEM>

    <BID_ITEM_LIST Type="list" Element="BID_ITEM" />

    <!-- 拍卖通用结构 -->
    <BID Type="tuple">
        <type Type="UINT" Persistent="true" />                                  <!-- 拍卖类型 -->
        <status Type="UINT" Persistent="true" />                                <!-- 拍卖当前状态 -->
        <startTime Type="UINT" Persistent="true" />                             <!-- 拍卖开启时间 -->
        <bidItems Type="BID_ITEM_LIST" Persistent="true" />                     <!-- 拍卖的物品信息 -->
        <opNUID Type="UUID" Persistent="true" />                                <!-- 此次拍卖的opNUID -->
        <itemCnts Type="KEY_NUMBER" Persistent="true" />                             <!-- 拍卖物品计数 -->
    </BID>

    <SERVER_BID Type="tuple">
        <unsoldItemsNum Type="UINT" Default="0" Persistent="true" />
        <bidItems Type="BID_ITEM_LIST" Persistent="true" />
        <startTime Type="UINT" />  <!-- 该场拍卖开始时间 -->
    </SERVER_BID>

    <GUILD_BID_ITEM_SET Type="tuple">
        <startTime Type="UINT" />  <!-- 该场拍卖开始时间 -->
        <bidItems Type="BID_ITEM_LIST" />  <!-- 拍卖道具信息 -->
    </GUILD_BID_ITEM_SET>

    <GUILD_BID Type="dict" Key="UINT" Value="GUILD_BID_ITEM_SET" />

    <ONE_BID_RECORD Type="tuple">
        <itemId Type="UINT"/>       <!-- 该物品的Id -->
        <itemNum Type="UINT"/>      <!-- 该物品的数量 -->
        <activityId Type="UINT"/>   <!-- 活动id -->
        <bidResult Type="UINT"/>    <!-- 竞价结果 -->
        <bidPrice Type="UINT"/>     <!-- 竞价价格 -->
        <bidEndTime Type="UINT" />     <!-- 成交时间 -->
        <rolename Type="string" />  <!-- 人物名字 -->
    </ONE_BID_RECORD>

    <GUILD_BID_RECORD Type="list" Element="ONE_BID_RECORD" />


    <BID_PRICE Type="dict" Key="UINT" Value="UINT" />
    <BID_PRICE_RECORD Type="dict" Key="UINT" Value="BID_PRICE" />
    <!-- 拍卖相关  结束 -->

    <!-- c8用到的结构体适配声明相关  开始 -->

    <!-- c8用到的结构体适配声明相关  开始 -->

    <!-- 交易所相关  开始 -->
    <BUY_ITEM_INFO Type="tuple">
        <sellerGbId Type="string"/>
        <index Type="UINT"/>
        <itemGbId Type="UUID"/>
        <number Type="UINT"/>
        <price Type="UINT"/>
    </BUY_ITEM_INFO>

    <TRADE_GOODS_ITEM_LIST Type="list" Element="BUY_ITEM_INFO"/> <!--���捷购买摆摊物品列表-->
    <STALL_ITEM_STATISTICS_INFO Type="tuple">
        <playerSellingCount Type="UINT" Persistent="true" />
        <systemSellingCount Type="UINT" Persistent="true" />
        <playerSellingTotalPrice Type="UINT" Persistent="true" />
        <systemSellingTotalPrice Type="UINT" Persistent="true" />
        <playerSoldCount Type="UINT" Persistent="true" />
        <systemSoldCount Type="UINT" Persistent="true" />
        <playerSoldTotalPrice Type="UINT" Persistent="true" />
        <systemSoldTotalPrice Type="UINT" Persistent="true" />
        <playerSoldMinPrice Type="INT" Default="-1" Persistent="true" />
        <systemSoldMinPrice Type="INT" Default="-1" Persistent="true" />
        <playerSoldMaxPrice Type="UINT" Persistent="true" />
        <systemSoldMaxPrice Type="UINT" Persistent="true" />
        <itemSellingMinPrice Type="UINT" Persistent="true" />
    </STALL_ITEM_STATISTICS_INFO>
    <STALL_SELLER_INFO Type="tuple" >
        <accountId Type="string"  Persistent="true"/>
        <gbId Type="string"  Persistent="true"/>
        <rolename Type="string"  Persistent="true"/>
        <lv Type="int"  Persistent="true"/>
        <vipLv Type="int"  Persistent="true"/>
        <vip Type="int"  Persistent="true"/>
        <mac_addr Type="string"  Persistent="true" />
        <ip Type="string"  Persistent="true" />
        <school Type="int"  Persistent="true" />
        <old_accountid Type="string"  Persistent="true" />
        <app_channel Type="string"  Persistent="true" />
        <role_type Type="string"  Persistent="true" />
        <role_gender Type="string"  Persistent="true" />
        <role_force Type="int"  Persistent="true" />
        <udid Type="string"  Persistent="true" />
        <isDisturbed Type="BOOL"  Persistent="true" />
        <greenCardState Type="int"  Persistent="true" />
    </STALL_SELLER_INFO>
    <STALL_HOUR_STATISTICS_INFO Type="tuple">
        <hour Type="UINT" Persistent="true" />
        <timeStamp Type="UINT" Persistent="true" />
        <itemId2StatisticsInfo Type="dict" Key="UINT" Value="STALL_ITEM_STATISTICS_INFO" Persistent="true" />
    </STALL_HOUR_STATISTICS_INFO>

    <STALL_DAY_STATISTICS_INFO Type="tuple">
        <day Type="UINT" Persistent="true" />
        <timeStamp Type="UINT" Persistent="true" />
        <itemId2StatisticsInfo Type="dict" Key="UINT" Value="STALL_ITEM_STATISTICS_INFO" Persistent="true" />
    </STALL_DAY_STATISTICS_INFO>

    <STALL_CLOSE_TIME Type="tuple">
        <beginHour Type="UINT" Persistent="true" />
        <endHour Type="UINT" Persistent="true" />
    </STALL_CLOSE_TIME>

    <STALL_SERVER_LEVEL_LIMIT Type="tuple">
        <min Type="UINT" Persistent="true" />
        <max Type="UINT" Persistent="true" />
    </STALL_SERVER_LEVEL_LIMIT>

    <STALL_AUTO_SYS_REPLENISH_SETTING Type="tuple">
        <countMode Type="UINT" Persistent="true" />
        <countParams Type="ARRAY_NUMBER" Persistent="true" />
        <priceMode Type="UINT" Persistent="true" />
        <priceParams Type="ARRAY_NUMBER" Persistent="true" />
        <conditionMode Type="UINT" Persistent="true" />
        <conditionParams Type="ARRAY_NUMBER" Persistent="true" />
        <timeCycle Type="UINT" Persistent="true" />
        <slotCount Type="UINT" Persistent="true" />
        <serverLevelLimit Type="STALL_SERVER_LEVEL_LIMIT" Persistent="true" Default="nil" />
        <closeTime Type="STALL_CLOSE_TIME" Persistent="true" Default="nil" />
    </STALL_AUTO_SYS_REPLENISH_SETTING>
    <STALL_ITEM_INFO Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" />
        <price Type="UINT" Persistent="true" />
        <expiryTime Type="UINT" Persistent="true" />    <!-- 过期时间(对应公示期和在售期商品的时间, 公示期时小于下架时间，在售期时等于下���时间) -->
        <soleOutTime Type="UINT" Persistent="true" />   <!-- 下架时间 -->
        <playerGbId Type="string" Persistent="true" />
        <followers Type="UINT" Persistent="true" />
        <hisPop Type="UINT" Default="nil" />       <!-- 历史最高热度 -->
        <curPop Type="UINT" Default="nil" />       <!-- 当前热度 -->
    </STALL_ITEM_INFO>

    <STALL_OVERTIME_ITEM_INFO Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" />
        <price Type="UINT" Persistent="true" />
        <hisPop Type="UINT" Default="nil" />       <!-- 历史最高热度 -->
        <curPop Type="UINT" Default="nil" />       <!-- 当前热度 -->
    </STALL_OVERTIME_ITEM_INFO>

    <STALL_SOLD_ITEM_INFO Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" />
        <price Type="UINT" Persistent="true" />
        <soldNum Type="UINT" Persistent="true" />
    </STALL_SOLD_ITEM_INFO>

    <STALL_DEAL_RECORD Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" />
        <price Type="UINT" Persistent="true" />
        <time Type="LONGTIME" Persistent="true" />
        <state Type="UINT" Persistent="true" />
    </STALL_DEAL_RECORD>

    <STALL_CENSOR_RECORD Type="tuple" >
        <item Type="INV_SLOT_VAL" />
        <price Type="UINT" />
        <overtime Type="LONGTIME" />
    </STALL_CENSOR_RECORD>
    <STALL_SELL_BASE_INFO Type="tuple" >
        <price Type="UINT" />
        <soldNum Type="UINT" />
    </STALL_SELL_BASE_INFO>
    <STALL_SELL_BASE_INFO_LIST Type="list" Element="STALL_SELL_BASE_INFO" />

    <STALL_CENSOR_RECORD_MAP Type="dict" Key="UUID" Value="STALL_CENSOR_RECORD" />

    <STALL_BOUGHT_ITEM_INFO Type="tuple" >
        <item Type="INV_SLOT_VAL" />
        <sellerGbId Type="string" />
        <price Type="UINT" />
        <totalNum Type="UINT" />
        <opNUID Type="NUID" />
        <sellerInfo Type="STALL_SELLER_INFO" />
    </STALL_BOUGHT_ITEM_INFO>

    <UGC_PLAYER_STALL_INFO Type="tuple" >
        <hisPop Type="UINT" />       <!-- 历史最高热度 -->
        <curPop Type="UINT" />       <!-- 当前热度 -->
        <gbId Type="string" />         <!-- gbId -->
        <itemCount Type="UINT" />    <!-- 道具总数 -->
        <name Type="string" />       <!-- 店铺名字 -->
    </UGC_PLAYER_STALL_INFO>

    <UGC_PLAYER_STALL_LIST Type="list" Element="UGC_PLAYER_STALL_INFO" />

    <FEE_COST_DATA Type="tuple" >
        <type Type="UINT" Persistent="true" />
        <count Type="UINT" Persistent="true" />
    </FEE_COST_DATA>

    <STALL_LOCKED_PARAM Type="tuple" >
        <price Type="UINT" Persistent="true" />
        <feeCost Type="FEE_COST_DATA" Persistent="true" />
        <needCensor Type="BOOL" Persistent="true" Default="nil"/>
        <gbId Type="string" Persistent="true" Default="nil"/>
        <name Type="string" Persistent="true" Default="nil"/>
    </STALL_LOCKED_PARAM>

    <STALL_LOCKED_DATA Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" Default="nil"/>
        <lockType Type="UINT" Default="0" Persistent="true"/>
        <param Type="STALL_LOCKED_PARAM" Persistent="true" Default="nil"/>
        <opNUID Type="NUID" Persistent="true" Default="nil"/>
        <timerId Type="UINT" />
        <tryFunction Type="string" />
    </STALL_LOCKED_DATA>

    <TRADE_GIVER_DATA Type="tuple" >
        <item Type="INV_SLOT_VAL" Persistent="true" Default="nil" />
        <opNUID Type="NUID" Persistent="true" Default="nil" />
        <pointsNeed Type="UINT" Persistent="true" Default="nil" />
        <gbId Type="string" Persistent="true" Default="nil" />
        <name Type="string" Persistent="true" Default="nil" />
    </TRADE_GIVER_DATA>

    <RANK_REGION_INFO Type="tuple" >
        <country Type="int" />
        <province Type="int" />
        <city Type="int" />
        <profession Type="int" />
    </RANK_REGION_INFO>

    <RANK_REPORT_INFO Type="tuple" >
        <keyScore Type="float" />
        <time Type="int" />
    </RANK_REPORT_INFO>

    <RANK_REPORT_INFOS Type="dict" Key="int" Value="RANK_REPORT_INFO" />

    <RANK_SYNC_INFOS Type="ListStr" />

    <RANK_EXTRA_INFOS Type="DictStrInt" />

    <RANK_PULL_GUILD_NAME Type="list" Element="ListStr" />

    <RANK_EXTRAARGS Type="tuple" >
        <profession Type="int" />
        <country Type="string" />
        <province Type="string" />
        <city Type="string" />
    </RANK_EXTRAARGS>

    <RANK_INFO Type="tuple" >
        <rankInfo Type="ListListStr" />
        <rankExtraInfo Type="DictStrInt" />
    </RANK_INFO>

    <RANK_INFOS Type="dict" Key="string" Value="RANK_INFO" />

    <STALL_ITEM_LIST Type="list" Element="STALL_ITEM_INFO" />
    <STALL_ITEM_MAP Type="dict" Key="UUID" Value="STALL_ITEM_INFO" />
    <STALL_OVERTIME_ITEM_MAP Type="dict" Key="UUID" Value="STALL_OVERTIME_ITEM_INFO" />
    <STALL_SOLD_ITEM_MAP Type="dict" Key="UUID" Value="STALL_SOLD_ITEM_INFO" />
    <STALL_DEAL_RECORD_LIST Type="list" Element="STALL_DEAL_RECORD"/>
    <STALL_BOUGHT_ITEM_LIST Type="list" Element="STALL_BOUGHT_ITEM_INFO"/>
    <STALL_EQUIP_ITEM_SEARCH_PARAM Type="tuple" >
        <itemId Type="UINT" />
        <minPrice Type="UINT" />
        <maxPrice Type="UINT" />
        <fixAttrib Type="list" Element="UINT" />
        <specialProp Type="list" Element="UINT" />
    </STALL_EQUIP_ITEM_SEARCH_PARAM>
    <STALL_FORBIDDEN_INFO Type="tuple" >
        <endTime Type="UINT" Persistent="true" />
        <reason Type="string" Persistent="true" />
    </STALL_FORBIDDEN_INFO>
    <ItemsInfoBySecondaryType Type="tuple" >
        <totalNum Type="UINT"  />
        <price Type="UINT"  />
    </ItemsInfoBySecondaryType>
    <ItemsInfoBySecondaryTypeList Type="dict" Key="int" Value="ItemsInfoBySecondaryType" />
    <STALL_FOLLOW_ITEM_PLAYER_INFO Type="tuple" >
        <rolename Type="string"  />
        <gbId Type="string"  />
        <username Type="string"  />
        <school Type="int"  />
        <lv Type="int"  />
    </STALL_FOLLOW_ITEM_PLAYER_INFO>
    <!-- 交易所相关  结束 -->
    <!-- 拍卖功能相关  开始 -->
    <!-- 拍卖道具详细数据 -->
    <BID_ITEM_INFO Type="tuple" >
        <id Type="UINT" />                      <!-- 拍卖道具编号id -->
        <itemId Type="UINT"  />                 <!-- ���卖道具id  -->
        <configID Type="UINT"  />               <!-- 配置ID(对应BidItem的ID) -->
        <beginTime Type="UINT"  />              <!-- 拍卖开始时间  -->
        <expiryTime Type="UINT"  />             <!-- 拍卖结束时间  -->
        <currentBidder Type="string" />         <!-- 当前出价者(玩家唯一id), 没有拍卖玩家时为“” -->
        <currentBidderName Type="string" />     <!-- 当前出价者名字-->
        <currentBidPrice Type="UINT"  />        <!-- 当前拍卖价格 -->
        <initialPrice Type="UINT" />            <!-- 初始起拍价格 -->
        <bidOutPrice Type="UINT"  />            <!-- 一口价 -->
    </BID_ITEM_INFO>
    <!-- 所有拍卖道具详细数据列表 -->
    <BID_ITEM_INFO_LIST Type="list" Element="BID_ITEM_INFO" />
    <!-- 拍卖道具摘要数据 -->
    <BID_ITEM_SIMPLE_INFO Type="tuple" >
        <id Type="UINT" />                      <!-- 拍卖道具编号id -->
        <expiryTime Type="UINT"  />             <!-- 拍卖结束时间  -->
        <currentBidder Type="string" />         <!-- 当前出价者(玩家唯一id), 没有拍卖玩家时为“” -->
        <currentBidderName Type="string" />     <!-- 当前出价者名字-->
        <currentBidPrice Type="UINT"  />        <!-- 当前拍卖价格 -->
    </BID_ITEM_SIMPLE_INFO>
    <!-- 所有拍卖道具摘要数据列表 -->
    <BID_ITEM_SIMPLE_INFO_LIST Type="list" Element="BID_ITEM_SIMPLE_INFO" />
    <BID_RECORD_LIST Type="dict" Key="int" Value="int" />
    <ALL_PLAYER_BID_RECORD_LIST Type="dict" Key="string" Value="BID_RECORD_LIST" />
    <BID_ITEM_DB_INFO Type="tuple" >
        <id Type="UINT" Persistent="true" />                     <!-- 拍卖道具编号id -->
        <itemId Type="UINT" Persistent="true" />                 <!-- 拍卖道具id  -->
        <configID Type="UINT" Persistent="true"  />              <!-- 配置ID(对应BidItem的ID) -->
        <beginTime Type="UINT" Persistent="true" />              <!-- 拍卖开始时间  -->
        <expiryTime Type="UINT" Persistent="true" />             <!-- 拍卖结束时间  -->
        <currentBidder Type="string" Persistent="true" />        <!-- 当前出价者(玩家唯一id), 没有拍卖玩家时为“” -->
        <currentBidderName Type="string" Persistent="true"/>     <!-- 当前出价者名字-->
        <initialPrice Type="UINT" Persistent="true"/>            <!-- 初始起拍价格-->
        <currentBidPrice Type="UINT" Persistent="true" />        <!-- 当前拍卖价格 -->
        <bidOutPrice Type="UINT" Persistent="true" />            <!-- 一口价 -->
        <itemBidStatus Type="UINT" Persistent="true" />          <!-- 道具拍卖状态 -->
        <buyoutPricePlayerIdList Type="list" Element="string" Persistent="true" />    <!-- 一口价玩家ID列表 -->
        <buyoutPricePlayerNameList Type="list" Element="string"  Persistent="true" />    <!-- 一口价玩家名字列表 -->

        <canResetExpiryTime Type="BOOL" Persistent="true" />     <!-- 能否重置倒计时 -->
    </BID_ITEM_DB_INFO>

    <BID_BASE_DATA Type="tuple" >
        <activityId Type="UINT" Persistent="true" />                                        <!-- 拍卖活动ID -->
        <bidType Type="UINT" Persistent="true" />                                           <!-- 拍卖类型 -->
        <beginTime Type="UINT" Persistent="true" />                                         <!-- 拍卖开始时间 -->
        <prepareEndTime Type="UINT"  Persistent="true" />                                   <!-- 准备阶段结束时间  -->
        <bidingEndTime Type="UINT" Persistent="true" />                                     <!-- 拍卖持续阶段结束时间 -->
        <buttonDisappearTime Type="UINT"  Persistent="true" />                              <!-- 拍卖按钮消失时间 -->
        <prepareDuration Type="UINT" Persistent="true" />                                   <!-- 拍卖准备阶段时间 -->
        <playerBidRecordList Type="BID_RECORD_LIST" Persistent="true" />                    <!-- 玩家拍卖过的id列表 -->
        <totalEndTime Type="UINT" Persistent="true" />                                      <!-- 总结束时间,显示用(公会拍卖 = 公会拍卖+ 世界拍卖， 世界拍卖不用加公会拍卖时间) -->
        <bidSourceId Type="UINT" Persistent="true" />
    </BID_BASE_DATA>

    <BID_ITEM_HISTORY Type="tuple" >
        <ItemId Type="UINT" Persistent="true" />                                         <!-- 拍卖道具id -->
        <BidPrice Type="UINT"  Persistent="true" />                                      <!-- 成交价格  -->
        <RecordTime Type="UINT" Persistent="true" />                                     <!-- 成交时间 -->
    </BID_ITEM_HISTORY>
    <BidItemHistory Type="tuple">
        <RecordsTable Type="list" Element="BID_ITEM_HISTORY"/>                           <!-- 成功拍卖道具信息 -->
        <PageLastRecord Type="string" Persistent="false"/>                               <!-- 最后一条数据id -->
        <TotalRecords Type="UINT" Persistent="false"/>                                   <!-- 总共有多少条数据记录 -->
    </BidItemHistory>
    <AbortBidItemIdList Type="list" Element="BID_ITEM_DB_INFO" Persistent="true" />      <!-- 流拍道具列表 -->
    <!-- 拍卖功能相关  结束 -->

    <!-- 药师相关  开始 -->
    <SINGLE_PRESCRIPTION_DEFINE Type="tuple">
        <prescription Type="DictIntInt" Persistent="true" />
        <passMystery Type="bool" Persistent="true"/>
    </SINGLE_PRESCRIPTION_DEFINE>
    <SINGLE_PRESCRIPTION_LIST Type="list" Element="SINGLE_PRESCRIPTION_DEFINE" Persistent="true" />
    <PRESCRIPTION_MAP Type="dict" Key="UINT" Value="SINGLE_PRESCRIPTION_LIST" Persistent="true" />
    <LAST_PRESCRIPTION Type="tuple" >
        <medicineID Type="UINT" Default="nil" Persistent="false" />
        <prescription Type="DictIntInt" Default="nil" Persistent="false" />
        <passMystery Type="bool" Default="nil" Persistent="false" />
    </LAST_PRESCRIPTION>

    <EXTRA_MEDICINE_ELEMENT Type="tuple" >
        <probability Type="float" Flags="SERVER_ONLY" Persistent="true" />
        <count Type="int" Flags="SERVER_ONLY" Persistent="true" />
    </EXTRA_MEDICINE_ELEMENT>

    <EXTRA_MEDICINE_INFO Type="tuple" >
        <extraMedicineDict Type="dict" Key="int" Value="EXTRA_MEDICINE_ELEMENT" Default="nil" Flags="SERVER_ONLY" Persistent="true" /> <!--key是额外药品id-->
        <diceID Type="int" Flags="SERVER_ONLY" Persistent="true" />
    </EXTRA_MEDICINE_INFO>

    <PHARMACIST_SKILL_PROPERTY_ELEMENT Type="tuple" >
        <exploreRecovery Type="float" Default="nil" Flags="SERVER_ONLY" Persistent="true" />
        <quickRecovery Type="float" Default="nil" Flags="SERVER_ONLY" Persistent="true" />
        <extraMedicine Type="EXTRA_MEDICINE_INFO" Default="nil" Flags="SERVER_ONLY" Persistent="true" />
    </PHARMACIST_SKILL_PROPERTY_ELEMENT>
    <PHARMACIST_SKILL_PROPERTY Type="dict" Key="int" Value="PHARMACIST_SKILL_PROPERTY_ELEMENT" Flags="SERVER_ONLY" Persistent="true"/>
    <!-- 药师相关  结束 -->

    <!-- 扮演玩法 开始 -->
    <SPIRIT_DEFINE Type="tuple" >
        <val Type="UINT" Flags="OWN_CLIENT" Persistent="true" />     <!--当前值-->
        <lastRecovery Type="int" Flags="OWN_CLIENT" Persistent="true" />   <!--上次恢复时间-->
    </SPIRIT_DEFINE>
    <ROLEPLAY_IDENTITY Type="tuple" >
        <level Type="int" Flags="OWN_INITIAL_ONLY" Persistent="true" />       <!--当前等级-->
        <exp Type="int" Flags="OWN_INITIAL_ONLY" Persistent="true" />         <!--经验-->
        <curExp Type="int" Flags="OWN_INITIAL_ONLY" Persistent="true" />      <!--今日已获得的经验,用于限制每日经验获得上限-->
    </ROLEPLAY_IDENTITY>
    <ROLEPLAY_IDENTITY_DEFINE Type="dict" Key="UINT" Value="ROLEPLAY_IDENTITY" />

    <!--非凡扮演棋盘单个格子信息-->
    <RPGameGridInfo Type="tuple">
        <status Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>          <!--格子状态，0为未开启，1为成功，2为失败-->
        <event Type="int" Flags="SERVER_ONLY" Persistent="true"/>                       <!--格子的事件编号-->
    </RPGameGridInfo>
    <!--棋盘信息，key为坐标（x * 10000 + y），value为单个格子信息-->
    <RPGameGridDict Type="dict" Key="int" Value="RPGameGridInfo"/>

    <!--非凡扮演的选项和分数信息-->
    <RPGameOptionScoreInfo Type="tuple">
        <serialIndex Type="int" Flags="SERVER_ONLY" Persistent="true"/>     <!--题组内的序号（选项）-->
        <score Type="int" Flags="SERVER_ONLY" Persistent="true"/>           <!--分数-->
    </RPGameOptionScoreInfo>
    <!--各个题组对应的分数-->
    <RPGameOptionScoreDict Type="dict" Key="int" Value="RPGameOptionScoreInfo"/>
    <!--各个事件的选项对应的分数-->
    <RPGameEventScoreDict Type="dict" Key="int" Value="RPGameOptionScoreDict"/>

    <!--棋盘格子对应的物品列表-->
    <RPGameGridShopItems Type="dict" Key="int" Value="ListInt"/>
    <!-- 扮演玩法 结束 -->

    <SUBMIT_ITEM Type="tuple" >
        <itemID Type="UINT" />   <!--itemID-->
        <count Type="UINT" />     <!--数量-->
    </SUBMIT_ITEM>
    <SUBMIT_ITEM_DEFINE Type="list" Element="SUBMIT_ITEM" /> <!--按顺序填入-->

    <FASHION_VIEW_DATA Type="tuple" >
        <viewOpen Type="UINT" />        <!--是否展示开关-->
        <fashionScore Type="UINT" />    <!--风尚值-->
    </FASHION_VIEW_DATA>
    <PVP_VIEW_DATA Type="tuple" >
        <viewOpen Type="UINT" />        <!--是否展示开关-->
        <pvpScore Type="UINT" />        <!--Pvp段位-->
    </PVP_VIEW_DATA>

    <VIEW_ATTR_BRIEF Type="tuple" >
        <!--1 Equips -->
        <equipmentInfoSlots Type="EQUIPMENT_INFO_SLOTS" Flags="OWN_CLIENT" />
        <!--2 Attributes -->
        <briefAttr Type="DictStrFloat" Flags="OWN_CLIENT" />
        <!--3  ElementDamages -->
        <elementAttr Type="DictStrFloat" Flags="OWN_CLIENT" />
        <!--4 CombatScoreInfo  -->
        <combatScore Type="CombatScoreInfo" Flags="OWN_CLIENT" />
        <!--5 state  -->
        <state Type="int" Flags="OWN_CLIENT" />
        <!--6 Bounty -->
        <bounty Type="int" Flags="OWN_CLIENT" />
        <!--7 shortUid -->
        <shortUid Type="int" Flags="OWN_CLIENT" />
        <!--8 GuildName -->
        <guildName Type="string" Flags="OWN_CLIENT" />
        <!--9 guildBadgeFrameId -->
        <guildBadgeFrameId Type="UINT" Flags="OWN_CLIENT" />
        <!--10 sequenceId -->
        <sequenceId Type="UINT" Flags="OWN_CLIENT" />
        <!--11 digestionId -->
        <digestionId Type="UINT" Flags="OWN_CLIENT" />
        <!--12 SefirotType -->
        <sefirotType Type="UINT" Flags="OWN_CLIENT" />
        <!--13 bGroupLeader -->
        <bGroupLeader Type="bool" Flags="OWN_CLIENT" />
        <!--14 sex -->
        <sex Type="UINT" Flags="OWN_CLIENT" />
    </VIEW_ATTR_BRIEF>

    <VIEW_ROLE_BRIEF Type="tuple" >
        <!--1 id -->
        <id Type="string" Flags="OWN_CLIENT" />
        <!--2 Name -->
        <name Type="string" Flags="OWN_CLIENT" />
        <!--3  Profession -->
        <profession Type="int" Flags="OWN_CLIENT" />
        <!--4  Sex -->
        <sex Type="int" Flags="OWN_CLIENT" />
        <!--5 Level  -->
        <level Type="int" Flags="OWN_CLIENT" />
        <!--6 teamID  -->
        <teamID Type="TEAM_ID" Flags="OWN_CLIENT" />
        <!--7 isCaptain -->
        <isCaptain Type="bool" Flags="OWN_CLIENT" />
        <!--8 ZhanLi -->
        <zhanLi Type="int" Flags="OWN_CLIENT" />
        <!--9 guildId -->
        <guildId Type="string" Flags="OWN_CLIENT" />
        <!--10 guildName -->
        <guildName Type="string" Flags="OWN_CLIENT" />
        <!--11 groupID -->
        <groupID Type="GROUP_ID" Flags="OWN_CLIENT" />
        <!--13 guildRoleIds -->
        <guildRoleIds Type="DictIntInt" Flags="OWN_CLIENT" />
        <!--15 CustomFaceData -->
        <customFaceData Type="string" Flags="OWN_CLIENT" />
        <!--16 FashionData -->
        <fashionData Type="FASHION_VIEW_DATA" Flags="OWN_CLIENT" />
        <!--17 PvpData -->
        <pvpData Type="PVP_VIEW_DATA" Flags="OWN_CLIENT" />
        <!--18 guildStatus -->
        <guildStatus Type="int" Flags="OWN_CLIENT" />
        <!--19 LineType -->
        <lineType Type="int" Flags="OWN_CLIENT" />
        <!--20 MapType -->
        <mapType Type="int" Flags="OWN_CLIENT" />
        <!--21 HomelandUnlockType -->
        <homelandUnlockType Type="UINT" Flags="OWN_CLIENT"/>
        <!--22 portrait -->
        <portrait Type="int" Default="nil" />
        <!--23 portraitFrame -->
		<portraitFrame Type="int" Default="nil" />
    </VIEW_ROLE_BRIEF>

    <REWARDTASK_PROP Type="tuple">
        <taskID Type="UINT" Flags="OWN_CLIENT" Default = "0" Persistent="true"/> <!--当前缉拿对应taskID-->
        <time Type="UINT" Flags="OWN_CLIENT" Default = "0" Persistent="true"/>  <!--任务的时间戳-->
    </REWARDTASK_PROP>
    <!-- Key: 治安官任务的唯一id -->
    <REWARD_CATCH_TASK_DEFINE Type="dict" Key="UINT" Value="REWARDTASK_PROP" Persistent="true" />

    <TEMPORARY_PREFAB_INFO Type="dict" Key="UINT" Value="DictStrStr" Persistent="true" />
    <TEMPORARY_INTERACTOR_INFO Type="dict" Key="string" Value="DictStrStr" Persistent="true" />

    <completedTriggerList Type="dict" Key="UINT" Value="UINT" Persistent="true" />

    <InteractorElemSpreadConfInfo Type="tuple">
        <ElemID Type="int"/>
        <SpreadSecs Type="float"/>
        <SpreadRadius Type="int"/>
        <ElemSecs Type="float"/>
        <RequireState Type="string"/>
    </InteractorElemSpreadConfInfo>
    <InteractorElemSpreadInfo Type="tuple">
        <SpreadConf Type="list" Element="InteractorElemSpreadConfInfo"/>
        <time Type="UINT"/>
    </InteractorElemSpreadInfo>
    <InteractorElemSpreadMap Type="dict" Key="string" Value="InteractorElemSpreadInfo" Persistent="true" />

    <!--扮演玩法序列相关开始-->
    <SEQUENCE_SKILL_DICT Type="dict" Key="int" Value="int" Persistent="true" />
    <SEQUENCE_UNLOCKED_SKILL_DEFINE Type="dict" Key="UINT" Value="SEQUENCE_SKILL_DICT" Persistent="true"/> <!--key是序列id，value是存储解锁技能的dict-->
    <!--扮演玩法序列相关结束-->

    <ReqNpcSPInteractSubmissionParams Type="tuple">
        <FreeChoice Type="SUBMIT_ITEM_DEFINE"/>
    </ReqNpcSPInteractSubmissionParams>
    <ReqNpcSPInteractParams Type="tuple">
        <Submission Type="ReqNpcSPInteractSubmissionParams"/>
    </ReqNpcSPInteractParams>

    <!--pvp记录信息-->

    <PVP_TEAM_BATTLE_INFO Type="tuple">
        <id Type="UINT" Persistent="true" />
    </PVP_TEAM_BATTLE_INFO>

    <PVP_TEAM_ARENA_BATTLE_INFO Type="tuple">
        <id Type="UINT" Persistent="true" />
    </PVP_TEAM_ARENA_BATTLE_INFO>

    <PVP_BATTLE_INFO Type="tuple">
        <!--3v3-->
        <pvpTeamBattleInfo Type="PVP_TEAM_BATTLE_INFO" Persistent="true"/>
        <!--5v5-->
        <pvpTeamArenaBattleInfo Type="PVP_TEAM_ARENA_BATTLE_INFO" Persistent="true"/>
    </PVP_BATTLE_INFO>
    <pvpBattleInfo Type="PVP_BATTLE_INFO" Persistent="true" />

	<INDIVIDUAL_PVP_REQ_INFO Type="tuple">
        <SpaceEntityID Type="string"/>
        <PosX Type="int"/>
        <PosY Type="int"/>
        <PosZ Type="int"/>
        <timerID Type="int"/>
    </INDIVIDUAL_PVP_REQ_INFO>
    <RECVREQAVATARLIST Type="dict" Key="string" Value="INDIVIDUAL_PVP_REQ_INFO"/>

    <TASK_EXTRA_DATA_ITEM Type="tuple">
        <TaskID Type="int" Persistent="true" />
        <Idx Type="int" Persistent="true" />
        <AutoRemove Type="bool" Persistent="true" />
        <DataType Type="int" Persistent="true" />
        <FindCorrentNpc Type="dict" Key="string" Value="int" Persistent="true" />
    </TASK_EXTRA_DATA_ITEM>

    <BUILDING_INFO Type="tuple">        <!-- 家具摆放信息 -->
        <buildingID Type="UINT" Persistent="true"/>         <!-- 家具索引,唯一ID -->
        <furnitureID Type="UINT" Persistent="true"/>        <!-- 家具类型 -->
        <position Type="UINT" Persistent="true"/>           <!-- 家具位置,其中20-23位表示layer层级,10-19位表示indexX,0-9位表示indexY,(indexX,indexY)表示所在格子-->
        <transform Type="INT" Persistent="true"/>           <!-- 家具的世界坐标transform,其中33-50表示X,18-33位表示Y,2-17位表示Z,0-1位表示yaw -->
    </BUILDING_INFO>
    <HOME_BUILDING_DATA_DICT Type="dict" Key="int" Value="BUILDING_INFO" Persistent="true"/>

    <FRAME_INFO Type="tuple">
        <value Type="int" Persistent="true"/>               <!-- 框架的值,低16位为frameValue,其他位为客户端标记 -->
        <transform Type="int" Persistent="true"/>           <!-- 框架的transform,其中33-50表示X,18-33位表示Y,2-17位表示Z,0-1位表示yaw-->
    </FRAME_INFO>
    <BUILDING_FRAME_LAYER Type="tuple"> <!-- 单层建造框架信息 -->
        <Wall Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>            <!-- 墙体-->
        <Floor Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>           <!-- 地板 -->
        <Pillar Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>          <!-- 柱子 -->
        <Stair Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>           <!-- 楼梯 -->
        <Roof Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>            <!-- 屋顶 -->
        <Railing Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>         <!-- 栏杆 -->
        <RailingPillar Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>   <!-- 栏杆柱子 -->
        <Spire Type="dict" Key="int" Value="FRAME_INFO" Persistent="true"/>           <!-- 尖顶 -->
    </BUILDING_FRAME_LAYER>
    <BUILDING_FRAME Type="list" Element="BUILDING_FRAME_LAYER" Persistent="true"/>    <!-- 整个房屋的框架,每一个元素是一层-->

    <SINGLE_BUILDING_FRAME Type="tuple"><!-- 单个建造框架 -->
        <layer Type="int"/>             <!-- 所在层数 -->
        <type Type="int"/>              <!-- 框架类型,墙/地板/柱子/楼梯 -->
        <index Type="int"/>             <!-- 框架下标 -->
        <value Type="int"/>             <!-- 框架的值,低16位为frameValue,其他位为客户端标记 -->
        <transform Type="int"/>         <!-- 框架的transform,其中33-50表示X,18-33位表示Y,2-17位表示Z,0-1位表示yaw-->
    </SINGLE_BUILDING_FRAME>
    <BUILDING_FRAME_LIST Type="list" Element="SINGLE_BUILDING_FRAME" />

    <BUILDING_SUM_CODE_INFO Type="tuple">   <!-- 建筑数据校验码 -->
        <buildingCode Type="int"/>          <!-- 家具数据校验码 -->
        <staticFrameCode Type="int"/>       <!-- 静态框架组件校验码,墙体/地板/柱子/楼梯 -->
        <dynamicFrameCode Type="int"/>      <!-- 动态框架组件校验码,屋顶/栏杆/栏杆柱子 -->
    </BUILDING_SUM_CODE_INFO>

    <FRIEND_HOME_INFO Type="tuple"><!-- 单个好友家园信息 -->
        <avatarID Type="string"/>               <!-- 好友玩家ID -->
        <homelandLevel Type="int"/>             <!-- 好友家园等级 -->
        <homelandTotalScore Type="int"/>        <!-- 好友家园总评分 -->
        <homelandName Type="string"/>           <!-- 好友家园名称 -->
        <portrait Type="int"/>                  <!-- 好友头像 -->
        <portraitFrame Type="int"/>             <!-- 好友头像框 -->
        <rolename Type="string"/>               <!-- 好友名称 -->
        <shortUid Type="int"/>                  <!-- 好友短ID -->
    </FRIEND_HOME_INFO>
    <FRIEND_HOME_INFO_LIST Type="list" Element="FRIEND_HOME_INFO" />

    <FURNITURE_TRANSFORM_DICT Type="dict" Key="int" Value="int" />

    <SquareTriggerDebugDetail Type="tuple">
        <extent Type="PVector3"/>
        <location Type="PVector3"/>
        <rotation Type="PRotator"/>
    </SquareTriggerDebugDetail>
    <CircleTriggerDebugDetail Type="tuple">
        <radius Type="int"/>
        <location Type="PVector3"/>
    </CircleTriggerDebugDetail>
    <SquareTriggerDebugInfo Type="dict" Key="string" Value="SquareTriggerDebugDetail"/>
    <CircleTriggerDebugInfo Type="dict" Key="string" Value="CircleTriggerDebugDetail"/>

    <!-- TODO 后续移动到CombatEffectComponent.xml -->
    <EFFECT_G_ID Type="int" />
    <EFFECT_EVENT_ID Type="int" />
    <EFFECT_TRIGGER_LEFT_TIME Type="float" />
    <EFFECT_ID_DICT Type="dict" Key="EFFECT_G_ID" Value="bool"/>
    <EFFECT_EVENT_DICT Type="dict" Key="EFFECT_EVENT_ID" Value="EFFECT_ID_DICT" />
    <TIMER_EFFECT_GID_LIST Type="dict" Key="EFFECT_G_ID" Value="EFFECT_TRIGGER_LEFT_TIME" />
    <COVER_SET_PROP_MODE_C_INFO Type="tuple">
        <setValue Type="float"/>
        <operateId Type="int"/>
    </COVER_SET_PROP_MODE_C_INFO>

    <!-- dict<opearteId, limitCamp> -->
    <SET_LIMIT_CAMP_OP_DICT Type="dict" Key="int" Value="int" />
    <STATIC_EFFECT_BLACK_BOARD Type="tuple">
        <instigatorID Type="int"/>
        <triggerID Type="int"/>
        <rootSkillID Type="int" Default="nil"/>
        <rootBuffID Type="int" Default="nil"/>
        <level Type="int"/>
        <buffID Type="int"/>
        <layer Type="int"/>
        <linkedTargetId Type="int" Default="nil"/>
        <insID Type="int" Default="nil"/>
    </STATIC_EFFECT_BLACK_BOARD>
    <DYNAMIC_EFFECT_BLACK_BOARD Type="tuple">
        <propModifyList Type="dict" Key="string" Value="float" Default="nil"/>
        <coverSetPropModeCMList Type="dict" Key="string" Value="COVER_SET_PROP_MODE_C_INFO" Default="nil"/>
        <fStatePropModifyList Type="dict" Key="string" Value="float" Default="nil"/>
        <coverSetFStatePropMList Type="dict" Key="string" Value="int" Default="nil"/>
        <limitFightActionOperateId Type="int" Default="nil" />
        <setFightActionLimitRecord Type="dict" Key="int" Value="SET_LIMIT_CAMP_OP_DICT" Default="nil" />
        <extraHurtMultiOperateId Type="int" Default="nil" />
        <keepBuffTargetIdList Type="list" Element="int" Default="nil" />
    </DYNAMIC_EFFECT_BLACK_BOARD>
    <EFFECT Type="tuple">
        <sourceType Type="int" Default="0"/>
		<abilityID Type="int" Default="0"/>
		<instigatorID Type="int" Default="0"/>
        <effectsColName Type="string" Default=""/>
        <effectIdx Type="int" Default="nil"/>
        <triggerCount Type="int" Default="0"/>
        <lastTriggerTime Type="int" Default="0"/>
        <active Type="bool" Default="false"/>
        <cumulativeProbability Type="float" Default="nil"/>
        <dynamicBlackboardDict Type="dict" Key="int" Value="DYNAMIC_EFFECT_BLACK_BOARD" Default="nil"/>
    </EFFECT>
    <EFFECT_DICT Type="dict" Key="EFFECT_G_ID" Value="EFFECT" />

    <!--占卜家玩法相关开始-->
    <FORTUNE_RECORD Type="tuple" >
        <PlayerName Type="string" Default=""/>  <!--玩家的名字-->
        <MoneyType  Type="int" Default="0"/> <!--金钱的类型-->
        <MoneyAmount Type="int" Default="0"/>    <!--金钱的数量-->
        <FortuneOrSwitch Type="int" Default="0"/>   <!--1 代表占卜 2 代表转运-->
        <CardID Type="int" Default="0"/>    <!--卡片的ID-->
        <CardSuccess Type="bool" Default="false"/> <!--占卜代表卡片的正逆(true: 正，false: 逆)，转运代表消除的是祝福还是诅咒（true：祝福，false：诅咒）-->
    </FORTUNE_RECORD>
    <FORTUNE_RECORD_DEFINE Type="list" Element="FORTUNE_RECORD" />
    <!--占卜家玩法相关结束-->

    <LLogicUnitData Type="tuple">
        <instigatorID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <level Type="int" Flags="ALL_CLIENTS" Default="0" />
        <rootSkillID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <bFollowAttachTargetYaw Type="bool" Flags="ALL_CLIENTS" Default="true"/>
        <bCheckGround Type="bool" Flags="ALL_CLIENTS" Default="true"/>
        <LauncherID Type="int" Flags="ALL_CLIENTS" Default="0" />
        <AttachedTargetID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <TemplateID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <InstID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Position Type="PVector3" Flags="ALL_CLIENTS" />
        <Yaw Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <BornTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0" />
        <Stage Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <StageStartTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <StageEndTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <LifeExpiredTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0"/>
    </LLogicUnitData>
    <LLogicUnitDataList Type="list" Element="LLogicUnitData" />

    <LBulletExtraData Type="tuple">
        <ClientCalcStartPos Type="bool" Flags="ALL_CLIENTS" Default="false"/>
        <PosOffset Type="PVector3" Flags="ALL_CLIENTS" Default="nil"/>
        <RotOffset Type="PRotator" Flags="ALL_CLIENTS" Default="nil"/>
        <DirectionMode Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <OffsetMode Type="int" Flags="ALL_CLIENTS" Default="0"/><!--0，相对创建者；1，相对Target-->
    </LBulletExtraData>

    <LBulletDataV2 Type="tuple" >
        <instigatorID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <level Type="int" Flags="ALL_CLIENTS" Default="0" />
        <rootSkillID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <LauncherID Type="int" Flags="ALL_CLIENTS" Default="0" />
        <TemplateID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <InstID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <Position Type="PVector3" Flags="ALL_CLIENTS" />
        <Yaw Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Pitch Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <TargetPos Type="PVector3" Flags="ALL_CLIENTS" />
        <TargetID Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <BornTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0" />
        <LifeExpiredTimeStamp Type="int" Flags="ALL_CLIENTS" Default="0"/>
        <MustHitTimeLength Type="float" Flags="ALL_CLIENTS" Default="nil"/>
        <!-- 通过task创建的话，传一些额外数据(offset之类的) -->
        <ExtraData Type="LBulletExtraData" Flags="ALL_CLIENTS" Default="nil"/>
    </LBulletDataV2>
    <LBulletDataV2List Type="list" Element="LBulletDataV2" />

    <GravitationFieldInfo Type="tuple">
        <LocX Type="float"/>
        <LocY Type="float"/>
        <LocZ Type="float"/>
        <GFSpeed Type="float"/>
        <GravFieldID Type="int"/>
    </GravitationFieldInfo>
    <GravitationFieldList Type="list" Element="GravitationFieldInfo"/>

    <TRIGGER_TEMP_DICT_INFO Type="dict" Key="int" Value="DictIntInt" Persistent="true" />
    <TRIGGER_TEMP_DICT Type="dict" Key="int" Value="TRIGGER_TEMP_DICT_INFO" Persistent="true" />
    <TRIGGER_FOREVER_DICT Type="DictIntInt" Persistent="true" />
    <TRIGGER_REGISTETED_DICT Type="dict" Key="OBJECT_ID" Value="DictIntInt" Persistent="true" />

    <DATACENTER_ARGS Type="tuple">
        <gbIds Type="ENTITY_ID_LIST" Flags="SERVER_ONLY" Default="nil"/>
        <filter Type="none" Flags="SERVER_ONLY" Default="nil"/>
        <query Type="none" Flags="SERVER_ONLY" Default="nil"/>
        <ctxId Type="int" Flags="SERVER_ONLY" Default="nil"/>
        <isOnline Type="bool" Flags="SERVER_ONLY" Default="nil"/>
    </DATACENTER_ARGS>

    <WITCH_MASTER_INFO_ELEMENT Type="tuple">
        <Eid Type="string" Default="nil" Flags="ALL_CLIENTS" Persistent="false" />
        <WorldID Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="false"/>
    </WITCH_MASTER_INFO_ELEMENT>

    <ACTION_CONTEXT Type="tuple">
        <targetEntityID Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="false" />
    </ACTION_CONTEXT>

    <!--单逻辑服相关全局配置-->
    <SINGLE_LOGIC_GLOBAL_DATA Type="tuple">
        <serverLaunchTime Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true" />
        <logicSwitch Type="DictIntStr" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </SINGLE_LOGIC_GLOBAL_DATA>
    <LOGIC_GLOBAL_DATA Type="dict" Key="OBJECT_ID" Value="SINGLE_LOGIC_GLOBAL_DATA" Persistent="true" />

    <!--二级区域探索玩法相关信息-->
    <EXPLORE_PROGRESS_INFO Type="tuple">
        <!--玩法完成的数量-->
        <FinishedNum Type="int" Default="0" Flags="OWN_CLIENT" />
        <!--玩法总数-->
        <TotalNum Type="int" Default="0" Flags="OWN_CLIENT" />
        <!--已完成探索度-->
        <FinishedExploreDegree Type="int" Default="0" Flags="OWN_CLIENT" />
    </EXPLORE_PROGRESS_INFO>
    <!--二级区域相关信息-->
    <SECOND_LEVEL_AREA_INFO Type="tuple">
        <!--探索玩法相关信息-->
        <GameplayProgress Type="dict" Key="int" Value="EXPLORE_PROGRESS_INFO" Flags="OWN_CLIENT" />
        <!--是否上锁-->
        <bLocked Type="bool" Default="true" Flags="OWN_CLIENT" Persistent="true" />
    </SECOND_LEVEL_AREA_INFO>
    <!--一级区域相关信息-->
    <FIRST_LEVEL_AREA_INFO Type="tuple">
        <!--一级区域ID-->
        <FirstLevelAreaID Type="int" Flags="OWN_CLIENT" Persistent="true" />
        <!--二级区域相关信息-->
        <SecondLevelAreaMap Type="dict" Key="int" Value="SECOND_LEVEL_AREA_INFO" Flags="OWN_CLIENT" Persistent="true" />
        <!--探索度奖励-->
        <RewardProgress Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true" />
        <!--探索度-->
        <ExploreDegree Type="int" Flags="OWN_CLIENT" />
    </FIRST_LEVEL_AREA_INFO>
    <!--同种类奉献之灵信息-->
    <EXPLORE_SOUL_INFO Type="tuple">
        <!--数量-->
        <Count Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <!--阶段-->
        <Stage Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <!--指引下一个奉献之灵的InsteanceID-->
        <NextSoulInstanceID Type="string" Default="" Flags="OWN_CLIENT" Persistent="true" />
        <!--当前DeBuffID-->
        <DeBuffID Type="int" Default="0" Flags="SERVER_ONLY" />
        <!--是否拥有指引奉献之灵的能力-->
        <bGuildSoul Type="bool" Default="false" Flags="SERVER_ONLY" Persistent="true" />
    </EXPLORE_SOUL_INFO>
    <!--奉献碑信息-->
    <EXPLORE_STELE_INFO Type="tuple">
        <!--任务是否已完成-->
        <bTaskFinished Type="bool" Default="false" Flags="OWN_CLIENT" Persistent="true" />
    </EXPLORE_STELE_INFO>
    <!-- 地图探索度解锁信息 -->
    <EXPLORE_UNLOCK_INFO Type="tuple">
        <!--不同类型玩法完成的数量映射-->
        <ExploreFinishedMap Type="DictIntInt" Flags="OWN_CLIENT" Persistent="true" />
        <!--当前地图已完成探索度-->
        <FinishedExploreDegree Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <!--探索度奖励-->
        <RewardProgress Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true" />
    </EXPLORE_UNLOCK_INFO>

    <SPECIAL_INTERACTIVE_KEY_INFO Type="dict" Key="int" Value="bool" Flags="ALL_CLIENTS"/>

    <APPEARANCE_ID Type="int"/>
    <APPEARANCE_SUBTYPE Type="int"/>
    <FASHION_PART_SLOT Type="int"/>

    <STAIN_UNION Type="tuple">
        <StainData Type="dict" Key="FASHION_PART_SLOT" Value="PVector4" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/> <!--普通染色-->
        <AdvancedStainData Type="dict" Key="FASHION_PART_SLOT" Value="int" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/> <!--高级染色-->
    </STAIN_UNION>

    <PLAYER_APPEARANCE_FINETUNE_INFO Type="tuple">
        <Pos Type="PVector3" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/> 
        <Rot Type="PRotator" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/> 
        <Scale Type="float" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/> 
        <SlotPos Type="int" Default="nil" Flags="ALL_CLIENTS" Persistent="true"/>
    </PLAYER_APPEARANCE_FINETUNE_INFO>

    <PLAYER_FASHION_INFO Type="STAIN_UNION" Flags="ALL_CLIENTS" Persistent="true"/> 

    <PLAYER_FASHION_DEFINE Type="dict" Key="APPEARANCE_ID" Value="PLAYER_FASHION_INFO" Flags="ALL_CLIENTS" Persistent="true"/> 

    <PLAYER_ACCESSORY_INFO Type="PLAYER_APPEARANCE_FINETUNE_INFO" Flags="ALL_CLIENTS" Persistent="true"/>

    <PLAYER_ACCESSORY_DEFINE Type="dict" Key="APPEARANCE_ID" Value="PLAYER_ACCESSORY_INFO" Flags="ALL_CLIENTS" Persistent="true"/> 

    <PLAYER_AROUND_INFO Type="bool" Flags="ALL_CLIENTS" Persistent="true"/>

    <PLAYER_AROUND_DEFINE Type="dict" Key="APPEARANCE_ID" Value="PLAYER_AROUND_INFO" Flags="ALL_CLIENTS" Persistent="true"/> 

    <PLAYER_APPEARANCE Type="tuple">
        <curWearFashion Type="PLAYER_FASHION_DEFINE" Default="{}" Flags="ALL_CLIENTS" Persistent="true"/>
		<curWearAccessory Type="PLAYER_ACCESSORY_DEFINE" Default="{}" Flags="ALL_CLIENTS" Persistent="true"/>
		<curWearAround Type="PLAYER_AROUND_DEFINE" Default="{}" Flags="ALL_CLIENTS" Persistent="true"/> 
    </PLAYER_APPEARANCE>

    <STORY_FASHION Type="tuple">
        <SizeChangeData Type="string" Default="nil" Flags="ALL_CLIENTS" Persistent="false"/><!--size微调数据-->
        <StainData Type="string" Default="nil" Flags="ALL_CLIENTS" Persistent="false"/> <!--染色信息-->
    </STORY_FASHION>
    <STORY_FASHION_DICT Type="dict" Key="int" Value="STORY_FASHION" Flags="ALL_CLIENTS" Persistent="false"/>

    <PVector3_Persistent Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Z Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
    </PVector3_Persistent>
    <PVector4_Persistent Type="tuple">
        <X Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Y Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Z Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <W Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
    </PVector4_Persistent>
    <PRotator_Persistent Type="tuple">
        <Pitch Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Yaw Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
        <Roll Type="float" Flags="ALL_CLIENTS" Default="0.0" Persistent="true"/>
    </PRotator_Persistent>

    <FASHION_STAIN_INFO Type="dict" Key="FASHION_PART_SLOT" Value="PVector4_Persistent" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    <FASHION_ADVANCED_STAIN_INFO Type="dict" Key="FASHION_PART_SLOT" Value="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    
    <PLAYER_WARDROBE_STAIN_INFO Type="tuple">
        <StainData Type="FASHION_STAIN_INFO" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> <!--普通染色-->
        <AdvancedStainData Type="FASHION_ADVANCED_STAIN_INFO" Value="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> <!--高级染色-->
        <StainPictureID Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </PLAYER_WARDROBE_STAIN_INFO>
    <PLAYER_WARDROBE_STAIN_DICT Type="dict" Key="int" Value="PLAYER_WARDROBE_STAIN_INFO" Flags="SERVER_ONLY" Persistent="true"/>

    <PLAYER_WARDROBE_CELL Type="tuple">
        <Status Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> <!-- 1：未获得， 2：新获得， 3：已获得-->
        <IsCollected Type="bool" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
        <Pos Type="PVector3_Persistent" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> 
        <Rot Type="PRotator_Persistent" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> 
        <Scale Type="float" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> 
        <SlotPos Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
        <StainSlot Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> <!--选择的槽位 nil 代表不选择-->
        <StainSlots2StainData Type="PLAYER_WARDROBE_STAIN_DICT" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </PLAYER_WARDROBE_CELL>

    <PLAYER_WARDROBE_CELL_DICT Type="dict" Key="APPEARANCE_ID" Value="PLAYER_WARDROBE_CELL"/>

    <PLAYER_WARDROBE Type="dict" Key="APPEARANCE_ID" Value="PLAYER_WARDROBE_CELL" Flags="SERVER_ONLY" Persistent="true"/>
    
    <PLAYER_WARDROBE_DEFINE Type="dict" Key="APPEARANCE_SUBTYPE" Value="PLAYER_WARDROBE" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>

    <APPEARANCE_ID_LIST Type="list" Element="APPEARANCE_ID"/>

    <STAIN_SLOT Type="int"/>
    <FASHION_STRATEGY_DEFINE Type="dict" Key="APPEARANCE_ID" Value="STAIN_SLOT" Flags="SERVER_ONLY" Persistent="true"/>
    
    <ACCESSORY_STRATEGY_DEFINE Type="PLAYER_ACCESSORY_DEFINE"/>
    
    <AROUND_STRATEGY_INFO Type="PLAYER_AROUND_DEFINE"/>

    <PLAYER_APPEARANCE_STRATEGY Type="tuple">
        <Name Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
        <FashionStrategy Type="FASHION_STRATEGY_DEFINE" Flags="SERVER_ONLY" Persistent="true"/>
        <AccessoryStrategy Type="ACCESSORY_STRATEGY_DEFINE" Flags="SERVER_ONLY" Persistent="true"/>
        <PictureID Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </PLAYER_APPEARANCE_STRATEGY>

    <APPEARANCE_STRATEGY_SLOT Type="int"/>
    <PLAYER_APPEARANCE_STRATEGY_DICT Type="dict" Key="APPEARANCE_STRATEGY_SLOT" Value="PLAYER_APPEARANCE_STRATEGY" Flags="SERVER_ONLY" Persistent="true"/> <!--key 是方案的槽位-->
    <STAIN_TO_STRATEGY Type="dict" Key="STAIN_SLOT" Value="ListInt" Flags="SERVER_ONLY" Persistent="true"/>
    <APPEARANCE_STAIN_TO_STRATEGY Type="dict" Key="APPEARANCE_ID" Value="STAIN_TO_STRATEGY" Flags="SERVER_ONLY" Persistent="true"/>

    <SCENE_CUSTOM_ITEM_WARDROBE Type="tuple"> 
        <sceneWardrobe Type="ListInt" Flags="SERVER_ONLY" Persistent="true"/>
        <compWardrobe Type="ListInt" Flags="SERVER_ONLY" Persistent="true"/>
        <actionWardrobe Type="ListInt" Flags="SERVER_ONLY" Persistent="true"/>
    </SCENE_CUSTOM_ITEM_WARDROBE>

    <SCENE_CUSTOM_STRATEGY_BRIEF_INFO Type="tuple">
        <Name Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
        <PictureID Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </SCENE_CUSTOM_STRATEGY_BRIEF_INFO>

    <SCENE_CUSTOM_STRATEGY_BRIEF_INFO_DICT Type="dict" Key="int" Value="SCENE_CUSTOM_STRATEGY_BRIEF_INFO" Flags="SERVER_ONLY" Persistent="true"/>
    
    <SCENE_CUSTOM_ROLE_INFO Type="tuple"> 
        <EntityID Type="ENTITY_ID" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <NpcID Type="NPC_ID" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <Pos Type="PVector3_Persistent" Default="nil" Flags="OWN_CLIENT" Persistent="true"/> 
        <Rot Type="float" Default="nil" Flags="OWN_CLIENT" Persistent="true"/> 
        <Anim Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
    </SCENE_CUSTOM_ROLE_INFO>

    <PLAYER_APPEARANCE_DICT Type="dict" Key="ENTITY_ID" Value="PLAYER_APPEARANCE"/>

    <SCENE_CUSTOM_COMP_INFO Type="tuple"> 
        <Pos Type="PVector3_Persistent" Default="nil" Flags="OWN_CLIENT" Persistent="true"/> 
        <Rot Type="float" Default="nil" Flags="OWN_CLIENT" Persistent="true"/> 
    </SCENE_CUSTOM_COMP_INFO>

    <SCENE_CUSTOM_STRATEGY Type="tuple" Inherit="SCENE_CUSTOM_STRATEGY_BRIEF_INFO">
        <Scene Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <RoleInfoList Type="list" Element="SCENE_CUSTOM_ROLE_INFO" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <CompInfoDict Type="dict" Key="int" Value="SCENE_CUSTOM_COMP_INFO" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <CameraData Type="string" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
    </SCENE_CUSTOM_STRATEGY>

    <SCENE_CUSTOM_STRATEGY_DICT Type="dict" Key="int" Value="SCENE_CUSTOM_STRATEGY" Flags="OWN_CLIENT" Persistent="true"/>

    <MOUNT_COMP_ID Type="int"/>
    <MOUNT_COMP_TYPE Type="int"/> 

    <MOUNT_COMP_ID_LIST Type="list" Element="MOUNT_COMP_ID"/>

    <MOUNT_WARDROBE_CELL Type="tuple"> 
        <Status Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/> <!-- 1：新获得，2：已获得-->
        <IsCollected Type="bool" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </MOUNT_WARDROBE_CELL>

    <MOUNT_WARDROBE_CELL_DICT Type="dict" Key="MOUNT_COMP_ID" Value="MOUNT_WARDROBE_CELL" Flags="SERVER_ONLY" Persistent="true"/>

    <MOUNT_WARDROBE_DEFINE Type="dict" Key="MOUNT_COMP_TYPE" Value="MOUNT_WARDROBE_CELL_DICT" Flags="SERVER_ONLY" Persistent="true"/>
    
    <ANIMATION_INFO Type="tuple">
        <FeatureName Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <AnimStage Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <LowerFeatureName Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <LowerAnimStage Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <EBlendType Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <StartTime Type="LONGTIME" Flags="ALL_CLIENTS" Default="nil" />
        <Duration Type="LONGTIME" Flags="SERVER_ONLY" Default="nil" Persistent="true"/>
        <IsInAnimQueue Type="bool" Flags="SERVER_ONLY" Default="nil" Persistent="true"/>
    </ANIMATION_INFO>

    <HALF_ANIMATION_INFO Type="tuple">
        <FeatureName Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <AnimStage Type="string" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>
        <StartTime Type="LONGTIME" Flags="ALL_CLIENTS" Default="nil" />
        <EBlendType Type="int" Flags="ALL_CLIENTS" Default="nil" Persistent="true"/>        
        <bActionMeshSpaceBlend Type="bool" Flags="ALL_CLIENTS" Default="false" Persistent="true"/>
    </HALF_ANIMATION_INFO>

    <ANIMATION_QUEUE_INFO Type="tuple">
        <bInAnimQueue Type="bool" Flags="ALL_CLIENTS" Default="nil"/>
        <StartTime Type="LONGTIME" Flags="ALL_CLIENTS" Default="nil" />
        <Index Type="int" Flags="ALL_CLIENTS" Default="nil" />
    </ANIMATION_QUEUE_INFO>

    <!-- 任务存档 start -->
    <QUEST_PLANE_ACTOR_INSTANCE Type="tuple">
        <!-- 后续拓展增加属性 -->
        <!-- 交互物相关 -->
        <SeatInfo Type="SCENE_ACTOR_SEAT_INFO" Default="nil" Persistent="true" />
        <SceneActorState Type="int" Default="nil" Persistent="true" />
        <bEnableInteract Type="bool" Default="nil" Persistent="true" />
        <ContinuousInteractCount Type="int" Default="nil" Persistent="true" />
        <!-- 一些固定位置的actor不需要存位置信息 -->
        <Pos Type="ListFloat" Default="nil" Persistent="true"/>
        <Yaw Type="float" Default="nil" Persistent="true"/>
        <Animation Type="ANIMATION_INFO" Default="nil" Persistent="true"/>
        <ContinuousInteractInsID Type="string" Default="nil" Persistent="true" />
        <ContinuousInteractSeatIndex Type="int" Default="nil" Persistent="true" />
        <ContinuousInteractPlaceHolder Type="string" Default="nil" Persistent="true" />
        <SubState Type="int" Default="nil" Persistent="true" />
    </QUEST_PLANE_ACTOR_INSTANCE>
    <!-- InstanceID -> Actor存档数据 -->
    <QUEST_PLANE_ACTOR_INSTANCE_MAP Type="dict" Key="string" Value="QUEST_PLANE_ACTOR_INSTANCE" />

    <!-- 任务环是线性的,每个Ring只会有一份存档数据,是存档时位面的一个快照 -->
    <QUEST_PLANE_ARCHIVE Type="tuple">
        <QuestID Type="int" Default="0" Persistent="true" />
        <PlaneID Type="int" Default="0" Persistent="true" />
        <!-- 玩家信息 -->
        <AvatarPos Type="ListFloat" Persistent="true" />
        <AvatarYaw Type="float" Persistent="true" />
        <MorphID Type="int" Persistent="true" />
        <!-- 位面基础信息 -->
        <PPVIntensity Type="float" Default="nil" Persistent="true" />
        <PPVExposure Type="float" Default="nil" Persistent="true" />
        <!-- 位面音频信息 -->
        <AkEvents Type="ListStr" Default="nil" Persistent="true" />
        <AkStateGroup Type="DictStrStr" Default="nil" Persistent="true" />
        <Rtpc Type="DictStrFloat" Default="nil" Persistent="true" />
        <!-- Actor信息(必须是场编中配置的 进行约束) -->
        <PlaneActors Type="QUEST_PLANE_ACTOR_INSTANCE_MAP" Persistent="true" />
        <ContinuousInteractInsID Type="string" Default="nil" Persistent="true" />
        <ContinuousInteractSeatIndex Type="int" Default="nil" Persistent="true" />
        <ContinuousInteractPlaceHolder Type="string" Default="nil" Persistent="true" />
        <ClimateID Type="int" Default="nil" Persistent="true" />
        <SceneTimeInfo Type="TASK_SET_SCENE_TIME_ITEM" Default="nil" Persistent="true" />
    </QUEST_PLANE_ARCHIVE>

    <!-- RingID -> 整体存档数据 -->
    <QUEST_TOTAL_PLANE_ARCHIVE Type="dict" Key="int" Value="QUEST_PLANE_ARCHIVE" />
    <!-- 任务存档 end -->

    
    <FortuitousInfo Type="tuple">
        <FortuitousTriggerTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <FortuitousStatus Type="int" Flags="SERVER_ONLY" Default="0"/>
        <FortuitousLastFinishedTime Type="int" Flags="SERVER_ONLY" Default="0"/>
        <FortuitousFinishedTimes Type="int" Flags="SERVER_ONLY" Default="0"/>
        <FortuitousFailedTime Type="int" Flags="SERVER_ONLY" Default="nil"/>
    </FortuitousInfo>
    <ReconnectInfo Type="tuple">
        <RpcName Type="string" Flags="SERVER_ONLY" Default="nil"/>
        <RpcArgs Type="string" Flags="SERVER_ONLY" Default="nil"/>
    </ReconnectInfo>

    <AVATAR_SHAPE_INFO Type="tuple">
        <Name Type="string"/>               <!-- 姓名 -->
        <shortUid Type="int"/>              <!-- 数字编号 -->
        <Profession Type="int"/>            <!-- 职业 -->
        <Sex Type="int"/>                   <!-- 性别 -->
        <Level Type="int"/>                 <!-- 等级 -->
        <LogicServerID Type="int"/>         <!-- 服务器编号 -->
        <CustomFaceData Type="string"/>     <!-- 捏脸 -->
        <playerAppearance Type="PLAYER_APPEARANCE"/> <!-- 时装 -->
    </AVATAR_SHAPE_INFO>
    <AVATAR_SHAPE_DICT Type="dict" Key="string" Value="AVATAR_SHAPE_INFO"/>

    <CreatingRoleInfo Type="tuple">
        <profession Type="int" Flags="SERVER_ONLY" Default="0"/>
        <sex Type="int" Flags="SERVER_ONLY" Default="0"/>
        <customFaceData Type="string" Flags="SERVER_ONLY"/>
    </CreatingRoleInfo>

    <RoleInfo Type="tuple">
        <shapeInfo Type="AVATAR_SHAPE_INFO"/>
        <eid Type="string" Flags="SERVER_ONLY" Default=""/>
        <LastLoginTime Type="int" Flags="SERVER_ONLY" Default="0"/>
    </RoleInfo>
    <RoleList Type="list" Element="RoleInfo"/>

    <VEHCILE_GAMEPLAY_PARAMS Type="tuple">
    </VEHCILE_GAMEPLAY_PARAMS>

    <SOCIAL_ACTION_INFO Type="tuple">
        <Type Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false"/>
        <ActionID Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false"/>
        <CastTime Type="int" Default="0" Flags="ALL_CLIENTS" Persistent="false"/>
        <Caster Type="string" Default="" Flags="ALL_CLIENTS" Persistent="false"/>
        <Target Type="string" Default="" Flags="ALL_CLIENTS" Persistent="false"/>
    </SOCIAL_ACTION_INFO>

    <SOCIAL_ACTION_INVITE_INFO Type="tuple">
        <Caster Type="string" Default="" Flags="OWN_CLIENT" Persistent="false"/>
        <ActionID Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false"/>
        <Deadline Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false"/>
    </SOCIAL_ACTION_INVITE_INFO>

    <NPC_WARNING_VALID Type="DictStrInt" Default="{}" Flags="OWN_CLIENT" Persistent="false"/>
    <!-- <FAHION_BUY_RESULT Type="tuple">
        <BuyFashionID Type="int" Default="0" Persistent="false"/>
        <DetailResult Type="int" Default="0"  Persistent="false"/>
        <SerialNo Type="string" Default="" Persistent="false"/>
    </FAHION_BUY_RESULT>
    <FAHION_BUY_RESULT_DEFINE Type="dict" Key="int" Value="FAHION_BUY_RESULT" Persistent="false"/>

    <FASHION_AROUND_BUY_RESULT Type="tuple">
        <BuyAroundID Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false"/>
        <DetailResult Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false"/>
        <SerialNo Type="string" Default="" Flags="OWN_CLIENT" Persistent="false"/>
    </FASHION_AROUND_BUY_RESULT>
    <FAHION_AROUND_BUY_RESULT_DEFINE Type="dict" Key="int" Value="FASHION_AROUND_BUY_RESULT" Flags="OWN_CLIENT" Persistent="false"/> -->


    <FASHION_MOUNT_BY_RESULT Type="tuple">
        <DetailResult Type="int" Default="0" Persistent="false"/>
        <SerialNo Type="string" Default="" Persistent="false"/>
        <IsMount Type="bool" Default="false" Persistent="false"/>
    </FASHION_MOUNT_BY_RESULT>
    <FASHION_MOUNT_BY_RESULT_DEFINE Type="dict" Key="int" Value="FASHION_MOUNT_BY_RESULT" Persistent="false"/>

    <MULTI_BUY_GOODS_RESULT Type="tuple">
        <GoodsID Type="int" Default="0"  Persistent="false"/>
        <SerialNo Type="string" Default="" Persistent="false"/>
        <Result Type="int" Default="0" Persistent="false"/>
    </MULTI_BUY_GOODS_RESULT>
    <MULTI_BUY_GOODS_RESULT_DEFINE Type="list" Element="MULTI_BUY_GOODS_RESULT" Persistent="false"/>

    <MULTI_BUY_GOODS Type="tuple">
        <GoodsID Type="int" Default="0" Persistent="false"/>
        <SerialNo Type="string" Default="" Persistent="false"/>
        <ShopID Type="int" Default="0" Persistent="false"/>
        <Count Type="int" Default="0" Persistent="false"/>
        <DiscountPrice Type="int" Default="0" Persistent="false"/>
    </MULTI_BUY_GOODS>
    <MULTI_BUY_GOODS_DEFINE Type="list" Element="MULTI_BUY_GOODS" Persistent="false"/>

    <ROLEPLAY_CHECK_PROPERTY Type="tuple">
        <MinValue Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true"/>
        <MaxValue Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true"/>
    </ROLEPLAY_CHECK_PROPERTY>
    <ROLEPLAY_CHECK_PROPERTY_DEFINE Type="dict" Key="int" Value="ROLEPLAY_CHECK_PROPERTY" Flags="OWN_CLIENT" Persistent="true"/>
    <CUSTOM_TRACING_ITEM Type="tuple">
        <ID Type="string" Default="" Flags="OWN_CLIENT" Persistent="true" />
        <TagID Type="UINT" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <MapID Type="UINT" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <Text Type="string" Default="" Flags="OWN_CLIENT" Persistent="true" />
        <X Type="float" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <Y Type="float" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <bHide Type="bool" Default="false" Flags="OWN_CLIENT" Persistent="true" />
    </CUSTOM_TRACING_ITEM>
    <CUSTOM_TRACING_ITEM_DEFINE Type="dict" Key="string" Value="CUSTOM_TRACING_ITEM" Flags="OWN_CLIENT" Persistent="true" />
    <TRACING_MAP_TAG_HIDE_INFO Type="dict" Key="int" Value="bool" Persistent="true" />
    <TRACING_MAP_TAG_CLASS_HIDE_INFO_ITEM Type="dict" Key="int" Value="bool" Persistent="true" />
    <TRACING_MAP_TAG_CLASS_HIDE_INFO Type="dict" Key="int" Value="TRACING_MAP_TAG_CLASS_HIDE_INFO_ITEM" Persistent="true" />
    <TRACING_INFO Type="tuple">
        <TracingType Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true" />
        <TracingValue Type="string" Default="" Flags="OWN_CLIENT" Persistent="true" />
        <TracingTime Type="int" Default="0" Flags="OWN_CLIENT" Persistent="true" />
    </TRACING_INFO>
    <TRACING_INFO_LIST Type="list" Element="TRACING_INFO"/>
    <TRACING_INFO_DEFINE Type="dict" Key="int" Value="TRACING_INFO_LIST"/>

    <SP_INTERACT_MAP Type="dict" Key="int" Value="bool" Flags="ALL_CLIENTS" />
    <CUT_PRICE_INFO Type="tuple">
        <InitPatienceValue Type="int" Default="nil" Flags="ALL_CLIENTS" />
        <InitPrice Type="int" Default="nil" Flags="ALL_CLIENTS" />
        <LowestPrice Type="int" Default="nil" Flags="ALL_CLIENTS" />
    </CUT_PRICE_INFO>
    <TELEPORT_POINT_LOCK_INFO Type="dict" Key="string" Value="bool" Flags="OWN_CLIENT" />
    <COLLECTION_ACTIVE_MAP Type="dict" Key="string" Value="bool" Flags="OWN_CLIENT" />

    <AvatarActorTeamBlockVoices Type="dict" Key="string" Value="bool" Flags="OWN_CLIENT" />

    <AvatarActorTeamProps Type="tuple">
        <bIsInSeekRescue Type="bool" Default="nil" Flags="SERVER_ONLY"/>
        <isAutoAcceptJoin Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <isInTeamMatch Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <matchStartTime Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <targetID Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <positionNeedList Type="AvatarActorTeamPositionNeedList" Default="nil" Flags="SERVER_ONLY"/>
        <zhanliNeed Type="int" Default="nil" Flags="SERVER_ONLY"/>
        <captainID Type="string" Default="nil" Flags="SERVER_ONLY"/>
        <WorldReturnInfo Type="WorldSimpleInfo" Default="nil" Flags="SERVER_ONLY"/>
    </AvatarActorTeamProps>
    <STATE_CONFLICT_DEFINE Type="dict" Key="int" Value="bool" Flags="OWN_CLIENT" />

    <LWantedAvatarDict ImplClass="WantedAvatarDictImpl" Type="ENTITY_ID_BOOL" Flags="ALL_CLIENTS" />

    <COLLECTIBLESINFODICT Type="DictIntInt" Flags="SERVER_ONLY" Persistent="true"/>

    <CollectiblesExternInfo Type="tuple">   <!-- 后续可能会扩展别的玩法属性,先用tuple保存 -->
        <desc Type="string" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>           <!-- 描述,仅图片藏品有效-->
    </CollectiblesExternInfo>
    <COLLECTIBLESEXTERNINFODICT Type="dict" Key="int" Value="CollectiblesExternInfo" Flags="SERVER_ONLY" Persistent="true"/>

    <COLLECTIBLESREWARD Type="tuple">
        <score Type="int" Default="0" Flags="SERVER_ONLY" Persistent="false"/>  <!-- 总积分 -->
        <rewarded Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>    <!-- 已领取奖励的等级列表,每一位表示一个等级,0-63级 -->
    </COLLECTIBLESREWARD>
    <COLLECTIBLESREWARDDICT Type="dict" Key="int" Value="COLLECTIBLESREWARD" Flags="SERVER_ONLY" Persistent="true"/>

    <CollectiblesScoreDict Type="DictIntInt" Flags="SERVER_ONLY"/>

    <LOGIN_QUEUE_INFO Type="tuple">
        <eid Type="ENTITY_ID" />
        <rank Type="UINT" />
    </LOGIN_QUEUE_INFO>

    <LOGIN_QUEUE_STATE Type="tuple">
        <value Type="int" Flags="OWN_CLIENT" />
        <name Type="string" Flags="OWN_CLIENT" />
    </LOGIN_QUEUE_STATE>
    <LOGIN_QUEUE_INFOS Type="list" Element="LOGIN_QUEUE_INFO" />

    <RESTORE_QUEUE_INFO Type="tuple">
        <id Type="string" />
        <mb Type="mailbox" />
        <rank Type="int" />
    </RESTORE_QUEUE_INFO>
    <RESTORE_QUEUE_INFOS Type="list" Element="RESTORE_QUEUE_INFO" />
    <PRIORITY_RESTORE_QUEUE_INFOS Type="dict" Key="int" Value="RESTORE_QUEUE_INFOS" />

    <FRIEND_STATUS_RANGE Type="tuple">
        <friendsRange Type="DictStrBool"  Flags="OWN_CLIENT" Persistent="true"/> <!--key: entity ID value: 无用-->
        <groupsRange Type="DictIntBool" Flags="OWN_CLIENT" Persistent="true"/> <!--key: groupID value: 无用-->
    </FRIEND_STATUS_RANGE>

    <FRIEND_STATUS_RANGE_FOR_DATACENTER Type="tuple">
        <friendsRange Type="DictStrBool"  Flags="SERVER_ONLY" Persistent="true"/> <!--key: entity ID value: true代表-->
    </FRIEND_STATUS_RANGE_FOR_DATACENTER>

    <ENTITY_INT_ID_LIST Type="list" Element="int" />

    <TEA_ROOM_MEMBER_BASIC_INFO Type="tuple">
        <AvatarID Type="string" Persistent="true" Default=""/>
        <IconID Type="int" Persistent="true" Default="0" />
        <ProfessionID Type="int" Persistent="true" Default="0" />
        <Level Type="int" Persistent="true" Default="0" />
        <Nickname Type="string" Persistent="true" Default="" />
        <Sex Type="int" Persistent="true" Default="0" />
        <ShortUid Type="int" Persistent="true"/> <!-- 玩家的shortUid -->
        <LogicServerID Type="int" Persistent="true"/> <!-- 玩家的逻辑服ID -->
    </TEA_ROOM_MEMBER_BASIC_INFO>

    <TEA_ROOM_INFO Type="tuple">
        <RoomID Type="int" Default="0"/> <!-- 房间ID -->
        <RoomName Type="string" Default="nil"/> <!-- 房间名字 -->
        <ServerID Type="int" Default="0"/> <!-- 服务器的名字 -->
        <RoomTag Type="int" Default="0"/> <!-- 房间标签 -->
        <RoomType Type="int" Default="0"/> <!-- 房间类型 -->
        <MemberCount Type="int" Default="0"/> <!-- 房间人数 -->
        <HotValue Type="int" Default="0"/> <!-- 房间热度 -->
    </TEA_ROOM_INFO>

    <TEA_ROOM_INFO_LIST Type="list" Element="TEA_ROOM_INFO" />
    <MemberShowInfo Type="tuple" Inherit="ChatHead">
        <Sex Type="int" Default="0" />
    </MemberShowInfo>
    <TEAROOM_BLACK_LIST Type="list" Element="MemberShowInfo" />
    <APPLICATION_INFO Type="tuple">
        <entityID Type="string"/> <!-- 玩家ID -->
        <message Type="string"/> <!-- 申请留言 -->
    </APPLICATION_INFO>
    <APPLICATION_INFO_LIST Type="list" Element="APPLICATION_INFO"/>

    <TEA_ROOM_MEMBER_INFO Type="tuple" Inherit="TEA_ROOM_MEMBER_BASIC_INFO">
        <speechState Type="int" /> <!-- 开麦状态 -->
        <speechRight Type="bool" /> <!-- 开麦权限 -->
        <speechOrder Type="int" /> <!-- 开麦顺序 -->
        <version Type="int"/> <!-- 进入房间时的顺序 -->
        <isForbidSpeak Type="bool"/> <!-- 玩家是否被禁言 -->
    </TEA_ROOM_MEMBER_INFO>

    <PARTY_ROOM_MEMBER_INFO Type="tuple" Inherit="TEA_ROOM_MEMBER_INFO">
        <SystemIdentityID Type="int" Default="0"/> <!-- 系统身份标签 -->
        <SystemIdentityState Type="int" Default="0"/> <!-- 系统身份状态 -->
        <AllocatedIdentityID Type="int" Default="0"/> <!-- 分配身份标签 -->
        <AllocatedIdentityState Type="int" Default="0"/> <!-- 分配身份状态 -->
    </PARTY_ROOM_MEMBER_INFO>

    <!-- 建房同步的信息 -->
    <CREATE_CHAT_ROOM_INFO Type="tuple" Inherit="TEA_ROOM_MEMBER_BASIC_INFO">
        <RoomName Type="string" /> <!-- 房间名字 -->
        <RoomType Type="int" /> <!-- 房间类型 -->
        <RoomTag Type="int"/> <!-- 房间标签 -->
        <IsShareToWorldChannel Type="bool" /> <!-- 是否分享给世界频道 -->
        <IsShareToMements Type="bool"/> <!-- 是否分享到朋友圈 -->
    </CREATE_CHAT_ROOM_INFO>
    <!-- 送礼信息 -->
    <TEAROOM_GIVE_GIFT_INFO Type="tuple">
        <RoomID Type="int" /> <!-- 房间ID -->
        <RoomTag Type="int" /> <!-- 房间标签 -->
        <HotValue Type="int"/> <!-- 房间热度 -->
        <SendRoleName Type="string" /> <!-- 送礼玩家的名字 -->
        <RecvRoleName Type="string" /> <!-- 收礼玩家的名字 -->
        <GiftCnt Type="int"/> <!-- 礼物数量 -->
        <GiftID Type="int"/> <!-- 礼物ID -->
        <SendTime Type="int"/> <!-- 送礼时间 -->
    </TEAROOM_GIVE_GIFT_INFO>
    <!-- 首次同步的基础成员信息 -->
    <TEAROOM_FIRST_SYNC_MEMBER_INFO Type="tuple">
        <version Type="int" /> <!-- 进房顺序 -->
        <speechState Type="int"/> <!-- 发言状态 -->
    </TEAROOM_FIRST_SYNC_MEMBER_INFO>

    <TEA_ROOM_DETAILED_INFO Type="tuple">
        <RoomID Type="int"/> <!-- 房间ID -->
        <RoomName Type="string"/> <!-- 房间名字 -->
        <RoomTag Type="int"/> <!-- 房间Tag -->
        <ServerID Type="int"/> <!-- 服务器的名字 -->
        <RoomType Type="int"/> <!-- 房间类型 -->
        <MemberCount Type="int"/> <!-- 房间人数 -->
        <HotValue Type="int" Default="0"/> <!-- 房间热度 -->
        <Master Type="string"/><!-- 房主ID -->
        <SpeechPrivilegeSetting Type="int"/><!-- 房间的发言权限 -->
        <TopMessage Type="string" Default=""/> <!-- 置顶消息内容 -->
        <TopMessageID Type="string" Default=""/> <!-- 置顶消息ID -->
        <TopMessageAuthor Type="string" Default=""/> <!-- 置顶消息作者 -->
        <MemberInfoList Type="list" Element="TEA_ROOM_MEMBER_INFO"/><!-- 第一批同步给客户端的玩家 先给有麦权的成员 再给排序靠前的成员  -->
        <MemberInfos Type="dict" Key="int" Value="TEAROOM_FIRST_SYNC_MEMBER_INFO"/> <!-- { ShortUid -> {Version, SpeechState} } -->
    </TEA_ROOM_DETAILED_INFO>
    <TEA_ROOM_MEMBER_INFO_LIST Type="list" Element="TEA_ROOM_MEMBER_INFO"/>
    <PARTY_MEMBER_INFO_LIST Type="list" Element="PARTY_ROOM_MEMBER_INFO"/>
    <OrderInfo Type="tuple">
        <orderType Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
        <orderIntArg Type="int" Default="nil" Flags="SERVER_ONLY" Persistent="true"/>
    </OrderInfo>
    <TEA_ROOM_ALL_INFO Type="tuple">
        <RoomID Type="int"/> <!-- 房间ID -->
        <RoomName Type="string"/> <!-- 房间名字 -->
        <ServerID Type="int"/> <!-- 服务器的名字 -->
        <RoomTag Type="int"/> <!-- 房间标签 -->
        <RoomType Type="int"/> <!-- 房间类型 -->
        <MemberCount Type="int"/> <!-- 房间人数 -->
        <HotValue Type="int" Default="0"/> <!-- 房间热度 -->
        <Master Type="string"/><!-- 房主ID -->
        <SpeechPrivilegeSetting Type="int"/><!-- 房间的发言权限 -->
        <TopMessage Type="string" Default=""/> <!-- 置顶消息内容 -->
        <TopMessageID Type="string" Default=""/> <!-- 置顶消息ID -->
        <TopMessageAuthor Type="string" Default=""/> <!-- 置顶消息作者 -->
        <MemberInfoList Type="dict" Key="int" Value="TEA_ROOM_MEMBER_INFO"/><!-- 所有成员信息  -->
        <SpeechOrderList Type="list" Element="string"/>
        <GagList Type="dict" Key="string" Value="int"/>
        <OpenMicApplicationList Type="dict" Key="string" Value="int"/>
        <BlackList Type="dict" Key="string" Value="int"/>
        <AvatarID2ShortUid Type="dict" Key="string" Value="int"/>
    </TEA_ROOM_ALL_INFO>

    <!-- 派对房间的信息 -->
    <PARTY_DETAILED_INFO Type="tuple">
        <RoomID Type="int"/> <!-- 房间ID -->
        <RoomName Type="string"/> <!-- 房间名字 -->
        <RoomTag Type="int"/> <!-- 房间Tag -->
        <ServerID Type="int"/> <!-- 服务器的名字 -->
        <RoomType Type="int"/> <!-- 房间类型 -->
        <MemberCount Type="int"/> <!-- 房间人数 -->
        <HotValue Type="int" Default="0"/> <!-- 房间热度 -->
        <Master Type="string"/><!-- 房主ID -->
        <SpeechPrivilegeSetting Type="int"/><!-- 房间的发言权限 -->
        <TopMessage Type="string" Default=""/> <!-- 置顶消息内容 -->
        <TopMessageID Type="string" Default=""/> <!-- 置顶消息ID -->
        <TopMessageAuthor Type="string" Default=""/> <!-- 置顶消息作者 -->
        <MemberInfos Type="dict" Key="int" Value="TEAROOM_FIRST_SYNC_MEMBER_INFO"/> <!-- { ShortUid -> {Version, SpeechState} } -->
        <MemberInfoList Type="list" Element="PARTY_ROOM_MEMBER_INFO"/>
        <IsPersistent Type="bool"/> <!-- 是否为常驻房间 -->
        <IsMasterLeave Type="bool"/> <!-- 房主是否暂离（常驻房间） -->
        <IsMasterOffLine Type="bool"/> <!-- 房主是否离线（常驻房间） -->
        <CustomIdentityID2Name Type="DictIntStr"/> <!-- 自定义标签类型到名称的映射 (没有的话就是默认) -->
    </PARTY_DETAILED_INFO>

    <!-- 常驻派对房间存盘属性 -->
    <PERSISTENT_PARTY_DB_INFO Type="tuple">
        <roomID Type="int"  Persistent="true" Default="nil"/>
        <roomName Type="string"  Persistent="true" Default="nil"/>
        <createTime Type="UINT"  Persistent="true" Default="nil"/>
        <masterInfo Type="TEA_ROOM_MEMBER_BASIC_INFO"  Persistent="true" Default="nil"/>
        <dieTime Type="UINT" Persistent="true" Default="nil"/>
        <logicServerID Type="int" Persistent="true" Default="nil"/>
        <roomTag Type="int" Persistent="true" Default="nil"/>
        <roomType Type="int" Persistent="true"  Default="nil"/>
        <topMessage Type="string" Persistent="true" Default="nil"/> <!-- 置顶消息内容 -->
        <topMessageAuthor Type="string" Persistent="true" Default="nil"/> <!-- 置顶消息作者名字 -->
        <topMessageID Type="string" Persistent="true" Default="nil"/> <!-- 置顶消息ID -->
        <blackList Type="dict" Key="string" Value="MemberShowInfo" Persistent="true" Default="nil"/> <!-- 黑名单的内容 -->
        
    </PERSISTENT_PARTY_DB_INFO>

    <GROUP_OCCUPY_AVATAR_INFO Type="tuple">
        <Name Type="string"/>           <!-- 姓名 -->
        <BotID Type="int"/>             <!-- 机器人ID -->
        <ProfessionID Type="int"/>      <!-- 职业 -->
        <Title Type="int"/>             <!-- 称号 -->
        <KillNum Type="int"/>           <!-- 击杀 -->
        <AssistNum Type="int"/>         <!-- 助攻 -->
        <DieNum Type="int"/>            <!-- 死亡 -->
        <Score Type="int"/>             <!-- 个人积分 -->
        <Damage Type="int"/>            <!-- 伤害-->
        <Bear Type="int"/>              <!-- 承伤 -->
        <Heal Type="int"/>              <!-- 治疗 -->
    </GROUP_OCCUPY_AVATAR_INFO>
    <GROUP_OCCUPY_CAMP_INFO Type="tuple">
        <occupyNum Type="int"/>         <!-- 占点数量 -->
        <camp Type="int"/>              <!-- 阵营 -->
        <avatarList Type="list" Element="GROUP_OCCUPY_AVATAR_INFO"/>    <!-- 玩家信息列表 -->
    </GROUP_OCCUPY_CAMP_INFO>
    <GROUP_OCCUPY_BATTLE_INFO Type="dict" Key="int" Value="GROUP_OCCUPY_CAMP_INFO"/>

    <TAROT_TEAM_ID Type="UINT"/>
    <TAROT_TEAM_CLUB_ID Type="string"/>
    <TAROT_TEAM_ID_LIST Type="list" Element="TAROT_TEAM_ID"/>
    <TAROT_TEAM_SHORT_UID Type="UINT"/>
    <TAROT_TEAM_MEMBER_LIST Type="ENTITY_ID_LIST"/>
    <TAROT_TEAM_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID" Persistent="true"/>
        <name Type="string" Persistent="true"/>            
        <tagList Type="ARRAY_UINT" Persistent="true"/>
        <bornPlace Type="UINT" Persistent="true"/>
        <memberList Type="TAROT_TEAM_MEMBER_LIST" Persistent="true"/>
        <createTime Type="UINT" Persistent="true"/>
        <isTemp Type="BOOL" Persistent="true"/>
    </TAROT_TEAM_INFO>
    <TAROT_TEAM_MAP Type="dict" Key="TAROT_TEAM_ID" Value="TAROT_TEAM_INFO"/>

    <TAROT_TEAM_KICK_LEADER_INFO Type="tuple">
        <launcherName Type="ROLENAME" Persistent="true"/>
        <launcherID Type="ENTITY_ID" Persistent="true"/>
        <leaderID Type="ENTITY_ID" Persistent="true"/>
        <confirmTime Type="UINT" Persistent="true"/>
    </TAROT_TEAM_KICK_LEADER_INFO>

    <TAROT_TEAM_SCORE_INFO Type="tuple">
        <lastWeekScore Type="UINT" Persistent="true"/>
        <lastWeekWage Type="UINT" Persistent="true"/>
        <maxWeekScore Type="UINT" Persistent="true"/>
        <curWeekScore Type="UINT" Persistent="true"/>
        <taskFinishTimes Type="UINT_MAP" Persistent="true"/>
        <memberWagedMap Type="dict" Key="ENTITY_ID" Value="UINT" Persistent="true"/>
    </TAROT_TEAM_SCORE_INFO>

    <!--立即存盘可用数据-->
    <TAROT_TEAM_DB_COLD_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"  Persistent="true" Default="nil"/>
        <clubID Type="TAROT_TEAM_CLUB_ID"  Persistent="true" Default="nil"/>
        <leaderName Type="ROLENAME" Persistent="true" Default="nil"/>
        <name Type="string" Persistent="true" Default="nil"/>
        <tagList Type="ARRAY_UINT" Persistent="true" Default="nil"/>
        <bornPlace Type="UINT" Persistent="true" Default="nil"/>
        <memberList Type="TAROT_TEAM_MEMBER_LIST" Persistent="true" Default="nil"/>
        <createTime Type="UINT" Persistent="true" Default="nil"/>
        <status Type="UINT" Persistent="true" Default="nil"/>
        <level Type="UINT" Persistent="true" Default="nil"/>
        <recordTime Type="UINT" Persistent="true" Default="nil"/>
        <score Type="UINT" Persistent="true" Default="nil"/>
        <disbanded Type="BOOL" Persistent="true" Default="nil"/>
        <hiddenFlag Type="BOOL" Persistent="true" Default="nil"/>
        <declaration Type="string" Persistent="true" Default="nil"/>
        <kickLeaderInfo Type="TAROT_TEAM_KICK_LEADER_INFO" Persistent="true" Default="nil"/>
    </TAROT_TEAM_DB_COLD_INFO>

    <!--队列存盘轻量数据-->
    <TAROT_TEAM_DB_COLD_INFO_LIGHT Type="tuple">
        <id Type="TAROT_TEAM_ID"  Persistent="true" Default="nil"/>
        <!-- <clubID Type="TAROT_TEAM_CLUB_ID"  Persistent="true" Default="nil"/> -->
        <leaderName Type="ROLENAME" Persistent="true" Default="nil"/>
        <name Type="string" Persistent="true" Default="nil"/>
        <tagList Type="ARRAY_UINT" Persistent="true" Default="nil"/>
        <bornPlace Type="UINT" Persistent="true" Default="nil"/>
        <!-- <memberList Type="TAROT_TEAM_MEMBER_LIST" Persistent="true" Default="nil"/> -->
        <createTime Type="UINT" Persistent="true" Default="nil"/>
        <!-- <status Type="UINT" Persistent="true" Default="nil"/> -->
        <level Type="UINT" Persistent="true" Default="nil"/>
        <recordTime Type="UINT" Persistent="true" Default="nil"/>
        <score Type="UINT" Persistent="true" Default="nil"/>
        <!-- <disbanded Type="BOOL" Persistent="true" Default="nil"/> -->
        <hiddenFlag Type="BOOL" Persistent="true" Default="nil"/>
        <declaration Type="string" Persistent="true" Default="nil"/>
        <kickLeaderInfo Type="TAROT_TEAM_KICK_LEADER_INFO" Persistent="true" Default="nil"/>
    </TAROT_TEAM_DB_COLD_INFO_LIGHT>

    <!--起服反序列化用-->
    <TAROT_TEAM_DB_HOT_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"  Persistent="true"/>
        <clubID Type="TAROT_TEAM_CLUB_ID"  Persistent="true"/>
        <leaderName Type="ROLENAME" Persistent="true"/>
        <name Type="string" Persistent="true"/>
        <tagList Type="ARRAY_UINT" Persistent="true"/>
        <bornPlace Type="UINT" Persistent="true"/>
        <memberList Type="TAROT_TEAM_MEMBER_LIST" Persistent="true"/>
        <createTime Type="UINT" Persistent="true"/>
        <status Type="UINT" Persistent="true"/>
        <level Type="UINT" Persistent="true"/>
        <recordTime Type="UINT" Persistent="true"/>
        <score Type="UINT" Persistent="true"/>
        <scoreInfo Type="TAROT_TEAM_SCORE_INFO" Persistent="true"/>
        <disbanded Type="BOOL" Default="false" Persistent="true"/>
        <hiddenFlag Type="BOOL" Persistent="true" KeepNil="true"/>
        <kickLeaderInfo Type="TAROT_TEAM_KICK_LEADER_INFO" Persistent="true" Default="nil"/>
        <declaration Type="string" Persistent="true" />
    </TAROT_TEAM_DB_HOT_INFO>
    <TAROT_TEAM_DB_HOT_INFO_MAP Type="dict" Key="TAROT_TEAM_ID" Value="TAROT_TEAM_DB_HOT_INFO"/>

     <!--队伍面板-->
    <TAROT_TEAM_DETAIL_INFO Type="TAROT_TEAM_DB_HOT_INFO"/>
    <!--队伍角色信息-->
    <TAROT_TEAM_ROLE_BRIEF_INFO Type="tuple">
        <id Type="ENTITY_ID" />
        <sex Type="int" />
        <hostId Type="UINT" Default="nil"/>
        <rolename Type="ROLENAME" />
        <school Type="SCHOOL" />
        <lv Type="LEVEL" />
        <state Type="UINT"/>
        <LogicServerID Type="UINT" Default="nil"/>
        <offlineTime Type="UINT" Default="nil"/>
    </TAROT_TEAM_ROLE_BRIEF_INFO>
    <TAROT_TEAM_ROLE_BRIEF_INFO_MAP Type="dict" Key="ENTITY_ID" Value="TAROT_TEAM_ROLE_BRIEF_INFO"/>
    <TAROT_TEAM_ROLE_BRIEF_INFO_LIST Type="list" Element="TAROT_TEAM_ROLE_BRIEF_INFO"/>

    <TAROT_TEAM_BRIEF_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"/>
        <clubID Type="TAROT_TEAM_CLUB_ID"/>
        <name Type="string"/>            
        <tagList Type="ARRAY_UINT"/>
        <bornPlace Type="UINT"/>
        <memberList Type="TAROT_TEAM_MEMBER_LIST"/>
        <createTime Type="UINT"/>
        <status Type="UINT"/>
        <level Type="UINT"/>
        <score Type="UINT"/>
    </TAROT_TEAM_BRIEF_INFO>

    <!--增量同步搜索列表-->
    <TAROT_TEAM_BRIEF_INFO_CACHE Type="tuple">
        <id Type="TAROT_TEAM_ID" Default="nil"/>
        <name Type="string" Default="nil"/>
        <leaderName Type="string" Default="nil"/>
        <tagList Type="ARRAY_UINT" Default="nil"/>
        <bornPlace Type="UINT" Default="nil"/>
        <memberList Type="TAROT_TEAM_MEMBER_LIST" Default="nil"/>
        <createTime Type="UINT" Default="nil"/>
        <status Type="UINT" Default="nil"/>
        <level Type="UINT" Default="nil"/>
        <recordTime Type="UINT" Default="nil"/>
        <score Type="UINT" Default="nil"/>
        <disbanded Type="BOOL" Default="nil"/>
        <declaration Type="string" Default="nil"/>
        <hiddenFlag Type="BOOL" Default="nil"/>
    </TAROT_TEAM_BRIEF_INFO_CACHE>

    <TAROT_TEAM_SEARCH_SCORE_INFO Type="tuple">
        <memberCount Type="UINT"/>
        <expireTime Type="UINT" Default="nil"/>
        <level Type="UINT" Default="nil"/>
    </TAROT_TEAM_SEARCH_SCORE_INFO>

    <!--搜索列表某一行-->
    <TAROT_TEAM_SEARCH_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"/>
        <name Type="string"/>
        <bornPlace Type="UINT"/>
        <tagList Type="ARRAY_UINT"/>
        <status Type="UINT" Default="nil"/>
        <scores Type="TAROT_TEAM_SEARCH_SCORE_INFO"/>
    </TAROT_TEAM_SEARCH_INFO>
    <TAROT_TEAM_SEARCH_INFO_LIST Type="list" Element="TAROT_TEAM_SEARCH_INFO"/>

    <!--搜索列表详情-->
    <TAROT_TEAM_SEARCH_DETAIL_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"/>
        <status Type="UINT"/>
        <declaration Type="string"/>
        <memberListDetail Type="ROLE_BRIEF_LIST"/>
    </TAROT_TEAM_SEARCH_DETAIL_INFO>

    <PLAYER_TAROT_TEAM_APPLY_INFO Type="tuple">
        <expireTime Type="UINT"/>
    </PLAYER_TAROT_TEAM_APPLY_INFO>

    <PLAYER_TAROT_TEAM_INFO Type="tuple">
        <id Type="TAROT_TEAM_ID"/>
        <status Type="UINT"/>
        <applyInfo Type="dict" Key="TAROT_TEAM_ID" Value="PLAYER_TAROT_TEAM_APPLY_INFO"/>
    </PLAYER_TAROT_TEAM_INFO>

    <TAROT_TEAM_SEARCH_ARGS Type="tuple">
        <id Type="TAROT_TEAM_ID" Default="nil"/> <!--精准搜索shortUid-->
        <name Type="string" Default="nil"/>                   <!--精准搜索name-->
        <isRecruting Type="BOOL" Default="nil"/>              <!--是否响应小队-->
    </TAROT_TEAM_SEARCH_ARGS>
    
    <FACE_SLOT_DEFINE Type="tuple">
        <faceData Type="string" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <pictureID Type="string" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
    </FACE_SLOT_DEFINE>

    <MAKEUP_SLOT_DEFINE Type="tuple">
        <makeUpData Type="string" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
        <pictureID Type="string" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>
    </MAKEUP_SLOT_DEFINE>

    <TAROT_TEAM_MODIFY_ARGS Type="tuple">
        <name Type="string" Default="nil"/>
        <bornPlace Type="UINT" Default="nil"/>
        <tagList Type="ARRAY_UINT" Default="nil"/>
        <declaration Type="string" Default="nil"/>
    </TAROT_TEAM_MODIFY_ARGS>
    <TAROT_TEAM_LEVEL_REWARD_MAP Type="dict" Key="UINT" Value="UINT" Flags="OWN_CLIENT" Persistent="true" />
    <PLAYER_TAROT_TEAM_APPLY_MAP Type="dict" Key="TAROT_TEAM_ID" Value="UINT"/>

    <!-- 受击相关 BEGIN -->

    <!-- RootMotionInfo -->
    <HIT_FEEDBACK_LIFE Type="tuple">
        <startTimestamp Type="int" Default="nil"/>
        <duration Type="float" Default="nil"/>
    </HIT_FEEDBACK_LIFE>

    <!-- HF_MOVE_INFO_LIST -->
    <HF_MOVE_INFO Type="tuple">  
        <type Type="int" Default="nil"/>   <!-- 0-physical type, 1-rootmotion type -->
        <startPos Type="PVector3" Default="nil"/>
        <targetPos Type="PVector3" Default="nil"/>
        <duration Type="float" Default="nil"/>
        <maxHeight Type="float" Default="nil"/>
    </HF_MOVE_INFO>
    <HF_MOVE_INFO_LIST Type="list" Element="HF_MOVE_INFO" /> 

    <!-- 受击相关 END -->

    <DEBUG_TRAP_INFO Type="tuple">
        <shapeType Type="int" Default="nil"/>
        <param1 Type="float" Default="nil"/>
        <param2 Type="float" Default="nil"/> 
        <param3 Type="float" Default="nil"/>    
    </DEBUG_TRAP_INFO>

    <!--天赋树数据-->
    <TALENT_TREE_DATA Type="tuple">
        <UsedPoint Type="int" Flags="OWN_CLIENT" Persistent="true"/>           			    	              <!--已使用天赋点-->
        <NodeLvMap Type="dict" Key="int" Value="int" Flags="OWN_CLIENT" Persistent="true"/>                   <!--天赋节点等级数据-->
        <SpecialNodeActiveInfo Type="dict" Key="int" Value="bool" Flags="OWN_CLIENT" Persistent="true"/>      <!--特殊节点激活数据-->
    </TALENT_TREE_DATA>

    <!--天赋系统-->
    <TALENT_DATA Type="tuple">
        <ActiveTreeID Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>   									<!--当前激活天赋树ID-->
        <TreeDataMap Type="dict" Key="int" Value="TALENT_TREE_DATA" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>    <!--天赋树数据-->
    </TALENT_DATA>

    <!--天赋树数据-->
    <TalentTreeData Type="tuple">
        <UsedPoint Type="int" Flags="OWN_CLIENT" Persistent="true"/>           			    	<!--已使用天赋点-->
        <NodeLvMap Type="dict" Key="int" Value="int" Flags="OWN_CLIENT" Persistent="true"/>     <!--天赋节点等级数据-->
    </TalentTreeData>

    <!--元素天赋数据-->
    <EleTalentData Type="tuple">    
        <SeasonID Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>       								    <!--赛季ID-->
		<ActiveTreeID Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>   									<!--激活天赋树ID-->
		<Level Type="int" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>          									<!--天赋树等级(所有天赋树共享)-->
        <TreeDataMap Type="dict" Key="int" Value="TalentTreeData" Default="nil" Flags="OWN_CLIENT" Persistent="true"/>    	<!--天赋树数据-->
    </EleTalentData>

    <ArmRotation Type="tuple">
        <Pitch Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
        <Yaw Type="float" Flags="ALL_CLIENTS" Default="0.0"/>
    </ArmRotation>

    <PlayCinematicExtraParam Type="tuple">
        <ArmRotation Type="ArmRotation" Default="nil" Flags="ALL_CLIENTS" />
        <ArmLen Type="float" Default="nil" Flags="ALL_CLIENTS" />
        <Pos Type="ListFloat" Default="nil" Flags="ALL_CLIENTS" />
        <Yaw Type="float" Default="nil" Flags="ALL_CLIENTS" />
        <AnchorMode Type="int" Default="0" Flags="ALL_CLIENTS" />
    </PlayCinematicExtraParam>

    <NPC_RUNTIME_SETTINGS Type="tuple">
        <GossipGroupID Type="int" Default="nil" Flags="ALL_CLIENTS" />
        <TalkGroupID Type="int" Default="nil" Flags="ALL_CLIENTS" />
        <GossipGroupTriggerSwitch Type="bool" Default="nil" Flags="ALL_CLIENTS" />
        <CollisionStatus Type="bool" Default="false" Flags="ALL_CLIENTS"/>
        <NpcHudPermeable Type="bool" Default="false" Flags="ALL_CLIENTS"/>
    </NPC_RUNTIME_SETTINGS>

    <!-- PlotRecap Begin -->
    <PLOT_RECAP_INFO Type="tuple">
        <plotRecapID Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!-- 剧情回顾ID -->
        <plotRecapState Type="int" Flags="SERVER_ONLY" Default="0" Persistent="true"/>  <!-- 剧情回顾状态,参考PLOT_RECAP_STATE -->
        <unlockTime Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!-- 剧情回顾解锁时间 -->
        <hasReceivedReward Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="true"/> <!-- 是否领取过奖励 -->
        <receivedRewardTime Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/> <!-- 奖励领取时间 -->
    </PLOT_RECAP_INFO>
    <PLOT_RECAP_DICT Type="dict" Key="int" Value="PLOT_RECAP_INFO"/>
    <!-- 大世界活动Start -->
    <EXTRAORDINARY_EVENT_INFO Type="tuple">
        <id Type="int" Persistent="true" Default="0" Flags="SERVER_ONLY"/> <!-- 非凡事件ID -->
        <isUnlock Type="bool"  Persistent="true" Default="false" Flags="OWN_CLIENT"/> <!-- 非凡事件是否解锁 -->
        <isDone Type="bool" Persistent="true" Default="false" Flags="OWN_CLIENT"/> <!-- 非凡事件是否完成 -->
        <isRewarded Type="bool"  Persistent="true" Default="false" Flags="SERVER_ONLY"/> <!-- 非凡事件奖励是否领取 -->
    </EXTRAORDINARY_EVENT_INFO>
    
    <EXTRAORDINARY_EVENT_INFO_MAP Type="dict" Key="int" Value="EXTRAORDINARY_EVENT_INFO" Flags="OWN_CLIENT"/>
    
    <WONDERLAND_INFO Type="tuple">
        <id Type="int" Persistent="true" Default="0" Flags="SERVER_ONLY"/> <!-- 奇观ID -->
        <isUnlock Type="bool"  Persistent="true" Default="false" Flags="OWN_CLIENT"/> <!-- 奇观是否解锁 -->
        <isDone Type="bool" Persistent="true" Default="false" Flags="SERVER_ONLY"/> <!-- 奇观是否完成 -->
        <isRewarded Type="bool"  Persistent="true" Default="false" Flags="SERVER_ONLY"/> <!-- 奇观奖励是否领取 -->
        <isEnterPlane Type="bool" Persistent="true" Default="false" Flags="OWN_CLIENT"/> <!-- 是否进入奇观位面 -->
    </WONDERLAND_INFO>
    
    <WONDERLAND_INFO_MAP Type="dict" Key="int" Value="WONDERLAND_INFO" Flags="OWN_CLIENT"/>
    <!-- 大世界活动End -->

    <PLOT_RECAP_RECEIVED_REWARD_INFO Type="tuple">
        <hasReceivedReward Type="bool" Flags="SERVER_ONLY" Default="false" Persistent="true"/>
        <receivedRewardTime Type="LONGTIME" Flags="SERVER_ONLY" Default="0" Persistent="true"/>
    </PLOT_RECAP_RECEIVED_REWARD_INFO>
    <PLOT_RECAP_RECEIVED_REWARD_DICT Type="dict" Key="int" Value="PLOT_RECAP_RECEIVED_REWARD_INFO"/>

    <PLOT_RECAP_MAIN_TYPE_INFO Type="tuple">
        <token Type="int" Default="0" Persistent="true"/>       <!--当前总积分-->
        <rewardFlag Type="int" Default="0" Persistent="true"/>   <!--位标识，哪些等级已领取了奖励-->
    </PLOT_RECAP_MAIN_TYPE_INFO>
    <PLOT_RECAP_MAIN_TYPE_DICT Type="dict" Key="int" Value="PLOT_RECAP_MAIN_TYPE_INFO"/>

    <!-- PlotRecap End -->

    <SPACE_REMAIN_TIME_DEFINE ImplClass="RageRemainTimeInfo" Type="tuple">
        <DictKey Type="string" Flags="ALL_CLIENTS" Persistent="false"/>
        <SpaceRemainTime Type="float" Flags="ALL_CLIENTS" Persistent="false"/>
        <SpaceMechanismID Type="int" Flags="ALL_CLIENTS" Persistent="false"/>
    </SPACE_REMAIN_TIME_DEFINE>

    <COMMON_INTERACTOR_PRIVATE_STATE Type="tuple">
        <State Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <CompletedActions Type="DictIntBool" Flags="SERVER_ONLY" Persistent="true"/>
        <InteractCount Type="int" Flags="SERVER_ONLY" Persistent="true"/>
    </COMMON_INTERACTOR_PRIVATE_STATE>

    <COMMON_INTERACTOR_SYNC_ATTR Type="tuple">                          <!-- 通用交互物同步属性 -->
        <ID Type="int" Flags="ALL_CLIENTS"/>                            <!-- 实例ID -->
        <interactorID Type="int" Flags="ALL_CLIENTS"/>                  <!-- 交互物配置ID -->
        <interactCount Type="int" Flags="ALL_CLIENTS"/>                 <!-- 交互次数 -->
        <curState Type="int" Flags="ALL_CLIENTS"/>                      <!-- 当前状态 -->
        <completedActions Type="DictIntBool" Flags="ALL_CLIENTS"/>      <!-- 当前状态已完成actions -->
        <pos Type="PVector3" Flags="ALL_CLIENTS"/>                      <!-- 位置 -->
        <rot Type="PRotator" Flags="ALL_CLIENTS"/>                      <!-- 朝向 -->
        <cdExpiredTime Type="int" Flags="ALL_CLIENTS"/>                 <!-- cd到期时间 -->
        <gameplayID Type="int" Flags="ALL_CLIENTS"/>                    <!-- 所属自定义玩法实例ID -->
        <scale Type="float" Flags="ALL_CLIENTS"/>                       <!-- 缩放,默认为1 -->
        <insID Type="string" Flags="ALL_CLIENTS"/>                      <!-- 场编ID -->
        <extraParam Type="none" Flags="ALL_CLIENTS"/>                   <!-- 自定义额外属性 -->
        <buttonList Type="ListInt" Flags="ALL_CLIENTS"/>                <!-- 新增的按钮列表 -->
        <isPrivate Type="bool" Flags="ALL_CLIENTS"/>                    <!-- 是否私有 -->
    </COMMON_INTERACTOR_SYNC_ATTR>
    <COMMON_INTERACTOR_LIST Type="list" Element="COMMON_INTERACTOR_SYNC_ATTR"/>
    <LEVEL_SEQUENCE Type="tuple">
        <assetID Type="int" Flags="ALL_CLIENTS" />
        <timestamp Type="int" Flags="ALL_CLIENTS" />
        <startFrame Type="int" Flags="ALL_CLIENTS" />
        <endFrame Type="int" Flags="ALL_CLIENTS" />
        <startMark Type="string" Default="nil" Flags="ALL_CLIENTS" />
        <endMark Type="string" Default="nil" Flags="ALL_CLIENTS" />
        <sequenceType Type="int" Default="nil" Flags="ALL_CLIENTS" />
        <pos Type="ListFloat" Default="nil" Flags="ALL_CLIENTS" />
        <yaw Type="float" Default="nil" Flags="ALL_CLIENTS" />
        <anchorMode Type="int" Default="0" Flags="ALL_CLIENTS" />
    </LEVEL_SEQUENCE>
    <LEVEL_SEQUENCE_MAP Type="dict" Key="int" Value="LEVEL_SEQUENCE" />

    <CUSTOMIZED_GAMEPLAY_INFO Type="tuple">                             <!-- 自定义玩法属性 -->
        <id Type="int" Flags="ALL_CLIENTS"/>                            <!-- 实例ID -->
        <gameplayType Type="int" Flags="ALL_CLIENTS"/>                  <!-- 玩法类型 -->
        <gameplayID Type="int" Flags="ALL_CLIENTS"/>                    <!-- 玩法配置ID -->
        <pos Type="PVector3" Flags="ALL_CLIENTS"/>                      <!-- 位置 -->
        <rot Type="PRotator" Flags="ALL_CLIENTS"/>                      <!-- 朝向 -->
    </CUSTOMIZED_GAMEPLAY_INFO>
    <CUSTOMIZED_GAMEPLAY_INFO_LIST Type="list" Element="CUSTOMIZED_GAMEPLAY_INFO"/>

    <GM_DEFINE Type="tuple">
        <key Type="string" Persistent="true"/>
        <name Type="string" Persistent="true"/>
        <args Type="GM_ARG_LIST" Persistent="true"/>
    </GM_DEFINE>
    <GM_DEFINE_MAP Type="dict" Key="string" Value="GM_DEFINE" Flags="SERVER_ONLY"/>
    <GM_DEFINE_MODULES Type="dict" Key="string" Value="GM_DEFINE_MAP" Flags="SERVER_ONLY"/>



    <!--立即存盘可用数据-->
    <CHAMPION_TROOP_ID Type="UINT"/>
    <CHAMPION_TROOP_ID_LIST Type="list" Element="CHAMPION_TROOP_ID"/>
    <CHAMPION_REGION_TROOP_LIST Type="list" Element="CHAMPION_TROOP_ID_LIST" Persistent="true"/>
    <CHAMPION_REGION_TROOP_DICT Type="dict" Key="UINT" Value="CHAMPION_TROOP_ID_LIST" Persistent="true"/>
    <CHAMPION_ROLE_ID Type="ENTITY_ID"/>
    <CHAMPION_TROOP_MEMBER_UPDATE_INFO Type="tuple">
        <id Type="CHAMPION_ROLE_ID" Persistent="true" Default="nil"/>
        <rolename Type="ROLENAME" Persistent="true" Default="nil"/>
        <sex Type="UINT" Persistent="true" Default="nil"/>
        <level Type="UINT" Persistent="true" Default="nil"/>
        <zhanLi Type="UINT" Persistent="true" Default="nil"/>
        <professionID Type="UINT" Persistent="true" Default="nil"/>
        <joinTime Type="UINT" Persistent="true" Default="nil"/>
    </CHAMPION_TROOP_MEMBER_UPDATE_INFO>

    <CHAMPION_TROOP_MEMBER_INFO Type="tuple">
        <id Type="CHAMPION_ROLE_ID" Persistent="true"/>
        <rolename Type="ROLENAME" Persistent="true"/>
        <sex Type="UINT" Persistent="true" />
        <level Type="UINT" Persistent="true" />
        <zhanLi Type="UINT" Persistent="true" />
        <professionID Type="UINT" Persistent="true" />
        <joinTime Type="UINT" Persistent="true"/>
    </CHAMPION_TROOP_MEMBER_INFO>
    <CHAMPION_TROOP_MEMBER_LIST Type="list" Element="CHAMPION_TROOP_MEMBER_INFO" Flags="SERVER_ONLY" Persistent="true"/>
    <CHAMPION_TROOP_MEMBER_ID_LIST Type="list" Element="CHAMPION_ROLE_ID" Flags="SERVER_ONLY" Persistent="true"/>

    <CHAMPION_TROOP_DB_INFO Type="tuple">
        <id Type="CHAMPION_TROOP_ID"  Persistent="true" Default="nil"/>
        <name Type="string"  Persistent="true" Default="nil"/>
        <leaderID Type="CHAMPION_ROLE_ID"  Persistent="true" Default="nil"/>
        <leaderName Type="string"  Persistent="true" Default="nil"/>
        <members Type="dict" Key="CHAMPION_ROLE_ID" Value="CHAMPION_TROOP_MEMBER_INFO" Persistent="true" Default="nil"/>
        <signUp Type="BOOL" Persistent="true" Default="nil"/>
        <regionID Type="BOOL" Persistent="true" Default="nil"/>
        <seasonID Type="UINT" Persistent="true"  Default="nil"/>
        <createTime Type="UINT" Persistent="true"  Default="nil"/>
    </CHAMPION_TROOP_DB_INFO>

    <CHAMPION_TROOP_INFO Type="tuple">
        <id Type="CHAMPION_TROOP_ID"  Persistent="true"/>
        <name Type="string"  Persistent="true"/>
        <leaderID Type="CHAMPION_ROLE_ID"  Persistent="true"/>
        <leaderName Type="string"  Persistent="true"/>
        <members Type="dict" Key="CHAMPION_ROLE_ID" Value="CHAMPION_TROOP_MEMBER_INFO" Persistent="true"/>
        <signUp Type="BOOL" Persistent="true"/>
        <createTime Type="UINT" Persistent="true"/>
        <regionID Type="UINT" Persistent="true"/>
    </CHAMPION_TROOP_INFO>
    <CHAMPION_TROOP_INFO_LIST Type="list" Element="CHAMPION_TROOP_INFO" Flags="SERVER_ONLY" Persistent="true"/>

    <CHAMPION_TROOP_BRIEF_INFO Type="tuple">
        <id Type="CHAMPION_TROOP_ID"  Persistent="true"/>
        <name Type="string"  Persistent="true"/>
        <leaderID Type="CHAMPION_ROLE_ID"  Persistent="true"/>
        <leaderName Type="string"  Persistent="true"/>
        <memberList Type="list" Element="CHAMPION_ROLE_ID" Persistent="true"/>
        <signUp Type="BOOL" Persistent="true"/>
        <regionID Type="UINT" Persistent="true"/>
    </CHAMPION_TROOP_BRIEF_INFO>

    <CHAMPION_DB_INFO Type="tuple" Inherit="CHAMPION_TROOP_BRIEF_INFO">
        <seasonID Type="UINT"  Persistent="true"/>
    </CHAMPION_DB_INFO>

    <CHAMPION_GROUP_BATTLE_SCHEDULE_INFO Type="tuple">
        <roundIndex Type="UINT" Persistent="true"/>
        <roundStatus Type="UINT" Persistent="true"/>
        <schedule Type="list" Element="ARRAY_UINT" Persistent="true"/>
    </CHAMPION_GROUP_BATTLE_SCHEDULE_INFO>
    <CHAMPION_REGION_MAP Type="dict" Key="UINT" Value="CHAMPION_TROOP_ID_LIST" Flags="SERVER_ONLY" Persistent="true"/>

    <CHAMPION_REGION_BATTLE_INFO Type="tuple">
        <scheduleType Type="UINT" Persistent="true"/> <!--champion_const.CHAMPION_SCHEDULE_TYPE-->
        <roundIndex Type="UINT" Persistent="true"/>
        <roundStatus Type="UINT" Persistent="true"/>
        <statusEndTime Type="UINT" Persistent="true"/> <!--0表示该状态无结束时间-->
    </CHAMPION_REGION_BATTLE_INFO>

    <CHAMPION_BATTLE_AVATAR_INFO Type="tuple">
        <Name Type="string"/>           <!-- 姓名 -->
        <BotID Type="int"/>             <!-- 机器人ID -->
        <ProfessionID Type="int"/>      <!-- 职业 -->
        <Title Type="int"/>             <!-- 称号 -->
        <KillNum Type="int"/>           <!-- 击杀 -->
        <AssistNum Type="int"/>         <!-- 助攻 -->
        <DieNum Type="int"/>            <!-- 死亡 -->
        <Score Type="int"/>             <!-- 个人积分 -->
        <Damage Type="int"/>            <!-- 伤害-->
        <Bear Type="int"/>              <!-- 承伤 -->
        <Heal Type="int"/>              <!-- 治疗 -->
    </CHAMPION_BATTLE_AVATAR_INFO>
    <CHAMPION_BATTLE_CAMP_INFO Type="tuple">
        <occupyNum Type="int"/>         <!-- 占点数量 -->
        <camp Type="int"/>              <!-- 阵营 -->
        <avatarList Type="list" Element="CHAMPION_BATTLE_AVATAR_INFO"/>    <!-- 玩家信息列表 -->
    </CHAMPION_BATTLE_CAMP_INFO>
    <CHAMPION_BATTLE_INFO Type="dict" Key="int" Value="CHAMPION_BATTLE_CAMP_INFO"/>

    <CHAMPION_GROUP_BATTLE_MATCH_ITEM Type="tuple">
        <troopID Type="CHAMPION_TROOP_ID" Persistent="true"/>         <!-- 战队id -->
        <regionID Type="UINT" Persistent="true"/>              <!-- 赛区 -->
        <name Type="string" Persistent="true"/>              <!-- 赛区 -->
        <score Type="UINT" Persistent="true"/>    <!-- 荣誉值 -->
        <defaultScore Type="UINT" Persistent="true"/>    <!-- 默认荣誉值(战力) -->
        <extraScore Type="UINT" Persistent="true"/>    <!-- 对局获得的荣誉值 -->
    </CHAMPION_GROUP_BATTLE_MATCH_ITEM>

    <CHAMPION_GROUP_BATTLE_MATCH_INFO Type="tuple">
        <matchItems Type="list" Element="CHAMPION_GROUP_BATTLE_MATCH_ITEM" Persistent="true"/>    <!-- 匹配, {itemA, itemB} -->
        <time Type="UINT" Persistent="true"/>                    <!-- 时间 -->
        <winTroopID Type="CHAMPION_TROOP_ID" Persistent="true"/> <!-- 胜利战队 -->
    </CHAMPION_GROUP_BATTLE_MATCH_INFO>
    <CHAMPION_GROUP_BATTLE_MATCH_INFO_MAP Type="dict" Key="UINT" Value="CHAMPION_GROUP_BATTLE_MATCH_INFO" Persistent="true"/> <!--roundIndex -> match -->
    <CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST Type="list" Element="CHAMPION_GROUP_BATTLE_MATCH_INFO" Persistent="true"/> <!--roundIndex -> match -->
    <CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST_MAP Type="dict" Key="UINT" Value="CHAMPION_GROUP_BATTLE_MATCH_INFO" Persistent="true"/> <!--roundIndex -> match -->
    <CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST_BY_MAP Type="dict" Key="UINT" Value="CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST" Persistent="true"/> <!--UINT -> matches-->

    <CHAMPION_GROUP_BATTLE_INFO Type="tuple">
        <roundIndex Type="UINT" Persistent="true"/>    <!-- 轮次 -->
        <roundStatus Type="UINT" Persistent="true"/>    <!-- 轮次状态 -->
        <matches Type="dict" Key="UINT" Value="CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST_BY_MAP"/>    <!-- roundIndex -> regionID -> matches -->
    </CHAMPION_GROUP_BATTLE_INFO>

    <CHAMPION_REGION_INFO Type="tuple">
        <roundIndex Type="UINT" Persistent="true"/>    <!-- 轮次 -->
        <roundStatus Type="UINT" Persistent="true"/>    <!-- 轮次状态 -->
        <statusEndTime Type="UINT" Persistent="true"/>    <!-- 本轮结束时间 -->
        <scheduleType Type="UINT" Persistent="true"/>    <!-- 小组赛/淘汰赛 -->
        <matches Type="CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST_BY_MAP" Persistent="true"/>    <!-- roundIndex ->  matches -->
        <supportMap Type="dict" Key="CHAMPION_TROOP_ID" Value="UINT_MAP" Default="nil" Persistent="true"/>    <!-- troopID -> shortUid -> count -->
    </CHAMPION_REGION_INFO>
    <CHAMPION_REGION_INFO_MAP Type="dict" Key="UINT" Value="CHAMPION_REGION_INFO" Persistent="true"/>

    <!--小组赛，单轮次对局展示-->
    <CHAMPION_GROUP_BATTLE_SHOW_LIST Type="tuple">
        <endIndex Type="UINT" Persistent="true"/>    <!-- 终止索引 -->
        <list Type="CHAMPION_GROUP_BATTLE_MATCH_INFO_LIST"/>    <!-- 对局列表 -->
    </CHAMPION_GROUP_BATTLE_SHOW_LIST>

    <!--小组赛，多轮次对局展示-->
    <CHAMPION_GROUP_BATTLE_MULTI_SHOW_LIST Type="dict" Key="UINT" Value="CHAMPION_GROUP_BATTLE_SHOW_LIST" />

    <!--小组赛，排行信息-->
    <CHAMPION_GROUP_BATTLE_RANK_ITEM Type="tuple">
        <id Type="CHAMPION_TROOP_ID" Persistent="true"/>    <!-- 战队id -->
        <name Type="string" Persistent="true"/>    <!-- 战队名 -->
        <winTimes Type="UINT" Persistent="true"/>    <!-- 胜利次数 -->
        <loseTimes Type="UINT" Persistent="true"/>    <!-- 胜利次数 -->
        <score Type="UINT" Persistent="true"/>    <!-- 荣誉值 -->
        <defaultScore Type="UINT" Persistent="true"/>    <!-- 战队战力 -->
        <leaderID Type="CHAMPION_ROLE_ID" Persistent="true"/> <!-- 队长ID -->
    </CHAMPION_GROUP_BATTLE_RANK_ITEM>
    <CHAMPION_GROUP_BATTLE_RANKLIST Type="list" Element="CHAMPION_GROUP_BATTLE_RANK_ITEM" Flags="SERVER_ONLY"/>
    <CHAMPION_TROOP_MEMBER_MAP Type="dict" Key="CHAMPION_ROLE_ID" Value="CHAMPION_TROOP_MEMBER_INFO" Flags="SERVER_ONLY" />
    <CHAMPION_MULTI_TROOP_MEMBER_MAP Type="dict" Key="CHAMPION_TROOP_ID" Value="CHAMPION_TROOP_MEMBER_MAP" Flags="SERVER_ONLY"/>

    <!--淘汰赛，助力排行信息-->
    <CHAMPION_ELIMINATION_SUPPORT_RANK_ITEM Type="tuple">
        <id Type="CHAMPION_TROOP_ID" Persistent="true"/>    <!-- 战队id -->
        <name Type="string" Persistent="true"/>    <!-- 战队名 -->
        <score Type="UINT" Persistent="true"/>    <!-- 助力 -->
        <leaderID Type="CHAMPION_ROLE_ID" Persistent="true"/> <!-- 队长ID -->
    </CHAMPION_ELIMINATION_SUPPORT_RANK_ITEM>
    <CHAMPION_ELIMINATION_SUPPORT_RANKLIST Type="list" Element="CHAMPION_ELIMINATION_SUPPORT_RANK_ITEM" Flags="SERVER_ONLY"/>

    <!--单位查询追踪信息-->
    <ENTITY_TRACE_INFO Type="tuple">
        <Position Type="list" Element="float"  Default="nil"  /> <!-- 坐标 -->
        <InstanceID Type="INSTANCE_ID"/> <!-- InstanceID -->
    </ENTITY_TRACE_INFO>

    <ENTITY_TRACE_INFO_DICT Type="dict" Key="ENTITY_ID" Value="ENTITY_TRACE_INFO" />

    <TEAM_ARENA_AVATAR_INFO Type="tuple">
        <avatarID Type="ENTITY_ID"/>           <!-- 姓名 -->
        <Name Type="string"/>           <!-- 姓名 -->
        <BotID Type="int"/>             <!-- 机器人ID -->
        <ProfessionID Type="int"/>      <!-- 职业 -->
        <Title Type="int"/>             <!-- 称号 -->
        <KillNum Type="int"/>           <!-- 击杀 -->
        <AssistNum Type="int"/>         <!-- 助攻 -->
        <DieNum Type="int"/>            <!-- 死亡 -->
        <Score Type="int"/>             <!-- 个人积分 -->
        <Damage Type="int"/>            <!-- 伤害-->
        <Bear Type="int"/>              <!-- 承伤 -->
        <Heal Type="int"/>              <!-- 治疗 -->
    </TEAM_ARENA_AVATAR_INFO>
    <TEAM_ARENA_BATTLE_INFO Type="tuple">
        <avatarList Type="list" Element="TEAM_ARENA_AVATAR_INFO"/>    <!-- 玩家信息列表 -->
    </TEAM_ARENA_BATTLE_INFO>
    <TEAM_ARENA_BATTLE_INFO_MAP Type="dict" Key="int" Value="TEAM_ARENA_BATTLE_INFO"/>

    <!-- DiceCheck预览信息 -->
    <DICE_CHECK_INFO Type="tuple">
        <id Type="int" /> <!-- dice check ID -->
        <defaultDiceCount Type="int" /> <!-- 默认骰子数量 -->
        <targetCount Type="int" /> <!-- 目标点数 -->
        <bonusDiceCount Type="DictIntInt" /> <!-- 特殊骰子数量 type: count -->
        <bonusTarget Type="int" /> <!-- 会额外加的点数, 校验用总和, 客户端需要自己算明细 -->
    </DICE_CHECK_INFO>

    <!-- 单个骰子的结果 -->
    <DICE_CHECK_ROLL_RESULT_ITEM Type="tuple">
        <index Type="int" /> <!-- 骰子索引 -->
        <type Type="int" /> <!-- 骰���类型 -->
        <origin Type="int" /> <!-- 原始点数 -->
        <total Type="int" /> <!-- 最终点数 -->
    </DICE_CHECK_ROLL_RESULT_ITEM>

    <!-- DiceCheck结果 -->
    <DICE_CHECK_RESULT Type="tuple">
        <id Type="int" /> <!-- dice check ID -->
        <defaultDiceCount Type="int" /> <!-- 默认骰子数量 -->
        <rollResult Type="dict" Key="int" Value="DICE_CHECK_ROLL_RESULT_ITEM" /> <!-- 骰子结果列表 -->
        <total Type="int" /> <!-- 骰子总和 -->
        <isSuccess Type="bool" /> <!-- 是否成功 -->
        <targetCount Type="int" /> <!-- 目标点数 -->
        <bonusTarget Type="int" /> <!-- 会额外加的点数, 校验用总和, 客户端需要自己算明细 -->
    </DICE_CHECK_RESULT>

    <!-- 单个黑屏字幕 -->
    <BLACK_SCREEN_SUBTITILE Type="tuple">
        <Text Type="string" /> <!-- 文本 -->
        <Duration Type="float" /> <!-- 显示时长 -->
        <CanSkip Type="bool" /> <!-- 是否可跳过 -->
        <CanSkipTime Type="float" /> <!-- 多长时间后可跳过 -->
    </BLACK_SCREEN_SUBTITILE>

    <!-- 黑屏字幕列表 -->
    <SCREEN_SUBTITILE_LIST Type="list" Element="BLACK_SCREEN_SUBTITILE"/> <!-- 字幕列表 -->

    <CUT_SCENE Type="tuple">
        <CutSceneID Type="int" />
        <SpaceEntityID Type="ENTITY_ID" />
    </CUT_SCENE>
    <FLOWCHART_AVATAR_PRE_ENTER_SPACE_INFO Type="tuple">
        <CutScene Type="CUT_SCENE" Default="nil" />
    </FLOWCHART_AVATAR_PRE_ENTER_SPACE_INFO>

    <GVG_OCCUPY_POINT_INFO Type="tuple">
        <OccupyCamp Type="int" />
        <curOccupyStatus Type="int" />
        <curOccupyProgress Type="float"/>
    </GVG_OCCUPY_POINT_INFO>
    <!-- 家园工坊 -->
    <!-- 单个工坊的生产状态 -->
    <WORKSHOP_PRODUCE_ITEM Type="tuple">
        <recipeID Type="int" Default="0" Persistent="true"/> <!-- 配方ID -->
        <num Type="int" Default="1" Persistent="true"/> <!-- 生产数量 -->
        <startTime Type="int" Default="nil" Persistent="true"/> <!-- 开始生产时间 -->
        <endTime Type="int" Default="nil" Persistent="true"/> <!-- 结束生产时间 -->
        <status Type="int" Default="0" Persistent="true"/> <!-- 生产状态 -->
        <productionTime Type="int" Default="0" Persistent="true"/> <!-- 生产所需时间 -->
    </WORKSHOP_PRODUCE_ITEM>

        <!-- 工坊雇员信息 -->
    <WORKSHOP_EMPLOYEE Type="tuple">
        <id Type="ENTITY_ID" Persistent="true"/> <!-- 雇员的唯一ID -->
        <employeeID Type="int" Persistent="true"/> <!-- 雇员ID -->
        <quality Type="UINT" Default="0" Persistent="true"/>  <!-- 雇员品质 -->
        <buildingID Type="UINT" Default="0" Persistent="true"/> <!-- 工坊建筑ID, 唯一ID -->
    </WORKSHOP_EMPLOYEE>

    <!-- 单个工坊的状态 -->
    <WORKSHOP_STATUS Type="tuple">
        <buildingID Type="UINT" Default="0" Persistent="true"/> <!-- 工坊建筑ID, 唯一ID -->
        <furnitureID Type="int" Default="0" Persistent="true"/> <!-- 家具ID -->
        <workshopType Type="int" Default="0" Persistent="true"/> <!-- 工坊类型 -->
        <status Type="int" Default="0" Persistent="true"/> <!-- 工坊状态 -->
        <productionQueue Type="list" Element="WORKSHOP_PRODUCE_ITEM" Default="nil" Persistent="true"/> <!-- 生产队列 -->
        <employees Type="dict" Key="int" Value="UUID" Default="nil" Persistent="true"/> <!-- 工坊雇员列表, value: 雇员的唯一ID -->
        <level Type="int" Default="1" Persistent="true"/> <!-- 工坊等级 -->
    </WORKSHOP_STATUS>
    <!-- 用户的工坊状态列表 key:工坊建筑ID -->
    <WORKSHOP_LIST Type="dict" Key="UINT" Value="WORKSHOP_STATUS" Flags="SERVER_ONLY" Persistent="true"/>

    <!-- 工坊雇员列表 key: 雇员的唯一ID -->
    <WORKSHOP_EMPLOYEE_LIST Type="dict" Key="ENTITY_ID" Value="WORKSHOP_EMPLOYEE" Flags="SERVER_ONLY" Persistent="true"/>


</root>
