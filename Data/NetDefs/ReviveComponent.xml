<?xml version="1.0" ?>
<root>
    <Properties>
        <ReviveRecords Type="ReviveRecords" Flags="OWN_CLIENT" Persistent="true"/>
        <ReviveHelpTimestamp Type="int" Flags="OWN_CLIENT" Persistent="true"/>
        <BossBattleReviveChance Type="bool" Flags="OWN_CLIENT"/>
    </Properties>

    <Implements/>

    <ClientMethods>
        <RetRevive>
            <Arg>int</Arg>
        </RetRevive>
        <RetReviveHelp>
            <Arg>int</Arg>
        </RetReviveHelp>
    </ClientMethods>

    <ServerMethods>
        <ReqRevive> <Exposed/>
            <Arg> int </Arg> <!-- reviveID -->
        </ReqRevive>
        <ReqReviveHelp> <Exposed/>
        </ReqReviveHelp>
    </ServerMethods>
</root>
