<?xml version="1.0" ?>
<root>
    <Properties>
        <VehicleEntID Type="string" Default="" Flags="ALL_CLIENTS"/>     <!-- 乘坐马车的entityID -->
        <VehicleSeatIndex Type="int" Default="0" Flags="ALL_CLIENTS"/>  <!-- 乘坐马车的位置 -->
    </Properties>
	<Implements/>
	<ClientMethods>
        <RetVehicleGetOn>
			<!--1 type -->
			<Arg> Result </Arg>
			<!--2 vehicleEntID-->
            <Arg> string </Arg>
		</RetVehicleGetOn>
        <RetVehicleGetOff>
			<!--1 type -->
			<Arg> Result </Arg>
			<!--2 vehicleEntID-->
            <Arg> string </Arg>
		</RetVehicleGetOff>
        <RetVehicleChangeSeat>
			<!--1 type -->
			<Arg> Result </Arg>
			<!--2 vehicleEntID-->
            <Arg> string </Arg>
		</RetVehicleChangeSeat>
		<RetVehicleGameplayOperate>
			<!--1 type -->
			<Arg> Result </Arg>
			<!--2 vehicleEntID-->
            <Arg> string </Arg>
			<!--3 Operate-->
            <Arg> int </Arg>
		</RetVehicleGameplayOperate>
	</ClientMethods>
	<!-- Exposed 待处理 -->
	<ServerMethods>
		<ReqVehicleGetOn> <Exposed/>       <!-- 上马车 -->
            <!--1 vehicleEntID-->
            <Arg> string </Arg>
		</ReqVehicleGetOn>
        <ReqVehicleGetOff> <Exposed/>      <!-- 下马车 -->
		</ReqVehicleGetOff>
        <ReqVehicleChangeSeat> <Exposed/>  <!-- 换座位 -->
		</ReqVehicleChangeSeat>
		<ReqVehicleGameplayOperate> <Exposed/>  <!-- 玩法相关操作 -->
			<!--1 Operate-->
			<Arg> int </Arg>
			<!--2 Params-->
			<Arg> VEHCILE_GAMEPLAY_PARAMS </Arg>
		</ReqVehicleGameplayOperate>
	</ServerMethods>
</root>
