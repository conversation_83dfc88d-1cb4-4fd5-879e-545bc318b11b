<?xml version="1.0" ?>
<root>
    <Properties>
        <!-- 家园工坊列表,key:工坊id,value:工坊数据 -->
        <workshopList Type="WORKSHOP_LIST" Flags="SERVER_ONLY" Persistent="true" />
        <!-- 家园工坊雇员列表,key:员工id,value:工坊雇员数据 -->
        <workshopEmployee Type="WORKSHOP_EMPLOYEE_LIST" Flags="SERVER_ONLY" Persistent="true" />
	</Properties>

    <Implements/>

    <ClientMethods>
        <!-- 同步工坊数据 -->
        <RetGetWorkshopStatus>
            <Arg> WORKSHOP_LIST </Arg> <!-- WorkshopList,工坊列表 -->
        </RetGetWorkshopStatus>

        <!-- 购买工坊回复 -->
        <RetBuyWorkshop>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UINT </Arg> <!-- furnitureID,工坊ID -->
        </RetBuyWorkshop>

        <!-- 工坊升级回复 -->
        <RetWorkshopUpgrade>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> BUILDING_INFO </Arg> <!-- BuildingInfo,建筑信息 -->
            <Arg> WORKSHOP_STATUS </Arg> <!-- WorkshopStatus,工坊状态 -->
        </RetWorkshopUpgrade>

        <!-- 添加工坊生产队列回复 -->
        <RetWorkshopAddProductionQueue>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> UINT </Arg> <!-- recipeID,配方ID -->
            <Arg> WORKSHOP_STATUS </Arg> <!-- WorkshopStatus,工坊状态 -->
        </RetWorkshopAddProductionQueue>

        <!-- 移除工坊生产队列回复 -->
        <RetWorkshopRemoveProductionQueue>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> WORKSHOP_STATUS </Arg> <!-- WorkshopStatus,工坊状态 -->
        </RetWorkshopRemoveProductionQueue>

        <!-- 工坊收获回复 -->
        <RetWorkshopHarvest>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> WORKSHOP_STATUS </Arg> <!-- WorkshopStatus,工坊状态 -->
        </RetWorkshopHarvest>

        <!-- 指派工坊雇员回复 -->
        <RetAssignWorkshop>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UUID </Arg> <!-- employeeUID,雇员唯一ID -->
            <Arg> UINT </Arg> <!-- buildingID,建筑ID -->
            <Arg> UINT </Arg> <!-- index,工位ID -->
        </RetAssignWorkshop>

        <!-- 移除工坊雇员回复 -->
        <RetRemoveWorkshopEmployee>
            <Arg> UINT </Arg> <!-- ResultCode,结果 -->
            <Arg> UUID </Arg> <!-- employeeUID,雇员唯一ID -->
        </RetRemoveWorkshopEmployee>

        <!-- 工坊雇员列表回复 -->
        <RetWorkshopEmployeeList>
            <Arg> WORKSHOP_EMPLOYEE_LIST </Arg> <!-- WorkshopEmployeeList,工坊雇员列表 -->
        </RetWorkshopEmployeeList>

        <!-- 工坊通知 -->
        <OnMsgWorkshopNotify>
        </OnMsgWorkshopNotify>

    </ClientMethods>

    <ServerMethods>
        <!-- 同步工坊数据 -->
        <ReqGetWorkshopStatus> <Exposed />
        </ReqGetWorkshopStatus>

        <!-- 购买工坊 -->
        <ReqBuyWorkshop> <Exposed />
            <Arg> UINT </Arg> <!-- furnitureID,工坊ID -->
        </ReqBuyWorkshop>

        <!-- 工坊升级 -->
        <ReqWorkshopUpgrade> <Exposed />
            <Arg> BUILDING_INFO </Arg> <!-- BuildingInfo,建筑信息 -->
        </ReqWorkshopUpgrade>

        <!-- 添加工坊生产队列 -->
        <ReqWorkshopAddProductionQueue> <Exposed />
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> UINT </Arg> <!-- recipeID,配方ID -->
            <Arg> UINT </Arg> <!-- num,数量 -->
        </ReqWorkshopAddProductionQueue>

        <!-- 移除工坊生产队列 -->
        <ReqWorkshopRemoveProductionQueue> <Exposed />
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> UINT </Arg> <!-- queueIndex,队列索引 -->
        </ReqWorkshopRemoveProductionQueue>

        <!-- 工坊收获 -->
        <ReqWorkshopHarvest> <Exposed />
            <Arg> UINT </Arg> <!-- buildingID,工坊ID -->
            <Arg> UINT </Arg> <!-- queueIndex,队列索引 -->
        </ReqWorkshopHarvest>

        <!-- 指派工坊雇员 -->
        <ReqAssignWorkshop> <Exposed />
            <Arg> UUID </Arg> <!-- employeeUID,雇员唯一ID -->
            <Arg> UINT </Arg> <!-- buildingID,建筑ID -->
            <Arg> UINT </Arg> <!-- index,工位ID -->
        </ReqAssignWorkshop>
        
        <!-- 移除工坊雇员 -->
        <ReqRemoveWorkshopEmployee> <Exposed />
            <Arg> UUID </Arg> <!-- employeeUID,雇员唯一ID -->
        </ReqRemoveWorkshopEmployee>

        <!-- 工坊雇员列表 -->
        <ReqWorkshopEmployeeList> <Exposed />
        </ReqWorkshopEmployeeList>

    </ServerMethods>
</root>
