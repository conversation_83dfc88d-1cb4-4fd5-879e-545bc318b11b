<root>	
    <Properties>
	</Properties>

	<ClientMethods>
    </ClientMethods>

    <ServerMethods>
        <OnCreateSpace>
            <!--1 CreateSpaceInfoList -->
            <Arg>CreateSpaceInfoList</Arg>
            <!--2 exParam -->
            <Arg>CreateDungeonExParam</Arg>
        </OnCreateSpace>

        <CreatePlaneSpace>
            <!-- AvatarActorMailbox -->
            <Arg> mailbox </Arg>
            <!-- MapID -->
            <Arg> int </Arg>
            <!-- exParam -->
            <Arg> none </Arg>
        </CreatePlaneSpace>

        <RetQualitySpaceLogic>
            <!--1 MapID -->
            <Arg> int </Arg>
            <!--1 ProcessID -->
            <Arg> string </Arg>
            <!--1 AvatarID  -->
            <Arg> string </Arg>                        
        </RetQualitySpaceLogic>

        <RetGMGetSpaceProcessID>
            <!--1 ProcessID -->
            <Arg> string </Arg>
            <!--2 AvatarID  -->
            <Arg> string </Arg>                        
        </RetGMGetSpaceProcessID>
    </ServerMethods>
</root>
