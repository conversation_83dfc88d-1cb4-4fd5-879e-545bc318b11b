<root>
    <Implements>
    </Implements>
    <!-- triggerRegistered(已注册Trigger),结构:triggerRegistered->customID->triggerKey(system_key) -->
    <!-- triggerTempCounter(接取计数Counter),结构:triggerTempCounter->triggerKey(system_key)->customID->conditionIndex->value -->
    <!-- triggerForeverCounter 记录出生计数Trigger -->
    <!-- triggerForeverCounter -> triggerKey(customID_conditionIndex) -> value -->

    <Properties>
        <triggerList Type="dict" Key="UINT" Value="TRIGGER_MAP_BY_EVENT" Flags="SERVER_ONLY" Persistent="true" /> <!--任务系统用-->
        <triggerRegistered  Type="TRIGGER_REGISTETED_DICT" Flags="SERVER_ONLY" Persistent="true"/>  <!--已注册Trigger-->
        <triggerTempCounter Type="TRIGGER_TEMP_DICT" Flags="SERVER_ONLY" Persistent="true"/>        <!--非永久Counter计数-->
        <triggerForeverCounter Type="TRIGGER_FOREVER_DICT" Flags="SERVER_ONLY" Persistent="true"/>  <!--永久Counter计数-->
    </Properties>

	<ClientMethods>
        <OnMsgSyncTriggerInfo>
            <!--1 temp -->
            <Arg> TRIGGER_TEMP_DICT </Arg>
            <!--2 forever -->
            <Arg> TRIGGER_FOREVER_DICT </Arg>
        </OnMsgSyncTriggerInfo>

        <OnMsgTriggerTempChange>
            <!--1 triggerKey -->
            <Arg> UINT </Arg>
            <!--2 customID -->
            <Arg> UINT </Arg>            
            <!--3 conditionIndex -->
            <Arg> UINT </Arg>
            <!--4 value -->
            <Arg> UINT </Arg>
        </OnMsgTriggerTempChange> 

        <OnMsgTriggerForeverChange>
            <!--1 triggerKey -->
            <Arg> UINT </Arg>
            <!--2 value -->
            <Arg> UINT </Arg>
        </OnMsgTriggerForeverChange> 

        <OnMsgTriggerTempClear>
            <!--1 triggerKey -->
            <Arg> UINT </Arg>            
            <!--2 customID -->
            <Arg> UINT </Arg>
        </OnMsgTriggerTempClear> 

        <OnMsgTriggerForeverClear>
            <!--1 triggerKey -->
            <Arg> UINT </Arg>
        </OnMsgTriggerForeverClear>

        <OnMsgTriggerCurrentRefresh>
            <!--1 triggerKey -->
            <Arg> UINT </Arg>
        </OnMsgTriggerCurrentRefresh>

        <OnMsgTriggerComplete>
            <!--1 systemID -->
            <Arg> UINT </Arg>
            <!--2 key -->
            <Arg> UINT </Arg>
            <!--3 trigger -->
            <Arg> UINT </Arg>            
        </OnMsgTriggerComplete>                  
	</ClientMethods>     

    <!-- Exposed 待处理 -->
	<ServerMethods>
	</ServerMethods>
</root>
