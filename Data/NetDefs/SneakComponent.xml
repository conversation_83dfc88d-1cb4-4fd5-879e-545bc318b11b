<?xml version="1.0" ?>
<root>
	<Properties>
        <npcWarningValue Type="DictStrFloat" Default="{}" Flags="OWN_CLIENT" Persistent="false"/> <!--key: npceid, value: warning value-->
        <npcWarningValid Type="NPC_WARNING_VALID" Flags="OWN_CLIENT" Persistent="false"/> <!--key: npceid, value: warning valid--> <!--用于记录客户端上报-->
        <npcSightValid Type="DictStrBool" Default="{}" Flags="SERVER_ONLY" Persistent="false"/>
        <trapExcelIDDict Type="dict" Key="string" Value="DictIntBool" Flags="SERVER_ONLY" Persistent="false"/> <!--key:npceid, value: key:trapExcelID, value:ignore-->
        <bInFollowTrap Type="int" Default="0" Flags="OWN_CLIENT" Persistent="false"/>
	</Properties>
	<Implements/>
	<ClientMethods>
        <OnMsgPlayerEnterTailFollowTrap>
            <!-- 进入的时间戳  -->
            <Arg> UINT </Arg>
        </OnMsgPlayerEnterTailFollowTrap>
        
        <OnMsgPlayerLeaveTailFollowTrap>
            <!-- 失败的时间戳  -->
            <Arg> UINT </Arg>
        </OnMsgPlayerLeaveTailFollowTrap>

        <OnMsgNpcWarningChange>
            <Arg> ENTITY_ID </Arg>
            <Arg> float </Arg>
        </OnMsgNpcWarningChange>

        <OnMsgSingleNpcWarningAllRemove>
            <Arg> ENTITY_ID </Arg>
        </OnMsgSingleNpcWarningAllRemove>

        <OnMsgPlayerEnterWarningTrap> <!--客户端将其视作上报视野的开端，之后监听npcWarningValid-->
            <Arg> ENTITY_ID </Arg> <!-- npcEid-->
        </OnMsgPlayerEnterWarningTrap>

        <OnMsgPlayerLeaveWarningTrap> <!--客户端将其视作停止上报视野的开端，之后监听npcWarningValid-->
            <Arg> ENTITY_ID </Arg> <!-- npcEid-->
        </OnMsgPlayerLeaveWarningTrap>

        <OnMsgAllNpcWarningAllRemove> <!--一般用于玩家退出相關副本-->
        </OnMsgAllNpcWarningAllRemove>
        
        <OnMsgPlayerDetectByNpc>
             <Arg> ENTITY_ID </Arg> <!-- npcEid-->
        </OnMsgPlayerDetectByNpc>

        <RetReportNpcSight>
            <Arg> Result </Arg>
        </RetReportNpcSight>
	</ClientMethods>
	<ServerMethods>
        <ReqReportNpcSight> <Exposed/>
            <Arg> ENTITY_ID </Arg>
            <Arg> bool </Arg>
        </ReqReportNpcSight>
	</ServerMethods>
</root>