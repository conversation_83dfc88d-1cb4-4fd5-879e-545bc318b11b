<root>
    <Properties>
        <GroupInsID Type="string" Flags="ALL_CLIENTS" Default=""/>
        <GroupInsType Type="int" Flags="ALL_CLIENTS" Default="1"/>
        <GroupBelongType Type="int" Flags="ALL_CLIENTS" Default="1"/>
        <CentralPos Type="PVector3" Flags="ALL_CLIENTS" Default="{}"/>
        <GroupMemberInfos Type="GroupMemberInfos" Flags="ALL_CLIENTS"/>
    </Properties>

    <Implements>
        <Interface>ActorBase</Interface>
    </Implements>

    <ClientMethods>
        <OnMsgInteractorEvent>
            <Arg>string</Arg>
            <Arg>string</Arg>
            <Arg>string</Arg>
            <Arg>InteractorEventSyncParam</Arg>
        </OnMsgInteractorEvent>
        <OnMsgInteractorMsg>
            <Arg>string</Arg>
            <Arg>string</Arg>
        </OnMsgInteractorMsg>
    </ClientMethods>
</root>
