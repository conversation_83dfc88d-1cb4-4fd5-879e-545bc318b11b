<?xml version="1.0" ?>
<root>
	<Properties>
        <!--玩家的扮演状态，其中true为扮演中, 要在头顶显示，非存盘属性-->
        <rpGameStatus Type="bool" Default="false" Flags="ALL_CLIENTS"/>
        <!--已解锁的身份-->
        <rpGameUnlockIdentity Type="DictIntInt" Default="{}" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!--已解锁的章节信息-->
        <rpGameUnlockChapters Type="DictIntInt" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!--第几章节的剧情-->
        <rpGameChapterID Type="int" Default="0" Flags="OWN_INITIAL_ONLY" Persistent="true"/>
        <!--玩家当前选择的身份-->
        <rpGameSelectIdentity Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <!--玩家当前的沙盘编号-->
        <rpGameMapID Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <!--本局所有层各个事件对应的分数，格式为{key: 事件编号，value: 分数}-->
        <rpGameEventScore Type="RPGameEventScoreDict" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--玩家当前所在的位置( x * 10000 + y)-->
        <rpGamePos Type="int" Default="0" Flags="SERVER_ONLY" Persistent="true"/>
        <!--拥有的各种货币数量, 格式为{key: 货币编号, value: 货币数量}-->
        <rpGameMoney Type="DictIntInt" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--拥有的封印物，格式为{key: 物品编号，value: 物品数量}-->
        <rpGameItems Type="DictIntInt" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--棋盘的各个格子信息, 格式为{key: x * 10000 + y, value: 格子信息}-->
        <rpGameGrids Type="RPGameGridDict" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--棋盘的格子对应的商店物品列表-->
        <rpGameShopItems Type="RPGameGridShopItems" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--各个对话框是否发放过奖励，防止重复领取奖励-->
        <rpGameRewardDialogue Type="DictIntInt" Default="{}" Flags="SERVER_ONLY" Persistent="true"/>
        <!--当前对话框的编号，每个格子的当前对话框不确定的（有的选项会打开新的对话框），通关副本后根据对话框+选项确定执行对应system action-->
        <rpGameDialogueID Type="int" Flags="SERVER_ONLY" Persistent="true"/>
        <!--当前选择的对话框选项，通关副本后根据对话框+选项确定执行对应system action，非存盘属性-->
        <rpGameOptionIndex Type="int" Flags="SERVER_ONLY"/>
        <!--当前事件触发的副本编号，当通关的副本编号等于rpGameDungeonID的时候，需要触发事件完成，非存盘属性-->
        <rpGameDungeonID Type="int" Flags = "SERVER_ONLY"/>
	</Properties>

	<Implements/>

	<ClientMethods>
        <!--退出沙盘的响应-->
        <RetRPGameQuit>
        </RetRPGameQuit>

        <!--移动的响应-->
        <RetRPGameMove>
            <Arg> int </Arg>            <!--移到的X坐标-->
            <Arg> int </Arg>            <!--移到的y坐标-->
            <Arg> int </Arg>            <!--对话框的编号，有可能为0（如重回已通关的某个格子)-->
        </RetRPGameMove>

        <!--选择某个选项的响应-->
        <RetRPGameSelect>
            <Arg> int </Arg>            <!--当前的对话框的编号-->
            <Arg> int </Arg>            <!--选项的索引-->
        </RetRPGameSelect>

        <!--商店购买的响应-->
        <RetRPGameBuy>
            <Arg> int </Arg>            <!--物品编号-->
            <Arg> DictIntInt </Arg>     <!--购买扣除后的货币对应的最新数量-->
        </RetRPGameBuy>

        <!--奖励的各种货币数量和封印物-->
        <OnMsgRPGameSyncItems>
            <Arg> DictIntInt </Arg>     <!--变更的货币对应的最加的数量-->
            <Arg> DictIntInt </Arg>     <!--变更的货币对应的最新数量-->
            <Arg> DictIntInt </Arg>     <!--新增的封印物-->
        </OnMsgRPGameSyncItems>

        <!--同步正在进行的章节信息和棋局信息-->
        <OnMsgSyncRPGameInfo>
            <Arg> int </Arg>            <!--章节编号-->
            <Arg> int </Arg>            <!--身份-->
            <Arg> DictIntInt </Arg>     <!--各种货币数量-->
            <Arg> DictIntInt </Arg>     <!--拥有的封印物-->
        </OnMsgSyncRPGameInfo>

        <!--同步新的棋局信息-->
        <OnMsgSyncRPGameMapInfo>
            <Arg> int </Arg>            <!--当前所在的沙盘编号-->
            <Arg> int </Arg>            <!--当前所在的格子（x * 10000 + y）-->
            <Arg> int </Arg>            <!--当前的对话框的编号-->
            <Arg> RPGameGridDict </Arg> <!--各个格子信息-->
        </OnMsgSyncRPGameMapInfo>

        <!--解锁新的章节通知-->
        <OnMsgUnlockChapter>
            <Arg> int </Arg>            <!--章节编号-->
        </OnMsgUnlockChapter>

        <!--同步已解锁的新身份-->
        <OnMsgRPGameIdentity>
            <Arg> int </Arg>            <!--身份编号-->
        </OnMsgRPGameIdentity>

        <!--更新某个格子信息-->
        <OnMsgRPGameUpdateGrid>
            <Arg> int </Arg>            <!--当前所在的格子（x * 10000 + y）-->
            <Arg> RPGameGridInfo </Arg> <!--格子信息-->
        </OnMsgRPGameUpdateGrid>

        <!--通知客户端打开非凡商店-->
        <OnMsgRPGameOpenShop>
            <Arg> ListInt </Arg>        <!--物品编号列表-->
        </OnMsgRPGameOpenShop>

        <!--结束本局沙盘-->
        <OnMsgRPGameEnd>
            <Arg> BOOL </Arg>           <!--true为成功，false为失败(如疯狂度达到100失败了)-->
            <Arg> DictIntInt </Arg>     <!--各种货币数量-->
            <Arg> DictIntInt </Arg>     <!--拥有的封印物-->
            <Arg> RPGameEventScoreDict </Arg> <!--各个事件选项的分数-->
        </OnMsgRPGameEnd>
	</ClientMethods>

	<ServerMethods>
        <!--开始沙盘的请求-->
        <ReqRPGameStart CD="2"> <Exposed/>
            <Arg> int </Arg>            <!--章节编号-->
            <Arg> int </Arg>            <!--身份-->
            <Arg> bool </Arg>            <!--是否新沙盘-->
        </ReqRPGameStart>

        <!--退出沙盘的请求-->
        <ReqRPGameQuit> <Exposed/>
        </ReqRPGameQuit>

        <!--移动的请求-->
        <ReqRPGameMove> <Exposed/>
            <Arg> int </Arg>            <!--移到的X坐标-->
            <Arg> int </Arg>            <!--移到的y坐标-->
        </ReqRPGameMove>

        <!--选择某个选项的请求-->
        <ReqRPGameSelect> <Exposed/>
            <Arg> int </Arg>            <!--选项的索引-->
        </ReqRPGameSelect>

        <!--购买封印物体的请求-->
        <ReqRPGameBuy> <Exposed/>
            <Arg> int </Arg>            <!--物品编号-->
        </ReqRPGameBuy>
	</ServerMethods>
</root>
