--[[
	当前只预加载一些很通用的公共库、常量等。 太靠近业务的代码不适合加到这里。
]]
kg_require "Data.Config.StringConst.StringConst"
kg_require "Shared.Const"
kg_require "Shared.Utils"

kg_require("Framework.KGFramework.KGCore.Delegates.LuaDelegate")
kg_require("Framework.KGFramework.KGCore.Delegates.LuaMulticastDelegate")
kg_require "Framework.KGFramework.KGUI.Core.UIPanel"
kg_require "Framework.KGFramework.KGUI.Core.UIComponent"
kg_require "Framework.KGFramework.KGUI.Component.BackGround.UIComMaskAdaptive"
kg_require "Framework.KGFramework.KGUI.Component.BackGround.UIComMask"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIComTabItem"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIComSimpleTabList"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIComAccordionListTab"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UITabListItem"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIComAccordionList"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIAccordionList"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIAccordionListTab"
kg_require "Framework.KGFramework.KGUI.Component.Tab.ComTabListR"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UIComTabList"
kg_require "Framework.KGFramework.KGUI.Component.Tab.UITabList"
kg_require "Framework.KGFramework.KGUI.Component.Tab.ComTabFoldSubNew"
kg_require "Framework.KGFramework.KGUI.Component.Tab.ComTabR"
kg_require "Framework.KGFramework.KGUI.Component.Tab.ComMutiMenuNew"
kg_require "Framework.KGFramework.KGUI.Component.Tab.ComTabFoldParentNew"
kg_require "Framework.KGFramework.KGUI.Component.Indicator.UIComPageNum"
kg_require "Framework.KGFramework.KGUI.Component.Indicator.UIComPagePointList"
kg_require "Framework.KGFramework.KGUI.Component.Select.UIComDropDown"
kg_require "Framework.KGFramework.KGUI.Component.Select.UIComDropDownItem"
kg_require "Framework.KGFramework.KGUI.Component.Select.ComComBox"
kg_require "Framework.KGFramework.KGUI.Component.Bar.UIComNumberSlider"
kg_require "Framework.KGFramework.KGUI.Component.Bar.UIComBar"
kg_require "Framework.KGFramework.KGUI.Component.Bar.UIComSlider"
kg_require "Framework.KGFramework.KGUI.Component.CommonLogic.UIComDragWidget"
kg_require "Framework.KGFramework.KGUI.Component.CommonLogic.UIDragOperation"
kg_require "Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyItem"
kg_require "Framework.KGFramework.KGUI.Component.Tag.UIComCurrencyList"
kg_require "Framework.KGFramework.KGUI.Component.Input.ComNumInput_Panel"
kg_require "Framework.KGFramework.KGUI.Component.Input.UIComInputBox"
kg_require "Framework.KGFramework.KGUI.Component.Input.ComNumInput_Item"
kg_require "Framework.KGFramework.KGUI.Component.Input.UIComTextSearchBox"
kg_require "Framework.KGFramework.KGUI.Component.UIListView.UIListView"
kg_require "Framework.KGFramework.KGUI.Component.UIListView.UISimpleList"
kg_require "Framework.KGFramework.KGUI.Component.UIListView.UIListItem"
kg_require "Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBox"
kg_require "Framework.KGFramework.KGUI.Component.CheckBox.UIComCheckBoxItem"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComButton"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComButtonItem"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComBackTitle"
kg_require "Framework.KGFramework.KGUI.Component.Button.UITitleButton"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIButton"
kg_require "Framework.KGFramework.KGUI.Component.Button.ComBtnBackNew"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComSwitchBtn"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComSortBtn"
kg_require "Framework.KGFramework.KGUI.Component.Button.UIComText"
kg_require "Framework.KGFramework.KGUI.Component.Popup.UIComBoxFrame"
kg_require "Framework.KGFramework.KGUI.Component.Panel.UIComFrame"
kg_require "Framework.KGFramework.KGUI.Component.Panel.UIPanelFrame"
kg_require "Framework.KGFramework.KGUI.Component.UITreeView.UITreeView"
kg_require "Framework.KGFramework.KGUI.Component.UITreeView.UITreeItem"
kg_require "Framework.KGFramework.KGUI.Component.UITreeView.UITreeViewData"