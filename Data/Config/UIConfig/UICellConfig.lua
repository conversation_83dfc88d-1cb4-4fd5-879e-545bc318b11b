-- luacheck: push ignore
TaskTag = "TaskTag"
ReportTargetPage = "ReportTargetPage"
ReportReasonPage = "ReportReasonPage"
ReportInfoPage = "ReportInfoPage"
ReportImagePage = "ReportImagePage"

EquipStrengthenPage = "EquipStrengthenPage"
EquipAttributeChangePage = "EquipAttributeChangePage"
EquipEntryPage = "EquipEntryPage"
EquipApplyEntryPage = "EquipApplyEntryPage"
EquipPropAttribute_Item = "EquipPropAttribute_Item"
EquipTagText_Item = "EquipTagText_Item"
EquipmentEnhancePage = "EquipmentEnhancePage"
EquipmentReformPage = "EquipmentReformPage"


ItemNml = "ItemNml"
ComSelectedLight = "ComSelectedLight"
ItemTipsEquipCondition = "ItemTipsEquipCondition"
ItemTipsEquipStory = "ItemTipsEquipStory"
ItemTipsEquipSpirituality = "ItemTipsEquipSpirituality"
ItemTipsEquipSpecial = "ItemTipsEquipSpecial"
ItemTipsEquipSuit = "ItemTipsEquipSuit"

GuildOutApplyPage = "GuildOutApplyPage"
GuildOutResponseOtherPage = "GuildOutResponseOtherPage"
GuildOutResponseCreatorPage = "GuildOutResponseCreatorPage"
GuildOutCreatePage = "GuildOutCreatePage"
GuildInMemberList = "GuildInMemberList"
GuildInMemberPage = "GuildInMemberPage"
GuildInMemberApply = "GuildInMemberApply"
GuildInMemberInvite = "GuildInMemberInvite"
GuildInBuildMain = "GuildInBuildMain"
GuildHomeWelfarePage = "GuildHomeWelfarePage"
GuildInActivity = "GuildInActivity"
GuildInStructure = "GuildInStructure"
GuildInside_StructPage = "GuildInside_StructPage"

GuildInside_Entrance_Page = "GuildInside_Entrance_Page"
GuildInside_Bg_Daylight = "GuildInside_Bg_Daylight"
GuildInside_Bg_Dusk = "GuildInside_Bg_Dusk"
GuildInside_Bg_Night = "GuildInside_Bg_Night"
GuildInside_ActivityPage = "GuildInside_ActivityPage"
GuilsInside_WelfarePage = "GuilsInside_WelfarePage"
GuildInside_DetailPage = "GuildInside_DetailPage"
GuildInside_ApplyPage = "GuildInside_ApplyPage"
GuildInside_ViewPage = "GuildInside_ViewPage"

RankingItem_Personal = "RankingItem_Personal"
RankingItem_Guild = "RankingItem_Guild"
RankingItem_Dungeon = "RankingItem_Dungeon"
RankingDungeonHead = "RankingDungeonHead"
RankingText = "RankingText"
RankingTitle_Personal= "RankingTitle_Personal"

GuildBattleOutVS_Item = "GuildBattleOutVS_Item"
GuildBattleOutRanking_Item = "GuildBattleOutRanking_Item"
GuildBattleOutResultRanking_Item = "GuildBattleOutResultRanking_Item"
GuildBattleOutResultLine_Item = "GuildBattleOutResultLine_Item"

PlayerFriendGroupPost_Item = "PlayerFriendGroupPost_Item"
FriendGroupTopic = "FriendGroupTopic"
HistoryMomentsPage = "HistoryMomentsPage"

FashionChange_Widget = "FashionChange_Widget"
FashionWardrobe_Widget = "FashionWardrobe_Widget"
FashionSceneCustom_Widget = "FashionSceneCustom_Widget"
FashionAchieve_Widget = "FashionAchieve_Widget"
FashionAround_Widget = "FashionAround_Widget"

SceneCustom_Anim_Page = "SceneCustom_Anim_Page"
SceneCustom_Component_Page = "SceneCustom_Component_Page"
SceneCustom_Environment_Page = "SceneCustom_Environment_Page"

EleCoreTreeNodeItem = "EleCoreTreeNodeItem"
SkillCustomizerPeculiarityComponent = "SkillCustomizerPeculiarityComponent"
SkillRoutineAllSkill_Sub = "SkillRoutineAllSkill_Sub"
SkillRoutineSwitch = "SkillRoutineSwitch"

RedPacket_SelRP = "P_SendRedPacket"
RedPacket_GiveMoney = "P_RPMoney"
RedPacket_GiveSkin = "P_RPSkin"
RedPacket_SelectSkin = "P_SelSkin"
HUDRedPacketBtn = "P_HUDRP"
RedPacket_Record = "P_RPHistory"
RedPacket_AllCurRP = "P_AllCurRP"


PVPMatch_SeasonList_Panel = "PVPMatch_SeasonList_Panel"
PVPMatch_Data_Panel = "PVPMatch_Data_Panel"
PVPMatch_Record_Panel = "PVPMatch_Record_Panel"
PVPMatch_Season_Panel = "PVPMatch_Season_Panel"


StatisticsCure = "StatisticsCure"
StatisticsData = "StatisticsData"

UIListViewExample = "UIListViewExample"
TreeListExample = "TreeListExample"
UICurrentcyWidget = "UICurrentcyWidget"
TabListExample = "TabListExample"
UITempComBtn = "UITempComBtn"
UITempComBtnBackArrow = "UITempComBtnBackArrow"
SettlementDungeonAwardAuction = "SettlementDungeonAwardAuction"
SettlementDungeonAwardTips = "SettlementDungeonAwardTips"
SettlementDungeonAwardAssignment = "SettlementDungeonAwardAssignment"
Scene3DDisplay = "Scene3DDisplay"
ReminderExplorationResult_Panel = "ReminderExplorationResult_Panel"
Reminder_GoddessPuzzle_Widget = "Reminder_GoddessPuzzle_Widget"

HUD_TeamGroup_Container = "HUD_TeamGroup_Container"
HUD_Team = "HUD_Team"
HUD_TeamMemberItem = "HUD_TeamMemberItem"
HUD_TeamMemberMarkMenu = "HUD_TeamMemberMarkMenu"
HUD_SceneMarkMenu = "HUD_SceneMarkMenu"
HUD_Group = "HUD_Group"
HUD_GroupTeamItem = "HUD_GroupTeamItem"
HUD_MiniGame_ItemCount = "HUD_MiniGame_ItemCount"
HUD_Task_Panel = "HUD_Task_Panel"

SeasonLastRankPage = "SeasonLastRankPage"
SeasonLastDataPage = "SeasonLastDataPage"

CollectiblesPhotoPage = "CollectiblesPhotoPage"
CollectiblesSealedPage = "CollectiblesSealedPage"
CollectiblesPropPage = "CollectiblesPropPage"
CollectiblesNPCPage = "CollectiblesNPCPage"
CollectiblesVideoPage = "CollectiblesVideoPage"
CollectiblesLettersPage = "CollectiblesLettersPage"
PlotRecapMainTask = "PlotRecapMainTask"
PlotRecapMainLine = "PlotRecapMainLine"
PlotRecapBranchTaskList = "PlotRecapBranchTaskList"
PlotRecapBranchInfoList = "PlotRecapBranchInfoList"
ComStatus = "ComStatus"
ComSortBtn = "ComSortBtn"
ComBtnBackArrow = "ComBtnBackArrow"
ComBtnClose = "ComBtnClose"
UIComButton = "UIComButton"
UIComEmptyConent = "UIComEmptyConent"
UIComMask = "UIComMask"
HUD1v1Countdown = "HUD1v1Countdown"
HUDPVPChampion = "HUDPVPChampion"
HUDChampionMatchBtn = "HUDChampionMatchBtn"
HUDChampionTeamBtn = "HUDChampionTeamBtn"
HUDChampionPrepareInfo = "HUDChampionPrepareInfo"
PVP_ChampionMatch_Sub_Page = "PVP_ChampionMatch_Sub_Page"
PVP_ChampionMatchRank_Sub_Page = "PVP_ChampionMatchRank_Sub_Page"
PVP_ChampionIntro_Sub = "PVP_ChampionIntro_Sub"
UIComBar = "UIComBar"
ComBtnIcon = "ComBtnIcon"
DropDownExample = "DropDownExample"
ComDIYText = "ComDIYText"
ComFirstBigText = "ComFirstBigText"
SearchBoxPage = "SearchBoxPage"
ButtonExamplePage = "ButtonExamplePage"
HUDSocialAction_Item = "HUDSocialAction_Item"
HUDSocialActionSelectPlayer = "HUDSocialActionSelectPlayer"
HUDSocialClooectBtn = "HUDSocialClooectBtn"
InputExample = "InputExample"
ItemQuality5 = "ItemQuality5"
ItemQuality6 = "ItemQuality6"
ItemQuality7 = "ItemQuality7"
ItemQuality9 = "ItemQuality9" -- ����ǵ��߿ɻ�õ���Ч
ComPopupL = "ComPopupL"
ComPopupM = "ComPopupM"
ComPopupS = "ComPopupS"
HUDSocialActionBtn = "P_HUDSocialActionBtn"
ComCurrencyList = "ComCurrencyList"
ChatSmall_Page = "ChatSmall_Page"
ChatContent_Page = "ChatContent_Page"
ChatDiscloseBg = "ChatDiscloseBg"
ChatBubble_TeamRecruitLMsg = "ChatBubble_TeamRecruitLMsg"
ChatExpressionMsg = "ChatExpression"
ChatRoom_RoomList_Page = "ChatRoom_RoomList_Page"
ChatRoom_RoomChat_Page = "ChatRoom_RoomChat_Page"
PartnerFather = "P_PartnerFather"
GuildTipsOut = "GuildTipsOut"
GuildHelpMsg = "GuildHelp"
RedPacketMsg = "WBP_RedPacket"
GuildResponseInviteMsg = "GuildResponseInvite"
ChatBarrage = "ChatBarrage"
OtherExample = "OtherExample"
HUDChat = "P_HUDChat"
WriteMessage = "WriteMessage"
ScreenEffect_Smoke = "ScreenEffect_Smoke"

Mail_Page = "Mail_Page"
MailMask = "MailMask"
MailGuildPlayerName = "MailGuildPlayerName"
MailPhoto = "MailPhoto"
MailVoice = "MailVoice"

LibCountDown = "LibCountDown"
LibEquipment = "LibEquipment"
LibLine = "LibLine"
LibText = "LibText"
LibSealedBreak = "LibSealedBreak"
SealedBuff = "SealedBuff"
ItemDetailList = "ItemDetailList"
Lib_Sealed_XMat = "Lib_Sealed_XMat"

PlayerBackpack = "P_ReplaceableEquip"
HUD_WeatherBtn = "P_HUDWeatherBtn"
HUD_Weather = "P_HUDWeather"
HUD_EXP = "P_HUDEXPBar"
HUD_QuickUse = "HUD_QuickUse"
HUD_PlayerInfo = "P_HUDPlayerInfo"
ReminderPower_Widget = "ReminderPower_Widget"
HUDCameraBtn = "HUDCameraBtn"
HUDConstantDisplay = "HUDConstantDisplay"

StarConnectLine = "StarConnectLine"
StarLight = "StarLight"
StarLightHole = "StarLightHole"

BookImageS = "BookImageS"
BookImageXL = "BookImageXL"
BookText = "BookText"
BookTitle = "BookTitle"

HUDBuffList = "HUDBuffList"
HUDActivityList = "HUDActivityList"
HUDOffCar = "HUDOffCar"

HUDComNoticeBtn = "P_HUDActivityNotice"
HUDStatusText = "HUDStatusText"
HUDAuctionBtn = "P_HUDAuction"
HUDComDownBtn = "P_HUDDownload"
HUDStatusBar = "P_SkillProgressBar"
HUDSkillRouletteTotal = "HUDSkillRouletteTotal"
HUDSkillRoulette_Mobile = "HUDSkillRoulette_Mobile"
HUDSkillRoulette_PC = "HUDSkillRoulette_PC"
HUDSkillSimpleRoulette_PC = "HUDSkillSimpleRoulette_PC"
HUDSkillSimpleRoulette_Mobile = "HUDSkillSimpleRoulette_Mobile"
HUDSkillJobMechanismApprentice = "HUDSkillJobMechanismApprentice"
HUDSkillJobMechanismArbiter = "HUDSkillJobMechanismArbiter"
HUDSkillJobMechanismFool = "HUDSkillJobMechanismFool"
HUDSkillJobMechanismSun = "HUDSkillJobMechanismSun"
HUDSkillJobMechanismUtopian = "HUDSkillJobMechanismUtopian"
HUDSkillJobMechanismWarrior = "HUDSkillJobMechanismWarrior"
P_HUDSimpleTask = "P_HUDSimpleTask"
HUDJoyStick = "HUDJoyStick"

ReminderDropItem = "ReminderDropItem"
ReminderMsgNormalItem = "ReminderMsgNormalItem"
ReminderMsgLightItem = "ReminderMsgLightItem"

-- Reminder
ReminderMsgNormal_Widget = "ReminderMsgNormal_Widget"
ReminderMsgLight_Widget = "ReminderMsgLight_Widget"
ReminderDropList_Widget = "ReminderDropList_Widget"
ReminderGetSkill_Widget = "ReminderGetSkill_Widget"
ReminderUpGrade_Widget = "ReminderUpGrade_Widget"
ReminderRegion_Widget = "ReminderRegion_Widget"
ReminderUnlock_Widget = "ReminderUnlock_Widget"
ReminderBossStats_Widget = "ReminderBossStats_Widget"
ReminderAchieve_Widget = "ReminderAchieve_Widget"
ReminderPVPQuickInteract_Widget = "ReminderPVPQuickInteract_Widget"
ReminderDungeonTips_Widget = "ReminderDungeonTips_Widget"
ReminderDungeonCaution_Widget = "ReminderDungeonCaution_Widget"
ReminderDungeonSkill_Widget = "ReminderDungeonSkill_Widget"
ReminderDungeonResult_Widget = "ReminderDungeonResult_Widget"
ReminderCD_Widget = "ReminderCD_Widget"
ReminderDoubleHint_Widget = "ReminderDoubleHint_Widget"
PVPDoubleReminder_Widget = "PVPDoubleReminder_Widget"
ReminderPVPInteract_Widget = "ReminderPVPInteract_Widget"
ReminderPVPInfo_Widget = "ReminderPVPInfo_Widget"
ReminderPVPWarn_Widget = "ReminderPVPWarn_Widget"
ReminderPVPStatus_Widget = "ReminderPVPStatus_Widget"
ReminderChallenges_Widget = "ReminderChallenges_Widget"
ReminderDungeonWarning_Widget = "ReminderDungeonWarning_Widget"
ReminderTaskFinish_Widget = "ReminderTaskFinish_Widget"
ReminderMission_Widget = "ReminderMission_Widget"
ReminderDungeonCDM_Widget = "ReminderDungeonCDM_Widget"
ReminderLike_Widget = "ReminderLike_Widget"
ReminderTaskChapter_Widget = "ReminderTaskChapter_Widget"
ReminderFateContract_Widget = "ReminderFateContract_Widget"
ReminderTeamAssistance_Widget = "ReminderTeamAssistance_Widget"
ReminderInteractResult_Widget = "ReminderInteractResult_Widget"
ReminderExplorationResult_Widget = "ReminderExplorationResult_Widget"
ReminderExplorationStart_Widget = "ReminderExplorationStart_Widget"
ReminderNewAppearance_Widget = "ReminderNewAppearance_Widget"
ReminderEquipLimitUp_Widget = "ReminderEquipLimitUp_Widget"
ReminderEquipSpiritGroup_Widget = "ReminderEquipSpiritGroup_Widget"
ReminderEquipSpiritStorage_Widget = "ReminderEquipSpiritStorage_Widget"

-- Marquee
ChatMarqueeItemGray = "ChatMarqueeItemGray"
ChatMarqueeItemPurple = "ChatMarqueeItemPurple"
ChatMarqueeItemGold = "ChatMarqueeItemGold"
AnnouncementMarqueeItem = "AnnouncementMarqueeItem"

-- TarotTeam
TarotTeamDsaplay_Add_Item = "TarotTeamDsaplay_Add_Item"
TarotTeamManageBtn_Item = "TarotTeamManageBtn_Item"
TarotTeamWindowTitle = "TarotTeamWindowTitle"

-- Sealed
Sealed_Normal_Sub = "Sealed_Normal_Sub"
Sealed_Special_Sub = "Sealed_Special_Sub"

--12V12
HUD_PVP12V12 = "HUD_PVP12V12"
HUD_PVP12V12Magic = "HUD_PVP12V12Magic"

--3V3
HUD3v3 = "HUD3v3"
HUDPVPStatsBtn = "HUDPVPStatsBtn"
HUDPVPLikeBtn = "HUDPVPLikeBtn"
HUDPVPContinuousKill = "HUDPVPContinuousKill"

-- mount
FashionVehicle_Widget = "FashionVehicle_Widget"

-- danceQTE
DanceTap_Widget = "DanceTap_Widget"
DanceHold_Widget = "DanceHold_Widget"
DanceFlick_Widget = "DanceFlick_Widget"

StatisticsDataItem = "StatisticsDataItem"
StatisticsCureItem = "StatisticsCureItem"

-- DialogueUIType
DialogueUITypeAside = "DialogueUITypeAside"
DialogueUITypeCaption = "DialogueUITypeCaption"
DialogueUITypeCSStyle = "DialogueUITypeCSStyle"
Dialogue_Debugger = "Dialogue_Debugger"
-- RolePlay
RolePlayGameHUD_Info_Page = "RolePlayGameHUD_Info_Page"

-- GvG
GvG_Statistic_Map = "GvG_Statistic_Map"
GvG_BattleStatistic_Rank_Sub = "GvG_BattleStatistic_Rank_Sub"


-- QTE

AshQTEBtn_2_Item = "AshQTEBtn_2_Item"

-- UIAppearance
UIAppearance_AvatarFrame = "AvatarFrame_Arrodes"
UIAppearance_TeamFrame = "UIAppearance_TeamFrame"
UIAppearance_GroupFrame = "UIAppearance_GroupFrame"
UIAppearance_TeamMistVx = "UIAppearance_TeamMistVx"
UIAppearance_GroupVX = "UIAppearance_GroupVX"

HUDPVECountDown = "HUDPVECountDown"



-- Photograph
Photograph_LensPage = "Photograph_LensPage"
Photograph_MovementPage = "Photograph_MovementPage"
Photograph_FilterPage = "Photograph_FilterPage"
Photograph_DisplayPage = "Photograph_DisplayPage"
Photograph_TemplatePage = "Photograph_TemplatePage"
Photograph_CompositionGrid = "Photograph_CompositionGrid"
PhotographEdit_Sticker = "PhotographEdit_Sticker"

---AutoGenerateTag
---@field res string （必填）资源路径
---@field cache boolean （选填）是否进入公共缓存池
---@field auth string (必填)程序负责
---@field classpath string （选填）不填会用资源上的脚本，填了会覆盖资源上的脚本
---@field PreloadResMap table (选填) 预加载资源路径
---@field parent string 所属ui完整路径
---@field parentui string 所属u
CellConfig = {
	[OtherExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/OtherExample/WBP_OtherExample.WBP_OtherExample_C", cache = false, auth = "huangjinbao"},
	[ComCurrencyList] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Tag/WBP_ComCurrencyList.WBP_ComCurrencyList_C", cache = false, auth = "huangjinbao"},
	[ComPopupS] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Popup/WBP_ComPopupS.WBP_ComPopupS_C", cache = false, auth = "huangjinbao"},
	[ComPopupM] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Popup/WBP_ComPopupM.WBP_ComPopupM_C", cache = false, auth = "huangjinbao"},
	[ComPopupL] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Popup/WBP_ComPopupL.WBP_ComPopupL_C", cache = false, auth = "huangjinbao"},
	[InputExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/InputExample/WBP_InputExample.WBP_InputExample_C", cache = false, auth = "huangjinbao"},
	[ButtonExamplePage] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/ButtonExample/WBP_ButtonExamplePage.WBP_ButtonExamplePage_C", cache = false, auth = "huangjinbao"},
	[SearchBoxPage] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/SearchBoxExample/WBP_SearchBoxPage.WBP_SearchBoxPage_C", cache = false, auth = "huangjinbao"},
	[ComFirstBigText] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Tools/WBP_ComFirstBigText.WBP_ComFirstBigText_C", cache = false, auth = "huangjinbao"},
	[ComDIYText] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Tools/WBP_ComDIYText.WBP_ComDIYText_C", cache = false, auth = "huangjinbao"},
	[DropDownExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/DropDownExample/WBP_DropDownExample.WBP_DropDownExample_C", cache = false, auth = "huangjinbao"},
	[ComBtnIcon] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComBtnIcon.WBP_ComBtnIcon_C", cache = false, auth = "huangjinbao"},
	[UIComBar] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Bar/WBP_ComBar.WBP_ComBar_C", cache = false, auth = "huangjinbao"},
	[UIComMask] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Background/WBP_ComMask.WBP_ComMask_C", cache = false, auth = "huangjinbao"},
	[UIComEmptyConent] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Tools/WBP_ComEmpty.WBP_ComEmpty_C", cache = false, auth = "huangjinbao"},
	[UIComButton] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComBtn.WBP_ComBtn_C", cache = false, auth = "huangjinbao"},
	[ComBtnClose] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComBtnClose.WBP_ComBtnClose_C", cache = false, auth = "huangjinbao"},
	[ComStatus] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComStatus.WBP_ComStatus_C", cache = false, auth = "huangjinbao"},
	[ComSortBtn] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComSortBtn.WBP_ComSortBtn_C", cache = false, auth = "huangjinbao"},
	[ComBtnBackArrow] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Button/WBP_ComBtnBackArrow.WBP_ComBtnBackArrow_C", cache = false, auth = "huangjinbao"},
	[TabListExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/TabListExample/WBP_TabListExample.WBP_TabListExample_C", cache = false, auth = "huangjinbao"},
	[UICurrentcyWidget] = {res = "/Game/Arts/UI_2/Blueprint/CommonUI/Tag/WBP_ComCurrencyList.WBP_ComCurrencyList_C", cache = false, auth = "huangjinbao"},
	[TreeListExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/TreeListExample/WBP_TreeListExample.WBP_TreeListExample_C", cache = false, auth = "huangjinbao"},
	[UIListViewExample] = {res = "/Game/Arts/UI_2/Blueprint/UMGPreview/UMGPreviewPanel/ListExample/WBP_UIListViewExample.WBP_UIListViewExample_C", cache = false, auth = "huangjinbao"},
	[Scene3DDisplay] = {res = "/Game/Blueprint/Scene3DDisplay/Scene3DDisplay.Scene3DDisplay_C", auth = "yuanhanqing"},
	[LibCountDown] = {res = "/Game/Arts/UI_2/Blueprint/Lib/WBP_Lib_Countdown.WBP_Lib_Countdown_C", cache = false, auth = "yangcao"},
	[LibEquipment] = {res = "/Game/Arts/UI_2/Blueprint/Lib/WBP_Lib_Equipment.WBP_Lib_Equipment_C", cache = false, auth = "yangcao"},
	[LibLine] = {res = "/Game/Arts/UI_2/Blueprint/Lib/WBP_Lib_Line.WBP_Lib_Line_C", cache = false, auth = "yangcao"},
	[LibText] = {res = "/Game/Arts/UI_2/Blueprint/Lib/WBP_Lib_Text.WBP_Lib_Text_C", cache = false, auth = "yangcao"},
	[SealedBuff] = {res = "/Game/Arts/UI_2/Blueprint/Sealed/WBP_SealedBuff.WBP_SealedBuff_C", cache = false, auth = "yangcao"},
	[ItemDetailList] = {res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/WBP_ItemDetailListNew.WBP_ItemDetailListNew_C", cache = false, auth = "yangcao"},
	[Lib_Sealed_XMat] = {res = "/Game/Arts/UI_2/Blueprint/Lib/WBP_Lib_Sealed_XMat.WBP_Lib_Sealed_XMat_C", cache = false, auth = "yechengyin"},
	[TaskTag] = {
		res = "/Game/Arts/UI_2/Blueprint/Task/WBP_TaskTag.WBP_TaskTag_C",
		cache = false, auth = "yuyue13",
		classpath = "Gameplay.LogicSystem.Task.TaskTag"
	},
	[ItemNml] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ItemNml.WBP_ItemNml_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ItemNml"
	},

	[ItemTipsEquipCondition] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/Equip/WBP_Lib_Equipment_Condition.WBP_Lib_Equipment_Condition_C",
		cache = true,
		auth = "chenpengfei06",
	},
	
	[ItemTipsEquipStory] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/Equip/WBP_Lib_Equipment_Backstory.WBP_Lib_Equipment_Backstory_C",
		cache = true,
		auth = "chenpengfei06",
	},
	
	[ItemTipsEquipSpirituality] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/Equip/WBP_Lib_Equipment_Spiritual.WBP_Lib_Equipment_Spiritual_C",
		cache = true,
		auth = "chenpengfei06",
	},

	[ItemTipsEquipSpecial] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/Equip/WBP_Lib_Equipment_Special.WBP_Lib_Equipment_Special_C",
		cache = true,
		auth = "chenpengfei06",
	},
	
	[ItemTipsEquipSuit] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/ItemTips/Equip/WBP_Lib_Equipment_Suit.WBP_Lib_Equipment_Suit_C",
		cache = true,
		auth = "chenpengfei06",
	},
	
	[ComSelectedLight] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ComSelectedLight.WBP_ComSelectedLight_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ComSelectedLight"
	},
	[ItemQuality5] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ItemQuality5.WBP_ItemQuality5_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ItemQuality"
	},
	[ItemQuality6] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ItemQuality6.WBP_ItemQuality6_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ItemQuality"
	},
	[ItemQuality7] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ItemQuality7.WBP_ItemQuality7_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ItemQuality"
	},
	[ItemQuality9] = {
		res = "/Game/Arts/UI_2/Blueprint/Item/WBP_ItemQuality9.WBP_ItemQuality9_C",
		cache = true, auth = "yangcao",
		classpath = "Gameplay.LogicSystem.Item.NewUI.ItemQuality"
	},
	[EquipmentEnhancePage] = {
		res = "/Game/Arts/UI_2/Blueprint/Equipment_2/WBP_Equipment_Enhance_Page.WBP_Equipment_Enhance_Page_C",
		auth = "chenpengfei06",
	},
	[EquipmentReformPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Equipment_2/Forging/WBP_EquipmentForging_Page.WBP_EquipmentForging_Page_C",
		auth = "chenpengfei06",
	},
	[EquipStrengthenPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Equip/Strengthen/WBP_EquipStrengthenPage.WBP_EquipStrengthenPage_C",
		cache = false,auth = "yechengyin",
		classpath = "Gameplay.LogicSystem.Equip.Strengthen.EquipStrengthenPage"
	},
	[EquipAttributeChangePage] = {
		res = "/Game/Arts/UI_2/Blueprint/Equip/AttributeChange/WBP_EquipAttributeChangePage.WBP_EquipAttributeChangePage_C",
		cache = false,auth = "yechengyin",
		classpath = "Gameplay.LogicSystem.Equip.AttributeChange.EquipAttributeChangePage"
	},
	[EquipEntryPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Equip/Entry/WBP_EquipEntryPage.WBP_EquipEntryPage_C",
		cache = false,auth = "yechengyin",
		classpath = "Gameplay.LogicSystem.Equip.Entry.EquipEntryPage"
	},
	-- 先屏蔽，后续迭代后可以删除
	--[EquipApplyEntryPage] = {
	--	res = "/Game/Arts/UI_2/Blueprint/Equip/Apply/WBP_EquipApplyEntryPage.WBP_EquipApplyEntryPage_C",
	--	cache = false,auth = "yechengyin",
	--	classpath = "Gameplay.LogicSystem.Equip.Apply.EquipApplyEntryPage"
	--},
	[EquipPropAttribute_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/Equip/ChosePropPanel/WBP_EquipPropAttribute_Item.WBP_EquipPropAttribute_Item_C",
		cache = true,auth = "yechengyin",
	},
	[EquipTagText_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/Equip/WBP_EquipTagText_Item.WBP_EquipTagText_Item_C",
		cache = false,auth = "yechengyin",
	},
	[ReportTargetPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Report/WBP_ReportTargetPage.WBP_ReportTargetPage_C", 
		cache = false, auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Report.ReportTargetPage"
	},
	[ReportReasonPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Report/WBP_ReportReasonPage.WBP_ReportReasonPage_C", 
		cache = false, auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Report.ReportReasonPage"
	},
	[ReportInfoPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Report/WBP_ReportInfoPage.WBP_ReportInfoPage_C", 
		cache = false, auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Report.ReportInfoPage"
	},
	[ReportImagePage] = {
		res = "/Game/Arts/UI_2/Blueprint/Report/WBP_ReportImagePage.WBP_ReportImagePage_C", 
		cache = false, auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Report.ReportImagePage"
	},
    [GuildOutApplyPage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Outside_2/Apply/WBP_GuildOutApplyPage.WBP_GuildOutApplyPage_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Outside_2.Apply.GuildOutApplyPage"
    },
    [GuildOutResponseOtherPage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Outside_2/Response/WBP_GuildOutResponseOtherPage.WBP_GuildOutResponseOtherPage_C",
        cache = false, auth = "yecaifeng05",
    },
    [GuildOutResponseCreatorPage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Outside_2/Response/WBP_GuildOutResponseCreatorPage.WBP_GuildOutResponseCreatorPage_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Outside_2.Response.GuildOutResponseCreatorPage"
    },
    [GuildOutCreatePage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Outside_2/Create/WBP_GuildOutCreatePage.WBP_GuildOutCreatePage_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Outside_2.Create.GuildOutCreatePage"
    },
    [GuildInMemberPage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Member/WBP_GuildInMemberPage.WBP_GuildInMemberPage_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberPage"
    },
    [GuildInMemberList] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Member/WBP_GuildInMemberList.WBP_GuildInMemberList_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberList"
    },
    [GuildInMemberApply] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Member/WBP_GuildInMemberApply.WBP_GuildInMemberApply_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberApply"
    },
    [GuildInMemberInvite] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Member/WBP_GuildInMemberInvite.WBP_GuildInMemberInvite_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Member.GuildInMemberInvite"
    },
    [GuildInBuildMain] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Build/WBP_GuildInBuildMain.WBP_GuildInBuildMain_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Build.GuildInBuildMain"
    },
    [GuildInStructure] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Structure/WBP_GuildInStructure.WBP_GuildInStructure_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Structure.GuildInStructure"
    },
	[GuildInside_Entrance_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/GuildInside_Entrance_Page/WBP_GuildInside_Entrance_Page.WBP_GuildInside_Entrance_Page_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_StructPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Members/GuildInside_Designate_Panel/WBP_GuildInside_StructPage.WBP_GuildInside_StructPage_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuilsInside_WelfarePage] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/Welfare_Page/WBP_GuilsInside_WelfarePage.WBP_GuilsInside_WelfarePage_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_Bg_Daylight] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/GuildInside_Entrance_Page/WBP_GuildInside_Bg_Daylight.WBP_GuildInside_Bg_Daylight_C",
        classpath = "Gameplay.LogicSystem.Guild.GuildInside.Entrance.GuildInside_Entrance_Page.GuildInside_Bg_Daylight",
		cache = false, auth = "yecaifeng05",
	},
	[GuildInside_Bg_Dusk] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/GuildInside_Entrance_Page/WBP_GuildInside_Bg_Dusk.WBP_GuildInside_Bg_Dusk_C",
		classpath = "Gameplay.LogicSystem.Guild.GuildInside.Entrance.GuildInside_Entrance_Page.GuildInside_Bg_Daylight",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_Bg_Night] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/GuildInside_Entrance_Page/WBP_GuildInside_Bg_Night.WBP_GuildInside_Bg_Night_C",
		classpath = "Gameplay.LogicSystem.Guild.GuildInside.Entrance.GuildInside_Entrance_Page.GuildInside_Bg_Daylight",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_ActivityPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Entrance/Activity_Page/WBP_GuildInside_ActivityPage.WBP_GuildInside_ActivityPage_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_DetailPage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Members/GuildInside_Member_Panel/WBP_GuildInside_DetailPage.WBP_GuildInside_DetailPage_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_ApplyPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Members/GuildInside_Member_Panel/WBP_GuildInside_ApplyPage.WBP_GuildInside_ApplyPage_C",
        cache = false, auth = "yecaifeng05",
	},
	[GuildInside_ViewPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildInside/Members/GuildInside_Member_Panel/WBP_GuildInside_ViewPage.WBP_GuildInside_ViewPage_C",
        cache = false, auth = "yecaifeng05",
	},
    [GuildHomeWelfarePage] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Welfare/WBP_GuildHomeWelfarePage.WBP_GuildHomeWelfarePage_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Welfare.GuildHomeWelfarePage"
    },
    [GuildInActivity] = {
        res = "/Game/Arts/UI_2/Blueprint/Guild/Inside_2/Activity/WBP_GuildInActivity.WBP_GuildInActivity_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Guild.Inside_2.Activity.GuildInActivity"
    },
	[GuildBattleOutVS_Item] = {
		res = '/Game/Arts/UI_2/Blueprint/Guild/GuildBattle/OutPanel/WBP_GuildBattleOutVS_Item.WBP_GuildBattleOutVS_Item_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutVS_Item"
	},
	[RankingItem_Personal] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingItem_Personal.WBP_RankingItem_Personal_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingItem_Personal"
	},
	[RankingItem_Guild] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingItem_Guild.WBP_RankingItem_Guild_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingItem_Guild"
	},
	[RankingItem_Dungeon] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingItem_Dungeon.WBP_RankingItem_Dungeon_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingItem_Dungeon"
	},
	[RankingDungeonHead] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingDungeonHead.WBP_RankingDungeonHead_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingDungeonHead"
	},
	[RankingText] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingText.WBP_RankingText_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingText"
	},
	[RankingTitle_Personal] = {
		res = '/Game/Arts/UI_2/Blueprint/Ranking/WBP_RankingTitleText.WBP_RankingTitleText_C',
		cache = false, auth = "zhangyoujun",
		classpath = "Gameplay.LogicSystem.Ranking.RankingTitle_Personal"
	},
	[GuildBattleOutRanking_Item] = {
		res = '/Game/Arts/UI_2/Blueprint/Guild/GuildBattle/OutPanel/WBP_GuildBattleOutRanking_Item.WBP_GuildBattleOutRanking_Item_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutRanking_Item"
	},
	[GuildBattleOutResultRanking_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildBattle/OutPanel/WBP_GuildBattleOutResultRanking_Item.WBP_GuildBattleOutResultRanking_Item_C",
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutResultRanking_Item"
	},
	[GuildBattleOutResultLine_Item] = {
		res = '/Game/Arts/UI_2/Blueprint/Guild/GuildBattle/OutPanel/WBP_GuildBattleOutResultLine_Item.WBP_GuildBattleOutResultLine_Item_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Guild.GuildBattle.OutPanel.GuildBattleOutResultLine_Item"
	},
    [PlayerFriendGroupPost_Item] = {
        res = "/Game/Arts/UI_2/Blueprint/Chat/FriendGroup/WBP_PlayerFriendGroupPost_Item.WBP_PlayerFriendGroupPost_Item_C",
        cache = false, auth = "yecaifeng05",
        classpath = "Gameplay.LogicSystem.Chat.FriendGroup.PlayerFriendGroupPost_Item"
    },
    [FriendGroupTopic] = {
        res = "/Game/Arts/UI_2/Blueprint/Chat/FriendGroup/WBP_FriendGroupTopic.WBP_FriendGroupTopic_C",
        cache = false, auth = "yecaifeng05",
    },
	[HistoryMomentsPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/FriendGroup/WBP_HistoryMomentsPage.WBP_HistoryMomentsPage_C",
		cache = false, auth = "yecaifeng05",
	},
	[EleCoreTreeNodeItem] = {
		res = "/Game/Arts/UI_2/Blueprint/ElementCore/EleTalentTree/WBP_EleCoreTreeNodeItem1.WBP_EleCoreTreeNodeItem1_C",
		cache = false, auth = "qusicheng03",
	},
	[SkillCustomizerPeculiarityComponent] = {
		res = "/Game/Arts/UI_2/Blueprint/SkillCustomizer_2/SkillPeculiarity/WBP_SkillCustomizerPeculiarityComponent.WBP_SkillCustomizerPeculiarityComponent_C",
		cache = false, auth = "qusicheng03",
	},
	[SkillRoutineAllSkill_Sub] = {
		res = "/Game/Arts/UI_2/Blueprint/SkillCustomizer_2/SkillRoutine/WBP_SkillRoutineAllSkill_Sub.WBP_SkillRoutineAllSkill_Sub_C",
		cache = false, auth = "qusicheng03",
	},
	[SkillRoutineSwitch] = {
		res = "/Game/Arts/UI_2/Blueprint/SkillCustomizer_2/SkillRoutine/WBP_SkillRoutineSwitch.WBP_SkillRoutineSwitch_C",
		cache = false, auth = "qusicheng03",
	},
	[RedPacket_SelRP] =
	{
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_SelRP.WBP_RedPacket_SelRP_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_SelRP",
	},
	[RedPacket_GiveMoney] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_GiveMoney.WBP_RedPacket_GiveMoney_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_GiveMoney",
	},
	[RedPacket_GiveSkin] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_GiveSkin.WBP_RedPacket_GiveSkin_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_GiveSkin",
	},
	[RedPacket_SelectSkin] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_SelectSkin.WBP_RedPacket_SelectSkin_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_SelectSkin",
	},
	[HUDRedPacketBtn] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_RedPacket/WBP_HUDRedPacketBtn.WBP_HUDRedPacketBtn_C",
		cache = false, auth = "shangyuzhong",
		parent = string.format("%s/WBP_HUDRedPacket", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		classpath = "Gameplay.LogicSystem.HUD.HUD_RedPacket.HUDRedPacketBtn",
	},
	[RedPacket_Record] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_Record.WBP_RedPacket_Record_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_Record",
	},
	[RedPacket_AllCurRP] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/WBP_RedPacket_AllCurRP.WBP_RedPacket_AllCurRP_C",
		cache = false, auth = "shangyuzhong",
		classpath = "Gameplay.LogicSystem.RedPacket.RedPacket_AllCurRP",
	},
	[PVPMatch_SeasonList_Panel] = {
		res = '/Game/Arts/UI_2/Blueprint/PVP/PVP_Match/WBP_PVPMatch_SeasonList_Panel.WBP_PVPMatch_SeasonList_Panel_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.PVP.PVP_Match.PVPMatch_SeasonList_Panel"
	},
	[PVPMatch_Data_Panel] = {
		res = '/Game/Arts/UI_2/Blueprint/PVP/PVP_Match/WBP_PVPMatch_Data_Panel.WBP_PVPMatch_Data_Panel_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.PVP.PVP_Match.PVPMatch_Data_Panel"
	},
	[PVPMatch_Record_Panel] = {
		res = '/Game/Arts/UI_2/Blueprint/PVP/PVP_Match/WBP_PVPMatch_Record_Panel.WBP_PVPMatch_Record_Panel_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.PVP.PVP_Match.PVPMatch_Record_Panel"
	},
	[PVPMatch_Season_Panel] = {
		res = '/Game/Arts/UI_2/Blueprint/PVP/PVP_Match/WBP_PVPMatch_Season_Panel.WBP_PVPMatch_Season_Panel_C',
		cache = false, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.PVP.PVP_Match.PVPMatch_Season_Panel"
	},
	[StatisticsCure] = {
		res = '/Game/Arts/UI_2/Blueprint/Dungeon/StatisticsDungeon/WBP_StatisticsCure.WBP_StatisticsCure_C',
		cache = true, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Dungeon.StatisticsDungeon.StatisticsCure"
	},
	[StatisticsData] = {
		res = '/Game/Arts/UI_2/Blueprint/Dungeon/StatisticsDungeon/WBP_StatisticsData.WBP_StatisticsData_C',
		cache = true, auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Dungeon.StatisticsDungeon.StatisticsData"
	},
	[SettlementDungeonAwardAssignment] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_BtnTime/WBP_HUDBtnTime.WBP_HUDBtnTime_C",
		auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Dungeon.Settlement.SettlementDungeonAwardAssignment"
	},
	[SettlementDungeonAwardAuction] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_BtnTime/WBP_HUDBtnTime.WBP_HUDBtnTime_C",
		auth = "qusicheng03",
		classpath = "Gameplay.LogicSystem.Dungeon.Settlement.SettlementDungeonAwardAuction"
	},
	[SettlementDungeonAwardTips] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_BtnTime/WBP_HUDBtnTimeAcution.WBP_HUDBtnTimeAcution_C",
		auth = "hejiaqi05",
		classpath = "Gameplay.LogicSystem.Dungeon.Settlement.SettlementDungeonAwardTips"
	},
	[CollectiblesPhotoPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Photo/WBP_CollectiblesPhoto_Panel.WBP_CollectiblesPhoto_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.Photo.CollectiblesPhotoPage"
	},
	[CollectiblesSealedPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Sealed/WBP_CollectiblesSealedPanel_Panel.WBP_CollectiblesSealedPanel_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.Sealed.CollectiblesSealedPage"
	},
	[CollectiblesPropPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Item/WBP_CollectiblesSealeds_Panel.WBP_CollectiblesSealeds_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.Prop.CollectiblesPropPage"
	},
	[CollectiblesNPCPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Constable/WBP_CollectiblesConstables_Panel.WBP_CollectiblesConstables_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.NPC.CollectiblesNPCPage"
	},
	[CollectiblesVideoPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Video/WBP_CollectiblesVideo_Panel.WBP_CollectiblesVideo_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.Video.CollectiblesVideoPage"
	},
	[CollectiblesLettersPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Achieve/Collectibles/Letter/WBP_CollectiblesLetters_Panel.WBP_CollectiblesLetters_Panel_C",
		auth = "zhangyu73",
		classpath = "Gameplay.LogicSystem.Collectibles.CollectiblesListPanel.Letter.CollectiblesLettersPage"
	},
	[PlotRecapMainTask] = {
		res = "/Game/Arts/UI_2/Blueprint/PlotRecap/PlotcapMainTask_Panel/WBP_PlotRecapMainTask.WBP_PlotRecapMainTask_C",
		auth = "zhanboce",
		classpath = "Gameplay.LogicSystem.PlotRecap.PlotcapMainTask_Panel.PlotRecapMainTask"
	},
	[PlotRecapMainLine] = {
		res = "/Game/Arts/UI_2/Blueprint/PlotRecap/PlotcapMainLine_Panel/WBP_PlotcapMainLinePanel_Background.WBP_PlotcapMainLinePanel_Background_C",
		auth = "zhanboce",
		classpath = "Gameplay.LogicSystem.PlotRecap.PlotcapMainLine_Panel.PlotcapMainLinePanel_Background"
	},
	[PlotRecapBranchTaskList] = {
		res = "/Game/Arts/UI_2/Blueprint/PlotRecap/PlotcapBranchTask_Panel/WBP_PlotRecapBranchTaskList.WBP_PlotRecapBranchTaskList_C",
		auth = "zhanboce",
		classpath = "Gameplay.LogicSystem.PlotRecap.PlotcapBranchTask_Panel.PlotRecapBranchTaskList"
	},
	[PlotRecapBranchInfoList] = {
		res = "/Game/Arts/UI_2/Blueprint/PlotRecap/PlotcapBranchTask_Panel/WBP_PlotRecapBranchInfoList.WBP_PlotRecapBranchInfoList_C",
		auth = "zhanboce",
		classpath = "Gameplay.LogicSystem.PlotRecap.PlotcapBranchTask_Panel.PlotRecapBranchInfoList"
	},
	[HUD1v1Countdown] = {
        res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_1v1/WBP_HUD1v1Countdown.WBP_HUD1v1Countdown_C",
		parent = string.format("%s/WBP_HUD1v1", UIPanelConfig.HUD_Panel),
        parentui = UIPanelConfig.HUD_Panel,
		auth = "yecaifeng05",
    },
	[HUDPVPChampion] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/Champion/WBP_HUDPVPChampion.WBP_HUDPVPChampion_C",
		parent = string.format("%s/WBP_HUDPVP", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUD3v3] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/WBP_HUD3v3.WBP_HUD3v3_C",
		parent = string.format("%s/WBP_HUDPVP", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUD_PVP12V12] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/WBP_HUD_PVP12V12.WBP_HUD_PVP12V12_C",
		parent = string.format("%s/WBP_HUDPVP", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUD_PVP12V12Magic] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/WBP_HUD_PVP12V12Magic.WBP_HUD_PVP12V12Magic_C",
		parent = string.format("%s/WBP_HUD_PVP12V12Magic", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDPVPLikeBtn] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_PVP.PVPInner.HUDPVPLikeBtn",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/PVPInner/WBP_HUDPVPBtn.WBP_HUDPVPBtn",
		parent = string.format("%s/WBP_HUDPVPLikeBtnRoot", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDPVPStatsBtn] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_PVP.PVPInner.HUDPVPStatsBtn",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/PVPInner/WBP_HUDPVPBtn.WBP_HUDPVPBtn_C",
		parent = string.format("%s/WBP_HUDPVPStatsBtnRoot", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDPVPContinuousKill] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_PVP.HUDPVPContinuousKill",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/WBP_HUDPVPContinuousKill.WBP_HUDPVPContinuousKill_C",
		parent = string.format("%s/WBP_Reminder_PVPContinuousKills", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDChampionMatchBtn] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/Champion/WBP_HUDChampionMatchBtn.WBP_HUDChampionMatchBtn_C",
		parent = string.format("%s/WBP_HUDBtnTime", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDChampionTeamBtn] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PVP/Champion/WBP_HUDChampionTeamBtn.WBP_HUDChampionTeamBtn_C",
		parent = string.format("%s/WBP_HUDBtnTime", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDChampionPrepareInfo] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_PVP.Champion.HUDChampionPrepareInfo",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Task/WBP_HUDSimpleTask.WBP_HUDSimpleTask_C",
		parent = string.format("%s/WBP_HUDTask", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "qusicheng03",
	},
	[HUDJoyStick] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_JoyStick/WBP_HUDJoyStick.WBP_HUDJoyStick_C",
		parent = string.format("%s/WBP_HUDJoyStick", UIPanelConfig.ScreenInput_Panel),
		parentui = UIPanelConfig.ScreenInput_Panel,
		auth = "liufeng",
	},
	[PVP_ChampionIntro_Sub] = {
		res = "/Game/Arts/UI_2/Blueprint/PVP/PVP_Competition/Champion/WBP_PVP_ChampionIntro_Sub.PVP_ChampionIntro_Sub_C",
		auth = "qusicheng03",
	},
	[PVP_ChampionMatch_Sub_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/PVP/PVP_Competition/Champion/WBP_PVP_ChampionMatch_Sub_Page.WBP_PVP_ChampionMatch_Sub_Page_C",
		auth = "qusicheng03",
	},	
	[PVP_ChampionMatchRank_Sub_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/PVP/PVP_Competition/Champion/WBP_PVP_ChampionMatchRank_Sub_Page.WBP_PVP_ChampionMatchRank_Sub_Page_C",
		auth = "qusicheng03",
	},
	[RolePlayGameHUD_Info_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/RolePlayGame/RolePlayGameHUD/WBP_RolePlayGameHUD_Info_Page.WBP_RolePlayGameHUD_Info_Page_C",
		parent = string.format("%s/Canvas_Solt", UIPanelConfig.RolePlayGameHUD_Inside_Main_Panel),
		parentui = UIPanelConfig.RolePlayGameHUD_Inside_Main_Panel,
		auth = "houhuijie",
	},
	[HUDSocialAction_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SocialAction/WBP_HUDSocialAction_Item.WBP_HUDSocialAction_Item_C",
		classpath = "Gameplay.LogicSystem.HUD.HUD_SocialAction.HUDSocialAction_Item",
		auth = "jiangyueming03",
	},
	[HUDSocialActionSelectPlayer] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SocialAction/WBP_HUDSocialActionSelectPlayer.WBP_HUDSocialActionSelectPlayer_C",
		classpath = "Gameplay.LogicSystem.HUD.HUD_SocialAction.HUDSocialActionSelectPlayer",
		auth = "jiangyueming03",
	},
	[HUDSocialClooectBtn] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SocialAction/WBP_HUDSocialClooectBtn.WBP_HUDSocialClooectBtn_C",
		classpath = "Gameplay.LogicSystem.HUD.HUD_SocialAction.HUDSocialClooectBtn",
		auth = "jiangyueming03",
	},
	[HUDSocialActionBtn] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SocialAction/WBP_HUDSocialActionBtn.WBP_HUDSocialActionBtn_C",
		auth = "jiangyueming03",
		parent = "P_HUDBaseView/WBP_SocialAction",
		parentui = "P_HUDBaseView",
	},
	[ChatSmall_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/ChatSmall_Panel/WBP_ChatSmall_Page.WBP_ChatSmall_Page_C",
		auth = "yecaifeng05",
	},
	[ChatContent_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/ChatSocial_Panel/ChatContent_Page/WBP_ChatContent_Page.WBP_ChatContent_Page_C",
		auth = "yecaifeng05",
	},
	[ChatDiscloseBg] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/WBP_ChatDiscloseBg.WBP_ChatDiscloseBg_C",
		auth = "zhangchangyu",
	},
	[ChatBubble_TeamRecruitLMsg] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/WBP_ChatBubble_TeamRecruitL.WBP_ChatBubble_TeamRecruitL_C",
		auth = "yecaifeng05",
	},
	[ChatExpressionMsg] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/WBP_ChatBubble_Emo.WBP_ChatBubble_Emo_C",
		auth = "yecaifeng05",
	},
	[ChatRoom_RoomList_Page] = {
        res = "/Game/Arts/UI_2/Blueprint/Chat/ChatRoom/RoomList_Page/WBP_ChatRoom_RoomList_Page.WBP_ChatRoom_RoomList_Page_C",
        auth = "yecaifeng05"
	},
	[ChatRoom_RoomChat_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/ChatRoom/RoomChat_Page/WBP_ChatRoom_RoomChat_Page.WBP_ChatRoom_RoomChat_Page_C",
		auth = "yecaifeng05"
	},
    [PartnerFather] =
    {
        res = "/Game/Arts/UI_2/Blueprint/Partner/WBP_PartnerFather.WBP_PartnerFather_C",
        auth = "shangyuzhong",
    },

	[GuildHelpMsg] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/WBP_ChatBubble_GuildHelp.WBP_ChatBubble_GuildHelp_C",
		classpath = "Gameplay.LogicSystem.Chat.ChatMsg.ChatMsgBubbleGuildHelp",
		auth = "yecaifeng05",
	},
	[RedPacketMsg] = {
		res = "/Game/Arts/UI_2/Blueprint/RedPacket/RedPacket_Item/WBP_RedPacket_Envelope_Small_Item.WBP_RedPacket_Envelope_Small_Item_C",
		classpath = "Gameplay.LogicSystem.Chat.ChatMsg.ChatMsgBubbleRedpacket",
		auth = "yecaifeng05",
	},
	[GuildResponseInviteMsg] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/WBP_ChatBubble_GuildResponse.WBP_ChatBubble_GuildResponse_C",
		auth = "yecaifeng05",
	},
	[ChatBarrage] = {
		res = "/Game/Arts/UI_2/Blueprint/Chat/ChatBarrageLayer_Panel/WBP_ChatBarrage.WBP_ChatBarrage_C",
		auth = "yecaifeng05",
	},
	[HUDChat] = {
        res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Chat/WBP_HUDChat.WBP_HUDChat_C",
        parent = string.format("%s/HUDChat", UIPanelConfig.HUD_Panel),
        parentui = UIPanelConfig.HUD_Panel,
        auth = "yecaifeng05"
    },
    [WriteMessage] = {
        res = "/Game/Arts/UI_2/Blueprint/ScreenEffect/WBP_WriteMessage.WBP_WriteMessage_C",
        auth = "zhangshuai15",
	},
	[ScreenEffect_Smoke] = {
		res = "/Game/Arts/UI_2/Blueprint/ScreenEffect/WBP_ScreenEffect_Smoke.WBP_ScreenEffect_Smoke_C",
		auth = "jiangyueming03"
	},
	[PlayerBackpack] = {
		res = "/Game/Arts/UI_2/Blueprint/PlayerDetails/WBP_PlayerBackpack.WBP_PlayerBackpack_C",
		auth = "jiangyueming03",
	},
	
	[HUD_TeamGroup_Container] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Team/WBP_HUDTeamTotal.WBP_HUDTeamTotal_C",
		parent = "P_HUDSideBar/overlay_content",
		parentui = "P_HUDSideBar",
		auth = "chenpengfei06"
	},
	
	[HUD_Team] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Team/WBP_HUDTeam.WBP_HUDTeam_C",
		parent = "HUD_TeamGroup_Container/Overlay_Team",
		parentui = HUD_TeamGroup_Container,
		auth = "chenpengfei06",
	},
	
	[HUD_TeamMemberItem] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Team/WBP_HUDTeamMemberItem.WBP_HUDTeamMemberItem_C",
		auth = "chenpengfei06",
	},

	[HUD_TeamMemberMarkMenu] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Group/WBP_HUDGroupRoulettle.WBP_HUDGroupRoulettle_C",
		parent = "HUD_TeamGroup_Container/Overlay_TeamMemberMark",
		parentui = HUD_TeamGroup_Container,
		auth = "chenpengfei06"
	},

	[HUD_Group] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Group/WBP_HUDGroup.WBP_HUDGroup_C",
		parent = "HUD_TeamGroup_Container/Overlay_Team",
		parentui = "HUD_TeamGroup_Container",
		auth = "chenpengfei06"
	},
	
	[HUD_GroupTeamItem] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_Group.HUD_GroupTeamItem",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Group/WBP_HUDGroupTeam.WBP_HUDGroupTeam_C",
		auth = "chenpengfei06"
	},


	[HUD_Task_Panel] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_Task.New.HUD_Task_Panel",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Task/New/WBP_HUD_Task_Panel.WBP_HUD_Task_Panel_C",
		parent = "P_HUDSideBar/overlay_content",
		parentui = "P_HUDSideBar",
		auth = "yuyue13",
	},
	
	[HUD_MiniGame_ItemCount] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_MiniGame/WBP_HUD_MiniGame_ItemCount.WBP_HUD_MiniGame_ItemCount_C",
		parent = "P_HUDBaseView/WBP_HUD_MiniGame_ItemCount",
		parentui = "P_HUDBaseView",
		auth = "chenhonghua",
	},
	[HUD_SceneMarkMenu] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Group/WBP_HUDGroupMark.WBP_HUDGroupMark_C",
		parent = "HUD_TeamGroup_Container/VB_TeamGroup/Overlay_SceneMark",
		parentui = HUD_TeamGroup_Container,
		auth = "chenpengfei06"
	},
	
	[HUD_WeatherBtn] = {
	    res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Weather/WBP_HUD_WeatherBtn.WBP_HUD_WeatherBtn_C",
	    parent = "P_HUDBaseView/WBP_Overlay_WeatherBtn",
	    parentui = "P_HUDBaseView",
	    author = "jiangyueming03",
	},
	[HUD_Weather] = {
	    res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Weather/WBP_HUD_Weather.WBP_HUD_Weather_C",
	    parent = "P_HUDBaseView/WBP_HUDMap",
	    parentui = "P_HUDBaseView",
	    author = "jiangyueming03",
	},
 	[HUD_EXP] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Common/WBP_HUDExp.WBP_HUDExp_c",
		parent = "P_HUDBaseView/WBP_HUDExp",
		parentui = "P_HUDBaseView",
		auth = "jiangyueming03",
	},
	[HUD_QuickUse] = {
        res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_QuickUse/WBP_HUD_QuickUse.WBP_HUD_QuickUse_C",
        parent = "P_HUDBaseView/WBP_HUDQuickUse",
        parentui = "P_HUDBaseView",
        auth = "yangcao",
    },
	[HUD_PlayerInfo] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_PlayerInfo/WBP_HUDPlayerInfo.WBP_HUDPlayerInfo_C",
		parent = "P_HUDBaseView/WBP_HUDPlayerInfo",
		parentui = "P_HUDBaseView",
		auth = "jiangyueming03",
	},
	[ReminderPower_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PlayerInfo/WBP_ReminderPower_Widget.WBP_ReminderPower_Widget_C",
		parent = "ReminderMain_Panel/Power",
		parentui = "ReminderMain_Panel",
		auth = "chenhonghua",
	},
	[ReminderMsgNormal_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Toast/WBP_ReminderMsgNormal_Widget.WBP_ReminderMsgNormal_Widget_C",
		parent = "ReminderMain_Panel/MsgNormal_List",
		parentui = "ReminderMain_Panel",
		PreloadResMap = {"ReminderMsgNormalItem", },
		auth = "houhuijie",
	},
	[ReminderMsgNormalItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Toast/WBP_ReminderMsgNormal.WBP_ReminderMsgNormal_C",
		auth = "houhuijie",
	},
	[ReminderMsgLight_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Toast/WBP_ReminderMsgLight_Widget.WBP_ReminderMsgLight_Widget_C",
		parent = "ReminderMain_Panel/MsgLight_List",
		parentui = "ReminderMain_Panel",
		PreloadResMap = {"ReminderMsgLightItem", },
		auth = "houhuijie",
	},
	[ReminderMsgLightItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Toast/WBP_ReminderMsgLight.WBP_ReminderMsgLight_C",
		auth = "houhuijie",
	},
	[ReminderDropList_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/GetItems/WBP_ReminderDropList_Widget.WBP_ReminderDropList_Widget_C",
		parent = "ReminderMain_Panel/DropList",
		parentui = "ReminderMain_Panel",
		PreloadResMap = {"ReminderDropItem", },
		auth = "houhuijie",
	},
	[ReminderDropItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/GetItems/WBP_ReminderDropItem.WBP_ReminderDropItem_C",
		auth = "houhuijie",
	},
	[ReminderGetSkill_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PlayerInfo/WBP_ReminderGetSkill_Widget.WBP_ReminderGetSkill_Widget_C",
		parent = "ReminderMain_Panel/GetSkill",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderUpGrade_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PlayerInfo/WBP_ReminderUpgrade_Widget.WBP_ReminderUpgrade_Widget_C",
		parent = "ReminderMain_Panel/LevelUp",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderRegion_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Dungeon/WBP_ReminderRegion_Widget.WBP_ReminderRegion_Widget_C",
		parent = "ReminderMain_Panel/DungeonAreaChange",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderUnlock_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Unlock/WBP_ReminderUnlock_Widget.WBP_ReminderUnlock_Widget_C",
		parent = "ReminderMain_Panel/Full",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderBossStats_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/BossStats/WBP_ReminderBossStats_Widget.WBP_ReminderBossStats_Widget_C",
		parent = "ReminderMain_Panel/WorldBossStateTips",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderAchieve_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PlayerInfo/WBP_ReminderAchieve_Widget.WBP_ReminderAchieve_Widget_C",
		parent = "ReminderMain_Panel/Achieve",
		parentui = "ReminderMain_Panel",
		auth = "chenhonghua",
	},
	[ReminderPVPQuickInteract_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderPVPQuickInteract_Widget.WBP_ReminderPVPQuickInteract_Widget_C",
		parent = "ReminderMain_Panel/PVPQucikInteract",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderDungeonTips_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonTips_Widget.WBP_ReminderDungeonTips_Widget_C",
		parent = "ReminderMain_Panel/DungeonTips",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderDungeonCaution_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonCaution_Widget.WBP_ReminderDungeonCaution_Widget_C",
		parent = "ReminderMain_Panel/DungeonCaution",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderDungeonSkill_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonSkill_Widget.WBP_ReminderDungeonSkill_Widget_C",
		parent = "ReminderMain_Panel/DungeonSkill",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderDungeonResult_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonResult_Widget.WBP_ReminderDungeonResult_Widget_C",
		parent = "ReminderMain_Panel/DungeonResult",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderCD_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderCD_Widget.WBP_ReminderCD_Widget_C",
		parent = "ReminderMain_Panel/PVPCountDown",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderDoubleHint_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderDoubleHint_Widget.WBP_ReminderDoubleHint_Widget_C",
		parent = "ReminderMain_Panel/DoubleHint",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[PVPDoubleReminder_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_PVPDoubleReminder_Widget.WBP_PVPDoubleReminder_Widget_C",
		parent = "ReminderMain_Panel/DoubleStart",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderPVPStatus_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderPVPStatus_Widget.WBP_ReminderPVPStatus_Widget_C",
		parent = "ReminderMain_Panel/PVPStatus",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderPVPInteract_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderPVPInteract_Widget.WBP_ReminderPVPInteract_Widget_C",
		parent = "ReminderMain_Panel/PVPInteract",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderPVPInfo_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderPVPInfo_Widget.WBP_ReminderPVPInfo_Widget_C",
		parent = "ReminderMain_Panel/PVPInfo",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderPVPWarn_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/PVP/WBP_ReminderPVPWarn_Widget.WBP_ReminderPVPWarn_Widget_C",
		parent = "ReminderMain_Panel/PVPWarn",
		parentui = "ReminderMain_Panel",
		auth = "zhangyu73",
	},
	[ReminderChallenges_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderChallenges_Widget.WBP_ReminderChallenges_Widget_C",
		parent = "ReminderMain_Panel/Challenge",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderDungeonWarning_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonWarning_Widget.WBP_ReminderDungeonWarning_Widget_C",
		parent = "ReminderMain_Panel/DungeonWarning",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderTaskFinish_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Task/WBP_ReminderTaskFinish_Widget.WBP_ReminderTaskFinish_Widget_C",
		parent = "ReminderMain_Panel/Mission",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderMission_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderMission_Widget.WBP_ReminderMission_Widget_C",
		parent = "ReminderMain_Panel/Mission",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderDungeonCDM_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/ToastPlay/WBP_ReminderDungeonCDM_Widget.WBP_ReminderDungeonCDM_Widget_C",
		parent = "ReminderMain_Panel/ComDungeonCDM",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderLike_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Like/WBP_ReminderLike_Widget.WBP_ReminderLike_Widget_C",
		parent = "ReminderMain_Panel/ComLike",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderTaskChapter_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Task/WBP_ReminderTaskChapter_Widget.WBP_ReminderTaskChapter_Widget_C",
		parent = "ReminderMain_Panel/Full",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderFateContract_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/FateContract/WBP_ReminderFateContract_Widget.WBP_ReminderFateContract_Widget_C",
		parent = "ReminderMain_Panel/ComMiddle",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderTeamAssistance_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Team/WBP_ReminderTeamAssistance_Widget.WBP_ReminderTeamAssistance_Widget_C",
		parent = "ReminderMain_Panel/TeamAssistance",
		parentui = "ReminderMain_Panel",
		auth = "houhuijie",
	},
	[ReminderInteractResult_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Exploration/WBP_ReminderInteractResult_Widget.WBP_ReminderInteractResult_Widget_C",
		auth = "houhuijie",
		parent = "ReminderMain_Panel/InteractorGameResult",
		parentui = "ReminderMain_Panel",
	},
	[ReminderExplorationStart_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Exploration/WBP_ReminderExplorationStart_Widget.WBP_ReminderExplorationStart_Widget_C",
		auth = "chenhonghua",
		parent = "ReminderMain_Panel/ExplorationStart",
		parentui = "ReminderMain_Panel",
	},
	[ReminderExplorationResult_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Exploration/WBP_ReminderExplorationResult_Widget.WBP_ReminderExplorationResult_Widget_C",
		auth = "chenhonghua",
		parent = "ReminderMain_Panel/InteractorGameResult",
		parentui = "ReminderMain_Panel",
	},
	[ReminderNewAppearance_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Fashion/WBP_ReminderNewAppearance_Widget.WBP_ReminderNewAppearance_Widget_C",
		auth = "houhuijie",
		parent = "ReminderMain_Panel/NewAppearance",
		parentui = "ReminderMain_Panel",
	},
	[Reminder_GoddessPuzzle_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Exploration/WBP_Reminder_GoddessPuzzle_Widget.WBP_Reminder_GoddessPuzzle_Widget_C",
		auth = "chenhonghua",
		parent = "ReminderMain_Panel/InteractorGameResult",
		parentui = "ReminderMain_Panel",
	},
	[ReminderEquipLimitUp_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Equipment/WBP_Reminder_Equipment_Upper.WBP_Reminder_Equipment_Upper_C",
		auth = "chenpengfei06",
		parent = "ReminderMain_Panel/Equipment_Upper",
		parentui = "ReminderMain_Panel",
	},
	[ReminderEquipSpiritGroup_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Equipment/WBP_Reminder_Equipment_NewForging.WBP_Reminder_Equipment_NewForging_C",
		auth = "chenpengfei06",
		parent = "ReminderMain_Panel/Equipment_Upper",
		parentui = "ReminderMain_Panel",
	},
	[ReminderEquipSpiritStorage_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Reminder/Equipment/WBP_Reminder_Equipment_Recorded.WBP_Reminder_Equipment_Recorded_C",
		auth = "chenpengfei06",
		parent = "ReminderMain_Panel/Equipment_Upper",
		parentui = "ReminderMain_Panel",
	},
	
	
	[FashionWardrobe_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/Wardrobe/WBP_FashionWardrobe_Widget.WBP_FashionWardrobe_Widget_C",
		cache = false,
		auth = "jitengfei",
	},
	[FashionChange_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/WBP_FashionChange_Widget.WBP_FashionChange_Widget_C",
		cache = false,
		auth = "jitengfei",
	},
	[FashionAround_Widget] = {
		classpath = "Gameplay.LogicSystem.Fashion.FashionMain.UI.FashionMain_Panel.AroundWidget.FashionAround_Widget",
		res = "/Game/Arts/UI_2/Blueprint/Fashion/WBP_FashionChange_Widget.WBP_FashionChange_Widget_C",
		cache = false,
		auth = "jitengfei",
	},
	[FashionSceneCustom_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/SceneCustom/WBP_SceneCustom_Widget.WBP_SceneCustom_Widget_C",
		cache = false,
		auth = "jitengfei",
	},
	[FashionVehicle_Widget] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/Vehicle/WBP_FashionVehicle_Widget.WBP_FashionVehicle_Widget_C",
		cache = false,
		auth = "yangcao",
	},
	[SceneCustom_Anim_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/SceneCustom/WBP_SceneCustom_Anim_Page.WBP_SceneCustom_Anim_Page_C",
		cache = false,
		auth = "jitengfei",
	},
	[SceneCustom_Component_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/SceneCustom/WBP_SceneCustom_Component_Page.WBP_SceneCustom_Component_Page_C",
		cache = false,
		auth = "jitengfei",
	},
	[SceneCustom_Environment_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Fashion/SceneCustom/WBP_SceneCustom_Environment_Page.WBP_SceneCustom_Environment_Page_C",
		cache = false,
		auth = "jitengfei",
	},
	[StarLight] = {
		res = "/Game/Arts/UI_2/Blueprint/MiniGame/StarGraph/WBP_StarLight.WBP_StarLight_C",
		auth = "jiangyueming03",
	},
	[StarLightHole] = {
		res = "/Game/Arts/UI_2/Blueprint/MiniGame/StarGraph/WBP_StarLightHole.WBP_StarLightHole_C",
		auth = "jiangyueming03",
	},
	[StarConnectLine] = {
		res = "/Game/Arts/UI_2/Blueprint/MiniGame/StarGraph/WBP_StarConnectLine.WBP_StarConnectLine_C",
		auth = "jiangyueming03",
	},
	[HUDCameraBtn] = {
		classpath = "Gameplay.LogicSystem.Photograph.HUDCameraBtn",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Common/WBP_HUDComSystemBtn.WBP_HUDComSystemBtn_C",
		parent = "P_HUDBaseView/WBP_HUDCamera",
		parentui = "P_HUDBaseView",
		auth = "yangcao",
	},
	[HUDConstantDisplay] = {
		res = "/Game/Arts/UI_2/Blueprint/ConstantDisplay/WBP_ComstantdisPlay_Icon_Item.WBP_ComstantdisPlay_Icon_Item_C",
		parent = "P_HUDBaseView/WBP_ConstantDisplay",
		parentui = "P_HUDBaseView",
		auth = "yangcao",
	},
	[BookImageS] = {
		res = "/Game/Arts/UI_2/Blueprint/Book/WBP_BookImageS.WBP_BookImageS_C",
		auth = "jiangyueming03",
	},
	[BookImageXL] = {
		res = "/Game/Arts/UI_2/Blueprint/Book/WBP_BookImageXL.WBP_BookImageXL_C",
		auth = "jiangyueming03",
	},
	[BookText] = {
		res = "/Game/Arts/UI_2/Blueprint/Book/WBP_BookText.WBP_BookText_C",
		auth = "jiangyueming03",
	},
	[BookTitle] = {
		res = "/Game/Arts/UI_2/Blueprint/Book/WBP_BookTitle.WBP_BookTitle_C",
		auth = "jiangyueming03",
	},
	[ChatMarqueeItemGray] = {
		res = "/Game/Arts/UI_2/Blueprint/Marquee/MarqueeItem/WBP_Marquee_Star.WBP_Marquee_Star_C",
		parent = "Marquee_Panel/ChatSpeaker",
		parentui = "Marquee_Panel",
		auth = "houhuijie",
	},
	[ChatMarqueeItemPurple] = {
		res = "/Game/Arts/UI_2/Blueprint/Marquee/MarqueeItem/WBP_Marquee_Feather.WBP_Marquee_Feather_C",
		parent = "Marquee_Panel/ChatSpeaker",
		parentui = "Marquee_Panel",
		auth = "houhuijie",
	},
	[ChatMarqueeItemGold] = {
		res = "/Game/Arts/UI_2/Blueprint/Marquee/MarqueeItem/WBP_Marquee_Lightning.WBP_Marquee_Lightning_C",
		parent = "Marquee_Panel/ChatSpeaker",
		parentui = "Marquee_Panel",
		auth = "houhuijie",
	},
	[AnnouncementMarqueeItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Marquee/MarqueeItem/WBP_Marquee_Announcement.WBP_Marquee_Announcement_C",
		parent = "Marquee_Panel/Announcement",
		parentui = "Marquee_Panel",
		auth = "houhuijie",
	},
	
	[SeasonLastRankPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Season/WBP_SeasonResult_Page.WBP_SeasonResult_Page_C",
		auth = "chenpengfei06",
	},
	[SeasonLastDataPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Season/WBP_SeasonData_Page.WBP_SeasonData_Page_C",
		auth = "chenpengfei06",
	},
	
	[HUDBuffList] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_BuffList/WBP_HUDBuffList.WBP_HUDBuffList_C",
		parent = string.format("%s/WBP_BuffList", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "zhangfeng15",
	},
	[HUDActivityList] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Activity/WBP_HUDActivityList.WBP_HUDActivityList_C",
		parent = string.format("%s/WBP_HUDActivityList", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "zhangyu73",
	},
	[HUDOffCar] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDOffCar.WBP_HUDOffCar_C",
		parent = string.format("%s/WBP_HUDTest", UIPanelConfig.HUD_Panel),
		parentui = UIPanelConfig.HUD_Panel,
		auth = "zhangfeng15",
	},
	[HUDComNoticeBtn] =
	{
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Common/WBP_HUDComNoticeBtn.WBP_HUDComNoticeBtn_C",
		parent = "P_HUDBaseView/WBP_HUDComNotices",
		parentui = "P_HUDBaseView",
		auth = "zhangyu73"
	},
	[HUDStatusText] =
	{
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Status/WBP_HUDStatusText.WBP_HUDStatusText_C",
		parent = "P_HUDBaseView/WBP_HUDFollowingHint",
		parentui = "P_HUDBaseView",
		auth = "huangjinbao"
	},
	[HUDAuctionBtn] =
	{
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Common/WBP_HUDAuctionBtn.WBP_HUDAuctionBtn_C",
		parent = "P_HUDBaseView/WBP_HUDAuction",
		parentui = "P_HUDBaseView",
		auth = "liaohaiqiang"
	},
	[HUDComDownBtn] =
	{
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Common/HUD_Button/WBP_HUDComDownBtn.WBP_HUDComDownBtn_C",
		parent = "P_HUDBaseView/WBP_HUDComDownBtn",
		parentui = "P_HUDBaseView",
		auth = "wanghuihui"
	},
	[HUDStatusBar] = {
		classpath = "Gameplay.LogicSystem.HUD.HUD_StatusBar.HUDStatusBar",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_StatusBar/WBP_HUDStatusBar.WBP_HUDStatusBar_C",
		parent = "P_HUDBaseView/WBP_HUDStatusBar",
		parentui = "P_HUDBaseView",
		auth = "jiangyueming03"
	},
	[HUDSkillRouletteTotal] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDSkillRouletteTotal.WBP_HUDSkillRouletteTotal_C",
		parent = "P_HUDBaseView/RightTopIn_Canvas_Panel",
		parentui = "P_HUDBaseView",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillRoulette_Mobile] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDSkillRoulette_Mobile.WBP_HUDSkillRoulette_Mobile_C",
		parent = "HUDSkillRouletteTotal/CP_SkillRoulette",
		parentui = "HUDSkillRouletteTotal",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillRoulette_PC] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill_PC/WBP_HUDSkillRoulette_PC.WBP_HUDSkillRoulette_PC_C",
		parent = "HUDSkillRouletteTotal/CP_SkillRoulette",
		parentui = "HUDSkillRouletteTotal",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillSimpleRoulette_PC] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SimpleRoulette/WBP_HUDSkillSimpleRoulette_PC.WBP_HUDSkillSimpleRoulette_PC_C",
		parent = "HUDSkillRouletteTotal/CP_SkillRoulette",
		parentui = "HUDSkillRouletteTotal",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillSimpleRoulette_Mobile] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_SimpleRoulette/WBP_HUDSimpleRoulette_Mobile.WBP_HUDSimpleRoulette_Mobile_C",
		parent = "HUDSkillRouletteTotal/CP_SkillRoulette",
		parentui = "HUDSkillRouletteTotal",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismApprentice] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDApprenticeSkill.WBP_HUDApprenticeSkill_C",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismArbiter] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDUtopianLight.WBP_HUDUtopianLight_C",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismFool] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDFeatherwitSkill.WBP_HUDFeatherwitSkill_C",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismSun] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDSunSkill.WBP_HUDSunSkill_C",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismUtopian] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDUtopianSkill.WBP_HUDUtopianSkill_C",
		auth = "baizihan",
		cache = true,
	},
	[HUDSkillJobMechanismWarrior] = {
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Skill/WBP_HUDUtopianLight.WBP_HUDUtopianLight_C",
		classpath = "Gameplay.LogicSystem.HUD.HUD_Skill.JobMechanism.HUDSkillJobMechanismWarrior",
		auth = "baizihan",
		cache = true,
	},
	[P_HUDSimpleTask] = {
		--classpath = "Gameplay.LogicSystem.HUD.P_HUDSimpleTask",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_Task/WBP_HUDSimpleTask.WBP_HUDSimpleTask_C",
		parent = "P_HUDBaseView/WBP_HUDTask",
		parentui = "P_HUDBaseView",
		auth = "yuyue13",
	},
	[Mail_Page] = {
		res = "/Game/Arts/UI_2/Blueprint/Mail/WBP_Mail_Page.WBP_Mail_Page_C",
		auth = "tianjia"
	},
	[MailMask] = {
		res = "/Game/Arts/UI_2/Blueprint/Mail/WBP_MailMask.WBP_MailMask_C",
		auth = "tianjia"
	},
	[MailGuildPlayerName] = {
		res = "/Game/Arts/UI_2/Blueprint/Mail/WBP_MailGuildPlayerName.WBP_MailGuildPlayerName_C",
		cache = true,
		auth = "tianjia",
	},
	[MailPhoto] = {
		res = "/Game/Arts/UI_2/Blueprint/Mail/WBP_MailPhoto.WBP_MailPhoto_C",
		cache = true,
		auth = "tianjia",
	},
	[MailVoice] = {
		res = "/Game/Arts/UI_2/Blueprint/Mail/WBP_MailVoice.WBP_MailVoice_C",
		cache = true,
		auth = "tianjia",
	},
	[TarotTeamDsaplay_Add_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/TarotTeam/WBP_TarotTeamDsaplay_Add_Item.WBP_TarotTeamDsaplay_Add_Item_C",
		auth = "zhangchangyu"
	},
	[TarotTeamManageBtn_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/TarotTeam/WBP_TarotTeamManageBtn_Item.WBP_TarotTeamManageBtn_Item_C",
		auth = "zhangchangyu"
	},
	[TarotTeamWindowTitle] = {
		res = "/Game/Arts/UI_2/Blueprint/TarotTeam/WBP_TarotTeamWindowTitle.WBP_TarotTeamWindowTitle_C",
		auth = "zhangchangyu"
	},
	[Sealed_Normal_Sub] = {
		res = "/Game/Arts/UI_2/Blueprint/Sealed_2/WBP_Sealed_Normal_Sub.WBP_Sealed_Normal_Sub_C",
		classpath = "Gameplay.LogicSystem.Sealed_2.Sealed_Normal_Sub",
		auth = "yechengyin",
	},
	[Sealed_Special_Sub] = {
		res = "/Game/Arts/UI_2/Blueprint/Sealed_2/WBP_Sealed_Special_Sub.WBP_Sealed_Special_Sub_C",
		classpath = "Gameplay.LogicSystem.Sealed_2.Sealed_Special_Sub",
		auth = "yechengyin",
	},
	[GuildTipsOut] = {
		res = "/Game/Arts/UI_2/Blueprint/Guild/GuildBattle/OutPanel/WBP_GuildTipsOut.WBP_GuildTipsOut_C",
		auth = "chenhonghua",
	},
	[DanceTap_Widget] = {
		res = '/Game/Arts/UI_2/Blueprint/Dance/DanceNew/DanceQTE/WBP_HUDGuildDanceTap.WBP_HUDGuildDanceTap_C',
		classpath = "Gameplay.LogicSystem.Dance.DanceNew.DanceQTE.HUDGuildDanceTap",
		auth = "zhangfeng15",
	},
	[DanceHold_Widget] = {
		res = '/Game/Arts/UI_2/Blueprint/Dance/DanceNew/DanceQTE/WBP_HUDGuildDanceTabHold.WBP_HUDGuildDanceTabHold_C',
		classpath = "Gameplay.LogicSystem.Dance.DanceNew.DanceQTE.HUDGuildDanceHold",
		auth = "zhangfeng15",
	},
	[DanceFlick_Widget] = {
		res = '/Game/Arts/UI_2/Blueprint/Dance/DanceNew/DanceQTE/WBP_HUDGuildDanceFlick.WBP_HUDGuildDanceFlick_C',
		classpath = "Gameplay.LogicSystem.Dance.DanceNew.DanceQTE.HUDGuildDanceTap",
		auth = "zhangfeng15",
	},
	[StatisticsDataItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Dungeon/StatisticsDungeon/WBP_StatisticsDataItem.WBP_StatisticsDataItem_C",
		auth = "tangmingzhe",
	},
	[StatisticsCureItem] = {
		res = "/Game/Arts/UI_2/Blueprint/Dungeon/StatisticsDungeon/WBP_StatisticsCureItem.WBP_StatisticsCureItem_C",
		auth = "tangmingzhe",
	},
	[DialogueUITypeAside] = {
		res = '/Game/Arts/UI_2/Blueprint/HUD/HUD_Aside/WBP_HUDAside.WBP_HUDAside_C',
		auth = "zhangsuohao",
        classpath = "Gameplay.LogicSystem.NPC.Dialogue.Dialogue_Aside"
    },
	[DialogueUITypeCaption] = {
        res = '/Game/Arts/UI_2/Blueprint/NPC/Dialogue/WBP_Dialogue_CSLine.WBP_Dialogue_CSLine_C',
        auth = "zhangsuohao",
    },
    [DialogueUITypeCSStyle] = {
        res = '/Game/Arts/UI_2/Blueprint/NPC/Dialogue/WBP_Dialogue_CSStyle.WBP_Dialogue_CSStyle_C',
        auth = "limunan",
	},
	[Dialogue_Debugger] = {
        res = "/Game/Arts/UI_2/Blueprint/NPC/Dialogue/WBP_Dialogue_Debugger.WBP_Dialogue_Debugger_C",
        auth = "limunan"
	},
	[GvG_Statistic_Map] = {
		res = "/Game/Arts/UI_2/Blueprint/GvG/GvG_Statistic/GvG_StatisticMap/WBP_GvG_Map.WBP_GvG_Map_C",
		auth = "yangcao",
	},
	[GvG_BattleStatistic_Rank_Sub] = {
		res = "/Game/Arts/UI_2/Blueprint/GvG/GvG_Statistic/GvG_Rank/WBP_GvG_BattleStatistic_Rank_Sub.WBP_GvG_BattleStatistic_Rank_Sub_C",
		auth = "yangcao",
	},

	[AshQTEBtn_2_Item] = {
		res = "/Game/Arts/UI_2/Blueprint/QTE/AshQTE/WBP_AshQTEBtn_2_Item.WBP_AshQTEBtn_2_Item_C",
		auth = "zhuzepeng",
		classPath = "Gameplay.LogicSystem.QTE.AshQTE.AshQTEBtn_2_Item",
	},
	[UIAppearance_AvatarFrame] = {
		res = "/Game/Arts/UI_2/Blueprint/InterfaceAppearance/AvatarFrame/WBP_AvatarFrame_Arrodes.WBP_AvatarFrame_Arrodes_C",
		auth = "yecaifeng05",
	},
	[UIAppearance_TeamFrame] = {
		res = "/Game/Arts/UI_2/Blueprint/InterfaceAppearance/ChatSet/ChatSet_Team/WBP_ChatSet_TeamFrame.WBP_ChatSet_TeamFrame_C",
		auth = "yecaifeng05",
	},
	[UIAppearance_GroupFrame] = {
		res = "/Game/Arts/UI_2/Blueprint/InterfaceAppearance/ChatSet/ChatSet_Team/WBP_ChatSet_GroupFrame.WBP_ChatSet_GroupFrame_C",
		auth = "yecaifeng05",
	},
	[UIAppearance_TeamMistVx] = {
		res = "/Game/Arts/UI_2/Blueprint/InterfaceAppearance/ChatSet/ChatSet_Team/WBP_ChatSet_TeamMistVx.WBP_ChatSet_TeamMistVx_C",
		auth = "yecaifeng05",
	},
	[UIAppearance_GroupVX] = {
		res = "/Game/Arts/UI_2/Blueprint/InterfaceAppearance/ChatSet/ChatSet_Team/WBP_ChatSet_GroupVX.WBP_ChatSet_GroupVX_C",
		auth = "yecaifeng05",
	},
	[HUDPVECountDown] = {
		classpath = "Gameplay.LogicSystem.HUD.HUDPVE.HUDPVECountDown",
		res = "/Game/Arts/UI_2/Blueprint/HUD/HUD_BtnTime/WBP_HUDBtnTime.WBP_HUDBtnTime_C",
		parent = "P_HUDBaseView/WBP_HUDBtnTime",
		parentui = "P_HUDBaseView",
		auth = "tangmingzhe"
	},
	[Photograph_LensPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_LensPage.WBP_Photograph_LensPage_C",
		auth = "wanghuihui"
	},
	[Photograph_MovementPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_MovementPage.WBP_Photograph_MovementPage_C",
		auth = "wanghuihui"
	},
	[Photograph_FilterPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_FilterPage.WBP_Photograph_FilterPage_C",
		auth = "wanghuihui"
	},
	[Photograph_DisplayPage] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_DisplayPage.WBP_Photograph_DisplayPage_C",
		auth = "wanghuihui"
	},
	[Photograph_TemplatePage] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_TemplatePage.WBP_Photograph_TemplatePage_C",
		auth = "wanghuihui"
	},
	[Photograph_CompositionGrid] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/PhotographMain/WBP_Photograph_CompositionGrid.WBP_Photograph_CompositionGrid_C",
		auth = "wanghuihui"
	},
	[PhotographEdit_Sticker] = {
		res = "/Game/Arts/UI_2/Blueprint/Photograph/Edit/WBP_PhotographEdit_Sticker.WBP_PhotographEdit_Sticker_C",
		auth = "wanghuihui"
	},
}

-- luacheck: pop