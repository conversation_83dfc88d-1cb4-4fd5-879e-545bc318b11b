return{
    ["EP_NonInteractMesh_1163301661"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1163301661",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Billboard/Mesh/XG/SM_Sign001_B.SM_Sign001_B",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20800, -7710, -1350),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1249692731"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1249692731",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Sign/SM_Sign001_A.SM_Sign001_A",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20620, -8470, -1410),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1254266957"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1254266957",
        ["MaterialOverlay"]="",
        ["Mesh"]="",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-21660, -7050, -1360),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1325043164"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1325043164",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Storeboard002.SM_Storeboard002",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15516, 9668, -1644.34),
            ["Rotator"]=FRotator(0, -55, 0),
            ["Scale"]=FVector(0.80, 0.80, 0.80),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1523552008"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1523552008",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Building/SM_JokerDream/Mesh/SM_JokerDream_Billboard01.SM_JokerDream_Billboard01",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22880, -7770, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1606673220"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1606673220",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Beckland/Outdoor/Decoration/Mesh/SM_Beckland_Billboard_02.SM_Beckland_Billboard_02",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-25280.24, -17654.15, -1426.77),
            ["Rotator"]=FRotator(0, 50, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1927034640"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1927034640",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Storeboard/SM_Storeboard004.SM_Storeboard004",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22580, -7970, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1998110385"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="1998110385",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Storeboard002.SM_Storeboard002",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15516, 9668, -1644.34),
            ["Rotator"]=FRotator(0, -55, 0),
            ["Scale"]=FVector(0.80, 0.80, 0.80),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2006601442"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2006601442",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Signboard_Large/SM_Signboard_Large_003.SM_Signboard_Large_003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-21510, -8060, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(0.20, 0.20, 0.20),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2077310266"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2077310266",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Signboard_A.SM_Signboard_A",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20560, -7700, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_233286028"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="233286028",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Building/SM_JokerDream/Mesh/SM_JokerDream_Billboard02.SM_JokerDream_Billboard02",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22720, -7890, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2382918565"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2382918565",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Shopsign003.SM_Shopsign003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-19032.49, -1076.11, -1786.02),
            ["Rotator"]=FRotator(0, 15, 0),
            ["Scale"]=FVector(2, 2, 2),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2386399478"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2386399478",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Signboard/SM_Signboard001.SM_Signboard001",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20670, -7990, -1530),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2702660454"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2702660454",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Shopsign003.SM_Shopsign003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(20634.91, -14511.53, -2227.36),
            ["Rotator"]=FRotator(0, 88, 0),
            ["Scale"]=FVector(2, 2, 2),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2939387844"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="2939387844",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Roadsign002/SM_Roadsign002.SM_Roadsign002",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-21040, -8610, -1590),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3047141330"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3047141330",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Billboard/Mesh/XG/SM_Sign001_E.SM_Sign001_E",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-18113.50, 13094.13, -1555.62),
            ["Rotator"]=FRotator(0, -20, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3131505717"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3131505717",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Decoration/Mesh/Gongchangqu/SM_Signboard_Large_008.SM_Signboard_Large_008",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-21840, -7860, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(0.20, 0.20, 0.20),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3138236568"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3138236568",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Shopsign003.SM_Shopsign003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-270, -10820, -1560),
            ["Rotator"]=FRotator(0, 90, 0),
            ["Scale"]=FVector(2, 2, 2),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3212738349"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3212738349",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Signboard_Large/SM_Signboard_Large_002.SM_Signboard_Large_002",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20180, -7930, -1510),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(0.20, 0.20, 0.20),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3577370000"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3577370000",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Decoration/Mesh/Gongchangqu/SM_Signboard_Large_012_006.SM_Signboard_Large_012_006",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22060, -8260, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(0.20, 0.20, 0.20),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3706400540"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3706400540",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Billboard/Mesh/XG/SM_Storeboard003.SM_Storeboard003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-25484.20, -4390.10, -1473.84),
            ["Rotator"]=FRotator(0, 106.00, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3906814434"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3906814434",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Building/SM_Backlund_Restaurant001/SM_Backlund_Restaurant001_billboard001.SM_Backlund_Restaurant001_billboard001",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22570, -8190, -1610),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3933408885"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="3933408885",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Prop/Mesh/xiqu/SM_Signboard_Large_009.SM_Signboard_Large_009",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-13580, -6590, -1080),
            ["Rotator"]=FRotator(0, -80.00, 0),
            ["Scale"]=FVector(0.70, 0.60, 0.60),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_4026414525"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="4026414525",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Prop/Mesh/SM_Signboard_Large_012.SM_Signboard_Large_012",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-3223.00, -26361.08, -1357.10),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_4261771834"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="4261771834",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/SmallDecoration/Mesh/SM_Storeboard002.SM_Storeboard002",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-16146, 10558, -1734.34),
            ["Rotator"]=FRotator(0, -50, 0),
            ["Scale"]=FVector(0.80, 0.80, 0.80),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_610945738"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="610945738",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Roadsign/SM_Roadsign.SM_Roadsign",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-20023.29, 20728.60, -2126.90),
            ["Rotator"]=FRotator(0, -50, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_804171373"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="804171373",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/Outdoor/Billboard/Mesh/XG/SM_Storeboard003.SM_Storeboard003",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14139.90, -7586.50, -1372.96),
            ["Rotator"]=FRotator(0, 6, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_896794414"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="896794414",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Storeboard/SM_Storeboard001.SM_Storeboard001",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-22460, -7950, -1460),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_938190178"]={
        ["ActorType"]=40008,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["ID"]="938190178",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Beckland/Outdoor/Decoration/Mesh/SM_Beckland_Billboard_03.SM_Beckland_Billboard_03",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]={
                ["EnumName"]="Always",
                ["EnumValue"]=0,
            },
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-345.08, -8927.08, -1720.15),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_SceneText_1535871623"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="1535871623",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="推荐套餐\n前菜：因蒂斯青酱焗蜗牛\n主菜：特级油封鸭腿\n甜品：罗塞尔酥皮挞",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=1,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-9370, -26500, -1380),
            ["Rotator"]=FRotator(0, 90.00, 0),
        },
    },
    ["EP_SceneText_1776403450"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="1776403450",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="18号\n罗森的民俗草药店",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=0.98,
                ["R"]=0.98,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(13610, 11520, -2010),
            ["Rotator"]=FRotator(0, -10, 0),
        },
    },
    ["EP_SceneText_2059634974"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="2059634974",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="波拿巴餐厅\n招牌特色\n因蒂斯红酒炖牛肉\n因蒂斯香槟\n黑松露与牛肝菌",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=1,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-10570, -26520, -1380),
            ["Rotator"]=FRotator(0, 90.00, 0),
        },
    },
    ["EP_SceneText_3112169232"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="3112169232",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="佐\n特\n兰\n街\n36\n号",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=1,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-20850, -10920, -1340),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_SceneText_487477958"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="487477958",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="铁十字街",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=1,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-18500, 13990, -2070),
            ["Rotator"]=FRotator(0, 120.00, 0),
        },
    },
    ["EP_SceneText_708661405"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="708661405",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="黑荆棘安保公司",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=0.99,
                ["R"]=0.99,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-20900, -10590, -980),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_SceneText_782810379"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["ETemplateCategory"]={
            ["EnumName"]="None",
            ["EnumValue"]=0,
        },
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontName"]="None",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["Hinting"]={
                ["EnumName"]="Default",
                ["EnumValue"]=0,
            },
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0,
                    ["G"]=0,
                    ["R"]=0,
                },
                ["OutlineMaterial"]="",
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=24,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="782810379",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=false,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="占卜俱乐部-13号",
            ["FadeInEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]={
                    ["EnumName"]="None",
                    ["EnumValue"]=0,
                },
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]={
                ["EnumName"]="Center",
                ["EnumValue"]=1,
            },
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=1,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-6840, -26600, -1380),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
}
