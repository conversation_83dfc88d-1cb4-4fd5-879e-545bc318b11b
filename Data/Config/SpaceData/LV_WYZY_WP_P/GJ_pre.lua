return{
    ["EP_BindViewController_3544938086"]={
        ["ActorType"]=40151,
        ["BindActors"]={
            [1]="%sWYZY/LV_WYZY_WP_P.LV_WYZY_WP_P:PersistentLevel.StaticMeshActor_UAID_BCE92FA47BBC926302_1471081009",
            [2]="%sWYZY/LV_WYZY_WP_P.LV_WYZY_WP_P:PersistentLevel.StaticMeshActor_UAID_047C166D73AA975D02_1899287304",
        },
        ["Coords"]={
            [0]=FVector2D(1, -1),
            [1]=FVector2D(-1, 0),
        },
        ["ID"]="3544938086",
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-17190, -6320, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_GameControlCamera_128122236"]={
        ["ActorType"]=60002,
        ["CameraConfig"]={
            ["BasicConfig"]={
                ["ArmInitRotation"]=FRotator(8.65, 131.60, 0),
                ["ArmInitZoomLen"]=0,
                ["DefaultFOV"]=90,
                ["ZoomMaxLen"]=0,
                ["ZoomMinLen"]=0,
                ["ZoomStep"]=0,
            },
        },
        ["ID"]="128122236",
        ["Preview"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15224.97, -6170.98, -1068.29),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
    },
    ["EP_MagicWallV2_1352675635"]={
        ["ActivateAudio"]="/Game/Arts/Audio/Events/Amb/Amb_3V3_Arena/Play_Amb_3V3_InvisibleWall_Idle.Play_Amb_3V3_InvisibleWall_Idle",
        ["ActorType"]=40046,
        ["BlockingType"]=0,
        ["BoxExtent"]=FVector(4045.00, 2520.48, 200),
        ["ID"]="1352675635",
        ["InActivateAudio"]="/Game/Arts/Audio/Events/Amb/Amb_3V3_Arena/Play_Amb_3V3_InvisibleWall_Disappear.Play_Amb_3V3_InvisibleWall_Disappear",
        ["NiagaraRelativeLocation"]=FVector(0, 0, 0),
        ["NiagaraSize"]=FVector2D(2, 1),
        ["SceneActorCommon"]={
            ["BelongType"]=1,
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14970, -4410, -1720),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bEnableUnidirectionalBlocking"]=false,
    },
    ["EP_MeshCarrier_4032664411"]={
        ["ActorType"]=40053,
        ["DissolveMaterialBySlot"]={
        },
        ["GroupID"]="3602987432",
        ["ID"]="4032664411",
        ["SceneActorCommon"]={
            ["Dissolve"]={
                ["DissolveInPeriod"]=FVector2D(0, 0),
                ["DissolveInTime"]=0,
                ["DissolveOutPeriod"]=FVector2D(0, 0),
                ["DissolveOutTime"]=1.50,
                ["DissolveOutType"]=1,
            },
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["StaticMesh_1"]={
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["StaticMesh"]="/Game/Arts/Environment/Mesh/Building/SM_GiantTing10P_HZ/Mesh/SM_TongYong/SM_GiantTing10P_daoju08.SM_GiantTing10P_daoju08",
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15560, -4420, -1510),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bCollisionIgnoreCamera"]=false,
    },
    ["EP_NiagaraCarrierV2_1067580838"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="1067580838",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15473.79, -4336.07, -1118.24),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_1245975318"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.50, 0.50, 0.50)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="1245975318",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15561.13, -4416.79, -1011.36),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_1356513310"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="1356513310",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15557.07, -4311.24, -1128.24),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_1679942830"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill07/NS_SHGJ_Skill07_warningLoop.NS_SHGJ_Skill07_warningLoop",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["ActorType"]=40032,
        ["ID"]="1679942830",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14690, -5270, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_2004750318"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="2004750318",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15482.24, -4492.14, -1134.31),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_2100808386"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.50)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="2100808386",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15675.81, -4419.03, -1139.04),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_2750921313"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="2750921313",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15636.86, -4335.58, -1130.49),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_3790985280"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="3790985280",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15441.50, -4424.13, -1131.49),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_3837706010"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="3837706010",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15560, -4540, -1140),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_454294993"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_LampFinish.NS_BOSS_SHGJ_Skill08_LampFinish",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="454294993",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15560, -4420, -1510),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_507646502"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill07/NS_SHGJ_Skill07_warningLoop.NS_SHGJ_Skill07_warningLoop",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["ActorType"]=40032,
        ["ID"]="507646502",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14800, -3550, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_769462786"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_LampLit.NS_BOSS_SHGJ_Skill08_LampLit",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="769462786",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15550, -4450, -1480),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_842193873"]={
        ["ActiveNiagara"]={
            ["Niagara"]="/Game/Arts/Effects/FX_Character/BOSS_SHGJ/Skill08/NS_BOSS_SHGJ_Skill08_drops.NS_BOSS_SHGJ_Skill08_drops",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(0.30, 0.30, 0.30)),
            ["bAllowScalability"]=true,
        },
        ["ActorType"]=40032,
        ["GroupID"]="3602987432",
        ["ID"]="842193873",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15634.51, -4496.83, -1127.26),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NiagaraCarrierV2_852188943"]={
        ["ActiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["ActorType"]=40032,
        ["ID"]="852188943",
        ["InactiveNiagara"]={
            ["Niagara"]="",
            ["NiagaraTickBehavior"]=0,
            ["RelativeTransform"]=FTransform(FQuat(0, -0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            ["bAllowScalability"]=false,
        },
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-16510, -3990, -1210),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_NpcSpawner_115556680"]={
        ["AOIRange"]=0,
        ["ActorType"]=60004,
        ["GroupMember"]={
            [1]={
                ["HalfHeight"]=0,
                ["InstanceID"]="606627332",
                ["Position"]=FVector(-17440, -4450, -1245.43),
                ["Rotator"]=FRotator(0, 0, 0),
                ["TemplateID"]=7102400,
                ["bForceBornPos"]=false,
            },
        },
        ["ID"]="115556680",
        ["LocationFormation"]={
        },
        ["PathType"]=0,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["Transform"]={
            ["Position"]=FVector(-17440, -4450, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["WayPathSpeed"]=200,
        ["WayPointPath"]="",
        ["bShowLinear"]=false,
        ["bStickGround"]=true,
        ["bUse3DRotation"]=false,
    },
    ["EP_Package_3602987432"]={
        ["ActorType"]=59999,
        ["ETemplateCategory"]=0,
        ["FlowchartName"]="",
        ["GroupChildsMap"]={
            [1]={
                ["ChildID"]="1067580838",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [2]={
                ["ChildID"]="1245975318",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [3]={
                ["ChildID"]="1356513310",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [4]={
                ["ChildID"]="2004750318",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [5]={
                ["ChildID"]="2100808386",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [6]={
                ["ChildID"]="2750921313",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [7]={
                ["ChildID"]="3790985280",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [8]={
                ["ChildID"]="3837706010",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [9]={
                ["ChildID"]="454294993",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [10]={
                ["ChildID"]="769462786",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [11]={
                ["ChildID"]="842193873",
                ["ClassName"]="EP_NiagaraCarrierV2",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [12]={
                ["ChildID"]="1845948940",
                ["ClassName"]="EP_ShapeTrigger",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
            [13]={
                ["ChildID"]="4032664411",
                ["ClassName"]="EP_MeshCarrier",
                ["CustomTransform"]={
                },
                ["TemplateName"]="",
                ["Transform"]=FTransform(FQuat(0, 0, 0, 1),FVector(0, 0, 0),FVector(1, 1, 1)),
            },
        },
        ["ID"]="3602987432",
        ["Radius"]=500,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15557.54, -4421.98, -1186.40),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_RespawnPoint_1425034926"]={
        ["ActorType"]=60006,
        ["ID"]="1425034926",
        ["Radius"]=500,
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14320, -4300, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bIsBornPoint"]=false,
    },
    ["EP_SceneText_1844658044"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=0.86,
                    ["G"]=0.93,
                    ["R"]=1,
                },
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=80.25,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="1844658044",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=true,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="进入切换一阶段场景",
            ["FadeInEffect"]={
                ["EffectType"]=0,
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]=0,
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]=1,
            ["TextColor"]={
                ["A"]=1,
                ["B"]=0.99,
                ["G"]=0.98,
                ["R"]=1,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-14970, -5190, -1340),
            ["Rotator"]=FRotator(21.35, 179.60, 4.83),
        },
    },
    ["EP_SceneText_4082546723"]={
        ["ActorType"]=40002,
        ["AdjustCamera"]=false,
        ["AoiRadius"]=3000,
        ["EndAudioEvent"]="",
        ["FontInfo"]={
            ["FontMaterial"]="/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
            ["FontObject"]="/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
            ["LetterSpacing"]=0,
            ["MonospacedWidth"]=1,
            ["OutlineSettings"]={
                ["OutlineColor"]={
                    ["A"]=1,
                    ["B"]=1,
                    ["G"]=0.97,
                    ["R"]=1.00,
                },
                ["OutlineSize"]=0,
                ["bApplyOutlineToDropShadows"]=false,
                ["bMiteredCorners"]=false,
                ["bSeparateFillAlpha"]=false,
            },
            ["Size"]=80.25,
            ["SkewAmount"]=0,
            ["TypefaceFontName"]="Title",
            ["bForceMonospaced"]=false,
            ["bMaterialIsStencil"]=false,
        },
        ["ID"]="4082546723",
        ["LoopAudioEvent"]="/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
        ["Radius"]=0,
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SceneTextExtend"]={
        },
        ["ShowInGame"]=true,
        ["StartAudioEvent"]="",
        ["TemplateID"]=0,
        ["TextInfo"]={
            ["DisplayText"]="进入切换二阶段场景",
            ["FadeInEffect"]={
                ["EffectType"]=0,
                ["TransitionDuration"]=0,
            },
            ["FadeOutEffect"]={
                ["EffectType"]=0,
                ["TransitionDuration"]=0,
            },
            ["GeneralEffect"]={
            },
            ["Justification"]=1,
            ["TextColor"]={
                ["A"]=1,
                ["B"]=1,
                ["G"]=0.95,
                ["R"]=0.96,
            },
        },
        ["Transform"]={
            ["Position"]=FVector(-15020, -3570, -1420),
            ["Rotator"]=FRotator(31.24, -178.70, -6.05),
        },
    },
    ["EP_ShapeTrigger_110459701"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["ID"]="110459701",
        ["Radius"]=500,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15410, -6270, -1550),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
    ["EP_ShapeTrigger_1845948940"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["GroupID"]="3602987432",
        ["ID"]="1845948940",
        ["Radius"]=500,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-15580, -4400, -1510),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
    ["EP_ShapeTrigger_2054432423"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["ID"]="2054432423",
        ["Radius"]=500,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14680, -5260, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
    ["EP_ShapeTrigger_4291612979"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["ID"]="4291612979",
        ["Radius"]=500,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-14810, -3560, -1520),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
}
