return{
    ["EP_BindViewController_3711751054"]={
        ["ActorType"]=40151,
        ["BindActors"]={
        },
        ["Coords"]={
        },
        ["ID"]="3711751054",
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1880, 0, 510),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_InteractiveChair_1587894457"]={
        ["ActorType"]=40001,
        ["ID"]="1587894457",
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=3,
        ["Transform"]={
            ["Position"]=FVector(-1850, -130, 504.50),
            ["Rotator"]=FRotator(0, -90.00, 0),
        },
    },
    ["EP_InteractiveChair_2670132233"]={
        ["ActorType"]=40001,
        ["ID"]="2670132233",
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=0,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["TemplateID"]=3,
        ["Transform"]={
            ["Position"]=FVector(-1850, -240, 504.50),
            ["Rotator"]=FRotator(0, -90, 0),
        },
    },
    ["EP_NonInteractMesh_1539092607"]={
        ["ActorType"]=40008,
        ["ID"]="1539092607",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/SM_Table01.SM_Table01",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1720, -240, 502),
            ["Rotator"]=FRotator(0, 90, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=true,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_163644987"]={
        ["ActorType"]=40008,
        ["ID"]="163644987",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Interact/Mesh/sm_CupCoffee_01_07_plate.sm_CupCoffee_01_07_plate",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1765, -238.50, 591),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_1840425813"]={
        ["ActorType"]=40008,
        ["ID"]="1840425813",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/ArtsTest/Maps/PCG_Demo/Store_Assets/OldBrickHouse/Meshes/Props/SM_CandleHolder_01.SM_CandleHolder_01",
        ["MeshBlueprint"]="/Game/Arts/SceneActorBP/MeshCombination/BP_MeshCombCandle.BP_MeshCombCandle_C",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1695.67, -272.50, 591),
            ["Rotator"]=FRotator(0, -30, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2040604750"]={
        ["ActorType"]=40008,
        ["ID"]="2040604750",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Tiengen_HZ/PublicWorks/Landmark/Mesh/SM_Chair01.SM_Chair01",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1850, -240, 504.50),
            ["Rotator"]=FRotator(0, -90, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2070709171"]={
        ["ActorType"]=40008,
        ["ID"]="2070709171",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/Gameplay/SM_Feynapotterpasta.SM_Feynapotterpasta",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1742.50, -215, 591),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2242694421"]={
        ["ActorType"]=40008,
        ["ID"]="2242694421",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Frame/SM_MeatFrame008.SM_MeatFrame008",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1765, -880, 611.30),
            ["Rotator"]=FRotator(0, 90, 0),
            ["Scale"]=FVector(0.50, 0.50, 0.50),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_2310282409"]={
        ["ActorType"]=40008,
        ["ID"]="2310282409",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/Gameplay/SM_Ryebread.SM_Ryebread",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1737.50, -145, 591),
            ["Rotator"]=FRotator(0, 50, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_236570663"]={
        ["ActorType"]=40008,
        ["ID"]="236570663",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/ArtsTest/Maps/PCG_Demo/User_Assets/Rome/Environment/Restaurant/GlassWater_01/sm_GlassWater_01_01.sm_GlassWater_01_01",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1716.17, -206.79, 591),
            ["Rotator"]=FRotator(0, -60, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3515851195"]={
        ["ActorType"]=40008,
        ["ID"]="3515851195",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/SM_Frame/SM_FruitFrame011.SM_FruitFrame011",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1645, -905.50, 611.50),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(0.75, 0.75, 0.75),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3619188899"]={
        ["ActorType"]=40008,
        ["ID"]="3619188899",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/ArtsTest/Maps/PCG_Demo/Store_Assets/OldBrickHouse/Meshes/Props/SM_CookingPot_04.SM_CookingPot_04",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1678, -894, 610.50),
            ["Rotator"]=FRotator(0, 90, 0),
            ["Scale"]=FVector(1.50, 1.50, 1.50),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_363059329"]={
        ["ActorType"]=40008,
        ["ID"]="363059329",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/ArtsTest/Maps/PCG_Demo/Store_Assets/OldBrickHouse/Meshes/Props/SM_CookingPot_04_Cover.SM_CookingPot_04_Cover",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1678, -894, 627),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1.50, 1.50, 1.50),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_3647510066"]={
        ["ActorType"]=40008,
        ["ID"]="3647510066",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Interact/Mesh/sm_CupCoffee_01_07_plate.sm_CupCoffee_01_07_plate",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1765, -115, 591),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_366655464"]={
        ["ActorType"]=40008,
        ["ID"]="366655464",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/ArtsTest/Environment/Mesh/Liesofp/Hotel_Prop_SM_Hotel_Candle_01_001.Hotel_Prop_SM_Hotel_Candle_01_001",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1715, -90, 593.50),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_4289625600"]={
        ["ActorType"]=40008,
        ["ID"]="4289625600",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Environment/Mesh/Props/Gameplay/SM_Oxtallsoup.SM_Oxtallsoup",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=1,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1730, -180, 591),
            ["Rotator"]=FRotator(0, 0, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=false,
        ["bUseBlueprint"]=false,
    },
    ["EP_NonInteractMesh_77085643"]={
        ["ActorType"]=40008,
        ["ID"]="77085643",
        ["MaterialOverlay"]="",
        ["Mesh"]="/Game/Arts/Effects/FX_Character/PlantMonster_FX/A_Monster_PM_Release/SM_Table01.SM_Table01",
        ["MeshBlueprint"]="",
        ["RelativeRotation"]=FRotator(0, 0, 0),
        ["SceneActorCommon"]={
            ["ExploreType"]=0,
            ["ExploreValue"]=0,
            ["InitialState"]=1,
            ["InsType"]=0,
            ["RewardID"]=0,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["StateToTag"]={
        },
        ["TemplateID"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1720, -120, 502),
            ["Rotator"]=FRotator(0, 90, 0),
            ["Scale"]=FVector(1, 1, 1),
        },
        ["bBlockCamera"]=false,
        ["bEnableMeshCollision"]=true,
        ["bUseBlueprint"]=false,
    },
    ["EP_NpcSingleSpawner_1330110503"]={
        ["ActorType"]=60005,
        ["AnimQueue"]={
            ["Queue"]={
                [1]={
                    ["AnimType"]={
                        ["AnimID"]="",
                        ["AssetID"]="A_F_LieDown_Loop",
                        ["StateName"]="",
                    },
                    ["Duration"]=-1,
                },
            },
            ["bAutoActivate"]=true,
            ["bLoop"]=false,
        },
        ["GroupMember"]={
            [1]={
                ["HalfHeight"]=0,
                ["InstanceID"]="1330110503",
                ["Position"]=FVector(-2257, -1680, 575),
                ["Rotator"]=FRotator(0, -90, 0),
                ["TemplateID"]=7250276,
            },
        },
        ["ID"]="1330110503",
        ["LookAtInstanceID"]="",
        ["PathType"]=0,
        ["SceneActorCommon"]={
            ["InsType"]=1,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["TemplateID"]=7250276,
        ["Transform"]={
            ["Position"]=FVector(-2257, -1680, 575),
            ["Rotator"]=FRotator(0, -90, 0),
        },
        ["WayPathSpeed"]=200,
        ["WayPointPath"]="",
        ["bBornWalkWayPath"]=false,
        ["bForceBornPos"]=false,
        ["bShowLinear"]=true,
        ["bStickGround"]=false,
    },
    ["EP_NpcSingleSpawner_4101586677"]={
        ["ActorType"]=60005,
        ["GroupMember"]={
            [1]={
                ["HalfHeight"]=0,
                ["InstanceID"]="4101586677",
                ["Position"]=FVector(-1797, -240, 578.57),
                ["Rotator"]=FRotator(0, 0, 0),
                ["TemplateID"]=7250276,
            },
        },
        ["ID"]="4101586677",
        ["LookAtInstanceID"]="",
        ["PathType"]=0,
        ["SceneActorCommon"]={
            ["InsType"]=1,
        },
        ["SpiritualVision"]={
            ["ShowType"]=0,
        },
        ["TemplateID"]=7250276,
        ["Transform"]={
            ["Position"]=FVector(-1797, -240, 578.57),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["WayPathSpeed"]=200,
        ["WayPointPath"]="",
        ["bBornWalkWayPath"]=false,
        ["bForceBornPos"]=false,
        ["bShowLinear"]=true,
        ["bStickGround"]=true,
    },
    ["EP_ShapeTrigger_2339431951"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["ID"]="2339431951",
        ["Radius"]=10,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1805, -240, 503.50),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
    ["EP_ShapeTrigger_2938060700"]={
        ["ActorType"]=60012,
        ["BoxExtent"]=FVector(500, 500, 500),
        ["ID"]="2938060700",
        ["Radius"]=10,
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["Shape"]=0,
        ["Transform"]={
            ["Position"]=FVector(-1760, -235, 600),
            ["Rotator"]=FRotator(0, 0, 0),
        },
        ["bShowPreviewMesh"]=true,
    },
    ["EP_TaskCollect_1086018389"]={
        ["ActorType"]=40035,
        ["ID"]="1086018389",
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["StartAnim"]={
            ["AnimID"]="",
            ["AssetID"]="StandCollection1",
            ["StateName"]="",
        },
        ["TemplateID"]=2400979,
        ["Transform"]={
            ["Position"]=FVector(-1590.50, -923, 639),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_TaskCollect_1535854792"]={
        ["ActorType"]=40035,
        ["ID"]="1535854792",
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["StartAnim"]={
            ["AnimID"]="",
            ["AssetID"]="StandCollection1",
            ["StateName"]="",
        },
        ["TemplateID"]=2400978,
        ["Transform"]={
            ["Position"]=FVector(-1764, -880, 624),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_TaskCollect_2341040122"]={
        ["ActorType"]=40035,
        ["ID"]="2341040122",
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["TemplateID"]=2400982,
        ["Transform"]={
            ["Position"]=FVector(-2330, 430, 570),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_TaskCollect_558293987"]={
        ["ActorType"]=40035,
        ["ID"]="558293987",
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["StartAnim"]={
            ["AnimID"]="",
            ["AssetID"]="StandCollection1",
            ["StateName"]="",
        },
        ["TemplateID"]=2400977,
        ["Transform"]={
            ["Position"]=FVector(-1645, -905.50, 623.50),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
    ["EP_TaskCollect_935351623"]={
        ["ActorType"]=40035,
        ["ID"]="935351623",
        ["SceneActorCommon"]={
            ["InsType"]=0,
        },
        ["StartAnim"]={
            ["AnimID"]="",
            ["AssetID"]="StandCollection1",
            ["StateName"]="",
        },
        ["TemplateID"]=2400976,
        ["Transform"]={
            ["Position"]=FVector(-1715.50, -896, 632),
            ["Rotator"]=FRotator(0, 0, 0),
        },
    },
}
