---@class Game
---@field NewUIManager NewUIManager
---@field DialogueManager DialogueManager
---@field TimerManager TimerManager
---@field Logger Logger
---@field EventsManager EventManager
---@field UIConfig UIConfig
---@field UIManager UIManager
---@field AutoNavigationSystem AutoNavigationSystem
---@field RedPointSystem RedPointSystem
---@field EquipmentSystem EquipmentSystem
---@field Arena50v50System Arena50v50System
---@field DungeonSystem DungeonSystem
---@field ClimateSystem ClimateSystem
---@field GuildMaterialTaskSystem GuildMaterialTaskSystem
---@field DungeonAwardSystem DungeonAwardSystem
---@field TeamAreanaSystem TeamAreanaSystem
---@field CreateRoleSystem CreateRoleSystem
---@field DungeonBattleStatisticsSystem DungeonBattleStatisticsSystem
---@field TipsSystem TipsSystem
---@field MailSystem MailSystem
---@field RankSystem RankSystem
---@field StatAtlasSystem StatAtlasSystem
---@field LoginSystem LoginSystem
---@field NewbieTaskSystem NewbieTaskSystem
---@field RechargeSystem RechargeSystem
---@field ActivitySystem ActivitySystem
---@field LoadingSystem LoadingSystem
---@field TraceSystem TraceSystem
---@field SeasonSystem SeasonSystem
---@field TeamSystem TeamSystem
---@field GroupSystem GroupSystem
---@field FriendSystem FriendSystem
---@field ChatSystem ChatSystem
---@field VoiceSystem VoiceSystem
---@field LocalResSystem LocalResSystem
---@field WebFileCacheSystem WebFileCacheSystem
---@field HttpSystem HttpSystem
---@field DiceCheckSystem DiceCheckSystem
---@field IndividualPVPSystem IndividualPVPSystem
---@field GuildSystem GuildSystem
---@field GuildDanceSystem GuildDanceSystem
---@field GuildLeagueSystem GuildLeagueSystem
---@field DanceSystem DanceSystem
---@field ManorSystem ManorSystem
---@field SkillCustomSystem SkillCustomSystem
---@field DropSystem DropSystem
---@filed GroupSystem GroupSystem
---@field TeaRoomSystem TeaRoomSystem
---@field WorldBossSystem WorldBossSystem
---@field ChatClubSystem ChatClubSystem
---@field ChatWhisperSystem ChatWhisperSystem
---@field CustomRoleSystem CustomRoleSystem
---@field FlameJumpSystem FlameJumpSystem
---@field TalkSystem TalkSystem
---@field HUDSystem HUDSystem
---@field SocialActionSystem SocialActionSystem
---@field RolePlayFortuneSystem RolePlayFortuneSystem
---@field SneakSystem SneakSystem
---@field ItemSubmitSystem ItemSubmitSystem
---@field SpiritualVisionSystem SpiritualVisionSystem
---@field NPCMoodSystem NPCMoodSystem
---@field FashionSystem FashionSystem
---@field SceneCustomSystem SceneCustomSystem
---@field MountSystem MountSystem
---@field QuestSystem QuestSystem
---@field NPCSystem NPCSystem
---@field ModuleLockSystem ModuleLockSystem
---@field PopTipsSystem PopTipsSystem
---@field GossipSystem GossipSystem
---@field NewbieGuideSystem NewbieGuideSystem
---@field CollectiblesSystem CollectiblesSystem
---@field PlotRecapSystem PlotRecapSystem
---@field TriggerTransitSystemSystem TriggerTransitSystemSystem
---@field MomentsSystem MomentsSystem
---@field DiscoverySystem DiscoverySystem
---@field AvatarBattleInteractSystem AvatarBattleInteractSystem
---@field TargetGuideSystem TargetGuideSystem
---@field SceneTextSystem SceneTextSystem
---@field TitleSystem TitleSystem
---@field TarotTeamSystem TarotTeamSystem
---@field ElementCoreSystem ElementCoreSystem
---@field AnnounceSystem AnnounceSystem
---@field BagSystem BagSystem
---@field WarehouseSystem WarehouseSystem
---@field ItemSystem ItemSystem
---@field TargetLockSystem TargetLockSystem
---@field MessageBoxSystem MessageBoxSystem
---@field StaminaSystem StaminaSystem
---@field MenuSystem MenuSystem
---@field ReasoningSystem ReasoningSystem
---@field ReportSystem ReportSystem
---@field InputSystem InputSystem
---@field UIJumpSystem UIJumpSystem
---@field NewHeadInfoSystem NewHeadInfoSystem
---@field RedDotSystem RedDotSystem
---@field Gameplay2DSystem Gameplay2DSystem
---@field ReminderManager ReminderManager
---@field RolePlaySystem RolePlaySystem
---@field PVPSystem PVPSystem
---@field BoxManSystem BoxManSystem

------------separation------------

---@class SystemConfig
local SystemConfig = {
    SYSTEM_CONFIG =
    {
        --autoGenerate
        {"RolePlaySystem",        "Gameplay.LogicSystem.RolePlay.System.RolePlaySystem"},
		{ "InputSystem",				   "Gameplay.LogicSystem.Input.System.InputSystem"},
        {"NewHeadInfoSystem",        "Gameplay.LogicSystem.NewHeadInfo.System.NewHeadInfoSystem"},
        { "MessageBoxSystem",          	   "Gameplay.LogicSystem.MessageBox.System.MessageBoxSystem" },
        { "OpenPanelCheckSystem",          "Gameplay.LogicSystem.OpenPanelCheck.OpenPanelCheckSystem" },
        { "ModuleLockSystem",              "Gameplay.LogicSystem.ModuleLock.ModuleLockSystem" },
        { "TitleSystem",                   "Gameplay.LogicSystem.Title.TitleSystem" },
        { "AutoNavigationSystem",          "Gameplay.LogicSystem.AutoNavigation.AutoNavigationSystem" },
        { "RedDotSystem",                   "Gameplay.LogicSystem.RedPoint.RedDotSystem"},
        { "RedPointSystem",                "Gameplay.LogicSystem.RedPoint.RedPointSystem" },
        { "DungeonSystem",                 "Gameplay.LogicSystem.Dungeon.DungeonSystem" },
        { "ClimateSystem",                 "Gameplay.LogicSystem.ClimateSystem.ClimateSystem" },
        { "TeamAreanaSystem",              "Gameplay.LogicSystem.TeamArena.TeamAreanaSystem" },
        { "DungeonAwardSystem",            "Gameplay.LogicSystem.Distribution.DungeonAwardSystem" },
        { "DialogueManager",               "Gameplay.DialogueSystem.DialogueManager" },
        { "CreateRoleSystem",              "Gameplay.LogicSystem.CreateRole.CreateRoleSystem" },
        { "DungeonBattleStatisticsSystem", "Gameplay.LogicSystem.DungeonBattleStatistics.DungeonBattleStatisticsSystem" },
        { "TipsSystem",                    "Gameplay.LogicSystem.Tips.TipsSystem" },
        { "MailSystem",                    "Gameplay.LogicSystem.Mail.MailSystem" },
        { "LetterSystem",                  "Gameplay.LogicSystem.Letter.LetterSystem" },
        { "EquipmentSystem",               "Gameplay.LogicSystem.Equipment.EquipmentSystem" },
        { "ChatSystem",                    "Gameplay.LogicSystem.Chat.System.ChatSystem" },
		{ "VoiceSystem",                   "Gameplay.LogicSystem.Chat.System.VoiceSystem" },
		{ "GuildMaterialTaskSystem",       "Gameplay.LogicSystem.Guild.GuildMaterialTask.GuildMaterialTaskSystem" },
		{ "LocalResSystem",                "Gameplay.LogicSystem.LocalResSystem.LocalResSystem"},
        { "WebFileCacheSystem",             "Gameplay.LogicSystem.LocalResSystem.WebFileCacheSystem"},
        { "HttpSystem",                    "Gameplay.LogicSystem.LocalResSystem.HttpSystem" },
        { "DiceCheckSystem",                    "Gameplay.LogicSystem.DiceCheck.System.DiceCheckSystem"},
        { "RankSystem",                    "Gameplay.LogicSystem.Rank.RankSystem" },
        -- {"RolePlayFortuneSystem",          "Gameplay.LogicSystem.RolePlay.RolePlayFortune.RolePlayFortuneSystem" },
        { "StatAtlasSystem",               "Gameplay.StatAtlasSystem.StatAtlasSystem" },
        { "LoginSystem",                   "Gameplay.LogicSystem.Login.LoginSystem" },
        -- { "NewbieTaskSystem",              "Gameplay.LogicSystem.NewbieTask.NewbieTaskSystem" },
        { "PhotographSystem",              "Gameplay.LogicSystem.PhotographSystem.System.PhotographSystem" },
        { "RechargeSystem",                "Gameplay.LogicSystem.Recharge.RechargeSystem" },
        { "ActivitySystem",                "Gameplay.LogicSystem.ActivitySystem.ActivitySystem" },
        { "LoadingSystem",                 "Gameplay.LogicSystem.Loading.LoadingSystem" },
        { "DungeonReviveSystem",           "Gameplay.LogicSystem.Dungeon.DungeonRevive.System.DungeonReviveSystem" },
        { "CurrencyExchangeSystem",        "Gameplay.LogicSystem.Currency.CurrencyExchange.CurrencyExchangeSystem" },
        { "TraceSystem",                   "Gameplay.LogicSystem.Trace.TraceSystem" },
        { "GuildSystem",                   "Gameplay.LogicSystem.Guild.GuildSystem" },
        { "GuildLeagueSystem",             "Gameplay.LogicSystem.GuildLeague.System.GuildLeagueSystem" },
        { "GuildDanceSystem",              "Gameplay.LogicSystem.Guild.GuildDance.System.GuildDanceSystem" },
        { "DanceSystem",                   "Gameplay.LogicSystem.Dance.System.DanceSystem" },
        { "CurrencySystem",                "Gameplay.LogicSystem.Currency.CurrencySystem" },
        { "WarehouseSystem",               "Gameplay.LogicSystem.BagSystem.Warehouse.WarehouseSystem" },
        { "BagSystem",                     "Gameplay.LogicSystem.BagSystem.System.BagSystem" },
        { "MenuSystem",                    "Gameplay.LogicSystem.Menu.MenuSystem" },
        { "ExchangeSystem",                "Gameplay.LogicSystem.Trade.Exchange.ExchangeSystem" },
        { "DepartmentStoreSystem",         "Gameplay.LogicSystem.Trade.DepartmentStore.DepartmentStoreSystem" },
        { "TeamSystem",                    "Gameplay.LogicSystem.Team.TeamSystem" },
        { "SeasonSystem",                  "Gameplay.LogicSystem.Season.SeasonSystem" },
        { "FriendSystem",                  "Gameplay.LogicSystem.Friend.FriendSystem" },
        { "GroupSystem",                   "Gameplay.LogicSystem.Group.GroupSystem" },
        { "RoleDisplaySystem",             "Gameplay.LogicSystem.PlayerDetails.RoleDisplaySystem" },
        { "HUDSystem",                     "Gameplay.LogicSystem.HUD.HUDSystem" },
        { "IndividualPVPSystem",           "Gameplay.LogicSystem.IndividualPVP.IndividualPVPSystem" },
        { "ManorSystem",                   "Gameplay.LogicSystem.Manor.Main.ManorSystem" },
        { "SkillCustomSystem",             "Gameplay.LogicSystem.SkillCustomizer_2.SkillCustomSystem" },
        { "DropSystem",                    "Gameplay.LogicSystem.Drop.DropSystem" },
        { "WorldBossSystem",               "Gameplay.LogicSystem.WorldBoss.WorldBossSystem" },
        { "ChatClubSystem",                "Gameplay.LogicSystem.Chat.ChatClub.ChatClubSystem" },
        { "ChatWhisperSystem",             "Gameplay.LogicSystem.Chat.ChatWhisper.ChatWhisperSystem" },
        { "CustomRoleSystem",              "Gameplay.LogicSystem.CustomRole.CustomRoleSystem" },
        { "PVPSystem",                     "Gameplay.LogicSystem.PVP.PVPSystem" },
        { "FlameJumpSystem",               "Gameplay.LogicSystem.FlameJump.FlameJumpSystem" },
        { "TalkSystem",                    "Gameplay.LogicSystem.Talk.TalkSystem" },
        { "SocialActionSystem",            "Gameplay.LogicSystem.SocialAction.SocialActionSystem" },
        { "SkillSystem",                   "Gameplay.LogicSystem.HUD.HUDSkill.SkillSystem" },
        { "SneakSystem",                   "Gameplay.LogicSystem.SneakSystem.SneakSystem"},
        { "ItemSubmitSystem",              "Gameplay.LogicSystem.ItemSubmit.ItemSubmitSystem"},
        { "SpiritualVisionSystem",         "Gameplay.LogicSystem.SpiritVision.SpiritualVisionSystem"},
        { "ArenaSystem",                   "Gameplay.LogicSystem.ArenaSystem.ArenaSystem" },
        { "DiscoverySystem",               "Gameplay.LogicSystem.Discovery.DiscoverySystem" },
        { "ScreenEffectSystem",            "Gameplay.LogicSystem.ScreenEffect.ScreenEffectSystem" },
        { "NPCMoodSystem",                 "Gameplay.LogicSystem.NPC.CutPrice.NPCMoodSystem" },
        { "MapSystem",                     "Gameplay.LogicSystem.MapV2.MapSystem"},
		{"FashionSystem",                  "Gameplay.LogicSystem.Fashion.FashionMain.System.FashionSystem"},
		{"SceneCustomSystem",              "Gameplay.LogicSystem.SceneCustom.SceneCustomSystem"},
        {"MountSystem",                    "Gameplay.LogicSystem.Mount.MountSystem"},
        {"QuestSystem",                    "Gameplay.LogicSystem.Quest.QuestSystem"},
        {"TargetGuideSystem",              "Gameplay.LogicSystem.TargetGuide.TargetGuideSystem"},
        {"NPCSystem",                      "Gameplay.LogicSystem.NPC.System.NPCSystem"},
        {"GossipSystem",                      "Gameplay.LogicSystem.Gossip.GossipSystem"},
        {"NPCCountDownSystem",              "Gameplay.LogicSystem.NPC.NPCCountDown.NPCCountDownSystem"},
        {"DamageEffectSystem",              "Gameplay.LogicSystem.DamageEffect.DamageEffectSystem"},
        {"AchievementSystem",              "Gameplay.LogicSystem.Achieve.Achievement.System.AchievementSystem"},
        {"CollectiblesSystem",             "Gameplay.LogicSystem.Collectibles.CollectiblesSystem"},
        {"PlotRecapSystem",                "Gameplay.LogicSystem.PlotRecap.System.PlotRecapSystem"},
        {"PopTipsSystem",                  "Gameplay.LogicSystem.PopTips.PopTipsSystem"},
        {"ItemSystem",                     "Gameplay.LogicSystem.Item.System.ItemSystem"},
        {"RedPacketSystem",                "Gameplay.LogicSystem.RedPacket.RedPacketSystem"},
        {"NewbieGuideSystem",              "Gameplay.LogicSystem.NewbieGuide.NewbieGuideSystem"},
		{"ReportSystem",	               "Gameplay.LogicSystem.Report.ReportSystem"},
		{"FateGiftSystem",		           "Gameplay.LogicSystem.FateGift.FateGiftSystem"},
        {"TradeManager",	               "Gameplay.LogicSystem.Trade.TradeManager"},
        {"AuctionManager",	               "Gameplay.LogicSystem.Trade.Auction.AuctionManager"},
		{"TriggerTransitSystem",		   "Gameplay.LogicSystem.Trigger.TriggerTransitSystem"},
        { "TeaRoomSystem",	               "Gameplay.LogicSystem.Chat.System.TeaRoomSystem"},
	    {"MomentsSystem",		           "Gameplay.LogicSystem.Moments.MomentsSystem"},
        {"AvatarBattleInteractSystem",	   "Gameplay.LogicSystem.AvatarBattleInteract.AvatarBattleInteractSystem"},
		{"SceneTextSystem", 			   "Gameplay.LogicSystem.SceneText.SceneTextSystem"},
		{"ArrodesDialogueSystem",          "Gameplay.LogicSystem.ArrodesDialogue.ArrodesDialogueSystem"},
        {"TarotTeamSystem",                "Gameplay.LogicSystem.TarotTeam.TarotTeamSystem"},
		{"ElementCoreSystem",              "Gameplay.LogicSystem.ElementCore.ElementCoreSystem"},
        {"WorldActivitySystem",       "Gameplay.LogicSystem.WorldActivity.WorldActivitySystem"},
		{"AnnounceSystem",				   "Gameplay.LogicSystem.Announce.System.AnnounceSystem"},
		{"GoddessExploreSystem",       "Gameplay.LogicSystem.Discovery.GoddessExplore.GoddessExploreSystem"},
		{"TargetLockSystem",                "Gameplay.LogicSystem.HUD.HUDTargetLock.TargetLockSystem"},
		{"StaminaSystem",					"Gameplay.LogicSystem.Stamina.StaminaSystem"},
		{"ReasoningSystem",					"Gameplay.LogicSystem.Reasoning.ReasoningSystem"},
		{"UIJumpSystem",					"Gameplay.LogicSystem.UIJump.UIJumpSystem"},
        {"TalentSystem",					"Gameplay.LogicSystem.Talent.TalentSystem"},
        {"ReminderManager",                 "Gameplay.LogicSystem.Reminder.ReminderManager"},
        {"MarqueeManager",                  "Gameplay.LogicSystem.Marquee.MarqueeManager"},
		{"Gameplay2DSystem",				"Gameplay.LogicSystem.Gameplay2D.Gameplay2DSystem"},
        { "SealedSystem",                  "Gameplay.LogicSystem.Sealed_2.SealedSystem" },
		{"BoxManSystem",                  	"Gameplay.LogicSystem.BoxMan.BoxManSystem"},
	}
}
return SystemConfig
