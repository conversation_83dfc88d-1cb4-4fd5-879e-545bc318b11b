return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 7216010,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor5",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["AppearanceID"] = 7216022,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["AppearanceID"] = 7216002,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["AppearanceID"] = 7216009,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor3",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [5] = {
      ["AppearanceID"] = 7216003,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor4",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "2255664694",
  ["AnchorNpc"] = "Actor1",
  ["AnchorType"] = 2,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_15",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 20,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 651.5099,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_16",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.6719,
          ["X"] = -0.0412,
          ["Y"] = -0.0376,
          ["Z"] = 0.7385,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 131.0659,
          ["Y"] = 621.5619,
          ["Z"] = 176.6722,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_17",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.9136,
          ["X"] = -0.0273,
          ["Y"] = -0.0623,
          ["Z"] = 0.401,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -228.5946,
          ["Y"] = 298.0601,
          ["Z"] = 179.7928,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 25,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 262.309,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_18",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1407,
          ["X"] = 0.0569,
          ["Y"] = 0.0081,
          ["Z"] = -0.9884,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 369.5608,
          ["Y"] = 101.9101,
          ["Z"] = 173.5881,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_19",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1043,
          ["X"] = 0.259,
          ["Y"] = 0.0281,
          ["Z"] = -0.9598,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 738.0608,
          ["Y"] = 231.6664,
          ["Z"] = 520.1093,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_20",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1336,
          ["X"] = -0.0778,
          ["Y"] = -0.0105,
          ["Z"] = 0.9879,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 163.7679,
          ["Y"] = 2.2279,
          ["Z"] = 178.5545,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 10,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 825.8726,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_21",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2426,
          ["X"] = -0.0929,
          ["Y"] = -0.0234,
          ["Z"] = 0.9654,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 884.682,
          ["Y"] = 322.8773,
          ["Z"] = 263.1637,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 105,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 70,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_22",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2706,
          ["X"] = 0.0134,
          ["Y"] = 0.0038,
          ["Z"] = 0.9626,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 104.7166,
          ["Y"] = 30.6758,
          ["Z"] = 158.2182,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "双人正视01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 70,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_23",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.9836,
          ["X"] = 0.0088,
          ["Y"] = 0.0498,
          ["Z"] = 0.1734,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 12.4531,
          ["Y"] = 33.4424,
          ["Z"] = 157.7309,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "双人正视02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_24",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4493,
          ["X"] = -0.0171,
          ["Y"] = -0.0086,
          ["Z"] = 0.8932,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 121.6453,
          ["Y"] = 84.0014,
          ["Z"] = 158.8494,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "同侧对话01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_25",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.9622,
          ["X"] = 0.0033,
          ["Y"] = 0.0118,
          ["Z"] = 0.2722,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -13.2563,
          ["Y"] = 46.8476,
          ["Z"] = 161.4816,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "同侧对话02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 190.263,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_26",
      ["OffsetZ"] = -18.7956,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1788,
          ["X"] = 0.0154,
          ["Y"] = 0.0028,
          ["Z"] = 0.9838,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 179.7882,
          ["Y"] = 52.201,
          ["Z"] = 77.6838,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_27",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2323,
          ["X"] = 0.0966,
          ["Y"] = 0.0232,
          ["Z"] = -0.9676,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 114.2642,
          ["Y"] = 50.6937,
          ["Z"] = 78.4438,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.9433,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_28",
      ["OffsetZ"] = -8.5276,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3305,
          ["X"] = 0.0099,
          ["Y"] = -0.0035,
          ["Z"] = 0.9438,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 125.5631,
          ["Y"] = -135.6693,
          ["Z"] = 66.0604,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 25,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 322.0011,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_29",
      ["OffsetZ"] = 28.1425,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2542,
          ["X"] = 0.096,
          ["Y"] = 0.0254,
          ["Z"] = -0.962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 298.3614,
          ["Y"] = 113.021,
          ["Z"] = 111.7644,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 85,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_30",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0872,
          ["X"] = 0.0104,
          ["Y"] = -0.0009,
          ["Z"] = -0.9961,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 97.1501,
          ["Y"] = -11.7124,
          ["Z"] = 81.2971,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor3近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_31",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1363,
          ["X"] = 0.1276,
          ["Y"] = -0.0177,
          ["Z"] = -0.9823,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 88.0445,
          ["Y"] = -14.0301,
          ["Z"] = 88.277,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor3中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [18] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 155.1409,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_32",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor4",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3478,
          ["X"] = 0.1109,
          ["Y"] = 0.0414,
          ["Z"] = -0.9301,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.8494,
          ["Y"] = 113.0769,
          ["Z"] = 96.1363,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor4近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [19] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_33",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor4",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3382,
          ["X"] = 0.0542,
          ["Y"] = -0.0195,
          ["Z"] = -0.9393,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 86.2971,
          ["Y"] = -47.6633,
          ["Z"] = 73.8248,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor4中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [20] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 326.6927,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 35,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9904,
          ["X"] = -0.0074,
          ["Y"] = 0.0588,
          ["Z"] = 0.1252,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -198.5498,
          ["Y"] = 30.7624,
          ["Z"] = 178.6587,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [21] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 50,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 200,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "None",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4958,
          ["X"] = 0.0892,
          ["Y"] = -0.0512,
          ["Z"] = -0.8623,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 218.1598,
          ["Y"] = -195.0634,
          ["Z"] = 212.6331,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [22] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 128.9851,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4419,
          ["X"] = 0.0641,
          ["Y"] = -0.0316,
          ["Z"] = -0.8942,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 106.7394,
          ["Y"] = -10.109,
          ["Z"] = 168.8843,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [23] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 50,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 167.5124,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.9158,
          ["X"] = -0.0147,
          ["Y"] = -0.0336,
          ["Z"] = 0.4,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -7.9667,
          ["Y"] = 181.9819,
          ["Z"] = 172.1188,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera4",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 26.5113,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 2.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [4] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [5] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [6] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 8.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [7] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 10.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [8] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 7,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_7",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [9] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 8,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_8",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 21.9113,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [10] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 9,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_9",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 23.4113,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [11] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 10,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_10",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 24.9113,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [12] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 11,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_11",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 26.4113,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_5",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "Actor1",
                },
                [2] = {
                  ["Delay"] = 0.2,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "Actor4",
                },
                [3] = {
                  ["Delay"] = 0.5,
                  ["Enable"] = true,
                  ["Looker"] = "Actor1",
                  ["Target"] = "Actor4",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "Actor2",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "Actor1",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [4] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor1",
                  ["Target"] = "Actor2",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.3395,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [5] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "Actor1",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.5,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [6] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "None",
                },
                [2] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "None",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [7] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "Actor3",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_7",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 20.5113,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [8] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "Actor4",
                },
                [2] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor4",
                  ["Target"] = "Actor2",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 25.0113,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [3] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_16",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景01",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_17",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景01",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_18",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景02",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_19",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景01",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_20",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景02",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_21",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "双人正视01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_22",
              ["Parent"] = "锚点",
              ["TrackName"] = "双人正视01",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "双人正视02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_23",
              ["Parent"] = "锚点",
              ["TrackName"] = "双人正视02",
              ["bAutoCameraTrack"] = false,
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "同侧对话01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_24",
              ["Parent"] = "锚点",
              ["TrackName"] = "同侧对话01",
              ["bAutoCameraTrack"] = false,
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "同侧对话02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_25",
              ["Parent"] = "锚点",
              ["TrackName"] = "同侧对话02",
              ["bAutoCameraTrack"] = false,
            },
            [11] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Short_Talk4",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "A_Base_Hello_Dark02",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 10.5,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_2",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.1167,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "科恩黎中点",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = true,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 12,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                    [2] = {
                      ["Duration"] = 1.6833,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "科恩黎终点",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = true,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 13.1167,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                    [3] = {
                      ["Duration"] = 2.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "科恩黎终点2",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = true,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 14.8,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 17.3176,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["ObjectName"] = "BP_DialogueTrackVisible_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor1近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_26",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "Actor1近景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor1中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_27",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "Actor1中景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [12] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Talk_No_A",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_1",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 2.3145,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 13.201,
                      ["Target"] = "科恩黎终点2",
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["Duration"] = 2.7147,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 18.9529,
                      ["Target"] = "Actor3",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor2近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_28",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "Actor2近景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor2中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_29",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "Actor2中景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [13] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = true,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "卡萝移动后位置",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 20.5113,
                      ["StickGround"] = false,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_1",
                  ["Parent"] = "",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_3",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 3,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_4",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                    [4] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 22.0113,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["ObjectName"] = "BP_DialogueTrackVisible_C_1",
                  ["Parent"] = "",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor3近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_30",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "Actor3近景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor3中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_31",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "Actor3中景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor3",
            },
            [14] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Short_Talk4",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_0",
                  ["Parent"] = "Actor4",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 2.3307,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 12.349,
                      ["Target"] = "科恩黎终点2",
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["Duration"] = 2.8356,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 18.5899,
                      ["Target"] = "Actor3",
                      ["bConstant"] = true,
                    },
                    [3] = {
                      ["Duration"] = 1.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 25.0113,
                      ["Target"] = "Actor2",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_1",
                  ["Parent"] = "Actor4",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor4近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_32",
                  ["Parent"] = "Actor4",
                  ["TrackName"] = "Actor4近景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor4中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_33",
                  ["Parent"] = "Actor4",
                  ["TrackName"] = "Actor4中景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor4",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor4",
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "科恩黎中点",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueTrackRoutePoint_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "科恩黎中点",
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "科恩黎终点",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueTrackRoutePoint_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "科恩黎终点",
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "科恩黎终点2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueTrackRoutePoint_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "科恩黎终点2",
            },
            [18] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera1",
              ["bAutoCameraTrack"] = false,
            },
            [19] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera2",
              ["bAutoCameraTrack"] = false,
            },
            [20] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera3",
              ["bAutoCameraTrack"] = false,
            },
            [21] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera4",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera4",
              ["bAutoCameraTrack"] = false,
            },
            [22] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "卡萝移动后位置",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueTrackRoutePoint_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "卡萝移动后位置",
            },
            [23] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 22.0113,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["ObjectName"] = "BP_DialogueTrackVisible_C_2",
                  ["Parent"] = "",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor5",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor5",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueTrackCamera_15",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 1,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 1,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "中景01",
              ["StartTime"] = 0,
              ["TargetCamera"] = "平视",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3,
              ["TargetCamera"] = "Actor4近景",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.5,
              ["TargetCamera"] = "Actor2近景",
              ["bConstant"] = true,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6,
              ["TargetCamera"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.5,
              ["TargetCamera"] = "Actor1近景",
              ["bConstant"] = true,
            },
            [6] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9,
              ["TargetCamera"] = "Actor2中景",
              ["bConstant"] = true,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_7",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 10.5,
              ["TargetCamera"] = "全景02",
              ["bConstant"] = true,
            },
            [8] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 5.0193,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_12",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12,
              ["TargetCamera"] = "远景01",
              ["bConstant"] = true,
            },
            [9] = {
              ["BreathAttenuation"] = 0.5,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 4,
              ["CameraBreathType"] = 4,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_8",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 20.5113,
              ["TargetCamera"] = "Camera1",
              ["bConstant"] = true,
            },
            [10] = {
              ["BreathAttenuation"] = 0.2,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 5,
              ["CameraName"] = "None",
              ["Duration"] = 0.8239,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_9",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 22.0113,
              ["TargetCamera"] = "Camera2",
              ["bConstant"] = true,
            },
            [11] = {
              ["BreathAttenuation"] = 0.5,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 7,
              ["CameraBreathType"] = 5,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_10",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 23.5113,
              ["TargetCamera"] = "Camera3",
              ["bConstant"] = true,
            },
            [12] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_11",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 25.0113,
              ["TargetCamera"] = "Camera4",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["ObjectName"] = "DialogueCameraCutAction_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "跟我走，我有办法追踪到阿诺德现在的位置。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor4",
              ["TalkerName"] = "戴莉",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "可是，要怎样……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 1.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "相信我，之后我会跟你解释。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 3,
              ["SubTitle"] = "",
              ["Talker"] = "Actor4",
              ["TalkerName"] = "戴莉",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_3",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "好，没问题，你来带路。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 4.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_4",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "科恩黎，麻烦回公司通知大家这起非凡事件。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_5",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "队长，我跟你一起去！毕竟是我弄丢了阿诺德……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 7.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_6",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "戴莉跟我一起，不用担心，我会带阿诺德回去的。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 9,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [8] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 8,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 7,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_7",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "哦哦，好的！那就拜托你们了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 10.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [9] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 9,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 8,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_8",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "拜托你了，带我们找到阿诺德吧，夜莺银哨。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 20.5113,
              ["SubTitle"] = "",
              ["Talker"] = "Actor4",
              ["TalkerName"] = "戴莉",
              ["bConstant"] = true,
            },
            [10] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 10,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 9,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_9",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "！！！",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 22.0113,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [11] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 11,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 10,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_10",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "是卡萝队长的遗物，它怎么会在这里……这就是你的任务？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 23.5113,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [12] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 12,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 11,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_11",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "说来话长，我们先去找到阿诺德吧。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 25.0113,
              ["SubTitle"] = "",
              ["Talker"] = "Actor4",
              ["TalkerName"] = "戴莉",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = true,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9848,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.1737,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 83.0329,
          ["Y"] = -384.0809,
          ["Z"] = -9.6392,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "科恩黎中点",
      ["bDefaultVisible"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0872,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -122.0885,
          ["Y"] = -834.6174,
          ["Z"] = -19.4634,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "科恩黎终点",
      ["bDefaultVisible"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0872,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -714.7061,
          ["Y"] = -734.5885,
          ["Z"] = -28.4579,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "科恩黎终点2",
      ["bDefaultVisible"] = true,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9848,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.1736,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 14.0867,
          ["Y"] = 111.1563,
          ["Z"] = 138.9214,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "卡萝移动后位置",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "支线·美梦成真·第二章第30段",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216022,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 78,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216002,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1736,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9848,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 140.8318,
          ["Y"] = -29.9591,
          ["Z"] = 93.6258,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216009,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_4",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.5736,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.8192,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 51.0054,
          ["Y"] = 113.1938,
          ["Z"] = 138.9214,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor3",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216003,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_5",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.342,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9397,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 121.5315,
          ["Y"] = 77.9013,
          ["Z"] = 91.2509,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor4",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216010,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4226,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9063,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 14.0868,
          ["Y"] = 111.1563,
          ["Z"] = 148.9214,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor5",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
    [3] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraCutTrack Name=\"DialogueCameraCutAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0'\"",
      [5] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_11'\"",
      [6] = "      End Object",
      [7] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_10'\"",
      [8] = "      End Object",
      [9] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_9'\"",
      [10] = "      End Object",
      [11] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_8'\"",
      [12] = "      End Object",
      [13] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_12'\"",
      [14] = "      End Object",
      [15] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_7'\"",
      [16] = "      End Object",
      [17] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_6'\"",
      [18] = "      End Object",
      [19] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_5'\"",
      [20] = "      End Object",
      [21] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_4'\"",
      [22] = "      End Object",
      [23] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_3'\"",
      [24] = "      End Object",
      [25] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_0'\"",
      [26] = "      End Object",
      [27] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_1'\"",
      [28] = "      End Object",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_15'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [33] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_6'\"",
      [34] = "      End Object",
      [35] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_5'\"",
      [36] = "      End Object",
      [37] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_4'\"",
      [38] = "      End Object",
      [39] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_3'\"",
      [40] = "      End Object",
      [41] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_2'\"",
      [42] = "      End Object",
      [43] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_1'\"",
      [44] = "      End Object",
      [45] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_0'\"",
      [46] = "      End Object",
      [47] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_7'\"",
      [48] = "      End Object",
      [49] = "   End Object",
      [50] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [51] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C Name=\"BP_DialogueTrackVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0'\"",
      [57] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0.BPS_ActorVisible_C_0'\"",
      [58] = "      End Object",
      [59] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0.BPS_ActorVisible_C_1'\"",
      [60] = "      End Object",
      [61] = "   End Object",
      [62] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [63] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_2'\"",
      [64] = "      End Object",
      [65] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_1'\"",
      [66] = "      End Object",
      [67] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [68] = "      End Object",
      [69] = "   End Object",
      [70] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2'\"",
      [71] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2.BPS_PlayAnimation_C_0'\"",
      [72] = "      End Object",
      [73] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2.BPS_PlayAnimation_C_1'\"",
      [74] = "      End Object",
      [75] = "   End Object",
      [76] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_27'\"",
      [77] = "   End Object",
      [78] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_26'\"",
      [79] = "   End Object",
      [80] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0'\"",
      [81] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_1'\"",
      [82] = "      End Object",
      [83] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [84] = "      End Object",
      [85] = "   End Object",
      [86] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [87] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [88] = "      End Object",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_29\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_29'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_28\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_28'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C Name=\"BP_DialogueTrackVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1'\"",
      [95] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_0'\"",
      [96] = "      End Object",
      [97] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_4'\"",
      [98] = "      End Object",
      [99] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_3'\"",
      [100] = "      End Object",
      [101] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_1'\"",
      [102] = "      End Object",
      [103] = "   End Object",
      [104] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1'\"",
      [105] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1.BPS_Transform_C_0'\"",
      [106] = "      End Object",
      [107] = "   End Object",
      [108] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_31\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_31'\"",
      [109] = "   End Object",
      [110] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_30\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_30'\"",
      [111] = "   End Object",
      [112] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [113] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_2'\"",
      [114] = "      End Object",
      [115] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_1'\"",
      [116] = "      End Object",
      [117] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_0'\"",
      [118] = "      End Object",
      [119] = "   End Object",
      [120] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [121] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [122] = "      End Object",
      [123] = "   End Object",
      [124] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_33\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_33'\"",
      [125] = "   End Object",
      [126] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_32\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_32'\"",
      [127] = "   End Object",
      [128] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C Name=\"BP_DialogueTrackVisible_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2'\"",
      [129] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2.BPS_ActorVisible_C_1'\"",
      [130] = "      End Object",
      [131] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2.BPS_ActorVisible_C_2'\"",
      [132] = "      End Object",
      [133] = "   End Object",
      [134] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueTrackRoutePoint_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_3'\"",
      [135] = "   End Object",
      [136] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [137] = "   End Object",
      [138] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [139] = "   End Object",
      [140] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [143] = "   End Object",
      [144] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueTrackRoutePoint_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_2'\"",
      [145] = "   End Object",
      [146] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueTrackRoutePoint_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_1'\"",
      [147] = "   End Object",
      [148] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueTrackRoutePoint_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_0'\"",
      [149] = "   End Object",
      [150] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_5'\"",
      [151] = "   End Object",
      [152] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_3'\"",
      [153] = "   End Object",
      [154] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_2'\"",
      [155] = "   End Object",
      [156] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_24'\"",
      [157] = "   End Object",
      [158] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_23'\"",
      [159] = "   End Object",
      [160] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_22'\"",
      [161] = "   End Object",
      [162] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_21'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_20'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_19'\"",
      [167] = "   End Object",
      [168] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_18'\"",
      [169] = "   End Object",
      [170] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_17'\"",
      [171] = "   End Object",
      [172] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_16'\"",
      [173] = "   End Object",
      [174] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_3'\"",
      [175] = "   End Object",
      [176] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_2'\"",
      [177] = "   End Object",
      [178] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_1'\"",
      [179] = "   End Object",
      [180] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_0'\"",
      [181] = "   End Object",
      [182] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [183] = "   End Object",
      [184] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [185] = "   End Object",
      [186] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [187] = "   End Object",
      [188] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [189] = "   End Object",
      [190] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_33\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_33'\"",
      [191] = "   End Object",
      [192] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_32\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_32'\"",
      [193] = "   End Object",
      [194] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_31\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_31'\"",
      [195] = "   End Object",
      [196] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_30\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_30'\"",
      [197] = "   End Object",
      [198] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_29\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_29'\"",
      [199] = "   End Object",
      [200] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_28\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_28'\"",
      [201] = "   End Object",
      [202] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_27\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_27'\"",
      [203] = "   End Object",
      [204] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_26'\"",
      [205] = "   End Object",
      [206] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_25'\"",
      [207] = "   End Object",
      [208] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_25'\"",
      [209] = "   End Object",
      [210] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_24'\"",
      [211] = "   End Object",
      [212] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [213] = "   End Object",
      [214] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [215] = "   End Object",
      [216] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [217] = "   End Object",
      [218] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [219] = "   End Object",
      [220] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [221] = "   End Object",
      [222] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [223] = "   End Object",
      [224] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [225] = "   End Object",
      [226] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [227] = "   End Object",
      [228] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [229] = "   End Object",
      [230] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [231] = "   End Object",
      [232] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_5'\"",
      [233] = "   End Object",
      [234] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_4'\"",
      [235] = "   End Object",
      [236] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [237] = "   End Object",
      [238] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [239] = "   End Object",
      [240] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [241] = "   End Object",
      [242] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [243] = "   End Object",
      [244] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [245] = "   End Object",
      [246] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [247] = "   End Object",
      [248] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [249] = "   End Object",
      [250] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_14\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_14'\"",
      [251] = "   End Object",
      [252] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_14'\"",
      [253] = "   End Object",
      [254] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_15\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_15'\"",
      [255] = "   End Object",
      [256] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_15'\"",
      [257] = "   End Object",
      [258] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_16\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_16'\"",
      [259] = "   End Object",
      [260] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_16'\"",
      [261] = "   End Object",
      [262] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_17\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_17'\"",
      [263] = "   End Object",
      [264] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_17'\"",
      [265] = "   End Object",
      [266] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_18\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_18'\"",
      [267] = "   End Object",
      [268] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_18'\"",
      [269] = "   End Object",
      [270] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_19\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_19'\"",
      [271] = "   End Object",
      [272] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_19'\"",
      [273] = "   End Object",
      [274] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_20\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_20'\"",
      [275] = "   End Object",
      [276] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_20'\"",
      [277] = "   End Object",
      [278] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_21\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_21'\"",
      [279] = "   End Object",
      [280] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_21'\"",
      [281] = "   End Object",
      [282] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_22\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_22'\"",
      [283] = "   End Object",
      [284] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_22'\"",
      [285] = "   End Object",
      [286] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_23\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_23'\"",
      [287] = "   End Object",
      [288] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_23'\"",
      [289] = "   End Object",
      [290] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [291] = "   End Object",
      [292] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [293] = "   End Object",
      [294] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [295] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [296] = "      End Object",
      [297] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [298] = "      End Object",
      [299] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [300] = "      End Object",
      [301] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [302] = "      End Object",
      [303] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [304] = "      End Object",
      [305] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [306] = "      End Object",
      [307] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [308] = "      End Object",
      [309] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_7'\"",
      [310] = "      End Object",
      [311] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_8'\"",
      [312] = "      End Object",
      [313] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_9'\"",
      [314] = "      End Object",
      [315] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_10'\"",
      [316] = "      End Object",
      [317] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_11'\"",
      [318] = "      End Object",
      [319] = "   End Object",
      [320] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [321] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_0'\"",
      [322] = "      End Object",
      [323] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_1'\"",
      [324] = "      End Object",
      [325] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_2'\"",
      [326] = "      End Object",
      [327] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_3'\"",
      [328] = "      End Object",
      [329] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_4'\"",
      [330] = "      End Object",
      [331] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_5'\"",
      [332] = "      End Object",
      [333] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_6'\"",
      [334] = "      End Object",
      [335] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_7'\"",
      [336] = "      End Object",
      [337] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_8'\"",
      [338] = "      End Object",
      [339] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_9'\"",
      [340] = "      End Object",
      [341] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_10'\"",
      [342] = "      End Object",
      [343] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_11'\"",
      [344] = "      End Object",
      [345] = "   End Object",
      [346] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [347] = "   End Object",
      [348] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [349] = "   End Object",
      [350] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [351] = "   End Object",
      [352] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [353] = "   End Object",
      [354] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [355] = "   End Object",
      [356] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [357] = "   End Object",
      [358] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [359] = "   End Object",
      [360] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [361] = "   End Object",
      [362] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [363] = "   End Object",
      [364] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [365] = "   End Object",
      [366] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [367] = "   End Object",
      [368] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [369] = "   End Object",
      [370] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [371] = "   End Object",
      [372] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [373] = "   End Object",
      [374] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [375] = "   End Object",
      [376] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [377] = "   End Object",
      [378] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [379] = "   End Object",
      [380] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [381] = "   End Object",
      [382] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [383] = "   End Object",
      [384] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [385] = "   End Object",
      [386] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [387] = "   End Object",
      [388] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [389] = "   End Object",
      [390] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [391] = "   End Object",
      [392] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [393] = "   End Object",
      [394] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [395] = "   End Object",
      [396] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [397] = "      EpisodeID=1",
      [398] = "   End Object",
      [399] = "   Begin Object Name=\"DialogueCameraCutAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0'\"",
      [400] = "      Begin Object Name=\"BPS_CameraCut_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_11'\"",
      [401] = "         TargetCamera=(CameraName=\"Camera4\")",
      [402] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [403] = "         LineGUIDLinked=2369900021",
      [404] = "         OwnedEpisodeID=1",
      [405] = "         StartTime=25.011299",
      [406] = "         Duration=1.500000",
      [407] = "      End Object",
      [408] = "      Begin Object Name=\"BPS_CameraCut_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_10'\"",
      [409] = "         CameraBreathType=NewEnumerator2",
      [410] = "         BreathAttenuation=0.500000",
      [411] = "         BreathSpeed=7.000000",
      [412] = "         TargetCamera=(CameraName=\"Camera3\")",
      [413] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [414] = "         LineGUIDLinked=3371102512",
      [415] = "         OwnedEpisodeID=1",
      [416] = "         StartTime=23.511299",
      [417] = "         Duration=1.500000",
      [418] = "      End Object",
      [419] = "      Begin Object Name=\"BPS_CameraCut_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_9'\"",
      [420] = "         CameraBreathType=NewEnumerator2",
      [421] = "         BreathAttenuation=0.200000",
      [422] = "         BreathSpeed=10.000000",
      [423] = "         TargetCamera=(CameraName=\"Camera2\")",
      [424] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [425] = "         LineGUIDLinked=2534311944",
      [426] = "         OwnedEpisodeID=1",
      [427] = "         StartTime=22.011299",
      [428] = "         Duration=0.823921",
      [429] = "      End Object",
      [430] = "      Begin Object Name=\"BPS_CameraCut_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_8'\"",
      [431] = "         CameraBreathType=NewEnumerator4",
      [432] = "         BreathAttenuation=0.500000",
      [433] = "         BreathSpeed=4.000000",
      [434] = "         TargetCamera=(CameraName=\"Camera1\")",
      [435] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [436] = "         LineGUIDLinked=3159574569",
      [437] = "         OwnedEpisodeID=1",
      [438] = "         StartTime=20.511299",
      [439] = "         Duration=1.500000",
      [440] = "      End Object",
      [441] = "      Begin Object Name=\"BPS_CameraCut_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_12'\"",
      [442] = "         TargetCamera=(CameraName=\"远景01\")",
      [443] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [444] = "         OwnedEpisodeID=1",
      [445] = "         StartTime=12.000000",
      [446] = "         Duration=5.019295",
      [447] = "      End Object",
      [448] = "      Begin Object Name=\"BPS_CameraCut_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_7'\"",
      [449] = "         TargetCamera=(CameraName=\"全景02\")",
      [450] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [451] = "         LineGUIDLinked=629977742",
      [452] = "         OwnedEpisodeID=1",
      [453] = "         StartTime=10.500000",
      [454] = "         Duration=1.500000",
      [455] = "      End Object",
      [456] = "      Begin Object Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_6'\"",
      [457] = "         TargetCamera=(CameraName=\"Actor2中景\")",
      [458] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [459] = "         LineGUIDLinked=3592249620",
      [460] = "         OwnedEpisodeID=1",
      [461] = "         StartTime=9.000000",
      [462] = "         Duration=1.500000",
      [463] = "      End Object",
      [464] = "      Begin Object Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_5'\"",
      [465] = "         TargetCamera=(CameraName=\"Actor1近景\")",
      [466] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [467] = "         LineGUIDLinked=1727624281",
      [468] = "         OwnedEpisodeID=1",
      [469] = "         StartTime=7.500000",
      [470] = "         Duration=1.500000",
      [471] = "      End Object",
      [472] = "      Begin Object Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_4'\"",
      [473] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [474] = "         LineGUIDLinked=217142600",
      [475] = "         OwnedEpisodeID=1",
      [476] = "         StartTime=6.000000",
      [477] = "         Duration=1.500000",
      [478] = "      End Object",
      [479] = "      Begin Object Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_3'\"",
      [480] = "         TargetCamera=(CameraName=\"Actor2近景\")",
      [481] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [482] = "         LineGUIDLinked=1374171887",
      [483] = "         OwnedEpisodeID=1",
      [484] = "         StartTime=4.500000",
      [485] = "         Duration=1.500000",
      [486] = "      End Object",
      [487] = "      Begin Object Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_0'\"",
      [488] = "         CameraBreathType=NewEnumerator0",
      [489] = "         BreathAttenuation=1.000000",
      [490] = "         BreathSpeed=10.000000",
      [491] = "         TargetCamera=(CameraName=\"平视\")",
      [492] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [493] = "         SectionName=NSLOCTEXT(\"\", \"7E0F47144C26A0AF14D2E4B76FE000B8\", \"中景01\")",
      [494] = "         FromLineIndex=0",
      [495] = "         LineGUIDLinked=2612427425",
      [496] = "         OwnedEpisodeID=1",
      [497] = "         Duration=3.000000",
      [498] = "      End Object",
      [499] = "      Begin Object Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_1'\"",
      [500] = "         TargetCamera=(CameraName=\"Actor4近景\")",
      [501] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [502] = "         OwnedEpisodeID=1",
      [503] = "         StartTime=3.000000",
      [504] = "         Duration=1.500000",
      [505] = "      End Object",
      [506] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_0'\"",
      [507] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_1'\"",
      [508] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_3'\"",
      [509] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_4'\"",
      [510] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_5'\"",
      [511] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_6'\"",
      [512] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_7'\"",
      [513] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_12'\"",
      [514] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_8'\"",
      [515] = "      ActionSections(9)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_9'\"",
      [516] = "      ActionSections(10)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_10'\"",
      [517] = "      ActionSections(11)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_11'\"",
      [518] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'\"",
      [519] = "      FromTemplate=False",
      [520] = "   End Object",
      [521] = "   Begin Object Name=\"DialogueTrackCamera_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_15'\"",
      [522] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [523] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_16'\"",
      [524] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_17'\"",
      [525] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_18'\"",
      [526] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_19'\"",
      [527] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_20'\"",
      [528] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_21'\"",
      [529] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_22'\"",
      [530] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_23'\"",
      [531] = "      Childs(8)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_24'\"",
      [532] = "      Childs(9)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_25'\"",
      [533] = "      Childs(10)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [534] = "      Childs(11)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_3'\"",
      [535] = "      Childs(12)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [536] = "      Childs(13)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_5'\"",
      [537] = "      Childs(14)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueTrackRoutePoint_0'\"",
      [538] = "      Childs(15)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueTrackRoutePoint_1'\"",
      [539] = "      Childs(16)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueTrackRoutePoint_2'\"",
      [540] = "      Childs(17)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [541] = "      Childs(18)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_1'\"",
      [542] = "      Childs(19)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_2'\"",
      [543] = "      Childs(20)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_3'\"",
      [544] = "      Childs(21)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueTrackRoutePoint_3'\"",
      [545] = "      Childs(22)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_1'\"",
      [546] = "      TrackName=\"锚点\"",
      [547] = "   End Object",
      [548] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [549] = "      Begin Object Name=\"BPS_LookAt_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_6'\"",
      [550] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor4\"))",
      [551] = "         LookAtInfo(1)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor2\"))",
      [552] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [553] = "         LineGUIDLinked=2369900021",
      [554] = "         OwnedEpisodeID=1",
      [555] = "         StartTime=25.011299",
      [556] = "      End Object",
      [557] = "      Begin Object Name=\"BPS_LookAt_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_5'\"",
      [558] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"))",
      [559] = "         LookAtInfo(1)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"))",
      [560] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [561] = "         OwnedEpisodeID=1",
      [562] = "         StartTime=12.000000",
      [563] = "      End Object",
      [564] = "      Begin Object Name=\"BPS_LookAt_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_4'\"",
      [565] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor1\"))",
      [566] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [567] = "         OwnedEpisodeID=1",
      [568] = "         StartTime=7.500000",
      [569] = "      End Object",
      [570] = "      Begin Object Name=\"BPS_LookAt_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_3'\"",
      [571] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor1\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor2\"))",
      [572] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [573] = "         OwnedEpisodeID=1",
      [574] = "         StartTime=6.339486",
      [575] = "      End Object",
      [576] = "      Begin Object Name=\"BPS_LookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_2'\"",
      [577] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor1\"))",
      [578] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [579] = "         OwnedEpisodeID=1",
      [580] = "         StartTime=6.000000",
      [581] = "      End Object",
      [582] = "      Begin Object Name=\"BPS_LookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_1'\"",
      [583] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor2\"))",
      [584] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [585] = "         OwnedEpisodeID=1",
      [586] = "         StartTime=3.000000",
      [587] = "      End Object",
      [588] = "      Begin Object Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_0'\"",
      [589] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor1\"))",
      [590] = "         LookAtInfo(1)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor4\"),Delay_11_85BDFA3A440A4FCF09F0788FF3E2BE0C=0.200000)",
      [591] = "         LookAtInfo(2)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor1\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor4\"),Delay_11_85BDFA3A440A4FCF09F0788FF3E2BE0C=0.500000)",
      [592] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [593] = "         OwnedEpisodeID=1",
      [594] = "      End Object",
      [595] = "      Begin Object Name=\"BPS_LookAt_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_7'\"",
      [596] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor4\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor3\"))",
      [597] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [598] = "         LineGUIDLinked=3159574569",
      [599] = "         OwnedEpisodeID=1",
      [600] = "         StartTime=20.511299",
      [601] = "      End Object",
      [602] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_0'\"",
      [603] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_1'\"",
      [604] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_2'\"",
      [605] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_3'\"",
      [606] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_4'\"",
      [607] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_5'\"",
      [608] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_7'\"",
      [609] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_6'\"",
      [610] = "      FromTemplate=False",
      [611] = "   End Object",
      [612] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [613] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [614] = "         NodeGuid=AFEAE54A483193DB469EE9914658A185",
      [615] = "         CustomProperties Pin (PinId=682B753646F1750F849AF5B40E20F3C6,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_2 2779224B44C4C71FAD1A3BAADFE5FCDE,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [616] = "      End Object",
      [617] = "      Begin Object Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [618] = "         EpisodeID=1",
      [619] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_1'\"",
      [620] = "         NodePosX=300",
      [621] = "         NodeGuid=1363CDF14A0509B932D027966C2BF164",
      [622] = "         CustomProperties Pin (PinId=2779224B44C4C71FAD1A3BAADFE5FCDE,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 682B753646F1750F849AF5B40E20F3C6,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [623] = "      End Object",
      [624] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [625] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [626] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_2'\"",
      [627] = "      GraphGuid=2C6DBCC54F04934B18C4ECBA5E46E8AB",
      [628] = "   End Object",
      [629] = "   Begin Object Name=\"BP_DialogueTrackVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0'\"",
      [630] = "      Begin Object Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0.BPS_ActorVisible_C_0'\"",
      [631] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_0'\"",
      [632] = "         OwnedEpisodeID=1",
      [633] = "         StartTime=17.317619",
      [634] = "      End Object",
      [635] = "      Begin Object Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0.BPS_ActorVisible_C_1'\"",
      [636] = "         Visible=True",
      [637] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_0'\"",
      [638] = "         OwnedEpisodeID=1",
      [639] = "      End Object",
      [640] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_1'\"",
      [641] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_0'\"",
      [642] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [643] = "   End Object",
      [644] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [645] = "      Begin Object Name=\"BPS_Transform_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_2'\"",
      [646] = "         Run=True",
      [647] = "         MoveTarget=(TrackName=\"科恩黎终点2\")",
      [648] = "         StickGround=True",
      [649] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [650] = "         OwnedEpisodeID=1",
      [651] = "         StartTime=14.800000",
      [652] = "         Duration=2.500000",
      [653] = "      End Object",
      [654] = "      Begin Object Name=\"BPS_Transform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_1'\"",
      [655] = "         Run=True",
      [656] = "         MoveTarget=(TrackName=\"科恩黎终点\")",
      [657] = "         StickGround=True",
      [658] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [659] = "         OwnedEpisodeID=1",
      [660] = "         StartTime=13.116658",
      [661] = "         Duration=1.683342",
      [662] = "      End Object",
      [663] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [664] = "         Run=True",
      [665] = "         MoveTarget=(TrackName=\"科恩黎中点\")",
      [666] = "         StickGround=True",
      [667] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [668] = "         OwnedEpisodeID=1",
      [669] = "         StartTime=12.000000",
      [670] = "         Duration=1.116658",
      [671] = "      End Object",
      [672] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [673] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_1'\"",
      [674] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_2'\"",
      [675] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [676] = "   End Object",
      [677] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2'\"",
      [678] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2.BPS_PlayAnimation_C_0'\"",
      [679] = "         AnimLibItem=(AssetID=\"A_Base_Hello_Dark02\")",
      [680] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_2'\"",
      [681] = "         OwnedEpisodeID=1",
      [682] = "         StartTime=10.500000",
      [683] = "         Duration=1.500000",
      [684] = "      End Object",
      [685] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_2.BPS_PlayAnimation_C_1'\"",
      [686] = "         AnimLibItem=(AssetID=\"Short_Talk4\")",
      [687] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_2'\"",
      [688] = "         OwnedEpisodeID=1",
      [689] = "         StartTime=7.500000",
      [690] = "         Duration=1.500000",
      [691] = "      End Object",
      [692] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [693] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [694] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [695] = "   End Object",
      [696] = "   Begin Object Name=\"DialogueTrackCamera_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_27'\"",
      [697] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_27'\"",
      [698] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [699] = "      TrackName=\"Actor1中景\"",
      [700] = "   End Object",
      [701] = "   Begin Object Name=\"DialogueTrackCamera_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_26'\"",
      [702] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_26'\"",
      [703] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_2'\"",
      [704] = "      TrackName=\"Actor1近景\"",
      [705] = "   End Object",
      [706] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0'\"",
      [707] = "      Begin Object Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_1'\"",
      [708] = "         Target=(TrackName=\"Actor3\")",
      [709] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [710] = "         OwnedEpisodeID=1",
      [711] = "         StartTime=18.952887",
      [712] = "         Duration=2.714741",
      [713] = "      End Object",
      [714] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [715] = "         Target=(TrackName=\"科恩黎终点2\")",
      [716] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [717] = "         OwnedEpisodeID=1",
      [718] = "         StartTime=13.201021",
      [719] = "         Duration=2.314530",
      [720] = "      End Object",
      [721] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [722] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_1'\"",
      [723] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_3'\"",
      [724] = "   End Object",
      [725] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [726] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [727] = "         AnimLibItem=(AssetID=\"Talk_No_A\")",
      [728] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [729] = "         OwnedEpisodeID=1",
      [730] = "         StartTime=9.000000",
      [731] = "         Duration=1.500000",
      [732] = "      End Object",
      [733] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [734] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_3'\"",
      [735] = "   End Object",
      [736] = "   Begin Object Name=\"DialogueTrackCamera_29\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_29'\"",
      [737] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_29'\"",
      [738] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_3'\"",
      [739] = "      TrackName=\"Actor2中景\"",
      [740] = "   End Object",
      [741] = "   Begin Object Name=\"DialogueTrackCamera_28\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_28'\"",
      [742] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_28'\"",
      [743] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_3'\"",
      [744] = "      TrackName=\"Actor2近景\"",
      [745] = "   End Object",
      [746] = "   Begin Object Name=\"BP_DialogueTrackVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1'\"",
      [747] = "      Begin Object Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_0'\"",
      [748] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [749] = "         LineGUIDLinked=2534311944",
      [750] = "         OwnedEpisodeID=1",
      [751] = "         StartTime=22.011299",
      [752] = "      End Object",
      [753] = "      Begin Object Name=\"BPS_ActorVisible_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_4'\"",
      [754] = "         Visible=True",
      [755] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [756] = "         OwnedEpisodeID=1",
      [757] = "         StartTime=7.500000",
      [758] = "      End Object",
      [759] = "      Begin Object Name=\"BPS_ActorVisible_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_3'\"",
      [760] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [761] = "         OwnedEpisodeID=1",
      [762] = "         StartTime=3.000000",
      [763] = "      End Object",
      [764] = "      Begin Object Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_1'\"",
      [765] = "         Visible=True",
      [766] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [767] = "         OwnedEpisodeID=1",
      [768] = "      End Object",
      [769] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_1'\"",
      [770] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_3'\"",
      [771] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_4'\"",
      [772] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_0'\"",
      [773] = "   End Object",
      [774] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1'\"",
      [775] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1.BPS_Transform_C_0'\"",
      [776] = "         Immediate=True",
      [777] = "         MoveTarget=(TrackName=\"卡萝移动后位置\")",
      [778] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_1'\"",
      [779] = "         OwnedEpisodeID=1",
      [780] = "         StartTime=20.511299",
      [781] = "         Duration=1.500000",
      [782] = "      End Object",
      [783] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [784] = "   End Object",
      [785] = "   Begin Object Name=\"DialogueTrackCamera_31\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_31'\"",
      [786] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_31'\"",
      [787] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [788] = "      TrackName=\"Actor3中景\"",
      [789] = "   End Object",
      [790] = "   Begin Object Name=\"DialogueTrackCamera_30\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_30'\"",
      [791] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_30'\"",
      [792] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [793] = "      TrackName=\"Actor3近景\"",
      [794] = "   End Object",
      [795] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [796] = "      Begin Object Name=\"BPS_ActorDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_2'\"",
      [797] = "         Target=(TrackName=\"Actor2\")",
      [798] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [799] = "         OwnedEpisodeID=1",
      [800] = "         StartTime=25.011299",
      [801] = "         Duration=1.500000",
      [802] = "      End Object",
      [803] = "      Begin Object Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_1'\"",
      [804] = "         Target=(TrackName=\"Actor3\")",
      [805] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [806] = "         OwnedEpisodeID=1",
      [807] = "         StartTime=18.589876",
      [808] = "         Duration=2.835625",
      [809] = "      End Object",
      [810] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_0'\"",
      [811] = "         Target=(TrackName=\"科恩黎终点2\")",
      [812] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [813] = "         OwnedEpisodeID=1",
      [814] = "         StartTime=12.348993",
      [815] = "         Duration=2.330650",
      [816] = "      End Object",
      [817] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [818] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_1'\"",
      [819] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_2'\"",
      [820] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_5'\"",
      [821] = "   End Object",
      [822] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [823] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [824] = "         AnimLibItem=(AssetID=\"Short_Talk4\")",
      [825] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [826] = "         OwnedEpisodeID=1",
      [827] = "         Duration=1.500000",
      [828] = "      End Object",
      [829] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [830] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_5'\"",
      [831] = "   End Object",
      [832] = "   Begin Object Name=\"DialogueTrackCamera_33\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_33'\"",
      [833] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_33'\"",
      [834] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_5'\"",
      [835] = "      TrackName=\"Actor4中景\"",
      [836] = "   End Object",
      [837] = "   Begin Object Name=\"DialogueTrackCamera_32\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_32'\"",
      [838] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_32'\"",
      [839] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_5'\"",
      [840] = "      TrackName=\"Actor4近景\"",
      [841] = "   End Object",
      [842] = "   Begin Object Name=\"BP_DialogueTrackVisible_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2'\"",
      [843] = "      Begin Object Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2.BPS_ActorVisible_C_1'\"",
      [844] = "         Visible=True",
      [845] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_2'\"",
      [846] = "         LineGUIDLinked=2534311944",
      [847] = "         OwnedEpisodeID=1",
      [848] = "         StartTime=22.011299",
      [849] = "      End Object",
      [850] = "      Begin Object Name=\"BPS_ActorVisible_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_2.BPS_ActorVisible_C_2'\"",
      [851] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_2'\"",
      [852] = "         OwnedEpisodeID=1",
      [853] = "      End Object",
      [854] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_2'\"",
      [855] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_1'\"",
      [856] = "   End Object",
      [857] = "   Begin Object Name=\"DialogueTrackRoutePoint_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_3'\"",
      [858] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_3'\"",
      [859] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [860] = "      FromTemplate=False",
      [861] = "      TrackName=\"卡萝移动后位置\"",
      [862] = "   End Object",
      [863] = "   Begin Object Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [864] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [865] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [866] = "      FromTemplate=False",
      [867] = "      TrackName=\"Camera4\"",
      [868] = "   End Object",
      [869] = "   Begin Object Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [870] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [871] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [872] = "      FromTemplate=False",
      [873] = "      TrackName=\"Camera3\"",
      [874] = "   End Object",
      [875] = "   Begin Object Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [876] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [877] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [878] = "      FromTemplate=False",
      [879] = "      TrackName=\"Camera2\"",
      [880] = "   End Object",
      [881] = "   Begin Object Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [882] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [883] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [884] = "      FromTemplate=False",
      [885] = "      TrackName=\"Camera1\"",
      [886] = "   End Object",
      [887] = "   Begin Object Name=\"DialogueTrackRoutePoint_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_2'\"",
      [888] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_2'\"",
      [889] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [890] = "      FromTemplate=False",
      [891] = "      TrackName=\"科恩黎终点2\"",
      [892] = "   End Object",
      [893] = "   Begin Object Name=\"DialogueTrackRoutePoint_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_1'\"",
      [894] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_1'\"",
      [895] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [896] = "      FromTemplate=False",
      [897] = "      TrackName=\"科恩黎终点\"",
      [898] = "   End Object",
      [899] = "   Begin Object Name=\"DialogueTrackRoutePoint_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueTrackRoutePoint_0'\"",
      [900] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_0'\"",
      [901] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [902] = "      FromTemplate=False",
      [903] = "      TrackName=\"科恩黎中点\"",
      [904] = "   End Object",
      [905] = "   Begin Object Name=\"DialogueTrackActor_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_5'\"",
      [906] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_5'\"",
      [907] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_32'\"",
      [908] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_33'\"",
      [909] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [910] = "      TrackName=\"Actor4\"",
      [911] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [912] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [913] = "   End Object",
      [914] = "   Begin Object Name=\"DialogueTrackActor_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_3'\"",
      [915] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [916] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_28'\"",
      [917] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_29'\"",
      [918] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [919] = "      TrackName=\"Actor2\"",
      [920] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [921] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [922] = "   End Object",
      [923] = "   Begin Object Name=\"DialogueTrackActor_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_2'\"",
      [924] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [925] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_26'\"",
      [926] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_27'\"",
      [927] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [928] = "      TrackName=\"Actor1\"",
      [929] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_2'\"",
      [930] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [931] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_0'\"",
      [932] = "   End Object",
      [933] = "   Begin Object Name=\"DialogueTrackCamera_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_24'\"",
      [934] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_24'\"",
      [935] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [936] = "      TrackName=\"同侧对话01\"",
      [937] = "   End Object",
      [938] = "   Begin Object Name=\"DialogueTrackCamera_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_23'\"",
      [939] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_23'\"",
      [940] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [941] = "      TrackName=\"双人正视02\"",
      [942] = "   End Object",
      [943] = "   Begin Object Name=\"DialogueTrackCamera_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_22'\"",
      [944] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_22'\"",
      [945] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [946] = "      TrackName=\"双人正视01\"",
      [947] = "   End Object",
      [948] = "   Begin Object Name=\"DialogueTrackCamera_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_21'\"",
      [949] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_21'\"",
      [950] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [951] = "      TrackName=\"平视\"",
      [952] = "   End Object",
      [953] = "   Begin Object Name=\"DialogueTrackCamera_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_20'\"",
      [954] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_20'\"",
      [955] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [956] = "      TrackName=\"中景02\"",
      [957] = "   End Object",
      [958] = "   Begin Object Name=\"DialogueTrackCamera_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_19'\"",
      [959] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_19'\"",
      [960] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [961] = "      TrackName=\"中景01\"",
      [962] = "   End Object",
      [963] = "   Begin Object Name=\"DialogueTrackCamera_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_18'\"",
      [964] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_18'\"",
      [965] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [966] = "      TrackName=\"全景02\"",
      [967] = "   End Object",
      [968] = "   Begin Object Name=\"DialogueTrackCamera_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_17'\"",
      [969] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_17'\"",
      [970] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [971] = "      TrackName=\"全景01\"",
      [972] = "   End Object",
      [973] = "   Begin Object Name=\"DialogueTrackCamera_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_16'\"",
      [974] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_16'\"",
      [975] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [976] = "      TrackName=\"远景01\"",
      [977] = "   End Object",
      [978] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_3'\"",
      [979] = "      SpawnTransform=(Rotation=(X=-0.000000,Y=0.000000,Z=-0.173614,W=0.984814),Translation=(X=14.086737,Y=111.156251,Z=138.921400))",
      [980] = "      TrackName=\"卡萝移动后位置\"",
      [981] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [982] = "   End Object",
      [983] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_2'\"",
      [984] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.996191,W=0.087199),Translation=(X=-714.706080,Y=-734.588467,Z=-28.457900))",
      [985] = "      TrackName=\"科恩黎终点2\"",
      [986] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [987] = "   End Object",
      [988] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_1'\"",
      [989] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.996194,W=0.087169),Translation=(X=-122.088454,Y=-834.617391,Z=-19.463424))",
      [990] = "      TrackName=\"科恩黎终点\"",
      [991] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [992] = "   End Object",
      [993] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_0'\"",
      [994] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.173700,W=0.984799),Translation=(X=83.032928,Y=-384.080933,Z=-9.639162))",
      [995] = "      TrackName=\"科恩黎中点\"",
      [996] = "      StickGround=True",
      [997] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [998] = "   End Object",
      [999] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [1000] = "      FOV=40.000000",
      [1001] = "      bOverride_DepthOfField=True",
      [1002] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor4\")",
      [1003] = "      DepthOfFieldFocalDistance=167.512375",
      [1004] = "      DepthOfFieldFStop=50.000000",
      [1005] = "      DepthOfFieldSensorWidth=300.000000",
      [1006] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1007] = "      SpawnTransform=(Rotation=(X=-0.014659,Y=-0.033622,Z=0.400018,W=-0.915773),Translation=(X=-7.966661,Y=181.981917,Z=172.118778))",
      [1008] = "      TrackName=\"Camera4\"",
      [1009] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1010] = "   End Object",
      [1011] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [1012] = "      FOV=30.000000",
      [1013] = "      bOverride_DepthOfField=True",
      [1014] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor3\")",
      [1015] = "      DepthOfFieldFocalDistance=128.985077",
      [1016] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1017] = "      SpawnTransform=(Rotation=(X=0.064083,Y=-0.031647,Z=-0.894234,W=-0.441856),Translation=(X=106.739367,Y=-10.108956,Z=168.884289))",
      [1018] = "      TrackName=\"Camera3\"",
      [1019] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1020] = "   End Object",
      [1021] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [1022] = "      FOV=30.000000",
      [1023] = "      bOverride_DepthOfField=True",
      [1024] = "      DepthOfFieldFocusActor=(PerformerName=\"None\")",
      [1025] = "      DepthOfFieldFocalDistance=200.000000",
      [1026] = "      DepthOfFieldFStop=50.000000",
      [1027] = "      DepthOfFieldSensorWidth=200.000000",
      [1028] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1029] = "      SpawnTransform=(Rotation=(X=0.089160,Y=-0.051219,Z=-0.862340,W=-0.495780),Translation=(X=218.159773,Y=-195.063423,Z=212.633058))",
      [1030] = "      TrackName=\"Camera2\"",
      [1031] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1032] = "   End Object",
      [1033] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [1034] = "      FOV=35.000000",
      [1035] = "      bOverride_DepthOfField=True",
      [1036] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor4\")",
      [1037] = "      DepthOfFieldFocalDistance=326.692719",
      [1038] = "      DepthOfFieldSensorWidth=300.000000",
      [1039] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1040] = "      SpawnTransform=(Rotation=(X=-0.007400,Y=0.058798,Z=0.125195,W=0.990361),Translation=(X=-198.549800,Y=30.762400,Z=178.658733))",
      [1041] = "      TrackName=\"Camera1\"",
      [1042] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1043] = "   End Object",
      [1044] = "   Begin Object Name=\"BP_DialogueCamera_C_33\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_33'\"",
      [1045] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1046] = "      SpawnTransform=(Rotation=(X=0.054161,Y=-0.019499,Z=-0.939321,W=-0.338176),Translation=(X=86.297145,Y=-47.663342,Z=73.824779))",
      [1047] = "      TrackName=\"Actor4中景\"",
      [1048] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_5'\"",
      [1049] = "   End Object",
      [1050] = "   Begin Object Name=\"BP_DialogueCamera_C_32\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_32'\"",
      [1051] = "      FOV=40.000000",
      [1052] = "      bOverride_DepthOfField=True",
      [1053] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor4\")",
      [1054] = "      DepthOfFieldFocalDistance=155.140869",
      [1055] = "      DepthOfFieldSensorWidth=300.000000",
      [1056] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1057] = "      SpawnTransform=(Rotation=(X=0.110903,Y=0.041446,Z=-0.930078,W=0.347761),Translation=(X=101.849449,Y=113.076949,Z=96.136328))",
      [1058] = "      TrackName=\"Actor4近景\"",
      [1059] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_5'\"",
      [1060] = "   End Object",
      [1061] = "   Begin Object Name=\"BP_DialogueCamera_C_31\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_31'\"",
      [1062] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1063] = "      SpawnTransform=(Rotation=(X=0.127573,Y=-0.017702,Z=-0.982260,W=-0.136300),Translation=(X=88.044531,Y=-14.030108,Z=88.276984))",
      [1064] = "      TrackName=\"Actor3中景\"",
      [1065] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_4'\"",
      [1066] = "   End Object",
      [1067] = "   Begin Object Name=\"BP_DialogueCamera_C_30\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_30'\"",
      [1068] = "      FOV=40.000000",
      [1069] = "      bOverride_DepthOfField=True",
      [1070] = "      DepthOfFieldFocalDistance=85.000000",
      [1071] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1072] = "      SpawnTransform=(Rotation=(X=0.010432,Y=-0.000913,Z=-0.996140,W=-0.087151),Translation=(X=97.150104,Y=-11.712366,Z=81.297101))",
      [1073] = "      TrackName=\"Actor3近景\"",
      [1074] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_4'\"",
      [1075] = "   End Object",
      [1076] = "   Begin Object Name=\"BP_DialogueCamera_C_29\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_29'\"",
      [1077] = "      FOV=40.000000",
      [1078] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1079] = "      OffsetZ=28.142536",
      [1080] = "      bOverride_DepthOfField=True",
      [1081] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1082] = "      DepthOfFieldFocalDistance=322.001068",
      [1083] = "      DepthOfFieldFStop=25.000000",
      [1084] = "      DepthOfFieldSensorWidth=300.000000",
      [1085] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1086] = "      SpawnTransform=(Rotation=(X=0.096025,Y=0.025371,Z=-0.962043,W=0.254183),Translation=(X=298.361391,Y=113.020973,Z=111.764367))",
      [1087] = "      TrackName=\"Actor2中景\"",
      [1088] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [1089] = "   End Object",
      [1090] = "   Begin Object Name=\"BP_DialogueCamera_C_28\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_28'\"",
      [1091] = "      FOV=45.000000",
      [1092] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1093] = "      OffsetZ=-8.527580",
      [1094] = "      bOverride_DepthOfField=True",
      [1095] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1096] = "      DepthOfFieldFocalDistance=185.943268",
      [1097] = "      DepthOfFieldSensorWidth=300.000000",
      [1098] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1099] = "      SpawnTransform=(Rotation=(X=0.009871,Y=-0.003452,Z=0.943754,W=0.330484),Translation=(X=125.563071,Y=-135.669302,Z=66.060387))",
      [1100] = "      TrackName=\"Actor2近景\"",
      [1101] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [1102] = "   End Object",
      [1103] = "   Begin Object Name=\"BP_DialogueCamera_C_27\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_27'\"",
      [1104] = "      FOV=80.000000",
      [1105] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1106] = "      SpawnTransform=(Rotation=(X=0.096576,Y=0.023186,Z=-0.967562,W=0.232291),Translation=(X=114.264188,Y=50.693740,Z=78.443845))",
      [1107] = "      TrackName=\"Actor1中景\"",
      [1108] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [1109] = "   End Object",
      [1110] = "   Begin Object Name=\"BP_DialogueCamera_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_26'\"",
      [1111] = "      FOV=40.000000",
      [1112] = "      bEnableLookAt=True",
      [1113] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1114] = "      OffsetZ=-18.795624",
      [1115] = "      bOverride_DepthOfField=True",
      [1116] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1117] = "      DepthOfFieldFocalDistance=190.262955",
      [1118] = "      DepthOfFieldSensorWidth=300.000000",
      [1119] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1120] = "      SpawnTransform=(Rotation=(X=0.015416,Y=0.002814,Z=0.983758,W=-0.178817),Translation=(X=179.788176,Y=52.200988,Z=77.683761))",
      [1121] = "      TrackName=\"Actor1近景\"",
      [1122] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [1123] = "   End Object",
      [1124] = "   Begin Object Name=\"BP_DialogueCamera_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_25'\"",
      [1125] = "      FOV=80.000000",
      [1126] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1127] = "      SpawnTransform=(Rotation=(X=0.003326,Y=0.011756,Z=0.272226,W=-0.962156),Translation=(X=-13.256276,Y=46.847557,Z=161.481588))",
      [1128] = "      TrackName=\"同侧对话02\"",
      [1129] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1130] = "   End Object",
      [1131] = "   Begin Object Name=\"DialogueTrackCamera_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_25'\"",
      [1132] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_25'\"",
      [1133] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [1134] = "      TrackName=\"同侧对话02\"",
      [1135] = "   End Object",
      [1136] = "   Begin Object Name=\"BP_DialogueCamera_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_24'\"",
      [1137] = "      FOV=80.000000",
      [1138] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1139] = "      SpawnTransform=(Rotation=(X=-0.017150,Y=-0.008626,Z=0.893191,W=-0.449267),Translation=(X=121.645263,Y=84.001374,Z=158.849415))",
      [1140] = "      TrackName=\"同侧对话01\"",
      [1141] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1142] = "   End Object",
      [1143] = "   Begin Object Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [1144] = "      FOV=70.000000",
      [1145] = "      bOverride_DepthOfField=True",
      [1146] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1147] = "      SpawnTransform=(Rotation=(X=0.008783,Y=0.049825,Z=0.173392,W=-0.983553),Translation=(X=12.453129,Y=33.442429,Z=157.730900))",
      [1148] = "      TrackName=\"双人正视02\"",
      [1149] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1150] = "   End Object",
      [1151] = "   Begin Object Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [1152] = "      FOV=70.000000",
      [1153] = "      bOverride_DepthOfField=True",
      [1154] = "      DepthOfFieldFocalDistance=105.000000",
      [1155] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1156] = "      SpawnTransform=(Rotation=(X=0.013441,Y=0.003778,Z=0.962589,W=-0.270607),Translation=(X=104.716643,Y=30.675828,Z=158.218246))",
      [1157] = "      TrackName=\"双人正视01\"",
      [1158] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1159] = "   End Object",
      [1160] = "   Begin Object Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [1161] = "      FOV=45.000000",
      [1162] = "      bOverride_DepthOfField=True",
      [1163] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1164] = "      DepthOfFieldFocalDistance=825.872559",
      [1165] = "      DepthOfFieldFStop=10.000000",
      [1166] = "      DepthOfFieldSensorWidth=300.000000",
      [1167] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1168] = "      SpawnTransform=(Rotation=(X=-0.092892,Y=-0.023371,Z=0.965391,W=-0.242579),Translation=(X=884.681953,Y=322.877252,Z=263.163650))",
      [1169] = "      TrackName=\"平视\"",
      [1170] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1171] = "   End Object",
      [1172] = "   Begin Object Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [1173] = "      FOV=80.000000",
      [1174] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1175] = "      SpawnTransform=(Rotation=(X=-0.077751,Y=-0.010515,Z=0.987924,W=-0.133607),Translation=(X=163.767937,Y=2.227863,Z=178.554511))",
      [1176] = "      TrackName=\"中景02\"",
      [1177] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1178] = "   End Object",
      [1179] = "   Begin Object Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [1180] = "      FOV=45.000000",
      [1181] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1182] = "      SpawnTransform=(Rotation=(X=0.258959,Y=0.028149,Z=-0.959832,W=0.104261),Translation=(X=738.060774,Y=231.666425,Z=520.109252))",
      [1183] = "      TrackName=\"中景01\"",
      [1184] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1185] = "   End Object",
      [1186] = "   Begin Object Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [1187] = "      FOV=45.000000",
      [1188] = "      bOverride_DepthOfField=True",
      [1189] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1190] = "      DepthOfFieldFocalDistance=262.308960",
      [1191] = "      DepthOfFieldFStop=25.000000",
      [1192] = "      DepthOfFieldSensorWidth=300.000000",
      [1193] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1194] = "      SpawnTransform=(Rotation=(X=0.056925,Y=0.008062,Z=-0.988379,W=0.140718),Translation=(X=369.560784,Y=101.910092,Z=173.588102))",
      [1195] = "      TrackName=\"全景02\"",
      [1196] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1197] = "   End Object",
      [1198] = "   Begin Object Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [1199] = "      FOV=80.000000",
      [1200] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1201] = "      SpawnTransform=(Rotation=(X=-0.027337,Y=-0.062280,Z=0.400985,W=-0.913556),Translation=(X=-228.594577,Y=298.060096,Z=179.792806))",
      [1202] = "      TrackName=\"全景01\"",
      [1203] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1204] = "   End Object",
      [1205] = "   Begin Object Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [1206] = "      FOV=45.000000",
      [1207] = "      bOverride_DepthOfField=True",
      [1208] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1209] = "      DepthOfFieldFocalDistance=651.509888",
      [1210] = "      DepthOfFieldFStop=20.000000",
      [1211] = "      DepthOfFieldSensorWidth=300.000000",
      [1212] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1213] = "      SpawnTransform=(Rotation=(X=-0.041183,Y=-0.037592,Z=0.738539,W=-0.671901),Translation=(X=131.065875,Y=621.561864,Z=176.672197))",
      [1214] = "      TrackName=\"远景01\"",
      [1215] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1216] = "   End Object",
      [1217] = "   Begin Object Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [1218] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1219] = "      TrackName=\"锚点\"",
      [1220] = "   End Object",
      [1221] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [1222] = "      AppearanceID=7216010",
      [1223] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1224] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.906302,W=-0.422630),Translation=(X=14.086778,Y=111.156283,Z=148.921400))",
      [1225] = "      TrackName=\"Actor5\"",
      [1226] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1227] = "   End Object",
      [1228] = "   Begin Object Name=\"BP_DialogueActor_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_5'\"",
      [1229] = "      AppearanceID=7216003",
      [1230] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1231] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.939685,W=0.342042),Translation=(X=121.531504,Y=77.901326,Z=91.250894))",
      [1232] = "      TrackName=\"Actor4\"",
      [1233] = "      StickGround=True",
      [1234] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1235] = "   End Object",
      [1236] = "   Begin Object Name=\"BP_DialogueActor_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_4'\"",
      [1237] = "      AppearanceID=7216009",
      [1238] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1239] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.819152,W=-0.573576),Translation=(X=51.005400,Y=113.193800,Z=138.921400))",
      [1240] = "      TrackName=\"Actor3\"",
      [1241] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1242] = "   End Object",
      [1243] = "   Begin Object Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [1244] = "      AppearanceID=7216002",
      [1245] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1246] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.984816,W=-0.173603),Translation=(X=140.831827,Y=-29.959132,Z=93.625752))",
      [1247] = "      TrackName=\"Actor2\"",
      [1248] = "      StickGround=True",
      [1249] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1250] = "   End Object",
      [1251] = "   Begin Object Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [1252] = "      AppearanceID=7216022",
      [1253] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1254] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.000009,W=-1.000000),Translation=(X=0.000007,Y=0.000045,Z=77.999961))",
      [1255] = "      TrackName=\"Actor1\"",
      [1256] = "      StickGround=True",
      [1257] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1258] = "   End Object",
      [1259] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [1260] = "      EpisodeID=1",
      [1261] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_12'\"",
      [1262] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_13'\"",
      [1263] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_14'\"",
      [1264] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_15'\"",
      [1265] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_16'\"",
      [1266] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_17'\"",
      [1267] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_18'\"",
      [1268] = "      DialogueLines(7)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_19'\"",
      [1269] = "      DialogueLines(8)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_20'\"",
      [1270] = "      DialogueLines(9)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_21'\"",
      [1271] = "      DialogueLines(10)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_22'\"",
      [1272] = "      DialogueLines(11)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_23'\"",
      [1273] = "   End Object",
      [1274] = "   Begin Object Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [1275] = "      GUID=2612427425",
      [1276] = "      Duration=1.500000",
      [1277] = "      ContentString=\"跟我走，我有办法追踪到阿诺德现在的位置。\"",
      [1278] = "      ContentUI=\"Default\"",
      [1279] = "      Talker=(PerformerName=\"Actor4\")",
      [1280] = "      TalkerName=\"戴莉\"",
      [1281] = "   End Object",
      [1282] = "   Begin Object Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [1283] = "      CameraCutDuration=3.000000",
      [1284] = "      CameraBreathType=NewEnumerator0",
      [1285] = "      BreathAttenuation=1.000000",
      [1286] = "      BreathSpeed=10.000000",
      [1287] = "      CanSkip=True",
      [1288] = "      EpisodeID=1",
      [1289] = "      ContentIndex=1",
      [1290] = "   End Object",
      [1291] = "   Begin Object Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [1292] = "      GUID=3996724380",
      [1293] = "      Duration=1.500000",
      [1294] = "      ContentString=\"可是，要怎样……\"",
      [1295] = "      ContentUI=\"Default\"",
      [1296] = "      Talker=(PerformerName=\"Actor2\")",
      [1297] = "   End Object",
      [1298] = "   Begin Object Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [1299] = "      CameraCutDuration=1.500000",
      [1300] = "      CanSkip=True",
      [1301] = "      EpisodeID=1",
      [1302] = "      ContentIndex=2",
      [1303] = "   End Object",
      [1304] = "   Begin Object Name=\"KGSLDialogueLine_14\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_14'\"",
      [1305] = "      GUID=791657287",
      [1306] = "      Duration=1.500000",
      [1307] = "      ContentString=\"相信我，之后我会跟你解释。\"",
      [1308] = "      ContentUI=\"Default\"",
      [1309] = "      Talker=(PerformerName=\"Actor4\")",
      [1310] = "      TalkerName=\"戴莉\"",
      [1311] = "   End Object",
      [1312] = "   Begin Object Name=\"BP_DLExtensionData_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_14'\"",
      [1313] = "      CameraCutDuration=1.500000",
      [1314] = "      CanSkip=True",
      [1315] = "      EpisodeID=1",
      [1316] = "      ContentIndex=3",
      [1317] = "   End Object",
      [1318] = "   Begin Object Name=\"KGSLDialogueLine_15\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_15'\"",
      [1319] = "      GUID=1374171887",
      [1320] = "      Duration=1.500000",
      [1321] = "      ContentString=\"好，没问题，你来带路。\"",
      [1322] = "      ContentUI=\"Default\"",
      [1323] = "      Talker=(PerformerName=\"Actor2\")",
      [1324] = "   End Object",
      [1325] = "   Begin Object Name=\"BP_DLExtensionData_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_15'\"",
      [1326] = "      CameraCutDuration=1.500000",
      [1327] = "      CanSkip=True",
      [1328] = "      EpisodeID=1",
      [1329] = "      ContentIndex=4",
      [1330] = "   End Object",
      [1331] = "   Begin Object Name=\"KGSLDialogueLine_16\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_16'\"",
      [1332] = "      GUID=217142600",
      [1333] = "      Duration=1.500000",
      [1334] = "      ContentString=\"科恩黎，麻烦回公司通知大家这起非凡事件。\"",
      [1335] = "      ContentUI=\"Default\"",
      [1336] = "      Talker=(PerformerName=\"Actor2\")",
      [1337] = "   End Object",
      [1338] = "   Begin Object Name=\"BP_DLExtensionData_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_16'\"",
      [1339] = "      CameraCutDuration=1.500000",
      [1340] = "      CanSkip=True",
      [1341] = "      EpisodeID=1",
      [1342] = "      ContentIndex=5",
      [1343] = "   End Object",
      [1344] = "   Begin Object Name=\"KGSLDialogueLine_17\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_17'\"",
      [1345] = "      GUID=1727624281",
      [1346] = "      Duration=1.500000",
      [1347] = "      ContentString=\"队长，我跟你一起去！毕竟是我弄丢了阿诺德……\"",
      [1348] = "      ContentUI=\"Default\"",
      [1349] = "      Talker=(PerformerName=\"Actor1\")",
      [1350] = "   End Object",
      [1351] = "   Begin Object Name=\"BP_DLExtensionData_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_17'\"",
      [1352] = "      CameraCutDuration=1.500000",
      [1353] = "      CanSkip=True",
      [1354] = "      EpisodeID=1",
      [1355] = "      ContentIndex=6",
      [1356] = "   End Object",
      [1357] = "   Begin Object Name=\"KGSLDialogueLine_18\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_18'\"",
      [1358] = "      GUID=3592249620",
      [1359] = "      Duration=1.500000",
      [1360] = "      ContentString=\"戴莉跟我一起，不用担心，我会带阿诺德回去的。\"",
      [1361] = "      ContentUI=\"Default\"",
      [1362] = "      Talker=(PerformerName=\"Actor2\")",
      [1363] = "   End Object",
      [1364] = "   Begin Object Name=\"BP_DLExtensionData_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_18'\"",
      [1365] = "      CameraCutDuration=1.500000",
      [1366] = "      CanSkip=True",
      [1367] = "      EpisodeID=1",
      [1368] = "      ContentIndex=7",
      [1369] = "   End Object",
      [1370] = "   Begin Object Name=\"KGSLDialogueLine_19\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_19'\"",
      [1371] = "      GUID=629977742",
      [1372] = "      Duration=1.500000",
      [1373] = "      ContentString=\"哦哦，好的！那就拜托你们了。\"",
      [1374] = "      ContentUI=\"Default\"",
      [1375] = "      Talker=(PerformerName=\"Actor1\")",
      [1376] = "   End Object",
      [1377] = "   Begin Object Name=\"BP_DLExtensionData_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_19'\"",
      [1378] = "      CameraCutDuration=1.500000",
      [1379] = "      CanSkip=True",
      [1380] = "      EpisodeID=1",
      [1381] = "      ContentIndex=8",
      [1382] = "   End Object",
      [1383] = "   Begin Object Name=\"KGSLDialogueLine_20\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_20'\"",
      [1384] = "      GUID=3159574569",
      [1385] = "      Delay=8.511299",
      [1386] = "      Duration=1.500000",
      [1387] = "      ContentString=\"拜托你了，带我们找到阿诺德吧，夜莺银哨。\"",
      [1388] = "      ContentUI=\"Default\"",
      [1389] = "      Talker=(PerformerName=\"Actor4\")",
      [1390] = "      TalkerName=\"戴莉\"",
      [1391] = "   End Object",
      [1392] = "   Begin Object Name=\"BP_DLExtensionData_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_20'\"",
      [1393] = "      CameraCutDuration=1.500000",
      [1394] = "      CameraBreathType=NewEnumerator4",
      [1395] = "      BreathAttenuation=0.500000",
      [1396] = "      BreathSpeed=4.000000",
      [1397] = "      CanSkip=True",
      [1398] = "      EpisodeID=1",
      [1399] = "      ContentIndex=9",
      [1400] = "   End Object",
      [1401] = "   Begin Object Name=\"KGSLDialogueLine_21\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_21'\"",
      [1402] = "      GUID=2534311944",
      [1403] = "      Duration=1.500000",
      [1404] = "      ContentString=\"！！！\"",
      [1405] = "      ContentUI=\"Default\"",
      [1406] = "      Talker=(PerformerName=\"Actor2\")",
      [1407] = "   End Object",
      [1408] = "   Begin Object Name=\"BP_DLExtensionData_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_21'\"",
      [1409] = "      CameraCutDuration=0.823921",
      [1410] = "      CameraBreathType=NewEnumerator2",
      [1411] = "      BreathAttenuation=0.200000",
      [1412] = "      BreathSpeed=10.000000",
      [1413] = "      CanSkip=True",
      [1414] = "      EpisodeID=1",
      [1415] = "      ContentIndex=10",
      [1416] = "   End Object",
      [1417] = "   Begin Object Name=\"KGSLDialogueLine_22\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_22'\"",
      [1418] = "      GUID=3371102512",
      [1419] = "      Duration=1.500000",
      [1420] = "      ContentString=\"是卡萝队长的遗物，它怎么会在这里……这就是你的任务？\"",
      [1421] = "      ContentUI=\"Default\"",
      [1422] = "      Talker=(PerformerName=\"Actor2\")",
      [1423] = "   End Object",
      [1424] = "   Begin Object Name=\"BP_DLExtensionData_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_22'\"",
      [1425] = "      CameraCutDuration=1.500000",
      [1426] = "      CameraBreathType=NewEnumerator2",
      [1427] = "      BreathAttenuation=0.500000",
      [1428] = "      BreathSpeed=7.000000",
      [1429] = "      CanSkip=True",
      [1430] = "      EpisodeID=1",
      [1431] = "      ContentIndex=11",
      [1432] = "   End Object",
      [1433] = "   Begin Object Name=\"KGSLDialogueLine_23\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_23'\"",
      [1434] = "      GUID=2369900021",
      [1435] = "      Duration=1.500000",
      [1436] = "      ContentString=\"说来话长，我们先去找到阿诺德吧。\"",
      [1437] = "      ContentUI=\"Default\"",
      [1438] = "      Talker=(PerformerName=\"Actor4\")",
      [1439] = "      TalkerName=\"戴莉\"",
      [1440] = "   End Object",
      [1441] = "   Begin Object Name=\"BP_DLExtensionData_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_23'\"",
      [1442] = "      CameraCutDuration=1.500000",
      [1443] = "      CanSkip=True",
      [1444] = "      EpisodeID=1",
      [1445] = "      ContentIndex=12",
      [1446] = "   End Object",
      [1447] = "   Begin Object Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [1448] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_4'\"",
      [1449] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_30'\"",
      [1450] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_31'\"",
      [1451] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [1452] = "      TrackName=\"Actor3\"",
      [1453] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_1'\"",
      [1454] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [1455] = "   End Object",
      [1456] = "   Begin Object Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [1457] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1458] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_15'\"",
      [1459] = "      FromTemplate=False",
      [1460] = "      TrackName=\"Actor5\"",
      [1461] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_2'\"",
      [1462] = "   End Object",
      [1463] = "   Begin Object Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [1464] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [1465] = "         EpisodeID=1",
      [1466] = "         ContentIndex=1",
      [1467] = "         Talker=\"Actor4\"",
      [1468] = "         TalkerName=\"戴莉\"",
      [1469] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1470] = "         SectionName=\"跟我走，我有办法追踪到阿诺德现在的位置。\"",
      [1471] = "         FromLineIndex=0",
      [1472] = "         LineGUIDLinked=2612427425",
      [1473] = "         OwnedEpisodeID=1",
      [1474] = "         Duration=1.500000",
      [1475] = "      End Object",
      [1476] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [1477] = "         EpisodeID=1",
      [1478] = "         ContentIndex=2",
      [1479] = "         Talker=\"Actor2\"",
      [1480] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1481] = "         SectionName=\"可是，要怎样……\"",
      [1482] = "         FromLineIndex=1",
      [1483] = "         LineGUIDLinked=3996724380",
      [1484] = "         OwnedEpisodeID=1",
      [1485] = "         StartTime=1.500000",
      [1486] = "         Duration=1.500000",
      [1487] = "      End Object",
      [1488] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [1489] = "         EpisodeID=1",
      [1490] = "         ContentIndex=3",
      [1491] = "         Talker=\"Actor4\"",
      [1492] = "         TalkerName=\"戴莉\"",
      [1493] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1494] = "         SectionName=\"相信我，之后我会跟你解释。\"",
      [1495] = "         FromLineIndex=2",
      [1496] = "         LineGUIDLinked=791657287",
      [1497] = "         OwnedEpisodeID=1",
      [1498] = "         StartTime=3.000000",
      [1499] = "         Duration=1.500000",
      [1500] = "      End Object",
      [1501] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [1502] = "         EpisodeID=1",
      [1503] = "         ContentIndex=4",
      [1504] = "         Talker=\"Actor2\"",
      [1505] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1506] = "         SectionName=\"好，没问题，你来带路。\"",
      [1507] = "         FromLineIndex=3",
      [1508] = "         LineGUIDLinked=1374171887",
      [1509] = "         OwnedEpisodeID=1",
      [1510] = "         StartTime=4.500000",
      [1511] = "         Duration=1.500000",
      [1512] = "      End Object",
      [1513] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [1514] = "         EpisodeID=1",
      [1515] = "         ContentIndex=5",
      [1516] = "         Talker=\"Actor2\"",
      [1517] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1518] = "         SectionName=\"科恩黎，麻烦回公司通知大家这起非凡事件。\"",
      [1519] = "         FromLineIndex=4",
      [1520] = "         LineGUIDLinked=217142600",
      [1521] = "         OwnedEpisodeID=1",
      [1522] = "         StartTime=6.000000",
      [1523] = "         Duration=1.500000",
      [1524] = "      End Object",
      [1525] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [1526] = "         EpisodeID=1",
      [1527] = "         ContentIndex=6",
      [1528] = "         Talker=\"Actor1\"",
      [1529] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1530] = "         SectionName=\"队长，我跟你一起去！毕竟是我弄丢了阿诺德……\"",
      [1531] = "         FromLineIndex=5",
      [1532] = "         LineGUIDLinked=1727624281",
      [1533] = "         OwnedEpisodeID=1",
      [1534] = "         StartTime=7.500000",
      [1535] = "         Duration=1.500000",
      [1536] = "      End Object",
      [1537] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [1538] = "         EpisodeID=1",
      [1539] = "         ContentIndex=7",
      [1540] = "         Talker=\"Actor2\"",
      [1541] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1542] = "         SectionName=\"戴莉跟我一起，不用担心，我会带阿诺德回去的。\"",
      [1543] = "         FromLineIndex=6",
      [1544] = "         LineGUIDLinked=3592249620",
      [1545] = "         OwnedEpisodeID=1",
      [1546] = "         StartTime=9.000000",
      [1547] = "         Duration=1.500000",
      [1548] = "      End Object",
      [1549] = "      Begin Object Name=\"BPS_Dialogue_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_7'\"",
      [1550] = "         EpisodeID=1",
      [1551] = "         ContentIndex=8",
      [1552] = "         Talker=\"Actor1\"",
      [1553] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1554] = "         SectionName=\"哦哦，好的！那就拜托你们了。\"",
      [1555] = "         FromLineIndex=7",
      [1556] = "         LineGUIDLinked=629977742",
      [1557] = "         OwnedEpisodeID=1",
      [1558] = "         StartTime=10.500000",
      [1559] = "         Duration=1.500000",
      [1560] = "      End Object",
      [1561] = "      Begin Object Name=\"BPS_Dialogue_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_8'\"",
      [1562] = "         EpisodeID=1",
      [1563] = "         ContentIndex=9",
      [1564] = "         Talker=\"Actor4\"",
      [1565] = "         TalkerName=\"戴莉\"",
      [1566] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1567] = "         SectionName=\"拜托你了，带我们找到阿诺德吧，夜莺银哨。\"",
      [1568] = "         FromLineIndex=8",
      [1569] = "         LineGUIDLinked=3159574569",
      [1570] = "         OwnedEpisodeID=1",
      [1571] = "         StartTime=20.511299",
      [1572] = "         Duration=1.500000",
      [1573] = "      End Object",
      [1574] = "      Begin Object Name=\"BPS_Dialogue_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_9'\"",
      [1575] = "         EpisodeID=1",
      [1576] = "         ContentIndex=10",
      [1577] = "         Talker=\"Actor2\"",
      [1578] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1579] = "         SectionName=\"！！！\"",
      [1580] = "         FromLineIndex=9",
      [1581] = "         LineGUIDLinked=2534311944",
      [1582] = "         OwnedEpisodeID=1",
      [1583] = "         StartTime=22.011299",
      [1584] = "         Duration=1.500000",
      [1585] = "      End Object",
      [1586] = "      Begin Object Name=\"BPS_Dialogue_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_10'\"",
      [1587] = "         EpisodeID=1",
      [1588] = "         ContentIndex=11",
      [1589] = "         Talker=\"Actor2\"",
      [1590] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1591] = "         SectionName=\"是卡萝队长的遗物，它怎么会在这里……这就是你的任务？\"",
      [1592] = "         FromLineIndex=10",
      [1593] = "         LineGUIDLinked=3371102512",
      [1594] = "         OwnedEpisodeID=1",
      [1595] = "         StartTime=23.511299",
      [1596] = "         Duration=1.500000",
      [1597] = "      End Object",
      [1598] = "      Begin Object Name=\"BPS_Dialogue_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_11'\"",
      [1599] = "         EpisodeID=1",
      [1600] = "         ContentIndex=12",
      [1601] = "         Talker=\"Actor4\"",
      [1602] = "         TalkerName=\"戴莉\"",
      [1603] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1604] = "         SectionName=\"说来话长，我们先去找到阿诺德吧。\"",
      [1605] = "         FromLineIndex=11",
      [1606] = "         LineGUIDLinked=2369900021",
      [1607] = "         OwnedEpisodeID=1",
      [1608] = "         StartTime=25.011299",
      [1609] = "         Duration=1.500000",
      [1610] = "      End Object",
      [1611] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1612] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1613] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1614] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1615] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1616] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1617] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1618] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_7'\"",
      [1619] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_8'\"",
      [1620] = "      ActionSections(9)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_9'\"",
      [1621] = "      ActionSections(10)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_10'\"",
      [1622] = "      ActionSections(11)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_11'\"",
      [1623] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1624] = "      FromTemplate=False",
      [1625] = "   End Object",
      [1626] = "   Begin Object Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [1627] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_0'\"",
      [1628] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1629] = "         FromLineIndex=0",
      [1630] = "         LineGUIDLinked=2612427425",
      [1631] = "         OwnedEpisodeID=1",
      [1632] = "         StartTime=1.400000",
      [1633] = "      End Object",
      [1634] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_1'\"",
      [1635] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1636] = "         FromLineIndex=1",
      [1637] = "         LineGUIDLinked=3996724380",
      [1638] = "         OwnedEpisodeID=1",
      [1639] = "         StartTime=2.900000",
      [1640] = "      End Object",
      [1641] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_2'\"",
      [1642] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1643] = "         FromLineIndex=2",
      [1644] = "         LineGUIDLinked=791657287",
      [1645] = "         OwnedEpisodeID=1",
      [1646] = "         StartTime=4.400000",
      [1647] = "      End Object",
      [1648] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_3'\"",
      [1649] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1650] = "         FromLineIndex=3",
      [1651] = "         LineGUIDLinked=1374171887",
      [1652] = "         OwnedEpisodeID=1",
      [1653] = "         StartTime=5.900000",
      [1654] = "      End Object",
      [1655] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_4'\"",
      [1656] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1657] = "         FromLineIndex=4",
      [1658] = "         LineGUIDLinked=217142600",
      [1659] = "         OwnedEpisodeID=1",
      [1660] = "         StartTime=7.400000",
      [1661] = "      End Object",
      [1662] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_5'\"",
      [1663] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1664] = "         FromLineIndex=5",
      [1665] = "         LineGUIDLinked=1727624281",
      [1666] = "         OwnedEpisodeID=1",
      [1667] = "         StartTime=8.900000",
      [1668] = "      End Object",
      [1669] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_6'\"",
      [1670] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1671] = "         FromLineIndex=6",
      [1672] = "         LineGUIDLinked=3592249620",
      [1673] = "         OwnedEpisodeID=1",
      [1674] = "         StartTime=10.400000",
      [1675] = "      End Object",
      [1676] = "      Begin Object Name=\"BPS_StateControl_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_7'\"",
      [1677] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1678] = "         FromLineIndex=7",
      [1679] = "         LineGUIDLinked=629977742",
      [1680] = "         OwnedEpisodeID=1",
      [1681] = "         StartTime=11.900000",
      [1682] = "      End Object",
      [1683] = "      Begin Object Name=\"BPS_StateControl_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_8'\"",
      [1684] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1685] = "         FromLineIndex=8",
      [1686] = "         LineGUIDLinked=3159574569",
      [1687] = "         OwnedEpisodeID=1",
      [1688] = "         StartTime=21.911299",
      [1689] = "      End Object",
      [1690] = "      Begin Object Name=\"BPS_StateControl_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_9'\"",
      [1691] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1692] = "         FromLineIndex=9",
      [1693] = "         LineGUIDLinked=2534311944",
      [1694] = "         OwnedEpisodeID=1",
      [1695] = "         StartTime=23.411299",
      [1696] = "      End Object",
      [1697] = "      Begin Object Name=\"BPS_StateControl_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_10'\"",
      [1698] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1699] = "         FromLineIndex=10",
      [1700] = "         LineGUIDLinked=3371102512",
      [1701] = "         OwnedEpisodeID=1",
      [1702] = "         StartTime=24.911299",
      [1703] = "      End Object",
      [1704] = "      Begin Object Name=\"BPS_StateControl_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_11'\"",
      [1705] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1706] = "         FromLineIndex=11",
      [1707] = "         LineGUIDLinked=2369900021",
      [1708] = "         OwnedEpisodeID=1",
      [1709] = "         StartTime=26.411299",
      [1710] = "      End Object",
      [1711] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1712] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1713] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1714] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1715] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1716] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1717] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1718] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_7'\"",
      [1719] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_8'\"",
      [1720] = "      ActionSections(9)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_9'\"",
      [1721] = "      ActionSections(10)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_10'\"",
      [1722] = "      ActionSections(11)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_11'\"",
      [1723] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1724] = "      FromTemplate=False",
      [1725] = "   End Object",
      [1726] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [1727] = "      EpisodeID=1",
      [1728] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [1729] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [1730] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [1731] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [1732] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_4'\"",
      [1733] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_5'\"",
      [1734] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_6'\"",
      [1735] = "      DialogueLines(7)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_7'\"",
      [1736] = "      DialogueLines(8)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_8'\"",
      [1737] = "      DialogueLines(9)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_9'\"",
      [1738] = "      DialogueLines(10)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_10'\"",
      [1739] = "      DialogueLines(11)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_11'\"",
      [1740] = "   End Object",
      [1741] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [1742] = "      EpisodeID=1",
      [1743] = "      ContentIndex=1",
      [1744] = "      GUID=2612427425",
      [1745] = "      UniqueID=CC4A773C03014CC5841949F536405AF3",
      [1746] = "      Duration=1.500000",
      [1747] = "      ContentString=\"跟我走，我有办法追踪到阿诺德现在的位置。\"",
      [1748] = "      ContentUI=\"Default\"",
      [1749] = "      Talker=(PerformerName=\"Actor4\")",
      [1750] = "      TalkerName=\"戴莉\"",
      [1751] = "   End Object",
      [1752] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [1753] = "   End Object",
      [1754] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [1755] = "      EpisodeID=1",
      [1756] = "      ContentIndex=2",
      [1757] = "      GUID=3996724380",
      [1758] = "      UniqueID=B59D768DE9884B5AAFB37A620AE7F251",
      [1759] = "      Duration=1.500000",
      [1760] = "      ContentString=\"可是，要怎样……\"",
      [1761] = "      ContentUI=\"Default\"",
      [1762] = "      Talker=(PerformerName=\"Actor2\")",
      [1763] = "   End Object",
      [1764] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [1765] = "   End Object",
      [1766] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [1767] = "      EpisodeID=1",
      [1768] = "      ContentIndex=3",
      [1769] = "      GUID=791657287",
      [1770] = "      UniqueID=02F0355735B44F67A5CCED5FE3DB4D39",
      [1771] = "      Duration=1.500000",
      [1772] = "      ContentString=\"相信我，之后我会跟你解释。\"",
      [1773] = "      ContentUI=\"Default\"",
      [1774] = "      Talker=(PerformerName=\"Actor4\")",
      [1775] = "      TalkerName=\"戴莉\"",
      [1776] = "   End Object",
      [1777] = "   Begin Object Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [1778] = "   End Object",
      [1779] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [1780] = "      EpisodeID=1",
      [1781] = "      ContentIndex=4",
      [1782] = "      GUID=1374171887",
      [1783] = "      UniqueID=9F8DA39821474955886FEA218A84871C",
      [1784] = "      Duration=1.500000",
      [1785] = "      ContentString=\"好，没问题，你来带路。\"",
      [1786] = "      ContentUI=\"Default\"",
      [1787] = "      Talker=(PerformerName=\"Actor2\")",
      [1788] = "   End Object",
      [1789] = "   Begin Object Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [1790] = "   End Object",
      [1791] = "   Begin Object Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [1792] = "      EpisodeID=1",
      [1793] = "      ContentIndex=5",
      [1794] = "      GUID=217142600",
      [1795] = "      UniqueID=EA12D3B0C62F48DEA10FCDAD4675A624",
      [1796] = "      Duration=1.500000",
      [1797] = "      ContentString=\"科恩黎，麻烦回公司通知大家这起非凡事件。\"",
      [1798] = "      ContentUI=\"Default\"",
      [1799] = "      Talker=(PerformerName=\"Actor2\")",
      [1800] = "   End Object",
      [1801] = "   Begin Object Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [1802] = "   End Object",
      [1803] = "   Begin Object Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [1804] = "      EpisodeID=1",
      [1805] = "      ContentIndex=6",
      [1806] = "      GUID=1727624281",
      [1807] = "      UniqueID=17C558C09F1C41818C58BF9AD9728774",
      [1808] = "      Duration=1.500000",
      [1809] = "      ContentString=\"队长，我跟你一起去！毕竟是我弄丢了阿诺德……\"",
      [1810] = "      ContentUI=\"Default\"",
      [1811] = "      Talker=(PerformerName=\"Actor1\")",
      [1812] = "   End Object",
      [1813] = "   Begin Object Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [1814] = "   End Object",
      [1815] = "   Begin Object Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [1816] = "      EpisodeID=1",
      [1817] = "      ContentIndex=7",
      [1818] = "      GUID=3592249620",
      [1819] = "      UniqueID=C3F46C417FD24B4699BF01147022D3DF",
      [1820] = "      Duration=1.500000",
      [1821] = "      ContentString=\"戴莉跟我一起，不用担心，我会带阿诺德回去的。\"",
      [1822] = "      ContentUI=\"Default\"",
      [1823] = "      Talker=(PerformerName=\"Actor2\")",
      [1824] = "   End Object",
      [1825] = "   Begin Object Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [1826] = "   End Object",
      [1827] = "   Begin Object Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [1828] = "      EpisodeID=1",
      [1829] = "      ContentIndex=8",
      [1830] = "      GUID=629977742",
      [1831] = "      UniqueID=D172F5B4BD374A4C9F0586D917D8057D",
      [1832] = "      Duration=1.500000",
      [1833] = "      ContentString=\"哦哦，好的！那就拜托你们了。\"",
      [1834] = "      ContentUI=\"Default\"",
      [1835] = "      Talker=(PerformerName=\"Actor1\")",
      [1836] = "   End Object",
      [1837] = "   Begin Object Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [1838] = "   End Object",
      [1839] = "   Begin Object Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [1840] = "      EpisodeID=1",
      [1841] = "      ContentIndex=9",
      [1842] = "      GUID=3159574569",
      [1843] = "      UniqueID=CE4BF24AE4A64F1292E1947D69D3495B",
      [1844] = "      Delay=8.511299",
      [1845] = "      Duration=1.500000",
      [1846] = "      ContentString=\"拜托你了，带我们找到阿诺德吧，夜莺银哨。\"",
      [1847] = "      ContentUI=\"Default\"",
      [1848] = "      Talker=(PerformerName=\"Actor4\")",
      [1849] = "      TalkerName=\"戴莉\"",
      [1850] = "   End Object",
      [1851] = "   Begin Object Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [1852] = "   End Object",
      [1853] = "   Begin Object Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [1854] = "      EpisodeID=1",
      [1855] = "      ContentIndex=10",
      [1856] = "      GUID=2534311944",
      [1857] = "      UniqueID=42FE4B3BE1F74D809B8F5789C1178407",
      [1858] = "      Duration=1.500000",
      [1859] = "      ContentString=\"！！！\"",
      [1860] = "      ContentUI=\"Default\"",
      [1861] = "      Talker=(PerformerName=\"Actor2\")",
      [1862] = "   End Object",
      [1863] = "   Begin Object Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [1864] = "   End Object",
      [1865] = "   Begin Object Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [1866] = "      EpisodeID=1",
      [1867] = "      ContentIndex=11",
      [1868] = "      GUID=3371102512",
      [1869] = "      UniqueID=B620ADEAA3184D9F93B26709BC8A8254",
      [1870] = "      Duration=1.500000",
      [1871] = "      ContentString=\"是卡萝队长的遗物，它怎么会在这里……这就是你的任务？\"",
      [1872] = "      ContentUI=\"Default\"",
      [1873] = "      Talker=(PerformerName=\"Actor2\")",
      [1874] = "   End Object",
      [1875] = "   Begin Object Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [1876] = "   End Object",
      [1877] = "   Begin Object Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [1878] = "      EpisodeID=1",
      [1879] = "      ContentIndex=12",
      [1880] = "      GUID=2369900021",
      [1881] = "      UniqueID=A501F3A8082B44718DEAAC15F2D75BE0",
      [1882] = "      Duration=1.500000",
      [1883] = "      ContentString=\"说来话长，我们先去找到阿诺德吧。\"",
      [1884] = "      ContentUI=\"Default\"",
      [1885] = "      Talker=(PerformerName=\"Actor4\")",
      [1886] = "      TalkerName=\"戴莉\"",
      [1887] = "   End Object",
      [1888] = "   Begin Object Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [1889] = "   End Object",
      [1890] = "   Note=\"支线·美梦成真·第二章第30段\"",
      [1891] = "   AnchorType=NewEnumerator1",
      [1892] = "   AnchorID=\"2255664694\"",
      [1893] = "   AnchorNpc=(PerformerName=\"Actor1\")",
      [1894] = "   NeedFadeOut=True",
      [1895] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [1896] = "   PreLoadArray(1)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C\"",
      [1897] = "   PreLoadArray(2)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [1898] = "   ActorInfos(0)=(PerformerName=\"Actor5\",AppearanceID=(ApperanceID=7216010),IdleAnimation=(AssetID=\"Idle\"))",
      [1899] = "   ActorInfos(1)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=7216022),IdleAnimation=(AssetID=\"Idle\"))",
      [1900] = "   ActorInfos(2)=(PerformerName=\"Actor2\",AppearanceID=(ApperanceID=7216002),IdleAnimation=(AssetID=\"Idle\"))",
      [1901] = "   ActorInfos(3)=(PerformerName=\"Actor3\",AppearanceID=(ApperanceID=7216009),IdleAnimation=(AssetID=\"Idle\"))",
      [1902] = "   ActorInfos(4)=(PerformerName=\"Actor4\",AppearanceID=(ApperanceID=7216003),IdleAnimation=(AssetID=\"Idle\"))",
      [1903] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/FourPeopleDialogue01.FourPeopleDialogue01'\"",
      [1904] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_0'\"",
      [1905] = "   StoryLineID=********",
      [1906] = "   Episodes(0)=(EpisodeID=1,Duration=26.511299,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_5'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueTrackCamera_15'\",\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'DialogueCameraCutAction_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_2'\"),Name=\"Episode:0\")",
      [1907] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_2'\"",
      [1908] = "   PerformerList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_3'\"",
      [1909] = "   PerformerList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_4'\"",
      [1910] = "   PerformerList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_5'\"",
      [1911] = "   PerformerList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [1912] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_15'\"",
      [1913] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_16'\"",
      [1914] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_17'\"",
      [1915] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_18'\"",
      [1916] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_19'\"",
      [1917] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_20'\"",
      [1918] = "   CameraList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_21'\"",
      [1919] = "   CameraList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_22'\"",
      [1920] = "   CameraList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_23'\"",
      [1921] = "   CameraList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_24'\"",
      [1922] = "   CameraList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_25'\"",
      [1923] = "   CameraList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_26'\"",
      [1924] = "   CameraList(12)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_27'\"",
      [1925] = "   CameraList(13)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_28'\"",
      [1926] = "   CameraList(14)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_29'\"",
      [1927] = "   CameraList(15)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_30'\"",
      [1928] = "   CameraList(16)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_31'\"",
      [1929] = "   CameraList(17)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_32'\"",
      [1930] = "   CameraList(18)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_33'\"",
      [1931] = "   CameraList(19)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [1932] = "   CameraList(20)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [1933] = "   CameraList(21)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [1934] = "   CameraList(22)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [1935] = "   NewEntityList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_0'\"",
      [1936] = "   NewEntityList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_1'\"",
      [1937] = "   NewEntityList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_2'\"",
      [1938] = "   NewEntityList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_3'\"",
      [1939] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [1940] = "End Object",
    },
  },
}