return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 12000010,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = true,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 558.7228,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 36.0019,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1232,
          ["X"] = 0.0761,
          ["Y"] = 0.0095,
          ["Z"] = -0.9894,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 541.9017,
          ["Y"] = 128.4821,
          ["Z"] = 207.1983,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 263.7121,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = -11.5404,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2349,
          ["X"] = 0.0475,
          ["Y"] = 0.0115,
          ["Z"] = -0.9708,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 218.0451,
          ["Y"] = 110.0633,
          ["Z"] = 159.6559,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 196.5365,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = -9.7912,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2554,
          ["X"] = -0.0085,
          ["Y"] = 0.0022,
          ["Z"] = 0.9668,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -2650.8081,
          ["Y"] = -24237.5824,
          ["Z"] = -1165.6856,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "侧面Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 213.3149,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_4",
      ["OffsetZ"] = -40.7512,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0974,
          ["X"] = -0.0625,
          ["Y"] = -0.0061,
          ["Z"] = -0.9933,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 200.0288,
          ["Y"] = 34.3235,
          ["Z"] = 130.4024,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "仰视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 213.1185,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_5",
      ["OffsetZ"] = -5.8691,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0209,
          ["X"] = 0.0209,
          ["Y"] = 0.0004,
          ["Z"] = -0.9996,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 212.5802,
          ["Y"] = 6.0524,
          ["Z"] = 165.3088,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "正视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 11.625,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.625,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["ObjectName"] = "BPS_DialogueShot_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "侧面Actor1",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["ObjectName"] = "BP_DialogueCameraCut_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [3] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Think_Start",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.1199,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Think_Loop",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 6.3579,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.1199,
                      ["bConstant"] = true,
                    },
                    [3] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Think_End",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3.8667,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5937,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "侧面Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "侧面Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "仰视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "仰视Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
              ["bAutoCameraTrack"] = false,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueCameraTrack_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_15",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_16",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_17",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "看起来菲利普就住在附近……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "他随身带着雪莉的照片，和她通了二十多年信，没结婚也可能是因为她……但她已经找到了他身边，他也一直没来见面？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 1.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "先找到菲利普，一切就都清楚了。不过这块怀表似乎也可以<P_Yellow>先交给雪莉保管</> ……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_5",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000010,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -2912.7556,
          ["Y"] = -24083.0469,
          ["Z"] = -1234.1837,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [5] = "   End Object",
      [6] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [43] = "   End Object",
      [44] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [45] = "   End Object",
      [46] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [47] = "   End Object",
      [48] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [49] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_15'\"",
      [50] = "      End Object",
      [51] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_16'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_17'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C Name=\"BPS_DialogueShot_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BPS_DialogueShot_C_0'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [59] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [60] = "      End Object",
      [61] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [62] = "      End Object",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5'\"",
      [65] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_0'\"",
      [66] = "      End Object",
      [67] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_1'\"",
      [68] = "      End Object",
      [69] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_2'\"",
      [70] = "      End Object",
      [71] = "   End Object",
      [72] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [73] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [74] = "      End Object",
      [75] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [76] = "      End Object",
      [77] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_2'\"",
      [78] = "      End Object",
      [79] = "   End Object",
      [80] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [81] = "   End Object",
      [82] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [83] = "   End Object",
      [84] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [85] = "   End Object",
      [86] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [87] = "   End Object",
      [88] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [95] = "   End Object",
      [96] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [97] = "   End Object",
      [98] = "   Begin Object Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [99] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'********:BPS_DialogueShot_C_0'\"",
      [100] = "   End Object",
      [101] = "   Begin Object Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [102] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [103] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [104] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_1'\"",
      [105] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_2'\"",
      [106] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_3'\"",
      [107] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_4'\"",
      [108] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_5'\"",
      [109] = "      TrackName=\"锚点\"",
      [110] = "   End Object",
      [111] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [112] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [113] = "      TrackName=\"锚点\"",
      [114] = "   End Object",
      [115] = "   Begin Object Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [116] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [117] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [118] = "      TrackName=\"Actor1\"",
      [119] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [120] = "   End Object",
      [121] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [122] = "      AppearanceID=12000010",
      [123] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [124] = "      bIsPlayer=True",
      [125] = "      SpawnTransform=(Translation=(X=-2912.755615,Y=-24083.046875,Z=-1234.183693))",
      [126] = "      TrackName=\"Actor1\"",
      [127] = "      StickGround=True",
      [128] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [129] = "   End Object",
      [130] = "   Begin Object Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [131] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [132] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [133] = "      TrackName=\"全景Actor1\"",
      [134] = "   End Object",
      [135] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [136] = "      FOV=60.000000",
      [137] = "      bEnableLookAt=True",
      [138] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [139] = "      OffsetZ=36.001919",
      [140] = "      bOverride_DepthOfField=True",
      [141] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [142] = "      DepthOfFieldFocalDistance=558.722778",
      [143] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [144] = "      SpawnTransform=(Rotation=(X=0.076131,Y=0.009483,Z=-0.989407,W=0.123237),Translation=(X=541.901706,Y=128.482063,Z=207.198300))",
      [145] = "      TrackName=\"全景Actor1\"",
      [146] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [147] = "   End Object",
      [148] = "   Begin Object Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [149] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [150] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [151] = "      TrackName=\"中景Actor1\"",
      [152] = "   End Object",
      [153] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [154] = "      FOV=60.000000",
      [155] = "      bEnableLookAt=True",
      [156] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [157] = "      OffsetZ=-11.540448",
      [158] = "      bOverride_DepthOfField=True",
      [159] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [160] = "      DepthOfFieldFocalDistance=263.712067",
      [161] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [162] = "      SpawnTransform=(Rotation=(X=0.047480,Y=0.011487,Z=-0.970801,W=0.234861),Translation=(X=218.045097,Y=110.063303,Z=159.655935))",
      [163] = "      TrackName=\"中景Actor1\"",
      [164] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [167] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [168] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [169] = "      TrackName=\"侧面Actor1\"",
      [170] = "   End Object",
      [171] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [172] = "      FOV=30.000000",
      [173] = "      bEnableLookAt=True",
      [174] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [175] = "      OffsetZ=-9.791186",
      [176] = "      bOverride_DepthOfField=True",
      [177] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [178] = "      DepthOfFieldFocalDistance=196.536469",
      [179] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [180] = "      SpawnTransform=(Rotation=(X=-0.008474,Y=0.002231,Z=0.966789,W=0.255427),Translation=(X=-2650.808080,Y=-24237.582378,Z=-1165.685614))",
      [181] = "      TrackName=\"侧面Actor1\"",
      [182] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [183] = "   End Object",
      [184] = "   Begin Object Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [185] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_4'\"",
      [186] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [187] = "      TrackName=\"仰视Actor1\"",
      [188] = "   End Object",
      [189] = "   Begin Object Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [190] = "      FOV=40.000000",
      [191] = "      bEnableLookAt=True",
      [192] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [193] = "      OffsetZ=-40.751160",
      [194] = "      bOverride_DepthOfField=True",
      [195] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [196] = "      DepthOfFieldFocalDistance=213.314896",
      [197] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [198] = "      SpawnTransform=(Rotation=(X=-0.062491,Y=-0.006127,Z=-0.993264,W=0.097390),Translation=(X=200.028765,Y=34.323458,Z=130.402434))",
      [199] = "      TrackName=\"仰视Actor1\"",
      [200] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [201] = "   End Object",
      [202] = "   Begin Object Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [203] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_5'\"",
      [204] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [205] = "      TrackName=\"正视Actor1\"",
      [206] = "   End Object",
      [207] = "   Begin Object Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [208] = "      FOV=40.000000",
      [209] = "      bEnableLookAt=True",
      [210] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [211] = "      OffsetZ=-5.869136",
      [212] = "      bOverride_DepthOfField=True",
      [213] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [214] = "      DepthOfFieldFocalDistance=213.118515",
      [215] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [216] = "      SpawnTransform=(Rotation=(X=0.020938,Y=0.000439,Z=-0.999561,W=0.020938),Translation=(X=212.580214,Y=6.052350,Z=165.308807))",
      [217] = "      TrackName=\"正视Actor1\"",
      [218] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [219] = "   End Object",
      [220] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [221] = "      EpisodeID=1",
      [222] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [223] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [224] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [225] = "   End Object",
      [226] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [227] = "      GUID=2177280606",
      [228] = "      Duration=1.625000",
      [229] = "      ContentString=\"看起来菲利普就住在附近……\"",
      [230] = "      ContentUI=\"Default\"",
      [231] = "      Talker=(PerformerName=\"Actor1\")",
      [232] = "   End Object",
      [233] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [234] = "      CameraCutDuration=1.625000",
      [235] = "      CanSkip=True",
      [236] = "      EpisodeID=1",
      [237] = "      ContentIndex=1",
      [238] = "   End Object",
      [239] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [240] = "      GUID=4197421987",
      [241] = "      ContentString=\"他随身带着雪莉的照片，和她通了二十多年信，没结婚也可能是因为她……但她已经找到了他身边，他也一直没来见面？\"",
      [242] = "      ContentUI=\"Default\"",
      [243] = "      Talker=(PerformerName=\"Actor1\")",
      [244] = "   End Object",
      [245] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [246] = "      CameraCutDuration=5.000000",
      [247] = "      CanSkip=True",
      [248] = "      EpisodeID=1",
      [249] = "      ContentIndex=2",
      [250] = "   End Object",
      [251] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [252] = "      GUID=2614395701",
      [253] = "      ContentString=\"先找到菲利普，一切就都清楚了。不过这块怀表似乎也可以<P_Yellow>先交给雪莉保管</> ……\"",
      [254] = "      ContentUI=\"Default\"",
      [255] = "      Talker=(PerformerName=\"Actor1\")",
      [256] = "   End Object",
      [257] = "   Begin Object Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [258] = "      CameraCutDuration=5.000000",
      [259] = "      CanSkip=True",
      [260] = "      EpisodeID=1",
      [261] = "      ContentIndex=3",
      [262] = "   End Object",
      [263] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [264] = "      Begin Object Name=\"BPS_StateControl_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_15'\"",
      [265] = "         bFixed=True",
      [266] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [267] = "         FromLineIndex=0",
      [268] = "         LineGUIDLinked=2177280606",
      [269] = "         OwnedEpisodeID=1",
      [270] = "         StartTime=1.525000",
      [271] = "      End Object",
      [272] = "      Begin Object Name=\"BPS_StateControl_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_16'\"",
      [273] = "         bFixed=True",
      [274] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [275] = "         FromLineIndex=1",
      [276] = "         LineGUIDLinked=4197421987",
      [277] = "         OwnedEpisodeID=1",
      [278] = "         StartTime=6.525000",
      [279] = "      End Object",
      [280] = "      Begin Object Name=\"BPS_StateControl_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_17'\"",
      [281] = "         bFixed=True",
      [282] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [283] = "         FromLineIndex=2",
      [284] = "         LineGUIDLinked=2614395701",
      [285] = "         OwnedEpisodeID=1",
      [286] = "         StartTime=11.525000",
      [287] = "      End Object",
      [288] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_15'\"",
      [289] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_16'\"",
      [290] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_17'\"",
      [291] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [292] = "      FromTemplate=False",
      [293] = "   End Object",
      [294] = "   Begin Object Name=\"BPS_DialogueShot_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BPS_DialogueShot_C_0'\"",
      [295] = "      TargetCamera=(CameraName=\"侧面Actor1\")",
      [296] = "      BreathDuration=1.625000",
      [297] = "      DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [298] = "      OwnedEpisodeID=1",
      [299] = "   End Object",
      [300] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [301] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [302] = "         NodeGuid=50B5308A4224F4402A9F2C947230287E",
      [303] = "         CustomProperties Pin (PinId=E39DD5424FFD1E544E544C84AB4A70C9,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_3 97CE99D444514097CF0064A00972FE9F,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [304] = "      End Object",
      [305] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [306] = "         EpisodeID=1",
      [307] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [308] = "         NodePosX=300",
      [309] = "         NodeGuid=B86BE9124955DAA462F31CBE83D4997E",
      [310] = "         CustomProperties Pin (PinId=97CE99D444514097CF0064A00972FE9F,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 E39DD5424FFD1E544E544C84AB4A70C9,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [311] = "      End Object",
      [312] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [313] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [314] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_3'\"",
      [315] = "      GraphGuid=E67FB02844F37584753C6EADC6458215",
      [316] = "   End Object",
      [317] = "   Begin Object Name=\"DialogueDialogueTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5'\"",
      [318] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_0'\"",
      [319] = "         EpisodeID=1",
      [320] = "         ContentIndex=1",
      [321] = "         Talker=\"Actor1\"",
      [322] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_5'\"",
      [323] = "         SectionName=\"看起来菲利普就住在附近……\"",
      [324] = "         FromLineIndex=0",
      [325] = "         LineGUIDLinked=2177280606",
      [326] = "         OwnedEpisodeID=1",
      [327] = "         Duration=1.625000",
      [328] = "      End Object",
      [329] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_1'\"",
      [330] = "         EpisodeID=1",
      [331] = "         ContentIndex=2",
      [332] = "         Talker=\"Actor1\"",
      [333] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_5'\"",
      [334] = "         SectionName=\"他随身带着雪莉的照片，和她通了二十多年信，没结婚也可能是因为她……但她已经找到了他身边，他也一直没来见面？\"",
      [335] = "         FromLineIndex=1",
      [336] = "         LineGUIDLinked=4197421987",
      [337] = "         OwnedEpisodeID=1",
      [338] = "         StartTime=1.625000",
      [339] = "         Duration=5.000000",
      [340] = "      End Object",
      [341] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5.BPS_Dialogue_C_2'\"",
      [342] = "         EpisodeID=1",
      [343] = "         ContentIndex=3",
      [344] = "         Talker=\"Actor1\"",
      [345] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_5'\"",
      [346] = "         SectionName=\"先找到菲利普，一切就都清楚了。不过这块怀表似乎也可以<P_Yellow>先交给雪莉保管</> ……\"",
      [347] = "         FromLineIndex=2",
      [348] = "         LineGUIDLinked=2614395701",
      [349] = "         OwnedEpisodeID=1",
      [350] = "         StartTime=6.625000",
      [351] = "         Duration=5.000000",
      [352] = "      End Object",
      [353] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [354] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [355] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [356] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [357] = "      FromTemplate=False",
      [358] = "   End Object",
      [359] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [360] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [361] = "         AnimLibItem=(AssetID=\"Think_Start\")",
      [362] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [363] = "         OwnedEpisodeID=1",
      [364] = "         Duration=1.119949",
      [365] = "      End Object",
      [366] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [367] = "         AnimLibItem=(AssetID=\"Think_Loop\")",
      [368] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [369] = "         OwnedEpisodeID=1",
      [370] = "         StartTime=1.119949",
      [371] = "         Duration=6.357919",
      [372] = "      End Object",
      [373] = "      Begin Object Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_2'\"",
      [374] = "         AnimLibItem=(AssetID=\"Think_End\")",
      [375] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [376] = "         OwnedEpisodeID=1",
      [377] = "         StartTime=7.593729",
      [378] = "         Duration=3.866667",
      [379] = "      End Object",
      [380] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [381] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [382] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_2'\"",
      [383] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [384] = "   End Object",
      [385] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [386] = "      EpisodeID=1",
      [387] = "   End Object",
      [388] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [389] = "      EpisodeID=1",
      [390] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [391] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_4'\"",
      [392] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_5'\"",
      [393] = "   End Object",
      [394] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [395] = "      EpisodeID=1",
      [396] = "      ContentIndex=1",
      [397] = "      UniqueID=905AABA68B604D96BE814A61571355DA",
      [398] = "      Duration=1.625000",
      [399] = "      ContentString=\"看起来菲利普就住在附近……\"",
      [400] = "      ContentUI=\"Default\"",
      [401] = "      Talker=(PerformerName=\"Actor1\")",
      [402] = "   End Object",
      [403] = "   Begin Object Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [404] = "   End Object",
      [405] = "   Begin Object Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [406] = "      EpisodeID=1",
      [407] = "      ContentIndex=2",
      [408] = "      UniqueID=5D7449653C764D889EE7E41A611CC3BC",
      [409] = "      ContentString=\"他随身带着雪莉的照片，和她通了二十多年信，没结婚也可能是因为她……但她已经找到了他身边，他也一直没来见面？\"",
      [410] = "      ContentUI=\"Default\"",
      [411] = "      Talker=(PerformerName=\"Actor1\")",
      [412] = "   End Object",
      [413] = "   Begin Object Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [414] = "   End Object",
      [415] = "   Begin Object Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [416] = "      EpisodeID=1",
      [417] = "      ContentIndex=3",
      [418] = "      UniqueID=9E256E80773E40458F5A438C5306C2EC",
      [419] = "      ContentString=\"先找到菲利普，一切就都清楚了。不过这块怀表似乎也可以<P_Yellow>先交给雪莉保管</> ……\"",
      [420] = "      ContentUI=\"Default\"",
      [421] = "      Talker=(PerformerName=\"Actor1\")",
      [422] = "   End Object",
      [423] = "   Begin Object Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [424] = "   End Object",
      [425] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [426] = "   PreLoadArray(1)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [427] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=12000010),IdleAnimation=(AssetID=\"Idle\"),bIsPlayer=True)",
      [428] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue'\"",
      [429] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_1'\"",
      [430] = "   StoryLineID=********",
      [431] = "   Episodes(0)=(EpisodeID=1,Duration=11.625000,TrackList=(\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'BP_DialogueCameraCut_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueCameraTrack_0'\",\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_5'\"))",
      [432] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [433] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [434] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [435] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [436] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [437] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_4'\"",
      [438] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_5'\"",
      [439] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [440] = "End Object",
    },
  },
}