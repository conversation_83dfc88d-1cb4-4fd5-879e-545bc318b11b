return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 12000011,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 558.7228,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 36.0019,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1232,
          ["X"] = 0.0761,
          ["Y"] = 0.0095,
          ["Z"] = -0.9894,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 541.9017,
          ["Y"] = 128.4821,
          ["Z"] = 207.1983,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 263.7121,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = -11.5404,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2349,
          ["X"] = 0.0475,
          ["Y"] = 0.0115,
          ["Z"] = -0.9708,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 218.0451,
          ["Y"] = 110.0633,
          ["Z"] = 159.6559,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 196.5365,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = -5.8488,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3859,
          ["X"] = -0.0048,
          ["Y"] = -0.002,
          ["Z"] = -0.9225,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 128.6362,
          ["Y"] = 127.6284,
          ["Z"] = 165.3475,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "侧面Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 213.3149,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_4",
      ["OffsetZ"] = -40.7939,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0974,
          ["X"] = -0.0625,
          ["Y"] = -0.0061,
          ["Z"] = -0.9933,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 200.0288,
          ["Y"] = 34.3235,
          ["Z"] = 130.4024,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "仰视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 213.1185,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_5",
      ["OffsetZ"] = -5.8876,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0209,
          ["X"] = 0.0209,
          ["Y"] = 0.0004,
          ["Z"] = -0.9996,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 212.5802,
          ["Y"] = 6.0524,
          ["Z"] = 165.3088,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "正视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["EnableDOF"] = false,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 5,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_1",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["AsideID"] = 60002003,
              ["BlackScreenType"] = 1,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["NeedFadeIn"] = true,
              ["NeedFadeOut"] = true,
              ["NeedPause"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C",
              ["ObjectName"] = "BPS_BlackScreenText_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C",
          ["ObjectName"] = "BP_DialogueTrackBlackScreenText_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C",
          ["TrackName"] = "黑屏白字",
        },
        [3] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [4] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["ObjectName"] = "BP_DialogueCameraCut_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "侧面Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "侧面Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "仰视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "仰视Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
              ["bAutoCameraTrack"] = false,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueTrackCamera_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [6] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000011,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 88,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [5] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [6] = "      End Object",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_5'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_4'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_0'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [43] = "   End Object",
      [44] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_1'\"",
      [45] = "   End Object",
      [46] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [47] = "   End Object",
      [48] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [49] = "   End Object",
      [50] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [51] = "   End Object",
      [52] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C Name=\"BP_DialogueTrackBlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [53] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C Name=\"BPS_BlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0.BPS_BlackScreenText_C_0'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlAction_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_3'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [59] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_5\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_5'\"",
      [60] = "      End Object",
      [61] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [62] = "      End Object",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [65] = "   End Object",
      [66] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [67] = "   End Object",
      [68] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [69] = "   End Object",
      [70] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [71] = "   End Object",
      [72] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [73] = "      EpisodeID=1",
      [74] = "   End Object",
      [75] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [76] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [77] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [78] = "         FromLineIndex=0",
      [79] = "         LineGUIDLinked=4178592775",
      [80] = "         OwnedEpisodeID=1",
      [81] = "         StartTime=1.400000",
      [82] = "      End Object",
      [83] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [84] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [85] = "      FromTemplate=False",
      [86] = "   End Object",
      [87] = "   Begin Object Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [88] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [89] = "      FromTemplate=False",
      [90] = "   End Object",
      [91] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [92] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [93] = "      FromTemplate=False",
      [94] = "   End Object",
      [95] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [96] = "      CanSkip=True",
      [97] = "      EpisodeID=1",
      [98] = "      ContentIndex=1",
      [99] = "   End Object",
      [100] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [101] = "      GUID=4178592775",
      [102] = "      Duration=1.500000",
      [103] = "   End Object",
      [104] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [105] = "      EpisodeID=1",
      [106] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [107] = "   End Object",
      [108] = "   Begin Object Name=\"DialogueTrackCamera_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_5'\"",
      [109] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_5'\"",
      [110] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [111] = "      TrackName=\"正视Actor1\"",
      [112] = "   End Object",
      [113] = "   Begin Object Name=\"DialogueTrackCamera_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_4'\"",
      [114] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_4'\"",
      [115] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [116] = "      TrackName=\"仰视Actor1\"",
      [117] = "   End Object",
      [118] = "   Begin Object Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [119] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [120] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [121] = "      TrackName=\"侧面Actor1\"",
      [122] = "   End Object",
      [123] = "   Begin Object Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [124] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [125] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [126] = "      TrackName=\"中景Actor1\"",
      [127] = "   End Object",
      [128] = "   Begin Object Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [129] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [130] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [131] = "      TrackName=\"全景Actor1\"",
      [132] = "   End Object",
      [133] = "   Begin Object Name=\"DialogueTrackActor_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_0'\"",
      [134] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [135] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [136] = "      TrackName=\"Actor1\"",
      [137] = "   End Object",
      [138] = "   Begin Object Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [139] = "      FOV=40.000000",
      [140] = "      bEnableLookAt=True",
      [141] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [142] = "      OffsetZ=-5.887576",
      [143] = "      bOverride_DepthOfField=True",
      [144] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [145] = "      DepthOfFieldFocalDistance=213.118515",
      [146] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [147] = "      SpawnTransform=(Rotation=(X=0.020938,Y=0.000439,Z=-0.999561,W=0.020938),Translation=(X=212.580214,Y=6.052350,Z=165.308807))",
      [148] = "      TrackName=\"正视Actor1\"",
      [149] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [150] = "   End Object",
      [151] = "   Begin Object Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [152] = "      FOV=40.000000",
      [153] = "      bEnableLookAt=True",
      [154] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [155] = "      OffsetZ=-40.793949",
      [156] = "      bOverride_DepthOfField=True",
      [157] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [158] = "      DepthOfFieldFocalDistance=213.314896",
      [159] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [160] = "      SpawnTransform=(Rotation=(X=-0.062491,Y=-0.006127,Z=-0.993264,W=0.097390),Translation=(X=200.028765,Y=34.323458,Z=130.402434))",
      [161] = "      TrackName=\"仰视Actor1\"",
      [162] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [165] = "      FOV=30.000000",
      [166] = "      bEnableLookAt=True",
      [167] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [168] = "      OffsetZ=-5.848835",
      [169] = "      bOverride_DepthOfField=True",
      [170] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [171] = "      DepthOfFieldFocalDistance=196.536469",
      [172] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [173] = "      SpawnTransform=(Rotation=(X=-0.004830,Y=-0.002021,Z=-0.922525,W=0.385901),Translation=(X=128.636248,Y=127.628353,Z=165.347548))",
      [174] = "      TrackName=\"侧面Actor1\"",
      [175] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [176] = "   End Object",
      [177] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [178] = "      FOV=60.000000",
      [179] = "      bEnableLookAt=True",
      [180] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [181] = "      OffsetZ=-11.540448",
      [182] = "      bOverride_DepthOfField=True",
      [183] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [184] = "      DepthOfFieldFocalDistance=263.712067",
      [185] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [186] = "      SpawnTransform=(Rotation=(X=0.047480,Y=0.011487,Z=-0.970801,W=0.234861),Translation=(X=218.045097,Y=110.063303,Z=159.655935))",
      [187] = "      TrackName=\"中景Actor1\"",
      [188] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [189] = "   End Object",
      [190] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [191] = "      FOV=60.000000",
      [192] = "      bEnableLookAt=True",
      [193] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [194] = "      OffsetZ=36.001919",
      [195] = "      bOverride_DepthOfField=True",
      [196] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [197] = "      DepthOfFieldFocalDistance=558.722778",
      [198] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [199] = "      SpawnTransform=(Rotation=(X=0.076131,Y=0.009483,Z=-0.989407,W=0.123237),Translation=(X=541.901706,Y=128.482063,Z=207.198300))",
      [200] = "      TrackName=\"全景Actor1\"",
      [201] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [202] = "   End Object",
      [203] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [204] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [205] = "      TrackName=\"锚点\"",
      [206] = "   End Object",
      [207] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [208] = "      AppearanceID=12000011",
      [209] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [210] = "      SpawnTransform=(Translation=(X=0.000000,Y=0.000000,Z=88.000000))",
      [211] = "      TrackName=\"Actor1\"",
      [212] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [213] = "   End Object",
      [214] = "   Begin Object Name=\"DialogueDialogueAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_1'\"",
      [215] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [216] = "      FromTemplate=False",
      [217] = "   End Object",
      [218] = "   Begin Object Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [219] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [220] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_0'\"",
      [221] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_1'\"",
      [222] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_2'\"",
      [223] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_3'\"",
      [224] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_4'\"",
      [225] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_5'\"",
      [226] = "      TrackName=\"锚点\"",
      [227] = "   End Object",
      [228] = "   Begin Object Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [229] = "   End Object",
      [230] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [231] = "   End Object",
      [232] = "   Begin Object Name=\"BP_DialogueTrackBlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [233] = "      Begin Object Name=\"BPS_BlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0.BPS_BlackScreenText_C_0'\"",
      [234] = "         BlackScreenType=NewEnumerator2",
      [235] = "         AsideID=60002003",
      [236] = "         NeedPause=True",
      [237] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [238] = "         OwnedEpisodeID=1",
      [239] = "         Duration=5.000000",
      [240] = "      End Object",
      [241] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'BPS_BlackScreenText_C_0'\"",
      [242] = "      FromTemplate=False",
      [243] = "   End Object",
      [244] = "   Begin Object Name=\"DialogueStateControlAction_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_3'\"",
      [245] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [246] = "      FromTemplate=False",
      [247] = "   End Object",
      [248] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [249] = "      Begin Object Name=\"EpisodeGraphNode_5\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_5'\"",
      [250] = "         EpisodeID=1",
      [251] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [252] = "         NodePosX=300",
      [253] = "         NodeGuid=08A06A204C9A7C51D01CA0B31661ECB3",
      [254] = "         CustomProperties Pin (PinId=6F4CB21B4B8568E407EA9AAE7C4406E5,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 8C9FBADF4269610754C4038DE84BFBC0,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [255] = "      End Object",
      [256] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [257] = "         NodeGuid=6C00238D423FAEFF02556BB81E5EDBFF",
      [258] = "         CustomProperties Pin (PinId=8C9FBADF4269610754C4038DE84BFBC0,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_5 6F4CB21B4B8568E407EA9AAE7C4406E5,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [259] = "      End Object",
      [260] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [261] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [262] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_5'\"",
      [263] = "      GraphGuid=75B7BDDE49E6C975B02FA1B8DD0E19CC",
      [264] = "   End Object",
      [265] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [266] = "      EpisodeID=1",
      [267] = "   End Object",
      [268] = "   Begin Object Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [269] = "      EpisodeID=1",
      [270] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [271] = "   End Object",
      [272] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [273] = "      EpisodeID=1",
      [274] = "      ContentIndex=1",
      [275] = "      UniqueID=A480C63C15D04D95B2A4957A096DFE12",
      [276] = "      Duration=1.500000",
      [277] = "   End Object",
      [278] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [279] = "   End Object",
      [280] = "   EnableDOF=False",
      [281] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [282] = "   PreLoadArray(1)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [283] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=12000011),IdleAnimation=(AssetID=\"Idle\"))",
      [284] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue'\"",
      [285] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_2'\"",
      [286] = "   StoryLineID=********",
      [287] = "   Episodes(0)=(EpisodeID=1,Duration=5.000000,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_1'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'BP_DialogueTrackBlackScreenText_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'BP_DialogueCameraCut_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueTrackCamera_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_0'\"))",
      [288] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [289] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [290] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [291] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [292] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [293] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_4'\"",
      [294] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_5'\"",
      [295] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [296] = "End Object",
    },
  },
}