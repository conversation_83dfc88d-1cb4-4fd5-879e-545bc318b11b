return {
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9962,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.0872,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 7026,
          ["Y"] = 25148,
          ["Z"] = -2598,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5766,
          ["X"] = 0.1827,
          ["Y"] = 0.1342,
          ["Z"] = -0.785,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 350.8687,
          ["Y"] = 594.7094,
          ["Z"] = 404.2426,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6515,
          ["X"] = 0.0317,
          ["Y"] = 0.0273,
          ["Z"] = -0.7575,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 616.8246,
          ["Y"] = 1144.0205,
          ["Z"] = 198.515,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "全景01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7207,
          ["X"] = 0.0873,
          ["Y"] = 0.0923,
          ["Z"] = -0.6815,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 100.1886,
          ["Y"] = 396.3069,
          ["Z"] = 204.9806,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "全景02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8069,
          ["X"] = 0.0082,
          ["Y"] = 0.0113,
          ["Z"] = -0.5905,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -13.5676,
          ["Y"] = 403.0668,
          ["Z"] = 135.9632,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 157.2332,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 4.9915,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1647,
          ["X"] = 0.0619,
          ["Y"] = 0.0104,
          ["Z"] = -0.9843,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 149.0268,
          ["Y"] = 47.5596,
          ["Z"] = 175.856,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "单人中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 180,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 55,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5058,
          ["X"] = 0.0256,
          ["Y"] = 0.015,
          ["Z"] = -0.8621,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 190.6237,
          ["Y"] = 248.8606,
          ["Z"] = 172.9037,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "三人中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 336.4417,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.981,
          ["X"] = 0.0044,
          ["Y"] = 0.0223,
          ["Z"] = -0.1925,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -114.5727,
          ["Y"] = 106.0487,
          ["Z"] = 151.6526,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "过肩对话01",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 278.55,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -5.1995,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1339,
          ["X"] = 0.0242,
          ["Y"] = 0.0033,
          ["Z"] = -0.9907,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 353.0854,
          ["Y"] = 93.9886,
          ["Z"] = 165.6839,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "过肩对话02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 157.6975,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 6.0004,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1632,
          ["X"] = 0.031,
          ["Y"] = 0.0051,
          ["Z"] = -0.9861,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 149.1314,
          ["Y"] = 48.82,
          ["Z"] = 88.8838,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor1近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 201.3155,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0.1315,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2299,
          ["X"] = -0.034,
          ["Y"] = 0.008,
          ["Z"] = 0.9726,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 180.6402,
          ["Y"] = -81.4096,
          ["Z"] = 69.9778,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor2中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 93.4978,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 4.9611,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2973,
          ["X"] = -0.0217,
          ["Y"] = 0.0067,
          ["Z"] = 0.9545,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 85.8108,
          ["Y"] = -47.4179,
          ["Z"] = 101.4133,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor2近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 108.8218,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor3",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 7.1331,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0871,
          ["X"] = 0.0261,
          ["Y"] = -0.0023,
          ["Z"] = -0.9959,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 103.6598,
          ["Y"] = -18.4753,
          ["Z"] = 77.0694,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor3近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 20,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 300,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor3",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -0.197,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1976,
          ["X"] = -0.0188,
          ["Y"] = 0.0038,
          ["Z"] = 0.9801,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 227.6898,
          ["Y"] = -78.6121,
          ["Z"] = 96.2418,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor3中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 257.981,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor4",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -13.0296,
      ["Parent"] = "Actor4",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2079,
          ["X"] = 0.0137,
          ["Y"] = -0.0029,
          ["Z"] = -0.9781,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 238.7803,
          ["Y"] = -96.1703,
          ["Z"] = 69.5624,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor4中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 125.744,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor4",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor4",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 7.0402,
      ["Parent"] = "Actor4",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1288,
          ["X"] = 0.0242,
          ["Y"] = -0.0031,
          ["Z"] = -0.9914,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 121.7036,
          ["Y"] = -29.7193,
          ["Z"] = 90.9104,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Actor4近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/FourPeopleDialogue02.FourPeopleDialogue02",
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 20,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 1256711304,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1367541495,
                ["B"] = -570078263,
                ["C"] = -1832269665,
                ["D"] = -991975169,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 218050180,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1988091857,
                ["B"] = -633648771,
                ["C"] = -1601070823,
                ["D"] = -1193602037,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 535582879,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1285930505,
                ["B"] = 2028749567,
                ["C"] = -1287440257,
                ["D"] = 1021562754,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [4] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 1284355376,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1748593320,
                ["B"] = 350176435,
                ["C"] = -1302826142,
                ["D"] = 1270812251,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [5] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 3365588354,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1701025895,
                ["B"] = 749619769,
                ["C"] = -1221321784,
                ["D"] = -1626580117,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 13.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [6] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 4030760430,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1121950241,
                ["B"] = 1423327381,
                ["C"] = -1161728397,
                ["D"] = -1393886108,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 15.775,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [7] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 1894739120,
              ["LineUniqueIDLinked"] = {
                ["A"] = 713687864,
                ["B"] = 1202341389,
                ["C"] = -2014332934,
                ["D"] = 881864256,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 18.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [8] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 4032483796,
              ["LineUniqueIDLinked"] = {
                ["A"] = -783285820,
                ["B"] = 26035692,
                ["C"] = -1881931238,
                ["D"] = 1216980269,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 19.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "Actor3",
                },
                [2] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor3",
                  ["Target"] = "Actor1",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.5,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "三人中景",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "三人中景",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor3中景",
              ["bConstant"] = false,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.75,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1近景",
              ["bConstant"] = false,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.25,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.25,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor3中景",
              ["bConstant"] = false,
            },
            [6] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.375,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 13.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "三人中景",
              ["bConstant"] = false,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.625,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 15.875,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor4中景",
              ["bConstant"] = false,
            },
            [8] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 18.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "全景02",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [4] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景01",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景02",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "单人中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "单人中景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "三人中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "三人中景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "过肩对话01",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "过肩对话01",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "过肩对话02",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "过肩对话02",
              ["bAutoCameraTrack"] = false,
            },
            [9] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "ScratchHead",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 4.6868,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.5,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "CrossArm",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.1149,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 13.7601,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor2中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "Actor2中景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor2近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "Actor2近景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [10] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Talk_Yes_A",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 6.5,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Big_Puzzled",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.0386,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 11.4614,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor3",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor3近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "Actor3近景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor3中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "Actor3中景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor3",
            },
            [11] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Short_Talk2",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.75,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9.5,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor1近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "Actor1近景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor4中景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor4",
                  ["TrackName"] = "Actor4中景",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Actor4近景",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor4",
                  ["TrackName"] = "Actor4近景",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor4",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor4",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 1256711304,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1367541495,
                ["B"] = -570078263,
                ["C"] = -1832269665,
                ["D"] = -991975169,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "你们也是来面试的吗？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 218050180,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1988091857,
                ["B"] = -633648771,
                ["C"] = -1601070823,
                ["D"] = -1193602037,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "<P_Heart>（小声）</>该死，这里好福利的名声已经传出去了，竞争者有点多啊……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 1.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 535582879,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1285930505,
                ["B"] = 2028749567,
                ["C"] = -1287440257,
                ["D"] = 1021562754,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "算是吧，我们之前在码头干活，想找一份稳定的工作。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 1.75,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 1284355376,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1748593320,
                ["B"] = 350176435,
                ["C"] = -1302826142,
                ["D"] = 1270812251,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "听说这里刚刚解雇了一批孩子。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 9.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 2.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 3365588354,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1701025895,
                ["B"] = 749619769,
                ["C"] = -1221321784,
                ["D"] = -1626580117,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "真的吗！但我听说，这里的待遇非常好？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 11.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 2.375,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 4030760430,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1121950241,
                ["B"] = 1423327381,
                ["C"] = -1161728397,
                ["D"] = -1393886108,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "那你听说，这里每天要做奇怪的事情了吗？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 13.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 2.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 1894739120,
              ["LineUniqueIDLinked"] = {
                ["A"] = 713687864,
                ["B"] = 1202341389,
                ["C"] = -2014332934,
                ["D"] = 881864256,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "应聘者们，我是工厂经理卡勒， 大家排好队！",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 15.875,
              ["SubTitle"] = "",
              ["Talker"] = "Actor4",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [8] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 8,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 4032483796,
              ["LineUniqueIDLinked"] = {
                ["A"] = -783285820,
                ["B"] = 26035692,
                ["C"] = -1881931238,
                ["D"] = 1216980269,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "面试要开始了！",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 18.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 3,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "最好的时代",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000010,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Idle.A_Base_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9659,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.2588,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 97.5552,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7212130,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewFemale/A_Base_F_Idle.A_Base_F_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 1,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 151.6809,
          ["Y"] = 67.324,
          ["Z"] = 78.2469,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7212131,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Idle.A_Base_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9659,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.2588,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 43.4229,
          ["Y"] = 107.5684,
          ["Z"] = 77.8553,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor3",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7212132,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "CrossArm",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Idle.A_Base_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6428,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.766,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 623.4909,
          ["Y"] = 978.5597,
          ["Z"] = 99.4252,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor4",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["StoryLineID"] = ********,
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnly"] = {
  },
}