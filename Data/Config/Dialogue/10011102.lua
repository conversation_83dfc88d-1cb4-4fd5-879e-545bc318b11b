return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 7216035,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Directions_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Performer1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 301,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_11",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["Actions"] = {
            [1] = {
              ["ActionSections"] = {
                [1] = {
                  ["Duration"] = 0.0333,
                  ["DynamicFlag"] = "",
                  ["Enable"] = true,
                  ["FromLineIndex"] = -1,
                  ["LineUniqueIDLinked"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["C"] = 0,
                    ["D"] = 0,
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C",
                  ["ObjectName"] = "BPS_ActorStill_C_0",
                  ["OwnedEpisodeID"] = 1,
                  ["SectionName"] = "Section",
                  ["StartTime"] = 1,
                  ["Still"] = true,
                  ["bConstant"] = false,
                },
              },
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C",
              ["ObjectName"] = "BP_DialogueTrackActorStill_C_0",
              ["Parent"] = "Performer1",
              ["Priority"] = 0,
              ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C",
              ["TrackName"] = "Still",
            },
          },
          ["Childs"] = {
          },
          ["DialogueEntity"] = "Performer1",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
          ["ObjectName"] = "DialoguePerformerTrack_0",
          ["Parent"] = "",
          ["TrackName"] = "Performer1",
        },
        [3] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_5",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 0,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7216035,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Directions_Loop",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.342,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9397,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 2095.164,
          ["Y"] = -1980.0018,
          ["Z"] = -12.1342,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Performer1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = false,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_11'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5'\"",
      [5] = "   End Object",
      [6] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_10'\"",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDissolve.BP_DialogueTrackDissolve_C Name=\"BP_DialogueTrackDissolve_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDissolve.BP_DialogueTrackDissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0'\"",
      [9] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C Name=\"BPS_Dissolve_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0.BPS_Dissolve_C_0'\"",
      [10] = "      End Object",
      [11] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C Name=\"BPS_Dissolve_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0.BPS_Dissolve_C_1'\"",
      [12] = "      End Object",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_2'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_4'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_7'\"",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_8'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_4'\"",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_9'\"",
      [43] = "   End Object",
      [44] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [45] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_4\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_4'\"",
      [46] = "      End Object",
      [47] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [48] = "      End Object",
      [49] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [50] = "      End Object",
      [51] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_1'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [59] = "   End Object",
      [60] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C Name=\"BP_DialogueTrackActorStill_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorStill_C_0'\"",
      [61] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C Name=\"BPS_ActorStill_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorStill_C_0.BPS_ActorStill_C_0'\"",
      [62] = "      End Object",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [65] = "   End Object",
      [66] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [67] = "   End Object",
      [68] = "   Begin Object Name=\"DialogueStateControlTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_11'\"",
      [69] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [70] = "      FromTemplate=False",
      [71] = "   End Object",
      [72] = "   Begin Object Name=\"DialogueDialogueTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_5'\"",
      [73] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [74] = "      FromTemplate=False",
      [75] = "   End Object",
      [76] = "   Begin Object Name=\"DialogueStateControlTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_10'\"",
      [77] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [78] = "      FromTemplate=False",
      [79] = "   End Object",
      [80] = "   Begin Object Name=\"BP_DialogueTrackDissolve_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDissolve.BP_DialogueTrackDissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0'\"",
      [81] = "      Begin Object Name=\"BPS_Dissolve_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0.BPS_Dissolve_C_0'\"",
      [82] = "         DissolveTime=2.000000",
      [83] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDissolve.BP_DialogueTrackDissolve_C'********:BP_DialogueTrackDissolve_C_0'\"",
      [84] = "         OwnedEpisodeID=1",
      [85] = "         Duration=2.000000",
      [86] = "      End Object",
      [87] = "      Begin Object Name=\"BPS_Dissolve_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDissolve_C_0.BPS_Dissolve_C_1'\"",
      [88] = "         DissolveTime=2.000000",
      [89] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDissolve.BP_DialogueTrackDissolve_C'********:BP_DialogueTrackDissolve_C_0'\"",
      [90] = "         OwnedEpisodeID=1",
      [91] = "         StartTime=4.000000",
      [92] = "         Duration=2.000000",
      [93] = "      End Object",
      [94] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dissolve.BPS_Dissolve_C'BPS_Dissolve_C_0'\"",
      [95] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [96] = "   End Object",
      [97] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [98] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [99] = "      FromTemplate=False",
      [100] = "   End Object",
      [101] = "   Begin Object Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [102] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [103] = "      FromTemplate=False",
      [104] = "   End Object",
      [105] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [106] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [107] = "      FromTemplate=False",
      [108] = "   End Object",
      [109] = "   Begin Object Name=\"DialogueStateControlTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_2'\"",
      [110] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [111] = "      FromTemplate=False",
      [112] = "   End Object",
      [113] = "   Begin Object Name=\"DialogueDialogueTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1'\"",
      [114] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [115] = "      FromTemplate=False",
      [116] = "   End Object",
      [117] = "   Begin Object Name=\"DialogueStateControlTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3'\"",
      [118] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [119] = "      FromTemplate=False",
      [120] = "   End Object",
      [121] = "   Begin Object Name=\"DialogueStateControlTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_4'\"",
      [122] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [123] = "      FromTemplate=False",
      [124] = "   End Object",
      [125] = "   Begin Object Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [126] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [127] = "      FromTemplate=False",
      [128] = "   End Object",
      [129] = "   Begin Object Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [130] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [131] = "      FromTemplate=False",
      [132] = "   End Object",
      [133] = "   Begin Object Name=\"DialogueStateControlTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6'\"",
      [134] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [135] = "      FromTemplate=False",
      [136] = "   End Object",
      [137] = "   Begin Object Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [138] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [139] = "      FromTemplate=False",
      [140] = "   End Object",
      [141] = "   Begin Object Name=\"DialogueStateControlTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_7'\"",
      [142] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [143] = "      FromTemplate=False",
      [144] = "   End Object",
      [145] = "   Begin Object Name=\"DialogueStateControlTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_8'\"",
      [146] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [147] = "      FromTemplate=False",
      [148] = "   End Object",
      [149] = "   Begin Object Name=\"DialogueDialogueTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_4'\"",
      [150] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [151] = "      FromTemplate=False",
      [152] = "   End Object",
      [153] = "   Begin Object Name=\"DialogueStateControlTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_9'\"",
      [154] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [155] = "      FromTemplate=False",
      [156] = "   End Object",
      [157] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [158] = "      Begin Object Name=\"EpisodeGraphNode_4\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_4'\"",
      [159] = "         EpisodeID=1",
      [160] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode'\"",
      [161] = "         NodePosX=300",
      [162] = "         NodeGuid=528BD22D412E4B58106307ABFE5547C4",
      [163] = "         CustomProperties Pin (PinId=36E2F3B440B98C7A6A5AFD8FD04C5316,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 D355014C4E4D369705EE4995EBE3EE37,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [164] = "      End Object",
      [165] = "      Begin Object Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [166] = "         EpisodeID=1",
      [167] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode'\"",
      [168] = "         NodePosX=300",
      [169] = "         NodeGuid=30DD28A14B1037EBEECC1CA12540F9C5",
      [170] = "         CustomProperties Pin (PinId=AB9A63944079F509B499D7953D572B2D,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [171] = "      End Object",
      [172] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [173] = "         NodeGuid=11ADC2334FCBE1930F0B098AD4A01E9C",
      [174] = "         CustomProperties Pin (PinId=D355014C4E4D369705EE4995EBE3EE37,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_4 36E2F3B440B98C7A6A5AFD8FD04C5316,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [175] = "      End Object",
      [176] = "      Begin Object Name=\"EpisodeGraphNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_1'\"",
      [177] = "         EpisodeID=1",
      [178] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode'\"",
      [179] = "         NodePosX=300",
      [180] = "         NodeGuid=DEEF640248E6F9B702B6F7B4955693FD",
      [181] = "         CustomProperties Pin (PinId=0A5E780945E4D82ADACC1CBF710D9962,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [182] = "      End Object",
      [183] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [184] = "         EpisodeID=1",
      [185] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode'\"",
      [186] = "         NodePosX=300",
      [187] = "         NodeGuid=2D06A72E4F7C7A05845920B050569D33",
      [188] = "         CustomProperties Pin (PinId=0DA1F3E6427F7D98B0CD91AFC4FE6707,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [189] = "      End Object",
      [190] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [191] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [192] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_4'\"",
      [193] = "      GraphGuid=AB9FA5DE4E858828D737B298D073BEFC",
      [194] = "   End Object",
      [195] = "   Begin Object Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [196] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [197] = "      FromTemplate=False",
      [198] = "      TrackName=\"Performer1\"",
      [199] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C'********:BP_DialogueTrackActorStill_C_0'\"",
      [200] = "   End Object",
      [201] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [202] = "      AppearanceID=7216035",
      [203] = "      IdleAnimLibAssetID=(AssetID=\"Directions_Loop\")",
      [204] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.939697,W=0.342009),Translation=(X=2095.164000,Y=-1980.001800,Z=-12.134200))",
      [205] = "      TrackName=\"Performer1\"",
      [206] = "   End Object",
      [207] = "   Begin Object Name=\"BP_DialogueTrackActorStill_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorStill_C_0'\"",
      [208] = "      Begin Object Name=\"BPS_ActorStill_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorStill_C_0.BPS_ActorStill_C_0'\"",
      [209] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C'********:BP_DialogueTrackActorStill_C_0'\"",
      [210] = "         OwnedEpisodeID=1",
      [211] = "         StartTime=1.000000",
      [212] = "      End Object",
      [213] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorStill.BPS_ActorStill_C'BPS_ActorStill_C_0'\"",
      [214] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [215] = "   End Object",
      [216] = "   Begin Object Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [217] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [218] = "      FromTemplate=False",
      [219] = "      TrackName=\"Performer1\"",
      [220] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorStill.BP_DialogueTrackActorStill_C'********:BP_DialogueTrackActorStill_C_0'\"",
      [221] = "   End Object",
      [222] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [223] = "      EpisodeID=1",
      [224] = "   End Object",
      [225] = "   HideNpcType=NewEnumerator0",
      [226] = "   PreLoadArray(0)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [227] = "   Unique=False",
      [228] = "   ActorInfos(0)=(PerformerName=\"Performer1\",AppearanceID=(ApperanceID=7216035),IdleAnimation=(AssetID=\"Directions_Loop\"))",
      [229] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode'\"",
      [230] = "   StoryLineID=********",
      [231] = "   Episodes(0)=(EpisodeID=1,Duration=301.000000,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_11'\",\"/Script/KGStoryLineEditor.DialoguePerformerTrack'DialoguePerformerTrack_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_5'\"))",
      [232] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [233] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [234] = "End Object",
    },
  },
}