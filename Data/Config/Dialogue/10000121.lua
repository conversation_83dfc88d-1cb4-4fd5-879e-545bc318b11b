return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 7209019,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "297393209",
  ["AnchorNpc"] = "Actor1",
  ["AnchorType"] = 2,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = true,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -88.003,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3512,
          ["X"] = -0.0588,
          ["Y"] = 0.0221,
          ["Z"] = -0.9342,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 218.8565,
          ["Y"] = -260.1759,
          ["Z"] = 189.4039,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "正视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
  },
  ["EnableDOF"] = false,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 4,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_1",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                    [1] = {
                      ["ActionSections"] = {
                        [1] = {
                          ["Active"] = true,
                          ["Duration"] = 0.0333,
                          ["DynamicFlag"] = "",
                          ["Enable"] = true,
                          ["FromLineIndex"] = -1,
                          ["LineUniqueIDLinked"] = {
                            ["A"] = 0,
                            ["B"] = 0,
                            ["C"] = 0,
                            ["D"] = 0,
                          },
                          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C",
                          ["ObjectName"] = "BPS_ParticleActive_C_0",
                          ["OwnedEpisodeID"] = 1,
                          ["SectionName"] = "Section",
                          ["StartTime"] = -0.0027,
                          ["bConstant"] = false,
                        },
                      },
                      ["Actions"] = {
                      },
                      ["Childs"] = {
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C",
                      ["ObjectName"] = "BP_DialogueTrackEffectActive_C_0",
                      ["Parent"] = "Effect1",
                      ["Priority"] = 0,
                      ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C",
                      ["TrackName"] = "Active",
                    },
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Effect1",
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C",
                  ["ObjectName"] = "BP_DialogueTrackEffect_C_0",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "Effect1",
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
              ["bAutoCameraTrack"] = false,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueTrackCamera_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 5,
              ["CameraName"] = "None",
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "正视Actor1",
              ["StartTime"] = 0,
              ["TargetCamera"] = "正视Actor1",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["ObjectName"] = "DialogueCameraCutAction_1",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [4] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C",
      ["AttachActor"] = "None",
      ["BoneName"] = "",
      ["Effect"] = "/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C",
      ["ObjectName"] = "BP_DialogueEntityEffect_C_0",
      ["Offset"] = {
        ["X"] = 0,
        ["Y"] = 0,
        ["Z"] = 0,
      },
      ["Parent"] = "Actor1",
      ["Rotation"] = {
        ["Pitch"] = 0,
        ["Roll"] = 0,
        ["Yaw"] = 0,
      },
      ["Scale"] = {
        ["X"] = 1,
        ["Y"] = 1,
        ["Z"] = 1,
      },
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7661,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.6428,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -162.9294,
          ["Y"] = -48.4987,
          ["Z"] = -227.4352,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Effect1",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7209019,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Boss/BossMoon/A_Boss_Moon_Idle.A_Boss_Moon_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9397,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.342,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 88,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [3] = "/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head",
    [4] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [5] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [6] = "      End Object",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C Name=\"BP_DialogueTrackEffectActive_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffectActive_C_0'\"",
      [23] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C Name=\"BPS_ParticleActive_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffectActive_C_0.BPS_ParticleActive_C_0'\"",
      [24] = "      End Object",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C Name=\"BP_DialogueTrackEffect_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffect_C_0'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_0'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C Name=\"BP_DialogueEntityEffect_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C'/Temp/DialogueTransientPackage.********:BP_DialogueEntityEffect_C_0'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_1'\"",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraCutTrack Name=\"DialogueCameraCutAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_1'\"",
      [43] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_1.BPS_CameraCut_C_0'\"",
      [44] = "      End Object",
      [45] = "   End Object",
      [46] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [47] = "   End Object",
      [48] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlAction_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_3'\"",
      [49] = "   End Object",
      [50] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [51] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [59] = "   End Object",
      [60] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [61] = "   End Object",
      [62] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [63] = "   End Object",
      [64] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [65] = "      EpisodeID=1",
      [66] = "   End Object",
      [67] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [68] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [69] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [70] = "         FromLineIndex=0",
      [71] = "         LineGUIDLinked=961289483",
      [72] = "         OwnedEpisodeID=1",
      [73] = "         StartTime=1.400000",
      [74] = "      End Object",
      [75] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [76] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [77] = "      FromTemplate=False",
      [78] = "   End Object",
      [79] = "   Begin Object Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [80] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [81] = "      FromTemplate=False",
      [82] = "   End Object",
      [83] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [84] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [85] = "      FromTemplate=False",
      [86] = "   End Object",
      [87] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [88] = "      EpisodeID=1",
      [89] = "      ContentIndex=1",
      [90] = "   End Object",
      [91] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [92] = "      GUID=961289483",
      [93] = "      Duration=1.500000",
      [94] = "      Talker=(PerformerName=\"Actor1\")",
      [95] = "   End Object",
      [96] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [97] = "      EpisodeID=1",
      [98] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [99] = "   End Object",
      [100] = "   Begin Object Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [101] = "      EpisodeID=1",
      [102] = "      ContentIndex=1",
      [103] = "   End Object",
      [104] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [105] = "      GUID=3107163899",
      [106] = "      Duration=4.000000",
      [107] = "      Talker=(PerformerName=\"Actor1\")",
      [108] = "   End Object",
      [109] = "   Begin Object Name=\"BP_DialogueTrackEffectActive_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffectActive_C_0'\"",
      [110] = "      Begin Object Name=\"BPS_ParticleActive_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffectActive_C_0.BPS_ParticleActive_C_0'\"",
      [111] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C'********:BP_DialogueTrackEffectActive_C_0'\"",
      [112] = "         OwnedEpisodeID=1",
      [113] = "         StartTime=-0.002662",
      [114] = "      End Object",
      [115] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C'BPS_ParticleActive_C_0'\"",
      [116] = "      Parent=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C'********:BP_DialogueTrackEffect_C_0'\"",
      [117] = "   End Object",
      [118] = "   Begin Object Name=\"BP_DialogueTrackEffect_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackEffect_C_0'\"",
      [119] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C'********:BP_DialogueEntityEffect_C_0'\"",
      [120] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_0'\"",
      [121] = "      FromTemplate=False",
      [122] = "      TrackName=\"Effect1\"",
      [123] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C'********:BP_DialogueTrackEffectActive_C_0'\"",
      [124] = "   End Object",
      [125] = "   Begin Object Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [126] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [127] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [128] = "      TrackName=\"正视Actor1\"",
      [129] = "   End Object",
      [130] = "   Begin Object Name=\"DialogueTrackActor_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_0'\"",
      [131] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [132] = "      Childs(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C'********:BP_DialogueTrackEffect_C_0'\"",
      [133] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [134] = "      TrackName=\"Actor1\"",
      [135] = "   End Object",
      [136] = "   Begin Object Name=\"BP_DialogueEntityEffect_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C'/Temp/DialogueTransientPackage.********:BP_DialogueEntityEffect_C_0'\"",
      [137] = "      Effect=\"/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head\"",
      [138] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.642768,W=0.766061),Translation=(X=-162.929433,Y=-48.498720,Z=-227.435200))",
      [139] = "      TrackName=\"Effect1\"",
      [140] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [143] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [144] = "      SpawnTransform=(Rotation=(X=-0.058780,Y=0.022126,Z=-0.934203,W=-0.351169),Translation=(X=218.856481,Y=-260.175928,Z=189.403943))",
      [145] = "      TrackName=\"正视Actor1\"",
      [146] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [147] = "   End Object",
      [148] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [149] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [150] = "      SpawnTransform=(Translation=(X=0.000000,Y=0.000000,Z=-88.003000))",
      [151] = "      TrackName=\"锚点\"",
      [152] = "   End Object",
      [153] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [154] = "      AppearanceID=7209019",
      [155] = "      IdleAnimation=\"/Game/Arts/Character/Animation/Boss/BossMoon/A_Boss_Moon_Idle.A_Boss_Moon_Idle\"",
      [156] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [157] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.342020,W=0.939693),Translation=(X=0.000000,Y=0.000000,Z=88.000000))",
      [158] = "      TrackName=\"Actor1\"",
      [159] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [160] = "   End Object",
      [161] = "   Begin Object Name=\"DialogueDialogueAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_1'\"",
      [162] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [163] = "      FromTemplate=False",
      [164] = "   End Object",
      [165] = "   Begin Object Name=\"DialogueCameraCutAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_1'\"",
      [166] = "      Begin Object Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_1.BPS_CameraCut_C_0'\"",
      [167] = "         CameraBreathType=NewEnumerator2",
      [168] = "         BreathSpeed=10.000000",
      [169] = "         TargetCamera=(CameraName=\"正视Actor1\")",
      [170] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_1'\"",
      [171] = "         SectionName=NSLOCTEXT(\"\", \"147F48124B28D7C2C60621AF5DC3233C\", \"正视Actor1\")",
      [172] = "         FromLineIndex=0",
      [173] = "         LineGUIDLinked=1795246954",
      [174] = "         OwnedEpisodeID=1",
      [175] = "         Duration=4.000000",
      [176] = "      End Object",
      [177] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_0'\"",
      [178] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'\"",
      [179] = "      FromTemplate=False",
      [180] = "   End Object",
      [181] = "   Begin Object Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [182] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [183] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_0'\"",
      [184] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_1'\"",
      [185] = "      TrackName=\"锚点\"",
      [186] = "   End Object",
      [187] = "   Begin Object Name=\"DialogueStateControlAction_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_3'\"",
      [188] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [189] = "      FromTemplate=False",
      [190] = "   End Object",
      [191] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [192] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [193] = "         EpisodeID=1",
      [194] = "         NodePosX=300",
      [195] = "         NodeGuid=B9C779604CC7CE34DE865894E169342B",
      [196] = "         CustomProperties Pin (PinId=6EE491A443BF1A292F9CA38ABDBA42EC,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 A1C980094E99EAA66E38EB9684D7F1BB,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [197] = "      End Object",
      [198] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [199] = "         NodeGuid=ACEF537F45052CA6CBDF329C558294C0",
      [200] = "         CustomProperties Pin (PinId=A1C980094E99EAA66E38EB9684D7F1BB,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_3 6EE491A443BF1A292F9CA38ABDBA42EC,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [201] = "      End Object",
      [202] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [203] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [204] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_3'\"",
      [205] = "      GraphGuid=8205BBB746EED609D495A39CB6669C27",
      [206] = "   End Object",
      [207] = "   Begin Object Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [208] = "      EpisodeID=1",
      [209] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [210] = "   End Object",
      [211] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [212] = "      EpisodeID=1",
      [213] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [214] = "   End Object",
      [215] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [216] = "      EpisodeID=1",
      [217] = "      ContentIndex=1",
      [218] = "      UniqueID=BB6FAFB9A533495F96C6DF9FA824A965",
      [219] = "      Duration=1.500000",
      [220] = "      Talker=(PerformerName=\"Actor1\")",
      [221] = "   End Object",
      [222] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [223] = "   End Object",
      [224] = "   EnableDOF=False",
      [225] = "   AnchorType=NewEnumerator1",
      [226] = "   AnchorID=\"297393209\"",
      [227] = "   AnchorNpc=(PerformerName=\"Actor1\")",
      [228] = "   BlendOutCamera=True",
      [229] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C\"",
      [230] = "   PreLoadArray(1)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [231] = "   PreLoadArray(2)=\"/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head\"",
      [232] = "   PreLoadArray(3)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [233] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=7209019),IdleAnimation=(AssetID=\"Idle\"))",
      [234] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue'\"",
      [235] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_1'\"",
      [236] = "   StoryLineID=********",
      [237] = "   Episodes(0)=(EpisodeID=1,Duration=4.000000,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_1'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueTrackCamera_0'\",\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'DialogueCameraCutAction_1'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_0'\"),Name=\"Episode:0\")",
      [238] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [239] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [240] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [241] = "   NewEntityList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C'BP_DialogueEntityEffect_C_0'\"",
      [242] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [243] = "End Object",
    },
  },
}