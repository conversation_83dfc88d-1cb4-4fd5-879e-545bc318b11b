return {
  ["AnchorID"] = "297393209",
  ["AnchorNpc"] = "Actor1",
  ["AnchorType"] = 2,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = true,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -88.003,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3512,
          ["X"] = -0.0588,
          ["Y"] = 0.0221,
          ["Z"] = -0.9342,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 218.8565,
          ["Y"] = -260.1759,
          ["Z"] = 189.4039,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "正视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
  },
  ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue",
  ["EnableDOF"] = false,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 4,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 961289483,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1150308423,
                ["B"] = -1523365537,
                ["C"] = -1765351521,
                ["D"] = -1473992347,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                    [1] = {
                      ["ActionSections"] = {
                        [1] = {
                          ["Active"] = true,
                          ["Duration"] = 0.0333,
                          ["DynamicFlag"] = "",
                          ["Enable"] = true,
                          ["FromLineIndex"] = -1,
                          ["LineGUIDLinked"] = 0,
                          ["LineUniqueIDLinked"] = {
                            ["A"] = 0,
                            ["B"] = 0,
                            ["C"] = 0,
                            ["D"] = 0,
                          },
                          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C",
                          ["OwnedEpisodeID"] = 1,
                          ["SectionName"] = "Section",
                          ["StartTime"] = -0.0027,
                          ["bConstant"] = false,
                        },
                      },
                      ["Actions"] = {
                      },
                      ["Childs"] = {
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffectActive.BP_DialogueTrackEffectActive_C",
                      ["Parent"] = "Effect1",
                      ["Priority"] = 0,
                      ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ParticleActive.BPS_ParticleActive_C",
                      ["TrackName"] = "Active",
                    },
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Effect1",
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackEffect.BP_DialogueTrackEffect_C",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "Effect1",
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
              ["bAutoCameraTrack"] = false,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 5,
              ["CameraName"] = "None",
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1795246954,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1150308423,
                ["B"] = -1523365537,
                ["C"] = -1765351521,
                ["D"] = -1473992347,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "正视Actor1",
              ["StartTime"] = 0,
              ["TargetCamera"] = "正视Actor1",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [4] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C",
      ["AttachActor"] = "None",
      ["BoneName"] = "",
      ["Effect"] = "/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityEffect.BP_DialogueEntityEffect_C",
      ["Offset"] = {
        ["X"] = 0,
        ["Y"] = 0,
        ["Z"] = 0,
      },
      ["Parent"] = "Actor1",
      ["Rotation"] = {
        ["Pitch"] = 0,
        ["Roll"] = 0,
        ["Yaw"] = 0,
      },
      ["Scale"] = {
        ["X"] = 1,
        ["Y"] = 1,
        ["Z"] = 1,
      },
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7661,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.6428,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -162.9294,
          ["Y"] = -48.4987,
          ["Z"] = -227.4352,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Effect1",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7209019,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Boss/BossMoon/A_Boss_Moon_Idle.A_Boss_Moon_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9397,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.342,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 88,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorEffect.BP_DialogueSceneActorEffect_C",
    [3] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [4] = "/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_head.NS_MoonMec02_head",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["StoryLineID"] = ********,
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnly"] = {
  },
}