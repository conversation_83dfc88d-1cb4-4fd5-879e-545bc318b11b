return {
  ["AnchorID"] = "294742671",
  ["AnchorNpc"] = "Actor2",
  ["AnchorType"] = 2,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9245,
          ["X"] = -0.0718,
          ["Y"] = 0.2185,
          ["Z"] = 0.3039,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -181.0346,
          ["Y"] = -98.6123,
          ["Z"] = 209.8906,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8692,
          ["X"] = -0.0129,
          ["Y"] = 0.0228,
          ["Z"] = 0.4938,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -182.2025,
          ["Y"] = -380.6395,
          ["Z"] = 128.8484,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4552,
          ["X"] = -0.0342,
          ["Y"] = 0.0175,
          ["Z"] = 0.8896,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 338.3511,
          ["Y"] = -380.7229,
          ["Z"] = 173.8493,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 5,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 554.1373,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0085,
          ["X"] = -0.1976,
          ["Y"] = -0.0017,
          ["Z"] = 0.9802,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 402.1264,
          ["Y"] = 3.4191,
          ["Z"] = 317.1879,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.413,
          ["X"] = 0.0143,
          ["Y"] = 0.0065,
          ["Z"] = -0.9106,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 896.4804,
          ["Y"] = 962.637,
          ["Z"] = 180.8768,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8736,
          ["X"] = 0.0085,
          ["Y"] = 0.0152,
          ["Z"] = -0.4863,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -217.3679,
          ["Y"] = 438.3493,
          ["Z"] = 131.6672,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7356,
          ["X"] = 0.0248,
          ["Y"] = 0.027,
          ["Z"] = -0.6764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 22.2633,
          ["Y"] = 417.7056,
          ["Z"] = 163.0909,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7424,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 15.7478,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1872,
          ["X"] = -0.0446,
          ["Y"] = 0.0085,
          ["Z"] = 0.9813,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 174.6057,
          ["Y"] = -50.4868,
          ["Z"] = 85.6869,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 124.7191,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 4.8553,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2871,
          ["X"] = -0.0384,
          ["Y"] = 0.0115,
          ["Z"] = 0.9571,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 200.8981,
          ["Y"] = -122.1146,
          ["Z"] = 87.1559,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 20,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 126.389,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -0.4907,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2957,
          ["X"] = -0.0084,
          ["Y"] = -0.0026,
          ["Z"] = 0.9552,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 106.9239,
          ["Y"] = 63.7312,
          ["Z"] = 81.838,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 228.636,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -5.2407,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3648,
          ["X"] = -0.013,
          ["Y"] = 0.0051,
          ["Z"] = 0.931,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 282.6801,
          ["Y"] = -200.0197,
          ["Z"] = 64.6984,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 198.2129,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 11.9924,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2093,
          ["X"] = -0.0512,
          ["Y"] = -0.011,
          ["Z"] = 0.9764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 185.7302,
          ["Y"] = 59.4629,
          ["Z"] = 81.8373,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 155.931,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 4.7219,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1097,
          ["X"] = 0.0035,
          ["Y"] = 0.0004,
          ["Z"] = -0.994,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 99.4334,
          ["Y"] = 22.5008,
          ["Z"] = 74.5134,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 99.1662,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 3.5064,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4242,
          ["X"] = 0.0047,
          ["Y"] = 0.0022,
          ["Z"] = -0.9056,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 62.7677,
          ["Y"] = 76.232,
          ["Z"] = 73.2979,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 413.3839,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -9.7787,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3404,
          ["X"] = 0.0147,
          ["Y"] = 0.0054,
          ["Z"] = -0.9402,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 330.8403,
          ["Y"] = 236.3403,
          ["Z"] = 60.0128,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7757,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -14.1812,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1976,
          ["X"] = -0.012,
          ["Y"] = -0.0024,
          ["Z"] = -0.9802,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 176.8058,
          ["Y"] = 52.9797,
          ["Z"] = 68.8251,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [18] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 111.2802,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0.062,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3519,
          ["X"] = -0.0098,
          ["Y"] = -0.0037,
          ["Z"] = -0.936,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 86.3025,
          ["Y"] = 68.0752,
          ["Z"] = 83.0849,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [19] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 107.9293,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 6.077,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0697,
          ["X"] = 0.0226,
          ["Y"] = 0.0016,
          ["Z"] = -0.9973,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.751,
          ["Y"] = 12.2958,
          ["Z"] = 89.0811,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [20] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 247.0631,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -12.5409,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3071,
          ["X"] = 0.0366,
          ["Y"] = 0.0118,
          ["Z"] = -0.9509,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 215.8233,
          ["Y"] = 117.9775,
          ["Z"] = 70.4883,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [21] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 172.1966,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -8.1054,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1668,
          ["X"] = -0.0086,
          ["Y"] = 0.0015,
          ["Z"] = -0.986,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 168.2474,
          ["Y"] = -34.3429,
          ["Z"] = 74.3048,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [22] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 103.9644,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 2.9312,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3971,
          ["X"] = -0.0017,
          ["Y"] = 0.0007,
          ["Z"] = -0.9178,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 75.0018,
          ["Y"] = -71.3756,
          ["Z"] = 86.2462,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [23] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 106.734,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 6.0496,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1719,
          ["X"] = 0.0155,
          ["Y"] = -0.0027,
          ["Z"] = -0.985,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.4203,
          ["Y"] = -31.6774,
          ["Z"] = 88.4598,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [24] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 272.3009,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -12.4972,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3501,
          ["X"] = 0.0294,
          ["Y"] = -0.011,
          ["Z"] = -0.9362,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 230.6554,
          ["Y"] = -143.7674,
          ["Z"] = 69.913,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [25] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 148.4824,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2738,
          ["X"] = 0.0353,
          ["Y"] = -0.01,
          ["Z"] = 0.9611,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -22.5578,
          ["Y"] = -73.3576,
          ["Z"] = 149.495,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [26] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 155.8896,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1652,
          ["X"] = -0.1372,
          ["Y"] = 0.0232,
          ["Z"] = 0.9764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 21.4947,
          ["Y"] = -50.6792,
          ["Z"] = 218.979,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [27] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 287.0948,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9718,
          ["X"] = 0.017,
          ["Y"] = -0.0739,
          ["Z"] = 0.2235,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -280.4891,
          ["Y"] = -99.2269,
          ["Z"] = 112.732,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [28] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9392,
          ["X"] = -0.0571,
          ["Y"] = 0.1928,
          ["Z"] = 0.2782,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1153.5493,
          ["Y"] = -918.0177,
          ["Z"] = 733.3432,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [29] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 189.3741,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9922,
          ["X"] = -0.0075,
          ["Y"] = 0.0746,
          ["Z"] = 0.0999,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -181.7058,
          ["Y"] = -32.5547,
          ["Z"] = 195.3461,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [30] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 257.6461,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 150,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1525,
          ["X"] = -0.0713,
          ["Y"] = 0.011,
          ["Z"] = 0.9857,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 110.6417,
          ["Y"] = -58.7044,
          ["Z"] = 194.9818,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/TwoPeopleDialogue.TwoPeopleDialogue",
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 15,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
        [1] = {
          ["Condition"] = "",
          ["CustomContent"] = "",
          ["DialogueID"] = 0,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "OptionString unset!!!!!",
          ["EpisodeID"] = 2,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 0,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "",
            ["bClose"] = false,
          },
        },
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
              },
              ["LookAtTalker"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor1",
                  ["Target"] = "笔记",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.75,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalMan",
                        ["AssetID"] = "ShowItem",
                        ["StateName"] = "Start",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3.5333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.5,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalMan",
                        ["AssetID"] = "ShowItem",
                        ["StateName"] = "End",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3.5333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 10.75,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [17] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 3.25,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 10.75,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["Parent"] = "笔记",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "笔记",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["Parent"] = "锚点",
              ["TrackName"] = "笔记",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 729627194,
                ["B"] = -342339876,
                ["C"] = -1781308098,
                ["D"] = 631866955,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2062882738,
                ["B"] = 1169179041,
                ["C"] = -1874973932,
                ["D"] = -2041635870,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3.15,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1775584926,
                ["B"] = -1728756266,
                ["C"] = -2079266281,
                ["D"] = -1917189467,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.65,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [4] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1230746161,
                ["B"] = -407812844,
                ["C"] = -1642106115,
                ["D"] = 2135826391,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 10.65,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [5] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2086058743,
                ["B"] = 421479500,
                ["C"] = -1577415543,
                ["D"] = 455354022,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12.775,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [6] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -310543418,
                ["B"] = -730904751,
                ["C"] = -1681995027,
                ["D"] = -1439423319,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 14.9,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 1,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3.25,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 4,
              ["Duration"] = 3.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 729627194,
                ["B"] = -342339876,
                ["C"] = -1781308098,
                ["D"] = 631866955,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1-近景",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "平视",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 1,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 5,
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3.25,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "远景",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1230746161,
                ["B"] = -407812844,
                ["C"] = -1642106115,
                ["D"] = 2135826391,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 7.75,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2086058743,
                ["B"] = 421479500,
                ["C"] = -1577415543,
                ["D"] = 455354022,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 10.75,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-近景",
              ["bConstant"] = false,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.125,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -310543418,
                ["B"] = -730904751,
                ["C"] = -1681995027,
                ["D"] = -1439423319,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 12.875,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-近景",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 729627194,
                ["B"] = -342339876,
                ["C"] = -1781308098,
                ["D"] = 631866955,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "队长，大家都没事。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 1.75,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2062882738,
                ["B"] = 1169179041,
                ["C"] = -1874973932,
                ["D"] = -2041635870,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "敌人都已确认死亡，而这个……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 1.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1691147862,
                ["B"] = 2040480517,
                ["C"] = -1247556505,
                ["D"] = -1884904425,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = false,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 3.25,
              ["SubTitle"] = "",
              ["Talker"] = "none",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1775584926,
                ["B"] = -1728756266,
                ["C"] = -2079266281,
                ["D"] = -1917189467,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "就是一切的源头——安提哥努斯家族笔记。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1230746161,
                ["B"] = -407812844,
                ["C"] = -1642106115,
                ["D"] = 2135826391,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 7.75,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 2.125,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2086058743,
                ["B"] = 421479500,
                ["C"] = -1577415543,
                ["D"] = 455354022,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "不用害怕，一切都结束了，我们会把它好好封印起来。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 10.75,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 2.125,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -310543418,
                ["B"] = -730904751,
                ["C"] = -1681995027,
                ["D"] = -1439423319,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "笔记的事件和影响，到这里就结束了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 12.875,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
    [2] = {
      ["Duration"] = 1.5,
      ["EpisodeID"] = 2,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
        [1] = {
          ["Condition"] = "0",
          ["CustomContent"] = "",
          ["DialogueID"] = 50010303,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "瑞尔·比伯",
          ["EpisodeID"] = 3,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 50010303,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "瑞尔·比伯",
            ["bClose"] = false,
          },
        },
        [2] = {
          ["Condition"] = "0",
          ["CustomContent"] = "",
          ["DialogueID"] = 50010304,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "小丑",
          ["EpisodeID"] = 4,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 50010304,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "小丑",
            ["bClose"] = false,
          },
        },
        [3] = {
          ["Condition"] = "0",
          ["CustomContent"] = "",
          ["DialogueID"] = 50010305,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "爱丽丝",
          ["EpisodeID"] = 5,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 50010305,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "爱丽丝",
            ["bClose"] = false,
          },
        },
        [4] = {
          ["Condition"] = "Pre:50010303,50010304,50010305",
          ["CustomContent"] = "",
          ["DialogueID"] = 50010382,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "已经没有了",
          ["EpisodeID"] = 2,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 50010382,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "已经没有了",
            ["bClose"] = true,
          },
        },
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
              },
              ["LookAtTalker"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 2,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "笔记",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["Parent"] = "锚点",
              ["TrackName"] = "笔记",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1258846280,
                ["B"] = -1295169048,
                ["C"] = -1970158814,
                ["D"] = 1187979185,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 2,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1258846280,
                ["B"] = -1295169048,
                ["C"] = -1970158814,
                ["D"] = 1187979185,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 2,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-中景",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 2,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1258846280,
                ["B"] = -1295169048,
                ["C"] = -1970158814,
                ["D"] = 1187979185,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 2,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "你还有什么想问的吗？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
    [3] = {
      ["Duration"] = 13.625,
      ["EpisodeID"] = 3,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
        [1] = {
          ["Condition"] = "",
          ["CustomContent"] = "",
          ["DialogueID"] = 0,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "OptionString unset!!!!!",
          ["EpisodeID"] = 2,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 0,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "",
            ["bClose"] = false,
          },
        },
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
              },
              ["LookAtTalker"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [17] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 3,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["Parent"] = "笔记",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "笔记",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["Parent"] = "锚点",
              ["TrackName"] = "笔记",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1892788946,
                ["B"] = 1582646123,
                ["C"] = -1785787379,
                ["D"] = -331649165,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.9,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1472801209,
                ["B"] = -1585691170,
                ["C"] = -2098547162,
                ["D"] = -461298098,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Section",
              ["StartTime"] = 8.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1840456178,
                ["B"] = -256032358,
                ["C"] = -1222348205,
                ["D"] = 1950451667,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Section",
              ["StartTime"] = 13.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1892788946,
                ["B"] = 1582646123,
                ["C"] = -1785787379,
                ["D"] = -331649165,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-近景",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3.625,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1472801209,
                ["B"] = -1585691170,
                ["C"] = -2098547162,
                ["D"] = -461298098,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 1,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 5,
              ["CameraBreathType"] = 4,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1840456178,
                ["B"] = -256032358,
                ["C"] = -1222348205,
                ["D"] = 1950451667,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 3,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 8.625,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 3,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1892788946,
                ["B"] = 1582646123,
                ["C"] = -1785787379,
                ["D"] = -331649165,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 3,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "他应该是在用古老的仪式吸收笔记的力量，就像我们直接服用高序列的魔药一样，这充满危险、也需要时间，他需要呆在一个安全的地方。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 3.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 3,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1472801209,
                ["B"] = -1585691170,
                ["C"] = -2098547162,
                ["D"] = -461298098,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 3,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "他的未婚妻告诉我，工厂区是他曾经跟朋友们一起工作的地方……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 3,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1840456178,
                ["B"] = -256032358,
                ["C"] = -1222348205,
                ["D"] = 1950451667,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 3,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "在非凡力量的影响下，人的思维是疯狂和混乱的，但他可能潜意识认为，这里是最安全的地方。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
    [4] = {
      ["Duration"] = 10.125,
      ["EpisodeID"] = 4,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
        [1] = {
          ["Condition"] = "",
          ["CustomContent"] = "",
          ["DialogueID"] = 0,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "OptionString unset!!!!!",
          ["EpisodeID"] = 2,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 0,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "",
            ["bClose"] = false,
          },
        },
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
              },
              ["LookAtTalker"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "笔记",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["Parent"] = "锚点",
              ["TrackName"] = "笔记",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238098254,
                ["B"] = -1514913069,
                ["C"] = -1417247125,
                ["D"] = 1742147300,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1781705500,
                ["B"] = 625100852,
                ["C"] = -1971782099,
                ["D"] = 902088989,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Section",
              ["StartTime"] = 8.275,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1580075549,
                ["B"] = -1177403044,
                ["C"] = -1619794501,
                ["D"] = 1331652296,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Section",
              ["StartTime"] = 10.025,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238098254,
                ["B"] = -1514913069,
                ["C"] = -1417247125,
                ["D"] = 1742147300,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-近景",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3.875,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1781705500,
                ["B"] = 625100852,
                ["C"] = -1971782099,
                ["D"] = 902088989,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 4.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.75,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1580075549,
                ["B"] = -1177403044,
                ["C"] = -1619794501,
                ["D"] = 1331652296,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 4,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 8.375,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 4.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 4,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238098254,
                ["B"] = -1514913069,
                ["C"] = -1417247125,
                ["D"] = 1742147300,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 4,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "小丑的尸体让弗莱做后续处理，看能不能辨认出他原本的长相，找到有关的线索。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 3.875,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 4,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1781705500,
                ["B"] = 625100852,
                ["C"] = -1971782099,
                ["D"] = 902088989,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 4,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "笔记已经被我们拿到了，密修会之后不会再有跟踪和伤害你的行为了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 4.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 1.75,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 4,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1580075549,
                ["B"] = -1177403044,
                ["C"] = -1619794501,
                ["D"] = 1331652296,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 4,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "克莱恩，你可以好好睡一觉了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8.375,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
    [5] = {
      ["Duration"] = 35.125,
      ["EpisodeID"] = 5,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
        [1] = {
          ["Condition"] = "",
          ["CustomContent"] = "",
          ["DialogueID"] = 0,
          ["DialogueLineIndex"] = 0,
          ["DialogueText"] = "OptionString unset!!!!!",
          ["EpisodeID"] = 2,
          ["Item"] = {
            ["AssetName"] = "",
            ["ExtraAction"] = {
            },
            ["ID"] = 0,
            ["Icon"] = {
              ["Path"] = {
                ["AssetPath"] = {
                  ["AssetName"] = "None",
                  ["PackageName"] = "None",
                },
                ["SubPathString"] = "",
              },
            },
            ["LockText"] = "",
            ["LockVisible"] = false,
            ["ReportServer"] = false,
            ["SelectHide"] = false,
            ["Text"] = "",
            ["bClose"] = false,
          },
        },
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
              },
              ["LookAtTalker"] = true,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "笔记",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["Parent"] = "锚点",
              ["TrackName"] = "笔记",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 402847157,
                ["B"] = -957593369,
                ["C"] = -1584425899,
                ["D"] = -1847762293,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 1.775,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -201907277,
                ["B"] = 350766953,
                ["C"] = -1669734757,
                ["D"] = 1522701253,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.15,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238865246,
                ["B"] = -1309454820,
                ["C"] = -2134414518,
                ["D"] = -905406715,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 8.15,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [4] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1523377056,
                ["B"] = 796738185,
                ["C"] = -1373487708,
                ["D"] = 666634622,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [5] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -648196466,
                ["B"] = -1358085801,
                ["C"] = -1516750784,
                ["D"] = -152790261,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 15.775,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [6] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -748576096,
                ["B"] = -1124318858,
                ["C"] = -1296816516,
                ["D"] = -120162653,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 17.65,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [7] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1105062146,
                ["B"] = -583446969,
                ["C"] = -1631699052,
                ["D"] = -419009508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 22.275,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [8] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -978630644,
                ["B"] = 167333823,
                ["C"] = -1644042793,
                ["D"] = -135092020,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 24.525,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [9] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 8,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2026554507,
                ["B"] = 1616333899,
                ["C"] = -1727943714,
                ["D"] = -2083222284,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 29.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [10] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 9,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1712960259,
                ["B"] = -777698818,
                ["C"] = -1215854699,
                ["D"] = -2006007675,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 30.9,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [11] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 10,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -139039916,
                ["B"] = 1096630603,
                ["C"] = -1273287593,
                ["D"] = -1600425770,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Section",
              ["StartTime"] = 35.025,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.875,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 402847157,
                ["B"] = -957593369,
                ["C"] = -1584425899,
                ["D"] = -1847762293,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor1-近景",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-近景",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.375,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -201907277,
                ["B"] = 350766953,
                ["C"] = -1669734757,
                ["D"] = 1522701253,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 1.875,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238865246,
                ["B"] = -1309454820,
                ["C"] = -2134414518,
                ["D"] = -905406715,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 4.25,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3.25,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1523377056,
                ["B"] = 796738185,
                ["C"] = -1373487708,
                ["D"] = 666634622,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 8.25,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-中景",
              ["bConstant"] = false,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4.375,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -648196466,
                ["B"] = -1358085801,
                ["C"] = -1516750784,
                ["D"] = -152790261,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 11.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-近景",
              ["bConstant"] = false,
            },
            [6] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4.625,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1105062146,
                ["B"] = -583446969,
                ["C"] = -1631699052,
                ["D"] = -419009508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 17.75,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 2.25,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -978630644,
                ["B"] = 167333823,
                ["C"] = -1644042793,
                ["D"] = -135092020,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 22.375,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [8] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4.875,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2026554507,
                ["B"] = 1616333899,
                ["C"] = -1727943714,
                ["D"] = -2083222284,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 24.625,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-中景",
              ["bConstant"] = false,
            },
            [9] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1712960259,
                ["B"] = -777698818,
                ["C"] = -1215854699,
                ["D"] = -2006007675,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor1-近景",
              ["StartTime"] = 29.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-近景",
              ["bConstant"] = false,
            },
            [10] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 4.125,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -139039916,
                ["B"] = 1096630603,
                ["C"] = -1273287593,
                ["D"] = -1600425770,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["OwnedEpisodeID"] = 5,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 31,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.875,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 402847157,
                ["B"] = -957593369,
                ["C"] = -1584425899,
                ["D"] = -1847762293,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我们要怎么跟爱丽丝解释这些……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 2.375,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -201907277,
                ["B"] = 350766953,
                ["C"] = -1669734757,
                ["D"] = 1522701253,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "你提起过，爱丽丝是瑞尔·比伯的未婚妻？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 1.875,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 238865246,
                ["B"] = -1309454820,
                ["C"] = -2134414518,
                ["D"] = -905406715,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "他们订了婚，想一起开一家珠宝店，瑞尔·比伯的未婚妻还在等他回家。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 4.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 3.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1523377056,
                ["B"] = 796738185,
                ["C"] = -1373487708,
                ["D"] = 666634622,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "非凡事件就是这样，危险和疯狂就隐藏在日常生活的背后。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 4.375,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -648196466,
                ["B"] = -1358085801,
                ["C"] = -1516750784,
                ["D"] = -152790261,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "瑞尔·比伯只是个普通人，但在非凡力量的诱惑之下，他的理智完全被吞噬了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 11.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 1.875,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -748576096,
                ["B"] = -1124318858,
                ["C"] = -1296816516,
                ["D"] = -120162653,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "那不是一般人类可以抵抗的诱惑。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 15.875,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 4.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1105062146,
                ["B"] = -583446969,
                ["C"] = -1631699052,
                ["D"] = -419009508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "瑞尔·比伯他们被非凡事件牵扯，死于诡异和混乱。韦尔奇和娜娅也是这样死的……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 17.75,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [8] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 8,
              ["ContentUI"] = 0,
              ["Duration"] = 2.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -978630644,
                ["B"] = 167333823,
                ["C"] = -1644042793,
                ["D"] = -135092020,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "其实我也一样，只是我比较幸运罢了……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 22.375,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [9] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 9,
              ["ContentUI"] = 0,
              ["Duration"] = 4.875,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 8,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 2026554507,
                ["B"] = 1616333899,
                ["C"] = -1727943714,
                ["D"] = -2083222284,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "值夜者就是在这样的事件中，一次又一次尽力保护着廷根。对抗敌人，也对抗我们自己。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 24.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [10] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 10,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 9,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1712960259,
                ["B"] = -777698818,
                ["C"] = -1215854699,
                ["D"] = -2006007675,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "……我明白了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 29.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [11] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 11,
              ["ContentUI"] = 0,
              ["Duration"] = 4.125,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 5,
              ["FromLineIndex"] = 10,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -139039916,
                ["B"] = 1096630603,
                ["C"] = -1273287593,
                ["D"] = -1600425770,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 5,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我们会处理事件的后续、通知相关人员的，以一种死者家属能接受的方式。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 31,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 3,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8676,
          ["X"] = -0.4563,
          ["Y"] = 0.1287,
          ["Z"] = 0.1501,
        },
        ["Scale3D"] = {
          ["X"] = 0.7,
          ["Y"] = 0.7,
          ["Z"] = 0.7,
        },
        ["Translation"] = {
          ["X"] = -80,
          ["Y"] = -30,
          ["Z"] = 150,
        },
      },
      ["StaticMesh"] = "/Game/Arts/Weapon/Monster/AntignusNote/SM_AntignusNote_001_Close.SM_AntignusNote_001_Close",
      ["StickGround"] = false,
      ["TrackName"] = "笔记",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "安提哥努斯家族笔记",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7255040,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "NormalMan",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0872,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -20,
          ["Y"] = 0,
          ["Z"] = 91.9,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7255039,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "NormalMan",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -151.5567,
          ["Y"] = 0,
          ["Z"] = 91.9,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7255045,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "NormalMan",
        ["AssetID"] = "Akimbo_Loop02",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9063,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.4226,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -120,
          ["Y"] = 140,
          ["Z"] = 79.9002,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Performer1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [3] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["StoryLineID"] = ********,
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnly"] = {
  },
}