return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 7255045,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["AppearanceID"] = 7255039,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "339025826",
  ["AnchorNpc"] = "Actor2",
  ["AnchorType"] = 2,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9113,
          ["X"] = -0.0172,
          ["Y"] = 0.0381,
          ["Z"] = 0.4095,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -786.2565,
          ["Y"] = -927.2757,
          ["Z"] = 256.417,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8692,
          ["X"] = -0.0129,
          ["Y"] = 0.0228,
          ["Z"] = 0.4938,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -182.2025,
          ["Y"] = -380.6395,
          ["Z"] = 128.8484,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 2,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 1312.9235,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9702,
          ["X"] = 0.0029,
          ["Y"] = 0.2419,
          ["Z"] = -0.0118,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1198.8519,
          ["Y"] = -57.772,
          ["Z"] = 673.1063,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 250,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_4",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7155,
          ["X"] = -0.017,
          ["Y"] = 0.0175,
          ["Z"] = 0.6982,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 49.2912,
          ["Y"] = -389.5339,
          ["Z"] = 157.544,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_5",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.413,
          ["X"] = 0.0143,
          ["Y"] = 0.0065,
          ["Z"] = -0.9106,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 896.4804,
          ["Y"] = 962.637,
          ["Z"] = 180.8768,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_6",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8736,
          ["X"] = 0.0085,
          ["Y"] = 0.0152,
          ["Z"] = -0.4863,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -217.3679,
          ["Y"] = 438.3493,
          ["Z"] = 131.6672,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_7",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7356,
          ["X"] = 0.0248,
          ["Y"] = 0.027,
          ["Z"] = -0.6764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 22.2633,
          ["Y"] = 417.7056,
          ["Z"] = 163.0909,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7424,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_8",
      ["OffsetZ"] = 15.7478,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1872,
          ["X"] = -0.0446,
          ["Y"] = 0.0085,
          ["Z"] = 0.9813,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 174.6057,
          ["Y"] = -50.4868,
          ["Z"] = 85.6869,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 124.7191,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_9",
      ["OffsetZ"] = 4.5444,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.108,
          ["X"] = -0.0104,
          ["Y"] = 0.0011,
          ["Z"] = 0.9941,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 92.7315,
          ["Y"] = -15.9797,
          ["Z"] = 74.4835,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 99.6247,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_10",
      ["OffsetZ"] = -0.4907,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4679,
          ["X"] = 0.0123,
          ["Y"] = -0.0065,
          ["Z"] = 0.8837,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 53.9049,
          ["Y"] = -74.4229,
          ["Z"] = 69.4485,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 228.636,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_11",
      ["OffsetZ"] = -5.2407,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3648,
          ["X"] = -0.013,
          ["Y"] = 0.0051,
          ["Z"] = 0.931,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 282.6801,
          ["Y"] = -200.0197,
          ["Z"] = 64.6984,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 198.2129,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_12",
      ["OffsetZ"] = 11.9924,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2093,
          ["X"] = -0.0512,
          ["Y"] = -0.011,
          ["Z"] = 0.9764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -214.263,
          ["Y"] = 109.4916,
          ["Z"] = 108.3236,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 155.931,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_13",
      ["OffsetZ"] = 4.7219,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1097,
          ["X"] = 0.0035,
          ["Y"] = 0.0004,
          ["Z"] = -0.994,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 99.4334,
          ["Y"] = 22.5008,
          ["Z"] = 74.5134,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 99.1662,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_14",
      ["OffsetZ"] = 3.5064,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4242,
          ["X"] = 0.0047,
          ["Y"] = 0.0022,
          ["Z"] = -0.9056,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 62.7677,
          ["Y"] = 76.232,
          ["Z"] = 73.2979,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 413.3839,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_15",
      ["OffsetZ"] = -9.7787,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3404,
          ["X"] = 0.0147,
          ["Y"] = 0.0054,
          ["Z"] = -0.9402,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 330.8403,
          ["Y"] = 236.3403,
          ["Z"] = 60.0128,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7757,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_16",
      ["OffsetZ"] = -14.1812,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1976,
          ["X"] = -0.012,
          ["Y"] = -0.0024,
          ["Z"] = -0.9802,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 176.8058,
          ["Y"] = 52.9797,
          ["Z"] = 68.8251,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [18] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 111.2802,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_17",
      ["OffsetZ"] = 0.062,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3519,
          ["X"] = -0.0098,
          ["Y"] = -0.0037,
          ["Z"] = -0.936,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 86.3025,
          ["Y"] = 68.0752,
          ["Z"] = 83.0849,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [19] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 107.9293,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_18",
      ["OffsetZ"] = 6.077,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0697,
          ["X"] = 0.0226,
          ["Y"] = 0.0016,
          ["Z"] = -0.9973,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.751,
          ["Y"] = 12.2958,
          ["Z"] = 89.0811,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [20] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 247.0631,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_19",
      ["OffsetZ"] = -12.5409,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3071,
          ["X"] = 0.0366,
          ["Y"] = 0.0118,
          ["Z"] = -0.9509,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 215.8233,
          ["Y"] = 117.9775,
          ["Z"] = 70.4883,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [21] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 172.1966,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_20",
      ["OffsetZ"] = -8.1054,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1668,
          ["X"] = -0.0086,
          ["Y"] = 0.0015,
          ["Z"] = -0.986,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 168.2474,
          ["Y"] = -34.3429,
          ["Z"] = 74.3048,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [22] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 103.9644,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_21",
      ["OffsetZ"] = 2.9312,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3971,
          ["X"] = -0.0017,
          ["Y"] = 0.0007,
          ["Z"] = -0.9178,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 75.0018,
          ["Y"] = -71.3756,
          ["Z"] = 86.2462,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [23] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 106.734,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_22",
      ["OffsetZ"] = 6.0496,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1719,
          ["X"] = 0.0155,
          ["Y"] = -0.0027,
          ["Z"] = -0.985,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.4203,
          ["Y"] = -31.6774,
          ["Z"] = 88.4598,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [24] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 272.3009,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_23",
      ["OffsetZ"] = -12.4972,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3501,
          ["X"] = 0.0294,
          ["Y"] = -0.011,
          ["Z"] = -0.9362,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 230.6554,
          ["Y"] = -143.7674,
          ["Z"] = 69.913,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [25] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 189.9815,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_24",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4193,
          ["X"] = -0.0285,
          ["Y"] = 0.0131,
          ["Z"] = 0.9073,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -9.4721,
          ["Y"] = -82.1518,
          ["Z"] = 168.7238,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [26] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_25",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9636,
          ["X"] = -0.013,
          ["Y"] = 0.0478,
          ["Z"] = 0.2627,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -97.7599,
          ["Y"] = -90.3284,
          ["Z"] = 188.3529,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [27] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 37.4,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_26",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9415,
          ["X"] = -0.0166,
          ["Y"] = 0.0467,
          ["Z"] = 0.3334,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -107.6717,
          ["Y"] = -137.2375,
          ["Z"] = 188.9069,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1-中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [28] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 20,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 289.6797,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_27",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9982,
          ["X"] = 0.0014,
          ["Y"] = 0.0541,
          ["Z"] = -0.0244,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -284.0911,
          ["Y"] = 40.6764,
          ["Z"] = 178.4642,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [29] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_28",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2545,
          ["X"] = -0.0253,
          ["Y"] = 0.0067,
          ["Z"] = 0.9667,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 204.3802,
          ["Y"] = -93.4633,
          ["Z"] = 186.2169,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [30] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 15,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 175,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 37.4,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_29",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.6653,
          ["X"] = -0.1025,
          ["Y"] = -0.0929,
          ["Z"] = 0.7337,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 164.1352,
          ["Y"] = 244.1451,
          ["Z"] = 156.9908,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["W"] = 0,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2-中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 11.75,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor1",
                  ["Target"] = "Actor2",
                },
                [2] = {
                  ["Delay"] = 2,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "Actor1",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor1",
                  ["Target"] = "反过肩Actor1",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3.5,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["LookAtInfo"] = {
                [1] = {
                  ["Delay"] = 0,
                  ["Enable"] = true,
                  ["Looker"] = "Actor2",
                  ["Target"] = "反过肩Actor2",
                },
              },
              ["LookAtTalker"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
              ["ObjectName"] = "BPS_LookAt_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5,
              ["TalkerLookAtTarget"] = "",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_6",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_7",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 2,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 951242253,
                        ["B"] = -46579055,
                        ["C"] = -1498202896,
                        ["D"] = -983809611,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Target"] = "Actor1",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_8",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_9",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_10",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_11",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_12",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_13",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_14",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_15",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_16",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_17",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_18",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_19",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_20",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_21",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_22",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [8] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_23",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_48",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-正面",
              ["bAutoCameraTrack"] = true,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_49",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-近景",
              ["bAutoCameraTrack"] = true,
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_50",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1-中景",
              ["bAutoCameraTrack"] = true,
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_51",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-正面",
              ["bAutoCameraTrack"] = true,
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_52",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-近景",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2-中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_53",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2-中景",
              ["bAutoCameraTrack"] = true,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueCameraTrack_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 951242253,
                ["B"] = -46579055,
                ["C"] = -1498202896,
                ["D"] = -983809611,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_12",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1112409553,
                ["B"] = 947145031,
                ["C"] = -2059395371,
                ["D"] = -649671499,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_13",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.9,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 609476300,
                ["B"] = -1988670596,
                ["C"] = -2094368139,
                ["D"] = -1196848039,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_14",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.65,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [4] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -37972908,
                ["B"] = 754598323,
                ["C"] = -1872923644,
                ["D"] = -355546235,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_15",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.65,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 1,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 3.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 2,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 951242253,
                ["B"] = -46579055,
                ["C"] = -1498202896,
                ["D"] = -983809611,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["ObjectName"] = "BPS_DialogueShot_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2-近景",
              ["StartTime"] = 0,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "中景",
              ["bConstant"] = false,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1112409553,
                ["B"] = 947145031,
                ["C"] = -2059395371,
                ["D"] = -649671499,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["ObjectName"] = "BPS_DialogueShot_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1-正面",
              ["StartTime"] = 3.5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor1-正面",
              ["bConstant"] = false,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 1.75,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 609476300,
                ["B"] = -1988670596,
                ["C"] = -2094368139,
                ["D"] = -1196848039,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["ObjectName"] = "BPS_DialogueShot_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2-正面",
              ["StartTime"] = 5,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-正面",
              ["bConstant"] = false,
            },
            [4] = {
              ["BreathAttenuation"] = 4,
              ["BreathDelay"] = 0,
              ["BreathDuration"] = 5,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 1,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -37972908,
                ["B"] = 754598323,
                ["C"] = -1872923644,
                ["D"] = -355546235,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
              ["ObjectName"] = "BPS_DialogueShot_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2-中景",
              ["StartTime"] = 6.75,
              ["SwitchType"] = 0,
              ["TargetCamera"] = "Actor2-中景",
              ["bConstant"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["ObjectName"] = "BP_DialogueCameraCut_C_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 951242253,
                ["B"] = -46579055,
                ["C"] = -1498202896,
                ["D"] = -983809611,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "早上好，你看起来很急切？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 2,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 1.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1112409553,
                ["B"] = 947145031,
                ["C"] = -2059395371,
                ["D"] = -649671499,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "队长呢？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 3.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 1.75,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 609476300,
                ["B"] = -1988670596,
                ["C"] = -2094368139,
                ["D"] = -1196848039,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "在他的办公室，昨晚是他值夜。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -37972908,
                ["B"] = 754598323,
                ["C"] = -1872923644,
                ["D"] = -355546235,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_3",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "作为一个“不眠者”，他每天只需要休息两个小时，那些工厂主和银行家肯定最喜欢这种魔药。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6.75,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_3",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "失踪的孩子",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7255045,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9962,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.0872,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 65.9978,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7255039,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9763,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.2164,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -135.4273,
          ["Y"] = 58.5417,
          ["Z"] = 77.9978,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C Name=\"BP_DialogueCameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2'\"",
      [3] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C Name=\"BPS_DialogueShot_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_3'\"",
      [4] = "      End Object",
      [5] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C Name=\"BPS_DialogueShot_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_2'\"",
      [6] = "      End Object",
      [7] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C Name=\"BPS_DialogueShot_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_1'\"",
      [8] = "      End Object",
      [9] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C Name=\"BPS_DialogueShot_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_0'\"",
      [10] = "      End Object",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_29\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_29'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_53\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_53'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_28\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_28'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_27\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_27'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_51\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_51'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_26'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_50\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_50'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_25'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_49\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_49'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_24'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_48\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_48'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_52\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_52'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [37] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [38] = "      End Object",
      [39] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [40] = "      End Object",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BPS_LookAt_C_0'\"",
      [43] = "   End Object",
      [44] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [45] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_15'\"",
      [46] = "      End Object",
      [47] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_14'\"",
      [48] = "      End Object",
      [49] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_13'\"",
      [50] = "      End Object",
      [51] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_12'\"",
      [52] = "      End Object",
      [53] = "   End Object",
      [54] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [59] = "   End Object",
      [60] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [61] = "   End Object",
      [62] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [65] = "   End Object",
      [66] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [67] = "   End Object",
      [68] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [69] = "   End Object",
      [70] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [71] = "   End Object",
      [72] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [73] = "   End Object",
      [74] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [75] = "   End Object",
      [76] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [77] = "   End Object",
      [78] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [79] = "   End Object",
      [80] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [81] = "   End Object",
      [82] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [83] = "   End Object",
      [84] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [85] = "   End Object",
      [86] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [87] = "   End Object",
      [88] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [95] = "   End Object",
      [96] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [97] = "   End Object",
      [98] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_1'\"",
      [99] = "   End Object",
      [100] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [101] = "   End Object",
      [102] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [103] = "   End Object",
      [104] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [105] = "   End Object",
      [106] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [107] = "   End Object",
      [108] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [109] = "   End Object",
      [110] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [111] = "   End Object",
      [112] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [113] = "   End Object",
      [114] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [115] = "   End Object",
      [116] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [117] = "   End Object",
      [118] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_11'\"",
      [119] = "   End Object",
      [120] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [121] = "   End Object",
      [122] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_10'\"",
      [123] = "   End Object",
      [124] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [125] = "   End Object",
      [126] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_9'\"",
      [127] = "   End Object",
      [128] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [129] = "   End Object",
      [130] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_8'\"",
      [131] = "   End Object",
      [132] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [133] = "   End Object",
      [134] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [135] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0.BP_DialogueTrackDirection_C_0'\"",
      [136] = "         Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0.BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [137] = "         End Object",
      [138] = "      End Object",
      [139] = "   End Object",
      [140] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_7'\"",
      [143] = "   End Object",
      [144] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [145] = "   End Object",
      [146] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_6'\"",
      [147] = "   End Object",
      [148] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [149] = "   End Object",
      [150] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [151] = "   End Object",
      [152] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [153] = "   End Object",
      [154] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [155] = "   End Object",
      [156] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [157] = "   End Object",
      [158] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [159] = "   End Object",
      [160] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [161] = "   End Object",
      [162] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [167] = "   End Object",
      [168] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [169] = "   End Object",
      [170] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [171] = "   End Object",
      [172] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [173] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_1'\"",
      [174] = "      End Object",
      [175] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_0'\"",
      [176] = "      End Object",
      [177] = "   End Object",
      [178] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [179] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_3'\"",
      [180] = "      End Object",
      [181] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_2'\"",
      [182] = "      End Object",
      [183] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_1'\"",
      [184] = "      End Object",
      [185] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_0'\"",
      [186] = "      End Object",
      [187] = "   End Object",
      [188] = "   Begin Object Name=\"BP_DialogueCameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2'\"",
      [189] = "      Begin Object Name=\"BPS_DialogueShot_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_3'\"",
      [190] = "         TargetCamera=(CameraName=\"Actor2-中景\")",
      [191] = "         CameraBreathType=Left",
      [192] = "         BreathDuration=5.000000",
      [193] = "         BreathAttenuation=4.000000",
      [194] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [195] = "         SectionName=\"Actor2-中景\"",
      [196] = "         LineUniqueIDLinked=FDBC94542CFA41B3905D7404EACECB85",
      [197] = "         OwnedEpisodeID=1",
      [198] = "         StartTime=6.750000",
      [199] = "         Duration=5.000000",
      [200] = "      End Object",
      [201] = "      Begin Object Name=\"BPS_DialogueShot_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_2'\"",
      [202] = "         TargetCamera=(CameraName=\"Actor2-正面\")",
      [203] = "         BreathDuration=1.750000",
      [204] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [205] = "         SectionName=\"Actor2-正面\"",
      [206] = "         LineUniqueIDLinked=2453DECC89774B7C832A7A75B8A98C59",
      [207] = "         OwnedEpisodeID=1",
      [208] = "         StartTime=5.000000",
      [209] = "      End Object",
      [210] = "      Begin Object Name=\"BPS_DialogueShot_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_1'\"",
      [211] = "         TargetCamera=(CameraName=\"Actor1-正面\")",
      [212] = "         BreathDuration=1.500000",
      [213] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [214] = "         SectionName=\"Actor1-正面\"",
      [215] = "         LineUniqueIDLinked=BDB1FA2F3874494785401ED5D946CCB5",
      [216] = "         OwnedEpisodeID=1",
      [217] = "         StartTime=3.500000",
      [218] = "      End Object",
      [219] = "      Begin Object Name=\"BPS_DialogueShot_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_DialogueShot_C_0'\"",
      [220] = "         TargetCamera=(CameraName=\"中景\")",
      [221] = "         CameraBreathType=Right",
      [222] = "         BreathDuration=3.500000",
      [223] = "         BreathAttenuation=1.000000",
      [224] = "         BreathSpeed=10.000000",
      [225] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [226] = "         LineLinkedOffset=-2.000000",
      [227] = "         SectionName=\"Actor2-近景\"",
      [228] = "         LineUniqueIDLinked=38B2CE0DFD394291A6B33CF0C55C41B5",
      [229] = "         OwnedEpisodeID=1",
      [230] = "         Duration=3.500000",
      [231] = "      End Object",
      [232] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'BPS_DialogueShot_C_0'\"",
      [233] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'BPS_DialogueShot_C_1'\"",
      [234] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'BPS_DialogueShot_C_2'\"",
      [235] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C'BPS_DialogueShot_C_3'\"",
      [236] = "   End Object",
      [237] = "   Begin Object Name=\"BP_DialogueCamera_C_29\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_29'\"",
      [238] = "      FOV=37.400002",
      [239] = "      bOverride_DepthOfField=True",
      [240] = "      DepthOfFieldFocalDistance=175.000000",
      [241] = "      DepthOfFieldFStop=15.000000",
      [242] = "      DepthOfFieldSensorWidth=200.000000",
      [243] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [244] = "      SpawnTransform=(Rotation=(X=-0.102491,Y=-0.092930,Z=0.733668,W=-0.665275),Translation=(X=164.135243,Y=244.145128,Z=156.990771))",
      [245] = "      TrackName=\"Actor2-中景\"",
      [246] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [247] = "   End Object",
      [248] = "   Begin Object Name=\"DialogueCameraTrack_53\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_53'\"",
      [249] = "      bAutoCameraTrack=True",
      [250] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_29'\"",
      [251] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [252] = "      FromTemplate=False",
      [253] = "      TrackName=\"Actor2-中景\"",
      [254] = "   End Object",
      [255] = "   Begin Object Name=\"BP_DialogueCamera_C_28\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_28'\"",
      [256] = "      FOV=30.000000",
      [257] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [258] = "      SpawnTransform=(Rotation=(X=-0.025314,Y=0.006665,Z=0.966715,W=0.254515),Translation=(X=204.380174,Y=-93.463290,Z=186.216945))",
      [259] = "      TrackName=\"Actor2-近景\"",
      [260] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [261] = "   End Object",
      [262] = "   Begin Object Name=\"BP_DialogueCamera_C_27\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_27'\"",
      [263] = "      FOV=30.000000",
      [264] = "      bOverride_DepthOfField=True",
      [265] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [266] = "      DepthOfFieldFocalDistance=289.679688",
      [267] = "      DepthOfFieldFStop=20.000000",
      [268] = "      DepthOfFieldSensorWidth=200.000000",
      [269] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [270] = "      SpawnTransform=(Rotation=(X=0.001351,Y=0.054055,Z=-0.024370,W=0.998240),Translation=(X=-284.091145,Y=40.676380,Z=178.464163))",
      [271] = "      TrackName=\"Actor2-正面\"",
      [272] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [273] = "   End Object",
      [274] = "   Begin Object Name=\"DialogueCameraTrack_51\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_51'\"",
      [275] = "      bAutoCameraTrack=True",
      [276] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_27'\"",
      [277] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [278] = "      FromTemplate=False",
      [279] = "      TrackName=\"Actor2-正面\"",
      [280] = "   End Object",
      [281] = "   Begin Object Name=\"BP_DialogueCamera_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_26'\"",
      [282] = "      FOV=37.400002",
      [283] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [284] = "      SpawnTransform=(Rotation=(X=-0.016555,Y=0.046749,Z=0.333396,W=0.941482),Translation=(X=-107.671660,Y=-137.237490,Z=188.906859))",
      [285] = "      TrackName=\"Actor1-中景\"",
      [286] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [287] = "   End Object",
      [288] = "   Begin Object Name=\"DialogueCameraTrack_50\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_50'\"",
      [289] = "      bAutoCameraTrack=True",
      [290] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_26'\"",
      [291] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [292] = "      FromTemplate=False",
      [293] = "      TrackName=\"Actor1-中景\"",
      [294] = "   End Object",
      [295] = "   Begin Object Name=\"BP_DialogueCamera_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_25'\"",
      [296] = "      FOV=30.000000",
      [297] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [298] = "      SpawnTransform=(Rotation=(X=-0.013045,Y=0.047848,Z=0.262708,W=0.963600),Translation=(X=-97.759943,Y=-90.328389,Z=188.352897))",
      [299] = "      TrackName=\"Actor1-近景\"",
      [300] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [301] = "   End Object",
      [302] = "   Begin Object Name=\"DialogueCameraTrack_49\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_49'\"",
      [303] = "      bAutoCameraTrack=True",
      [304] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_25'\"",
      [305] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [306] = "      FromTemplate=False",
      [307] = "      TrackName=\"Actor1-近景\"",
      [308] = "   End Object",
      [309] = "   Begin Object Name=\"BP_DialogueCamera_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_24'\"",
      [310] = "      FOV=30.000000",
      [311] = "      bOverride_DepthOfField=True",
      [312] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [313] = "      DepthOfFieldFocalDistance=189.981461",
      [314] = "      DepthOfFieldSensorWidth=200.000000",
      [315] = "      SplineCurves=(Position=(Points=(())),Rotation=(Points=((OutVal=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000)))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000)))),ReparamTable=(Points=(())))",
      [316] = "      SpawnTransform=(Rotation=(X=-0.028521,Y=0.013144,Z=0.907318,W=0.419270),Translation=(X=-9.472103,Y=-82.151849,Z=168.723813))",
      [317] = "      TrackName=\"Actor1-正面\"",
      [318] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [319] = "   End Object",
      [320] = "   Begin Object Name=\"DialogueCameraTrack_48\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_48'\"",
      [321] = "      bAutoCameraTrack=True",
      [322] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_24'\"",
      [323] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [324] = "      FromTemplate=False",
      [325] = "      TrackName=\"Actor1-正面\"",
      [326] = "   End Object",
      [327] = "   Begin Object Name=\"DialogueCameraTrack_52\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_52'\"",
      [328] = "      bAutoCameraTrack=True",
      [329] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_28'\"",
      [330] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [331] = "      FromTemplate=False",
      [332] = "      TrackName=\"Actor2-近景\"",
      [333] = "   End Object",
      [334] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [335] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [336] = "         EpisodeID=1",
      [337] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [338] = "         NodePosX=300",
      [339] = "         NodeGuid=254BEA2146900E946EC0BCAB93A463FD",
      [340] = "         CustomProperties Pin (PinId=C079FF5640DCDBA505E079A6A72D8144,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 EB12A9CA41BD15E6401961A807CDDF86,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [341] = "      End Object",
      [342] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [343] = "         NodeGuid=15482E784E60C51B2FC503884D4568ED",
      [344] = "         CustomProperties Pin (PinId=EB12A9CA41BD15E6401961A807CDDF86,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_3 C079FF5640DCDBA505E079A6A72D8144,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [345] = "      End Object",
      [346] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [347] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [348] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_3'\"",
      [349] = "      GraphGuid=EDC8CE01465B4403E1E88F9D56E75F8D",
      [350] = "   End Object",
      [351] = "   Begin Object Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BPS_LookAt_C_0'\"",
      [352] = "      LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor1\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor2\"))",
      [353] = "      LookAtInfo(1)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"Actor1\"),Delay_11_85BDFA3A440A4FCF09F0788FF3E2BE0C=2.000000)",
      [354] = "      DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [355] = "      OwnedEpisodeID=1",
      [356] = "   End Object",
      [357] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [358] = "      Begin Object Name=\"BPS_StateControl_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_15'\"",
      [359] = "         bFixed=True",
      [360] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [361] = "         FromLineIndex=3",
      [362] = "         LineUniqueIDLinked=FDBC94542CFA41B3905D7404EACECB85",
      [363] = "         OwnedEpisodeID=1",
      [364] = "         StartTime=11.650000",
      [365] = "         Duration=0.100000",
      [366] = "      End Object",
      [367] = "      Begin Object Name=\"BPS_StateControl_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_14'\"",
      [368] = "         bFixed=True",
      [369] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [370] = "         FromLineIndex=2",
      [371] = "         LineUniqueIDLinked=2453DECC89774B7C832A7A75B8A98C59",
      [372] = "         OwnedEpisodeID=1",
      [373] = "         StartTime=6.650000",
      [374] = "         Duration=0.100000",
      [375] = "      End Object",
      [376] = "      Begin Object Name=\"BPS_StateControl_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_13'\"",
      [377] = "         bFixed=True",
      [378] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [379] = "         FromLineIndex=1",
      [380] = "         LineUniqueIDLinked=BDB1FA2F3874494785401ED5D946CCB5",
      [381] = "         OwnedEpisodeID=1",
      [382] = "         StartTime=4.900000",
      [383] = "         Duration=0.100000",
      [384] = "      End Object",
      [385] = "      Begin Object Name=\"BPS_StateControl_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0.BPS_StateControl_C_12'\"",
      [386] = "         bFixed=True",
      [387] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_0'\"",
      [388] = "         FromLineIndex=0",
      [389] = "         LineUniqueIDLinked=38B2CE0DFD394291A6B33CF0C55C41B5",
      [390] = "         OwnedEpisodeID=1",
      [391] = "         StartTime=3.400000",
      [392] = "         Duration=0.100000",
      [393] = "      End Object",
      [394] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_12'\"",
      [395] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_13'\"",
      [396] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_14'\"",
      [397] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_15'\"",
      [398] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [399] = "      FromTemplate=False",
      [400] = "   End Object",
      [401] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [402] = "      EpisodeID=1",
      [403] = "      ContentIndex=4",
      [404] = "      UniqueID=FDBC94542CFA41B3905D7404EACECB85",
      [405] = "      ContentString=\"作为一个“不眠者”，他每天只需要休息两个小时，那些工厂主和银行家肯定最喜欢这种魔药。\"",
      [406] = "      ContentUI=\"Default\"",
      [407] = "      Talker=(PerformerName=\"Actor2\")",
      [408] = "   End Object",
      [409] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [410] = "      EpisodeID=1",
      [411] = "      ContentIndex=3",
      [412] = "      UniqueID=2453DECC89774B7C832A7A75B8A98C59",
      [413] = "      Duration=1.750000",
      [414] = "      ContentString=\"在他的办公室，昨晚是他值夜。\"",
      [415] = "      ContentUI=\"Default\"",
      [416] = "      Talker=(PerformerName=\"Actor2\")",
      [417] = "   End Object",
      [418] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [419] = "      EpisodeID=1",
      [420] = "      ContentIndex=2",
      [421] = "      UniqueID=BDB1FA2F3874494785401ED5D946CCB5",
      [422] = "      Duration=1.500000",
      [423] = "      ContentString=\"队长呢？\"",
      [424] = "      ContentUI=\"Default\"",
      [425] = "      Talker=(PerformerName=\"Actor1\")",
      [426] = "   End Object",
      [427] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [428] = "      EpisodeID=1",
      [429] = "      ContentIndex=1",
      [430] = "      UniqueID=38B2CE0DFD394291A6B33CF0C55C41B5",
      [431] = "      Delay=2.000000",
      [432] = "      Duration=1.500000",
      [433] = "      ContentString=\"早上好，你看起来很急切？\"",
      [434] = "      ContentUI=\"Default\"",
      [435] = "      Talker=(PerformerName=\"Actor2\")",
      [436] = "   End Object",
      [437] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [438] = "      EpisodeID=1",
      [439] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [440] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [441] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [442] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [443] = "   End Object",
      [444] = "   Begin Object Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [445] = "      FOV=50.000000",
      [446] = "      bEnableLookAt=True",
      [447] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [448] = "      OffsetZ=-12.497244",
      [449] = "      bOverride_DepthOfField=True",
      [450] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [451] = "      DepthOfFieldFocalDistance=272.300934",
      [452] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [453] = "      SpawnTransform=(Rotation=(X=0.029425,Y=-0.011029,Z=-0.936200,W=-0.350059),Translation=(X=230.655376,Y=-143.767401,Z=69.912966))",
      [454] = "      TrackName=\"反中01Actor1\"",
      [455] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [456] = "   End Object",
      [457] = "   Begin Object Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [458] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_23'\"",
      [459] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [460] = "      TrackName=\"反中01Actor1\"",
      [461] = "   End Object",
      [462] = "   Begin Object Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [463] = "      FOV=40.000000",
      [464] = "      bEnableLookAt=True",
      [465] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [466] = "      OffsetZ=6.049607",
      [467] = "      bOverride_DepthOfField=True",
      [468] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [469] = "      DepthOfFieldFocalDistance=106.733963",
      [470] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [471] = "      SpawnTransform=(Rotation=(X=0.015473,Y=-0.002701,Z=-0.984988,W=-0.171908),Translation=(X=101.420298,Y=-31.677382,Z=88.459816))",
      [472] = "      TrackName=\"反近02Actor1\"",
      [473] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [474] = "   End Object",
      [475] = "   Begin Object Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [476] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_22'\"",
      [477] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [478] = "      TrackName=\"反近02Actor1\"",
      [479] = "   End Object",
      [480] = "   Begin Object Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [481] = "      FOV=40.000000",
      [482] = "      bEnableLookAt=True",
      [483] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [484] = "      OffsetZ=2.931192",
      [485] = "      bOverride_DepthOfField=True",
      [486] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [487] = "      DepthOfFieldFocalDistance=103.964363",
      [488] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [489] = "      SpawnTransform=(Rotation=(X=-0.001700,Y=0.000700,Z=-0.917778,W=-0.397090),Translation=(X=75.001798,Y=-71.375600,Z=86.246199))",
      [490] = "      TrackName=\"反近01Actor1\"",
      [491] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [492] = "   End Object",
      [493] = "   Begin Object Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [494] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_21'\"",
      [495] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [496] = "      TrackName=\"反近01Actor1\"",
      [497] = "   End Object",
      [498] = "   Begin Object Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [499] = "      FOV=40.000000",
      [500] = "      bEnableLookAt=True",
      [501] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [502] = "      OffsetZ=-8.105359",
      [503] = "      bOverride_DepthOfField=True",
      [504] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [505] = "      DepthOfFieldFocalDistance=172.196579",
      [506] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [507] = "      SpawnTransform=(Rotation=(X=-0.008604,Y=0.001456,Z=-0.985951,W=-0.166804),Translation=(X=168.247411,Y=-34.342888,Z=74.304763))",
      [508] = "      TrackName=\"反过肩Actor1\"",
      [509] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [510] = "   End Object",
      [511] = "   Begin Object Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [512] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_20'\"",
      [513] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [514] = "      TrackName=\"反过肩Actor1\"",
      [515] = "   End Object",
      [516] = "   Begin Object Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [517] = "      FOV=50.000000",
      [518] = "      bEnableLookAt=True",
      [519] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [520] = "      OffsetZ=-12.540889",
      [521] = "      bOverride_DepthOfField=True",
      [522] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [523] = "      DepthOfFieldFocalDistance=247.063095",
      [524] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [525] = "      SpawnTransform=(Rotation=(X=0.036600,Y=0.011800,Z=-0.950900,W=0.307100),Translation=(X=215.823298,Y=117.977500,Z=70.488294))",
      [526] = "      TrackName=\"中01Actor1\"",
      [527] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [528] = "   End Object",
      [529] = "   Begin Object Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [530] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_19'\"",
      [531] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [532] = "      TrackName=\"中01Actor1\"",
      [533] = "   End Object",
      [534] = "   Begin Object Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [535] = "      FOV=40.000000",
      [536] = "      bEnableLookAt=True",
      [537] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [538] = "      OffsetZ=6.077021",
      [539] = "      bOverride_DepthOfField=True",
      [540] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [541] = "      DepthOfFieldFocalDistance=107.929329",
      [542] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [543] = "      SpawnTransform=(Rotation=(X=0.022600,Y=0.001600,Z=-0.997311,W=0.069701),Translation=(X=101.750998,Y=12.295800,Z=89.081068))",
      [544] = "      TrackName=\"近02Actor1\"",
      [545] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [546] = "   End Object",
      [547] = "   Begin Object Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [548] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_18'\"",
      [549] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [550] = "      TrackName=\"近02Actor1\"",
      [551] = "   End Object",
      [552] = "   Begin Object Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [553] = "      FOV=40.000000",
      [554] = "      bEnableLookAt=True",
      [555] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [556] = "      OffsetZ=0.061963",
      [557] = "      bOverride_DepthOfField=True",
      [558] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [559] = "      DepthOfFieldFocalDistance=111.280205",
      [560] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [561] = "      SpawnTransform=(Rotation=(X=-0.009800,Y=-0.003700,Z=-0.935982,W=0.351893),Translation=(X=86.302498,Y=68.075200,Z=83.084910))",
      [562] = "      TrackName=\"近01Actor1\"",
      [563] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [564] = "   End Object",
      [565] = "   Begin Object Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [566] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_17'\"",
      [567] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [568] = "      TrackName=\"近01Actor1\"",
      [569] = "   End Object",
      [570] = "   Begin Object Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [571] = "      FOV=40.000000",
      [572] = "      bEnableLookAt=True",
      [573] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [574] = "      OffsetZ=-14.181160",
      [575] = "      bOverride_DepthOfField=True",
      [576] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [577] = "      DepthOfFieldFocalDistance=185.775696",
      [578] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [579] = "      SpawnTransform=(Rotation=(X=-0.012000,Y=-0.002400,Z=-0.980206,W=0.197601),Translation=(X=176.805798,Y=52.979700,Z=68.825099))",
      [580] = "      TrackName=\"过肩Actor1\"",
      [581] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [582] = "   End Object",
      [583] = "   Begin Object Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [584] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_16'\"",
      [585] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [586] = "      TrackName=\"过肩Actor1\"",
      [587] = "   End Object",
      [588] = "   Begin Object Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [589] = "      AppearanceID=7255039",
      [590] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [591] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.216397,W=0.976305),Translation=(X=-135.427300,Y=58.541713,Z=77.997757))",
      [592] = "      TrackName=\"Actor1\"",
      [593] = "      StickGround=True",
      [594] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [595] = "   End Object",
      [596] = "   Begin Object Name=\"DialogueActorTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_1'\"",
      [597] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [598] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_16'\"",
      [599] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_17'\"",
      [600] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_18'\"",
      [601] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_19'\"",
      [602] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_20'\"",
      [603] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_21'\"",
      [604] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_22'\"",
      [605] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_23'\"",
      [606] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [607] = "      TrackName=\"Actor1\"",
      [608] = "   End Object",
      [609] = "   Begin Object Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [610] = "      FOV=30.000000",
      [611] = "      bEnableLookAt=True",
      [612] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [613] = "      OffsetZ=-9.778734",
      [614] = "      bOverride_DepthOfField=True",
      [615] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [616] = "      DepthOfFieldFocalDistance=413.383881",
      [617] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [618] = "      SpawnTransform=(Rotation=(X=0.014740,Y=0.005363,Z=-0.940158,W=0.340377),Translation=(X=330.840293,Y=236.340323,Z=60.012759))",
      [619] = "      TrackName=\"反中01Actor2\"",
      [620] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [621] = "   End Object",
      [622] = "   Begin Object Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [623] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [624] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [625] = "      TrackName=\"反中01Actor2\"",
      [626] = "   End Object",
      [627] = "   Begin Object Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [628] = "      FOV=40.000000",
      [629] = "      bEnableLookAt=True",
      [630] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [631] = "      OffsetZ=3.506431",
      [632] = "      bOverride_DepthOfField=True",
      [633] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [634] = "      DepthOfFieldFocalDistance=99.166183",
      [635] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [636] = "      SpawnTransform=(Rotation=(X=0.004742,Y=0.002221,Z=-0.905556,W=0.424194),Translation=(X=62.767722,Y=76.232026,Z=73.297923))",
      [637] = "      TrackName=\"反近02Actor2\"",
      [638] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [639] = "   End Object",
      [640] = "   Begin Object Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [641] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_14'\"",
      [642] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [643] = "      TrackName=\"反近02Actor2\"",
      [644] = "   End Object",
      [645] = "   Begin Object Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [646] = "      FOV=40.000000",
      [647] = "      bEnableLookAt=True",
      [648] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [649] = "      OffsetZ=4.721918",
      [650] = "      bOverride_DepthOfField=True",
      [651] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [652] = "      DepthOfFieldFocalDistance=155.931000",
      [653] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [654] = "      SpawnTransform=(Rotation=(X=0.003470,Y=0.000383,Z=-0.993955,W=0.109734),Translation=(X=99.433446,Y=22.500819,Z=74.513410))",
      [655] = "      TrackName=\"反近01Actor2\"",
      [656] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [657] = "   End Object",
      [658] = "   Begin Object Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [659] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_13'\"",
      [660] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [661] = "      TrackName=\"反近01Actor2\"",
      [662] = "   End Object",
      [663] = "   Begin Object Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [664] = "      FOV=40.000000",
      [665] = "      bEnableLookAt=True",
      [666] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [667] = "      OffsetZ=11.992385",
      [668] = "      bOverride_DepthOfField=True",
      [669] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [670] = "      DepthOfFieldFocalDistance=198.212906",
      [671] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [672] = "      SpawnTransform=(Rotation=(X=-0.051202,Y=-0.011001,Z=0.976444,W=-0.209319),Translation=(X=-214.262955,Y=109.491577,Z=108.323640))",
      [673] = "      TrackName=\"反过肩Actor2\"",
      [674] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [675] = "   End Object",
      [676] = "   Begin Object Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [677] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_12'\"",
      [678] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [679] = "      TrackName=\"反过肩Actor2\"",
      [680] = "   End Object",
      [681] = "   Begin Object Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [682] = "      FOV=30.000000",
      [683] = "      bEnableLookAt=True",
      [684] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [685] = "      OffsetZ=-5.240726",
      [686] = "      bOverride_DepthOfField=True",
      [687] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [688] = "      DepthOfFieldFocalDistance=228.636032",
      [689] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [690] = "      SpawnTransform=(Rotation=(X=-0.013000,Y=0.005100,Z=0.930984,W=0.364794),Translation=(X=282.680100,Y=-200.019700,Z=64.698391))",
      [691] = "      TrackName=\"中01Actor2\"",
      [692] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [693] = "   End Object",
      [694] = "   Begin Object Name=\"DialogueCameraTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_11'\"",
      [695] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_11'\"",
      [696] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [697] = "      TrackName=\"中01Actor2\"",
      [698] = "   End Object",
      [699] = "   Begin Object Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [700] = "      FOV=40.000000",
      [701] = "      bEnableLookAt=True",
      [702] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [703] = "      OffsetZ=-0.490653",
      [704] = "      bOverride_DepthOfField=True",
      [705] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [706] = "      DepthOfFieldFocalDistance=99.624741",
      [707] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [708] = "      SpawnTransform=(Rotation=(X=0.012300,Y=-0.006500,Z=0.883678,W=0.467888),Translation=(X=53.904900,Y=-74.422900,Z=69.448465))",
      [709] = "      TrackName=\"近02Actor2\"",
      [710] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [711] = "   End Object",
      [712] = "   Begin Object Name=\"DialogueCameraTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_10'\"",
      [713] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_10'\"",
      [714] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [715] = "      TrackName=\"近02Actor2\"",
      [716] = "   End Object",
      [717] = "   Begin Object Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [718] = "      FOV=40.000000",
      [719] = "      bEnableLookAt=True",
      [720] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [721] = "      OffsetZ=4.544427",
      [722] = "      bOverride_DepthOfField=True",
      [723] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [724] = "      DepthOfFieldFocalDistance=124.719078",
      [725] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [726] = "      SpawnTransform=(Rotation=(X=-0.010400,Y=0.001100,Z=0.994096,W=0.108000),Translation=(X=92.731500,Y=-15.979700,Z=74.483545))",
      [727] = "      TrackName=\"近01Actor2\"",
      [728] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [729] = "   End Object",
      [730] = "   Begin Object Name=\"DialogueCameraTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_9'\"",
      [731] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_9'\"",
      [732] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [733] = "      TrackName=\"近01Actor2\"",
      [734] = "   End Object",
      [735] = "   Begin Object Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [736] = "      FOV=40.000000",
      [737] = "      bEnableLookAt=True",
      [738] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [739] = "      OffsetZ=15.747757",
      [740] = "      bOverride_DepthOfField=True",
      [741] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [742] = "      DepthOfFieldFocalDistance=185.742432",
      [743] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [744] = "      SpawnTransform=(Rotation=(X=-0.044599,Y=0.008500,Z=0.981273,W=0.187195),Translation=(X=174.605700,Y=-50.486800,Z=85.686875))",
      [745] = "      TrackName=\"过肩Actor2\"",
      [746] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [747] = "   End Object",
      [748] = "   Begin Object Name=\"DialogueCameraTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_8'\"",
      [749] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_8'\"",
      [750] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [751] = "      TrackName=\"过肩Actor2\"",
      [752] = "   End Object",
      [753] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [754] = "      AppearanceID=7255045",
      [755] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [756] = "      SpawnTransform=(Rotation=(X=0.000000,Y=-0.000000,Z=0.087156,W=0.996195),Translation=(X=0.000000,Y=0.000000,Z=65.997764))",
      [757] = "      TrackName=\"Actor2\"",
      [758] = "      StickGround=True",
      [759] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [760] = "   End Object",
      [761] = "   Begin Object Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [762] = "      Begin Object Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0.BP_DialogueTrackDirection_C_0'\"",
      [763] = "         Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0.BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [764] = "            Target=(TrackName=\"Actor1\")",
      [765] = "            DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:DialogueActorTrack_0.BP_DialogueTrackDirection_C_0'\"",
      [766] = "            LineLinkedOffset=-1.980542",
      [767] = "            LineUniqueIDLinked=38B2CE0DFD394291A6B33CF0C55C41B5",
      [768] = "            OwnedEpisodeID=1",
      [769] = "            Duration=2.000000",
      [770] = "         End Object",
      [771] = "         ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [772] = "         Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [773] = "      End Object",
      [774] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [775] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_8'\"",
      [776] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_9'\"",
      [777] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_10'\"",
      [778] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_11'\"",
      [779] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [780] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_13'\"",
      [781] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_14'\"",
      [782] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_15'\"",
      [783] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [784] = "      TrackName=\"Actor2\"",
      [785] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'BP_DialogueTrackDirection_C_0'\"",
      [786] = "   End Object",
      [787] = "   Begin Object Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [788] = "      FOV=40.000000",
      [789] = "      bEnableLookAt=True",
      [790] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [791] = "      SpawnTransform=(Rotation=(X=0.024821,Y=0.026953,Z=-0.676421,W=0.735604),Translation=(X=22.263340,Y=417.705584,Z=163.090943))",
      [792] = "      TrackName=\"反平视\"",
      [793] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [794] = "   End Object",
      [795] = "   Begin Object Name=\"DialogueCameraTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_7'\"",
      [796] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_7'\"",
      [797] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [798] = "      TrackName=\"反平视\"",
      [799] = "   End Object",
      [800] = "   Begin Object Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [801] = "      FOV=60.000000",
      [802] = "      bEnableLookAt=True",
      [803] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [804] = "      SpawnTransform=(Rotation=(X=0.008500,Y=0.015200,Z=-0.486308,W=0.873614),Translation=(X=-217.367933,Y=438.349287,Z=131.667217))",
      [805] = "      TrackName=\"反全景\"",
      [806] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [807] = "   End Object",
      [808] = "   Begin Object Name=\"DialogueCameraTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_6'\"",
      [809] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [810] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [811] = "      TrackName=\"反全景\"",
      [812] = "   End Object",
      [813] = "   Begin Object Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [814] = "      FOV=50.000000",
      [815] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [816] = "      SpawnTransform=(Rotation=(X=0.014324,Y=0.006452,Z=-0.910579,W=0.413037),Translation=(X=896.480400,Y=962.637000,Z=180.876767))",
      [817] = "      TrackName=\"反远景\"",
      [818] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [819] = "   End Object",
      [820] = "   Begin Object Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [821] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_5'\"",
      [822] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [823] = "      TrackName=\"反远景\"",
      [824] = "   End Object",
      [825] = "   Begin Object Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [826] = "      FOV=40.000000",
      [827] = "      DepthOfFieldFocalDistance=250.000000",
      [828] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [829] = "      SpawnTransform=(Rotation=(X=-0.017000,Y=0.017500,Z=0.698193,W=0.715493),Translation=(X=49.291200,Y=-389.533900,Z=157.544000))",
      [830] = "      TrackName=\"平视\"",
      [831] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [832] = "   End Object",
      [833] = "   Begin Object Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [834] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_4'\"",
      [835] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [836] = "      TrackName=\"平视\"",
      [837] = "   End Object",
      [838] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [839] = "      FOV=30.000000",
      [840] = "      bOverride_DepthOfField=True",
      [841] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [842] = "      DepthOfFieldFocalDistance=1312.923462",
      [843] = "      DepthOfFieldFStop=2.000000",
      [844] = "      DepthOfFieldSensorWidth=200.000000",
      [845] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [846] = "      SpawnTransform=(Rotation=(X=0.002900,Y=0.241906,Z=-0.011800,W=0.970224),Translation=(X=-1198.851900,Y=-57.772000,Z=673.106326))",
      [847] = "      TrackName=\"中景\"",
      [848] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [849] = "   End Object",
      [850] = "   Begin Object Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [851] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [852] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [853] = "      TrackName=\"中景\"",
      [854] = "   End Object",
      [855] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [856] = "      FOV=60.000000",
      [857] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [858] = "      SpawnTransform=(Rotation=(X=-0.012900,Y=0.022800,Z=0.493792,W=0.869186),Translation=(X=-182.202500,Y=-380.639500,Z=128.848400))",
      [859] = "      TrackName=\"全景\"",
      [860] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [861] = "   End Object",
      [862] = "   Begin Object Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [863] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [864] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [865] = "      TrackName=\"全景\"",
      [866] = "   End Object",
      [867] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [868] = "      FOV=50.000000",
      [869] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [870] = "      SpawnTransform=(Rotation=(X=-0.017163,Y=0.038135,Z=0.409535,W=0.911336),Translation=(X=-786.256472,Y=-927.275683,Z=256.417030))",
      [871] = "      TrackName=\"远景\"",
      [872] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [873] = "   End Object",
      [874] = "   Begin Object Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [875] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [876] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [877] = "      TrackName=\"远景\"",
      [878] = "   End Object",
      [879] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [880] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [881] = "      TrackName=\"锚点\"",
      [882] = "   End Object",
      [883] = "   Begin Object Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [884] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [885] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_1'\"",
      [886] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_2'\"",
      [887] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_3'\"",
      [888] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_4'\"",
      [889] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_5'\"",
      [890] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_6'\"",
      [891] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_7'\"",
      [892] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [893] = "      Childs(8)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [894] = "      Childs(9)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_48'\"",
      [895] = "      Childs(10)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_49'\"",
      [896] = "      Childs(11)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_50'\"",
      [897] = "      Childs(12)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_51'\"",
      [898] = "      Childs(13)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_52'\"",
      [899] = "      Childs(14)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_53'\"",
      [900] = "      TrackName=\"锚点\"",
      [901] = "   End Object",
      [902] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [903] = "      Begin Object Name=\"BPS_LookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_1'\"",
      [904] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor2\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"反过肩Actor2\"))",
      [905] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [906] = "         OwnedEpisodeID=1",
      [907] = "         StartTime=5.000000",
      [908] = "      End Object",
      [909] = "      Begin Object Name=\"BPS_LookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0.BPS_LookAt_C_0'\"",
      [910] = "         LookAtInfo(0)=(Looker_6_C69172D6451878E86AE39FA27E6D594D=(PerformerName=\"Actor1\"),Target_5_5D8F5F864DFD8E6CA51D498DAC22BA94=(TrackName=\"反过肩Actor1\"))",
      [911] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'********:BP_DialogueTrackLookAt_C_0'\"",
      [912] = "         OwnedEpisodeID=1",
      [913] = "         StartTime=3.500000",
      [914] = "      End Object",
      [915] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'********:BPS_LookAt_C_0'\"",
      [916] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_0'\"",
      [917] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C'BPS_LookAt_C_1'\"",
      [918] = "   End Object",
      [919] = "   Begin Object Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [920] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_3'\"",
      [921] = "         EpisodeID=1",
      [922] = "         ContentIndex=4",
      [923] = "         Talker=\"Actor2\"",
      [924] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [925] = "         SectionName=\"作为一个“不眠者”，他每天只需要休息两个小时，那些工厂主和银行家肯定最喜欢这种魔药。\"",
      [926] = "         FromLineIndex=3",
      [927] = "         LineUniqueIDLinked=FDBC94542CFA41B3905D7404EACECB85",
      [928] = "         OwnedEpisodeID=1",
      [929] = "         StartTime=6.750000",
      [930] = "         Duration=5.000000",
      [931] = "      End Object",
      [932] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_2'\"",
      [933] = "         EpisodeID=1",
      [934] = "         ContentIndex=3",
      [935] = "         Talker=\"Actor2\"",
      [936] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [937] = "         SectionName=\"在他的办公室，昨晚是他值夜。\"",
      [938] = "         FromLineIndex=2",
      [939] = "         LineUniqueIDLinked=2453DECC89774B7C832A7A75B8A98C59",
      [940] = "         OwnedEpisodeID=1",
      [941] = "         StartTime=5.000000",
      [942] = "         Duration=1.750000",
      [943] = "      End Object",
      [944] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_1'\"",
      [945] = "         EpisodeID=1",
      [946] = "         ContentIndex=2",
      [947] = "         Talker=\"Actor1\"",
      [948] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [949] = "         SectionName=\"队长呢？\"",
      [950] = "         FromLineIndex=1",
      [951] = "         LineUniqueIDLinked=BDB1FA2F3874494785401ED5D946CCB5",
      [952] = "         OwnedEpisodeID=1",
      [953] = "         StartTime=3.500000",
      [954] = "         Duration=1.500000",
      [955] = "      End Object",
      [956] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_0'\"",
      [957] = "         EpisodeID=1",
      [958] = "         ContentIndex=1",
      [959] = "         Talker=\"Actor2\"",
      [960] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [961] = "         SectionName=\"早上好，你看起来很急切？\"",
      [962] = "         FromLineIndex=0",
      [963] = "         LineUniqueIDLinked=38B2CE0DFD394291A6B33CF0C55C41B5",
      [964] = "         OwnedEpisodeID=1",
      [965] = "         StartTime=2.000000",
      [966] = "         Duration=1.500000",
      [967] = "      End Object",
      [968] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [969] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [970] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [971] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [972] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [973] = "      FromTemplate=False",
      [974] = "   End Object",
      [975] = "   Note=\"失踪的孩子\"",
      [976] = "   AnchorType=NewEnumerator1",
      [977] = "   AnchorID=\"339025826\"",
      [978] = "   AnchorNpc=(PerformerName=\"Actor2\")",
      [979] = "   PreLoadArray(0)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [980] = "   PreLoadArray(1)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [981] = "   ActorInfos(0)=(PerformerName=\"Actor2\",AppearanceID=(ApperanceID=7255045),IdleAnimation=(AssetID=\"Idle\"))",
      [982] = "   ActorInfos(1)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=7255039),IdleAnimation=(AssetID=\"Idle\"))",
      [983] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/TwoPeopleDialogue.TwoPeopleDialogue'\"",
      [984] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_0'\"",
      [985] = "   StoryLineID=********",
      [986] = "   Episodes(0)=(EpisodeID=1,Duration=11.750000,TrackList=(\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueCameraTrack_0'\",\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'BP_DialogueCameraCut_C_2'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_3'\"))",
      [987] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [988] = "   PerformerList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_1'\"",
      [989] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [990] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [991] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [992] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [993] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_4'\"",
      [994] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_5'\"",
      [995] = "   CameraList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_6'\"",
      [996] = "   CameraList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_7'\"",
      [997] = "   CameraList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_8'\"",
      [998] = "   CameraList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_9'\"",
      [999] = "   CameraList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_10'\"",
      [1000] = "   CameraList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_11'\"",
      [1001] = "   CameraList(12)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_12'\"",
      [1002] = "   CameraList(13)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_13'\"",
      [1003] = "   CameraList(14)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_14'\"",
      [1004] = "   CameraList(15)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_15'\"",
      [1005] = "   CameraList(16)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_16'\"",
      [1006] = "   CameraList(17)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_17'\"",
      [1007] = "   CameraList(18)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_18'\"",
      [1008] = "   CameraList(19)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_19'\"",
      [1009] = "   CameraList(20)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_20'\"",
      [1010] = "   CameraList(21)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_21'\"",
      [1011] = "   CameraList(22)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_22'\"",
      [1012] = "   CameraList(23)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_23'\"",
      [1013] = "   CameraList(24)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_24'\"",
      [1014] = "   CameraList(25)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_25'\"",
      [1015] = "   CameraList(26)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_26'\"",
      [1016] = "   CameraList(27)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_27'\"",
      [1017] = "   CameraList(28)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_28'\"",
      [1018] = "   CameraList(29)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_29'\"",
      [1019] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [1020] = "End Object",
    },
  },
}