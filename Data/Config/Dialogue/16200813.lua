return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 12000011,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["AppearanceID"] = 7201007,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle_Rich",
        ["StateName"] = "",
      },
      ["InsID"] = "980559969",
      ["PerformerName"] = "Actor2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["AppearanceID"] = 7201010,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "2345769968",
      ["PerformerName"] = "Actor3",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_6",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7019,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.7123,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -50988.1241,
          ["Y"] = -44202.3549,
          ["Z"] = -779.2612,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_7",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4043,
          ["X"] = 0.1505,
          ["Y"] = 0.0677,
          ["Z"] = -0.8996,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 478.4554,
          ["Y"] = 519.008,
          ["Z"] = 326.6988,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_8",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5134,
          ["X"] = 0.0165,
          ["Y"] = 0.0099,
          ["Z"] = -0.8579,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 313.9842,
          ["Y"] = 481.2069,
          ["Z"] = 128.7731,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_9",
      ["OffsetZ"] = 69.8578,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9815,
          ["X"] = 0.0131,
          ["Y"] = 0.1649,
          ["Z"] = -0.0966,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -232.6197,
          ["Y"] = 103.3625,
          ["Z"] = 230.0513,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_10",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0243,
          ["X"] = -0.0975,
          ["Y"] = 0.0024,
          ["Z"] = 0.9949,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 331.4805,
          ["Y"] = 43.9403,
          ["Z"] = 201.6302,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 32,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_11",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6211,
          ["X"] = -0.0109,
          ["Y"] = -0.0087,
          ["Z"] = -0.7836,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 301.4653,
          ["Y"] = 950.8038,
          ["Z"] = 82.8137,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 143.9757,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_12",
      ["OffsetZ"] = 9.8212,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0087,
          ["X"] = 0.0367,
          ["Y"] = 0.0003,
          ["Z"] = -0.9993,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 203.9684,
          ["Y"] = 4.8632,
          ["Z"] = 93.0357,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 230,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_13",
      ["OffsetZ"] = -6.6951,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1409,
          ["X"] = 0.0207,
          ["Y"] = 0.003,
          ["Z"] = -0.9898,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 224.3656,
          ["Y"] = 25.5644,
          ["Z"] = 76.1977,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 352.0524,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 20,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_14",
      ["OffsetZ"] = -31.0521,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1739,
          ["X"] = 0.0034,
          ["Y"] = 0.0006,
          ["Z"] = -0.9848,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 806.7606,
          ["Y"] = 296.9659,
          ["Z"] = 52.0005,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 92,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_15",
      ["OffsetZ"] = 6.3612,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1097,
          ["X"] = 0.0156,
          ["Y"] = -0.0017,
          ["Z"] = -0.9938,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 191.6953,
          ["Y"] = -41.6389,
          ["Z"] = 76.3283,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 246.935,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_16",
      ["OffsetZ"] = 20.8963,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8524,
          ["X"] = 0.0517,
          ["Y"] = 0.1063,
          ["Z"] = -0.5093,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -54.2615,
          ["Y"] = 257.0724,
          ["Z"] = 181.0702,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 273.5193,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_17",
      ["OffsetZ"] = 1.3434,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0436,
          ["X"] = 0.0296,
          ["Y"] = -0.0013,
          ["Z"] = -0.9986,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 266.423,
          ["Y"] = -56.1065,
          ["Z"] = 71.3105,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中2Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 362.5349,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 20,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_18",
      ["OffsetZ"] = 10.8981,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0035,
          ["X"] = 0.0192,
          ["Y"] = -0.0001,
          ["Z"] = -0.9998,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 415.9614,
          ["Y"] = 27.0369,
          ["Z"] = 80.8653,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 134.9227,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor3",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_19",
      ["OffsetZ"] = -6.28,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1322,
          ["X"] = 0.0103,
          ["Y"] = 0.0014,
          ["Z"] = 0.9912,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 130.3703,
          ["Y"] = 33.382,
          ["Z"] = 63.3501,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 258.643,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor3",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_20",
      ["OffsetZ"] = -4.8521,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.6235,
          ["X"] = 0.0389,
          ["Y"] = -0.0265,
          ["Z"] = -0.7804,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 154.5047,
          ["Y"] = -249.7355,
          ["Z"] = 174.833,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 223.6975,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor3",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_21",
      ["OffsetZ"] = -25.4897,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2503,
          ["X"] = -0.0017,
          ["Y"] = -0.0005,
          ["Z"] = 0.9682,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 284.5526,
          ["Y"] = 96.6488,
          ["Z"] = 44.523,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 24.75,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [4] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 15.65,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [5] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 18.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [6] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 21.15,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [7] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 24.65,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_5",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 4.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["TargetCamera"] = "中景",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 4.25,
              ["TargetCamera"] = "中景",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9.25,
              ["TargetCamera"] = "过肩Actor2",
              ["bConstant"] = true,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12.25,
              ["TargetCamera"] = "过肩Actor2",
              ["bConstant"] = true,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 2.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 15.75,
              ["TargetCamera"] = "过肩Actor3",
              ["bConstant"] = true,
            },
            [6] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 18.25,
              ["TargetCamera"] = "过肩Actor3",
              ["bConstant"] = true,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 21.25,
              ["TargetCamera"] = "过肩Actor3",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["ObjectName"] = "BP_DialogueCameraCut_C_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [4] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_13",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_14",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_15",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_16",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景2",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_17",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "过肩Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_22",
              ["Parent"] = "锚点",
              ["TrackName"] = "过肩Actor2",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "过肩Actor3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_26",
              ["Parent"] = "锚点",
              ["TrackName"] = "过肩Actor3",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 4.25,
                      ["Target"] = "Actor3",
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9.25,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 12.25,
                      ["Target"] = "Actor3",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["ObjectName"] = "BP_DialogueTrackActorLookAt_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
                [2] = {
                  ["ActionSections"] = {
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_1",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Talk_Yes_B",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.1728,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 12.25,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_3",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_18",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近景Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_19",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_20",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [9] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Ask",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.0238,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.0415,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Salute",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.8646,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 10.3854,
                      ["bConstant"] = true,
                    },
                    [3] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Salute_End",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.3694,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_3",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 1.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 12.25,
                      ["bConstant"] = true,
                    },
                    [4] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "FirmRefusal",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.6049,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 21.25,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.9255,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "芬尼斯前进1",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 16.1874,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.0831,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9.25,
                      ["Target"] = "Actor1",
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["Duration"] = 0.4092,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 15.75,
                      ["Target"] = "Actor3",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [4] = {
                  ["ActionSections"] = {
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["ObjectName"] = "BP_DialogueTrackActorLookAt_C_2",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_21",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近景Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中2Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_23",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中2Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_24",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [10] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Welcome",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.2011,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 4.25,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Finger_002",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 18.25,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_1",
                  ["Parent"] = "Actor3",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.3581,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 16.5766,
                      ["Target"] = "Actor2",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_2",
                  ["Parent"] = "Actor3",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0.2675,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 11.557,
                      ["Target"] = "Actor1",
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 16.9288,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["ObjectName"] = "BP_DialogueTrackActorLookAt_C_1",
                  ["Parent"] = "Actor3",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_25",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "近景Actor3",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_27",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "中Actor3",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor3",
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "芬尼斯前进1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueRoutePointTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "芬尼斯前进1",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueCameraTrack_12",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 4.25,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 4.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "感谢您对小主人的照料，我代表我的女主人向您致谢。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 9.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_3",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 12.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 2.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_4",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "小主人，马车正在园外等待。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 15.75,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_5",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我想再和道格说几句话，求您再给我几分钟......",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 18.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_6",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 21.25,
              ["SubTitle"] = "",
              ["Talker"] = "Actor3",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 117.5851,
          ["Y"] = 100.8301,
          ["Z"] = -2.724,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "芬尼斯前进1",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000011,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9848,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.1736,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 79.5605,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201007,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle_Rich",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "980559969",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1803,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9836,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 169.6449,
          ["Y"] = 54.0096,
          ["Z"] = 63.6244,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201010,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "2345769968",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9659,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.2588,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 15.6155,
          ["Y"] = 122.6255,
          ["Z"] = 89.0529,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor3",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
    [3] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_27'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [5] = "   End Object",
      [6] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_26'\"",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_25'\"",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_4'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_24'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_3'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [41] = "   End Object",
      [42] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [43] = "   End Object",
      [44] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [45] = "   End Object",
      [46] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [47] = "   End Object",
      [48] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [49] = "   End Object",
      [50] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_2'\"",
      [51] = "   End Object",
      [52] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [53] = "   End Object",
      [54] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [59] = "   End Object",
      [60] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [61] = "   End Object",
      [62] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [65] = "   End Object",
      [66] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [67] = "   End Object",
      [68] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [69] = "   End Object",
      [70] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [71] = "   End Object",
      [72] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [73] = "   End Object",
      [74] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C Name=\"BP_DialogueCameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2'\"",
      [75] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_0'\"",
      [76] = "      End Object",
      [77] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_1'\"",
      [78] = "      End Object",
      [79] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_2'\"",
      [80] = "      End Object",
      [81] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_3'\"",
      [82] = "      End Object",
      [83] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_4'\"",
      [84] = "      End Object",
      [85] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_5'\"",
      [86] = "      End Object",
      [87] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_6'\"",
      [88] = "      End Object",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_2'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [95] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [96] = "      End Object",
      [97] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [98] = "      End Object",
      [99] = "   End Object",
      [100] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [101] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [102] = "      End Object",
      [103] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_1'\"",
      [104] = "      End Object",
      [105] = "   End Object",
      [106] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [107] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [108] = "      End Object",
      [109] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [110] = "      End Object",
      [111] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_2'\"",
      [112] = "      End Object",
      [113] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_3'\"",
      [114] = "      End Object",
      [115] = "   End Object",
      [116] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [117] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [118] = "      End Object",
      [119] = "   End Object",
      [120] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueRoutePointTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_0'\"",
      [121] = "   End Object",
      [122] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_0'\"",
      [123] = "   End Object",
      [124] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0'\"",
      [125] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_2'\"",
      [126] = "      End Object",
      [127] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [128] = "      End Object",
      [129] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_1'\"",
      [130] = "      End Object",
      [131] = "   End Object",
      [132] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C Name=\"BP_DialogueTrackActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0'\"",
      [133] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_0'\"",
      [134] = "      End Object",
      [135] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_1'\"",
      [136] = "      End Object",
      [137] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_2'\"",
      [138] = "      End Object",
      [139] = "   End Object",
      [140] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2'\"",
      [143] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2.BPS_ActorDirection_C_1'\"",
      [144] = "      End Object",
      [145] = "   End Object",
      [146] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C Name=\"BP_DialogueTrackActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1'\"",
      [147] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_2'\"",
      [148] = "      End Object",
      [149] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_0'\"",
      [150] = "      End Object",
      [151] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_1'\"",
      [152] = "      End Object",
      [153] = "   End Object",
      [154] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C Name=\"BP_DialogueTrackActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2'\"",
      [155] = "   End Object",
      [156] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [157] = "   End Object",
      [158] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3'\"",
      [159] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3.BPS_PlayAnimation_C_0'\"",
      [160] = "      End Object",
      [161] = "   End Object",
      [162] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [167] = "   End Object",
      [168] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [169] = "   End Object",
      [170] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [171] = "   End Object",
      [172] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [173] = "   End Object",
      [174] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [175] = "   End Object",
      [176] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [177] = "   End Object",
      [178] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [179] = "   End Object",
      [180] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [181] = "   End Object",
      [182] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [183] = "   End Object",
      [184] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [185] = "   End Object",
      [186] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [187] = "   End Object",
      [188] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [189] = "   End Object",
      [190] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [191] = "   End Object",
      [192] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [193] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_0'\"",
      [194] = "      End Object",
      [195] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_1'\"",
      [196] = "      End Object",
      [197] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_2'\"",
      [198] = "      End Object",
      [199] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_3'\"",
      [200] = "      End Object",
      [201] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_4'\"",
      [202] = "      End Object",
      [203] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_5'\"",
      [204] = "      End Object",
      [205] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_6'\"",
      [206] = "      End Object",
      [207] = "   End Object",
      [208] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6'\"",
      [209] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_0'\"",
      [210] = "      End Object",
      [211] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_1'\"",
      [212] = "      End Object",
      [213] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_2'\"",
      [214] = "      End Object",
      [215] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_3'\"",
      [216] = "      End Object",
      [217] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_4'\"",
      [218] = "      End Object",
      [219] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_5'\"",
      [220] = "      End Object",
      [221] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_6'\"",
      [222] = "      End Object",
      [223] = "   End Object",
      [224] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_2'\"",
      [225] = "   End Object",
      [226] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1'\"",
      [227] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_0'\"",
      [228] = "      End Object",
      [229] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_1'\"",
      [230] = "      End Object",
      [231] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_2'\"",
      [232] = "      End Object",
      [233] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_3'\"",
      [234] = "      End Object",
      [235] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_4'\"",
      [236] = "      End Object",
      [237] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_5'\"",
      [238] = "      End Object",
      [239] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_6'\"",
      [240] = "      End Object",
      [241] = "   End Object",
      [242] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3'\"",
      [243] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_0'\"",
      [244] = "      End Object",
      [245] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_1'\"",
      [246] = "      End Object",
      [247] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_2'\"",
      [248] = "      End Object",
      [249] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_3'\"",
      [250] = "      End Object",
      [251] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_4'\"",
      [252] = "      End Object",
      [253] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_5'\"",
      [254] = "      End Object",
      [255] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_6'\"",
      [256] = "      End Object",
      [257] = "   End Object",
      [258] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_4'\"",
      [259] = "   End Object",
      [260] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [261] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [262] = "      End Object",
      [263] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [264] = "      End Object",
      [265] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [266] = "      End Object",
      [267] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [268] = "      End Object",
      [269] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [270] = "      End Object",
      [271] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [272] = "      End Object",
      [273] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [274] = "      End Object",
      [275] = "   End Object",
      [276] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [277] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_0'\"",
      [278] = "      End Object",
      [279] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_1'\"",
      [280] = "      End Object",
      [281] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_2'\"",
      [282] = "      End Object",
      [283] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_3'\"",
      [284] = "      End Object",
      [285] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_4'\"",
      [286] = "      End Object",
      [287] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_5'\"",
      [288] = "      End Object",
      [289] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_6'\"",
      [290] = "      End Object",
      [291] = "   End Object",
      [292] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [293] = "   End Object",
      [294] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [295] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_0'\"",
      [296] = "      End Object",
      [297] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_1'\"",
      [298] = "      End Object",
      [299] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_2'\"",
      [300] = "      End Object",
      [301] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_3'\"",
      [302] = "      End Object",
      [303] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_4'\"",
      [304] = "      End Object",
      [305] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_5'\"",
      [306] = "      End Object",
      [307] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_6'\"",
      [308] = "      End Object",
      [309] = "   End Object",
      [310] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [311] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [312] = "      End Object",
      [313] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_1'\"",
      [314] = "      End Object",
      [315] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_2'\"",
      [316] = "      End Object",
      [317] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_3'\"",
      [318] = "      End Object",
      [319] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_4'\"",
      [320] = "      End Object",
      [321] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_5'\"",
      [322] = "      End Object",
      [323] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_6'\"",
      [324] = "      End Object",
      [325] = "   End Object",
      [326] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [327] = "   End Object",
      [328] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [329] = "   End Object",
      [330] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [331] = "   End Object",
      [332] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [333] = "   End Object",
      [334] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [335] = "   End Object",
      [336] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [337] = "   End Object",
      [338] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [339] = "   End Object",
      [340] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [341] = "   End Object",
      [342] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [343] = "   End Object",
      [344] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [345] = "   End Object",
      [346] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [347] = "   End Object",
      [348] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [349] = "   End Object",
      [350] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [351] = "   End Object",
      [352] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [353] = "   End Object",
      [354] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [355] = "   End Object",
      [356] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [357] = "   End Object",
      [358] = "   Begin Object Name=\"DialogueCameraTrack_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_27'\"",
      [359] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_21'\"",
      [360] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [361] = "      TrackName=\"中Actor3\"",
      [362] = "   End Object",
      [363] = "   Begin Object Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [364] = "      FOV=30.000000",
      [365] = "      LookAtTarget=(PerformerName=\"Actor3\")",
      [366] = "      OffsetZ=-4.852090",
      [367] = "      bOverride_DepthOfField=True",
      [368] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor3\")",
      [369] = "      DepthOfFieldFocalDistance=258.643005",
      [370] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [371] = "      SpawnTransform=(Rotation=(X=0.038900,Y=-0.026500,Z=-0.780403,W=-0.623503),Translation=(X=154.504743,Y=-249.735523,Z=174.833037))",
      [372] = "      TrackName=\"过肩Actor3\"",
      [373] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [374] = "   End Object",
      [375] = "   Begin Object Name=\"DialogueCameraTrack_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_26'\"",
      [376] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_20'\"",
      [377] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [378] = "      TrackName=\"过肩Actor3\"",
      [379] = "   End Object",
      [380] = "   Begin Object Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [381] = "      FOV=40.000000",
      [382] = "      bEnableLookAt=True",
      [383] = "      LookAtTarget=(PerformerName=\"Actor3\")",
      [384] = "      OffsetZ=-6.280021",
      [385] = "      bOverride_DepthOfField=True",
      [386] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor3\")",
      [387] = "      DepthOfFieldFocalDistance=134.922653",
      [388] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [389] = "      SpawnTransform=(Rotation=(X=0.010339,Y=0.001391,Z=0.991163,W=-0.132236),Translation=(X=130.370259,Y=33.382028,Z=63.350100))",
      [390] = "      TrackName=\"近景Actor3\"",
      [391] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [392] = "   End Object",
      [393] = "   Begin Object Name=\"DialogueCameraTrack_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_25'\"",
      [394] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_19'\"",
      [395] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [396] = "      TrackName=\"近景Actor3\"",
      [397] = "   End Object",
      [398] = "   Begin Object Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [399] = "      AppearanceID=7201010",
      [400] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [401] = "      InsID=\"2345769968\"",
      [402] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.258819,W=0.965926),Translation=(X=15.615535,Y=122.625476,Z=89.052875))",
      [403] = "      TrackName=\"Actor3\"",
      [404] = "      StickGround=True",
      [405] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [406] = "   End Object",
      [407] = "   Begin Object Name=\"DialogueActorTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_4'\"",
      [408] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [409] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_25'\"",
      [410] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_27'\"",
      [411] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [412] = "      TrackName=\"Actor3\"",
      [413] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [414] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_2'\"",
      [415] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_1'\"",
      [416] = "   End Object",
      [417] = "   Begin Object Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [418] = "      FOV=20.000000",
      [419] = "      bEnableLookAt=True",
      [420] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [421] = "      OffsetZ=10.898146",
      [422] = "      bOverride_DepthOfField=True",
      [423] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [424] = "      DepthOfFieldFocalDistance=362.534943",
      [425] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [426] = "      SpawnTransform=(Rotation=(X=0.019191,Y=-0.000056,Z=-0.999810,W=-0.003519),Translation=(X=415.961427,Y=27.036903,Z=80.865299))",
      [427] = "      TrackName=\"中Actor2\"",
      [428] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [429] = "   End Object",
      [430] = "   Begin Object Name=\"DialogueCameraTrack_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_24'\"",
      [431] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_18'\"",
      [432] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [433] = "      TrackName=\"中Actor2\"",
      [434] = "   End Object",
      [435] = "   Begin Object Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [436] = "      FOV=40.000000",
      [437] = "      bEnableLookAt=True",
      [438] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [439] = "      OffsetZ=1.343372",
      [440] = "      bOverride_DepthOfField=True",
      [441] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [442] = "      DepthOfFieldFocalDistance=273.519318",
      [443] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [444] = "      SpawnTransform=(Rotation=(X=0.029628,Y=-0.001265,Z=-0.998607,W=-0.043633),Translation=(X=266.423028,Y=-56.106503,Z=71.310524))",
      [445] = "      TrackName=\"中2Actor2\"",
      [446] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [447] = "   End Object",
      [448] = "   Begin Object Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [449] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_17'\"",
      [450] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [451] = "      TrackName=\"中2Actor2\"",
      [452] = "   End Object",
      [453] = "   Begin Object Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [454] = "      FOV=60.000000",
      [455] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [456] = "      OffsetZ=20.896255",
      [457] = "      bOverride_DepthOfField=True",
      [458] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [459] = "      DepthOfFieldFocalDistance=246.934967",
      [460] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [461] = "      SpawnTransform=(Rotation=(X=0.051701,Y=0.106303,Z=-0.509314,W=0.852424),Translation=(X=-54.261491,Y=257.072428,Z=181.070200))",
      [462] = "      TrackName=\"过肩Actor2\"",
      [463] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [464] = "   End Object",
      [465] = "   Begin Object Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [466] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_16'\"",
      [467] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [468] = "      TrackName=\"过肩Actor2\"",
      [469] = "   End Object",
      [470] = "   Begin Object Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [471] = "      FOV=30.000000",
      [472] = "      bEnableLookAt=True",
      [473] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [474] = "      OffsetZ=6.361160",
      [475] = "      DepthOfFieldFocalDistance=92.000000",
      [476] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [477] = "      SpawnTransform=(Rotation=(X=0.015612,Y=-0.001724,Z=-0.993838,W=-0.109721),Translation=(X=191.695344,Y=-41.638890,Z=76.328312))",
      [478] = "      TrackName=\"近景Actor2\"",
      [479] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [480] = "   End Object",
      [481] = "   Begin Object Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [482] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [483] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [484] = "      TrackName=\"近景Actor2\"",
      [485] = "   End Object",
      [486] = "   Begin Object Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [487] = "      AppearanceID=7201007",
      [488] = "      IdleAnimLibAssetID=(AssetID=\"Idle_Rich\")",
      [489] = "      InsID=\"980559969\"",
      [490] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.983609,W=-0.180312),Translation=(X=169.644923,Y=54.009632,Z=63.624401))",
      [491] = "      TrackName=\"Actor2\"",
      [492] = "      StickGround=True",
      [493] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [494] = "   End Object",
      [495] = "   Begin Object Name=\"DialogueActorTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_3'\"",
      [496] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [497] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_21'\"",
      [498] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_23'\"",
      [499] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_24'\"",
      [500] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [501] = "      TrackName=\"Actor2\"",
      [502] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [503] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [504] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [505] = "      Actions(3)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_2'\"",
      [506] = "   End Object",
      [507] = "   Begin Object Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [508] = "      FOV=20.000000",
      [509] = "      bEnableLookAt=True",
      [510] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [511] = "      OffsetZ=-31.052053",
      [512] = "      bOverride_DepthOfField=True",
      [513] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [514] = "      DepthOfFieldFocalDistance=352.052429",
      [515] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [516] = "      SpawnTransform=(Rotation=(X=0.003436,Y=0.000582,Z=-0.984759,W=0.173887),Translation=(X=806.760615,Y=296.965892,Z=52.000487))",
      [517] = "      TrackName=\"中Actor1\"",
      [518] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [519] = "   End Object",
      [520] = "   Begin Object Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [521] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_14'\"",
      [522] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [523] = "      TrackName=\"中Actor1\"",
      [524] = "   End Object",
      [525] = "   Begin Object Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [526] = "      FOV=50.000000",
      [527] = "      bEnableLookAt=True",
      [528] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [529] = "      OffsetZ=-6.695117",
      [530] = "      bOverride_DepthOfField=True",
      [531] = "      DepthOfFieldFocalDistance=230.000000",
      [532] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [533] = "      SpawnTransform=(Rotation=(X=0.020728,Y=0.002981,Z=-0.989803,W=0.140892),Translation=(X=224.365577,Y=25.564438,Z=76.197671))",
      [534] = "      TrackName=\"过肩Actor1\"",
      [535] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [536] = "   End Object",
      [537] = "   Begin Object Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [538] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_13'\"",
      [539] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [540] = "      TrackName=\"过肩Actor1\"",
      [541] = "   End Object",
      [542] = "   Begin Object Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [543] = "      FOV=30.000000",
      [544] = "      bEnableLookAt=True",
      [545] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [546] = "      OffsetZ=9.821169",
      [547] = "      bOverride_DepthOfField=True",
      [548] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [549] = "      DepthOfFieldFocalDistance=143.975677",
      [550] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [551] = "      SpawnTransform=(Rotation=(X=0.036665,Y=0.000342,Z=-0.999289,W=0.008749),Translation=(X=203.968360,Y=4.863159,Z=93.035690))",
      [552] = "      TrackName=\"近景Actor1\"",
      [553] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [554] = "   End Object",
      [555] = "   Begin Object Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [556] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_12'\"",
      [557] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [558] = "      TrackName=\"近景Actor1\"",
      [559] = "   End Object",
      [560] = "   Begin Object Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [561] = "      AppearanceID=12000011",
      [562] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [563] = "      bIsPlayer=True",
      [564] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.173648,W=0.984808),Translation=(X=0.000002,Y=0.000000,Z=79.560450))",
      [565] = "      TrackName=\"Actor1\"",
      [566] = "      StickGround=True",
      [567] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [568] = "   End Object",
      [569] = "   Begin Object Name=\"DialogueActorTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_2'\"",
      [570] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [571] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_18'\"",
      [572] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_19'\"",
      [573] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_20'\"",
      [574] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [575] = "      TrackName=\"Actor1\"",
      [576] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [577] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [578] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_3'\"",
      [579] = "   End Object",
      [580] = "   Begin Object Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [581] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_11'\"",
      [582] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [583] = "      TrackName=\"平视\"",
      [584] = "   End Object",
      [585] = "   Begin Object Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [586] = "      FOV=40.000000",
      [587] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [588] = "      SpawnTransform=(Rotation=(X=-0.097540,Y=0.002409,Z=0.994932,W=0.024291),Translation=(X=331.480505,Y=43.940285,Z=201.630241))",
      [589] = "      TrackName=\"中景2\"",
      [590] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [591] = "   End Object",
      [592] = "   Begin Object Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [593] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_10'\"",
      [594] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [595] = "      TrackName=\"中景2\"",
      [596] = "   End Object",
      [597] = "   Begin Object Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [598] = "      FOV=50.000000",
      [599] = "      bEnableLookAt=True",
      [600] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [601] = "      OffsetZ=69.857834",
      [602] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [603] = "      SpawnTransform=(Rotation=(X=0.013061,Y=0.164882,Z=-0.096605,W=0.981484),Translation=(X=-232.619653,Y=103.362452,Z=230.051296))",
      [604] = "      TrackName=\"中景\"",
      [605] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [606] = "   End Object",
      [607] = "   Begin Object Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [608] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_9'\"",
      [609] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [610] = "      TrackName=\"中景\"",
      [611] = "   End Object",
      [612] = "   Begin Object Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [613] = "      FOV=60.000000",
      [614] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [615] = "      SpawnTransform=(Rotation=(X=0.016500,Y=0.009900,Z=-0.857925,W=0.513415),Translation=(X=313.984220,Y=481.206852,Z=128.773057))",
      [616] = "      TrackName=\"全景\"",
      [617] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [618] = "   End Object",
      [619] = "   Begin Object Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [620] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_8'\"",
      [621] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [622] = "      TrackName=\"全景\"",
      [623] = "   End Object",
      [624] = "   Begin Object Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [625] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [626] = "      SpawnTransform=(Rotation=(X=0.150543,Y=0.067657,Z=-0.899611,W=0.404301),Translation=(X=478.455423,Y=519.008043,Z=326.698832))",
      [627] = "      TrackName=\"远景\"",
      [628] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [629] = "   End Object",
      [630] = "   Begin Object Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [631] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_7'\"",
      [632] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [633] = "      TrackName=\"远景\"",
      [634] = "   End Object",
      [635] = "   Begin Object Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [636] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [637] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.712288,W=0.701888),Translation=(X=-50988.124100,Y=-44202.354900,Z=-779.261200))",
      [638] = "      TrackName=\"锚点\"",
      [639] = "   End Object",
      [640] = "   Begin Object Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [641] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [642] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_13'\"",
      [643] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_14'\"",
      [644] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_15'\"",
      [645] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_16'\"",
      [646] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_17'\"",
      [647] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_22'\"",
      [648] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_26'\"",
      [649] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [650] = "      Childs(8)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [651] = "      Childs(9)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [652] = "      Childs(10)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueRoutePointTrack_0'\"",
      [653] = "      TrackName=\"锚点\"",
      [654] = "   End Object",
      [655] = "   Begin Object Name=\"BP_DialogueCameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2'\"",
      [656] = "      Begin Object Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_0'\"",
      [657] = "         TargetCamera=(CameraName=\"中景\")",
      [658] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [659] = "         LineGUIDLinked=748692227",
      [660] = "         OwnedEpisodeID=1",
      [661] = "         Duration=4.250000",
      [662] = "      End Object",
      [663] = "      Begin Object Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_1'\"",
      [664] = "         TargetCamera=(CameraName=\"中景\")",
      [665] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [666] = "         LineGUIDLinked=3341332579",
      [667] = "         OwnedEpisodeID=1",
      [668] = "         StartTime=4.250000",
      [669] = "         Duration=5.000000",
      [670] = "      End Object",
      [671] = "      Begin Object Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_2'\"",
      [672] = "         TargetCamera=(CameraName=\"过肩Actor2\")",
      [673] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [674] = "         LineGUIDLinked=959981811",
      [675] = "         OwnedEpisodeID=1",
      [676] = "         StartTime=9.250000",
      [677] = "         Duration=3.000000",
      [678] = "      End Object",
      [679] = "      Begin Object Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_3'\"",
      [680] = "         TargetCamera=(CameraName=\"过肩Actor2\")",
      [681] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [682] = "         LineGUIDLinked=885157243",
      [683] = "         OwnedEpisodeID=1",
      [684] = "         StartTime=12.250000",
      [685] = "         Duration=3.500000",
      [686] = "      End Object",
      [687] = "      Begin Object Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_4'\"",
      [688] = "         TargetCamera=(CameraName=\"过肩Actor3\")",
      [689] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [690] = "         LineGUIDLinked=2433113019",
      [691] = "         OwnedEpisodeID=1",
      [692] = "         StartTime=15.750000",
      [693] = "         Duration=2.500000",
      [694] = "      End Object",
      [695] = "      Begin Object Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_5'\"",
      [696] = "         TargetCamera=(CameraName=\"过肩Actor3\")",
      [697] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [698] = "         LineGUIDLinked=2905916935",
      [699] = "         OwnedEpisodeID=1",
      [700] = "         StartTime=18.250000",
      [701] = "         Duration=3.000000",
      [702] = "      End Object",
      [703] = "      Begin Object Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_2.BPS_CameraCut_C_6'\"",
      [704] = "         TargetCamera=(CameraName=\"过肩Actor3\")",
      [705] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_2'\"",
      [706] = "         LineGUIDLinked=400437238",
      [707] = "         OwnedEpisodeID=1",
      [708] = "         StartTime=21.250000",
      [709] = "         Duration=3.500000",
      [710] = "      End Object",
      [711] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_0'\"",
      [712] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_1'\"",
      [713] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_2'\"",
      [714] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_3'\"",
      [715] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_4'\"",
      [716] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_5'\"",
      [717] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_6'\"",
      [718] = "   End Object",
      [719] = "   Begin Object Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [720] = "      FOV=32.000000",
      [721] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [722] = "      SpawnTransform=(Rotation=(X=-0.010942,Y=-0.008673,Z=-0.783617,W=0.621087),Translation=(X=301.465289,Y=950.803828,Z=82.813676))",
      [723] = "      TrackName=\"平视\"",
      [724] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [725] = "   End Object",
      [726] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_2'\"",
      [727] = "   End Object",
      [728] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [729] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [730] = "         NodeGuid=08FFAF0843FEC66E7AD83291510C0BB9",
      [731] = "         CustomProperties Pin (PinId=CAE385964C6DF5D96F9583953549A8CA,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_2 776FB3094A23A1AE733B608DC49D8795,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [732] = "      End Object",
      [733] = "      Begin Object Name=\"EpisodeGraphNode_2\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_2'\"",
      [734] = "         EpisodeID=1",
      [735] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [736] = "         NodePosX=300",
      [737] = "         NodeGuid=1BDFFB8844F50B96B9BFEAB023BFB739",
      [738] = "         CustomProperties Pin (PinId=776FB3094A23A1AE733B608DC49D8795,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 CAE385964C6DF5D96F9583953549A8CA,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [739] = "      End Object",
      [740] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [741] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [742] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_2'\"",
      [743] = "      GraphGuid=D82AB68E48CAD1181CB6C4A4BC9FB16B",
      [744] = "   End Object",
      [745] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [746] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [747] = "         AnimLibItem=(AssetID=\"Finger_002\")",
      [748] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [749] = "         OwnedEpisodeID=1",
      [750] = "         StartTime=18.250000",
      [751] = "         Duration=3.000000",
      [752] = "      End Object",
      [753] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_1'\"",
      [754] = "         AnimLibItem=(AssetID=\"Welcome\")",
      [755] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [756] = "         OwnedEpisodeID=1",
      [757] = "         StartTime=4.250000",
      [758] = "         Duration=2.201075",
      [759] = "      End Object",
      [760] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [761] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [762] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [763] = "   End Object",
      [764] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [765] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [766] = "         AnimLibItem=(AssetID=\"Salute\")",
      [767] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [768] = "         LineLinkedOffset=1.135444",
      [769] = "         LineGUIDLinked=959981811",
      [770] = "         OwnedEpisodeID=1",
      [771] = "         StartTime=10.385444",
      [772] = "         Duration=1.864556",
      [773] = "      End Object",
      [774] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [775] = "         AnimLibItem=(AssetID=\"Ask\")",
      [776] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [777] = "         OwnedEpisodeID=1",
      [778] = "         StartTime=1.041458",
      [779] = "         Duration=2.023768",
      [780] = "      End Object",
      [781] = "      Begin Object Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_2'\"",
      [782] = "         AnimLibItem=(AssetID=\"FirmRefusal\")",
      [783] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [784] = "         OwnedEpisodeID=1",
      [785] = "         StartTime=21.250000",
      [786] = "         Duration=1.604900",
      [787] = "      End Object",
      [788] = "      Begin Object Name=\"BPS_PlayAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_3'\"",
      [789] = "         AnimLibItem=(AssetID=\"Salute_End\")",
      [790] = "         PreAnimationBlendOutTime=1.800000",
      [791] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [792] = "         OwnedEpisodeID=1",
      [793] = "         StartTime=12.250000",
      [794] = "         Duration=1.369441",
      [795] = "      End Object",
      [796] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [797] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [798] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_3'\"",
      [799] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_2'\"",
      [800] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [801] = "   End Object",
      [802] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [803] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [804] = "         MoveTarget=(TrackName=\"芬尼斯前进1\")",
      [805] = "         StickGround=True",
      [806] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [807] = "         OwnedEpisodeID=1",
      [808] = "         StartTime=16.187370",
      [809] = "         Duration=1.925518",
      [810] = "      End Object",
      [811] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [812] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [813] = "   End Object",
      [814] = "   Begin Object Name=\"DialogueRoutePointTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_0'\"",
      [815] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_0'\"",
      [816] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [817] = "      FromTemplate=False",
      [818] = "      TrackName=\"芬尼斯前进1\"",
      [819] = "   End Object",
      [820] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_0'\"",
      [821] = "      SpawnTransform=(Translation=(X=117.585065,Y=100.830141,Z=-2.724025))",
      [822] = "      TrackName=\"芬尼斯前进1\"",
      [823] = "      StickGround=True",
      [824] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [825] = "   End Object",
      [826] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0'\"",
      [827] = "      Begin Object Name=\"BPS_ActorDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_2'\"",
      [828] = "         Target=(TrackName=\"Actor1\")",
      [829] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [830] = "         OwnedEpisodeID=1",
      [831] = "         StartTime=9.250000",
      [832] = "         Duration=1.083084",
      [833] = "      End Object",
      [834] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_0'\"",
      [835] = "         Target=(TrackName=\"Actor3\")",
      [836] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [837] = "         OwnedEpisodeID=1",
      [838] = "         StartTime=15.750000",
      [839] = "         Duration=0.409248",
      [840] = "      End Object",
      [841] = "      Begin Object Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_0.BPS_ActorDirection_C_1'\"",
      [842] = "         Target=(TrackName=\"Actor3\")",
      [843] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_0'\"",
      [844] = "         OwnedEpisodeID=1",
      [845] = "         Duration=1.252843",
      [846] = "      End Object",
      [847] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_2'\"",
      [848] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [849] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [850] = "   End Object",
      [851] = "   Begin Object Name=\"BP_DialogueTrackActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0'\"",
      [852] = "      Begin Object Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_0'\"",
      [853] = "         Target=(TrackName=\"Actor3\")",
      [854] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [855] = "         OwnedEpisodeID=1",
      [856] = "         StartTime=4.250000",
      [857] = "      End Object",
      [858] = "      Begin Object Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_1'\"",
      [859] = "         Target=(TrackName=\"Actor2\")",
      [860] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [861] = "         OwnedEpisodeID=1",
      [862] = "         StartTime=9.250000",
      [863] = "      End Object",
      [864] = "      Begin Object Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_2'\"",
      [865] = "         Target=(TrackName=\"Actor3\")",
      [866] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [867] = "         OwnedEpisodeID=1",
      [868] = "         StartTime=12.250000",
      [869] = "      End Object",
      [870] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_0'\"",
      [871] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_1'\"",
      [872] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_2'\"",
      [873] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [874] = "   End Object",
      [875] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [876] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [877] = "   End Object",
      [878] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2'\"",
      [879] = "      Begin Object Name=\"BPS_ActorDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2.BPS_ActorDirection_C_1'\"",
      [880] = "         Target=(TrackName=\"Actor2\")",
      [881] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_2'\"",
      [882] = "         OwnedEpisodeID=1",
      [883] = "         StartTime=16.576572",
      [884] = "         Duration=1.358139",
      [885] = "      End Object",
      [886] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_1'\"",
      [887] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [888] = "   End Object",
      [889] = "   Begin Object Name=\"BP_DialogueTrackActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1'\"",
      [890] = "      Begin Object Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_2'\"",
      [891] = "         Target=(TrackName=\"Actor2\")",
      [892] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_1'\"",
      [893] = "         OwnedEpisodeID=1",
      [894] = "         StartTime=0.267454",
      [895] = "      End Object",
      [896] = "      Begin Object Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_0'\"",
      [897] = "         Target=(TrackName=\"Actor1\")",
      [898] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_1'\"",
      [899] = "         OwnedEpisodeID=1",
      [900] = "         StartTime=11.557022",
      [901] = "      End Object",
      [902] = "      Begin Object Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_1.BPS_ActorLookAt_C_1'\"",
      [903] = "         Target=(TrackName=\"Actor2\")",
      [904] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_1'\"",
      [905] = "         OwnedEpisodeID=1",
      [906] = "         StartTime=16.928822",
      [907] = "      End Object",
      [908] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_2'\"",
      [909] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_0'\"",
      [910] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_1'\"",
      [911] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_4'\"",
      [912] = "   End Object",
      [913] = "   Begin Object Name=\"BP_DialogueTrackActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2'\"",
      [914] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_3'\"",
      [915] = "   End Object",
      [916] = "   Begin Object Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [917] = "      FOV=40.000000",
      [918] = "      bEnableLookAt=True",
      [919] = "      LookAtTarget=(PerformerName=\"Actor3\")",
      [920] = "      OffsetZ=-25.489708",
      [921] = "      bOverride_DepthOfField=True",
      [922] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor3\")",
      [923] = "      DepthOfFieldFocalDistance=223.697495",
      [924] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [925] = "      SpawnTransform=(Rotation=(X=-0.001715,Y=-0.000469,Z=0.968161,W=-0.250322),Translation=(X=284.552566,Y=96.648827,Z=44.522990))",
      [926] = "      TrackName=\"中Actor3\"",
      [927] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [928] = "   End Object",
      [929] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3'\"",
      [930] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3.BPS_PlayAnimation_C_0'\"",
      [931] = "         AnimLibItem=(AssetID=\"Talk_Yes_B\")",
      [932] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_3'\"",
      [933] = "         OwnedEpisodeID=1",
      [934] = "         StartTime=12.250000",
      [935] = "         Duration=1.172812",
      [936] = "      End Object",
      [937] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [938] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_2'\"",
      [939] = "   End Object",
      [940] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [941] = "      EpisodeID=1",
      [942] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [943] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [944] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [945] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [946] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_4'\"",
      [947] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_5'\"",
      [948] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_6'\"",
      [949] = "   End Object",
      [950] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [951] = "      GUID=748692227",
      [952] = "      Duration=4.250000",
      [953] = "      ContentString=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [954] = "      ContentUI=\"Default\"",
      [955] = "      Talker=(PerformerName=\"Actor3\")",
      [956] = "   End Object",
      [957] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [958] = "      CameraCutDuration=4.250000",
      [959] = "      CanSkip=True",
      [960] = "      EpisodeID=1",
      [961] = "      ContentIndex=1",
      [962] = "   End Object",
      [963] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [964] = "      GUID=3341332579",
      [965] = "      ContentString=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [966] = "      ContentUI=\"Default\"",
      [967] = "      Talker=(PerformerName=\"Actor2\")",
      [968] = "   End Object",
      [969] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [970] = "      CameraCutDuration=5.000000",
      [971] = "      CanSkip=True",
      [972] = "      EpisodeID=1",
      [973] = "      ContentIndex=2",
      [974] = "   End Object",
      [975] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [976] = "      GUID=959981811",
      [977] = "      Duration=3.000000",
      [978] = "      ContentString=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [979] = "      ContentUI=\"Default\"",
      [980] = "      Talker=(PerformerName=\"Actor3\")",
      [981] = "   End Object",
      [982] = "   Begin Object Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [983] = "      CameraCutDuration=3.000000",
      [984] = "      CanSkip=True",
      [985] = "      EpisodeID=1",
      [986] = "      ContentIndex=3",
      [987] = "   End Object",
      [988] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [989] = "      GUID=885157243",
      [990] = "      Duration=3.500000",
      [991] = "      ContentString=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [992] = "      ContentUI=\"Default\"",
      [993] = "      Talker=(PerformerName=\"Actor1\")",
      [994] = "   End Object",
      [995] = "   Begin Object Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [996] = "      CameraCutDuration=3.500000",
      [997] = "      CanSkip=True",
      [998] = "      EpisodeID=1",
      [999] = "      ContentIndex=4",
      [1000] = "   End Object",
      [1001] = "   Begin Object Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [1002] = "      GUID=2433113019",
      [1003] = "      Duration=2.500000",
      [1004] = "      ContentString=\"小主人，马车正在园外等待。\"",
      [1005] = "      ContentUI=\"Default\"",
      [1006] = "      Talker=(PerformerName=\"Actor3\")",
      [1007] = "   End Object",
      [1008] = "   Begin Object Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [1009] = "      CameraCutDuration=2.500000",
      [1010] = "      CanSkip=True",
      [1011] = "      EpisodeID=1",
      [1012] = "      ContentIndex=5",
      [1013] = "   End Object",
      [1014] = "   Begin Object Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [1015] = "      GUID=2905916935",
      [1016] = "      Duration=3.000000",
      [1017] = "      ContentString=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1018] = "      ContentUI=\"Default\"",
      [1019] = "      Talker=(PerformerName=\"Actor2\")",
      [1020] = "   End Object",
      [1021] = "   Begin Object Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [1022] = "      CameraCutDuration=3.000000",
      [1023] = "      CanSkip=True",
      [1024] = "      EpisodeID=1",
      [1025] = "      ContentIndex=6",
      [1026] = "   End Object",
      [1027] = "   Begin Object Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [1028] = "      GUID=400437238",
      [1029] = "      Duration=3.500000",
      [1030] = "      ContentString=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1031] = "      ContentUI=\"Default\"",
      [1032] = "      Talker=(PerformerName=\"Actor3\")",
      [1033] = "   End Object",
      [1034] = "   Begin Object Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [1035] = "      CameraCutDuration=3.500000",
      [1036] = "      CanSkip=True",
      [1037] = "      EpisodeID=1",
      [1038] = "      ContentIndex=7",
      [1039] = "   End Object",
      [1040] = "   Begin Object Name=\"DialogueDialogueTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3'\"",
      [1041] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_0'\"",
      [1042] = "         EpisodeID=1",
      [1043] = "         ContentIndex=1",
      [1044] = "         Talker=\"Actor3\"",
      [1045] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1046] = "         SectionName=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [1047] = "         FromLineIndex=0",
      [1048] = "         LineGUIDLinked=748692227",
      [1049] = "         OwnedEpisodeID=1",
      [1050] = "         Duration=4.250000",
      [1051] = "      End Object",
      [1052] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_1'\"",
      [1053] = "         EpisodeID=1",
      [1054] = "         ContentIndex=2",
      [1055] = "         Talker=\"Actor2\"",
      [1056] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1057] = "         SectionName=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [1058] = "         FromLineIndex=1",
      [1059] = "         LineGUIDLinked=3341332579",
      [1060] = "         OwnedEpisodeID=1",
      [1061] = "         StartTime=4.250000",
      [1062] = "         Duration=5.000000",
      [1063] = "      End Object",
      [1064] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_2'\"",
      [1065] = "         EpisodeID=1",
      [1066] = "         ContentIndex=3",
      [1067] = "         Talker=\"Actor3\"",
      [1068] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1069] = "         SectionName=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [1070] = "         FromLineIndex=2",
      [1071] = "         LineGUIDLinked=959981811",
      [1072] = "         OwnedEpisodeID=1",
      [1073] = "         StartTime=9.250000",
      [1074] = "         Duration=3.000000",
      [1075] = "      End Object",
      [1076] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_3'\"",
      [1077] = "         EpisodeID=1",
      [1078] = "         ContentIndex=4",
      [1079] = "         Talker=\"Actor1\"",
      [1080] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1081] = "         SectionName=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [1082] = "         FromLineIndex=3",
      [1083] = "         LineGUIDLinked=885157243",
      [1084] = "         OwnedEpisodeID=1",
      [1085] = "         StartTime=12.250000",
      [1086] = "         Duration=3.500000",
      [1087] = "      End Object",
      [1088] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_4'\"",
      [1089] = "         EpisodeID=1",
      [1090] = "         ContentIndex=5",
      [1091] = "         Talker=\"Actor3\"",
      [1092] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1093] = "         SectionName=\"小主人，马车正在园外等待。\"",
      [1094] = "         FromLineIndex=4",
      [1095] = "         LineGUIDLinked=2433113019",
      [1096] = "         OwnedEpisodeID=1",
      [1097] = "         StartTime=15.750000",
      [1098] = "         Duration=2.500000",
      [1099] = "      End Object",
      [1100] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_5'\"",
      [1101] = "         EpisodeID=1",
      [1102] = "         ContentIndex=6",
      [1103] = "         Talker=\"Actor2\"",
      [1104] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1105] = "         SectionName=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1106] = "         FromLineIndex=5",
      [1107] = "         LineGUIDLinked=2905916935",
      [1108] = "         OwnedEpisodeID=1",
      [1109] = "         StartTime=18.250000",
      [1110] = "         Duration=3.000000",
      [1111] = "      End Object",
      [1112] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_3.BPS_Dialogue_C_6'\"",
      [1113] = "         EpisodeID=1",
      [1114] = "         ContentIndex=7",
      [1115] = "         Talker=\"Actor3\"",
      [1116] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_3'\"",
      [1117] = "         SectionName=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1118] = "         FromLineIndex=6",
      [1119] = "         LineGUIDLinked=400437238",
      [1120] = "         OwnedEpisodeID=1",
      [1121] = "         StartTime=21.250000",
      [1122] = "         Duration=3.500000",
      [1123] = "      End Object",
      [1124] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1125] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1126] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1127] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1128] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1129] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1130] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1131] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1132] = "      FromTemplate=False",
      [1133] = "   End Object",
      [1134] = "   Begin Object Name=\"DialogueStateControlTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6'\"",
      [1135] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_0'\"",
      [1136] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1137] = "         FromLineIndex=0",
      [1138] = "         LineGUIDLinked=748692227",
      [1139] = "         OwnedEpisodeID=1",
      [1140] = "         StartTime=4.150000",
      [1141] = "      End Object",
      [1142] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_1'\"",
      [1143] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1144] = "         FromLineIndex=1",
      [1145] = "         LineGUIDLinked=3341332579",
      [1146] = "         OwnedEpisodeID=1",
      [1147] = "         StartTime=9.150000",
      [1148] = "      End Object",
      [1149] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_2'\"",
      [1150] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1151] = "         FromLineIndex=2",
      [1152] = "         LineGUIDLinked=959981811",
      [1153] = "         OwnedEpisodeID=1",
      [1154] = "         StartTime=12.150000",
      [1155] = "      End Object",
      [1156] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_3'\"",
      [1157] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1158] = "         FromLineIndex=3",
      [1159] = "         LineGUIDLinked=885157243",
      [1160] = "         OwnedEpisodeID=1",
      [1161] = "         StartTime=15.650000",
      [1162] = "      End Object",
      [1163] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_4'\"",
      [1164] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1165] = "         FromLineIndex=4",
      [1166] = "         LineGUIDLinked=2433113019",
      [1167] = "         OwnedEpisodeID=1",
      [1168] = "         StartTime=18.150000",
      [1169] = "      End Object",
      [1170] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_5'\"",
      [1171] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1172] = "         FromLineIndex=5",
      [1173] = "         LineGUIDLinked=2905916935",
      [1174] = "         OwnedEpisodeID=1",
      [1175] = "         StartTime=21.150000",
      [1176] = "      End Object",
      [1177] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_6.BPS_StateControl_C_6'\"",
      [1178] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_6'\"",
      [1179] = "         FromLineIndex=6",
      [1180] = "         LineGUIDLinked=400437238",
      [1181] = "         OwnedEpisodeID=1",
      [1182] = "         StartTime=24.650000",
      [1183] = "      End Object",
      [1184] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1185] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1186] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1187] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1188] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1189] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1190] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1191] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1192] = "      FromTemplate=False",
      [1193] = "   End Object",
      [1194] = "   Begin Object Name=\"DialogueStateControlTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_2'\"",
      [1195] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1196] = "      FromTemplate=False",
      [1197] = "   End Object",
      [1198] = "   Begin Object Name=\"DialogueDialogueTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1'\"",
      [1199] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_0'\"",
      [1200] = "         EpisodeID=1",
      [1201] = "         ContentIndex=1",
      [1202] = "         Talker=\"Actor3\"",
      [1203] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1204] = "         SectionName=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [1205] = "         FromLineIndex=0",
      [1206] = "         LineGUIDLinked=748692227",
      [1207] = "         OwnedEpisodeID=1",
      [1208] = "         Duration=4.250000",
      [1209] = "      End Object",
      [1210] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_1'\"",
      [1211] = "         EpisodeID=1",
      [1212] = "         ContentIndex=2",
      [1213] = "         Talker=\"Actor2\"",
      [1214] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1215] = "         SectionName=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [1216] = "         FromLineIndex=1",
      [1217] = "         LineGUIDLinked=3341332579",
      [1218] = "         OwnedEpisodeID=1",
      [1219] = "         StartTime=4.250000",
      [1220] = "         Duration=5.000000",
      [1221] = "      End Object",
      [1222] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_2'\"",
      [1223] = "         EpisodeID=1",
      [1224] = "         ContentIndex=3",
      [1225] = "         Talker=\"Actor3\"",
      [1226] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1227] = "         SectionName=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [1228] = "         FromLineIndex=2",
      [1229] = "         LineGUIDLinked=959981811",
      [1230] = "         OwnedEpisodeID=1",
      [1231] = "         StartTime=9.250000",
      [1232] = "         Duration=3.000000",
      [1233] = "      End Object",
      [1234] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_3'\"",
      [1235] = "         EpisodeID=1",
      [1236] = "         ContentIndex=4",
      [1237] = "         Talker=\"Actor1\"",
      [1238] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1239] = "         SectionName=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [1240] = "         FromLineIndex=3",
      [1241] = "         LineGUIDLinked=885157243",
      [1242] = "         OwnedEpisodeID=1",
      [1243] = "         StartTime=12.250000",
      [1244] = "         Duration=3.500000",
      [1245] = "      End Object",
      [1246] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_4'\"",
      [1247] = "         EpisodeID=1",
      [1248] = "         ContentIndex=5",
      [1249] = "         Talker=\"Actor3\"",
      [1250] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1251] = "         SectionName=\"小主人，马车正在园外等待。\"",
      [1252] = "         FromLineIndex=4",
      [1253] = "         LineGUIDLinked=2433113019",
      [1254] = "         OwnedEpisodeID=1",
      [1255] = "         StartTime=15.750000",
      [1256] = "         Duration=2.500000",
      [1257] = "      End Object",
      [1258] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_5'\"",
      [1259] = "         EpisodeID=1",
      [1260] = "         ContentIndex=6",
      [1261] = "         Talker=\"Actor2\"",
      [1262] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1263] = "         SectionName=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1264] = "         FromLineIndex=5",
      [1265] = "         LineGUIDLinked=2905916935",
      [1266] = "         OwnedEpisodeID=1",
      [1267] = "         StartTime=18.250000",
      [1268] = "         Duration=3.000000",
      [1269] = "      End Object",
      [1270] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_1.BPS_Dialogue_C_6'\"",
      [1271] = "         EpisodeID=1",
      [1272] = "         ContentIndex=7",
      [1273] = "         Talker=\"Actor3\"",
      [1274] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_1'\"",
      [1275] = "         SectionName=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1276] = "         FromLineIndex=6",
      [1277] = "         LineGUIDLinked=400437238",
      [1278] = "         OwnedEpisodeID=1",
      [1279] = "         StartTime=21.250000",
      [1280] = "         Duration=3.500000",
      [1281] = "      End Object",
      [1282] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1283] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1284] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1285] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1286] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1287] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1288] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1289] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1290] = "      FromTemplate=False",
      [1291] = "   End Object",
      [1292] = "   Begin Object Name=\"DialogueStateControlTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3'\"",
      [1293] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_0'\"",
      [1294] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1295] = "         FromLineIndex=0",
      [1296] = "         LineGUIDLinked=748692227",
      [1297] = "         OwnedEpisodeID=1",
      [1298] = "         StartTime=4.150000",
      [1299] = "         Duration=0.100000",
      [1300] = "      End Object",
      [1301] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_1'\"",
      [1302] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1303] = "         FromLineIndex=1",
      [1304] = "         LineGUIDLinked=3341332579",
      [1305] = "         OwnedEpisodeID=1",
      [1306] = "         StartTime=9.150000",
      [1307] = "         Duration=0.100000",
      [1308] = "      End Object",
      [1309] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_2'\"",
      [1310] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1311] = "         FromLineIndex=2",
      [1312] = "         LineGUIDLinked=959981811",
      [1313] = "         OwnedEpisodeID=1",
      [1314] = "         StartTime=12.150000",
      [1315] = "         Duration=0.100000",
      [1316] = "      End Object",
      [1317] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_3'\"",
      [1318] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1319] = "         FromLineIndex=3",
      [1320] = "         LineGUIDLinked=885157243",
      [1321] = "         OwnedEpisodeID=1",
      [1322] = "         StartTime=15.650000",
      [1323] = "         Duration=0.100000",
      [1324] = "      End Object",
      [1325] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_4'\"",
      [1326] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1327] = "         FromLineIndex=4",
      [1328] = "         LineGUIDLinked=2433113019",
      [1329] = "         OwnedEpisodeID=1",
      [1330] = "         StartTime=18.150000",
      [1331] = "         Duration=0.100000",
      [1332] = "      End Object",
      [1333] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_5'\"",
      [1334] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1335] = "         FromLineIndex=5",
      [1336] = "         LineGUIDLinked=2905916935",
      [1337] = "         OwnedEpisodeID=1",
      [1338] = "         StartTime=21.150000",
      [1339] = "         Duration=0.100000",
      [1340] = "      End Object",
      [1341] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_3.BPS_StateControl_C_6'\"",
      [1342] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_3'\"",
      [1343] = "         FromLineIndex=6",
      [1344] = "         LineGUIDLinked=400437238",
      [1345] = "         OwnedEpisodeID=1",
      [1346] = "         StartTime=24.650000",
      [1347] = "         Duration=0.100000",
      [1348] = "      End Object",
      [1349] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1350] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1351] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1352] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1353] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1354] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1355] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1356] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1357] = "      FromTemplate=False",
      [1358] = "   End Object",
      [1359] = "   Begin Object Name=\"DialogueStateControlTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_4'\"",
      [1360] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1361] = "      FromTemplate=False",
      [1362] = "   End Object",
      [1363] = "   Begin Object Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [1364] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [1365] = "         EpisodeID=1",
      [1366] = "         ContentIndex=1",
      [1367] = "         Talker=\"Actor3\"",
      [1368] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1369] = "         SectionName=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [1370] = "         FromLineIndex=0",
      [1371] = "         LineGUIDLinked=748692227",
      [1372] = "         OwnedEpisodeID=1",
      [1373] = "         Duration=4.250000",
      [1374] = "      End Object",
      [1375] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [1376] = "         EpisodeID=1",
      [1377] = "         ContentIndex=2",
      [1378] = "         Talker=\"Actor2\"",
      [1379] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1380] = "         SectionName=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [1381] = "         FromLineIndex=1",
      [1382] = "         LineGUIDLinked=3341332579",
      [1383] = "         OwnedEpisodeID=1",
      [1384] = "         StartTime=4.250000",
      [1385] = "         Duration=5.000000",
      [1386] = "      End Object",
      [1387] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [1388] = "         EpisodeID=1",
      [1389] = "         ContentIndex=3",
      [1390] = "         Talker=\"Actor3\"",
      [1391] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1392] = "         SectionName=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [1393] = "         FromLineIndex=2",
      [1394] = "         LineGUIDLinked=959981811",
      [1395] = "         OwnedEpisodeID=1",
      [1396] = "         StartTime=9.250000",
      [1397] = "         Duration=3.000000",
      [1398] = "      End Object",
      [1399] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [1400] = "         EpisodeID=1",
      [1401] = "         ContentIndex=4",
      [1402] = "         Talker=\"Actor1\"",
      [1403] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1404] = "         SectionName=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [1405] = "         FromLineIndex=3",
      [1406] = "         LineGUIDLinked=885157243",
      [1407] = "         OwnedEpisodeID=1",
      [1408] = "         StartTime=12.250000",
      [1409] = "         Duration=3.500000",
      [1410] = "      End Object",
      [1411] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [1412] = "         EpisodeID=1",
      [1413] = "         ContentIndex=5",
      [1414] = "         Talker=\"Actor3\"",
      [1415] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1416] = "         SectionName=\"小主人，马车正在园外等待。\"",
      [1417] = "         FromLineIndex=4",
      [1418] = "         LineGUIDLinked=2433113019",
      [1419] = "         OwnedEpisodeID=1",
      [1420] = "         StartTime=15.750000",
      [1421] = "         Duration=2.500000",
      [1422] = "      End Object",
      [1423] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [1424] = "         EpisodeID=1",
      [1425] = "         ContentIndex=6",
      [1426] = "         Talker=\"Actor2\"",
      [1427] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1428] = "         SectionName=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1429] = "         FromLineIndex=5",
      [1430] = "         LineGUIDLinked=2905916935",
      [1431] = "         OwnedEpisodeID=1",
      [1432] = "         StartTime=18.250000",
      [1433] = "         Duration=3.000000",
      [1434] = "      End Object",
      [1435] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [1436] = "         EpisodeID=1",
      [1437] = "         ContentIndex=7",
      [1438] = "         Talker=\"Actor3\"",
      [1439] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1440] = "         SectionName=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1441] = "         FromLineIndex=6",
      [1442] = "         LineGUIDLinked=400437238",
      [1443] = "         OwnedEpisodeID=1",
      [1444] = "         StartTime=21.250000",
      [1445] = "         Duration=3.500000",
      [1446] = "      End Object",
      [1447] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1448] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1449] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1450] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1451] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1452] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1453] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1454] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1455] = "      FromTemplate=False",
      [1456] = "   End Object",
      [1457] = "   Begin Object Name=\"DialogueStateControlTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5'\"",
      [1458] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_0'\"",
      [1459] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1460] = "         FromLineIndex=0",
      [1461] = "         LineGUIDLinked=748692227",
      [1462] = "         OwnedEpisodeID=1",
      [1463] = "         StartTime=4.150000",
      [1464] = "      End Object",
      [1465] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_1'\"",
      [1466] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1467] = "         FromLineIndex=1",
      [1468] = "         LineGUIDLinked=3341332579",
      [1469] = "         OwnedEpisodeID=1",
      [1470] = "         StartTime=9.150000",
      [1471] = "      End Object",
      [1472] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_2'\"",
      [1473] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1474] = "         FromLineIndex=2",
      [1475] = "         LineGUIDLinked=959981811",
      [1476] = "         OwnedEpisodeID=1",
      [1477] = "         StartTime=12.150000",
      [1478] = "      End Object",
      [1479] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_3'\"",
      [1480] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1481] = "         FromLineIndex=3",
      [1482] = "         LineGUIDLinked=885157243",
      [1483] = "         OwnedEpisodeID=1",
      [1484] = "         StartTime=15.650000",
      [1485] = "      End Object",
      [1486] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_4'\"",
      [1487] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1488] = "         FromLineIndex=4",
      [1489] = "         LineGUIDLinked=2433113019",
      [1490] = "         OwnedEpisodeID=1",
      [1491] = "         StartTime=18.150000",
      [1492] = "      End Object",
      [1493] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_5'\"",
      [1494] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1495] = "         FromLineIndex=5",
      [1496] = "         LineGUIDLinked=2905916935",
      [1497] = "         OwnedEpisodeID=1",
      [1498] = "         StartTime=21.150000",
      [1499] = "      End Object",
      [1500] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_5.BPS_StateControl_C_6'\"",
      [1501] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_5'\"",
      [1502] = "         FromLineIndex=6",
      [1503] = "         LineGUIDLinked=400437238",
      [1504] = "         OwnedEpisodeID=1",
      [1505] = "         StartTime=24.650000",
      [1506] = "      End Object",
      [1507] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1508] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1509] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1510] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1511] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1512] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1513] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1514] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1515] = "      FromTemplate=False",
      [1516] = "   End Object",
      [1517] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [1518] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1519] = "      FromTemplate=False",
      [1520] = "   End Object",
      [1521] = "   Begin Object Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [1522] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_0'\"",
      [1523] = "         EpisodeID=1",
      [1524] = "         ContentIndex=1",
      [1525] = "         Talker=\"Actor3\"",
      [1526] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1527] = "         SectionName=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [1528] = "         FromLineIndex=0",
      [1529] = "         LineGUIDLinked=748692227",
      [1530] = "         OwnedEpisodeID=1",
      [1531] = "         Duration=4.250000",
      [1532] = "      End Object",
      [1533] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_1'\"",
      [1534] = "         EpisodeID=1",
      [1535] = "         ContentIndex=2",
      [1536] = "         Talker=\"Actor2\"",
      [1537] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1538] = "         SectionName=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [1539] = "         FromLineIndex=1",
      [1540] = "         LineGUIDLinked=3341332579",
      [1541] = "         OwnedEpisodeID=1",
      [1542] = "         StartTime=4.250000",
      [1543] = "         Duration=5.000000",
      [1544] = "      End Object",
      [1545] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_2'\"",
      [1546] = "         EpisodeID=1",
      [1547] = "         ContentIndex=3",
      [1548] = "         Talker=\"Actor3\"",
      [1549] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1550] = "         SectionName=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [1551] = "         FromLineIndex=2",
      [1552] = "         LineGUIDLinked=959981811",
      [1553] = "         OwnedEpisodeID=1",
      [1554] = "         StartTime=9.250000",
      [1555] = "         Duration=3.000000",
      [1556] = "      End Object",
      [1557] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_3'\"",
      [1558] = "         EpisodeID=1",
      [1559] = "         ContentIndex=4",
      [1560] = "         Talker=\"Actor1\"",
      [1561] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1562] = "         SectionName=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [1563] = "         FromLineIndex=3",
      [1564] = "         LineGUIDLinked=885157243",
      [1565] = "         OwnedEpisodeID=1",
      [1566] = "         StartTime=12.250000",
      [1567] = "         Duration=3.500000",
      [1568] = "      End Object",
      [1569] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_4'\"",
      [1570] = "         EpisodeID=1",
      [1571] = "         ContentIndex=5",
      [1572] = "         Talker=\"Actor3\"",
      [1573] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1574] = "         SectionName=\"小主人，马车正在园外等待。\"",
      [1575] = "         FromLineIndex=4",
      [1576] = "         LineGUIDLinked=2433113019",
      [1577] = "         OwnedEpisodeID=1",
      [1578] = "         StartTime=15.750000",
      [1579] = "         Duration=2.500000",
      [1580] = "      End Object",
      [1581] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_5'\"",
      [1582] = "         EpisodeID=1",
      [1583] = "         ContentIndex=6",
      [1584] = "         Talker=\"Actor2\"",
      [1585] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1586] = "         SectionName=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1587] = "         FromLineIndex=5",
      [1588] = "         LineGUIDLinked=2905916935",
      [1589] = "         OwnedEpisodeID=1",
      [1590] = "         StartTime=18.250000",
      [1591] = "         Duration=3.000000",
      [1592] = "      End Object",
      [1593] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_6'\"",
      [1594] = "         EpisodeID=1",
      [1595] = "         ContentIndex=7",
      [1596] = "         Talker=\"Actor3\"",
      [1597] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1598] = "         SectionName=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1599] = "         FromLineIndex=6",
      [1600] = "         LineGUIDLinked=400437238",
      [1601] = "         OwnedEpisodeID=1",
      [1602] = "         StartTime=21.250000",
      [1603] = "         Duration=3.500000",
      [1604] = "      End Object",
      [1605] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1606] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1607] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1608] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1609] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1610] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1611] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1612] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1613] = "      FromTemplate=False",
      [1614] = "   End Object",
      [1615] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [1616] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [1617] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1618] = "         FromLineIndex=0",
      [1619] = "         LineGUIDLinked=748692227",
      [1620] = "         OwnedEpisodeID=1",
      [1621] = "         StartTime=4.150000",
      [1622] = "         Duration=0.100000",
      [1623] = "      End Object",
      [1624] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_1'\"",
      [1625] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1626] = "         FromLineIndex=1",
      [1627] = "         LineGUIDLinked=3341332579",
      [1628] = "         OwnedEpisodeID=1",
      [1629] = "         StartTime=9.150000",
      [1630] = "         Duration=0.100000",
      [1631] = "      End Object",
      [1632] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_2'\"",
      [1633] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1634] = "         FromLineIndex=2",
      [1635] = "         LineGUIDLinked=959981811",
      [1636] = "         OwnedEpisodeID=1",
      [1637] = "         StartTime=12.150000",
      [1638] = "         Duration=0.100000",
      [1639] = "      End Object",
      [1640] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_3'\"",
      [1641] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1642] = "         FromLineIndex=3",
      [1643] = "         LineGUIDLinked=885157243",
      [1644] = "         OwnedEpisodeID=1",
      [1645] = "         StartTime=15.650000",
      [1646] = "         Duration=0.100000",
      [1647] = "      End Object",
      [1648] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_4'\"",
      [1649] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1650] = "         FromLineIndex=4",
      [1651] = "         LineGUIDLinked=2433113019",
      [1652] = "         OwnedEpisodeID=1",
      [1653] = "         StartTime=18.150000",
      [1654] = "         Duration=0.100000",
      [1655] = "      End Object",
      [1656] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_5'\"",
      [1657] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1658] = "         FromLineIndex=5",
      [1659] = "         LineGUIDLinked=2905916935",
      [1660] = "         OwnedEpisodeID=1",
      [1661] = "         StartTime=21.150000",
      [1662] = "         Duration=0.100000",
      [1663] = "      End Object",
      [1664] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_6'\"",
      [1665] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1666] = "         FromLineIndex=6",
      [1667] = "         LineGUIDLinked=400437238",
      [1668] = "         OwnedEpisodeID=1",
      [1669] = "         StartTime=24.650000",
      [1670] = "         Duration=0.100000",
      [1671] = "      End Object",
      [1672] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1673] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1674] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1675] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1676] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1677] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1678] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1679] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1680] = "      FromTemplate=False",
      [1681] = "   End Object",
      [1682] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [1683] = "      EpisodeID=1",
      [1684] = "   End Object",
      [1685] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [1686] = "      EpisodeID=1",
      [1687] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_7'\"",
      [1688] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_8'\"",
      [1689] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_9'\"",
      [1690] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_10'\"",
      [1691] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_11'\"",
      [1692] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_12'\"",
      [1693] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_13'\"",
      [1694] = "   End Object",
      [1695] = "   Begin Object Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [1696] = "      EpisodeID=1",
      [1697] = "      ContentIndex=1",
      [1698] = "      GUID=748692227",
      [1699] = "      UniqueID=42DAA47FAC4D439D8378F7C9759EEF0C",
      [1700] = "      Duration=4.250000",
      [1701] = "      ContentString=\"小主人，夫人说得没错，您果然在这儿！——这位{{先生|小姐}}是？\"",
      [1702] = "      ContentUI=\"Default\"",
      [1703] = "      Talker=(PerformerName=\"Actor3\")",
      [1704] = "   End Object",
      [1705] = "   Begin Object Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [1706] = "   End Object",
      [1707] = "   Begin Object Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [1708] = "      EpisodeID=1",
      [1709] = "      ContentIndex=2",
      [1710] = "      GUID=3341332579",
      [1711] = "      UniqueID=E57BABE8B5154CACA26EB0FCF6DCEAEF",
      [1712] = "      ContentString=\"我的新朋友！{{PlayerName}}帮我找到了道格的墓碑，还赶走了纠缠道格的怪物！\"",
      [1713] = "      ContentUI=\"Default\"",
      [1714] = "      Talker=(PerformerName=\"Actor2\")",
      [1715] = "   End Object",
      [1716] = "   Begin Object Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [1717] = "   End Object",
      [1718] = "   Begin Object Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [1719] = "      EpisodeID=1",
      [1720] = "      ContentIndex=3",
      [1721] = "      GUID=959981811",
      [1722] = "      UniqueID=9C802B4811CF42E7A6AF08CC39D14296",
      [1723] = "      Duration=3.000000",
      [1724] = "      ContentString=\"感谢您对小主人的照料，我代表我的女主人向您致谢。\"",
      [1725] = "      ContentUI=\"Default\"",
      [1726] = "      Talker=(PerformerName=\"Actor3\")",
      [1727] = "   End Object",
      [1728] = "   Begin Object Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [1729] = "   End Object",
      [1730] = "   Begin Object Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [1731] = "      EpisodeID=1",
      [1732] = "      ContentIndex=4",
      [1733] = "      GUID=885157243",
      [1734] = "      UniqueID=1B00BE5F004C43D78C002F009B8CEA6F",
      [1735] = "      Duration=3.500000",
      [1736] = "      ContentString=\"莱昂是一个可爱的孩子，他高贵的品质像宝石一样耀眼。\"",
      [1737] = "      ContentUI=\"Default\"",
      [1738] = "      Talker=(PerformerName=\"Actor1\")",
      [1739] = "   End Object",
      [1740] = "   Begin Object Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [1741] = "   End Object",
      [1742] = "   Begin Object Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [1743] = "      EpisodeID=1",
      [1744] = "      ContentIndex=5",
      [1745] = "      GUID=2433113019",
      [1746] = "      UniqueID=A505461D1D0647508B79137E6F84441A",
      [1747] = "      Duration=2.500000",
      [1748] = "      ContentString=\"小主人，马车正在园外等待。\"",
      [1749] = "      ContentUI=\"Default\"",
      [1750] = "      Talker=(PerformerName=\"Actor3\")",
      [1751] = "   End Object",
      [1752] = "   Begin Object Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [1753] = "   End Object",
      [1754] = "   Begin Object Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [1755] = "      EpisodeID=1",
      [1756] = "      ContentIndex=6",
      [1757] = "      GUID=2905916935",
      [1758] = "      UniqueID=06898A75A4B348E4BB0C5165AB58B37B",
      [1759] = "      Duration=3.000000",
      [1760] = "      ContentString=\"我想再和道格说几句话，求您再给我几分钟......\"",
      [1761] = "      ContentUI=\"Default\"",
      [1762] = "      Talker=(PerformerName=\"Actor2\")",
      [1763] = "   End Object",
      [1764] = "   Begin Object Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [1765] = "   End Object",
      [1766] = "   Begin Object Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [1767] = "      EpisodeID=1",
      [1768] = "      ContentIndex=7",
      [1769] = "      GUID=400437238",
      [1770] = "      UniqueID=1277797F20554948A1322CA3530FD5B9",
      [1771] = "      Duration=3.500000",
      [1772] = "      ContentString=\"夫人请您立刻回家。墓园阴冷，您不便长时间待在这里。\"",
      [1773] = "      ContentUI=\"Default\"",
      [1774] = "      Talker=(PerformerName=\"Actor3\")",
      [1775] = "   End Object",
      [1776] = "   Begin Object Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [1777] = "   End Object",
      [1778] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [1779] = "   PreLoadArray(1)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C\"",
      [1780] = "   PreLoadArray(2)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [1781] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=12000011),IdleAnimation=(AssetID=\"Idle\"),bIsPlayer=True)",
      [1782] = "   ActorInfos(1)=(PerformerName=\"Actor2\",AppearanceID=(ApperanceID=7201007),IdleAnimation=(AssetID=\"Idle_Rich\"),InsID=\"980559969\")",
      [1783] = "   ActorInfos(2)=(PerformerName=\"Actor3\",AppearanceID=(ApperanceID=7201010),IdleAnimation=(AssetID=\"Idle\"),InsID=\"2345769968\")",
      [1784] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/ThreePeopleDialogue.ThreePeopleDialogue'\"",
      [1785] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_1'\"",
      [1786] = "   StoryLineID=********",
      [1787] = "   Episodes(0)=(EpisodeID=1,Duration=24.750000,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_5'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_2'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'BP_DialogueCameraCut_C_2'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueCameraTrack_12'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_2'\"))",
      [1788] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_1'\"",
      [1789] = "   PerformerList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_2'\"",
      [1790] = "   PerformerList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_3'\"",
      [1791] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_6'\"",
      [1792] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_7'\"",
      [1793] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_8'\"",
      [1794] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_9'\"",
      [1795] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_10'\"",
      [1796] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_11'\"",
      [1797] = "   CameraList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_12'\"",
      [1798] = "   CameraList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_13'\"",
      [1799] = "   CameraList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_14'\"",
      [1800] = "   CameraList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_15'\"",
      [1801] = "   CameraList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_16'\"",
      [1802] = "   CameraList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_17'\"",
      [1803] = "   CameraList(12)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_18'\"",
      [1804] = "   CameraList(13)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_19'\"",
      [1805] = "   CameraList(14)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_20'\"",
      [1806] = "   CameraList(15)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_21'\"",
      [1807] = "   NewEntityList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_0'\"",
      [1808] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [1809] = "End Object",
    },
  },
}