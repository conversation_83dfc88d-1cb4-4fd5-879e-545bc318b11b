return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 12000010,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["AppearanceID"] = 7201007,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["AppearanceID"] = 7201006,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Performer1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["AppearanceID"] = 7201007,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Performer2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9364,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.3509,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -51042.7651,
          ["Y"] = -44307.5013,
          ["Z"] = -830.4187,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 5,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 1429.3445,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Performer1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8334,
          ["X"] = -0.035,
          ["Y"] = 0.0076,
          ["Z"] = -0.5515,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -418.633,
          ["Y"] = 1098.5308,
          ["Z"] = 105.6774,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 10,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 678.287,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Performer2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 69.5688,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1532,
          ["X"] = -0.0079,
          ["Y"] = 0.0054,
          ["Z"] = 0.9881,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -120.2204,
          ["Y"] = 1933.2637,
          ["Z"] = 69.7219,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4552,
          ["X"] = -0.0342,
          ["Y"] = 0.0175,
          ["Z"] = 0.8896,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 338.3511,
          ["Y"] = -380.7229,
          ["Z"] = 173.8493,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 250,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_4",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6035,
          ["X"] = -0.0294,
          ["Y"] = 0.0417,
          ["Z"] = 0.7957,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 147.1824,
          ["Y"] = -304.9609,
          ["Z"] = 208.5901,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_5",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.413,
          ["X"] = 0.0143,
          ["Y"] = 0.0065,
          ["Z"] = -0.9106,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 896.4804,
          ["Y"] = 962.637,
          ["Z"] = 180.8768,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_6",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8736,
          ["X"] = 0.0085,
          ["Y"] = 0.0152,
          ["Z"] = -0.4863,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -217.3679,
          ["Y"] = 438.3493,
          ["Z"] = 131.6672,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = false,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 15,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 589.2331,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Performer1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_7",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6743,
          ["X"] = -0.0463,
          ["Y"] = -0.0424,
          ["Z"] = -0.7358,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 189.8892,
          ["Y"] = 451.7878,
          ["Z"] = 97.6505,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7424,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_8",
      ["OffsetZ"] = 15.7478,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2648,
          ["X"] = -0.005,
          ["Y"] = -0.019,
          ["Z"] = 0.9641,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 209.6946,
          ["Y"] = -111.0728,
          ["Z"] = 219.4567,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 124.7191,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_9",
      ["OffsetZ"] = 4.5444,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.108,
          ["X"] = -0.0104,
          ["Y"] = 0.0011,
          ["Z"] = 0.9941,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 92.7315,
          ["Y"] = -15.9797,
          ["Z"] = 74.4835,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 99.6247,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_10",
      ["OffsetZ"] = -0.4907,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4679,
          ["X"] = 0.0123,
          ["Y"] = -0.0065,
          ["Z"] = 0.8837,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 53.9049,
          ["Y"] = -74.4229,
          ["Z"] = 69.4485,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 228.636,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_11",
      ["OffsetZ"] = -5.2407,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3651,
          ["X"] = -0.0596,
          ["Y"] = 0.0025,
          ["Z"] = 0.929,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -221.9626,
          ["Y"] = 736.2956,
          ["Z"] = 146.2779,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 198.2129,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_12",
      ["OffsetZ"] = 11.9924,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2093,
          ["X"] = -0.0512,
          ["Y"] = -0.011,
          ["Z"] = 0.9764,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 185.7302,
          ["Y"] = 59.4629,
          ["Z"] = 81.8373,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 155.931,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_13",
      ["OffsetZ"] = 4.7219,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1097,
          ["X"] = 0.0035,
          ["Y"] = 0.0004,
          ["Z"] = -0.994,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 99.4334,
          ["Y"] = 22.5008,
          ["Z"] = 74.5134,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 99.1662,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_14",
      ["OffsetZ"] = 3.5064,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4242,
          ["X"] = 0.0047,
          ["Y"] = 0.0022,
          ["Z"] = -0.9056,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 62.7677,
          ["Y"] = 76.232,
          ["Z"] = 73.2979,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 413.3839,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor2",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_15",
      ["OffsetZ"] = -9.7787,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3228,
          ["X"] = -0.0124,
          ["Y"] = 0.0082,
          ["Z"] = 0.9464,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -174.3892,
          ["Y"] = 775.535,
          ["Z"] = 125.1634,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 185.7757,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_16",
      ["OffsetZ"] = -14.1812,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1976,
          ["X"] = -0.012,
          ["Y"] = -0.0024,
          ["Z"] = -0.9802,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 176.8058,
          ["Y"] = 52.9797,
          ["Z"] = 68.8251,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [18] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 111.2802,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_17",
      ["OffsetZ"] = 0.062,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3519,
          ["X"] = -0.0098,
          ["Y"] = -0.0037,
          ["Z"] = -0.936,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 86.3025,
          ["Y"] = 68.0752,
          ["Z"] = 83.0849,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [19] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 107.9293,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_18",
      ["OffsetZ"] = 6.077,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0697,
          ["X"] = 0.0226,
          ["Y"] = 0.0016,
          ["Z"] = -0.9973,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.751,
          ["Y"] = 12.2958,
          ["Z"] = 89.0811,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [20] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 247.0631,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_19",
      ["OffsetZ"] = -12.5409,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3071,
          ["X"] = 0.0366,
          ["Y"] = 0.0118,
          ["Z"] = -0.9509,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 215.8233,
          ["Y"] = 117.9775,
          ["Z"] = 70.4883,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [21] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 172.1966,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 20,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_20",
      ["OffsetZ"] = -8.1054,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.945,
          ["X"] = 0.0068,
          ["Y"] = 0.0985,
          ["Z"] = -0.3119,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -573.3528,
          ["Y"] = 1108.9303,
          ["Z"] = 181.2196,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [22] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 103.9644,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_21",
      ["OffsetZ"] = 2.9312,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3971,
          ["X"] = -0.0017,
          ["Y"] = 0.0007,
          ["Z"] = -0.9178,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 75.0018,
          ["Y"] = -71.3756,
          ["Z"] = 86.2462,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [23] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 106.734,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_22",
      ["OffsetZ"] = 6.0496,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.1719,
          ["X"] = 0.0155,
          ["Y"] = -0.0027,
          ["Z"] = -0.985,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 101.4203,
          ["Y"] = -31.6774,
          ["Z"] = 88.4598,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [24] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 272.3009,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_23",
      ["OffsetZ"] = -12.4972,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.3501,
          ["X"] = 0.0294,
          ["Y"] = -0.011,
          ["Z"] = -0.9362,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 230.6554,
          ["Y"] = -143.7674,
          ["Z"] = 69.913,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "反中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [25] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_30",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9847,
          ["X"] = 0.003,
          ["Y"] = -0.0172,
          ["Z"] = 0.1736,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -46.0794,
          ["Y"] = -44.8331,
          ["Z"] = 168.4331,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [26] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_31",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9622,
          ["X"] = -0.0147,
          ["Y"] = 0.053,
          ["Z"] = 0.2668,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -96.7561,
          ["Y"] = -92.2141,
          ["Z"] = 190.0407,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [27] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 37.4,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_32",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9086,
          ["X"] = -0.0228,
          ["Y"] = 0.05,
          ["Z"] = 0.4141,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -103.2555,
          ["Y"] = -193.4122,
          ["Z"] = 191.999,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor1中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [28] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_33",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1736,
          ["X"] = 0.0172,
          ["Y"] = -0.003,
          ["Z"] = 0.9847,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 155.1786,
          ["Y"] = -46.0549,
          ["Z"] = 177.3882,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2正面",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [29] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 30,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_34",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2587,
          ["X"] = -0.0253,
          ["Y"] = 0.0068,
          ["Z"] = 0.9656,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 204.5794,
          ["Y"] = -95.0451,
          ["Z"] = 186.3471,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2近景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [30] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 37.4,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_35",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3419,
          ["X"] = -0.0205,
          ["Y"] = 0.0075,
          ["Z"] = 0.9395,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 240.175,
          ["Y"] = -159.9518,
          ["Z"] = 182.23,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Actor2中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
  },
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 38.9983,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 362430836,
                ["B"] = 1146045357,
                ["C"] = -1770262156,
                ["D"] = -779627244,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_18",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 3.4,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1438810258,
                ["B"] = 1512063039,
                ["C"] = -1419074258,
                ["D"] = 805760716,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_19",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5.3707,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1358424546,
                ["B"] = -1150202314,
                ["C"] = -1860959986,
                ["D"] = -1182134299,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_20",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 13.108,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [4] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1968123578,
                ["B"] = -1272165338,
                ["C"] = -1465526630,
                ["D"] = 1815675122,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_21",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 16.108,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [5] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1300489842,
                ["B"] = -1096137562,
                ["C"] = -1210590021,
                ["D"] = 1731957085,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_22",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 19.608,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [6] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1922608014,
                ["B"] = -2001649062,
                ["C"] = -1538699229,
                ["D"] = -655222870,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_23",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 24.608,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [7] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -386276187,
                ["B"] = -843756244,
                ["C"] = -1625997110,
                ["D"] = -2073306486,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_24",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 28.608,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [8] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 240919350,
                ["B"] = 777208513,
                ["C"] = -1169705660,
                ["D"] = -988114284,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_25",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 32.108,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [9] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 8,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1795764542,
                ["B"] = -418689356,
                ["C"] = -1891565598,
                ["D"] = -1358673014,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_26",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 35.108,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_1",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["AsideID"] = 60015026,
              ["BlackScreenType"] = 0,
              ["Duration"] = 2.9,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["NeedFadeIn"] = true,
              ["NeedFadeOut"] = true,
              ["NeedPause"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C",
              ["ObjectName"] = "BPS_BlackScreenText_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5.4707,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C",
          ["ObjectName"] = "BP_DialogueTrackBlackScreenText_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C",
          ["TrackName"] = "黑屏白字",
        },
        [3] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C",
          ["ObjectName"] = "BP_DialogueTrackLookAt_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_LookAt.BPS_LookAt_C",
          ["TrackName"] = "LookAt",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 5.4707,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1027611070,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1近景",
              ["StartTime"] = 0,
              ["TargetCamera"] = "过肩Actor2",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 4.8373,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 2501928711,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_11",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1正面",
              ["StartTime"] = 8.3707,
              ["TargetCamera"] = "反过肩Actor1",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 3,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 4,
              ["CameraBreathType"] = 4,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 2634839394,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_12",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2正面",
              ["StartTime"] = 13.208,
              ["TargetCamera"] = "远景",
              ["bConstant"] = true,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 3250966899,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_13",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2近景",
              ["StartTime"] = 16.208,
              ["TargetCamera"] = "反中01Actor2",
              ["bConstant"] = true,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 224323094,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_14",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1正面",
              ["StartTime"] = 19.708,
              ["TargetCamera"] = "反过肩Actor1",
              ["bConstant"] = true,
            },
            [6] = {
              ["BreathAttenuation"] = 4,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 100,
              ["BreathSpeed"] = 1.5,
              ["CameraBreathType"] = 8,
              ["CameraName"] = "None",
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 399312228,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_15",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor1正面",
              ["StartTime"] = 24.708,
              ["TargetCamera"] = "反平视",
              ["bConstant"] = true,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 542530340,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_16",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2正面",
              ["StartTime"] = 28.708,
              ["TargetCamera"] = "反中01Actor2",
              ["bConstant"] = true,
            },
            [8] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 263855788,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_17",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2正面",
              ["StartTime"] = 32.208,
              ["TargetCamera"] = "反中01Actor2",
              ["bConstant"] = true,
            },
            [9] = {
              ["BreathAttenuation"] = 3,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 8,
              ["CameraBreathType"] = 6,
              ["CameraName"] = "None",
              ["Duration"] = 3.7775,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1843316958,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_18",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Actor2近景",
              ["StartTime"] = 35.208,
              ["TargetCamera"] = "全景",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C",
          ["ObjectName"] = "BP_DialogueCameraCut_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueShot.BPS_DialogueShot_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "反远景",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_6",
              ["Parent"] = "锚点",
              ["TrackName"] = "反全景",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_7",
              ["Parent"] = "锚点",
              ["TrackName"] = "反平视",
              ["bAutoCameraTrack"] = false,
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反过肩Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_20",
              ["Parent"] = "锚点",
              ["TrackName"] = "反过肩Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "反中01Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_15",
              ["Parent"] = "锚点",
              ["TrackName"] = "反中01Actor2",
              ["bAutoCameraTrack"] = false,
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中01Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_11",
              ["Parent"] = "锚点",
              ["TrackName"] = "中01Actor2",
              ["bAutoCameraTrack"] = false,
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "过肩Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_8",
              ["Parent"] = "锚点",
              ["TrackName"] = "过肩Actor2",
              ["bAutoCameraTrack"] = false,
            },
            [12] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Talk_Yes_A",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.2196,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 2526149507,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 4.2512,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Big_Sigh",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.145,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 28.708,
                      ["bConstant"] = true,
                    },
                    [3] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Salute",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.0344,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 32.208,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_1",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.9189,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "芬尼斯转移1",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 6.5166,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_1",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5565,
                      ["Target"] = "Actor1",
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 2634839394,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 13.208,
                      ["Target"] = "Performer1",
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 3250966899,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 17.5787,
                      ["Target"] = "Actor1",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["ObjectName"] = "BP_DialogueTrackActorLookAt_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
                [4] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.8021,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5686,
                      ["Target"] = "Actor1",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_2",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [5] = {
                  ["ActionSections"] = {
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["ObjectName"] = "BP_DialogueTrackVisible_C_0",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_9",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_10",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_12",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_13",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_14",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "反近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [13] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Short_Talk2",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 4.2634,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Short_Talk2",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.5432,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 19.708,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "玩家转移1",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 6.3504,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_0",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_2",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5427,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 2634839394,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 13.208,
                      ["Target"] = "Performer1",
                      ["bConstant"] = false,
                    },
                    [3] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 3250966899,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["ObjectName"] = "BPS_ActorLookAt_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 16.208,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["ObjectName"] = "BP_DialogueTrackActorLookAt_C_2",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
                [4] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.842,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.5287,
                      ["Target"] = "Actor2",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_1",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_16",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_17",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_18",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_19",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_21",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [6] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_22",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [7] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "反中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueCameraTrack_23",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "反中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueActorTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [14] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_24",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1正面",
              ["bAutoCameraTrack"] = true,
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_25",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1近景",
              ["bAutoCameraTrack"] = true,
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_26",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1中景",
              ["bAutoCameraTrack"] = true,
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2正面",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_27",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2正面",
              ["bAutoCameraTrack"] = true,
            },
            [18] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2近景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_28",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2近景",
              ["bAutoCameraTrack"] = true,
            },
            [19] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor2中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueCameraTrack_29",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2中景",
              ["bAutoCameraTrack"] = true,
            },
            [20] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "芬尼斯转移1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueRoutePointTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "芬尼斯转移1",
            },
            [21] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "玩家转移1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueRoutePointTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "玩家转移1",
            },
            [22] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "莱昂转移1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueRoutePointTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "莱昂转移1",
            },
            [23] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "芬尼斯跑路1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["ObjectName"] = "DialogueRoutePointTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "芬尼斯跑路1",
            },
            [24] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                      ["ObjectName"] = "BPS_ActorDirection_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.8785,
                      ["Target"] = "中景",
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["ObjectName"] = "BP_DialogueTrackDirection_C_3",
                  ["Parent"] = "Performer1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Crouch",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 1.1711,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9.318,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_3",
                  ["Parent"] = "Performer1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "莱昂转移1",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 6.9269,
                      ["StickGround"] = false,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_4",
                  ["Parent"] = "Performer1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer1",
            },
            [25] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 4.1578,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "芬尼斯跑路1",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["ObjectName"] = "BPS_Transform_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 34.8404,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["ObjectName"] = "BP_DialogueTrackTransform_C_2",
                  ["Parent"] = "Performer2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 34.697,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["ObjectName"] = "BPS_ActorVisible_C_1",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0.1273,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["ObjectName"] = "BP_DialogueTrackVisible_C_1",
                  ["Parent"] = "Performer2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Performer2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Performer2",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueCameraTrack_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [6] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 362430836,
                ["B"] = 1146045357,
                ["C"] = -1770262156,
                ["D"] = -779627244,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "芬尼斯先生，我能和你聊几句吗？让莱昂在这稍等一会儿。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 1.9707,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1438810258,
                ["B"] = 1512063039,
                ["C"] = -1419074258,
                ["D"] = 805760716,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "这……好吧。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 3.5,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 4.8373,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1358424546,
                ["B"] = -1150202314,
                ["C"] = -1860959986,
                ["D"] = -1182134299,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "莱昂对道格倾注了非常强烈的感情，作为他的管家，您比我更清楚。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8.3707,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1968123578,
                ["B"] = -1272165338,
                ["C"] = -1465526630,
                ["D"] = 1815675122,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_3",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "是的。小主人最爱道格，把那只狗看得无比重要。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 13.208,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1300489842,
                ["B"] = -1096137562,
                ["C"] = -1210590021,
                ["D"] = 1731957085,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_4",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "说实话，我不明白——那不过是只宠物，可能连人话都听不懂！",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 16.208,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1922608014,
                ["B"] = -2001649062,
                ["C"] = -1538699229,
                ["D"] = -655222870,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_5",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "先生，莱昂是个纯真的孩子。他付出感情时，不像我们成年人一样顾虑许多。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 19.708,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -386276187,
                ["B"] = -843756244,
                ["C"] = -1625997110,
                ["D"] = -2073306486,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_6",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "让他认真对待自己的感情，这并不会影响什么，甚至令人羡慕。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 24.708,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [8] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 8,
              ["ContentUI"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 7,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 240919350,
                ["B"] = 777208513,
                ["C"] = -1169705660,
                ["D"] = -988114284,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_7",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "……也许您是对的。哎，年纪大了，总会把事情想复杂。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 28.708,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [9] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 9,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 8,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1795764542,
                ["B"] = -418689356,
                ["C"] = -1891565598,
                ["D"] = -1358673014,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_8",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我会在园外等小主人，感谢您照看他。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 32.208,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_2",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -397.442,
          ["Y"] = 922.8623,
          ["Z"] = -51.8278,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "芬尼斯转移1",
      ["bDefaultVisible"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -260.8157,
          ["Y"] = 881.3313,
          ["Z"] = -21.9101,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "玩家转移1",
      ["bDefaultVisible"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 116.5793,
          ["Y"] = -163.4657,
          ["Z"] = 70.8291,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "莱昂转移1",
      ["bDefaultVisible"] = true,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["ObjectName"] = "BP_DialogueRoutePoint_C_4",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1533.8307,
          ["Y"] = 2545.4805,
          ["Z"] = -153.4175,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "芬尼斯跑路1",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201007,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 114.2977,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000010,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 1,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 108.4433,
          ["Y"] = 0,
          ["Z"] = 117.8187,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201006,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4454,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.8953,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 106.7636,
          ["Y"] = -123.0627,
          ["Z"] = 145.1825,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Performer1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201007,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2269,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9739,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -792.2751,
          ["Y"] = 2096.5175,
          ["Z"] = -76.226,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Performer2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [3] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [5] = "   End Object",
      [6] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_7'\"",
      [7] = "   End Object",
      [8] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_6'\"",
      [11] = "   End Object",
      [12] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [13] = "   End Object",
      [14] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [15] = "   End Object",
      [16] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [17] = "   End Object",
      [18] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [19] = "   End Object",
      [20] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [21] = "   End Object",
      [22] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [23] = "   End Object",
      [24] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [27] = "   End Object",
      [28] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [31] = "   End Object",
      [32] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [35] = "   End Object",
      [36] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [37] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_18'\"",
      [38] = "      End Object",
      [39] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_17'\"",
      [40] = "      End Object",
      [41] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_16'\"",
      [42] = "      End Object",
      [43] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_15'\"",
      [44] = "      End Object",
      [45] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_14'\"",
      [46] = "      End Object",
      [47] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_13'\"",
      [48] = "      End Object",
      [49] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_12'\"",
      [50] = "      End Object",
      [51] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_11'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_2'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [57] = "   End Object",
      [58] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [59] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [60] = "      End Object",
      [61] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [62] = "      End Object",
      [63] = "   End Object",
      [64] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [65] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [66] = "      End Object",
      [67] = "   End Object",
      [68] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [69] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_2'\"",
      [70] = "      End Object",
      [71] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_1'\"",
      [72] = "      End Object",
      [73] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [74] = "      End Object",
      [75] = "   End Object",
      [76] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1'\"",
      [77] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1.BPS_Transform_C_0'\"",
      [78] = "      End Object",
      [79] = "   End Object",
      [80] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2'\"",
      [81] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2.BPS_ActorDirection_C_0'\"",
      [82] = "      End Object",
      [83] = "   End Object",
      [84] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_2'\"",
      [85] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_2.BPS_Transform_C_0'\"",
      [86] = "      End Object",
      [87] = "   End Object",
      [88] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C Name=\"BP_DialogueTrackVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0'\"",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C Name=\"BP_DialogueTrackVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1'\"",
      [95] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_1'\"",
      [96] = "      End Object",
      [97] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_0'\"",
      [98] = "      End Object",
      [99] = "   End Object",
      [100] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C Name=\"BP_DialogueTrackTransform_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_4'\"",
      [101] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_4.BPS_Transform_C_0'\"",
      [102] = "      End Object",
      [103] = "   End Object",
      [104] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3'\"",
      [105] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3.BPS_PlayAnimation_C_0'\"",
      [106] = "      End Object",
      [107] = "   End Object",
      [108] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_3'\"",
      [109] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_3.BPS_ActorDirection_C_0'\"",
      [110] = "      End Object",
      [111] = "   End Object",
      [112] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [113] = "   End Object",
      [114] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_2'\"",
      [115] = "   End Object",
      [116] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [117] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_0'\"",
      [118] = "      End Object",
      [119] = "   End Object",
      [120] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_4'\"",
      [121] = "   End Object",
      [122] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueRoutePointTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_4'\"",
      [123] = "   End Object",
      [124] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C Name=\"BP_DialogueTrackActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2'\"",
      [125] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_2'\"",
      [126] = "      End Object",
      [127] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_1'\"",
      [128] = "      End Object",
      [129] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_0'\"",
      [130] = "      End Object",
      [131] = "   End Object",
      [132] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_3'\"",
      [133] = "   End Object",
      [134] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueRoutePointTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_3'\"",
      [135] = "   End Object",
      [136] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_2'\"",
      [137] = "   End Object",
      [138] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueRoutePointTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_2'\"",
      [139] = "   End Object",
      [140] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C Name=\"BP_DialogueRoutePoint_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_1'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueRoutePointTrack Name=\"DialogueRoutePointTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_1'\"",
      [143] = "   End Object",
      [144] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C Name=\"BP_DialogueTrackActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0'\"",
      [145] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_2'\"",
      [146] = "      End Object",
      [147] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_1'\"",
      [148] = "      End Object",
      [149] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_0'\"",
      [150] = "      End Object",
      [151] = "   End Object",
      [152] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_35\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_35'\"",
      [153] = "   End Object",
      [154] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_29\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_29'\"",
      [155] = "   End Object",
      [156] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_34\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_34'\"",
      [157] = "   End Object",
      [158] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_28\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_28'\"",
      [159] = "   End Object",
      [160] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_33\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_33'\"",
      [161] = "   End Object",
      [162] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_27'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_32\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_32'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_26'\"",
      [167] = "   End Object",
      [168] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_31\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_31'\"",
      [169] = "   End Object",
      [170] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_25'\"",
      [171] = "   End Object",
      [172] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_30\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_30'\"",
      [173] = "   End Object",
      [174] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_24'\"",
      [175] = "   End Object",
      [176] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C Name=\"BP_DialogueTrackBlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [177] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C Name=\"BPS_BlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0.BPS_BlackScreenText_C_0'\"",
      [178] = "      End Object",
      [179] = "   End Object",
      [180] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [181] = "   End Object",
      [182] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [183] = "   End Object",
      [184] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [185] = "   End Object",
      [186] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [187] = "   End Object",
      [188] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [189] = "   End Object",
      [190] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [191] = "   End Object",
      [192] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [193] = "   End Object",
      [194] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [195] = "   End Object",
      [196] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [197] = "   End Object",
      [198] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [199] = "   End Object",
      [200] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [201] = "   End Object",
      [202] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [203] = "   End Object",
      [204] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [205] = "   End Object",
      [206] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [207] = "   End Object",
      [208] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [209] = "   End Object",
      [210] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [211] = "   End Object",
      [212] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [213] = "   End Object",
      [214] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueActorTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_1'\"",
      [215] = "   End Object",
      [216] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [217] = "   End Object",
      [218] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [219] = "   End Object",
      [220] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [221] = "   End Object",
      [222] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [223] = "   End Object",
      [224] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [225] = "   End Object",
      [226] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [227] = "   End Object",
      [228] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [229] = "   End Object",
      [230] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [231] = "   End Object",
      [232] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [233] = "   End Object",
      [234] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_11'\"",
      [235] = "   End Object",
      [236] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [237] = "   End Object",
      [238] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_10'\"",
      [239] = "   End Object",
      [240] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [241] = "   End Object",
      [242] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_9'\"",
      [243] = "   End Object",
      [244] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [245] = "   End Object",
      [246] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueCameraTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_8'\"",
      [247] = "   End Object",
      [248] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [249] = "   End Object",
      [250] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [251] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_18'\"",
      [252] = "      End Object",
      [253] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_19'\"",
      [254] = "      End Object",
      [255] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_20'\"",
      [256] = "      End Object",
      [257] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_21'\"",
      [258] = "      End Object",
      [259] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_22'\"",
      [260] = "      End Object",
      [261] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_23'\"",
      [262] = "      End Object",
      [263] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_24'\"",
      [264] = "      End Object",
      [265] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_25'\"",
      [266] = "      End Object",
      [267] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_26'\"",
      [268] = "      End Object",
      [269] = "   End Object",
      [270] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph_0\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph_0'\"",
      [271] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph_0.EpisodeGraphEntryNode_1'\"",
      [272] = "      End Object",
      [273] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_5\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph_0.EpisodeGraphNode_5'\"",
      [274] = "      End Object",
      [275] = "   End Object",
      [276] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [277] = "   End Object",
      [278] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [279] = "   End Object",
      [280] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [281] = "   End Object",
      [282] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [283] = "   End Object",
      [284] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [285] = "   End Object",
      [286] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [287] = "   End Object",
      [288] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [289] = "   End Object",
      [290] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [291] = "   End Object",
      [292] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [293] = "   End Object",
      [294] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [295] = "   End Object",
      [296] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [297] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [298] = "      End Object",
      [299] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [300] = "      End Object",
      [301] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [302] = "      End Object",
      [303] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [304] = "      End Object",
      [305] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [306] = "      End Object",
      [307] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [308] = "      End Object",
      [309] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [310] = "      End Object",
      [311] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_7'\"",
      [312] = "      End Object",
      [313] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_8'\"",
      [314] = "      End Object",
      [315] = "   End Object",
      [316] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [317] = "   End Object",
      [318] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [319] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [320] = "      End Object",
      [321] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_1'\"",
      [322] = "      End Object",
      [323] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [324] = "      End Object",
      [325] = "   End Object",
      [326] = "   Begin Object Name=\"DialogueActorTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_0'\"",
      [327] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [328] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_9'\"",
      [329] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_10'\"",
      [330] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_12'\"",
      [331] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_13'\"",
      [332] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_14'\"",
      [333] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [334] = "      TrackName=\"Actor2\"",
      [335] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [336] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_1'\"",
      [337] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [338] = "      Actions(3)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_2'\"",
      [339] = "      Actions(4)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_0'\"",
      [340] = "   End Object",
      [341] = "   Begin Object Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [342] = "      FOV=40.000000",
      [343] = "      bEnableLookAt=True",
      [344] = "      bOverride_DepthOfField=True",
      [345] = "      DepthOfFieldFocusActor=(PerformerName=\"Performer1\")",
      [346] = "      DepthOfFieldFocalDistance=589.233093",
      [347] = "      DepthOfFieldFStop=15.000000",
      [348] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [349] = "      SpawnTransform=(Rotation=(X=-0.046309,Y=-0.042441,Z=-0.735765,W=0.674318),Translation=(X=189.889233,Y=451.787849,Z=97.650500))",
      [350] = "      TrackName=\"反平视\"",
      [351] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [352] = "   End Object",
      [353] = "   Begin Object Name=\"DialogueCameraTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_7'\"",
      [354] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_7'\"",
      [355] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [356] = "      TrackName=\"反平视\"",
      [357] = "   End Object",
      [358] = "   Begin Object Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [359] = "      FOV=60.000000",
      [360] = "      bEnableLookAt=True",
      [361] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [362] = "      SpawnTransform=(Rotation=(X=0.008500,Y=0.015200,Z=-0.486308,W=0.873614),Translation=(X=-217.367933,Y=438.349287,Z=131.667217))",
      [363] = "      TrackName=\"反全景\"",
      [364] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [365] = "   End Object",
      [366] = "   Begin Object Name=\"DialogueCameraTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_6'\"",
      [367] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [368] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [369] = "      TrackName=\"反全景\"",
      [370] = "   End Object",
      [371] = "   Begin Object Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [372] = "      FOV=50.000000",
      [373] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [374] = "      SpawnTransform=(Rotation=(X=0.014324,Y=0.006452,Z=-0.910579,W=0.413037),Translation=(X=896.480400,Y=962.637000,Z=180.876767))",
      [375] = "      TrackName=\"反远景\"",
      [376] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [377] = "   End Object",
      [378] = "   Begin Object Name=\"DialogueCameraTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_5'\"",
      [379] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_5'\"",
      [380] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [381] = "      TrackName=\"反远景\"",
      [382] = "   End Object",
      [383] = "   Begin Object Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [384] = "      FOV=40.000000",
      [385] = "      DepthOfFieldFocalDistance=250.000000",
      [386] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [387] = "      SpawnTransform=(Rotation=(X=-0.029388,Y=0.041667,Z=0.795736,W=0.603494),Translation=(X=147.182391,Y=-304.960893,Z=208.590142))",
      [388] = "      TrackName=\"平视\"",
      [389] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [390] = "   End Object",
      [391] = "   Begin Object Name=\"DialogueCameraTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_4'\"",
      [392] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_4'\"",
      [393] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [394] = "      TrackName=\"平视\"",
      [395] = "   End Object",
      [396] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [397] = "      FOV=30.000000",
      [398] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [399] = "      SpawnTransform=(Rotation=(X=-0.034199,Y=0.017499,Z=0.889568,W=0.455184),Translation=(X=338.351100,Y=-380.722900,Z=173.849300))",
      [400] = "      TrackName=\"中景\"",
      [401] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [402] = "   End Object",
      [403] = "   Begin Object Name=\"DialogueCameraTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_3'\"",
      [404] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [405] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [406] = "      TrackName=\"中景\"",
      [407] = "   End Object",
      [408] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [409] = "      FOV=69.568848",
      [410] = "      bOverride_DepthOfField=True",
      [411] = "      DepthOfFieldFocusActor=(PerformerName=\"Performer2\")",
      [412] = "      DepthOfFieldFocalDistance=678.286987",
      [413] = "      DepthOfFieldFStop=10.000000",
      [414] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [415] = "      SpawnTransform=(Rotation=(X=-0.007927,Y=0.005390,Z=0.988145,W=0.153224),Translation=(X=-120.220367,Y=1933.263650,Z=69.721900))",
      [416] = "      TrackName=\"全景\"",
      [417] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [418] = "   End Object",
      [419] = "   Begin Object Name=\"DialogueCameraTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_2'\"",
      [420] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [421] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [422] = "      TrackName=\"全景\"",
      [423] = "   End Object",
      [424] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [425] = "      FOV=30.000000",
      [426] = "      bOverride_DepthOfField=True",
      [427] = "      DepthOfFieldFocusActor=(PerformerName=\"Performer1\")",
      [428] = "      DepthOfFieldFocalDistance=1429.344482",
      [429] = "      DepthOfFieldFStop=5.000000",
      [430] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [431] = "      SpawnTransform=(Rotation=(X=-0.035010,Y=0.007557,Z=-0.551522,W=0.833391),Translation=(X=-418.632967,Y=1098.530804,Z=105.677392))",
      [432] = "      TrackName=\"远景\"",
      [433] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [434] = "   End Object",
      [435] = "   Begin Object Name=\"DialogueCameraTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_1'\"",
      [436] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [437] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [438] = "      TrackName=\"远景\"",
      [439] = "   End Object",
      [440] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [441] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [442] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.350904,W=0.936411),Translation=(X=-51042.765100,Y=-44307.501300,Z=-830.418700))",
      [443] = "      TrackName=\"锚点\"",
      [444] = "   End Object",
      [445] = "   Begin Object Name=\"DialogueCameraTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_0'\"",
      [446] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [447] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_1'\"",
      [448] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_2'\"",
      [449] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_3'\"",
      [450] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_4'\"",
      [451] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_5'\"",
      [452] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_6'\"",
      [453] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_7'\"",
      [454] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_20'\"",
      [455] = "      Childs(8)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_15'\"",
      [456] = "      Childs(9)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_11'\"",
      [457] = "      Childs(10)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_8'\"",
      [458] = "      Childs(11)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [459] = "      Childs(12)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [460] = "      Childs(13)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_24'\"",
      [461] = "      Childs(14)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_25'\"",
      [462] = "      Childs(15)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_26'\"",
      [463] = "      Childs(16)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_27'\"",
      [464] = "      Childs(17)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_28'\"",
      [465] = "      Childs(18)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_29'\"",
      [466] = "      Childs(19)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueRoutePointTrack_1'\"",
      [467] = "      Childs(20)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueRoutePointTrack_2'\"",
      [468] = "      Childs(21)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueRoutePointTrack_3'\"",
      [469] = "      Childs(22)=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'********:DialogueRoutePointTrack_4'\"",
      [470] = "      Childs(23)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_2'\"",
      [471] = "      Childs(24)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [472] = "      TrackName=\"锚点\"",
      [473] = "   End Object",
      [474] = "   Begin Object Name=\"BP_DialogueCameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0'\"",
      [475] = "      Begin Object Name=\"BPS_CameraCut_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_18'\"",
      [476] = "         CameraBreathType=NewEnumerator5",
      [477] = "         BreathAttenuation=3.000000",
      [478] = "         BreathSpeed=8.000000",
      [479] = "         TargetCamera=(CameraName=\"全景\")",
      [480] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [481] = "         SectionName=\"Actor2近景\"",
      [482] = "         LineGUIDLinked=1843316958",
      [483] = "         OwnedEpisodeID=1",
      [484] = "         StartTime=35.208042",
      [485] = "         Duration=3.777496",
      [486] = "      End Object",
      [487] = "      Begin Object Name=\"BPS_CameraCut_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_17'\"",
      [488] = "         TargetCamera=(CameraName=\"反中01Actor2\")",
      [489] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [490] = "         SectionName=\"Actor2正面\"",
      [491] = "         LineGUIDLinked=263855788",
      [492] = "         OwnedEpisodeID=1",
      [493] = "         StartTime=32.208042",
      [494] = "         Duration=3.000000",
      [495] = "      End Object",
      [496] = "      Begin Object Name=\"BPS_CameraCut_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_16'\"",
      [497] = "         TargetCamera=(CameraName=\"反中01Actor2\")",
      [498] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [499] = "         SectionName=\"Actor2正面\"",
      [500] = "         LineGUIDLinked=542530340",
      [501] = "         OwnedEpisodeID=1",
      [502] = "         StartTime=28.708042",
      [503] = "         Duration=3.500000",
      [504] = "      End Object",
      [505] = "      Begin Object Name=\"BPS_CameraCut_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_15'\"",
      [506] = "         CameraBreathType=NewEnumerator7",
      [507] = "         BreathAttenuation=4.000000",
      [508] = "         BreathSpeed=1.500000",
      [509] = "         BreathFocusDistance=100.000000",
      [510] = "         TargetCamera=(CameraName=\"反平视\")",
      [511] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [512] = "         SectionName=\"Actor1正面\"",
      [513] = "         LineGUIDLinked=399312228",
      [514] = "         OwnedEpisodeID=1",
      [515] = "         StartTime=24.708042",
      [516] = "         Duration=4.000000",
      [517] = "      End Object",
      [518] = "      Begin Object Name=\"BPS_CameraCut_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_14'\"",
      [519] = "         TargetCamera=(CameraName=\"反过肩Actor1\")",
      [520] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [521] = "         SectionName=\"Actor1正面\"",
      [522] = "         LineGUIDLinked=224323094",
      [523] = "         OwnedEpisodeID=1",
      [524] = "         StartTime=19.708042",
      [525] = "         Duration=5.000000",
      [526] = "      End Object",
      [527] = "      Begin Object Name=\"BPS_CameraCut_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_13'\"",
      [528] = "         TargetCamera=(CameraName=\"反中01Actor2\")",
      [529] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [530] = "         SectionName=\"Actor2近景\"",
      [531] = "         LineGUIDLinked=3250966899",
      [532] = "         OwnedEpisodeID=1",
      [533] = "         StartTime=16.208040",
      [534] = "         Duration=3.500000",
      [535] = "      End Object",
      [536] = "      Begin Object Name=\"BPS_CameraCut_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_12'\"",
      [537] = "         CameraBreathType=NewEnumerator4",
      [538] = "         BreathAttenuation=3.000000",
      [539] = "         BreathSpeed=4.000000",
      [540] = "         TargetCamera=(CameraName=\"远景\")",
      [541] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [542] = "         SectionName=\"Actor2正面\"",
      [543] = "         LineGUIDLinked=2634839394",
      [544] = "         OwnedEpisodeID=1",
      [545] = "         StartTime=13.208040",
      [546] = "         Duration=3.000000",
      [547] = "      End Object",
      [548] = "      Begin Object Name=\"BPS_CameraCut_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_11'\"",
      [549] = "         TargetCamera=(CameraName=\"反过肩Actor1\")",
      [550] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [551] = "         SectionName=\"Actor1正面\"",
      [552] = "         LineGUIDLinked=2501928711",
      [553] = "         OwnedEpisodeID=1",
      [554] = "         StartTime=8.370738",
      [555] = "         Duration=4.837302",
      [556] = "      End Object",
      [557] = "      Begin Object Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueCameraCut_C_0.BPS_CameraCut_C_2'\"",
      [558] = "         TargetCamera=(CameraName=\"过肩Actor2\")",
      [559] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'********:BP_DialogueCameraCut_C_0'\"",
      [560] = "         SectionName=\"Actor1近景\"",
      [561] = "         LineGUIDLinked=1027611070",
      [562] = "         OwnedEpisodeID=1",
      [563] = "         Duration=5.470738",
      [564] = "      End Object",
      [565] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_2'\"",
      [566] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_11'\"",
      [567] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_12'\"",
      [568] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_13'\"",
      [569] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_14'\"",
      [570] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_15'\"",
      [571] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_16'\"",
      [572] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_17'\"",
      [573] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_18'\"",
      [574] = "   End Object",
      [575] = "   Begin Object Name=\"BP_DialogueTrackLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackLookAt_C_0'\"",
      [576] = "   End Object",
      [577] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0'\"",
      [578] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_1'\"",
      [579] = "         AnimLibItem=(AssetID=\"Short_Talk2\")",
      [580] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [581] = "         OwnedEpisodeID=1",
      [582] = "         StartTime=19.708040",
      [583] = "         Duration=2.543224",
      [584] = "      End Object",
      [585] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_0.BPS_PlayAnimation_C_0'\"",
      [586] = "         AnimLibItem=(AssetID=\"Short_Talk2\")",
      [587] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [588] = "         OwnedEpisodeID=1",
      [589] = "         Duration=4.263419",
      [590] = "      End Object",
      [591] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [592] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [593] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [594] = "   End Object",
      [595] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0'\"",
      [596] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_0.BPS_Transform_C_0'\"",
      [597] = "         MoveTarget=(TrackName=\"玩家转移1\")",
      [598] = "         StickGround=True",
      [599] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [600] = "         OwnedEpisodeID=1",
      [601] = "         StartTime=6.350402",
      [602] = "      End Object",
      [603] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [604] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [605] = "   End Object",
      [606] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1'\"",
      [607] = "      Begin Object Name=\"BPS_PlayAnimation_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_2'\"",
      [608] = "         AnimLibItem=(AssetID=\"Big_Sigh\")",
      [609] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [610] = "         OwnedEpisodeID=1",
      [611] = "         StartTime=28.708042",
      [612] = "         Duration=2.145042",
      [613] = "      End Object",
      [614] = "      Begin Object Name=\"BPS_PlayAnimation_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_1'\"",
      [615] = "         AnimLibItem=(AssetID=\"Salute\")",
      [616] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [617] = "         OwnedEpisodeID=1",
      [618] = "         StartTime=32.208042",
      [619] = "         Duration=2.034435",
      [620] = "      End Object",
      [621] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_1.BPS_PlayAnimation_C_0'\"",
      [622] = "         AnimLibItem=(AssetID=\"Talk_Yes_A\")",
      [623] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_1'\"",
      [624] = "         LineGUIDLinked=2526149507",
      [625] = "         OwnedEpisodeID=1",
      [626] = "         StartTime=4.251157",
      [627] = "         Duration=1.219581",
      [628] = "      End Object",
      [629] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [630] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_2'\"",
      [631] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_1'\"",
      [632] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [633] = "   End Object",
      [634] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1'\"",
      [635] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_1.BPS_Transform_C_0'\"",
      [636] = "         MoveTarget=(TrackName=\"芬尼斯转移1\")",
      [637] = "         StickGround=True",
      [638] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_1'\"",
      [639] = "         OwnedEpisodeID=1",
      [640] = "         StartTime=6.516633",
      [641] = "         Duration=0.918861",
      [642] = "      End Object",
      [643] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [644] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [645] = "   End Object",
      [646] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2'\"",
      [647] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_2.BPS_ActorDirection_C_0'\"",
      [648] = "         Target=(TrackName=\"Actor1\")",
      [649] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_2'\"",
      [650] = "         OwnedEpisodeID=1",
      [651] = "         StartTime=7.568649",
      [652] = "         Duration=0.802089",
      [653] = "      End Object",
      [654] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [655] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [656] = "   End Object",
      [657] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_2'\"",
      [658] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_2.BPS_Transform_C_0'\"",
      [659] = "         MoveTarget=(TrackName=\"芬尼斯跑路1\")",
      [660] = "         StickGround=True",
      [661] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_2'\"",
      [662] = "         OwnedEpisodeID=1",
      [663] = "         StartTime=34.840443",
      [664] = "         Duration=4.157837",
      [665] = "      End Object",
      [666] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [667] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [668] = "   End Object",
      [669] = "   Begin Object Name=\"BP_DialogueTrackVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_0'\"",
      [670] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [671] = "   End Object",
      [672] = "   Begin Object Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [673] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [674] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [675] = "      FromTemplate=False",
      [676] = "      TrackName=\"Performer2\"",
      [677] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_2'\"",
      [678] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [679] = "   End Object",
      [680] = "   Begin Object Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [681] = "      AppearanceID=7201007",
      [682] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [683] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.973917,W=0.226904),Translation=(X=-792.275060,Y=2096.517536,Z=-76.226000))",
      [684] = "      TrackName=\"Performer2\"",
      [685] = "      StickGround=True",
      [686] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [687] = "   End Object",
      [688] = "   Begin Object Name=\"BP_DialogueTrackVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1'\"",
      [689] = "      Begin Object Name=\"BPS_ActorVisible_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_1'\"",
      [690] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [691] = "         OwnedEpisodeID=1",
      [692] = "         StartTime=0.127323",
      [693] = "      End Object",
      [694] = "      Begin Object Name=\"BPS_ActorVisible_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackVisible_C_1.BPS_ActorVisible_C_0'\"",
      [695] = "         Visible=True",
      [696] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C'********:BP_DialogueTrackVisible_C_1'\"",
      [697] = "         OwnedEpisodeID=1",
      [698] = "         StartTime=34.696957",
      [699] = "      End Object",
      [700] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_0'\"",
      [701] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C'BPS_ActorVisible_C_1'\"",
      [702] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [703] = "   End Object",
      [704] = "   Begin Object Name=\"BP_DialogueTrackTransform_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_4'\"",
      [705] = "      Begin Object Name=\"BPS_Transform_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackTransform_C_4.BPS_Transform_C_0'\"",
      [706] = "         MoveTarget=(TrackName=\"莱昂转移1\")",
      [707] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_4'\"",
      [708] = "         OwnedEpisodeID=1",
      [709] = "         StartTime=6.926943",
      [710] = "      End Object",
      [711] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C'BPS_Transform_C_0'\"",
      [712] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_2'\"",
      [713] = "   End Object",
      [714] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3'\"",
      [715] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_3.BPS_PlayAnimation_C_0'\"",
      [716] = "         AnimLibItem=(AssetID=\"Crouch\")",
      [717] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_3'\"",
      [718] = "         OwnedEpisodeID=1",
      [719] = "         StartTime=9.318014",
      [720] = "         Duration=1.171138",
      [721] = "      End Object",
      [722] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [723] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_2'\"",
      [724] = "   End Object",
      [725] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_3'\"",
      [726] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_3.BPS_ActorDirection_C_0'\"",
      [727] = "         Target=(TrackName=\"中景\")",
      [728] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_3'\"",
      [729] = "         OwnedEpisodeID=1",
      [730] = "         StartTime=7.878483",
      [731] = "      End Object",
      [732] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [733] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_2'\"",
      [734] = "   End Object",
      [735] = "   Begin Object Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [736] = "      AppearanceID=7201006",
      [737] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [738] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.895341,W=0.445381),Translation=(X=106.763633,Y=-123.062683,Z=145.182450))",
      [739] = "      TrackName=\"Performer1\"",
      [740] = "      StickGround=True",
      [741] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [742] = "   End Object",
      [743] = "   Begin Object Name=\"DialoguePerformerTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_2'\"",
      [744] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [745] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [746] = "      FromTemplate=False",
      [747] = "      TrackName=\"Performer1\"",
      [748] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_3'\"",
      [749] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_3'\"",
      [750] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_4'\"",
      [751] = "   End Object",
      [752] = "   Begin Object Name=\"BP_DialogueTrackDirection_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1'\"",
      [753] = "      Begin Object Name=\"BPS_ActorDirection_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackDirection_C_1.BPS_ActorDirection_C_0'\"",
      [754] = "         Target=(TrackName=\"Actor2\")",
      [755] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [756] = "         OwnedEpisodeID=1",
      [757] = "         StartTime=7.528703",
      [758] = "         Duration=0.842035",
      [759] = "      End Object",
      [760] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C'BPS_ActorDirection_C_0'\"",
      [761] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [762] = "   End Object",
      [763] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_4'\"",
      [764] = "      SpawnTransform=(Translation=(X=-1533.830658,Y=2545.480488,Z=-153.417527))",
      [765] = "      TrackName=\"芬尼斯跑路1\"",
      [766] = "      StickGround=True",
      [767] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [768] = "   End Object",
      [769] = "   Begin Object Name=\"DialogueRoutePointTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_4'\"",
      [770] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_4'\"",
      [771] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [772] = "      FromTemplate=False",
      [773] = "      TrackName=\"芬尼斯跑路1\"",
      [774] = "   End Object",
      [775] = "   Begin Object Name=\"BP_DialogueTrackActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2'\"",
      [776] = "      Begin Object Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_2'\"",
      [777] = "         Target=(TrackName=\"Actor2\")",
      [778] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_2'\"",
      [779] = "         OwnedEpisodeID=1",
      [780] = "         StartTime=7.542659",
      [781] = "      End Object",
      [782] = "      Begin Object Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_1'\"",
      [783] = "         Target=(TrackName=\"Actor2\")",
      [784] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_2'\"",
      [785] = "         LineGUIDLinked=3250966899",
      [786] = "         OwnedEpisodeID=1",
      [787] = "         StartTime=16.208040",
      [788] = "      End Object",
      [789] = "      Begin Object Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_2.BPS_ActorLookAt_C_0'\"",
      [790] = "         Target=(TrackName=\"Performer1\")",
      [791] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_2'\"",
      [792] = "         LineGUIDLinked=2634839394",
      [793] = "         OwnedEpisodeID=1",
      [794] = "         StartTime=13.208040",
      [795] = "      End Object",
      [796] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_2'\"",
      [797] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_0'\"",
      [798] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_1'\"",
      [799] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [800] = "   End Object",
      [801] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_3'\"",
      [802] = "      SpawnTransform=(Translation=(X=116.579340,Y=-163.465707,Z=70.829117))",
      [803] = "      TrackName=\"莱昂转移1\"",
      [804] = "      StickGround=True",
      [805] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [806] = "   End Object",
      [807] = "   Begin Object Name=\"DialogueRoutePointTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_3'\"",
      [808] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_3'\"",
      [809] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [810] = "      FromTemplate=False",
      [811] = "      TrackName=\"莱昂转移1\"",
      [812] = "   End Object",
      [813] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_2'\"",
      [814] = "      SpawnTransform=(Translation=(X=-260.815720,Y=881.331320,Z=-21.910100))",
      [815] = "      TrackName=\"玩家转移1\"",
      [816] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [817] = "   End Object",
      [818] = "   Begin Object Name=\"DialogueRoutePointTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_2'\"",
      [819] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_2'\"",
      [820] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [821] = "      FromTemplate=False",
      [822] = "      TrackName=\"玩家转移1\"",
      [823] = "   End Object",
      [824] = "   Begin Object Name=\"BP_DialogueRoutePoint_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'/Temp/DialogueTransientPackage.********:BP_DialogueRoutePoint_C_1'\"",
      [825] = "      SpawnTransform=(Translation=(X=-397.442014,Y=922.862334,Z=-51.827802))",
      [826] = "      TrackName=\"芬尼斯转移1\"",
      [827] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [828] = "   End Object",
      [829] = "   Begin Object Name=\"DialogueRoutePointTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueRoutePointTrack'/Temp/DialogueTransientPackage.********:DialogueRoutePointTrack_1'\"",
      [830] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'********:BP_DialogueRoutePoint_C_1'\"",
      [831] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [832] = "      FromTemplate=False",
      [833] = "      TrackName=\"芬尼斯转移1\"",
      [834] = "   End Object",
      [835] = "   Begin Object Name=\"BP_DialogueTrackActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0'\"",
      [836] = "      Begin Object Name=\"BPS_ActorLookAt_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_2'\"",
      [837] = "         Target=(TrackName=\"Actor1\")",
      [838] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [839] = "         OwnedEpisodeID=1",
      [840] = "         StartTime=7.556526",
      [841] = "      End Object",
      [842] = "      Begin Object Name=\"BPS_ActorLookAt_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_1'\"",
      [843] = "         Target=(TrackName=\"Actor1\")",
      [844] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [845] = "         LineGUIDLinked=3250966899",
      [846] = "         OwnedEpisodeID=1",
      [847] = "         StartTime=17.578671",
      [848] = "      End Object",
      [849] = "      Begin Object Name=\"BPS_ActorLookAt_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackActorLookAt_C_0.BPS_ActorLookAt_C_0'\"",
      [850] = "         Target=(TrackName=\"Performer1\")",
      [851] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_0'\"",
      [852] = "         LineGUIDLinked=2634839394",
      [853] = "         OwnedEpisodeID=1",
      [854] = "         StartTime=13.208040",
      [855] = "      End Object",
      [856] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_2'\"",
      [857] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_0'\"",
      [858] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C'BPS_ActorLookAt_C_1'\"",
      [859] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [860] = "   End Object",
      [861] = "   Begin Object Name=\"BP_DialogueCamera_C_35\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_35'\"",
      [862] = "      FOV=37.400002",
      [863] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [864] = "      SpawnTransform=(Rotation=(X=-0.020499,Y=0.007461,Z=0.939469,W=0.341939),Translation=(X=240.174993,Y=-159.951758,Z=182.230020))",
      [865] = "      TrackName=\"Actor2中景\"",
      [866] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [867] = "   End Object",
      [868] = "   Begin Object Name=\"DialogueCameraTrack_29\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_29'\"",
      [869] = "      bAutoCameraTrack=True",
      [870] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_35'\"",
      [871] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [872] = "      FromTemplate=False",
      [873] = "      TrackName=\"Actor2中景\"",
      [874] = "   End Object",
      [875] = "   Begin Object Name=\"BP_DialogueCamera_C_34\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_34'\"",
      [876] = "      FOV=30.000000",
      [877] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [878] = "      SpawnTransform=(Rotation=(X=-0.025285,Y=0.006775,Z=0.965595,W=0.258730),Translation=(X=204.579422,Y=-95.045144,Z=186.347129))",
      [879] = "      TrackName=\"Actor2近景\"",
      [880] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [881] = "   End Object",
      [882] = "   Begin Object Name=\"DialogueCameraTrack_28\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_28'\"",
      [883] = "      bAutoCameraTrack=True",
      [884] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_34'\"",
      [885] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [886] = "      FromTemplate=False",
      [887] = "      TrackName=\"Actor2近景\"",
      [888] = "   End Object",
      [889] = "   Begin Object Name=\"BP_DialogueCamera_C_33\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_33'\"",
      [890] = "      FOV=30.000000",
      [891] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [892] = "      SpawnTransform=(Rotation=(X=0.017187,Y=-0.003031,Z=0.984658,W=0.173622),Translation=(X=155.178553,Y=-46.054889,Z=177.388168))",
      [893] = "      TrackName=\"Actor2正面\"",
      [894] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [895] = "   End Object",
      [896] = "   Begin Object Name=\"DialogueCameraTrack_27\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_27'\"",
      [897] = "      bAutoCameraTrack=True",
      [898] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_33'\"",
      [899] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [900] = "      FromTemplate=False",
      [901] = "      TrackName=\"Actor2正面\"",
      [902] = "   End Object",
      [903] = "   Begin Object Name=\"BP_DialogueCamera_C_32\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_32'\"",
      [904] = "      FOV=37.400002",
      [905] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [906] = "      SpawnTransform=(Rotation=(X=-0.022802,Y=0.050035,Z=0.414066,W=0.908585),Translation=(X=-103.255512,Y=-193.412221,Z=191.999018))",
      [907] = "      TrackName=\"Actor1中景\"",
      [908] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [909] = "   End Object",
      [910] = "   Begin Object Name=\"DialogueCameraTrack_26\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_26'\"",
      [911] = "      bAutoCameraTrack=True",
      [912] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_32'\"",
      [913] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [914] = "      FromTemplate=False",
      [915] = "      TrackName=\"Actor1中景\"",
      [916] = "   End Object",
      [917] = "   Begin Object Name=\"BP_DialogueCamera_C_31\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_31'\"",
      [918] = "      FOV=30.000000",
      [919] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [920] = "      SpawnTransform=(Rotation=(X=-0.014694,Y=0.052986,Z=0.266834,W=0.962173),Translation=(X=-96.756085,Y=-92.214092,Z=190.040680))",
      [921] = "      TrackName=\"Actor1近景\"",
      [922] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [923] = "   End Object",
      [924] = "   Begin Object Name=\"DialogueCameraTrack_25\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_25'\"",
      [925] = "      bAutoCameraTrack=True",
      [926] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_31'\"",
      [927] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [928] = "      FromTemplate=False",
      [929] = "      TrackName=\"Actor1近景\"",
      [930] = "   End Object",
      [931] = "   Begin Object Name=\"BP_DialogueCamera_C_30\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_30'\"",
      [932] = "      FOV=30.000000",
      [933] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [934] = "      SpawnTransform=(Rotation=(X=0.003031,Y=-0.017187,Z=0.173622,W=0.984658),Translation=(X=-46.079427,Y=-44.833147,Z=168.433088))",
      [935] = "      TrackName=\"Actor1正面\"",
      [936] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [937] = "   End Object",
      [938] = "   Begin Object Name=\"DialogueCameraTrack_24\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_24'\"",
      [939] = "      bAutoCameraTrack=True",
      [940] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_30'\"",
      [941] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [942] = "      FromTemplate=False",
      [943] = "      TrackName=\"Actor1正面\"",
      [944] = "   End Object",
      [945] = "   Begin Object Name=\"BP_DialogueTrackBlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [946] = "      Begin Object Name=\"BPS_BlackScreenText_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackBlackScreenText_C_0.BPS_BlackScreenText_C_0'\"",
      [947] = "         AsideID=60015026",
      [948] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'********:BP_DialogueTrackBlackScreenText_C_0'\"",
      [949] = "         OwnedEpisodeID=1",
      [950] = "         StartTime=5.470738",
      [951] = "         Duration=2.900000",
      [952] = "      End Object",
      [953] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_BlackScreenText.BPS_BlackScreenText_C'BPS_BlackScreenText_C_0'\"",
      [954] = "      FromTemplate=False",
      [955] = "   End Object",
      [956] = "   Begin Object Name=\"BP_DialogueCamera_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_23'\"",
      [957] = "      FOV=50.000000",
      [958] = "      bEnableLookAt=True",
      [959] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [960] = "      OffsetZ=-12.497244",
      [961] = "      bOverride_DepthOfField=True",
      [962] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [963] = "      DepthOfFieldFocalDistance=272.300934",
      [964] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [965] = "      SpawnTransform=(Rotation=(X=0.029425,Y=-0.011029,Z=-0.936200,W=-0.350059),Translation=(X=230.655376,Y=-143.767401,Z=69.912966))",
      [966] = "      TrackName=\"反中01Actor1\"",
      [967] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [968] = "   End Object",
      [969] = "   Begin Object Name=\"DialogueCameraTrack_23\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_23'\"",
      [970] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_23'\"",
      [971] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [972] = "      TrackName=\"反中01Actor1\"",
      [973] = "   End Object",
      [974] = "   Begin Object Name=\"BP_DialogueCamera_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_22'\"",
      [975] = "      FOV=40.000000",
      [976] = "      bEnableLookAt=True",
      [977] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [978] = "      OffsetZ=6.049607",
      [979] = "      bOverride_DepthOfField=True",
      [980] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [981] = "      DepthOfFieldFocalDistance=106.733963",
      [982] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [983] = "      SpawnTransform=(Rotation=(X=0.015473,Y=-0.002701,Z=-0.984988,W=-0.171908),Translation=(X=101.420298,Y=-31.677382,Z=88.459816))",
      [984] = "      TrackName=\"反近02Actor1\"",
      [985] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [986] = "   End Object",
      [987] = "   Begin Object Name=\"DialogueCameraTrack_22\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_22'\"",
      [988] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_22'\"",
      [989] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [990] = "      TrackName=\"反近02Actor1\"",
      [991] = "   End Object",
      [992] = "   Begin Object Name=\"BP_DialogueCamera_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_21'\"",
      [993] = "      FOV=40.000000",
      [994] = "      bEnableLookAt=True",
      [995] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [996] = "      OffsetZ=2.931192",
      [997] = "      bOverride_DepthOfField=True",
      [998] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [999] = "      DepthOfFieldFocalDistance=103.964363",
      [1000] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1001] = "      SpawnTransform=(Rotation=(X=-0.001700,Y=0.000700,Z=-0.917778,W=-0.397090),Translation=(X=75.001798,Y=-71.375600,Z=86.246199))",
      [1002] = "      TrackName=\"反近01Actor1\"",
      [1003] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1004] = "   End Object",
      [1005] = "   Begin Object Name=\"DialogueCameraTrack_21\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_21'\"",
      [1006] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_21'\"",
      [1007] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [1008] = "      TrackName=\"反近01Actor1\"",
      [1009] = "   End Object",
      [1010] = "   Begin Object Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [1011] = "      FOV=20.000000",
      [1012] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1013] = "      OffsetZ=-8.105359",
      [1014] = "      bOverride_DepthOfField=True",
      [1015] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1016] = "      DepthOfFieldFocalDistance=172.196579",
      [1017] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1018] = "      SpawnTransform=(Rotation=(X=0.006800,Y=0.098497,Z=-0.311891,W=0.944974),Translation=(X=-573.352751,Y=1108.930336,Z=181.219588))",
      [1019] = "      TrackName=\"反过肩Actor1\"",
      [1020] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1021] = "   End Object",
      [1022] = "   Begin Object Name=\"DialogueCameraTrack_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_20'\"",
      [1023] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_20'\"",
      [1024] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [1025] = "      TrackName=\"反过肩Actor1\"",
      [1026] = "   End Object",
      [1027] = "   Begin Object Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [1028] = "      FOV=50.000000",
      [1029] = "      bEnableLookAt=True",
      [1030] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1031] = "      OffsetZ=-12.540889",
      [1032] = "      bOverride_DepthOfField=True",
      [1033] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1034] = "      DepthOfFieldFocalDistance=247.063095",
      [1035] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1036] = "      SpawnTransform=(Rotation=(X=0.036600,Y=0.011800,Z=-0.950900,W=0.307100),Translation=(X=215.823298,Y=117.977500,Z=70.488294))",
      [1037] = "      TrackName=\"中01Actor1\"",
      [1038] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1039] = "   End Object",
      [1040] = "   Begin Object Name=\"DialogueCameraTrack_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_19'\"",
      [1041] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_19'\"",
      [1042] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [1043] = "      TrackName=\"中01Actor1\"",
      [1044] = "   End Object",
      [1045] = "   Begin Object Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [1046] = "      FOV=40.000000",
      [1047] = "      bEnableLookAt=True",
      [1048] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1049] = "      OffsetZ=6.077021",
      [1050] = "      bOverride_DepthOfField=True",
      [1051] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1052] = "      DepthOfFieldFocalDistance=107.929329",
      [1053] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1054] = "      SpawnTransform=(Rotation=(X=0.022600,Y=0.001600,Z=-0.997311,W=0.069701),Translation=(X=101.750998,Y=12.295800,Z=89.081068))",
      [1055] = "      TrackName=\"近02Actor1\"",
      [1056] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1057] = "   End Object",
      [1058] = "   Begin Object Name=\"DialogueCameraTrack_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_18'\"",
      [1059] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_18'\"",
      [1060] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [1061] = "      TrackName=\"近02Actor1\"",
      [1062] = "   End Object",
      [1063] = "   Begin Object Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [1064] = "      FOV=40.000000",
      [1065] = "      bEnableLookAt=True",
      [1066] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1067] = "      OffsetZ=0.061963",
      [1068] = "      bOverride_DepthOfField=True",
      [1069] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1070] = "      DepthOfFieldFocalDistance=111.280205",
      [1071] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1072] = "      SpawnTransform=(Rotation=(X=-0.009800,Y=-0.003700,Z=-0.935982,W=0.351893),Translation=(X=86.302498,Y=68.075200,Z=83.084910))",
      [1073] = "      TrackName=\"近01Actor1\"",
      [1074] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1075] = "   End Object",
      [1076] = "   Begin Object Name=\"DialogueCameraTrack_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_17'\"",
      [1077] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_17'\"",
      [1078] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [1079] = "      TrackName=\"近01Actor1\"",
      [1080] = "   End Object",
      [1081] = "   Begin Object Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [1082] = "      FOV=40.000000",
      [1083] = "      bEnableLookAt=True",
      [1084] = "      LookAtTarget=(PerformerName=\"Actor1\")",
      [1085] = "      OffsetZ=-14.181160",
      [1086] = "      bOverride_DepthOfField=True",
      [1087] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor1\")",
      [1088] = "      DepthOfFieldFocalDistance=185.775696",
      [1089] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1090] = "      SpawnTransform=(Rotation=(X=-0.012000,Y=-0.002400,Z=-0.980206,W=0.197601),Translation=(X=176.805798,Y=52.979700,Z=68.825099))",
      [1091] = "      TrackName=\"过肩Actor1\"",
      [1092] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1093] = "   End Object",
      [1094] = "   Begin Object Name=\"DialogueCameraTrack_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_16'\"",
      [1095] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_16'\"",
      [1096] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_1'\"",
      [1097] = "      TrackName=\"过肩Actor1\"",
      [1098] = "   End Object",
      [1099] = "   Begin Object Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [1100] = "      AppearanceID=12000010",
      [1101] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1102] = "      bIsPlayer=True",
      [1103] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=1.000000,W=0.000000),Translation=(X=108.443267,Y=0.000000,Z=117.818697))",
      [1104] = "      TrackName=\"Actor1\"",
      [1105] = "      StickGround=True",
      [1106] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1107] = "   End Object",
      [1108] = "   Begin Object Name=\"DialogueActorTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueActorTrack_1'\"",
      [1109] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [1110] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_16'\"",
      [1111] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_17'\"",
      [1112] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_18'\"",
      [1113] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_19'\"",
      [1114] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_21'\"",
      [1115] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_22'\"",
      [1116] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_23'\"",
      [1117] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [1118] = "      TrackName=\"Actor1\"",
      [1119] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_0'\"",
      [1120] = "      Actions(1)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C'********:BP_DialogueTrackTransform_C_0'\"",
      [1121] = "      Actions(2)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C'********:BP_DialogueTrackActorLookAt_C_2'\"",
      [1122] = "      Actions(3)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C'********:BP_DialogueTrackDirection_C_1'\"",
      [1123] = "   End Object",
      [1124] = "   Begin Object Name=\"BP_DialogueCamera_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_15'\"",
      [1125] = "      FOV=30.000000",
      [1126] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1127] = "      OffsetZ=-9.778734",
      [1128] = "      bOverride_DepthOfField=True",
      [1129] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1130] = "      DepthOfFieldFocalDistance=413.383881",
      [1131] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1132] = "      SpawnTransform=(Rotation=(X=-0.012429,Y=0.008218,Z=0.946353,W=0.322792),Translation=(X=-174.389244,Y=775.534978,Z=125.163400))",
      [1133] = "      TrackName=\"反中01Actor2\"",
      [1134] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1135] = "   End Object",
      [1136] = "   Begin Object Name=\"DialogueCameraTrack_15\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_15'\"",
      [1137] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_15'\"",
      [1138] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [1139] = "      TrackName=\"反中01Actor2\"",
      [1140] = "   End Object",
      [1141] = "   Begin Object Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [1142] = "      FOV=40.000000",
      [1143] = "      bEnableLookAt=True",
      [1144] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1145] = "      OffsetZ=3.506431",
      [1146] = "      bOverride_DepthOfField=True",
      [1147] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1148] = "      DepthOfFieldFocalDistance=99.166183",
      [1149] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1150] = "      SpawnTransform=(Rotation=(X=0.004742,Y=0.002221,Z=-0.905556,W=0.424194),Translation=(X=62.767722,Y=76.232026,Z=73.297923))",
      [1151] = "      TrackName=\"反近02Actor2\"",
      [1152] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1153] = "   End Object",
      [1154] = "   Begin Object Name=\"DialogueCameraTrack_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_14'\"",
      [1155] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_14'\"",
      [1156] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [1157] = "      TrackName=\"反近02Actor2\"",
      [1158] = "   End Object",
      [1159] = "   Begin Object Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [1160] = "      FOV=40.000000",
      [1161] = "      bEnableLookAt=True",
      [1162] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1163] = "      OffsetZ=4.721918",
      [1164] = "      bOverride_DepthOfField=True",
      [1165] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1166] = "      DepthOfFieldFocalDistance=155.931000",
      [1167] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1168] = "      SpawnTransform=(Rotation=(X=0.003470,Y=0.000383,Z=-0.993955,W=0.109734),Translation=(X=99.433446,Y=22.500819,Z=74.513410))",
      [1169] = "      TrackName=\"反近01Actor2\"",
      [1170] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1171] = "   End Object",
      [1172] = "   Begin Object Name=\"DialogueCameraTrack_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_13'\"",
      [1173] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_13'\"",
      [1174] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [1175] = "      TrackName=\"反近01Actor2\"",
      [1176] = "   End Object",
      [1177] = "   Begin Object Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [1178] = "      FOV=40.000000",
      [1179] = "      bEnableLookAt=True",
      [1180] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1181] = "      OffsetZ=11.992385",
      [1182] = "      bOverride_DepthOfField=True",
      [1183] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1184] = "      DepthOfFieldFocalDistance=198.212906",
      [1185] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1186] = "      SpawnTransform=(Rotation=(X=-0.051173,Y=-0.010971,Z=0.976443,W=-0.209331),Translation=(X=185.730196,Y=59.462903,Z=81.837273))",
      [1187] = "      TrackName=\"反过肩Actor2\"",
      [1188] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1189] = "   End Object",
      [1190] = "   Begin Object Name=\"DialogueCameraTrack_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_12'\"",
      [1191] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_12'\"",
      [1192] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [1193] = "      TrackName=\"反过肩Actor2\"",
      [1194] = "   End Object",
      [1195] = "   Begin Object Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [1196] = "      FOV=30.000000",
      [1197] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1198] = "      OffsetZ=-5.240726",
      [1199] = "      bOverride_DepthOfField=True",
      [1200] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1201] = "      DepthOfFieldFocalDistance=228.636032",
      [1202] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1203] = "      SpawnTransform=(Rotation=(X=-0.059645,Y=0.002463,Z=0.929034,W=0.365146),Translation=(X=-221.962604,Y=736.295572,Z=146.277912))",
      [1204] = "      TrackName=\"中01Actor2\"",
      [1205] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1206] = "   End Object",
      [1207] = "   Begin Object Name=\"DialogueCameraTrack_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_11'\"",
      [1208] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_11'\"",
      [1209] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [1210] = "      TrackName=\"中01Actor2\"",
      [1211] = "   End Object",
      [1212] = "   Begin Object Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [1213] = "      FOV=40.000000",
      [1214] = "      bEnableLookAt=True",
      [1215] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1216] = "      OffsetZ=-0.490653",
      [1217] = "      bOverride_DepthOfField=True",
      [1218] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1219] = "      DepthOfFieldFocalDistance=99.624741",
      [1220] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1221] = "      SpawnTransform=(Rotation=(X=0.012300,Y=-0.006500,Z=0.883678,W=0.467888),Translation=(X=53.904900,Y=-74.422900,Z=69.448465))",
      [1222] = "      TrackName=\"近02Actor2\"",
      [1223] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1224] = "   End Object",
      [1225] = "   Begin Object Name=\"DialogueCameraTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_10'\"",
      [1226] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_10'\"",
      [1227] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [1228] = "      TrackName=\"近02Actor2\"",
      [1229] = "   End Object",
      [1230] = "   Begin Object Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [1231] = "      FOV=40.000000",
      [1232] = "      bEnableLookAt=True",
      [1233] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1234] = "      OffsetZ=4.544427",
      [1235] = "      bOverride_DepthOfField=True",
      [1236] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1237] = "      DepthOfFieldFocalDistance=124.719078",
      [1238] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1239] = "      SpawnTransform=(Rotation=(X=-0.010400,Y=0.001100,Z=0.994096,W=0.108000),Translation=(X=92.731500,Y=-15.979700,Z=74.483545))",
      [1240] = "      TrackName=\"近01Actor2\"",
      [1241] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1242] = "   End Object",
      [1243] = "   Begin Object Name=\"DialogueCameraTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_9'\"",
      [1244] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_9'\"",
      [1245] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueActorTrack_0'\"",
      [1246] = "      TrackName=\"近01Actor2\"",
      [1247] = "   End Object",
      [1248] = "   Begin Object Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [1249] = "      FOV=40.000000",
      [1250] = "      LookAtTarget=(PerformerName=\"Actor2\")",
      [1251] = "      OffsetZ=15.747757",
      [1252] = "      bOverride_DepthOfField=True",
      [1253] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [1254] = "      DepthOfFieldFocalDistance=185.742432",
      [1255] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [1256] = "      SpawnTransform=(Rotation=(X=-0.005000,Y=-0.019000,Z=0.964103,W=0.264801),Translation=(X=209.694615,Y=-111.072768,Z=219.456692))",
      [1257] = "      TrackName=\"过肩Actor2\"",
      [1258] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1259] = "   End Object",
      [1260] = "   Begin Object Name=\"DialogueCameraTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueCameraTrack_8'\"",
      [1261] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_8'\"",
      [1262] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueCameraTrack_0'\"",
      [1263] = "      TrackName=\"过肩Actor2\"",
      [1264] = "   End Object",
      [1265] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [1266] = "      AppearanceID=7201007",
      [1267] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [1268] = "      SpawnTransform=(Translation=(X=-0.000001,Y=0.000000,Z=114.297730))",
      [1269] = "      TrackName=\"Actor2\"",
      [1270] = "      StickGround=True",
      [1271] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [1272] = "   End Object",
      [1273] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [1274] = "      Begin Object Name=\"BPS_StateControl_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_18'\"",
      [1275] = "         bFixed=True",
      [1276] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1277] = "         FromLineIndex=0",
      [1278] = "         LineUniqueIDLinked=159A4174444F43AD967BF174D187D514",
      [1279] = "         OwnedEpisodeID=1",
      [1280] = "         StartTime=3.400000",
      [1281] = "      End Object",
      [1282] = "      Begin Object Name=\"BPS_StateControl_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_19'\"",
      [1283] = "         bFixed=True",
      [1284] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1285] = "         FromLineIndex=1",
      [1286] = "         LineUniqueIDLinked=55C280925A20403FAB6AA52E3006EECC",
      [1287] = "         OwnedEpisodeID=1",
      [1288] = "         StartTime=5.370738",
      [1289] = "      End Object",
      [1290] = "      Begin Object Name=\"BPS_StateControl_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_20'\"",
      [1291] = "         bFixed=True",
      [1292] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1293] = "         FromLineIndex=2",
      [1294] = "         LineUniqueIDLinked=AF08161EBB714E369114010EB98A0FE5",
      [1295] = "         OwnedEpisodeID=1",
      [1296] = "         StartTime=13.108041",
      [1297] = "      End Object",
      [1298] = "      Begin Object Name=\"BPS_StateControl_C_21\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_21'\"",
      [1299] = "         bFixed=True",
      [1300] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1301] = "         FromLineIndex=3",
      [1302] = "         LineUniqueIDLinked=754F2EBAB42C4C26A8A5D69A6C3900F2",
      [1303] = "         OwnedEpisodeID=1",
      [1304] = "         StartTime=16.108040",
      [1305] = "      End Object",
      [1306] = "      Begin Object Name=\"BPS_StateControl_C_22\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_22'\"",
      [1307] = "         bFixed=True",
      [1308] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1309] = "         FromLineIndex=4",
      [1310] = "         LineUniqueIDLinked=4D83E672BEAA44A6B7D7DCBB673B915D",
      [1311] = "         OwnedEpisodeID=1",
      [1312] = "         StartTime=19.608040",
      [1313] = "      End Object",
      [1314] = "      Begin Object Name=\"BPS_StateControl_C_23\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_23'\"",
      [1315] = "         bFixed=True",
      [1316] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1317] = "         FromLineIndex=5",
      [1318] = "         LineUniqueIDLinked=7298AB8E88B1425AA4495023D8F217AA",
      [1319] = "         OwnedEpisodeID=1",
      [1320] = "         StartTime=24.608042",
      [1321] = "      End Object",
      [1322] = "      Begin Object Name=\"BPS_StateControl_C_24\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_24'\"",
      [1323] = "         bFixed=True",
      [1324] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1325] = "         FromLineIndex=6",
      [1326] = "         LineUniqueIDLinked=E8F9E4A5CDB54D2C9F1540CA846BDA8A",
      [1327] = "         OwnedEpisodeID=1",
      [1328] = "         StartTime=28.608042",
      [1329] = "      End Object",
      [1330] = "      Begin Object Name=\"BPS_StateControl_C_25\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_25'\"",
      [1331] = "         bFixed=True",
      [1332] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1333] = "         FromLineIndex=7",
      [1334] = "         LineUniqueIDLinked=0E5C23362E5342C1BA47B544C51A9294",
      [1335] = "         OwnedEpisodeID=1",
      [1336] = "         StartTime=32.108044",
      [1337] = "      End Object",
      [1338] = "      Begin Object Name=\"BPS_StateControl_C_26\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_26'\"",
      [1339] = "         bFixed=True",
      [1340] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1341] = "         FromLineIndex=8",
      [1342] = "         LineUniqueIDLinked=6B09313EE70B4EB48F40FFE2AF044B8A",
      [1343] = "         OwnedEpisodeID=1",
      [1344] = "         StartTime=35.108044",
      [1345] = "      End Object",
      [1346] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_18'\"",
      [1347] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_19'\"",
      [1348] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_20'\"",
      [1349] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_21'\"",
      [1350] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_22'\"",
      [1351] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_23'\"",
      [1352] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_24'\"",
      [1353] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_25'\"",
      [1354] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_26'\"",
      [1355] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1356] = "      FromTemplate=False",
      [1357] = "   End Object",
      [1358] = "   Begin Object Name=\"EpisodeGraph_0\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph_0'\"",
      [1359] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph_0.EpisodeGraphEntryNode_1'\"",
      [1360] = "         NodeGuid=A39070F04467E0E7C27A0DA9A28F3777",
      [1361] = "         CustomProperties Pin (PinId=3CC6224347A42A17B19E60B60FED334D,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_5 95CC336F4AE9FD805ECAB28652F25A4D,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [1362] = "      End Object",
      [1363] = "      Begin Object Name=\"EpisodeGraphNode_5\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph_0.EpisodeGraphNode_5'\"",
      [1364] = "         EpisodeID=1",
      [1365] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [1366] = "         NodePosX=300",
      [1367] = "         NodeGuid=57B722764C1605C9FE11BB875C77EFAC",
      [1368] = "         CustomProperties Pin (PinId=95CC336F4AE9FD805ECAB28652F25A4D,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 3CC6224347A42A17B19E60B60FED334D,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [1369] = "      End Object",
      [1370] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [1371] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [1372] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_5'\"",
      [1373] = "      GraphGuid=A27C1FC94A2D7D029E4FC9B9B7108F0F",
      [1374] = "   End Object",
      [1375] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [1376] = "      EpisodeID=1",
      [1377] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [1378] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [1379] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [1380] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [1381] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_4'\"",
      [1382] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_5'\"",
      [1383] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_6'\"",
      [1384] = "      DialogueLines(7)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_7'\"",
      [1385] = "      DialogueLines(8)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_8'\"",
      [1386] = "   End Object",
      [1387] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [1388] = "      EpisodeID=1",
      [1389] = "      ContentIndex=1",
      [1390] = "      GUID=1027611070",
      [1391] = "      UniqueID=159A4174444F43AD967BF174D187D514",
      [1392] = "      Duration=3.500000",
      [1393] = "      ContentString=\"芬尼斯先生，我能和你聊几句吗？让莱昂在这稍等一会儿。\"",
      [1394] = "      ContentUI=\"Default\"",
      [1395] = "      Talker=(PerformerName=\"Actor1\")",
      [1396] = "   End Object",
      [1397] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [1398] = "      EpisodeID=1",
      [1399] = "      ContentIndex=2",
      [1400] = "      GUID=2526149507",
      [1401] = "      UniqueID=55C280925A20403FAB6AA52E3006EECC",
      [1402] = "      Duration=1.970738",
      [1403] = "      ContentString=\"这……好吧。\"",
      [1404] = "      ContentUI=\"Default\"",
      [1405] = "      Talker=(PerformerName=\"Actor2\")",
      [1406] = "   End Object",
      [1407] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [1408] = "      EpisodeID=1",
      [1409] = "      ContentIndex=3",
      [1410] = "      GUID=2501928711",
      [1411] = "      UniqueID=AF08161EBB714E369114010EB98A0FE5",
      [1412] = "      Delay=2.900000",
      [1413] = "      Duration=4.837302",
      [1414] = "      ContentString=\"莱昂对道格倾注了非常强烈的感情，作为他的管家，您比我更清楚。\"",
      [1415] = "      ContentUI=\"Default\"",
      [1416] = "      Talker=(PerformerName=\"Actor1\")",
      [1417] = "   End Object",
      [1418] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [1419] = "      EpisodeID=1",
      [1420] = "      ContentIndex=4",
      [1421] = "      GUID=2634839394",
      [1422] = "      UniqueID=754F2EBAB42C4C26A8A5D69A6C3900F2",
      [1423] = "      Duration=3.000000",
      [1424] = "      ContentString=\"是的。小主人最爱道格，把那只狗看得无比重要。\"",
      [1425] = "      ContentUI=\"Default\"",
      [1426] = "      Talker=(PerformerName=\"Actor2\")",
      [1427] = "   End Object",
      [1428] = "   Begin Object Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [1429] = "      EpisodeID=1",
      [1430] = "      ContentIndex=5",
      [1431] = "      GUID=3250966899",
      [1432] = "      UniqueID=4D83E672BEAA44A6B7D7DCBB673B915D",
      [1433] = "      Duration=3.500000",
      [1434] = "      ContentString=\"说实话，我不明白——那不过是只宠物，可能连人话都听不懂！\"",
      [1435] = "      ContentUI=\"Default\"",
      [1436] = "      Talker=(PerformerName=\"Actor2\")",
      [1437] = "   End Object",
      [1438] = "   Begin Object Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [1439] = "      EpisodeID=1",
      [1440] = "      ContentIndex=6",
      [1441] = "      GUID=224323094",
      [1442] = "      UniqueID=7298AB8E88B1425AA4495023D8F217AA",
      [1443] = "      Delay=0.000002",
      [1444] = "      ContentString=\"先生，莱昂是个纯真的孩子。他付出感情时，不像我们成年人一样顾虑许多。\"",
      [1445] = "      ContentUI=\"Default\"",
      [1446] = "      Talker=(PerformerName=\"Actor1\")",
      [1447] = "   End Object",
      [1448] = "   Begin Object Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [1449] = "      EpisodeID=1",
      [1450] = "      ContentIndex=7",
      [1451] = "      GUID=399312228",
      [1452] = "      UniqueID=E8F9E4A5CDB54D2C9F1540CA846BDA8A",
      [1453] = "      Duration=4.000000",
      [1454] = "      ContentString=\"让他认真对待自己的感情，这并不会影响什么，甚至令人羡慕。\"",
      [1455] = "      ContentUI=\"Default\"",
      [1456] = "      Talker=(PerformerName=\"Actor1\")",
      [1457] = "   End Object",
      [1458] = "   Begin Object Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [1459] = "      EpisodeID=1",
      [1460] = "      ContentIndex=8",
      [1461] = "      GUID=542530340",
      [1462] = "      UniqueID=0E5C23362E5342C1BA47B544C51A9294",
      [1463] = "      Duration=3.500000",
      [1464] = "      ContentString=\"……也许您是对的。哎，年纪大了，总会把事情想复杂。\"",
      [1465] = "      ContentUI=\"Default\"",
      [1466] = "      Talker=(PerformerName=\"Actor2\")",
      [1467] = "   End Object",
      [1468] = "   Begin Object Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [1469] = "      EpisodeID=1",
      [1470] = "      ContentIndex=9",
      [1471] = "      GUID=263855788",
      [1472] = "      UniqueID=6B09313EE70B4EB48F40FFE2AF044B8A",
      [1473] = "      Duration=3.000000",
      [1474] = "      ContentString=\"我会在园外等小主人，感谢您照看他。\"",
      [1475] = "      ContentUI=\"Default\"",
      [1476] = "      Talker=(PerformerName=\"Actor2\")",
      [1477] = "   End Object",
      [1478] = "   Begin Object Name=\"DialogueDialogueTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2'\"",
      [1479] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_0'\"",
      [1480] = "         EpisodeID=1",
      [1481] = "         ContentIndex=1",
      [1482] = "         Talker=\"Actor1\"",
      [1483] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1484] = "         SectionName=\"芬尼斯先生，我能和你聊几句吗？让莱昂在这稍等一会儿。\"",
      [1485] = "         FromLineIndex=0",
      [1486] = "         LineUniqueIDLinked=159A4174444F43AD967BF174D187D514",
      [1487] = "         OwnedEpisodeID=1",
      [1488] = "         Duration=3.500000",
      [1489] = "      End Object",
      [1490] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_1'\"",
      [1491] = "         EpisodeID=1",
      [1492] = "         ContentIndex=2",
      [1493] = "         Talker=\"Actor2\"",
      [1494] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1495] = "         SectionName=\"这……好吧。\"",
      [1496] = "         FromLineIndex=1",
      [1497] = "         LineUniqueIDLinked=55C280925A20403FAB6AA52E3006EECC",
      [1498] = "         OwnedEpisodeID=1",
      [1499] = "         StartTime=3.500000",
      [1500] = "         Duration=1.970738",
      [1501] = "      End Object",
      [1502] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_2'\"",
      [1503] = "         EpisodeID=1",
      [1504] = "         ContentIndex=3",
      [1505] = "         Talker=\"Actor1\"",
      [1506] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1507] = "         SectionName=\"莱昂对道格倾注了非常强烈的感情，作为他的管家，您比我更清楚。\"",
      [1508] = "         FromLineIndex=2",
      [1509] = "         LineUniqueIDLinked=AF08161EBB714E369114010EB98A0FE5",
      [1510] = "         OwnedEpisodeID=1",
      [1511] = "         StartTime=8.370738",
      [1512] = "         Duration=4.837302",
      [1513] = "      End Object",
      [1514] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_3'\"",
      [1515] = "         EpisodeID=1",
      [1516] = "         ContentIndex=4",
      [1517] = "         Talker=\"Actor2\"",
      [1518] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1519] = "         SectionName=\"是的。小主人最爱道格，把那只狗看得无比重要。\"",
      [1520] = "         FromLineIndex=3",
      [1521] = "         LineUniqueIDLinked=754F2EBAB42C4C26A8A5D69A6C3900F2",
      [1522] = "         OwnedEpisodeID=1",
      [1523] = "         StartTime=13.208040",
      [1524] = "         Duration=3.000000",
      [1525] = "      End Object",
      [1526] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_4'\"",
      [1527] = "         EpisodeID=1",
      [1528] = "         ContentIndex=5",
      [1529] = "         Talker=\"Actor2\"",
      [1530] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1531] = "         SectionName=\"说实话，我不明白——那不过是只宠物，可能连人话都听不懂！\"",
      [1532] = "         FromLineIndex=4",
      [1533] = "         LineUniqueIDLinked=4D83E672BEAA44A6B7D7DCBB673B915D",
      [1534] = "         OwnedEpisodeID=1",
      [1535] = "         StartTime=16.208040",
      [1536] = "         Duration=3.500000",
      [1537] = "      End Object",
      [1538] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_5'\"",
      [1539] = "         EpisodeID=1",
      [1540] = "         ContentIndex=6",
      [1541] = "         Talker=\"Actor1\"",
      [1542] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1543] = "         SectionName=\"先生，莱昂是个纯真的孩子。他付出感情时，不像我们成年人一样顾虑许多。\"",
      [1544] = "         FromLineIndex=5",
      [1545] = "         LineUniqueIDLinked=7298AB8E88B1425AA4495023D8F217AA",
      [1546] = "         OwnedEpisodeID=1",
      [1547] = "         StartTime=19.708042",
      [1548] = "         Duration=5.000000",
      [1549] = "      End Object",
      [1550] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_6'\"",
      [1551] = "         EpisodeID=1",
      [1552] = "         ContentIndex=7",
      [1553] = "         Talker=\"Actor1\"",
      [1554] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1555] = "         SectionName=\"让他认真对待自己的感情，这并不会影响什么，甚至令人羡慕。\"",
      [1556] = "         FromLineIndex=6",
      [1557] = "         LineUniqueIDLinked=E8F9E4A5CDB54D2C9F1540CA846BDA8A",
      [1558] = "         OwnedEpisodeID=1",
      [1559] = "         StartTime=24.708042",
      [1560] = "         Duration=4.000000",
      [1561] = "      End Object",
      [1562] = "      Begin Object Name=\"BPS_Dialogue_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_7'\"",
      [1563] = "         EpisodeID=1",
      [1564] = "         ContentIndex=8",
      [1565] = "         Talker=\"Actor2\"",
      [1566] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1567] = "         SectionName=\"……也许您是对的。哎，年纪大了，总会把事情想复杂。\"",
      [1568] = "         FromLineIndex=7",
      [1569] = "         LineUniqueIDLinked=0E5C23362E5342C1BA47B544C51A9294",
      [1570] = "         OwnedEpisodeID=1",
      [1571] = "         StartTime=28.708042",
      [1572] = "         Duration=3.500000",
      [1573] = "      End Object",
      [1574] = "      Begin Object Name=\"BPS_Dialogue_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_2.BPS_Dialogue_C_8'\"",
      [1575] = "         EpisodeID=1",
      [1576] = "         ContentIndex=9",
      [1577] = "         Talker=\"Actor2\"",
      [1578] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_2'\"",
      [1579] = "         SectionName=\"我会在园外等小主人，感谢您照看他。\"",
      [1580] = "         FromLineIndex=8",
      [1581] = "         LineUniqueIDLinked=6B09313EE70B4EB48F40FFE2AF044B8A",
      [1582] = "         OwnedEpisodeID=1",
      [1583] = "         StartTime=32.208042",
      [1584] = "         Duration=3.000000",
      [1585] = "      End Object",
      [1586] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1587] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1588] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1589] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1590] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1591] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1592] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1593] = "      ActionSections(7)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_7'\"",
      [1594] = "      ActionSections(8)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_8'\"",
      [1595] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1596] = "      FromTemplate=False",
      [1597] = "   End Object",
      [1598] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [1599] = "      EpisodeID=1",
      [1600] = "   End Object",
      [1601] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [1602] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [1603] = "         NodeGuid=BA09462244C9644192F051B27DB984F3",
      [1604] = "         CustomProperties Pin (PinId=EDB8749D450E6A66E9785084455E8481,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_3 C828689245BD20F174475CB586FDA90B,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [1605] = "      End Object",
      [1606] = "      Begin Object Name=\"EpisodeGraphNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_1'\"",
      [1607] = "         EpisodeID=1",
      [1608] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [1609] = "         NodePosX=300",
      [1610] = "         NodeGuid=7EA1D8BA4BB238D0620DFB81C346C66B",
      [1611] = "         CustomProperties Pin (PinId=A7809F97469D085DDFB68BBB51D34305,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [1612] = "      End Object",
      [1613] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [1614] = "         EpisodeID=1",
      [1615] = "         OwnerDialogueEpisode=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'********:KGSLDialogueEpisode_0'\"",
      [1616] = "         NodePosX=300",
      [1617] = "         NodeGuid=CE312D204FFD29E7DC45AA9788AF452E",
      [1618] = "         CustomProperties Pin (PinId=C828689245BD20F174475CB586FDA90B,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 EDB8749D450E6A66E9785084455E8481,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [1619] = "      End Object",
      [1620] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [1621] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [1622] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_3'\"",
      [1623] = "      GraphGuid=6EA1FA314C407F397DFDF79DB4DB9DF6",
      [1624] = "   End Object",
      [1625] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [1626] = "   PreLoadArray(1)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [1627] = "   PreLoadArray(2)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C\"",
      [1628] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=12000010),IdleAnimation=(AssetID=\"Idle\"),bIsPlayer=True)",
      [1629] = "   ActorInfos(1)=(PerformerName=\"Actor2\",AppearanceID=(ApperanceID=7201007),IdleAnimation=(AssetID=\"Idle\"))",
      [1630] = "   ActorInfos(2)=(PerformerName=\"Performer1\",AppearanceID=(ApperanceID=7201006),IdleAnimation=(AssetID=\"Idle\"))",
      [1631] = "   ActorInfos(3)=(PerformerName=\"Performer2\",AppearanceID=(ApperanceID=7201007),IdleAnimation=(AssetID=\"Idle\"))",
      [1632] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/TwoPeopleDialogue.TwoPeopleDialogue'\"",
      [1633] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_0'\"",
      [1634] = "   StoryLineID=********",
      [1635] = "   Episodes(0)=(EpisodeID=1,Duration=38.998280,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_1'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackBlackScreenText.BP_DialogueTrackBlackScreenText_C'BP_DialogueTrackBlackScreenText_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackLookAt.BP_DialogueTrackLookAt_C'BP_DialogueTrackLookAt_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueCameraCut.BP_DialogueCameraCut_C'BP_DialogueCameraCut_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueCameraTrack_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_2'\"))",
      [1636] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [1637] = "   PerformerList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_1'\"",
      [1638] = "   PerformerList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_2'\"",
      [1639] = "   PerformerList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_3'\"",
      [1640] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [1641] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [1642] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [1643] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [1644] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_4'\"",
      [1645] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_5'\"",
      [1646] = "   CameraList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_6'\"",
      [1647] = "   CameraList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_7'\"",
      [1648] = "   CameraList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_8'\"",
      [1649] = "   CameraList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_9'\"",
      [1650] = "   CameraList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_10'\"",
      [1651] = "   CameraList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_11'\"",
      [1652] = "   CameraList(12)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_12'\"",
      [1653] = "   CameraList(13)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_13'\"",
      [1654] = "   CameraList(14)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_14'\"",
      [1655] = "   CameraList(15)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_15'\"",
      [1656] = "   CameraList(16)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_16'\"",
      [1657] = "   CameraList(17)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_17'\"",
      [1658] = "   CameraList(18)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_18'\"",
      [1659] = "   CameraList(19)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_19'\"",
      [1660] = "   CameraList(20)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_20'\"",
      [1661] = "   CameraList(21)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_21'\"",
      [1662] = "   CameraList(22)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_22'\"",
      [1663] = "   CameraList(23)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_23'\"",
      [1664] = "   CameraList(24)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_30'\"",
      [1665] = "   CameraList(25)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_31'\"",
      [1666] = "   CameraList(26)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_32'\"",
      [1667] = "   CameraList(27)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_33'\"",
      [1668] = "   CameraList(28)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_34'\"",
      [1669] = "   CameraList(29)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_35'\"",
      [1670] = "   NewEntityList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_1'\"",
      [1671] = "   NewEntityList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_2'\"",
      [1672] = "   NewEntityList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_3'\"",
      [1673] = "   NewEntityList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C'BP_DialogueRoutePoint_C_4'\"",
      [1674] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [1675] = "End Object",
    },
  },
}