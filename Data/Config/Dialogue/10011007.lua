return {
  ["CameraList"] = {
    [1] = {
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["SpawnTransform"] = {
        ["ObjectClass"] = "Transform",
        ["Translation"] = {
          ["X"] = 2650,
          ["Y"] = -5400,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["TrackName"] = "锚点",
    },
    [2] = {
      ["DepthOfFieldFStop"] = 15,
      ["DepthOfFieldFocalDistance"] = 337.8604,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldSensorWidth"] = 300,
      ["FOV"] = 40,
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["ObjectClass"] = "Transform",
        ["Rotation"] = {
          ["W"] = 0.8692,
          ["X"] = 0.0758,
          ["Y"] = 0.1407,
          ["Z"] = -0.468,
        },
        ["Translation"] = {
          ["X"] = -153.4571,
          ["Y"] = 185.1997,
          ["Z"] = 200.8975,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["TrackName"] = "正视Actor1",
      ["bOverride_DepthOfField"] = true,
    },
    [3] = {
      ["FOV"] = 50,
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["ObjectClass"] = "Transform",
        ["Rotation"] = {
          ["W"] = 0.3744,
          ["X"] = 0.0291,
          ["Y"] = -0.0117,
          ["Z"] = 0.9267,
        },
        ["Translation"] = {
          ["X"] = -117.405,
          ["Y"] = 105.2269,
          ["Z"] = 102.1628,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["TrackName"] = "巨响",
    },
    [4] = {
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["Parent"] = "锚点",
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["TrackName"] = "警觉",
    },
    [5] = {
      ["DepthOfFieldFStop"] = 15,
      ["DepthOfFieldFocalDistance"] = 174.6518,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldSensorWidth"] = 200,
      ["FOV"] = 40,
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["ObjectClass"] = "Transform",
        ["Rotation"] = {
          ["W"] = 0.8702,
          ["X"] = 0.0602,
          ["Y"] = 0.1099,
          ["Z"] = -0.4764,
        },
        ["Translation"] = {
          ["X"] = -88.0055,
          ["Y"] = 106.972,
          ["Z"] = 156.1264,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["TrackName"] = "单人镜头",
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 23.5,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1285493635,
                ["B"] = 1586710380,
                ["C"] = -1751799285,
                ["D"] = -1713529820,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["StartTime"] = 17.4,
              ["bFixed"] = true,
            },
            [2] = {
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 232402833,
                ["B"] = -275035084,
                ["C"] = -1383794903,
                ["D"] = 2121666914,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["StartTime"] = 20.4,
              ["bFixed"] = true,
            },
            [3] = {
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1882559686,
                ["B"] = -394377128,
                ["C"] = -1798642218,
                ["D"] = -350321939,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["StartTime"] = 23.4,
              ["bFixed"] = true,
            },
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 3,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Sound.BPS_Sound_C",
              ["OwnedEpisodeID"] = 1,
              ["Sound"] = "/Game/Arts/Audio/Events/Plot/Plot_Common/Plot_MMCZ/Play_Plot_MMCZ_Explosion.Play_Plot_MMCZ_Explosion",
              ["StartTime"] = 20.5,
            },
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackSound.BP_DialogueTrackSound_C",
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Color"] = 1,
              ["Duration"] = 2,
              ["FadeInTime"] = 0,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C",
              ["OwnedEpisodeID"] = 1,
            },
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 16,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_DialogueSequenceCut.BPS_DialogueSequenceCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SequenceAssetID"] = 10000017,
            },
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueSequenceCut.BP_DialogueSequenceCut_C",
        },
        [5] = {
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C",
        },
        [6] = {
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["StartTime"] = 0.0121,
                    },
                    [2] = {
                      ["LineGUIDLinked"] = 3357929773,
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["StartTime"] = 16.1785,
                      ["Visible"] = true,
                    },
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["Parent"] = "Actor1",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Awaken_RubHead",
                        ["StateName"] = "",
                      },
                      ["Duration"] = 1.6504,
                      ["LineGUIDLinked"] = 3357929773,
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0,
                      ["StartTime"] = 15.7628,
                    },
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor1",
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
            },
            [3] = {
              ["DialogueEntity"] = "巨响",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "巨响",
            },
            [4] = {
              ["DialogueEntity"] = "警觉",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "警觉",
            },
            [5] = {
              ["DialogueEntity"] = "单人镜头",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "单人镜头",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["TrackName"] = "锚点",
        },
        [7] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 1,
              ["BreathSpeed"] = 5,
              ["CameraBreathType"] = 4,
              ["Duration"] = 1.5,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 3357929773,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1285493635,
                ["B"] = 1586710380,
                ["C"] = -1751799285,
                ["D"] = -1713529820,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "正视Actor1",
              ["StartTime"] = 16,
              ["TargetCamera"] = "正视Actor1",
            },
            [2] = {
              ["BreathAttenuation"] = 1,
              ["BreathSpeed"] = 3,
              ["CameraBreathType"] = 2,
              ["Duration"] = 3,
              ["LineGUIDLinked"] = 3290082890,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["StartTime"] = 17.5,
              ["TargetCamera"] = "单人镜头",
            },
            [3] = {
              ["BreathAttenuation"] = 0.3,
              ["BreathDelay"] = 0.2,
              ["BreathSpeed"] = 100,
              ["CameraBreathType"] = 6,
              ["Duration"] = 0.7,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["StartTime"] = 20.5,
              ["TargetCamera"] = "巨响",
            },
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
        },
        [8] = {
          ["ActionSections"] = {
            [1] = {
              ["ContentIndex"] = 1,
              ["Duration"] = 1.5,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1285493635,
                ["B"] = 1586710380,
                ["C"] = -1751799285,
                ["D"] = -1713529820,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "呼，好困……怎么回事？",
              ["StartTime"] = 16,
              ["Talker"] = "Actor1",
              ["TalkerName"] = "？？？",
            },
            [2] = {
              ["ContentIndex"] = 2,
              ["Duration"] = 3,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 232402833,
                ["B"] = -275035084,
                ["C"] = -1383794903,
                ["D"] = 2121666914,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "脑袋昏昏沉沉的，好像忘记了什么……",
              ["StartTime"] = 17.5,
              ["Talker"] = "Actor1",
              ["TalkerName"] = "？？？",
            },
            [3] = {
              ["CanSkip"] = false,
              ["ContentIndex"] = 3,
              ["Duration"] = 3,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1882559686,
                ["B"] = -394377128,
                ["C"] = -1798642218,
                ["D"] = -350321939,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "什么情况？！外面怎么这么吵？",
              ["StartTime"] = 20.5,
              ["Talker"] = "Actor1",
              ["TalkerName"] = "？？？",
            },
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
        },
      },
    },
  },
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = true,
  ["Note"] = "支线·美梦成真·第一章第6段",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["AppearanceID"] = 7216002,
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Sitchair_Loop",
        ["StateName"] = "Loop",
      },
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["ObjectClass"] = "Transform",
        ["Rotation"] = {
          ["W"] = 0.7071,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.7071,
        },
        ["Translation"] = {
          ["X"] = 30,
          ["Y"] = -40,
          ["Z"] = 88,
        },
      },
      ["TrackName"] = "Actor1",
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
  },
  ["PreLoadBanks"] = {
    [1] = "Plot_MMCZ",
  },
  ["ZZZ_EditorOnly"] = {
    ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue",
    ["StoryLineID"] = ********,
  },
}