return {
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -14020,
          ["Y"] = 13600,
          ["Z"] = -2000,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4384,
          ["X"] = -0.1662,
          ["Y"] = -0.0829,
          ["Z"] = 0.8794,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 247.6494,
          ["Y"] = -315.1972,
          ["Z"] = 279.0921,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "正视Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "AutoCamera",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.9372,
          ["X"] = -0.0494,
          ["Y"] = -0.3117,
          ["Z"] = 0.1485,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 465.3457,
          ["Y"] = -362.6271,
          ["Z"] = 131.2737,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Camera1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 28.5829,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 66.5092,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 179.992,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6017,
          ["X"] = -0.0154,
          ["Y"] = 0.0116,
          ["Z"] = 0.7985,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 357.1045,
          ["Y"] = -293.1324,
          ["Z"] = 173.4283,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Camera2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2814,
          ["X"] = -0.1664,
          ["Y"] = 0.0496,
          ["Z"] = 0.9438,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 496.6283,
          ["Y"] = -57.9511,
          ["Z"] = 155.2727,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
      },
      ["TrackName"] = "Camera3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
  },
  ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/OnePeopleDialogue.OnePeopleDialogue",
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 11.718,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 876749357,
              ["LineUniqueIDLinked"] = {
                ["A"] = -967394737,
                ["B"] = 1476807557,
                ["C"] = -1807805980,
                ["D"] = -1076595611,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 11.618,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["BgFadeInTime"] = 0.5,
              ["BgFadeOutTime"] = 0.5,
              ["BlackScreenType"] = 0,
              ["Duration"] = 3.5649,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FadeInTime"] = 1,
              ["FadeOutTime"] = 1,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutSection.BPS_FadeInOutSection_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5.7483,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOut.BP_DialogueTrackFadeInOut_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutSection.BPS_FadeInOutSection_C",
          ["TrackName"] = "黑屏",
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["AsideID"] = 60001002,
              ["Duration"] = 3.1355,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FadeInTime"] = 0.5,
              ["FadeOutTime"] = 0.5,
              ["FromLineIndex"] = -1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["NeedPause"] = false,
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_AsideSection.BPS_AsideSection_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 5.7483,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAside.BP_DialogueTrackAside_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_AsideSection.BPS_AsideSection_C",
          ["TrackName"] = "白字",
        },
        [4] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Think",
                        ["StateName"] = "Start",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2.875,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 1,
                      ["LineGUIDLinked"] = 3582699733,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = -967394737,
                        ["B"] = 1476807557,
                        ["C"] = -1807805980,
                        ["D"] = -1076595611,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor1",
                      ["StartTime"] = 2.8733,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "正视Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "正视Actor1",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "AutoCamera",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "AutoCamera",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera1",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera2",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 1.7053,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["Immediate"] = false,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["MoveTarget"] = "RoutePoint2",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                      ["OwnedEpisodeID"] = 1,
                      ["Run"] = false,
                      ["SectionName"] = "Section",
                      ["SpecificAnim"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "",
                        ["StateName"] = "",
                      },
                      ["StartTime"] = 8.718,
                      ["StickGround"] = true,
                      ["TargetActor"] = "",
                      ["TargetTransform"] = {
                        ["Rotation"] = {
                          ["W"] = 1,
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                        ["Scale3D"] = {
                          ["X"] = 1,
                          ["Y"] = 1,
                          ["Z"] = 1,
                        },
                        ["Translation"] = {
                          ["X"] = 0,
                          ["Y"] = 0,
                          ["Z"] = 0,
                        },
                      },
                      ["bConstant"] = true,
                      ["bFixedRotation"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackTransform.BP_DialogueTrackTransform_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Transform.BPS_Transform_C",
                  ["TrackName"] = "移动",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.5,
                      ["Visible"] = false,
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["CameraName"] = "None",
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 7.9218,
                      ["Visible"] = true,
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackVisible.BP_DialogueTrackVisible_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorVisible.BPS_ActorVisible_C",
                  ["TrackName"] = "Visible",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "RoutePoint1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "RoutePoint1",
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "Camera3",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "RoutePoint2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueRoutePointTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "RoutePoint2",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [5] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueAutoCameraCutTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_AutoCameraCut.BPS_AutoCameraCut_C",
          ["TrackName"] = "AutoCameraCut",
        },
        [6] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 6,
              ["CameraName"] = "None",
              ["Duration"] = 2.8497,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 2522171890,
              ["LineUniqueIDLinked"] = {
                ["A"] = 881930778,
                ["B"] = 795887891,
                ["C"] = -2112004572,
                ["D"] = 429491650,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera1",
              ["StartTime"] = 0.0236,
              ["TargetCamera"] = "Camera1",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 2.8733,
              ["TargetCamera"] = "Camera2",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 18,
              ["CameraBreathType"] = 2,
              ["CameraName"] = "None",
              ["Duration"] = 3.8343,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 876749357,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "正视Actor1",
              ["StartTime"] = 7.8837,
              ["TargetCamera"] = "正视Actor1",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [7] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 2.875,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 3582699733,
              ["LineUniqueIDLinked"] = {
                ["A"] = 881930778,
                ["B"] = 795887891,
                ["C"] = -2112004572,
                ["D"] = 429491650,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "那枚领结和约瑟夫的领结一模一样。说不定可以……",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 2.8733,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 876749357,
              ["LineUniqueIDLinked"] = {
                ["A"] = -967394737,
                ["B"] = 1476807557,
                ["C"] = -1807805980,
                ["D"] = -1076595611,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "已经为您修补并熨烫好领结了。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8.718,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["_ContentUI"] = 0,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 123.4723,
          ["Y"] = -170.7651,
          ["Z"] = -60,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "RoutePoint1",
      ["bDefaultVisible"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueRoutePoint.BP_DialogueRoutePoint_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 110,
          ["Y"] = -520,
          ["Z"] = 50,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "RoutePoint2",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000011,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewFemale/A_Base_F_Idle.A_Base_F_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9212,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.3891,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 340,
          ["Y"] = -230,
          ["Z"] = 88.9242,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201022,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewFemale/A_Base_F_Idle.A_Base_F_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9936,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.1129,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -170,
          ["Y"] = -610,
          ["Z"] = 76.9242,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [3] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_RoutePointActor.BP_RoutePointActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["StoryLineID"] = ********,
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnly"] = {
  },
}