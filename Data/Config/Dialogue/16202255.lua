return {
  ["ActorInfos"] = {
    [1] = {
      ["AppearanceID"] = 1200001,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor1",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["AppearanceID"] = 7201036,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor2",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["AppearanceID"] = 7201038,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Peed_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor3",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["AppearanceID"] = 7211087,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Sad_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor4",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [5] = {
      ["AppearanceID"] = 7207020,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Anger_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor5",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [6] = {
      ["AppearanceID"] = 7215021,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Dance",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor6",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [7] = {
      ["AppearanceID"] = 7201014,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Applaud_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor7",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [8] = {
      ["AppearanceID"] = 7201054,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "ShowItem_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor9",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [9] = {
      ["AppearanceID"] = 7201038,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor10",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [10] = {
      ["AppearanceID"] = 7201042,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Stand_Read_Newspaper_Loop",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor11",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [11] = {
      ["AppearanceID"] = 7201038,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Down",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor12",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
    [12] = {
      ["AppearanceID"] = 7201042,
      ["IdleAnimation"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Sitdesk",
        ["StateName"] = "",
      },
      ["InsID"] = "",
      ["PerformerName"] = "Actor13",
      ["UseSceneActor"] = false,
      ["bIsPlayer"] = false,
    },
  },
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_0",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2776,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9607,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1240,
          ["Y"] = -1350,
          ["Z"] = 0,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30.6243,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 1325.3004,
      ["DepthOfFieldFocalRegion"] = 0.0592,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 144.0856,
      ["FOV"] = 55,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_1",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9673,
          ["X"] = 0.028,
          ["Y"] = 0.2198,
          ["Z"] = -0.1238,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1269.2136,
          ["Y"] = 292.7482,
          ["Z"] = 723.1657,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_2",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5134,
          ["X"] = 0.0165,
          ["Y"] = 0.0099,
          ["Z"] = -0.8579,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 307.149,
          ["Y"] = 484.8725,
          ["Z"] = 128.7734,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_3",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9992,
          ["X"] = 0.0008,
          ["Y"] = 0.0331,
          ["Z"] = -0.0227,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -179.6973,
          ["Y"] = 69.842,
          ["Z"] = 174.1997,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_4",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0174,
          ["X"] = -0.0558,
          ["Y"] = 0.001,
          ["Z"] = 0.9983,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 302.4109,
          ["Y"] = 48.7343,
          ["Z"] = 181.5261,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 32,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_5",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6211,
          ["X"] = -0.0109,
          ["Y"] = -0.0087,
          ["Z"] = -0.7836,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 301.4653,
          ["Y"] = 950.8038,
          ["Z"] = 82.8137,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 135,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_6",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0401,
          ["X"] = -0.014,
          ["Y"] = -0.0006,
          ["Z"] = -0.9991,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 142.6114,
          ["Y"] = 10.1865,
          ["Z"] = 71.7408,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 230,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_7",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0837,
          ["X"] = -0.0087,
          ["Y"] = -0.0007,
          ["Z"] = -0.9965,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 226.1417,
          ["Y"] = 18.114,
          ["Z"] = 68.1826,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_8",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0867,
          ["X"] = 0.0972,
          ["Y"] = 0.0085,
          ["Z"] = -0.9914,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 228.0662,
          ["Y"] = 40.095,
          ["Z"] = 80.8265,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 92,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_9",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0864,
          ["X"] = 0.2325,
          ["Y"] = -0.0207,
          ["Z"] = -0.9685,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 70.9463,
          ["Y"] = -174.9786,
          ["Z"] = 142.9226,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 280.6441,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 70,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_10",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.4837,
          ["X"] = 0.0595,
          ["Y"] = -0.033,
          ["Z"] = -0.8726,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 174.6525,
          ["Y"] = -168.9263,
          ["Z"] = 84.031,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_11",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0452,
          ["X"] = 0.0749,
          ["Y"] = -0.0034,
          ["Z"] = -0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 274.6085,
          ["Y"] = 13.1001,
          ["Z"] = 90.1924,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 58.8631,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 322.2153,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor3",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 147.9952,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_12",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7076,
          ["X"] = 0.0308,
          ["Y"] = 0.0309,
          ["Z"] = -0.7052,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 34.3545,
          ["Y"] = 403.7472,
          ["Z"] = 76.1254,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 180,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_13",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9812,
          ["X"] = 0.0157,
          ["Y"] = 0.0911,
          ["Z"] = -0.1695,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -341.7595,
          ["Y"] = 252.3835,
          ["Z"] = 122.8308,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_14",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor3",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3019,
          ["X"] = 0.0532,
          ["Y"] = 0.0169,
          ["Z"] = -0.9517,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 192.7205,
          ["Y"] = 88.4914,
          ["Z"] = 67.1728,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中Actor3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 45,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_16",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor9",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8991,
          ["X"] = 0.0901,
          ["Y"] = 0.2209,
          ["Z"] = -0.3669,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -721.6644,
          ["Y"] = 613.8951,
          ["Z"] = 484.7688,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_17",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor6",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.8006,
          ["X"] = 0.0848,
          ["Y"] = -0.1168,
          ["Z"] = -0.5816,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -217.139,
          ["Y"] = -273.3309,
          ["Z"] = 165.2857,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [18] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_18",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor11",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.019,
          ["X"] = 0.1305,
          ["Y"] = 0.0025,
          ["Z"] = -0.9913,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 456.9881,
          ["Y"] = 87.9459,
          ["Z"] = 136.7207,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera4",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [19] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_19",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor13",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9134,
          ["X"] = -0.0618,
          ["Y"] = 0.1512,
          ["Z"] = 0.3728,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -86.0703,
          ["Y"] = -352.0408,
          ["Z"] = 159.6219,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera5",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [20] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["ObjectName"] = "BP_DialogueCamera_C_20",
      ["OffsetZ"] = 0,
      ["Parent"] = "Model1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4179,
          ["X"] = -0.0079,
          ["Y"] = -0.0036,
          ["Z"] = -0.9085,
        },
        ["Scale3D"] = {
          ["X"] = 0.1,
          ["Y"] = 0.1,
          ["Z"] = 0.1,
        },
        ["Translation"] = {
          ["X"] = 17.6642,
          ["Y"] = 26.2884,
          ["Z"] = 0.9449,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
  },
  ["EnableDOF"] = false,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 41.0511,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 7.9,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [2] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12.525,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [3] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 17.525,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [4] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 22.525,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [5] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 28.025,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [6] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 33.8625,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
            [7] = {
              ["Duration"] = 0.0333,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["ObjectName"] = "BPS_StateControl_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 39.8625,
              ["bConstant"] = false,
              ["bFixed"] = false,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["ObjectName"] = "DialogueStateControlTrack_1",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["ActionSections"] = {
            [1] = {
              ["DarkEdgeFarFogColor"] = {
                ["A"] = 1,
                ["B"] = 0.151,
                ["G"] = 0.151,
                ["R"] = 0.151,
              },
              ["DarkEdgeHeightFogIntensity"] = 0.5,
              ["DarkEdgeNearFogColor"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Duration"] = 19.7713,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["ExcludeActors"] = {
              },
              ["FadeInTime"] = 0,
              ["FadeOutTime"] = 0,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C",
              ["ObjectName"] = "BPS_PostProcess_C_0",
              ["OwnedEpisodeID"] = 1,
              ["PostProcessID"] = 0,
              ["SectionName"] = "Section",
              ["StartTime"] = 0,
              ["Type"] = 2,
              ["bClearAtEnd"] = true,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C",
          ["ObjectName"] = "BP_DialogueTrackPostProcess_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C",
          ["TrackName"] = "PostProcess",
        },
        [3] = {
          ["ActionSections"] = {
            [1] = {
              ["Color"] = 1,
              ["Duration"] = 0.5197,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FadeInTime"] = 2,
              ["FadeOutTime"] = 0,
              ["FromLineIndex"] = -1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C",
              ["ObjectName"] = "BPS_FadeInOutPureColor_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 40.5313,
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C",
          ["ObjectName"] = "BP_DialogueTrackFadeInOutPureColor_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C",
          ["TrackName"] = "纯色淡入淡出",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 3.6849,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = -1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "近景Actor3",
              ["StartTime"] = 3.2643,
              ["TargetCamera"] = "近景Actor3",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C",
          ["ObjectName"] = "BP_DialogueTrackCameraAnim_C_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCutAnim",
        },
        [5] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景2",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["ObjectName"] = "DialogueTrackCamera_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_6",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近景Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_7",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_8",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [7] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_9",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近景Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_10",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_11",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["ObjectName"] = "DialogueTrackActor_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景Actor3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_12",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "近景Actor3",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_13",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "过肩Actor3",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中Actor3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_14",
                  ["Parent"] = "Actor3",
                  ["TrackName"] = "中Actor3",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_1",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor3",
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor4",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_2",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor4",
            },
            [10] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor5",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_3",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor5",
            },
            [11] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera3",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_17",
                  ["Parent"] = "Actor6",
                  ["TrackName"] = "Camera3",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor6",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_4",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor6",
            },
            [12] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor7",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_5",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor7",
            },
            [13] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_16",
                  ["Parent"] = "Actor9",
                  ["TrackName"] = "Camera2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor9",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_6",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor9",
            },
            [14] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "HoldHead_Pain",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 4.625,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 1,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["ObjectName"] = "BPS_PlayAnimation_C_0",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor10",
                      ["StartTime"] = 8,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["ObjectName"] = "BP_DialogueTrackAnimation_C_8",
                  ["Parent"] = "",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor10",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_7",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor10",
            },
            [15] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera4",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_18",
                  ["Parent"] = "Actor11",
                  ["TrackName"] = "Camera4",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor11",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_8",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor11",
            },
            [16] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Actor12",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_9",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor12",
            },
            [17] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera5",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_19",
                  ["Parent"] = "Actor13",
                  ["TrackName"] = "Camera5",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor13",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialoguePerformerTrack",
              ["ObjectName"] = "DialoguePerformerTrack_10",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor13",
            },
            [18] = {
              ["Actions"] = {
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "Camera1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["ObjectName"] = "DialogueTrackCamera_20",
                  ["Parent"] = "Model1",
                  ["TrackName"] = "Camera1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Model1",
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C",
              ["ObjectName"] = "BP_DialogueTrackModel_C_0",
              ["Parent"] = "锚点",
              ["TrackName"] = "Model1",
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["ObjectName"] = "DialogueTrackCamera_0",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [6] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 20,
              ["BreathSpeed"] = 1.5,
              ["CameraBreathType"] = 10,
              ["CameraName"] = "None",
              ["Duration"] = 8,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_0",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "远景",
              ["StartTime"] = 0,
              ["TargetCamera"] = "远景",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 4.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_1",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "过肩Actor3",
              ["StartTime"] = 8,
              ["TargetCamera"] = "过肩Actor3",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 4,
              ["CameraName"] = "None",
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_2",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera2",
              ["StartTime"] = 12.625,
              ["TargetCamera"] = "Camera2",
              ["bConstant"] = true,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 25,
              ["BreathSpeed"] = 6,
              ["CameraBreathType"] = 1,
              ["CameraName"] = "None",
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_3",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera1",
              ["StartTime"] = 17.625,
              ["TargetCamera"] = "Camera1",
              ["bConstant"] = true,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150.1,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 4,
              ["CameraName"] = "None",
              ["Duration"] = 5.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_4",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera3",
              ["StartTime"] = 22.625,
              ["TargetCamera"] = "Camera3",
              ["bConstant"] = true,
            },
            [6] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 0,
              ["CameraName"] = "None",
              ["Duration"] = 5.8375,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 5,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_5",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera4",
              ["StartTime"] = 28.125,
              ["TargetCamera"] = "Camera4",
              ["bConstant"] = true,
            },
            [7] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 10,
              ["CameraBreathType"] = 6,
              ["CameraName"] = "None",
              ["Duration"] = 6,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 6,
              ["ImmediateCut"] = true,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["ObjectName"] = "BPS_CameraCut_C_6",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Camera5",
              ["StartTime"] = 33.9625,
              ["TargetCamera"] = "Camera5",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["ObjectName"] = "DialogueCameraCutAction_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [7] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["Duration"] = 8,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_0",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "我早年意外成为非凡者后，为了获得更多创作的灵感，我违背了很多原则。我过于深入别人的生活，窥探人们最隐秘的情感。也正因如此，我写下了无数杰作。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["Duration"] = 4.625,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_1",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 8,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_2",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 12.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["Duration"] = 5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_3",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 17.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["Duration"] = 5.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_4",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 22.625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [6] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 6,
              ["ContentUI"] = 0,
              ["Duration"] = 5.8375,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 5,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_5",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 28.125,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [7] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 7,
              ["ContentUI"] = 0,
              ["Duration"] = 6,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 6,
              ["LineUniqueIDLinked"] = {
                ["A"] = 0,
                ["B"] = 0,
                ["C"] = 0,
                ["D"] = 0,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["ObjectName"] = "BPS_Dialogue_C_6",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 33.9625,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["ObjectName"] = "DialogueDialogueTrack_0",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C",
      ["FollowParentSocket"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C",
      ["ObjectName"] = "BP_DialogueEntityModel_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.7071,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.7071,
        },
        ["Scale3D"] = {
          ["X"] = 10,
          ["Y"] = 10,
          ["Z"] = 10,
        },
        ["Translation"] = {
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 1830,
        },
      },
      ["StaticMesh"] = "/Game/Arts/Environment/Mesh/Props/Gameplay/SM_Interact_06.SM_Interact_06",
      ["StickGround"] = false,
      ["TrackName"] = "Model1",
      ["bDefaultVisible"] = true,
    },
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 1200001,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_0",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9848,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.1736,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -246.0646,
          ["Y"] = 34.0054,
          ["Z"] = 89.806,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201036,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_1",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1195,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9928,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -77.8095,
          ["Y"] = 104.6515,
          ["Z"] = 78.125,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201038,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Peed",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_2",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2588,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9659,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -883.9176,
          ["Y"] = 1026.8341,
          ["Z"] = 120,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor3",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7211087,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Sad",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_3",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1210.5433,
          ["Y"] = 1057.3205,
          ["Z"] = 120,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor4",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7207020,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Anger",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_4",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0872,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -1077.0313,
          ["Y"] = 1070.5754,
          ["Z"] = 120,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor5",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7215021,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Dance",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_5",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 278.5014,
          ["Y"] = 1133.1977,
          ["Z"] = 90,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor6",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201014,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Applaud",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_6",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5735,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.8192,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 296.8733,
          ["Y"] = 1310.3242,
          ["Z"] = 90,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor7",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201054,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "ShowItem",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_8",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 1,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 2303.9582,
          ["Y"] = -2791.368,
          ["Z"] = 80,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor9",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201038,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_9",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -1,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 2401.4246,
          ["Y"] = -2777.1981,
          ["Z"] = 80,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor10",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201042,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Stand_Read_Newspaper",
        ["StateName"] = "Loop",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_10",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.0872,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 1383.3388,
          ["Y"] = -262.6521,
          ["Z"] = 70,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor11",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201038,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Down",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_12",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.866,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.5,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -208.4597,
          ["Y"] = -888.0768,
          ["Z"] = 80,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor12",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7201042,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Sitdesk",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["ObjectName"] = "BP_DialogueActor_C_13",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0871,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.9962,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 54.5193,
          ["Y"] = -911.4049,
          ["Z"] = 80,
        },
      },
      ["StickGround"] = false,
      ["TrackName"] = "Actor13",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C",
    [3] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnlyInfo"] = {
    ["AssetInfo"] = {
      [1] = "Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C Name=\"********\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'/Temp/DialogueTransientPackage.********'\"",
      [2] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [3] = "   End Object",
      [4] = "   Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraph Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [5] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphEntryNode Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [6] = "      End Object",
      [7] = "      Begin Object Class=/Script/KGStoryLineEditor.EpisodeGraphNode Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [8] = "      End Object",
      [9] = "   End Object",
      [10] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1'\"",
      [11] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_0'\"",
      [12] = "      End Object",
      [13] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_1'\"",
      [14] = "      End Object",
      [15] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_2'\"",
      [16] = "      End Object",
      [17] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_3'\"",
      [18] = "      End Object",
      [19] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_4'\"",
      [20] = "      End Object",
      [21] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_5'\"",
      [22] = "      End Object",
      [23] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_6'\"",
      [24] = "      End Object",
      [25] = "   End Object",
      [26] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C Name=\"BP_DialogueTrackPostProcess_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackPostProcess_C_0'\"",
      [27] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C Name=\"BPS_PostProcess_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackPostProcess_C_0.BPS_PostProcess_C_0'\"",
      [28] = "      End Object",
      [29] = "   End Object",
      [30] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C Name=\"BP_DialogueTrackFadeInOutPureColor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackFadeInOutPureColor_C_0'\"",
      [31] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C Name=\"BPS_FadeInOutPureColor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackFadeInOutPureColor_C_0.BPS_FadeInOutPureColor_C_0'\"",
      [32] = "      End Object",
      [33] = "   End Object",
      [34] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C Name=\"BP_DialogueTrackCameraAnim_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackCameraAnim_C_0'\"",
      [35] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackCameraAnim_C_0.BPS_CameraCut_C_1'\"",
      [36] = "      End Object",
      [37] = "   End Object",
      [38] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [39] = "   End Object",
      [40] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraCutTrack Name=\"DialogueCameraCutAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0'\"",
      [41] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_0'\"",
      [42] = "      End Object",
      [43] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_1'\"",
      [44] = "      End Object",
      [45] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_2'\"",
      [46] = "      End Object",
      [47] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_3'\"",
      [48] = "      End Object",
      [49] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_4'\"",
      [50] = "      End Object",
      [51] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_5'\"",
      [52] = "      End Object",
      [53] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_6'\"",
      [54] = "      End Object",
      [55] = "   End Object",
      [56] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0'\"",
      [57] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_0'\"",
      [58] = "      End Object",
      [59] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_1'\"",
      [60] = "      End Object",
      [61] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_2'\"",
      [62] = "      End Object",
      [63] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_3'\"",
      [64] = "      End Object",
      [65] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_4'\"",
      [66] = "      End Object",
      [67] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_5'\"",
      [68] = "      End Object",
      [69] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_6'\"",
      [70] = "      End Object",
      [71] = "   End Object",
      [72] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [73] = "   End Object",
      [74] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [75] = "   End Object",
      [76] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [77] = "   End Object",
      [78] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [79] = "   End Object",
      [80] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_4'\"",
      [81] = "   End Object",
      [82] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_5'\"",
      [83] = "   End Object",
      [84] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_6'\"",
      [85] = "   End Object",
      [86] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_8'\"",
      [87] = "   End Object",
      [88] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_9'\"",
      [89] = "   End Object",
      [90] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_10'\"",
      [91] = "   End Object",
      [92] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_12'\"",
      [93] = "   End Object",
      [94] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C Name=\"BP_DialogueActor_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_13'\"",
      [95] = "   End Object",
      [96] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [97] = "   End Object",
      [98] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [99] = "   End Object",
      [100] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [101] = "   End Object",
      [102] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [103] = "   End Object",
      [104] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [105] = "   End Object",
      [106] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [107] = "   End Object",
      [108] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [109] = "   End Object",
      [110] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [111] = "   End Object",
      [112] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [113] = "   End Object",
      [114] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [115] = "   End Object",
      [116] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [117] = "   End Object",
      [118] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [119] = "   End Object",
      [120] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [121] = "   End Object",
      [122] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [123] = "   End Object",
      [124] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [125] = "   End Object",
      [126] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [127] = "   End Object",
      [128] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [129] = "   End Object",
      [130] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [131] = "   End Object",
      [132] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [133] = "   End Object",
      [134] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [135] = "   End Object",
      [136] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C Name=\"BP_DialogueEntityModel_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C'/Temp/DialogueTransientPackage.********:BP_DialogueEntityModel_C_0'\"",
      [137] = "   End Object",
      [138] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [139] = "   End Object",
      [140] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [141] = "   End Object",
      [142] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [143] = "   End Object",
      [144] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_4'\"",
      [145] = "   End Object",
      [146] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_5'\"",
      [147] = "   End Object",
      [148] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueActorTrack Name=\"DialogueTrackActor_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_1'\"",
      [149] = "   End Object",
      [150] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C Name=\"BP_DialogueTrackModel_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackModel_C_0'\"",
      [151] = "   End Object",
      [152] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_20'\"",
      [153] = "   End Object",
      [154] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_19'\"",
      [155] = "   End Object",
      [156] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_18'\"",
      [157] = "   End Object",
      [158] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C Name=\"BP_DialogueTrackAnimation_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_8'\"",
      [159] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_8.BPS_PlayAnimation_C_0'\"",
      [160] = "      End Object",
      [161] = "   End Object",
      [162] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_16'\"",
      [163] = "   End Object",
      [164] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_17'\"",
      [165] = "   End Object",
      [166] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_12'\"",
      [167] = "   End Object",
      [168] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_13'\"",
      [169] = "   End Object",
      [170] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_14'\"",
      [171] = "   End Object",
      [172] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_9'\"",
      [173] = "   End Object",
      [174] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_10'\"",
      [175] = "   End Object",
      [176] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_11'\"",
      [177] = "   End Object",
      [178] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_6'\"",
      [179] = "   End Object",
      [180] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_7'\"",
      [181] = "   End Object",
      [182] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueCameraTrack Name=\"DialogueTrackCamera_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_8'\"",
      [183] = "   End Object",
      [184] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [185] = "   End Object",
      [186] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [187] = "   End Object",
      [188] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [189] = "   End Object",
      [190] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [191] = "   End Object",
      [192] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [193] = "   End Object",
      [194] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [195] = "   End Object",
      [196] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [197] = "   End Object",
      [198] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [199] = "   End Object",
      [200] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [201] = "   End Object",
      [202] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [203] = "   End Object",
      [204] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [205] = "   End Object",
      [206] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [207] = "   End Object",
      [208] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [209] = "   End Object",
      [210] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [211] = "   End Object",
      [212] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [213] = "   End Object",
      [214] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [215] = "   End Object",
      [216] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [217] = "   End Object",
      [218] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [219] = "   End Object",
      [220] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [221] = "   End Object",
      [222] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [223] = "   End Object",
      [224] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [225] = "   End Object",
      [226] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [227] = "   End Object",
      [228] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [229] = "   End Object",
      [230] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [231] = "   End Object",
      [232] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [233] = "   End Object",
      [234] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [235] = "   End Object",
      [236] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [237] = "   End Object",
      [238] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [239] = "   End Object",
      [240] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [241] = "   End Object",
      [242] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [243] = "   End Object",
      [244] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueDialogueTrack Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [245] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_0'\"",
      [246] = "      End Object",
      [247] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_1'\"",
      [248] = "      End Object",
      [249] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_2'\"",
      [250] = "      End Object",
      [251] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_3'\"",
      [252] = "      End Object",
      [253] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_4'\"",
      [254] = "      End Object",
      [255] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_5'\"",
      [256] = "      End Object",
      [257] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_6'\"",
      [258] = "      End Object",
      [259] = "   End Object",
      [260] = "   Begin Object Class=/Script/KGStoryLineEditor.DialogueStateControlTrack Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [261] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [262] = "      End Object",
      [263] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_1'\"",
      [264] = "      End Object",
      [265] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_2'\"",
      [266] = "      End Object",
      [267] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_3'\"",
      [268] = "      End Object",
      [269] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_4'\"",
      [270] = "      End Object",
      [271] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_5'\"",
      [272] = "      End Object",
      [273] = "      Begin Object Class=/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_6'\"",
      [274] = "      End Object",
      [275] = "   End Object",
      [276] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [277] = "   End Object",
      [278] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [279] = "   End Object",
      [280] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_2'\"",
      [281] = "   End Object",
      [282] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_3'\"",
      [283] = "   End Object",
      [284] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_4'\"",
      [285] = "   End Object",
      [286] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_5'\"",
      [287] = "   End Object",
      [288] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_6'\"",
      [289] = "   End Object",
      [290] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_7'\"",
      [291] = "   End Object",
      [292] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_8'\"",
      [293] = "   End Object",
      [294] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_9'\"",
      [295] = "   End Object",
      [296] = "   Begin Object Class=/Script/KGStoryLineEditor.DialoguePerformerTrack Name=\"DialoguePerformerTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_10'\"",
      [297] = "   End Object",
      [298] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [299] = "   End Object",
      [300] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueEpisode Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [301] = "   End Object",
      [302] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_14\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_14'\"",
      [303] = "   End Object",
      [304] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_14'\"",
      [305] = "   End Object",
      [306] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_15\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_15'\"",
      [307] = "   End Object",
      [308] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_15'\"",
      [309] = "   End Object",
      [310] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_16\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_16'\"",
      [311] = "   End Object",
      [312] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_16'\"",
      [313] = "   End Object",
      [314] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_17\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_17'\"",
      [315] = "   End Object",
      [316] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_17'\"",
      [317] = "   End Object",
      [318] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_18\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_18'\"",
      [319] = "   End Object",
      [320] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_18'\"",
      [321] = "   End Object",
      [322] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_19\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_19'\"",
      [323] = "   End Object",
      [324] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_19'\"",
      [325] = "   End Object",
      [326] = "   Begin Object Class=/Script/KGStoryLineEditor.KGSLDialogueLine Name=\"KGSLDialogueLine_20\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_20'\"",
      [327] = "   End Object",
      [328] = "   Begin Object Class=/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C Name=\"BP_DLExtensionData_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_20'\"",
      [329] = "   End Object",
      [330] = "   Begin Object Name=\"KGSLDialogueEpisode_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_2'\"",
      [331] = "      EpisodeID=1",
      [332] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_7'\"",
      [333] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_8'\"",
      [334] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_9'\"",
      [335] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_10'\"",
      [336] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_11'\"",
      [337] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_12'\"",
      [338] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_13'\"",
      [339] = "   End Object",
      [340] = "   Begin Object Name=\"EpisodeGraph\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraph'/Temp/DialogueTransientPackage.********:EpisodeGraph'\"",
      [341] = "      Begin Object Name=\"EpisodeGraphEntryNode_1\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphEntryNode_1'\"",
      [342] = "         NodeGuid=69F91EF44FCDD66804BE4C83AE8339DC",
      [343] = "         CustomProperties Pin (PinId=54B2232A4622917FA6D89189F8081DD7,Direction=\"EGPD_Output\",PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphNode_3 3DB3A59C4F9444D8F4411FAA05019259,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [344] = "      End Object",
      [345] = "      Begin Object Name=\"EpisodeGraphNode_3\" ExportPath=\"/Script/KGStoryLineEditor.EpisodeGraphNode'/Temp/DialogueTransientPackage.********:EpisodeGraph.EpisodeGraphNode_3'\"",
      [346] = "         EpisodeID=1",
      [347] = "         NodePosX=300",
      [348] = "         NodeGuid=02F8B3B54FC059D7BE7E8E9BF67DD4F6",
      [349] = "         CustomProperties Pin (PinId=3DB3A59C4F9444D8F4411FAA05019259,PinType.PinCategory=\"\",PinType.PinSubCategory=\"\",PinType.PinSubCategoryObject=None,PinType.PinSubCategoryMemberReference=(),PinType.PinValueType=(),PinType.ContainerType=None,PinType.bIsReference=False,PinType.bIsConst=False,PinType.bIsWeakPointer=False,PinType.bIsUObjectWrapper=False,PinType.bSerializeAsSinglePrecisionFloat=False,LinkedTo=(EpisodeGraphEntryNode_1 54B2232A4622917FA6D89189F8081DD7,),PersistentGuid=00000000000000000000000000000000,bHidden=False,bNotConnectable=False,bDefaultValueIsReadOnly=False,bDefaultValueIsIgnored=False,bAdvancedView=False,bOrphanedPin=False,)",
      [350] = "      End Object",
      [351] = "      Schema=\"/Script/CoreUObject.Class'/Script/KGStoryLineEditor.EpisodeGraphSchema'\"",
      [352] = "      Nodes(0)=\"/Script/KGStoryLineEditor.EpisodeGraphEntryNode'EpisodeGraphEntryNode_1'\"",
      [353] = "      Nodes(1)=\"/Script/KGStoryLineEditor.EpisodeGraphNode'EpisodeGraphNode_3'\"",
      [354] = "      GraphGuid=59C7C6DE4FBC4A2C28A1CC9F740D293D",
      [355] = "   End Object",
      [356] = "   Begin Object Name=\"DialogueStateControlAction_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1'\"",
      [357] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_0'\"",
      [358] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [359] = "         FromLineIndex=0",
      [360] = "         LineGUIDLinked=1810087296",
      [361] = "         OwnedEpisodeID=1",
      [362] = "         StartTime=7.900000",
      [363] = "      End Object",
      [364] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_1'\"",
      [365] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [366] = "         FromLineIndex=1",
      [367] = "         LineGUIDLinked=2003163743",
      [368] = "         OwnedEpisodeID=1",
      [369] = "         StartTime=12.525000",
      [370] = "      End Object",
      [371] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_2'\"",
      [372] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [373] = "         FromLineIndex=2",
      [374] = "         LineGUIDLinked=3546257477",
      [375] = "         OwnedEpisodeID=1",
      [376] = "         StartTime=17.525000",
      [377] = "      End Object",
      [378] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_3'\"",
      [379] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [380] = "         FromLineIndex=3",
      [381] = "         LineGUIDLinked=526793861",
      [382] = "         OwnedEpisodeID=1",
      [383] = "         StartTime=22.525000",
      [384] = "      End Object",
      [385] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_4'\"",
      [386] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [387] = "         FromLineIndex=4",
      [388] = "         LineGUIDLinked=3346857647",
      [389] = "         OwnedEpisodeID=1",
      [390] = "         StartTime=28.025000",
      [391] = "      End Object",
      [392] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_5'\"",
      [393] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [394] = "         FromLineIndex=5",
      [395] = "         LineGUIDLinked=970648999",
      [396] = "         OwnedEpisodeID=1",
      [397] = "         StartTime=33.862499",
      [398] = "      End Object",
      [399] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlAction_1.BPS_StateControl_C_6'\"",
      [400] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlAction_1'\"",
      [401] = "         FromLineIndex=6",
      [402] = "         LineGUIDLinked=491772944",
      [403] = "         OwnedEpisodeID=1",
      [404] = "         StartTime=39.862499",
      [405] = "      End Object",
      [406] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [407] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [408] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [409] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [410] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [411] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [412] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [413] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [414] = "      FromTemplate=False",
      [415] = "   End Object",
      [416] = "   Begin Object Name=\"BP_DialogueTrackPostProcess_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackPostProcess_C_0'\"",
      [417] = "      Begin Object Name=\"BPS_PostProcess_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackPostProcess_C_0.BPS_PostProcess_C_0'\"",
      [418] = "         Type=NewEnumerator1",
      [419] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C'********:BP_DialogueTrackPostProcess_C_0'\"",
      [420] = "         OwnedEpisodeID=1",
      [421] = "         Duration=19.771330",
      [422] = "      End Object",
      [423] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PostProcess.BPS_PostProcess_C'BPS_PostProcess_C_0'\"",
      [424] = "      FromTemplate=False",
      [425] = "   End Object",
      [426] = "   Begin Object Name=\"BP_DialogueTrackFadeInOutPureColor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackFadeInOutPureColor_C_0'\"",
      [427] = "      Begin Object Name=\"BPS_FadeInOutPureColor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackFadeInOutPureColor_C_0.BPS_FadeInOutPureColor_C_0'\"",
      [428] = "         FadeInTime=2.000000",
      [429] = "         FadeOutTime=0.000000",
      [430] = "         Color=NewEnumerator1",
      [431] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C'********:BP_DialogueTrackFadeInOutPureColor_C_0'\"",
      [432] = "         OwnedEpisodeID=1",
      [433] = "         StartTime=40.531342",
      [434] = "         Duration=0.519730",
      [435] = "      End Object",
      [436] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_FadeInOutPureColor.BPS_FadeInOutPureColor_C'BPS_FadeInOutPureColor_C_0'\"",
      [437] = "      FromTemplate=False",
      [438] = "   End Object",
      [439] = "   Begin Object Name=\"BP_DialogueTrackCameraAnim_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackCameraAnim_C_0'\"",
      [440] = "      Begin Object Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackCameraAnim_C_0.BPS_CameraCut_C_1'\"",
      [441] = "         TargetCamera=(CameraName=\"近景Actor3\")",
      [442] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C'********:BP_DialogueTrackCameraAnim_C_0'\"",
      [443] = "         SectionName=NSLOCTEXT(\"\", \"530764AB4382E1C5180C44BAA5ACFC1C\", \"近景Actor3\")",
      [444] = "         OwnedEpisodeID=1",
      [445] = "         StartTime=3.264264",
      [446] = "         Duration=3.684857",
      [447] = "      End Object",
      [448] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_1'\"",
      [449] = "      FromTemplate=False",
      [450] = "   End Object",
      [451] = "   Begin Object Name=\"DialogueTrackCamera_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_0'\"",
      [452] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [453] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_1'\"",
      [454] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_2'\"",
      [455] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_3'\"",
      [456] = "      Childs(3)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_4'\"",
      [457] = "      Childs(4)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_5'\"",
      [458] = "      Childs(5)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [459] = "      Childs(6)=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_1'\"",
      [460] = "      Childs(7)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_1'\"",
      [461] = "      Childs(8)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_2'\"",
      [462] = "      Childs(9)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_3'\"",
      [463] = "      Childs(10)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_4'\"",
      [464] = "      Childs(11)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_5'\"",
      [465] = "      Childs(12)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_6'\"",
      [466] = "      Childs(13)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_7'\"",
      [467] = "      Childs(14)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_8'\"",
      [468] = "      Childs(15)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_9'\"",
      [469] = "      Childs(16)=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_10'\"",
      [470] = "      Childs(17)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C'********:BP_DialogueTrackModel_C_0'\"",
      [471] = "      TrackName=\"锚点\"",
      [472] = "   End Object",
      [473] = "   Begin Object Name=\"DialogueCameraCutAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0'\"",
      [474] = "      Begin Object Name=\"BPS_CameraCut_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_0'\"",
      [475] = "         CameraBreathType=NewEnumerator9",
      [476] = "         BreathSpeed=1.500000",
      [477] = "         BreathFocusDistance=20.000000",
      [478] = "         TargetCamera=(CameraName=\"远景\")",
      [479] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [480] = "         SectionName=NSLOCTEXT(\"\", \"6FA8AE684B59BB07EE91849EF5A86053\", \"远景\")",
      [481] = "         FromLineIndex=0",
      [482] = "         LineGUIDLinked=1810087296",
      [483] = "         OwnedEpisodeID=1",
      [484] = "         Duration=8.000000",
      [485] = "      End Object",
      [486] = "      Begin Object Name=\"BPS_CameraCut_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_1'\"",
      [487] = "         TargetCamera=(CameraName=\"过肩Actor3\")",
      [488] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [489] = "         SectionName=NSLOCTEXT(\"\", \"55BF544B4685A417109BA4AD776E4823\", \"过肩Actor3\")",
      [490] = "         FromLineIndex=1",
      [491] = "         LineGUIDLinked=2003163743",
      [492] = "         OwnedEpisodeID=1",
      [493] = "         StartTime=8.000000",
      [494] = "         Duration=4.625000",
      [495] = "      End Object",
      [496] = "      Begin Object Name=\"BPS_CameraCut_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_2'\"",
      [497] = "         CameraBreathType=NewEnumerator4",
      [498] = "         TargetCamera=(CameraName=\"Camera2\")",
      [499] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [500] = "         SectionName=NSLOCTEXT(\"\", \"6251FD2847229406BA97029899C969DB\", \"Camera2\")",
      [501] = "         FromLineIndex=2",
      [502] = "         LineGUIDLinked=3546257477",
      [503] = "         OwnedEpisodeID=1",
      [504] = "         StartTime=12.625000",
      [505] = "         Duration=5.000000",
      [506] = "      End Object",
      [507] = "      Begin Object Name=\"BPS_CameraCut_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_3'\"",
      [508] = "         CameraBreathType=NewEnumerator0",
      [509] = "         BreathSpeed=6.000000",
      [510] = "         BreathFocusDistance=25.000000",
      [511] = "         TargetCamera=(CameraName=\"Camera1\")",
      [512] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [513] = "         SectionName=NSLOCTEXT(\"\", \"05985E814B66729AE44C2CAC0876314A\", \"Camera1\")",
      [514] = "         FromLineIndex=3",
      [515] = "         LineGUIDLinked=526793861",
      [516] = "         OwnedEpisodeID=1",
      [517] = "         StartTime=17.625000",
      [518] = "         Duration=5.000000",
      [519] = "      End Object",
      [520] = "      Begin Object Name=\"BPS_CameraCut_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_4'\"",
      [521] = "         CameraBreathType=NewEnumerator4",
      [522] = "         BreathSpeed=10.000000",
      [523] = "         BreathFocusDistance=150.100000",
      [524] = "         TargetCamera=(CameraName=\"Camera3\")",
      [525] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [526] = "         SectionName=NSLOCTEXT(\"\", \"A0937CCE4EC5FA3F45C444A2BD8F1AA4\", \"Camera3\")",
      [527] = "         FromLineIndex=4",
      [528] = "         LineGUIDLinked=3346857647",
      [529] = "         OwnedEpisodeID=1",
      [530] = "         StartTime=22.625000",
      [531] = "         Duration=5.500000",
      [532] = "      End Object",
      [533] = "      Begin Object Name=\"BPS_CameraCut_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_5'\"",
      [534] = "         TargetCamera=(CameraName=\"Camera4\")",
      [535] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [536] = "         SectionName=NSLOCTEXT(\"\", \"803CF20D43F5703A8D08A09D268CD301\", \"Camera4\")",
      [537] = "         FromLineIndex=5",
      [538] = "         LineGUIDLinked=970648999",
      [539] = "         OwnedEpisodeID=1",
      [540] = "         StartTime=28.125000",
      [541] = "         Duration=5.837500",
      [542] = "      End Object",
      [543] = "      Begin Object Name=\"BPS_CameraCut_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'/Temp/DialogueTransientPackage.********:DialogueCameraCutAction_0.BPS_CameraCut_C_6'\"",
      [544] = "         CameraBreathType=NewEnumerator5",
      [545] = "         BreathSpeed=10.000000",
      [546] = "         TargetCamera=(CameraName=\"Camera5\")",
      [547] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'********:DialogueCameraCutAction_0'\"",
      [548] = "         SectionName=NSLOCTEXT(\"\", \"2D221BB148833B9AD8F3A3B41164B544\", \"Camera5\")",
      [549] = "         FromLineIndex=6",
      [550] = "         LineGUIDLinked=491772944",
      [551] = "         OwnedEpisodeID=1",
      [552] = "         StartTime=33.962502",
      [553] = "         Duration=6.000000",
      [554] = "      End Object",
      [555] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_0'\"",
      [556] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_1'\"",
      [557] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_2'\"",
      [558] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_3'\"",
      [559] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_4'\"",
      [560] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_5'\"",
      [561] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'BPS_CameraCut_C_6'\"",
      [562] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C'\"",
      [563] = "      FromTemplate=False",
      [564] = "   End Object",
      [565] = "   Begin Object Name=\"DialogueDialogueAction_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0'\"",
      [566] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_0'\"",
      [567] = "         EpisodeID=1",
      [568] = "         ContentIndex=1",
      [569] = "         Talker=\"Actor2\"",
      [570] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [571] = "         SectionName=NSLOCTEXT(\"\", \"E2DA5D234BC80F9D1D6D64BD386CA629\", \"我早年意外成为非凡者后，为了获得更多创作的灵感，我违背了很多原则。我过于深入别人的生活，窥探人们最隐秘的情感。也正因如此，我写下了无数杰作。\")",
      [572] = "         FromLineIndex=0",
      [573] = "         LineGUIDLinked=1810087296",
      [574] = "         OwnedEpisodeID=1",
      [575] = "         Duration=8.000000",
      [576] = "      End Object",
      [577] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_1'\"",
      [578] = "         EpisodeID=1",
      [579] = "         ContentIndex=2",
      [580] = "         Talker=\"Actor2\"",
      [581] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [582] = "         SectionName=NSLOCTEXT(\"\", \"4CA2B0DE4DF2E1049958D58D96DB9C4B\", \"然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。\")",
      [583] = "         FromLineIndex=1",
      [584] = "         LineGUIDLinked=2003163743",
      [585] = "         OwnedEpisodeID=1",
      [586] = "         StartTime=8.000000",
      [587] = "         Duration=4.625000",
      [588] = "      End Object",
      [589] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_2'\"",
      [590] = "         EpisodeID=1",
      [591] = "         ContentIndex=3",
      [592] = "         Talker=\"Actor2\"",
      [593] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [594] = "         SectionName=NSLOCTEXT(\"\", \"2C86D4054F9377885AF206A9B5F349D0\", \"在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。\")",
      [595] = "         FromLineIndex=2",
      [596] = "         LineGUIDLinked=3546257477",
      [597] = "         OwnedEpisodeID=1",
      [598] = "         StartTime=12.625000",
      [599] = "         Duration=5.000000",
      [600] = "      End Object",
      [601] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_3'\"",
      [602] = "         EpisodeID=1",
      [603] = "         ContentIndex=4",
      [604] = "         Talker=\"Actor2\"",
      [605] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [606] = "         SectionName=NSLOCTEXT(\"\", \"1B850C3448D3802CAFBB82AD41817F87\", \"一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。\")",
      [607] = "         FromLineIndex=3",
      [608] = "         LineGUIDLinked=526793861",
      [609] = "         OwnedEpisodeID=1",
      [610] = "         StartTime=17.625000",
      [611] = "         Duration=5.000000",
      [612] = "      End Object",
      [613] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_4'\"",
      [614] = "         EpisodeID=1",
      [615] = "         ContentIndex=5",
      [616] = "         Talker=\"Actor2\"",
      [617] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [618] = "         SectionName=NSLOCTEXT(\"\", \"7FFE315D401A97AFAD069297123D5CB5\", \"可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。\")",
      [619] = "         FromLineIndex=4",
      [620] = "         LineGUIDLinked=3346857647",
      [621] = "         OwnedEpisodeID=1",
      [622] = "         StartTime=22.625000",
      [623] = "         Duration=5.500000",
      [624] = "      End Object",
      [625] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_5'\"",
      [626] = "         EpisodeID=1",
      [627] = "         ContentIndex=6",
      [628] = "         Talker=\"Actor2\"",
      [629] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [630] = "         SectionName=NSLOCTEXT(\"\", \"8E99F79A4A4EAB8B0402BBB34ED51719\", \"《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。\")",
      [631] = "         FromLineIndex=5",
      [632] = "         LineGUIDLinked=970648999",
      [633] = "         OwnedEpisodeID=1",
      [634] = "         StartTime=28.125000",
      [635] = "         Duration=5.837500",
      [636] = "      End Object",
      [637] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueAction_0.BPS_Dialogue_C_6'\"",
      [638] = "         EpisodeID=1",
      [639] = "         ContentIndex=7",
      [640] = "         Talker=\"Actor2\"",
      [641] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueAction_0'\"",
      [642] = "         SectionName=NSLOCTEXT(\"\", \"AC5ECDC541964998765057935412B3C4\", \"于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。\")",
      [643] = "         FromLineIndex=6",
      [644] = "         LineGUIDLinked=491772944",
      [645] = "         OwnedEpisodeID=1",
      [646] = "         StartTime=33.962502",
      [647] = "         Duration=6.000000",
      [648] = "      End Object",
      [649] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [650] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [651] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [652] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [653] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [654] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [655] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [656] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [657] = "      FromTemplate=False",
      [658] = "   End Object",
      [659] = "   Begin Object Name=\"BP_DialogueActor_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_0'\"",
      [660] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [661] = "      bIsPlayer=True",
      [662] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.173632,W=0.984811),Translation=(X=-246.064633,Y=34.005380,Z=89.806000))",
      [663] = "      TrackName=\"Actor1\"",
      [664] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [665] = "   End Object",
      [666] = "   Begin Object Name=\"BP_DialogueActor_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_1'\"",
      [667] = "      AppearanceID=7201036",
      [668] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [669] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.992839,W=0.119461),Translation=(X=-77.809458,Y=104.651519,Z=78.125000))",
      [670] = "      TrackName=\"Actor2\"",
      [671] = "      StickGround=True",
      [672] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [673] = "   End Object",
      [674] = "   Begin Object Name=\"BP_DialogueActor_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_2'\"",
      [675] = "      AppearanceID=7201038",
      [676] = "      IdleAnimLibAssetID=(AssetID=\"Peed\",StateName=\"Loop\")",
      [677] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.965935,W=-0.258785),Translation=(X=-883.917553,Y=1026.834080,Z=120.000000))",
      [678] = "      TrackName=\"Actor3\"",
      [679] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [680] = "   End Object",
      [681] = "   Begin Object Name=\"BP_DialogueActor_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_3'\"",
      [682] = "      AppearanceID=7211087",
      [683] = "      IdleAnimLibAssetID=(AssetID=\"Sad\",StateName=\"Loop\")",
      [684] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.000002,W=1.000000),Translation=(X=-1210.543339,Y=1057.320457,Z=120.000000))",
      [685] = "      TrackName=\"Actor4\"",
      [686] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [687] = "   End Object",
      [688] = "   Begin Object Name=\"BP_DialogueActor_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_4'\"",
      [689] = "      AppearanceID=7207020",
      [690] = "      IdleAnimLibAssetID=(AssetID=\"Anger\",StateName=\"Loop\")",
      [691] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.996191,W=0.087195),Translation=(X=-1077.031281,Y=1070.575449,Z=120.000000))",
      [692] = "      TrackName=\"Actor5\"",
      [693] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [694] = "   End Object",
      [695] = "   Begin Object Name=\"BP_DialogueActor_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_5'\"",
      [696] = "      AppearanceID=7215021",
      [697] = "      IdleAnimLibAssetID=(AssetID=\"Dance\")",
      [698] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.000002,W=1.000000),Translation=(X=278.501434,Y=1133.197666,Z=90.000000))",
      [699] = "      TrackName=\"Actor6\"",
      [700] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [701] = "   End Object",
      [702] = "   Begin Object Name=\"BP_DialogueActor_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_6'\"",
      [703] = "      AppearanceID=7201014",
      [704] = "      IdleAnimLibAssetID=(AssetID=\"Applaud\",StateName=\"Loop\")",
      [705] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.819186,W=0.573528),Translation=(X=296.873257,Y=1310.324206,Z=90.000000))",
      [706] = "      TrackName=\"Actor7\"",
      [707] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [708] = "   End Object",
      [709] = "   Begin Object Name=\"BP_DialogueActor_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_8'\"",
      [710] = "      AppearanceID=7201054",
      [711] = "      IdleAnimLibAssetID=(AssetID=\"ShowItem\",StateName=\"Loop\")",
      [712] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.000002,W=1.000000),Translation=(X=2303.958210,Y=-2791.368024,Z=80.000000))",
      [713] = "      TrackName=\"Actor9\"",
      [714] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [715] = "   End Object",
      [716] = "   Begin Object Name=\"BP_DialogueActor_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_9'\"",
      [717] = "      AppearanceID=7201038",
      [718] = "      IdleAnimLibAssetID=(AssetID=\"Idle\")",
      [719] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-1.000000,W=-0.000002),Translation=(X=2401.424614,Y=-2777.198108,Z=80.000000))",
      [720] = "      TrackName=\"Actor10\"",
      [721] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [722] = "   End Object",
      [723] = "   Begin Object Name=\"BP_DialogueActor_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_10'\"",
      [724] = "      AppearanceID=7201042",
      [725] = "      IdleAnimLibAssetID=(AssetID=\"Stand_Read_Newspaper\",StateName=\"Loop\")",
      [726] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.996190,W=-0.087207),Translation=(X=1383.338811,Y=-262.652106,Z=70.000000))",
      [727] = "      TrackName=\"Actor11\"",
      [728] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [729] = "   End Object",
      [730] = "   Begin Object Name=\"BP_DialogueActor_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_12'\"",
      [731] = "      AppearanceID=7201038",
      [732] = "      IdleAnimLibAssetID=(AssetID=\"Down\")",
      [733] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.499999,W=0.866026),Translation=(X=-208.459744,Y=-888.076840,Z=80.000000))",
      [734] = "      TrackName=\"Actor12\"",
      [735] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [736] = "   End Object",
      [737] = "   Begin Object Name=\"BP_DialogueActor_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'/Temp/DialogueTransientPackage.********:BP_DialogueActor_C_13'\"",
      [738] = "      AppearanceID=7201042",
      [739] = "      IdleAnimLibAssetID=(AssetID=\"Sitdesk\")",
      [740] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.996196,W=0.087139),Translation=(X=54.519261,Y=-911.404910,Z=80.000000))",
      [741] = "      TrackName=\"Actor13\"",
      [742] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [743] = "   End Object",
      [744] = "   Begin Object Name=\"BP_DialogueCamera_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_0'\"",
      [745] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [746] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=0.960697,W=0.277599),Translation=(X=-1240.000000,Y=-1350.000000,Z=0.000000))",
      [747] = "      TrackName=\"锚点\"",
      [748] = "   End Object",
      [749] = "   Begin Object Name=\"BP_DialogueCamera_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_1'\"",
      [750] = "      FOV=55.000000",
      [751] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor2\")",
      [752] = "      DepthOfFieldFocalDistance=1325.300415",
      [753] = "      DepthOfFieldFStop=30.624336",
      [754] = "      DepthOfFieldSensorWidth=144.085587",
      [755] = "      DepthOfFieldFocalRegion=0.059177",
      [756] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [757] = "      SpawnTransform=(Rotation=(X=0.028033,Y=0.219775,Z=-0.123790,W=0.967259),Translation=(X=-1269.213571,Y=292.748191,Z=723.165699))",
      [758] = "      TrackName=\"远景\"",
      [759] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [760] = "   End Object",
      [761] = "   Begin Object Name=\"BP_DialogueCamera_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_2'\"",
      [762] = "      FOV=60.000000",
      [763] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [764] = "      SpawnTransform=(Rotation=(X=0.016473,Y=0.009859,Z=-0.857907,W=0.513447),Translation=(X=307.148997,Y=484.872502,Z=128.773370))",
      [765] = "      TrackName=\"全景\"",
      [766] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [767] = "   End Object",
      [768] = "   Begin Object Name=\"BP_DialogueCamera_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_3'\"",
      [769] = "      FOV=50.000000",
      [770] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [771] = "      SpawnTransform=(Rotation=(X=0.000752,Y=0.033147,Z=-0.022675,W=0.999193),Translation=(X=-179.697334,Y=69.841961,Z=174.199745))",
      [772] = "      TrackName=\"中景\"",
      [773] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [774] = "   End Object",
      [775] = "   Begin Object Name=\"BP_DialogueCamera_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_4'\"",
      [776] = "      FOV=45.000000",
      [777] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [778] = "      SpawnTransform=(Rotation=(X=-0.055813,Y=0.000974,Z=0.998289,W=0.017425),Translation=(X=302.410935,Y=48.734316,Z=181.526100))",
      [779] = "      TrackName=\"中景2\"",
      [780] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [781] = "   End Object",
      [782] = "   Begin Object Name=\"BP_DialogueCamera_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_5'\"",
      [783] = "      FOV=32.000000",
      [784] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [785] = "      SpawnTransform=(Rotation=(X=-0.010942,Y=-0.008673,Z=-0.783617,W=0.621087),Translation=(X=301.465289,Y=950.803828,Z=82.813676))",
      [786] = "      TrackName=\"平视\"",
      [787] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [788] = "   End Object",
      [789] = "   Begin Object Name=\"BP_DialogueCamera_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_6'\"",
      [790] = "      FOV=40.000000",
      [791] = "      bOverride_DepthOfField=True",
      [792] = "      DepthOfFieldFocalDistance=135.000000",
      [793] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [794] = "      SpawnTransform=(Rotation=(X=-0.013951,Y=-0.000560,Z=-0.999097,W=0.040128),Translation=(X=142.611363,Y=10.186491,Z=71.740812))",
      [795] = "      TrackName=\"近景Actor1\"",
      [796] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [797] = "   End Object",
      [798] = "   Begin Object Name=\"BP_DialogueCamera_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_7'\"",
      [799] = "      FOV=50.000000",
      [800] = "      bOverride_DepthOfField=True",
      [801] = "      DepthOfFieldFocalDistance=230.000000",
      [802] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [803] = "      SpawnTransform=(Rotation=(X=-0.008696,Y=-0.000730,Z=-0.996455,W=0.083675),Translation=(X=226.141652,Y=18.113974,Z=68.182552))",
      [804] = "      TrackName=\"过肩Actor1\"",
      [805] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [806] = "   End Object",
      [807] = "   Begin Object Name=\"BP_DialogueCamera_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_8'\"",
      [808] = "      FOV=80.000000",
      [809] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [810] = "      SpawnTransform=(Rotation=(X=0.097212,Y=0.008505,Z=-0.991440,W=0.086740),Translation=(X=228.066246,Y=40.094980,Z=80.826537))",
      [811] = "      TrackName=\"中Actor1\"",
      [812] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [813] = "   End Object",
      [814] = "   Begin Object Name=\"BP_DialogueCamera_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_9'\"",
      [815] = "      FOV=50.000000",
      [816] = "      bOverride_DepthOfField=True",
      [817] = "      DepthOfFieldFocalDistance=92.000000",
      [818] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [819] = "      SpawnTransform=(Rotation=(X=0.232490,Y=-0.020697,Z=-0.968534,W=-0.086385),Translation=(X=70.946314,Y=-174.978561,Z=142.922629))",
      [820] = "      TrackName=\"近景Actor2\"",
      [821] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [822] = "   End Object",
      [823] = "   Begin Object Name=\"BP_DialogueCamera_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_10'\"",
      [824] = "      FOV=70.000000",
      [825] = "      DepthOfFieldFocalDistance=280.644135",
      [826] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [827] = "      SpawnTransform=(Rotation=(X=0.059488,Y=-0.032974,Z=-0.872594,W=-0.483687),Translation=(X=174.652469,Y=-168.926286,Z=84.030997))",
      [828] = "      TrackName=\"过肩Actor2\"",
      [829] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [830] = "   End Object",
      [831] = "   Begin Object Name=\"BP_DialogueCamera_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_11'\"",
      [832] = "      FOV=50.000000",
      [833] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [834] = "      SpawnTransform=(Rotation=(X=0.074902,Y=-0.003401,Z=-0.996159,W=-0.045235),Translation=(X=274.608464,Y=13.100110,Z=90.192426))",
      [835] = "      TrackName=\"中Actor2\"",
      [836] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [837] = "   End Object",
      [838] = "   Begin Object Name=\"BP_DialogueCamera_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_12'\"",
      [839] = "      FOV=40.000000",
      [840] = "      bOverride_DepthOfField=True",
      [841] = "      DepthOfFieldFocusActor=(PerformerName=\"Actor3\")",
      [842] = "      DepthOfFieldFocalDistance=322.215271",
      [843] = "      DepthOfFieldFStop=58.863064",
      [844] = "      DepthOfFieldSensorWidth=147.995178",
      [845] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [846] = "      SpawnTransform=(Rotation=(X=0.030827,Y=0.030916,Z=-0.705225,W=0.707638),Translation=(X=34.354497,Y=403.747243,Z=76.125355))",
      [847] = "      TrackName=\"近景Actor3\"",
      [848] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [849] = "   End Object",
      [850] = "   Begin Object Name=\"BP_DialogueCamera_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_13'\"",
      [851] = "      FOV=60.000000",
      [852] = "      bOverride_DepthOfField=True",
      [853] = "      DepthOfFieldFocalDistance=180.000000",
      [854] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [855] = "      SpawnTransform=(Rotation=(X=0.015714,Y=0.091063,Z=-0.169508,W=0.981187),Translation=(X=-341.759513,Y=252.383534,Z=122.830804))",
      [856] = "      TrackName=\"过肩Actor3\"",
      [857] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [858] = "   End Object",
      [859] = "   Begin Object Name=\"BP_DialogueCamera_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_14'\"",
      [860] = "      FOV=80.000000",
      [861] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [862] = "      SpawnTransform=(Rotation=(X=0.053209,Y=0.016879,Z=-0.951704,W=0.301898),Translation=(X=192.720541,Y=88.491412,Z=67.172811))",
      [863] = "      TrackName=\"中Actor3\"",
      [864] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [865] = "   End Object",
      [866] = "   Begin Object Name=\"BP_DialogueCamera_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_16'\"",
      [867] = "      FOV=45.000000",
      [868] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [869] = "      SpawnTransform=(Rotation=(X=0.090093,Y=0.220868,Z=-0.366930,W=0.899146),Translation=(X=-721.664443,Y=613.895057,Z=484.768818))",
      [870] = "      TrackName=\"Camera2\"",
      [871] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_8'\"",
      [872] = "   End Object",
      [873] = "   Begin Object Name=\"BP_DialogueCamera_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_17'\"",
      [874] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [875] = "      SpawnTransform=(Rotation=(X=0.084839,Y=-0.116811,Z=-0.581602,W=-0.800560),Translation=(X=-217.138989,Y=-273.330941,Z=165.285742))",
      [876] = "      TrackName=\"Camera3\"",
      [877] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_5'\"",
      [878] = "   End Object",
      [879] = "   Begin Object Name=\"BP_DialogueCamera_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_18'\"",
      [880] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [881] = "      SpawnTransform=(Rotation=(X=0.130502,Y=0.002506,Z=-0.991262,W=0.019033),Translation=(X=456.988108,Y=87.945920,Z=136.720671))",
      [882] = "      TrackName=\"Camera4\"",
      [883] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_10'\"",
      [884] = "   End Object",
      [885] = "   Begin Object Name=\"BP_DialogueCamera_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_19'\"",
      [886] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [887] = "      SpawnTransform=(Rotation=(X=-0.061766,Y=0.151185,Z=0.372837,W=0.913412),Translation=(X=-86.070314,Y=-352.040769,Z=159.621900))",
      [888] = "      TrackName=\"Camera5\"",
      [889] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_13'\"",
      [890] = "   End Object",
      [891] = "   Begin Object Name=\"BP_DialogueCamera_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'/Temp/DialogueTransientPackage.********:BP_DialogueCamera_C_20'\"",
      [892] = "      SplineCurves=(Position=(Points=((ArriveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),LeaveTangent=(X=0.000000,Y=-130.000000,Z=0.000000),InterpMode=CIM_CurveUser))),Rotation=(Points=((OutVal=(X=0.000000,Y=-0.000000,Z=0.000000,W=1.000000),ArriveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),LeaveTangent=(X=0.000000,Y=0.000000,Z=0.000000,W=10000.000253),InterpMode=CIM_CurveAuto))),Scale=(Points=((OutVal=(X=1.000000,Y=1.000000,Z=1.000000),InterpMode=CIM_CurveAuto))),ReparamTable=(Points=(())))",
      [893] = "      SpawnTransform=(Rotation=(X=-0.007928,Y=-0.003647,Z=-0.908474,W=0.417851),Translation=(X=17.664239,Y=26.288390,Z=0.944869),Scale3D=(X=0.100000,Y=0.100000,Z=0.100000))",
      [894] = "      TrackName=\"Camera1\"",
      [895] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C'********:BP_DialogueEntityModel_C_0'\"",
      [896] = "   End Object",
      [897] = "   Begin Object Name=\"BP_DialogueEntityModel_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C'/Temp/DialogueTransientPackage.********:BP_DialogueEntityModel_C_0'\"",
      [898] = "      StaticMesh=\"/Script/Engine.StaticMesh'/Game/Arts/Environment/Mesh/Props/Gameplay/SM_Interact_06.SM_Interact_06'\"",
      [899] = "      SpawnTransform=(Rotation=(X=0.000000,Y=0.000000,Z=-0.707107,W=0.707107),Translation=(X=0.000000,Y=0.000000,Z=1830.000000),Scale3D=(X=10.000000,Y=10.000000,Z=10.000000))",
      [900] = "      TrackName=\"Model1\"",
      [901] = "      Parent=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_0'\"",
      [902] = "   End Object",
      [903] = "   Begin Object Name=\"DialogueTrackCamera_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_1'\"",
      [904] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_1'\"",
      [905] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [906] = "      TrackName=\"远景\"",
      [907] = "   End Object",
      [908] = "   Begin Object Name=\"DialogueTrackCamera_2\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_2'\"",
      [909] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_2'\"",
      [910] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [911] = "      TrackName=\"全景\"",
      [912] = "   End Object",
      [913] = "   Begin Object Name=\"DialogueTrackCamera_3\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_3'\"",
      [914] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_3'\"",
      [915] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [916] = "      TrackName=\"中景\"",
      [917] = "   End Object",
      [918] = "   Begin Object Name=\"DialogueTrackCamera_4\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_4'\"",
      [919] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_4'\"",
      [920] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [921] = "      TrackName=\"中景2\"",
      [922] = "   End Object",
      [923] = "   Begin Object Name=\"DialogueTrackCamera_5\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_5'\"",
      [924] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_5'\"",
      [925] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [926] = "      TrackName=\"平视\"",
      [927] = "   End Object",
      [928] = "   Begin Object Name=\"DialogueTrackActor_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueActorTrack'/Temp/DialogueTransientPackage.********:DialogueTrackActor_1'\"",
      [929] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_1'\"",
      [930] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_9'\"",
      [931] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_10'\"",
      [932] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_11'\"",
      [933] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [934] = "      TrackName=\"Actor2\"",
      [935] = "   End Object",
      [936] = "   Begin Object Name=\"BP_DialogueTrackModel_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackModel_C_0'\"",
      [937] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C'********:BP_DialogueEntityModel_C_0'\"",
      [938] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_20'\"",
      [939] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [940] = "      FromTemplate=False",
      [941] = "      TrackName=\"Model1\"",
      [942] = "   End Object",
      [943] = "   Begin Object Name=\"DialogueTrackCamera_20\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_20'\"",
      [944] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_20'\"",
      [945] = "      Parent=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackModel.BP_DialogueTrackModel_C'********:BP_DialogueTrackModel_C_0'\"",
      [946] = "      FromTemplate=False",
      [947] = "      TrackName=\"Camera1\"",
      [948] = "   End Object",
      [949] = "   Begin Object Name=\"DialogueTrackCamera_19\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_19'\"",
      [950] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_19'\"",
      [951] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_10'\"",
      [952] = "      FromTemplate=False",
      [953] = "      TrackName=\"Camera5\"",
      [954] = "   End Object",
      [955] = "   Begin Object Name=\"DialogueTrackCamera_18\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_18'\"",
      [956] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_18'\"",
      [957] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_8'\"",
      [958] = "      FromTemplate=False",
      [959] = "      TrackName=\"Camera4\"",
      [960] = "   End Object",
      [961] = "   Begin Object Name=\"BP_DialogueTrackAnimation_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_8'\"",
      [962] = "      Begin Object Name=\"BPS_PlayAnimation_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'/Temp/DialogueTransientPackage.********:BP_DialogueTrackAnimation_C_8.BPS_PlayAnimation_C_0'\"",
      [963] = "         AnimLibItem=(AssetID=\"HoldHead_Pain\")",
      [964] = "         PreAnimationBlendOutTime=0.200000",
      [965] = "         DialogueAction=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_8'\"",
      [966] = "         SectionName=NSLOCTEXT(\"\", \"3FC2A55E4DD99E08A5322386B43CDCED\", \"Actor10\")",
      [967] = "         FromLineIndex=1",
      [968] = "         LineGUIDLinked=2003163743",
      [969] = "         OwnedEpisodeID=1",
      [970] = "         StartTime=8.000000",
      [971] = "         Duration=4.625000",
      [972] = "      End Object",
      [973] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C'BPS_PlayAnimation_C_0'\"",
      [974] = "   End Object",
      [975] = "   Begin Object Name=\"DialogueTrackCamera_16\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_16'\"",
      [976] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_16'\"",
      [977] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_6'\"",
      [978] = "      FromTemplate=False",
      [979] = "      TrackName=\"Camera2\"",
      [980] = "   End Object",
      [981] = "   Begin Object Name=\"DialogueTrackCamera_17\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_17'\"",
      [982] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_17'\"",
      [983] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_4'\"",
      [984] = "      FromTemplate=False",
      [985] = "      TrackName=\"Camera3\"",
      [986] = "   End Object",
      [987] = "   Begin Object Name=\"DialogueTrackCamera_12\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_12'\"",
      [988] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_12'\"",
      [989] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_1'\"",
      [990] = "      TrackName=\"近景Actor3\"",
      [991] = "   End Object",
      [992] = "   Begin Object Name=\"DialogueTrackCamera_13\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_13'\"",
      [993] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_13'\"",
      [994] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_1'\"",
      [995] = "      TrackName=\"过肩Actor3\"",
      [996] = "   End Object",
      [997] = "   Begin Object Name=\"DialogueTrackCamera_14\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_14'\"",
      [998] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_14'\"",
      [999] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_1'\"",
      [1000] = "      TrackName=\"中Actor3\"",
      [1001] = "   End Object",
      [1002] = "   Begin Object Name=\"DialogueTrackCamera_9\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_9'\"",
      [1003] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_9'\"",
      [1004] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_1'\"",
      [1005] = "      TrackName=\"近景Actor2\"",
      [1006] = "   End Object",
      [1007] = "   Begin Object Name=\"DialogueTrackCamera_10\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_10'\"",
      [1008] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_10'\"",
      [1009] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_1'\"",
      [1010] = "      TrackName=\"过肩Actor2\"",
      [1011] = "   End Object",
      [1012] = "   Begin Object Name=\"DialogueTrackCamera_11\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_11'\"",
      [1013] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_11'\"",
      [1014] = "      Parent=\"/Script/KGStoryLineEditor.DialogueActorTrack'********:DialogueTrackActor_1'\"",
      [1015] = "      TrackName=\"中Actor2\"",
      [1016] = "   End Object",
      [1017] = "   Begin Object Name=\"DialogueTrackCamera_6\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_6'\"",
      [1018] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_6'\"",
      [1019] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [1020] = "      TrackName=\"近景Actor1\"",
      [1021] = "   End Object",
      [1022] = "   Begin Object Name=\"DialogueTrackCamera_7\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_7'\"",
      [1023] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_7'\"",
      [1024] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [1025] = "      TrackName=\"过肩Actor1\"",
      [1026] = "   End Object",
      [1027] = "   Begin Object Name=\"DialogueTrackCamera_8\" ExportPath=\"/Script/KGStoryLineEditor.DialogueCameraTrack'/Temp/DialogueTransientPackage.********:DialogueTrackCamera_8'\"",
      [1028] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'********:BP_DialogueCamera_C_8'\"",
      [1029] = "      Parent=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'********:DialoguePerformerTrack_0'\"",
      [1030] = "      TrackName=\"中Actor1\"",
      [1031] = "   End Object",
      [1032] = "   Begin Object Name=\"KGSLDialogueLine_7\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_7'\"",
      [1033] = "      GUID=1810087296",
      [1034] = "      Duration=8.000000",
      [1035] = "      ContentString=\"我早年意外成为非凡者后，为了获得更多创作的灵感，我违背了很多原则。我过于深入别人的生活，窥探人们最隐秘的情感。也正因如此，我写下了无数杰作。\"",
      [1036] = "      ContentUI=\"Default\"",
      [1037] = "      Talker=(PerformerName=\"Actor2\")",
      [1038] = "   End Object",
      [1039] = "   Begin Object Name=\"KGSLDialogueLine_8\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_8'\"",
      [1040] = "      GUID=2003163743",
      [1041] = "      Duration=4.630000",
      [1042] = "      ContentString=\"然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。\"",
      [1043] = "      ContentUI=\"Default\"",
      [1044] = "      Talker=(PerformerName=\"Actor2\")",
      [1045] = "   End Object",
      [1046] = "   Begin Object Name=\"KGSLDialogueLine_9\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_9'\"",
      [1047] = "      GUID=3546257477",
      [1048] = "      ContentString=\"在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。\"",
      [1049] = "      ContentUI=\"Default\"",
      [1050] = "      Talker=(PerformerName=\"Actor2\")",
      [1051] = "   End Object",
      [1052] = "   Begin Object Name=\"KGSLDialogueLine_10\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_10'\"",
      [1053] = "      GUID=526793861",
      [1054] = "      ContentString=\"一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。\"",
      [1055] = "      ContentUI=\"Default\"",
      [1056] = "      Talker=(PerformerName=\"Actor2\")",
      [1057] = "   End Object",
      [1058] = "   Begin Object Name=\"KGSLDialogueLine_11\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_11'\"",
      [1059] = "      GUID=3346857647",
      [1060] = "      Duration=5.500000",
      [1061] = "      ContentString=\"可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。\"",
      [1062] = "      ContentUI=\"Default\"",
      [1063] = "      Talker=(PerformerName=\"Actor2\")",
      [1064] = "   End Object",
      [1065] = "   Begin Object Name=\"KGSLDialogueLine_12\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_12'\"",
      [1066] = "      GUID=970648999",
      [1067] = "      Duration=5.840000",
      [1068] = "      ContentString=\"《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。\"",
      [1069] = "      ContentUI=\"Default\"",
      [1070] = "      Talker=(PerformerName=\"Actor2\")",
      [1071] = "   End Object",
      [1072] = "   Begin Object Name=\"KGSLDialogueLine_13\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_13'\"",
      [1073] = "      GUID=491772944",
      [1074] = "      Duration=6.000000",
      [1075] = "      ContentString=\"于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。\"",
      [1076] = "      ContentUI=\"Default\"",
      [1077] = "      Talker=(PerformerName=\"Actor2\")",
      [1078] = "   End Object",
      [1079] = "   Begin Object Name=\"BP_DLExtensionData_C_13\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_13'\"",
      [1080] = "      CanSkip=True",
      [1081] = "      EpisodeID=1",
      [1082] = "      ContentIndex=7",
      [1083] = "   End Object",
      [1084] = "   Begin Object Name=\"BP_DLExtensionData_C_12\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_12'\"",
      [1085] = "      CanSkip=True",
      [1086] = "      EpisodeID=1",
      [1087] = "      ContentIndex=6",
      [1088] = "   End Object",
      [1089] = "   Begin Object Name=\"BP_DLExtensionData_C_11\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_11'\"",
      [1090] = "      CanSkip=True",
      [1091] = "      EpisodeID=1",
      [1092] = "      ContentIndex=5",
      [1093] = "   End Object",
      [1094] = "   Begin Object Name=\"BP_DLExtensionData_C_10\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_10'\"",
      [1095] = "      CanSkip=True",
      [1096] = "      EpisodeID=1",
      [1097] = "      ContentIndex=4",
      [1098] = "   End Object",
      [1099] = "   Begin Object Name=\"BP_DLExtensionData_C_9\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_9'\"",
      [1100] = "      CanSkip=True",
      [1101] = "      EpisodeID=1",
      [1102] = "      ContentIndex=3",
      [1103] = "   End Object",
      [1104] = "   Begin Object Name=\"BP_DLExtensionData_C_8\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_8'\"",
      [1105] = "      CanSkip=True",
      [1106] = "      EpisodeID=1",
      [1107] = "      ContentIndex=2",
      [1108] = "   End Object",
      [1109] = "   Begin Object Name=\"BP_DLExtensionData_C_7\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_7'\"",
      [1110] = "      CanSkip=True",
      [1111] = "      EpisodeID=1",
      [1112] = "      ContentIndex=1",
      [1113] = "   End Object",
      [1114] = "   Begin Object Name=\"KGSLDialogueEpisode_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_0'\"",
      [1115] = "      EpisodeID=1",
      [1116] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_0'\"",
      [1117] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_1'\"",
      [1118] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_2'\"",
      [1119] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_3'\"",
      [1120] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_4'\"",
      [1121] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_5'\"",
      [1122] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_6'\"",
      [1123] = "   End Object",
      [1124] = "   Begin Object Name=\"KGSLDialogueLine_0\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_0'\"",
      [1125] = "      GUID=1810087296",
      [1126] = "      Duration=8.000000",
      [1127] = "      ContentString=\"我早年意外成为非凡者后，为了获得更多创作的灵感，我违背了很多原则。我过于深入别人的生活，窥探人们最隐秘的情感。也正因如此，我写下了无数杰作。\"",
      [1128] = "      ContentUI=\"Default\"",
      [1129] = "      Talker=(PerformerName=\"Actor2\")",
      [1130] = "   End Object",
      [1131] = "   Begin Object Name=\"BP_DLExtensionData_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_0'\"",
      [1132] = "      CanSkip=True",
      [1133] = "      EpisodeID=1",
      [1134] = "      ContentIndex=1",
      [1135] = "   End Object",
      [1136] = "   Begin Object Name=\"KGSLDialogueLine_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_1'\"",
      [1137] = "      GUID=2003163743",
      [1138] = "      Duration=4.625000",
      [1139] = "      ContentString=\"然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。\"",
      [1140] = "      ContentUI=\"Default\"",
      [1141] = "      Talker=(PerformerName=\"Actor2\")",
      [1142] = "   End Object",
      [1143] = "   Begin Object Name=\"BP_DLExtensionData_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_1'\"",
      [1144] = "      CanSkip=True",
      [1145] = "      EpisodeID=1",
      [1146] = "      ContentIndex=2",
      [1147] = "   End Object",
      [1148] = "   Begin Object Name=\"KGSLDialogueLine_2\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_2'\"",
      [1149] = "      GUID=3546257477",
      [1150] = "      ContentString=\"在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。\"",
      [1151] = "      ContentUI=\"Default\"",
      [1152] = "      Talker=(PerformerName=\"Actor2\")",
      [1153] = "   End Object",
      [1154] = "   Begin Object Name=\"BP_DLExtensionData_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_2'\"",
      [1155] = "      CanSkip=True",
      [1156] = "      EpisodeID=1",
      [1157] = "      ContentIndex=3",
      [1158] = "   End Object",
      [1159] = "   Begin Object Name=\"KGSLDialogueLine_3\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_3'\"",
      [1160] = "      GUID=526793861",
      [1161] = "      ContentString=\"一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。\"",
      [1162] = "      ContentUI=\"Default\"",
      [1163] = "      Talker=(PerformerName=\"Actor2\")",
      [1164] = "   End Object",
      [1165] = "   Begin Object Name=\"BP_DLExtensionData_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_3'\"",
      [1166] = "      CanSkip=True",
      [1167] = "      EpisodeID=1",
      [1168] = "      ContentIndex=4",
      [1169] = "   End Object",
      [1170] = "   Begin Object Name=\"KGSLDialogueLine_4\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_4'\"",
      [1171] = "      GUID=3346857647",
      [1172] = "      Duration=5.500000",
      [1173] = "      ContentString=\"可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。\"",
      [1174] = "      ContentUI=\"Default\"",
      [1175] = "      Talker=(PerformerName=\"Actor2\")",
      [1176] = "   End Object",
      [1177] = "   Begin Object Name=\"BP_DLExtensionData_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_4'\"",
      [1178] = "      CanSkip=True",
      [1179] = "      EpisodeID=1",
      [1180] = "      ContentIndex=5",
      [1181] = "   End Object",
      [1182] = "   Begin Object Name=\"KGSLDialogueLine_5\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_5'\"",
      [1183] = "      GUID=970648999",
      [1184] = "      Duration=5.837500",
      [1185] = "      ContentString=\"《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。\"",
      [1186] = "      ContentUI=\"Default\"",
      [1187] = "      Talker=(PerformerName=\"Actor2\")",
      [1188] = "   End Object",
      [1189] = "   Begin Object Name=\"BP_DLExtensionData_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_5'\"",
      [1190] = "      CanSkip=True",
      [1191] = "      EpisodeID=1",
      [1192] = "      ContentIndex=6",
      [1193] = "   End Object",
      [1194] = "   Begin Object Name=\"KGSLDialogueLine_6\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_6'\"",
      [1195] = "      GUID=491772944",
      [1196] = "      Delay=0.000001",
      [1197] = "      Duration=6.000000",
      [1198] = "      ContentString=\"于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。\"",
      [1199] = "      ContentUI=\"Default\"",
      [1200] = "      Talker=(PerformerName=\"Actor2\")",
      [1201] = "   End Object",
      [1202] = "   Begin Object Name=\"BP_DLExtensionData_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_6'\"",
      [1203] = "      CanSkip=True",
      [1204] = "      EpisodeID=1",
      [1205] = "      ContentIndex=7",
      [1206] = "   End Object",
      [1207] = "   Begin Object Name=\"DialogueStateControlTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_0'\"",
      [1208] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1209] = "      FromTemplate=False",
      [1210] = "   End Object",
      [1211] = "   Begin Object Name=\"DialogueDialogueTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0'\"",
      [1212] = "      Begin Object Name=\"BPS_Dialogue_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_0'\"",
      [1213] = "         EpisodeID=1",
      [1214] = "         ContentIndex=1",
      [1215] = "         Talker=\"Actor2\"",
      [1216] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1217] = "         SectionName=\"我早年意外成为非凡者后，为了获得更多创作的灵感，我违背了很多原则。我过于深入别人的生活，窥探人们最隐秘的情感。也正因如此，我写下了无数杰作。\"",
      [1218] = "         FromLineIndex=0",
      [1219] = "         LineGUIDLinked=1810087296",
      [1220] = "         OwnedEpisodeID=1",
      [1221] = "         Duration=8.000000",
      [1222] = "      End Object",
      [1223] = "      Begin Object Name=\"BPS_Dialogue_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_1'\"",
      [1224] = "         EpisodeID=1",
      [1225] = "         ContentIndex=2",
      [1226] = "         Talker=\"Actor2\"",
      [1227] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1228] = "         SectionName=\"然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。\"",
      [1229] = "         FromLineIndex=1",
      [1230] = "         LineGUIDLinked=2003163743",
      [1231] = "         OwnedEpisodeID=1",
      [1232] = "         StartTime=8.000000",
      [1233] = "         Duration=4.625000",
      [1234] = "      End Object",
      [1235] = "      Begin Object Name=\"BPS_Dialogue_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_2'\"",
      [1236] = "         EpisodeID=1",
      [1237] = "         ContentIndex=3",
      [1238] = "         Talker=\"Actor2\"",
      [1239] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1240] = "         SectionName=\"在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。\"",
      [1241] = "         FromLineIndex=2",
      [1242] = "         LineGUIDLinked=3546257477",
      [1243] = "         OwnedEpisodeID=1",
      [1244] = "         StartTime=12.625000",
      [1245] = "         Duration=5.000000",
      [1246] = "      End Object",
      [1247] = "      Begin Object Name=\"BPS_Dialogue_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_3'\"",
      [1248] = "         EpisodeID=1",
      [1249] = "         ContentIndex=4",
      [1250] = "         Talker=\"Actor2\"",
      [1251] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1252] = "         SectionName=\"一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。\"",
      [1253] = "         FromLineIndex=3",
      [1254] = "         LineGUIDLinked=526793861",
      [1255] = "         OwnedEpisodeID=1",
      [1256] = "         StartTime=17.625000",
      [1257] = "         Duration=5.000000",
      [1258] = "      End Object",
      [1259] = "      Begin Object Name=\"BPS_Dialogue_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_4'\"",
      [1260] = "         EpisodeID=1",
      [1261] = "         ContentIndex=5",
      [1262] = "         Talker=\"Actor2\"",
      [1263] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1264] = "         SectionName=\"可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。\"",
      [1265] = "         FromLineIndex=4",
      [1266] = "         LineGUIDLinked=3346857647",
      [1267] = "         OwnedEpisodeID=1",
      [1268] = "         StartTime=22.625000",
      [1269] = "         Duration=5.500000",
      [1270] = "      End Object",
      [1271] = "      Begin Object Name=\"BPS_Dialogue_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_5'\"",
      [1272] = "         EpisodeID=1",
      [1273] = "         ContentIndex=6",
      [1274] = "         Talker=\"Actor2\"",
      [1275] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1276] = "         SectionName=\"《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。\"",
      [1277] = "         FromLineIndex=5",
      [1278] = "         LineGUIDLinked=970648999",
      [1279] = "         OwnedEpisodeID=1",
      [1280] = "         StartTime=28.125000",
      [1281] = "         Duration=5.837500",
      [1282] = "      End Object",
      [1283] = "      Begin Object Name=\"BPS_Dialogue_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'/Temp/DialogueTransientPackage.********:DialogueDialogueTrack_0.BPS_Dialogue_C_6'\"",
      [1284] = "         EpisodeID=1",
      [1285] = "         ContentIndex=7",
      [1286] = "         Talker=\"Actor2\"",
      [1287] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueDialogueTrack'********:DialogueDialogueTrack_0'\"",
      [1288] = "         SectionName=\"于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。\"",
      [1289] = "         FromLineIndex=6",
      [1290] = "         LineGUIDLinked=491772944",
      [1291] = "         OwnedEpisodeID=1",
      [1292] = "         StartTime=33.962502",
      [1293] = "         Duration=6.000000",
      [1294] = "      End Object",
      [1295] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_0'\"",
      [1296] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_1'\"",
      [1297] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_2'\"",
      [1298] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_3'\"",
      [1299] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_4'\"",
      [1300] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_5'\"",
      [1301] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'BPS_Dialogue_C_6'\"",
      [1302] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C'\"",
      [1303] = "      FromTemplate=False",
      [1304] = "   End Object",
      [1305] = "   Begin Object Name=\"DialogueStateControlTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1'\"",
      [1306] = "      Begin Object Name=\"BPS_StateControl_C_0\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_0'\"",
      [1307] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1308] = "         FromLineIndex=0",
      [1309] = "         LineGUIDLinked=1810087296",
      [1310] = "         OwnedEpisodeID=1",
      [1311] = "         StartTime=7.900000",
      [1312] = "      End Object",
      [1313] = "      Begin Object Name=\"BPS_StateControl_C_1\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_1'\"",
      [1314] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1315] = "         FromLineIndex=1",
      [1316] = "         LineGUIDLinked=2003163743",
      [1317] = "         OwnedEpisodeID=1",
      [1318] = "         StartTime=12.525000",
      [1319] = "      End Object",
      [1320] = "      Begin Object Name=\"BPS_StateControl_C_2\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_2'\"",
      [1321] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1322] = "         FromLineIndex=2",
      [1323] = "         LineGUIDLinked=3546257477",
      [1324] = "         OwnedEpisodeID=1",
      [1325] = "         StartTime=17.525000",
      [1326] = "      End Object",
      [1327] = "      Begin Object Name=\"BPS_StateControl_C_3\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_3'\"",
      [1328] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1329] = "         FromLineIndex=3",
      [1330] = "         LineGUIDLinked=526793861",
      [1331] = "         OwnedEpisodeID=1",
      [1332] = "         StartTime=22.525000",
      [1333] = "      End Object",
      [1334] = "      Begin Object Name=\"BPS_StateControl_C_4\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_4'\"",
      [1335] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1336] = "         FromLineIndex=4",
      [1337] = "         LineGUIDLinked=3346857647",
      [1338] = "         OwnedEpisodeID=1",
      [1339] = "         StartTime=28.025000",
      [1340] = "      End Object",
      [1341] = "      Begin Object Name=\"BPS_StateControl_C_5\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_5'\"",
      [1342] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1343] = "         FromLineIndex=5",
      [1344] = "         LineGUIDLinked=970648999",
      [1345] = "         OwnedEpisodeID=1",
      [1346] = "         StartTime=33.862499",
      [1347] = "      End Object",
      [1348] = "      Begin Object Name=\"BPS_StateControl_C_6\" ExportPath=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'/Temp/DialogueTransientPackage.********:DialogueStateControlTrack_1.BPS_StateControl_C_6'\"",
      [1349] = "         DialogueAction=\"/Script/KGStoryLineEditor.DialogueStateControlTrack'********:DialogueStateControlTrack_1'\"",
      [1350] = "         FromLineIndex=6",
      [1351] = "         LineGUIDLinked=491772944",
      [1352] = "         OwnedEpisodeID=1",
      [1353] = "         StartTime=39.862503",
      [1354] = "      End Object",
      [1355] = "      ActionSections(0)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_0'\"",
      [1356] = "      ActionSections(1)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_1'\"",
      [1357] = "      ActionSections(2)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_2'\"",
      [1358] = "      ActionSections(3)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_3'\"",
      [1359] = "      ActionSections(4)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_4'\"",
      [1360] = "      ActionSections(5)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_5'\"",
      [1361] = "      ActionSections(6)=\"/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'BPS_StateControl_C_6'\"",
      [1362] = "      SectionType=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C'\"",
      [1363] = "      FromTemplate=False",
      [1364] = "   End Object",
      [1365] = "   Begin Object Name=\"DialoguePerformerTrack_0\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_0'\"",
      [1366] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_0'\"",
      [1367] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_6'\"",
      [1368] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_7'\"",
      [1369] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_8'\"",
      [1370] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1371] = "      TrackName=\"Actor1\"",
      [1372] = "   End Object",
      [1373] = "   Begin Object Name=\"DialoguePerformerTrack_1\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_1'\"",
      [1374] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_2'\"",
      [1375] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_12'\"",
      [1376] = "      Childs(1)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_13'\"",
      [1377] = "      Childs(2)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_14'\"",
      [1378] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1379] = "      TrackName=\"Actor3\"",
      [1380] = "   End Object",
      [1381] = "   Begin Object Name=\"DialoguePerformerTrack_2\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_2'\"",
      [1382] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_3'\"",
      [1383] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1384] = "      FromTemplate=False",
      [1385] = "      TrackName=\"Actor4\"",
      [1386] = "   End Object",
      [1387] = "   Begin Object Name=\"DialoguePerformerTrack_3\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_3'\"",
      [1388] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_4'\"",
      [1389] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1390] = "      FromTemplate=False",
      [1391] = "      TrackName=\"Actor5\"",
      [1392] = "   End Object",
      [1393] = "   Begin Object Name=\"DialoguePerformerTrack_4\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_4'\"",
      [1394] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_5'\"",
      [1395] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_17'\"",
      [1396] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1397] = "      FromTemplate=False",
      [1398] = "      TrackName=\"Actor6\"",
      [1399] = "   End Object",
      [1400] = "   Begin Object Name=\"DialoguePerformerTrack_5\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_5'\"",
      [1401] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_6'\"",
      [1402] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1403] = "      FromTemplate=False",
      [1404] = "      TrackName=\"Actor7\"",
      [1405] = "   End Object",
      [1406] = "   Begin Object Name=\"DialoguePerformerTrack_6\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_6'\"",
      [1407] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_8'\"",
      [1408] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_16'\"",
      [1409] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1410] = "      FromTemplate=False",
      [1411] = "      TrackName=\"Actor9\"",
      [1412] = "   End Object",
      [1413] = "   Begin Object Name=\"DialoguePerformerTrack_7\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_7'\"",
      [1414] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_9'\"",
      [1415] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1416] = "      FromTemplate=False",
      [1417] = "      TrackName=\"Actor10\"",
      [1418] = "      Actions(0)=\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C'********:BP_DialogueTrackAnimation_C_8'\"",
      [1419] = "   End Object",
      [1420] = "   Begin Object Name=\"DialoguePerformerTrack_8\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_8'\"",
      [1421] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_10'\"",
      [1422] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_18'\"",
      [1423] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1424] = "      FromTemplate=False",
      [1425] = "      TrackName=\"Actor11\"",
      [1426] = "   End Object",
      [1427] = "   Begin Object Name=\"DialoguePerformerTrack_9\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_9'\"",
      [1428] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_12'\"",
      [1429] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1430] = "      FromTemplate=False",
      [1431] = "      TrackName=\"Actor12\"",
      [1432] = "   End Object",
      [1433] = "   Begin Object Name=\"DialoguePerformerTrack_10\" ExportPath=\"/Script/KGStoryLineEditor.DialoguePerformerTrack'/Temp/DialogueTransientPackage.********:DialoguePerformerTrack_10'\"",
      [1434] = "      DialogueEntity=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'********:BP_DialogueActor_C_13'\"",
      [1435] = "      Childs(0)=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_19'\"",
      [1436] = "      Parent=\"/Script/KGStoryLineEditor.DialogueCameraTrack'********:DialogueTrackCamera_0'\"",
      [1437] = "      FromTemplate=False",
      [1438] = "      TrackName=\"Actor13\"",
      [1439] = "   End Object",
      [1440] = "   Begin Object Name=\"KGSLDialogueEpisode\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode'\"",
      [1441] = "      EpisodeID=1",
      [1442] = "   End Object",
      [1443] = "   Begin Object Name=\"KGSLDialogueEpisode_1\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'/Temp/DialogueTransientPackage.********:KGSLDialogueEpisode_1'\"",
      [1444] = "      EpisodeID=1",
      [1445] = "      DialogueLines(0)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_14'\"",
      [1446] = "      DialogueLines(1)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_15'\"",
      [1447] = "      DialogueLines(2)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_16'\"",
      [1448] = "      DialogueLines(3)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_17'\"",
      [1449] = "      DialogueLines(4)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_18'\"",
      [1450] = "      DialogueLines(5)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_19'\"",
      [1451] = "      DialogueLines(6)=\"/Script/KGStoryLineEditor.KGSLDialogueLine'********:KGSLDialogueLine_20'\"",
      [1452] = "   End Object",
      [1453] = "   Begin Object Name=\"KGSLDialogueLine_14\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_14'\"",
      [1454] = "      EpisodeID=1",
      [1455] = "      ContentIndex=1",
      [1456] = "      GUID=1810087296",
      [1457] = "      UniqueID=84E0A61F5D9E4491A6FD2764F4B7C986",
      [1458] = "      Duration=8.000000",
      [1459] = "      ContentString=\"超字数厚葬\"",
      [1460] = "      ContentUI=\"Default\"",
      [1461] = "      Talker=(PerformerName=\"Actor2\")",
      [1462] = "   End Object",
      [1463] = "   Begin Object Name=\"BP_DLExtensionData_C_14\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_14'\"",
      [1464] = "   End Object",
      [1465] = "   Begin Object Name=\"KGSLDialogueLine_15\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_15'\"",
      [1466] = "      EpisodeID=1",
      [1467] = "      ContentIndex=2",
      [1468] = "      GUID=2003163743",
      [1469] = "      UniqueID=94C86ACFD82C4B63971547D47DFBC3D3",
      [1470] = "      Duration=4.625000",
      [1471] = "      ContentString=\"然而，在我达到戏剧巅峰后，我突然无法感动、共情，我丧失了人类最基本的情绪。\"",
      [1472] = "      ContentUI=\"Default\"",
      [1473] = "      Talker=(PerformerName=\"Actor2\")",
      [1474] = "   End Object",
      [1475] = "   Begin Object Name=\"BP_DLExtensionData_C_15\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_15'\"",
      [1476] = "   End Object",
      [1477] = "   Begin Object Name=\"KGSLDialogueLine_16\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_16'\"",
      [1478] = "      EpisodeID=1",
      [1479] = "      ContentIndex=3",
      [1480] = "      GUID=3546257477",
      [1481] = "      UniqueID=F11FD54DCD19461A9BFED376A2C8E8F2",
      [1482] = "      ContentString=\"在我陷入绝望之际，一位皇室成员赠予了我“报幕员”，他声称这件封印物品源自某位热爱观看戏剧的空想家途径强者。\"",
      [1483] = "      ContentUI=\"Default\"",
      [1484] = "      Talker=(PerformerName=\"Actor2\")",
      [1485] = "   End Object",
      [1486] = "   Begin Object Name=\"BP_DLExtensionData_C_16\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_16'\"",
      [1487] = "   End Object",
      [1488] = "   Begin Object Name=\"KGSLDialogueLine_17\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_17'\"",
      [1489] = "      EpisodeID=1",
      [1490] = "      ContentIndex=4",
      [1491] = "      GUID=526793861",
      [1492] = "      UniqueID=0EC5216A7B1F4FEA9F6AB79701C888AC",
      [1493] = "      ContentString=\"一开始，我忌惮它。它会剥夺使用者的心智，使其丧失相应的情绪能力，严重者甚至会被污染、死亡。\"",
      [1494] = "      ContentUI=\"Default\"",
      [1495] = "      Talker=(PerformerName=\"Actor2\")",
      [1496] = "   End Object",
      [1497] = "   Begin Object Name=\"BP_DLExtensionData_C_17\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_17'\"",
      [1498] = "   End Object",
      [1499] = "   Begin Object Name=\"KGSLDialogueLine_18\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_18'\"",
      [1500] = "      EpisodeID=1",
      [1501] = "      ContentIndex=5",
      [1502] = "      GUID=3346857647",
      [1503] = "      UniqueID=4F1E4F8D3DFF499185218B722A733CC8",
      [1504] = "      Duration=5.500000",
      [1505] = "      ContentString=\"可当我把“报幕员”使用在其他人身上时，我渐渐发现它弥补了我的不足，它能为我收取人们的情绪。我又找回我了自己。\"",
      [1506] = "      ContentUI=\"Default\"",
      [1507] = "      Talker=(PerformerName=\"Actor2\")",
      [1508] = "   End Object",
      [1509] = "   Begin Object Name=\"BP_DLExtensionData_C_18\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_18'\"",
      [1510] = "   End Object",
      [1511] = "   Begin Object Name=\"KGSLDialogueLine_19\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_19'\"",
      [1512] = "      EpisodeID=1",
      [1513] = "      ContentIndex=6",
      [1514] = "      GUID=970648999",
      [1515] = "      UniqueID=15E561D1EDC3438CBF99607F0BDB9B2A",
      [1516] = "      Duration=5.837500",
      [1517] = "      ContentString=\"《伯爵归来》25周年首演定在了廷根，我想起了伊琳，我想在她改变过去的美梦里，收集“释怀”的情绪。\"",
      [1518] = "      ContentUI=\"Default\"",
      [1519] = "      Talker=(PerformerName=\"Actor2\")",
      [1520] = "   End Object",
      [1521] = "   Begin Object Name=\"BP_DLExtensionData_C_19\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_19'\"",
      [1522] = "   End Object",
      [1523] = "   Begin Object Name=\"KGSLDialogueLine_20\" ExportPath=\"/Script/KGStoryLineEditor.KGSLDialogueLine'/Temp/DialogueTransientPackage.********:KGSLDialogueLine_20'\"",
      [1524] = "      EpisodeID=1",
      [1525] = "      ContentIndex=7",
      [1526] = "      GUID=491772944",
      [1527] = "      UniqueID=234F7D0C62164BD1B35CA7B560EC5D58",
      [1528] = "      Delay=0.000001",
      [1529] = "      Duration=6.000000",
      [1530] = "      ContentString=\"于是我把报幕员邮给了她。没想到，我竟收获了名为“绝望”的情绪。\"",
      [1531] = "      ContentUI=\"Default\"",
      [1532] = "      Talker=(PerformerName=\"Actor2\")",
      [1533] = "   End Object",
      [1534] = "   Begin Object Name=\"BP_DLExtensionData_C_20\" ExportPath=\"/Game/Blueprint/DialogueSystem/BP_DLExtensionData.BP_DLExtensionData_C'/Temp/DialogueTransientPackage.********:BP_DLExtensionData_C_20'\"",
      [1535] = "   End Object",
      [1536] = "   EnableDOF=False",
      [1537] = "   PreLoadArray(0)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C\"",
      [1538] = "   PreLoadArray(1)=\"/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueSceneActorModel.BP_DialogueSceneActorModel_C\"",
      [1539] = "   PreLoadArray(2)=\"/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C\"",
      [1540] = "   ActorInfos(0)=(PerformerName=\"Actor1\",AppearanceID=(ApperanceID=1200001),IdleAnimation=(AssetID=\"Idle\"),bIsPlayer=True)",
      [1541] = "   ActorInfos(1)=(PerformerName=\"Actor2\",AppearanceID=(ApperanceID=7201036),IdleAnimation=(AssetID=\"Idle\"))",
      [1542] = "   ActorInfos(2)=(PerformerName=\"Actor3\",AppearanceID=(ApperanceID=7201038),IdleAnimation=(AssetID=\"Peed_Loop\"))",
      [1543] = "   ActorInfos(3)=(PerformerName=\"Actor4\",AppearanceID=(ApperanceID=7211087),IdleAnimation=(AssetID=\"Sad_Loop\"))",
      [1544] = "   ActorInfos(4)=(PerformerName=\"Actor5\",AppearanceID=(ApperanceID=7207020),IdleAnimation=(AssetID=\"Anger_Loop\"))",
      [1545] = "   ActorInfos(5)=(PerformerName=\"Actor6\",AppearanceID=(ApperanceID=7215021),IdleAnimation=(AssetID=\"Dance\"))",
      [1546] = "   ActorInfos(6)=(PerformerName=\"Actor7\",AppearanceID=(ApperanceID=7201014),IdleAnimation=(AssetID=\"Applaud_Loop\"))",
      [1547] = "   ActorInfos(7)=(PerformerName=\"Actor9\",AppearanceID=(ApperanceID=7201054),IdleAnimation=(AssetID=\"ShowItem_Loop\"))",
      [1548] = "   ActorInfos(8)=(PerformerName=\"Actor10\",AppearanceID=(ApperanceID=7201038),IdleAnimation=(AssetID=\"Idle\"))",
      [1549] = "   ActorInfos(9)=(PerformerName=\"Actor11\",AppearanceID=(ApperanceID=7201042),IdleAnimation=(AssetID=\"Stand_Read_Newspaper_Loop\"))",
      [1550] = "   ActorInfos(10)=(PerformerName=\"Actor12\",AppearanceID=(ApperanceID=7201038),IdleAnimation=(AssetID=\"Down\"))",
      [1551] = "   ActorInfos(11)=(PerformerName=\"Actor13\",AppearanceID=(ApperanceID=7201042),IdleAnimation=(AssetID=\"Sitdesk\"))",
      [1552] = "   DialogueTemplate=\"/Script/KGStoryLineEditor.DialogueTemplateAsset'/Game/Blueprint/DialogueSystem/Template/ThreePeopleDialogue.ThreePeopleDialogue'\"",
      [1553] = "   EditDialogueLineProxyIndex=0",
      [1554] = "   EpisodesList(0)=\"/Script/KGStoryLineEditor.KGSLDialogueEpisode'KGSLDialogueEpisode_1'\"",
      [1555] = "   StoryLineID=********",
      [1556] = "   Episodes(0)=(EpisodeID=1,Duration=41.051071,TrackList=(\"/Script/KGStoryLineEditor.DialogueStateControlTrack'DialogueStateControlTrack_1'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackPostProcess.BP_DialogueTrackPostProcess_C'BP_DialogueTrackPostProcess_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackFadeInOutPureColor.BP_DialogueTrackFadeInOutPureColor_C'BP_DialogueTrackFadeInOutPureColor_C_0'\",\"/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackCameraAnim.BP_DialogueTrackCameraAnim_C'BP_DialogueTrackCameraAnim_C_0'\",\"/Script/KGStoryLineEditor.DialogueCameraTrack'DialogueTrackCamera_0'\",\"/Script/KGStoryLineEditor.DialogueCameraCutTrack'DialogueCameraCutAction_0'\",\"/Script/KGStoryLineEditor.DialogueDialogueTrack'DialogueDialogueTrack_0'\"),Name=\"Episode:0\")",
      [1557] = "   PerformerList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_0'\"",
      [1558] = "   PerformerList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_1'\"",
      [1559] = "   PerformerList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_2'\"",
      [1560] = "   PerformerList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_3'\"",
      [1561] = "   PerformerList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_4'\"",
      [1562] = "   PerformerList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_5'\"",
      [1563] = "   PerformerList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_6'\"",
      [1564] = "   PerformerList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_8'\"",
      [1565] = "   PerformerList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_9'\"",
      [1566] = "   PerformerList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_10'\"",
      [1567] = "   PerformerList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_12'\"",
      [1568] = "   PerformerList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C'BP_DialogueActor_C_13'\"",
      [1569] = "   CameraList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_0'\"",
      [1570] = "   CameraList(1)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_1'\"",
      [1571] = "   CameraList(2)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_2'\"",
      [1572] = "   CameraList(3)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_3'\"",
      [1573] = "   CameraList(4)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_4'\"",
      [1574] = "   CameraList(5)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_5'\"",
      [1575] = "   CameraList(6)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_6'\"",
      [1576] = "   CameraList(7)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_7'\"",
      [1577] = "   CameraList(8)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_8'\"",
      [1578] = "   CameraList(9)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_9'\"",
      [1579] = "   CameraList(10)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_10'\"",
      [1580] = "   CameraList(11)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_11'\"",
      [1581] = "   CameraList(12)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_12'\"",
      [1582] = "   CameraList(13)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_13'\"",
      [1583] = "   CameraList(14)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_14'\"",
      [1584] = "   CameraList(15)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_16'\"",
      [1585] = "   CameraList(16)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_17'\"",
      [1586] = "   CameraList(17)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_18'\"",
      [1587] = "   CameraList(18)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_19'\"",
      [1588] = "   CameraList(19)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C'BP_DialogueCamera_C_20'\"",
      [1589] = "   NewEntityList(0)=\"/Game/Blueprint/DialogueSystem/Entity/BP_DialogueEntityModel.BP_DialogueEntityModel_C'BP_DialogueEntityModel_C_0'\"",
      [1590] = "   NativeClass=\"/Script/Engine.BlueprintGeneratedClass'/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C'\"",
      [1591] = "End Object",
    },
  },
}