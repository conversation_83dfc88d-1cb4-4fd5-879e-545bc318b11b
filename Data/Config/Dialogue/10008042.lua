return {
  ["AnchorID"] = "",
  ["AnchorNpc"] = "",
  ["AnchorType"] = 0,
  ["AutoPlayType"] = 0,
  ["BlendInCamera"] = false,
  ["BlendOutCamera"] = false,
  ["CameraList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 90,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9962,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.0872,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 492.8302,
          ["Y"] = 14183.9195,
          ["Z"] = -1022.4081,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "锚点",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8441,
          ["X"] = -0.0131,
          ["Y"] = 0.0206,
          ["Z"] = 0.5357,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -519.0089,
          ["Y"] = -1205.7868,
          ["Z"] = 194.8469,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "远景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [3] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 60,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.8692,
          ["X"] = -0.0129,
          ["Y"] = 0.0228,
          ["Z"] = 0.4938,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -175.1346,
          ["Y"] = -384.8524,
          ["Z"] = 128.8476,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "全景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [4] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 3.9837,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 393.9056,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor1",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9852,
          ["X"] = -0.0132,
          ["Y"] = 0.0897,
          ["Z"] = 0.1453,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -485.9932,
          ["Y"] = -105.573,
          ["Z"] = 258.6528,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [5] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 250,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.6946,
          ["X"] = 0.0038,
          ["Y"] = -0.0036,
          ["Z"] = 0.7193,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 63.7669,
          ["Y"] = -337.2652,
          ["Z"] = 142.7291,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "平视",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [6] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 175,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -1.4549,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3186,
          ["X"] = -0.0463,
          ["Y"] = 0.0154,
          ["Z"] = 0.9466,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 157.3794,
          ["Y"] = -112.1206,
          ["Z"] = 79.6971,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "过肩Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [7] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 120,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1614,
          ["X"] = -0.0516,
          ["Y"] = 0.0085,
          ["Z"] = 0.9855,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 116.3174,
          ["Y"] = -33.6888,
          ["Z"] = 80.8985,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [8] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 32,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0087,
          ["X"] = -0.0314,
          ["Y"] = 0.0003,
          ["Z"] = 0.9995,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 106.3032,
          ["Y"] = -1.4732,
          ["Z"] = 78.9625,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [9] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 70,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor2",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2956,
          ["X"] = -0.1443,
          ["Y"] = 0.0452,
          ["Z"] = 0.9433,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 153.1633,
          ["Y"] = -72.7805,
          ["Z"] = 98.7596,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [10] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 175,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 12.4097,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.3134,
          ["X"] = 0.0546,
          ["Y"] = 0.0181,
          ["Z"] = -0.9479,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 262.7274,
          ["Y"] = 162.387,
          ["Z"] = 88.8004,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近景过肩Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = true,
      ["bOverride_DepthOfField"] = true,
    },
    [11] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 85,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.0087,
          ["X"] = -0.0279,
          ["Y"] = -0.0002,
          ["Z"] = -0.9996,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 93.9252,
          ["Y"] = 1.5421,
          ["Z"] = 75.807,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近02Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [12] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 105,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 40,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.1219,
          ["X"] = -0.0121,
          ["Y"] = -0.0015,
          ["Z"] = -0.9925,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 107.6363,
          ["Y"] = 20.3786,
          ["Z"] = 76.1934,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "近01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [13] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 80,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.4954,
          ["X"] = 0.0122,
          ["Y"] = 0.0069,
          ["Z"] = -0.8686,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 86.8862,
          ["Y"] = 117.3691,
          ["Z"] = 63.5466,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中01Actor1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [14] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 30,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 100,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = 0,
      ["Parent"] = "Actor1",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = -0.2663,
          ["X"] = 0.134,
          ["Y"] = -0.0374,
          ["Z"] = -0.9538,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = 518.0476,
          ["Y"] = -335.796,
          ["Z"] = 159.5042,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "中景02",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = false,
    },
    [15] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 5.3012,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 278.3276,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -1.4549,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5256,
          ["X"] = -0.0623,
          ["Y"] = 0.0384,
          ["Z"] = 0.8476,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -31.1249,
          ["Y"] = -272.0817,
          ["Z"] = 241.1085,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera1",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [16] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 6.5757,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 278.3276,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -1.4549,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9923,
          ["X"] = 0.0002,
          ["Y"] = 0.1235,
          ["Z"] = -0.0035,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -432.057,
          ["Y"] = -20.3805,
          ["Z"] = 269.4746,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera2",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
    [17] = {
      ["ActorClass"] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
      ["BoneName"] = "head",
      ["DepthOfFieldFStop"] = 10.0384,
      ["DepthOfFieldFarTransitionRegion"] = 10000,
      ["DepthOfFieldFocalDistance"] = 278.3276,
      ["DepthOfFieldFocalRegion"] = 0,
      ["DepthOfFieldFocusActor"] = "Actor2",
      ["DepthOfFieldNearTransitionRegion"] = 10000,
      ["DepthOfFieldSensorWidth"] = 100,
      ["FOV"] = 50,
      ["FollowParentSocket"] = "",
      ["LookAtTarget"] = "Actor1",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueCamera.BP_DialogueCamera_C",
      ["OffsetZ"] = -1.4549,
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.5023,
          ["X"] = -0.0468,
          ["Y"] = 0.0269,
          ["Z"] = 0.863,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -81.7226,
          ["Y"] = -138.2028,
          ["Z"] = 223.532,
        },
      },
      ["SplineCurves"] = {
        ["Position"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 3,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = -130,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["ReparamTable"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = 0,
              ["InVal"] = 0,
              ["InterpMode"] = 0,
              ["LeaveTangent"] = 0,
              ["OutVal"] = 0,
            },
          },
          ["bIsLooped"] = false,
        },
        ["Rotation"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["W"] = 10000.0003,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
          },
          ["bIsLooped"] = false,
        },
        ["Scale"] = {
          ["LoopKeyOffset"] = 0,
          ["Points"] = {
            [1] = {
              ["ArriveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["InVal"] = 0,
              ["InterpMode"] = 1,
              ["LeaveTangent"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["OutVal"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
            },
          },
          ["bIsLooped"] = false,
        },
      },
      ["StickGround"] = false,
      ["SubCameraRotations"] = {
        [1] = {
          ["Pitch"] = 0,
          ["Roll"] = 0,
          ["Yaw"] = 0,
        },
      },
      ["TrackName"] = "Camera3",
      ["bDefaultVisible"] = true,
      ["bEnableLookAt"] = false,
      ["bOverride_DepthOfField"] = true,
    },
  },
  ["DialogueTemplate"] = "/Game/Blueprint/DialogueSystem/Template/TwoPeopleDialogue.TwoPeopleDialogue",
  ["EnableDOF"] = true,
  ["Episodes"] = {
    [1] = {
      ["Duration"] = 16.2527,
      ["EpisodeID"] = 1,
      ["LoopTimes"] = 0,
      ["OptionType"] = 0,
      ["Options"] = {
      },
      ["TimeLimit"] = 0,
      ["TimeOutDefaultChoice"] = 0,
      ["TrackList"] = {
        [1] = {
          ["ActionSections"] = {
            [1] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1077590874,
                ["B"] = 1375028200,
                ["C"] = -1292094284,
                ["D"] = 307280491,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 2.6527,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [2] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1744854910,
                ["B"] = -1630715424,
                ["C"] = -1734561906,
                ["D"] = -2118480823,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 6.6527,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [3] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1686380503,
                ["B"] = -1201126511,
                ["C"] = -1939697451,
                ["D"] = 1518095959,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 9.6527,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [4] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1456635828,
                ["B"] = -354992034,
                ["C"] = -2094599747,
                ["D"] = -514305803,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 12.6527,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
            [5] = {
              ["Duration"] = 0.1,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -2117245180,
                ["B"] = 2140423542,
                ["C"] = -1824357009,
                ["D"] = 2065904508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "Section",
              ["StartTime"] = 16.1527,
              ["bConstant"] = false,
              ["bFixed"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueStateControlTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_StateControl.BPS_StateControl_C",
          ["TrackName"] = "StateControl",
        },
        [2] = {
          ["Actions"] = {
          },
          ["Childs"] = {
            [1] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "远景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "远景",
              ["bAutoCameraTrack"] = false,
            },
            [2] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "全景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "全景",
              ["bAutoCameraTrack"] = false,
            },
            [3] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "中景",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "中景",
              ["bAutoCameraTrack"] = false,
            },
            [4] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "平视",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "平视",
              ["bAutoCameraTrack"] = false,
            },
            [5] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackDirection.BP_DialogueTrackDirection_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorDirection.BPS_ActorDirection_C",
                  ["TrackName"] = "Direction",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalWoman",
                        ["AssetID"] = "StandCollection_End1",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 2,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalWoman",
                        ["AssetID"] = "Talk_Yes_A",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3.1667,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 1,
                      ["LineGUIDLinked"] = 1703617203,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 1744854910,
                        ["B"] = -1630715424,
                        ["C"] = -1734561906,
                        ["D"] = -2118480823,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor2",
                      ["StartTime"] = 2.7527,
                      ["bConstant"] = true,
                    },
                    [3] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalWoman",
                        ["AssetID"] = "Talk_No_B",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 3,
                      ["LineGUIDLinked"] = 2915934767,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = -1456635828,
                        ["B"] = -354992034,
                        ["C"] = -2094599747,
                        ["D"] = -514305803,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor2",
                      ["StartTime"] = 9.7527,
                      ["bConstant"] = true,
                    },
                    [4] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "NormalWoman",
                        ["AssetID"] = "Big_Breath",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3.5,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 4,
                      ["LineGUIDLinked"] = 1749822887,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = -2117245180,
                        ["B"] = 2140423542,
                        ["C"] = -1824357009,
                        ["D"] = 2065904508,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor2",
                      ["StartTime"] = 12.7527,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [3] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["LookAtSocket"] = "head",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Target"] = "Actor1",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["Parent"] = "Actor2",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "过肩Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "过肩Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "近02Actor2",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor2",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor2",
                  ["TrackName"] = "中01Actor2",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor2",
            },
            [6] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera1",
              ["bAutoCameraTrack"] = false,
            },
            [7] = {
              ["Actions"] = {
                [1] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Big_Think",
                        ["StateName"] = "",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = 2,
                      ["LineGUIDLinked"] = 831623416,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = -1686380503,
                        ["B"] = -1201126511,
                        ["C"] = -1939697451,
                        ["D"] = 1518095959,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.2,
                      ["SectionName"] = "Actor1",
                      ["StartTime"] = 6.7527,
                      ["bConstant"] = true,
                    },
                    [2] = {
                      ["AnimLibItem"] = {
                        ["AnimID"] = "",
                        ["AssetID"] = "Big_Think",
                        ["StateName"] = "End",
                      },
                      ["BlendOutTime"] = 0,
                      ["Duration"] = 3,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                      ["OwnedEpisodeID"] = 1,
                      ["PreAnimationBlendOutTime"] = 0.8,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 9.7527,
                      ["bConstant"] = true,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackAnimation.BP_DialogueTrackAnimation_C",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_PlayAnimation.BPS_PlayAnimation_C",
                  ["TrackName"] = "Animation",
                },
                [2] = {
                  ["ActionSections"] = {
                    [1] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["LookAtSocket"] = "head",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 0,
                      ["Target"] = "中01Actor1",
                      ["bConstant"] = false,
                    },
                    [2] = {
                      ["Duration"] = 0.0333,
                      ["DynamicFlag"] = "",
                      ["Enable"] = true,
                      ["FromLineIndex"] = -1,
                      ["LineGUIDLinked"] = 0,
                      ["LineUniqueIDLinked"] = {
                        ["A"] = 0,
                        ["B"] = 0,
                        ["C"] = 0,
                        ["D"] = 0,
                      },
                      ["LookAtSocket"] = "head",
                      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                      ["OwnedEpisodeID"] = 1,
                      ["SectionName"] = "Section",
                      ["StartTime"] = 1.8209,
                      ["Target"] = "Actor2",
                      ["bConstant"] = false,
                    },
                  },
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackActorLookAt.BP_DialogueTrackActorLookAt_C",
                  ["Parent"] = "Actor1",
                  ["Priority"] = 0,
                  ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_ActorLookAt.BPS_ActorLookAt_C",
                  ["TrackName"] = "LookAt",
                },
              },
              ["Childs"] = {
                [1] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近02Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近02Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [2] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [3] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中01Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中01Actor1",
                  ["bAutoCameraTrack"] = false,
                },
                [4] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "中景02",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "中景02",
                  ["bAutoCameraTrack"] = false,
                },
                [5] = {
                  ["Actions"] = {
                  },
                  ["Childs"] = {
                  },
                  ["DialogueEntity"] = "近景过肩Actor1",
                  ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
                  ["Parent"] = "Actor1",
                  ["TrackName"] = "近景过肩Actor1",
                  ["bAutoCameraTrack"] = false,
                },
              },
              ["DialogueEntity"] = "Actor1",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueActorTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Actor1",
            },
            [8] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera2",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera2",
              ["bAutoCameraTrack"] = false,
            },
            [9] = {
              ["Actions"] = {
              },
              ["Childs"] = {
              },
              ["DialogueEntity"] = "Camera3",
              ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
              ["Parent"] = "锚点",
              ["TrackName"] = "Camera3",
              ["bAutoCameraTrack"] = false,
            },
          },
          ["DialogueEntity"] = "锚点",
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraTrack",
          ["Parent"] = "",
          ["TrackName"] = "锚点",
          ["bAutoCameraTrack"] = false,
        },
        [3] = {
          ["ActionSections"] = {
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Track/BP_DialogueTrackSound.BP_DialogueTrackSound_C",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Sound.BPS_Sound_C",
          ["TrackName"] = "Sound",
        },
        [4] = {
          ["ActionSections"] = {
            [1] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 3,
              ["CameraBreathType"] = 2,
              ["CameraName"] = "None",
              ["Duration"] = 2.7527,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 0,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1681949863,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1077590874,
                ["B"] = 1375028200,
                ["C"] = -1292094284,
                ["D"] = 307280491,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "近景过肩Actor1",
              ["StartTime"] = 0,
              ["TargetCamera"] = "中景",
              ["bConstant"] = true,
            },
            [2] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 1,
              ["CameraName"] = "None",
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 1,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1703617203,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1744854910,
                ["B"] = -1630715424,
                ["C"] = -1734561906,
                ["D"] = -2118480823,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "过肩Actor2",
              ["StartTime"] = 2.7527,
              ["TargetCamera"] = "Camera1",
              ["bConstant"] = true,
            },
            [3] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 2,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 2,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 831623416,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1686380503,
                ["B"] = -1201126511,
                ["C"] = -1939697451,
                ["D"] = 1518095959,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "近景过肩Actor1",
              ["StartTime"] = 6.7527,
              ["TargetCamera"] = "Camera2",
              ["bConstant"] = true,
            },
            [4] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 2,
              ["CameraBreathType"] = 2,
              ["CameraName"] = "None",
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 3,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 2915934767,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1456635828,
                ["B"] = -354992034,
                ["C"] = -2094599747,
                ["D"] = -514305803,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "过肩Actor2",
              ["StartTime"] = 9.7527,
              ["TargetCamera"] = "Camera3",
              ["bConstant"] = true,
            },
            [5] = {
              ["BreathAttenuation"] = 0,
              ["BreathDelay"] = 0,
              ["BreathFocusDistance"] = 150,
              ["BreathSpeed"] = 15,
              ["CameraBreathType"] = 6,
              ["CameraName"] = "None",
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["FromLineIndex"] = 4,
              ["ImmediateCut"] = true,
              ["LineGUIDLinked"] = 1749822887,
              ["LineUniqueIDLinked"] = {
                ["A"] = -2117245180,
                ["B"] = 2140423542,
                ["C"] = -1824357009,
                ["D"] = 2065904508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
              ["OwnedEpisodeID"] = 1,
              ["SectionName"] = "过肩Actor2",
              ["StartTime"] = 12.7527,
              ["TargetCamera"] = "中景",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueCameraCutTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_CameraCut.BPS_CameraCut_C",
          ["TrackName"] = "CameraCut",
        },
        [5] = {
          ["ActionSections"] = {
            [1] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 1,
              ["ContentUI"] = 0,
              ["ContentUI_0"] = 0,
              ["Duration"] = 2.7527,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 0,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1077590874,
                ["B"] = 1375028200,
                ["C"] = -1292094284,
                ["D"] = 307280491,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "你喜欢雏菊，丽埃娜？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 0,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [2] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 2,
              ["ContentUI"] = 0,
              ["ContentUI_0"] = 0,
              ["Duration"] = 4,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 1,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = 1744854910,
                ["B"] = -1630715424,
                ["C"] = -1734561906,
                ["D"] = -2118480823,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "雏菊让我想到很多。比如天真、纯洁、无忧无虑，和永远快乐。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 2.7527,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [3] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 3,
              ["ContentUI"] = 0,
              ["ContentUI_0"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 2,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1686380503,
                ["B"] = -1201126511,
                ["C"] = -1939697451,
                ["D"] = 1518095959,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "听起来……是一些属于孩子的东西。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 6.7527,
              ["SubTitle"] = "",
              ["Talker"] = "Actor1",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [4] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 4,
              ["ContentUI"] = 0,
              ["ContentUI_0"] = 0,
              ["Duration"] = 3,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 3,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -1456635828,
                ["B"] = -354992034,
                ["C"] = -2094599747,
                ["D"] = -514305803,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "是的，这些对成年人来说，总是很稀缺。",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 9.7527,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
            [5] = {
              ["CanSkip"] = true,
              ["ContentIndex"] = 5,
              ["ContentUI"] = 0,
              ["ContentUI_0"] = 0,
              ["Duration"] = 3.5,
              ["DynamicFlag"] = "",
              ["Enable"] = true,
              ["EpisodeID"] = 1,
              ["FromLineIndex"] = 4,
              ["LineGUIDLinked"] = 0,
              ["LineUniqueIDLinked"] = {
                ["A"] = -2117245180,
                ["B"] = 2140423542,
                ["C"] = -1824357009,
                ["D"] = 2065904508,
              },
              ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
              ["OwnedEpisodeID"] = 1,
              ["Pause"] = true,
              ["PrinterSpeed"] = 0,
              ["SectionName"] = "但至少花香还在，为什么不闻一闻？",
              ["SendOther"] = false,
              ["Small"] = "",
              ["SmallPos"] = "",
              ["StartTime"] = 12.7527,
              ["SubTitle"] = "",
              ["Talker"] = "Actor2",
              ["TalkerName"] = "",
              ["bConstant"] = true,
            },
          },
          ["Actions"] = {
          },
          ["Childs"] = {
          },
          ["ObjectClass"] = "/Script/KGStoryLineEditor.DialogueDialogueTrack",
          ["Parent"] = "",
          ["Priority"] = 0,
          ["SectionType"] = "/Game/Blueprint/DialogueSystem/Section/BPS_Dialogue.BPS_Dialogue_C",
          ["TrackName"] = "Dialogue",
        },
      },
    },
  },
  ["ExportToServer"] = false,
  ["HideAtmosphereNpc"] = true,
  ["HideNpcRange"] = 0,
  ["HideNpcType"] = 1,
  ["NativeClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["NeedFadeIn"] = false,
  ["NeedFadeOut"] = false,
  ["NewEntityList"] = {
  },
  ["Note"] = "",
  ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/BP_DialogueAsset.BP_DialogueAsset_C",
  ["ObjectName"] = "********",
  ["PerformerList"] = {
    [1] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 7215073,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.9848,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = -0.1737,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -178.8831,
          ["Y"] = -0.9377,
          ["Z"] = 144.3276,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor2",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = false,
    },
    [2] = {
      ["ActorClass"] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
      ["AppearanceID"] = 12000011,
      ["FollowParentSocket"] = "",
      ["IdleAnimLibAssetID"] = {
        ["AnimID"] = "",
        ["AssetID"] = "Idle",
        ["StateName"] = "",
      },
      ["IdleAnimation"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Idle.A_Base_Idle",
      ["InsID"] = "",
      ["ObjectClass"] = "/Game/Blueprint/DialogueSystem/Entity/BP_DialogueActor.BP_DialogueActor_C",
      ["Parent"] = "锚点",
      ["SpawnTransform"] = {
        ["Rotation"] = {
          ["W"] = 0.2589,
          ["X"] = 0,
          ["Y"] = 0,
          ["Z"] = 0.9659,
        },
        ["Scale3D"] = {
          ["X"] = 1,
          ["Y"] = 1,
          ["Z"] = 1,
        },
        ["Translation"] = {
          ["X"] = -100.2731,
          ["Y"] = -43.3969,
          ["Z"] = 147.7483,
        },
      },
      ["StickGround"] = true,
      ["TrackName"] = "Actor1",
      ["UseLookAtFunction"] = true,
      ["UseSceneActor"] = false,
      ["bDefaultVisible"] = true,
      ["bIsPlayer"] = true,
    },
  },
  ["PreLoadArray"] = {
    [1] = "/Game/Blueprint/3C/Actor/BP_DialogueActor.BP_DialogueActor_C",
    [2] = "/Game/Blueprint/DialogueSystem/SceneActor/BP_DialogueCameraActor.BP_DialogueCameraActor_C",
  },
  ["PreLoadBanks"] = {
  },
  ["RoutePointList"] = {
  },
  ["StoryLineID"] = ********,
  ["Unique"] = true,
  ["UseTemplateCamera"] = true,
  ["ZZZ_EditorOnly"] = {
  },
}