local AnimLibEnum = {}

AnimLibEnum.EAnimLib_NpcAnimType = {
	["A_Base_Hello_Storm"] = "A_Base_Hello_Storm",
	["A_Base_Shock"] = "A_Base_Shock",
	["A_F_Beatground_End"] = "A_F_Beatground_End",
	["A_F_Beatground_Loop"] = "A_F_Beatground_Loop",
	["A_F_Beatground_Start"] = "A_F_Beatground_Start",
	["A_F_Opendoor"] = "A_F_Opendoor",
	["A_F_Perform_End"] = "A_F_Perform_End",
	["A_F_Perform_Idle"] = "A_F_Perform_Idle",
	["A_F_Perform_Loop01"] = "A_F_Perform_Loop01",
	["A_F_Perform_Loop02"] = "A_F_Perform_Loop02",
	["A_F_Perform_Start"] = "A_F_Perform_Start",
	["A_M_Perform_End"] = "A_M_Perform_End",
	["A_M_Perform_Idle"] = "A_M_Perform_Idle",
	["A_M_Perform_Loop01"] = "A_M_Perform_Loop01",
	["A_M_Perform_Loop02"] = "A_M_Perform_Loop02",
	["A_M_Perform_Start"] = "A_M_Perform_Start",
	["A_Manta_Swim魔鬼鱼_游泳"] = "A_Manta_Swim魔鬼鱼_游泳",
	["A_NPC_Crying"] = "A_NPC_Crying",
	["A_NPC_F_Sit_Poor"] = "A_NPC_F_Sit_Poor",
	["A_NPC_Lookback_L"] = "A_NPC_Lookback_L",
	["A_NPC_M_Fight_Fight_01"] = "A_NPC_M_Fight_Fight_01",
	["A_NPC_M_Fight_Fight_02"] = "A_NPC_M_Fight_Fight_02",
	["A_NPC_M_Gangfight_Beat01_Start"] = "A_NPC_M_Gangfight_Beat01_Start",
	["A_NPC_M_Shoeshine_Loop"] = "A_NPC_M_Shoeshine_Loop",
	["A_NPC_Walk_Lookaround"] = "A_NPC_Walk_Lookaround",
	["AirHitGourp"] = "AirHitGourp",
	["AirHitGroup"] = "AirHitGroup",
	["Ball_Partner_Dance"] = "Ball_Partner_Dance",
	["Base_Reject"] = "Base_Reject",
	["BasicDance_Dance"] = "BasicDance_Dance",
	["BasicDance_Waltz_P1"] = "BasicDance_Waltz_P1",
	["BasicDance_Waltz_P2"] = "BasicDance_Waltz_P2",
	["BowSelect"] = "BowSelect",
	["Broom_Clean_End"] = "Broom_Clean_End",
	["Broom_Clean_Loop"] = "Broom_Clean_Loop",
	["Broom_Clean_Start"] = "Broom_Clean_Start",
	["Camera_Dir_Move_Back"] = "Camera_Dir_Move_Back",
	["Camera_Dir_Move_End"] = "Camera_Dir_Move_End",
	["Camera_Dir_Move_Forward"] = "Camera_Dir_Move_Forward",
	["Camera_Dir_Move_Idle"] = "Camera_Dir_Move_Idle",
	["Camera_Dir_Move_Left"] = "Camera_Dir_Move_Left",
	["Camera_Dir_Move_Right"] = "Camera_Dir_Move_Right",
	["Camera_Dir_Move_Start"] = "Camera_Dir_Move_Start",
	["Carry_End"] = "Carry_End",
	["Carry_Idle_Loop"] = "Carry_Idle_Loop",
	["Check_Record"] = "Check_Record",
	["Cheerless"] = "Cheerless",
	["Cheerless_To_Spirited"] = "Cheerless_To_Spirited",
	["CollisionFeedback_LeftBack"] = "CollisionFeedback_LeftBack",
	["CollisionFeedback_LeftFront"] = "CollisionFeedback_LeftFront",
	["CollisionFeedback_RightBack"] = "CollisionFeedback_RightBack",
	["CollisionFeedback_RightFront"] = "CollisionFeedback_RightFront",
	["Comment"] = "Comment",
	["Cook"] = "Cook",
	["CustomRoleShow_Loop"] = "CustomRoleShow_Loop",
	["Cute"] = "Cute",
	["Disappear"] = "Disappear",
	["Dizziness_End"] = "Dizziness_End",
	["Dizziness_Idle"] = "Dizziness_Idle",
	["Dizziness_Start"] = "Dizziness_Start",
	["Dizziness_Walk"] = "Dizziness_Walk",
	["Dodge"] = "Dodge",
	["Dodge_Air"] = "Dodge_Air",
	["Dodge_Glide"] = "Dodge_Glide",
	["Dodge_Glide_Loop"] = "Dodge_Glide_Loop",
	["Dodge_R"] = "Dodge_R",
	["Dodge_Restart"] = "Dodge_Restart",
	["Dodge_Restart_R"] = "Dodge_Restart_R",
	["Double_Jump_Restart"] = "Double_Jump_Restart",
	["Emlyn001_Show"] = "Emlyn001_Show",
	["Emlyndoll7_Idle_End"] = "Emlyndoll7_Idle_End",
	["Emlyndoll7_Idle_Loop"] = "Emlyndoll7_Idle_Loop",
	["Emlyndoll7_Idle_Start"] = "Emlyndoll7_Idle_Start",
	["Emlyndoll7_Show_End"] = "Emlyndoll7_Show_End",
	["Emlyndoll7_Show_Loop"] = "Emlyndoll7_Show_Loop",
	["Emlyndoll7_Show_Start"] = "Emlyndoll7_Show_Start",
	["ExDizzy_Loop"] = "ExDizzy_Loop",
	["ExSleep_Loop"] = "ExSleep_Loop",
	["F_Broom_Clean_End"] = "F_Broom_Clean_End",
	["F_Broom_Clean_Loop"] = "F_Broom_Clean_Loop",
	["F_Broom_Clean_Start"] = "F_Broom_Clean_Start",
	["GoundHit"] = "GoundHit",
	["GroundHitGroup"] = "GroundHitGroup",
	["Hit_Air_AirHit_1"] = "Hit_Air_AirHit_1",
	["Hit_Air_AirHit_2"] = "Hit_Air_AirHit_2",
	["Hit_Air_Height01_1"] = "Hit_Air_Height01_1",
	["Hit_Air_Height01_2"] = "Hit_Air_Height01_2",
	["Hit_Air_Height01_3"] = "Hit_Air_Height01_3",
	["Hit_Air_Height02_1"] = "Hit_Air_Height02_1",
	["Hit_Air_Height03_1"] = "Hit_Air_Height03_1",
	["Hit_Air_Height04_1"] = "Hit_Air_Height04_1",
	["Hit_Back_End_B_1"] = "Hit_Back_End_B_1",
	["Hit_Back_End_B_2"] = "Hit_Back_End_B_2",
	["Hit_Back_End_F_1"] = "Hit_Back_End_F_1",
	["Hit_Back_End_F_2"] = "Hit_Back_End_F_2",
	["Hit_Back_End_L_1"] = "Hit_Back_End_L_1",
	["Hit_Back_End_R_1"] = "Hit_Back_End_R_1",
	["Hit_Back_Loop_B_1"] = "Hit_Back_Loop_B_1",
	["Hit_Back_Loop_B_2"] = "Hit_Back_Loop_B_2",
	["Hit_Back_Loop_F_1"] = "Hit_Back_Loop_F_1",
	["Hit_Back_Loop_F_2"] = "Hit_Back_Loop_F_2",
	["Hit_Back_Loop_L_1"] = "Hit_Back_Loop_L_1",
	["Hit_Back_Loop_R_1"] = "Hit_Back_Loop_R_1",
	["Hit_BlowFly_Loop"] = "Hit_BlowFly_Loop",
	["Hit_Fall_End"] = "Hit_Fall_End",
	["Hit_Fall_Loop"] = "Hit_Fall_Loop",
	["Hit_Fall_Start"] = "Hit_Fall_Start",
	["Hit_GroundHit_1"] = "Hit_GroundHit_1",
	["Hit_GroundHit_2"] = "Hit_GroundHit_2",
	["Hit_GroundHit_3"] = "Hit_GroundHit_3",
	["Hit_Stiff_1"] = "Hit_Stiff_1",
	["Hit_Stiff_2"] = "Hit_Stiff_2",
	["Hit_Struggle_B_1"] = "Hit_Struggle_B_1",
	["Hit_Struggle_B_2"] = "Hit_Struggle_B_2",
	["Hit_Struggle_F_1"] = "Hit_Struggle_F_1",
	["Hit_Struggle_F_2"] = "Hit_Struggle_F_2",
	["Hit_Struggle_L_1"] = "Hit_Struggle_L_1",
	["Hit_Struggle_R_1"] = "Hit_Struggle_R_1",
	["HitBackGroup"] = "HitBackGroup",
	["HitDownGroup"] = "HitDownGroup",
	["HitDragGroup"] = "HitDragGroup",
	["HitFallGroup"] = "HitFallGroup",
	["HitFloatGroup"] = "HitFloatGroup",
	["HitFlyFroup"] = "HitFlyFroup",
	["HitFlyGroup"] = "HitFlyGroup",
	["HitStiffGroup"] = "HitStiffGroup",
	["Horse_Calling"] = "Horse_Calling",
	["Horse_Dash"] = "Horse_Dash",
	["Horse_GetIn"] = "Horse_GetIn",
	["Horse_GetIn_Move"] = "Horse_GetIn_Move",
	["Horse_GetIn_Move_Right"] = "Horse_GetIn_Move_Right",
	["Horse_GetIn_Right"] = "Horse_GetIn_Right",
	["Horse_GetOut"] = "Horse_GetOut",
	["Horse_GetOut_Move"] = "Horse_GetOut_Move",
	["Horse_Idle"] = "Horse_Idle",
	["Horse_Jump_End"] = "Horse_Jump_End",
	["Horse_Jump_Loop"] = "Horse_Jump_Loop",
	["Horse_Jump_Start"] = "Horse_Jump_Start",
	["Horse_Move_Turn"] = "Horse_Move_Turn",
	["Horse_Move_Turn_Sprint"] = "Horse_Move_Turn_Sprint",
	["Horse_Run_End"] = "Horse_Run_End",
	["Horse_Run_Loop"] = "Horse_Run_Loop",
	["Horse_Run_Start"] = "Horse_Run_Start",
	["Horse_Sprint_End"] = "Horse_Sprint_End",
	["Horse_Sprint_Loop"] = "Horse_Sprint_Loop",
	["Horse_Turn45_L"] = "Horse_Turn45_L",
	["Horse_Turn45_R"] = "Horse_Turn45_R",
	["Horse_Walk_End"] = "Horse_Walk_End",
	["Horse_Walk_Loop"] = "Horse_Walk_Loop",
	["Horse_Walk_Start"] = "Horse_Walk_Start",
	["HorseRiding"] = "HorseRiding",
	["Idle2"] = "Idle2",
	["Idle_Cheer"] = "Idle_Cheer",
	["Idle_Dance"] = "Idle_Dance",
	["Idle_Dance_Royal"] = "Idle_Dance_Royal",
	["Idle_Fear"] = "Idle_Fear",
	["Idle_Hit_1"] = "Idle_Hit_1",
	["Idle_Hit_2"] = "Idle_Hit_2",
	["Idle_Hit_3"] = "Idle_Hit_3",
	["Idle_Hit_4"] = "Idle_Hit_4",
	["Idle_Hit_5"] = "Idle_Hit_5",
	["Idle_Illusion"] = "Idle_Illusion",
	["Idle_Spoil"] = "Idle_Spoil",
	["Idle_Stand"] = "Idle_Stand",
	["Idle_to_WaterIdle"] = "Idle_to_WaterIdle",
	["Idle_to_WaterIdle_End"] = "Idle_to_WaterIdle_End",
	["Idle_to_WaterIdle_Loop"] = "Idle_to_WaterIdle_Loop",
	["Idle_to_WaterIdle_Start"] = "Idle_to_WaterIdle_Start",
	["IdleHit1"] = "IdleHit1",
	["IdleHit2"] = "IdleHit2",
	["Jump_2nd"] = "Jump_2nd",
	["Jump_3rd_End"] = "Jump_3rd_End",
	["Jump_3rd_Loop"] = "Jump_3rd_Loop",
	["Jump_3rd_Start"] = "Jump_3rd_Start",
	["Kneedown_Illusion"] = "Kneedown_Illusion",
	["Lie_Ed04"] = "Lie_Ed04",
	["Lie_Loop04"] = "Lie_Loop04",
	["Lie_St04"] = "Lie_St04",
	["LightHitFullBody"] = "LightHitFullBody",
	["LightHitGroup"] = "LightHitGroup",
	["Mount_Passenger_Dash"] = "Mount_Passenger_Dash",
	["Mount_Passenger_GetIn"] = "Mount_Passenger_GetIn",
	["Mount_Passenger_GetOut"] = "Mount_Passenger_GetOut",
	["Mount_Passenger_Idle"] = "Mount_Passenger_Idle",
	["Mount_Passenger_Jump_End"] = "Mount_Passenger_Jump_End",
	["Mount_Passenger_Jump_Start"] = "Mount_Passenger_Jump_Start",
	["Mount_Passenger_MoveTurn"] = "Mount_Passenger_MoveTurn",
	["Move_Hit_1"] = "Move_Hit_1",
	["NPC_M_Be_Shoeshine_End"] = "NPC_M_Be_Shoeshine_End",
	["NPC_M_Shoeshine_Call_Loop"] = "NPC_M_Shoeshine_Call_Loop",
	["NPC_M_Shoeshine_Call_Start"] = "NPC_M_Shoeshine_Call_Start",
	["NPC_M_Shoeshine_End"] = "NPC_M_Shoeshine_End",
	["NPC_M_Shoeshine_exchange"] = "NPC_M_Shoeshine_exchange",
	["NPC_M_Shoeshine_Idle"] = "NPC_M_Shoeshine_Idle",
	["NPC_M_Shoeshine_Loop"] = "NPC_M_Shoeshine_Loop",
	["NPC_M_Shoeshine_Start"] = "NPC_M_Shoeshine_Start",
	["OnGround"] = "OnGround",
	["Passedout_End"] = "Passedout_End",
	["Passedout_Loop"] = "Passedout_Loop",
	["Passedout_Start"] = "Passedout_Start",
	["petcat"] = "petcat",
	["Play"] = "Play",
	["PlayBilliards_End"] = "PlayBilliards_End",
	["PlayBilliards_Loop"] = "PlayBilliards_Loop",
	["PlayBilliards_Start"] = "PlayBilliards_Start",
	["PlayPiano"] = "PlayPiano",
	["Rage"] = "Rage",
	["Riding_Bike"] = "Riding_Bike",
	["Riding_Car"] = "Riding_Car",
	["Riding_GetIn_Move"] = "Riding_GetIn_Move",
	["Riding_GetIn_Move_Right"] = "Riding_GetIn_Move_Right",
	["Riding_GetIn_Right"] = "Riding_GetIn_Right",
	["Riding_GetOut_Move"] = "Riding_GetOut_Move",
	["Riding_Jump_2nd"] = "Riding_Jump_2nd",
	["Riding_Jump_2nd_Run"] = "Riding_Jump_2nd_Run",
	["Riding_Jump_2nd_Sprint"] = "Riding_Jump_2nd_Sprint",
	["Riding_Jump_2nd_Walk"] = "Riding_Jump_2nd_Walk",
	["Riding_Jump_Start_Run"] = "Riding_Jump_Start_Run",
	["Riding_Jump_Start_Sprint"] = "Riding_Jump_Start_Sprint",
	["Riding_Jump_Start_Walk"] = "Riding_Jump_Start_Walk",
	["Riding_Jump_Turn45_L"] = "Riding_Jump_Turn45_L",
	["Riding_Jump_Turn45_R"] = "Riding_Jump_Turn45_R",
	["Riding_Move_Turn_45_L"] = "Riding_Move_Turn_45_L",
	["Riding_Move_Turn_45_R"] = "Riding_Move_Turn_45_R",
	["Riding_MoveTurn_End"] = "Riding_MoveTurn_End",
	["Riding_Run_Jump_End_Restart"] = "Riding_Run_Jump_End_Restart",
	["Riding_Sprint_Jump_End_Restart"] = "Riding_Sprint_Jump_End_Restart",
	["Riding_Turn45_L_Rapid"] = "Riding_Turn45_L_Rapid",
	["Riding_Turn45_L_Sprint"] = "Riding_Turn45_L_Sprint",
	["Riding_Turn45_R_Rapid"] = "Riding_Turn45_R_Rapid",
	["Riding_Turn45_R_Sprint"] = "Riding_Turn45_R_Sprint",
	["Riding_Walk_Jump_End_Restart"] = "Riding_Walk_Jump_End_Restart",
	["Run_Jump"] = "Run_Jump",
	["Run_Left"] = "Run_Left",
	["Run_Right"] = "Run_Right",
	["Run_to_WaterIdle"] = "Run_to_WaterIdle",
	["Rush"] = "Rush",
	["Sitcarriage_Loop"] = "Sitcarriage_Loop",
	["SK_Rag_Shoeshine_call_loop"] = "SK_Rag_Shoeshine_call_loop",
	["SK_Rag_Shoeshine_call_start"] = "SK_Rag_Shoeshine_call_start",
	["SK_Rag_Shoeshine_End"] = "SK_Rag_Shoeshine_End",
	["SK_Rag_Shoeshine_Loop"] = "SK_Rag_Shoeshine_Loop",
	["SK_Rag_Shoeshine_Start"] = "SK_Rag_Shoeshine_Start",
	["Sleep"] = "Sleep",
	["Special_Idle"] = "Special_Idle",
	["Spirited"] = "Spirited",
	["Spirited_To_Cheerless"] = "Spirited_To_Cheerless",
	["Sun_Attack"] = "Sun_Attack",
	["Thanks"] = "Thanks",
	["Trot"] = "Trot",
	["Trot_Left"] = "Trot_Left",
	["Trot_Right"] = "Trot_Right",
	["Turn_Left"] = "Turn_Left",
	["Turn_Right"] = "Turn_Right",
	["Umbrella_Idle"] = "Umbrella_Idle",
	["Umbrella_Move"] = "Umbrella_Move",
	["Umbrella_Move_Woman"] = "Umbrella_Move_Woman",
	["VintageCar"] = "VintageCar",
	["VintageCar_Dash"] = "VintageCar_Dash",
	["VintageCar_Idle"] = "VintageCar_Idle",
	["VintageCar_Jump_2nd"] = "VintageCar_Jump_2nd",
	["VintageCar_Jump_2nd_Run"] = "VintageCar_Jump_2nd_Run",
	["VintageCar_Jump_2nd_Sprint"] = "VintageCar_Jump_2nd_Sprint",
	["VintageCar_Jump_2nd_Walk"] = "VintageCar_Jump_2nd_Walk",
	["VintageCar_Jump_End"] = "VintageCar_Jump_End",
	["VintageCar_Jump_Loop"] = "VintageCar_Jump_Loop",
	["VintageCar_Jump_Start"] = "VintageCar_Jump_Start",
	["VintageCar_Jump_Start_Run"] = "VintageCar_Jump_Start_Run",
	["VintageCar_Jump_Start_Sprint"] = "VintageCar_Jump_Start_Sprint",
	["VintageCar_Jump_Start_Walk"] = "VintageCar_Jump_Start_Walk",
	["VintageCar_Move_Turn"] = "VintageCar_Move_Turn",
	["VintageCar_Move_Turn_Sprint"] = "VintageCar_Move_Turn_Sprint",
	["VintageCar_Run_End"] = "VintageCar_Run_End",
	["VintageCar_Run_Jump_End_Restart"] = "VintageCar_Run_Jump_End_Restart",
	["VintageCar_Run_Loop"] = "VintageCar_Run_Loop",
	["VintageCar_Run_Start"] = "VintageCar_Run_Start",
	["VintageCar_Sprint_End"] = "VintageCar_Sprint_End",
	["VintageCar_Sprint_Jump_End_Restart"] = "VintageCar_Sprint_Jump_End_Restart",
	["VintageCar_Sprint_Loop"] = "VintageCar_Sprint_Loop",
	["VintageCar_Turn45_L"] = "VintageCar_Turn45_L",
	["VintageCar_Turn45_R"] = "VintageCar_Turn45_R",
	["VintageCar_Walk_End"] = "VintageCar_Walk_End",
	["VintageCar_Walk_Jump_End_Restart"] = "VintageCar_Walk_Jump_End_Restart",
	["VintageCar_Walk_Loop"] = "VintageCar_Walk_Loop",
	["VintageCar_Walk_Start"] = "VintageCar_Walk_Start",
	["Walk_BackLeft"] = "Walk_BackLeft",
	["Walk_BackRight"] = "Walk_BackRight",
	["Water_Dodge"] = "Water_Dodge",
	["Water_Jump_2nd"] = "Water_Jump_2nd",
	["Water_Jump_3nd_End"] = "Water_Jump_3nd_End",
	["Water_Jump_3nd_Loop"] = "Water_Jump_3nd_Loop",
	["Water_Jump_3nd_Start"] = "Water_Jump_3nd_Start",
	["Water_Jump_3rd_Restart"] = "Water_Jump_3rd_Restart",
	["Water_Jump_End"] = "Water_Jump_End",
	["Water_Jump_Loop"] = "Water_Jump_Loop",
	["Water_Jump_Restart"] = "Water_Jump_Restart",
	["Water_Jump_Start"] = "Water_Jump_Start",
	["Water_Run"] = "Water_Run",
	["Water_Run_End"] = "Water_Run_End",
	["Water_Run_MoveTurn_L"] = "Water_Run_MoveTurn_L",
	["Water_Run_MoveTurn_R"] = "Water_Run_MoveTurn_R",
	["Water_Run_Start"] = "Water_Run_Start",
	["WaterIdle"] = "WaterIdle",
	["WaterIdle_to_Idle"] = "WaterIdle_to_Idle",
	["WaterIdle_to_Idle_End"] = "WaterIdle_to_Idle_End",
	["WaterIdle_to_Idle_Start"] = "WaterIdle_to_Idle_Start",
	["上马车"] = "GetOn_HorseCar",
	["下刺"] = "Stab_Dagger",
	["下跪祈祷"] = "Kneelpray",
	["下跪祈祷_开始"] = "Kneelpray_Start",
	["下跪祈祷_循环"] = "Kneelpray_Loop",
	["下跪祈祷_结束"] = "Kneelpray_End",
	["下蹲_循环"] = "Squat_Loop",
	["下蹲交谈"] = "SquatTalk",
	["下蹲交谈_开始"] = "SquatTalk_Start",
	["下蹲交谈_循环"] = "SquatTalk_Loop",
	["下蹲交谈_结束"] = "SquatTalk_End",
	["下蹲叫卖"] = "Squat_Sell",
	["下蹲安抚小孩"] = "Main_SquatStop",
	["下蹲安抚小孩_开始"] = "Main_SquatStop_Start",
	["下蹲安抚小孩_循环"] = "Main_SquatStop_Loop",
	["下蹲安抚小孩_结束"] = "Main_SquatStop_End",
	["丢"] = "Throw",
	["丢东西_侧面掏出丢"] = "Boomber",
	["个性待机3"] = "Idle3",
	["个性待机4"] = "Idle4",
	["个性待机5"] = "Idle5",
	["主线_倒地挣扎"] = "Main_Suffer",
	["主线_倒地挣扎_开始_男"] = "M_Main_Suffer_Start",
	["主线_倒地挣扎_循环_男"] = "M_Main_Suffer_Loop",
	["主线_倒地挣扎_结束_男"] = "M_Main_Suffer_End",
	["主线_受伤起身"] = "Hurt_Getup",
	["主线_哭泣摇头_小女孩"] = "G_Main_Cry_ShakeHead",
	["主线_小女孩_一章捆绑"] = "Main_Girl_Tied_01",
	["主线_小女孩_一章捆绑挣扎"] = "Main_Girl_Tied_Struggle",
	["主线_小女孩_捆绑"] = "Main_Girl_Tied",
	["主线_惊慌站起"] = "Main_Scared",
	["主线_扶起小女孩"] = "Help_Girlup",
	["主线_扶踉跄玩家"] = "Main_LeonardAppear",
	["主线_拉开伊妮德"] = "Pull",
	["主线_木偶_法阵"] = "Main_Puppet_Tied",
	["主线_查看自己"] = "LookYourself",
	["主线_皮特_警戒"] = "Main_Peter_Alert",
	["主线_站立双手合十_开始_小女孩"] = "G_Main_Pray_Start",
	["主线_站立双手合十_结束_小女孩"] = "G_Main_Pray_End",
	["主线_站立双手吹飞痛_小女孩"] = "G_Main_Pray_Blow",
	["主线_被罗珊拉走"] = "BePulled",
	["主线_跪地哭泣"] = "Cry_Kneel",
	["主线_跪地哭泣_开始_小女孩"] = "G_Cry_Kneel_Start",
	["主线_跪地哭泣_循环_小女孩"] = "G_Cry_Kneel_Loop",
	["主线_跪地哭泣_结束_小女孩"] = "G_Cry_Kneel_End",
	["主线_踉跄被扶"] = "Main_Shot",
	["争吵_男01"] = "M_Quarrel_Provoke01",
	["争吵_男02"] = "M_Quarrel_Provoke02",
	["交给我吧"] = "Me",
	["交给我吧_开始"] = "Me_Start",
	["交给我吧_循环"] = "Me_Loop",
	["交给我吧_结束"] = "Me_End",
	["仰天看天_循环"] = "Main_LookSky_Loop",
	["仰天看天_翻身"] = "Main_LookSky",
	["伊妮德_坐地被拥抱"] = "Main_BugBrother",
	["伊妮德跑"] = "Enid_Run",
	["休闲待机"] = "Idle_Random",
	["传送_吸入"] = "Teleport_Absorbed",
	["伦纳德_坐姿"] = "LeonardSit",
	["伦纳德_坐姿_交谈"] = "LeonardSit_Talk",
	["伦纳德_坐姿_开始"] = "LeonardSit_Start",
	["伦纳德_坐姿_循环"] = "LeonardSit_Loop",
	["伦纳德_坐姿_结束"] = "LeonardSit_End",
	["伦纳德_自我介绍"] = "Main01_Leonard_New",
	["伸懒腰"] = "Stretch",
	["伸手交付"] = "ShowItem",
	["伸手交付_开始"] = "ShowItem_Start",
	["伸手交付_循环"] = "ShowItem_Loop",
	["伸手交付_结束"] = "ShowItem_End",
	["伸手阻拦"] = "Stop",
	["伸手阻拦_开始"] = "Stop_Start",
	["伸手阻拦_循环"] = "Stop_Loop",
	["伸手阻拦_结束"] = "Stop_End",
	["伸臂仰天"] = "RaiseArms",
	["低头搜索_开始"] = "A_Base_Search_Low_St",
	["低头搜索_循环"] = "A_Base_Search_Low_Loop",
	["低头搜索_结束"] = "A_Base_Search_Low_End",
	["体侧运动"] = "Radio2",
	["体侧运动_ 开始"] = "Radio_Start2",
	["体侧运动_循环"] = "Radio_Loop2",
	["体侧运动_结束"] = "Radio_End2",
	["使用道具"] = "UseItem",
	["使用道具_开始"] = "UseItem_Start",
	["使用道具_循环"] = "UseItem_Loop",
	["使用道具_结束"] = "UseItem_End",
	["侧坐_右_正面落座"] = "Sit_Straddling_Right_Forward",
	["侧坐_右_背面落座"] = "Sit_Straddling_Right_Backward",
	["侧坐_左_正面落座"] = "Sit_Straddling_Left_Forward",
	["侧坐_左_背面落座"] = "Sit_Straddling_Left_Backward",
	["侧身躲避左"] = "LeftHide",
	["偷看_扶墙"] = "Peed",
	["偷看_扶墙_开始"] = "Peed_Start",
	["偷看_扶墙_循环"] = "Peed_Loop",
	["偷看_扶墙_结束"] = "Peed_End",
	["光茧结晶_待机"] = "RedStone_Idle",
	["克莱恩_持手杖打响指"] = "Cane_Snap",
	["克莱恩_持手杖站立"] = "Cane_Idle",
	["八音盒_开启"] = "Musicbox01_Start",
	["八音盒_循环"] = "Musicbox01_Loop",
	["八音盒_结束"] = "Musicbox01_End",
	["公鹿死亡"] = "Dead_Deer",
	["再见_孩童"] = "Bye_Children",
	["冲刺停步"] = "Sprint_End",
	["冲刺停步右"] = "Sprint_End_R",
	["冲刺停步左"] = "Sprint_End_L",
	["冲刺循环"] = "Sprint",
	["冲刺结束起步高"] = "Sprint_Jump_End_Restart_High",
	["冲刺跳跃开始右脚步"] = "Sprint_Jump_Start_R",
	["冲刺跳跃开始左脚步"] = "Sprint_Jump_Start_L",
	["冲刺跳跃结束起步低"] = "Sprint_Jump_End_Restart",
	["凝视眼部叠加_向上"] = "Gaze_EyeAdditive_T",
	["凝视眼部叠加_向下"] = "Gaze_EyeAdditive_B",
	["凝视眼部叠加_向前"] = "Gaze_EyeAdditive_F",
	["凝视眼部叠加_向右"] = "Gaze_EyeAdditive_R",
	["凝视眼部叠加_向左"] = "Gaze_EyeAdditive_L",
	["出三拳"] = "A_NPC_Boxing_all",
	["出右拳"] = "A_NPC_Boxing_right",
	["出左拳"] = "A_NPC_Boxing_left",
	["出生动画(蒙太奇)"] = "Born",
	["击地"] = "Beatground",
	["击掌_戴莉"] = "Dalyn_Change_Dun",
	["击掌_邓恩"] = "Dunn_Change_Daly",
	["前倒死亡_开始_女"] = "A_Base_F_Dead",
	["前倒死亡_循环_女"] = "A_Base_F_Dead_Loop",
	["半躺_咳嗽_开始"] = "Recline_Cough_Start",
	["半躺_咳嗽_结束"] = "Recline_Cough_End",
	["半躺_大咳嗽_循环"] = "Recline_Cough_Loop02",
	["半躺_小咳嗽_循环"] = "Recline_Cough_Loop01",
	["半躺_看书"] = "Recline_Read",
	["半躺祈祷"] = "Prayer",
	["单手拿本子"] = "Readnote_Book",
	["单手拿本子_开始"] = "单手拿本子_开始",
	["单手拿本子_循环"] = "单手拿本子_循环",
	["单手拿本子_结束"] = "单手拿本子_结束",
	["卖冰淇淋_女"] = "SellIceCream_F",
	["卖冰淇淋_男"] = "SellIceCream_M",
	["卖报纸"] = "Selling_Newspaper",
	["卖报纸_单次"] = "Selling_Newspaper_Complete",
	["卖报纸_开始"] = "Selling_Newspaper_Start",
	["卖报纸_循环"] = "Selling_Newspaper_Loop",
	["卖报纸_结束"] = "Selling_Newspaper_End",
	["卜杖寻路_通用"] = "Cane_Common",
	["占卜"] = "Divination",
	["占卜摊"] = "Divination_Table",
	["占卜摊_放置"] = "Divination_TableSet",
	["占卜摊_退出"] = "Divination_TableUnset",
	["占卜摊_退出男"] = "占卜摊_退出男",
	["原地复活(蒙太奇)"] = "Revive",
	["原地跳跃"] = "Jump_Stand_Start",
	["原地踏步"] = "Radio1",
	["原地踏步_ 开始"] = "Radio_Start1",
	["原地踏步_ 循环"] = "Radio_Loop1",
	["原地踏步_ 结束"] = "Radio_End1",
	["受伤"] = "Injured",
	["受伤坐地_循环_女"] = "A_F_Sit_Hurt",
	["受伤坐地_循环_男"] = "A_M_Sit_Hurt",
	["受击"] = "Hit",
	["受击_02"] = "Hit_02",
	["叫卖"] = "Peddle",
	["召唤圣光"] = "Sacred",
	["右点位起身"] = "Standup_Right",
	["吃东西"] = "Eat",
	["吃东西_开始"] = "Eat_Start",
	["吃东西_循环"] = "Eat_Loop",
	["吃东西_结束"] = "Eat_End",
	["吃面包"] = "Eat_Bread",
	["向右看"] = "NPC_Lookback_R",
	["向右转身180度"] = "Turn180_R",
	["向右转身90度"] = "Turn90_R",
	["向左看"] = "NPC_Lookback_L",
	["向左转身180度"] = "Turn180_L",
	["向左转身90度"] = "Turn90_L",
	["向母神祈祷"] = "RaiseArms_Peter",
	["向风暴之王致意"] = "A_Base_Hello_Steam",
	["向黑暗致意01"] = "A_Base_Hello_Dark01",
	["向黑暗致意02"] = "A_Base_Hello_Dark02",
	["吟唱_开始到胸前循环"] = "Chant",
	["吟唱_胸前循环到举手循环"] = "chant_02",
	["吧台椅坐下"] = "Sit_Highchair",
	["吧台椅坐下-开始"] = "Sit_Highchair_Start",
	["吧台椅坐下-循环"] = "Sit_Highchair_Loop",
	["吧台椅坐下-结束"] = "Sit_Highchair_End",
	["吼叫"] = "Bark",
	["呕吐_循环"] = "Vomit",
	["咳嗽"] = "A_Base_Cough",
	["哭"] = "NPC_Crying",
	["唱歌"] = "Sing",
	["唱歌_开始"] = "Sing_Start",
	["唱歌_循环"] = "Sing_Loop",
	["唱歌_结束"] = "Sing_End",
	["喂鸽子"] = "A_Base_Pigeon",
	["喜悦扭动"] = "Twril_Happy",
	["喝东西"] = "Drink",
	["喝东西_开始"] = "Drink_Start",
	["喝东西_循环"] = "Drink_Loop",
	["喝东西_结束"] = "Drink_End",
	["喝咖啡"] = "Drink_Coffee",
	["嘘"] = "Hush",
	["坐"] = "Sit",
	["坐2"] = "Sit2",
	["坐_到_趴"] = "Sit_To_Prone",
	["坐_到_躺"] = "Sit_To_Lie",
	["坐下"] = "Sitchair",
	["坐下_循环（新）"] = "Sit_Loop_02",
	["坐下开始"] = "Sitchair_Start",
	["坐下循环"] = "Sitchair_Loop",
	["坐下打盹"] = "NPC_Sit_Nap",
	["坐下结束"] = "Sitchair_End",
	["坐个性动作"] = "SitAction",
	["坐个性动作_02"] = "SitAction_02",
	["坐个性动作_03"] = "SitAction_03",
	["坐地"] = "Sit01",
	["坐地_坐下"] = "Sit01_Start",
	["坐地_坐下_腿右"] = "SitRight_Start",
	["坐地_循环"] = "Sit01_Loop",
	["坐地_循环_腿右"] = "SitRight_Loop",
	["坐地_起身"] = "Sit01_End",
	["坐地_起身_腿右"] = "SitRight_End",
	["坐地翘右腿"] = "SitRight",
	["坐姿_交谈"] = "SitTalk",
	["坐姿_出牌"] = "Sit_PlayCard",
	["坐姿_出牌_女"] = "Sit_PlayCard_F",
	["坐姿_出牌_男"] = "Sit_PlayCard_M",
	["坐姿_大笑"] = "SitHappy",
	["坐姿_打牌优势"] = "Sit_PlayCard_Win",
	["坐姿_打牌优势_女"] = "Sit_PlayCard_Win_F",
	["坐姿_打牌优势_男"] = "Sit_PlayCard_Win_M",
	["坐姿_打牌劣势"] = "Sit_PlayCard_Fail",
	["坐姿_打牌劣势_女"] = "Sit_PlayCard_Fail_F",
	["坐姿_打牌劣势_男"] = "Sit_PlayCard_Fail_M",
	["坐姿_拿牌"] = "Sit_TakeCard",
	["坐姿_拿牌_女"] = "Sit_TakeCard_F",
	["坐姿_拿牌_男"] = "Sit_TakeCard_M",
	["坐姿_看牌"] = "Sit_WatchCard",
	["坐姿_看牌_女"] = "Sit_WatchCard_F",
	["坐姿_看牌_男"] = "Sit_WatchCard_M",
	["坐姿_睡醒站起"] = "Main_SitWake",
	["坐姿摇头"] = "SitNo",
	["坐姿点头"] = "SitYes",
	["坐床_仰望"] = "SitBed_LookUp",
	["坐床_仰望到坐床边"] = "SitBed_LookUpToBedside",
	["坐床_坐床边"] = "SitBed_Bedside",
	["坐床_坐床边到仰望"] = "SitBed_BedsideToLookUp",
	["坐床_祈祷"] = "SitBed_Pray",
	["坐椅子_02"] = "SitOnChair_02",
	["坐着书写_开始"] = "Sit_Write_Start",
	["坐着书写_循环1"] = "Sit_Write_Loop1",
	["坐着书写_循环2"] = "Sit_Write_Loop2",
	["坐着书写_结束"] = "Sit_Write_End",
	["坐着喝酒"] = "A_DrinkBeer",
	["坐着喝酒_待机"] = "A_DrinkBeer_Idle",
	["坐着头晕"] = "Sitdizzy",
	["坐着头晕_循环"] = "Sitdizzy_Loop",
	["坐着死"] = "Sit_Die",
	["坐着看报纸"] = "Sit_Read_Newspaper_Single",
	["坐着睡觉"] = "Sit_Sleep",
	["坐着睡觉_醒_循环"] = "SitSleep_Sit",
	["坐着睡觉_醒来"] = "Sitsleep_Wake",
	["坐自行车挥手"] = "Bike_Bye",
	["坚定拒绝"] = "FirmRefusal",
	["垂死挣扎_开始"] = "Main_BeforeDie_Start",
	["垂死挣扎_循环"] = "Main_BeforeDie_Loop",
	["埃姆林休闲待机"] = "Emlyndoll7_Show",
	["埃姆林普通待机"] = "Emlyndoll7_Idle",
	["复生"] = "Rebirth_Start",
	["复生_待机"] = "Rebirth_Idle",
	["大梅丽莎_出场"] = "Merrisa002_Show",
	["大梅丽莎_站姿"] = "Merrisa002_Stand",
	["大蜘蛛_发抖衔接受伤"] = "Araneid_002_Task3",
	["大蜘蛛_受伤循环"] = "Araneid_002_Task4",
	["大蜘蛛_攻击"] = "Araneid_002_Attack",
	["大蜘蛛_死亡"] = "Araneid_002_Dead",
	["大蜘蛛_死亡循环"] = "Araneid_002_Deadloop",
	["大蜘蛛_瑟瑟发抖"] = "Araneid_002_Task2",
	["大蜘蛛_被捕获"] = "Araneid_002_Task1",
	["大蜘蛛_被捕获_开始"] = "Araneid_002_Task1_Start",
	["大蜘蛛_被捕获_循环"] = "Araneid_002_Task1_Loop",
	["大蜘蛛_被捕获_结束"] = "Araneid_002_Task1_End",
	["太阳攻击"] = "A_Base_SunAttack",
	["头晕倒下"] = "Chairdown",
	["头晕倒下_循环"] = "Chairdown_Loop",
	["奥黛丽_伸手"] = "ShowHand",
	["奥黛丽_坐姿交谈_手放胸前"] = "SitTalk_OnChest",
	["奥黛丽_对话"] = "Audrey_Talk",
	["奥黛丽_自我介绍"] = "Main01_Audrey_New",
	["奥黛丽_行走"] = "Audrey_Walk",
	["女女_公主抱_受邀方"] = "FF_Princess_P",
	["女女_公主抱_开始_受邀方"] = "FF_Princess_P_Start",
	["女女_公主抱_开始_邀请方"] = "FF_Princess_I_Start",
	["女女_公主抱_循环_受邀方"] = "FF_Princess_P_Loop",
	["女女_公主抱_循环_邀请方"] = "FF_Princess_I_Loop",
	["女女_公主抱_结束_受邀方"] = "FF_Princess_P_End",
	["女女_公主抱_结束_邀请方"] = "FF_Princess_I_End",
	["女女_公主抱_走路_受邀方"] = "FF_Princess_P_Walk",
	["女女_公主抱_走路_邀请方"] = "FF_Princess_I_Walk",
	["女女_公主抱_邀请方"] = "FF_Princess_I",
	["女女_拥抱_受邀方"] = "FF_Hug_P",
	["女女_拥抱_开始_受邀方"] = "FF_Hug_P_Start",
	["女女_拥抱_开始_邀请方"] = "FF_Hug_I_Start",
	["女女_拥抱_循环_受邀方"] = "FF_Hug_P_Loop",
	["女女_拥抱_循环_邀请方"] = "FF_Hug_I_Loop",
	["女女_拥抱_结束_受邀方"] = "FF_Hug_P_End",
	["女女_拥抱_结束_邀请方"] = "FF_Hug_I_End",
	["女女_拥抱_邀请方"] = "FF_Hug_I",
	["女小丑帽子_失败"] = "女小丑帽子_失败",
	["女小丑帽子_开始"] = "女小丑帽子_开始",
	["女小丑帽子_循环"] = "女小丑帽子_循环",
	["女小丑帽子_成功"] = "女小丑帽子_成功",
	["女小丑独轮_失败"] = "女小丑独轮_失败",
	["女小丑独轮_开始"] = "女小丑独轮_开始",
	["女小丑独轮_循环"] = "女小丑独轮_循环",
	["女小丑独轮_成功"] = "女小丑独轮_成功",
	["女小丑耍球_失败"] = "女小丑耍球_失败",
	["女小丑耍球_开始"] = "女小丑耍球_开始",
	["女小丑耍球_循环"] = "女小丑耍球_循环",
	["女小丑耍球_成功"] = "女小丑耍球_成功",
	["女性浇花_开始"] = "女性浇花_开始",
	["女性浇花_循环"] = "女性浇花_循环",
	["女性浇花_结束"] = "女性浇花_结束",
	["女性看报纸_坐姿_开始"] = "女性看报纸_坐姿_开始",
	["女性看报纸_坐姿_循环"] = "女性看报纸_坐姿_循环",
	["女性看报纸_坐姿_结束"] = "女性看报纸_坐姿_结束",
	["女男_公主抱_受邀方"] = "FM_Princess_I",
	["女男_公主抱_开始_受邀方"] = "FM_Princess_P_Start",
	["女男_公主抱_开始_邀请方"] = "FM_Princess_I_Start",
	["女男_公主抱_循环_受邀方"] = "FM_Princess_P_Loop",
	["女男_公主抱_循环_邀请方"] = "FM_Princess_I_Loop",
	["女男_公主抱_结束_受邀方"] = "FM_Princess_P_End",
	["女男_公主抱_结束_邀请方"] = "FM_Princess_I_End",
	["女男_公主抱_走路_受邀方"] = "FM_Princess_P_Walk",
	["女男_公主抱_走路_邀请方"] = "FM_Princess_I_Walk",
	["好奇_蹲下_孩童"] = "Curiouschild_Squat",
	["好奇_蹲下_孩童_开始"] = "Curiouschild_Squat_Start",
	["好奇_蹲下_孩童_循环"] = "Curiouschild_Squat_Loop",
	["好奇_蹲下_孩童_结束"] = "Curiouschild_Squat_End",
	["好奇地下蹲看"] = "Squat_Curiouschild",
	["威胁_女"] = "A_F_Threaten",
	["威胁_男"] = "A_M_Threaten",
	["学生克莱恩_出场"] = "Klein002_Show",
	["学生克莱恩_站姿"] = "Klein002_Stand",
	["害羞"] = "Shy",
	["对峙"] = "A_Base_HandUp",
	["对话_Idle"] = "Talk",
	["对话_交互"] = "Greet",
	["对话_单手伸出_短"] = "Short_Talk2",
	["对话_单手前放_短"] = "Short_Talk4",
	["对话_单手叉腰"] = "Akimbo",
	["对话_单手叉腰_开始"] = "Akimbo_Start",
	["对话_单手叉腰_循环"] = "Akimbo_Loop",
	["对话_单手叉腰_结束"] = "Akimbo_End",
	["对话_双手叉腰_女"] = "A_F_Talk_Akimbo",
	["对话_双手叉腰_男"] = "A_M_Talk_Akimbo",
	["对话_双手展开_短"] = "Short_Talk3",
	["对话_困惑"] = "Puzzled",
	["对话_愤怒"] = "Anger",
	["对话_愤怒_开始"] = "Anger_Start",
	["对话_愤怒_循环"] = "Anger_Loop",
	["对话_愤怒_结束"] = "Anger_End",
	["对话_抱胸"] = "CrossArm",
	["对话_抱胸_开始"] = "CrossArm_Start",
	["对话_抱胸_循环"] = "CrossArm_Loop",
	["对话_抱胸_结束"] = "CrossArm_End",
	["对话_挠头"] = "ScratchHead",
	["对话_摊手_短"] = "Short_Talk1",
	["对话_激动"] = "Excite",
	["对话_激动_开始"] = "Excite_Start",
	["对话_激动_循环"] = "Excite_Loop",
	["对话_激动_结束"] = "Excite_End",
	["对话_翻转胳膊"] = "Turnarm",
	["对话_询问"] = "Ask",
	["对话_风衣单手叉腰"] = "Akimbo2",
	["对话_风衣单手叉腰_开始"] = "Akimbo_Start02",
	["对话_风衣单手叉腰_循环"] = "Akimbo_Loop02",
	["对话_风衣单手叉腰_结束"] = "Akimbo_End02",
	["对话_高兴"] = "Happy",
	["对话_高兴_开始"] = "Happy_Start",
	["对话_高兴_循环"] = "Happy_Loop",
	["对话_高兴_结束"] = "Happy_End",
	["小丑_帽子_失败"] = "Joker_Hat_Fail",
	["小丑_帽子_开始"] = "Joker_Hat_Start",
	["小丑_帽子_循环"] = "Joker_Hat_Loop",
	["小丑_帽子_成功"] = "Joker_Hat_Success",
	["小丑_抛球_失败"] = "Joker_Juggle_Fail",
	["小丑_抛球_开始"] = "Joker_Juggle_Start",
	["小丑_抛球_循环"] = "Joker_Juggle_Loop",
	["小丑_抛球_成功"] = "Joker_Juggle_Success",
	["小丑_独轮车_失败"] = "Joker_Rider_Fail",
	["小丑_独轮车_开始"] = "Joker_Rider_Start",
	["小丑_独轮车_循环"] = "Joker_Rider_Loop",
	["小丑_独轮车_成功"] = "Joker_Rider_Success",
	["小丑独轮_loop"] = "小丑独轮_loop",
	["小丑独轮_失败"] = "小丑独轮_失败",
	["小丑独轮_开始"] = "小丑独轮_开始",
	["小丑独轮_成功"] = "小丑独轮_成功",
	["小丑耍球"] = "Juggling",
	["小丑耍球_失败"] = "Juggling_Fail",
	["小丑耍球_开始"] = "Juggling_Start",
	["小丑耍球_循环"] = "Juggling_Loop",
	["小丑耍球_结束"] = "Juggling_End",
	["小乌贼_游动"] = "Sepia_Swim",
	["小孩_边走边看"] = "Child_LookAroundWalk",
	["小孩翻"] = "NPC_Garbage",
	["小孩行走"] = "Walk_Bounce",
	["小孩被安抚"] = "Main_StandHug",
	["小孩被安抚_开始"] = "Main_StandHug_Start",
	["小孩被安抚_循环"] = "Main_StandHug_Loop",
	["小孩被安抚_结束"] = "Main_StandHug_End",
	["小孩跑"] = "NPC_Run_Children",
	["小幅度激动"] = "Excited_Short",
	["小心触摸"] = "Careful_Touch",
	["小梅丽莎_出场"] = "Merrisa001_Show",
	["小梅丽莎_站姿"] = "Merrisa001_Stand",
	["小狗_攻击"] = "A_Dog_Border_Attack",
	["小狗_晃头待机"] = "A_Dog_Border_Idle2",
	["小狗_被击倒"] = "A_Dog_Border_Death_L",
	["小猫_伸懒腰待机"] = "Cat_Idle_4",
	["小猫_俯身前进"] = "Cat_Crouch_F_IP",
	["小猫_倒退走路"] = "A_Cat_Walk_B_IP",
	["小猫_前进走路"] = "A_Cat_walk_F_IP",
	["小猫_吃东西循环"] = "Cat_Eating",
	["小猫_向右前方走"] = "A_Cat_Walk_R_IP",
	["小猫_向左前方走"] = "A_Cat_Walk_L_IP",
	["小猫_喝水循环"] = "Cat_Drinking",
	["小猫_左爪攻击"] = "Cat_Attack_L",
	["小猫_快速跑"] = "A_Cat_RunFast_F_IP",
	["小猫_挠痒痒"] = "A_Cat_Scratching",
	["小猫_摇尾待机"] = "Cat_Idle_1",
	["小猫_睡觉"] = "Cat_Lie_Belly_Sleep",
	["小猫_睡觉开始"] = "Cat_Lie_Belly_Sleep_Start",
	["小猫_睡觉结束"] = "Cat_Lie_Belly_Sleep_End",
	["小猫_舔脚"] = "A_Cat_Lick",
	["小猫_跑"] = "A_Cat_Run_F_IP",
	["小猫_进食开始"] = "Cat_EatDrink_Start",
	["小猫_进食结束"] = "Cat_EatDrink_End",
	["小章鱼_游动"] = "BabyOctopus_Swim",
	["小蜘蛛_攻击"] = "Araneid_001_Attack",
	["小蜘蛛_死亡"] = "Araneid_001_Dead",
	["小鹿_休闲吃草_待机"] = "Deer_Eat",
	["尤朵拉_坐地哼歌"] = "Eudora_SitSing",
	["尤朵拉_怪物出生"] = "Eudora_Born",
	["尤朵拉_跪地"] = "Eudora_Kneel",
	["展示肌肉_男"] = "A_NPC_M_ShowBody",
	["左点位起身"] = "Standup_Left",
	["布置灵性之墙"] = "BuildSpiritualWall",
	["帽子_失败"] = "帽子_失败",
	["帽子_开始"] = "帽子_开始",
	["帽子_循环"] = "帽子_循环",
	["帽子_成功"] = "帽子_成功",
	["平躺_循环_女"] = "A_F_LieDown_Loop",
	["平躺_循环_无呼吸"] = "LieDown_Nobreath",
	["平躺_循环_男"] = "A_M_LieDown_Loop",
	["平躺坐起"] = "LieDown_GetUp",
	["平躺坐起_开始_女"] = "A_F_LieDown_GetUp_Start",
	["平躺坐起_开始_男"] = "A_M_LieDown_GetUp_Start",
	["平躺坐起_循环_女"] = "A_F_LieDown_GetUp_Loop",
	["平躺坐起_循环_男"] = "A_M_LieDown_GetUp_Loop",
	["平躺坐起_结束_女"] = "A_F_LieDown_GetUp_End",
	["平躺坐起_结束_男"] = "A_M_LieDown_GetUp_End",
	["幽魂_技能2"] = "Skill02",
	["幽魂_技能3"] = "Skill03",
	["序列2梅林_出场"] = "Merlin001_Show",
	["序列2梅林_站姿"] = "Merlin001_Stand",
	["序列2贝尔纳黛_出场"] = "Bernade2_Show",
	["序列2贝尔纳黛_站姿"] = "Bernade2_Idle",
	["序列3奥黛丽_站姿_开始"] = "Audrey3_Idle_Start",
	["序列3奥黛丽_站姿_循环"] = "Audrey3_Idle_Loop",
	["序列3奥黛丽_站姿_结束"] = "Audrey3_Idle_End",
	["序列4休_出场"] = "Xio4_Show",
	["序列4休_站姿"] = "Xio4_Idle",
	["序列4伦纳德_出场"] = "Leonard002_Show",
	["序列4伦纳德_站姿"] = "Leonard002_Stand",
	["序列4佛尔思_出场"] = "Fors4_Show",
	["序列4佛尔思_站姿_开始"] = "Fors4_Idle_Start",
	["序列4佛尔思_站姿_循环"] = "Fors4_Idle_Loop",
	["序列4佛尔思_站姿_结束"] = "Fors4_Idle_End",
	["序列4戴里克_出场"] = "Derrick4_Show",
	["序列4戴里克_站姿"] = "Derrick4_Idle",
	["序列5嘉德丽雅_出场"] = "Cattleya5_Show",
	["序列5嘉德丽雅_站姿"] = "Cattleya5_Idle",
	["序列5莎伦_出场"] = "Sharon001_Show",
	["序列5莎伦_站姿"] = "Sharon001_Stand",
	["序列7dengen_站姿"] = "Dunn7_Stand",
	["序列7埃姆林_出场_开始"] = "Emlyn7_Show_Start",
	["序列7埃姆林_出场_循环"] = "Emlyn7_Show_Loop",
	["序列7埃姆林_出场_结束"] = "Emlyn7_Show_End",
	["序列7埃姆林_站姿_开始"] = "Emlyn7_Idle_Start",
	["序列7埃姆林_站姿_循环"] = "Emlyn7_Idle_Loop",
	["序列7埃姆林_站姿_结束"] = "Emlyn7_Idle_End",
	["序列7弗莱_出场"] = "Fran001_Show",
	["序列7弗莱_站姿"] = "Fran001_Stand",
	["序列7戴莉_出场"] = "Dally001_Show",
	["序列7戴莉_站姿"] = "Dally001_Stand",
	["序列7特莉丝_出场"] = "Triss001_Show",
	["序列7特莉丝_站姿"] = "Triss001_Stand",
	["序列7邓恩_出场_开始"] = "Dunn1_Show_Start",
	["序列7邓恩_出场_循环"] = "Dunn1_Show_Loop",
	["序列7邓恩_出场_结束"] = "Dunn1_Show_End",
	["序列7邓恩_站姿_开始"] = "Dunn1_Idle_Start",
	["序列7邓恩_站姿_循环"] = "Dunn1_Idle_Loop",
	["序列7邓恩_站姿_结束"] = "Dunn1_Idle_End",
	["序列7阿尔杰_出场_开始"] = "Alger7_Show_Start",
	["序列7阿尔杰_出场_循环"] = "Alger7_Show_Loop",
	["序列7阿尔杰_出场_结束"] = "Alger7_Show_End",
	["序列7阿尔杰_站姿_开始"] = "Alger7_Idle_Start",
	["序列7阿尔杰_站姿_循环"] = "Alger7_Idle_Loop",
	["序列7阿尔杰_站姿_结束"] = "Alger7_Idle_End",
	["序列8伦纳德_出场"] = "Leonard001_Show",
	["序列8伦纳德_站姿"] = "Leonard001_Stand",
	["序列9休_出场"] = "Xio9_Show",
	["序列9休_站姿"] = "Xio9_Idle",
	["序列9佛尔思_特殊待机"] = "Fors9_Show",
	["序列9佛尔思_站姿_开始"] = "Fors9_Idle_Start",
	["序列9佛尔思_站姿_循环"] = "Fors9_Idle_Loop",
	["序列9佛尔思_站姿_结束"] = "Fors9_Idle_End",
	["序列9克莱恩_出场"] = "Klein001_Show",
	["序列9克莱恩_站姿"] = "Klein001_Stand",
	["序列9奥黛丽_出场"] = "Audrey9_Show",
	["序列9戴里克_出场_开始"] = "Derrick9_Show_Start",
	["序列9戴里克_出场_循环"] = "Derrick9_Show_Loop",
	["序列9戴里克_出场_结束"] = "Derrick9_Show_End",
	["序列9戴里克_站姿_开始"] = "Derrick9_Idle_Start",
	["序列9戴里克_站姿_循环"] = "Derrick9_Idle_Loop",
	["序列9戴里克_站姿_结束"] = "Derrick9_Idle_End",
	["序列晋升-准备拿起魔药"] = "Pick_Potion_Start",
	["序列晋升-后撤捂脸"] = "Step_Back",
	["序列晋升-喝魔药-开始"] = "Drink_Potion_Start",
	["序列晋升-喝魔药-开始接循环"] = "Drink_Potion",
	["序列晋升-喝魔药-循环"] = "Drink_Potion_Loop",
	["序列晋升-喝魔药-结束"] = "Drink_Potion_End",
	["序列晋升-头疼"] = "A_Base_Headache",
	["序列晋升-拿勺子-开始接循环"] = "Hold_Spoon",
	["序列晋升-拿着勺子-循环"] = "Hold_Spoon_Loop",
	["序列晋升-拿起勺子"] = "Hold_Spoon_Start",
	["序列晋升-搅拌"] = "Stir_With_Spoon",
	["序列晋升-放下勺子"] = "Hold_Spoon_End",
	["序列晋升-放入材料-开始"] = "Hold_Bottle_Start",
	["序列晋升-放入材料-开始接循环"] = "Hold_Bottle",
	["序列晋升-放入材料-循环"] = "Hold_Bottle_Loop",
	["序列晋升-放入材料-结束"] = "Hold_Bottle_End",
	["序列晋升-放入粉末-开始"] = "Pour_Powder_Start",
	["序列晋升-放入粉末-循环"] = "Pour_Powder_Loop",
	["序列晋升-放入粉末-结束"] = "Pour_Powder_End",
	["序列晋升-阅读配方"] = "Read_Recipe",
	["开心"] = "F_Happy",
	["开门"] = "A_M_Opendoor",
	["弯腰查探"] = "Inspect",
	["弯腰查探_开始_女"] = "A_F_Inspect_Start",
	["弯腰查探_开始_男"] = "A_M_Inspect_Start",
	["弯腰查探_循环_女"] = "A_F_Inspect_Loop",
	["弯腰查探_循环_男"] = "A_M_Inspect_Loop",
	["弯腰查探_结束_女"] = "A_F_Inspect_End",
	["弯腰查探_结束_男"] = "A_M_Inspect_End",
	["弹七弦琴_开始"] = "Perform_Start",
	["弹七弦琴_待机"] = "Perform_Idle",
	["弹七弦琴_演奏循环1"] = "Perform_Loop01",
	["弹七弦琴_演奏循环2"] = "Perform_Loop02",
	["弹七弦琴_结束"] = "Perform_End",
	["弹钢琴旧"] = "Play_Piano",
	["待机"] = "Idle1",
	["待机_孩童"] = "Idle_Children",
	["待机_弯曲手臂"] = "Idle_BendArm",
	["待机_捏脸"] = "Idle_Lobby",
	["待机_潜行"] = "Idle_Stalk",
	["待机_贵族"] = "Idle_Rich",
	["待机个性动作"] = "IdleAction",
	["待机个性动作_02"] = "IdleAction_02",
	["待机个性动作_03"] = "IdleAction_03",
	["待机个性动作_04"] = "IdleAction_04",
	["待机个性动作_05"] = "IdleAction_05",
	["待机个性动作_06"] = "IdleAction_06",
	["待机个性动作_07"] = "IdleAction_07",
	["待机个性动作_抚摸"] = "IdleAction_Caress",
	["徘徊向前走"] = "Walk_Front",
	["徘徊结束"] = "Wander_End",
	["微侧坐下_开始_女"] = "F_Sit02_Start",
	["微侧坐下_循环_女"] = "F_Sit02_Loop",
	["微动作_偷看"] = "Peep",
	["微动作_偷看_单次"] = "Big_Peep",
	["微动作_偷看_开始"] = "Peep_Start",
	["微动作_偷看_循环"] = "Peep_Loop",
	["微动作_偷看_结束"] = "Peep_End",
	["微动作_叹气"] = "Big_Sigh",
	["微动作_呸"] = "Big_Boo",
	["微动作_咳嗽"] = "Big_Cough",
	["微动作_嗅闻"] = "Sniff",
	["微动作_嘘"] = "Big_Quiet",
	["微动作_困惑歪头"] = "Big_Puzzled",
	["微动作_困惑歪头无表情"] = "Big_Puzzled_NoFace",
	["微动作_坐_无聊"] = "Big_Sit",
	["微动作_头疼"] = "Headache_002",
	["微动作_头疼_单次"] = "Big_Headache_002",
	["微动作_头疼_开始"] = "Headache_002_Start",
	["微动作_头疼_循环"] = "Headache_002_Loop",
	["微动作_头疼_结束"] = "Headache_002_End",
	["微动作_害怕"] = "Fear",
	["微动作_害怕_单次"] = "Big_Fear",
	["微动作_害怕_开始"] = "Fear_Start",
	["微动作_害怕_循环"] = "Fear_Loop",
	["微动作_害怕_结束"] = "Fear_End",
	["微动作_少女害羞"] = "Big_Melissa_Shy",
	["微动作_尴尬"] = "Big_Awkward",
	["微动作_心虚"] = "Guilty",
	["微动作_心虚_单次"] = "Big_Guilty",
	["微动作_心虚_开始"] = "Guilty_Start",
	["微动作_心虚_循环"] = "Guilty_Loop",
	["微动作_心虚_结束"] = "Guilty_End",
	["微动作_思考"] = "Big_Think",
	["微动作_思考_单次"] = "Big_Think_Single",
	["微动作_思考_开始"] = "Big_Think_Start",
	["微动作_思考_循环"] = "Big_Think_Loop",
	["微动作_思考_结束"] = "Big_Think_End",
	["微动作_愤怒"] = "Big_Anger",
	["微动作_我吗"] = "Big_Me",
	["微动作_打哈欠"] = "Big_Yawn",
	["微动作_抠手指_胸前"] = "Big_Finger_001",
	["微动作_抠手指_胸前_循环"] = "Finger_001_Loop",
	["微动作_抠手指_腹部"] = "Finger_002",
	["微动作_抠手指_腹部_单次"] = "Big_Finger_002",
	["微动作_抠手指_腹部_开始"] = "Finger_002_Start",
	["微动作_抠手指_腹部_循环"] = "Finger_002_Loop",
	["微动作_抠手指_腹部_结束"] = "Finger_002_End",
	["微动作_拍肚子"] = "Big_Belly_001",
	["微动作_捂嘴笑"] = "Big_Smile",
	["微动作_推眼镜"] = "Big_Glasses",
	["微动作_揉肚子"] = "Big_Belly_002",
	["微动作_揉额角"] = "Big_Headache_001",
	["微动作_摸发际线"] = "Big_Touchhair",
	["微动作_深呼吸"] = "Big_Breath",
	["微动作_点点脑袋"] = "Big_Head",
	["微动作_站_无聊"] = "Big_Stand",
	["微动作_站立"] = "Stand",
	["微动作_站立_开始"] = "Stand_Start",
	["微动作_站立_循环"] = "Stand_Loop",
	["微动作_站立_结束"] = "Stand_End",
	["微动作_纠结害羞"] = "Big_Shy",
	["微动作_耸肩"] = "Big_Shrug",
	["微动作_被控"] = "Hypnotized",
	["微动作_警惕"] = "Big_Guard",
	["微动作_轻蔑"] = "Big_Contempt",
	["微动作_飞吻"] = "Big_Kiss",
	["思考"] = "Think",
	["思考_坐椅子"] = "Think_SitOnChair",
	["思考_坐椅子_02"] = "Think_SitOnChair_02",
	["思考_开始"] = "Think_Start",
	["思考_循环"] = "Think_Loop",
	["思考_结束"] = "Think_End",
	["思考挠头"] = "BigThink",
	["急切"] = "Hurry",
	["悲伤"] = "Sad",
	["悲伤_开始"] = "Sad_Start",
	["悲伤_循环"] = "Sad_Loop",
	["悲伤_结束"] = "Sad_End",
	["惊吓"] = "Shock",
	["惊吓_发起"] = "Scared_Yell",
	["惊吓_开始"] = "Shock_Start",
	["惊吓_循环"] = "Shock_Loop",
	["惊吓_结束"] = "Shock_End",
	["惊恐转身"] = "TurnPanic",
	["惊讶_轻度"] = "Surprised",
	["惊醒_揉头"] = "Awaken_RubHead",
	["我"] = "A_Base_Me",
	["战斗到待机"] = "FightToIdle",
	["战斗到待机-武器"] = "FightToIdle_Weapon",
	["战斗待机"] = "Idle_Combat",
	["战斗待机-占卜家-卡牌"] = "FightIdle_Fool_Cane",
	["战斗待机-学徒-权杖"] = "FightIdle_Apprentice_Staff",
	["战斗待机-战士-双刀"] = "FightIdle_Warrior_DualSowrd",
	["战斗待机-战士-巨剑"] = "FightIdle_Warrior_GiantSowrd",
	["战斗待机-战士-长枪"] = "FightIdle_Warrior_Lance",
	["战斗待机-观众-金"] = "FightIdle_Visionary_MagicBallG",
	["战斗待机-观众-银"] = "FightIdle_Visionary_MagicBallS",
	["战斗待机回基础-占卜家-卡牌"] = "FightToIdle_Fool_Cane",
	["战斗待机回基础-学徒-权杖"] = "FightToIdle_Apprentice_Staff",
	["战斗待机回基础-战士-双刀"] = "FightToIdle_Warrior_DualSowrd",
	["战斗待机回基础-战士-巨剑"] = "FightToIdle_Warrior_GiantSowrd",
	["战斗待机回基础-战士-长枪"] = "FightToIdle_Warrior_Lance",
	["战斗待机回基础-观众-金"] = "FightToIdle_Visionary_MagicBallG",
	["战斗待机回基础-观众-银"] = "FightToIdle_Visionary_MagicBallS",
	["戴莉信使_出现"] = "Appear",
	["戴莉信使_消失"] = "Disappear02",
	["戴莉信使_特殊待机"] = "Leisure",
	["戴莉信使_闭嘴待机"] = "Disappear_Idle",
	["扇耳光_男01"] = "M_Quarrel_Slap01",
	["扇风"] = "Handfan",
	["打伞-开始"] = "打伞-开始",
	["打伞-循环"] = "打伞-循环",
	["打伞-结束"] = "打伞-结束",
	["打台球"] = "PlayBilliards",
	["打响指"] = "Snap",
	["打扫开始"] = "A_Base_Clean_Start",
	["打招呼"] = "A_Base_Hello",
	["打招呼_快速"] = "Hello_Quickly",
	["打招呼_浮夸"] = "Hello_A",
	["打招呼_行礼"] = "Salute",
	["打招呼_行礼_开始"] = "Salute_Start",
	["打招呼_行礼_循环"] = "Salute_Loop",
	["打招呼_行礼_结束"] = "Salute_End",
	["打招呼_通用"] = "Hello_B",
	["扫地"] = "CleanFloor",
	["扫地_开始"] = "CleanFloor_Start",
	["扫地_循环"] = "CleanFloor_Loop",
	["扫地_结束"] = "CleanFloor_End",
	["扮演_仲裁人发起审讯"] = "Arbitrator_QSSucceed",
	["扮演_进入"] = "RolePlay_Start",
	["扮演_退出"] = "RolePlay_End",
	["扮演界面入场Idle"] = "RolePlayUI_In",
	["扮演界面面具放下Idle"] = "RolePlayUI_Out",
	["扶人"] = "Support",
	["扶栏杆向下看"] = "LookDown",
	["扶栏杆向下看_开始"] = "LookDown_Start",
	["扶栏杆向下看_循环"] = "LookDown_Loop",
	["扶栏杆向下看_结束"] = "LookDown_End",
	["扶额沮丧"] = "Depress",
	["扶额沮丧_开始"] = "Depress_Start",
	["扶额沮丧_循环"] = "Depress_Loop",
	["扶额沮丧_结束"] = "Depress_End",
	["投技_开始"] = "Grab_Start",
	["投技_持续"] = "Grab_Loop",
	["投技_结束"] = "Grab_End",
	["投技_起身"] = "Grab_StandUp",
	["折返左脚步"] = "Move_Turn_L",
	["报幕员_合手_循环"] = "A_Announcer_Idle01",
	["报幕员_开手_循环"] = "A_Announcer_Idle02",
	["报幕员_张开"] = "A_Announcer_Emerge",
	["报幕员_破碎"] = "A_Announcer_Break",
	["抬手"] = "Handup",
	["抱头痛哭_结束"] = "HoldHead_End",
	["抱头痛苦"] = "HoldHead_Pain",
	["抱头痛苦_单次"] = "HoldHead_Pain_Single",
	["抱头痛苦_开始"] = "HoldHead_Pain_Start",
	["抱头痛苦_循环"] = "HoldHead_Loop",
	["抱头痛苦_新"] = "ClapHead",
	["抱头痛苦_结束"] = "HoldHead_Pain_End",
	["抱臂思考"] = "Think_ArmsCrossed",
	["抱臂思考_开始"] = "A_Base_Search_High_St",
	["抱臂思考_循环"] = "A_Base_Search_High_Loop",
	["抱臂思考_结束"] = "A_Base_Search_High_End",
	["拉小提琴"] = "Play_Violin",
	["拍头痛苦"] = "ClapHead_Pain",
	["拍手大笑"] = "Fight_Laugh",
	["拎行李箱站立"] = "Suicase_Stand",
	["拎行李箱行走"] = "Suicase_Walk",
	["拒绝"] = "Reject",
	["拳击KO女01"] = "A_F_Boxing_KO_01",
	["拳击KO女02"] = "A_F_Boxing_KO_02",
	["拳击KO女03"] = "A_F_Boxing_KO_03",
	["拳击KO女04"] = "A_F_Boxing_KO_04",
	["拳击KO男01"] = "A_M_Boxing_KO_01",
	["拳击KO男02"] = "A_M_Boxing_KO_02",
	["拳击KO男03"] = "A_M_Boxing_KO_03",
	["拳击KO男04"] = "A_M_Boxing_KO_04",
	["拳击对峙一女1_1"] = "A_F01_Boxing_Fight_01",
	["拳击对峙一女2_1"] = "A_F01_Boxing_Fight_02",
	["拳击对峙一女3_1"] = "A_F01_Boxing_Fight_03",
	["拳击对峙一男1_1"] = "A_M01_Boxing_Fight_01.uasset",
	["拳击对峙一男1_2"] = "A_M01_Boxing_Fight_02.uasset",
	["拳击对峙一男1_3"] = "A_M01_Boxing_Fight_03.uasset",
	["拳击对峙一男2_1"] = "A_M02_Boxing_Fight_01.uasset",
	["拳击对峙一男2_2"] = "A_M02_Boxing_Fight_02.uasset",
	["拳击对峙一男2_3"] = "A_M02_Boxing_Fight_03.uasset",
	["拳击待机"] = "A_NPC_Boxing_idle",
	["拳击闪避"] = "A_NPC_Boxing_dodge",
	["拳击防守"] = "A_NPC_Boxing_defense",
	["拿东西_查看"] = "View_Letter",
	["拿扫帚清扫"] = "Broom_Clean",
	["拿本子捂头"] = "Readnote_Ache",
	["拿本子捂头_开始"] = "拿本子捂头_开始",
	["拿本子捂头_循环"] = "拿本子捂头_循环",
	["拿本子捂头_结束"] = "拿本子捂头_结束",
	["拿枪"] = "Hold_Gun",
	["拿法杖起身"] = "Ellen_WandGetUp",
	["拿烟斗_坐姿"] = "Sit_Pipe",
	["拿烟斗_坐姿_单次"] = "Sit_Pipe_Single",
	["拿烟斗_坐姿_开始"] = "Sit_Pipe_Start",
	["拿烟斗_坐姿_循环"] = "Sit_Pipe_Loop",
	["拿烟斗_坐姿_结束"] = "Sit_Pipe_End",
	["拿烟斗_站姿"] = "Stand_Pipe",
	["拿烟斗_站姿_单次"] = "Stand_Pipe_Single",
	["拿烟斗_站姿_开始"] = "Stand_Pipe_Start",
	["拿烟斗_站姿_循环"] = "Stand_Pipe_Loop",
	["拿烟斗_站姿_结束"] = "Stand_Pipe_End",
	["指向自己"] = "Point_Self",
	["指路"] = "Directions",
	["指路_开始"] = "Directions_Start",
	["指路_循环"] = "Directions_Loop",
	["指路_结束"] = "Directions_End",
	["挠"] = "Scratch",
	["挥手下令_女"] = "A_F_Order",
	["挥手下令_男"] = "A_M_Order",
	["挥手拦车_右"] = "StopCar_Right",
	["挥手拦车_左"] = "StopCar_Left",
	["捏脸展示-循环"] = "CustomRole_Show_Idle",
	["捏脸展示loop动作"] = "CustomRole_Loop",
	["捧起物品"] = "Hold_Things",
	["捶地"] = "A_M_Beatground",
	["捶地_开始"] = "A_M_Beatground_Start",
	["捶地_循环"] = "A_M_Beatground_Loop",
	["捶地_结束"] = "A_M_Beatground_End",
	["排行榜1号待机动画"] = "RankPos1",
	["排行榜2号待机动画"] = "RankPos2",
	["排行榜3号待机动画"] = "RankPos3",
	["排行榜动作_1号"] = "Rank_01",
	["排行榜动作_2号"] = "Rank_02",
	["排行榜动作_3号"] = "Rank_03",
	["探索技能_衰败动画"] = "Explore_Decay_Skill",
	["接"] = "Get",
	["推车"] = "Construct",
	["推车_开始_女"] = "A_F_Construct_Start",
	["推车_开始_男"] = "A_M_Construct01_Start",
	["推车_循环_女"] = "A_F_Construct_Loop",
	["推车_循环_男"] = "A_M_Construct01_Loop",
	["推车_结束_女"] = "A_F_Construct_End",
	["推车_结束_男"] = "A_M_Construct01_End",
	["掩面哭泣"] = "Cry",
	["掩面哭泣_开始"] = "Cry_Start",
	["掩面哭泣_循环"] = "Cry_Loop",
	["掩面哭泣_结束"] = "Cry_End",
	["掸灰"] = "DustOff",
	["搜索"] = "Search",
	["摇头_无手势"] = "Talk_No_B",
	["摇头_无手势_新"] = "Talk_No",
	["摇头_有手势"] = "Talk_No_A",
	["摇曳"] = "Waggle",
	["摔倒"] = "Tumble",
	["摔倒_单次"] = "Tumble_Single",
	["摔倒_开始"] = "Tumble_Start",
	["摔倒_循环"] = "Tumble_Loop",
	["摔倒_结束"] = "Tumble_End",
	["摩拳擦掌"] = "Threat",
	["撑伞"] = "Holdumbrella",
	["撑伞_开始"] = "Holdumbrella_Start",
	["撑伞_循环"] = "Holdumbrella_Loop",
	["撑伞_结束"] = "Holdumbrella_End",
	["擦柜子"] = "WipeCabinet",
	["擦桌子"] = "WipeTable",
	["擦火柴"] = "LightMatch",
	["擦火柴_开始"] = "LightMatch_Start",
	["擦火柴_循环"] = "LightMatch_Loop",
	["擦火柴_结束"] = "LightMatch_End",
	["擦皮鞋"] = "Shoeshine",
	["收下某物"] = "Catchsome",
	["收下某物_开始"] = "Catchsome_Start",
	["收下某物_循环"] = "Catchsome_Loop",
	["收下某物_结束"] = "Catchsome_End",
	["收下某物_贝"] = "Catchsome_Bernadette",
	["收下某物_贝_开始"] = "Catchsome_Bernadette_Start",
	["收下某物_贝_循环"] = "Catchsome_Bernadette_Loop",
	["收下某物_贝_结束"] = "Catchsome_Bernadette_End",
	["收集_开始"] = "A_Base_Collection_St01",
	["收集_循环"] = "A_Base_Collection_Loop01",
	["收集_结束"] = "A_Base_Collection_Ed01",
	["攻击2"] = "Attack2",
	["攻击_单手_开枪"] = "A_Base_Attack",
	["攻击_单手_挥动"] = "A_Fea_Attack_01",
	["攻击_右"] = "Attack_Right",
	["攻击_咬"] = "Attack_Bite",
	["攻击_左"] = "Attack_Left",
	["攻击_扑"] = "Attack_Pounce",
	["攻击_连续"] = "Attack_Series",
	["教学"] = "Teach",
	["敲门"] = "Knockdoor",
	["敲门_开始"] = "Knockdoor_start",
	["敲门_循环"] = "Knockdoor_loop",
	["敲门_结束"] = "Knockdoor_end",
	["新版创角选职Sequence动画"] = "RoleCreateNew_Sequence",
	["新版创角选职入场动画"] = "RoleCreateNew_In",
	["新版创角选职待机动画"] = "RoleCreateNew_Idle",
	["新版创角选职退场动画"] = "RoleCreateNew_Out",
	["施法复生"] = "Herb_Rebirth",
	["旅人_激动"] = "Traveler_Escape",
	["旅人_激动_开始"] = "Traveler_Escape_Start",
	["旅人_激动_循环"] = "Traveler_Escape_Loop",
	["旅人_激动_结束"] = "Traveler_Escape_End",
	["旅人_疑惑"] = "Traveler_Doubt",
	["旅人_疑惑_开始"] = "Traveler_Doubt_Start",
	["旅人_疑惑_循环"] = "Traveler_Doubt_Loop",
	["旅人_疑惑_结束"] = "Traveler_Doubt_End",
	["无奈摊手"] = "Helpless",
	["无奈摊手_开始"] = "Helpless_Start",
	["无奈摊手_循环"] = "Helpless_Loop",
	["无奈摊手_结束"] = "Helpless_End",
	["无输入滑翔中"] = "Glide_Loop_Idle",
	["无输入进入滑翔"] = "Glide_Start_Idle",
	["时装_待机_04"] = "Fashion_Idle_04",
	["昏倒"] = "Passedout",
	["星光囚笼_挣扎"] = "StoryBeforeSleep_Struggle",
	["普通->战斗Idle"] = "NormalToCombatIdle",
	["普通待机"] = "Idle",
	["有输入滑翔中"] = "Glide_Loop_Dash",
	["有输入进入滑翔"] = "Glide_Start_Dash",
	["服装展示"] = "Suit_Show",
	["服装展示_01"] = "Suit_Show_01",
	["服装展示_02"] = "Suit_Show_02",
	["服装展示_03"] = "Suit_Show_03",
	["服装展示_吸血鬼套"] = "Suit_Show_Vampire",
	["朝前呼喊"] = "Shout",
	["朝天干杯"] = "A_Sit_DrinkBeer",
	["木堆倒塌循环"] = "Woodpile_Loop",
	["木堆倒塌过程"] = "Woodpile",
	["木头待机"] = "WoodRoll_Idle",
	["木头滚动停止"] = "WoodRoll_End",
	["木头滚动开始"] = "WoodRoll_Srart",
	["木头滚动循环"] = "WoodRoll_Loop",
	["木架倒塌"] = "scaff_broken",
	["木架倒塌待机"] = "scaff_broken_idle",
	["木架正常待机"] = "scaff001_idle",
	["松鼠_下树"] = "Squirrel_ClimbDown",
	["松鼠_吃松果"] = "Squirrel_Eat",
	["松鼠_待机"] = "Squirrel_Idle",
	["松鼠_爬树"] = "Squirrel_ClimbUp",
	["松鼠_跑"] = "Squirrel_Run",
	["桌子破碎"] = "Broken",
	["梅丽莎出场"] = "Melisa_Show",
	["检查"] = "NPC_Check",
	["棺材_关闭"] = "Casket_Close",
	["棺材_关闭_循环"] = "Casket_Close_Idle",
	["棺材_开启"] = "Casket_Open",
	["棺材_开启_循环"] = "Casket_Open_Idle",
	["欢呼02"] = "A_Base_Cheer02",
	["欢呼03"] = "A_Base_Cheer03",
	["欢迎"] = "Welcome",
	["正面点位_正面落座"] = "Sit_Forward",
	["正面点位_背面落座"] = "Sit_Backward",
	["正面点位起身"] = "Standup_Forward",
	["死亡(蒙太奇)"] = "Dead",
	["死亡_任务通用1"] = "TaskDead1",
	["死亡_任务通用2"] = "TaskDead2",
	["死亡_任务通用3"] = "TaskDead3",
	["死亡_任务通用4"] = "TaskDead4",
	["死亡_右"] = "Die_Right",
	["死亡_左"] = "Die_Left",
	["死亡_趴"] = "Down",
	["死亡_躺"] = "lying",
	["死亡循环"] = "Dead_Loop",
	["比划交谈"] = "Talk_Gesture",
	["氛围_欢呼"] = "A_Base_Cheer",
	["洗衣服"] = "Wash",
	["浇花"] = "WaterFlowers",
	["海精灵_出现"] = "SeaElves_Appear",
	["海精灵_左右摇摆"] = "SeaElves_swing",
	["海精灵_待机"] = "SeaElves_Idle",
	["海精灵_移动_跑"] = "SeaElves_Run",
	["海精灵_转圈"] = "SeaElves_Spinning",
	["海精灵_高兴"] = "SeaElves_Happy",
	["演讲_男01"] = "M_Talk_Accuse01",
	["潜行待机个性动作"] = "IdleStalkAction",
	["灵摆占卜_开始_黄"] = "PendulumDivine_Y_Start",
	["灵摆占卜_循环_黄"] = "PendulumDivine_Y_Loop",
	["灵摆占卜_结束_黄"] = "PendulumDivine_Y_End",
	["灵摆占卜_逆时针快速转动_黄"] = "PendulumDivine_Y_Fast02",
	["灵摆占卜_逆时针慢速转动_黄"] = "PendulumDivine_Y_Slow02",
	["灵摆占卜_顺时针快速转动_黄"] = "PendulumDivine_Y_Fast01",
	["灵摆占卜_顺时针慢速转动_黄"] = "PendulumDivine_Y_Slow01",
	["灵摆占卜黄"] = "PendulumDivine_Y",
	["灵摆占卜黄_逆时针快速转动"] = "PendulumDivine_Y_Fast04",
	["灵摆占卜黄_逆时针慢速转动"] = "PendulumDivine_Y_Slow04",
	["灵摆占卜黄_顺时针快速转动"] = "PendulumDivine_Y_Fast03",
	["灵摆占卜黄_顺时针慢速转动"] = "PendulumDivine_Y_Slow03",
	["灵鸟地面待机"] = "Idle_Ground",
	["点头_无手势"] = "Talk_Yes_A",
	["点头_无手势_新"] = "Talk_Yes",
	["点头_有手势"] = "Talk_Yes_B",
	["照相"] = "Photography",
	["特殊->战斗Idle"] = "RandomToCombatIdle",
	["特殊->普通Idle"] = "RandomToNormalIdle",
	["狂暴狒狒_变身_攻击"] = "MonkeyRage_Attack",
	["狒狒_开始变身"] = "Monkey_Morph_Start",
	["狒狒_无精打采"] = "Monkey_Cheerless",
	["狒狒_生机勃勃"] = "Monkey_Spirited",
	["献花_放下"] = "PresentFlower_End",
	["献花_站立待机"] = "PresentFlower_Stand",
	["献花_行走"] = "PresentFlower_Walk",
	["班森_出场_开始"] = "Benson2_Show_Start",
	["班森_出场_循环"] = "Benson2_Show_Loop",
	["班森_出场_结束"] = "Benson2_Show_End",
	["班森_站姿_开始"] = "Benson2_Idle_Start",
	["班森_站姿_循环"] = "Benson2_Idle_Loop",
	["班森_站姿_结束"] = "Benson2_Idle_End",
	["瑞尔比伯投技_开始"] = "Grab_RB_Start",
	["瑞尔比伯投技_持续"] = "Grab_RB_Loop",
	["瑞尔比伯投技_结束"] = "Grab_RB_End",
	["生气"] = "Angry",
	["生气_单次"] = "A_Base_Angry",
	["生气_开始"] = "Angry_Start",
	["生气_循环"] = "Angry_Loop",
	["生气_愤怒"] = "Angry_Single",
	["生气_结束"] = "Angry_End",
	["生病咳嗽"] = "Cough",
	["生病咳嗽_开始"] = "Cough_Start",
	["生病咳嗽_循环"] = "Cough_Loop",
	["生病咳嗽_结束"] = "Cough_End",
	["男女_公主抱_受邀方"] = "MF_Princess_P",
	["男女_公主抱_开始_受邀方"] = "MF_Princess_P_Start",
	["男女_公主抱_开始_邀请方"] = "MF_Princess_I_Start",
	["男女_公主抱_循环_受邀方"] = "MF_Princess_P_Loop",
	["男女_公主抱_循环_邀请方"] = "MF_Princess_I_Loop",
	["男女_公主抱_结束_受邀方"] = "MF_Princess_P_End",
	["男女_公主抱_结束_邀请方"] = "MF_Princess_I_End",
	["男女_公主抱_走路_受邀方"] = "MF_Princess_P_Walk",
	["男女_公主抱_走路_邀请方"] = "MF_Princess_I_Walk",
	["男女_公主抱_邀请方"] = "MF_Princess_I",
	["男女_拥抱_受邀方"] = "MF_Hug_P",
	["男女_拥抱_开始_受邀方"] = "MF_Hug_P_Start",
	["男女_拥抱_开始_邀请方"] = "MF_Hug_I_Start",
	["男女_拥抱_循环_受邀方"] = "MF_Hug_P_Loop",
	["男女_拥抱_循环_邀请方"] = "MF_Hug_I_Loop",
	["男女_拥抱_结束_受邀方"] = "MF_Hug_P_End",
	["男女_拥抱_结束_邀请方"] = "MF_Hug_I_End",
	["男女_拥抱_邀请方"] = "MF_Hug_I",
	["男小丑耍球_失败"] = "男小丑耍球_失败",
	["男小丑耍球_开始"] = "男小丑耍球_开始",
	["男小丑耍球_循环"] = "男小丑耍球_循环",
	["男小丑耍球_成功"] = "男小丑耍球_成功",
	["男性坐下"] = "M_Sit_Poor",
	["男性宫廷舞"] = "M_Idle_Dance_Royal",
	["男性浇花_开始"] = "男性浇花_开始",
	["男性浇花_循环"] = "男性浇花_循环",
	["男性浇花_结束"] = "男性浇花_结束",
	["男性演讲"] = "M_Idle_Speech",
	["男性独舞"] = "M_Idle_Dance",
	["男男_公主抱_受邀方"] = "MM_Princess_P",
	["男男_公主抱_开始_受邀方"] = "MM_Princess_P_Start",
	["男男_公主抱_开始_邀请方"] = "MM_Princess_I_Start",
	["男男_公主抱_循环_受邀方"] = "MM_Princess_P_Loop",
	["男男_公主抱_循环_邀请方"] = "MM_Princess_I_Loop",
	["男男_公主抱_结束_受邀方"] = "MM_Princess_P_End",
	["男男_公主抱_结束_邀请方"] = "MM_Princess_I_End",
	["男男_公主抱_走路_受邀方"] = "MM_Princess_P_Walk",
	["男男_公主抱_走路_邀请方"] = "MM_Princess_I_Walk",
	["男男_公主抱_邀请方"] = "MM_Princess_I",
	["男男_拥抱_受邀方"] = "MM_Hug_P",
	["男男_拥抱_开始_受邀方"] = "MM_Hug_P_Start",
	["男男_拥抱_开始_邀请方"] = "MM_Hug_I_Start",
	["男男_拥抱_循环_受邀方"] = "MM_Hug_P_Loop",
	["男男_拥抱_循环_邀请方"] = "MM_Hug_I_Loop",
	["男男_拥抱_结束_受邀方"] = "MM_Hug_P_End",
	["男男_拥抱_结束_邀请方"] = "MM_Hug_I_End",
	["男男_拥抱_邀请方"] = "MM_Hug_I",
	["画画_新"] = "Draw",
	["画画_新_无画板"] = "Draw_NoBoard",
	["画画_男"] = "M_Painting",
	["瘫坐"] = "Sitdesk",
	["瘫坐挣扎"] = "Sitdesk_001",
	["皮特_半跪拥抱_开始"] = "Main_HugSister_Start",
	["皮特_半跪拥抱_循环"] = "Main_HugSister_Loop",
	["看书"] = "Read",
	["看书_开始"] = "Read_Start",
	["看书_开始_女"] = "看书_开始_女",
	["看书_开始_男"] = "看书_开始_男",
	["看书_循环"] = "Read_Loop",
	["看书_循环_女"] = "看书_循环_女",
	["看书_循环_男"] = "看书_循环_男",
	["看书_结束"] = "Read_End",
	["看书_结束_女"] = "看书_结束_女",
	["看书_结束_男"] = "看书_结束_男",
	["看报纸_坐姿"] = "Sit_Read_Newspaper",
	["看报纸_坐姿_开始"] = "Sit_Read_Newspaper_Start",
	["看报纸_坐姿_循环"] = "Sit_Read_Newspaper_Loop",
	["看报纸_坐姿_结束"] = "Sit_Read_Newspaper_End",
	["看报纸_站姿"] = "Stand_Read_Newspaper",
	["看报纸_站姿_开始"] = "Stand_Read_Newspaper_Start",
	["看报纸_站姿_循环"] = "Stand_Read_Newspaper_Loop",
	["看报纸_站姿_结束"] = "Stand_Read_Newspaper_End",
	["眩晕_循环"] = "Main_Sleep",
	["睡觉_趴"] = "Sleep_Prone",
	["睡觉_躺"] = "Sleep_Lie",
	["砸八音盒"] = "A_M_Smash",
	["砸八音盒_开始"] = "A_M_Smash_Start",
	["砸八音盒_循环"] = "A_M_Smash_Loop",
	["砸八音盒_结束"] = "A_M_Smash_End",
	["磨爪子"] = "SharpenClaws",
	["磨爪子_02"] = "SharpenClaws_02",
	["礼物盒_保持关闭"] = "GiftBox_Closeing",
	["礼物盒_保持开启"] = "GiftBox_Opening",
	["礼物盒_关闭"] = "GiftBox_Close",
	["礼物盒_开启"] = "GiftBox_Open",
	["秋千"] = "Swing",
	["秋千_开始"] = "Swing_Start",
	["秋千_循环"] = "Swing_Loop",
	["秋千_结束"] = "Swing_End",
	["积极_激动"] = "Excite_Positive",
	["积极_生气"] = "Excite_Angry",
	["穷人_站着书写_循环"] = "StandWrite_Loop_Poor",
	["穷版_站着书写_循环"] = "Stand_Write_Poor",
	["站姿书写有书"] = "StandWrite",
	["站姿书写有书_开始"] = "StandWrite_Start",
	["站姿书写有书_循环"] = "StandWrite_Loop",
	["站姿书写有书_结束"] = "StandWrite_End",
	["站着书写"] = "Stand_Write",
	["站着书写_开始"] = "Stand_Write_Start",
	["站着书写_循环"] = "Stand_Write_Loop",
	["站着书写_结束"] = "Stand_Write_End",
	["站立_Idle"] = "Idle_Normal",
	["站立_喝东西"] = "StandDrink",
	["站立_喝酒致敬"] = "Stand_Drink_Salute",
	["站立书写有书_开始_女"] = "站立书写有书_开始_女",
	["站立书写有书_开始_男"] = "站立书写有书_开始_男",
	["站立书写有书_循环_女"] = "站立书写有书_循环_女",
	["站立书写有书_循环_男"] = "站立书写有书_循环_男",
	["站立书写有书_结束_女"] = "站立书写有书_结束_女",
	["站立书写有书_结束_男"] = "站立书写有书_结束_男",
	["站立哭泣"] = "Cry_Stand",
	["站立喝酒"] = "Stand_Drink",
	["站立虚弱"] = "Stand_Weak",
	["站立采集上收"] = "StandCollection3",
	["站立采集上收_开始"] = "StandCollection_Start3",
	["站立采集上收_循环"] = "StandCollection_Loop3",
	["站立采集上收_结束"] = "StandCollection_End3",
	["站立采集不收回"] = "StandCollection1",
	["站立采集不收回_开始"] = "StandCollection_Start1",
	["站立采集不收回_循环"] = "StandCollection_Loop1",
	["站立采集不收回_结束"] = "StandCollection_End1",
	["站立采集前收"] = "StandCollection2",
	["站立采集前收_开始"] = "StandCollection_Start2",
	["站立采集前收_循环"] = "StandCollection_Loop2",
	["站立采集前收_结束"] = "StandCollection_End2",
	["端盘子_待机"] = "Serve_Wine",
	["端盘子_走路"] = "Serve_Wine_Walk",
	["简短打招呼"] = "Hello_Short",
	["组队待机动作"] = "TeamIdleAnim",
	["结算场景站位1入场动画"] = "SettlementPos1_In",
	["结算场景站位1待机动画"] = "SettlementPos1_Idle",
	["结算场景站位2入场动画"] = "SettlementPos2_In",
	["结算场景站位2待机动画"] = "SettlementPos2_Idle",
	["结算场景站位3入场动画"] = "SettlementPos3_In",
	["结算场景站位3待机动画"] = "SettlementPos3_Idle",
	["结算场景站位4入场动画"] = "SettlementPos4_In",
	["结算场景站位4待机动画"] = "SettlementPos4_Idle",
	["结算场景站位5入场动画"] = "SettlementPos5_In",
	["结算场景站位5待机动画"] = "SettlementPos5_Idle",
	["绯红意志_展示用"] = "Moon_Show",
	["罗珊_站姿"] = "Rosanne_Stand",
	["群殴2循环_男"] = "M_Gangfight_Beat02_Loop",
	["群殴_循环1"] = "Gangfight_Beat01_Loop",
	["群殴_结束1"] = "Gangfight_Beat01_End",
	["群殴_结束2"] = "Gangfight_Beat02_End",
	["群殴击倒"] = "Gangfight_Beaten",
	["翻车"] = "Rollover",
	["翻车_开始_男"] = "A_M_Rollover_Start",
	["翻车_循环_女"] = "A_F_Rollover_Loop",
	["翻车_循环_男"] = "A_M_Rollover_Loop",
	["翻车_结束_女"] = "A_F_Rollover_End",
	["翻车_结束_男"] = "A_M_Rollover_End",
	["老人_坐椅子"] = "Elder_SitOnChair",
	["老人_睡觉_坐椅子"] = "Elder_Sleep_SitOnChair",
	["老人_说话"] = "Elder_Talk",
	["老人_说话_坐椅子"] = "Elder_Talk_SitOnChair",
	["老人_说话和指"] = "Elder_TalkAndPoint",
	["老人_跑"] = "Elder_Run",
	["老人待机"] = "Old_Idle",
	["老人行走"] = "NPC_Walk_Elder",
	["老尼尔出场"] = "Niel_Show",
	["背广告牌"] = "Publicize",
	["脚印展示"] = "footprint_show",
	["自行车-单人休闲待机"] = "Base_Bike_IdleShow",
	["自行车_上车"] = "Bike_Geton",
	["自行车_下车"] = "Bike_GetOff",
	["自行车共乘"] = "Mount_Passenger",
	["自行车类模板"] = "Riding",
	["舒畅"] = "Good",
	["舔毛"] = "LickFur",
	["舞会_单人舞"] = "Dance_Solo",
	["舞会_女方被邀请"] = "DancingParty_Invited_F",
	["舞会_女方邀请插花"] = "DancingParty_Invite_F",
	["舞会_男方被邀请"] = "DancingParty_Invited_M",
	["舞会_男方邀请插花"] = "DancingParty_Invite_M",
	["舞蹈"] = "Dance",
	["芙拉开心"] = "Fatbird_Happy",
	["芙拉撞笼子"] = "Fatbird_Crazy",
	["芙拉飞"] = "Fatbird_Fly",
	["苏格兰舞蹈"] = "ScotlandDance",
	["苏茜3M跑"] = "Run_3M",
	["苏茜6M跑"] = "Run_6M",
	["苏茜吐舌头蹲"] = "Dog01_Crouch_OpenMouth",
	["苏茜待机"] = "Susai_Idle",
	["苏茜看书"] = "Dog01_Readbook_Show",
	["苏茜站起"] = "Dog01_Crouch_To_Stand",
	["蒸汽锅_动画"] = "SteamCooker_Cook",
	["蒸汽锅_待机"] = "SteamCooker_Idle",
	["藤曼待机"] = "Vine_Idle",
	["藤曼放下"] = "Vine_Loosen",
	["藤曼缠绕"] = "Vine_Struggle",
	["行走"] = "Walk1",
	["行走_哭泣_孩童"] = "Walk_Cry_Children",
	["行走_喝醉"] = "Drunk_Walk",
	["行走_四处看看"] = "NPC_Walk_Lookaround",
	["行走_幻觉"] = "NPC_Walk_Illusion",
	["行走_老年"] = "Walk_Elder",
	["行走_虚弱"] = "Walk_Weak",
	["行走_贵族"] = "Walk_Rich",
	["表情_微笑"] = "Emoji_Smile",
	["表情_悲伤"] = "Emoji_Sad",
	["表情_愤怒"] = "Emoji_Angry",
	["表演动作1"] = "Idle_Performance1",
	["表演动作2"] = "Idle_Performance2",
	["表演动作3"] = "Idle_Performance3",
	["表演动作4"] = "Idle_Performance4",
	["被踩"] = "Peter_BeStepedOn",
	["角色扮演入场动画"] = "RolePlay_FadeIn",
	["角色扮演待机动画"] = "RolePlay_Loop",
	["角色扮演待机动画2"] = "RolePlay_Loop2",
	["角色扮演抬手动画"] = "RolePlay_HandsUp",
	["角色扮演放手动画"] = "RolePlay_HandsDown",
	["角色扮演离场动画"] = "RolePlay_FadeOn",
	["解除灵性之墙"] = "DestroySpiritualWall",
	["警戒"] = "Alert",
	["警戒_开始"] = "Alert_Start",
	["警戒_循环"] = "Alert_Loop",
	["警戒_结束"] = "Alert_End",
	["记录_男"] = "M_Record",
	["记录_男_开始"] = "M_Record_Start",
	["记录_男_循环"] = "M_Record_Loop",
	["记录_男_结束"] = "M_Record_End",
	["调查_低处"] = "Search_Low",
	["调查_低处_开始"] = "Search_Low_Start",
	["调查_低处_循环"] = "Search_Low_Loop",
	["调查_低处_结束"] = "Search_Low_End",
	["调查_平视"] = "Search_Flat",
	["调查_平视_开始"] = "Search_Flat_Start",
	["调查_平视_循环"] = "Search_Flat_Loop",
	["调查_平视_结束"] = "Search_Flat_End",
	["调查_高处"] = "Search_High",
	["调查_高处_开始"] = "Search_High_Start",
	["调查_高处_循环"] = "Search_High_Loop",
	["调查_高处_结束"] = "Search_High_End",
	["调查沉思_高处"] = "SearchThink_High",
	["调查沉思_高处_开始"] = "SearchThink_High_Start",
	["调查沉思_高处_循环"] = "SearchThink_High_Loop",
	["调查沉思_高处_结束"] = "SearchThink_High_End",
	["质疑"] = "Question",
	["质疑-开始"] = "Question_Start",
	["质疑-循环"] = "Question_Loop",
	["质疑-结束"] = "Question_End",
	["贴邮票"] = "NPC_Stamp",
	["走"] = "Walk",
	["走_后退"] = "Walk_Back",
	["走_向右"] = "Walk_Right",
	["走_向左"] = "Walk_Left",
	["走_潜行"] = "Walk_Stalk",
	["走_潜行_后退"] = "Walk_Stalk_Back",
	["走_醉酒"] = "Walk_Drunk",
	["走停止右"] = "Walk_End_R",
	["走停止左"] = "Walk_End_L",
	["走起步"] = "Walk_Start",
	["走路结束起步高"] = "Walk_Jump_End_Restart_High",
	["走路跳跃开始右脚步"] = "Walk_Jump_Start_R",
	["走路跳跃开始左脚步"] = "Walk_Jump_Start_L",
	["走路跳跃结束起步低"] = "Walk_Jump_End_Restart",
	["趴"] = "Prone",
	["趴_到_坐"] = "Prone_To_Sit",
	["趴个性动作"] = "ProneAction",
	["趴个性动作2"] = "ProneAction_02",
	["跑"] = "Run1",
	["跑_孩童"] = "Run_Children",
	["跑停步"] = "Run_End",
	["跑右停步"] = "Run_End_R",
	["跑左停步"] = "Run_End_L",
	["跑循环"] = "Run",
	["跑起步"] = "Run_Start",
	["跪地_痛苦"] = "KneeHeadache",
	["路人女_躲避马车_1"] = "A_F_Passer_Dodge01",
	["路人女_躲避马车_2"] = "A_F_Passer_Dodge02",
	["路人女_躲避马车_3"] = "A_F_Passer_Dodge03",
	["路人女_躲避马车_4"] = "A_F_Passer_Dodge04",
	["路人男_躲避马车_1"] = "A_M_Passer_Dodge01",
	["路人男_躲避马车_2"] = "A_M_Passer_Dodge02",
	["路人男_躲避马车_3"] = "A_M_Passer_Dodge03",
	["路人男_躲避马车_4"] = "A_M_Passer_Dodge04",
	["跳"] = "Jump",
	["跳跃开始"] = "Jump_Start",
	["跳跃开始右脚步"] = "Jump_Start_R",
	["跳跃开始左脚步"] = "Jump_Start_L",
	["跳跃循环"] = "Jump_Loop",
	["跳跃结束"] = "Jump_End",
	["跳跃结束起步低"] = "Jump_End_Restart",
	["跳跃结束起步高"] = "Jump_End_Restart_High",
	["跳跃结束高"] = "Jump_End_High",
	["跳跃运动"] = "Radio3",
	["跳跃运动_开始"] = "Radio_Start3",
	["跳跃运动_循环"] = "Radio_Loop3",
	["跳跃运动_结束"] = "Radio_End3",
	["踩人"] = "Step_On",
	["蹲下_单膝"] = "Crouch",
	["蹲下_单膝_一次性"] = "Crouch_All",
	["蹲下_单膝_开始"] = "Crouch_Start",
	["蹲下_单膝_循环"] = "Crouch_Loop",
	["蹲下_单膝_结束"] = "Crouch_End",
	["蹲下_害怕"] = "Crouch_Scared",
	["蹲下_害怕_开始"] = "Crouch_Scared_Start",
	["蹲下_害怕_结束"] = "Crouch_Scared_End",
	["蹲下_小害怕_循环"] = "Crouch_Scared_Loop02",
	["蹲下_抚摸"] = "CrouchCaress",
	["蹲下_超害怕"] = "Crouch_Scared_Extreme",
	["蹲下_超害怕_循环"] = "Crouch_Scared_Loop01",
	["蹲下放本子"] = "PutNote",
	["蹲下采集"] = "Rummage",
	["躺"] = "Lie",
	["躺2"] = "Lie2",
	["躺_04"] = "Lie_04",
	["躺_到_坐"] = "Lie_To_Sit",
	["躺_开始_男03"] = "M_Lie_St03",
	["躺_循环_男"] = "M_Lie_Loop",
	["躺_无呼吸"] = "Lie_NoBreathe",
	["躺_痛苦"] = "LieSuffer",
	["躺_痛苦_开始"] = "LieSuffer_Start",
	["躺_痛苦_循环"] = "LieSuffer_Loop",
	["躺_痛苦_结束"] = "LieSuffer_End",
	["躺_结束_男03"] = "M_Lie_Ed03",
	["躺_闭眼"] = "Lie_EyesClose",
	["躺下_孩童"] = "Lie_Children",
	["躺下_孩童_开始"] = "Lie_Children_Start",
	["躺下_孩童_循环"] = "Lie_Children_Loop",
	["躺下_孩童_结束"] = "Lie_Children_End",
	["躺个性动作"] = "LieAction",
	["躺地"] = "Lie01",
	["躺地_开始"] = "Lie01_Start",
	["躺地_循环"] = "Lie01_Loop",
	["躺地_结束"] = "Lie01_End",
	["躺着翻身"] = "Lie_Turn",
	["过来_示意"] = "ComeHere",
	["逃跑_男"] = "M_Run_Escape",
	["选职界面角色入场动画"] = "RoleCreate_In",
	["选职界面角色待机动画"] = "RoleCreate_Loop",
	["选角界面模型的idle动画"] = "SelectIRole_Idle",
	["邓恩出场"] = "Dunn_Show",
	["邮差_摘下帽子"] = "PutOffHat",
	["邮差_照镜子"] = "LookMirror",
	["钻入镜子_镜子待机"] = "MirrorInto_MirrorIdle",
	["钻出镜子_小精灵待机"] = "MirrorOut_MirrorElfIdle",
	["铲土_女"] = "A_F_Shovel",
	["铲土_男"] = "A_M_Shovel",
	["闻气味_女"] = "A_F_Smell",
	["闻气味_男"] = "A_M_Smell",
	["闻臭拒绝"] = "Smelly",
	["闻香气_女"] = "A_F_Fragrance",
	["闻香气_男"] = "A_M_Fragrance",
	["阿罗德斯_出现"] = "MirrorElfAppear",
	["阿罗德斯_出现_待机"] = "MirrorElfAppear_idle",
	["阿罗德斯_开心"] = "MirrorElf_Happy",
	["阿罗德斯_敲镜子"] = "MirrorElf_Knock",
	["阿罗德斯_无语"] = "MirrorElf_Depressed",
	["阿罗德斯_溶解出现"] = "Arrodes_Dissolve_Appear",
	["阿罗德斯_生气"] = "MirrorElf_Angry",
	["阿罗德斯_精灵待机"] = "MirrorElfIdle",
	["阿罗德斯_精灵说话_开始"] = "MirrorElfTalk_Start",
	["阿罗德斯_精灵说话_循环"] = "MirrorElfTalk_Loop",
	["阿罗德斯_精灵说话_结束"] = "MirrorElfTalk_End",
	["阿罗德斯_说话"] = "MirrorElf_Talk",
	["阿罗德斯_谄媚"] = "MirrorElf_Flattery",
	["阿罗德斯_钻入镜子（快）"] = "MirrorInto",
	["阿罗德斯_钻出镜子（快）"] = "MirrorOut",
	["阿罗德斯_镜子待机"] = "MirrorIdle",
	["阿罗德斯红光"] = "Arrodes_Red",
	["阿罗德斯绿光"] = "Arrodes_Green",
	["阿罗德斯蓝光"] = "Arrodes_Blue",
	["阿罗德斯雷劈开始"] = "Arrodes_Thunder_Start",
	["阿罗德斯雷劈循环"] = "Arrodes_Thunder_Loop",
	["阿罗德斯雷劈结束"] = "Arrodes_Thunder_End",
	["阿罗德斯黄光"] = "Arrodes_Yellow",
	["雷劈倒地后站起新"] = "Arrodes_Thunder_Z",
	["雷劈后倒地持续"] = "Arrodes_Thunder_Y",
	["雷劈开始新"] = "Arrodes_Thunder_X",
	["震撼_女"] = "A_Base_w_Shock",
	["震撼_男"] = "A_Base_M_Shock",
	["靠墙交谈"] = "Stand_WallTalk",
	["靠墙休息"] = "Stand_WallRest",
	["靠墙站立"] = "Lean",
	["靠墙站立_开始"] = "Lean_Start",
	["靠墙站立_循环"] = "Lean_Loop",
	["靠墙站立_结束"] = "Lean_End",
	["面部_害怕"] = "Face_Fear",
	["面部_开心"] = "Face_Happy",
	["面部_微笑"] = "Face_Smile",
	["面部_思考"] = "Face_Think",
	["面部_悲伤"] = "Face_Sad",
	["面部_惊讶"] = "Face_Surprise",
	["面部_生气"] = "Face_Angry",
	["面部_眨眼"] = "Face_Blink",
	["面部_闭眼"] = "Face_Close",
	["骑行Idle"] = "Riding_Idle",
	["骑行Idle镜像"] = "Riding_Idle_Mirror",
	["骑行中速循环"] = "Riding_Loop",
	["骑行中速循环镜像"] = "Riding_Loop_Mirror",
	["骑行低速"] = "Riding_End_Walk",
	["骑行低速循环"] = "Riding_Loop_Walk",
	["骑行冲刺"] = "Riding_Dash",
	["骑行冲刺镜像"] = "Riding_Dash_Mirror",
	["骑行快速起步"] = "Riding_Start_Sprint",
	["骑行快速起步镜像"] = "Riding_Start_Sprint_Mirror",
	["骑行快速转身"] = "Riding_MoveTurn_Sprint",
	["骑行快速转身镜像"] = "Riding_MoveTurn_Sprint_Mirror",
	["骑行慢速起步"] = "Riding_Start_Walk",
	["骑行普通停步"] = "Riding_End",
	["骑行普通停步镜像"] = "Riding_End_Mirror",
	["骑行普通起步"] = "Riding_Start",
	["骑行普通起步镜像"] = "Riding_Start_Mirror",
	["骑行跳跃低停"] = "Riding_Jump_End",
	["骑行跳跃低停镜像"] = "Riding_Jump_End_Mirror",
	["骑行跳跃开始"] = "Riding_Jump_Start",
	["骑行跳跃开始镜像"] = "Riding_Jump_Start_Mirror",
	["骑行跳跃循环"] = "Riding_Jump_Loop",
	["骑行跳跃循环镜像"] = "Riding_Jump_Loop_Mirror",
	["骑行跳跃高停"] = "Riding_Jump_End_High",
	["骑行跳跃高停镜像"] = "Riding_Jump_End_High_Mirror",
	["骑行转身"] = "Riding_MoveTurn",
	["骑行转身镜像"] = "Riding_MoveTurn_Mirror",
	["骑行进入"] = "Riding_GetIn",
	["骑行退出"] = "Riding_GetOut",
	["骑行高速"] = "Riding_End_Sprint",
	["骑行高速循环"] = "Riding_Loop_Sprint",
	["骑行高速循环镜像"] = "Riding_Loop_Sprint_Mirror",
	["骑行高速镜像"] = "Riding_End_Sprint_Mirror",
	["骑马类模板"] = "Riding_Horse",
	["魔女_主导进入"] = "Witch_MasterTied_Enter",
	["魔女_气泡持续"] = "Witch_PetTied_Single",
	["魔女_气泡进入"] = "Witch_PetTied_Enter",
	["魔女_气泡退出"] = "Witch_PetTied_End",
	["魔女_躺椅_出现"] = "魔女_躺椅_出现",
	["魔女_躺椅_结束"] = "魔女_躺椅_结束",
	["魔女单人躺椅_结束"] = "WitchLieEnd",
	["魔女双人躺_被女抱"] = "Witch_Chair_F",
	["魔女双人躺_被女抱_被邀请"] = "Witch_Chair_FF",
	["魔女双人躺_被男抱"] = "Witch_Chair_M",
	["魔女双人躺_被男抱_被邀请"] = "Witch_Chair_MF",
	["魔女双人躺椅_女女_主_结束"] = "WitchEmbraceLoopFFEnd",
	["魔女双人躺椅_女女_被_结束"] = "WitchLieEmbracedFEnd",
	["魔女双人躺椅_女男_主_结束"] = "WitchEmbraceLoopFMEnd",
	["魔女双人躺椅_女男_被_结束"] = "WitchEmbracedLoopMEnd",
	["魔女双人躺椅待机"] = "WitchEmbraceLoop",
	["魔女拥抱女女1"] = "WitchHugFTF_01",
	["魔女拥抱女女2"] = "WitchHugFTF_02",
	["魔女拥抱男女"] = "WitchHugMTF",
	["魔女被女抱"] = "WitchLieEmbracedF",
	["魔女被女抱待机"] = "WitchEmbracedLoopF",
	["魔女被男抱"] = "WitchLieEmbracedM",
	["魔女被男抱待机"] = "WitchEmbracedLoopM",
	["魔女躺下"] = "WitchIdleToLie",
	["魔女躺下待机"] = "WitchLie",
	["魔女躺椅_单人"] = "Witch_Chair",
	["魔女躺椅拥抱"] = "WitchEmbrace",
	["魔鬼鱼_游动"] = "Manta_Swim",
	["魔鬼鱼_翻身"] = "Manta_TurnOver",
	["鲸鱼_游动_循环"] = "BlueWhale_Swim",
	["鲸鱼_翻身"] = "BlueWhale_TurnOver",
	["鲸鱼_跳跃"] = "BlueWhale_Jump",
	["鸟_待机休闲"] = "Preening",
	["鸟_待机到死亡"] = "IdleToDie",
	["鸟_循环死亡"] = "Die",
	["鸟_激动"] = "Excited",
	["鸟_绕人盘旋"] = "CircleAround",
	["鸟_蹭人"] = "Nuzzles",
	["鸟_飞行"] = "Fly",
	["鸟_飞行到死亡"] = "FlyToDie",
	["鸟笼打开_开始"] = "BirdCage_Open_Start",
	["鸟笼打开_循环"] = "BirdCage_Open_Loop",
	["鼓励_高兴"] = "Encourage_Happy",
	["鼓掌"] = "Applaud",
	["鼓掌_单次"] = "Applaud_Single",
	["鼓掌_开始"] = "Applaud_Start",
	["鼓掌_循环"] = "Applaud_Loop",
	["鼓掌_结束"] = "Applaud_End",
	["鼓舞加油"] = "A_Shout",
	["鼓舞加油_开始"] = "A_Shout_Start",
	["鼓舞加油_循环"] = "A_Shout_Loop",
	["鼓舞加油_结束"] = "A_Shout_End",
}

return AnimLibEnum
