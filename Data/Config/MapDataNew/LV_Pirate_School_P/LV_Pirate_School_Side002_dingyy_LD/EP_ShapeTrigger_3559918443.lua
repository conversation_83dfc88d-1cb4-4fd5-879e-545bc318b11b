return {
  ["ActorType"] = 60012,
  ["BoxExtent"] = FVector(340.000000,680.000000,85.000000),
  ["CType"] = "2",
  ["Class"] = "EP_ShapeTrigger",
  ["Cluster"] = "",
  ["DName"] = "还书时下楼踩点",
  ["DesignTag"] = "",
  ["GroupID"] = "",
  ["ID"] = "3559918443",
  ["LayerName"] = "LV_Pirate_School_Side002_dingyy_LD",
  ["Owner"] = "liusongfei",
  ["Radius"] = 500,
  ["SceneActorCommon"] = {
    ["InsType"] = 0,
  },
  ["Shape"] = 1,
  ["TaskCallID"] = "",
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["TraceGroundLocation"] = FVector(0.000000,0.000000,0.000000),
  ["Transform"] = {
    ["Position"] = FVector(1198.500000,1334.500000,221.000000),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
  },
  ["bEnable3D"] = true,
  ["bShowPreviewMesh"] = true,
  ["bTraceGround"] = false,
}