return {
  ["AOIRange"] = 0,
  ["ActorType"] = 60005,
  ["AnimQueue"] = {
    ["Queue"] = {
      [1] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "SeaElves_Spinning",
          ["StateName"] = "",
        },
        ["Duration"] = 10,
      },
      [2] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "SeaElves_Idle",
          ["StateName"] = "",
        },
        ["Duration"] = 10,
      },
      [3] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "SeaElves_Happy",
          ["StateName"] = "",
        },
        ["Duration"] = 10,
      },
    },
    ["bAutoActivate"] = true,
    ["bLoop"] = true,
  },
  ["BackUpWayPointPath"] = {
  },
  ["BindSceneActorInsID"] = "",
  ["BornChairInstanceID"] = "",
  ["CType"] = "2",
  ["Class"] = "EP_NpcSingleSpawner",
  ["Cluster"] = "",
  ["DName"] = "海精灵-青静态",
  ["DesignTag"] = "屏障碎片",
  ["FootLocation"] = FVector(-4420.000000,2945.000000,366.000000),
  ["GroupID"] = "",
  ["GroupMember"] = {
    [1] = {
      ["AnimQueue"] = {
        ["Queue"] = {
          [1] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "SeaElves_Spinning",
              ["StateName"] = "",
            },
            ["Duration"] = 10,
          },
          [2] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "SeaElves_Idle",
              ["StateName"] = "",
            },
            ["Duration"] = 10,
          },
          [3] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "SeaElves_Happy",
              ["StateName"] = "",
            },
            ["Duration"] = 10,
          },
        },
        ["bAutoActivate"] = true,
        ["bLoop"] = true,
      },
      ["BackUpWayPointPath"] = {
      },
      ["HalfHeight"] = 0,
      ["InstanceID"] = "759140785",
      ["PatrolOffset"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeParams"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeType"] = 0,
      ["Position"] = FVector(-4420.000000,2945.000000,410.000000),
      ["Rotator"] = FRotator(0.000000,140.000000,0.000000),
      ["TemplateID"] = 7265282,
      ["Voice"] = {
        ["AkAudioEvent"] = "",
        ["RandomPeriod"] = FVector2D(0.000000,0.000000),
      },
      ["bForceBornPos"] = false,
    },
  },
  ["ID"] = "759140785",
  ["LayerName"] = "520_Pirate_School",
  ["LookAtInstanceID"] = "",
  ["NpcPatrol"] = {
    ["PatrolOffset"] = FVector2D(0.000000,0.000000),
    ["PatrolScopeParams"] = FVector2D(0.000000,0.000000),
    ["PatrolScopeType"] = 0,
  },
  ["NpcSpawnerCommon"] = {
    ["NpcType"] = 1,
    ["Radius"] = 300,
    ["SpawnInterval"] = 0,
    ["bSequentialSpawn"] = false,
  },
  ["Owner"] = "lichen23",
  ["PathType"] = 0,
  ["RoleConfigID"] = {
    ["ConfigID"] = "7265282",
    ["Count"] = 1,
  },
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["InsType"] = 0,
  },
  ["SpiritualVision"] = {
    ["ShowType"] = 0,
  },
  ["TemplateID"] = 7265282,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-4420.000000,2945.000000,410.000000),
    ["Rotator"] = FRotator(0.000000,140.000000,0.000000),
  },
  ["Voice"] = {
    ["AkAudioEvent"] = "",
    ["RandomPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["WayPathSpeed"] = 200,
  ["WayPointListV2"] = {
  },
  ["WayPointPath"] = "",
  ["bBornWalkWayPath"] = false,
  ["bForceBornPos"] = false,
  ["bPathWithNpc"] = false,
  ["bShowLinear"] = true,
  ["bSitNearestChair"] = false,
  ["bStickGround"] = false,
  ["bUse3DRotation"] = false,
}