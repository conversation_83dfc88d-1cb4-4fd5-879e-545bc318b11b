return {
  ["ActorType"] = 60002,
  ["CType"] = "2",
  ["CameraConfig"] = {
    ["BasicConfig"] = {
      ["ArmInitRotation"] = FRotator(-3.700000,114.949997,0.000000),
      ["ArmInitRotationType"] = 0,
      ["ArmInitZoomLen"] = 1200,
      ["ArmInitZoomLenType"] = 0,
      ["DefaultFOV"] = 90,
      ["ZoomMaxLen"] = 1200,
      ["ZoomMinLen"] = 1200,
      ["ZoomStep"] = 40,
      ["bInitRotationCollisionTest"] = false,
    },
    ["BlendConfig"] = {
      ["BlendExp"] = 2,
      ["BlendFunc"] = 3,
      ["BlendTime"] = 1,
      ["CameraBlendCurve"] = "",
      ["bIsUseCurveForBlend"] = false,
      ["bLockOutgoing"] = false,
    },
    ["BlendOutConfig"] = {
      ["BlendExp"] = 2,
      ["BlendFunc"] = 3,
      ["BlendTime"] = 1,
      ["CameraBlendCurve"] = "",
      ["bIsUseCurveForBlend"] = false,
      ["bLockOutgoing"] = false,
    },
    ["CurveConfig"] = {
      ["CamRotationOffsetCurve"] = "",
      ["FOVCurve"] = "",
      ["SocketLocationOffsetCurve"] = "",
      ["TargetLocationOffsetCurve"] = "",
      ["bUseCamRotationOffsetCurve"] = false,
      ["bUseFOVCurve"] = false,
      ["bUseSocketLocationOffsetCurve"] = false,
      ["bUseTargetLocationOffsetCurve"] = false,
    },
    ["LerpConfig"] = {
      ["ArmLengthLerpCurve"] = "",
      ["CameraBoneOffsetSmoothTime"] = 0.5,
      ["CameraXOYHalftime"] = 1,
      ["CameraZHalftime"] = 0,
      ["CollisionSpringCompressSpeed"] = 0,
      ["CollisionSpringReleaseSpeed"] = 0,
      ["HalfLife"] = 0,
      ["LerpTime"] = 0,
      ["bEnableArmLengthLerp"] = false,
      ["bEnableCameraLag"] = false,
      ["bEnableCameraXOYLag"] = false,
      ["bIsCurve"] = false,
    },
    ["RotationControlConfig"] = {
      ["ViewPitchMax"] = 90,
      ["ViewPitchMin"] = -90,
      ["ViewRollMax"] = 0,
      ["ViewRollMin"] = 0,
      ["ViewYawMax"] = 180,
      ["ViewYawMin"] = -180,
      ["bInheritPitch"] = true,
      ["bInheritRoll"] = true,
      ["bInheritYaw"] = true,
      ["bUsePawnControlRotation"] = true,
    },
    ["bAllowLoopUpInput"] = true,
    ["bAllowTurn_Axis"] = true,
    ["bAttachToMainChar"] = false,
    ["bEnableCameraBlend"] = false,
    ["bIsBasicConfigOverride"] = true,
    ["bIsLerpConfigOverride"] = false,
    ["bOVerrideCameraBlendOut"] = false,
    ["bOverrideCameraRotationConfig"] = true,
    ["bUseZoomCurve"] = false,
  },
  ["Class"] = "EP_GameControlCamera",
  ["Cluster"] = "",
  ["DName"] = "",
  ["DesignTag"] = "",
  ["ETemplateCategory"] = 0,
  ["GroupID"] = "",
  ["ID"] = "3265612381",
  ["LayerName"] = "sanyuadai3",
  ["Name"] = "EP_GameControlCamera_3265612381",
  ["Owner"] = "tengyanbo",
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-6339.000000,16608.000000,4166.000000),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
    ["Scale"] = FVector(1.000000,1.000000,1.000000),
  },
}