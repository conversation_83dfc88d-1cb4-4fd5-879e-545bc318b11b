return {
  ["ActorType"] = 40008,
  ["CType"] = "2",
  ["Class"] = "EP_NonInteractMesh",
  ["Cluster"] = "",
  ["DName"] = "Door",
  ["DesignTag"] = "",
  ["GroupID"] = "",
  ["ID"] = "654017745",
  ["LayerName"] = "LTZX_Test2",
  ["MaterialOverlay"] = "",
  ["Mesh"] = "/Game/Arts/Environment/Mesh/Building/SM_Factory001/SM_Factory001_Door_A.SM_Factory001_Door_A",
  ["MeshBlueprint"] = "",
  ["Owner"] = "xiatianle",
  ["RelativeRotation"] = FRotator(0.000000,0.000000,0.000000),
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["Dissolve"] = {
      ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveInTime"] = 0,
      ["DissolveInType"] = 0,
      ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveOutTime"] = 1.5,
      ["DissolveOutType"] = 0,
    },
    ["EnableConditionID"] = 0,
    ["ExploreElement"] = {
      ["ExtraRadius"] = 0,
      ["bEnableExtraRadius"] = false,
    },
    ["ExploreType"] = 0,
    ["ExploreValue"] = 0,
    ["InitialState"] = 1,
    ["InsType"] = 1,
    ["LoadDiceCheck"] = 0,
    ["RewardID"] = 0,
    ["SpiritualVision"] = {
    },
  },
  ["SpiritualVision"] = {
    ["ShowType"] = 0,
  },
  ["StateToTag"] = {
  },
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-6242.726074,-1491.226685,97.155289),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
    ["Scale"] = FVector(0.500000,0.500000,0.600000),
  },
  ["bBlockCamera"] = false,
  ["bEnableMeshCollision"] = false,
  ["bUseBlueprint"] = false,
}