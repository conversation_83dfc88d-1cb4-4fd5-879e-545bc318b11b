return {
  ["ActorType"] = 40053,
  ["CType"] = "2",
  ["Class"] = "EP_MeshCarrier",
  ["Cluster"] = "",
  ["DName"] = "灯4",
  ["DesignTag"] = "",
  ["DissolveMaterialBySlot"] = {
  },
  ["GroupID"] = "",
  ["ID"] = "1811583918",
  ["LayerName"] = "LD_QhtTest",
  ["Owner"] = "qihaotian",
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["Dissolve"] = {
      ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveInTime"] = 0,
      ["DissolveInType"] = 0,
      ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveOutTime"] = 1.5,
      ["DissolveOutType"] = 1,
    },
    ["EnableConditionID"] = 0,
    ["ExploreElement"] = {
      ["ExtraRadius"] = 0,
      ["bEnableExtraRadius"] = false,
    },
    ["ExploreType"] = 0,
    ["ExploreValue"] = 0,
    ["InitialState"] = 1,
    ["InsType"] = 0,
    ["LoadDiceCheck"] = 0,
    ["RewardID"] = 0,
    ["SpiritualVision"] = {
    },
  },
  ["StaticMesh_1"] = {
    ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(1.000000,1.000000,1.000000)),
    ["StaticMesh"] = "/Game/Arts/Environment/Mesh/Building/SM_Octopus_Island/Mesh/SM_Octopus_Light_04.SM_Octopus_Light_04",
  },
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-3348.498566,-42.871622,-90.134211),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
  },
  ["bCollisionIgnoreCamera"] = false,
}