return {
  ["ActorType"] = 40008,
  ["CType"] = "2",
  ["Class"] = "EP_NonInteractMesh",
  ["Cluster"] = "",
  ["DName"] = "煮老鼠柴火",
  ["DesignTag"] = "铁十字街东南角氛围",
  ["GroupID"] = "",
  ["ID"] = "3946136707",
  ["LayerName"] = "520_West_Church",
  ["MaterialOverlay"] = "",
  ["Mesh"] = "/Game/Arts/Environment/Mesh/Building/SM_Pirate_School/SM_Pirate_School_PotionRoom/SM_Pirate_School_PotionRoom_FirePlace_03.SM_Pirate_School_PotionRoom_FirePlace_03",
  ["MeshBlueprint"] = "",
  ["Name"] = "EP_NonInteractMesh_3946136707",
  ["Owner"] = "luoruilin",
  ["PreviewState"] = false,
  ["RelativeRotation"] = FRotator(0.000000,0.000000,0.000000),
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["Dissolve"] = {
      ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveInTime"] = 0,
      ["DissolveInType"] = 0,
      ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveOutTime"] = 1.5,
      ["DissolveOutType"] = 0,
    },
    ["EnableConditionID"] = 0,
    ["ExploreElement"] = {
      ["ExtraRadius"] = 0,
      ["bEnableExtraRadius"] = false,
    },
    ["ExploreType"] = 0,
    ["ExploreValue"] = 0,
    ["InitialState"] = 1,
    ["InsType"] = 1,
    ["LoadDiceCheck"] = 0,
    ["Reminder"] = {
      ["ActiveReminder"] = {
        ["CD"] = 0,
        ["ReminderID"] = 0,
      },
      ["FinishReminder"] = {
        ["CD"] = 0,
        ["ReminderID"] = 0,
      },
      ["InActiveReminder"] = {
        ["CD"] = 0,
        ["ReminderID"] = 0,
      },
      ["bSupport"] = true,
    },
    ["RewardID"] = 0,
    ["SpiritualVision"] = {
    },
  },
  ["SpiritualVision"] = {
    ["IconType"] = 0,
    ["ShaderTemplate"] = 0,
    ["ShowType"] = 0,
    ["bSpiritualDisable"] = false,
  },
  ["StateToTag"] = {
  },
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-12640.000000,23400.000000,-2220.000000),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
    ["Scale"] = FVector(0.400000,0.400000,0.400000),
  },
  ["bBlockCamera"] = false,
  ["bEnableMeshCollision"] = false,
  ["bUseBlueprint"] = false,
}