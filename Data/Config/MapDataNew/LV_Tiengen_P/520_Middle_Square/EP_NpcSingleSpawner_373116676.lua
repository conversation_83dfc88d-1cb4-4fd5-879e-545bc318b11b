return {
  ["AOIRange"] = 0,
  ["ActorType"] = 60005,
  ["AnimQueue"] = {
    ["Queue"] = {
      [1] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "A_Cat_Lick",
          ["StateName"] = "",
        },
        ["Duration"] = 6.8667,
      },
      [2] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "Cat_Idle_1",
          ["StateName"] = "",
        },
        ["Duration"] = 3.3333,
      },
      [3] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "Cat_Lie_Belly_Sleep_Start",
          ["StateName"] = "",
        },
        ["Duration"] = 0.8333,
      },
      [4] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "Cat_Lie_Belly_Sleep",
          ["StateName"] = "",
        },
        ["Duration"] = 1.6667,
      },
      [5] = {
        ["AnimType"] = {
          ["AnimID"] = "",
          ["AssetID"] = "Cat_Lie_Belly_Sleep_End",
          ["StateName"] = "",
        },
        ["Duration"] = 0.8333,
      },
    },
    ["bAutoActivate"] = true,
    ["bLoop"] = true,
  },
  ["BackUpWayPointPath"] = {
  },
  ["BindSceneActorInsID"] = "",
  ["BornChairInstanceID"] = "",
  ["CType"] = "2",
  ["Class"] = "EP_NpcSingleSpawner",
  ["Cluster"] = "",
  ["DName"] = "占坑40",
  ["DesignTag"] = "中心广场氛围",
  ["FootLocation"] = FVector(-10709.099609,-8195.766602,-1414.893188),
  ["GroupID"] = "",
  ["GroupMember"] = {
    [1] = {
      ["AnimQueue"] = {
        ["Queue"] = {
          [1] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "A_Cat_Lick",
              ["StateName"] = "",
            },
            ["Duration"] = 6.8667,
          },
          [2] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "Cat_Idle_1",
              ["StateName"] = "",
            },
            ["Duration"] = 3.3333,
          },
          [3] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "Cat_Lie_Belly_Sleep_Start",
              ["StateName"] = "",
            },
            ["Duration"] = 0.8333,
          },
          [4] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "Cat_Lie_Belly_Sleep",
              ["StateName"] = "",
            },
            ["Duration"] = 1.6667,
          },
          [5] = {
            ["AnimType"] = {
              ["AnimID"] = "",
              ["AssetID"] = "Cat_Lie_Belly_Sleep_End",
              ["StateName"] = "",
            },
            ["Duration"] = 0.8333,
          },
        },
        ["bAutoActivate"] = true,
        ["bLoop"] = true,
      },
      ["BackUpWayPointPath"] = {
      },
      ["HalfHeight"] = 0,
      ["InstanceID"] = "373116676",
      ["PatrolOffset"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeParams"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeType"] = 0,
      ["Position"] = FVector(-10709.099609,-8195.766602,-1370.893188),
      ["Rotator"] = FRotator(0.000000,110.000000,0.000000),
      ["TemplateID"] = 7212111,
      ["Voice"] = {
        ["AkAudioEvent"] = "",
        ["RandomPeriod"] = FVector2D(0.000000,0.000000),
      },
      ["bForceBornPos"] = true,
    },
  },
  ["ID"] = "373116676",
  ["LayerName"] = "520_Middle_Square",
  ["LookAtInstanceID"] = "",
  ["NpcPatrol"] = {
    ["PatrolOffset"] = FVector2D(0.000000,0.000000),
    ["PatrolScopeParams"] = FVector2D(0.000000,0.000000),
    ["PatrolScopeType"] = 0,
  },
  ["NpcSpawnerCommon"] = {
    ["NpcType"] = 1,
    ["Radius"] = 300,
    ["SpawnInterval"] = 0,
    ["bSequentialSpawn"] = false,
  },
  ["Owner"] = "luxinyi08",
  ["PathType"] = 0,
  ["RoleConfigID"] = {
    ["ConfigID"] = "7212111",
    ["Count"] = 1,
  },
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["InsType"] = 0,
  },
  ["SpiritualVision"] = {
    ["ShowType"] = 0,
  },
  ["TemplateID"] = 7212111,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-10709.099609,-8195.766602,-1370.893188),
    ["Rotator"] = FRotator(0.000000,110.000000,0.000000),
  },
  ["Voice"] = {
    ["AkAudioEvent"] = "",
    ["RandomPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["WayPathSpeed"] = 200,
  ["WayPointListV2"] = {
  },
  ["WayPointPath"] = "",
  ["bBornWalkWayPath"] = false,
  ["bForceBornPos"] = true,
  ["bPathWithNpc"] = false,
  ["bShowLinear"] = true,
  ["bSitNearestChair"] = false,
  ["bStickGround"] = true,
  ["bUse3DRotation"] = false,
}