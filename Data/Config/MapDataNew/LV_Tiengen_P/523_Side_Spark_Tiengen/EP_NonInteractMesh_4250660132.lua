return {
  ["ActorType"] = 40008,
  ["CType"] = "2",
  ["Class"] = "EP_NonInteractMesh",
  ["Cluster"] = "mouse",
  ["DName"] = "笼子",
  ["DesignTag"] = "",
  ["GroupID"] = "",
  ["ID"] = "4250660132",
  ["LayerName"] = "523_Side_Spark_Tiengen",
  ["MaterialOverlay"] = "",
  ["Mesh"] = "/Game/Arts/Environment/Mesh/Props/Animation/SM_Mousecage_001.SM_Mousecage_001",
  ["MeshBlueprint"] = "",
  ["Owner"] = "dingyuanyuan05",
  ["RelativeRotation"] = FRotator(0.000000,0.000000,0.000000),
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["Dissolve"] = {
      ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveInTime"] = 0,
      ["DissolveInType"] = 0,
      ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
      ["DissolveOutTime"] = 1.5,
      ["DissolveOutType"] = 0,
    },
    ["EnableConditionID"] = 0,
    ["ExploreElement"] = {
      ["ExtraRadius"] = 0,
      ["bEnableExtraRadius"] = false,
    },
    ["ExploreType"] = 0,
    ["ExploreValue"] = 0,
    ["InitialState"] = 1,
    ["InsType"] = 1,
    ["LoadDiceCheck"] = 0,
    ["RewardID"] = 0,
    ["SpiritualVision"] = {
    },
  },
  ["SpiritualVision"] = {
    ["ShowType"] = 0,
  },
  ["StateToTag"] = {
  },
  ["TemplateID"] = 0,
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(-27610.830078,23350.708984,-2155.175049),
    ["Rotator"] = FRotator(0.000000,4.635041,0.000000),
    ["Scale"] = FVector(1.000000,1.000000,1.000000),
  },
  ["bBlockCamera"] = false,
  ["bEnableMeshCollision"] = false,
  ["bUseBlueprint"] = false,
}