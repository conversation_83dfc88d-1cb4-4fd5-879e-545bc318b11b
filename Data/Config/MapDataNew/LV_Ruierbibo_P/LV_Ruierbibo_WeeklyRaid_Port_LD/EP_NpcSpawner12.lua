return {
  ["ActorType"] = 60004,
  ["CType"] = "2",
  ["Class"] = "EP_NpcSpawner",
  ["Cluster"] = "",
  ["DName"] = "2-049",
  ["DesignTag"] = "$Puppet2049",
  ["ETemplateCategory"] = 0,
  ["GroupID"] = "",
  ["GroupMember"] = {
    [1] = {
      ["AnimQueue"] = {
        ["Queue"] = {
        },
        ["bAutoActivate"] = false,
        ["bLoop"] = false,
      },
      ["BackUpWayPointPath"] = {
      },
      ["BornIdleAssetID"] = "",
      ["DesignTag"] = "",
      ["HalfHeight"] = 0,
      ["InstanceID"] = "609486228",
      ["MaxPatrolDis"] = 0,
      ["MaxStayTime"] = 0,
      ["MinPatrolDis"] = 0,
      ["MinStayTime"] = 0,
      ["PatrolOffset"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeParams"] = FVector2D(0.000000,0.000000),
      ["PatrolScopeType"] = 0,
      ["PatrolStaySkillID"] = "",
      ["Position"] = FVector(31390.000000,6900.000000,238.365005),
      ["Rotator"] = FRotator(0.000000,90.000000,-0.000000),
      ["TaskCallID"] = "",
      ["TemplateID"] = 7102831,
      ["Voice"] = {
        ["AkAudioEvent"] = "",
        ["RandomPeriod"] = FVector2D(0.000000,0.000000),
      },
      ["WayPointPath"] = "",
      ["bBornWalkWayPath"] = false,
      ["bForceBornPos"] = false,
      ["bImportNpc"] = false,
    },
  },
  ["ID"] = "2737586608",
  ["LayerName"] = "LV_Ruierbibo_WeeklyRaid_Port_LD",
  ["Name"] = "EP_NpcSpawner12",
  ["NpcSpawnerCommon"] = {
    ["AggroGroupID"] = "",
    ["NpcType"] = 0,
    ["Radius"] = 300,
    ["RespawnDelay"] = 0,
    ["SpawnDelay"] = 0,
    ["TaskCallID"] = "",
    ["bRandom"] = false,
    ["bRespawn"] = false,
  },
  ["Owner"] = "chenjihong03",
  ["RoleConfigIDList"] = {
    [1] = {
      ["ConfigID"] = "7102831",
      ["Count"] = 1,
    },
  },
  ["SceneActorCommon"] = {
    ["BelongType"] = 0,
    ["InsType"] = 1,
  },
  ["TemplateID"] = 0,
  ["TemplateIDList"] = {
    [1] = 7102831,
  },
  ["TemplateName"] = "",
  ["Transform"] = {
    ["Position"] = FVector(31390.000000,6800.000000,90.000000),
    ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
  },
}