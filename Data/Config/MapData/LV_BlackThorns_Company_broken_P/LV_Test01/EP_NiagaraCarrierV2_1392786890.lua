return {
["Transform"] = {
  ["Position"] = FVector(2619.998054,2415.363613,1786.958132),
  ["Rotator"] = FRotator(0.000000,0.000000,-89.999999),
},
["ID"] = "1392786890",
["Name"] = "EP_NiagaraCarrierV2_1392786890",
["LayerName"] = "LV_Test01",
["Owner"] = "wangkuijia",
["DName"] = "",
["DesignTag"] = "爆炸特效2",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_NiagaraCarrierV2",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
  ["InitialState"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "INACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["ActiveNiagara"] = {
  ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill05/NS_Boss_SasrielSkill05_Burst.NS_Boss_SasrielSkill05_Burst",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(3.000000,3.000000,3.000000)),
},
["InactiveNiagara"] = {
  ["Niagara"] = "",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(3.000000,3.000000,3.000000)),
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40032,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}