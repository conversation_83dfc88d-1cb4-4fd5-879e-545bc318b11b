return {
["SceneTextExtend"] = {
},
["ID"] = "4082546723",
["Name"] = "EP_SceneText_4082546723",
["LayerName"] = "GJ_pre",
["Owner"] = "qihaotian",
["DName"] = "",
["DesignTag"] = "",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_SceneText",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["Transform"] = {
  ["Position"] = FVector(-15020.000000,-3570.000000,-1420.000000),
  ["Rotator"] = FRotator(31.242578,-178.700992,-6.045151),
},
["TextInfo"] = {
  ["DisplayText"] = "进入切换二阶段场景",
  ["TextColor"] = {
    ["R"] = 0.960409,
    ["G"] = 0.954860,
    ["B"] = 1,
    ["A"] = 1,
  },
  ["Justification"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "Center",
  },
  ["FadeInEffect"] = {
    ["EffectType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["TransitionDuration"] = 0,
  },
  ["FadeOutEffect"] = {
    ["EffectType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["TransitionDuration"] = 0,
  },
  ["GeneralEffect"] = {
  },
},
["FontInfo"] = {
  ["Font Family"] = "/Game/Arts/UI_2/Resource/Font/Font_Aleo.Font_Aleo",
  ["FontMaterial"] = "/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
  ["OutlineSettings"] = {
    ["OutlineSize"] = 0,
    ["Mitered Corners"] = false,
    ["bSeparateFillAlpha"] = false,
    ["bApplyOutlineToDropShadows"] = false,
    ["OutlineMaterial"] = "",
    ["OutlineColor"] = {
      ["R"] = 0.995331,
      ["G"] = 0.970334,
      ["B"] = 1,
      ["A"] = 1,
    },
  },
  ["Typeface"] = "Title",
  ["Size"] = 80.250000,
  ["LetterSpacing"] = 0,
  ["SkewAmount"] = 0,
  ["Monospacing"] = false,
  ["bMaterialIsStencil"] = false,
  ["MonospacedWidth"] = 1,
  ["FontName"] = "None",
  ["Hinting"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
},
["ShowInGame"] = true,
["Radius"] = 0,
["LoopAudioEvent"] = "/Game/Arts/Audio/Events/Plot/Plot_Common/Play_Plot_Common_Word.Play_Plot_Common_Word",
["StartAudioEvent"] = "",
["EndAudioEvent"] = "",
["AdjustCamera"] = false,
["AoiRadius"] = 3000,
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40002,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}