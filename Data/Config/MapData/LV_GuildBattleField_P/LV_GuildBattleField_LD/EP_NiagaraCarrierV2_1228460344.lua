return {
["Transform"] = {
  ["Position"] = FVector(5270.240838,-13999.975428,52.993377),
  ["Rotator"] = FRotator(0.000000,89.999999,0.000000),
},
["ID"] = "1228460344",
["LayerName"] = "LV_GuildBattleField_LD",
["Owner"] = "tangsu03",
["DName"] = "阵营A传送门",
["DesignTag"] = "",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_NiagaraCarrierV2",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "SystemCall",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["ActiveNiagara"] = {
  ["Niagara"] = "/Game/Arts/Effects/System/Transmit/New/NS_Transmit_01.NS_Transmit_01",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(1.000000,1.000000,1.000000)),
},
["InactiveNiagara"] = {
  ["Niagara"] = "",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(1.000000,1.000000,1.000000)),
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40032,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}