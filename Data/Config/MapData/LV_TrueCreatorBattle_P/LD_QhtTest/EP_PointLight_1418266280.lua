return {
["Transform"] = {
  ["Position"] = FVector(-2400.000000,-1270.000000,430.000000),
  ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
},
["ID"] = "1418266280",
["Name"] = "EP_PointLight_1418266280",
["LayerName"] = "LD_QhtTest",
["Owner"] = "qihaotian",
["DName"] = "",
["DesignTag"] = "",
["Cluster"] = "dmglight",
["CType"] = "2",
["Class"] = "EP_PointLight",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "SystemCall",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["LightParam"] = {
  ["Intensity"] = 13.226669,
  ["LightColor"] = FColor(0,255,0,255),
  ["AttenuationRadius"] = 850,
  ["SourceRadius"] = 0,
  ["SoftSourceRadius"] = 85,
  ["SourceLength"] = 0,
  ["IntensityUnits"] = {
    ["EnumValue"] = 3,
    ["EnumName"] = "EV",
  },
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40100,
["TemplateID"] = 10,
["GroupID"] = "3038057593",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}