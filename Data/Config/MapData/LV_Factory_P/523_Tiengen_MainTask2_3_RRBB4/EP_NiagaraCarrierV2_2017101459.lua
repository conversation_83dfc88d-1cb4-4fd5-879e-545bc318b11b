return {
["Transform"] = {
  ["Position"] = FVector(1490.000000,-360.000000,-40.000000),
  ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
},
["ID"] = "2017101459",
["Name"] = "EP_NiagaraCarrierV2_2017101459",
["LayerName"] = "523_Tiengen_MainTask2_3_RRBB4",
["Owner"] = "qiaofengying",
["DName"] = "特效·白雾",
["DesignTag"] = "",
["Cluster"] = "wu",
["CType"] = "2",
["Class"] = "EP_NiagaraCarrierV2",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "SystemCall",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["ActiveNiagara"] = {
  ["Niagara"] = "/Game/ArtsTest/TA/Lankeying/Level/autumn/niagara/niagara/NS_GroundSmoke_Loop02.NS_GroundSmoke_Loop02",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(5.000000,5.000000,5.000000)),
},
["InactiveNiagara"] = {
  ["Niagara"] = "",
  ["bAllowScalability"] = false,
  ["NiagaraTickBehavior"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "UsePrereqs",
  },
  ["RelativeTransform"] = FTransform(FQuat(0.000000,-0.000000,0.000000,1.000000),FVector(0.000000,0.000000,0.000000),FVector(1.000000,1.000000,1.000000)),
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40032,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}