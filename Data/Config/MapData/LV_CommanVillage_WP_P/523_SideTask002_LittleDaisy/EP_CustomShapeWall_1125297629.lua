return {
["Transform"] = {
  ["Position"] = FVector(740.000000,15060.000000,-790.000000),
  ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
},
["ID"] = "1125297629",
["Name"] = "EP_CustomShapeWall_1125297629",
["LayerName"] = "523_SideTask002_LittleDaisy",
["Owner"] = "liusongfei",
["DName"] = "【位面边界】花田后侧到别墅后方路口",
["DesignTag"] = "",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_CustomShapeWall",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["WallType"] = {
  ["EnumValue"] = 2,
  ["EnumName"] = "InfinitelyHigh",
},
["IsInfinitelyLow"] = true,
["Points"] = {
  [1] = FVector(2010.000000,11170.000000,-670.000000),
  [2] = FVector(1610.000000,11590.000000,-270.000000),
  [3] = FVector(1560.000000,11840.000000,-620.000000),
  [4] = FVector(1570.000000,11860.000000,-610.000000),
  [5] = FVector(1090.000000,12350.000000,-530.000000),
  [6] = FVector(910.000000,12490.000000,-530.000000),
  [7] = FVector(210.000000,12830.000000,-560.000000),
  [8] = FVector(-470.000000,13740.000000,-660.000000),
  [9] = FVector(-950.000000,14050.000000,-690.000000),
  [10] = FVector(-1210.000000,14150.000000,-690.000000),
  [11] = FVector(-1850.000000,14220.000000,-690.000000),
  [12] = FVector(-2300.000000,14190.000000,-690.000000),
  [13] = FVector(-2450.000000,14140.000000,-690.000000),
  [14] = FVector(-2430.000000,14790.000000,810.000000),
  [15] = FVector(-2080.000000,14860.000000,810.000000),
  [16] = FVector(-1980.000000,15090.000000,810.000000),
  [17] = FVector(-1570.000000,15390.000000,-1080.000000),
  [18] = FVector(-1190.000000,15640.000000,-90.000000),
  [19] = FVector(-810.000000,15960.000000,810.000000),
  [20] = FVector(540.000000,15340.000000,630.000000),
  [21] = FVector(960.000000,15150.000000,550.000000),
  [22] = FVector(1050.000000,15100.000000,530.000000),
  [23] = FVector(1280.000000,15070.000000,1330.000000),
  [24] = FVector(1270.000000,14890.000000,1330.000000),
  [25] = FVector(1640.000000,14900.000000,1330.000000),
  [26] = FVector(1640.000000,14580.000000,1330.000000),
  [27] = FVector(1620.000000,14190.000000,-870.000000),
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40150,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}