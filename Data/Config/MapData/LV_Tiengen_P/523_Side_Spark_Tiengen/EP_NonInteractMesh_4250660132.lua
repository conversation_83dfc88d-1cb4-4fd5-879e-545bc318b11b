return {
["Transform"] = {
  ["Position"] = FVector(-27610.830891,23350.709599,-2155.175138),
  ["Rotator"] = FRotator(0.000000,4.635041,0.000000),
  ["Scale"] = FVector(1.000000,1.000000,1.000000),
},
["ID"] = "4250660132",
["LayerName"] = "523_Side_Spark_Tiengen",
["Owner"] = "dingyuanyuan05",
["DName"] = "笼子",
["DesignTag"] = "",
["Cluster"] = "mouse",
["CType"] = "2",
["Class"] = "EP_NonInteractMesh",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "SystemCall",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["RelativeRotation"] = FRotator(0.000000,0.000000,0.000000),
["bUseBlueprint"] = false,
["Mesh"] = "/Game/Arts/Environment/Mesh/Props/Animation/SM_Mousecage_001.SM_Mousecage_001",
["bEnableMeshCollision"] = false,
["bBlockCamera"] = false,
["MaterialOverlay"] = "",
["MeshBlueprint"] = "",
["StateToTag"] = {
},
["SpiritualVision"] = {
  ["ShaderTemplate"] = 0,
  ["IconType"] = 0,
  ["bSpiritualDisable"] = false,
  ["ShowType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Always",
  },
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40008,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}