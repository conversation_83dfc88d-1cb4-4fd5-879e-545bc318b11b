return {
["Transform"] = {
  ["Position"] = FVector(-4640.000000,31735.000000,-2005.000000),
  ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
},
["ID"] = "1465781004",
["Name"] = "EP_JumpPointV2_1465781004",
["LayerName"] = "LiuYangTest02",
["Owner"] = "liuyang74",
["DName"] = "",
["DesignTag"] = "",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_JumpPointV2",
["bLineTraceCamera"] = false,
["bLineTracePlayer"] = false,
["MaxDetectDistance"] = 2200,
["MinInteractDistance"] = 500,
["MaxInteractDistance"] = 2000,
["JumpSkillID"] = 8091012,
["CameraInstanceList"] = {
  [1] = "2410134847",
},
["bAdjustCamera"] = false,
["EndCameraRotator"] = FRotator(0.000000,0.000000,0.000000),
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "Private",
  },
  ["InsType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40056,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}