return {
["SceneTextExtend"] = {
},
["ID"] = "3013731256",
["Name"] = "EP_SceneText_3013731256",
["LayerName"] = "523_GaoJieShi",
["Owner"] = "wangxiaoqi03",
["DName"] = "1.15【小偷】",
["DesignTag"] = "",
["Cluster"] = "people3",
["CType"] = "2",
["Class"] = "EP_SceneText",
["SceneActorCommon"] = {
  ["BelongType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Public",
  },
  ["InsType"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "SystemCall",
  },
  ["InitialState"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "ACTIVE",
  },
  ["RewardID"] = 0,
  ["ExploreType"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "NONE",
  },
  ["Dissolve"] = {
    ["DissolveOutType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveOutTime"] = 1.500000,
    ["DissolveOutPeriod"] = FVector2D(0.000000,0.000000),
    ["DissolveInType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["DissolveInTime"] = 0,
    ["DissolveInPeriod"] = FVector2D(0.000000,0.000000),
  },
  ["LoadDiceCheck"] = 0,
  ["SpiritualVision"] = {
  },
  ["ExploreValue"] = 0,
  ["ExploreElement"] = {
    ["bEnableExtraRadius"] = false,
    ["ExtraRadius"] = 0,
  },
  ["Reminder"] = {
    ["ActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["FinishReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
    ["bSupport"] = true,
    ["InActiveReminder"] = {
      ["ReminderID"] = 0,
      ["CD"] = 0,
    },
  },
  ["EnableConditionID"] = 0,
},
["Transform"] = {
  ["Position"] = FVector(-28053.533450,11964.171597,-1913.911776),
  ["Rotator"] = FRotator(90.000000,172.874984,-126.521620),
},
["TextInfo"] = {
  ["DisplayText"] = "暴食",
  ["TextColor"] = {
    ["R"] = 1,
    ["G"] = 1,
    ["B"] = 1,
    ["A"] = 1,
  },
  ["Justification"] = {
    ["EnumValue"] = 1,
    ["EnumName"] = "Center",
  },
  ["FadeInEffect"] = {
    ["EffectType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["TransitionDuration"] = 0,
  },
  ["FadeOutEffect"] = {
    ["EffectType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "None",
    },
    ["TransitionDuration"] = 0,
  },
  ["GeneralEffect"] = {
  },
},
["FontInfo"] = {
  ["Font Family"] = "/Game/Arts/UI_Update/Resource/Font/Font_Aleo_Update.Font_Aleo_Update",
  ["FontMaterial"] = "/Game/Arts/SceneActorBP/TextBoard/FontMaterials/MI_TextBoardFont_B.MI_TextBoardFont_B",
  ["OutlineSettings"] = {
    ["OutlineSize"] = 0,
    ["Mitered Corners"] = false,
    ["bSeparateFillAlpha"] = false,
    ["bApplyOutlineToDropShadows"] = false,
    ["OutlineMaterial"] = "",
    ["OutlineColor"] = {
      ["R"] = 0,
      ["G"] = 0,
      ["B"] = 0,
      ["A"] = 1,
    },
  },
  ["Typeface"] = "Bold",
  ["Size"] = 22.500000,
  ["LetterSpacing"] = 0,
  ["SkewAmount"] = 0,
  ["Monospacing"] = false,
  ["bMaterialIsStencil"] = false,
  ["MonospacedWidth"] = 1,
  ["FontName"] = "None",
  ["Hinting"] = {
    ["EnumValue"] = 0,
    ["EnumName"] = "Default",
  },
},
["ShowInGame"] = true,
["Radius"] = 0,
["LoopAudioEvent"] = "",
["StartAudioEvent"] = "",
["EndAudioEvent"] = "",
["AdjustCamera"] = false,
["AoiRadius"] = 1200,
["TemplateName"] = "",
["ETemplateCategory"] = {
  ["EnumValue"] = 0,
  ["EnumName"] = "None",
},
["ActorType"] = 40002,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}