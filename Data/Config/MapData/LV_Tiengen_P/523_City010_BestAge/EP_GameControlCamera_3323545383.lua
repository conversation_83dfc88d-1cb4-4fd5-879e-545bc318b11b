return {
["Transform"] = {
  ["Position"] = FVector(1206.173795,27395.343185,-2448.217127),
  ["Rotator"] = FRotator(0.000000,0.000000,0.000000),
  ["Scale"] = FVector(1.000000,1.000000,1.000000),
},
["ID"] = "3323545383",
["Name"] = "EP_GameControlCamera_3323545383",
["LayerName"] = "523_City010_BestAge",
["Owner"] = "luxinyi08",
["DName"] = "回工厂找到福戈镜头",
["DesignTag"] = "",
["Cluster"] = "",
["CType"] = "2",
["Class"] = "EP_GameControlCamera",
["CameraConfig"] = {
  ["bIsBasicConfigOverride"] = true,
  ["BasicConfig"] = {
    ["DefaultFOV"] = 90,
    ["ZoomMinLen"] = 0,
    ["ZoomMaxLen"] = 0,
    ["ZoomStep"] = 0,
    ["ArmInitRotationType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "USE_MODE_INIT_ARM_ROT",
    },
    ["ArmInitRotation"] = FRotator(-27.600000,100.000002,0.000000),
    ["bInitRotationCollisionTest"] = false,
    ["ArmInitZoomLenType"] = {
      ["EnumValue"] = 0,
      ["EnumName"] = "USE_MODE_CONFIG",
    },
    ["ArmInitZoomLen"] = 0,
  },
  ["bIsLerpConfigOverride"] = false,
  ["LerpConfig"] = {
    ["bEnableCameraLag"] = false,
    ["CameraBoneOffsetSmoothTime"] = 0,
    ["CameraZHalftime"] = 0,
    ["bEnableCameraXOYLag"] = false,
    ["CameraXOYHalftime"] = 0,
    ["bEnableArmLengthLerp"] = false,
    ["bIsCurve"] = false,
    ["HalfLife"] = 0,
    ["LerpTime"] = 0,
    ["ArmLengthLerpCurve"] = "",
    ["CollisionSpringReleaseSpeed"] = 0,
    ["CollisionSpringCompressSpeed"] = 0,
  },
  ["bEnableCameraBlend"] = false,
  ["BlendConfig"] = {
    ["BlendTime"] = 1,
    ["bIsUseCurveForBlend"] = false,
    ["CameraBlendCurve"] = "",
    ["BlendFunc"] = {
      ["EnumValue"] = 3,
      ["EnumName"] = "VTBlend_EaseOut",
    },
    ["BlendExp"] = 2,
    ["bLockOutgoing"] = false,
  },
  ["bOVerrideCameraBlendOut"] = false,
  ["BlendOutConfig"] = {
    ["BlendTime"] = 1,
    ["bIsUseCurveForBlend"] = false,
    ["CameraBlendCurve"] = "",
    ["BlendFunc"] = {
      ["EnumValue"] = 3,
      ["EnumName"] = "VTBlend_EaseOut",
    },
    ["BlendExp"] = 2,
    ["bLockOutgoing"] = false,
  },
  ["bUseZoomCurve"] = false,
  ["CurveConfig"] = {
    ["bUseCamRotationOffsetCurve"] = false,
    ["CamRotationOffsetCurve"] = "",
    ["bUseTargetLocationOffsetCurve"] = false,
    ["TargetLocationOffsetCurve"] = "",
    ["bUseSocketLocationOffsetCurve"] = false,
    ["SocketLocationOffsetCurve"] = "",
    ["bUseFOVCurve"] = false,
    ["FOVCurve"] = "",
  },
  ["bOverrideCameraRotationConfig"] = true,
  ["RotationControlConfig"] = {
    ["bUsePawnControlRotation"] = true,
    ["bInheritPitch"] = true,
    ["bInheritYaw"] = true,
    ["bInheritRoll"] = true,
    ["ViewPitchMin"] = -89.999001,
    ["ViewPitchMax"] = 89.999001,
    ["ViewYawMin"] = -179.998993,
    ["ViewYawMax"] = 179.998993,
    ["ViewRollMin"] = 0,
    ["ViewRollMax"] = 0,
  },
  ["bAttachToMainChar"] = false,
  ["bAllowLoopUpInput"] = true,
  ["bAllowTurn_Axis"] = true,
},
["Preview"] = {
},
["TemplateName"] = "",
["ActorType"] = 60002,
["TemplateID"] = 0,
["GroupID"] = "",
["GroupChilds"] = {
},
["GroupChildsMap"] = {
},
["CurGroupChildIndex"] = 0,
}