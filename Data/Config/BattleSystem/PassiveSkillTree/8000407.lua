BSAPS[8000407] = {
  [1] = {
    ["ID"] = 8000407,
    ["CheckNotInSkillSlot"] = false,
    ["PermanentPassive"] = true,
    ["Edges"] = {
      [1] = {
        ["EndNode"] = 2,
        ["StartNode"] = 1,
        ["IsTrue"] = true,
      },
      [2] = {
        ["EndNode"] = 3,
        ["StartNode"] = 2,
        ["IsTrue"] = true,
      },
      [3] = {
        ["EndNode"] = 1,
        ["StartNode"] = 4,
        ["IsTrue"] = true,
      },
    },
    ["PassvieData"] = {
      ["DirectTasks"] = {
      },
      ["Events"] = {
        [1] = {
          ["NodeIndex"] = 4,
        },
      },
    },
    ["Nodes"] = {
      [1] = {
        ["OutEdges"] = {
          [1] = 1,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 1,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = true,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "PassiveSkillBranch",
                ["Branch"] = {
                  ["bIsNot"] = false,
                  ["AttributeComparator"] = {
                    ["AttributeValue"] = {
                      ["Denominator"] = "None",
                      ["Molecular"] = "InBattle",
                      ["Proportion"] = 1,
                    },
                    ["Sign"] = 4,
                    ["Value"] = 0,
                  },
                  ["CheckTarget"] = 1,
                  ["Name"] = "CompareAttribute",
                  ["UberGraphFrame"] = {
                  },
                },
              },
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 0,
            },
            ["InEdges"] = {
              [1] = 2,
            },
          },
        },
        ["InEdges"] = {
          [1] = 3,
        },
      },
      [2] = {
        ["OutEdges"] = {
          [1] = 2,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 2,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "CheckSkillTags",
                ["FilterSkillTags"] = {
                  [1] = "SkillTag.Displacement",
                },
              },
              [2] = {
                ["CondtionType"] = "AttackSkillType",
                ["SkillTypes"] = 2,
              },
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 1,
            },
            ["InEdges"] = {
              [1] = 0,
            },
          },
        },
        ["InEdges"] = {
          [1] = 1,
        },
      },
      [3] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 3,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["MaxAddLayer"] = 10,
              ["NetType"] = 0,
              ["AddLayer"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 1,
              },
              ["TriggerMethod"] = 1,
              ["BuffAssets"] = {
                [1] = 8100421,
              },
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 14,
              ["OutputDatas"] = {
              },
              ["LevelType"] = 0,
              ["BuffLevel"] = 0,
              ["AddStrategy"] = 0,
              ["TriggerType"] = 1,
              ["CollisionTargetInfos"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CollisionResults",
                },
              },
              ["Duration"] = 1,
              ["LifeType"] = 0,
              ["Index"] = 0,
              ["OverLife"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = -1,
              },
              ["StartTime"] = 0,
              ["BuffTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 16,
              ["TriggerSignificance"] = 10,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "Number",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "OverLife",
                },
                [3] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CurrentLife",
                },
                [4] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
              },
            },
            ["InEdges"] = {
              [1] = 1,
            },
          },
        },
        ["InEdges"] = {
          [1] = 2,
        },
      },
      [4] = {
        ["OutEdges"] = {
          [1] = 3,
        },
        ["NodeType"] = "Events",
        ["Events"] = {
          [1] = {
            ["EventGroup"] = {
              [1] = {
                ["EventType"] = "OnSkillStart",
              },
            },
            ["NodeIndex"] = 4,
            ["ConditionGroup"] = {
            },
            ["OutEdges"] = {
              [1] = 2,
            },
            ["ASAEventGroup"] = {
            },
            ["InEdges"] = {
            },
          },
        },
        ["InEdges"] = {
        },
      },
    },
    ["RootNodes"] = {
      [1] = 4,
    },
  },
}
return BSAPS[8000407]