BSAPS[8060104] = {
  [1] = {
    ["ID"] = 8060104,
    ["CheckNotInSkillSlot"] = false,
    ["PermanentPassive"] = true,
    ["Edges"] = {
      [1] = {
        ["EndNode"] = 2,
        ["StartNode"] = 1,
        ["IsTrue"] = true,
      },
      [2] = {
        ["EndNode"] = 1,
        ["StartNode"] = 3,
        ["IsTrue"] = true,
      },
      [3] = {
        ["EndNode"] = 4,
        ["StartNode"] = 1,
        ["IsTrue"] = true,
      },
      [4] = {
        ["EndNode"] = 3,
        ["StartNode"] = 5,
        ["IsTrue"] = true,
      },
      [5] = {
        ["EndNode"] = 6,
        ["StartNode"] = 1,
        ["IsTrue"] = true,
      },
      [6] = {
        ["EndNode"] = 7,
        ["StartNode"] = 6,
        ["IsTrue"] = true,
      },
    },
    ["PassvieData"] = {
      ["DirectTasks"] = {
      },
      ["Events"] = {
        [1] = {
          ["NodeIndex"] = 5,
        },
      },
    },
    ["Nodes"] = {
      [1] = {
        ["OutEdges"] = {
          [1] = 1,
          [2] = 3,
          [3] = 5,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 1,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "PassiveSkillBranch",
                ["Branch"] = {
                  ["bIsNot"] = false,
                  ["bUseAndLogic"] = false,
                  ["ConditionList"] = {
                    [1] = {
                      ["CompareValue"] = 0,
                      ["CheckBuffInstigator"] = 1,
                      ["CheckTarget"] = 1,
                      ["Name"] = "BuffLayer",
                      ["bIsNot"] = false,
                      ["CheckBuff"] = 8140109,
                      ["CompareSign"] = 2,
                      ["UberGraphFrame"] = {
                      },
                    },
                    [2] = {
                      ["bIsNot"] = false,
                      ["bUseAndLogic"] = true,
                      ["ConditionList"] = {
                        [1] = {
                          ["CompareValue"] = 0,
                          ["CheckBuffInstigator"] = 5,
                          ["CheckTarget"] = 1,
                          ["Name"] = "BuffLayer",
                          ["bIsNot"] = false,
                          ["CheckBuff"] = 8140101,
                          ["CompareSign"] = 4,
                          ["UberGraphFrame"] = {
                          },
                        },
                        [2] = {
                          ["CompareValue"] = 4,
                          ["CheckBuffInstigator"] = 5,
                          ["CheckTarget"] = 1,
                          ["Name"] = "BuffLayer",
                          ["bIsNot"] = false,
                          ["CheckBuff"] = 8140101,
                          ["CompareSign"] = 0,
                          ["UberGraphFrame"] = {
                          },
                        },
                      },
                      ["Name"] = "ConditionGroup",
                      ["CheckTarget"] = 1,
                    },
                  },
                  ["Name"] = "ConditionGroup",
                  ["CheckTarget"] = 1,
                },
              },
            },
            ["Probability"] = 0.35,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 0,
              [2] = 2,
              [3] = 4,
            },
            ["InEdges"] = {
              [1] = 1,
            },
          },
        },
        ["InEdges"] = {
          [1] = 2,
        },
      },
      [2] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 2,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["MaxAddLayer"] = 10,
              ["NetType"] = 0,
              ["AddLayer"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 1,
              },
              ["TriggerMethod"] = 1,
              ["BuffAssets"] = {
                [1] = 8140101,
              },
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 14,
              ["OutputDatas"] = {
              },
              ["LevelType"] = 0,
              ["BuffLevel"] = 0,
              ["AddStrategy"] = 0,
              ["TriggerType"] = 1,
              ["CollisionTargetInfos"] = {
              },
              ["Duration"] = 0.9999,
              ["LifeType"] = 0,
              ["Index"] = 0,
              ["OverLife"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 10,
              },
              ["StartTime"] = 0,
              ["BuffTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 1,
              ["TriggerSignificance"] = 10,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "Number",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "OverLife",
                },
                [3] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CurrentLife",
                },
                [4] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
              },
            },
            ["InEdges"] = {
              [1] = 0,
            },
          },
        },
        ["InEdges"] = {
          [1] = 1,
        },
      },
      [3] = {
        ["OutEdges"] = {
          [1] = 2,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 3,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "CheckSkillTags",
                ["FilterSkillTags"] = {
                  [1] = "SkillTag.Attack",
                },
              },
              [2] = {
                ["CondtionType"] = "AttackSkillType",
                ["SkillTypes"] = 2,
              },
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 1,
            },
            ["InEdges"] = {
              [1] = 3,
            },
          },
        },
        ["InEdges"] = {
          [1] = 4,
        },
      },
      [4] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 4,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["MaxAddLayer"] = 10,
              ["NetType"] = 0,
              ["AddLayer"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 1,
              },
              ["TriggerMethod"] = 1,
              ["BuffAssets"] = {
                [1] = 8140109,
              },
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 14,
              ["OutputDatas"] = {
              },
              ["LevelType"] = 0,
              ["BuffLevel"] = 0,
              ["AddStrategy"] = 0,
              ["TriggerType"] = 1,
              ["CollisionTargetInfos"] = {
              },
              ["Duration"] = 0.9999,
              ["LifeType"] = 0,
              ["Index"] = 0,
              ["OverLife"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 25,
              },
              ["StartTime"] = 0,
              ["BuffTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 1,
              ["TriggerSignificance"] = 10,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "Number",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "OverLife",
                },
                [3] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CurrentLife",
                },
                [4] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
              },
            },
            ["InEdges"] = {
              [1] = 2,
            },
          },
        },
        ["InEdges"] = {
          [1] = 3,
        },
      },
      [5] = {
        ["OutEdges"] = {
          [1] = 4,
        },
        ["NodeType"] = "Events",
        ["Events"] = {
          [1] = {
            ["EventGroup"] = {
              [1] = {
                ["EventType"] = "OnAfterApplyDamage",
              },
            },
            ["NodeIndex"] = 5,
            ["ConditionGroup"] = {
            },
            ["OutEdges"] = {
              [1] = 3,
            },
            ["ASAEventGroup"] = {
            },
            ["InEdges"] = {
            },
          },
        },
        ["InEdges"] = {
        },
      },
      [6] = {
        ["OutEdges"] = {
          [1] = 6,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 6,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 1,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 5,
            },
            ["InEdges"] = {
              [1] = 4,
            },
          },
        },
        ["InEdges"] = {
          [1] = 5,
        },
      },
      [7] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 7,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["MaxAddLayer"] = 10,
              ["NetType"] = 0,
              ["AddLayer"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 1,
              },
              ["TriggerMethod"] = 1,
              ["BuffAssets"] = {
                [1] = 8140102,
              },
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 14,
              ["OutputDatas"] = {
              },
              ["LevelType"] = 0,
              ["BuffLevel"] = 0,
              ["AddStrategy"] = 0,
              ["TriggerType"] = 1,
              ["CollisionTargetInfos"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CollisionResults",
                },
              },
              ["Duration"] = 0.9999,
              ["LifeType"] = 0,
              ["Index"] = 0,
              ["OverLife"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 10,
              },
              ["StartTime"] = 0,
              ["BuffTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 16,
              ["TriggerSignificance"] = 10,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "Number",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "OverLife",
                },
                [3] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CurrentLife",
                },
                [4] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
              },
            },
            ["InEdges"] = {
              [1] = 5,
            },
          },
        },
        ["InEdges"] = {
          [1] = 6,
        },
      },
    },
    ["RootNodes"] = {
      [1] = 5,
    },
  },
}
return BSAPS[8060104]