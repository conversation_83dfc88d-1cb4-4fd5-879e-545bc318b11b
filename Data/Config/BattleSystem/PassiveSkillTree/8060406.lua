BSAPS[8060406] = {
  [1] = {
    ["ID"] = 8060406,
    ["CheckNotInSkillSlot"] = false,
    ["PermanentPassive"] = true,
    ["Edges"] = {
      [1] = {
        ["EndNode"] = 2,
        ["StartNode"] = 1,
        ["IsTrue"] = true,
      },
      [2] = {
        ["EndNode"] = 1,
        ["StartNode"] = 3,
        ["IsTrue"] = true,
      },
      [3] = {
        ["EndNode"] = 3,
        ["StartNode"] = 4,
        ["IsTrue"] = false,
      },
      [4] = {
        ["EndNode"] = 5,
        ["StartNode"] = 3,
        ["IsTrue"] = true,
      },
      [5] = {
        ["EndNode"] = 4,
        ["StartNode"] = 6,
        ["IsTrue"] = false,
      },
      [6] = {
        ["EndNode"] = 6,
        ["StartNode"] = 7,
        ["IsTrue"] = true,
      },
    },
    ["PassvieData"] = {
      ["DirectTasks"] = {
      },
      ["Events"] = {
        [1] = {
          ["NodeIndex"] = 7,
        },
      },
    },
    ["Nodes"] = {
      [1] = {
        ["OutEdges"] = {
          [1] = 1,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 1,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 2,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 0,
            },
            ["InEdges"] = {
              [1] = 1,
            },
          },
        },
        ["InEdges"] = {
          [1] = 2,
        },
      },
      [2] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 2,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["StartTime"] = 0,
              ["AttackStrategy"] = 0,
              ["TriggerMethod"] = 0,
              ["ImpulseOriginType"] = 0,
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [3] = {
                  ["Key"] = "Sucess",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 15,
              ["OutputDatas"] = {
              },
              ["AttackerType"] = 1,
              ["LifeType"] = 0,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageApportionment",
                },
              },
              ["AttackTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 16,
              ["CollisionTargetInfos"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CollisionResults",
                },
              },
              ["ImpulseDirection"] = {
                ["X"] = 1,
                ["Z"] = 0,
                ["Y"] = 0,
              },
              ["ImpulseOrigin"] = {
                ["OffsetTransform"] = {
                  ["bUseInputData"] = false,
                  ["InputDataID"] = 0,
                  ["ConfigValue"] = {
                    ["Translation"] = {
                      ["X"] = 0,
                      ["Z"] = 0,
                      ["Y"] = 0,
                    },
                    ["Rotation"] = {
                      ["X"] = 0,
                      ["W"] = 1,
                      ["Z"] = 0,
                      ["Y"] = 0,
                    },
                    ["Scale3D"] = {
                      ["X"] = 1,
                      ["Z"] = 1,
                      ["Y"] = 1,
                    },
                  },
                },
                ["bUseConnection"] = false,
                ["DirectionSpecialName"] = "None",
                ["DirectionInputID"] = -1,
                ["OriginSpecialName"] = "None",
                ["DirectionSocketName"] = "None",
                ["bDirectionUseSocket"] = true,
                ["DirectionType"] = 8,
                ["OriginInputID"] = -1,
                ["OriginType"] = 1,
                ["OriginSocketName"] = "None",
                ["bOriginUseSocket"] = true,
              },
              ["Duration"] = 0.9999,
              ["Index"] = 0,
              ["NetType"] = 1,
              ["AttackActionID"] = 1,
              ["TriggerSignificance"] = 3,
              ["EffectRelativeRotation"] = {
                ["Pitch"] = 0,
                ["Roll"] = 0,
                ["Yaw"] = 0,
              },
            },
            ["InEdges"] = {
              [1] = 0,
            },
          },
        },
        ["InEdges"] = {
          [1] = 1,
        },
      },
      [3] = {
        ["OutEdges"] = {
          [1] = 2,
          [2] = 4,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 3,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "PassiveSkillBranch",
                ["Branch"] = {
                  ["CompareValue"] = 1,
                  ["CheckBuffInstigator"] = 1,
                  ["CheckTarget"] = 1,
                  ["Name"] = "BuffLayer",
                  ["bIsNot"] = false,
                  ["CheckBuff"] = 8140309,
                  ["CompareSign"] = 0,
                  ["UberGraphFrame"] = {
                  },
                },
              },
              [2] = {
                ["CondtionType"] = "AttackSkillType",
                ["SkillTypes"] = 2,
              },
            },
            ["Probability"] = 0.25,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 1,
              [2] = 3,
            },
            ["InEdges"] = {
              [1] = 2,
            },
          },
        },
        ["InEdges"] = {
          [1] = 3,
        },
      },
      [4] = {
        ["OutEdges"] = {
          [1] = 3,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 4,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "CheckSkillTags",
                ["FilterSkillTags"] = {
                  [1] = "SkillTag.Sealed",
                },
              },
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 2,
            },
            ["InEdges"] = {
              [1] = 4,
            },
          },
        },
        ["InEdges"] = {
          [1] = 5,
        },
      },
      [5] = {
        ["OutEdges"] = {
        },
        ["NodeType"] = "Tasks",
        ["Tasks"] = {
          [1] = {
            ["OutEdges"] = {
            },
            ["NodeIndex"] = 5,
            ["ConditionGroup"] = {
            },
            ["TaskData"] = {
              ["MaxAddLayer"] = 10,
              ["NetType"] = 0,
              ["AddLayer"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 1,
              },
              ["TriggerMethod"] = 1,
              ["BuffAssets"] = {
                [1] = 8140309,
              },
              ["EventTaskMap"] = {
                [1] = {
                  ["Key"] = "Start",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
                [2] = {
                  ["Key"] = "End",
                  ["Value"] = {
                    ["SelectedTaskList"] = {
                    },
                  },
                },
              },
              ["bNetMulticastEvent"] = false,
              ["ClassType"] = 14,
              ["OutputDatas"] = {
              },
              ["LevelType"] = 0,
              ["BuffLevel"] = 0,
              ["AddStrategy"] = 0,
              ["TriggerType"] = 1,
              ["CollisionTargetInfos"] = {
              },
              ["Duration"] = 0.9999,
              ["LifeType"] = 0,
              ["Index"] = 0,
              ["OverLife"] = {
                ["bUseInputData"] = false,
                ["InputDataID"] = 0,
                ["ConfigValue"] = 10,
              },
              ["StartTime"] = 0,
              ["BuffTargetInfo"] = {
                ["TargetCampType"] = 0,
                ["TargetCharacterType"] = 0,
              },
              ["TargetTypes"] = 1,
              ["TriggerSignificance"] = 10,
              ["InputDatas"] = {
                [1] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "Number",
                },
                [2] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "OverLife",
                },
                [3] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "CurrentLife",
                },
                [4] = {
                  ["ProduceDataTask"] = {
                    ["SelectedTask"] = "None",
                  },
                  ["DataDesc"] = "DamageValue",
                },
              },
            },
            ["InEdges"] = {
              [1] = 3,
            },
          },
        },
        ["InEdges"] = {
          [1] = 4,
        },
      },
      [6] = {
        ["OutEdges"] = {
          [1] = 5,
        },
        ["NodeType"] = "Conditions",
        ["Conditions"] = {
          [1] = {
            ["NodeIndex"] = 6,
            ["ASAConditionDatas"] = {
            },
            ["ConditionGroup"] = {
            },
            ["NotCD"] = false,
            ["ConditionDatas"] = {
              [1] = {
                ["CondtionType"] = "CheckBuffTags",
                ["FilterBuffTags"] = {
                },
                ["IsBuff"] = true,
              },
            },
            ["Probability"] = 1,
            ["TriggerActor"] = 0,
            ["NeedStartTrigger"] = true,
            ["OutEdges"] = {
              [1] = 4,
            },
            ["InEdges"] = {
              [1] = 5,
            },
          },
        },
        ["InEdges"] = {
          [1] = 6,
        },
      },
      [7] = {
        ["OutEdges"] = {
          [1] = 6,
        },
        ["NodeType"] = "Events",
        ["Events"] = {
          [1] = {
            ["EventGroup"] = {
              [1] = {
                ["EventType"] = "OnAfterApplyDamage",
              },
            },
            ["NodeIndex"] = 7,
            ["ConditionGroup"] = {
            },
            ["OutEdges"] = {
              [1] = 5,
            },
            ["ASAEventGroup"] = {
            },
            ["InEdges"] = {
            },
          },
        },
        ["InEdges"] = {
        },
      },
    },
    ["RootNodes"] = {
      [1] = 7,
    },
  },
}
return BSAPS[8060406]