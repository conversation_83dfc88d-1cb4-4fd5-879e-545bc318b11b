local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Effects/FX_Character/Boss_Statue_FX/Charge/NS_Statue_Charge.NS_Statue_Charge",
    [2] = "/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill06/NS_Boss_Milgongen_Skill06_Laster_loop.NS_Boss_Milgongen_Skill06_Laster_loop",
    [3] = "/Game/Arts/Character/Model/Boss/BossMilgongenStatue/SK_Boss_MilgongenStatue.SK_Boss_MilgongenStatue",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["NodeGuid"] = {
      ["A"] = -1878414282,
      ["B"] = 1286224691,
      ["C"] = -1583322474,
      ["D"] = -2053637658,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 6,
        [3] = 1,
        [4] = 5,
        [5] = 7,
        [6] = 3,
        [7] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 5,
        [4] = 6,
        [5] = 7,
        [6] = 3,
        [7] = 4,
      },
      ["Duration"] = 30.000001907349,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 541743801,
        ["B"] = 1088233959,
        ["C"] = 1248464512,
        ["D"] = -1849576212,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 30.000001907349,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 300,
            ["Duration"] = 30.000001907349,
            ["EndFrame"] = 300,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 30.000001907349,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AttackIDList"] = {
              [1] = 88105582,
            },
            ["AttackInterval"] = 4,
            ["AttackTimes"] = 7,
            ["BaseYawType"] = true,
            ["BoneName"] = "root",
            ["CheckCloseDistance"] = 0,
            ["CheckOpenDistance"] = 0,
            ["DeltaYaw"] = 0,
            ["DisplayOffset"] = 0,
            ["Duration"] = 29.966669082642,
            ["EffectTargetNum"] = 1,
            ["EndNiagaraDuration"] = 0,
            ["EndNiagaraPath"] = "None",
            ["FirstAttackDelay"] = 0,
            ["FollowSpawner"] = true,
            ["Index"] = 0,
            ["NiagaraPath"] = "/Game/Arts/Effects/FX_Character/Boss_Milgongen_FX/Skill06/NS_Boss_Milgongen_Skill06_Laster_loop.NS_Boss_Milgongen_Skill06_Laster_loop",
            ["PositionOffset"] = {
              [1] = 0,
              [2] = 0,
              [3] = 125,
            },
            ["RotateSpeed"] = 0,
            ["RotateType"] = 1,
            ["TargetSelectionRuleID"] = 1880055802,
            ["TaskType"] = 192,
            ["UberGraphFrame"] = {
            },
            ["bTargetDeadEnd"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 30.000001907349,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 29.966669082642,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 30.000001907349,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 30.000001907349,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Statue_FX/Charge/NS_Statue_Charge.NS_Statue_Charge",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 0.70000004768372,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Milgongen/Boss_Milgongen/Play_Boss_Milgongen_Statue_Link.Play_Boss_Milgongen_Statue_Link",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.70000004768372,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 30.000001907349,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Milgongen/Boss_Milgongen/Play_Boss_Milgongen_Statue_LinkLoop.Play_Boss_Milgongen_Statue_LinkLoop",
            ["BlendOutType"] = 4,
            ["Duration"] = 30.000001907349,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data