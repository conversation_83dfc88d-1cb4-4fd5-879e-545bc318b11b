local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/Monster_Shooter/montage_Monster_Shooter_Attack01.montage_Monster_Shooter_Attack01",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1064251485,
      ["B"] = 1077787832,
      ["C"] = 199695493,
      ["D"] = 8862047,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 3,
        [3] = 5,
        [4] = 1,
        [5] = 2,
        [6] = 6,
        [7] = 7,
        [8] = 8,
        [9] = 9,
        [10] = 10,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 4,
        [4] = 5,
        [5] = 3,
        [6] = 6,
        [7] = 7,
        [8] = 8,
        [9] = 9,
        [10] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 7.0000004768372,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 986098577,
        ["B"] = 1333008834,
        ["C"] = -2127065691,
        ["D"] = -1990175351,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
      },
      ["TaskMap"] = {
        [1] = {
          ["AppearanceOverride"] = 1200301,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.6666667461395,
          ["EndTime"] = 1.6666667461395,
          ["Index"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 69,
        },
        [2] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 62,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.6666667461395,
          ["EndFrame"] = 62,
          ["EndTime"] = 1.6666667461395,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1.24,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_Shooter/montage_Monster_Shooter_Attack01.montage_Monster_Shooter_Attack01",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [3] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.5,
          ["EffectPriority"] = 5,
          ["EndTime"] = 1.1333334445953,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Common/Fire_Blink/FX_NS/NS_FireBlink_Over.NS_FireBlink_Over",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 0.8,
          ["PosOffset"] = {
            ["X"] = 130,
            ["Y"] = 0,
            ["Z"] = 50,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 0.2,
            ["Y"] = 0.2,
            ["Z"] = 0.2,
          },
          ["StartTime"] = 0.63333338499069,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [4] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 0.20000001788139,
          ["EndTime"] = 0.20000001788139,
          ["Index"] = 0,
          ["StartTime"] = 0,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 118,
          ["UseStaticBlackboard"] = false,
        },
        [5] = {
          ["AttachSocket"] = "None",
          ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Mobs/Play_Monster_MobsGunman_Shot.Play_Monster_MobsGunman_Shot",
          ["BlendOutType"] = 4,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.5666667222977,
          ["EndTime"] = 1.5666667222977,
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0.66666668653488,
          ["FadeOutCurve"] = "",
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["RelativeLocation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 42,
          ["bNeedAttach"] = true,
          ["bPlayAtLast"] = false,
          ["bSetWorldLocation"] = false,
        },
        [6] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.96666669845581,
          ["EffectPriority"] = 5,
          ["EndTime"] = 5.966667175293,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Sp03/NS_Warrior_Sp03.NS_Warrior_Sp03",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = -90,
          },
          ["RotationQuat"] = {
            ["W"] = 0.7071,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = -0.7071,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [7] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.96666669845581,
          ["EffectPriority"] = 5,
          ["EndTime"] = 5.966667175293,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Sp03/NS_Warrior_Sp03.NS_Warrior_Sp03",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = -150,
          },
          ["RotationQuat"] = {
            ["W"] = 0.2588,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = -0.9659,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [8] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.96666669845581,
          ["EffectPriority"] = 5,
          ["EndTime"] = 5.966667175293,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Sp03/NS_Warrior_Sp03.NS_Warrior_Sp03",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = -30,
          },
          ["RotationQuat"] = {
            ["W"] = 0.9659,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = -0.2588,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [9] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 1.1000000238419,
          ["EffectPriority"] = 5,
          ["EndTime"] = 6.1000003814697,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 1,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/SchoolMentor/Derrick/Skill03/NS_Derrick_Skill03_01.NS_Derrick_Skill03_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 0.5,
          ["PosOffset"] = {
            ["X"] = 250,
            ["Y"] = 0,
            ["Z"] = 200,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 4,
            ["Y"] = 4,
            ["Z"] = 4,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [10] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 1.4333333969116,
          ["EffectPriority"] = 5,
          ["EndTime"] = 6.4333338737488,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Sun_FX/Skill10/New/NS_SunSkill10_01N.NS_SunSkill10_01N",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 200,
            ["Y"] = 0,
            ["Z"] = 200,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 2,
            ["Z"] = 2,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data