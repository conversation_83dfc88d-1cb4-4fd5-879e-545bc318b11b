local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/Attack/Stage01/A_Boss_Theil_Stage01_Attack03_Montage.A_Boss_Theil_Stage01_Attack03_Montage",
    [2] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A958305A00F91.88099224_Curve_5D6A958305A00F91",
    [3] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A9583B0400F92.88099224_RootMotionCurve_5D6A9583B0400F92",
    [4] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A958478000F93.88099224_Curve_5D6A958478000F93",
    [5] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A95850A600F94.88099224_RootMotionCurve_5D6A95850A600F94",
    [6] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A9585B8000F95.88099224_Curve_5D6A9585B8000F95",
    [7] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A958663A00F96.88099224_RootMotionCurve_5D6A958663A00F96",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 342374332,
      ["B"] = 1149472272,
      ["C"] = 942502285,
      ["D"] = -659773160,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 8,
        [2] = 2,
        [3] = 9,
        [4] = 5,
        [5] = 3,
        [6] = 6,
        [7] = 7,
        [8] = 1,
        [9] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 8,
        [4] = 2,
        [5] = 6,
        [6] = 9,
        [7] = 7,
        [8] = 3,
        [9] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.7000002861023,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -567150602,
        ["B"] = 1261766122,
        ["C"] = -808840291,
        ["D"] = 983851950,
      },
      ["ServerTaskEndList"] = {
        [1] = 8,
        [2] = 2,
        [3] = 9,
        [4] = 3,
        [5] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 8,
        [2] = 2,
        [3] = 9,
        [4] = 3,
        [5] = 4,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.7000002861023,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 6,
            ["BlendOutTime"] = 8,
            ["BlendOutWhenInterrupt"] = 8,
            ["ClipLength"] = 111,
            ["Duration"] = 3.7000002861023,
            ["EndFrame"] = 111,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/Attack/Stage01/A_Boss_Theil_Stage01_Attack03_Montage.A_Boss_Theil_Stage01_Attack03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0.83333337306976,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 0.30000001192093,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293272941048847,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A958305A00F91.88099224_Curve_5D6A958305A00F91",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A9583B0400F92.88099224_RootMotionCurve_5D6A9583B0400F92",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 2.3333334922791,
          ["StartTime"] = 1.7333334684372,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 0.60000002384186,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293272942909456,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A958478000F93.88099224_Curve_5D6A958478000F93",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A95850A600F94.88099224_RootMotionCurve_5D6A95850A600F94",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [4] = {
          ["EndTime"] = 3.7000002861023,
          ["StartTime"] = 3.0000002384186,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 0.70000004768372,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293272944773137,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/88099224_Curve_5D6A9585B8000F95.88099224_Curve_5D6A9585B8000F95",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099224_RootMotionCurve_5D6A958663A00F96.88099224_RootMotionCurve_5D6A958663A00F96",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [5] = {
          ["EndTime"] = 2,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "head",
            ["Duration"] = 2,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.20000001788139,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Theil/Attack01/NS_A_Boss_Theil_Attack01_prepare.NS_A_Boss_Theil_Attack01_prepare",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 3.066666841507,
          ["StartTime"] = 1.0666667222977,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.20000001788139,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Theil/Attack05/NS_A_Boss_Theil_Attack05_01.NS_A_Boss_Theil_Attack05_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -50,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 0.7,
              ["Y"] = 0.7,
              ["Z"] = 0.7,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 3.6666667461395,
          ["StartTime"] = 1.6666667461395,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.30000001192093,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Theil/Attack05/NS_A_Boss_Theil_Attack05_02.NS_A_Boss_Theil_Attack05_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 50,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 0.7,
              ["Y"] = 0.7,
              ["Z"] = 0.7,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 1.1333334445953,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 90,
            ["CheckBlock"] = true,
            ["Duration"] = 0.53333336114883,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data