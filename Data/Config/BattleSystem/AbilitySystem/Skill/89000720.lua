local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossEyesRaven_001/A_Boss_EyesRaven_Skill03_Montage.A_Boss_EyesRaven_Skill03_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1879978289,
      ["B"] = 1254652772,
      ["C"] = -1149364326,
      ["D"] = -1214181554,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 5,
        [4] = 4,
        [5] = 7,
        [6] = 6,
        [7] = 9,
        [8] = 8,
        [9] = 1,
        [10] = 10,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 5,
        [5] = 4,
        [6] = 7,
        [7] = 6,
        [8] = 9,
        [9] = 8,
        [10] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1071532240,
        ["B"] = 1244561278,
        ["C"] = -752268626,
        ["D"] = -249153665,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 94,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 3.1333334445953,
          ["EndFrame"] = 94,
          ["EndTime"] = 3.1333334445953,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossEyesRaven_001/A_Boss_EyesRaven_Skill03_Montage.A_Boss_EyesRaven_Skill03_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [2] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 0.20000001788139,
          ["EndTime"] = 0.20000001788139,
          ["Index"] = 0,
          ["StartTime"] = 0,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 118,
          ["UseStaticBlackboard"] = false,
        },
        [3] = {
          ["BoneDock"] = "None",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
          ["DecalSize"] = {
            ["X"] = 200,
            ["Y"] = 285,
            ["Z"] = 285,
          },
          ["Duration"] = 0.66666668653488,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89000720_BPT_AS_PlayDecal_C_0_5D8D2AAD7D800209.89000720_BPT_AS_PlayDecal_C_0_5D8D2AAD7D800209",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
          },
          ["EndTime"] = 1.1666667461395,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = 90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 5,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 0.5,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 210,
            ["Y"] = 500,
            ["Z"] = 0,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = false,
          ["bAttachScale"] = false,
          ["bNeedAttach"] = false,
          ["bNeedCheckGround"] = false,
        },
        [4] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 0.90000003576279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.066666841507,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Goulu/Skill03/NS_Boss_Goulu_Skill03_02_01.NS_Boss_Goulu_Skill03_02_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = -700,
            ["Y"] = -260,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.1666667461395,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [5] = {
          ["BoneDock"] = "None",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
          ["DecalSize"] = {
            ["X"] = 200,
            ["Y"] = 285,
            ["Z"] = 285,
          },
          ["Duration"] = 0.66666668653488,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89000720_BPT_AS_PlayDecal_C_2_5D8D2AAE0C60020A.89000720_BPT_AS_PlayDecal_C_2_5D8D2AAE0C60020A",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
          },
          ["EndTime"] = 1.6666667461395,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = 90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 5,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 1,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 500,
            ["Y"] = 210,
            ["Z"] = 0,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = false,
          ["bAttachScale"] = false,
          ["bNeedAttach"] = false,
          ["bNeedCheckGround"] = false,
        },
        [6] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 0.90000003576279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.566666841507,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Goulu/Skill03/NS_Boss_Goulu_Skill03_02_01.NS_Boss_Goulu_Skill03_02_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = -410,
            ["Y"] = 50,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.6666667461395,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [7] = {
          ["BoneDock"] = "None",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
          ["DecalSize"] = {
            ["X"] = 200,
            ["Y"] = 285,
            ["Z"] = 285,
          },
          ["Duration"] = 0.66666668653488,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89000720_BPT_AS_PlayDecal_C_3_5D8D2AAEBB00020B.89000720_BPT_AS_PlayDecal_C_3_5D8D2AAEBB00020B",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
          },
          ["EndTime"] = 2.1666667461395,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = 90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 5,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 1.5000001192093,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 500,
            ["Y"] = -210,
            ["Z"] = 0,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = false,
          ["bAttachScale"] = false,
          ["bNeedAttach"] = false,
          ["bNeedCheckGround"] = false,
        },
        [8] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 0.90000003576279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 3.066666841507,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Goulu/Skill03/NS_Boss_Goulu_Skill03_02_01.NS_Boss_Goulu_Skill03_02_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 10,
            ["Y"] = 50,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 2.1666667461395,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [9] = {
          ["BoneDock"] = "None",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
          ["DecalSize"] = {
            ["X"] = 200,
            ["Y"] = 285,
            ["Z"] = 285,
          },
          ["Duration"] = 0.66666668653488,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89000720_BPT_AS_PlayDecal_C_4_5D8D2AAF4B00020C.89000720_BPT_AS_PlayDecal_C_4_5D8D2AAF4B00020C",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
          },
          ["EndTime"] = 2.6666667461395,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = 90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 5,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 2,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 210,
            ["Y"] = -500,
            ["Z"] = 0,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = false,
          ["bAttachScale"] = false,
          ["bNeedAttach"] = false,
          ["bNeedCheckGround"] = false,
        },
        [10] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 0.90000003576279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 3.566666841507,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Goulu/Skill03/NS_Boss_Goulu_Skill03_02_01.NS_Boss_Goulu_Skill03_02_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 300,
            ["Y"] = -260,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 2.6666667461395,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data