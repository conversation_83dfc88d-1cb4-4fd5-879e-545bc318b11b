local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterTreeman/MonsterMoonwoman/A_Monster_Moonwoman_Attack01_Montage.A_Monster_Moonwoman_Attack01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 75801747,
      ["B"] = 1317094905,
      ["C"] = -1324631901,
      ["D"] = 2066801123,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 4,
        [3] = 5,
        [4] = 1,
        [5] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 4,
        [4] = 5,
        [5] = 3,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 2.8333334922791,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1934498906,
        ["B"] = 1114599040,
        ["C"] = -1624981595,
        ["D"] = 1561077983,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 85,
            ["Duration"] = 2.8333334922791,
            ["EndFrame"] = 85,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterTreeman/MonsterMoonwoman/A_Monster_Moonwoman_Attack01_Montage.A_Monster_Moonwoman_Attack01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 1.9000000953674,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 180,
            ["CheckBlock"] = true,
            ["Duration"] = 0.93333339691162,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 2.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_TreeWoman/Play_Vo_Monster_TreeWoman_Attack.Play_Vo_Monster_TreeWoman_Attack",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.1333334445953,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 2.1333334445953,
          ["StartTime"] = 0.76666671037674,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_TreeWoman/Play_Monster_TreeWoman_Attack.Play_Monster_TreeWoman_Attack",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3666667938232,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data