local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 2045322526,
      ["B"] = 1187101723,
      ["C"] = 1105299091,
      ["D"] = 1531566334,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.83333337306976,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -821706240,
        ["B"] = 1259057798,
        ["C"] = 1025737629,
        ["D"] = 562013935,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["MontageAsset"] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/Turn/A_Boss_Theil_Combat_Turn_180_Left_Montage.A_Boss_Theil_Combat_Turn_180_Left_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 102,
          },
        },
        [2] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["RootmotionMoveScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskType"] = 172,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data