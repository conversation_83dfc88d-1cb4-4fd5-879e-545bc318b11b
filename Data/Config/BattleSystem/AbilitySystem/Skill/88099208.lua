local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1555860544,
      ["B"] = 1086342218,
      ["C"] = 1805735326,
      ["D"] = -861289599,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.83333337306976,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -483622844,
        ["B"] = 1089258366,
        ["C"] = 62117513,
        ["D"] = -897109780,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["MontageAsset"] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/Turn/A_Boss_Theil_Combat_Turn_180_Right_Montage.A_Boss_Theil_Combat_Turn_180_Right_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 102,
          },
        },
        [2] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["RootmotionMoveScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskType"] = 172,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data