local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -587101296,
      ["B"] = 1309491885,
      ["C"] = 458978988,
      ["D"] = -1222177588,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 1,
        [3] = 2,
        [4] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.3000000715256,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 590403724,
        ["B"] = 1226754035,
        ["C"] = -860056955,
        ["D"] = 1837270978,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 1.3000000715256,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 1.3000000715256,
            ["Index"] = 0,
            ["MontageAsset"] = "/Game/Arts/Character/Animation/Boss/Raid_Boss_Goulu/A_Boss_Goulu_001_TurnLeftBack_Montage.A_Boss_Goulu_001_TurnLeftBack_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 102,
          },
        },
        [2] = {
          ["EndTime"] = 1.3000000715256,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 1.3000000715256,
            ["Index"] = 0,
            ["RootmotionMoveScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskType"] = 172,
          },
        },
        [3] = {
          ["EndTime"] = 1.3000000715256,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Goulu/Boss_Goulu/Play_Boss_Goulu_BigTurn.Play_Boss_Goulu_BigTurn",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3000000715256,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.80000007152557,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Goulu/Vo_Boss_Goulu_Shout/Play_Vo_Boss_Goulu_Turn.Play_Vo_Boss_Goulu_Turn",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.80000007152557,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data