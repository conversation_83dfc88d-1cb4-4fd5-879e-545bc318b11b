local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1612429344,
      ["B"] = 1236355462,
      ["C"] = -284721752,
      ["D"] = 1850444552,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 5,
        [3] = 1,
        [4] = 2,
        [5] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 5,
        [5] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.1333334445953,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1662972466,
        ["B"] = 1192653898,
        ["C"] = 1016129214,
        ["D"] = 1010513670,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 6,
            ["ClientTimelineIDForDash"] = 0,
            ["ClientTimelineIDForIdle"] = 0,
            ["Duration"] = 1.1333334445953,
            ["IdleMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpApprentice/Apprentice_DoubleJump_From_Idle_Montage.Apprentice_DoubleJump_From_Idle_Montage",
            ["Index"] = 0,
            ["InterruptBlendOut"] = 6,
            ["LocoInputMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpApprentice/Apprentice_DoubleJump_From_Dash_Montage.Apprentice_DoubleJump_From_Dash_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 121,
            ["bFinishMontageWhenTaskEnd"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ArgSourceMode"] = 1,
            ["Duration"] = 1.1333334445953,
            ["Index"] = 0,
            ["InterpolationMode"] = 1,
            ["MaxRotateSpeed"] = 90,
            ["MovementOutputMode"] = 1,
            ["OverrideGravityScale"] = 0,
            ["OverrideThruster"] = {
              ["X"] = -1,
              ["Y"] = -1,
              ["Z"] = -1,
            },
            ["RootmotionScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["SurfAngles"] = {
              ["W"] = 80,
              ["X"] = 0.1,
              ["Y"] = 0.5,
              ["Z"] = 10,
            },
            ["TaskType"] = 131,
            ["bAllowSurfMode"] = false,
            ["bAllowThruster"] = false,
            ["bAllowYawController"] = false,
            ["bNeedReset"] = true,
            ["bSetRootmotionScale"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0.56666672229767,
          ["StartTime"] = 0.23333334922791,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = false,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 0.33333334326744,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data