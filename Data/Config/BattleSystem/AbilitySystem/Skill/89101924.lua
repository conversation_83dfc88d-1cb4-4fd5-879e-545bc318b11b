local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/Monster_Araneid_002/A_Monster_Araneid_002_Big_Yell.A_Monster_Araneid_002_Big_Yell",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 195419869,
      ["B"] = 1203185813,
      ["C"] = -1938879813,
      ["D"] = -419184744,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 12,
        [2] = 17,
        [3] = 19,
        [4] = 2,
        [5] = 7,
        [6] = 8,
        [7] = 3,
        [8] = 9,
        [9] = 4,
        [10] = 10,
        [11] = 5,
        [12] = 11,
        [13] = 6,
        [14] = 18,
        [15] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 12,
        [3] = 17,
        [4] = 18,
        [5] = 19,
        [6] = 2,
        [7] = 7,
        [8] = 3,
        [9] = 8,
        [10] = 4,
        [11] = 9,
        [12] = 5,
        [13] = 10,
        [14] = 6,
        [15] = 11,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 10.************,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1586157921,
        ["B"] = 1139007232,
        ["C"] = -1128481639,
        ["D"] = 1332613539,
      },
      ["ServerTaskEndList"] = {
        [1] = 17,
        [2] = 20,
        [3] = 14,
        [4] = 13,
        [5] = 15,
        [6] = 16,
      },
      ["ServerTaskStartList"] = {
        [1] = 17,
        [2] = 20,
        [3] = 14,
        [4] = 13,
        [5] = 15,
        [6] = 16,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 10.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 310,
            ["Duration"] = 10.************,
            ["EndFrame"] = 310,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_Araneid_002/A_Monster_Araneid_002_Big_Yell.A_Monster_Araneid_002_Big_Yell",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 3.5000002384186,
          ["StartTime"] = 1.1666667461395,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "Body",
            ["Duration"] = 2.3333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Araneid/Yell/NS_Araneid_002_Yell_Air.NS_Araneid_002_Yell_Air",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 52.4627,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 5.6666669845581,
          ["StartTime"] = 4.3333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.3333333730698,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Araneid/Yell/NS_Araneid_002_Yell_Foolr.NS_Araneid_002_Yell_Foolr",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 420,
              ["Y"] = 23.0884,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 7.3000001907349,
          ["StartTime"] = 6.2000002861023,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.1000000238419,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Araneid/Yell/NS_Araneid_002_Yell_Foolr.NS_Araneid_002_Yell_Foolr",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 200,
              ["Y"] = 23.0884,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 8.5,
          ["StartTime"] = 7.466667175293,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.0333334207535,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Araneid/Yell/NS_Araneid_002_Yell_Foolr.NS_Araneid_002_Yell_Foolr",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 420,
              ["Y"] = 23.0884,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 9.966667175293,
          ["StartTime"] = 8.6333341598511,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.3333333730698,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Araneid/Yell/NS_Araneid_002_Yell_Foolr.NS_Araneid_002_Yell_Foolr",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 420,
              ["Y"] = 23.0884,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 3.5000002384186,
          ["StartTime"] = 1.1666667461395,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 2.3333334922791,
            ["Index"] = 0,
            ["Radius"] = 10000,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Sound_CameraShake.Sound_CameraShake_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [8] = {
          ["EndTime"] = 4.8333334922791,
          ["StartTime"] = 4.3333334922791,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 10000,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [9] = {
          ["EndTime"] = 6.7000002861023,
          ["StartTime"] = 6.2000002861023,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 10000,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [10] = {
          ["EndTime"] = 7.966667175293,
          ["StartTime"] = 7.466667175293,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 10000,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [11] = {
          ["EndTime"] = 9.1333341598511,
          ["StartTime"] = 8.6333341598511,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 10000,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [12] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 1,
            ["ShakeAsset"] = "",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [13] = {
          ["EndTime"] = 2,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["IgnoreSelf"] = true,
            ["Index"] = 0,
            ["MessageBoolArgs"] = {
            },
            ["MessageIntArgs"] = {
            },
            ["MessageName"] = "boom",
            ["MessageStringArgs"] = {
            },
            ["Radius"] = 800,
            ["SenFlowchartTargetModeList"] = 1,
            ["SendFlowchartTargetMode"] = 2,
            ["TargetSelectionRuleID"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 159,
            ["bIsTargetModeMultiSelect"] = false,
          },
        },
        [14] = {
          ["EndTime"] = 1.4000000953674,
          ["StartTime"] = 1.4000000953674,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["IgnoreSelf"] = true,
            ["Index"] = 0,
            ["MessageBoolArgs"] = {
            },
            ["MessageIntArgs"] = {
            },
            ["MessageName"] = "boom",
            ["MessageStringArgs"] = {
            },
            ["Radius"] = 500,
            ["SenFlowchartTargetModeList"] = 1,
            ["SendFlowchartTargetMode"] = 2,
            ["TargetSelectionRuleID"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 159,
            ["bIsTargetModeMultiSelect"] = false,
          },
        },
        [15] = {
          ["EndTime"] = 2.566666841507,
          ["StartTime"] = 2.566666841507,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["IgnoreSelf"] = true,
            ["Index"] = 0,
            ["MessageBoolArgs"] = {
            },
            ["MessageIntArgs"] = {
            },
            ["MessageName"] = "boom",
            ["MessageStringArgs"] = {
            },
            ["Radius"] = 1200,
            ["SenFlowchartTargetModeList"] = 1,
            ["SendFlowchartTargetMode"] = 2,
            ["TargetSelectionRuleID"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 159,
            ["bIsTargetModeMultiSelect"] = false,
          },
        },
        [16] = {
          ["EndTime"] = 3.2666668891907,
          ["StartTime"] = 3.2666668891907,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["IgnoreSelf"] = true,
            ["Index"] = 0,
            ["MessageBoolArgs"] = {
            },
            ["MessageIntArgs"] = {
            },
            ["MessageName"] = "boom",
            ["MessageStringArgs"] = {
            },
            ["Radius"] = 5000,
            ["SenFlowchartTargetModeList"] = 1,
            ["SendFlowchartTargetMode"] = 2,
            ["TargetSelectionRuleID"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 159,
            ["bIsTargetModeMultiSelect"] = false,
          },
        },
        [17] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [18] = {
          ["EndTime"] = 10.300000190735,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Skauna/Play_Boss_Skauna_Skill_UncontrolledSound.Play_Boss_Skauna_Skill_UncontrolledSound",
            ["BlendOutType"] = 4,
            ["Duration"] = 10.300000190735,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [19] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ConfigID"] = 6442001,
            ["ConfigIDs"] = {
            },
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["ParamMap"] = {
            },
            ["TDUITypeEnum"] = 0,
            ["TargetMode"] = 2,
            ["TaskType"] = 18,
            ["UIName"] = "",
            ["UIType"] = 2,
            ["bShowInPuppetSlot"] = false,
            ["bStopReminderOnStateEnd"] = false,
          },
        },
        [20] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AsideTargetType"] = 0,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TargetSelectionRuleID"] = 0,
            ["TaskType"] = 157,
            ["asideID"] = 30041201,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data