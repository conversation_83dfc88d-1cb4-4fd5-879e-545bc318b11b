local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_<PERSON><PERSON><PERSON>_Sheriff_Skill_02_a.A_BOSS_Sheriff_Skill_02_a",
    [2] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_B<PERSON>S_Sheriff_Skill_02_b.<PERSON>_B<PERSON>S_Sheriff_Skill_02_b",
    [3] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
    [4] = "/Game/Blueprint/CombatSystem/Curve/88007118_Curve_5DA892DD9E200009.88007118_Curve_5DA892DD9E200009",
    [5] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88007118_RootMotionCurve_5DA3FB5420E00018.88007118_RootMotionCurve_5DA3FB5420E00018",
    [6] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_2_Curve_5DA892DE53E0000B.ASASkillGraphStateNode_2_Curve_5DA892DE53E0000B",
    [7] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_2_RootMotionCurve_5DA3FB549A40001A.ASASkillGraphStateNode_2_RootMotionCurve_5DA3FB549A40001A",
    [8] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_3_Curve_5DA892DEE8A0000D.ASASkillGraphStateNode_3_Curve_5DA892DEE8A0000D",
    [9] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_3_RootMotionCurve_5DA3FB551480001C.ASASkillGraphStateNode_3_RootMotionCurve_5DA3FB551480001C",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
      [1] = {
        ["BindSkillID"] = 0,
        ["ConditionRelationType"] = 0,
        ["Conditions"] = {
          [1] = {
            ["AvaliableConditionTargetTypes"] = {
              [1] = 0,
            },
            ["ConditionTargetType"] = 0,
            ["ConditionType"] = 5,
            ["IsInstance"] = true,
            ["probability"] = 0.33,
          },
        },
        ["FailStateID"] = 2,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = 30603076,
          ["B"] = 1285581092,
          ["C"] = -1063730043,
          ["D"] = 2010749468,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 0,
        ["TaskMap"] = {
        },
      },
    },
    ["NodeGuid"] = {
      ["A"] = 774696148,
      ["B"] = 1200745592,
      ["C"] = -578703481,
      ["D"] = 463202633,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 7,
        [4] = 8,
        [5] = 1,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 7,
        [4] = 4,
        [5] = 8,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.3333334922791,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1060599509,
        ["B"] = 1197253354,
        ["C"] = 1683102850,
        ["D"] = -1314911321,
      },
      ["ServerTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 92,
            ["Duration"] = 2.0333335399628,
            ["EndFrame"] = 92,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.5082,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_a.A_BOSS_Sheriff_Skill_02_a",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2.4666666984558,
          ["StartTime"] = 2.0333335399628,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 13,
            ["Duration"] = 0.4333333671093,
            ["EndFrame"] = 13,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_b.A_BOSS_Sheriff_Skill_02_b",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 56,
            ["Duration"] = 1.8666667938232,
            ["EndFrame"] = 56,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.4333333969116,
          ["StartTime"] = 1.4333333969116,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = -630,
                [2] = 5870,
                [3] = -440,
              },
              ["DestType"] = 13,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.8666667938232,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/88007118_Curve_5DA892DD9E200009.88007118_Curve_5DA892DD9E200009",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88007118_RootMotionCurve_5DA3FB5420E00018.88007118_RootMotionCurve_5DA3FB5420E00018",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [6] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 720,
            ["CheckBlock"] = true,
            ["Duration"] = 1.1666667461395,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 1.5333334207535,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.4666666984558,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_xuli1.NS_skill02_xuli1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -300,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.4666666984558,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.56666672229767,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_ready1.NS_skill02_ready1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -290,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 1,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["Alpha"] = 0.5,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "spine_02",
            ["Duration"] = 0.53333336114883,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 2,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_Skill02_Fly.NS_Skill02_Fly",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
        [1] = {
          ["BindSkillID"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
            [1] = {
              ["AvaliableConditionTargetTypes"] = {
                [1] = 0,
              },
              ["ConditionTargetType"] = 0,
              ["ConditionType"] = 5,
              ["IsInstance"] = true,
              ["probability"] = 0.51,
            },
          },
          ["FailStateID"] = 4,
          ["LogicGraph"] = "None",
          ["MultiTask"] = "None",
          ["NodeGuid"] = {
            ["A"] = 609412989,
            ["B"] = 1286272899,
            ["C"] = 456564907,
            ["D"] = 1173764083,
          },
          ["SubConditions"] = {
          },
          ["SuccessStateID"] = 0,
          ["TaskMap"] = {
          },
        },
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -528508925,
        ["B"] = 1245483085,
        ["C"] = 46851249,
        ["D"] = 415463842,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 7,
        [4] = 8,
        [5] = 1,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 7,
        [4] = 4,
        [5] = 8,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.3333334922791,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -2045599545,
        ["B"] = 1172623791,
        ["C"] = -119873880,
        ["D"] = -1758809311,
      },
      ["ServerTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 92,
            ["Duration"] = 2.0333335399628,
            ["EndFrame"] = 92,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.5082,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_a.A_BOSS_Sheriff_Skill_02_a",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2.4666666984558,
          ["StartTime"] = 2.0333335399628,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 13,
            ["Duration"] = 0.4333333671093,
            ["EndFrame"] = 13,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_b.A_BOSS_Sheriff_Skill_02_b",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 56,
            ["Duration"] = 1.8666667938232,
            ["EndFrame"] = 56,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.4333333969116,
          ["StartTime"] = 1.4333333969116,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 730,
                [2] = 9260,
                [3] = -450,
              },
              ["DestType"] = 13,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.8666667938232,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_2_Curve_5DA892DE53E0000B.ASASkillGraphStateNode_2_Curve_5DA892DE53E0000B",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_2_RootMotionCurve_5DA3FB549A40001A.ASASkillGraphStateNode_2_RootMotionCurve_5DA3FB549A40001A",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [6] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 720,
            ["CheckBlock"] = true,
            ["Duration"] = 1.1666667461395,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 1.5333334207535,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.4666666984558,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_xuli1.NS_skill02_xuli1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -300,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.4666666984558,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.56666672229767,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_ready1.NS_skill02_ready1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -290,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 1,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["Alpha"] = 0.5,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "spine_02",
            ["Duration"] = 0.53333336114883,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 2,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_Skill02_Fly.NS_Skill02_Fly",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [4] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 7,
        [4] = 8,
        [5] = 1,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 7,
        [4] = 4,
        [5] = 8,
        [6] = 9,
        [7] = 2,
        [8] = 3,
        [9] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.3333334922791,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1044846693,
        ["B"] = 1178702060,
        ["C"] = -2022469459,
        ["D"] = -1660221079,
      },
      ["ServerTaskEndList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 6,
        [2] = 4,
        [3] = 5,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 92,
            ["Duration"] = 2.0333335399628,
            ["EndFrame"] = 92,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.5082,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_a.A_BOSS_Sheriff_Skill_02_a",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2.4666666984558,
          ["StartTime"] = 2.0333335399628,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 13,
            ["Duration"] = 0.4333333671093,
            ["EndFrame"] = 13,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_b.A_BOSS_Sheriff_Skill_02_b",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 56,
            ["Duration"] = 1.8666667938232,
            ["EndFrame"] = 56,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.4333333969116,
          ["StartTime"] = 1.4333333969116,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = -2060,
                [2] = 9320,
                [3] = -440,
              },
              ["DestType"] = 13,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 2.4666666984558,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.8666667938232,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_3_Curve_5DA892DEE8A0000D.ASASkillGraphStateNode_3_Curve_5DA892DEE8A0000D",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_3_RootMotionCurve_5DA3FB551480001C.ASASkillGraphStateNode_3_RootMotionCurve_5DA3FB551480001C",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [6] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 720,
            ["CheckBlock"] = true,
            ["Duration"] = 1.1666667461395,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 1.5333334207535,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.4666666984558,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_xuli1.NS_skill02_xuli1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -300,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.4666666984558,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.56666672229767,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_skill02_ready1.NS_skill02_ready1",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -290,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 1,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 2.0333335399628,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["Alpha"] = 0.5,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "spine_02",
            ["Duration"] = 0.53333336114883,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 2,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sheriff/Skill02/NS_Skill02_Fly.NS_Skill02_Fly",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data