local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/backup/A_M_Featherwit_Skill07_Montage.A_M_Featherwit_Skill07_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1966929268,
      ["B"] = 1286170315,
      ["C"] = 589636785,
      ["D"] = 1927135187,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 15,
        [4] = 2,
        [5] = 3,
        [6] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 4,
        [4] = 5,
        [5] = 15,
        [6] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.7000000476837,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 772205864,
        ["B"] = 1339245080,
        ["C"] = 670412956,
        ["D"] = -62298279,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 4,
        [4] = 7,
        [5] = 8,
        [6] = 9,
        [7] = 10,
        [8] = 11,
        [9] = 12,
        [10] = 13,
        [11] = 2,
        [12] = 14,
        [13] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 5,
        [4] = 6,
        [5] = 7,
        [6] = 8,
        [7] = 9,
        [8] = 10,
        [9] = 11,
        [10] = 12,
        [11] = 13,
        [12] = 2,
        [13] = 14,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 51,
            ["Duration"] = 1.7000000476837,
            ["EndFrame"] = 51,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/backup/A_M_Featherwit_Skill07_Montage.A_M_Featherwit_Skill07_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Acceleration"] = -1,
            ["CurSpeed"] = 0,
            ["DecayTime"] = 0.05,
            ["Deceleration"] = -1,
            ["Duration"] = 1.7000000476837,
            ["Index"] = 0,
            ["MoveMode"] = 0,
            ["TaskType"] = 99,
            ["bNeedReset"] = true,
          },
        },
        [6] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [13] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [14] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [15] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Featherwit/Play_Featherwit_Skill07_RapidFire.Play_Featherwit_Skill07_RapidFire",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.20000001788139,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data