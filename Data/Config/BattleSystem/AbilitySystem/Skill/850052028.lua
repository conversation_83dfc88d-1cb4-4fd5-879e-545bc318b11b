local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Effects/FX_Character/2049_FX/Skill01/NS_2049Skill01_Link.NS_2049Skill01_Link",
    [2] = "/Game/Arts/Character/Model/Npc/Amon/SK_Amon_001.SK_Amon_001",
  },
  ["StartNode"] = {
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["Duration"] = 15.000000953674,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -10256717,
        ["B"] = 1190252038,
        ["C"] = 1939806361,
        ["D"] = -289088413,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1986881132,
        ["B"] = 1079783674,
        ["C"] = -117933665,
        ["D"] = -456350062,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 801543008,
        ["B"] = 1195423119,
        ["C"] = -2071112832,
        ["D"] = -130766140,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data