local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 1,
        [5] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 5,
        [5] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 9.1666669845581,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1303271945,
        ["B"] = 1237381459,
        ["C"] = -196686433,
        ["D"] = 1630349915,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 9.5000009536743,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 9.5000009536743,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/MeiMeng_Dally/Skill02/NS_MeiMeng_Skill02_Dally_Warnning.NS_MeiMeng_Skill02_Dally_Warnning",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 3.9000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimSequence"] = "/Game/Arts/Character/Animation/NPC/Dunn_001_S07/A_Duun_001_S07_Skill01_03_VFX.A_Duun_001_S07_Skill01_03_VFX",
            ["AttachSocket"] = "None",
            ["AttachTime"] = -1,
            ["BPAnimPath"] = "",
            ["Duration"] = 3.9000000953674,
            ["EnableLight"] = false,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MeshName"] = "Mesh1",
            ["NeedAttach"] = false,
            ["NeedLoop"] = false,
            ["Rotation"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SkeletalMesh"] = "/Game/Arts/Character/Model/Npc/Dunn/SK_Dunn_Fresnel.SK_Dunn_Fresnel",
            ["StaticMesh"] = "None",
            ["TaskTargetType"] = 0,
            ["TaskType"] = 8,
            ["Translation"] = {
              ["X"] = -150,
              ["Y"] = -100,
              ["Z"] = 0,
            },
            ["UseLOD0"] = false,
            ["bAttachToCamera"] = false,
            ["bCopyCurFrameAnim"] = false,
            ["bFollowParentBound"] = true,
            ["bInherit"] = false,
            ["bOnlySelf"] = false,
            ["bStickGround"] = true,
            ["bUseStaticMesh"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 3.9000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimSequence"] = "/Game/Arts/Character/Animation/NPC/Dally_001_S07/A_Dally_001_S07_Skill01_02.A_Dally_001_S07_Skill01_02",
            ["AttachSocket"] = "None",
            ["AttachTime"] = -1,
            ["BPAnimPath"] = "",
            ["Duration"] = 3.9000000953674,
            ["EnableLight"] = false,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MeshName"] = "Mesh2",
            ["NeedAttach"] = false,
            ["NeedLoop"] = false,
            ["Rotation"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SkeletalMesh"] = "/Game/Arts/Character/Model/Npc/Dally/SK_Dally_Fresnel.SK_Dally_Fresnel",
            ["StaticMesh"] = "None",
            ["TaskTargetType"] = 0,
            ["TaskType"] = 8,
            ["Translation"] = {
              ["X"] = -150,
              ["Y"] = -100,
              ["Z"] = 0,
            },
            ["UseLOD0"] = false,
            ["bAttachToCamera"] = false,
            ["bCopyCurFrameAnim"] = false,
            ["bFollowParentBound"] = true,
            ["bInherit"] = false,
            ["bOnlySelf"] = false,
            ["bStickGround"] = true,
            ["bUseStaticMesh"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 3.0000002384186,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Partner_FX/Dally_001_S07/NS_Dally_001_S07_05.NS_Dally_001_S07_05",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -150,
              ["Y"] = -25,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 9.5000009536743,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Transform/Transform_Dally/Play_Transform_Dally_Skill_Call_Help_Spell_Field.Play_Transform_Dally_Skill_Call_Help_Spell_Field",
            ["BlendOutType"] = 4,
            ["Duration"] = 9.5000009536743,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 1.3333333730698,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1768671286,
        ["B"] = 1327125300,
        ["C"] = 1368611001,
        ["D"] = -1101559825,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 42222549,
        ["B"] = 1277890249,
        ["C"] = 947201193,
        ["D"] = -1898172448,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data