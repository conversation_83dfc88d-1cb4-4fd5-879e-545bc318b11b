local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossSHGJ01/A_Monster_GuanJia_SpecialIdle1.A_Monster_GuanJia_SpecialIdle1",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["NodeGuid"] = {
      ["A"] = -1256870856,
      ["B"] = 1248597495,
      ["C"] = 658884025,
      ["D"] = 1898851789,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
      },
      ["Duration"] = 2,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -630891515,
        ["B"] = 1124369946,
        ["C"] = -986264957,
        ["D"] = -521762421,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 5,
            ["BlendOutTime"] = 0,
            ["BlendOutWhenInterrupt"] = 0,
            ["ClipLength"] = 60,
            ["Duration"] = 2,
            ["EndFrame"] = 60,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSHGJ01/A_Monster_GuanJia_SpecialIdle1.A_Monster_GuanJia_SpecialIdle1",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = true,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = true,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = false,
            ["bNotInterrupt"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data