local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterGaintsSilver_001/A_Monster_Giants_Silver_001_Spawn.A_Monster_Giants_Silver_001_Spawn",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 570788622,
      ["B"] = 1089538408,
      ["C"] = -1206707315,
      ["D"] = -46561749,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.7666668891907,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1709485040,
        ["B"] = 1318586460,
        ["C"] = 604528792,
        ["D"] = 871516300,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 4.7666668891907,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 10,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 143,
            ["Duration"] = 4.7666668891907,
            ["EndFrame"] = 173,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterGaintsSilver_001/A_Monster_Giants_Silver_001_Spawn.A_Monster_Giants_Silver_001_Spawn",
            ["StartFrame"] = 30,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data