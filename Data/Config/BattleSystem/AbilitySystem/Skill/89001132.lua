local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Arbiter/Female/A_Arb_Skill10_2_Montage.A_Arb_Skill10_2_Montage",
    [2] = "/Game/Arts/Character/Animation/Player/Arbiter/Female/A_Arb_Skill10_1_Montage.A_Arb_Skill10_1_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -721677833,
      ["B"] = 1141268913,
      ["C"] = 1219839146,
      ["D"] = -1183112798,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 9,
        [2] = 3,
        [3] = 1,
        [4] = 6,
        [5] = 5,
        [6] = 2,
        [7] = 4,
        [8] = 7,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 5,
        [4] = 6,
        [5] = 9,
        [6] = 2,
        [7] = 4,
        [8] = 7,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.9333333969116,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1761859091,
        ["B"] = 1323016449,
        ["C"] = 1281894572,
        ["D"] = 182073693,
      },
      ["ServerTaskEndList"] = {
        [1] = 9,
        [2] = 8,
      },
      ["ServerTaskStartList"] = {
        [1] = 9,
        [2] = 8,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 25,
            ["Duration"] = 0.83333337306976,
            ["EndFrame"] = 25,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Arbiter/Female/A_Arb_Skill10_2_Montage.A_Arb_Skill10_2_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.9000000953674,
          ["StartTime"] = 1.0666667222977,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 25,
            ["Duration"] = 0.83333337306976,
            ["EndFrame"] = 25,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Arbiter/Female/A_Arb_Skill10_1_Montage.A_Arb_Skill10_1_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Arbiter/Play_Arbiter_Skill_JudgmentSword_Whoosh.Play_Arbiter_Skill_JudgmentSword_Whoosh",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.66666668653488,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.9333333969116,
          ["StartTime"] = 1.0666667222977,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Arbiter/Play_Arbiter_Skill_JudgmentSword_Whoosh03.Play_Arbiter_Skill_JudgmentSword_Whoosh03",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.8666667342186,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.8333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ConfigID"] = 6406272,
            ["ConfigIDs"] = {
            },
            ["Duration"] = 1.8333333730698,
            ["Index"] = 0,
            ["ParamMap"] = {
            },
            ["TDUITypeEnum"] = 0,
            ["TargetMode"] = 2,
            ["TaskType"] = 18,
            ["UIName"] = "",
            ["UIType"] = 2,
            ["bShowInPuppetSlot"] = false,
            ["bStopReminderOnStateEnd"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "hand_r",
            ["Duration"] = 0.83333337306976,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/SchoolMentor/Xio_FX/Attack/NS_Xio_Attack_Trail_01.NS_Xio_Attack_Trail_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0.5799,
              ["Y"] = -1.2342,
              ["Z"] = 60.2355,
            },
            ["RotOffset"] = {
              ["Pitch"] = 86.8797,
              ["Roll"] = -94.55,
              ["Yaw"] = -94.5941,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7053,
              ["X"] = 0.0189,
              ["Y"] = -0.7084,
              ["Z"] = -0.0195,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 1.9333333969116,
          ["StartTime"] = 1.0666667222977,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "hand_r",
            ["Duration"] = 0.8666667342186,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/SchoolMentor/Xio_FX/Attack/NS_Xio_Attack_Trail_01.NS_Xio_Attack_Trail_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0.5799,
              ["Y"] = -1.2342,
              ["Z"] = 60.2355,
            },
            ["RotOffset"] = {
              ["Pitch"] = 86.8797,
              ["Roll"] = -94.55,
              ["Yaw"] = -94.5941,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7053,
              ["X"] = 0.0189,
              ["Y"] = -0.7084,
              ["Z"] = -0.0195,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data