local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterTentacleMimic_001/A_Monster_TentacleMimic_Skill01_Montage.A_Monster_TentacleMimic_Skill01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 570788622,
      ["B"] = 1089538408,
      ["C"] = -1206707315,
      ["D"] = -46561749,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 5,
        [3] = 3,
        [4] = 4,
        [5] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 4,
        [4] = 2,
        [5] = 3,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1709485040,
        ["B"] = 1318586460,
        ["C"] = 604528792,
        ["D"] = 871516300,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 120,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 4,
          ["EndFrame"] = 120,
          ["EndTime"] = 4,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterTentacleMimic_001/A_Monster_TentacleMimic_Skill01_Montage.A_Monster_TentacleMimic_Skill01_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [2] = {
          ["Duration"] = 0,
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 0,
        },
        [3] = {
          ["Duration"] = 0,
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 0,
        },
        [4] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 0.93333339691162,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.3333334922791,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 0,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 2,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Carrion_FX/Skill01/NS_Monster_Carrion_Skill01.NS_Monster_Carrion_Skill01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 256,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.4000000953674,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = true,
          ["bBenefitEffect"] = false,
          ["bFadeoutWhenBorn"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["bWorldTransform"] = false,
          ["maxGazeDuration"] = 0,
        },
        [5] = {
          ["BoneDock"] = "root",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
          ["DecalSize"] = {
            ["X"] = 200,
            ["Y"] = 600,
            ["Z"] = 600,
          },
          ["Duration"] = 1.4000000953674,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89001511_BPT_AS_PlayDecal_C_0_5D8D2B02DFC0025F.89001511_BPT_AS_PlayDecal_C_0_5D8D2B02DFC0025F",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
          },
          ["EndTime"] = 1.5000001192093,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = -90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 0.1000000089407,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = -50,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = false,
          ["bAttachScale"] = false,
          ["bNeedAttach"] = true,
          ["bNeedCheckGround"] = false,
          ["bWorldTransform"] = false,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data