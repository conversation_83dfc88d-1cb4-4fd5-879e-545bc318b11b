local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_Skill11_Montage.A_F_Apprentice_Skill11_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1378033351,
      ["B"] = 1335207616,
      ["C"] = 1181141944,
      ["D"] = 658036756,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 9,
        [3] = 3,
        [4] = 4,
        [5] = 2,
        [6] = 10,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 9,
        [4] = 3,
        [5] = 4,
        [6] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.8000000715256,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -2053896657,
        ["B"] = 1319987451,
        ["C"] = 724800955,
        ["D"] = 2092740756,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 6,
        [4] = 7,
        [5] = 8,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 6,
        [4] = 7,
        [5] = 8,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.8000000715256,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 54,
            ["Duration"] = 1.8000000715256,
            ["EndFrame"] = 54,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_Skill11_Montage.A_F_Apprentice_Skill11_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 0.60000002384186,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.76666671037674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Apprentice_FX/Skill11/NS_ApprenticeSkill11_Key_001.NS_ApprenticeSkill11_Key_001",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 200,
              ["Z"] = 120,
            },
            ["RotOffset"] = {
              ["Pitch"] = 90,
              ["Roll"] = 75.9638,
              ["Yaw"] = 165.9638,
            },
            ["RotationQuat"] = {
              ["W"] = 0.5,
              ["X"] = 0.5,
              ["Y"] = -0.5,
              ["Z"] = 0.5,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 0.60000002384186,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.76666671037674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Apprentice_FX/Skill11/NS_ApprenticeSkill11_Key_002.NS_ApprenticeSkill11_Key_002",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 200,
              ["Z"] = 120,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 1,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0.66666668653488,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 56,
            ["UnitNo"] = 2400669,
            ["UnitType"] = 7,
          },
        },
        [6] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0.66666668653488,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 56,
            ["UnitNo"] = 860510701,
            ["UnitType"] = 3,
          },
        },
        [7] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0.66666668653488,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 56,
            ["UnitNo"] = 860510703,
            ["UnitType"] = 3,
          },
        },
        [8] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 0.70000004768372,
          ["TaskData"] = {
            ["Count"] = 1,
            ["DeltaYaw"] = 0,
            ["DirectionMode"] = 0,
            ["Duration"] = 0.66666668653488,
            ["Index"] = 0,
            ["InteractorFaction"] = 3,
            ["InteractorTemplateID"] = 2400669,
            ["LifeTime"] = 15,
            ["MaxDistance"] = 1000,
            ["Pos"] = {
              ["BasePointArgs"] = {
                ["X"] = 200,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["BasePointType"] = 1,
              ["ClockwiseRotation"] = 0,
              ["PosNum"] = 1,
              ["ShapeType_37_7F4FBB924470A9C736F3D1956C952A61"] = {
              },
            },
            ["PositionMode"] = 0,
            ["PositionOffset"] = {
              [1] = 200,
              [2] = 0,
              [3] = 0,
            },
            ["SpellFieldID"] = 860510703,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 217,
            ["bNeedCheckGround"] = true,
          },
        },
        [9] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Apprentice/Play_Apprentice_Skill11_IllusoryGate_Cast.Play_Apprentice_Skill11_IllusoryGate_Cast",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3666667938232,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 2.6666667461395,
          ["StartTime"] = 1.3000000715256,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Apprentice/Play_Apprentice_Skill11_IllusoryGate_SpellUI.Play_Apprentice_Skill11_IllusoryGate_SpellUI",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3666667938232,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data