local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -229804882,
      ["B"] = 1187888032,
      ["C"] = 1887785090,
      ["D"] = -1730847162,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 1,
        [3] = 2,
        [4] = 5,
        [5] = 8,
        [6] = 9,
        [7] = 7,
        [8] = 6,
      },
      ["ClientTaskStartList"] = {
        [1] = 5,
        [2] = 8,
        [3] = 9,
        [4] = 2,
        [5] = 3,
        [6] = 7,
        [7] = 6,
        [8] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -922437053,
        ["B"] = 1290658775,
        ["C"] = 149007490,
        ["D"] = -154610599,
      },
      ["ServerTaskEndList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0.93333339691162,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = false,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["MeshNames"] = {
              [1] = "dynamicmesh",
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [3] = {
          ["EndTime"] = 0.33333334326744,
          ["StartTime"] = 0.13333334028721,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 0,
            ["CheckBlock"] = true,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["RotateDirection"] = 4,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0.46666669845581,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 182,
          },
        },
        [5] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Common/Fire_Blink/FX_NS/NS_FireBlink_Start.NS_FireBlink_Start",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0.73333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.26666668057442,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Common/Fire_Blink/FX_NS/NS_FireBlink_Over.NS_FireBlink_Over",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["BloomParams"] = {
              ["Bloom1Size"] = 0,
              ["Bloom1Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom2Size"] = 0,
              ["Bloom2Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom3Size"] = 0,
              ["Bloom3Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom4Size"] = 0,
              ["Bloom4Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom5Size"] = 0,
              ["Bloom5Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom6Size"] = 0,
              ["Bloom6Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["BloomConvolutionBufferScale"] = 0,
              ["BloomConvolutionCenterUV"] = {
                ["X"] = 0,
                ["Y"] = 0,
              },
              ["BloomConvolutionPreFilterMax"] = 0,
              ["BloomConvolutionPreFilterMin"] = 0,
              ["BloomConvolutionPreFilterMult"] = 0,
              ["BloomConvolutionSize"] = 0,
              ["BloomConvolutionTexture"] = "None",
              ["BloomIntensity"] = 0,
              ["BloomMethod"] = 1,
              ["BloomSizeScale"] = 0,
              ["BloomThreshold"] = 0,
              ["bOverride_Bloom1Size"] = false,
              ["bOverride_Bloom1Tint"] = false,
              ["bOverride_Bloom2Size"] = false,
              ["bOverride_Bloom2Tint"] = false,
              ["bOverride_Bloom3Size"] = false,
              ["bOverride_Bloom3Tint"] = false,
              ["bOverride_Bloom4Size"] = false,
              ["bOverride_Bloom4Tint"] = false,
              ["bOverride_Bloom5Size"] = false,
              ["bOverride_Bloom5Tint"] = false,
              ["bOverride_Bloom6Size"] = false,
              ["bOverride_Bloom6Tint"] = false,
              ["bOverride_BloomConvolutionBufferScale"] = false,
              ["bOverride_BloomConvolutionCenterUV"] = false,
              ["bOverride_BloomConvolutionPreFilterMax"] = false,
              ["bOverride_BloomConvolutionPreFilterMin"] = false,
              ["bOverride_BloomConvolutionPreFilterMult"] = false,
              ["bOverride_BloomConvolutionSize"] = false,
              ["bOverride_BloomConvolutionTexture"] = false,
              ["bOverride_BloomIntensity"] = false,
              ["bOverride_BloomMethod"] = false,
              ["bOverride_BloomSizeScale"] = false,
              ["bOverride_BloomThreshold"] = false,
            },
            ["BlurInvertParameter"] = 0,
            ["BlurMaskRadiusParameter"] = 0,
            ["BlurMaskSoftnessParameter"] = 0,
            ["BlurStrideParameter"] = 0,
            ["CenterParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorSplitStrideParameter"] = 0,
            ["ContrastParameter"] = 0,
            ["DOFParams"] = {
              ["DepthBlurAmount"] = 1,
              ["DepthBlurRadius"] = 100,
              ["DepthOfFieldBladeCount"] = 5,
              ["DepthOfFieldFstop"] = 4,
              ["DepthOfFieldMinFstop"] = 1.2,
              ["FarBlurSize"] = 15,
              ["FarTransitionRegion"] = 8000,
              ["FocalDistance"] = 300,
              ["FocalRegion"] = 0,
              ["NearBlurSize"] = 15,
              ["NearTransitionRegion"] = 300,
              ["Scale"] = 1,
              ["SensorWidth"] = 60,
              ["SqueezeFactor"] = 2,
              ["bMobileHQGaussian"] = false,
            },
            ["DesaturateParameter"] = 0,
            ["Duration"] = 0.63333338499069,
            ["ExposureBias"] = 0,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FocalRegionUpdateType"] = 0,
            ["Index"] = 0,
            ["IntensityParameter"] = 0,
            ["InterruptMode"] = 0,
            ["InvertColorBParameter"] = 0,
            ["InvertColorGParameter"] = 0,
            ["InvertColorRParameter"] = 0,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0,
            ["MaskSoftnessParameter"] = 0,
            ["Material"] = "None",
            ["PostProcessSettings"] = {
              ["BloomParams"] = {
                ["BloomParams"] = {
                  ["Bloom1Size"] = 0,
                  ["Bloom1Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom2Size"] = 0,
                  ["Bloom2Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom3Size"] = 0,
                  ["Bloom3Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom4Size"] = 0,
                  ["Bloom4Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom5Size"] = 0,
                  ["Bloom5Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom6Size"] = 0,
                  ["Bloom6Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["BloomConvolutionBufferScale"] = 0,
                  ["BloomConvolutionCenterUV"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                  },
                  ["BloomConvolutionPreFilterMax"] = 0,
                  ["BloomConvolutionPreFilterMin"] = 0,
                  ["BloomConvolutionPreFilterMult"] = 0,
                  ["BloomConvolutionSize"] = 0,
                  ["BloomConvolutionTexture"] = "None",
                  ["BloomIntensity"] = 0,
                  ["BloomMethod"] = 1,
                  ["BloomSizeScale"] = 0,
                  ["BloomThreshold"] = 0,
                  ["bOverride_Bloom1Size"] = false,
                  ["bOverride_Bloom1Tint"] = false,
                  ["bOverride_Bloom2Size"] = false,
                  ["bOverride_Bloom2Tint"] = false,
                  ["bOverride_Bloom3Size"] = false,
                  ["bOverride_Bloom3Tint"] = false,
                  ["bOverride_Bloom4Size"] = false,
                  ["bOverride_Bloom4Tint"] = false,
                  ["bOverride_Bloom5Size"] = false,
                  ["bOverride_Bloom5Tint"] = false,
                  ["bOverride_Bloom6Size"] = false,
                  ["bOverride_Bloom6Tint"] = false,
                  ["bOverride_BloomConvolutionBufferScale"] = false,
                  ["bOverride_BloomConvolutionCenterUV"] = false,
                  ["bOverride_BloomConvolutionPreFilterMax"] = false,
                  ["bOverride_BloomConvolutionPreFilterMin"] = false,
                  ["bOverride_BloomConvolutionPreFilterMult"] = false,
                  ["bOverride_BloomConvolutionSize"] = false,
                  ["bOverride_BloomConvolutionTexture"] = false,
                  ["bOverride_BloomIntensity"] = false,
                  ["bOverride_BloomMethod"] = false,
                  ["bOverride_BloomSizeScale"] = false,
                  ["bOverride_BloomThreshold"] = false,
                },
              },
              ["ColorAdjustParams"] = {
                ["Contrast"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Desaturate"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorB"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorG"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorR"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["bEnableDesaturateProgress"] = false,
              },
              ["ColorSplitRadialBlurParams"] = {
                ["BlurInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["ColorSplitStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["CustomMaterialParams"] = {
                ["CustomMaterial"] = "",
                ["MainCharPositionParamName"] = "",
                ["Scalars"] = {
                },
                ["Texture"] = {
                },
                ["Vectors"] = {
                },
                ["bFixBlendWeight"] = false,
              },
              ["DOFParams"] = {
                ["DOFParams"] = {
                  ["DepthBlurAmount"] = 1,
                  ["DepthBlurRadius"] = 100,
                  ["DepthOfFieldBladeCount"] = 5,
                  ["DepthOfFieldFstop"] = 4,
                  ["DepthOfFieldMinFstop"] = 1.2,
                  ["FarBlurSize"] = 15,
                  ["FarTransitionRegion"] = 8000,
                  ["FocalDistance"] = 300,
                  ["FocalRegion"] = 0,
                  ["NearBlurSize"] = 15,
                  ["NearTransitionRegion"] = 300,
                  ["Scale"] = 1,
                  ["SensorWidth"] = 60,
                  ["SqueezeFactor"] = 2,
                  ["bMobileHQGaussian"] = false,
                },
              },
              ["DarkenParams"] = {
                ["ExposureBias"] = 0,
              },
              ["PhantomParams"] = {
                ["Direction"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Gap"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Speed"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["PostProcessType"] = 0,
              ["Priority"] = 0,
              ["RGBSplitParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["RadialBlurParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 1,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0.1,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["VignetteParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Color"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Radius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
            },
            ["PostProcessType"] = 0,
            ["Priority"] = 0,
            ["Radius"] = 0,
            ["RadiusParameter"] = 0,
            ["Scalars"] = {
              [""] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 0,
                ["Max"] = 0,
                ["Min"] = 0,
                ["ParamName"] = "",
              },
            },
            ["SoftnessParameter"] = 0,
            ["StrideParameter"] = 0,
            ["TargetType"] = 0,
            ["TaskType"] = 126,
            ["Textures"] = {
            },
            ["Vectors"] = {
            },
            ["bUseNewPostProcessSetting"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Common_Skill/Play_Common_Skill_Explore_Convey.Play_Common_Skill_Explore_Convey",
            ["BlendOutType"] = 4,
            ["Duration"] = 1,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ArgSourceMode"] = 1,
            ["Duration"] = 1,
            ["Index"] = 0,
            ["InterpolationMode"] = 1,
            ["MaxRotateSpeed"] = 90,
            ["MovementOutputMode"] = 1,
            ["OverrideGravityScale"] = 0,
            ["OverrideThruster"] = {
              ["X"] = -1,
              ["Y"] = -1,
              ["Z"] = -1,
            },
            ["RootmotionScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["SurfAngles"] = {
              ["W"] = 80,
              ["X"] = 0.1,
              ["Y"] = 0.5,
              ["Z"] = 10,
            },
            ["TaskType"] = 131,
            ["bAllowSurfMode"] = false,
            ["bAllowThruster"] = false,
            ["bAllowYawController"] = false,
            ["bNeedReset"] = true,
            ["bSetRootmotionScale"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data