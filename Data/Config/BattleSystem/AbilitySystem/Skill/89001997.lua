local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat_Sickle/A_Monster_Redhat_Sickle_Attack01_Montage.A_Monster_Redhat_Sickle_Attack01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1230084972,
      ["B"] = 1314939496,
      ["C"] = 719595911,
      ["D"] = 1352186357,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 8,
        [4] = 4,
        [5] = 5,
        [6] = 9,
        [7] = 6,
        [8] = 7,
        [9] = 10,
        [10] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 8,
        [4] = 3,
        [5] = 4,
        [6] = 9,
        [7] = 5,
        [8] = 6,
        [9] = 10,
        [10] = 7,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 5.8000001907349,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -821629379,
        ["B"] = 1099116609,
        ["C"] = -493612635,
        ["D"] = -1557617965,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 8,
        [4] = 4,
        [5] = 5,
        [6] = 9,
        [7] = 6,
        [8] = 7,
        [9] = 10,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 8,
        [3] = 3,
        [4] = 4,
        [5] = 9,
        [6] = 5,
        [7] = 6,
        [8] = 10,
        [9] = 7,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 5.8000001907349,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 174,
            ["Duration"] = 5.8000001907349,
            ["EndFrame"] = 174,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat_Sickle/A_Monster_Redhat_Sickle_Attack01_Montage.A_Monster_Redhat_Sickle_Attack01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 2.1666667461395,
          ["StartTime"] = 1.9666668176651,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 3.2666668891907,
          ["StartTime"] = 3.066666841507,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data