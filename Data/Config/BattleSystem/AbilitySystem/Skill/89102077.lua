local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterMoonwoman/A_Monster_Moonwoman_Spawn_Montage_ShiKong.A_Monster_Moonwoman_Spawn_Montage_ShiKong",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 60870921,
      ["B"] = 1193192366,
      ["C"] = -1968622431,
      ["D"] = -1614188560,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.7333335876465,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -521096135,
        ["B"] = 1126274306,
        ["C"] = -1236173402,
        ["D"] = -1630366140,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.7333335876465,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 10,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 112,
            ["Duration"] = 3.7333335876465,
            ["EndFrame"] = 112,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterMoonwoman/A_Monster_Moonwoman_Spawn_Montage_ShiKong.A_Monster_Moonwoman_Spawn_Montage_ShiKong",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 3.4666669368744,
          ["StartTime"] = 2.3666667938232,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.1000000238419,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Moonwoman_FX/Attack/NS_MoonwomanAttack_Spawn.NS_MoonwomanAttack_Spawn",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 250,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 90,
              ["Yaw"] = -180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = 0.7071,
              ["Z"] = -0.7071,
            },
            ["ScaleOffset"] = {
              ["X"] = 3,
              ["Y"] = 3,
              ["Z"] = 3,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data