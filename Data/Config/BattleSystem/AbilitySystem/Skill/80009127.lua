local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Appear.A_SeaElves_Appear",
    [2] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 983953951,
      ["B"] = 1241303419,
      ["C"] = 1184337028,
      ["D"] = -1660693093,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 12,
        [2] = 11,
        [3] = 1,
        [4] = 2,
        [5] = 3,
        [6] = 4,
        [7] = 5,
        [8] = 6,
        [9] = 7,
        [10] = 8,
        [11] = 9,
        [12] = 13,
        [13] = 10,
      },
      ["ClientTaskStartList"] = {
        [1] = 11,
        [2] = 12,
        [3] = 1,
        [4] = 13,
        [5] = 2,
        [6] = 3,
        [7] = 4,
        [8] = 5,
        [9] = 6,
        [10] = 7,
        [11] = 8,
        [12] = 9,
        [13] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 20.000001907349,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1370015392,
        ["B"] = 1112414213,
        ["C"] = -1185603932,
        ["D"] = -1655215138,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.2666668891907,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 60,
            ["Duration"] = 1.9000000953674,
            ["EndFrame"] = 60,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Appear.A_SeaElves_Appear",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 4.2666668891907,
          ["StartTime"] = 2.2666668891907,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 6.2333335876465,
          ["StartTime"] = 4.2333335876465,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 8.2000007629395,
          ["StartTime"] = 6.2000002861023,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 10.166666984558,
          ["StartTime"] = 8.1666669845581,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 12.133334159851,
          ["StartTime"] = 10.133334159851,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 14.133334159851,
          ["StartTime"] = 12.133334159851,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 16.133335113525,
          ["StartTime"] = 14.133334159851,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 18.133335113525,
          ["StartTime"] = 16.133335113525,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 20.133335113525,
          ["StartTime"] = 18.133335113525,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 80,
            ["Duration"] = 2,
            ["EndFrame"] = 80,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.3333,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/NPC/SeaElves/A_SeaElves_Idle.A_SeaElves_Idle",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [11] = {
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1.0333334207535,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Visionary_FX/Weapon/NS_Visionary_Weapon_Enter02_Blue_01.NS_Visionary_Weapon_Enter02_Blue_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 5,
              ["Y"] = 5,
              ["Z"] = 5,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 0.36666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 0.36666667461395,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [13] = {
          ["EndTime"] = 20.033334732056,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["AnimSequence"] = "",
            ["AttachSocket"] = "Hand_L_001",
            ["AttachTime"] = -1,
            ["BPAnimPath"] = "",
            ["Duration"] = 19.666667938232,
            ["EnableLight"] = false,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MeshName"] = "",
            ["NeedAttach"] = true,
            ["NeedLoop"] = false,
            ["Rotation"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 90,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SkeletalMesh"] = "/Game/Arts/Character/Model/Npc/SeaElves/Props/SK_SeaElvesProps_Pendant.SK_SeaElvesProps_Pendant",
            ["StaticMesh"] = "None",
            ["TaskTargetType"] = 0,
            ["TaskType"] = 8,
            ["Translation"] = {
              ["X"] = -5,
              ["Y"] = -5,
              ["Z"] = 0,
            },
            ["UseLOD0"] = false,
            ["bAttachToCamera"] = false,
            ["bCopyCurFrameAnim"] = false,
            ["bFollowParentBound"] = true,
            ["bInherit"] = false,
            ["bOnlySelf"] = false,
            ["bStickGround"] = false,
            ["bUseStaticMesh"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data