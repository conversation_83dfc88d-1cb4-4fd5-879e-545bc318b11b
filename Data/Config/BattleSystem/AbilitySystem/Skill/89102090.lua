local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/Monster_PlantMonster_001/A_Monster_PM_Skill01_Montage.A_Monster_PM_Skill01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1725126264,
      ["B"] = 1319269715,
      ["C"] = -1098260607,
      ["D"] = -1105135931,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 4,
        [4] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 4,
        [4] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.9000000953674,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 93297233,
        ["B"] = 1327669059,
        ["C"] = 188876721,
        ["D"] = 1735786493,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.9000000953674,
          ["StartTime"] = 1.6666667461395,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 180,
            ["CheckBlock"] = true,
            ["Duration"] = 0.23333334922791,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.9000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 0,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 57,
            ["Duration"] = 1.9000000953674,
            ["EndFrame"] = 57,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_PlantMonster_001/A_Monster_PM_Skill01_Montage.A_Monster_PM_Skill01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.9000000953674,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Plant/Play_Monster_Plant_Skill01.Play_Monster_Plant_Skill01",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.8666667938232,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data