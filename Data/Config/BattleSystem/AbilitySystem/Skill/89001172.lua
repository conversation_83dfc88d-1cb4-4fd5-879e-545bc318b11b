local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Common/Story/NPC/Boxing/Male/A_M_Boxing_Right.A_M_Boxing_Right",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1274490316,
      ["B"] = 1273743681,
      ["C"] = 1590041246,
      ["D"] = 783099979,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 1,
        [4] = 4,
        [5] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 5,
        [4] = 4,
        [5] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.1666667461395,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 69536141,
        ["B"] = 1105595406,
        ["C"] = -539927894,
        ["D"] = -1606165593,
      },
      ["ServerTaskEndList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 1,
        [4] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 4,
        [4] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 10,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 35,
            ["Duration"] = 1.1666667461395,
            ["EndFrame"] = 35,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/NPC/Boxing/Male/A_M_Boxing_Right.A_M_Boxing_Right",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0.13333334028721,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 3600,
            ["CheckBlock"] = true,
            ["Duration"] = 0.13333334028721,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0.80000007152557,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 3600,
            ["CheckBlock"] = true,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data