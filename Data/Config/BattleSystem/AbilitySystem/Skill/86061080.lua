local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_01_Montage.A_Warrior_Sword_Skill12_01_Montage",
    [2] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_02_Montage.A_Warrior_Sword_Skill12_02_Montage",
    [3] = "/Game/Blueprint/CombatSystem/Curve/86061080_Curve_5DA7EEAA55E0027A.86061080_Curve_5DA7EEAA55E0027A",
    [4] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86061080_RootMotionCurve_5DA7EEAA7600027B.86061080_RootMotionCurve_5DA7EEAA7600027B",
    [5] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_03_Montage.A_Warrior_Sword_Skill12_03_Montage",
    [6] = "/Game/Blueprint/CombatSystem/Curve/86061080_Curve_5DA7EE9FFCA0026D.86061080_Curve_5DA7EE9FFCA0026D",
    [7] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86061080_RootMotionCurve_5DA7EEA01A00026E.86061080_RootMotionCurve_5DA7EEA01A00026E",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
      [1] = {
        ["BindSkillID"] = 0,
        ["ConditionRelationType"] = 0,
        ["Conditions"] = {
          [1] = {
            ["AvaliableConditionTargetTypes"] = {
              [1] = 0,
              [2] = 3,
              [3] = 6,
            },
            ["BuffID"] = 82060028,
            ["BuffIDs"] = {
            },
            ["ConditionTargetType"] = 0,
            ["ConditionType"] = 3,
            ["IsInstance"] = true,
            ["Layer"] = 0,
            ["Sign"] = 2,
            ["bCompareSearchTargetLen"] = false,
          },
        },
        ["FailStateID"] = 0,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = 1506686639,
          ["B"] = 1336970962,
          ["C"] = -1240502128,
          ["D"] = -941368430,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 1,
        ["TaskMap"] = {
        },
      },
      [2] = {
        ["BindSkillID"] = 0,
        ["ConditionRelationType"] = 0,
        ["Conditions"] = {
          [1] = {
            ["AvaliableConditionTargetTypes"] = {
              [1] = 0,
              [2] = 3,
              [3] = 6,
            },
            ["BuffID"] = 82060028,
            ["BuffIDs"] = {
            },
            ["ConditionTargetType"] = 0,
            ["ConditionType"] = 3,
            ["IsInstance"] = true,
            ["Layer"] = 1,
            ["Sign"] = 2,
            ["bCompareSearchTargetLen"] = false,
          },
        },
        ["FailStateID"] = 0,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = -1640781455,
          ["B"] = 1287764277,
          ["C"] = 517464465,
          ["D"] = 2025989883,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 2,
        ["TaskMap"] = {
        },
      },
      [3] = {
        ["BindSkillID"] = 0,
        ["ConditionRelationType"] = 0,
        ["Conditions"] = {
          [1] = {
            ["AvaliableConditionTargetTypes"] = {
              [1] = 0,
              [2] = 3,
              [3] = 6,
            },
            ["BuffID"] = 82060028,
            ["BuffIDs"] = {
            },
            ["ConditionTargetType"] = 0,
            ["ConditionType"] = 3,
            ["IsInstance"] = true,
            ["Layer"] = 2,
            ["Sign"] = 2,
            ["bCompareSearchTargetLen"] = false,
          },
        },
        ["FailStateID"] = 0,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = -1246283073,
          ["B"] = 1200360420,
          ["C"] = 544187778,
          ["D"] = -324253017,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 3,
        ["TaskMap"] = {
        },
      },
    },
    ["NodeGuid"] = {
      ["A"] = 207426426,
      ["B"] = 1084268658,
      ["C"] = -470059073,
      ["D"] = 643112461,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 86061080,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 3,
        [3] = 6,
        [4] = 7,
        [5] = 12,
        [6] = 10,
        [7] = 11,
        [8] = 2,
        [9] = 1,
        [10] = 8,
        [11] = 9,
        [12] = 13,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 8,
        [4] = 9,
        [5] = 2,
        [6] = 3,
        [7] = 10,
        [8] = 11,
        [9] = 6,
        [10] = 7,
        [11] = 12,
        [12] = 13,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.9666669368744,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1228384590,
        ["B"] = 1216256687,
        ["C"] = -1029750389,
        ["D"] = -493262541,
      },
      ["ServerTaskEndList"] = {
        [1] = 5,
        [2] = 4,
        [3] = 3,
        [4] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 5,
        [3] = 2,
        [4] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.9666669368744,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 119,
            ["Duration"] = 3.9666669368744,
            ["EndFrame"] = 119,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_01_Montage.A_Warrior_Sword_Skill12_01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 200,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 4,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 0,
                },
                ["ConditionTargetType"] = 0,
                ["ConditionType"] = 16,
                ["IsInstance"] = false,
                ["Mode"] = 2,
                ["RuleID"] = 0,
                ["Sign"] = 2,
                ["Value"] = 0,
              },
            },
            ["ConditionRelationType"] = 1,
          },
          ["EndTime"] = 3.9666669368744,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 3.9000000953674,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/86061080_Curve_5DAF09FA7A60002F.86061080_Curve_5DAF09FA7A60002F",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86061080_RootMotionCurve_5DAF09FA7BC00030.86061080_RootMotionCurve_5DAF09FA7BC00030",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 400,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 1,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 100,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 3,
              },
            },
            ["ConditionRelationType"] = 0,
          },
          ["EndTime"] = 0.23333334922791,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = true,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 100,
                [2] = 400,
                [3] = 0,
              },
              ["DestType"] = 2,
            },
            ["Duration"] = 0.13333334028721,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.1,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200006,
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 68,
          },
        },
        [6] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.30000001192093,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [7] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.30000001192093,
          ["TaskData"] = {
            ["BloomParams"] = {
              ["Bloom1Size"] = 0,
              ["Bloom1Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom2Size"] = 0,
              ["Bloom2Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom3Size"] = 0,
              ["Bloom3Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom4Size"] = 0,
              ["Bloom4Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom5Size"] = 0,
              ["Bloom5Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom6Size"] = 0,
              ["Bloom6Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["BloomConvolutionBufferScale"] = 0,
              ["BloomConvolutionCenterUV"] = {
                ["X"] = 0,
                ["Y"] = 0,
              },
              ["BloomConvolutionPreFilterMax"] = 0,
              ["BloomConvolutionPreFilterMin"] = 0,
              ["BloomConvolutionPreFilterMult"] = 0,
              ["BloomConvolutionSize"] = 0,
              ["BloomConvolutionTexture"] = "None",
              ["BloomIntensity"] = 0,
              ["BloomMethod"] = 1,
              ["BloomSizeScale"] = 0,
              ["BloomThreshold"] = 0,
              ["bOverride_Bloom1Size"] = false,
              ["bOverride_Bloom1Tint"] = false,
              ["bOverride_Bloom2Size"] = false,
              ["bOverride_Bloom2Tint"] = false,
              ["bOverride_Bloom3Size"] = false,
              ["bOverride_Bloom3Tint"] = false,
              ["bOverride_Bloom4Size"] = false,
              ["bOverride_Bloom4Tint"] = false,
              ["bOverride_Bloom5Size"] = false,
              ["bOverride_Bloom5Tint"] = false,
              ["bOverride_Bloom6Size"] = false,
              ["bOverride_Bloom6Tint"] = false,
              ["bOverride_BloomConvolutionBufferScale"] = false,
              ["bOverride_BloomConvolutionCenterUV"] = false,
              ["bOverride_BloomConvolutionPreFilterMax"] = false,
              ["bOverride_BloomConvolutionPreFilterMin"] = false,
              ["bOverride_BloomConvolutionPreFilterMult"] = false,
              ["bOverride_BloomConvolutionSize"] = false,
              ["bOverride_BloomConvolutionTexture"] = false,
              ["bOverride_BloomIntensity"] = false,
              ["bOverride_BloomMethod"] = false,
              ["bOverride_BloomSizeScale"] = false,
              ["bOverride_BloomThreshold"] = false,
            },
            ["BlurInvertParameter"] = 0,
            ["BlurMaskRadiusParameter"] = 0,
            ["BlurMaskSoftnessParameter"] = 0,
            ["BlurStrideParameter"] = 0,
            ["CenterParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorSplitStrideParameter"] = 0,
            ["ContrastParameter"] = 0,
            ["DOFParams"] = {
              ["DepthBlurAmount"] = 1,
              ["DepthBlurRadius"] = 100,
              ["DepthOfFieldBladeCount"] = 5,
              ["DepthOfFieldFstop"] = 4,
              ["DepthOfFieldMinFstop"] = 1.2,
              ["FarBlurSize"] = 15,
              ["FarTransitionRegion"] = 8000,
              ["FocalDistance"] = 300,
              ["FocalRegion"] = 0,
              ["NearBlurSize"] = 15,
              ["NearTransitionRegion"] = 300,
              ["Scale"] = 1,
              ["SensorWidth"] = 60,
              ["SqueezeFactor"] = 2,
              ["bMobileHQGaussian"] = false,
            },
            ["DesaturateParameter"] = 0,
            ["Duration"] = 0.20000001788139,
            ["ExposureBias"] = 0,
            ["FadeIn"] = 0.20000001788139,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.20000001788139,
            ["FadeOutCurve"] = "",
            ["FocalRegionUpdateType"] = 0,
            ["Index"] = 0,
            ["IntensityParameter"] = 0,
            ["InterruptMode"] = 0,
            ["InvertColorBParameter"] = 0,
            ["InvertColorGParameter"] = 0,
            ["InvertColorRParameter"] = 0,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0,
            ["MaskSoftnessParameter"] = 0,
            ["Material"] = "/Game/Arts/Effects/FX_Library/FX_Materials/FX_C8_Materials/PostProcessFX/M_PP_RadialBlur_ColorSplit_Inst.M_PP_RadialBlur_ColorSplit_Inst",
            ["PostProcessSettings"] = {
              ["BloomParams"] = {
                ["BloomParams"] = {
                  ["Bloom1Size"] = 0,
                  ["Bloom1Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom2Size"] = 0,
                  ["Bloom2Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom3Size"] = 0,
                  ["Bloom3Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom4Size"] = 0,
                  ["Bloom4Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom5Size"] = 0,
                  ["Bloom5Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom6Size"] = 0,
                  ["Bloom6Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["BloomConvolutionBufferScale"] = 0,
                  ["BloomConvolutionCenterUV"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                  },
                  ["BloomConvolutionPreFilterMax"] = 0,
                  ["BloomConvolutionPreFilterMin"] = 0,
                  ["BloomConvolutionPreFilterMult"] = 0,
                  ["BloomConvolutionSize"] = 0,
                  ["BloomConvolutionTexture"] = "None",
                  ["BloomIntensity"] = 0,
                  ["BloomMethod"] = 1,
                  ["BloomSizeScale"] = 0,
                  ["BloomThreshold"] = 0,
                  ["bOverride_Bloom1Size"] = false,
                  ["bOverride_Bloom1Tint"] = false,
                  ["bOverride_Bloom2Size"] = false,
                  ["bOverride_Bloom2Tint"] = false,
                  ["bOverride_Bloom3Size"] = false,
                  ["bOverride_Bloom3Tint"] = false,
                  ["bOverride_Bloom4Size"] = false,
                  ["bOverride_Bloom4Tint"] = false,
                  ["bOverride_Bloom5Size"] = false,
                  ["bOverride_Bloom5Tint"] = false,
                  ["bOverride_Bloom6Size"] = false,
                  ["bOverride_Bloom6Tint"] = false,
                  ["bOverride_BloomConvolutionBufferScale"] = false,
                  ["bOverride_BloomConvolutionCenterUV"] = false,
                  ["bOverride_BloomConvolutionPreFilterMax"] = false,
                  ["bOverride_BloomConvolutionPreFilterMin"] = false,
                  ["bOverride_BloomConvolutionPreFilterMult"] = false,
                  ["bOverride_BloomConvolutionSize"] = false,
                  ["bOverride_BloomConvolutionTexture"] = false,
                  ["bOverride_BloomIntensity"] = false,
                  ["bOverride_BloomMethod"] = false,
                  ["bOverride_BloomSizeScale"] = false,
                  ["bOverride_BloomThreshold"] = false,
                },
              },
              ["ColorAdjustParams"] = {
                ["Contrast"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Desaturate"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorB"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorG"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorR"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["bEnableDesaturateProgress"] = false,
              },
              ["ColorSplitRadialBlurParams"] = {
                ["BlurInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["ColorSplitStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["CustomMaterialParams"] = {
                ["CustomMaterial"] = "",
                ["MainCharPositionParamName"] = "",
                ["Scalars"] = {
                },
                ["Texture"] = {
                },
                ["Vectors"] = {
                },
                ["bFixBlendWeight"] = false,
              },
              ["DOFParams"] = {
                ["DOFParams"] = {
                  ["DepthBlurAmount"] = 1,
                  ["DepthBlurRadius"] = 100,
                  ["DepthOfFieldBladeCount"] = 5,
                  ["DepthOfFieldFstop"] = 4,
                  ["DepthOfFieldMinFstop"] = 1.2,
                  ["FarBlurSize"] = 15,
                  ["FarTransitionRegion"] = 8000,
                  ["FocalDistance"] = 300,
                  ["FocalRegion"] = 0,
                  ["NearBlurSize"] = 15,
                  ["NearTransitionRegion"] = 300,
                  ["Scale"] = 1,
                  ["SensorWidth"] = 60,
                  ["SqueezeFactor"] = 2,
                  ["bMobileHQGaussian"] = false,
                },
              },
              ["DarkenParams"] = {
                ["ExposureBias"] = 0,
              },
              ["PhantomParams"] = {
                ["Direction"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Gap"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Speed"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["PostProcessType"] = 0,
              ["Priority"] = 0,
              ["RGBSplitParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["RadialBlurParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 1,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0.1,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["VignetteParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Color"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Radius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
            },
            ["PostProcessType"] = 9,
            ["Priority"] = 0,
            ["Radius"] = 0,
            ["RadiusParameter"] = 0,
            ["Scalars"] = {
              ["Radial Blur Intensity"] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 3,
                ["Max"] = 0,
                ["Min"] = 0.5,
                ["ParamName"] = "",
              },
              ["Radial Blur Stride"] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 3,
                ["Max"] = 0.5,
                ["Min"] = 0.5,
                ["ParamName"] = "",
              },
              ["Radial Color Split Stride"] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 3,
                ["Max"] = 0.02,
                ["Min"] = 0.03,
                ["ParamName"] = "",
              },
            },
            ["SoftnessParameter"] = 0,
            ["StrideParameter"] = 0,
            ["TargetType"] = 0,
            ["TaskType"] = 126,
            ["Textures"] = {
            },
            ["Vectors"] = {
            },
            ["bUseNewPostProcessSetting"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 3.9666669368744,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_BigSword_Skill_Rage_Strike_P1.Play_Warrior_BigSword_Skill_Rage_Strike_P1",
            ["BlendOutType"] = 4,
            ["Duration"] = 3.9666669368744,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 3.9666669368744,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk5.Play_Vo_Role_Atk5",
            ["BlendOutType"] = 4,
            ["Duration"] = 3.9666669368744,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0.23333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 0.93333339691162,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_Weapon_Trail.NS_WarriorSkill12_Weapon_Trail",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 80,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 0.23333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.93333339691162,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_01.NS_WarriorSkill12_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -2.7783,
              ["Y"] = -0.1014,
              ["Z"] = 135.5006,
            },
            ["RotOffset"] = {
              ["Pitch"] = 5.0674,
              ["Roll"] = -168.1581,
              ["Yaw"] = -180,
            },
            ["RotationQuat"] = {
              ["W"] = 0.044,
              ["X"] = -0.0046,
              ["Y"] = -0.9937,
              ["Z"] = -0.1031,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.2,
              ["Y"] = 1.2,
              ["Z"] = 1.2,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0.30000001192093,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.73333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_02.NS_WarriorSkill12_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 100,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [13] = {
          ["EndTime"] = 3.9666669368744,
          ["StartTime"] = 2.8333334922791,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_GiantSword_Retake_Foley.Play_Warrior_GiantSword_Retake_Foley",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1333334445953,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 86061081,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 5,
        [3] = 6,
        [4] = 9,
        [5] = 11,
        [6] = 13,
        [7] = 10,
        [8] = 12,
        [9] = 8,
        [10] = 1,
        [11] = 7,
        [12] = 2,
        [13] = 14,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 7,
        [4] = 2,
        [5] = 8,
        [6] = 11,
        [7] = 9,
        [8] = 5,
        [9] = 6,
        [10] = 13,
        [11] = 12,
        [12] = 10,
        [13] = 14,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.************,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1243318506,
        ["B"] = 1073921945,
        ["C"] = -791998806,
        ["D"] = 1281286681,
      },
      ["ServerTaskEndList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 107,
            ["Duration"] = 3.************,
            ["EndFrame"] = 107,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_02_Montage.A_Warrior_Sword_Skill12_02_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 200,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 4,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 0,
                },
                ["ConditionTargetType"] = 0,
                ["ConditionType"] = 16,
                ["IsInstance"] = false,
                ["Mode"] = 2,
                ["RuleID"] = 0,
                ["Sign"] = 2,
                ["Value"] = 0,
              },
            },
            ["ConditionRelationType"] = 1,
          },
          ["EndTime"] = 3.************,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 3.5000002384186,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/86061080_Curve_5DA7EEAA55E0027A.86061080_Curve_5DA7EEAA55E0027A",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86061080_RootMotionCurve_5DA7EEAA7600027B.86061080_RootMotionCurve_5DA7EEAA7600027B",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200006,
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 68,
          },
        },
        [4] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0.70000004768372,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.33333334326744,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 2.5,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/X_blunt_1_light.X_blunt_1_light_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [6] = {
          ["EndTime"] = 0.70000004768372,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["BloomParams"] = {
              ["Bloom1Size"] = 0,
              ["Bloom1Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom2Size"] = 0,
              ["Bloom2Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom3Size"] = 0,
              ["Bloom3Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom4Size"] = 0,
              ["Bloom4Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom5Size"] = 0,
              ["Bloom5Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom6Size"] = 0,
              ["Bloom6Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["BloomConvolutionBufferScale"] = 0,
              ["BloomConvolutionCenterUV"] = {
                ["X"] = 0,
                ["Y"] = 0,
              },
              ["BloomConvolutionPreFilterMax"] = 0,
              ["BloomConvolutionPreFilterMin"] = 0,
              ["BloomConvolutionPreFilterMult"] = 0,
              ["BloomConvolutionSize"] = 0,
              ["BloomConvolutionTexture"] = "None",
              ["BloomIntensity"] = 0,
              ["BloomMethod"] = 1,
              ["BloomSizeScale"] = 0,
              ["BloomThreshold"] = 0,
              ["bOverride_Bloom1Size"] = false,
              ["bOverride_Bloom1Tint"] = false,
              ["bOverride_Bloom2Size"] = false,
              ["bOverride_Bloom2Tint"] = false,
              ["bOverride_Bloom3Size"] = false,
              ["bOverride_Bloom3Tint"] = false,
              ["bOverride_Bloom4Size"] = false,
              ["bOverride_Bloom4Tint"] = false,
              ["bOverride_Bloom5Size"] = false,
              ["bOverride_Bloom5Tint"] = false,
              ["bOverride_Bloom6Size"] = false,
              ["bOverride_Bloom6Tint"] = false,
              ["bOverride_BloomConvolutionBufferScale"] = false,
              ["bOverride_BloomConvolutionCenterUV"] = false,
              ["bOverride_BloomConvolutionPreFilterMax"] = false,
              ["bOverride_BloomConvolutionPreFilterMin"] = false,
              ["bOverride_BloomConvolutionPreFilterMult"] = false,
              ["bOverride_BloomConvolutionSize"] = false,
              ["bOverride_BloomConvolutionTexture"] = false,
              ["bOverride_BloomIntensity"] = false,
              ["bOverride_BloomMethod"] = false,
              ["bOverride_BloomSizeScale"] = false,
              ["bOverride_BloomThreshold"] = false,
            },
            ["BlurInvertParameter"] = 0,
            ["BlurMaskRadiusParameter"] = 0,
            ["BlurMaskSoftnessParameter"] = 0,
            ["BlurStrideParameter"] = 0,
            ["CenterParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorSplitStrideParameter"] = 0,
            ["ContrastParameter"] = 0,
            ["DOFParams"] = {
              ["DepthBlurAmount"] = 1,
              ["DepthBlurRadius"] = 100,
              ["DepthOfFieldBladeCount"] = 5,
              ["DepthOfFieldFstop"] = 4,
              ["DepthOfFieldMinFstop"] = 1.2,
              ["FarBlurSize"] = 15,
              ["FarTransitionRegion"] = 8000,
              ["FocalDistance"] = 300,
              ["FocalRegion"] = 0,
              ["NearBlurSize"] = 15,
              ["NearTransitionRegion"] = 300,
              ["Scale"] = 1,
              ["SensorWidth"] = 60,
              ["SqueezeFactor"] = 2,
              ["bMobileHQGaussian"] = false,
            },
            ["DesaturateParameter"] = 0,
            ["Duration"] = 0.33333334326744,
            ["ExposureBias"] = 0,
            ["FadeIn"] = 0.20000001788139,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.20000001788139,
            ["FadeOutCurve"] = "",
            ["FocalRegionUpdateType"] = 0,
            ["Index"] = 0,
            ["IntensityParameter"] = 0,
            ["InterruptMode"] = 0,
            ["InvertColorBParameter"] = 0,
            ["InvertColorGParameter"] = 0,
            ["InvertColorRParameter"] = 0,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0,
            ["MaskSoftnessParameter"] = 0,
            ["Material"] = "/Game/Arts/Effects/FX_Library/FX_Materials/FX_C8_Materials/PostProcessFX/M_PP_RadialBlur_ColorSplit_Inst.M_PP_RadialBlur_ColorSplit_Inst",
            ["PostProcessSettings"] = {
              ["BloomParams"] = {
                ["BloomParams"] = {
                  ["Bloom1Size"] = 0,
                  ["Bloom1Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom2Size"] = 0,
                  ["Bloom2Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom3Size"] = 0,
                  ["Bloom3Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom4Size"] = 0,
                  ["Bloom4Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom5Size"] = 0,
                  ["Bloom5Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom6Size"] = 0,
                  ["Bloom6Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["BloomConvolutionBufferScale"] = 0,
                  ["BloomConvolutionCenterUV"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                  },
                  ["BloomConvolutionPreFilterMax"] = 0,
                  ["BloomConvolutionPreFilterMin"] = 0,
                  ["BloomConvolutionPreFilterMult"] = 0,
                  ["BloomConvolutionSize"] = 0,
                  ["BloomConvolutionTexture"] = "None",
                  ["BloomIntensity"] = 0,
                  ["BloomMethod"] = 1,
                  ["BloomSizeScale"] = 0,
                  ["BloomThreshold"] = 0,
                  ["bOverride_Bloom1Size"] = false,
                  ["bOverride_Bloom1Tint"] = false,
                  ["bOverride_Bloom2Size"] = false,
                  ["bOverride_Bloom2Tint"] = false,
                  ["bOverride_Bloom3Size"] = false,
                  ["bOverride_Bloom3Tint"] = false,
                  ["bOverride_Bloom4Size"] = false,
                  ["bOverride_Bloom4Tint"] = false,
                  ["bOverride_Bloom5Size"] = false,
                  ["bOverride_Bloom5Tint"] = false,
                  ["bOverride_Bloom6Size"] = false,
                  ["bOverride_Bloom6Tint"] = false,
                  ["bOverride_BloomConvolutionBufferScale"] = false,
                  ["bOverride_BloomConvolutionCenterUV"] = false,
                  ["bOverride_BloomConvolutionPreFilterMax"] = false,
                  ["bOverride_BloomConvolutionPreFilterMin"] = false,
                  ["bOverride_BloomConvolutionPreFilterMult"] = false,
                  ["bOverride_BloomConvolutionSize"] = false,
                  ["bOverride_BloomConvolutionTexture"] = false,
                  ["bOverride_BloomIntensity"] = false,
                  ["bOverride_BloomMethod"] = false,
                  ["bOverride_BloomSizeScale"] = false,
                  ["bOverride_BloomThreshold"] = false,
                },
              },
              ["ColorAdjustParams"] = {
                ["Contrast"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Desaturate"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorB"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorG"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorR"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["bEnableDesaturateProgress"] = false,
              },
              ["ColorSplitRadialBlurParams"] = {
                ["BlurInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["ColorSplitStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["CustomMaterialParams"] = {
                ["CustomMaterial"] = "",
                ["MainCharPositionParamName"] = "",
                ["Scalars"] = {
                },
                ["Texture"] = {
                },
                ["Vectors"] = {
                },
                ["bFixBlendWeight"] = false,
              },
              ["DOFParams"] = {
                ["DOFParams"] = {
                  ["DepthBlurAmount"] = 1,
                  ["DepthBlurRadius"] = 100,
                  ["DepthOfFieldBladeCount"] = 5,
                  ["DepthOfFieldFstop"] = 4,
                  ["DepthOfFieldMinFstop"] = 1.2,
                  ["FarBlurSize"] = 15,
                  ["FarTransitionRegion"] = 8000,
                  ["FocalDistance"] = 300,
                  ["FocalRegion"] = 0,
                  ["NearBlurSize"] = 15,
                  ["NearTransitionRegion"] = 300,
                  ["Scale"] = 1,
                  ["SensorWidth"] = 60,
                  ["SqueezeFactor"] = 2,
                  ["bMobileHQGaussian"] = false,
                },
              },
              ["DarkenParams"] = {
                ["ExposureBias"] = 0,
              },
              ["PhantomParams"] = {
                ["Direction"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Gap"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Speed"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["PostProcessType"] = 0,
              ["Priority"] = 0,
              ["RGBSplitParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["RadialBlurParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 1,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0.1,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["VignetteParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Color"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Radius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
            },
            ["PostProcessType"] = 9,
            ["Priority"] = 0,
            ["Radius"] = 0,
            ["RadiusParameter"] = 0,
            ["Scalars"] = {
              ["Radial Blur Intensity"] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 3,
                ["Max"] = 0,
                ["Min"] = 1,
                ["ParamName"] = "",
              },
              ["Radial Blur Stride"] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 3,
                ["Max"] = 1,
                ["Min"] = 2,
                ["ParamName"] = "",
              },
            },
            ["SoftnessParameter"] = 0,
            ["StrideParameter"] = 0,
            ["TargetType"] = 0,
            ["TaskType"] = 126,
            ["Textures"] = {
            },
            ["Vectors"] = {
            },
            ["bUseNewPostProcessSetting"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 3.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_BigSword_Skill_Rage_Strike_P2.Play_Warrior_BigSword_Skill_Rage_Strike_P2",
            ["BlendOutType"] = 4,
            ["Duration"] = 3.************,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 2.3000001907349,
          ["StartTime"] = 0.26666668057442,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk2_1.Play_Vo_Role_Atk2_1",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.0333335399628,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 2,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0.30000001192093,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.73333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_02.NS_WarriorSkill12_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = -150,
              ["Z"] = 100,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 1.4000000953674,
          ["StartTime"] = 0.66666668653488,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.73333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_02.NS_WarriorSkill12_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = -70,
              ["Z"] = 100,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 1.2000000476837,
          ["StartTime"] = 0.26666668057442,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.93333339691162,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_01.NS_WarriorSkill12_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -2.649,
              ["Y"] = -14.4935,
              ["Z"] = 103.9921,
            },
            ["RotOffset"] = {
              ["Pitch"] = -22.3656,
              ["Roll"] = 7.9269,
              ["Yaw"] = -14.5352,
            },
            ["RotationQuat"] = {
              ["W"] = 0.9725,
              ["X"] = -0.0428,
              ["Y"] = 0.2005,
              ["Z"] = -0.1105,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.2,
              ["Y"] = 1.2,
              ["Z"] = 1.2,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 1.5666667222977,
          ["StartTime"] = 0.63333338499069,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.93333339691162,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_01.NS_WarriorSkill12_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -0.1704,
              ["Y"] = 27.0957,
              ["Z"] = 141.7691,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 13.6142,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 0.993,
              ["X"] = -0.1185,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.2,
              ["Y"] = 1.2,
              ["Z"] = 1.2,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [13] = {
          ["EndTime"] = 1.4000000953674,
          ["StartTime"] = 0.56666672229767,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 0.83333337306976,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_Weapon_Trail.NS_WarriorSkill12_Weapon_Trail",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 80,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [14] = {
          ["EndTime"] = 3.************,
          ["StartTime"] = 2.6666667461395,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_GiantSword_Retake_Foley.Play_Warrior_GiantSword_Retake_Foley",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.90000003576279,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 86061083,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 6,
        [3] = 14,
        [4] = 5,
        [5] = 12,
        [6] = 15,
        [7] = 13,
        [8] = 7,
        [9] = 9,
        [10] = 10,
        [11] = 17,
        [12] = 18,
        [13] = 16,
        [14] = 11,
        [15] = 8,
        [16] = 1,
        [17] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 8,
        [4] = 12,
        [5] = 2,
        [6] = 14,
        [7] = 15,
        [8] = 13,
        [9] = 9,
        [10] = 6,
        [11] = 5,
        [12] = 16,
        [13] = 17,
        [14] = 7,
        [15] = 10,
        [16] = 18,
        [17] = 11,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.1333336830139,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 381086340,
        ["B"] = 1180904924,
        ["C"] = 1235373240,
        ["D"] = 1917400087,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 3,
        [3] = 5,
        [4] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 2,
        [4] = 5,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 4.1333336830139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 124,
            ["Duration"] = 4.1333336830139,
            ["EndFrame"] = 124,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Sword_Skill12_03_Montage.A_Warrior_Sword_Skill12_03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 300,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 4,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 0,
                },
                ["ConditionTargetType"] = 0,
                ["ConditionType"] = 16,
                ["IsInstance"] = false,
                ["Mode"] = 2,
                ["RuleID"] = 0,
                ["Sign"] = 2,
                ["Value"] = 0,
              },
            },
            ["ConditionRelationType"] = 1,
          },
          ["EndTime"] = 4.1333336830139,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 4.0666670799255,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/86061080_Curve_5DA7EE9FFCA0026D.86061080_Curve_5DA7EE9FFCA0026D",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86061080_RootMotionCurve_5DA7EEA01A00026E.86061080_RootMotionCurve_5DA7EEA01A00026E",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200006,
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 68,
          },
        },
        [5] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 400,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 1,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 100,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 3,
              },
            },
            ["ConditionRelationType"] = 0,
          },
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0.90000003576279,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = true,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 100,
                [2] = 400,
                [3] = 0,
              },
              ["DestType"] = 2,
            },
            ["Duration"] = 0.13333334028721,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.1,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0.4333333671093,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_1_light.Z_blunt_1_light_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [7] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.36666667461395,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 1.5,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_3_heavy.Z_blunt_3_heavy_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [8] = {
          ["EndTime"] = 4.1000003814697,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_BigSword_Skill_Rage_Strike_P3.Play_Warrior_BigSword_Skill_Rage_Strike_P3",
            ["BlendOutType"] = 4,
            ["Duration"] = 4.1000003814697,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 1.5666667222977,
          ["StartTime"] = 0.40000003576279,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk2_2.Play_Vo_Role_Atk2_2",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1666667461395,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 2.1666667461395,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk2_2.Play_Vo_Role_Atk2_2",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1666667461395,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [11] = {
          ["EndTime"] = 3.************,
          ["StartTime"] = 3.0000002384186,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Warrior/New/Play_Warrior_GiantSword_Retake_Foley.Play_Warrior_GiantSword_Retake_Foley",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.56666672229767,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 1.2000000476837,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 0,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 1.2000000476837,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
                [1] = 0,
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/86060005_BPT_AS_MaterialEffect_C_1_5D518D3EC4400E45.86060005_BPT_AS_MaterialEffect_C_1_5D518D3EC4400E45",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "SM_Warrior_001A_P001",
              ["SearchMeshType"] = 2,
              ["bOverrideEdgeColor"] = false,
              ["bOverrideEdgeIntensity"] = true,
              ["bOverrideEdgePow"] = false,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 1,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "SM_Warrior_001A_P001",
                ["SearchMeshType"] = 2,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [13] = {
          ["EndTime"] = 1.3000000715256,
          ["StartTime"] = 0.36666667461395,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.93333339691162,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_01.NS_WarriorSkill12_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -0.0321,
              ["Y"] = 25.4809,
              ["Z"] = 215.0231,
            },
            ["RotOffset"] = {
              ["Pitch"] = -84.3897,
              ["Roll"] = 43.3687,
              ["Yaw"] = -36.952,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7316,
              ["X"] = -0.0618,
              ["Y"] = 0.6787,
              ["Z"] = 0.0172,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.3,
              ["Y"] = 1.3,
              ["Z"] = 1.3,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [14] = {
          ["EndTime"] = 0.93333339691162,
          ["StartTime"] = 0.26666668057442,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 0.66666668653488,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_Weapon_Trail.NS_WarriorSkill12_Weapon_Trail",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 80,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [15] = {
          ["EndTime"] = 1.2333333492279,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 0.90000003576279,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_03.NS_WarriorSkill12_03",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = -121,
              ["Y"] = -18,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 10,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = 0,
              ["X"] = 0,
              ["Y"] = -0.0872,
              ["Z"] = 0.9962,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [16] = {
          ["EndTime"] = 3.066666841507,
          ["StartTime"] = 0.96666669845581,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 2.1000001430511,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/NS_WarriorSkill12_04.NS_WarriorSkill12_04",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 100,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [17] = {
          ["EndTime"] = 2.3000001907349,
          ["StartTime"] = 0.96666669845581,
          ["TaskData"] = {
            ["AnimSequence"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/SM_Stone_04_Ani_Anim.SM_Stone_04_Ani_Anim",
            ["AttachSocket"] = "VB Effect",
            ["AttachTime"] = -1,
            ["BPAnimPath"] = "",
            ["Duration"] = 1.3333333730698,
            ["EnableLight"] = false,
            ["Index"] = 0,
            ["InterruptMode"] = 2,
            ["MeshName"] = "SM_Stone_04",
            ["NeedAttach"] = false,
            ["NeedLoop"] = false,
            ["Rotation"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 3.8,
              ["Y"] = 3.8,
              ["Z"] = 3.8,
            },
            ["SkeletalMesh"] = "/Game/Arts/Effects/FX_Character/Warrior_FX_New/Skill12/SM_Stone_04_Ani.SM_Stone_04_Ani",
            ["StaticMesh"] = "None",
            ["TaskTargetType"] = 0,
            ["TaskType"] = 8,
            ["Translation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -100,
            },
            ["UseLOD0"] = false,
            ["bAttachToCamera"] = false,
            ["bCopyCurFrameAnim"] = false,
            ["bFollowParentBound"] = true,
            ["bInherit"] = true,
            ["bOnlySelf"] = false,
            ["bStickGround"] = false,
            ["bUseStaticMesh"] = false,
          },
        },
        [18] = {
          ["EndTime"] = 2.3333334922791,
          ["StartTime"] = 1.2333333492279,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/86060034_BPT_AS_MaterialEffect_C_1_5D5BB6B39980238D.86060034_BPT_AS_MaterialEffect_C_1_5D5BB6B39980238D",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 4,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "SM_Stone_04",
              ["SearchMeshType"] = 2,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 1.1000000238419,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideEdgeColor"] = false,
              ["bOverrideEdgeIntensity"] = false,
              ["bOverrideEdgePow"] = false,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 2,
            ["MaterialEffectType"] = 0,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data