local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Teleport_Start.A_BOSS_FHYZ_Teleport_Start",
    [2] = "/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill04/NS_BOSS_FHYZ_Body01.NS_BOSS_FHYZ_Body01",
    [4] = "/Game/Arts/Character/Model/Boss/BossMoonPrototype/SK_Boss_Moon_Prototype.SK_Boss_Moon_Prototype",
    [6] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge",
    [7] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Teleport_End.A_BOSS_FHYZ_Teleport_End",
    [8] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Skill_06_Start.A_BOSS_FHYZ_Skill_06_Start",
    [9] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Skill_06_End1.A_BOSS_FHYZ_Skill_06_End1",
    [10] = "/Game/Blueprint/CombatSystem/Curve/89001315_Curve_5DA58E418F4001F4.89001315_Curve_5DA58E418F4001F4",
    [11] = "/Game/Blueprint/CombatSystem/Curve/89001315_Curve_5DA58E51BB4001FF.89001315_Curve_5DA58E51BB4001FF",
    [12] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001315_RootMotionCurve_5DA58E41AAC001F5.89001315_RootMotionCurve_5DA58E41AAC001F5",
    [13] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001315_RootMotionCurve_5DA58E51D3800200.89001315_RootMotionCurve_5DA58E51D3800200",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["NodeGuid"] = {
      ["A"] = 224761448,
      ["B"] = 1301121739,
      ["C"] = -1239325793,
      ["D"] = 1036564049,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 10,
        [3] = 5,
        [4] = 11,
        [5] = 6,
        [6] = 4,
        [7] = 7,
        [8] = 8,
        [9] = 9,
        [10] = 13,
        [11] = 12,
        [12] = 2,
        [13] = 14,
        [14] = 15,
        [15] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 8,
        [3] = 6,
        [4] = 5,
        [5] = 4,
        [6] = 10,
        [7] = 7,
        [8] = 9,
        [9] = 11,
        [10] = 2,
        [11] = 12,
        [12] = 13,
        [13] = 14,
        [14] = 3,
        [15] = 15,
      },
      ["Duration"] = 21.************,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -22141585,
        ["B"] = 1314416256,
        ["C"] = 363472287,
        ["D"] = -1659624945,
      },
      ["ServerTaskEndList"] = {
        [1] = 10,
        [2] = 11,
        [3] = 12,
        [4] = 14,
        [5] = 15,
      },
      ["ServerTaskStartList"] = {
        [1] = 10,
        [2] = 11,
        [3] = 12,
        [4] = 14,
        [5] = 15,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.96666669845581,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 29,
            ["Duration"] = 0.96666669845581,
            ["EndFrame"] = 29,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Teleport_Start.A_BOSS_FHYZ_Teleport_Start",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 16.************,
          ["StartTime"] = 2.2333333492279,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 438,
            ["Duration"] = 14.60000038147,
            ["EndFrame"] = 438,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Skill_06_Start.A_BOSS_FHYZ_Skill_06_Start",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 21.************,
          ["StartTime"] = 16.************,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 140,
            ["Duration"] = 4.6666669845581,
            ["EndFrame"] = 140,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Skill_06_End1.A_BOSS_FHYZ_Skill_06_End1",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 2.2333333492279,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 37,
            ["Duration"] = 1.2333333492279,
            ["EndFrame"] = 37,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_Teleport_End.A_BOSS_FHYZ_Teleport_End",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 0.96666669845581,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 0.70000004768372,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [6] = {
          ["EndTime"] = 1.7333334684372,
          ["StartTime"] = 0.73333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill04/NS_BOSS_FHYZ_Body01.NS_BOSS_FHYZ_Body01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 2.5000002384186,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BOSS_FHYZ/Skill04/NS_BOSS_FHYZ_Body01.NS_BOSS_FHYZ_Body01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 2.7333335876465,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_FHYZ/Boss_FHYZ/Play_Boss_FHYZ_Disappear.Play_Boss_FHYZ_Disappear",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.7333335876465,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_FHYZ/Boss_FHYZ/Play_Boss_FHYZ_Appear.Play_Boss_FHYZ_Appear",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3333333730698,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 1.2666667699814,
          ["StartTime"] = 1.2666667699814,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = -1000,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 3,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [11] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 4.5,
          ["StartTime"] = 2.4333333969116,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 0,
            ["CheckBlock"] = true,
            ["Duration"] = 2.066666841507,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [13] = {
          ["EndTime"] = 3.6666667461395,
          ["StartTime"] = 3.1666667461395,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 3,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 50000,
            ["Scale"] = 2,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [14] = {
          ["EndTime"] = 16.************,
          ["StartTime"] = 5.8333334922791,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 11.000000953674,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = true,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/89001315_Curve_5DA58E418F4001F4.89001315_Curve_5DA58E418F4001F4",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001315_RootMotionCurve_5DA58E41AAC001F5.89001315_RootMotionCurve_5DA58E41AAC001F5",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [15] = {
          ["EndTime"] = 21.************,
          ["StartTime"] = 16.************,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 4.6000003814697,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = ************,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/89001315_Curve_5DA58E51BB4001FF.89001315_Curve_5DA58E51BB4001FF",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001315_RootMotionCurve_5DA58E51D3800200.89001315_RootMotionCurve_5DA58E51D3800200",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data