local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat_Slave/A_Monster_Redhat_Slave_Attack01_Montage.A_Monster_Redhat_Slave_Attack01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1242274491,
      ["B"] = 1340476103,
      ["C"] = 1340849288,
      ["D"] = -512508467,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 3,
        [4] = 5,
        [5] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 4,
        [4] = 3,
        [5] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.2333335876465,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -736604866,
        ["B"] = 1238245259,
        ["C"] = -185698403,
        ["D"] = 1058997416,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 3,
        [4] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 3,
        [4] = 5,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 3.2333335876465,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 97,
            ["Duration"] = 3.2333335876465,
            ["EndFrame"] = 97,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat_Slave/A_Monster_Redhat_Slave_Attack01_Montage.A_Monster_Redhat_Slave_Attack01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.5333334207535,
          ["StartTime"] = 1.3333333730698,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.90000003576279,
          ["StartTime"] = 0.70000004768372,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 50,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.20000001788139,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = 50,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 1.4666666984558,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 50,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.20000001788139,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = 50,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data