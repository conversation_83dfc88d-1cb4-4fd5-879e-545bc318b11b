local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/Boss<PERSON>her<PERSON>/A_Boss_She<PERSON>n_Skill01_Montage.A_<PERSON>_Sherron_Skill01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -403551517,
      ["B"] = 1125743444,
      ["C"] = -108247111,
      ["D"] = -1918049186,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 8,
        [3] = 9,
        [4] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 8,
        [4] = 9,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 5.6666669845581,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1440552599,
        ["B"] = 1211479802,
        ["C"] = -578747519,
        ["D"] = 830983072,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 5,
        [3] = 6,
        [4] = 7,
        [5] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 4,
        [3] = 5,
        [4] = 6,
        [5] = 7,
      },
      ["TaskMap"] = {
        [1] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["ConfigID"] = 6406265,
          ["ConfigIDs"] = {
          },
          ["Duration"] = 0.20000001788139,
          ["EndTime"] = 0.20000001788139,
          ["Index"] = 0,
          ["ParamMap"] = {
          },
          ["StartTime"] = 0,
          ["TDUITypeEnum"] = 0,
          ["TargetMode"] = 2,
          ["TaskType"] = 18,
          ["UIName"] = "",
          ["UIType"] = 2,
          ["bShowInPuppetSlot"] = false,
          ["bStopReminderOnStateEnd"] = false,
        },
        [2] = {
          ["AngularSpeedUpperLimit"] = 3600,
          ["CheckBlock"] = true,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.5000001192093,
          ["EndTime"] = 1.5000001192093,
          ["Index"] = 0,
          ["RotateDirection"] = 5,
          ["SelectionRuleID"] = 0,
          ["StartTime"] = 0,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 37,
          ["bFallbackOnNoTarget"] = false,
        },
        [3] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 150,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 5.0000004768372,
          ["EndFrame"] = 150,
          ["EndTime"] = 5.6000003814697,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSherron/A_Boss_Sherron_Skill01_Montage.A_Boss_Sherron_Skill01_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0.60000002384186,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [4] = {
        },
        [5] = {
        },
        [6] = {
        },
        [7] = {
        },
        [8] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.93333339691162,
          ["EffectPriority"] = 5,
          ["EndTime"] = 1.5333334207535,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Moon_FX/Mechanism02/NS_MoonMec02_Warnning.NS_MoonMec02_Warnning",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0.60000002384186,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [9] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 4,
          ["EffectPriority"] = 5,
          ["EndTime"] = 4.6333336830139,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic01/NS_MegoseMechanic01_SGround.NS_MegoseMechanic01_SGround",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = -100,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0.63333338499069,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data