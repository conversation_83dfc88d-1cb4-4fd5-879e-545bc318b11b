local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterTreeman/MonsterMoonwoman/A_Monster_Moonwoman_Skill03.A_Monster_Moonwoman_Skill03",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1256870856,
      ["B"] = 1248597495,
      ["C"] = 658884025,
      ["D"] = 1898851789,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 2,
        [3] = 4,
        [4] = 5,
        [5] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 3,
        [3] = 4,
        [4] = 2,
        [5] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 2.9000000953674,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -630891515,
        ["B"] = 1124369946,
        ["C"] = -986264957,
        ["D"] = -521762421,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.9000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 0,
            ["BlendOutTime"] = 0,
            ["BlendOutWhenInterrupt"] = 0,
            ["ClipLength"] = 115,
            ["Duration"] = 2.9000000953674,
            ["EndFrame"] = 115,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterTreeman/MonsterMoonwoman/A_Monster_Moonwoman_Skill03.A_Monster_Moonwoman_Skill03",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/86060027_BPT_AS_MaterialEffect_C_0_5D6081F1174034DA.86060027_BPT_AS_MaterialEffect_C_0_5D6081F1174034DA",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 1,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 0,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 1,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideEdgeColor"] = false,
              ["bOverrideEdgeIntensity"] = false,
              ["bOverrideEdgePow"] = false,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 0,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [3] = {
          ["EndTime"] = 0.1000000089407,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 0.1000000089407,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [4] = {
          ["EndTime"] = 1.9000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.9000000953674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill05/NS_Boss_Sasriel_Skill05_Charge.NS_Boss_Sasriel_Skill05_Charge",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 0.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 250,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 8,
              ["Y"] = 8,
              ["Z"] = 8,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = true,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 2.1666667461395,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.66666668653488,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Sasriel_FX/Skill05/NS_Boss_SasrielSkill05_Burst.NS_Boss_SasrielSkill05_Burst",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 0.5,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 250,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 90,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7071,
              ["X"] = -0.7071,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 8,
              ["Y"] = 8,
              ["Z"] = 8,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = true,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data