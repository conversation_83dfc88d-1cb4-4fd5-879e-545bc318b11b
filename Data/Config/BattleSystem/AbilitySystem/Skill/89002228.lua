local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -293479958,
      ["B"] = 1213053221,
      ["C"] = -646706284,
      ["D"] = 1720578375,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -798980134,
        ["B"] = 1233925749,
        ["C"] = 161318289,
        ["D"] = -1171506641,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TargetSelectionRuleID"] = 890022281,
            ["TaskType"] = 72,
          },
        },
        [2] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data