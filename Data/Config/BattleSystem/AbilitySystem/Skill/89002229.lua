local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_start.Base_F_Injured_start",
    [2] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_Loop.Base_F_Injured_Loop",
    [3] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_End.Base_F_Injured_End",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 188151697,
      ["B"] = 1218647959,
      ["C"] = -59093577,
      ["D"] = 315201930,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 8,
        [2] = 1,
        [3] = 4,
        [4] = 2,
        [5] = 5,
        [6] = 3,
        [7] = 6,
        [8] = 7,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 7,
        [3] = 8,
        [4] = 4,
        [5] = 2,
        [6] = 5,
        [7] = 3,
        [8] = 6,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 25.033334732056,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1172376899,
        ["B"] = 1226934065,
        ["C"] = -2022444896,
        ["D"] = 682960365,
      },
      ["ServerTaskEndList"] = {
        [1] = 7,
      },
      ["ServerTaskStartList"] = {
        [1] = 7,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 85,
            ["Duration"] = 2.8333334922791,
            ["EndFrame"] = 85,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "Chairdown_Loop",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_start.Base_F_Injured_start",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 11.766667366028,
          ["StartTime"] = 7.2666668891907,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 135,
            ["Duration"] = 4.5,
            ["EndFrame"] = 135,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_Loop.Base_F_Injured_Loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 20.700000762939,
          ["StartTime"] = 16.200000762939,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 135,
            ["Duration"] = 4.5,
            ["EndFrame"] = 135,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_Loop.Base_F_Injured_Loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 7.3000001907349,
          ["StartTime"] = 2.8000001907349,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 135,
            ["Duration"] = 4.5,
            ["EndFrame"] = 135,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_Loop.Base_F_Injured_Loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 16.233333587646,
          ["StartTime"] = 11.733333587646,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 135,
            ["Duration"] = 4.5,
            ["EndFrame"] = 135,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_Loop.Base_F_Injured_Loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 25.033334732056,
          ["StartTime"] = 18.700000762939,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 190,
            ["Duration"] = 6.3333334922791,
            ["EndFrame"] = 190,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/Base_F_Injured_End.Base_F_Injured_End",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 25.10000038147,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 25.10000038147,
            ["Index"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 137,
            ["bDisableDodge"] = false,
            ["bDisableJump"] = true,
            ["bDisableLocoStartMovement"] = true,
            ["bDisableMovement"] = true,
            ["bDisableRotation"] = true,
            ["bNeedReset"] = true,
          },
        },
        [8] = {
          ["EndTime"] = 1.0666667222977,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ConfigID"] = 6406380,
            ["ConfigIDs"] = {
            },
            ["Duration"] = 1.0666667222977,
            ["Index"] = 0,
            ["ParamMap"] = {
            },
            ["TDUITypeEnum"] = 0,
            ["TargetMode"] = 2,
            ["TaskType"] = 18,
            ["UIName"] = "",
            ["UIType"] = 2,
            ["bShowInPuppetSlot"] = false,
            ["bStopReminderOnStateEnd"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data