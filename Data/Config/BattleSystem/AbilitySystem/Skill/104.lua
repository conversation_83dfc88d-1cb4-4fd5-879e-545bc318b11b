local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_Start_Montage.A_Warrior_Skill03_Start_Montage",
    [2] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_Loop_Montage.A_Warrior_Skill03_Loop_Montage",
    [3] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_End_Montage.A_Warrior_Skill03_End_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -588126077,
      ["B"] = 1207738347,
      ["C"] = 611701410,
      ["D"] = -956536875,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 32,
        [3] = 29,
        [4] = 20,
        [5] = 21,
        [6] = 22,
        [7] = 23,
        [8] = 24,
        [9] = 2,
        [10] = 31,
        [11] = 4,
        [12] = 25,
        [13] = 19,
        [14] = 5,
        [15] = 26,
        [16] = 17,
        [17] = 18,
        [18] = 6,
        [19] = 7,
        [20] = 27,
        [21] = 8,
        [22] = 9,
        [23] = 10,
        [24] = 11,
        [25] = 28,
        [26] = 12,
        [27] = 3,
        [28] = 30,
        [29] = 13,
        [30] = 14,
        [31] = 15,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 28,
        [3] = 29,
        [4] = 19,
        [5] = 32,
        [6] = 2,
        [7] = 4,
        [8] = 31,
        [9] = 5,
        [10] = 20,
        [11] = 6,
        [12] = 21,
        [13] = 7,
        [14] = 8,
        [15] = 22,
        [16] = 9,
        [17] = 23,
        [18] = 10,
        [19] = 11,
        [20] = 24,
        [21] = 12,
        [22] = 25,
        [23] = 13,
        [24] = 14,
        [25] = 26,
        [26] = 15,
        [27] = 30,
        [28] = 3,
        [29] = 27,
        [30] = 17,
        [31] = 18,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 7.6333336830139,
      ["EventTransferMap"] = {
        [1] = {
          ["BindSkillID"] = 0,
          ["ConditionTransferMap"] = {
          },
          ["EndTime"] = 4.7000002861023,
          ["Event"] = {
            ["AvailableEventTargetTypes"] = {
            },
            ["EventType"] = 7,
            ["IsInstance"] = true,
            ["PressType"] = 0,
          },
          ["NextStateID"] = 0,
          ["NodeGuid"] = {
            ["A"] = 13608759,
            ["B"] = 1280772262,
            ["C"] = -1118268234,
            ["D"] = 830284649,
          },
          ["StartTime"] = 0.76666671037674,
          ["TaskMap"] = {
          },
        },
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -689235247,
        ["B"] = 1104162891,
        ["C"] = -1819405651,
        ["D"] = 1573686813,
      },
      ["ServerTaskEndList"] = {
        [1] = 16,
        [2] = 31,
      },
      ["ServerTaskStartList"] = {
        [1] = 16,
        [2] = 31,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.76666671037674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 27,
            ["Duration"] = 0.76666671037674,
            ["EndFrame"] = 27,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.1739,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_Start_Montage.A_Warrior_Skill03_Start_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 4.7000002861023,
          ["StartTime"] = 0.76666671037674,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 118,
            ["Duration"] = 3.9333336353302,
            ["EndFrame"] = 118,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_Loop_Montage.A_Warrior_Skill03_Loop_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 7.6333336830139,
          ["StartTime"] = 4.7000002861023,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 88,
            ["Duration"] = 2.9333333969116,
            ["EndFrame"] = 88,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Warrior/Male/A_Warrior_Skill03_End_Montage.A_Warrior_Skill03_End_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 4.7666668891907,
          ["StartTime"] = 0.76666671037674,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [5] = {
          ["EndTime"] = 5.2666668891907,
          ["StartTime"] = 1.2666667699814,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [6] = {
          ["EndTime"] = 5.6000003814697,
          ["StartTime"] = 1.6000001430511,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [7] = {
          ["EndTime"] = 5.9333338737488,
          ["StartTime"] = 1.9333333969116,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [8] = {
          ["EndTime"] = 6.2666668891907,
          ["StartTime"] = 2.2666668891907,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [9] = {
          ["EndTime"] = 6.6000003814697,
          ["StartTime"] = 2.6000001430511,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [10] = {
          ["EndTime"] = 6.9333338737488,
          ["StartTime"] = 2.9333333969116,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [11] = {
          ["EndTime"] = 7.2666668891907,
          ["StartTime"] = 3.2666668891907,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [12] = {
          ["EndTime"] = 7.6000003814697,
          ["StartTime"] = 3.6000001430511,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [13] = {
          ["EndTime"] = 7.9333338737488,
          ["StartTime"] = 3.9333336353302,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [14] = {
          ["EndTime"] = 8.2666673660278,
          ["StartTime"] = 4.2666668891907,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [15] = {
          ["EndTime"] = 8.6000003814697,
          ["StartTime"] = 4.6000003814697,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 4,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 0.1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/CameraShake_120F.CameraShake_120F_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [16] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200006,
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 68,
          },
        },
        [17] = {
          ["EndTime"] = 5.2666668891907,
          ["StartTime"] = 5.0666670799255,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Y_blunt_3_heavy.Y_blunt_3_heavy_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [18] = {
          ["EndTime"] = 5.2666668891907,
          ["StartTime"] = 5.0666670799255,
          ["TaskData"] = {
            ["CenterParameter"] = {
              ["A"] = 1,
              ["B"] = 0.5,
              ["G"] = 0.5,
              ["R"] = 0.5,
            },
            ["Duration"] = 0.20000001788139,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["IntensityParameter"] = 0.5,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0.5,
            ["Priority"] = 0,
            ["SoftnessParameter"] = 5,
            ["StrideParameter"] = 0.5,
            ["TaskType"] = 62,
          },
        },
        [19] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 0.13333334028721,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 4.8666667938232,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.1000000089407,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 1,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03Trail01.NS_WarriorSkill03Trail01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 105,
            },
            ["RotOffset"] = {
              ["Pitch"] = 90,
              ["Roll"] = -90,
              ["Yaw"] = 180,
            },
            ["RotationQuat"] = {
              ["W"] = -0.5,
              ["X"] = 0.5,
              ["Y"] = 0.5,
              ["Z"] = 0.5,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [20] = {
          ["EndTime"] = 2.2666668891907,
          ["StartTime"] = 1.2666667699814,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [21] = {
          ["EndTime"] = 2.7666668891907,
          ["StartTime"] = 1.7666667699814,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [22] = {
          ["EndTime"] = 3.2666668891907,
          ["StartTime"] = 2.2666668891907,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [23] = {
          ["EndTime"] = 3.7666668891907,
          ["StartTime"] = 2.7666668891907,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [24] = {
          ["EndTime"] = 4.2666668891907,
          ["StartTime"] = 3.2666668891907,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [25] = {
          ["EndTime"] = 4.7666668891907,
          ["StartTime"] = 3.7666668891907,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [26] = {
          ["EndTime"] = 5.2666668891907,
          ["StartTime"] = 4.2666668891907,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [27] = {
          ["EndTime"] = 6.0333337783813,
          ["StartTime"] = 5.0333337783813,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [28] = {
          ["EndTime"] = 7.6000003814697,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "None",
            ["BlendOutType"] = 4,
            ["Duration"] = 7.5333337783813,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [29] = {
          ["EndTime"] = 1.9333333969116,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Batle_Male_Vo/Play_Vo_Warrior_WhirlwindChop01.Play_Vo_Warrior_WhirlwindChop01",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.8666667938232,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [30] = {
          ["EndTime"] = 7.6333336830139,
          ["StartTime"] = 4.6666669845581,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Batle_Male_Vo/Play_Vo_Warrior_WhirlwindChop02.Play_Vo_Warrior_WhirlwindChop02",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.9666669368744,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [31] = {
          ["EndTime"] = 4.7000002861023,
          ["StartTime"] = 0.76666671037674,
          ["TaskData"] = {
            ["Acceleration"] = -1,
            ["CurSpeed"] = 0,
            ["DecayTime"] = 0.05,
            ["Deceleration"] = -1,
            ["Duration"] = 3.9333336353302,
            ["Index"] = 0,
            ["MoveMode"] = 0,
            ["TaskType"] = 99,
            ["bNeedReset"] = true,
          },
        },
        [32] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 0.70000004768372,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 1,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/Skill03/NS_WarriorSkill03_01.NS_WarriorSkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 30,
              ["Y"] = 0,
              ["Z"] = 10,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data