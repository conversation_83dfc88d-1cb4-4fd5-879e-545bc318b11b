local Data = {
  ["OtherResources"] = {
    [2] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill09_02_Montage.A_M_Featherwit_Skill09_02_Montage",
    [4] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill09_01_Montage.A_M_Featherwit_Skill09_01_Montage",
    [5] = "/Game/Arts/Effects/CameraShake/Template/X_blunt_2_middle.X_blunt_2_middle",
    [6] = "/Game/Blueprint/CombatSystem/Curve/86032010_Curve_5DA34401E160007B.86032010_Curve_5DA34401E160007B",
    [7] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_6_Curve_5DA343E0A8800061.ASASkillGraphStateNode_6_Curve_5DA343E0A8800061",
    [8] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86032010_RootMotionCurve_5DA34401FCA0007C.86032010_RootMotionCurve_5DA34401FCA0007C",
    [9] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_6_RootMotionCurve_5DA343E0C3400062.ASASkillGraphStateNode_6_RootMotionCurve_5DA343E0C3400062",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
      [1] = {
        ["BindSkillID"] = 0,
        ["ConditionGroup"] = {
          ["ConditionList"] = {
            [1] = {
              ["AvaliableConditionTargetTypes"] = {
                [1] = 0,
              },
              ["ConditionTargetType"] = 0,
              ["ConditionType"] = 37,
              ["IsInstance"] = true,
            },
          },
          ["ConditionRelationType"] = 0,
        },
        ["FailStateID"] = 2,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = 2049046111,
          ["B"] = 1329346218,
          ["C"] = -1599442511,
          ["D"] = 1774545734,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 1,
        ["TaskMap"] = {
        },
      },
    },
    ["NodeGuid"] = {
      ["A"] = 1452376731,
      ["B"] = 1131711545,
      ["C"] = 1628749743,
      ["D"] = -1426202685,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 86032013,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 1,
        [4] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
      },
      ["Duration"] = 1.6000001430511,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 978587027,
        ["B"] = 1189530321,
        ["C"] = -1897914704,
        ["D"] = -911785670,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 1.6000001430511,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 48,
            ["Duration"] = 1.6000001430511,
            ["EndFrame"] = 48,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill09_02_Montage.A_M_Featherwit_Skill09_02_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.6000001430511,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.6000001430511,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3294585664842794,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/86032010_Curve_5DA34401E160007B.86032010_Curve_5DA34401E160007B",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86032010_RootMotionCurve_5DA34401FCA0007C.86032010_RootMotionCurve_5DA34401FCA0007C",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 1.1000000238419,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Featherwit/Play_Featherwit_Skill05_CardStuntman.Play_Featherwit_Skill05_CardStuntman",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.1000000238419,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk4_1.Play_Vo_Role_Atk4_1",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 86032010,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
      },
      ["Duration"] = 1.6666667461395,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -774275627,
        ["B"] = 1259425979,
        ["C"] = -1670211675,
        ["D"] = 1519764860,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 50,
            ["Duration"] = 1.6666667461395,
            ["EndFrame"] = 50,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill09_01_Montage.A_M_Featherwit_Skill09_01_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.30000001192093,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 0.30000001192093,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3294585665281067,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/ASASkillGraphStateNode_6_Curve_5DA343E0A8800061.ASASkillGraphStateNode_6_Curve_5DA343E0A8800061",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/ASASkillGraphStateNode_6_RootMotionCurve_5DA343E0C3400062.ASASkillGraphStateNode_6_RootMotionCurve_5DA343E0C3400062",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 1.1000000238419,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Featherwit/Play_Featherwit_Skill05_CardStuntman.Play_Featherwit_Skill05_CardStuntman",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.1000000238419,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk4_1.Play_Vo_Role_Atk4_1",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data