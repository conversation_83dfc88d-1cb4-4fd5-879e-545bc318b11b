local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -752791381,
      ["B"] = 1092855944,
      ["C"] = -25134711,
      ["D"] = -2133037383,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.5,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 525244671,
        ["B"] = 1278282039,
        ["C"] = -1404088652,
        ["D"] = 423735968,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["MontageAsset"] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/Turn/A_Boss_Theil_Combat_Turn_90_Right_Montage.A_Boss_Theil_Combat_Turn_90_Right_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 102,
          },
        },
        [2] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["RootmotionMoveScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskType"] = 172,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data