local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_Start.A_Monster_Shinv_Skill_03_Start",
    [2] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_End_02.A_Monster_Shinv_Skill_03_End_02",
    [3] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_loop.A_Monster_Shinv_Skill_03_loop",
    [4] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_End_01.A_Monster_Shinv_Skill_03_End_01",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 191386161,
      ["B"] = 1173768112,
      ["C"] = 59990441,
      ["D"] = 1683731451,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 89102086,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 7,
        [3] = 2,
        [4] = 4,
        [5] = 8,
        [6] = 5,
        [7] = 6,
        [8] = 9,
        [9] = 3,
        [10] = 10,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 7,
        [4] = 9,
        [5] = 8,
        [6] = 4,
        [7] = 6,
        [8] = 5,
        [9] = 3,
        [10] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 9.2333335876465,
      ["EventTransferMap"] = {
        [1] = {
          ["BindSkillID"] = 0,
          ["ConditionTransferMap"] = {
          },
          ["EndTime"] = 3.6000001430511,
          ["Event"] = {
            ["AvailableEventTargetTypes"] = {
              [1] = 5,
            },
            ["EventType"] = 8,
            ["IsInstance"] = true,
            ["hitNum"] = 1,
          },
          ["NextStateID"] = 2,
          ["NodeGuid"] = {
            ["A"] = -142953880,
            ["B"] = 1258451548,
            ["C"] = 1841565345,
            ["D"] = 1063992549,
          },
          ["StartTime"] = 2.1333334445953,
          ["TaskMap"] = {
          },
        },
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 49735846,
        ["B"] = 1095036217,
        ["C"] = 895030661,
        ["D"] = -210347382,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 6,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 6,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2.5000002384186,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 45,
            ["Duration"] = 2.5000002384186,
            ["EndFrame"] = 45,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 0.6,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_Start.A_Monster_Shinv_Skill_03_Start",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 9.2333335876465,
          ["StartTime"] = 3.566666841507,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 170,
            ["Duration"] = 5.6666669845581,
            ["EndFrame"] = 170,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_End_02.A_Monster_Shinv_Skill_03_End_02",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 3.0333335399628,
          ["StartTime"] = 2.5000002384186,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 16,
            ["Duration"] = 0.53333336114883,
            ["EndFrame"] = 16,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_loop.A_Monster_Shinv_Skill_03_loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 3.566666841507,
          ["StartTime"] = 3.0333335399628,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 16,
            ["Duration"] = 0.53333336114883,
            ["EndFrame"] = 16,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_loop.A_Monster_Shinv_Skill_03_loop",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 3.8666667938232,
          ["StartTime"] = 2.5000002384186,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 1000,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 1.3666667938232,
            ["EnableRotate"] = false,
            ["Ground"] = false,
            ["Index"] = 0,
            ["MaxDistance"] = 1000,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1.37,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 2.5000002384186,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["BoneDock"] = "root",
            ["Color"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Color",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
            ["Control"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
            ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_rec.MI_Decal_Yujing_rec",
            ["DecalSize"] = {
              ["X"] = 1000,
              ["Y"] = 100,
              ["Z"] = 500,
            },
            ["Duration"] = 2.4000000953674,
            ["DynamicMaterialParams"] = {
              [1] = {
                ["FloatTimeCurveNew"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89102086_BPT_AS_PlayDecal_C_0_5D8D2B3A166002B9.89102086_BPT_AS_PlayDecal_C_0_5D8D2B3A166002B9",
                },
                ["InitFloatValue"] = 0,
                ["InitLinearColorValue"] = {
                  ["A"] = 1,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["InitTextureValue"] = "",
                ["InitVectorValue"] = {
                  ["X"] = 0,
                  ["Y"] = 0,
                  ["Z"] = 0,
                },
                ["IsLoop"] = false,
                ["ParamName"] = "Control",
                ["ParamType"] = 0,
                ["VectorTimeCurveNew"] = {
                  ["CurveAsset"] = "",
                },
                ["bNeedEdit"] = false,
              },
            },
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GlowColor"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "GlowColor",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InsideRadius"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "InsideRadius",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["OriginSpecialName"] = "None",
            ["RotateAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "RotateAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["Rotation"] = {
              ["X"] = 90,
              ["Y"] = -90,
              ["Z"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SectorAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "SectorAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 40,
            ["Translation"] = {
              ["X"] = 0,
              ["Y"] = 500,
              ["Z"] = 0,
            },
            ["UberGraphFrame"] = {
            },
            ["bAttachRotation"] = true,
            ["bAttachScale"] = false,
            ["bNeedAttach"] = true,
            ["bNeedCheckGround"] = true,
            ["bWorldTransform"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 3.5333335399628,
          ["StartTime"] = 0.53333336114883,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 3.0000002384186,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill03/NS_MonsterShinv_Skill03_Star.NS_MonsterShinv_Skill03_Star",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 4.3666667938232,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 1,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 4.0333337783813,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill03/NS_MonsterShinv_Skill03_Attack.NS_MonsterShinv_Skill03_Attack",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 5.5333,
              ["Y"] = 1.3311,
              ["Z"] = 65.8004,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 9.2333335876465,
          ["StartTime"] = 4.3333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 4.9000000953674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill03/NS_MonsterShinv_Skill03_Gorund.NS_MonsterShinv_Skill03_Gorund",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 89202086,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 1,
        [3] = 3,
        [4] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.1333334445953,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -2007791247,
        ["B"] = 1189011607,
        ["C"] = 1023463356,
        ["D"] = -2036311195,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 94,
            ["Duration"] = 3.1333334445953,
            ["EndFrame"] = 94,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_03_End_01.A_Monster_Shinv_Skill_03_End_01",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 150,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.5,
            ["EnableRotate"] = false,
            ["Ground"] = false,
            ["Index"] = 0,
            ["MaxDistance"] = 150,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.5,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 3.1333334445953,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 2.8000001907349,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill03/NS_MonsterShinv_Skill03_Daoguang.NS_MonsterShinv_Skill03_Daoguang",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 3.1333334445953,
          ["StartTime"] = 1.0666667222977,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 2.066666841507,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill03/NS_MonsterShinv_Skill03_Attack2.NS_MonsterShinv_Skill03_Attack2",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data