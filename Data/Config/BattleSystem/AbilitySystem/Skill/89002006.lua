local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterWizard_01/A_Monster_Wizard_Skill01_Montage.A_Monster_Wizard_Skill01_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -573294896,
      ["B"] = 1126939730,
      ["C"] = 56976003,
      ["D"] = 279522142,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 10,
        [4] = 9,
        [5] = 7,
        [6] = 6,
        [7] = 3,
        [8] = 4,
        [9] = 8,
        [10] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 8,
        [4] = 9,
        [5] = 5,
        [6] = 7,
        [7] = 6,
        [8] = 3,
        [9] = 4,
        [10] = 10,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 6.7666668891907,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -202147302,
        ["B"] = 1290881715,
        ["C"] = -1439198031,
        ["D"] = 1231159915,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 10,
        [4] = 11,
        [5] = 12,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 12,
        [3] = 5,
        [4] = 10,
        [5] = 11,
      },
      ["TaskMap"] = {
        [1] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 0.20000001788139,
          ["EndTime"] = 0.20000001788139,
          ["Index"] = 0,
          ["StartTime"] = 0,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 118,
          ["UseStaticBlackboard"] = false,
        },
        [2] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 203,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 6.7666668891907,
          ["EndFrame"] = 203,
          ["EndTime"] = 6.7666668891907,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterWizard_01/A_Monster_Wizard_Skill01_Montage.A_Monster_Wizard_Skill01_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [3] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 1,
          ["EffectPriority"] = 5,
          ["EndTime"] = 6.0000004768372,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 0,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Wizard_FX/Skill01/NS_WizardSkill01_Start_02.NS_WizardSkill01_Start_02",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [4] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "root",
          ["Duration"] = 1,
          ["EffectPriority"] = 5,
          ["EndTime"] = 6.0000004768372,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 0,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Wizard_FX/Skill01/NS_WizardSkill01_Fly.NS_WizardSkill01_Fly",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 5.0000004768372,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [5] = {
          ["AngularSpeedUpperLimit"] = 540,
          ["CheckBlock"] = true,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 4.0666670799255,
          ["EndTime"] = 4.1666669845581,
          ["Index"] = 0,
          ["RotateDirection"] = 5,
          ["SelectionRuleID"] = 0,
          ["StartTime"] = 0.1000000089407,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 37,
          ["bFallbackOnNoTarget"] = false,
        },
        [6] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 5.0000004768372,
          ["EffectPriority"] = 5,
          ["EndTime"] = 5.7666668891907,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 0,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Wizard_FX/Skill01/NS_WizardSkill01_Start_01.NS_WizardSkill01_Start_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0.76666671037674,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [7] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "weapon_l",
          ["Duration"] = 5.0000004768372,
          ["EffectPriority"] = 5,
          ["EndTime"] = 5.5000004768372,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 0,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Wizard_FX/Skill01/NS_WizardSkill01_Start.NS_WizardSkill01_Start",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0.5,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = true,
          ["maxGazeDuration"] = 0,
        },
        [8] = {
          ["AttachSocket"] = "None",
          ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Mobs/Play_Monster_MobsMage_ArcaneFissure.Play_Monster_MobsMage_ArcaneFissure",
          ["BlendOutType"] = 4,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 6.6666669845581,
          ["EndTime"] = 6.6666669845581,
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0.66666668653488,
          ["FadeOutCurve"] = "",
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["RelativeLocation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 42,
          ["bNeedAttach"] = false,
          ["bPlayAtLast"] = false,
          ["bSetWorldLocation"] = false,
        },
        [9] = {
          ["BoneDock"] = "None",
          ["Color"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Color",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Control"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "Control",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = true,
          },
          ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_rec.MI_Decal_Yujing_rec",
          ["DecalSize"] = {
            ["X"] = 150,
            ["Y"] = 200,
            ["Z"] = 1000,
          },
          ["Duration"] = 5.1666669845581,
          ["DynamicMaterialParams"] = {
            [1] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89002006_BPT_AS_PlayDecal_C_0_5D8D2B0C7200026F.89002006_BPT_AS_PlayDecal_C_0_5D8D2B0C7200026F",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
          },
          ["EndTime"] = 5.2333335876465,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["GlowColor"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "GlowColor",
            ["ParamType"] = 3,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InsideRadius"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "InsideRadius",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["OriginSpecialName"] = "None",
          ["RotateAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "RotateAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["Rotation"] = {
            ["X"] = 0,
            ["Y"] = -90,
            ["Z"] = 0,
          },
          ["Scale3D"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["SectorAngle"] = {
            ["FloatTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["InitFloatValue"] = 0,
            ["InitLinearColorValue"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["InitTextureValue"] = "",
            ["InitVectorValue"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["IsLoop"] = false,
            ["ParamName"] = "SectorAngle",
            ["ParamType"] = 0,
            ["VectorTimeCurveNew"] = {
              ["CurveAsset"] = "",
            },
            ["bNeedEdit"] = false,
          },
          ["StartTime"] = 0.066666670143604,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 40,
          ["Translation"] = {
            ["X"] = 1000,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["UberGraphFrame"] = {
          },
          ["bAttachRotation"] = true,
          ["bAttachScale"] = true,
          ["bNeedAttach"] = true,
          ["bNeedCheckGround"] = true,
        },
        [10] = {
        },
        [11] = {
        },
        [12] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DeltaValue"] = 0,
          ["Duration"] = 6.6666669845581,
          ["EndTime"] = 6.7333335876465,
          ["Index"] = 0,
          ["NeedReset"] = false,
          ["StartTime"] = 0.066666670143604,
          ["TaskType"] = 110,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data