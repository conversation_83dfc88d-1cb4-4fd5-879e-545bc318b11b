local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill01_03_Montage.A_M_Featherwit_Skill01_03_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
      [1] = {
        ["BindSkillID"] = 0,
        ["ConditionRelationType"] = 0,
        ["Conditions"] = {
          [1] = {
            ["AvaliableConditionTargetTypes"] = {
              [1] = 0,
            },
            ["ConditionTargetType"] = 0,
            ["ConditionType"] = 16,
            ["IsInstance"] = true,
            ["Mode"] = 0,
            ["RuleID"] = 860300151,
            ["Sign"] = 4,
            ["Value"] = 0,
          },
        },
        ["FailStateID"] = 2,
        ["LogicGraph"] = "None",
        ["MultiTask"] = "None",
        ["NodeGuid"] = {
          ["A"] = -166401998,
          ["B"] = 1157900394,
          ["C"] = 794289818,
          ["D"] = 293769284,
        },
        ["SubConditions"] = {
        },
        ["SuccessStateID"] = 1,
        ["TaskMap"] = {
        },
      },
    },
    ["NodeGuid"] = {
      ["A"] = 398092874,
      ["B"] = 1241195768,
      ["C"] = -969630050,
      ["D"] = 1844608168,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 5,
        [2] = 6,
        [3] = 7,
        [4] = 8,
        [5] = 3,
        [6] = 9,
        [7] = 13,
        [8] = 14,
        [9] = 15,
        [10] = 16,
        [11] = 10,
        [12] = 1,
        [13] = 2,
        [14] = 12,
        [15] = 11,
        [16] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 5,
        [4] = 6,
        [5] = 11,
        [6] = 13,
        [7] = 12,
        [8] = 14,
        [9] = 15,
        [10] = 7,
        [11] = 8,
        [12] = 9,
        [13] = 10,
        [14] = 16,
        [15] = 1,
        [16] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.7000000476837,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1403518459,
        ["B"] = 1250219131,
        ["C"] = 449137339,
        ["D"] = -922736,
      },
      ["ServerTaskEndList"] = {
        [1] = 5,
        [2] = 7,
        [3] = 8,
        [4] = 3,
        [5] = 1,
        [6] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 7,
        [4] = 8,
        [5] = 1,
        [6] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 51,
            ["Duration"] = 1.7000000476837,
            ["EndFrame"] = 51,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill01_03_Montage.A_M_Featherwit_Skill01_03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 0.1000000089407,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 0.1000000089407,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
              [1] = {
                ["NewCollisionChannel"] = 15,
                ["NewCollisionResponse"] = 0,
              },
              [2] = {
                ["NewCollisionChannel"] = 16,
                ["NewCollisionResponse"] = 0,
              },
              [3] = {
                ["NewCollisionChannel"] = 17,
                ["NewCollisionResponse"] = 0,
              },
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 0,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0.30000001192093,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.20000001788139,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Fool_FX/Skill08/New/NS_FoolSkill08R.NS_FoolSkill08R",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 130,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Y_blunt_4_huge.Y_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [11] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Featherwit/Play_Featherwit_Skill08_ShootingGibberish01.Play_Featherwit_Skill08_ShootingGibberish01",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1333334445953,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Batle_Male_Vo/Play_Vo_Male_Atk1.Play_Vo_Male_Atk1",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [13] = {
          ["EndTime"] = 0.40000003576279,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [14] = {
          ["EndTime"] = 0.4333333671093,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [15] = {
          ["EndTime"] = 0.46666669845581,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [16] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 5,
        [2] = 7,
        [3] = 8,
        [4] = 3,
        [5] = 6,
        [6] = 9,
        [7] = 13,
        [8] = 14,
        [9] = 15,
        [10] = 16,
        [11] = 10,
        [12] = 1,
        [13] = 2,
        [14] = 12,
        [15] = 11,
        [16] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 5,
        [4] = 6,
        [5] = 11,
        [6] = 13,
        [7] = 12,
        [8] = 14,
        [9] = 15,
        [10] = 7,
        [11] = 8,
        [12] = 9,
        [13] = 10,
        [14] = 16,
        [15] = 1,
        [16] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.7000000476837,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -339948724,
        ["B"] = 1129012271,
        ["C"] = -1872429905,
        ["D"] = -1515024111,
      },
      ["ServerTaskEndList"] = {
        [1] = 5,
        [2] = 7,
        [3] = 8,
        [4] = 3,
        [5] = 1,
        [6] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 7,
        [4] = 8,
        [5] = 1,
        [6] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.7000000476837,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 51,
            ["Duration"] = 1.7000000476837,
            ["EndFrame"] = 51,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Featherwit/NewMale/A_M_Featherwit_Skill01_03_Montage.A_M_Featherwit_Skill01_03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 0.23333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 0.23333334922791,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
              [1] = {
                ["NewCollisionChannel"] = 15,
                ["NewCollisionResponse"] = 0,
              },
              [2] = {
                ["NewCollisionChannel"] = 16,
                ["NewCollisionResponse"] = 0,
              },
              [3] = {
                ["NewCollisionChannel"] = 17,
                ["NewCollisionResponse"] = 0,
              },
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 0,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0.30000001192093,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.20000001788139,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Fool_FX/Skill08/New/NS_FoolSkill08R.NS_FoolSkill08R",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 130,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 4,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 100,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Y_blunt_4_huge.Y_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [11] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Featherwit/Play_Featherwit_Skill08_ShootingGibberish01.Play_Featherwit_Skill08_ShootingGibberish01",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1333334445953,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 1.1333334445953,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Batle_Male_Vo/Play_Vo_Male_Atk1.Play_Vo_Male_Atk1",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.1000000238419,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [13] = {
          ["EndTime"] = 0.40000003576279,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [14] = {
          ["EndTime"] = 0.4333333671093,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [15] = {
          ["EndTime"] = 0.46666669845581,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
        [16] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["AnimSequence"] = "None",
            ["AttachSocket"] = "None",
            ["AttachTransform"] = {
              ["Rotation"] = {
                ["W"] = 1,
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["Scale3D"] = {
                ["X"] = 1,
                ["Y"] = 1,
                ["Z"] = 1,
              },
              ["Translation"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
            },
            ["BaseMaterial"] = {
              ["MaterialIDs"] = {
              },
              ["MaterialScalarParamValues"] = {
              },
              ["MaterialSlotNames"] = {
              },
              ["MaterialTextureParamValues"] = {
              },
              ["MaterialVectorParamValues"] = {
              },
              ["Priority"] = 5,
              ["ReplaceMaterial"] = "/Game/Arts/Effects/FX_Library/FX_Materials/MI_Trail_Test.MI_Trail_Test",
              ["bReplaceAllMaterial"] = true,
            },
            ["CustomDepthValue"] = 0,
            ["Duration"] = 0.40000003576279,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GhostLife"] = -1,
            ["GhostSpawnFrequency"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["MeshLOD"] = 2,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 95,
            ["bLookAtTarget"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bNeedLoop"] = false,
            ["bNeedRenderInMainPass"] = false,
            ["bNeedUpdatePose"] = false,
            ["bTerminateGhostWhenTaskEnd"] = false,
            ["bUseTargetOriginMaterial"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data