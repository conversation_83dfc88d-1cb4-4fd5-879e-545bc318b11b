local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_Pre_Montage.A_Boss_RB_Skill03_Pre_Montage",
    [2] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_PreLoop_Montage.A_Boss_RB_Skill03_PreLoop_Montage",
    [3] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_Attack_Montage.A_Boss_RB_Skill03_Attack_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1249042864,
      ["B"] = 1173930516,
      ["C"] = -1215437942,
      ["D"] = 1638922577,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 8,
        [2] = 5,
        [3] = 13,
        [4] = 1,
        [5] = 2,
        [6] = 16,
        [7] = 19,
        [8] = 6,
        [9] = 11,
        [10] = 12,
        [11] = 14,
        [12] = 3,
        [13] = 7,
        [14] = 9,
        [15] = 17,
        [16] = 20,
        [17] = 15,
        [18] = 10,
        [19] = 21,
        [20] = 23,
        [21] = 25,
        [22] = 26,
        [23] = 28,
        [24] = 22,
        [25] = 24,
        [26] = 27,
        [27] = 29,
        [28] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 8,
        [3] = 9,
        [4] = 10,
        [5] = 21,
        [6] = 5,
        [7] = 11,
        [8] = 13,
        [9] = 12,
        [10] = 2,
        [11] = 3,
        [12] = 22,
        [13] = 24,
        [14] = 16,
        [15] = 19,
        [16] = 14,
        [17] = 23,
        [18] = 25,
        [19] = 6,
        [20] = 4,
        [21] = 7,
        [22] = 27,
        [23] = 29,
        [24] = 17,
        [25] = 20,
        [26] = 15,
        [27] = 26,
        [28] = 28,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 8.3333339691162,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1782906586,
        ["B"] = 1265155911,
        ["C"] = -1856773731,
        ["D"] = 1060056395,
      },
      ["ServerTaskEndList"] = {
        [1] = 18,
        [2] = 8,
        [3] = 5,
        [4] = 16,
        [5] = 6,
        [6] = 7,
        [7] = 17,
      },
      ["ServerTaskStartList"] = {
        [1] = 8,
        [2] = 18,
        [3] = 5,
        [4] = 16,
        [5] = 6,
        [6] = 7,
        [7] = 17,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 1.5000001192093,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 50,
            ["Duration"] = 1.5000001192093,
            ["EndFrame"] = 50,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.1111,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_Pre_Montage.A_Boss_RB_Skill03_Pre_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 15,
            ["Duration"] = 0.5,
            ["EndFrame"] = 15,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_PreLoop_Montage.A_Boss_RB_Skill03_PreLoop_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 10,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 60,
            ["Duration"] = 2,
            ["EndFrame"] = 60,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_Attack_Montage.A_Boss_RB_Skill03_Attack_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 8.3333339691162,
          ["StartTime"] = 4,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 10,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 130,
            ["Duration"] = 4.3333334922791,
            ["EndFrame"] = 130,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossRayBieber/A_Boss_RB_Skill03_Attack_Montage.A_Boss_RB_Skill03_Attack_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["Pos"] = {
              ["BasePointArgs"] = {
                ["X"] = 300,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["BasePointType"] = 4,
              ["ClockwiseRotation"] = 0,
              ["PosNum"] = 1,
              ["ShapeArgs"] = {
              },
              ["ShapeType"] = 0,
            },
            ["TaskTargetType"] = 3,
            ["TaskType"] = 229,
          },
        },
        [6] = {
          ["EndTime"] = 3.4666669368744,
          ["StartTime"] = 3.4666669368744,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TargetSelectionRuleID"] = 880011052,
            ["TaskType"] = 73,
          },
        },
        [7] = {
          ["EndTime"] = 4.2000002861023,
          ["StartTime"] = 4,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = true,
          },
        },
        [8] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 10000,
            ["CheckBlock"] = true,
            ["Duration"] = 1,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 4.5,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 4.5,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 9,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 6.0000004768372,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "spine_01",
            ["Duration"] = 6.0000004768372,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/RayBieber_FX_New/Skill03/Boss_RB_Skill03/NS_Boss_RB_Skill03_Attack_Spine_Ribbon_C001.NS_Boss_RB_Skill03_Attack_Spine_Ribbon_C001",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 3.5000002384186,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2.5000002384186,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/RayBieber_FX_New/Skill03/Boss_RB_Skill03/NS_Boss_RB_Skill03_Decal_Start_C001.NS_Boss_RB_Skill03_Decal_Start_C001",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 200,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = -90,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7071,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -0.7071,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 3.5333335399628,
          ["StartTime"] = 1.0333334207535,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2.5000002384186,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/RayBieber_FX_New/Skill03/Boss_RB_Skill03/NS_Boss_RB_Skill03_Preticle_Loop_C001.NS_Boss_RB_Skill03_Preticle_Loop_C001",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = -90,
            },
            ["RotationQuat"] = {
              ["W"] = 0.7071,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -0.7071,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [13] = {
          ["EndTime"] = 1.5000001192093,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.5,
            ["Index"] = 0,
            ["Radius"] = 2000,
            ["Scale"] = 3,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_blunt_4_huge.Z_blunt_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [14] = {
          ["EndTime"] = 3.8333334922791,
          ["StartTime"] = 3.4333333969116,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.40000003576279,
            ["Index"] = 0,
            ["Radius"] = 2000,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_sharp_4_huge.Z_sharp_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [15] = {
          ["EndTime"] = 5.8333334922791,
          ["StartTime"] = 5.4333333969116,
          ["TaskData"] = {
            ["AttenuationCoefficient"] = 1,
            ["CampFilter"] = 1,
            ["Duration"] = 0.40000003576279,
            ["Index"] = 0,
            ["Radius"] = 2000,
            ["Scale"] = 1,
            ["ShakeAsset"] = "/Game/Arts/Effects/CameraShake/Template/Z_sharp_4_huge.Z_sharp_4_huge_C",
            ["TaskType"] = 59,
            ["UberGraphFrame"] = {
            },
          },
        },
        [16] = {
          ["EndTime"] = 3.4000000953674,
          ["StartTime"] = 2.2666668891907,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 0,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 12,
            },
            ["Duration"] = 1.1333334445953,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1.13,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [17] = {
          ["EndTime"] = 5.4000000953674,
          ["StartTime"] = 4.2666668891907,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 0,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 12,
            },
            ["Duration"] = 1.1333334445953,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 1.13,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [18] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AsideTargetType"] = 0,
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TargetSelectionRuleID"] = 0,
            ["TaskType"] = 157,
            ["asideID"] = 39000341,
          },
        },
        [19] = {
          ["EndTime"] = 3.4000000953674,
          ["StartTime"] = 2.2666668891907,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 1.1333334445953,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 0,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
        [20] = {
          ["EndTime"] = 5.4000000953674,
          ["StartTime"] = 4.2666668891907,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 1.1333334445953,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 0,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
        [21] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Boss_RayBiber_New/Play_Boss_Rb_Skill_Brutal_Leap_Pre.Play_Boss_Rb_Skill_Brutal_Leap_Pre",
            ["BlendOutType"] = 4,
            ["Duration"] = 6.3333334922791,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [22] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 2.1666667461395,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Boss_RayBiber_New/Play_Boss_Rb_Skill_Brutal_Leap_Jump.Play_Boss_Rb_Skill_Brutal_Leap_Jump",
            ["BlendOutType"] = 4,
            ["Duration"] = 4.1666669845581,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [23] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 3.4333333969116,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Boss_RayBiber_New/Play_Boss_Rb_Skill_Brutal_Leap_Land.Play_Boss_Rb_Skill_Brutal_Leap_Land",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.9000000953674,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [24] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 2.1666667461395,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Vo_Boss_RayBiber_Shout/Play_Vo_Boss_Rb_Attack1.Play_Vo_Boss_Rb_Attack1",
            ["BlendOutType"] = 4,
            ["Duration"] = 4.1666669845581,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [25] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 3.4333333969116,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Vo_Boss_RayBiber_Shout/Play_Vo_Boss_Rb_Attack1.Play_Vo_Boss_Rb_Attack1",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.9000000953674,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [26] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 5.4333333969116,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Boss_RayBiber_New/Play_Boss_Rb_Skill_Brutal_Leap_Land.Play_Boss_Rb_Skill_Brutal_Leap_Land",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.90000003576279,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [27] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 4.1666669845581,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Boss_RayBiber_New/Play_Boss_Rb_Skill_Brutal_Leap_Jump.Play_Boss_Rb_Skill_Brutal_Leap_Jump",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.1666667461395,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [28] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 5.4333333969116,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Vo_Boss_RayBiber_Shout/Play_Vo_Boss_Rb_Attack1.Play_Vo_Boss_Rb_Attack1",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.90000003576279,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [29] = {
          ["EndTime"] = 6.3333334922791,
          ["StartTime"] = 4.1666669845581,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RayBiber/Vo_Boss_RayBiber_Shout/Play_Vo_Boss_Rb_Attack1.Play_Vo_Boss_Rb_Attack1",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.1666667461395,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data