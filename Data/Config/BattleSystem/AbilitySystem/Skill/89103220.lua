local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossDeerTita/A_Boss_DeerTita_001_attack_Montage.A_Boss_DeerTita_001_attack_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 75801747,
      ["B"] = 1317094905,
      ["C"] = -1324631901,
      ["D"] = 2066801123,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 2.1666667461395,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1934498906,
        ["B"] = 1114599040,
        ["C"] = -1624981595,
        ["D"] = 1561077983,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 65,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 2.1666667461395,
          ["EndFrame"] = 65,
          ["EndTime"] = 2.1666667461395,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossDeerTita/A_Boss_DeerTita_001_attack_Montage.A_Boss_DeerTita_001_attack_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [2] = {
          ["AngularSpeedUpperLimit"] = 360,
          ["CheckBlock"] = true,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.1000000238419,
          ["EndTime"] = 1.1000000238419,
          ["Index"] = 0,
          ["RotateDirection"] = 0,
          ["SelectionRuleID"] = 0,
          ["StartTime"] = 0,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 37,
          ["bFallbackOnNoTarget"] = false,
        },
        [3] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 0.90000003576279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.0333335399628,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/RayBieber_FX_New/Attack01/NS_RayBieber_Attack01.NS_RayBieber_Attack01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 60,
            ["Roll"] = 160,
            ["Yaw"] = 90,
          },
          ["RotationQuat"] = {
            ["W"] = 0.4545,
            ["X"] = -0.5417,
            ["Y"] = -0.6645,
            ["Z"] = -0.2418,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.1333334445953,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bFadeoutWhenBorn"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = false,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["bWorldTransform"] = false,
          ["maxGazeDuration"] = 0,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data