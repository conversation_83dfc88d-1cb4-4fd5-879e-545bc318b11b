local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat/A_Monster_Redhat_Dead.A_Monster_Redhat_Dead",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1029241559,
      ["B"] = 1299855266,
      ["C"] = -2022990946,
      ["D"] = -1169074971,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 5.0000004768372,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 265392307,
        ["B"] = 1112873152,
        ["C"] = -934527564,
        ["D"] = 1238716782,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 74,
            ["Duration"] = 5.0000004768372,
            ["EndFrame"] = 74,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterRedhat/MonsterRedhat/A_Monster_Redhat_Dead.A_Monster_Redhat_Dead",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 1.8000000715256,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/89001529_BPT_AS_MaterialEffect_C_0_5D305F9D14A001D6.89001529_BPT_AS_MaterialEffect_C_0_5D305F9D14A001D6",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 3,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 3.2000002861023,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideEdgeColor"] = false,
              ["bOverrideEdgeIntensity"] = false,
              ["bOverrideEdgePow"] = false,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 0,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [3] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 4.0666670799255,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = false,
            ["BRecover"] = true,
            ["BReverse"] = false,
            ["Duration"] = 0.93333339691162,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data