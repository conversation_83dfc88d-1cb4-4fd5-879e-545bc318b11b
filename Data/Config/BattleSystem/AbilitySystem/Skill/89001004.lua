local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossPriest_001/A_Boss_Priest_001_Skill03_Montage.A_Boss_Priest_001_Skill03_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 2087885247,
      ["B"] = 1093161602,
      ["C"] = 882978690,
      ["D"] = -622261597,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 89001004,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 2,
        [3] = 4,
        [4] = 5,
        [5] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
        [5] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 5.1000003814697,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -801959170,
        ["B"] = 1273694587,
        ["C"] = -1647555701,
        ["D"] = 2001176121,
      },
      ["ServerTaskEndList"] = {
        [1] = 3,
        [2] = 2,
        [3] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 5.1000003814697,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 153,
            ["Duration"] = 5.1000003814697,
            ["EndFrame"] = 153,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossPriest_001/A_Boss_Priest_001_Skill03_Montage.A_Boss_Priest_001_Skill03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1.9333333969116,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 0,
            ["CheckBlock"] = true,
            ["Duration"] = 1.9333333969116,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 1.6666667461395,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["Pos"] = {
              ["BasePointArgs"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["BasePointType"] = 3,
              ["ClockwiseRotation"] = 0,
              ["PosNum"] = 1,
              ["ShapeArgs"] = {
              },
              ["ShapeType"] = 0,
            },
            ["TaskTargetType"] = 3,
            ["TaskType"] = 229,
          },
        },
        [4] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 0,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 12,
            },
            ["Duration"] = 0.83333337306976,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = 3000,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.8333,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 2.8333334922791,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["CollisionBoxSize"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["ComponentVarName"] = "CapsuleComponent",
            ["Duration"] = 0.83333337306976,
            ["Index"] = 0,
            ["NewCollisionChannelInfos"] = {
            },
            ["NewCollisionState"] = 0,
            ["NewObjectType"] = 0,
            ["TaskType"] = 64,
            ["bChangeCollisionState"] = false,
            ["bChangeObjectType"] = false,
            ["bOverrideCollisionBoxSize"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data