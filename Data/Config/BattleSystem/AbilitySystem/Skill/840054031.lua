local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 5,
        [2] = 1,
        [3] = 3,
        [4] = 4,
        [5] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
        [5] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 33.************,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 410171356,
        ["B"] = 1143441694,
        ["C"] = -1178732106,
        ["D"] = -260646944,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 33.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "VB Effect",
            ["Duration"] = 33.************,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Skill02/NS_Boss_Medici_Skill02_Buff.NS_Boss_Medici_Skill02_Buff",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 120,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 34.400001525879,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BloomParams"] = {
              ["Bloom1Size"] = 0,
              ["Bloom1Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom2Size"] = 0,
              ["Bloom2Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom3Size"] = 0,
              ["Bloom3Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom4Size"] = 0,
              ["Bloom4Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom5Size"] = 0,
              ["Bloom5Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom6Size"] = 0,
              ["Bloom6Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["BloomConvolutionBufferScale"] = 0,
              ["BloomConvolutionCenterUV"] = {
                ["X"] = 0,
                ["Y"] = 0,
              },
              ["BloomConvolutionPreFilterMax"] = 0,
              ["BloomConvolutionPreFilterMin"] = 0,
              ["BloomConvolutionPreFilterMult"] = 0,
              ["BloomConvolutionSize"] = 0,
              ["BloomConvolutionTexture"] = "None",
              ["BloomIntensity"] = 0,
              ["BloomMethod"] = 1,
              ["BloomSizeScale"] = 0,
              ["BloomThreshold"] = 0,
              ["bOverride_Bloom1Size"] = false,
              ["bOverride_Bloom1Tint"] = false,
              ["bOverride_Bloom2Size"] = false,
              ["bOverride_Bloom2Tint"] = false,
              ["bOverride_Bloom3Size"] = false,
              ["bOverride_Bloom3Tint"] = false,
              ["bOverride_Bloom4Size"] = false,
              ["bOverride_Bloom4Tint"] = false,
              ["bOverride_Bloom5Size"] = false,
              ["bOverride_Bloom5Tint"] = false,
              ["bOverride_Bloom6Size"] = false,
              ["bOverride_Bloom6Tint"] = false,
              ["bOverride_BloomConvolutionBufferScale"] = false,
              ["bOverride_BloomConvolutionCenterUV"] = false,
              ["bOverride_BloomConvolutionPreFilterMax"] = false,
              ["bOverride_BloomConvolutionPreFilterMin"] = false,
              ["bOverride_BloomConvolutionPreFilterMult"] = false,
              ["bOverride_BloomConvolutionSize"] = false,
              ["bOverride_BloomConvolutionTexture"] = false,
              ["bOverride_BloomIntensity"] = false,
              ["bOverride_BloomMethod"] = false,
              ["bOverride_BloomSizeScale"] = false,
              ["bOverride_BloomThreshold"] = false,
            },
            ["BlurInvertParameter"] = 0,
            ["BlurMaskRadiusParameter"] = 0,
            ["BlurMaskSoftnessParameter"] = 0,
            ["BlurStrideParameter"] = 0,
            ["CenterParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorSplitStrideParameter"] = 0,
            ["ContrastParameter"] = 0,
            ["DOFParams"] = {
              ["DepthBlurAmount"] = 1,
              ["DepthBlurRadius"] = 100,
              ["DepthOfFieldBladeCount"] = 5,
              ["DepthOfFieldFstop"] = 4,
              ["DepthOfFieldMinFstop"] = 1.2,
              ["FarBlurSize"] = 15,
              ["FarTransitionRegion"] = 8000,
              ["FocalDistance"] = 300,
              ["FocalRegion"] = 0,
              ["NearBlurSize"] = 15,
              ["NearTransitionRegion"] = 300,
              ["Scale"] = 1,
              ["SensorWidth"] = 60,
              ["SqueezeFactor"] = 2,
              ["bMobileHQGaussian"] = false,
            },
            ["DesaturateParameter"] = 0,
            ["Duration"] = 34.400001525879,
            ["ExposureBias"] = 0,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FocalRegionUpdateType"] = 0,
            ["Index"] = 0,
            ["IntensityParameter"] = 0,
            ["InterruptMode"] = 0,
            ["InvertColorBParameter"] = 0,
            ["InvertColorGParameter"] = 0,
            ["InvertColorRParameter"] = 0,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0,
            ["MaskSoftnessParameter"] = 0,
            ["Material"] = "/Game/Arts/Effects/FX_Character/Boss_Medici_Fx/Skill02/MI_Boss_Medici_Skill02_FireScreen.MI_Boss_Medici_Skill02_FireScreen",
            ["PostProcessSettings"] = {
              ["BloomParams"] = {
                ["BloomParams"] = {
                  ["Bloom1Size"] = 0,
                  ["Bloom1Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom2Size"] = 0,
                  ["Bloom2Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom3Size"] = 0,
                  ["Bloom3Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom4Size"] = 0,
                  ["Bloom4Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom5Size"] = 0,
                  ["Bloom5Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom6Size"] = 0,
                  ["Bloom6Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["BloomConvolutionBufferScale"] = 0,
                  ["BloomConvolutionCenterUV"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                  },
                  ["BloomConvolutionPreFilterMax"] = 0,
                  ["BloomConvolutionPreFilterMin"] = 0,
                  ["BloomConvolutionPreFilterMult"] = 0,
                  ["BloomConvolutionSize"] = 0,
                  ["BloomConvolutionTexture"] = "None",
                  ["BloomIntensity"] = 0,
                  ["BloomMethod"] = 1,
                  ["BloomSizeScale"] = 0,
                  ["BloomThreshold"] = 0,
                  ["bOverride_Bloom1Size"] = false,
                  ["bOverride_Bloom1Tint"] = false,
                  ["bOverride_Bloom2Size"] = false,
                  ["bOverride_Bloom2Tint"] = false,
                  ["bOverride_Bloom3Size"] = false,
                  ["bOverride_Bloom3Tint"] = false,
                  ["bOverride_Bloom4Size"] = false,
                  ["bOverride_Bloom4Tint"] = false,
                  ["bOverride_Bloom5Size"] = false,
                  ["bOverride_Bloom5Tint"] = false,
                  ["bOverride_Bloom6Size"] = false,
                  ["bOverride_Bloom6Tint"] = false,
                  ["bOverride_BloomConvolutionBufferScale"] = false,
                  ["bOverride_BloomConvolutionCenterUV"] = false,
                  ["bOverride_BloomConvolutionPreFilterMax"] = false,
                  ["bOverride_BloomConvolutionPreFilterMin"] = false,
                  ["bOverride_BloomConvolutionPreFilterMult"] = false,
                  ["bOverride_BloomConvolutionSize"] = false,
                  ["bOverride_BloomConvolutionTexture"] = false,
                  ["bOverride_BloomIntensity"] = false,
                  ["bOverride_BloomMethod"] = false,
                  ["bOverride_BloomSizeScale"] = false,
                  ["bOverride_BloomThreshold"] = false,
                },
              },
              ["ColorAdjustParams"] = {
                ["Contrast"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Desaturate"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorB"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorG"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorR"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["bEnableDesaturateProgress"] = false,
              },
              ["ColorSplitRadialBlurParams"] = {
                ["BlurInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["ColorSplitStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["CustomMaterialParams"] = {
                ["CustomMaterial"] = "",
                ["MainCharPositionParamName"] = "",
                ["Scalars"] = {
                },
                ["Texture"] = {
                },
                ["Vectors"] = {
                },
                ["bFixBlendWeight"] = false,
              },
              ["DOFParams"] = {
                ["DOFParams"] = {
                  ["DepthBlurAmount"] = 1,
                  ["DepthBlurRadius"] = 100,
                  ["DepthOfFieldBladeCount"] = 5,
                  ["DepthOfFieldFstop"] = 4,
                  ["DepthOfFieldMinFstop"] = 1.2,
                  ["FarBlurSize"] = 15,
                  ["FarTransitionRegion"] = 8000,
                  ["FocalDistance"] = 300,
                  ["FocalRegion"] = 0,
                  ["NearBlurSize"] = 15,
                  ["NearTransitionRegion"] = 300,
                  ["Scale"] = 1,
                  ["SensorWidth"] = 60,
                  ["SqueezeFactor"] = 2,
                  ["bMobileHQGaussian"] = false,
                },
              },
              ["DarkenParams"] = {
                ["ExposureBias"] = 0,
              },
              ["PhantomParams"] = {
                ["Direction"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Gap"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Speed"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["PostProcessType"] = 0,
              ["Priority"] = 0,
              ["RGBSplitParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["RadialBlurParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 1,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0.1,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["VignetteParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Color"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Radius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
            },
            ["PostProcessType"] = 9,
            ["Priority"] = 0,
            ["Radius"] = 3000,
            ["RadiusParameter"] = 0,
            ["Scalars"] = {
              [""] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 0,
                ["Max"] = 0,
                ["Min"] = 0,
                ["ParamName"] = "",
              },
            },
            ["SoftnessParameter"] = 0,
            ["StrideParameter"] = 0,
            ["TargetType"] = 0,
            ["TaskType"] = 126,
            ["Textures"] = {
            },
            ["Vectors"] = {
            },
            ["bUseNewPostProcessSetting"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 33.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RoseGhost/Boss_Medici/Play_Boss_Medici_Skill01_BurningHeart_SpellLoop.Play_Boss_Medici_Skill01_BurningHeart_SpellLoop",
            ["BlendOutType"] = 4,
            ["Duration"] = 33.************,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 33.************,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BoneDock"] = "root",
            ["Color"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Color",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
            ["Control"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_ring_sdf.MI_Decal_Yujing_ring_sdf",
            ["DecalSize"] = {
              ["X"] = 300,
              ["Y"] = 500,
              ["Z"] = 500,
            },
            ["Duration"] = 33.************,
            ["DynamicMaterialParams"] = {
              [1] = {
                ["FloatTimeCurveNew"] = {
                  ["CurveAsset"] = "",
                },
                ["InitFloatValue"] = 0,
                ["InitLinearColorValue"] = {
                  ["A"] = 1,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["InitTextureValue"] = "",
                ["InitVectorValue"] = {
                  ["X"] = 0,
                  ["Y"] = 0,
                  ["Z"] = 0,
                },
                ["IsLoop"] = false,
                ["ParamName"] = "None",
                ["ParamType"] = 0,
                ["VectorTimeCurveNew"] = {
                  ["CurveAsset"] = "",
                },
                ["bNeedEdit"] = false,
              },
            },
            ["ExtraGroundMsg"] = {
              ["X"] = -500,
              ["Y"] = 500,
              ["Z"] = 0.2,
            },
            ["GlowColor"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "GlowColor",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InsideRadius"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "InsideRadius",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["OriginSpecialName"] = "None",
            ["RotateAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "RotateAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["Rotation"] = {
              ["X"] = 0,
              ["Y"] = -90,
              ["Z"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SectorAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "SectorAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 40,
            ["Translation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["UberGraphFrame"] = {
            },
            ["bAttachRotation"] = false,
            ["bAttachScale"] = true,
            ["bNeedAttach"] = true,
            ["bNeedCheckGround"] = true,
            ["bWorldTransform"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ConfigID"] = 6406543,
            ["ConfigIDs"] = {
            },
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["ParamMap"] = {
            },
            ["TDUITypeEnum"] = 0,
            ["TargetMode"] = 0,
            ["TaskType"] = 18,
            ["UIName"] = "",
            ["UIType"] = 2,
            ["bShowInPuppetSlot"] = false,
            ["bStopReminderOnStateEnd"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1352089684,
        ["B"] = 1098312379,
        ["C"] = -126907724,
        ["D"] = 1233876911,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 150055072,
        ["B"] = 1175417901,
        ["C"] = 1374034364,
        ["D"] = 399621646,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data