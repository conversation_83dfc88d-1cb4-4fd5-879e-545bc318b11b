local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1612429344,
      ["B"] = 1236355462,
      ["C"] = -284721752,
      ["D"] = 1850444552,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 1,
        [3] = 3,
        [4] = 5,
        [5] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 5,
        [5] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.60000002384186,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1662972466,
        ["B"] = 1192653898,
        ["C"] = 1016129214,
        ["D"] = 1010513670,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 6,
            ["ClientTimelineIDForDash"] = 0,
            ["ClientTimelineIDForIdle"] = 0,
            ["Duration"] = 0.60000002384186,
            ["IdleMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpWarrior/Warrior_DoubleJump_From_Idle_Montage.Warrior_DoubleJump_From_Idle_Montage",
            ["Index"] = 0,
            ["InterruptBlendOut"] = 6,
            ["LocoInputMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpWarrior/Warrior_DoubleJump_From_Dash_Montage.Warrior_DoubleJump_From_Dash_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 121,
            ["bFinishMontageWhenTaskEnd"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 0.56666672229767,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ArgSourceMode"] = 1,
            ["Duration"] = 0.56666672229767,
            ["Index"] = 0,
            ["InterpolationMode"] = 1,
            ["MaxRotateSpeed"] = 90,
            ["MovementOutputMode"] = 1,
            ["OverrideGravityScale"] = 0,
            ["OverrideThruster"] = {
              ["X"] = -1,
              ["Y"] = -1,
              ["Z"] = -1,
            },
            ["RootmotionScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["SurfAngles"] = {
              ["W"] = 80,
              ["X"] = 0.1,
              ["Y"] = 0.5,
              ["Z"] = 10,
            },
            ["TaskType"] = 131,
            ["bAllowSurfMode"] = false,
            ["bAllowThruster"] = false,
            ["bAllowYawController"] = false,
            ["bNeedReset"] = true,
            ["bSetRootmotionScale"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 0.56666672229767,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.56666672229767,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.1000000089407,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Warrior_FX/DoubleJump/NS_Warrior_DoubleJump_From_Idle_Airwave.NS_Warrior_DoubleJump_From_Idle_Airwave",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -60,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data