local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 2,
        [4] = 3,
        [5] = 4,
        [6] = 6,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 5,
        [3] = 2,
        [4] = 3,
        [5] = 6,
        [6] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 6.6333336830139,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1496158119,
        ["B"] = 1332927988,
        ["C"] = 48081338,
        ["D"] = -1864218029,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.90000003576279,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.90000003576279,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/2049_FX/Skill01/NS_2049Skill_Common_Change.NS_2049Skill_Common_Change",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 6.466667175293,
          ["StartTime"] = 0.5,
          ["TaskData"] = {
            ["AnimSequence"] = "/Game/Arts/Character/Animation/Monster/Monster_2_049/A_2049_Death_Montage.A_2049_Death_Montage",
            ["AttachSocket"] = "None",
            ["AttachTime"] = -1,
            ["BPAnimPath"] = "",
            ["Duration"] = 5.966667175293,
            ["EnableLight"] = false,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MeshName"] = "",
            ["NeedAttach"] = false,
            ["NeedLoop"] = false,
            ["Rotation"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 3,
              ["Y"] = 3,
              ["Z"] = 3,
            },
            ["SkeletalMesh"] = "/Game/Arts/Character/Model/Monster/Monster_2_049/SK_Monster_Puppet_2049.SK_Monster_Puppet_2049",
            ["StaticMesh"] = "None",
            ["TaskTargetType"] = 0,
            ["TaskType"] = 8,
            ["Translation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["UseLOD0"] = false,
            ["bAttachToCamera"] = false,
            ["bCopyCurFrameAnim"] = false,
            ["bFollowParentBound"] = true,
            ["bInherit"] = false,
            ["bOnlySelf"] = false,
            ["bStickGround"] = true,
            ["bUseStaticMesh"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 6.466667175293,
          ["StartTime"] = 0.5,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 5.966667175293,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [4] = {
          ["EndTime"] = 6.9000005722046,
          ["StartTime"] = 6.0000004768372,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.90000003576279,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/2049_FX/Skill01/NS_2049Skill_Common_Change.NS_2049Skill_Common_Change",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 5.966667175293,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Mobs/Play_Monster_MobPuppet_Performance.Play_Monster_MobPuppet_Performance",
            ["BlendOutType"] = 4,
            ["Duration"] = 5.966667175293,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 7.2666668891907,
          ["StartTime"] = 5.9333338737488,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Mobs/Play_Monster_MobPuppet_Performance_End.Play_Monster_MobPuppet_Performance_End",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.3333333730698,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1808805651,
        ["B"] = 1259564707,
        ["C"] = -1855244413,
        ["D"] = -623285491,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -78256183,
        ["B"] = 1109550504,
        ["C"] = -1898043752,
        ["D"] = -1743994792,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data