local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_Start.A_Monster_Thug_Skill03_Start",
    [2] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_Loop_Montage.A_Monster_Thug_Skill03_Loop_Montage",
    [3] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_End.A_Monster_Thug_Skill03_End",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 334840055,
      ["B"] = 1108307973,
      ["C"] = -315778114,
      ["D"] = -773393649,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 2,
        [4] = 7,
        [5] = 5,
        [6] = 6,
        [7] = 8,
        [8] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 4,
        [4] = 5,
        [5] = 7,
        [6] = 2,
        [7] = 3,
        [8] = 8,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 9.3333339691162,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 893203034,
        ["B"] = 1255063757,
        ["C"] = 1733831329,
        ["D"] = -1901026455,
      },
      ["ServerTaskEndList"] = {
        [1] = 9,
        [2] = 10,
      },
      ["ServerTaskStartList"] = {
        [1] = 10,
        [2] = 9,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.5000002384186,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 75,
            ["Duration"] = 2.5000002384186,
            ["EndFrame"] = 75,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_Start.A_Monster_Thug_Skill03_Start",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 7.3000001907349,
          ["StartTime"] = 2.5000002384186,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 180,
            ["Duration"] = 4.8000001907349,
            ["EndFrame"] = 180,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.25,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_Loop_Montage.A_Monster_Thug_Skill03_Loop_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 9.3333339691162,
          ["StartTime"] = 7.3000001907349,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 61,
            ["Duration"] = 2.0333335399628,
            ["EndFrame"] = 61,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/Monster_Thug/A_Monster_Thug_Skill03_End.A_Monster_Thug_Skill03_End",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 2.8666667938232,
          ["StartTime"] = 0.033333335071802,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_r",
            ["Duration"] = 2.8333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Thug_FX/Skill02/NS_Thug_skill02_star.NS_Thug_skill02_star",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 70,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 0.5,
              ["Y"] = 0.5,
              ["Z"] = 0.5,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 7.3666672706604,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 5.3666667938232,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 1,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Thug_FX/Skill02/NS_Thug_skill02_loop.NS_Thug_skill02_loop",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 8.6000003814697,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
                [1] = 0,
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 2,
                ["EndVal"] = 1,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 1,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 2,
                ["EndVal"] = 1,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 8.6000003814697,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
                [1] = 0,
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 10,
                  ["G"] = 2.7,
                  ["R"] = 3.6,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 1,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 2.4,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "SM_Monster_DHhatchet_Weapon_001",
              ["SearchMeshType"] = 2,
              ["bOverrideEdgeColor"] = true,
              ["bOverrideEdgeIntensity"] = true,
              ["bOverrideEdgePow"] = true,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 1,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "/Game/Arts/Effects/FX_Character/2049_FX/Born/MI_2049Born_Body_Fresnel.MI_2049Born_Body_Fresnel",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [7] = {
          ["EndTime"] = 7.3333334922791,
          ["StartTime"] = 2.3333334922791,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
                [1] = 0,
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 2,
                ["EndVal"] = 1,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 1,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 2,
                ["EndVal"] = 1,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 5.0000004768372,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 10,
                  ["G"] = 3.5417,
                  ["R"] = 4.3379,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 1,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 10,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "SM_Monster_DHhatchet_Weapon_001",
              ["SearchMeshType"] = 1,
              ["bOverrideEdgeColor"] = true,
              ["bOverrideEdgeIntensity"] = true,
              ["bOverrideEdgePow"] = true,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 1,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "/Game/Arts/Effects/FX_Character/2049_FX/Born/MI_2049Born_Body_Fresnel.MI_2049Born_Body_Fresnel",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [8] = {
          ["EndTime"] = 8.966667175293,
          ["StartTime"] = 7.3000001907349,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.6666667461395,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Thug_FX/Skill02/NS_Thug_skill02_end.NS_Thug_skill02_end",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 7.4000005722046,
          ["StartTime"] = 2.0333335399628,
          ["TaskData"] = {
            ["Duration"] = 5.3666667938232,
            ["Index"] = 0,
            ["NearRadius"] = 50,
            ["Speed"] = 710,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 113,
            ["bStopOnOverlap"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data