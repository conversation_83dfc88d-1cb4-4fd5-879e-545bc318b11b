local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterGouluHound_001/A_Monster_GouluHound_001_Skill02_Montage.A_Monster_GouluHound_001_Skill02_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1494190965,
      ["B"] = 1265517825,
      ["C"] = -1082878535,
      ["D"] = -225939945,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 5,
        [3] = 1,
        [4] = 6,
        [5] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 3,
        [4] = 5,
        [5] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.0000002384186,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1709671588,
        ["B"] = 1204688829,
        ["C"] = -1749154169,
        ["D"] = 333403181,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.3333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 70,
            ["Duration"] = 2.3333334922791,
            ["EndFrame"] = 70,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterGouluHound_001/A_Monster_GouluHound_001_Skill02_Montage.A_Monster_GouluHound_001_Skill02_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 3.0000002384186,
          ["StartTime"] = 2.3333334922791,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = false,
            ["BReverse"] = true,
            ["Duration"] = 0.66666668653488,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [3] = {
          ["EndTime"] = 0.40000003576279,
          ["StartTime"] = 0.20000001788139,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.20000001788139,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Goulu/mechanic01/GouluHound/NS_Boss_Goulu_mechanic01_Hound_05.NS_Boss_Goulu_mechanic01_Hound_05",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 2.3666667938232,
          ["StartTime"] = 2.3666667938232,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 44,
          },
        },
        [5] = {
          ["EndTime"] = 2.1666667461395,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["BPName"] = "",
            ["BlackenMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["BlackenAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
            },
            ["DissolveMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Dissolve"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 2,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["DissolveEdgeWidth"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveNoiseIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveProgress"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["DissolveType"] = 0,
              ["DitherAlpha"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 1,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "/Game/Arts/Character/Model/Monster/MonsterZombie/Materials/MI_DeathDissolveFromBlack.MI_DeathDissolveFromBlack",
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideDissolve"] = false,
              ["bOverrideDissolveEdgeColor"] = false,
              ["bOverrideDissolveEdgeWidth"] = false,
              ["bOverrideDissolveNoiseIntensity"] = false,
              ["bOverrideDissolveProgress"] = true,
            },
            ["Duration"] = 1.8333333730698,
            ["EdgeMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["EdgeColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 1,
                  ["B"] = 0.4508,
                  ["G"] = 1,
                  ["R"] = 0.8148,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["EdgeIntensity"] = {
                ["ConstParamValue"] = 20,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["EdgePow"] = {
                ["ConstParamValue"] = 15,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialSlotNames"] = {
              },
              ["SearchMaterialType"] = 0,
              ["SearchMeshName"] = "",
              ["SearchMeshType"] = 1,
              ["bOverrideEdgeColor"] = true,
              ["bOverrideEdgeIntensity"] = true,
              ["bOverrideEdgePow"] = true,
            },
            ["EffectivePartType"] = 0,
            ["FresnelMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["FresnelIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["GrayMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["Height"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 1,
              ["Width"] = {
                ["ConstParamValue"] = 50,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideHeight"] = false,
              ["bOverrideWidth"] = false,
            },
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["MaterialEffectType"] = 1,
            ["ModifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 0,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["OpacityMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Opacity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
            },
            ["PetrifyMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["DissolveMove"] = {
                ["ConstParamValue"] = -10000,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["SearchMaterialParams"] = {
                ["MaterialSlotNames"] = {
                },
                ["SearchMaterialType"] = 2,
                ["SearchMeshName"] = "",
                ["SearchMeshType"] = 1,
              },
            },
            ["Priority"] = 100,
            ["SurfaceMaterialParams"] = {
              ["AffectedAttachEntityTypes"] = {
              },
              ["MainTexColor"] = {
                ["ConstParamValue"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["ParamUpdateType"] = 0,
                ["StartVal"] = {
                  ["A"] = 0,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["bEnableLoop"] = false,
              },
              ["MainTexColorIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["MaterialPath"] = "",
              ["MaterialSlotNames"] = {
              },
              ["Radius"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["SearchMaterialType"] = 2,
              ["SearchMeshName"] = "Mesh",
              ["SearchMeshType"] = 2,
              ["SurfaceIntensity"] = {
                ["ConstParamValue"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["Duration"] = 0,
                ["EndVal"] = 0,
                ["ParamUpdateType"] = 0,
                ["StartVal"] = 0,
                ["bEnableLoop"] = false,
              },
              ["bOverrideMainTexColor"] = false,
              ["bOverrideMainTexColorIntensity"] = false,
              ["bOverrideRadius"] = false,
              ["bOverrideSurfaceIntensity"] = false,
            },
            ["TaskType"] = 193,
          },
        },
        [6] = {
          ["EndTime"] = 2.4333333969116,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_GouluCub/Play_Boss_GouluCub_DiePoisonPool.Play_Boss_GouluCub_DiePoisonPool",
            ["BlendOutType"] = 4,
            ["Duration"] = 2.4333333969116,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data