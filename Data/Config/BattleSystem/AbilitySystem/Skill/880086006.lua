local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Blueprint/CombatSystem/Curve/80009106_BPT_AS_PlayDecal_C_0_5D8D298FA3C0002D.80009106_BPT_AS_PlayDecal_C_0_5D8D298FA3C0002D",
  },
  ["StartNode"] = {
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 5.0000004768372,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 38798789,
        ["B"] = 1340784123,
        ["C"] = 347006875,
        ["D"] = 1190679730,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.0000002384186,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BoneDock"] = "None",
            ["Color"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Color",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
            ["Control"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "Control",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["DecalAssetPath"] = "/Game/Arts/Effects/FX_Common/Decal/MI_Decal_Yujing_SDF_Ring01.MI_Decal_Yujing_SDF_Ring01",
            ["DecalSize"] = {
              ["X"] = 200,
              ["Y"] = 5000,
              ["Z"] = 5000,
            },
            ["Duration"] = 3.0000002384186,
            ["DynamicMaterialParams"] = {
              [1] = {
                ["FloatTimeCurveNew"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/880086006_BPT_AS_PlayDecal_C_0_5DA4260B0340050C.880086006_BPT_AS_PlayDecal_C_0_5DA4260B0340050C",
                },
                ["InitFloatValue"] = 0,
                ["InitLinearColorValue"] = {
                  ["A"] = 1,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["InitTextureValue"] = "",
                ["InitVectorValue"] = {
                  ["X"] = 0,
                  ["Y"] = 0,
                  ["Z"] = 0,
                },
                ["IsLoop"] = false,
                ["ParamName"] = "InsideRadius",
                ["ParamType"] = 0,
                ["VectorTimeCurveNew"] = {
                  ["CurveAsset"] = "",
                },
                ["bNeedEdit"] = true,
              },
              [2] = {
                ["FloatTimeCurveNew"] = {
                  ["CurveAsset"] = "/Game/Blueprint/CombatSystem/Curve/880086006_BPT_AS_PlayDecal_C_0_5DA4264DB460050D.880086006_BPT_AS_PlayDecal_C_0_5DA4264DB460050D",
                },
                ["InitFloatValue"] = 0,
                ["InitLinearColorValue"] = {
                  ["A"] = 1,
                  ["B"] = 0,
                  ["G"] = 0,
                  ["R"] = 0,
                },
                ["InitTextureValue"] = "",
                ["InitVectorValue"] = {
                  ["X"] = 0,
                  ["Y"] = 0,
                  ["Z"] = 0,
                },
                ["IsLoop"] = false,
                ["ParamName"] = "Control",
                ["ParamType"] = 0,
                ["VectorTimeCurveNew"] = {
                  ["CurveAsset"] = "",
                },
                ["bNeedEdit"] = true,
              },
            },
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["GlowColor"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "GlowColor",
              ["ParamType"] = 3,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InsideRadius"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "InsideRadius",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = true,
            },
            ["OriginSpecialName"] = "None",
            ["RotateAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "RotateAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["Rotation"] = {
              ["X"] = 0,
              ["Y"] = -90,
              ["Z"] = 0,
            },
            ["Scale3D"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SectorAngle"] = {
              ["FloatTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["InitFloatValue"] = 0,
              ["InitLinearColorValue"] = {
                ["A"] = 1,
                ["B"] = 0,
                ["G"] = 0,
                ["R"] = 0,
              },
              ["InitTextureValue"] = "",
              ["InitVectorValue"] = {
                ["X"] = 0,
                ["Y"] = 0,
                ["Z"] = 0,
              },
              ["IsLoop"] = false,
              ["ParamName"] = "SectorAngle",
              ["ParamType"] = 0,
              ["VectorTimeCurveNew"] = {
                ["CurveAsset"] = "",
              },
              ["bNeedEdit"] = false,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 40,
            ["Translation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -50,
            },
            ["UberGraphFrame"] = {
            },
            ["bAttachRotation"] = false,
            ["bAttachScale"] = false,
            ["bNeedAttach"] = false,
            ["bNeedCheckGround"] = false,
            ["bWorldTransform"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 3.0000002384186,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 2,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 10.000000953674,
            ["FadeInCurve"] = "/Game/Blueprint/CombatSystem/Curve/80009106_BPT_AS_PlayDecal_C_0_5D8D298FA3C0002D.80009106_BPT_AS_PlayDecal_C_0_5D8D298FA3C0002D",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 1,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_XSRQ/Skill07/NS_XSZS_skill07_Boom01.NS_XSZS_skill07_Boom01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 150,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.2,
              ["Y"] = 1.2,
              ["Z"] = 1.2,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = true,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [2] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1601678033,
        ["B"] = 1129961379,
        ["C"] = -2117523786,
        ["D"] = 1063223023,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
    [3] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.033333335071802,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1162031745,
        ["B"] = 1160272062,
        ["C"] = -1756743261,
        ["D"] = 1341135045,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data