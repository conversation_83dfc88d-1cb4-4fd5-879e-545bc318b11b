local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -607233273,
      ["B"] = 1310131453,
      ["C"] = -16609633,
      ["D"] = -124301810,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.3333333730698,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1209862672,
        ["B"] = 1138929857,
        ["C"] = -24773962,
        ["D"] = 1123354294,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 1.3333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 1.3333333730698,
            ["Index"] = 0,
            ["MontageAsset"] = "/Game/Arts/Character/Animation/Boss/BossQSKJ/BossQSKJ_02/A_Boss_KnightWithHrose_Turn_180_Left_Montage.A_Boss_KnightWithHrose_Turn_180_Left_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 102,
          },
        },
        [2] = {
          ["EndTime"] = 1.3333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 1.3333333730698,
            ["Index"] = 0,
            ["RootmotionMoveScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskType"] = 172,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data