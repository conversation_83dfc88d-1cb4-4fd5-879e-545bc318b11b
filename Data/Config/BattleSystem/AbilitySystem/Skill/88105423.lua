local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossUlorus/A_Boss_Ulorus_Skill04_Montage.A_Boss_Ulorus_Skill04_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 635098685,
      ["B"] = 1158575195,
      ["C"] = 1448132483,
      ["D"] = -754140981,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 3,
        [3] = 10,
        [4] = 1,
        [5] = 5,
        [6] = 6,
        [7] = 7,
        [8] = 11,
        [9] = 2,
        [10] = 8,
        [11] = 9,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 5,
        [3] = 6,
        [4] = 7,
        [5] = 10,
        [6] = 3,
        [7] = 4,
        [8] = 11,
        [9] = 1,
        [10] = 8,
        [11] = 9,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 2.6333334445953,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1733483752,
        ["B"] = 1284080461,
        ["C"] = 154723993,
        ["D"] = -479070663,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 0.20000001788139,
          ["EndTime"] = 1.6000001430511,
          ["Index"] = 0,
          ["StartTime"] = 1.4000000953674,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 118,
          ["UseStaticBlackboard"] = false,
        },
        [2] = {
          ["AnimType"] = 0,
          ["BlendInTime"] = 3,
          ["BlendOutTime"] = 3,
          ["BlendOutWhenInterrupt"] = 3,
          ["ClipLength"] = 88,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 2.6333334445953,
          ["EndFrame"] = 88,
          ["EndTime"] = 2.6333334445953,
          ["FullBodyMask"] = 0,
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["LibAssetID"] = {
            ["AnimID"] = "",
            ["AssetID"] = "",
            ["StateName"] = "",
          },
          ["PlayRate"] = 1,
          ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossUlorus/A_Boss_Ulorus_Skill04_Montage.A_Boss_Ulorus_Skill04_Montage",
          ["StartFrame"] = 0,
          ["StartTime"] = 0,
          ["TaskType"] = 101,
          ["UpdateBound"] = false,
          ["bCommonIdle"] = false,
          ["bHoldWeapon"] = true,
          ["bNotInterrupt"] = false,
        },
        [3] = {
          ["BHiddenHeadInfo"] = false,
          ["BRecover"] = true,
          ["BReverse"] = true,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 0.4333333671093,
          ["EndTime"] = 1.3666667938232,
          ["Index"] = 0,
          ["MeshNames"] = {
          },
          ["Priority"] = 3,
          ["SocketNames"] = {
          },
          ["StartTime"] = 0.93333339691162,
          ["TaskType"] = 14,
        },
        [4] = {
          ["AnimRootInfo"] = {
          },
          ["Collision"] = false,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Curve"] = "",
          ["Destination"] = {
            ["DestArgs"] = {
              [1] = -15460,
              [2] = 64340,
              [3] = -3200,
            },
            ["DestType"] = 13,
          },
          ["Duration"] = 0,
          ["EnableRotate"] = false,
          ["EndTime"] = 1.3000000715256,
          ["Ground"] = true,
          ["Index"] = 0,
          ["MaxDistance"] = -1,
          ["MoveByAnimGUID"] = 0,
          ["RootMotionCurve"] = "",
          ["RootMotionCurveLinearColor"] = "",
          ["StartTime"] = 1.3000000715256,
          ["TaskTargetType"] = 3,
          ["TaskType"] = 228,
          ["Time"] = 0,
          ["bUseAnimRootMotion"] = false,
        },
        [5] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 2.5000002384186,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.5000002384186,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Ulorus/Skill01/NS_Ulorus_Skill01_01.NS_Ulorus_Skill01_01",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [6] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 2.5000002384186,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.5000002384186,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Ulorus/Skill01/NS_Ulorus_Skill01_03.NS_Ulorus_Skill01_03",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [7] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 2.5000002384186,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.5000002384186,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Ulorus/Skill01/NS_Ulorus_Skill01_04.NS_Ulorus_Skill01_04",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [8] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 1.2333333492279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.6333334445953,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Ulorus/Skill01/NS_Ulorus_Skill01_02.NS_Ulorus_Skill01_02",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.4000000953674,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [9] = {
          ["Alpha"] = 1,
          ["AttachComponentName"] = "",
          ["AttachTargetType"] = 0,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["DockBone"] = "None",
          ["Duration"] = 1.2333333492279,
          ["EffectPriority"] = 5,
          ["EndTime"] = 2.6333334445953,
          ["ExtraGroundMsg"] = {
            ["X"] = -300,
            ["Y"] = 300,
            ["Z"] = 0.2,
          },
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0,
          ["FadeOutCurve"] = "",
          ["FloatCurve"] = {
          },
          ["FollowMode"] = 2,
          ["GroundCheckObjectTypes"] = {
            [1] = 0,
          },
          ["Index"] = 0,
          ["InitialOrientation"] = 0,
          ["InterruptMode"] = 0,
          ["Is3DFx"] = false,
          ["MeshColor"] = {
            ["A"] = 1,
            ["B"] = 1,
            ["G"] = 1,
            ["R"] = 1,
          },
          ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_Ulorus/Skill01/NS_Ulorus_Skill01_05.NS_Ulorus_Skill01_05",
          ["NiagaraMobile"] = "",
          ["ParticleColorScaleCurve"] = {
            ["CurveAsset"] = "",
          },
          ["PlayRate"] = 1,
          ["PosOffset"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["RotOffset"] = {
            ["Pitch"] = 0,
            ["Roll"] = 0,
            ["Yaw"] = 0,
          },
          ["RotationQuat"] = {
            ["W"] = 1,
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["ScaleOffset"] = {
            ["X"] = 1,
            ["Y"] = 1,
            ["Z"] = 1,
          },
          ["StartTime"] = 1.4000000953674,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 26,
          ["TranslucencySortDistanceOffset"] = 0,
          ["TranslucencySortPriority"] = 0,
          ["UberGraphFrame"] = {
          },
          ["bAttachScale"] = false,
          ["bBenefitEffect"] = false,
          ["bIgnoreRelationCommonTags"] = false,
          ["bNeedCheckGround"] = true,
          ["bPlayAtLast"] = false,
          ["bSpiritualVision"] = false,
          ["bTowardInstigator"] = false,
          ["bUseWorldCoordinate"] = false,
          ["maxGazeDuration"] = 0,
        },
        [10] = {
          ["AttachSocket"] = "None",
          ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RoseGhost/Boss_Ulorus/Play_Boss_Ulorus_Skill01_Convey01.Play_Boss_Ulorus_Skill01_Convey01",
          ["BlendOutType"] = 4,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.5666667222977,
          ["EndTime"] = 1.5666667222977,
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0.66666668653488,
          ["FadeOutCurve"] = "",
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["RelativeLocation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 42,
          ["bNeedAttach"] = true,
          ["bPlayAtLast"] = false,
          ["bSetWorldLocation"] = false,
        },
        [11] = {
          ["AttachSocket"] = "None",
          ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_RoseGhost/Boss_Ulorus/Play_Boss_Ulorus_Skill01_Convey02.Play_Boss_Ulorus_Skill01_Convey02",
          ["BlendOutType"] = 4,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 1.2333333492279,
          ["EndTime"] = 2.5333335399628,
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0.66666668653488,
          ["FadeOutCurve"] = "",
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["RelativeLocation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["StartTime"] = 1.3000000715256,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 42,
          ["bNeedAttach"] = true,
          ["bPlayAtLast"] = false,
          ["bSetWorldLocation"] = false,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data