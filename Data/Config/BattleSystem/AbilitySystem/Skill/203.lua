local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1572685471,
      ["B"] = 1105366705,
      ["C"] = -721484635,
      ["D"] = 1057344184,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
      },
      ["ClientTaskStartList"] = {
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 736669136,
        ["B"] = 1123331744,
        ["C"] = -852700535,
        ["D"] = 686983905,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["ResumeProps"] = {
              [1] = {
                [1] = "Hp",
                [2] = "MaxHp",
                [3] = 0,
                [4] = 30,
              },
            },
            ["TaskTargetType"] = 3,
            ["TaskType"] = 78,
            ["bRequestOrDirectly"] = true,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data