local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Common/World/Action/A_M_Push_People_I.A_M_Push_People_I",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -573294896,
      ["B"] = 1126939730,
      ["C"] = 56976003,
      ["D"] = 279522142,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 1,
        [3] = 3,
        [4] = 4,
        [5] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
        [5] = 5,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.1666667461395,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -202147302,
        ["B"] = 1290881715,
        ["C"] = -1439198031,
        ["D"] = 1231159915,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.76666671037674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 5,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 41,
            ["Duration"] = 0.76666671037674,
            ["EndFrame"] = 56,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 2,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.7826,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/World/Action/A_M_Push_People_I.A_M_Push_People_I",
            ["StartFrame"] = 15,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0.76666671037674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.76666671037674,
            ["Index"] = 0,
            ["TaskTargetType"] = 0,
            ["TaskType"] = 137,
            ["bDisableDodge"] = true,
            ["bDisableJump"] = true,
            ["bDisableLocoStartMovement"] = false,
            ["bDisableMovement"] = true,
            ["bDisableRotation"] = false,
            ["bNeedReset"] = true,
          },
        },
        [4] = {
          ["EndTime"] = 0.76666671037674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.76666671037674,
            ["Index"] = 0,
            ["TaskType"] = 86,
          },
        },
        [5] = {
          ["EndTime"] = 0.83333337306976,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_Mobs/Play_Monster_MobsCommon_TeamPush.Play_Monster_MobsCommon_TeamPush",
            ["BlendOutType"] = 4,
            ["Duration"] = 0.83333337306976,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data