local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Fight_Dodge_L_Montage.A_Base_Fight_Dodge_L_Montage",
    [2] = "/Game/Blueprint/CombatSystem/Curve/86900001_Curve_5D9CE5E6D4200003.86900001_Curve_5D9CE5E6D4200003",
    [3] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86900001_RootMotionCurve_5D9CE5E75EC00004.86900001_RootMotionCurve_5D9CE5E75EC00004",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 460793814,
      ["B"] = 1109603562,
      ["C"] = -1795941955,
      ["D"] = -923517053,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 1,
        [3] = 2,
        [4] = 4,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.6333334445953,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1453536668,
        ["B"] = 1230331417,
        ["C"] = 1166428861,
        ["D"] = 1744335561,
      },
      ["ServerTaskEndList"] = {
        [1] = 3,
        [2] = 1,
        [3] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1.6333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 0,
            ["BlendOutTime"] = 0,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 49,
            ["Duration"] = 1.6333334445953,
            ["EndFrame"] = 49,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["MontageAssetRightFoot"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Fight_Dodge_R_Montage.A_Base_Fight_Dodge_R_Montage",
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Common/NewMale/A_Base_Fight_Dodge_L_Montage.A_Base_Fight_Dodge_L_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 160,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bDistinguishLeftRightFoot"] = true,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.6333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.6333334445953,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293710508523522,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/86900001_Curve_5D9CE5E6D4200003.86900001_Curve_5D9CE5E6D4200003",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/86900001_RootMotionCurve_5D9CE5E75EC00004.86900001_RootMotionCurve_5D9CE5E75EC00004",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data