local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1612429344,
      ["B"] = 1236355462,
      ["C"] = -284721752,
      ["D"] = 1850444552,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 3,
        [4] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 1,
        [4] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 329016742,
        ["B"] = 1238031315,
        ["C"] = 819784844,
        ["D"] = -1758332791,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 6,
            ["ClientTimelineIDForDash"] = 0,
            ["ClientTimelineIDForIdle"] = 0,
            ["Duration"] = 1,
            ["IdleMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpVisionary/Visionary_DoubleJump_Transition_Enter_From_Idle_Montage.Visionary_DoubleJump_Transition_Enter_From_Idle_Montage",
            ["Index"] = 0,
            ["InterruptBlendOut"] = 6,
            ["LocoInputMontageAsset"] = "/Game/Arts/Character/Animation/Common/DoubleJump/JumpVisionary/Visionary_DoubleJump_Transition_Enter_From_Idle_Montage.Visionary_DoubleJump_Transition_Enter_From_Idle_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 121,
            ["bFinishMontageWhenTaskEnd"] = true,
          },
        },
        [3] = {
          ["EndTime"] = 0.56666672229767,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ArgSourceMode"] = 1,
            ["Duration"] = 0.56666672229767,
            ["Index"] = 0,
            ["InterpolationMode"] = 1,
            ["MaxRotateSpeed"] = 90,
            ["MovementOutputMode"] = 1,
            ["OverrideGravityScale"] = 0,
            ["OverrideThruster"] = {
              ["X"] = -1,
              ["Y"] = -1,
              ["Z"] = -1,
            },
            ["RootmotionScale"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["SurfAngles"] = {
              ["W"] = 80,
              ["X"] = 0.1,
              ["Y"] = 0.5,
              ["Z"] = 10,
            },
            ["TaskType"] = 131,
            ["bAllowSurfMode"] = false,
            ["bAllowThruster"] = false,
            ["bAllowYawController"] = false,
            ["bNeedReset"] = true,
            ["bSetRootmotionScale"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.53333336114883,
          ["StartTime"] = 0.5,
          ["TaskData"] = {
            ["Duration"] = 0.033333335071802,
            ["Index"] = 0,
            ["NewAnimStateName"] = "JumpLoop",
            ["TaskType"] = 122,
            ["TransitionTime"] = 0.1,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data