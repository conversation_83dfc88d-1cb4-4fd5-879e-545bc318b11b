local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/Raid_BossCotard/A_Boss_Cotard_001_F_Skill02_Montage.A_Boss_Cotard_001_F_Skill02_Montage",
    [2] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_01.NS_BossMegose_Mechanic02_Npc_Safe_01",
    [3] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_02.NS_BossMegose_Mechanic02_Npc_Safe_02",
    [4] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_03.NS_BossMegose_Mechanic02_Npc_Safe_03",
    [5] = "/Game/Arts/Character/Animation/Boss/BossEudora/A_Boss_Eudora_Child_Farewall_Montage.A_Boss_Eudora_Child_Farewall_Montage",
    [6] = "/Game/Arts/Character/Model/Npc/Dunn/SK_Dunn_001.SK_Dunn_001",
    [7] = "/Game/Arts/Effects/FX_Character/Moon_FX/Dead/NS_MoonDead_01.NS_MoonDead_01",
    [9] = "/Game/Arts/Character/Animation/Boss/BossEudora/A_Boss_Eudora_Child_Spawn_Montage.A_Boss_Eudora_Child_Spawn_Montage",
    [10] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_XiaoShi.NS_BossMegose_Mechanic02_Npc_XiaoShi",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["NodeGuid"] = {
      ["A"] = -521394785,
      ["B"] = 1230170567,
      ["C"] = -1718690140,
      ["D"] = -996121549,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 8,
        [2] = 11,
        [3] = 1,
        [4] = 4,
        [5] = 2,
        [6] = 10,
        [7] = 9,
        [8] = 6,
        [9] = 3,
        [10] = 12,
        [11] = 5,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 8,
        [3] = 11,
        [4] = 12,
        [5] = 10,
        [6] = 2,
        [7] = 4,
        [8] = 9,
        [9] = 3,
        [10] = 6,
        [11] = 5,
      },
      ["Duration"] = 11.000000953674,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1875622136,
        ["B"] = 1311542173,
        ["C"] = 1592685208,
        ["D"] = 1170983340,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 7,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 7,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.1333334445953,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 64,
            ["Duration"] = 2.1333334445953,
            ["EndFrame"] = 64,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossEudora/A_Boss_Eudora_Child_Spawn_Montage.A_Boss_Eudora_Child_Spawn_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 8.6666669845581,
          ["StartTime"] = 2.1000001430511,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 69,
            ["Duration"] = 6.5666670799255,
            ["EndFrame"] = 69,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 0.3503,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/Raid_BossCotard/A_Boss_Cotard_001_F_Skill02_Montage.A_Boss_Cotard_001_F_Skill02_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 10.133334159851,
          ["StartTime"] = 8,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 100,
            ["Duration"] = 2.1333334445953,
            ["EndFrame"] = 100,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1.5625,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossEudora/A_Boss_Eudora_Child_Farewall_Montage.A_Boss_Eudora_Child_Farewall_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 8,
          ["StartTime"] = 5.0000004768372,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 180,
            ["CheckBlock"] = true,
            ["Duration"] = 3.0000002384186,
            ["Index"] = 0,
            ["RotateDirection"] = 2,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 11.066667556763,
          ["StartTime"] = 9.6666669845581,
          ["TaskData"] = {
            ["BHiddenHeadInfo"] = true,
            ["BRecover"] = true,
            ["BReverse"] = true,
            ["Duration"] = 1.4000000953674,
            ["Index"] = 0,
            ["MeshNames"] = {
            },
            ["Priority"] = 3,
            ["SocketNames"] = {
            },
            ["TaskType"] = 14,
          },
        },
        [6] = {
          ["EndTime"] = 9.7666673660278,
          ["StartTime"] = 9.3333339691162,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.4333333671093,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Moon_FX/Dead/NS_MoonDead_01.NS_MoonDead_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 10.133334159851,
          ["StartTime"] = 10.133334159851,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["Index"] = 0,
            ["TaskType"] = 44,
          },
        },
        [8] = {
          ["EndTime"] = 1.3333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.3333333730698,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_01.NS_BossMegose_Mechanic02_Npc_Safe_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = true,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [9] = {
          ["EndTime"] = 9.3333339691162,
          ["StartTime"] = 5.0000004768372,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 4.3333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_02.NS_BossMegose_Mechanic02_Npc_Safe_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = true,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 9.3333339691162,
          ["StartTime"] = 1.3333333730698,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 8,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_Safe_03.NS_BossMegose_Mechanic02_Npc_Safe_03",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -200,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = true,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 1.3333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.3333333730698,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/BossMegose_FX/Mechanic02/New/NS_BossMegose_Mechanic02_Npc_XiaoShi.NS_BossMegose_Mechanic02_Npc_XiaoShi",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = true,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = true,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [12] = {
          ["EndTime"] = 10.966667175293,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Eudora/Boss_Eudora/Play_Boss_Eudora_Spirit_Body.Play_Boss_Eudora_Spirit_Body",
            ["BlendOutType"] = 4,
            ["Duration"] = 10.966667175293,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data