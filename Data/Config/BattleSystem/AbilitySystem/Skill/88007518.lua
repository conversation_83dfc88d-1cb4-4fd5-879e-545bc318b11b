local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossYHXT01/A_Boss_YHXT01_001_Skill02.A_Boss_YHXT01_001_Skill02",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 957718516,
      ["B"] = 1207893341,
      ["C"] = -1009916008,
      ["D"] = 313978091,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 4,
        [3] = 3,
        [4] = 5,
        [5] = 8,
        [6] = 1,
        [7] = 6,
        [8] = 7,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 4,
        [4] = 3,
        [5] = 5,
        [6] = 6,
        [7] = 7,
        [8] = 8,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 7.6000003814697,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -125320161,
        ["B"] = 1134067044,
        ["C"] = 1820816287,
        ["D"] = -767167313,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 7.6000003814697,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 228,
            ["Duration"] = 7.6000003814697,
            ["EndFrame"] = 228,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossYHXT01/A_Boss_YHXT01_001_Skill02.A_Boss_YHXT01_001_Skill02",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 3.2666668891907,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "Bip001-R-Hand",
            ["Duration"] = 3.2666668891907,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Start.NS_Boss_YHXT01_Skill02_Start",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 5.3333334922791,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "Bip001-R-Hand",
            ["Duration"] = 4.3333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Xuli_01.NS_Boss_YHXT01_Skill02_Xuli_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 3.2666668891907,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 3.2666668891907,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Start_01.NS_Boss_YHXT01_Skill02_Start_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 5.3666667938232,
          ["StartTime"] = 1.0333334207535,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 4.3333334922791,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Xuli_01_01.NS_Boss_YHXT01_Skill02_Xuli_01_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 150,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 10.033333778381,
          ["StartTime"] = 5.0333337783813,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 5.0000004768372,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Aoe.NS_Boss_YHXT01_Skill02_Aoe",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 10.033333778381,
          ["StartTime"] = 5.0333337783813,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 5.0000004768372,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Boss_YHXT01_FX/Skill02/NS_Boss_YHXT01_Skill02_Aoe_01_02.NS_Boss_YHXT01_Skill02_Aoe_01_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 150,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [8] = {
          ["EndTime"] = 5.5000004768372,
          ["StartTime"] = 5.0333337783813,
          ["TaskData"] = {
            ["BloomParams"] = {
              ["Bloom1Size"] = 0,
              ["Bloom1Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom2Size"] = 0,
              ["Bloom2Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom3Size"] = 0,
              ["Bloom3Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom4Size"] = 0,
              ["Bloom4Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom5Size"] = 0,
              ["Bloom5Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["Bloom6Size"] = 0,
              ["Bloom6Tint"] = {
                ["A"] = 1,
                ["B"] = 1,
                ["G"] = 1,
                ["R"] = 1,
              },
              ["BloomConvolutionBufferScale"] = 0,
              ["BloomConvolutionCenterUV"] = {
                ["X"] = 0,
                ["Y"] = 0,
              },
              ["BloomConvolutionPreFilterMax"] = 0,
              ["BloomConvolutionPreFilterMin"] = 0,
              ["BloomConvolutionPreFilterMult"] = 0,
              ["BloomConvolutionSize"] = 0,
              ["BloomConvolutionTexture"] = "None",
              ["BloomIntensity"] = 0,
              ["BloomMethod"] = 1,
              ["BloomSizeScale"] = 0,
              ["BloomThreshold"] = 0,
              ["bOverride_Bloom1Size"] = false,
              ["bOverride_Bloom1Tint"] = false,
              ["bOverride_Bloom2Size"] = false,
              ["bOverride_Bloom2Tint"] = false,
              ["bOverride_Bloom3Size"] = false,
              ["bOverride_Bloom3Tint"] = false,
              ["bOverride_Bloom4Size"] = false,
              ["bOverride_Bloom4Tint"] = false,
              ["bOverride_Bloom5Size"] = false,
              ["bOverride_Bloom5Tint"] = false,
              ["bOverride_Bloom6Size"] = false,
              ["bOverride_Bloom6Tint"] = false,
              ["bOverride_BloomConvolutionBufferScale"] = false,
              ["bOverride_BloomConvolutionCenterUV"] = false,
              ["bOverride_BloomConvolutionPreFilterMax"] = false,
              ["bOverride_BloomConvolutionPreFilterMin"] = false,
              ["bOverride_BloomConvolutionPreFilterMult"] = false,
              ["bOverride_BloomConvolutionSize"] = false,
              ["bOverride_BloomConvolutionTexture"] = false,
              ["bOverride_BloomIntensity"] = false,
              ["bOverride_BloomMethod"] = false,
              ["bOverride_BloomSizeScale"] = false,
              ["bOverride_BloomThreshold"] = false,
            },
            ["BlurInvertParameter"] = 0,
            ["BlurMaskRadiusParameter"] = 0,
            ["BlurMaskSoftnessParameter"] = 10,
            ["BlurStrideParameter"] = 2,
            ["CenterParameter"] = {
              ["A"] = 1,
              ["B"] = 0,
              ["G"] = 0.5,
              ["R"] = 0.5,
            },
            ["ColorParameter"] = {
              ["A"] = 0,
              ["B"] = 0,
              ["G"] = 0,
              ["R"] = 0,
            },
            ["ColorSplitStrideParameter"] = 0,
            ["ContrastParameter"] = 0,
            ["DOFParams"] = {
              ["DepthBlurAmount"] = 1,
              ["DepthBlurRadius"] = 100,
              ["DepthOfFieldBladeCount"] = 5,
              ["DepthOfFieldFstop"] = 4,
              ["DepthOfFieldMinFstop"] = 1.2,
              ["FarBlurSize"] = 15,
              ["FarTransitionRegion"] = 8000,
              ["FocalDistance"] = 300,
              ["FocalRegion"] = 0,
              ["NearBlurSize"] = 15,
              ["NearTransitionRegion"] = 300,
              ["Scale"] = 1,
              ["SensorWidth"] = 60,
              ["SqueezeFactor"] = 2,
              ["bMobileHQGaussian"] = false,
            },
            ["DesaturateParameter"] = 0,
            ["Duration"] = 0.46666669845581,
            ["ExposureBias"] = 0,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FocalRegionUpdateType"] = 0,
            ["Index"] = 0,
            ["IntensityParameter"] = 1,
            ["InterruptMode"] = 0,
            ["InvertColorBParameter"] = 0,
            ["InvertColorGParameter"] = 0,
            ["InvertColorRParameter"] = 0,
            ["MaskInvertParameter"] = 0,
            ["MaskRadiusParameter"] = 0,
            ["MaskSoftnessParameter"] = 0,
            ["Material"] = "None",
            ["PostProcessSettings"] = {
              ["BloomParams"] = {
                ["BloomParams"] = {
                  ["Bloom1Size"] = 0,
                  ["Bloom1Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom2Size"] = 0,
                  ["Bloom2Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom3Size"] = 0,
                  ["Bloom3Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom4Size"] = 0,
                  ["Bloom4Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom5Size"] = 0,
                  ["Bloom5Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["Bloom6Size"] = 0,
                  ["Bloom6Tint"] = {
                    ["A"] = 1,
                    ["B"] = 1,
                    ["G"] = 1,
                    ["R"] = 1,
                  },
                  ["BloomConvolutionBufferScale"] = 0,
                  ["BloomConvolutionCenterUV"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                  },
                  ["BloomConvolutionPreFilterMax"] = 0,
                  ["BloomConvolutionPreFilterMin"] = 0,
                  ["BloomConvolutionPreFilterMult"] = 0,
                  ["BloomConvolutionSize"] = 0,
                  ["BloomConvolutionTexture"] = "None",
                  ["BloomIntensity"] = 0,
                  ["BloomMethod"] = 1,
                  ["BloomSizeScale"] = 0,
                  ["BloomThreshold"] = 0,
                  ["bOverride_Bloom1Size"] = false,
                  ["bOverride_Bloom1Tint"] = false,
                  ["bOverride_Bloom2Size"] = false,
                  ["bOverride_Bloom2Tint"] = false,
                  ["bOverride_Bloom3Size"] = false,
                  ["bOverride_Bloom3Tint"] = false,
                  ["bOverride_Bloom4Size"] = false,
                  ["bOverride_Bloom4Tint"] = false,
                  ["bOverride_Bloom5Size"] = false,
                  ["bOverride_Bloom5Tint"] = false,
                  ["bOverride_Bloom6Size"] = false,
                  ["bOverride_Bloom6Tint"] = false,
                  ["bOverride_BloomConvolutionBufferScale"] = false,
                  ["bOverride_BloomConvolutionCenterUV"] = false,
                  ["bOverride_BloomConvolutionPreFilterMax"] = false,
                  ["bOverride_BloomConvolutionPreFilterMin"] = false,
                  ["bOverride_BloomConvolutionPreFilterMult"] = false,
                  ["bOverride_BloomConvolutionSize"] = false,
                  ["bOverride_BloomConvolutionTexture"] = false,
                  ["bOverride_BloomIntensity"] = false,
                  ["bOverride_BloomMethod"] = false,
                  ["bOverride_BloomSizeScale"] = false,
                  ["bOverride_BloomThreshold"] = false,
                },
              },
              ["ColorAdjustParams"] = {
                ["Contrast"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Desaturate"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorB"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorG"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["InvertColorR"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["bEnableDesaturateProgress"] = false,
              },
              ["ColorSplitRadialBlurParams"] = {
                ["BlurInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurMaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["BlurStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["ColorSplitStride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["CustomMaterialParams"] = {
                ["CustomMaterial"] = "",
                ["MainCharPositionParamName"] = "",
                ["Scalars"] = {
                },
                ["Texture"] = {
                },
                ["Vectors"] = {
                },
                ["bFixBlendWeight"] = false,
              },
              ["DOFParams"] = {
                ["DOFParams"] = {
                  ["DepthBlurAmount"] = 1,
                  ["DepthBlurRadius"] = 100,
                  ["DepthOfFieldBladeCount"] = 5,
                  ["DepthOfFieldFstop"] = 4,
                  ["DepthOfFieldMinFstop"] = 1.2,
                  ["FarBlurSize"] = 15,
                  ["FarTransitionRegion"] = 8000,
                  ["FocalDistance"] = 300,
                  ["FocalRegion"] = 0,
                  ["NearBlurSize"] = 15,
                  ["NearTransitionRegion"] = 300,
                  ["Scale"] = 1,
                  ["SensorWidth"] = 60,
                  ["SqueezeFactor"] = 2,
                  ["bMobileHQGaussian"] = false,
                },
              },
              ["DarkenParams"] = {
                ["ExposureBias"] = 0,
              },
              ["PhantomParams"] = {
                ["Direction"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Gap"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Speed"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["PostProcessType"] = 0,
              ["Priority"] = 0,
              ["RGBSplitParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskSoftness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["RadialBlurParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 1,
                  ["bConstValue"] = true,
                },
                ["MaskInvert"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["MaskRadius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0.1,
                  ["bConstValue"] = true,
                },
                ["Stride"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
              ["VignetteParams"] = {
                ["Center"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Color"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = {
                    ["A"] = 0,
                    ["B"] = 0,
                    ["G"] = 0,
                    ["R"] = 0,
                  },
                  ["bConstValue"] = true,
                },
                ["Intensity"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Radius"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
                ["Softness"] = {
                  ["BlendCurve"] = {
                    ["CurveAsset"] = "",
                  },
                  ["Value"] = 0,
                  ["bConstValue"] = true,
                },
              },
            },
            ["PostProcessType"] = 8,
            ["Priority"] = 0,
            ["Radius"] = 1500,
            ["RadiusParameter"] = 0,
            ["Scalars"] = {
              [""] = {
                ["Constant"] = 0,
                ["Curve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopCurve"] = {
                  ["CurveAsset"] = "",
                },
                ["LoopTime"] = 0,
                ["MaterialParameterType"] = 0,
                ["Max"] = 0,
                ["Min"] = 0,
                ["ParamName"] = "",
              },
            },
            ["SoftnessParameter"] = 0,
            ["StrideParameter"] = 0,
            ["TargetType"] = 1,
            ["TaskType"] = 126,
            ["Textures"] = {
            },
            ["Vectors"] = {
            },
            ["bUseNewPostProcessSetting"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data