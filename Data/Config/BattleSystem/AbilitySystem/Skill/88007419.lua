local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossQSKJ/BossQSKJ_02/A_BOSS_KnightWithHorse_Performance_03.A_BOSS_KnightWithHorse_Performance_03",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["NodeGuid"] = {
      ["A"] = -607233273,
      ["B"] = 1310131453,
      ["C"] = -16609633,
      ["D"] = -124301810,
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["Duration"] = 6.0666670799255,
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1209862672,
        ["B"] = 1138929857,
        ["C"] = -24773962,
        ["D"] = 1123354294,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 6.0666670799255,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 182,
            ["Duration"] = 6.0666670799255,
            ["EndFrame"] = 182,
            ["FemaleSequenceAsset"] = "",
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossQSKJ/BossQSKJ_02/A_BOSS_KnightWithHorse_Performance_03.A_BOSS_KnightWithHorse_Performance_03",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bGenderAnim"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 6.0333337783813,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_XZQS/Boss_XZQS/Play_Boss_XZQS_Phase2_Show03.Play_Boss_XZQS_Phase2_Show03",
            ["BlendOutType"] = 4,
            ["Duration"] = 6.0333337783813,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data