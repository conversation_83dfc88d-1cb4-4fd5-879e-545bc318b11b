local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterArmouredGiant/MonsterArmouredGiant_001/A_Monster_ArmouredGiant_001_Skill03_Montage.A_Monster_ArmouredGiant_001_Skill03_Montage",
    [2] = "/Game/Blueprint/CombatSystem/Curve/89001184_Curve_5D6619D4334002C4.89001184_Curve_5D6619D4334002C4",
    [3] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001184_RootMotionCurve_5D6619D44F2002C5.89001184_RootMotionCurve_5D6619D44F2002C5",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1256870856,
      ["B"] = 1248597495,
      ["C"] = 658884025,
      ["D"] = 1898851789,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 11,
        [2] = 2,
        [3] = 3,
        [4] = 4,
        [5] = 5,
        [6] = 6,
        [7] = 7,
        [8] = 8,
        [9] = 9,
        [10] = 13,
        [11] = 10,
        [12] = 15,
        [13] = 1,
        [14] = 14,
        [15] = 12,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 11,
        [3] = 14,
        [4] = 15,
        [5] = 13,
        [6] = 2,
        [7] = 3,
        [8] = 4,
        [9] = 5,
        [10] = 6,
        [11] = 7,
        [12] = 8,
        [13] = 9,
        [14] = 10,
        [15] = 12,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.2333335876465,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -630891515,
        ["B"] = 1124369946,
        ["C"] = -986264957,
        ["D"] = -521762421,
      },
      ["ServerTaskEndList"] = {
        [1] = 11,
        [2] = 2,
        [3] = 3,
        [4] = 4,
        [5] = 5,
        [6] = 6,
        [7] = 7,
        [8] = 8,
        [9] = 9,
        [10] = 13,
        [11] = 10,
        [12] = 12,
      },
      ["ServerTaskStartList"] = {
        [1] = 11,
        [2] = 13,
        [3] = 2,
        [4] = 3,
        [5] = 4,
        [6] = 5,
        [7] = 6,
        [8] = 7,
        [9] = 8,
        [10] = 9,
        [11] = 10,
        [12] = 12,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 3.2333335876465,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 0,
            ["BlendOutTime"] = 0,
            ["BlendOutWhenInterrupt"] = 0,
            ["ClipLength"] = 97,
            ["Duration"] = 3.2333335876465,
            ["EndFrame"] = 97,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterArmouredGiant/MonsterArmouredGiant_001/A_Monster_ArmouredGiant_001_Skill03_Montage.A_Monster_ArmouredGiant_001_Skill03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 1,
          ["StartTime"] = 0.73333334922791,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 60,
            ["CheckBlock"] = true,
            ["Duration"] = 0.26666668057442,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.1666667461395,
          ["StartTime"] = 1,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 1.3333333730698,
          ["StartTime"] = 1.1666667461395,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.5000001192093,
          ["StartTime"] = 1.3333333730698,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 1.6666667461395,
          ["StartTime"] = 1.5000001192093,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 1.8333333730698,
          ["StartTime"] = 1.6666667461395,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 2,
          ["StartTime"] = 1.8333333730698,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [9] = {
          ["EndTime"] = 2.1666667461395,
          ["StartTime"] = 2,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [10] = {
          ["EndTime"] = 2.4666666984558,
          ["StartTime"] = 2.1666667461395,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.30000001192093,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [11] = {
          ["EndTime"] = 0.16666667163372,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.16666667163372,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 3.2333335876465,
          ["StartTime"] = 3.0000002384186,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 60,
            ["CheckBlock"] = true,
            ["Duration"] = 0.23333334922791,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [13] = {
          ["EndTime"] = 2.4666666984558,
          ["StartTime"] = 0.5,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 1.9666668176651,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293273098834026,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/89001184_Curve_5D6619D4334002C4.89001184_Curve_5D6619D4334002C4",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89001184_RootMotionCurve_5D6619D44F2002C5.89001184_RootMotionCurve_5D6619D44F2002C5",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = true,
            ["bUseAnimRootMotion"] = true,
          },
        },
        [14] = {
          ["EndTime"] = 3.2333335876465,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "Wp_001",
            ["Duration"] = 3.2333335876465,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_ArmouredGiant_001/Skill03/NS_Monster_ArmouredGiant_001_Skill03_Cast.NS_Monster_ArmouredGiant_001_Skill03_Cast",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = -110,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = true,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [15] = {
          ["EndTime"] = 3.2000002861023,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Monster/Monster_VisionaryMage/Play_Monster_VisionaryMage_Skill03_RelentlessChase.Play_Monster_VisionaryMage_Skill03_RelentlessChase",
            ["BlendOutType"] = 4,
            ["Duration"] = 3.2000002861023,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data