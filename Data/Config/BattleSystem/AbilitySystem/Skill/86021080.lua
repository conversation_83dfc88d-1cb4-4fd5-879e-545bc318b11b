local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Visionary/Female/New/A_Vis_Skill03_Montage.A_Vis_Skill03_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1478148562,
      ["B"] = 1159797576,
      ["C"] = -483050324,
      ["D"] = -803120353,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 1,
        [3] = 5,
        [4] = 6,
        [5] = 7,
        [6] = 3,
        [7] = 4,
        [8] = 8,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 5,
        [5] = 7,
        [6] = 1,
        [7] = 6,
        [8] = 8,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.6666669845581,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -2146562208,
        ["B"] = 1308985841,
        ["C"] = -1244655942,
        ["D"] = -337986882,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 1,
        [3] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
        [2] = 4,
        [3] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.16666667163372,
          ["TaskData"] = {
            ["Duration"] = 0.33333334326744,
            ["Index"] = 0,
            ["SkillIDs"] = {
            },
            ["SkillTags"] = {
            },
            ["SkillTypes"] = {
              [1] = 1,
              [2] = 2,
              [3] = 4,
              [4] = 8,
              [5] = 16,
              [6] = 32,
            },
            ["TaskType"] = 134,
            ["WhiteListSkillIDs"] = {
            },
            ["bDisableAllTypeSkill"] = true,
            ["bInterruptSkill"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4.6666669845581,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 0,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 140,
            ["Duration"] = 4.6666669845581,
            ["EndFrame"] = 140,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Visionary/Female/New/A_Vis_Skill03_Montage.A_Vis_Skill03_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 4.6666669845581,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Acceleration"] = -1,
            ["CurSpeed"] = 600,
            ["DecayTime"] = 0.05,
            ["Deceleration"] = -1,
            ["Duration"] = 4.6666669845581,
            ["Index"] = 0,
            ["MoveMode"] = 1,
            ["TaskType"] = 99,
            ["bNeedReset"] = true,
          },
        },
        [5] = {
          ["EndTime"] = 1.0333334207535,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 1.0333334207535,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill03_New/NS_VisionarySkill03_01.NS_VisionarySkill03_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["EndTime"] = 2.6333334445953,
          ["StartTime"] = 0.23333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 2.4000000953674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 1,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 2,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Visionary_FX_New/Skill03_New/NS_VisionarySkill03_02.NS_VisionarySkill03_02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = -27.923,
            },
            ["RotationQuat"] = {
              ["W"] = 0.9705,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = -0.2413,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.3,
              ["Y"] = 1.3,
              ["Z"] = 1.3,
            },
            ["TaskTargetType"] = 3,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [7] = {
          ["EndTime"] = 4.6333336830139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Visionary/New/Play_Visionary_Skill_Psychological_Suggestion.Play_Visionary_Skill_Psychological_Suggestion",
            ["BlendOutType"] = 4,
            ["Duration"] = 4.6333336830139,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 4.6666669845581,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk4_1.Play_Vo_Role_Atk4_1",
            ["BlendOutType"] = 4,
            ["Duration"] = 4.3333334922791,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.66666668653488,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = true,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data