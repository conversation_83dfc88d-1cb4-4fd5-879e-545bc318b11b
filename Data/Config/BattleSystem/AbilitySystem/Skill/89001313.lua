local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_TurnLeft_180.A_BOSS_FHYZ_TurnLeft_180",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -704682174,
      ["B"] = 1218770134,
      ["C"] = -722086009,
      ["D"] = 449182549,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 3,
        [4] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 1.8333333730698,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1833760085,
        ["B"] = 1078020058,
        ["C"] = -1503027820,
        ["D"] = 1264489504,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 3,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1.8333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 55,
            ["Duration"] = 1.8333333730698,
            ["EndFrame"] = 55,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BOSSFHYZ/A_BOSS_FHYZ_TurnLeft_180.A_BOSS_FHYZ_TurnLeft_180",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 1.5000001192093,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 240,
            ["CheckBlock"] = true,
            ["Duration"] = 1.5000001192093,
            ["Index"] = 0,
            ["RotateDirection"] = 0,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.33333334326744,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["FoamScale"] = 180,
            ["Index"] = 0,
            ["KeepTrigger"] = false,
            ["MotorTexture"] = "/Game/Arts/MaterialLibrary/Utility/Textures/WaterDynamicLocalWave/T_Motor_Default.T_Motor_Default",
            ["MotorTextureId"] = 0,
            ["MoveDirAngle"] = 0,
            ["MoveSpeed"] = 0,
            ["OffsetX"] = 0,
            ["OffsetY"] = 0,
            ["ScaleX"] = 80,
            ["ScaleY"] = 80,
            ["TaskType"] = 143,
            ["TimeGap"] = 0,
            ["WaveMaxHeight"] = 1.5,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data