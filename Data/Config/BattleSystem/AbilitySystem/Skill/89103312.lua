local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_New_Skill01_Montage_KeLaiEn.A_F_Apprentice_New_Skill01_Montage_KeLaiEn",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -524321212,
      ["B"] = 1298337144,
      ["C"] = 676558754,
      ["D"] = 774276337,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 6,
        [2] = 7,
        [3] = 3,
        [4] = 5,
        [5] = 4,
        [6] = 2,
        [7] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 2,
        [4] = 3,
        [5] = 7,
        [6] = 5,
        [7] = 4,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 2.5333335399628,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1617584090,
        ["B"] = 1269268230,
        ["C"] = -1485247308,
        ["D"] = -1901133213,
      },
      ["ServerTaskEndList"] = {
        [1] = 6,
        [2] = 7,
      },
      ["ServerTaskStartList"] = {
        [1] = 6,
        [2] = 7,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 2.5333335399628,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 76,
            ["Duration"] = 2.5333335399628,
            ["EndFrame"] = 76,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_New_Skill01_Montage_KeLaiEn.A_F_Apprentice_New_Skill01_Montage_KeLaiEn",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 2.4333333969116,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200100,
            ["Duration"] = 2.3666667938232,
            ["Index"] = 0,
            ["TaskType"] = 69,
          },
        },
        [3] = {
          ["EndTime"] = 1.1000000238419,
          ["StartTime"] = 0.23333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "None",
            ["Duration"] = 0.8666667342186,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Arb_FX/Attack/New/NS_Arb_Attack_01.NS_Arb_Attack_01",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 0.8,
            ["PosOffset"] = {
              ["X"] = 110,
              ["Y"] = 0,
              ["Z"] = 50,
            },
            ["RotOffset"] = {
              ["Pitch"] = 30,
              ["Roll"] = 0,
              ["Yaw"] = 20,
            },
            ["RotationQuat"] = {
              ["W"] = 0.9513,
              ["X"] = 0.0449,
              ["Y"] = -0.2549,
              ["Z"] = 0.1677,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.5,
              ["Y"] = 1.5,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 1.4333333969116,
          ["StartTime"] = 0.73333334922791,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.70000004768372,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Arb_FX/Attack/New/NS_Arb_AttackTrail03.NS_Arb_AttackTrail03",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 120,
              ["Z"] = 120,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 50,
              ["Yaw"] = 90,
            },
            ["RotationQuat"] = {
              ["W"] = 0.6409,
              ["X"] = -0.2988,
              ["Y"] = -0.2988,
              ["Z"] = 0.6409,
            },
            ["ScaleOffset"] = {
              ["X"] = 1.5,
              ["Y"] = 1.5,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [5] = {
          ["EndTime"] = 1.3666667938232,
          ["StartTime"] = 0.66666668653488,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 0.70000004768372,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Arb_FX/Attack/NS_Arb_AttackTrail02.NS_Arb_AttackTrail02",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 120,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 50,
              ["Yaw"] = 90,
            },
            ["RotationQuat"] = {
              ["W"] = 0.6409,
              ["X"] = -0.2988,
              ["Y"] = -0.2988,
              ["Z"] = 0.6409,
            },
            ["ScaleOffset"] = {
              ["X"] = 2,
              ["Y"] = 1.4,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [6] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 500,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 4,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 0,
                },
                ["ConditionTargetType"] = 0,
                ["ConditionType"] = 16,
                ["IsInstance"] = false,
                ["Mode"] = 2,
                ["RuleID"] = 0,
                ["Sign"] = 2,
                ["Value"] = 0,
              },
            },
            ["ConditionRelationType"] = 1,
          },
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 78,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.33,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [7] = {
          ["ConditionGroup"] = {
            ["ConditionList"] = {
              [1] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 3,
                },
                ["ConditionTargetType"] = 3,
                ["ConditionType"] = 6,
                ["Dist"] = 500,
                ["IsCalculateCollision"] = true,
                ["IsInstance"] = false,
                ["Pos"] = {
                  ["BasePointArgs"] = {
                    ["X"] = 0,
                    ["Y"] = 0,
                    ["Z"] = 0,
                  },
                  ["BasePointType"] = 2,
                  ["ClockwiseRotation"] = 0,
                  ["PosNum"] = 1,
                  ["ShapeArgs"] = {
                  },
                  ["ShapeType"] = 0,
                },
                ["cmp"] = 4,
              },
              [2] = {
                ["AvaliableConditionTargetTypes"] = {
                  [1] = 0,
                },
                ["ConditionTargetType"] = 0,
                ["ConditionType"] = 16,
                ["IsInstance"] = false,
                ["Mode"] = 2,
                ["RuleID"] = 0,
                ["Sign"] = 2,
                ["Value"] = 0,
              },
            },
            ["ConditionRelationType"] = 1,
          },
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0.60000002384186,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 400,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data