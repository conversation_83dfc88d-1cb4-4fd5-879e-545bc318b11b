local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1617923708,
      ["B"] = 1231427013,
      ["C"] = -466005844,
      ["D"] = -136542784,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 11.000000953674,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1095240801,
        ["B"] = 1080166472,
        ["C"] = -1221289595,
        ["D"] = 500586918,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["ConfigID"] = 1300013,
          ["ConfigIDs"] = {
          },
          ["Duration"] = 11.000000953674,
          ["EndTime"] = 11.000000953674,
          ["Index"] = 0,
          ["ParamMap"] = {
          },
          ["StartTime"] = 0,
          ["TDUITypeEnum"] = 0,
          ["TargetMode"] = 0,
          ["TaskType"] = 18,
          ["UIName"] = "",
          ["UIType"] = 1,
          ["bShowInPuppetSlot"] = false,
          ["bStopReminderOnStateEnd"] = false,
        },
        [2] = {
          ["AttachSocket"] = "None",
          ["Audio"] = "/Game/Arts/Audio/Events/Enemy/Boss/Boss_Joker/Boss_Joker/Play_Boss_Joker_Rotary_Buff01_SwitchUI.Play_Boss_Joker_Rotary_Buff01_SwitchUI",
          ["BlendOutType"] = 4,
          ["ConditionRelationType"] = 0,
          ["Conditions"] = {
          },
          ["Duration"] = 11.000000953674,
          ["EndTime"] = 11.000000953674,
          ["FadeIn"] = 0,
          ["FadeInCurve"] = "",
          ["FadeOut"] = 0.66666668653488,
          ["FadeOutCurve"] = "",
          ["Index"] = 0,
          ["InterruptMode"] = 0,
          ["RelativeLocation"] = {
            ["X"] = 0,
            ["Y"] = 0,
            ["Z"] = 0,
          },
          ["StartTime"] = 0,
          ["TaskTargetType"] = 0,
          ["TaskType"] = 42,
          ["bNeedAttach"] = false,
          ["bPlayAtLast"] = false,
          ["bSetWorldLocation"] = false,
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data