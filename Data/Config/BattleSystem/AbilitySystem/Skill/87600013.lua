local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_Skill09_Montage.A_F_Apprentice_Skill09_Montage",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 1378033351,
      ["B"] = 1335207616,
      ["C"] = 1181141944,
      ["D"] = 658036756,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 3,
        [2] = 11,
        [3] = 2,
        [4] = 10,
        [5] = 7,
        [6] = 1,
        [7] = 6,
        [8] = 8,
        [9] = 4,
        [10] = 5,
        [11] = 12,
      },
      ["ClientTaskStartList"] = {
        [1] = 3,
        [2] = 4,
        [3] = 5,
        [4] = 6,
        [5] = 7,
        [6] = 10,
        [7] = 11,
        [8] = 12,
        [9] = 2,
        [10] = 8,
        [11] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.4666666984558,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -2053896657,
        ["B"] = 1319987451,
        ["C"] = 724800955,
        ["D"] = 2092740756,
      },
      ["ServerTaskEndList"] = {
        [1] = 9,
        [2] = 3,
        [3] = 2,
        [4] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 3,
        [2] = 9,
        [3] = 2,
        [4] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [3] = {
          ["EndTime"] = 0.30000001192093,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.30000001192093,
            ["Index"] = 0,
            ["SkillIDs"] = {
            },
            ["SkillTags"] = {
            },
            ["SkillTypes"] = {
              [1] = 4,
            },
            ["TaskType"] = 134,
            ["WhiteListSkillIDs"] = {
            },
            ["bDisableAllTypeSkill"] = false,
            ["bInterruptSkill"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 4.4666666984558,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["ConfigID"] = 0,
            ["ConfigIDs"] = {
            },
            ["Duration"] = 4.4666666984558,
            ["Index"] = 0,
            ["ParamMap"] = {
              ["Duration"] = "2.7",
              ["SkillID"] = "86050016",
            },
            ["TDUITypeEnum"] = 0,
            ["TargetMode"] = 0,
            ["TaskType"] = 18,
            ["UIName"] = "P_SkillProgressBar",
            ["UIType"] = 0,
            ["bShowInPuppetSlot"] = false,
            ["bStopReminderOnStateEnd"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 4.4666666984558,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 134,
            ["Duration"] = 4.4666666984558,
            ["EndFrame"] = 134,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Player/Apprentice/Female/A_F_Apprentice_Skill09_Montage.A_F_Apprentice_Skill09_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 3.8000001907349,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AppearanceOverride"] = 1200011,
            ["Duration"] = 3.8000001907349,
            ["Index"] = 0,
            ["TaskType"] = 69,
          },
        },
        [7] = {
          ["EndTime"] = 3.5000002384186,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 3.5000002384186,
            ["Index"] = 0,
            ["ModelID"] = "",
            ["MontageAsset"] = "/Game/Arts/Weapon/Apprentice/Animation/A_Apprentice_Weapon_001A_P003_Skill09_Montage.A_Apprentice_Weapon_001A_P003_Skill09_Montage",
            ["PlayRate"] = 1,
            ["TaskType"] = 82,
            ["UberGraphFrame"] = {
            },
            ["bAutoSearchWeapon"] = true,
            ["bFinishMontageWhenTaskEnd"] = true,
            ["bPropagateToOwner"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 3.8000001907349,
          ["StartTime"] = 3.5000002384186,
          ["TaskData"] = {
            ["Duration"] = 0.30000001192093,
            ["Index"] = 0,
            ["TaskType"] = 86,
          },
        },
        [9] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [10] = {
          ["EndTime"] = 3.4000000953674,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "weapon_u",
            ["Duration"] = 3.4000000953674,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 0,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Apprentice_FX/Skill09/NS_ApprenticeSkill09_Weapon.NS_ApprenticeSkill09_Weapon",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [11] = {
          ["EndTime"] = 1.8333333730698,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk4_2.Play_Vo_Role_Atk4_2",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.8333333730698,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
        [12] = {
          ["EndTime"] = 4.4666666984558,
          ["StartTime"] = 2.566666841507,
          ["TaskData"] = {
            ["AttachSocket"] = "None",
            ["Audio"] = "/Game/Arts/Audio/Events/Role/Role_Battle_Commom_Vo/Play_Vo_Role_Atk1.Play_Vo_Role_Atk1",
            ["BlendOutType"] = 4,
            ["Duration"] = 1.9000000953674,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0.80000007152557,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["RelativeLocation"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 42,
            ["bNeedAttach"] = false,
            ["bPlayAtLast"] = false,
            ["bSetWorldLocation"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data