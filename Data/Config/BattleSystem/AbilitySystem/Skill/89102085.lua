local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_02.A_Monster_Shinv_Skill_02",
    [2] = "/Game/Blueprint/CombatSystem/Curve/89102085_Curve_5D773560868006D2.89102085_Curve_5D773560868006D2",
    [3] = "/Game/Blueprint/CombatSystem/RootMotionCurve/89102085_RootMotionCurve_5D773560A8A006D3.89102085_RootMotionCurve_5D773560A8A006D3",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 81983108,
      ["B"] = 1323281131,
      ["C"] = -7416900,
      ["D"] = 436678190,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 4,
        [4] = 7,
        [5] = 8,
        [6] = 5,
        [7] = 2,
        [8] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 6,
        [5] = 4,
        [6] = 7,
        [7] = 5,
        [8] = 8,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.3333334922791,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = 1522695044,
        ["B"] = 1100920455,
        ["C"] = 1853469090,
        ["D"] = -79639112,
      },
      ["ServerTaskEndList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 4,
        [4] = 7,
        [5] = 8,
        [6] = 5,
      },
      ["ServerTaskStartList"] = {
        [1] = 1,
        [2] = 6,
        [3] = 4,
        [4] = 7,
        [5] = 5,
        [6] = 8,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.20000001788139,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 4.3333334922791,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 130,
            ["Duration"] = 4.3333334922791,
            ["EndFrame"] = 130,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterShinv/A_Monster_Shinv_Skill_02.A_Monster_Shinv_Skill_02",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 4.7666668891907,
          ["StartTime"] = 0.1000000089407,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["AttachTargetType"] = 0,
            ["DockBone"] = "root",
            ["Duration"] = 4.6666669845581,
            ["EffectPriority"] = 5,
            ["ExtraGroundMsg"] = {
              ["X"] = -300,
              ["Y"] = 300,
              ["Z"] = 0.2,
            },
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["FloatCurve"] = {
            },
            ["FollowMode"] = 2,
            ["GroundCheckObjectTypes"] = {
              [1] = 0,
            },
            ["Index"] = 0,
            ["InitialOrientation"] = 0,
            ["InterruptMode"] = 0,
            ["Is3DFx"] = false,
            ["MeshColor"] = {
              ["A"] = 1,
              ["B"] = 1,
              ["G"] = 1,
              ["R"] = 1,
            },
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Monster_Shinv_FX/Skill02/NS_MonsterShinv_Skill02_Trail.NS_MonsterShinv_Skill02_Trail",
            ["NiagaraMobile"] = "",
            ["ParticleColorScaleCurve"] = {
              ["CurveAsset"] = "",
            },
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 24.676,
              ["Y"] = -8.2576,
              ["Z"] = 1.3804,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["TaskTargetType"] = 0,
            ["TaskType"] = 26,
            ["TranslucencySortDistanceOffset"] = 0,
            ["TranslucencySortPriority"] = 0,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = false,
            ["bBenefitEffect"] = false,
            ["bFadeoutWhenBorn"] = false,
            ["bIgnoreRelationCommonTags"] = false,
            ["bNeedCheckGround"] = false,
            ["bPlayAtLast"] = false,
            ["bSpiritualVision"] = false,
            ["bTowardInstigator"] = false,
            ["bUseWorldCoordinate"] = false,
            ["bWorldTransform"] = false,
            ["maxGazeDuration"] = 0,
          },
        },
        [4] = {
          ["EndTime"] = 0.66666668653488,
          ["StartTime"] = 0.46666669845581,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [5] = {
          ["EndTime"] = 1.5333334207535,
          ["StartTime"] = 1.3333333730698,
          ["TaskData"] = {
            ["Duration"] = 0.20000001788139,
            ["Index"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 118,
            ["UseStaticBlackboard"] = false,
          },
        },
        [6] = {
          ["EndTime"] = 0.5,
          ["StartTime"] = 0.33333334326744,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 50,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.16666667163372,
            ["EnableRotate"] = false,
            ["Ground"] = false,
            ["Index"] = 0,
            ["MaxDistance"] = 50,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [7] = {
          ["EndTime"] = 0.76666671037674,
          ["StartTime"] = 0.60000002384186,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 50,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.16666667163372,
            ["EnableRotate"] = false,
            ["Ground"] = false,
            ["Index"] = 0,
            ["MaxDistance"] = 50,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
        [8] = {
          ["EndTime"] = 1.5000001192093,
          ["StartTime"] = 1.3333333730698,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = false,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = 50,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 1,
            },
            ["Duration"] = 0.16666667163372,
            ["EnableRotate"] = false,
            ["Ground"] = false,
            ["Index"] = 0,
            ["MaxDistance"] = 50,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 0.2,
            ["bUseAnimRootMotion"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data