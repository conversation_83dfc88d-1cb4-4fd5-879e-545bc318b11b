local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/SideStep/A_Boss_Theil_Sidestep_Right_Montage.A_Boss_Theil_Sidestep_Right_Montage",
    [2] = "/Game/Blueprint/CombatSystem/Curve/88099204_Curve_5D99B315A1E0000D.88099204_Curve_5D99B315A1E0000D",
    [3] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099204_RootMotionCurve_5D99B315A340000E.88099204_RootMotionCurve_5D99B315A340000E",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = 450226613,
      ["B"] = 1182897573,
      ["C"] = -958766716,
      ["D"] = -554990178,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
        [4] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
        [2] = 4,
        [3] = 2,
        [4] = 3,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0.93333339691162,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1080178686,
        ["B"] = 1217286406,
        ["C"] = -1938767481,
        ["D"] = -1009224455,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
        [2] = 3,
        [3] = 4,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 2,
        [3] = 3,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.93333339691162,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 6,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 28,
            ["Duration"] = 0.93333339691162,
            ["EndFrame"] = 28,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/Tyre/WorldBossTyre/SideStep/A_Boss_Theil_Sidestep_Right_Montage.A_Boss_Theil_Sidestep_Right_Montage",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 0.60000002384186,
          ["StartTime"] = 0.16666667163372,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 150,
            ["CheckBlock"] = true,
            ["Duration"] = 0.4333333671093,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 0.70000004768372,
          ["StartTime"] = 0.60000002384186,
          ["TaskData"] = {
            ["AngularSpeedUpperLimit"] = 360,
            ["CheckBlock"] = true,
            ["Duration"] = 0.1000000089407,
            ["Index"] = 0,
            ["RotateDirection"] = 5,
            ["SelectionRuleID"] = 0,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 37,
            ["bFallbackOnNoTarget"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.90000003576279,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AccumulateMode"] = 0,
            ["AimTargetDistance"] = 0,
            ["AnimRootInfo"] = {
            },
            ["Duration"] = 0.90000003576279,
            ["IgnoreImpetus"] = false,
            ["Index"] = 0,
            ["ModelScaleAdjust"] = false,
            ["MoveByAnimGUID"] = 3293701479568412,
            ["RootMotionCurve"] = "/Game/Blueprint/CombatSystem/Curve/88099204_Curve_5D99B315A1E0000D.88099204_Curve_5D99B315A1E0000D",
            ["RootMotionCurveLinearColor"] = "/Game/Blueprint/CombatSystem/RootMotionCurve/88099204_RootMotionCurve_5D99B315A340000E.88099204_RootMotionCurve_5D99B315A340000E",
            ["StickGround"] = true,
            ["TaskType"] = 74,
            ["UseEndPosition"] = false,
            ["bUseAnimRootMotion"] = true,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data