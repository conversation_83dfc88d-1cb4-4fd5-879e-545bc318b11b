local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1017329411,
      ["B"] = 1077039084,
      ["C"] = -207896916,
      ["D"] = 94444489,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 4.4333333969116,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -1268116503,
        ["B"] = 1188158243,
        ["C"] = 1892682907,
        ["D"] = 1309689300,
      },
      ["ServerTaskEndList"] = {
        [1] = 2,
      },
      ["ServerTaskStartList"] = {
        [1] = 2,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 4.4333333969116,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 56,
            ["Duration"] = 4.4333333969116,
            ["EndFrame"] = 56,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 0.4211,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Boss/BossSheriff/A_BOSS_Sheriff_Skill_02_c.A_BOSS_Sheriff_Skill_02_c",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [2] = {
          ["EndTime"] = 5.0000004768372,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 5.0000004768372,
            ["Index"] = 0,
            ["NearRadius"] = 200,
            ["Speed"] = 200,
            ["TaskTargetType"] = 3,
            ["TaskType"] = 113,
            ["bStopOnOverlap"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data