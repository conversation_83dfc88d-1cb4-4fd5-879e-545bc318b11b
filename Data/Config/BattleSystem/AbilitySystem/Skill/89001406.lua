local Data = {
  ["OtherResources"] = {
    [1] = "/Game/Arts/Character/Animation/Monster/MonsterEllen/A_Monster_Ellen_Walk_Back.A_Monster_Ellen_Walk_Back",
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1781186238,
      ["B"] = 1084314035,
      ["C"] = 606803875,
      ["D"] = -1982656032,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 4,
        [2] = 2,
        [3] = 1,
        [4] = 3,
      },
      ["ClientTaskStartList"] = {
        [1] = 2,
        [2] = 4,
        [3] = 3,
        [4] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 3.2000002861023,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -968421323,
        ["B"] = 1168519401,
        ["C"] = 1323476137,
        ["D"] = 1027628783,
      },
      ["ServerTaskEndList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["ServerTaskStartList"] = {
        [1] = 4,
        [2] = 1,
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Duration"] = 0,
            ["TaskType"] = 0,
          },
        },
        [2] = {
          ["EndTime"] = 1.6000001430511,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 48,
            ["Duration"] = 1.6000001430511,
            ["EndFrame"] = 48,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterEllen/A_Monster_Ellen_Walk_Back.A_Monster_Ellen_Walk_Back",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [3] = {
          ["EndTime"] = 3.2000002861023,
          ["StartTime"] = 1.6000001430511,
          ["TaskData"] = {
            ["AnimType"] = 0,
            ["BlendInTime"] = 3,
            ["BlendOutTime"] = 3,
            ["BlendOutWhenInterrupt"] = 3,
            ["ClipLength"] = 48,
            ["Duration"] = 1.6000001430511,
            ["EndFrame"] = 48,
            ["FullBodyMask"] = 0,
            ["Index"] = 0,
            ["InterruptMode"] = 0,
            ["LibAssetID"] = {
              ["AnimID"] = "",
              ["AssetID"] = "",
              ["StateName"] = "",
            },
            ["PlayRate"] = 1,
            ["SequenceAsset"] = "/Game/Arts/Character/Animation/Monster/MonsterEllen/A_Monster_Ellen_Walk_Back.A_Monster_Ellen_Walk_Back",
            ["StartFrame"] = 0,
            ["TaskType"] = 101,
            ["UpdateBound"] = false,
            ["bAnimLoop"] = false,
            ["bCommonIdle"] = false,
            ["bFixedPlayRate"] = false,
            ["bHoldWeapon"] = true,
            ["bNotInterrupt"] = false,
          },
        },
        [4] = {
          ["EndTime"] = 0.066666670143604,
          ["StartTime"] = 0.066666670143604,
          ["TaskData"] = {
            ["AnimRootInfo"] = {
            },
            ["Collision"] = true,
            ["Curve"] = "",
            ["Destination"] = {
              ["DestArgs"] = {
                [1] = -230,
                [2] = 0,
                [3] = 0,
              },
              ["DestType"] = 16,
            },
            ["Duration"] = 0,
            ["EnableRotate"] = false,
            ["Ground"] = true,
            ["Index"] = 0,
            ["MaxDistance"] = -1,
            ["MoveByAnimGUID"] = 0,
            ["RootMotionCurve"] = "",
            ["RootMotionCurveLinearColor"] = "",
            ["TaskTargetType"] = 3,
            ["TaskType"] = 228,
            ["Time"] = 3,
            ["bUseAnimRootMotion"] = false,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data