local Data = {
  ["OtherResources"] = {
  },
  ["StartNode"] = {
    ["BindSkillID"] = 0,
    ["ConditionTransferMap"] = {
    },
    ["NodeGuid"] = {
      ["A"] = -1626728127,
      ["B"] = 1074701130,
      ["C"] = 262928305,
      ["D"] = 1178082604,
    },
    ["TaskList"] = {
    },
    ["TaskMap"] = {
    },
  },
  ["StateMap"] = {
    [1] = {
      ["BindSkillID"] = 0,
      ["ClientTaskEndList"] = {
        [1] = 1,
      },
      ["ClientTaskStartList"] = {
        [1] = 1,
      },
      ["ConditionTransferMap"] = {
      },
      ["Duration"] = 0,
      ["EventTransferMap"] = {
      },
      ["NextStateID"] = 0,
      ["NodeGuid"] = {
        ["A"] = -73031167,
        ["B"] = 1108989808,
        ["C"] = 1937736859,
        ["D"] = 989575823,
      },
      ["ServerTaskEndList"] = {
      },
      ["ServerTaskStartList"] = {
      },
      ["TaskMap"] = {
        [1] = {
          ["EndTime"] = 0.33333334326744,
          ["StartTime"] = 0,
          ["TaskData"] = {
            ["Alpha"] = 1,
            ["AttachComponentName"] = "",
            ["DockBone"] = "weapon_l",
            ["Duration"] = 0.33333334326744,
            ["EffectPriority"] = 5,
            ["FadeIn"] = 0,
            ["FadeInCurve"] = "",
            ["FadeOut"] = 0,
            ["FadeOutCurve"] = "",
            ["Index"] = 0,
            ["LinkAgentType"] = 0,
            ["Niagara"] = "/Game/Arts/Effects/FX_Character/Fool_FX/UltiSkill01New/NS_FoolUltiSkill01New_Line.NS_FoolUltiSkill01New_Line",
            ["NiagaraMeshParam"] = {
              ["SKMesh"] = "mesh",
            },
            ["NiagaraMobile"] = "",
            ["PlayRate"] = 1,
            ["PosOffset"] = {
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["RotOffset"] = {
              ["Pitch"] = 0,
              ["Roll"] = 0,
              ["Yaw"] = 0,
            },
            ["RotationQuat"] = {
              ["W"] = 1,
              ["X"] = 0,
              ["Y"] = 0,
              ["Z"] = 0,
            },
            ["SKMeshTargetType"] = 2,
            ["ScaleOffset"] = {
              ["X"] = 1,
              ["Y"] = 1,
              ["Z"] = 1,
            },
            ["SkeletalFilteredBones"] = {
            },
            ["SpellFieldID"] = 0,
            ["TaskTargetType"] = 1,
            ["TaskType"] = 213,
            ["UberGraphFrame"] = {
            },
            ["bAttachScale"] = true,
          },
        },
      },
      ["TrackConditionMaps"] = {
      },
      ["bCustomMergeToleranceOnExportAnimCurve"] = false,
    },
  },
  ["bAllowDeletion"] = true,
  ["bAllowRenaming"] = false,
  ["bEditable"] = true,
}
return Data