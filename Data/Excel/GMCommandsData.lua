--
-- 表名: $GMCommands.xlsx  页名：$GMCommands
--

local TopData = {
	data = {
		["QuickTeamUpServer"] = {
			["Cmd"] = "QuickTeamUpServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584908288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853343744'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintTeamInfo"] = {
			["Cmd"] = "PrintTeamInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584908544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853344000'),
			["Args"] = {"uid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamCollect"] = {
			["Cmd"] = "TeamCollect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584908800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853344256'),
			["Args"] = {"targetID", "operate"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamPlayerCard"] = {
			["Cmd"] = "TeamPlayerCard",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584909056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853344512'),
			["Args"] = {"EntityID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenQuickTeamUp"] = {
			["Cmd"] = "OpenQuickTeamUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584909312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853344768'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeamPrintMatchPool"] = {
			["Cmd"] = "TeamPrintMatchPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584909568'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584909568'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamAddOneMember"] = {
			["Cmd"] = "TeamAddOneMember",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584909824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853345280'),
			["Args"] = {"professionID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamAddAllMembers"] = {
			["Cmd"] = "TeamAddAllMembers",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584910080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853345536'),
			["Args"] = {"num"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CloseTeamFollow"] = {
			["Cmd"] = "CloseTeamFollow",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584910336'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853345792'),
			["Args"] = {"bClose"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CreateRobotTeam"] = {
			["Cmd"] = "CreateRobotTeam",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584910592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853346048'),
			["Args"] = {"count", "targetID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EquipRefreshWeekly"] = {
			["Cmd"] = "EquipRefreshWeekly",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562688'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584910848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584910848'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSwitchEquipPlan"] = {
			["Cmd"] = "GMSwitchEquipPlan",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562688'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584911104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584911104'),
			["Args"] = {"planId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMPutOnAllEquipIfCan"] = {
			["Cmd"] = "GMPutOnAllEquipIfCan",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562688'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584911360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853346816'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SummonNpc"] = {
			["Cmd"] = "SummonNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584911616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853347072'),
			["Args"] = {"SkillLvl", "NpcTid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintBuffLevel"] = {
			["Cmd"] = "PrintBuffLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584911872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853347328'),
			["Args"] = {"id"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillAllEnemy"] = {
			["Cmd"] = "KillAllEnemy",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584912128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853347584'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["PauseAllBotAI"] = {
			["Cmd"] = "PauseAllBotAI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584912640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853348096'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ResumeAllBotAI"] = {
			["Cmd"] = "ResumeAllBotAI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584912896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853348352'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetMaxBattleNiagara"] = {
			["Cmd"] = "SetMaxBattleNiagara",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584913152'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584913152'),
			["Args"] = {"MaxNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartMorph"] = {
			["Cmd"] = "StartMorph",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_54495618797312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853348864'),
			["Args"] = {"MorphID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CreateAvatarMirror"] = {
			["Cmd"] = "CreateAvatarMirror",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584913664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853349120'),
			["Args"] = {"camp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ReloadAbilityTemplate"] = {
			["Cmd"] = "ReloadAbilityTemplate",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584913920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853349376'),
			["Args"] = {"ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["SwitchAllowMultiJump"] = {
			["Cmd"] = "SwitchAllowMultiJump",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584914176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853349632'),
			["Args"] = {"InAllow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDamageLog"] = {
			["Cmd"] = "ShowDamageLog",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584914432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853349888'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenSequencePage"] = {
			["Cmd"] = "gmOpenSequencePage",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584914944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenRefinePage"] = {
			["Cmd"] = "gmOpenRefinePage",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584915712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853351168'),
			["Args"] = {"recipeID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmGetRefineMaterial"] = {
			["Cmd"] = "gmGetRefineMaterial",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584915968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853351424'),
			["Args"] = {"recipeID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectPreloadAsset"] = {
			["Cmd"] = "SystemEffectPreloadAsset",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584916224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853351680'),
			["Args"] = {"InPath"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectInitialEffectActor"] = {
			["Cmd"] = "SystemEffectInitialEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584916480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853351936'),
			["Args"] = {"InPath", "sx", "sy", "sz", "sw", "px", "py", "pz"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectHideSingleActorEffect"] = {
			["Cmd"] = "SystemEffectHideSingleActorEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584916736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853352192'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectShowSingleActorEffect"] = {
			["Cmd"] = "SystemEffectShowSingleActorEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584916992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853352448'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectDestroySingleEffectActor"] = {
			["Cmd"] = "SystemEffectDestroySingleEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584917248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853352704'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectDestroyAllEffectActor"] = {
			["Cmd"] = "SystemEffectDestroyAllEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584917504'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowItemID"] = {
			["Cmd"] = "ShowItemID",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328563712'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584917760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853353216'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeTraceTask"] = {
			["Cmd"] = "ChangeTraceTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584918016'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853353472'),
			["Args"] = {"TaskRingID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CompleteTaskTalk"] = {
			["Cmd"] = "CompleteTaskTalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584918272'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853353728'),
			["Args"] = {"NPCID", "TalkID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayDialogue"] = {
			["Cmd"] = "PlayDialogue",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584918528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853353984'),
			["Args"] = {"dialogueID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopDialogue"] = {
			["Cmd"] = "StopDialogue",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584918784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584918784'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartMistery"] = {
			["Cmd"] = "StartMistery",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584919040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853354496'),
			["Args"] = {"MisteryID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayCutscene"] = {
			["Cmd"] = "PlayCutscene",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584919296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853354752'),
			["Args"] = {"cutsceneID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayLevelSequence"] = {
			["Cmd"] = "PlayLevelSequence",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584919552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853355008'),
			["Args"] = {"LevelSequenceID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayAside"] = {
			["Cmd"] = "PlayAside",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_56145959985920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853356032'),
			["Args"] = {"asideID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchLuaVersion"] = {
			["Cmd"] = "SwitchLuaVersion",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584920832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853356288'),
			["Args"] = {"bUseLua"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TaskToggleGMHeadInfo"] = {
			["Cmd"] = "TaskToggleGMHeadInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584921088'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853356544'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenFog"] = {
			["Cmd"] = "OpenFog",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584921344'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853356800'),
			["Args"] = {"R", "G", "B", "distance", "density", "bOverlayClimate"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseFog"] = {
			["Cmd"] = "CloseFog",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584921600'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GoToTaskTracePos"] = {
			["Cmd"] = "GoToTaskTracePos",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584921856'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853357312'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadData"] = {
			["Cmd"] = "ReloadData",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584922112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853357568'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadQuestData"] = {
			["Cmd"] = "ReloadQuestData",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584922368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853357824'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GetLocalPatchs"] = {
			["Cmd"] = "GetLocalPatchs",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584922624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584922624'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["RepairClient"] = {
			["Cmd"] = "RepairClient",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584922880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584922880'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseHotPatch"] = {
			["Cmd"] = "CloseHotPatch",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584923136'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584923136'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DownloadInGame"] = {
			["Cmd"] = "DownloadInGame",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584923392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584923392'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadMapData"] = {
			["Cmd"] = "ReloadMapData",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584923648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853359104'),
			["Args"] = {"reloadFlowchart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["JumpToUI"] = {
			["Cmd"] = "JumpToUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25908316488448'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584923904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853359360'),
			["Args"] = {"target"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientLogin"] = {
			["Cmd"] = "ClientLogin",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584924160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853359616'),
			["Args"] = {"ServerAdd", "UserName", "Password"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DeleteRole"] = {
			["Cmd"] = "DeleteRole",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_54632789344768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853359872'),
			["Args"] = {"RoleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintRoleInfo"] = {
			["Cmd"] = "PrintRoleInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584924672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853360128'),
			["Args"] = {"type"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ConfigureLocalSkillSlot"] = {
			["Cmd"] = "ConfigureLocalSkillSlot",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584924928'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853360384'),
			["Args"] = {"SkillID", "SkillSlot"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["TeleportField"] = {
			["Cmd"] = "TeleportField",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584925184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853360640'),
			["Args"] = {"InsID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeStagger"] = {
			["Cmd"] = "ChangeStagger",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584925440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853360896'),
			["Args"] = {"entityId", "newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CancelPauseLoading"] = {
			["Cmd"] = "CancelPauseLoading",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584925696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584925696'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintAllLine"] = {
			["Cmd"] = "PrintAllLine",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584925952'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584925952'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GoToPlayerLogic"] = {
			["Cmd"] = "GoToPlayerLogic",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584926208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853361664'),
			["Args"] = {"TargetUserName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SpawnTracingTaskNpc"] = {
			["Cmd"] = "SpawnTracingTaskNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584926464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584926464'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CompleteTracingTaskTalk"] = {
			["Cmd"] = "CompleteTracingTaskTalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584926720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584926720'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintTaskList"] = {
			["Cmd"] = "PrintTaskList",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584926976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853362432'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["BatchAddItem"] = {
			["Cmd"] = "BatchAddItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584927232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853362688'),
			["Args"] = {"items", "counts", "isBind"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["BatchDelItem"] = {
			["Cmd"] = "BatchDelItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584927488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853362944'),
			["Args"] = {"items", "counts", "isBind"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetBagNewState"] = {
			["Cmd"] = "SetBagNewState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584927744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853363200'),
			["Args"] = {"bagID", "uids", "isnews"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ItemUse"] = {
			["Cmd"] = "ItemUse",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584928000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853363456'),
			["Args"] = {"bagID", "uid", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ReqGovernWar"] = {
			["Cmd"] = "ReqGovernWar",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584928256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853363712'),
			["Args"] = {"modeID", "rankID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ConfirmEnter"] = {
			["Cmd"] = "ConfirmEnter",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584928512'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853363968'),
			["Args"] = {"bAgree"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReqOtherRoleAttrBrief"] = {
			["Cmd"] = "ReqOtherRoleAttrBrief",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584928768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853364224'),
			["Args"] = {"AvatarActorID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReqOtherRoleAttrDetail"] = {
			["Cmd"] = "ReqOtherRoleAttrDetail",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584929024'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853364480'),
			["Args"] = {"AvatarActorID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChatMessage"] = {
			["Cmd"] = "ChatMessage",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584929280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584929280'),
			["Args"] = {"channelType", "messageType", "messageText"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SendPrivateChat"] = {
			["Cmd"] = "SendPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584929792'),
			["Description"] = "TargetID Content",
			["Args"] = {"TargetID", "Content"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FetchPrivateChatCache"] = {
			["Cmd"] = "FetchPrivateChatCache",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584930048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584930048'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FetchPriavteChatHistory"] = {
			["Cmd"] = "FetchPriavteChatHistory",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584930304'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TopPrivateChat"] = {
			["Cmd"] = "TopPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584930560'),
			["Description"] = "TargetID bTop",
			["Args"] = {"TargetID", "bTop"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["BlockPrivateChat"] = {
			["Cmd"] = "BlockPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584930816'),
			["Description"] = "TargetID bBlock",
			["Args"] = {"TargetID", "bBlock"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OnMsgFetchUnread"] = {
			["Cmd"] = "OnMsgFetchUnread",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584931072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584931072'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DelPrivateChatHistory"] = {
			["Cmd"] = "DelPrivateChatHistory",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584931328'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DelPrivateChatCache"] = {
			["Cmd"] = "DelPrivateChatCache",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584931584'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenMachineReview"] = {
			["Cmd"] = "OpenMachineReview",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584931840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853367296'),
			["Args"] = {"IsOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["QueryOtherRoleOnlineBrief"] = {
			["Cmd"] = "QueryOtherRoleOnlineBrief",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584932096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853367552'),
			["Args"] = {"memberID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StartWwiseProfiler"] = {
			["Cmd"] = "StartWwiseProfiler",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584932352'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopWwiseProfiler"] = {
			["Cmd"] = "StopWwiseProfiler",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584932608'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestUseItem"] = {
			["Cmd"] = "TestUseItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584932864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853368320'),
			["Args"] = {"itemUid", "itemId", "itemCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionBid"] = {
			["Cmd"] = "DungeonAuctionBid",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584933120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853368576'),
			["Args"] = {"AuctionID", "GoodsID", "MoneyType", "MoneyCount"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionGiveUp"] = {
			["Cmd"] = "DungeonAuctionGiveUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584933376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853368832'),
			["Args"] = {"AuctionID", "GoodsID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonRoll"] = {
			["Cmd"] = "DungeonRoll",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584933632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853369088'),
			["Args"] = {"RollID", "GoodsID", "RollType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenAndCloseMailUI"] = {
			["Cmd"] = "OpenAndCloseMailUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584933888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853369344'),
			["Args"] = {"Count"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["InvitePlayerUI"] = {
			["Cmd"] = "InvitePlayerUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584934144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584934144'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintModuleLockInfo"] = {
			["Cmd"] = "PrintModuleLockInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584934400'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584934400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetNearActorSpeed"] = {
			["Cmd"] = "SetNearActorSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584934656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584934656'),
			["Args"] = {"speed"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintTeamModuleLockInfo"] = {
			["Cmd"] = "PrintTeamModuleLockInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584934912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584934912'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowTestUI"] = {
			["Cmd"] = "ShowTestUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584935168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853370624'),
			["Args"] = {"slot"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowGacha"] = {
			["Cmd"] = "ShowGacha",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584935424'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowFellow"] = {
			["Cmd"] = "ShowFellow",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584935680'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChatGMSystem"] = {
			["Cmd"] = "ChatGMSystem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584935936'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853371392'),
			["Args"] = {"Text", "ToUser"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowTargetDialogPopUp"] = {
			["Cmd"] = "ShowTargetDialogPopUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584936192'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584936192'),
			["Args"] = {"id", "text1", "text2", "text3", "text4", "text5"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MediaPlayerInvestigate"] = {
			["Cmd"] = "MediaPlayerInvestigate",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584936448'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LogRedPointNumber"] = {
			["Cmd"] = "LogRedPointNumber",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584936704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584936704'),
			["Args"] = {"Name", "ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["resetMoneySaleDataByGM"] = {
			["Cmd"] = "resetMoneySaleDataByGM",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584936960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584936960'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmControlCameraZoomScale"] = {
			["Cmd"] = "gmControlCameraZoomScale",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584937216'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853372672'),
			["Args"] = {"param"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseAllUI"] = {
			["Cmd"] = "CloseAllUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584937472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseAllUIAndGM"] = {
			["Cmd"] = "CloseAllUIAndGM",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584937728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853373184'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenCustomRole"] = {
			["Cmd"] = "OpenCustomRole",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584937984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853373440'),
			["Args"] = {"isOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchShowWidgetPath"] = {
			["Cmd"] = "SwitchShowWidgetPath",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564224'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584938240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853373696'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["KillSelf"] = {
			["Cmd"] = "KillSelf",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584938496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853373952'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["ShowEntityID"] = {
			["Cmd"] = "ShowEntityID",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584938752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584938752'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeSelfStagger"] = {
			["Cmd"] = "ChangeSelfStagger",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584939008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853374464'),
			["Args"] = {"newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddExpandState"] = {
			["Cmd"] = "AddExpandState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584939264'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584939264'),
			["Args"] = {"newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RemoveExpandState"] = {
			["Cmd"] = "RemoveExpandState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584939520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584939520'),
			["Args"] = {"state", "forceRemove"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowEntityInfo"] = {
			["Cmd"] = "ShowEntityInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584939776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584939776'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeSpeed"] = {
			["Cmd"] = "ChangeSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584940032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853375488'),
			["Args"] = {"SpeedState"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UnlockAllModule"] = {
			["Cmd"] = "UnlockAllModule",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584940288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584940288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["PrintMainPlayerState"] = {
			["Cmd"] = "PrintMainPlayerState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584940544'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintSystemMachineState"] = {
			["Cmd"] = "PrintSystemMachineState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584940800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584940800'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowLogicServerID"] = {
			["Cmd"] = "ShowLogicServerID",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584941056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584941056'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PlayerRunFlowchart"] = {
			["Cmd"] = "PlayerRunFlowchart",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584941312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853376768'),
			["Args"] = {"flowchartName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PreviewAvatarPreset"] = {
			["Cmd"] = "PreviewAvatarPreset",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584941568'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853377024'),
			["Args"] = {"AvatarPresetName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModelEditorTest"] = {
			["Cmd"] = "ModelEditorTest",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584941824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853377280'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyKawaiiStatus"] = {
			["Cmd"] = "ModifyKawaiiStatus",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584942080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853377536'),
			["Args"] = {"bMan", "bClothOpen", "bHairOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyChaosClothStatus"] = {
			["Cmd"] = "ModifyChaosClothStatus",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584942336'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853377792'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AddTitle"] = {
			["Cmd"] = "AddTitle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584942592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853378048'),
			["Args"] = {"titleType", "titleID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RmTitle"] = {
			["Cmd"] = "RmTitle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584942848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853378304'),
			["Args"] = {"titleType", "titleID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnterGroupArena"] = {
			["Cmd"] = "EnterGroupArena",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584943104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853378560'),
			["Args"] = {"modeID", "rankID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CalcTeamArenaBattle"] = {
			["Cmd"] = "CalcTeamArenaBattle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584943360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853378816'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PauseTeamArenaBattle"] = {
			["Cmd"] = "PauseTeamArenaBattle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584943616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853379072'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowArena5V5Battle"] = {
			["Cmd"] = "ShowArena5V5Battle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584944640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853380096'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OverAllScalabilityLevel"] = {
			["Cmd"] = "OverAllScalabilityLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565504'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584944896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853380352'),
			["Args"] = {"Level"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["Dungeon"] = {
			["Cmd"] = "Dungeon",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_55869739894784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853380608'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TaskBoardUI"] = {
			["Cmd"] = "TaskBoardUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584945408'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853380864'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SkillCustomizerUI"] = {
			["Cmd"] = "SkillCustomizerUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584945664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853381120'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CrosswordPuzzle"] = {
			["Cmd"] = "CrosswordPuzzle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584945920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853381376'),
			["Args"] = {"PuzzleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetServerLaunchTime"] = {
			["Cmd"] = "SetServerLaunchTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584946176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584946176'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetServerLaunchTime"] = {
			["Cmd"] = "GetServerLaunchTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584946432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584946432'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["JigsawPuzzle"] = {
			["Cmd"] = "JigsawPuzzle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584946688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853382144'),
			["Args"] = {"PuzzleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSettings"] = {
			["Cmd"] = "ShowSettings",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584946944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584946944'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSkillCustomizer"] = {
			["Cmd"] = "ShowSkillCustomizer",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584947200'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["NeedSdkLogin"] = {
			["Cmd"] = "NeedSdkLogin",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584947456'),
			["Description"] = "1=true,other=false",
			["Args"] = {"needSdkLogin"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearAnnouncementStorage"] = {
			["Cmd"] = "ClearAnnouncementStorage",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584947712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853383168'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenClientTrackingOrReport"] = {
			["Cmd"] = "OpenClientTrackingOrReport",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584947968'),
			["Description"] = "1=true,other=false",
			["Args"] = {"needTracking"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenTrade"] = {
			["Cmd"] = "OpenTrade",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584948224'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenAchievement"] = {
			["Cmd"] = "OpenAchievement",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584948480'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UIjump"] = {
			["Cmd"] = "UIjump",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584948736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853384192'),
			["Args"] = {"jumpid"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenRolePlay"] = {
			["Cmd"] = "OpenRolePlay",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584948992'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearTriggerDebugLine"] = {
			["Cmd"] = "ClearTriggerDebugLine",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584949248'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnNpc"] = {
			["Cmd"] = "SpawnNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584949504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853384960'),
			["Args"] = {"npcTpId", "npcType", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSpawnNpcInCircle"] = {
			["Cmd"] = "GMSpawnNpcInCircle",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584949760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853385216'),
			["Args"] = {"facadeID", "count", "radius"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableAudioVolumeDebugDraw"] = {
			["Cmd"] = "EnableAudioVolumeDebugDraw",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584950016'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DisableAudioVolumeDebugDraw"] = {
			["Cmd"] = "DisableAudioVolumeDebugDraw",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584950272'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnTestAINpc"] = {
			["Cmd"] = "SpawnTestAINpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584950528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853385984'),
			["Args"] = {"npcTemplateID", "npcType", "flowChartPath"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillMonster"] = {
			["Cmd"] = "KillMonster",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584950784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853386240'),
			["Args"] = {"targetID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillNpc"] = {
			["Cmd"] = "KillNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584951040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853386496'),
			["Args"] = {"entityId", "clearAll"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonSettlement"] = {
			["Cmd"] = "DungeonSettlement",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584951296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853386752'),
			["Args"] = {"IsSucceed"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetAggroInfoList"] = {
			["Cmd"] = "GetAggroInfoList",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584951552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853387008'),
			["Args"] = {"targetNpcId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenAggroInfoList"] = {
			["Cmd"] = "OpenAggroInfoList",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584951808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584951808'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionStart"] = {
			["Cmd"] = "DungeonAuctionStart",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584952064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853387520'),
			["Args"] = {"DropID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonRollStart"] = {
			["Cmd"] = "DungeonRollStart",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584952320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853387776'),
			["Args"] = {"DropID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DestroyNpcs"] = {
			["Cmd"] = "DestroyNpcs",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584952576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853388032'),
			["Args"] = {"NpcType", "TemplateID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnterTowerClimb"] = {
			["Cmd"] = "EnterTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584952832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853388288'),
			["Args"] = {"TowerLevel"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LeaveTowerClimb"] = {
			["Cmd"] = "LeaveTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584953088'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ClearTowerClimbRecord"] = {
			["Cmd"] = "ClearTowerClimbRecord",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584953344'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetTowerClimbLevel"] = {
			["Cmd"] = "SetTowerClimbLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584953600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853388288'),
			["Args"] = {"TowerLevel"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StartAutoTowerClimb"] = {
			["Cmd"] = "StartAutoTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584953856'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StopAutoTowerClimb"] = {
			["Cmd"] = "StopAutoTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584954112'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ModifyGameTimeSpeed"] = {
			["Cmd"] = "ModifyGameTimeSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584954368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853389824'),
			["Args"] = {"timeFlowSpeed"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyGameTime"] = {
			["Cmd"] = "ModifyGameTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584954624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853390080'),
			["Args"] = {"hour", "minute"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientControlClimate"] = {
			["Cmd"] = "ClientControlClimate",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584954880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853390336'),
			["Args"] = {"climateId"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ResetClimateToServer"] = {
			["Cmd"] = "ResetClimateToServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584955136'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FilterNpcAttachFlowchart"] = {
			["Cmd"] = "FilterNpcAttachFlowchart",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584955392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853390848'),
			["Args"] = {"templateID", "flowchartName", "range"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TriggerDrop"] = {
			["Cmd"] = "TriggerDrop",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584955648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853391104'),
			["Args"] = {"dropID", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMNewBagAddItem"] = {
			["Cmd"] = "GMNewBagAddItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584955904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853391360'),
			["Args"] = {"itemId", "itemCount", "bindType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMNewBagConsumeItem"] = {
			["Cmd"] = "GMNewBagConsumeItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584956160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853391616'),
			["Args"] = {"itemId", "itemCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMBatchAddItems"] = {
			["Cmd"] = "GMBatchAddItems",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584956416'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853391872'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMCombineFellow"] = {
			["Cmd"] = "GMCombineFellow",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584956672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853392128'),
			["Args"] = {"ConfigID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowJoinCombat"] = {
			["Cmd"] = "GMFellowJoinCombat",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584956928'),
			["Description"] = "(ConfigID,bCancle,pos)",
			["Args"] = {"ConfigID", "bCancel", "Pos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowAssistCombat"] = {
			["Cmd"] = "GMFellowAssistCombat",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584957184'),
			["Description"] = "(ConfigID,bCancle,pos)",
			["Args"] = {"ConfigID", "bCancel", "Pos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowLevelUP"] = {
			["Cmd"] = "GMFellowLevelUP",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584957440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853392896'),
			["Args"] = {"ConfigID", "UseItems"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMStarUpLevel"] = {
			["Cmd"] = "GMStarUpLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584957696'),
			["Description"] = "(ConfigID, StarUpType)",
			["Args"] = {"ConfigID", "StarUpType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMNewBagGetInventoryPool"] = {
			["Cmd"] = "GMNewBagGetInventoryPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584957952'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853393408'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GachaOverleapAnim"] = {
			["Cmd"] = "GachaOverleapAnim",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584958208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853393664'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMClearBag"] = {
			["Cmd"] = "GMClearBag",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584958464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853393920'),
			["Args"] = {"invId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenLetter"] = {
			["Cmd"] = "OpenLetter",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584958720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853394176'),
			["Args"] = {"LetterID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmitemsubmitnew"] = {
			["Cmd"] = "gmitemsubmitnew",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584958976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853394432'),
			["Args"] = {"itemSubmitID", "itemID1", "count1", "itemID2", "count2", "itemID3", "count3"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMClearDrop"] = {
			["Cmd"] = "GMClearDrop",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584959232'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSetFreezeTime"] = {
			["Cmd"] = "GMSetFreezeTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328562432'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584959488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853394944'),
			["Args"] = {"invId", "slotIndex", "timestamp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddCircleBlock"] = {
			["Cmd"] = "AddCircleBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584959744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853395200'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "radius", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddAnnularBlock"] = {
			["Cmd"] = "AddAnnularBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584960000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853395456'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "innerRadius", "outerRadius", "blockMask", "unidirect", "inner2Outter"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddRectBlock"] = {
			["Cmd"] = "AddRectBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584960256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853395712'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "halfW", "halfL", "facing", "blockMask", "unidirect"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddFanBlock"] = {
			["Cmd"] = "AddFanBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584960256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853395968'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "innerRadius", "outerRadius", "facing", "halfAngle", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RemoveBlock"] = {
			["Cmd"] = "RemoveBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584960768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584960768'),
			["Args"] = {"blockId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["IsBlock"] = {
			["Cmd"] = "IsBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584961024'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584961024'),
			["Args"] = {"x", "y", "z", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RaycastCheckReach"] = {
			["Cmd"] = "RaycastCheckReach",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564992'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584961280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584961280'),
			["Args"] = {"sX", "sY", "sZ", "eX", "eY", "eZ", "stickGround", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayincspirit"] = {
			["Cmd"] = "gmroleplayincspirit",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584961536'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853396992'),
			["Args"] = {"val", "canOverMax"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayaddexp"] = {
			["Cmd"] = "gmroleplayaddexp",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584961792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853397248'),
			["Args"] = {"role", "exp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetexp"] = {
			["Cmd"] = "gmroleplaysetexp",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584962048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853397504'),
			["Args"] = {"role", "exp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetrolelevel"] = {
			["Cmd"] = "gmroleplaysetrolelevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584962304'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853397760'),
			["Args"] = {"role", "level"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayresetskilltree"] = {
			["Cmd"] = "gmroleplayresetskilltree",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584962560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853398016'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetpoint"] = {
			["Cmd"] = "gmroleplaysetpoint",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584962816'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853398272'),
			["Args"] = {"role", "point"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayresetrole"] = {
			["Cmd"] = "gmroleplayresetrole",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584963072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853398016'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetcurrentrole"] = {
			["Cmd"] = "gmroleplaysetcurrentrole",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584963328'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853398016'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetbasicproperty"] = {
			["Cmd"] = "gmroleplaysetbasicproperty",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584963584'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853399040'),
			["Args"] = {"property", "value"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayunlockskill"] = {
			["Cmd"] = "gmroleplayunlockskill",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584963840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853399296'),
			["Args"] = {"role", "skillID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmOpenRolePlay"] = {
			["Cmd"] = "gmOpenRolePlay",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584964096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {"param"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenRolePlayIdentify"] = {
			["Cmd"] = "gmOpenRolePlayIdentify",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328571648'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584964352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853399808'),
			["Args"] = {"DiceID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmSpawnFortuneStation"] = {
			["Cmd"] = "gmSpawnFortuneStation",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584964608'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584964608'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmDestroyFortuneStation"] = {
			["Cmd"] = "gmDestroyFortuneStation",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584964864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584964864'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmPlayDanceChallenge"] = {
			["Cmd"] = "gmPlayDanceChallenge",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584965120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853400576'),
			["Args"] = {"challengeType", "uniqueId"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmroleplaymorph"] = {
			["Cmd"] = "gmroleplaymorph",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584965376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853400832'),
			["Args"] = {"identityID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishScheduleDailyTask"] = {
			["Cmd"] = "finishScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584965632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853401088'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["printScheduleDailyTasks"] = {
			["Cmd"] = "printScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584965888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["dailyRefreshScheduleDailyTasks"] = {
			["Cmd"] = "dailyRefreshScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584966144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["manualRefreshScheduleDailyTasks"] = {
			["Cmd"] = "manualRefreshScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584966400'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["printSingleScheduleDailyTask"] = {
			["Cmd"] = "printSingleScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584966656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853401088'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishScheduleFateRevelationTask"] = {
			["Cmd"] = "finishScheduleFateRevelationTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584966912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853401088'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishAllScheduleDailyTask"] = {
			["Cmd"] = "finishAllScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565760'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584967168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UploadSaved"] = {
			["Cmd"] = "UploadSaved",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584967424'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853402880'),
			["Args"] = {"ExtraInfo", "Interval"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UploadC7Log"] = {
			["Cmd"] = "UploadC7Log",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584967680'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853403136'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowBSStat"] = {
			["Cmd"] = "ShowBSStat",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584967936'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853403392'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDmgRecordLog"] = {
			["Cmd"] = "ShowDmgRecordLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584968192'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853403648'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaPandaRemote"] = {
			["Cmd"] = "LuaPandaRemote",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584968448'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584968448'),
			["Args"] = {"IP", "Port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCustomOP"] = {
			["Cmd"] = "EnableCustomOP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584968704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853404160'),
			["Args"] = {"Type"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowLag"] = {
			["Cmd"] = "ShowLag",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584968960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853404416'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DebugFreeCamera"] = {
			["Cmd"] = "DebugFreeCamera",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584969216'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853404672'),
			["Args"] = {"ResetPos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnablePrintFloorPhysicalMaterial"] = {
			["Cmd"] = "EnablePrintFloorPhysicalMaterial",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584969472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853404928'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GenerateAllNpcLocationData"] = {
			["Cmd"] = "GenerateAllNpcLocationData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584969728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853405184'),
			["Args"] = {"mapID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GenerateAllChairsLocationData"] = {
			["Cmd"] = "GenerateAllChairsLocationData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584969984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853405440'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintEntitiesPos"] = {
			["Cmd"] = "PrintEntitiesPos",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584970240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853405696'),
			["Args"] = {"actorType", "limitCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LoadURL"] = {
			["Cmd"] = "LoadURL",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584970496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853405952'),
			["Args"] = {"Url"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestCrash"] = {
			["Cmd"] = "TestCrash",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584970752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584970752'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DrawServerPosition"] = {
			["Cmd"] = "DrawServerPosition",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584971008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853406464'),
			["Args"] = {"acotrType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ToggleUIVisible"] = {
			["Cmd"] = "ToggleUIVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584971264'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853406720'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GC"] = {
			["Cmd"] = "GC",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584971520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584971520'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaProfileStart"] = {
			["Cmd"] = "LuaProfileStart",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584971776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853407232'),
			["Args"] = {"mode"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaProfileStop"] = {
			["Cmd"] = "LuaProfileStop",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584972032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584972032'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaMemReport"] = {
			["Cmd"] = "LuaMemReport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584972288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853407744'),
			["Args"] = {"outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaMemDiff"] = {
			["Cmd"] = "LuaMemDiff",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584972544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853408000'),
			["Args"] = {"file1", "file2", "outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaEntityMemReport"] = {
			["Cmd"] = "LuaEntityMemReport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584972800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853408256'),
			["Args"] = {"entityId", "outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaForceGC"] = {
			["Cmd"] = "LuaForceGC",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584973056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853408512'),
			["Args"] = {"checkMemChange"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaGetTotalMem"] = {
			["Cmd"] = "LuaGetTotalMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584973312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853408768'),
			["Args"] = {"isLoop", "interval"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["Disconnect"] = {
			["Cmd"] = "Disconnect",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584973568'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584973568'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintActorLocation"] = {
			["Cmd"] = "PrintActorLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584973824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584973824'),
			["Args"] = {"Interval", "ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintPostProcessVolume"] = {
			["Cmd"] = "PrintPostProcessVolume",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584974080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584974080'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleGMHeadInfo"] = {
			["Cmd"] = "ToggleGMHeadInfo",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908853356544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853356544'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetCameraParams"] = {
			["Cmd"] = "SetCameraParams",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584974592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853410048'),
			["Args"] = {"Name", "Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetLogLevel"] = {
			["Cmd"] = "SetLogLevel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584974848'),
			["Description"] = "1:Debug  4:Release",
			["Args"] = {"Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseInterpolationMovement"] = {
			["Cmd"] = "UseInterpolationMovement",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584975104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584975104'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ExecuteNavigation"] = {
			["Cmd"] = "ExecuteNavigation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584975360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853410816'),
			["Args"] = {"X", "Y", "SizeZ", "ShowTarget"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StatAtlasData"] = {
			["Cmd"] = "StatAtlasData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584975616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853411072'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartSLuaProfile"] = {
			["Cmd"] = "StartSLuaProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584975872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584975872'),
			["Args"] = {"IP", "Port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopSLuaProfile"] = {
			["Cmd"] = "StopSLuaProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584976128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584976128'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UploadPSO"] = {
			["Cmd"] = "UploadPSO",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584976384'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584976384'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SLuaBuildInProfile"] = {
			["Cmd"] = "SLuaBuildInProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584976640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853412096'),
			["Args"] = {"open", "memoryStackNum", "ip", "port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleHeadInfoVisible"] = {
			["Cmd"] = "ToggleHeadInfoVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584976896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584976896'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetDamageEffectTxtVisible"] = {
			["Cmd"] = "SetDamageEffectTxtVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584977152'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853412608'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetNewDamageEffectVisible"] = {
			["Cmd"] = "SetNewDamageEffectVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584977408'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853412608'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLua"] = {
			["Cmd"] = "StartProfileLua",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584977664'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLua"] = {
			["Cmd"] = "StopProfileLua",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584977920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853413376'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLuaMem"] = {
			["Cmd"] = "StartProfileLuaMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584978176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853413632'),
			["Args"] = {"memoryStackNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLuaMem"] = {
			["Cmd"] = "StopProfileLuaMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584978432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853413888'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLuaNewObjectMem"] = {
			["Cmd"] = "StartProfileLuaNewObjectMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584978688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853414144'),
			["Args"] = {"memoryStackNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLuaNewObjectMem"] = {
			["Cmd"] = "StopProfileLuaNewObjectMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584978944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853414400'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DumpObjectGraph"] = {
			["Cmd"] = "DumpObjectGraph",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584979200'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853414656'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchDebug"] = {
			["Cmd"] = "SwitchDebug",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584979456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853414912'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleMapTouchTeleport"] = {
			["Cmd"] = "ToggleMapTouchTeleport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584979712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853415168'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeResourceManagerDump"] = {
			["Cmd"] = "ChangeResourceManagerDump",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584979968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584979968'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintResourceManagerDump"] = {
			["Cmd"] = "PrintResourceManagerDump",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584980224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584980224'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DebugQuickStart"] = {
			["Cmd"] = "DebugQuickStart",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584980480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853415936'),
			["Args"] = {"profession", "sex"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetTrackLineDistance"] = {
			["Cmd"] = "SetTrackLineDistance",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584980736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584980736'),
			["Args"] = {"distance"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestMalloc"] = {
			["Cmd"] = "TestMalloc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584980992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853416448'),
			["Args"] = {"size"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestSpawnActor"] = {
			["Cmd"] = "TestSpawnActor",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584981248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853416704'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaInsightProfiler"] = {
			["Cmd"] = "LuaInsightProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584981504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853416960'),
			["Args"] = {"isStart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaInsightProfilerTraceFile"] = {
			["Cmd"] = "LuaInsightProfilerTraceFile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584981760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853417216'),
			["Args"] = {"isStart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AssistVar"] = {
			["Cmd"] = "AssistVar",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584982016'),
			["Description"] = "var,value",
			["Args"] = {"var", "value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnTestNpc"] = {
			["Cmd"] = "SpawnTestNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584982272'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584982272'),
			["Args"] = {"RowNum", "NpcType", "NpcDistance", "SpawnedMaxNum", "SpawnOneRowInterval"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleMiniMapDebug"] = {
			["Cmd"] = "ToggleMiniMapDebug",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584982528'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearAllTestNpc"] = {
			["Cmd"] = "ClearAllTestNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584982784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584982784'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SceneInspection"] = {
			["Cmd"] = "SceneInspection",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584983040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584983040'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseKCP"] = {
			["Cmd"] = "UseKCP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584983296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853418752'),
			["Args"] = {"enable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LogDeviceName"] = {
			["Cmd"] = "LogDeviceName",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584983552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853419008'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["bEnableDumpObjList"] = {
			["Cmd"] = "bEnableDumpObjList",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584983808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853419264'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayBinkMediaByOverlay"] = {
			["Cmd"] = "PlayBinkMediaByOverlay",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584984064'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayBinkMediaByTexture"] = {
			["Cmd"] = "PlayBinkMediaByTexture",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584984320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["Testdatacentermemory"] = {
			["Cmd"] = "Testdatacentermemory",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584984576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenLuaProfiler"] = {
			["Cmd"] = "OpenLuaProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584984832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseLuaProfiler"] = {
			["Cmd"] = "CloseLuaProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584985088'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853350400'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmLogUIObject"] = {
			["Cmd"] = "gmLogUIObject",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584985344'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853420800'),
			["Args"] = {"UIControllerName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenOrCloseMoveSyncLog"] = {
			["Cmd"] = "OpenOrCloseMoveSyncLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584985600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853421056'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetClockDiff"] = {
			["Cmd"] = "SetClockDiff",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584985856'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584985856'),
			["Args"] = {"Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableServerErrorLogToClient"] = {
			["Cmd"] = "EnableServerErrorLogToClient",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584986112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853421568'),
			["Args"] = {"KimID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DisableServerErrorLogToClient"] = {
			["Cmd"] = "DisableServerErrorLogToClient",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584986368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584986368'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintFaceData"] = {
			["Cmd"] = "PrintFaceData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584986624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853422080'),
			["Args"] = {"uid"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartUIAutomationProfile"] = {
			["Cmd"] = "StartUIAutomationProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584986880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584986880'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleFaceLight"] = {
			["Cmd"] = "ToggleFaceLight",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584987136'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ApplyIndividualPVP"] = {
			["Cmd"] = "ApplyIndividualPVP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584987392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853422848'),
			["Args"] = {"targetAvatarID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HomeClearInstance"] = {
			["Cmd"] = "HomeClearInstance",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584987648'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HomeCameraDissolveOpen"] = {
			["Cmd"] = "HomeCameraDissolveOpen",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584987904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853423360'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassHideAllEntities"] = {
			["Cmd"] = "MassHideAllEntities",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584988160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853423616'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassActivateAllEntities"] = {
			["Cmd"] = "MassActivateAllEntities",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584988416'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853423872'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassGenerateSpecificNpc"] = {
			["Cmd"] = "MassGenerateSpecificNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584988672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853424128'),
			["Args"] = {"MassNpcName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenCreateLock"] = {
			["Cmd"] = "gmOpenCreateLock",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584988928'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetTime"] = {
			["Cmd"] = "SetTime",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584989184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853424640'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetTime"] = {
			["Cmd"] = "GetTime",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584989440'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnablePrivateSceneActorAoi"] = {
			["Cmd"] = "EnablePrivateSceneActorAoi",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584989696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853425152'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintUEActorData"] = {
			["Cmd"] = "PrintUEActorData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584989952'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCookMap"] = {
			["Cmd"] = "EnableCookMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584990208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853425152'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableEngineShortcut"] = {
			["Cmd"] = "EnableEngineShortcut",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584990464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853425920'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowAoiState"] = {
			["Cmd"] = "ShowAoiState",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564480'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584990720'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableActorLimitClip"] = {
			["Cmd"] = "EnableActorLimitClip",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564480'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584990976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584990976'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableActorViewMatrixClip"] = {
			["Cmd"] = "EnableActorViewMatrixClip",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564480'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584991232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584991232'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenDragonNavigate"] = {
			["Cmd"] = "OpenDragonNavigate",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584991488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584991488'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["IOSGPUCapture"] = {
			["Cmd"] = "IOSGPUCapture",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584991744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853427200'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayAnnouncementMarquee"] = {
			["Cmd"] = "PlayAnnouncementMarquee",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584992000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853427456'),
			["Args"] = {"newsTickerType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TriggerFireJump"] = {
			["Cmd"] = "TriggerFireJump",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584992256'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AutoSkillCast"] = {
			["Cmd"] = "AutoSkillCast",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584992512'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartPVPMatch"] = {
			["Cmd"] = "StartPVPMatch",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584992768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584992768'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CancelPVPMatch"] = {
			["Cmd"] = "CancelPVPMatch",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584993024'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584993024'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMCreateNpcTailAndWarning"] = {
			["Cmd"] = "GMCreateNpcTailAndWarning",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584993280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584993280'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["WitchCheck"] = {
			["Cmd"] = "WitchCheck",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328571648'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584993536'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853428992'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PvpConfirm"] = {
			["Cmd"] = "PvpConfirm",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584993792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584993792'),
			["Args"] = {"bAgree"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PvpCancel"] = {
			["Cmd"] = "PvpCancel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584994048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584994048'),
			["Args"] = {"bPrint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintSneakWarning"] = {
			["Cmd"] = "PrintSneakWarning",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584994304'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853429760'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CanCompleteCondition"] = {
			["Cmd"] = "CanCompleteCondition",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584994560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584994560'),
			["Args"] = {"system", "key", "triggerID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RegisterCustomTrigger"] = {
			["Cmd"] = "RegisterCustomTrigger",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584994816'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584994816'),
			["Args"] = {"key", "triggerID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowPlayerLocation"] = {
			["Cmd"] = "ShowPlayerLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584995072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853430528'),
			["Args"] = {"bShow", "bPrint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayPPEffectPreset"] = {
			["Cmd"] = "PlayPPEffectPreset",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584995328'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853430784'),
			["Args"] = {"EffectID", "Duration", "AlphaIn", "AlphaOut"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlaySceneEffect"] = {
			["Cmd"] = "PlaySceneEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584995584'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853431040'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayEntityEffect"] = {
			["Cmd"] = "PlayEntityEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584995840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853431296'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayScreenEffect"] = {
			["Cmd"] = "PlayScreenEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328565248'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584996096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853431552'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowFPS"] = {
			["Cmd"] = "ShowFPS",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584996352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853431808'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["ShowStatUnit"] = {
			["Cmd"] = "ShowStatUnit",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584996608'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853432064'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["SkipCutscene"] = {
			["Cmd"] = "SkipCutscene",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584996864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584996864'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeleportByMeter"] = {
			["Cmd"] = "TeleportByMeter",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584997120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853432576'),
			["Args"] = {"X, Y, Z"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GMGetFashionMountById"] = {
			["Cmd"] = "GMGetFashionMountById",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584997376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853432832'),
			["Args"] = {"mountID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashionMount"] = {
			["Cmd"] = "GMGetAllFashionMount",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584997632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853433088'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetFashionMountAccById"] = {
			["Cmd"] = "GMGetFashionMountAccById",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584997888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853433344'),
			["Args"] = {"mountAccId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashionMountAcc"] = {
			["Cmd"] = "GMGetAllFashionMountAcc",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584998144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853433088'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMUnlockTeleportPoint"] = {
			["Cmd"] = "GMUnlockTeleportPoint",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328572416'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584998400'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GMAddBlackFoe"] = {
			["Cmd"] = "GMAddBlackFoe",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584998656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908584998656'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMModifyAttraction"] = {
			["Cmd"] = "GMModifyAttraction",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584998912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853434368'),
			["Args"] = {"name", "num"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriend"] = {
			["Cmd"] = "GMAddFriend",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584999168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853434624'),
			["Args"] = {"name"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMResetFriendApplyNum"] = {
			["Cmd"] = "GMResetFriendApplyNum",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584999424'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMClearAllApplicationInfo"] = {
			["Cmd"] = "GMClearAllApplicationInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584999680'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriendsInServer"] = {
			["Cmd"] = "GMAddFriendsInServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908584999936'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriendsInDB"] = {
			["Cmd"] = "GMAddFriendsInDB",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585000192'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SpawnWorldBoss"] = {
			["Cmd"] = "SpawnWorldBoss",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561152'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585000448'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853435904'),
			["Args"] = {"npcTpId", "midHitHpRate"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchQuestSytem"] = {
			["Cmd"] = "SwitchQuestSytem",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567808'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585000704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853436160'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReturnQuestPlane"] = {
			["Cmd"] = "ReturnQuestPlane",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567808'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585000960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853436416'),
			["Args"] = {"QuestID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TriggerClientSystemAction"] = {
			["Cmd"] = "TriggerClientSystemAction",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585001216'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853436672'),
			["Args"] = {"actionName", "paramStr", "entityID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UnlockSelectLimit"] = {
			["Cmd"] = "UnlockSelectLimit",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585001472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853436928'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSelectEntityInfo"] = {
			["Cmd"] = "ShowSelectEntityInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585001728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853437184'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GetMaxFightEnrolledPlayerNum"] = {
			["Cmd"] = "GetMaxFightEnrolledPlayerNum",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585001984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853437440'),
			["Args"] = {"EntityId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UnlockCollectibles"] = {
			["Cmd"] = "UnlockCollectibles",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585002240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853437696'),
			["Args"] = {"collectiblesID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UpdateCollectiblesScore"] = {
			["Cmd"] = "UpdateCollectiblesScore",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585002496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853437952'),
			["Args"] = {"collectiblesType", "score"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchRideHorse"] = {
			["Cmd"] = "SwitchRideHorse",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585002752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908585002752'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowBikeUI"] = {
			["Cmd"] = "ShowBikeUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585003008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853438464'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchRiding"] = {
			["Cmd"] = "SwitchRiding",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585003264'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchWaterWalk"] = {
			["Cmd"] = "SwitchWaterWalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585003520'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DoUIAutomationProfile"] = {
			["Cmd"] = "DoUIAutomationProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585003776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853439232'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishedRing"] = {
			["Cmd"] = "FinishedRing",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561408'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585004032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853439488'),
			["Args"] = {"RingID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddPass"] = {
			["Cmd"] = "AddPass",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585004288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908585004288'),
			["Args"] = {"sid", "lv"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DelPass"] = {
			["Cmd"] = "DelPass",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25************'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25************'),
			["Args"] = {"sid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowEquipedPassive"] = {
			["Cmd"] = "ShowEquipedPassive",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585004800'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenNewbieGuide"] = {
			["Cmd"] = "OpenNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585005056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908585005056'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseNewbieGuide"] = {
			["Cmd"] = "CloseNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585005312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908585005312'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishNewbieGuide"] = {
			["Cmd"] = "FinishNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585005568'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853441024'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishAllNewbieGuide"] = {
			["Cmd"] = "FinishAllNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585005824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908585005824'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenNewbieGuideGroup"] = {
			["Cmd"] = "OpenNewbieGuideGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585006080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853441536'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ResetNewbieGuideGroup"] = {
			["Cmd"] = "ResetNewbieGuideGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328568320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585006336'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853441792'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReplaceNpcModelMaterial"] = {
			["Cmd"] = "ReplaceNpcModelMaterial",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585006592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853442048'),
			["Args"] = {"npcID", "materialID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetPvpPoints"] = {
			["Cmd"] = "SetPvpPoints",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585006848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853442304'),
			["Args"] = {"type", "rankPoints"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetTeamMemberPvpPoints"] = {
			["Cmd"] = "SetTeamMemberPvpPoints",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585007104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853442304'),
			["Args"] = {"type", "rankPoints", "includeSelf"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintWaterPipePuzzleMap"] = {
			["Cmd"] = "PrintWaterPipePuzzleMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585007360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853442816'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OutputKSBCReaderLog"] = {
			["Cmd"] = "OutputKSBCReaderLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585007616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TimerPerformanceAnalysis"] = {
			["Cmd"] = "TimerPerformanceAnalysis",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585007872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443328'),
			["Args"] = {"bOpen", "outputCount", "bReport"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EventSystemProfile"] = {
			["Cmd"] = "EventSystemProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585008128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443584'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["RpcPerformanceAnalysis"] = {
			["Cmd"] = "RpcPerformanceAnalysis",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585008384'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443584'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMAvatarMoveToLocation"] = {
			["Cmd"] = "GMAvatarMoveToLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585008640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853444096'),
			["Args"] = {"X", "Y", "Z"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSetLoadNavmeshAndVoxel"] = {
			["Cmd"] = "GMSetLoadNavmeshAndVoxel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585008896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenInteractorGMPanel"] = {
			["Cmd"] = "OpenInteractorGMPanel",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585009152'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetPvpWarmMatch"] = {
			["Cmd"] = "SetPvpWarmMatch",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585009408'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"gmWarmMatch"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetDropPickUpTrailParams"] = {
			["Cmd"] = "SetDropPickUpTrailParams",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585009664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853445120'),
			["Args"] = {"GlowTime", "TrailTime"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetSwitchValue"] = {
			["Cmd"] = "SetSwitchValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585009920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853445376'),
			["Args"] = {"switchName", "switchValue"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetSwitchValue"] = {
			["Cmd"] = "GetSwitchValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585010176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853445632'),
			["Args"] = {"switchName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DisableCreateRoleName"] = {
			["Cmd"] = "DisableCreateRoleName",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585010432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853445888'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintPvpMatchPool"] = {
			["Cmd"] = "PrintPvpMatchPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585010688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853446144'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashion"] = {
			["Cmd"] = "GMGetAllFashion",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585010944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_54632789322496'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchRecordViewBudgetInfo"] = {
			["Cmd"] = "SwitchRecordViewBudgetInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585011200'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853446656'),
			["Args"] = {"IsOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetBigWorldMapShowAll"] = {
			["Cmd"] = "SetBigWorldMapShowAll",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567552'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585011456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853446912'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenMaterialManagerLog"] = {
			["Cmd"] = "OpenMaterialManagerLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585011712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseLuaDebugTraceback"] = {
			["Cmd"] = "CloseLuaDebugTraceback",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585011968'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableNiagaraPriorityCulling"] = {
			["Cmd"] = "EnableNiagaraPriorityCulling",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585012224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeTarotTeamMemberProfession"] = {
			["Cmd"] = "ChangeTarotTeamMemberProfession",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570368'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585012480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853447936'),
			["Args"] = {"position", "classID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchAutoSkill"] = {
			["Cmd"] = "SwitchAutoSkill",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585013248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853448704'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetNiagaraNumLimit"] = {
			["Cmd"] = "SetNiagaraNumLimit",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585013504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853448960'),
			["Args"] = {"InNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DisableNiagaraCreate"] = {
			["Cmd"] = "DisableNiagaraCreate",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585013760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853449216'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartArrodesDialogue"] = {
			["Cmd"] = "StartArrodesDialogue",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585014016'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearPVPMatchPunishTime"] = {
			["Cmd"] = "ClearPVPMatchPunishTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585014272'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853449728'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OptQAMemEnv"] = {
			["Cmd"] = "OptQAMemEnv",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585014528'),
			["Description"] = "",
			["Args"] = {"OptQAMemEnv"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OptQAMemEnvView"] = {
			["Cmd"] = "OptQAMemEnvView",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585014784'),
			["Description"] = "",
			["Args"] = {"OptQAMemEnvView"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["settimeshort"] = {
			["Cmd"] = "settimeshort",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585015040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853424640'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnableNiagaraEffectTypeCheck"] = {
			["Cmd"] = "EnableNiagaraEffectTypeCheck",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585015296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMShowCutPriceMood"] = {
			["Cmd"] = "GMShowCutPriceMood",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585015552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMOpenPCApplicationScale"] = {
			["Cmd"] = "GMOpenPCApplicationScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585015808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853451264'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMClosePCApplicationScale"] = {
			["Cmd"] = "GMClosePCApplicationScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585016064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853451520'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetNextDiceValue"] = {
			["Cmd"] = "GMSetNextDiceValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328571648'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585016320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853451776'),
			["Args"] = {"diceValue"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMUseNewDiceMethod"] = {
			["Cmd"] = "GMUseNewDiceMethod",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328571648'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585016576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853452032'),
			["Args"] = {"useNewMethod"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMFashionOpenScreenShot"] = {
			["Cmd"] = "GMFashionOpenScreenShot",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585016832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853452032'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenUseNpcStickGroundConfig"] = {
			["Cmd"] = "OpenUseNpcStickGroundConfig",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585017088'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetLoginQueueCfgVal"] = {
			["Cmd"] = "SetLoginQueueCfgVal",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328572160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585017344'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853452800'),
			["Args"] = {"key", "val"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetLoginQueueOffset"] = {
			["Cmd"] = "SetLoginQueueOffset",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328572160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585017600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853453056'),
			["Args"] = {"offset"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetLoginAfkSpeed"] = {
			["Cmd"] = "SetLoginAfkSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328572160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585017856'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853453312'),
			["Args"] = {"afkNumPerMin"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMClientNewDiceBlueprint"] = {
			["Cmd"] = "GMClientNewDiceBlueprint",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328571648'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585018112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853452032'),
			["Args"] = {"bUseNewDiceBlueprint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableGPUInstanceHeadInfo"] = {
			["Cmd"] = "EnableGPUInstanceHeadInfo",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585018368'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCameraDitherDefaultOLM"] = {
			["Cmd"] = "EnableCameraDitherDefaultOLM",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585018624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853454080'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetCameraDitherDistanceScale"] = {
			["Cmd"] = "SetCameraDitherDistanceScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585018880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853454336'),
			["Args"] = {"DistanceScale"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientStateConflictShow"] = {
			["Cmd"] = "ClientStateConflictShow",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585019136'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853452032'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MoveMapDataGroup"] = {
			["Cmd"] = "MoveMapDataGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585019392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853454848'),
			["Args"] = {"SourceID", "TargetID", "GroupName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenLevelSequenceRealBinding"] = {
			["Cmd"] = "OpenLevelSequenceRealBinding",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569344'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585019648'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetCurBattery"] = {
			["Cmd"] = "GMSetCurBattery",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585019904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853455360'),
			["Args"] = {"percent", "bInCharging"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetCurSignal"] = {
			["Cmd"] = "GMSetCurSignal",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585020160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853455616'),
			["Args"] = {"state", "ms"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeleportMap"] = {
			["Cmd"] = "TeleportMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585020416'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853455872'),
			["Args"] = {"X", "Y", "Z"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["HideAllMapTags"] = {
			["Cmd"] = "HideAllMapTags",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585020672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853456128'),
			["Args"] = {"bIsHide"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowCommonInteractorInfo"] = {
			["Cmd"] = "ShowCommonInteractorInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328569856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585020928'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HitTestGridDebugging"] = {
			["Cmd"] = "HitTestGridDebugging",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585021184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853456640'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenEffectManagerLog"] = {
			["Cmd"] = "OpenEffectManagerLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585021440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853443072'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDungeonLeftTimes"] = {
			["Cmd"] = "ShowDungeonLeftTimes",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585021696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853457152'),
			["Args"] = {"stageID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseNewReminderUI"] = {
			["Cmd"] = "UseNewReminderUI",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585021952'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853457408'),
			["Args"] = {"bUse"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AddReminder"] = {
			["Cmd"] = "AddReminder",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585022208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853457664'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SimpleCreateRoleProcess"] = {
			["Cmd"] = "SimpleCreateRoleProcess",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328570112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585022464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853457920'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartAudioRecapRecord"] = {
			["Cmd"] = "StartAudioRecapRecord",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585022720'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopAudioRecapRecord"] = {
			["Cmd"] = "StopAudioRecapRecord",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585022976'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableNewDialogue"] = {
			["Cmd"] = "EnableNewDialogue",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585023232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853458688'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AllowPVPInGameStats"] = {
			["Cmd"] = "AllowPVPInGameStats",
			["Category"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585023488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853458944'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenPendulumDivination"] = {
			["Cmd"] = "OpenPendulumDivination",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585023744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853459200'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenTarotDivination"] = {
			["Cmd"] = "OpenTarotDivination",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_25908585024256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_25908853459712'),
			["Args"] = {"id"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
	},
}

return TopData
