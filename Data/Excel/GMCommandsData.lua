--
-- 表名: $GMCommands.xlsx  页名：$GMCommands
--

local TopData = {
	data = {
		["QuickTeamUpServer"] = {
			["Cmd"] = "QuickTeamUpServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340722176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609157632'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintTeamInfo"] = {
			["Cmd"] = "PrintTeamInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340722432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609157888'),
			["Args"] = {"uid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamCollect"] = {
			["Cmd"] = "TeamCollect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340722688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609158144'),
			["Args"] = {"targetID", "operate"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamPlayerCard"] = {
			["Cmd"] = "TeamPlayerCard",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340722944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609158400'),
			["Args"] = {"EntityID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenQuickTeamUp"] = {
			["Cmd"] = "OpenQuickTeamUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_56419495787520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609158656'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeamPrintMatchPool"] = {
			["Cmd"] = "TeamPrintMatchPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340723456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340723456'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamAddOneMember"] = {
			["Cmd"] = "TeamAddOneMember",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340723712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609159168'),
			["Args"] = {"professionID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TeamAddAllMembers"] = {
			["Cmd"] = "TeamAddAllMembers",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340723968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609159424'),
			["Args"] = {"num"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CloseTeamFollow"] = {
			["Cmd"] = "CloseTeamFollow",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340724224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609159680'),
			["Args"] = {"bClose"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CreateRobotTeam"] = {
			["Cmd"] = "CreateRobotTeam",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377856'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340724480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609159936'),
			["Args"] = {"count", "targetID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EquipRefreshWeekly"] = {
			["Cmd"] = "EquipRefreshWeekly",
			["Category"] = Game.TableDataManager:GetLangStr('str_31886910949632'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340724736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340724736'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSwitchEquipPlan"] = {
			["Cmd"] = "GMSwitchEquipPlan",
			["Category"] = Game.TableDataManager:GetLangStr('str_31886910949632'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340724992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340724992'),
			["Args"] = {"planId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMPutOnAllEquipIfCan"] = {
			["Cmd"] = "GMPutOnAllEquipIfCan",
			["Category"] = Game.TableDataManager:GetLangStr('str_31886910949632'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340725248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609160704'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SummonNpc"] = {
			["Cmd"] = "SummonNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340725504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609160960'),
			["Args"] = {"SkillLvl", "NpcTid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintBuffLevel"] = {
			["Cmd"] = "PrintBuffLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340725760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609161216'),
			["Args"] = {"id"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillAllEnemy"] = {
			["Cmd"] = "KillAllEnemy",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340726016'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609161472'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["PauseAllBotAI"] = {
			["Cmd"] = "PauseAllBotAI",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340726528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609161984'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ResumeAllBotAI"] = {
			["Cmd"] = "ResumeAllBotAI",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340726784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609162240'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetMaxBattleNiagara"] = {
			["Cmd"] = "SetMaxBattleNiagara",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340727040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340727040'),
			["Args"] = {"MaxNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartMorph"] = {
			["Cmd"] = "StartMorph",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340727296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609162752'),
			["Args"] = {"MorphID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CreateAvatarMirror"] = {
			["Cmd"] = "CreateAvatarMirror",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340727552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609163008'),
			["Args"] = {"camp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ReloadAbilityTemplate"] = {
			["Cmd"] = "ReloadAbilityTemplate",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340727808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609163264'),
			["Args"] = {"ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["SwitchAllowMultiJump"] = {
			["Cmd"] = "SwitchAllowMultiJump",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340728064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609163520'),
			["Args"] = {"InAllow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDamageLog"] = {
			["Cmd"] = "ShowDamageLog",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340728320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609163776'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenSequencePage"] = {
			["Cmd"] = "gmOpenSequencePage",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206474496'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340728832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenRefinePage"] = {
			["Cmd"] = "gmOpenRefinePage",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206474496'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340729600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609165056'),
			["Args"] = {"recipeID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmGetRefineMaterial"] = {
			["Cmd"] = "gmGetRefineMaterial",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206474496'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340729856'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609165312'),
			["Args"] = {"recipeID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectPreloadAsset"] = {
			["Cmd"] = "SystemEffectPreloadAsset",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340730112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609165568'),
			["Args"] = {"InPath"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectInitialEffectActor"] = {
			["Cmd"] = "SystemEffectInitialEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340730368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609165824'),
			["Args"] = {"InPath", "sx", "sy", "sz", "sw", "px", "py", "pz"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectHideSingleActorEffect"] = {
			["Cmd"] = "SystemEffectHideSingleActorEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340730624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609166080'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectShowSingleActorEffect"] = {
			["Cmd"] = "SystemEffectShowSingleActorEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340730880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609166336'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectDestroySingleEffectActor"] = {
			["Cmd"] = "SystemEffectDestroySingleEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340731136'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609166592'),
			["Args"] = {"gUID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SystemEffectDestroyAllEffectActor"] = {
			["Cmd"] = "SystemEffectDestroyAllEffectActor",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340731392'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowItemID"] = {
			["Cmd"] = "ShowItemID",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084377600'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340731648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609167104'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeTraceTask"] = {
			["Cmd"] = "ChangeTraceTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340731904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609167360'),
			["Args"] = {"TaskRingID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CompleteTaskTalk"] = {
			["Cmd"] = "CompleteTaskTalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340732160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609167616'),
			["Args"] = {"NPCID", "TalkID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayDialogue"] = {
			["Cmd"] = "PlayDialogue",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340732416'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609167872'),
			["Args"] = {"dialogueID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopDialogue"] = {
			["Cmd"] = "StopDialogue",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340732672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340732672'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartMistery"] = {
			["Cmd"] = "StartMistery",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382976'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340732928'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609168384'),
			["Args"] = {"MisteryID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayCutscene"] = {
			["Cmd"] = "PlayCutscene",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340733184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609168640'),
			["Args"] = {"cutsceneID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayLevelSequence"] = {
			["Cmd"] = "PlayLevelSequence",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340733440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609168896'),
			["Args"] = {"LevelSequenceID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayAside"] = {
			["Cmd"] = "PlayAside",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383488'),
			["Name"] = Game.TableDataManager:GetLangStr('str_57932666381056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609169920'),
			["Args"] = {"asideID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchLuaVersion"] = {
			["Cmd"] = "SwitchLuaVersion",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340734720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609170176'),
			["Args"] = {"bUseLua"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TaskToggleGMHeadInfo"] = {
			["Cmd"] = "TaskToggleGMHeadInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340734976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609170432'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenFog"] = {
			["Cmd"] = "OpenFog",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382976'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340735232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609170688'),
			["Args"] = {"R", "G", "B", "distance", "SmoothDist", "density", "BlendTime", "bOverlayClimate"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseFog"] = {
			["Cmd"] = "CloseFog",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382976'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340735488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609170944'),
			["Args"] = {"BlendTime"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GoToTaskTracePos"] = {
			["Cmd"] = "GoToTaskTracePos",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340735744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609171200'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadData"] = {
			["Cmd"] = "ReloadData",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340736000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609171456'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadQuestData"] = {
			["Cmd"] = "ReloadQuestData",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340736256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609171712'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GetLocalPatchs"] = {
			["Cmd"] = "GetLocalPatchs",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340736512'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340736512'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["RepairClient"] = {
			["Cmd"] = "RepairClient",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340736768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340736768'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseHotPatch"] = {
			["Cmd"] = "CloseHotPatch",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340737024'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340737024'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DownloadInGame"] = {
			["Cmd"] = "DownloadInGame",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340737280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340737280'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReloadMapData"] = {
			["Cmd"] = "ReloadMapData",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376832'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340737536'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609172992'),
			["Args"] = {"reloadFlowchart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["JumpToUI"] = {
			["Cmd"] = "JumpToUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_26458072302336'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340737792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609173248'),
			["Args"] = {"target"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientLogin"] = {
			["Cmd"] = "ClientLogin",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340738048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609173504'),
			["Args"] = {"ServerAdd", "UserName", "Password"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DeleteRole"] = {
			["Cmd"] = "DeleteRole",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_56419495739904'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609173760'),
			["Args"] = {"RoleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintRoleInfo"] = {
			["Cmd"] = "PrintRoleInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340738560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609174016'),
			["Args"] = {"type"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ConfigureLocalSkillSlot"] = {
			["Cmd"] = "ConfigureLocalSkillSlot",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340738816'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609174272'),
			["Args"] = {"SkillID", "SkillSlot"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["TeleportField"] = {
			["Cmd"] = "TeleportField",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340739072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609174528'),
			["Args"] = {"InsID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeStagger"] = {
			["Cmd"] = "ChangeStagger",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340739328'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609174784'),
			["Args"] = {"entityId", "newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CancelPauseLoading"] = {
			["Cmd"] = "CancelPauseLoading",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340739584'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340739584'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintAllLine"] = {
			["Cmd"] = "PrintAllLine",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340739840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340739840'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GoToPlayerLogic"] = {
			["Cmd"] = "GoToPlayerLogic",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340740096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609175552'),
			["Args"] = {"TargetUserName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SpawnTracingTaskNpc"] = {
			["Cmd"] = "SpawnTracingTaskNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340740352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340740352'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CompleteTracingTaskTalk"] = {
			["Cmd"] = "CompleteTracingTaskTalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340740608'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340740608'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintTaskList"] = {
			["Cmd"] = "PrintTaskList",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340740864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609176320'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["BatchAddItem"] = {
			["Cmd"] = "BatchAddItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340741120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609176576'),
			["Args"] = {"items", "counts", "isBind"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["BatchDelItem"] = {
			["Cmd"] = "BatchDelItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340741376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609176832'),
			["Args"] = {"items", "counts", "isBind"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetBagNewState"] = {
			["Cmd"] = "SetBagNewState",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340741632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609177088'),
			["Args"] = {"bagID", "uids", "isnews"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ItemUse"] = {
			["Cmd"] = "ItemUse",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340741888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609177344'),
			["Args"] = {"bagID", "uid", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ReqGovernWar"] = {
			["Cmd"] = "ReqGovernWar",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340742144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609177600'),
			["Args"] = {"modeID", "rankID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ConfirmEnter"] = {
			["Cmd"] = "ConfirmEnter",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340742400'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609177856'),
			["Args"] = {"bAgree"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReqOtherRoleAttrBrief"] = {
			["Cmd"] = "ReqOtherRoleAttrBrief",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340742656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609178112'),
			["Args"] = {"AvatarActorID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReqOtherRoleAttrDetail"] = {
			["Cmd"] = "ReqOtherRoleAttrDetail",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340742912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609178368'),
			["Args"] = {"AvatarActorID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChatMessage"] = {
			["Cmd"] = "ChatMessage",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340743168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340743168'),
			["Args"] = {"channelType", "messageType", "messageText"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SendPrivateChat"] = {
			["Cmd"] = "SendPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340743680'),
			["Description"] = "TargetID Content",
			["Args"] = {"TargetID", "Content"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FetchPrivateChatCache"] = {
			["Cmd"] = "FetchPrivateChatCache",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340743936'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340743936'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FetchPriavteChatHistory"] = {
			["Cmd"] = "FetchPriavteChatHistory",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340744192'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TopPrivateChat"] = {
			["Cmd"] = "TopPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340744448'),
			["Description"] = "TargetID bTop",
			["Args"] = {"TargetID", "bTop"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["BlockPrivateChat"] = {
			["Cmd"] = "BlockPrivateChat",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340744704'),
			["Description"] = "TargetID bBlock",
			["Args"] = {"TargetID", "bBlock"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OnMsgFetchUnread"] = {
			["Cmd"] = "OnMsgFetchUnread",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340744960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340744960'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DelPrivateChatHistory"] = {
			["Cmd"] = "DelPrivateChatHistory",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340745216'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DelPrivateChatCache"] = {
			["Cmd"] = "DelPrivateChatCache",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340745472'),
			["Description"] = "TargetID",
			["Args"] = {"TargetID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenMachineReview"] = {
			["Cmd"] = "OpenMachineReview",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340745728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609181184'),
			["Args"] = {"IsOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["QueryOtherRoleOnlineBrief"] = {
			["Cmd"] = "QueryOtherRoleOnlineBrief",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340745984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609181440'),
			["Args"] = {"memberID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StartWwiseProfiler"] = {
			["Cmd"] = "StartWwiseProfiler",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340746240'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopWwiseProfiler"] = {
			["Cmd"] = "StopWwiseProfiler",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340746496'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestUseItem"] = {
			["Cmd"] = "TestUseItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340746752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609182208'),
			["Args"] = {"itemUid", "itemId", "itemCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionBid"] = {
			["Cmd"] = "DungeonAuctionBid",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340747008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609182464'),
			["Args"] = {"AuctionID", "GoodsID", "MoneyType", "MoneyCount"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionGiveUp"] = {
			["Cmd"] = "DungeonAuctionGiveUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340747264'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609182720'),
			["Args"] = {"AuctionID", "GoodsID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonRoll"] = {
			["Cmd"] = "DungeonRoll",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340747520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609182976'),
			["Args"] = {"RollID", "GoodsID", "RollType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenAndCloseMailUI"] = {
			["Cmd"] = "OpenAndCloseMailUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340747776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609183232'),
			["Args"] = {"Count"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["InvitePlayerUI"] = {
			["Cmd"] = "InvitePlayerUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340748032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340748032'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintModuleLockInfo"] = {
			["Cmd"] = "PrintModuleLockInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340748288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340748288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetNearActorSpeed"] = {
			["Cmd"] = "SetNearActorSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340748544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340748544'),
			["Args"] = {"speed"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintTeamModuleLockInfo"] = {
			["Cmd"] = "PrintTeamModuleLockInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340748800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340748800'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowTestUI"] = {
			["Cmd"] = "ShowTestUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340749056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609184512'),
			["Args"] = {"slot"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowGacha"] = {
			["Cmd"] = "ShowGacha",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340749312'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowFellow"] = {
			["Cmd"] = "ShowFellow",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340749568'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChatGMSystem"] = {
			["Cmd"] = "ChatGMSystem",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340749824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609185280'),
			["Args"] = {"Text", "ToUser"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowTargetDialogPopUp"] = {
			["Cmd"] = "ShowTargetDialogPopUp",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340750080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340750080'),
			["Args"] = {"id", "text1", "text2", "text3", "text4", "text5"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MediaPlayerInvestigate"] = {
			["Cmd"] = "MediaPlayerInvestigate",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340750336'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LogRedPointNumber"] = {
			["Cmd"] = "LogRedPointNumber",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340750592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340750592'),
			["Args"] = {"Name", "ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["resetMoneySaleDataByGM"] = {
			["Cmd"] = "resetMoneySaleDataByGM",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340750848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340750848'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmControlCameraZoomScale"] = {
			["Cmd"] = "gmControlCameraZoomScale",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340751104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609186560'),
			["Args"] = {"param"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseAllUI"] = {
			["Cmd"] = "CloseAllUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340751360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseAllUIAndGM"] = {
			["Cmd"] = "CloseAllUIAndGM",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340751616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609187072'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenCustomRole"] = {
			["Cmd"] = "OpenCustomRole",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340751872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609187328'),
			["Args"] = {"isOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchShowWidgetPath"] = {
			["Cmd"] = "SwitchShowWidgetPath",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378112'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340752128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609187584'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["KillSelf"] = {
			["Cmd"] = "KillSelf",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_8660264688896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609187840'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["ShowEntityID"] = {
			["Cmd"] = "ShowEntityID",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340752640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340752640'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeSelfStagger"] = {
			["Cmd"] = "ChangeSelfStagger",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340752896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609188352'),
			["Args"] = {"newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddExpandState"] = {
			["Cmd"] = "AddExpandState",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340753152'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340753152'),
			["Args"] = {"newState", "duration"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RemoveExpandState"] = {
			["Cmd"] = "RemoveExpandState",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340753408'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340753408'),
			["Args"] = {"state", "forceRemove"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowEntityInfo"] = {
			["Cmd"] = "ShowEntityInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340753664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340753664'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeSpeed"] = {
			["Cmd"] = "ChangeSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340753920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609189376'),
			["Args"] = {"SpeedState"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UnlockAllModule"] = {
			["Cmd"] = "UnlockAllModule",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340754176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340754176'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["PrintMainPlayerState"] = {
			["Cmd"] = "PrintMainPlayerState",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340754432'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintSystemMachineState"] = {
			["Cmd"] = "PrintSystemMachineState",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340754688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340754688'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowLogicServerID"] = {
			["Cmd"] = "ShowLogicServerID",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340754944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340754944'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PlayerRunFlowchart"] = {
			["Cmd"] = "PlayerRunFlowchart",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340755200'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609190656'),
			["Args"] = {"flowchartName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PreviewAvatarPreset"] = {
			["Cmd"] = "PreviewAvatarPreset",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340755456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609190912'),
			["Args"] = {"AvatarPresetName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModelEditorTest"] = {
			["Cmd"] = "ModelEditorTest",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340755712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609191168'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyKawaiiStatus"] = {
			["Cmd"] = "ModifyKawaiiStatus",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340755968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609191424'),
			["Args"] = {"bMan", "bClothOpen", "bHairOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyChaosClothStatus"] = {
			["Cmd"] = "ModifyChaosClothStatus",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340756224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609191680'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AddTitle"] = {
			["Cmd"] = "AddTitle",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340756480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609191936'),
			["Args"] = {"titleType", "titleID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RmTitle"] = {
			["Cmd"] = "RmTitle",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340756736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609192192'),
			["Args"] = {"titleType", "titleID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnterGroupArena"] = {
			["Cmd"] = "EnterGroupArena",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340756992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609192448'),
			["Args"] = {"modeID", "rankID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CalcTeamArenaBattle"] = {
			["Cmd"] = "CalcTeamArenaBattle",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340757248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609192704'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PauseTeamArenaBattle"] = {
			["Cmd"] = "PauseTeamArenaBattle",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340757504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609192960'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowArena5V5Battle"] = {
			["Cmd"] = "ShowArena5V5Battle",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340758528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609193984'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OverAllScalabilityLevel"] = {
			["Cmd"] = "OverAllScalabilityLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379392'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340758784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609194240'),
			["Args"] = {"Level"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["Dungeon"] = {
			["Cmd"] = "Dungeon",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_31817923036672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609194496'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TaskBoardUI"] = {
			["Cmd"] = "TaskBoardUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26114206473472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609194752'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SkillCustomizerUI"] = {
			["Cmd"] = "SkillCustomizerUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340759552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609195008'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CrosswordPuzzle"] = {
			["Cmd"] = "CrosswordPuzzle",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340759808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609195264'),
			["Args"] = {"PuzzleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetServerLaunchTime"] = {
			["Cmd"] = "SetServerLaunchTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340760064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340760064'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetServerLaunchTime"] = {
			["Cmd"] = "GetServerLaunchTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340760320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340760320'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["JigsawPuzzle"] = {
			["Cmd"] = "JigsawPuzzle",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340760576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609196032'),
			["Args"] = {"PuzzleID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSettings"] = {
			["Cmd"] = "ShowSettings",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340760832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340760832'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSkillCustomizer"] = {
			["Cmd"] = "ShowSkillCustomizer",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340761088'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["NeedSdkLogin"] = {
			["Cmd"] = "NeedSdkLogin",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340761344'),
			["Description"] = "1=true,other=false",
			["Args"] = {"needSdkLogin"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearAnnouncementStorage"] = {
			["Cmd"] = "ClearAnnouncementStorage",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340761600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609197056'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenClientTrackingOrReport"] = {
			["Cmd"] = "OpenClientTrackingOrReport",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340761856'),
			["Description"] = "1=true,other=false",
			["Args"] = {"needTracking"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenTrade"] = {
			["Cmd"] = "OpenTrade",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340762112'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenAchievement"] = {
			["Cmd"] = "OpenAchievement",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340762368'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UIjump"] = {
			["Cmd"] = "UIjump",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340762624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609198080'),
			["Args"] = {"jumpid"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenRolePlay"] = {
			["Cmd"] = "OpenRolePlay",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376064'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340762880'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearTriggerDebugLine"] = {
			["Cmd"] = "ClearTriggerDebugLine",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340763136'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnNpc"] = {
			["Cmd"] = "SpawnNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340763392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609198848'),
			["Args"] = {"npcTpId", "npcType", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSpawnNpcInCircle"] = {
			["Cmd"] = "GMSpawnNpcInCircle",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340763648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609199104'),
			["Args"] = {"facadeID", "count", "radius"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableAudioVolumeDebugDraw"] = {
			["Cmd"] = "EnableAudioVolumeDebugDraw",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340763904'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DisableAudioVolumeDebugDraw"] = {
			["Cmd"] = "DisableAudioVolumeDebugDraw",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340764160'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnTestAINpc"] = {
			["Cmd"] = "SpawnTestAINpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340764416'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609199872'),
			["Args"] = {"npcTemplateID", "npcType", "flowChartPath"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillMonster"] = {
			["Cmd"] = "KillMonster",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340764672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609200128'),
			["Args"] = {"targetID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["KillNpc"] = {
			["Cmd"] = "KillNpc",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340764928'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609200384'),
			["Args"] = {"entityId", "clearAll"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonSettlement"] = {
			["Cmd"] = "DungeonSettlement",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340765184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609200640'),
			["Args"] = {"IsSucceed"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetAggroInfoList"] = {
			["Cmd"] = "GetAggroInfoList",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340765440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609200896'),
			["Args"] = {"targetNpcId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenAggroInfoList"] = {
			["Cmd"] = "OpenAggroInfoList",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340765696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340765696'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DungeonAuctionStart"] = {
			["Cmd"] = "DungeonAuctionStart",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340765952'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609201408'),
			["Args"] = {"DropID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DungeonRollStart"] = {
			["Cmd"] = "DungeonRollStart",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340766208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609201664'),
			["Args"] = {"DropID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DestroyNpcs"] = {
			["Cmd"] = "DestroyNpcs",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340766464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609201920'),
			["Args"] = {"NpcType", "TemplateID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnterTowerClimb"] = {
			["Cmd"] = "EnterTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340766720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609202176'),
			["Args"] = {"TowerLevel"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LeaveTowerClimb"] = {
			["Cmd"] = "LeaveTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340766976'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ClearTowerClimbRecord"] = {
			["Cmd"] = "ClearTowerClimbRecord",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340767232'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetTowerClimbLevel"] = {
			["Cmd"] = "SetTowerClimbLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340767488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609202176'),
			["Args"] = {"TowerLevel"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StartAutoTowerClimb"] = {
			["Cmd"] = "StartAutoTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340767744'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["StopAutoTowerClimb"] = {
			["Cmd"] = "StopAutoTowerClimb",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340768000'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ModifyGameTimeSpeed"] = {
			["Cmd"] = "ModifyGameTimeSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340768256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609203712'),
			["Args"] = {"timeFlowSpeed"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ModifyGameTime"] = {
			["Cmd"] = "ModifyGameTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340768512'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609203968'),
			["Args"] = {"hour", "minute"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientControlClimate"] = {
			["Cmd"] = "ClientControlClimate",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340768768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609204224'),
			["Args"] = {"climateId"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ResetClimateToServer"] = {
			["Cmd"] = "ResetClimateToServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340769024'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FilterNpcAttachFlowchart"] = {
			["Cmd"] = "FilterNpcAttachFlowchart",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340769280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609204736'),
			["Args"] = {"templateID", "flowchartName", "range"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["TriggerDrop"] = {
			["Cmd"] = "TriggerDrop",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340769536'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609204992'),
			["Args"] = {"dropID", "count"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMNewBagAddItem"] = {
			["Cmd"] = "GMNewBagAddItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340769792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609205248'),
			["Args"] = {"itemId", "itemCount", "bindType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMNewBagConsumeItem"] = {
			["Cmd"] = "GMNewBagConsumeItem",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340770048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609205504'),
			["Args"] = {"itemId", "itemCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMBatchAddItems"] = {
			["Cmd"] = "GMBatchAddItems",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340770304'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609205760'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMCombineFellow"] = {
			["Cmd"] = "GMCombineFellow",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340770560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609206016'),
			["Args"] = {"ConfigID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowJoinCombat"] = {
			["Cmd"] = "GMFellowJoinCombat",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340770816'),
			["Description"] = "(ConfigID,bCancle,pos)",
			["Args"] = {"ConfigID", "bCancel", "Pos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowAssistCombat"] = {
			["Cmd"] = "GMFellowAssistCombat",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340771072'),
			["Description"] = "(ConfigID,bCancle,pos)",
			["Args"] = {"ConfigID", "bCancel", "Pos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMFellowLevelUP"] = {
			["Cmd"] = "GMFellowLevelUP",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340771328'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609206784'),
			["Args"] = {"ConfigID", "UseItems"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMStarUpLevel"] = {
			["Cmd"] = "GMStarUpLevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340771584'),
			["Description"] = "(ConfigID, StarUpType)",
			["Args"] = {"ConfigID", "StarUpType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMNewBagGetInventoryPool"] = {
			["Cmd"] = "GMNewBagGetInventoryPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340771840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609207296'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GachaOverleapAnim"] = {
			["Cmd"] = "GachaOverleapAnim",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340772096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609207552'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMClearBag"] = {
			["Cmd"] = "GMClearBag",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340772352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609207808'),
			["Args"] = {"invId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenLetter"] = {
			["Cmd"] = "OpenLetter",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340772608'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609208064'),
			["Args"] = {"LetterID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmitemsubmitnew"] = {
			["Cmd"] = "gmitemsubmitnew",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340772864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609208320'),
			["Args"] = {"itemSubmitID", "itemID1", "count1", "itemID2", "count2", "itemID3", "count3"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["GMClearDrop"] = {
			["Cmd"] = "GMClearDrop",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340773120'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSetFreezeTime"] = {
			["Cmd"] = "GMSetFreezeTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084376320'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340773376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609208832'),
			["Args"] = {"invId", "slotIndex", "timestamp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddCircleBlock"] = {
			["Cmd"] = "AddCircleBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340773632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609209088'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "radius", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddAnnularBlock"] = {
			["Cmd"] = "AddAnnularBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340773888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609209344'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "innerRadius", "outerRadius", "blockMask", "unidirect", "inner2Outter"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddRectBlock"] = {
			["Cmd"] = "AddRectBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340774144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609209600'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "halfW", "halfL", "facing", "blockMask", "unidirect"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddFanBlock"] = {
			["Cmd"] = "AddFanBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340774144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609209856'),
			["Args"] = {"blockId", "cX", "cY", "cZ", "innerRadius", "outerRadius", "facing", "halfAngle", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RemoveBlock"] = {
			["Cmd"] = "RemoveBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340774656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340774656'),
			["Args"] = {"blockId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["IsBlock"] = {
			["Cmd"] = "IsBlock",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340774912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340774912'),
			["Args"] = {"x", "y", "z", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RaycastCheckReach"] = {
			["Cmd"] = "RaycastCheckReach",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378880'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340775168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340775168'),
			["Args"] = {"sX", "sY", "sZ", "eX", "eY", "eZ", "stickGround", "blockMask"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayincspirit"] = {
			["Cmd"] = "gmroleplayincspirit",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340775424'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609210880'),
			["Args"] = {"val", "canOverMax"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayaddexp"] = {
			["Cmd"] = "gmroleplayaddexp",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340775680'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211136'),
			["Args"] = {"role", "exp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetexp"] = {
			["Cmd"] = "gmroleplaysetexp",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340775936'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211392'),
			["Args"] = {"role", "exp"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetrolelevel"] = {
			["Cmd"] = "gmroleplaysetrolelevel",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340776192'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211648'),
			["Args"] = {"role", "level"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayresetskilltree"] = {
			["Cmd"] = "gmroleplayresetskilltree",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340776448'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211904'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetpoint"] = {
			["Cmd"] = "gmroleplaysetpoint",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340776704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609212160'),
			["Args"] = {"role", "point"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayresetrole"] = {
			["Cmd"] = "gmroleplayresetrole",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340776960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211904'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetcurrentrole"] = {
			["Cmd"] = "gmroleplaysetcurrentrole",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340777216'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609211904'),
			["Args"] = {"role"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplaysetbasicproperty"] = {
			["Cmd"] = "gmroleplaysetbasicproperty",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340777472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609212928'),
			["Args"] = {"property", "value"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmroleplayunlockskill"] = {
			["Cmd"] = "gmroleplayunlockskill",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340777728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609213184'),
			["Args"] = {"role", "skillID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmOpenRolePlay"] = {
			["Cmd"] = "gmOpenRolePlay",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340777984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {"param"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenRolePlayIdentify"] = {
			["Cmd"] = "gmOpenRolePlayIdentify",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084385536'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340778240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609213696'),
			["Args"] = {"DiceID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmSpawnFortuneStation"] = {
			["Cmd"] = "gmSpawnFortuneStation",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340778496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340778496'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmDestroyFortuneStation"] = {
			["Cmd"] = "gmDestroyFortuneStation",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340778752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340778752'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["gmPlayDanceChallenge"] = {
			["Cmd"] = "gmPlayDanceChallenge",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340779008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609214464'),
			["Args"] = {"challengeType", "uniqueId"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmroleplaymorph"] = {
			["Cmd"] = "gmroleplaymorph",
			["Category"] = Game.TableDataManager:GetLangStr('str_62605053983232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340779264'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609214720'),
			["Args"] = {"identityID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishScheduleDailyTask"] = {
			["Cmd"] = "finishScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340779520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609214976'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["printScheduleDailyTasks"] = {
			["Cmd"] = "printScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340779776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["dailyRefreshScheduleDailyTasks"] = {
			["Cmd"] = "dailyRefreshScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340780032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["manualRefreshScheduleDailyTasks"] = {
			["Cmd"] = "manualRefreshScheduleDailyTasks",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340780288'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["printSingleScheduleDailyTask"] = {
			["Cmd"] = "printSingleScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340780544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609214976'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishScheduleFateRevelationTask"] = {
			["Cmd"] = "finishScheduleFateRevelationTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340780800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609214976'),
			["Args"] = {"taskId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["finishAllScheduleDailyTask"] = {
			["Cmd"] = "finishAllScheduleDailyTask",
			["Category"] = Game.TableDataManager:GetLangStr('str_52846351356160'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340781056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UploadSaved"] = {
			["Cmd"] = "UploadSaved",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340781312'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609216768'),
			["Args"] = {"ExtraInfo", "Interval"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UploadC7Log"] = {
			["Cmd"] = "UploadC7Log",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340781568'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609217024'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowBSStat"] = {
			["Cmd"] = "ShowBSStat",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340781824'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609217280'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDmgRecordLog"] = {
			["Cmd"] = "ShowDmgRecordLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340782080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609217536'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaPandaRemote"] = {
			["Cmd"] = "LuaPandaRemote",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340782336'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340782336'),
			["Args"] = {"IP", "Port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCustomOP"] = {
			["Cmd"] = "EnableCustomOP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340782592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609218048'),
			["Args"] = {"Type"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowLag"] = {
			["Cmd"] = "ShowLag",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340782848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609218304'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DebugFreeCamera"] = {
			["Cmd"] = "DebugFreeCamera",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340783104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609218560'),
			["Args"] = {"ResetPos"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnablePrintFloorPhysicalMaterial"] = {
			["Cmd"] = "EnablePrintFloorPhysicalMaterial",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340783360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609218816'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GenerateAllNpcLocationData"] = {
			["Cmd"] = "GenerateAllNpcLocationData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340783616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609219072'),
			["Args"] = {"mapID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GenerateAllChairsLocationData"] = {
			["Cmd"] = "GenerateAllChairsLocationData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340783872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609219328'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintEntitiesPos"] = {
			["Cmd"] = "PrintEntitiesPos",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340784128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609219584'),
			["Args"] = {"actorType", "limitCount"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LoadURL"] = {
			["Cmd"] = "LoadURL",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340784384'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609219840'),
			["Args"] = {"Url"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestCrash"] = {
			["Cmd"] = "TestCrash",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340784640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340784640'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DrawServerPosition"] = {
			["Cmd"] = "DrawServerPosition",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340784896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609220352'),
			["Args"] = {"acotrType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ToggleUIVisible"] = {
			["Cmd"] = "ToggleUIVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340785152'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609220608'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GC"] = {
			["Cmd"] = "GC",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340785408'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340785408'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaProfileStart"] = {
			["Cmd"] = "LuaProfileStart",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340785664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609221120'),
			["Args"] = {"mode"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaProfileStop"] = {
			["Cmd"] = "LuaProfileStop",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340785920'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340785920'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaMemReport"] = {
			["Cmd"] = "LuaMemReport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340786176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609221632'),
			["Args"] = {"outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaMemDiff"] = {
			["Cmd"] = "LuaMemDiff",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340786432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609221888'),
			["Args"] = {"file1", "file2", "outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaEntityMemReport"] = {
			["Cmd"] = "LuaEntityMemReport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340786688'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609222144'),
			["Args"] = {"entityId", "outFile"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaForceGC"] = {
			["Cmd"] = "LuaForceGC",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340786944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609222400'),
			["Args"] = {"checkMemChange"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["LuaGetTotalMem"] = {
			["Cmd"] = "LuaGetTotalMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340787200'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609222656'),
			["Args"] = {"isLoop", "interval"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["Disconnect"] = {
			["Cmd"] = "Disconnect",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340787456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340787456'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintActorLocation"] = {
			["Cmd"] = "PrintActorLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340787712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340787712'),
			["Args"] = {"Interval", "ID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintPostProcessVolume"] = {
			["Cmd"] = "PrintPostProcessVolume",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340787968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340787968'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleGMHeadInfo"] = {
			["Cmd"] = "ToggleGMHeadInfo",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458609170432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609170432'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetCameraParams"] = {
			["Cmd"] = "SetCameraParams",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340788480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609223936'),
			["Args"] = {"Name", "Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetLogLevel"] = {
			["Cmd"] = "SetLogLevel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340788736'),
			["Description"] = "1:Debug  4:Release",
			["Args"] = {"Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseInterpolationMovement"] = {
			["Cmd"] = "UseInterpolationMovement",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340788992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340788992'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ExecuteNavigation"] = {
			["Cmd"] = "ExecuteNavigation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340789248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609224704'),
			["Args"] = {"X", "Y", "SizeZ", "ShowTarget"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StatAtlasData"] = {
			["Cmd"] = "StatAtlasData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340789504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609224960'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartSLuaProfile"] = {
			["Cmd"] = "StartSLuaProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340789760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340789760'),
			["Args"] = {"IP", "Port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopSLuaProfile"] = {
			["Cmd"] = "StopSLuaProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340790016'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340790016'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UploadPSO"] = {
			["Cmd"] = "UploadPSO",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340790272'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340790272'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SLuaBuildInProfile"] = {
			["Cmd"] = "SLuaBuildInProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340790528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609225984'),
			["Args"] = {"open", "memoryStackNum", "ip", "port"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleHeadInfoVisible"] = {
			["Cmd"] = "ToggleHeadInfoVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340790784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340790784'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetDamageEffectTxtVisible"] = {
			["Cmd"] = "SetDamageEffectTxtVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340791040'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609226496'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetNewDamageEffectVisible"] = {
			["Cmd"] = "SetNewDamageEffectVisible",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340791296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609226496'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLua"] = {
			["Cmd"] = "StartProfileLua",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340791552'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLua"] = {
			["Cmd"] = "StopProfileLua",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340791808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609227264'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLuaMem"] = {
			["Cmd"] = "StartProfileLuaMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340792064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609227520'),
			["Args"] = {"memoryStackNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLuaMem"] = {
			["Cmd"] = "StopProfileLuaMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340792320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609227776'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartProfileLuaNewObjectMem"] = {
			["Cmd"] = "StartProfileLuaNewObjectMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340792576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609228032'),
			["Args"] = {"memoryStackNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopProfileLuaNewObjectMem"] = {
			["Cmd"] = "StopProfileLuaNewObjectMem",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340792832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609228288'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DumpObjectGraph"] = {
			["Cmd"] = "DumpObjectGraph",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340793088'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609228544'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchDebug"] = {
			["Cmd"] = "SwitchDebug",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340793344'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609228800'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleMapTouchTeleport"] = {
			["Cmd"] = "ToggleMapTouchTeleport",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340793600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609229056'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeResourceManagerDump"] = {
			["Cmd"] = "ChangeResourceManagerDump",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340793856'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340793856'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintResourceManagerDump"] = {
			["Cmd"] = "PrintResourceManagerDump",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340794112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340794112'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DebugQuickStart"] = {
			["Cmd"] = "DebugQuickStart",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340794368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609229824'),
			["Args"] = {"profession", "sex"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetTrackLineDistance"] = {
			["Cmd"] = "SetTrackLineDistance",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340794624'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340794624'),
			["Args"] = {"distance"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestMalloc"] = {
			["Cmd"] = "TestMalloc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340794880'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609230336'),
			["Args"] = {"size"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TestSpawnActor"] = {
			["Cmd"] = "TestSpawnActor",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340795136'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609230592'),
			["Args"] = {"number"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaInsightProfiler"] = {
			["Cmd"] = "LuaInsightProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340795392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609230848'),
			["Args"] = {"isStart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LuaInsightProfilerTraceFile"] = {
			["Cmd"] = "LuaInsightProfilerTraceFile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340795648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609231104'),
			["Args"] = {"isStart"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AssistVar"] = {
			["Cmd"] = "AssistVar",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340795904'),
			["Description"] = "var,value",
			["Args"] = {"var", "value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SpawnTestNpc"] = {
			["Cmd"] = "SpawnTestNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340796160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340796160'),
			["Args"] = {"RowNum", "NpcType", "NpcDistance", "SpawnedMaxNum", "SpawnOneRowInterval"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleMiniMapDebug"] = {
			["Cmd"] = "ToggleMiniMapDebug",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340796416'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearAllTestNpc"] = {
			["Cmd"] = "ClearAllTestNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340796672'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340796672'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SceneInspection"] = {
			["Cmd"] = "SceneInspection",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340796928'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340796928'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseKCP"] = {
			["Cmd"] = "UseKCP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340797184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609232640'),
			["Args"] = {"enable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["LogDeviceName"] = {
			["Cmd"] = "LogDeviceName",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340797440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609232896'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["bEnableDumpObjList"] = {
			["Cmd"] = "bEnableDumpObjList",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340797696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609233152'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayBinkMediaByOverlay"] = {
			["Cmd"] = "PlayBinkMediaByOverlay",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340797952'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayBinkMediaByTexture"] = {
			["Cmd"] = "PlayBinkMediaByTexture",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340798208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["Testdatacentermemory"] = {
			["Cmd"] = "Testdatacentermemory",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340798464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenLuaProfiler"] = {
			["Cmd"] = "OpenLuaProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340798720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseLuaProfiler"] = {
			["Cmd"] = "CloseLuaProfiler",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340798976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609164288'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmLogUIObject"] = {
			["Cmd"] = "gmLogUIObject",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340799232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609234688'),
			["Args"] = {"UIControllerName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenOrCloseMoveSyncLog"] = {
			["Cmd"] = "OpenOrCloseMoveSyncLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340799488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609234944'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetClockDiff"] = {
			["Cmd"] = "SetClockDiff",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340799744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340799744'),
			["Args"] = {"Value"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableServerErrorLogToClient"] = {
			["Cmd"] = "EnableServerErrorLogToClient",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340800000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609235456'),
			["Args"] = {"KimID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DisableServerErrorLogToClient"] = {
			["Cmd"] = "DisableServerErrorLogToClient",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340800256'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340800256'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintFaceData"] = {
			["Cmd"] = "PrintFaceData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340800512'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609235968'),
			["Args"] = {"uid"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartUIAutomationProfile"] = {
			["Cmd"] = "StartUIAutomationProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340800768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340800768'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ToggleFaceLight"] = {
			["Cmd"] = "ToggleFaceLight",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340801024'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ApplyIndividualPVP"] = {
			["Cmd"] = "ApplyIndividualPVP",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340801280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609236736'),
			["Args"] = {"targetAvatarID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HomeClearInstance"] = {
			["Cmd"] = "HomeClearInstance",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340801536'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HomeCameraDissolveOpen"] = {
			["Cmd"] = "HomeCameraDissolveOpen",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340801792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609237248'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassHideAllEntities"] = {
			["Cmd"] = "MassHideAllEntities",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340802048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609237504'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassActivateAllEntities"] = {
			["Cmd"] = "MassActivateAllEntities",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340802304'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609237760'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MassGenerateSpecificNpc"] = {
			["Cmd"] = "MassGenerateSpecificNpc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340802560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609238016'),
			["Args"] = {"MassNpcName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["gmOpenCreateLock"] = {
			["Cmd"] = "gmOpenCreateLock",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340802816'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetTime"] = {
			["Cmd"] = "SetTime",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340803072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609238528'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetTime"] = {
			["Cmd"] = "GetTime",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340803328'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnablePrivateSceneActorAoi"] = {
			["Cmd"] = "EnablePrivateSceneActorAoi",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340803584'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609239040'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintUEActorData"] = {
			["Cmd"] = "PrintUEActorData",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340803840'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCookMap"] = {
			["Cmd"] = "EnableCookMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340804096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609239040'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableEngineShortcut"] = {
			["Cmd"] = "EnableEngineShortcut",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340804352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609239808'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowAoiState"] = {
			["Cmd"] = "ShowAoiState",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378368'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340804608'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableActorLimitClip"] = {
			["Cmd"] = "EnableActorLimitClip",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378368'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340804864'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340804864'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableActorViewMatrixClip"] = {
			["Cmd"] = "EnableActorViewMatrixClip",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084378368'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340805120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340805120'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenDragonNavigate"] = {
			["Cmd"] = "OpenDragonNavigate",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340805376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340805376'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["IOSGPUCapture"] = {
			["Cmd"] = "IOSGPUCapture",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_62674041870080'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609241088'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayAnnouncementMarquee"] = {
			["Cmd"] = "PlayAnnouncementMarquee",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340805888'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609241344'),
			["Args"] = {"newsTickerType"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TriggerFireJump"] = {
			["Cmd"] = "TriggerFireJump",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_62743566736896'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AutoSkillCast"] = {
			["Cmd"] = "AutoSkillCast",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340806400'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartPVPMatch"] = {
			["Cmd"] = "StartPVPMatch",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340806656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340806656'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["CancelPVPMatch"] = {
			["Cmd"] = "CancelPVPMatch",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340806912'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340806912'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMCreateNpcTailAndWarning"] = {
			["Cmd"] = "GMCreateNpcTailAndWarning",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340807168'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340807168'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = true,
		},
		["WitchCheck"] = {
			["Cmd"] = "WitchCheck",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084385536'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340807424'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609242880'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PvpConfirm"] = {
			["Cmd"] = "PvpConfirm",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340807680'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340807680'),
			["Args"] = {"bAgree"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PvpCancel"] = {
			["Cmd"] = "PvpCancel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340807936'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340807936'),
			["Args"] = {"bPrint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintSneakWarning"] = {
			["Cmd"] = "PrintSneakWarning",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382976'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340808192'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609243648'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CanCompleteCondition"] = {
			["Cmd"] = "CanCompleteCondition",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340808448'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340808448'),
			["Args"] = {"system", "key", "triggerID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["RegisterCustomTrigger"] = {
			["Cmd"] = "RegisterCustomTrigger",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340808704'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340808704'),
			["Args"] = {"key", "triggerID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowPlayerLocation"] = {
			["Cmd"] = "ShowPlayerLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340808960'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609244416'),
			["Args"] = {"bShow", "bPrint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayPPEffectPreset"] = {
			["Cmd"] = "PlayPPEffectPreset",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340809216'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609244672'),
			["Args"] = {"EffectID", "Duration", "AlphaIn", "AlphaOut"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlaySceneEffect"] = {
			["Cmd"] = "PlaySceneEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340809472'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609244928'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayEntityEffect"] = {
			["Cmd"] = "PlayEntityEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340809728'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609245184'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PlayScreenEffect"] = {
			["Cmd"] = "PlayScreenEffect",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084379136'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340809984'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609245440'),
			["Args"] = {"EffectID", "Duration"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowFPS"] = {
			["Cmd"] = "ShowFPS",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340810240'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609245696'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["ShowStatUnit"] = {
			["Cmd"] = "ShowStatUnit",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340810496'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609245952'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["SkipCutscene"] = {
			["Cmd"] = "SkipCutscene",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340810752'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340810752'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeleportByMeter"] = {
			["Cmd"] = "TeleportByMeter",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340811008'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609246464'),
			["Args"] = {"X, Y, Z"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GMGetFashionMountById"] = {
			["Cmd"] = "GMGetFashionMountById",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340811264'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609246720'),
			["Args"] = {"mountID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashionMount"] = {
			["Cmd"] = "GMGetAllFashionMount",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340811520'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609246976'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetFashionMountAccById"] = {
			["Cmd"] = "GMGetFashionMountAccById",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340811776'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609247232'),
			["Args"] = {"mountAccId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashionMountAcc"] = {
			["Cmd"] = "GMGetAllFashionMountAcc",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340812032'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609246976'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMUnlockTeleportPoint"] = {
			["Cmd"] = "GMUnlockTeleportPoint",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084386304'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340812288'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = true,
		},
		["GMAddBlackFoe"] = {
			["Cmd"] = "GMAddBlackFoe",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340812544'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340812544'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMModifyAttraction"] = {
			["Cmd"] = "GMModifyAttraction",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340812800'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609248256'),
			["Args"] = {"name", "num"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriend"] = {
			["Cmd"] = "GMAddFriend",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340813056'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609248512'),
			["Args"] = {"name"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMResetFriendApplyNum"] = {
			["Cmd"] = "GMResetFriendApplyNum",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340813312'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMClearAllApplicationInfo"] = {
			["Cmd"] = "GMClearAllApplicationInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340813568'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriendsInServer"] = {
			["Cmd"] = "GMAddFriendsInServer",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340813824'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMAddFriendsInDB"] = {
			["Cmd"] = "GMAddFriendsInDB",
			["Category"] = Game.TableDataManager:GetLangStr('str_36971883794176'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340814080'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SpawnWorldBoss"] = {
			["Cmd"] = "SpawnWorldBoss",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375040'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340814336'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609249792'),
			["Args"] = {"npcTpId", "midHitHpRate"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchQuestSytem"] = {
			["Cmd"] = "SwitchQuestSytem",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381696'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340814592'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609250048'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReturnQuestPlane"] = {
			["Cmd"] = "ReturnQuestPlane",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381696'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340814848'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609250304'),
			["Args"] = {"QuestID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TriggerClientSystemAction"] = {
			["Cmd"] = "TriggerClientSystemAction",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340815104'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609250560'),
			["Args"] = {"actionName", "paramStr", "entityID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UnlockSelectLimit"] = {
			["Cmd"] = "UnlockSelectLimit",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340815360'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609250816'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowSelectEntityInfo"] = {
			["Cmd"] = "ShowSelectEntityInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340815616'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609251072'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GetMaxFightEnrolledPlayerNum"] = {
			["Cmd"] = "GetMaxFightEnrolledPlayerNum",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340815872'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609251328'),
			["Args"] = {"EntityId"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UnlockCollectibles"] = {
			["Cmd"] = "UnlockCollectibles",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340816128'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609251584'),
			["Args"] = {"collectiblesID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["UpdateCollectiblesScore"] = {
			["Cmd"] = "UpdateCollectiblesScore",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340816384'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609251840'),
			["Args"] = {"collectiblesType", "score"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchRideHorse"] = {
			["Cmd"] = "SwitchRideHorse",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381952'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340816640'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340816640'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowBikeUI"] = {
			["Cmd"] = "ShowBikeUI",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381952'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340816896'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609252352'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchRiding"] = {
			["Cmd"] = "SwitchRiding",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381952'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340817152'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchWaterWalk"] = {
			["Cmd"] = "SwitchWaterWalk",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381952'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340817408'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DoUIAutomationProfile"] = {
			["Cmd"] = "DoUIAutomationProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340817664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609253120'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishedRing"] = {
			["Cmd"] = "FinishedRing",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084375296'),
			["Name"] = Game.TableDataManager:GetLangStr('str_57932666449664'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609253376'),
			["Args"] = {"RingID"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["AddPass"] = {
			["Cmd"] = "AddPass",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340818176'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340818176'),
			["Args"] = {"sid", "lv"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DelPass"] = {
			["Cmd"] = "DelPass",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340818432'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340818432'),
			["Args"] = {"sid"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["ShowEquipedPassive"] = {
			["Cmd"] = "ShowEquipedPassive",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340818688'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenNewbieGuide"] = {
			["Cmd"] = "OpenNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340818944'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340818944'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseNewbieGuide"] = {
			["Cmd"] = "CloseNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340819200'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340819200'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishNewbieGuide"] = {
			["Cmd"] = "FinishNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340819456'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609254912'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["FinishAllNewbieGuide"] = {
			["Cmd"] = "FinishAllNewbieGuide",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340819712'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458340819712'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenNewbieGuideGroup"] = {
			["Cmd"] = "OpenNewbieGuideGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340819968'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609255424'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ResetNewbieGuideGroup"] = {
			["Cmd"] = "ResetNewbieGuideGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084382208'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340820224'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609255680'),
			["Args"] = {"groupID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ReplaceNpcModelMaterial"] = {
			["Cmd"] = "ReplaceNpcModelMaterial",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340820480'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609255936'),
			["Args"] = {"npcID", "materialID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetPvpPoints"] = {
			["Cmd"] = "SetPvpPoints",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340820736'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256192'),
			["Args"] = {"type", "rankPoints"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetTeamMemberPvpPoints"] = {
			["Cmd"] = "SetTeamMemberPvpPoints",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340820992'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256192'),
			["Args"] = {"type", "rankPoints", "includeSelf"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["PrintWaterPipePuzzleMap"] = {
			["Cmd"] = "PrintWaterPipePuzzleMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340821248'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256704'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OutputKSBCReaderLog"] = {
			["Cmd"] = "OutputKSBCReaderLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340821504'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TimerPerformanceAnalysis"] = {
			["Cmd"] = "TimerPerformanceAnalysis",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340821760'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609257216'),
			["Args"] = {"bOpen", "outputCount", "bReport"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EventSystemProfile"] = {
			["Cmd"] = "EventSystemProfile",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340822016'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609257472'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["RpcPerformanceAnalysis"] = {
			["Cmd"] = "RpcPerformanceAnalysis",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340822272'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609257472'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMAvatarMoveToLocation"] = {
			["Cmd"] = "GMAvatarMoveToLocation",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340822528'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609257984'),
			["Args"] = {"X", "Y", "Z"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMSetLoadNavmeshAndVoxel"] = {
			["Cmd"] = "GMSetLoadNavmeshAndVoxel",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340822784'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OpenInteractorGMPanel"] = {
			["Cmd"] = "OpenInteractorGMPanel",
			["Category"] = Game.TableDataManager:GetLangStr('str_57932666416896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340823040'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetPvpWarmMatch"] = {
			["Cmd"] = "SetPvpWarmMatch",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340823296'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"gmWarmMatch"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetDropPickUpTrailParams"] = {
			["Cmd"] = "SetDropPickUpTrailParams",
			["Category"] = Game.TableDataManager:GetLangStr('str_57932666416896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340823552'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609259008'),
			["Args"] = {"GlowTime", "TrailTime"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetSwitchValue"] = {
			["Cmd"] = "SetSwitchValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340823808'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609259264'),
			["Args"] = {"switchName", "switchValue"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GetSwitchValue"] = {
			["Cmd"] = "GetSwitchValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340824064'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609259520'),
			["Args"] = {"switchName"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["DisableCreateRoleName"] = {
			["Cmd"] = "DisableCreateRoleName",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340824320'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609259776'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["PrintPvpMatchPool"] = {
			["Cmd"] = "PrintPvpMatchPool",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340824576'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609260032'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMGetAllFashion"] = {
			["Cmd"] = "GMGetAllFashion",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340824832'),
			["Description"] = Game.TableDataManager:GetLangStr('str_36697811192581'),
			["Args"] = {},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SwitchRecordViewBudgetInfo"] = {
			["Cmd"] = "SwitchRecordViewBudgetInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340825088'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609260544'),
			["Args"] = {"IsOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetBigWorldMapShowAll"] = {
			["Cmd"] = "SetBigWorldMapShowAll",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381440'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340825344'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609260800'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenMaterialManagerLog"] = {
			["Cmd"] = "OpenMaterialManagerLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340825600'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["CloseLuaDebugTraceback"] = {
			["Cmd"] = "CloseLuaDebugTraceback",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340825856'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableNiagaraPriorityCulling"] = {
			["Cmd"] = "EnableNiagaraPriorityCulling",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340826112'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ChangeTarotTeamMemberProfession"] = {
			["Cmd"] = "ChangeTarotTeamMemberProfession",
			["Category"] = Game.TableDataManager:GetLangStr('str_26114206487552'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340826368'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609261824'),
			["Args"] = {"position", "classID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SwitchAutoSkill"] = {
			["Cmd"] = "SwitchAutoSkill",
			["Category"] = Game.TableDataManager:GetLangStr('str_4811168681984'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340827136'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609262592'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetNiagaraNumLimit"] = {
			["Cmd"] = "SetNiagaraNumLimit",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340827392'),
			["Description"] = Game.TableDataManager:GetLangStr('str_56419495766016'),
			["Args"] = {"InNum"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["DisableNiagaraCreate"] = {
			["Cmd"] = "DisableNiagaraCreate",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340827648'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609263104'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartArrodesDialogue"] = {
			["Cmd"] = "StartArrodesDialogue",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340827904'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClearPVPMatchPunishTime"] = {
			["Cmd"] = "ClearPVPMatchPunishTime",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340828160'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609263616'),
			["Args"] = {"matchType"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["OptQAMemEnv"] = {
			["Cmd"] = "OptQAMemEnv",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340828416'),
			["Description"] = "",
			["Args"] = {"OptQAMemEnv"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OptQAMemEnvView"] = {
			["Cmd"] = "OptQAMemEnvView",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340828672'),
			["Description"] = "",
			["Args"] = {"OptQAMemEnvView"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["settimeshort"] = {
			["Cmd"] = "settimeshort",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340828928'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609238528'),
			["Args"] = {"year", "month", "day", "hour", "minute", "second"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["EnableNiagaraEffectTypeCheck"] = {
			["Cmd"] = "EnableNiagaraEffectTypeCheck",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340829184'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMShowCutPriceMood"] = {
			["Cmd"] = "GMShowCutPriceMood",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340829440'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMOpenPCApplicationScale"] = {
			["Cmd"] = "GMOpenPCApplicationScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340829696'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265152'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMClosePCApplicationScale"] = {
			["Cmd"] = "GMClosePCApplicationScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340829952'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265408'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetNextDiceValue"] = {
			["Cmd"] = "GMSetNextDiceValue",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084385536'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340830208'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265664'),
			["Args"] = {"diceValue"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMUseNewDiceMethod"] = {
			["Cmd"] = "GMUseNewDiceMethod",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084385536'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340830464'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265920'),
			["Args"] = {"useNewMethod"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMFashionOpenScreenShot"] = {
			["Cmd"] = "GMFashionOpenScreenShot",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084381184'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340830720'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265920'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenUseNpcStickGroundConfig"] = {
			["Cmd"] = "OpenUseNpcStickGroundConfig",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340830976'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetLoginQueueCfgVal"] = {
			["Cmd"] = "SetLoginQueueCfgVal",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084386048'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340831232'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609266688'),
			["Args"] = {"key", "val"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetLoginQueueOffset"] = {
			["Cmd"] = "SetLoginQueueOffset",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084386048'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340831488'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609266944'),
			["Args"] = {"offset"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["SetLoginAfkSpeed"] = {
			["Cmd"] = "SetLoginAfkSpeed",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084386048'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340831744'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609267200'),
			["Args"] = {"afkNumPerMin"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["GMClientNewDiceBlueprint"] = {
			["Cmd"] = "GMClientNewDiceBlueprint",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084385536'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340832000'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265920'),
			["Args"] = {"bUseNewDiceBlueprint"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableGPUInstanceHeadInfo"] = {
			["Cmd"] = "EnableGPUInstanceHeadInfo",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340832256'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableCameraDitherDefaultOLM"] = {
			["Cmd"] = "EnableCameraDitherDefaultOLM",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340832512'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609267968'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SetCameraDitherDistanceScale"] = {
			["Cmd"] = "SetCameraDitherDistanceScale",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340832768'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609268224'),
			["Args"] = {"DistanceScale"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ClientStateConflictShow"] = {
			["Cmd"] = "ClientStateConflictShow",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340833024'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609265920'),
			["Args"] = {"bShow"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["MoveMapDataGroup"] = {
			["Cmd"] = "MoveMapDataGroup",
			["Category"] = Game.TableDataManager:GetLangStr('str_55045106174720'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340833280'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609268736'),
			["Args"] = {"SourceID", "TargetID", "GroupName"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenLevelSequenceRealBinding"] = {
			["Cmd"] = "OpenLevelSequenceRealBinding",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084383232'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340833536'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetCurBattery"] = {
			["Cmd"] = "GMSetCurBattery",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340833792'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609269248'),
			["Args"] = {"percent", "bInCharging"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["GMSetCurSignal"] = {
			["Cmd"] = "GMSetCurSignal",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340834048'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609269504'),
			["Args"] = {"state", "ms"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["TeleportMap"] = {
			["Cmd"] = "TeleportMap",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340834304'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609269760'),
			["Args"] = {"X", "Y", "Z"},
			["bIsServerCommand"] = true,
			["IsFrequenly"] = false,
		},
		["HideAllMapTags"] = {
			["Cmd"] = "HideAllMapTags",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340834560'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609270016'),
			["Args"] = {"bIsHide"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowCommonInteractorInfo"] = {
			["Cmd"] = "ShowCommonInteractorInfo",
			["Category"] = Game.TableDataManager:GetLangStr('str_57932666416896'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340834816'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["HitTestGridDebugging"] = {
			["Cmd"] = "HitTestGridDebugging",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340835072'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609270528'),
			["Args"] = {"open"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenEffectManagerLog"] = {
			["Cmd"] = "OpenEffectManagerLog",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340835328'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609256960'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["ShowDungeonLeftTimes"] = {
			["Cmd"] = "ShowDungeonLeftTimes",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340835584'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609271040'),
			["Args"] = {"stageID"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["UseNewReminderUI"] = {
			["Cmd"] = "UseNewReminderUI",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340835840'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609271296'),
			["Args"] = {"bUse"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AddReminder"] = {
			["Cmd"] = "AddReminder",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340836096'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609271552'),
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["SimpleCreateRoleProcess"] = {
			["Cmd"] = "SimpleCreateRoleProcess",
			["Category"] = Game.TableDataManager:GetLangStr('str_26389084384000'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340836352'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609271808'),
			["Args"] = {"bOpen"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StartAudioRecapRecord"] = {
			["Cmd"] = "StartAudioRecapRecord",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340836608'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["StopAudioRecapRecord"] = {
			["Cmd"] = "StopAudioRecapRecord",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340836864'),
			["Description"] = "",
			["Args"] = {},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableNewDialogue"] = {
			["Cmd"] = "EnableNewDialogue",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340837120'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609272576'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["AllowPVPInGameStats"] = {
			["Cmd"] = "AllowPVPInGameStats",
			["Category"] = Game.TableDataManager:GetLangStr('str_61918396026624'),
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340837376'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609272832'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenPendulumDivination"] = {
			["Cmd"] = "OpenPendulumDivination",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340837632'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609273088'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["OpenTarotDivination"] = {
			["Cmd"] = "OpenTarotDivination",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340838144'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609273600'),
			["Args"] = {"id"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableAudioListenerSwitch"] = {
			["Cmd"] = "EnableAudioListenerSwitch",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340838400'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609272576'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
		["EnableSpaceDataKsbc"] = {
			["Cmd"] = "EnableSpaceDataKsbc",
			["Category"] = "Debug",
			["Name"] = Game.TableDataManager:GetLangStr('str_26458340838656'),
			["Description"] = Game.TableDataManager:GetLangStr('str_26458609272576'),
			["Args"] = {"bEnable"},
			["bIsServerCommand"] = false,
			["IsFrequenly"] = false,
		},
	},
}

return TopData
