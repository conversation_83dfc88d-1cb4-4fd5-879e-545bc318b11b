--
-- 表名: DungeonEnterData后处理
--

local TopData = {
    DungeonID2DungeonEnterID = {
        [5100040] = 16, 
        [5100042] = 18, 
        [5100043] = 19, 
        [5100044] = 16, 
        [5100045] = 22, 
        [5100047] = 23, 
        [5100099] = 20, 
        [5101001] = 13, 
    },
    data = {
        [13] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss01.UI_Dungeon_Img_DungeonBoss01;/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss02.UI_Dungeon_Img_DungeonBoss02;/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss04.UI_Dungeon_Img_DungeonBoss04', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140826624'),
            ['DisplayNumber'] = 0, 
            ['DisplaySequence'] = '', 
            ['DungeonEnterId'] = 13, 
            ['DungeonId'] = {[0] = 5101001, }, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/ConfigImage/Dungeon/UI_ConfigImg_Dungeon_Img_Entrance01.UI_ConfigImg_Dungeon_Img_Entrance01', 
            ['EnterPreviewBoss'] = '', 
            ['FacadeControlID'] = 0, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_345744869120'),
            ['NiagaraAssets'] = {}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['Type'] = 4, 
        },
        [16] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823552'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000117', 
            ['DungeonEnterId'] = 16, 
            ['DungeonId'] = {5100040, 5100044}, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/ConfigImage/Dungeon/UI_ConfigImg_Dungeon_Img_Entrance02.UI_ConfigImg_Dungeon_Img_Entrance02', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['FacadeControlID'] = 7900003, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54083570370304'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['Type'] = 2, 
        },
        [18] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000120', 
            ['DungeonEnterId'] = 18, 
            ['DungeonId'] = {[0] = 5100042, }, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/ConfigImage/Dungeon/UI_ConfigImg_Dungeon_Img_Entrance03.UI_ConfigImg_Dungeon_Img_Entrance03', 
            ['EnterPreviewBoss'] = '', 
            ['FacadeControlID'] = 7900174, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597888'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['Type'] = 3, 
        },
        [19] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000119', 
            ['DungeonEnterId'] = 19, 
            ['DungeonId'] = {[0] = 5100043, }, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['FacadeControlID'] = 7900149, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597632'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['Type'] = 3, 
        },
        [20] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000118', 
            ['DungeonEnterId'] = 20, 
            ['DungeonId'] = {[0] = 5100099, }, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/ConfigImage/Dungeon/UI_ConfigImg_Dungeon_Img_Entrance01.UI_ConfigImg_Dungeon_Img_Entrance01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Steven.UI_Dungeon_Img_Steven', 
            ['FacadeControlID'] = 7900020, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54632789776384'),
            ['NiagaraAssets'] = {}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['Type'] = 5, 
        },
        [22] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000117', 
            ['DungeonEnterId'] = 22, 
            ['DungeonId'] = {[0] = 5100045, }, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['FacadeControlID'] = 7900003, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14363444386048'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['Type'] = 3, 
        },
        [23] = {
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '1000117', 
            ['DungeonEnterId'] = 23, 
            ['DungeonId'] = {[0] = 5100047, }, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['FacadeControlID'] = 7900003, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14363444386304'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['Type'] = 3, 
        },
    }
}
return TopData