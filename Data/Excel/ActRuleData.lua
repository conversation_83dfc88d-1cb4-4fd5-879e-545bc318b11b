--
-- 表名: $Sequence_序列系统.xlsx  页名：$ActRule_扮演法则
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266591232'),
		},
		[2] = {
			["ID"] = 2,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266591488'),
		},
		[3] = {
			["ID"] = 3,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266591744'),
		},
		[4] = {
			["ID"] = 4,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266592000'),
		},
		[5] = {
			["ID"] = 5,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266592256'),
		},
		[6] = {
			["ID"] = 6,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266592512'),
		},
		[7] = {
			["ID"] = 7,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266592768'),
		},
		[8] = {
			["ID"] = 8,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266593024'),
		},
		[9] = {
			["ID"] = 9,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266593280'),
		},
		[10] = {
			["ID"] = 10,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266593536'),
		},
		[11] = {
			["ID"] = 11,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266593792'),
		},
		[12] = {
			["ID"] = 12,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266594048'),
		},
		[13] = {
			["ID"] = 13,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266594304'),
		},
		[14] = {
			["ID"] = 14,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266594560'),
		},
		[15] = {
			["ID"] = 15,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266594816'),
		},
		[16] = {
			["ID"] = 16,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266595072'),
		},
		[17] = {
			["ID"] = 17,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266595328'),
		},
		[18] = {
			["ID"] = 18,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266595584'),
		},
		[19] = {
			["ID"] = 19,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266595840'),
		},
		[20] = {
			["ID"] = 20,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266596096'),
		},
		[21] = {
			["ID"] = 21,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266596352'),
		},
		[22] = {
			["ID"] = 22,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266596608'),
		},
		[23] = {
			["ID"] = 23,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266596864'),
		},
		[24] = {
			["ID"] = 24,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266597120'),
		},
		[25] = {
			["ID"] = 25,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266597376'),
		},
		[26] = {
			["ID"] = 26,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266597632'),
		},
		[27] = {
			["ID"] = 27,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266597888'),
		},
		[28] = {
			["ID"] = 28,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266598144'),
		},
		[29] = {
			["ID"] = 29,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266598400'),
		},
		[30] = {
			["ID"] = 30,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266598656'),
		},
		[31] = {
			["ID"] = 31,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266598912'),
		},
		[32] = {
			["ID"] = 32,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266599168'),
		},
		[33] = {
			["ID"] = 33,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266599424'),
		},
		[34] = {
			["ID"] = 34,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266599680'),
		},
		[35] = {
			["ID"] = 35,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266599936'),
		},
		[36] = {
			["ID"] = 36,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266600192'),
		},
		[37] = {
			["ID"] = 37,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266600448'),
		},
		[38] = {
			["ID"] = 38,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266600704'),
		},
		[39] = {
			["ID"] = 39,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266600960'),
		},
		[40] = {
			["ID"] = 40,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266601216'),
		},
		[41] = {
			["ID"] = 41,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266601472'),
		},
		[42] = {
			["ID"] = 42,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266601728'),
		},
		[43] = {
			["ID"] = 43,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266601984'),
		},
		[44] = {
			["ID"] = 44,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266602240'),
		},
		[45] = {
			["ID"] = 45,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266602496'),
		},
		[46] = {
			["ID"] = 46,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266602752'),
		},
		[47] = {
			["ID"] = 47,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266603008'),
		},
		[48] = {
			["ID"] = 48,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266603264'),
		},
		[49] = {
			["ID"] = 49,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266603520'),
		},
		[50] = {
			["ID"] = 50,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266603776'),
		},
		[51] = {
			["ID"] = 51,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266604032'),
		},
		[52] = {
			["ID"] = 52,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266604288'),
		},
		[53] = {
			["ID"] = 53,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266604544'),
		},
		[54] = {
			["ID"] = 54,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266604800'),
		},
		[55] = {
			["ID"] = 55,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266605056'),
		},
		[56] = {
			["ID"] = 56,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266605312'),
		},
		[57] = {
			["ID"] = 57,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266605568'),
		},
		[58] = {
			["ID"] = 58,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266605824'),
		},
		[59] = {
			["ID"] = 59,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266606080'),
		},
		[60] = {
			["ID"] = 60,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266606336'),
		},
		[61] = {
			["ID"] = 61,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266606592'),
		},
		[62] = {
			["ID"] = 62,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266606848'),
		},
		[63] = {
			["ID"] = 63,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266607104'),
		},
		[64] = {
			["ID"] = 64,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266607360'),
		},
		[65] = {
			["ID"] = 65,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266607616'),
		},
		[66] = {
			["ID"] = 66,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266607872'),
		},
		[67] = {
			["ID"] = 67,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266608128'),
		},
		[68] = {
			["ID"] = 68,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266608384'),
		},
		[69] = {
			["ID"] = 69,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266608640'),
		},
		[70] = {
			["ID"] = 70,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266608896'),
		},
		[71] = {
			["ID"] = 71,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266609152'),
		},
		[72] = {
			["ID"] = 72,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266609408'),
		},
		[73] = {
			["ID"] = 73,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266609664'),
		},
		[74] = {
			["ID"] = 74,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266609920'),
		},
		[75] = {
			["ID"] = 75,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266610176'),
		},
		[76] = {
			["ID"] = 76,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266610432'),
		},
		[77] = {
			["ID"] = 77,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266610688'),
		},
		[78] = {
			["ID"] = 78,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266610944'),
		},
		[79] = {
			["ID"] = 79,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266611200'),
		},
		[80] = {
			["ID"] = 80,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266611456'),
		},
		[81] = {
			["ID"] = 81,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266611712'),
		},
		[82] = {
			["ID"] = 82,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266611968'),
		},
		[83] = {
			["ID"] = 83,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266612224'),
		},
		[84] = {
			["ID"] = 84,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266612480'),
		},
		[85] = {
			["ID"] = 85,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266612736'),
		},
		[86] = {
			["ID"] = 86,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266612992'),
		},
		[87] = {
			["ID"] = 87,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266613248'),
		},
		[88] = {
			["ID"] = 88,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266613504'),
		},
		[89] = {
			["ID"] = 89,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266613760'),
		},
		[90] = {
			["ID"] = 90,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266614016'),
		},
		[91] = {
			["ID"] = 91,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266614272'),
		},
		[92] = {
			["ID"] = 92,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266614528'),
		},
		[93] = {
			["ID"] = 93,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266614784'),
		},
		[94] = {
			["ID"] = 94,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266615040'),
		},
		[95] = {
			["ID"] = 95,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266615296'),
		},
		[96] = {
			["ID"] = 96,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266615552'),
		},
		[97] = {
			["ID"] = 97,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266615808'),
		},
		[98] = {
			["ID"] = 98,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266616064'),
		},
		[99] = {
			["ID"] = 99,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266616320'),
		},
		[100] = {
			["ID"] = 100,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266616576'),
		},
		[101] = {
			["ID"] = 101,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266616832'),
		},
		[102] = {
			["ID"] = 102,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266617088'),
		},
		[103] = {
			["ID"] = 103,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266617344'),
		},
		[104] = {
			["ID"] = 104,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266617600'),
		},
		[105] = {
			["ID"] = 105,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266617856'),
		},
		[106] = {
			["ID"] = 106,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266618112'),
		},
		[107] = {
			["ID"] = 107,
			["EnText"] = "Sequence promotioon",
			["ChText"] = Game.TableDataManager:GetLangStr('str_51953266618368'),
		},
	},
}

return TopData
