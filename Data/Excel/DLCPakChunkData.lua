--
-- 表名: $DLC.xlsx  页名：$DLCPakChunk
--

local TopData = {
	data = {
		["Movies"] = {
			["DLCChunkName"] = "Movies",
			["NeedPackage"] = false,
			["DLCGroupID"] = 1,
			["BuildTag"] = "buildin,unpakmodle,ungenerate",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_14226810734080'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "/Movies",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk0"] = {
			["DLCChunkName"] = "pakchunk0",
			["NeedPackage"] = true,
			["DLCGroupID"] = 1,
			["BuildTag"] = "main",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_14226810734336'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[lua]/Gameplay/Launch\n[lua]/Tools\n[asset]/Game/Arts/UI_Update/\n[asset]/Game/Blueprint/Misc/\n[asset]/Game/Blueprint/GameLoop/Startup/",
			["ChunkFiles"] = "[lua]/Misc/LuaFunctionLibrary.lua\n[lua]/Framework/Utils/StringUtils.lua\n[lua]/Utils/PlatformUtil.lua\n[lua]/Framework/DoraSDK/SDK/Core/DefineClass.lua\n[lua]/Data/Excel/AnimPlayerData.lua\n[asset]/Game/Arts/Audio/InitBank\n[asset]/Game/Arts/Maps/Startup/LV_Startup\n[asset]/Game/Arts/Maps/HotPatch/LV_HotPatch\n[asset]/Game/Arts/Maps/Loading/LV_Loading\n[asset]/Game/Arts/Maps/Startplace/LV_Startplace_P\n[asset]/Game/Arts/Maps/Login/LV_Login",
			["FilterRules"] = ".../Content/WwiseAudio/.../MX_Update.bnk\n.../Content/WwiseAudio/.../Init.bnk\n.../Content/WwiseAudio/.../DolbyAtmos.bnk\n.../Content/WwiseAudio/.../*********.wem\n.../Content/Arts/NonAsset/*.*\n.../Content/Script/Gameplay/Launch/*.*\n.../Content/Script/Tools/*.*\n.../Content/ScriptOPCode/Gameplay/Launch/*.*\n.../Content/ScriptOPCode/Tools/*.*",
		},
		["pakchunk9999"] = {
			["DLCChunkName"] = "pakchunk9999",
			["NeedPackage"] = true,
			["DLCGroupID"] = 2,
			["BuildTag"] = "salary_chunk",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_14226810734592'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "",
			["ChunkFiles"] = "",
			["FilterRules"] = ".../Content/WwiseAudio/.../*.*\n-.../Content/WwiseAudio/.../*********.wem\n.../Content/Script/*.*\n.../Content/ScriptOPCode/*.*\n-.../Content/ScriptOPCode/Hotfix/*.*",
		},
		["pakchunk2001"] = {
			["DLCChunkName"] = "pakchunk2001",
			["NeedPackage"] = true,
			["DLCGroupID"] = 3,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_54083570371584'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/Tiengen_Lake02/\n[asset]/Game/Arts/Cinematics/Mainplot/LV_Tiengen_Lake/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2002"] = {
			["DLCChunkName"] = "pakchunk2002",
			["NeedPackage"] = true,
			["DLCGroupID"] = 3,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_32574642591488'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/SefirahCastle/\n[asset]/Game/Arts/Cinematics/Mainplot/LV_SefirahCastle_P/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2010"] = {
			["DLCChunkName"] = "pakchunk2010",
			["NeedPackage"] = true,
			["DLCGroupID"] = 4,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_34983850824960'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/Tiengen_New/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2011"] = {
			["DLCChunkName"] = "pakchunk2011",
			["NeedPackage"] = true,
			["DLCGroupID"] = 5,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_34983850814720'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/Pirate_school/\n[asset]/Game/Arts/Cinematics/Mainplot/LV_Pirate_School_P/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2012"] = {
			["DLCChunkName"] = "pakchunk2012",
			["NeedPackage"] = true,
			["DLCGroupID"] = 5,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_34983582374400'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/BlackThorns_Company/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2013"] = {
			["DLCChunkName"] = "pakchunk2013",
			["NeedPackage"] = true,
			["DLCGroupID"] = 5,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_59236994260736'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/Ferlanqi_Flats/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
		["pakchunk2014"] = {
			["DLCChunkName"] = "pakchunk2014",
			["NeedPackage"] = true,
			["DLCGroupID"] = 5,
			["BuildTag"] = "",
			["DLCChunkDesc"] = Game.TableDataManager:GetLangStr('str_34983582374912'),
			["DLCChunkUI"] = "",
			["ChunkDirectory"] = "[asset]/Game/Arts/Maps/DragonBar/",
			["ChunkFiles"] = "",
			["FilterRules"] = "",
		},
	},
}

return TopData
