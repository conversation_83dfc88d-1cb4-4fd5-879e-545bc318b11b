--
-- 表名: $Friend_好友.xlsx  页名：$FriendAttractionSource_好友度来源
--

local TopData = {
	data = {
		[1] = {
			["SourceId"] = 1,
			["AttractionSourceName"] = "ACTIVITY",
			["AttractionSourceDes"] = Game.TableDataManager:GetLangStr('str_25358560658944'),
			["AttractionRefreshType"] = 1,
			["AttractionSingleLimit"] = 120,
			["AttractionIncrement"] = 30,
			["IsNotice"] = 1,
		},
		[2] = {
			["SourceId"] = 2,
			["AttractionSourceName"] = "INTERACTIVE_ACTION",
			["AttractionSourceDes"] = Game.TableDataManager:GetLangStr('str_10721043707648'),
			["AttractionRefreshType"] = 1,
			["AttractionSingleLimit"] = 10,
			["AttractionIncrement"] = 10,
			["IsNotice"] = 1,
		},
		[3] = {
			["SourceId"] = 3,
			["AttractionSourceName"] = "GIFT",
			["AttractionSourceDes"] = Game.TableDataManager:GetLangStr('str_25358560659456'),
			["AttractionRefreshType"] = 1,
			["AttractionSingleLimit"] = 1314520,
			["AttractionIncrement"] = 0,
			["IsNotice"] = 1,
		},
		[4] = {
			["SourceId"] = 4,
			["AttractionSourceName"] = "KILL_MONSTER_IN_TEAM",
			["AttractionSourceDes"] = Game.TableDataManager:GetLangStr('str_25358560659712'),
			["AttractionRefreshType"] = 1,
			["AttractionSingleLimit"] = 200,
			["AttractionIncrement"] = 0,
			["IsNotice"] = 0,
		},
		[5] = {
			["SourceId"] = 5,
			["AttractionSourceName"] = "FIRST_WHISPER_DAILY",
			["AttractionSourceDes"] = Game.TableDataManager:GetLangStr('str_25358560659968'),
			["AttractionRefreshType"] = 1,
			["AttractionSingleLimit"] = 10,
			["AttractionIncrement"] = 5,
			["IsNotice"] = 1,
		},
	},
}

return TopData
