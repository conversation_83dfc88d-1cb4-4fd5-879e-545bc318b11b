--
-- 表名: BubbleData后处理
--

local TopData = {
    ConditionsMap = {
    },
    data = {
        [9007000] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325302529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007000, ['Order'] = 1, ['TalkerID'] = {7211001}, ['Type'] = 0, }, }, 
        },
        [9007001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325302785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007001, ['Order'] = 1, ['TalkerID'] = {7211004}, ['Type'] = 0, }, }, 
        },
        [9007002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325303041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007002, ['Order'] = 1, ['TalkerID'] = {7211006}, ['Type'] = 0, }, }, 
        },
        [9007003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325303297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007003, ['Order'] = 1, ['TalkerID'] = {7211008}, ['Type'] = 0, }, }, 
        },
        [9007004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325303553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007004, ['Order'] = 1, ['TalkerID'] = {7211009}, ['Type'] = 0, }, }, 
        },
        [9007005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325303809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007005, ['Order'] = 1, ['TalkerID'] = {7211011}, ['Type'] = 0, }, }, 
        },
        [9007006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325304065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007006, ['Order'] = 1, ['TalkerID'] = {7211013}, ['Type'] = 0, }, }, 
        },
        [9007007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325304321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007007, ['Order'] = 1, ['TalkerID'] = {7211015}, ['Type'] = 0, }, }, 
        },
        [9007008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325304577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007008, ['Order'] = 1, ['TalkerID'] = {7211017}, ['Type'] = 0, }, }, 
        },
        [9007009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325304833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007009, ['Order'] = 1, ['TalkerID'] = {7211018}, ['Type'] = 0, }, }, 
        },
        [9007010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325305089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007010, ['Order'] = 1, ['TalkerID'] = {7211019}, ['Type'] = 0, }, }, 
        },
        [9007011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325305345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007011, ['Order'] = 1, ['TalkerID'] = {7211020}, ['Type'] = 0, }, }, 
        },
        [9007012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325305601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007012, ['Order'] = 1, ['TalkerID'] = {7211022}, ['Type'] = 0, }, }, 
        },
        [9007013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325305857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007013, ['Order'] = 1, ['TalkerID'] = {7211023}, ['Type'] = 0, }, }, 
        },
        [9007014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325306113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007014, ['Order'] = 1, ['TalkerID'] = {7211024}, ['Type'] = 0, }, }, 
        },
        [9007015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325306369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007015, ['Order'] = 1, ['TalkerID'] = {7211025}, ['Type'] = 0, }, }, 
        },
        [9007016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325306625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007016, ['Order'] = 1, ['TalkerID'] = {7211026}, ['Type'] = 0, }, }, 
        },
        [9007017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325306881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007017, ['Order'] = 1, ['TalkerID'] = {7211028}, ['Type'] = 0, }, }, 
        },
        [9007018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325307137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007018, ['Order'] = 1, ['TalkerID'] = {7211027}, ['Type'] = 0, }, }, 
        },
        [9007019] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325307393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007019, ['Order'] = 1, ['TalkerID'] = {7211097}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325307649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007019, ['Order'] = 2, ['TalkerID'] = {7211097}, ['Type'] = 0, }, }, 
        },
        [9007020] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325307905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007020, ['Order'] = 1, ['TalkerID'] = {7211098}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325308161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007020, ['Order'] = 2, ['TalkerID'] = {7211098}, ['Type'] = 0, }, }, 
        },
        [9007021] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325308417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007021, ['Order'] = 1, ['TalkerID'] = {7211099}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325308673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007021, ['Order'] = 2, ['TalkerID'] = {7211099}, ['Type'] = 0, }, }, 
        },
        [9007022] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325308929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007022, ['Order'] = 1, ['TalkerID'] = {7211100}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325309697'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007022, ['Order'] = 1, ['TalkerID'] = {7211101}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325309185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2500, ['Duration'] = {5000}, ['ID'] = 9007022, ['Order'] = 2, ['TalkerID'] = {7211100}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325309953'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 2500, ['Duration'] = {5000}, ['ID'] = 9007022, ['Order'] = 2, ['TalkerID'] = {7211102}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325309441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5555, ['Duration'] = {5000}, ['ID'] = 9007022, ['Order'] = 3, ['TalkerID'] = {7211101}, ['Type'] = 0, }, }, 
        },
        [9007023] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325310209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007023, ['Order'] = 1, ['TalkerID'] = {7211103}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325310465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {5000}, ['ID'] = 9007023, ['Order'] = 2, ['TalkerID'] = {7211107}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325310721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3500, ['Duration'] = {5000}, ['ID'] = 9007023, ['Order'] = 3, ['TalkerID'] = {7211103}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325310977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 6500, ['Duration'] = {5000}, ['ID'] = 9007023, ['Order'] = 4, ['TalkerID'] = {7211107}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325311233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 8600, ['Duration'] = {5000}, ['ID'] = 9007023, ['Order'] = 5, ['TalkerID'] = {7211103}, ['Type'] = 0, }, }, 
        },
        [9007024] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325311489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007024, ['Order'] = 1, ['TalkerID'] = {7211104}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325311745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007024, ['Order'] = 2, ['TalkerID'] = {7211104}, ['Type'] = 0, }, }, 
        },
        [9007025] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325312001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007025, ['Order'] = 1, ['TalkerID'] = {7211105}, ['Type'] = 0, }, }, 
        },
        [9007026] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325312257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007026, ['Order'] = 1, ['TalkerID'] = {7211106}, ['Type'] = 0, }, }, 
        },
        [9007027] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325312513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007027, ['Order'] = 1, ['TalkerID'] = {7211108}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325312769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007027, ['Order'] = 2, ['TalkerID'] = {7211108}, ['Type'] = 0, }, }, 
        },
        [9007028] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325313025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007028, ['Order'] = 1, ['TalkerID'] = {7211109}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325313281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007028, ['Order'] = 2, ['TalkerID'] = {7211109}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325313537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007028, ['Order'] = 3, ['TalkerID'] = {7211109}, ['Type'] = 0, }, }, 
        },
        [9007029] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325313793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007029, ['Order'] = 1, ['TalkerID'] = {7211110}, ['Type'] = 0, }, }, 
        },
        [9007030] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325314049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007030, ['Order'] = 1, ['TalkerID'] = {7211111}, ['Type'] = 0, }, }, 
        },
        [9007031] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325314305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007031, ['Order'] = 1, ['TalkerID'] = {7211112}, ['Type'] = 0, }, }, 
        },
        [9007032] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325314561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007032, ['Order'] = 1, ['TalkerID'] = {7211113}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325314817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007032, ['Order'] = 2, ['TalkerID'] = {7211113}, ['Type'] = 0, }, }, 
        },
        [9007033] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325315073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007033, ['Order'] = 1, ['TalkerID'] = {7211114}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325315329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007033, ['Order'] = 2, ['TalkerID'] = {7211114}, ['Type'] = 0, }, }, 
        },
        [9007034] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325315585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007034, ['Order'] = 1, ['TalkerID'] = {7211115}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325315841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007034, ['Order'] = 2, ['TalkerID'] = {7211115}, ['Type'] = 0, }, }, 
        },
        [9007035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325316097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007035, ['Order'] = 1, ['TalkerID'] = {7211097}, ['Type'] = 0, }, }, 
        },
        [9007036] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325316353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007036, ['Order'] = 1, ['TalkerID'] = {7211105}, ['Type'] = 0, }, }, 
        },
        [9007037] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325316609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007037, ['Order'] = 1, ['TalkerID'] = {7211117}, ['Type'] = 0, }, }, 
        },
        [9007038] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325316865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007038, ['Order'] = 1, ['TalkerID'] = {7211118}, ['Type'] = 0, }, }, 
        },
        [9007039] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325317121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007039, ['Order'] = 1, ['TalkerID'] = {7211119}, ['Type'] = 0, }, }, 
        },
        [9007040] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325317377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007040, ['Order'] = 1, ['TalkerID'] = {7211120}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325317633'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007040, ['Order'] = 1, ['TalkerID'] = {7211120}, ['Type'] = 0, }, }, 
        },
        [9007041] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325317889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007041, ['Order'] = 1, ['TalkerID'] = {7211051}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325318145'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007041, ['Order'] = 1, ['TalkerID'] = {7211051}, ['Type'] = 0, }, }, 
        },
        [9007042] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325318401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007042, ['Order'] = 1, ['TalkerID'] = {7211035}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325318657'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007042, ['Order'] = 1, ['TalkerID'] = {7211035}, ['Type'] = 0, }, }, 
        },
        [9007043] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325318913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007043, ['Order'] = 1, ['TalkerID'] = {7211033}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325319169'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007043, ['Order'] = 1, ['TalkerID'] = {7211033}, ['Type'] = 0, }, }, 
        },
        [9007044] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325319425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007044, ['Order'] = 1, ['TalkerID'] = {7211036}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325319681'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 2500, ['Duration'] = {5000}, ['ID'] = 9007044, ['Order'] = 1, ['TalkerID'] = {7211036}, ['Type'] = 0, }, }, 
        },
        [9007045] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325319937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007045, ['Order'] = 1, ['TalkerID'] = {7211032}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325320193'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 2500, ['Duration'] = {5000}, ['ID'] = 9007045, ['Order'] = 1, ['TalkerID'] = {7211032}, ['Type'] = 0, }, }, 
        },
        [9007046] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325320449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 9007046, ['Order'] = 1, ['TalkerID'] = {7211125}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325320705'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 9007046, ['Order'] = 1, ['TalkerID'] = {7211126}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325320961'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 3000, ['Duration'] = {5000}, ['ID'] = 9007046, ['Order'] = 1, ['TalkerID'] = {7211125}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325321217'),}, ['Condition'] = {}, ['CutID'] = 4, ['Delay'] = 4000, ['Duration'] = {5000}, ['ID'] = 9007046, ['Order'] = 1, ['TalkerID'] = {7211126}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325321473'),}, ['Condition'] = {}, ['CutID'] = 5, ['Delay'] = 6000, ['Duration'] = {5000}, ['ID'] = 9007046, ['Order'] = 1, ['TalkerID'] = {7211125}, ['Type'] = 0, }, }, 
        },
        [9007047] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325321729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211127}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325321985'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 2500, ['Duration'] = {2000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211128}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325322241'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 4500, ['Duration'] = {2000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211127}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325322497'),}, ['Condition'] = {}, ['CutID'] = 4, ['Delay'] = 6500, ['Duration'] = {2000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211128}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325322753'),}, ['Condition'] = {}, ['CutID'] = 5, ['Delay'] = 8500, ['Duration'] = {3000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211127}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325323009'),}, ['Condition'] = {}, ['CutID'] = 6, ['Delay'] = 11500, ['Duration'] = {3000}, ['ID'] = 9007047, ['Order'] = 1, ['TalkerID'] = {7211128}, ['Type'] = 0, }, }, 
        },
        [9007048] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325323265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007048, ['Order'] = 1, ['TalkerID'] = {7211129}, ['Type'] = 0, }, }, 
        },
        [9007049] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325323521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007049, ['Order'] = 1, ['TalkerID'] = {7211130}, ['Type'] = 0, }, }, 
        },
        [9007050] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325323777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007050, ['Order'] = 1, ['TalkerID'] = {7211131}, ['Type'] = 0, }, }, 
        },
        [9007051] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325324033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007051, ['Order'] = 1, ['TalkerID'] = {7211132}, ['Type'] = 0, }, }, 
        },
        [9007052] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325324289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007052, ['Order'] = 1, ['TalkerID'] = {7211133}, ['Type'] = 0, }, }, 
        },
        [9007053] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325324545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007053, ['Order'] = 1, ['TalkerID'] = {7211134}, ['Type'] = 0, }, }, 
        },
        [9007054] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325324801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007054, ['Order'] = 1, ['TalkerID'] = {7211135}, ['Type'] = 0, }, }, 
        },
        [9007055] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325325057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007055, ['Order'] = 1, ['TalkerID'] = {7211136}, ['Type'] = 0, }, }, 
        },
        [9007056] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325325313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007056, ['Order'] = 1, ['TalkerID'] = {7211137}, ['Type'] = 0, }, }, 
        },
        [9007057] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325325569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007057, ['Order'] = 1, ['TalkerID'] = {7211138}, ['Type'] = 0, }, {['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325325825'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1500, ['Duration'] = {5000}, ['ID'] = 9007057, ['Order'] = 1, ['TalkerID'] = {7211138}, ['Type'] = 0, }, }, 
        },
        [70001000] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325289985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70001000, ['Order'] = 1, ['TalkerID'] = {7203002}, ['Type'] = 0, }, }, 
        },
        [70001001] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325290241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70001001, ['Order'] = 1, ['TalkerID'] = {7203036}, ['Type'] = 0, }, }, 
        },
        [70001002] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325290497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001002, ['Order'] = 1, ['TalkerID'] = {7203037}, ['Type'] = 0, }, }, 
        },
        [70001003] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325290753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70001003, ['Order'] = 1, ['TalkerID'] = {7203038}, ['Type'] = 0, }, }, 
        },
        [70001004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325291009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70001004, ['Order'] = 1, ['TalkerID'] = {7203017}, ['Type'] = 0, }, }, 
        },
        [70001005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325291265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {5000}, ['ID'] = 70001005, ['Order'] = 1, ['TalkerID'] = {7203018}, ['Type'] = 0, }, }, 
        },
        [70001006] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325291521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001006, ['Order'] = 1, ['TalkerID'] = {7203047}, ['Type'] = 0, }, }, 
        },
        [70001007] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325291777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001007, ['Order'] = 1, ['TalkerID'] = {7203048}, ['Type'] = 0, }, }, 
        },
        [70001008] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325292033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70001008, ['Order'] = 1, ['TalkerID'] = {7203049}, ['Type'] = 0, }, }, 
        },
        [70001009] = {{{['BubbleGroupCD'] = 4000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325292289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001009, ['Order'] = 1, ['TalkerID'] = {7203050}, ['Type'] = 0, }, }, 
        },
        [70001010] = {{{['BubbleGroupCD'] = 1000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325292545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001010, ['Order'] = 1, ['TalkerID'] = {7203051}, ['Type'] = 0, }, }, 
        },
        [70001011] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325292801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001011, ['Order'] = 1, ['TalkerID'] = {7203052}, ['Type'] = 0, }, }, 
        },
        [70001012] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325293057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001012, ['Order'] = 1, ['TalkerID'] = {7203027}, ['Type'] = 0, }, }, 
        },
        [70001013] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325293313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001013, ['Order'] = 1, ['TalkerID'] = {7203010}, ['Type'] = 0, }, }, 
        },
        [70001014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325293569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70001014, ['Order'] = 1, ['TalkerID'] = {7203020}, ['Type'] = 0, }, }, 
        },
        [70001015] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325293825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001015, ['Order'] = 1, ['TalkerID'] = {7203030}, ['Type'] = 0, }, }, 
        },
        [70001016] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325294081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001016, ['Order'] = 1, ['TalkerID'] = {7203068}, ['Type'] = 0, }, }, 
        },
        [70001017] = {{{['BubbleGroupCD'] = 1000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325294337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70001017, ['Order'] = 1, ['TalkerID'] = {7203069}, ['Type'] = 0, }, }, 
        },
        [70001018] = {{{['BubbleGroupCD'] = 1000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325294593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001018, ['Order'] = 1, ['TalkerID'] = {7203070}, ['Type'] = 0, }, }, 
        },
        [70001019] = {{{['BubbleGroupCD'] = 1000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325294849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001019, ['Order'] = 1, ['TalkerID'] = {7203072}, ['Type'] = 0, }, }, 
        },
        [70001020] = {{{['BubbleGroupCD'] = 1000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325295105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70001020, ['Order'] = 1, ['TalkerID'] = {7203074}, ['Type'] = 0, }, }, 
        },
        [70001021] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325295361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001021, ['Order'] = 1, ['TalkerID'] = {7203031}, ['Type'] = 0, }, }, 
        },
        [70001022] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325295617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001022, ['Order'] = 1, ['TalkerID'] = {7203074}, ['Type'] = 0, }, }, 
        },
        [70001023] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325295873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001023, ['Order'] = 1, ['TalkerID'] = {7203075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325296129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001023, ['Order'] = 2, ['TalkerID'] = {7203075}, ['Type'] = 0, }, }, 
        },
        [70001024] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325296385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001024, ['Order'] = 1, ['TalkerID'] = {7203076}, ['Type'] = 0, }, }, 
        },
        [70001025] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325296641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70001025, ['Order'] = 1, ['TalkerID'] = {7203077}, ['Type'] = 0, }, }, 
        },
        [70001026] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325296897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001026, ['Order'] = 1, ['TalkerID'] = {7203079}, ['Type'] = 0, }, }, 
        },
        [70001027] = {{{['BubbleGroupCD'] = 2000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325297153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001027, ['Order'] = 1, ['TalkerID'] = {7203084}, ['Type'] = 0, }, }, 
        },
        [70001028] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325297409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001028, ['Order'] = 1, ['TalkerID'] = {7203080}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325297665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001028, ['Order'] = 2, ['TalkerID'] = {7203081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325297921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001028, ['Order'] = 3, ['TalkerID'] = {7203082}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325298177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001028, ['Order'] = 4, ['TalkerID'] = {7203083}, ['Type'] = 0, }, }, 
        },
        [70001029] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325298433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001029, ['Order'] = 1, ['TalkerID'] = {7203055}, ['Type'] = 0, }, }, 
        },
        [70001030] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325298689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001030, ['Order'] = 1, ['TalkerID'] = {7203087}, ['Type'] = 0, }, }, 
        },
        [70001031] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325298945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001031, ['Order'] = 1, ['TalkerID'] = {7203090}, ['Type'] = 0, }, }, 
        },
        [70001032] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325299201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001032, ['Order'] = 1, ['TalkerID'] = {7203088}, ['Type'] = 0, }, }, 
        },
        [70001033] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325299457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001033, ['Order'] = 1, ['TalkerID'] = {7203091}, ['Type'] = 0, }, }, 
        },
        [70001034] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325299713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001034, ['Order'] = 1, ['TalkerID'] = {7203092}, ['Type'] = 0, }, }, 
        },
        [70001035] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325299969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001035, ['Order'] = 1, ['TalkerID'] = {7203093}, ['Type'] = 0, }, }, 
        },
        [70001036] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325300225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001036, ['Order'] = 1, ['TalkerID'] = {7203094}, ['Type'] = 0, }, }, 
        },
        [70001037] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325300481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001037, ['Order'] = 1, ['TalkerID'] = {7203099}, ['Type'] = 0, }, }, 
        },
        [70001038] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325300737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001038, ['Order'] = 1, ['TalkerID'] = {7203102}, ['Type'] = 0, }, }, 
        },
        [70001039] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325300993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001039, ['Order'] = 1, ['TalkerID'] = {7203105}, ['Type'] = 0, }, }, 
        },
        [70001040] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325301249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001040, ['Order'] = 1, ['TalkerID'] = {7203101}, ['Type'] = 0, }, }, 
        },
        [70001041] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325301505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001041, ['Order'] = 1, ['TalkerID'] = {7203100}, ['Type'] = 0, }, }, 
        },
        [70001042] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325301761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001042, ['Order'] = 1, ['TalkerID'] = {7203023}, ['Type'] = 0, }, }, 
        },
        [70001043] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325302017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001043, ['Order'] = 1, ['TalkerID'] = {7203024}, ['Type'] = 0, }, }, 
        },
        [70001044] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325302273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70001044, ['Order'] = 1, ['TalkerID'] = {7203023}, ['Type'] = 0, }, }, 
        },
        [70002001] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325412865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70002001, ['Order'] = 1, ['TalkerID'] = {7201001}, ['Type'] = 0, }, }, 
        },
        [70002002] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325413121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {30000}, ['ID'] = 70002002, ['Order'] = 1, ['TalkerID'] = {7201009}, ['Type'] = 0, }, }, 
        },
        [70002003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325440769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70002003, ['Order'] = 1, ['TalkerID'] = {7201011}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325441025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70002003, ['Order'] = 2, ['TalkerID'] = {7201012}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325441281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70002003, ['Order'] = 3, ['TalkerID'] = {7201013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 8000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325441537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70002003, ['Order'] = 4, ['TalkerID'] = {7201013}, ['Type'] = 0, }, }, 
        },
        [70002004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325441793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 1, ['TalkerID'] = {7201014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325442049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 2, ['TalkerID'] = {7201014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325442305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 3, ['TalkerID'] = {7201014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325442561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 4, ['TalkerID'] = {7201016}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325442817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 5, ['TalkerID'] = {7201017}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 8000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325443073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70002004, ['Order'] = 6, ['TalkerID'] = {7201018}, ['Type'] = 0, }, }, 
        },
        [70002005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325443329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70002005, ['Order'] = 1, ['TalkerID'] = {7201036}, ['Type'] = 0, }, }, 
        },
        [70002006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325301761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70002006, ['Order'] = 1, ['TalkerID'] = {7201036}, ['Type'] = 0, }, }, 
        },
        [70002007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325443841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70002007, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70002008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325444097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70002008, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70003001] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325287937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003001, ['Order'] = 1, ['TalkerID'] = {7205003}, ['Type'] = 0, }, }, 
        },
        [70003002] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325288193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003002, ['Order'] = 1, ['TalkerID'] = {7250091}, ['Type'] = 0, }, }, 
        },
        [70003003] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325288449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003003, ['Order'] = 1, ['TalkerID'] = {7250092}, ['Type'] = 0, }, }, 
        },
        [70003004] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325288705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003004, ['Order'] = 1, ['TalkerID'] = {7250093}, ['Type'] = 0, }, }, 
        },
        [70003005] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325288961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003005, ['Order'] = 1, ['TalkerID'] = {7205004}, ['Type'] = 0, }, }, 
        },
        [70003006] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325289217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003006, ['Order'] = 1, ['TalkerID'] = {7205036}, ['Type'] = 0, }, }, 
        },
        [70003007] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325289473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003007, ['Order'] = 1, ['TalkerID'] = {7205037}, ['Type'] = 0, }, }, 
        },
        [70003008] = {{{['BubbleGroupCD'] = 500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325289729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70003008, ['Order'] = 1, ['TalkerID'] = {7205038}, ['Type'] = 0, }, }, 
        },
        [70005001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325326081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005001, ['Order'] = 1, ['TalkerID'] = {7211002}, ['Type'] = 0, }, }, 
        },
        [70005002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325326849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005002, ['Order'] = 1, ['TalkerID'] = {7211003}, ['Type'] = 0, }, }, 
        },
        [70005003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325327105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005003, ['Order'] = 1, ['TalkerID'] = {7211032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325327361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005003, ['Order'] = 2, ['TalkerID'] = {7211032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325327617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005003, ['Order'] = 3, ['TalkerID'] = {7211032}, ['Type'] = 0, }, }, 
        },
        [70005004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325327873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005004, ['Order'] = 1, ['TalkerID'] = {7211033}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325328129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005004, ['Order'] = 2, ['TalkerID'] = {7211033}, ['Type'] = 0, }, }, 
        },
        [70005005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325328385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005005, ['Order'] = 1, ['TalkerID'] = {7211034}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325328641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005005, ['Order'] = 2, ['TalkerID'] = {7211034}, ['Type'] = 0, }, }, 
        },
        [70005006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325328897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005006, ['Order'] = 1, ['TalkerID'] = {7211035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325329153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005006, ['Order'] = 2, ['TalkerID'] = {7211035}, ['Type'] = 0, }, }, 
        },
        [70005007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325329409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005007, ['Order'] = 1, ['TalkerID'] = {7211036}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325329665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005007, ['Order'] = 2, ['TalkerID'] = {7211036}, ['Type'] = 0, }, }, 
        },
        [70005008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325329921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005008, ['Order'] = 1, ['TalkerID'] = {7211037}, ['Type'] = 0, }, }, 
        },
        [70005009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325330177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005009, ['Order'] = 1, ['TalkerID'] = {7211038}, ['Type'] = 0, }, }, 
        },
        [70005010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325330433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005010, ['Order'] = 1, ['TalkerID'] = {7211039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325330689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005010, ['Order'] = 2, ['TalkerID'] = {7211039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325330945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005010, ['Order'] = 3, ['TalkerID'] = {7211039}, ['Type'] = 0, }, }, 
        },
        [70005011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325331201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005011, ['Order'] = 1, ['TalkerID'] = {7211040}, ['Type'] = 0, }, }, 
        },
        [70005012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325331457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005012, ['Order'] = 1, ['TalkerID'] = {7211041}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325331713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005012, ['Order'] = 2, ['TalkerID'] = {7211041}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325331969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005012, ['Order'] = 3, ['TalkerID'] = {7211041}, ['Type'] = 0, }, }, 
        },
        [70005013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325332225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005013, ['Order'] = 1, ['TalkerID'] = {7211042}, ['Type'] = 0, }, }, 
        },
        [70005014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325332481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005014, ['Order'] = 1, ['TalkerID'] = {7211043}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325332737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005014, ['Order'] = 2, ['TalkerID'] = {7211043}, ['Type'] = 0, }, }, 
        },
        [70005015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325332993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005015, ['Order'] = 1, ['TalkerID'] = {7211044}, ['Type'] = 0, }, }, 
        },
        [70005016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325333249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005016, ['Order'] = 1, ['TalkerID'] = {7211045}, ['Type'] = 0, }, }, 
        },
        [70005017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325333505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005017, ['Order'] = 1, ['TalkerID'] = {7211046}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325333761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005017, ['Order'] = 2, ['TalkerID'] = {7211046}, ['Type'] = 0, }, }, 
        },
        [70005018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325334017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005018, ['Order'] = 1, ['TalkerID'] = {7211047}, ['Type'] = 0, }, }, 
        },
        [70005019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325334273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005019, ['Order'] = 1, ['TalkerID'] = {7211048}, ['Type'] = 0, }, }, 
        },
        [70005020] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325334529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005020, ['Order'] = 1, ['TalkerID'] = {7211049}, ['Type'] = 0, }, }, 
        },
        [70005021] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325334785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005021, ['Order'] = 1, ['TalkerID'] = {7211050}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325335041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005021, ['Order'] = 2, ['TalkerID'] = {7211050}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325335297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005021, ['Order'] = 3, ['TalkerID'] = {7211050}, ['Type'] = 0, }, }, 
        },
        [70005022] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325335553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005022, ['Order'] = 1, ['TalkerID'] = {7211051}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325335809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005022, ['Order'] = 2, ['TalkerID'] = {7211051}, ['Type'] = 0, }, }, 
        },
        [70005023] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325336065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005023, ['Order'] = 1, ['TalkerID'] = {7211052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325336321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005023, ['Order'] = 2, ['TalkerID'] = {7211052}, ['Type'] = 0, }, }, 
        },
        [70005024] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325336577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005024, ['Order'] = 1, ['TalkerID'] = {7211053}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325336833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005024, ['Order'] = 2, ['TalkerID'] = {7211053}, ['Type'] = 0, }, }, 
        },
        [70005025] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325337089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005025, ['Order'] = 1, ['TalkerID'] = {7211054}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325337345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005025, ['Order'] = 2, ['TalkerID'] = {7211054}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325331201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005025, ['Order'] = 3, ['TalkerID'] = {7211054}, ['Type'] = 0, }, }, 
        },
        [70005026] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325337857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005026, ['Order'] = 1, ['TalkerID'] = {7211055}, ['Type'] = 0, }, }, 
        },
        [70005027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325338113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005027, ['Order'] = 1, ['TalkerID'] = {7211056}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325338369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005027, ['Order'] = 2, ['TalkerID'] = {7211056}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325338625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005027, ['Order'] = 3, ['TalkerID'] = {7211056}, ['Type'] = 0, }, }, 
        },
        [70005028] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325338881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005028, ['Order'] = 1, ['TalkerID'] = {7211057}, ['Type'] = 0, }, }, 
        },
        [70005029] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325339137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005029, ['Order'] = 1, ['TalkerID'] = {7211058}, ['Type'] = 0, }, }, 
        },
        [70005030] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325339393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005030, ['Order'] = 1, ['TalkerID'] = {7211059}, ['Type'] = 0, }, }, 
        },
        [70005031] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325339649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005031, ['Order'] = 1, ['TalkerID'] = {7211060}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325339905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005031, ['Order'] = 2, ['TalkerID'] = {7211060}, ['Type'] = 0, }, }, 
        },
        [70005032] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325340161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005032, ['Order'] = 1, ['TalkerID'] = {7211061}, ['Type'] = 0, }, }, 
        },
        [70005033] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325340417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005033, ['Order'] = 1, ['TalkerID'] = {7211062}, ['Type'] = 0, }, }, 
        },
        [70005034] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325340673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005034, ['Order'] = 1, ['TalkerID'] = {7211063}, ['Type'] = 0, }, }, 
        },
        [70005035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325340929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005035, ['Order'] = 1, ['TalkerID'] = {7207001}, ['Type'] = 0, }, }, 
        },
        [70005036] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325341185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005036, ['Order'] = 1, ['TalkerID'] = {7207002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325341441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005036, ['Order'] = 2, ['TalkerID'] = {7207002}, ['Type'] = 0, }, }, 
        },
        [70005037] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325341697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {2000}, ['ID'] = 70005037, ['Order'] = 1, ['TalkerID'] = {7207003}, ['Type'] = 0, }, }, 
        },
        [70005038] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325341953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005038, ['Order'] = 1, ['TalkerID'] = {7207006}, ['Type'] = 0, }, }, 
        },
        [70005039] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325342209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005039, ['Order'] = 1, ['TalkerID'] = {7207007}, ['Type'] = 0, }, }, 
        },
        [70005040] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325342465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005040, ['Order'] = 1, ['TalkerID'] = {7207008}, ['Type'] = 0, }, }, 
        },
        [70005041] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325342721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005041, ['Order'] = 1, ['TalkerID'] = {7207009}, ['Type'] = 0, }, }, 
        },
        [70005042] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325342977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005042, ['Order'] = 1, ['TalkerID'] = {7207114}, ['Type'] = 0, }, }, 
        },
        [70005043] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325343233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005043, ['Order'] = 1, ['TalkerID'] = {7207011}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325343489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {2000}, ['ID'] = 70005043, ['Order'] = 2, ['TalkerID'] = {7207011}, ['Type'] = 0, }, }, 
        },
        [70005044] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325343745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 8000, ['Duration'] = {5000}, ['ID'] = 70005044, ['Order'] = 1, ['TalkerID'] = {7207012}, ['Type'] = 0, }, }, 
        },
        [70005045] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325344001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005045, ['Order'] = 1, ['TalkerID'] = {7207066}, ['Type'] = 0, }, }, 
        },
        [70005047] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325344513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005047, ['Order'] = 1, ['TalkerID'] = {7207015}, ['Type'] = 0, }, }, 
        },
        [70005048] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325344769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005048, ['Order'] = 1, ['TalkerID'] = {7207016}, ['Type'] = 0, }, }, 
        },
        [70005049] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325345025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005049, ['Order'] = 1, ['TalkerID'] = {7207017}, ['Type'] = 0, }, }, 
        },
        [70005050] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325345281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005050, ['Order'] = 1, ['TalkerID'] = {7207018}, ['Type'] = 0, }, }, 
        },
        [70005051] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325345537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005051, ['Order'] = 1, ['TalkerID'] = {7207115}, ['Type'] = 0, }, }, 
        },
        [70005052] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325345793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005052, ['Order'] = 1, ['TalkerID'] = {7207020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325346049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005052, ['Order'] = 2, ['TalkerID'] = {7207020}, ['Type'] = 0, }, }, 
        },
        [70005056] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005056, ['Order'] = 1, ['TalkerID'] = {7207024}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005056, ['Order'] = 2, ['TalkerID'] = {7207024}, ['Type'] = 0, }, }, 
        },
        [70005057] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005057, ['Order'] = 1, ['TalkerID'] = {7207025}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005057, ['Order'] = 2, ['TalkerID'] = {7207025}, ['Type'] = 0, }, }, 
        },
        [70005058] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005058, ['Order'] = 1, ['TalkerID'] = {7207026}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325347841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005058, ['Order'] = 2, ['TalkerID'] = {7207026}, ['Type'] = 0, }, }, 
        },
        [70005059] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325348609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005059, ['Order'] = 1, ['TalkerID'] = {7207027}, ['Type'] = 0, }, }, 
        },
        [70005060] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325348865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005060, ['Order'] = 1, ['TalkerID'] = {7207028}, ['Type'] = 0, }, }, 
        },
        [70005063] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325349633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005063, ['Order'] = 1, ['TalkerID'] = {7207031}, ['Type'] = 0, }, }, 
        },
        [70005064] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325349889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005064, ['Order'] = 1, ['TalkerID'] = {7207032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325350145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005064, ['Order'] = 2, ['TalkerID'] = {7207032}, ['Type'] = 0, }, }, 
        },
        [70005065] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325350401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005065, ['Order'] = 1, ['TalkerID'] = {7207033}, ['Type'] = 0, }, }, 
        },
        [70005066] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325350657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005066, ['Order'] = 1, ['TalkerID'] = {7207034}, ['Type'] = 0, }, }, 
        },
        [70005067] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325350913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005067, ['Order'] = 1, ['TalkerID'] = {7207035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325351169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005067, ['Order'] = 2, ['TalkerID'] = {7207035}, ['Type'] = 0, }, }, 
        },
        [70005068] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325351425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005068, ['Order'] = 1, ['TalkerID'] = {7207036}, ['Type'] = 0, }, }, 
        },
        [70005069] = {{{['BubbleGroupCD'] = 32000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325351681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {5000}, ['ID'] = 70005069, ['Order'] = 1, ['TalkerID'] = {7207037}, ['Type'] = 0, }, }, 
        },
        [70005073] = {{{['BubbleGroupCD'] = 180000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325353217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005073, ['Order'] = 1, ['TalkerID'] = {7207041}, ['Type'] = 0, }, }, 
        },
        [70005074] = {{{['BubbleGroupCD'] = 180000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325353473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005074, ['Order'] = 1, ['TalkerID'] = {7207042}, ['Type'] = 0, }, }, 
        },
        [70005075] = {{{['BubbleGroupCD'] = 180000, ['BubbleText'] = {}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005075, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70005076] = {{{['BubbleGroupCD'] = 180000, ['BubbleText'] = {}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2500, ['Duration'] = {2000}, ['ID'] = 70005076, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70005077] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325354241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005077, ['Order'] = 1, ['TalkerID'] = {7207045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325354497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005077, ['Order'] = 2, ['TalkerID'] = {7207045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325354753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 11000, ['Duration'] = {2000}, ['ID'] = 70005077, ['Order'] = 3, ['TalkerID'] = {7207045}, ['Type'] = 0, }, }, 
        },
        [70005078] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325355009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {2000}, ['ID'] = 70005078, ['Order'] = 1, ['TalkerID'] = {7207046}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325355265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 8000, ['Duration'] = {2000}, ['ID'] = 70005078, ['Order'] = 2, ['TalkerID'] = {7207046}, ['Type'] = 0, }, }, 
        },
        [70005079] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325355521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005079, ['Order'] = 1, ['TalkerID'] = {7207047}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325355777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 8500, ['Duration'] = {2000}, ['ID'] = 70005079, ['Order'] = 2, ['TalkerID'] = {7207047}, ['Type'] = 0, }, }, 
        },
        [70005080] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325356033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2500, ['Duration'] = {2000}, ['ID'] = 70005080, ['Order'] = 1, ['TalkerID'] = {7207048}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325356289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005080, ['Order'] = 2, ['TalkerID'] = {7207048}, ['Type'] = 0, }, }, 
        },
        [70005081] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325356545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005081, ['Order'] = 1, ['TalkerID'] = {7207049}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325356801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005081, ['Order'] = 2, ['TalkerID'] = {7207049}, ['Type'] = 0, }, }, 
        },
        [70005082] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325357057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {2000}, ['ID'] = 70005082, ['Order'] = 1, ['TalkerID'] = {7207050}, ['Type'] = 0, }, }, 
        },
        [70005083] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325357313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005083, ['Order'] = 1, ['TalkerID'] = {7207063}, ['Type'] = 0, }, }, 
        },
        [70005084] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325357569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005084, ['Order'] = 1, ['TalkerID'] = {7207064}, ['Type'] = 0, }, }, 
        },
        [70005085] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325357825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005085, ['Order'] = 1, ['TalkerID'] = {7207069}, ['Type'] = 0, }, }, 
        },
        [70005086] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325358081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70005086, ['Order'] = 1, ['TalkerID'] = {7207071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325358337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70005086, ['Order'] = 2, ['TalkerID'] = {7207071}, ['Type'] = 0, }, }, 
        },
        [70005087] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325358593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70005087, ['Order'] = 1, ['TalkerID'] = {7207073}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325358849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70005087, ['Order'] = 2, ['TalkerID'] = {7207073}, ['Type'] = 0, }, }, 
        },
        [70005088] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325359105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005088, ['Order'] = 1, ['TalkerID'] = {7207076}, ['Type'] = 0, }, }, 
        },
        [70005089] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325359361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005089, ['Order'] = 1, ['TalkerID'] = {7207074}, ['Type'] = 0, }, }, 
        },
        [70005090] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325359617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005090, ['Order'] = 1, ['TalkerID'] = {7207075}, ['Type'] = 0, }, }, 
        },
        [70005091] = {{{['BubbleGroupCD'] = 7000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325359873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70005091, ['Order'] = 1, ['TalkerID'] = {7250106}, ['Type'] = 0, }, }, 
        },
        [70005092] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325360129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 1, ['TalkerID'] = {7207078}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325360385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 2, ['TalkerID'] = {7207079}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325360641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 3, ['TalkerID'] = {7207080}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325360897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 4, ['TalkerID'] = {7207081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325361153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 5, ['TalkerID'] = {7207082}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325361409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005092, ['Order'] = 6, ['TalkerID'] = {7207083}, ['Type'] = 0, }, }, 
        },
        [70005093] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325361665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005093, ['Order'] = 1, ['TalkerID'] = {7207084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325361921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005093, ['Order'] = 2, ['TalkerID'] = {7207085}, ['Type'] = 0, }, }, 
        },
        [70005094] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325362177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005094, ['Order'] = 1, ['TalkerID'] = {7207090}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325362433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005094, ['Order'] = 2, ['TalkerID'] = {7207090}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325362689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005094, ['Order'] = 3, ['TalkerID'] = {7207086}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325362945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005094, ['Order'] = 4, ['TalkerID'] = {7207087}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325363201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005094, ['Order'] = 5, ['TalkerID'] = {7207088}, ['Type'] = 0, }, }, 
        },
        [70005095] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005095, ['Order'] = 1, ['TalkerID'] = {7207057}, ['Type'] = 0, }, }, 
        },
        [70005096] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005096, ['Order'] = 1, ['TalkerID'] = {7207058}, ['Type'] = 0, }, }, 
        },
        [70005097] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005097, ['Order'] = 1, ['TalkerID'] = {7207051}, ['Type'] = 0, }, }, 
        },
        [70005098] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325364225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005098, ['Order'] = 1, ['TalkerID'] = {7207093}, ['Type'] = 0, }, }, 
        },
        [70005099] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325364481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005099, ['Order'] = 1, ['TalkerID'] = {7250106}, ['Type'] = 0, }, }, 
        },
        [70005100] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325364737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005100, ['Order'] = 1, ['TalkerID'] = {7207091}, ['Type'] = 0, }, }, 
        },
        [70005101] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325364993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005101, ['Order'] = 1, ['TalkerID'] = {7207100}, ['Type'] = 0, }, }, 
        },
        [70005102] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325365249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005102, ['Order'] = 1, ['TalkerID'] = {7207101}, ['Type'] = 0, }, }, 
        },
        [70005103] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325365505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005103, ['Order'] = 1, ['TalkerID'] = {7207091}, ['Type'] = 0, }, }, 
        },
        [70005104] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325365761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {}, ['ID'] = 70005104, ['Order'] = 1, ['TalkerID'] = {7207091}, ['Type'] = 0, }, }, 
        },
        [70005105] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325366017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005105, ['Order'] = 1, ['TalkerID'] = {7207106}, ['Type'] = 0, }, }, 
        },
        [70005106] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325366273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005106, ['Order'] = 1, ['TalkerID'] = {7207112}, ['Type'] = 0, }, }, 
        },
        [70005107] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325366529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2000}, ['ID'] = 70005107, ['Order'] = 1, ['TalkerID'] = {7207110}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325366785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2000}, ['ID'] = 70005107, ['Order'] = 2, ['TalkerID'] = {7207110}, ['Type'] = 0, }, }, 
        },
        [70005108] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325367041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005108, ['Order'] = 1, ['TalkerID'] = {7207117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325367297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005108, ['Order'] = 2, ['TalkerID'] = {7207117}, ['Type'] = 0, }, }, 
        },
        [70005109] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325367553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005109, ['Order'] = 1, ['TalkerID'] = {7207111}, ['Type'] = 0, }, }, 
        },
        [70005110] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325367809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70005110, ['Order'] = 1, ['TalkerID'] = {7207118}, ['Type'] = 0, }, }, 
        },
        [70005111] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325368065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70005111, ['Order'] = 1, ['TalkerID'] = {7207119}, ['Type'] = 0, }, }, 
        },
        [70006001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325373953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70006001, ['Order'] = 1, ['TalkerID'] = {7214001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325374209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70006001, ['Order'] = 2, ['TalkerID'] = {7214001}, ['Type'] = 0, }, }, 
        },
        [70007001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325396993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3500}, ['ID'] = 70007001, ['Order'] = 1, ['TalkerID'] = {7215069}, ['Type'] = 0, }, }, 
        },
        [70007002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325397249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3500}, ['ID'] = 70007002, ['Order'] = 1, ['TalkerID'] = {7215002}, ['Type'] = 0, }, }, 
        },
        [70007003] = {{{['Anim'] = {'Talk_Yes_A'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325397505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007003, ['Order'] = 1, ['TalkerID'] = {7215002}, ['Type'] = 0, }, }, 
        },
        [70007004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325397761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007004, ['Order'] = 1, ['TalkerID'] = {7215002}, ['Type'] = 0, }, }, 
        },
        [70007005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325398017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {4000}, ['ID'] = 70007005, ['Order'] = 1, ['TalkerID'] = {7215002}, ['Type'] = 0, }, }, 
        },
        [70007006] = {{{['Anim'] = {'Salute_Start'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325398273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70007006, ['Order'] = 1, ['TalkerID'] = {7215002}, ['Type'] = 0, }, }, 
        },
        [70007007] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325398529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70007007, ['Order'] = 1, ['TalkerID'] = {7215072}, ['Type'] = 0, }, }, 
        },
        [70007008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325398785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007008, ['Order'] = 1, ['TalkerID'] = {7215005}, ['Type'] = 0, }, }, 
        },
        [70007009] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325399041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007009, ['Order'] = 1, ['TalkerID'] = {7215005}, ['Type'] = 0, }, }, 
        },
        [70007010] = {{{['Anim'] = {'Short_Talk2'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325399297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70007010, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007011] = {{{['Anim'] = {'Talk_Yes_B'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325399553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70007011, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007012] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325399809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007012, ['Order'] = 1, ['TalkerID'] = {7215005}, ['Type'] = 0, }, }, 
        },
        [70007013] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325400065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007013, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325400321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70007014, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007015] = {{{['Anim'] = {'Short_Talk2'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325400577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007015, ['Order'] = 1, ['TalkerID'] = {7215007}, ['Type'] = 0, }, }, 
        },
        [70007016] = {{{['Anim'] = {'StandCollection_Loop1'}, ['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325400833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007016, ['Order'] = 1, ['TalkerID'] = {7215073}, ['Type'] = 0, }, }, 
        },
        [70007017] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325401089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70007017, ['Order'] = 1, ['TalkerID'] = {7215074}, ['Type'] = 0, }, }, 
        },
        [70007018] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325401345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007018, ['Order'] = 1, ['TalkerID'] = {7215009}, ['Type'] = 0, }, }, 
        },
        [70007019] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325401601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007019, ['Order'] = 1, ['TalkerID'] = {7215009}, ['Type'] = 0, }, }, 
        },
        [70007020] = {{{['Anim'] = {'Short_Talk3'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325401857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007020, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007021] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325402113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007021, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007022] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325402369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70007022, ['Order'] = 1, ['TalkerID'] = {7215003}, ['Type'] = 0, }, }, 
        },
        [70007023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325402625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007023, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325402881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007024, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007025] = {{{['Anim'] = {'Short_Talk3'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325403137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007025, ['Order'] = 1, ['TalkerID'] = {7215009}, ['Type'] = 0, }, }, 
        },
        [70007026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325403393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007026, ['Order'] = 1, ['TalkerID'] = {7215009}, ['Type'] = 0, }, }, 
        },
        [70007027] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325403649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007027, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007028] = {{{['Anim'] = {'Sad_Start'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325403905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007028, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007029] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325404161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007029, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007030] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325404417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007030, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007031] = {{{['Anim'] = {'Short_Talk2'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325404673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007031, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007032] = {{{['Anim'] = {'Talk_Yes_B'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325404929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007032, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007033] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325405185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1500}, ['ID'] = 70007033, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007034] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325405441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1500}, ['ID'] = 70007034, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007035] = {{{['Anim'] = {'Short_Talk2'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325405697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007035, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007036] = {{{['Anim'] = {'Salute_Start'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325405953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007036, ['Order'] = 1, ['TalkerID'] = {7215017}, ['Type'] = 0, }, }, 
        },
        [70007037] = {{{['Anim'] = {'Short_Talk3'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325406209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007037, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007038] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325406465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007038, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007039] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325406721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007039, ['Order'] = 1, ['TalkerID'] = {7215006}, ['Type'] = 0, }, }, 
        },
        [70007040] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325406977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007040, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007041] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325407233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007041, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007042] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325407489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007042, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70007043] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325407745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007043, ['Order'] = 1, ['TalkerID'] = {7215004}, ['Type'] = 0, }, }, 
        },
        [70007044] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325408001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007044, ['Order'] = 1, ['TalkerID'] = {7215018}, ['Type'] = 0, }, }, 
        },
        [70007045] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325408257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007045, ['Order'] = 1, ['TalkerID'] = {7215007}, ['Type'] = 0, }, }, 
        },
        [70007046] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325408513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 5000, ['Duration'] = {2500}, ['ID'] = 70007046, ['Order'] = 1, ['TalkerID'] = {7215017}, ['Type'] = 0, }, }, 
        },
        [70007047] = {{{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325408769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007047, ['Order'] = 1, ['TalkerID'] = {7215010}, ['Type'] = 0, }, }, 
        },
        [70007048] = {{{['BubbleGroupCD'] = 13500, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325409025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007048, ['Order'] = 1, ['TalkerID'] = {7215011}, ['Type'] = 0, }, }, 
        },
        [70007049] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325409281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007049, ['Order'] = 1, ['TalkerID'] = {7215071}, ['Type'] = 0, }, }, 
        },
        [70007050] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325398529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007050, ['Order'] = 1, ['TalkerID'] = {7215072}, ['Type'] = 0, }, }, 
        },
        [70007051] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325400833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007051, ['Order'] = 1, ['TalkerID'] = {7215073}, ['Type'] = 0, }, }, 
        },
        [70007052] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325401089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007052, ['Order'] = 1, ['TalkerID'] = {7215074}, ['Type'] = 0, }, }, 
        },
        [70007053] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325410305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70007053, ['Order'] = 1, ['TalkerID'] = {7215069}, ['Type'] = 0, }, }, 
        },
        [70007054] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325410561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007054, ['Order'] = 1, ['TalkerID'] = {7215003}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325410817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70007054, ['Order'] = 2, ['TalkerID'] = {7215003}, ['Type'] = 0, }, }, 
        },
        [70007055] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325411073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007055, ['Order'] = 1, ['TalkerID'] = {7242190}, ['Type'] = 0, }, }, 
        },
        [70007056] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325411073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007056, ['Order'] = 1, ['TalkerID'] = {7215082}, ['Type'] = 0, }, }, 
        },
        [70007057] = {{{['Anim'] = {'Short_Talk2'}, ['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325411585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {3000}, ['ID'] = 70007057, ['Order'] = 1, ['TalkerID'] = {7235014}, ['Type'] = 0, }, }, 
        },
        [70007058] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325411841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70007058, ['Order'] = 1, ['TalkerID'] = {7215009}, ['Type'] = 0, }, }, 
        },
        [70007059] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325412097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007059, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007060] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325412353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70007060, ['Order'] = 1, ['TalkerID'] = {7215008}, ['Type'] = 0, }, }, 
        },
        [70007061] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325412609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70007061, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70008001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325326081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70008001, ['Order'] = 1, ['TalkerID'] = {7211002}, ['Type'] = 0, }, }, 
        },
        [70008002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325326337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70008002, ['Order'] = 1, ['TalkerID'] = {7216018}, ['Type'] = 0, }, }, 
        },
        [70009001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325368321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70009001, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70009002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325368577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70009002, ['Order'] = 1, ['TalkerID'] = {7102150}, ['Type'] = 0, }, }, 
        },
        [70009003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325368833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70009003, ['Order'] = 1, ['TalkerID'] = {7102153}, ['Type'] = 0, }, }, 
        },
        [70009004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325369089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70009004, ['Order'] = 1, ['TalkerID'] = {7102154}, ['Type'] = 0, }, }, 
        },
        [70009005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325369345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70009005, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70009006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325369601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70009006, ['Order'] = 1, ['TalkerID'] = {7217005}, ['Type'] = 0, }, }, 
        },
        [70009007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325369857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70009007, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70009008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325370113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70009008, ['Order'] = 1, ['TalkerID'] = {7102157}, ['Type'] = 0, }, }, 
        },
        [70009009] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325370369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70009009, ['Order'] = 1, ['TalkerID'] = {7102157}, ['Type'] = 0, }, }, 
        },
        [70009010] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325370625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70009010, ['Order'] = 1, ['TalkerID'] = {7102157}, ['Type'] = 0, }, }, 
        },
        [70009011] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325370881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70009011, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70009012] = {{{['BubbleGroupCD'] = 8000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325371137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70009012, ['Order'] = 1, ['TalkerID'] = {7217007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325371393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1500}, ['ID'] = 70009012, ['Order'] = 2, ['TalkerID'] = {7217008}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325371649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1500}, ['ID'] = 70009012, ['Order'] = 3, ['TalkerID'] = {7217009}, ['Type'] = 0, }, }, 
        },
        [70009013] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325371905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70009013, ['Order'] = 1, ['TalkerID'] = {7217005}, ['Type'] = 0, }, }, 
        },
        [70009014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325372161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70009014, ['Order'] = 1, ['TalkerID'] = {7217005}, ['Type'] = 0, }, }, 
        },
        [70009015] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325372417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70009015, ['Order'] = 1, ['TalkerID'] = {7217004}, ['Type'] = 0, }, }, 
        },
        [70009016] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325372673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70009016, ['Order'] = 1, ['TalkerID'] = {7217006}, ['Type'] = 0, }, }, 
        },
        [70009017] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325372929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70009017, ['Order'] = 1, ['TalkerID'] = {7217004}, ['Type'] = 0, }, }, 
        },
        [70009018] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325373185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70009018, ['Order'] = 1, ['TalkerID'] = {7217004}, ['Type'] = 0, }, }, 
        },
        [70009019] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325373441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70009019, ['Order'] = 1, ['TalkerID'] = {7217004}, ['Type'] = 0, }, }, 
        },
        [70009020] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325373697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70009020, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70009021] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958984705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70009021, ['Order'] = 1, ['TalkerID'] = {7250186}, ['Type'] = 0, }, }, 
        },
        [70009022] = {{{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958984961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {3000}, ['ID'] = 70009022, ['Order'] = 1, ['TalkerID'] = {7250189}, ['Type'] = 0, }, }, 
        },
        [70009023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958985217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70009023, ['Order'] = 1, ['TalkerID'] = {7209009}, ['Type'] = 0, }, }, 
        },
        [70009024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958985473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70009024, ['Order'] = 1, ['TalkerID'] = {7209009}, ['Type'] = 0, }, }, 
        },
        [70009025] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958985729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70009025, ['Order'] = 1, ['TalkerID'] = {7250263}, ['Type'] = 0, }, }, 
        },
        [70009026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57794958985985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 70009026, ['Order'] = 1, ['TalkerID'] = {7209027}, ['Type'] = 0, }, }, 
        },
        [70010001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325413377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010001, ['Order'] = 1, ['TalkerID'] = {7219001}, ['Type'] = 0, }, }, 
        },
        [70010002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325413633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010002, ['Order'] = 1, ['TalkerID'] = {7219002}, ['Type'] = 0, }, }, 
        },
        [70010003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325413889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010003, ['Order'] = 1, ['TalkerID'] = {7219003}, ['Type'] = 0, }, }, 
        },
        [70010004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325414145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010004, ['Order'] = 1, ['TalkerID'] = {7219004}, ['Type'] = 0, }, }, 
        },
        [70010005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325414401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010005, ['Order'] = 1, ['TalkerID'] = {7219005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325414657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010005, ['Order'] = 2, ['TalkerID'] = {7219005}, ['Type'] = 0, }, }, 
        },
        [70010006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325414913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010006, ['Order'] = 1, ['TalkerID'] = {7219006}, ['Type'] = 0, }, }, 
        },
        [70010007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325415169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010007, ['Order'] = 1, ['TalkerID'] = {7219007}, ['Type'] = 0, }, }, 
        },
        [70010008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325415425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010008, ['Order'] = 1, ['TalkerID'] = {7219009}, ['Type'] = 0, }, }, 
        },
        [70010009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325415681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010009, ['Order'] = 1, ['TalkerID'] = {7219012}, ['Type'] = 0, }, }, 
        },
        [70010011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325415937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010011, ['Order'] = 1, ['TalkerID'] = {7219014}, ['Type'] = 0, }, }, 
        },
        [70010012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325416193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010012, ['Order'] = 1, ['TalkerID'] = {7219015}, ['Type'] = 0, }, }, 
        },
        [70010013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325416449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010013, ['Order'] = 1, ['TalkerID'] = {7219016}, ['Type'] = 0, }, }, 
        },
        [70010014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325416705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010014, ['Order'] = 1, ['TalkerID'] = {7219010}, ['Type'] = 0, }, }, 
        },
        [70010015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325416961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010015, ['Order'] = 1, ['TalkerID'] = {7219018}, ['Type'] = 0, }, }, 
        },
        [70010016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325417217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010016, ['Order'] = 1, ['TalkerID'] = {7219024}, ['Type'] = 0, }, }, 
        },
        [70010017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325417473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010017, ['Order'] = 1, ['TalkerID'] = {7219025}, ['Type'] = 0, }, }, 
        },
        [70010018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325417729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010018, ['Order'] = 1, ['TalkerID'] = {7219026}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325417985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010018, ['Order'] = 2, ['TalkerID'] = {7219026}, ['Type'] = 0, }, }, 
        },
        [70010019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325418241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010019, ['Order'] = 1, ['TalkerID'] = {7219031}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325418497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010019, ['Order'] = 2, ['TalkerID'] = {7219032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325418753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010019, ['Order'] = 3, ['TalkerID'] = {7219032}, ['Type'] = 0, }, }, 
        },
        [70010020] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325419009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010020, ['Order'] = 1, ['TalkerID'] = {7219033}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325419265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010020, ['Order'] = 2, ['TalkerID'] = {7219033}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325419521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010020, ['Order'] = 3, ['TalkerID'] = {7219033}, ['Type'] = 0, }, }, 
        },
        [70010021] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325419777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010021, ['Order'] = 1, ['TalkerID'] = {7219034}, ['Type'] = 0, }, }, 
        },
        [70010022] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325420033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010022, ['Order'] = 1, ['TalkerID'] = {7219035}, ['Type'] = 0, }, }, 
        },
        [70010023] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325420289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010023, ['Order'] = 1, ['TalkerID'] = {7219038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325420545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010023, ['Order'] = 2, ['TalkerID'] = {7219038}, ['Type'] = 0, }, }, 
        },
        [70010024] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325420801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010024, ['Order'] = 1, ['TalkerID'] = {7219039}, ['Type'] = 0, }, }, 
        },
        [70010025] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325421057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010025, ['Order'] = 1, ['TalkerID'] = {7219040}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325421313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010025, ['Order'] = 2, ['TalkerID'] = {7219040}, ['Type'] = 0, }, }, 
        },
        [70010026] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325421569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010026, ['Order'] = 1, ['TalkerID'] = {7219041}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325421825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010026, ['Order'] = 2, ['TalkerID'] = {7219041}, ['Type'] = 0, }, }, 
        },
        [70010027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325422081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010027, ['Order'] = 1, ['TalkerID'] = {7219042}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325422337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010027, ['Order'] = 2, ['TalkerID'] = {7219043}, ['Type'] = 0, }, }, 
        },
        [70010028] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325422593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010028, ['Order'] = 1, ['TalkerID'] = {7219044}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325422849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010028, ['Order'] = 2, ['TalkerID'] = {7219044}, ['Type'] = 0, }, }, 
        },
        [70010029] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325423105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010029, ['Order'] = 1, ['TalkerID'] = {7219045}, ['Type'] = 0, }, }, 
        },
        [70010030] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325423361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010030, ['Order'] = 1, ['TalkerID'] = {7219046}, ['Type'] = 0, }, }, 
        },
        [70010031] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325423617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010031, ['Order'] = 1, ['TalkerID'] = {7219047}, ['Type'] = 0, }, }, 
        },
        [70010032] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325423873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010032, ['Order'] = 1, ['TalkerID'] = {7219048}, ['Type'] = 0, }, }, 
        },
        [70010033] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325424129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010033, ['Order'] = 1, ['TalkerID'] = {7219049}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325424385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010033, ['Order'] = 2, ['TalkerID'] = {7219049}, ['Type'] = 0, }, }, 
        },
        [70010034] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325424641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010034, ['Order'] = 1, ['TalkerID'] = {7219052}, ['Type'] = 0, }, }, 
        },
        [70010035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325424897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010035, ['Order'] = 1, ['TalkerID'] = {7219053}, ['Type'] = 0, }, }, 
        },
        [70010036] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325425153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010036, ['Order'] = 1, ['TalkerID'] = {7219054}, ['Type'] = 0, }, }, 
        },
        [70010037] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325425409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010037, ['Order'] = 1, ['TalkerID'] = {7219055}, ['Type'] = 0, }, }, 
        },
        [70010038] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325425665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70010038, ['Order'] = 1, ['TalkerID'] = {7219057}, ['Type'] = 0, }, }, 
        },
        [70011001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325444353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70011001, ['Order'] = 1, ['TalkerID'] = {7221007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325444609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70011001, ['Order'] = 2, ['TalkerID'] = {7221007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325444865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 70011001, ['Order'] = 3, ['TalkerID'] = {7221007}, ['Type'] = 0, }, }, 
        },
        [70011004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325445121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70011004, ['Order'] = 1, ['TalkerID'] = {7221011}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325445377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70011004, ['Order'] = 2, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70011005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325445633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70011005, ['Order'] = 1, ['TalkerID'] = {7221012}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325445889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70011005, ['Order'] = 2, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70012001] = {{{['BubbleGroupCD'] = 100000000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325425921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {4000}, ['ID'] = 70012001, ['Order'] = 1, ['TalkerID'] = {7223001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325426177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {4000}, ['ID'] = 70012001, ['Order'] = 2, ['TalkerID'] = {7223002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325426433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {4000}, ['ID'] = 70012001, ['Order'] = 3, ['TalkerID'] = {7223001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325426689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {4000}, ['ID'] = 70012001, ['Order'] = 4, ['TalkerID'] = {7223003}, ['Type'] = 0, }, }, 
        },
        [70012002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325426945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012002, ['Order'] = 1, ['TalkerID'] = {7223009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325427201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012002, ['Order'] = 2, ['TalkerID'] = {7223009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325427457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012002, ['Order'] = 3, ['TalkerID'] = {7223009}, ['Type'] = 0, }, }, 
        },
        [70012003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325427713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012003, ['Order'] = 1, ['TalkerID'] = {7223010}, ['Type'] = 0, }, }, 
        },
        [70012004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325427969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012004, ['Order'] = 1, ['TalkerID'] = {7223011}, ['Type'] = 0, }, }, 
        },
        [70012005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325428225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012005, ['Order'] = 1, ['TalkerID'] = {7223011}, ['Type'] = 0, }, }, 
        },
        [70012006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325428481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012006, ['Order'] = 1, ['TalkerID'] = {7223013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325428737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012006, ['Order'] = 2, ['TalkerID'] = {7223013}, ['Type'] = 0, }, }, 
        },
        [70012007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325428993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012007, ['Order'] = 1, ['TalkerID'] = {7223019}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325429249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012007, ['Order'] = 2, ['TalkerID'] = {7223020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325429505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012007, ['Order'] = 3, ['TalkerID'] = {7223020}, ['Type'] = 0, }, }, 
        },
        [70012008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325429761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012008, ['Order'] = 1, ['TalkerID'] = {7223012}, ['Type'] = 0, }, }, 
        },
        [70012009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325430017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012009, ['Order'] = 1, ['TalkerID'] = {7223014}, ['Type'] = 0, }, }, 
        },
        [70012010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325430273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012010, ['Order'] = 1, ['TalkerID'] = {7223015}, ['Type'] = 0, }, }, 
        },
        [70012011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325430529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012011, ['Order'] = 1, ['TalkerID'] = {7223016}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325430785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012011, ['Order'] = 2, ['TalkerID'] = {7223017}, ['Type'] = 0, }, }, 
        },
        [70012012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325431041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012012, ['Order'] = 1, ['TalkerID'] = {7223018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325431297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012012, ['Order'] = 2, ['TalkerID'] = {7223022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325431553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012012, ['Order'] = 3, ['TalkerID'] = {7223018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325431809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012012, ['Order'] = 4, ['TalkerID'] = {7223018}, ['Type'] = 0, }, }, 
        },
        [70012013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325432065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012013, ['Order'] = 1, ['TalkerID'] = {7223032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325432321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012013, ['Order'] = 2, ['TalkerID'] = {7223032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325432577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012013, ['Order'] = 3, ['TalkerID'] = {7223032}, ['Type'] = 0, }, }, 
        },
        [70012014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325432833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012014, ['Order'] = 1, ['TalkerID'] = {7223033}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325433089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012014, ['Order'] = 2, ['TalkerID'] = {7223034}, ['Type'] = 0, }, }, 
        },
        [70012015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325433345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012015, ['Order'] = 1, ['TalkerID'] = {7223024}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325433601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012015, ['Order'] = 2, ['TalkerID'] = {7223025}, ['Type'] = 0, }, }, 
        },
        [70012016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325433857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012016, ['Order'] = 1, ['TalkerID'] = {7223023}, ['Type'] = 0, }, }, 
        },
        [70012017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325434113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012017, ['Order'] = 1, ['TalkerID'] = {7223028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325434369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012017, ['Order'] = 2, ['TalkerID'] = {7223029}, ['Type'] = 0, }, }, 
        },
        [70012018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325434625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012018, ['Order'] = 1, ['TalkerID'] = {7223027}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325434881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012018, ['Order'] = 2, ['TalkerID'] = {7223026}, ['Type'] = 0, }, }, 
        },
        [70012019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325435137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012019, ['Order'] = 1, ['TalkerID'] = {7223031}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325435393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012019, ['Order'] = 2, ['TalkerID'] = {7223030}, ['Type'] = 0, }, }, 
        },
        [70012020] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325435649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012020, ['Order'] = 1, ['TalkerID'] = {7223039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325435905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012020, ['Order'] = 2, ['TalkerID'] = {7223040}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325436161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012020, ['Order'] = 3, ['TalkerID'] = {7223039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325436417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012020, ['Order'] = 4, ['TalkerID'] = {7223040}, ['Type'] = 0, }, }, 
        },
        [70012021] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325436673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012021, ['Order'] = 1, ['TalkerID'] = {7223041}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325436929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012021, ['Order'] = 2, ['TalkerID'] = {7223042}, ['Type'] = 0, }, }, 
        },
        [70012022] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325437185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012022, ['Order'] = 1, ['TalkerID'] = {7223038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325437441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012022, ['Order'] = 2, ['TalkerID'] = {7223038}, ['Type'] = 0, }, }, 
        },
        [70012023] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325437697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012023, ['Order'] = 1, ['TalkerID'] = {7223048}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325437953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012023, ['Order'] = 2, ['TalkerID'] = {7223049}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325438209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012023, ['Order'] = 3, ['TalkerID'] = {7223049}, ['Type'] = 0, }, }, 
        },
        [70012024] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_39309956642816'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70012024, ['Order'] = 1, ['TalkerID'] = {7223050}, ['Type'] = 0, }, }, 
        },
        [70012025] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325438721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70012025, ['Order'] = 1, ['TalkerID'] = {7223044}, ['Type'] = 0, }, }, 
        },
        [70012026] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325438977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70012026, ['Order'] = 1, ['TalkerID'] = {7223052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325439233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012026, ['Order'] = 2, ['TalkerID'] = {7223053}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325439489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012026, ['Order'] = 3, ['TalkerID'] = {7223052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325439745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {5000}, ['ID'] = 70012026, ['Order'] = 4, ['TalkerID'] = {7223053}, ['Type'] = 0, }, }, 
        },
        [70012027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325440001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70012027, ['Order'] = 1, ['TalkerID'] = {7223023}, ['Type'] = 0, }, }, 
        },
        [70012028] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325440257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70012028, ['Order'] = 1, ['TalkerID'] = {7223054}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325440513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70012028, ['Order'] = 2, ['TalkerID'] = {7223055}, ['Type'] = 0, }, }, 
        },
        [70013000] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325446145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70013000, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70013001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325446401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {8000}, ['ID'] = 70013001, ['Order'] = 1, ['TalkerID'] = {7229002}, ['Type'] = 0, }, }, 
        },
        [70013002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325446657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {8000}, ['ID'] = 70013002, ['Order'] = 1, ['TalkerID'] = {7229001}, ['Type'] = 0, }, }, 
        },
        [70013003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325446913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {8000}, ['ID'] = 70013003, ['Order'] = 1, ['TalkerID'] = {7229001}, ['Type'] = 0, }, }, 
        },
        [70013004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325447169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70013004, ['Order'] = 1, ['TalkerID'] = {7229001}, ['Type'] = 0, }, }, 
        },
        [70013005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325447425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70013005, ['Order'] = 1, ['TalkerID'] = {7229001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325447681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70013005, ['Order'] = 2, ['TalkerID'] = {7229001}, ['Type'] = 0, }, }, 
        },
        [70013006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325447937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70013006, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70013007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325448193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70013007, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70014001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325448449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70014001, ['Order'] = 1, ['TalkerID'] = {7225001}, ['Type'] = 0, }, }, 
        },
        [70014002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325448705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {6000}, ['ID'] = 70014002, ['Order'] = 1, ['TalkerID'] = {7225005}, ['Type'] = 0, }, }, 
        },
        [70014003] = {{{['BubbleGroupCD'] = 99999999999, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325448961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {4000}, ['ID'] = 70014003, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70015001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015001, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015002, ['Order'] = 1, ['TalkerID'] = {7218005}, ['Type'] = 0, }, }, 
        },
        [70015003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015003, ['Order'] = 1, ['TalkerID'] = {7218007}, ['Type'] = 0, }, }, 
        },
        [70015004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015004, ['Order'] = 1, ['TalkerID'] = {7218008}, ['Type'] = 0, }, }, 
        },
        [70015005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015005, ['Order'] = 1, ['TalkerID'] = {7218009}, ['Type'] = 0, }, }, 
        },
        [70015006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015006, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015007, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015008, ['Order'] = 1, ['TalkerID'] = {7218005}, ['Type'] = 0, }, }, 
        },
        [70015009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015009, ['Order'] = 1, ['TalkerID'] = {7218007}, ['Type'] = 0, }, }, 
        },
        [70015010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015010, ['Order'] = 1, ['TalkerID'] = {7218008}, ['Type'] = 0, }, }, 
        },
        [70015011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015011, ['Order'] = 1, ['TalkerID'] = {7218009}, ['Type'] = 0, }, }, 
        },
        [70015012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015012, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015013, ['Order'] = 1, ['TalkerID'] = {7218005}, ['Type'] = 0, }, }, 
        },
        [70015014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015014, ['Order'] = 1, ['TalkerID'] = {7218007}, ['Type'] = 0, }, }, 
        },
        [70015015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015015, ['Order'] = 1, ['TalkerID'] = {7218008}, ['Type'] = 0, }, }, 
        },
        [70015016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015016, ['Order'] = 1, ['TalkerID'] = {7218009}, ['Type'] = 0, }, }, 
        },
        [70015017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015017, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015018, ['Order'] = 1, ['TalkerID'] = {7218005}, ['Type'] = 0, }, }, 
        },
        [70015019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015019, ['Order'] = 1, ['TalkerID'] = {7218007}, ['Type'] = 0, }, }, 
        },
        [70015020] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015020, ['Order'] = 1, ['TalkerID'] = {7218008}, ['Type'] = 0, }, }, 
        },
        [70015021] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015021, ['Order'] = 1, ['TalkerID'] = {7218009}, ['Type'] = 0, }, }, 
        },
        [70015022] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015022, ['Order'] = 1, ['TalkerID'] = {7218004}, ['Type'] = 0, }, }, 
        },
        [70015023] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015023, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70015024] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015024, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70015025] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325380609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 1, ['TalkerID'] = {7210829}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325380865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 2, ['TalkerID'] = {7210830}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325381121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 3, ['TalkerID'] = {7210829}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325381377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 4, ['TalkerID'] = {7210830}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325381633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 5, ['TalkerID'] = {7210829}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325381889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 6, ['TalkerID'] = {7210830}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325382145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 7, ['TalkerID'] = {7210829}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325382401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 8, ['TalkerID'] = {7210830}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325382657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015025, ['Order'] = 9, ['TalkerID'] = {7210829}, ['Type'] = 0, }, }, 
        },
        [70015026] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325382913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015026, ['Order'] = 1, ['TalkerID'] = {7210854}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325383169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015026, ['Order'] = 2, ['TalkerID'] = {7210854}, ['Type'] = 0, }, }, 
        },
        [70015027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325383425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015027, ['Order'] = 1, ['TalkerID'] = {7210831}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325383681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015027, ['Order'] = 2, ['TalkerID'] = {7210831}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325383937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015027, ['Order'] = 3, ['TalkerID'] = {7210832}, ['Type'] = 0, }, }, 
        },
        [70015028] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325384193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015028, ['Order'] = 1, ['TalkerID'] = {7210833}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325384449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015028, ['Order'] = 2, ['TalkerID'] = {7210833}, ['Type'] = 0, }, }, 
        },
        [70015029] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325384705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015029, ['Order'] = 1, ['TalkerID'] = {7210834}, ['Type'] = 0, }, }, 
        },
        [70015030] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325384961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015030, ['Order'] = 1, ['TalkerID'] = {7210835}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325385217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015030, ['Order'] = 2, ['TalkerID'] = {7210835}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325385473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015030, ['Order'] = 3, ['TalkerID'] = {7210835}, ['Type'] = 0, }, }, 
        },
        [70015031] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325385729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 1, ['TalkerID'] = {7210836}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325385729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 2, ['TalkerID'] = {7210837}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325386241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 3, ['TalkerID'] = {7210836}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325386497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 4, ['TalkerID'] = {7210837}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325386753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 5, ['TalkerID'] = {7210836}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325387009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 6, ['TalkerID'] = {7210837}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325387265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 7, ['TalkerID'] = {7210836}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325387521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 8, ['TalkerID'] = {7210837}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325387777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 9, ['TalkerID'] = {7210836}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325388033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015031, ['Order'] = 10, ['TalkerID'] = {7210837}, ['Type'] = 0, }, }, 
        },
        [70015032] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325388289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015032, ['Order'] = 1, ['TalkerID'] = {7210838}, ['Type'] = 0, }, }, 
        },
        [70015033] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325388545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015033, ['Order'] = 1, ['TalkerID'] = {7210839}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325388801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015033, ['Order'] = 2, ['TalkerID'] = {7210840}, ['Type'] = 0, }, }, 
        },
        [70015034] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325389057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015034, ['Order'] = 1, ['TalkerID'] = {7210842}, ['Type'] = 0, }, }, 
        },
        [70015035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325389313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 1, ['TalkerID'] = {7210843}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325389569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 2, ['TalkerID'] = {7210844}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325389825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 3, ['TalkerID'] = {7210843}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325390081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 4, ['TalkerID'] = {7210844}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325390337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 5, ['TalkerID'] = {7210843}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325390593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 6, ['TalkerID'] = {7210844}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325390849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015035, ['Order'] = 7, ['TalkerID'] = {7210843}, ['Type'] = 0, }, }, 
        },
        [70015036] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325391105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015036, ['Order'] = 1, ['TalkerID'] = {7210845}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325391361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015036, ['Order'] = 2, ['TalkerID'] = {7210846}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325391617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015036, ['Order'] = 3, ['TalkerID'] = {7210845}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325391873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015036, ['Order'] = 4, ['TalkerID'] = {7210846}, ['Type'] = 0, }, }, 
        },
        [70015037] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325392129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 1, ['TalkerID'] = {7210824}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325392385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 2, ['TalkerID'] = {7210848}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325392641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 3, ['TalkerID'] = {7210824}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325392897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 4, ['TalkerID'] = {7210848}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325393153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 5, ['TalkerID'] = {7210824}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325393409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015037, ['Order'] = 6, ['TalkerID'] = {7210848}, ['Type'] = 0, }, }, 
        },
        [70015038] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325393665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015038, ['Order'] = 1, ['TalkerID'] = {7210849}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325393921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015038, ['Order'] = 2, ['TalkerID'] = {7210850}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325394177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015038, ['Order'] = 3, ['TalkerID'] = {7210849}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325394433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015038, ['Order'] = 4, ['TalkerID'] = {7210850}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325394689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015038, ['Order'] = 5, ['TalkerID'] = {7210849}, ['Type'] = 0, }, }, 
        },
        [70015039] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325394945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015039, ['Order'] = 1, ['TalkerID'] = {7210852}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325395201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015039, ['Order'] = 2, ['TalkerID'] = {7210853}, ['Type'] = 0, }, }, 
        },
        [70015040] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325395457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015040, ['Order'] = 1, ['TalkerID'] = {7250112}, ['Type'] = 0, }, }, 
        },
        [70015041] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325395713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015041, ['Order'] = 1, ['TalkerID'] = {7250116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325395969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015041, ['Order'] = 2, ['TalkerID'] = {7250117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325396225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015041, ['Order'] = 3, ['TalkerID'] = {7250116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325396481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015041, ['Order'] = 4, ['TalkerID'] = {7250117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325396737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70015041, ['Order'] = 5, ['TalkerID'] = {7250116}, ['Type'] = 0, }, }, 
        },
        [70016001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325449217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016001, ['Order'] = 1, ['TalkerID'] = {7231001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325449473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016001, ['Order'] = 2, ['TalkerID'] = {7231001}, ['Type'] = 0, }, }, 
        },
        [70016002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325449729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016002, ['Order'] = 1, ['TalkerID'] = {7231002}, ['Type'] = 0, }, }, 
        },
        [70016003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325449985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016003, ['Order'] = 1, ['TalkerID'] = {7231003}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325450241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016003, ['Order'] = 2, ['TalkerID'] = {7231003}, ['Type'] = 0, }, }, 
        },
        [70016005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325450497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016005, ['Order'] = 1, ['TalkerID'] = {7231006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325450753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016005, ['Order'] = 2, ['TalkerID'] = {7231006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325451009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016005, ['Order'] = 3, ['TalkerID'] = {7231006}, ['Type'] = 0, }, }, 
        },
        [70016007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325451265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016007, ['Order'] = 1, ['TalkerID'] = {7231008}, ['Type'] = 0, }, }, 
        },
        [70016009] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325451521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016009, ['Order'] = 1, ['TalkerID'] = {7231010}, ['Type'] = 0, }, }, 
        },
        [70016010] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325451777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016010, ['Order'] = 1, ['TalkerID'] = {7231011}, ['Type'] = 0, }, }, 
        },
        [70016011] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325452033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016011, ['Order'] = 1, ['TalkerID'] = {7231012}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325452289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016011, ['Order'] = 2, ['TalkerID'] = {7231013}, ['Type'] = 0, }, }, 
        },
        [70016012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325452545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016012, ['Order'] = 1, ['TalkerID'] = {7231080}, ['Type'] = 0, }, }, 
        },
        [70016013] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325452801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016013, ['Order'] = 1, ['TalkerID'] = {7231014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325453057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016013, ['Order'] = 2, ['TalkerID'] = {7231015}, ['Type'] = 0, }, }, 
        },
        [70016014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325453313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016014, ['Order'] = 1, ['TalkerID'] = {7231080}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325453569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016014, ['Order'] = 2, ['TalkerID'] = {7231081}, ['Type'] = 0, }, }, 
        },
        [70016015] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325453825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016015, ['Order'] = 1, ['TalkerID'] = {7231016}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325454081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016015, ['Order'] = 2, ['TalkerID'] = {7231016}, ['Type'] = 0, }, }, 
        },
        [70016016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325454337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016016, ['Order'] = 1, ['TalkerID'] = {7231017}, ['Type'] = 0, }, }, 
        },
        [70016017] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325454593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016017, ['Order'] = 1, ['TalkerID'] = {7231018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325454849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016017, ['Order'] = 2, ['TalkerID'] = {7231018}, ['Type'] = 0, }, }, 
        },
        [70016018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325455105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016018, ['Order'] = 1, ['TalkerID'] = {7231019}, ['Type'] = 0, }, }, 
        },
        [70016019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325455361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016019, ['Order'] = 1, ['TalkerID'] = {7231020}, ['Type'] = 0, }, }, 
        },
        [70016022] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325455617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016022, ['Order'] = 1, ['TalkerID'] = {7231023}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325455873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016022, ['Order'] = 2, ['TalkerID'] = {7231024}, ['Type'] = 0, }, }, 
        },
        [70016023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325456129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016023, ['Order'] = 1, ['TalkerID'] = {7231081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325456385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016023, ['Order'] = 2, ['TalkerID'] = {7231081}, ['Type'] = 0, }, }, 
        },
        [70016024] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325456641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016024, ['Order'] = 1, ['TalkerID'] = {7231025}, ['Type'] = 0, }, }, 
        },
        [70016025] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325456897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016025, ['Order'] = 1, ['TalkerID'] = {7231026}, ['Type'] = 0, }, }, 
        },
        [70016026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325457153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016026, ['Order'] = 1, ['TalkerID'] = {7231027}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325457409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016026, ['Order'] = 2, ['TalkerID'] = {7231027}, ['Type'] = 0, }, }, 
        },
        [70016027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325457665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016027, ['Order'] = 1, ['TalkerID'] = {7231028}, ['Type'] = 0, }, }, 
        },
        [70016028] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325457921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016028, ['Order'] = 1, ['TalkerID'] = {7231029}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325458177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016028, ['Order'] = 2, ['TalkerID'] = {7231029}, ['Type'] = 0, }, }, 
        },
        [70016029] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325458433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016029, ['Order'] = 1, ['TalkerID'] = {7231031}, ['Type'] = 0, }, }, 
        },
        [70016030] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325458689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016030, ['Order'] = 1, ['TalkerID'] = {7231032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325458945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016030, ['Order'] = 2, ['TalkerID'] = {7231032}, ['Type'] = 0, }, }, 
        },
        [70016031] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325459201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016031, ['Order'] = 1, ['TalkerID'] = {7231033}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325459457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016031, ['Order'] = 2, ['TalkerID'] = {7231033}, ['Type'] = 0, }, }, 
        },
        [70016032] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325459713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016032, ['Order'] = 1, ['TalkerID'] = {7231034}, ['Type'] = 0, }, }, 
        },
        [70016033] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325459969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 700, ['Duration'] = {5000}, ['ID'] = 70016033, ['Order'] = 1, ['TalkerID'] = {7231035}, ['Type'] = 0, }, }, 
        },
        [70016034] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325460225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016034, ['Order'] = 1, ['TalkerID'] = {7231036}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325460481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016034, ['Order'] = 2, ['TalkerID'] = {7231037}, ['Type'] = 0, }, }, 
        },
        [70016035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_39309956642816'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016035, ['Order'] = 1, ['TalkerID'] = {7231082}, ['Type'] = 0, }, }, 
        },
        [70016036] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325460993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016036, ['Order'] = 1, ['TalkerID'] = {7231038}, ['Type'] = 0, }, }, 
        },
        [70016037] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325461249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016037, ['Order'] = 1, ['TalkerID'] = {7231039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325461505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016037, ['Order'] = 2, ['TalkerID'] = {7231039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325461761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016037, ['Order'] = 3, ['TalkerID'] = {7239017}, ['Type'] = 0, }, }, 
        },
        [70016038] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325462017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016038, ['Order'] = 1, ['TalkerID'] = {7231040}, ['Type'] = 0, }, }, {{['Anim'] = {'ScotlandDance'}, ['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325462273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {10000}, ['ID'] = 70016038, ['Order'] = 2, ['TalkerID'] = {7231040}, ['Type'] = 0, }, }, 
        },
        [70016039] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325462529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016039, ['Order'] = 1, ['TalkerID'] = {7231133}, ['Type'] = 0, }, }, 
        },
        [70016041] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325462785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016041, ['Order'] = 1, ['TalkerID'] = {7231043}, ['Type'] = 0, }, }, 
        },
        [70016045] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325463041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016045, ['Order'] = 1, ['TalkerID'] = {7231046}, ['Type'] = 0, }, }, 
        },
        [70016046] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325463297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 10000, ['Duration'] = {5000}, ['ID'] = 70016046, ['Order'] = 1, ['TalkerID'] = {7231047}, ['Type'] = 0, }, }, 
        },
        [70016047] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325463553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016047, ['Order'] = 1, ['TalkerID'] = {7231048}, ['Type'] = 0, }, }, 
        },
        [70016048] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325463809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016048, ['Order'] = 1, ['TalkerID'] = {7231049}, ['Type'] = 0, }, }, 
        },
        [70016049] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325464065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016049, ['Order'] = 1, ['TalkerID'] = {7231050}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325464321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016049, ['Order'] = 2, ['TalkerID'] = {7231050}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325464577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016049, ['Order'] = 3, ['TalkerID'] = {7231050}, ['Type'] = 0, }, }, 
        },
        [70016050] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325464833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 700, ['Duration'] = {5000}, ['ID'] = 70016050, ['Order'] = 1, ['TalkerID'] = {7231051}, ['Type'] = 0, }, }, 
        },
        [70016051] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325465089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016051, ['Order'] = 1, ['TalkerID'] = {7231052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325465345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016051, ['Order'] = 2, ['TalkerID'] = {7231052}, ['Type'] = 0, }, }, 
        },
        [70016052] = {{{['BubbleGroupCD'] = 120000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325465601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 700, ['Duration'] = {5000}, ['ID'] = 70016052, ['Order'] = 1, ['TalkerID'] = {7231053}, ['Type'] = 0, }, }, 
        },
        [70016053] = {{{['BubbleGroupCD'] = 120000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325465857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016053, ['Order'] = 1, ['TalkerID'] = {7231054}, ['Type'] = 0, }, }, 
        },
        [70016054] = {{{['BubbleGroupCD'] = 120000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325466113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016054, ['Order'] = 1, ['TalkerID'] = {7231055}, ['Type'] = 0, }, }, 
        },
        [70016055] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325466369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016055, ['Order'] = 1, ['TalkerID'] = {7231056}, ['Type'] = 0, }, }, 
        },
        [70016056] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325466625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016056, ['Order'] = 1, ['TalkerID'] = {7231057}, ['Type'] = 0, }, }, 
        },
        [70016057] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325466881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016057, ['Order'] = 1, ['TalkerID'] = {7231058}, ['Type'] = 0, }, }, 
        },
        [70016058] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325467137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016058, ['Order'] = 1, ['TalkerID'] = {7231059}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325467393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016058, ['Order'] = 2, ['TalkerID'] = {7231059}, ['Type'] = 0, }, }, 
        },
        [70016059] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325467649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016059, ['Order'] = 1, ['TalkerID'] = {7231060}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325467905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016059, ['Order'] = 2, ['TalkerID'] = {7231060}, ['Type'] = 0, }, }, 
        },
        [70016060] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325468161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016060, ['Order'] = 1, ['TalkerID'] = {7231061}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325468417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016060, ['Order'] = 2, ['TalkerID'] = {7231062}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325468673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016060, ['Order'] = 3, ['TalkerID'] = {7231062}, ['Type'] = 0, }, }, 
        },
        [70016061] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325468929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016061, ['Order'] = 1, ['TalkerID'] = {7231083}, ['Type'] = 0, }, }, 
        },
        [70016062] = {{{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325469185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016062, ['Order'] = 1, ['TalkerID'] = {7231063}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325469441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016062, ['Order'] = 2, ['TalkerID'] = {7231063}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325469697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016062, ['Order'] = 3, ['TalkerID'] = {7231063}, ['Type'] = 0, }, }, 
        },
        [70016063] = {{{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325469953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016063, ['Order'] = 1, ['TalkerID'] = {7231064}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325470209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016063, ['Order'] = 2, ['TalkerID'] = {7231064}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325470465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016063, ['Order'] = 3, ['TalkerID'] = {7231065}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325470721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016063, ['Order'] = 4, ['TalkerID'] = {7231065}, ['Type'] = 0, }, }, 
        },
        [70016065] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325470977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016065, ['Order'] = 1, ['TalkerID'] = {7231066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325471233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016065, ['Order'] = 2, ['TalkerID'] = {7231066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325471489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016065, ['Order'] = 3, ['TalkerID'] = {7231066}, ['Type'] = 0, }, }, 
        },
        [70016066] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325468929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016066, ['Order'] = 1, ['TalkerID'] = {7231084}, ['Type'] = 0, }, }, 
        },
        [70016067] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325472001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016067, ['Order'] = 1, ['TalkerID'] = {7231068}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325472257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016067, ['Order'] = 2, ['TalkerID'] = {7231068}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325472513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016067, ['Order'] = 3, ['TalkerID'] = {7231069}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325472769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016067, ['Order'] = 4, ['TalkerID'] = {7231069}, ['Type'] = 0, }, }, 
        },
        [70016068] = {{{['BubbleGroupCD'] = 90000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325473025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016068, ['Order'] = 1, ['TalkerID'] = {7231130}, ['Type'] = 0, }, }, 
        },
        [70016069] = {{{['BubbleGroupCD'] = 80000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325473281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016069, ['Order'] = 1, ['TalkerID'] = {7231070}, ['Type'] = 0, }, }, 
        },
        [70016070] = {{{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325473537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016070, ['Order'] = 1, ['TalkerID'] = {7231071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325473793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016070, ['Order'] = 2, ['TalkerID'] = {7231071}, ['Type'] = 0, }, }, 
        },
        [70016071] = {{{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325474049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016071, ['Order'] = 1, ['TalkerID'] = {7231072}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325474305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016071, ['Order'] = 2, ['TalkerID'] = {7231072}, ['Type'] = 0, }, }, 
        },
        [70016072] = {{{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325474561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016072, ['Order'] = 1, ['TalkerID'] = {7231073}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325474817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016072, ['Order'] = 2, ['TalkerID'] = {7231073}, ['Type'] = 0, }, }, 
        },
        [70016073] = {{{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325475073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016073, ['Order'] = 1, ['TalkerID'] = {7231074}, ['Type'] = 0, }, }, 
        },
        [70016074] = {{{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325475329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016074, ['Order'] = 1, ['TalkerID'] = {7231075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 70000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325475585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016074, ['Order'] = 2, ['TalkerID'] = {7231075}, ['Type'] = 0, }, }, 
        },
        [70016075] = {{{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325475841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016075, ['Order'] = 1, ['TalkerID'] = {7231076}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325476097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016075, ['Order'] = 2, ['TalkerID'] = {7231076}, ['Type'] = 0, }, }, 
        },
        [70016076] = {{{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325476353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016076, ['Order'] = 1, ['TalkerID'] = {7231077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325476609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016076, ['Order'] = 2, ['TalkerID'] = {7231077}, ['Type'] = 0, }, }, 
        },
        [70016077] = {{{['BubbleGroupCD'] = 100000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325476865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016077, ['Order'] = 1, ['TalkerID'] = {7231078}, ['Type'] = 0, }, }, 
        },
        [70016078] = {{{['BubbleGroupCD'] = 120000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325477121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016078, ['Order'] = 1, ['TalkerID'] = {7231079}, ['Type'] = 0, }, }, 
        },
        [70016079] = {{{['BubbleGroupCD'] = 120000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325477377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016079, ['Order'] = 1, ['TalkerID'] = {7231086}, ['Type'] = 0, }, }, 
        },
        [70016080] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325477633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016080, ['Order'] = 1, ['TalkerID'] = {7231087}, ['Type'] = 0, }, }, 
        },
        [70016081] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325477889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016081, ['Order'] = 1, ['TalkerID'] = {7231088}, ['Type'] = 0, }, }, 
        },
        [70016082] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325478145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016082, ['Order'] = 1, ['TalkerID'] = {7231089}, ['Type'] = 0, }, }, 
        },
        [70016083] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325478401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016083, ['Order'] = 1, ['TalkerID'] = {7231090}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325478657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016083, ['Order'] = 2, ['TalkerID'] = {7231090}, ['Type'] = 0, }, }, 
        },
        [70016084] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325478913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016084, ['Order'] = 1, ['TalkerID'] = {7231091}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325479169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016084, ['Order'] = 2, ['TalkerID'] = {7231091}, ['Type'] = 0, }, }, 
        },
        [70016085] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325479425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016085, ['Order'] = 1, ['TalkerID'] = {7231092}, ['Type'] = 0, }, }, 
        },
        [70016086] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325479681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016086, ['Order'] = 1, ['TalkerID'] = {7231093}, ['Type'] = 0, }, }, 
        },
        [70016087] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325479937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016087, ['Order'] = 1, ['TalkerID'] = {7231094}, ['Type'] = 0, }, }, 
        },
        [70016088] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325480193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016088, ['Order'] = 1, ['TalkerID'] = {7231095}, ['Type'] = 0, }, }, 
        },
        [70016089] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325480449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016089, ['Order'] = 1, ['TalkerID'] = {7231096}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325480705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016089, ['Order'] = 2, ['TalkerID'] = {7231096}, ['Type'] = 0, }, }, 
        },
        [70016090] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325480961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016090, ['Order'] = 1, ['TalkerID'] = {7231097}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325481217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016090, ['Order'] = 2, ['TalkerID'] = {7231097}, ['Type'] = 0, }, }, 
        },
        [70016091] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325481473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016091, ['Order'] = 1, ['TalkerID'] = {7231098}, ['Type'] = 0, }, }, 
        },
        [70016094] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325481729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016094, ['Order'] = 1, ['TalkerID'] = {7231101}, ['Type'] = 0, }, }, 
        },
        [70016095] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325481985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016095, ['Order'] = 1, ['TalkerID'] = {7231102}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325482241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 700, ['Duration'] = {5000}, ['ID'] = 70016095, ['Order'] = 2, ['TalkerID'] = {7231102}, ['Type'] = 0, }, }, 
        },
        [70016096] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325482497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1500, ['Duration'] = {3000}, ['ID'] = 70016096, ['Order'] = 1, ['TalkerID'] = {7231103}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325482753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016096, ['Order'] = 2, ['TalkerID'] = {7231125}, ['Type'] = 0, }, }, 
        },
        [70016097] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325483009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016097, ['Order'] = 1, ['TalkerID'] = {7231104}, ['Type'] = 0, }, }, 
        },
        [70016098] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_57862873167360'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016098, ['Order'] = 1, ['TalkerID'] = {7231105}, ['Type'] = 0, }, }, 
        },
        [70016099] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325483521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016099, ['Order'] = 1, ['TalkerID'] = {7231106}, ['Type'] = 0, }, }, 
        },
        [70016100] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325483777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016100, ['Order'] = 1, ['TalkerID'] = {7231107}, ['Type'] = 0, }, }, 
        },
        [70016101] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325484033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016101, ['Order'] = 1, ['TalkerID'] = {7231132}, ['Type'] = 0, }, }, 
        },
        [70016104] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325484289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016104, ['Order'] = 1, ['TalkerID'] = {7231111}, ['Type'] = 0, }, }, 
        },
        [70016105] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325484545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016105, ['Order'] = 1, ['TalkerID'] = {7231112}, ['Type'] = 0, }, }, 
        },
        [70016106] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325484801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016106, ['Order'] = 1, ['TalkerID'] = {7231113}, ['Type'] = 0, }, }, 
        },
        [70016107] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325485057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016107, ['Order'] = 1, ['TalkerID'] = {7231114}, ['Type'] = 0, }, }, 
        },
        [70016108] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325485313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016108, ['Order'] = 1, ['TalkerID'] = {7231115}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325485569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016108, ['Order'] = 2, ['TalkerID'] = {7231115}, ['Type'] = 0, }, }, 
        },
        [70016109] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325485825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70016109, ['Order'] = 1, ['TalkerID'] = {7231116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325486081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016109, ['Order'] = 2, ['TalkerID'] = {7231116}, ['Type'] = 0, }, }, 
        },
        [70016110] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325486337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016110, ['Order'] = 1, ['TalkerID'] = {7231117}, ['Type'] = 0, }, }, 
        },
        [70016111] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325486593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016111, ['Order'] = 1, ['TalkerID'] = {7231118}, ['Type'] = 0, }, }, 
        },
        [70016112] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325486849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016112, ['Order'] = 1, ['TalkerID'] = {7231119}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325487105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016112, ['Order'] = 2, ['TalkerID'] = {7231119}, ['Type'] = 0, }, }, 
        },
        [70016113] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325487361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016113, ['Order'] = 1, ['TalkerID'] = {7231120}, ['Type'] = 0, }, }, 
        },
        [70016114] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325487617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016114, ['Order'] = 1, ['TalkerID'] = {7231121}, ['Type'] = 0, }, }, 
        },
        [70016115] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325487873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016115, ['Order'] = 1, ['TalkerID'] = {7231122}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325488129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70016115, ['Order'] = 2, ['TalkerID'] = {7231122}, ['Type'] = 0, }, }, 
        },
        [70016116] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325488385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016116, ['Order'] = 1, ['TalkerID'] = {7231123}, ['Type'] = 0, }, }, 
        },
        [70016117] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325488641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016117, ['Order'] = 1, ['TalkerID'] = {7231124}, ['Type'] = 0, }, }, 
        },
        [70016119] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325488897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016119, ['Order'] = 1, ['TalkerID'] = {7231126}, ['Type'] = 0, }, }, 
        },
        [70016120] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325489153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016120, ['Order'] = 1, ['TalkerID'] = {7231127}, ['Type'] = 0, }, }, 
        },
        [70016121] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325489409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70016121, ['Order'] = 1, ['TalkerID'] = {7231128}, ['Type'] = 0, }, }, 
        },
        [70016122] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325489665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70016122, ['Order'] = 1, ['TalkerID'] = {7231129}, ['Type'] = 0, }, }, 
        },
        [70017001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325614337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70017001, ['Order'] = 1, ['TalkerID'] = {7227001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325614593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70017001, ['Order'] = 2, ['TalkerID'] = {7227002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325614849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70017001, ['Order'] = 3, ['TalkerID'] = {7227001}, ['Type'] = 0, }, }, 
        },
        [70017002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325615105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70017002, ['Order'] = 1, ['TalkerID'] = {7227005}, ['Type'] = 0, }, }, 
        },
        [70018001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325489921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 1, ['TalkerID'] = {7212001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325490433'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 1, ['TalkerID'] = {7212002}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325490945'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 1, ['TalkerID'] = {7212003}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325490177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 2, ['TalkerID'] = {7212001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325490689'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 2, ['TalkerID'] = {7212002}, ['Type'] = 0, }, {['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325491201'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018001, ['Order'] = 2, ['TalkerID'] = {7212003}, ['Type'] = 0, }, }, 
        },
        [70018004] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325491457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018004, ['Order'] = 1, ['TalkerID'] = {7212001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325491713'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018004, ['Order'] = 1, ['TalkerID'] = {7212002}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325491969'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018004, ['Order'] = 1, ['TalkerID'] = {7212003}, ['Type'] = 0, }, }, 
        },
        [70018007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325492225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018007, ['Order'] = 1, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325492481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018007, ['Order'] = 2, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325492737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018007, ['Order'] = 3, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325492993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 4, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325493249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 5, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325493505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 6, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325493761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 7, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325494017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 8, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325494273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {1000}, ['ID'] = 70018007, ['Order'] = 9, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325494529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018007, ['Order'] = 10, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, 
        },
        [70018008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325494785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 1, ['TalkerID'] = {7212004}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325495809'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 1, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325495041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 2, ['TalkerID'] = {7212004}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325496065'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 2, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325495297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 3, ['TalkerID'] = {7212004}, ['Type'] = 0, }, {['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325496321'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 3, ['TalkerID'] = {7212005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325495553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018008, ['Order'] = 4, ['TalkerID'] = {7212004}, ['Type'] = 0, }, }, 
        },
        [70018010] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325496577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018010, ['Order'] = 1, ['TalkerID'] = {7212008}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325496833'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018010, ['Order'] = 1, ['TalkerID'] = {7212007}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325497089'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018010, ['Order'] = 1, ['TalkerID'] = {7212006}, ['Type'] = 0, }, }, 
        },
        [70018013] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325497345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 1, ['TalkerID'] = {7212007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325497601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 2, ['TalkerID'] = {7212007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325497857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 3, ['TalkerID'] = {7212008}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325498113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 4, ['TalkerID'] = {7212008}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325498369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 5, ['TalkerID'] = {7212006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325498625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 6, ['TalkerID'] = {7212006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325498881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 7, ['TalkerID'] = {7212008}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325499137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018013, ['Order'] = 8, ['TalkerID'] = {7212008}, ['Type'] = 0, }, }, 
        },
        [70018014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325499393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 1, ['TalkerID'] = {7212009}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325499905'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 1, ['TalkerID'] = {7212010}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325500417'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 1, ['TalkerID'] = {7212011}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325499649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 2, ['TalkerID'] = {7212009}, ['Type'] = 0, }, {['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325500161'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 2, ['TalkerID'] = {7212010}, ['Type'] = 0, }, {['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325500673'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018014, ['Order'] = 2, ['TalkerID'] = {7212011}, ['Type'] = 0, }, }, 
        },
        [70018017] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325500929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018017, ['Order'] = 1, ['TalkerID'] = {7212009}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325501185'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018017, ['Order'] = 1, ['TalkerID'] = {7212010}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325501441'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018017, ['Order'] = 1, ['TalkerID'] = {7212011}, ['Type'] = 0, }, }, 
        },
        [70018020] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325501697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 1, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325503233'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 1, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325501953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 2, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325503489'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 2, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325502209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 3, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325503745'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 3, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325502465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 4, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325504001'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 4, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325502721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 5, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325504257'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 5, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325502977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 6, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325504513'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018020, ['Order'] = 6, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, 
        },
        [70018021] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325504769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018021, ['Order'] = 1, ['TalkerID'] = {7212012}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325505025'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018021, ['Order'] = 1, ['TalkerID'] = {7212013}, ['Type'] = 0, }, }, 
        },
        [70018022] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325505281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018022, ['Order'] = 1, ['TalkerID'] = {7212014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325505537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018022, ['Order'] = 2, ['TalkerID'] = {7212015}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325505793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018022, ['Order'] = 3, ['TalkerID'] = {7212014}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325506049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018022, ['Order'] = 4, ['TalkerID'] = {7212015}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325506305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018022, ['Order'] = 5, ['TalkerID'] = {7212016}, ['Type'] = 0, }, }, 
        },
        [70018023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325506561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018023, ['Order'] = 1, ['TalkerID'] = {7212014}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325506817'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018023, ['Order'] = 1, ['TalkerID'] = {7212015}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325507073'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018023, ['Order'] = 1, ['TalkerID'] = {7212016}, ['Type'] = 0, }, }, 
        },
        [70018024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325507329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 1, ['TalkerID'] = {7212017}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325507585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 2, ['TalkerID'] = {7212018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325507841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 3, ['TalkerID'] = {7212017}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325508097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 4, ['TalkerID'] = {7212018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325508353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 5, ['TalkerID'] = {7212017}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325508609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 6, ['TalkerID'] = {7212018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325508865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 7, ['TalkerID'] = {7212017}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 35000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325509121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018024, ['Order'] = 8, ['TalkerID'] = {7212018}, ['Type'] = 0, }, }, 
        },
        [70018025] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325509377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018025, ['Order'] = 1, ['TalkerID'] = {7212017}, ['Type'] = 0, }, {['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325509633'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018025, ['Order'] = 1, ['TalkerID'] = {7212018}, ['Type'] = 0, }, }, 
        },
        [70018026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325509889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 1, ['TalkerID'] = {7212019}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325510145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 2, ['TalkerID'] = {7212020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325510401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 3, ['TalkerID'] = {7212019}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325510657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 4, ['TalkerID'] = {7212020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325510913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 5, ['TalkerID'] = {7212019}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325511169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 6, ['TalkerID'] = {7212020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325511425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018026, ['Order'] = 7, ['TalkerID'] = {7212019}, ['Type'] = 0, }, }, 
        },
        [70018027] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325511681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018027, ['Order'] = 1, ['TalkerID'] = {7212021}, ['Type'] = 0, }, }, 
        },
        [70018028] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325511937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018028, ['Order'] = 1, ['TalkerID'] = {7212022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325512193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018028, ['Order'] = 2, ['TalkerID'] = {7212023}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325512449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018028, ['Order'] = 3, ['TalkerID'] = {7212022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325512705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018028, ['Order'] = 4, ['TalkerID'] = {7212023}, ['Type'] = 0, }, }, 
        },
        [70018029] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325512961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018029, ['Order'] = 1, ['TalkerID'] = {7212022}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325513217'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018029, ['Order'] = 1, ['TalkerID'] = {7212023}, ['Type'] = 0, }, }, 
        },
        [70018030] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325513473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018030, ['Order'] = 1, ['TalkerID'] = {7212024}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325513729'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018030, ['Order'] = 1, ['TalkerID'] = {7212025}, ['Type'] = 0, }, }, 
        },
        [70018031] = {{{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325513985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018031, ['Order'] = 1, ['TalkerID'] = {7212026}, ['Type'] = 0, }, }, 
        },
        [70018032] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325514497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018032, ['Order'] = 1, ['TalkerID'] = {7212026}, ['Type'] = 0, }, }, 
        },
        [70018033] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325515009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018033, ['Order'] = 1, ['TalkerID'] = {7212028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325515265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018033, ['Order'] = 2, ['TalkerID'] = {7212028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325515521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018033, ['Order'] = 3, ['TalkerID'] = {7212029}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325515777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018033, ['Order'] = 4, ['TalkerID'] = {7212029}, ['Type'] = 0, }, }, 
        },
        [70018034] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325516033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018034, ['Order'] = 1, ['TalkerID'] = {7212028}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325516289'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018034, ['Order'] = 1, ['TalkerID'] = {7212029}, ['Type'] = 0, }, }, 
        },
        [70018035] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325516545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018035, ['Order'] = 1, ['TalkerID'] = {7212030}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325516801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018035, ['Order'] = 2, ['TalkerID'] = {7212031}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325517057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018035, ['Order'] = 3, ['TalkerID'] = {7212032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325517313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018035, ['Order'] = 4, ['TalkerID'] = {7212032}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325517569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018035, ['Order'] = 5, ['TalkerID'] = {7212031}, ['Type'] = 0, }, }, 
        },
        [70018036] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325517825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018036, ['Order'] = 1, ['TalkerID'] = {7212035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325518081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018036, ['Order'] = 2, ['TalkerID'] = {7212035}, ['Type'] = 0, }, }, 
        },
        [70018037] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325518337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018037, ['Order'] = 1, ['TalkerID'] = {7212036}, ['Type'] = 0, }, }, 
        },
        [70018038] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325518593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018038, ['Order'] = 1, ['TalkerID'] = {7212035}, ['Type'] = 0, }, }, 
        },
        [70018039] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325518849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018039, ['Order'] = 1, ['TalkerID'] = {7212036}, ['Type'] = 0, }, }, 
        },
        [70018040] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325519105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018040, ['Order'] = 1, ['TalkerID'] = {7212037}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325519361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018040, ['Order'] = 2, ['TalkerID'] = {7212037}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325519617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018040, ['Order'] = 3, ['TalkerID'] = {7212037}, ['Type'] = 0, }, }, 
        },
        [70018041] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325519873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 1, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325520129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 2, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325520385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 3, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325520641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 4, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325520897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 5, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325521153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 6, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325521409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 7, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325521665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018041, ['Order'] = 8, ['TalkerID'] = {7212038}, ['Type'] = 0, }, }, 
        },
        [70018042] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325521921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018042, ['Order'] = 1, ['TalkerID'] = {7212039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325522177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018042, ['Order'] = 2, ['TalkerID'] = {7212040}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325522433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018042, ['Order'] = 3, ['TalkerID'] = {7212039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325522689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018042, ['Order'] = 4, ['TalkerID'] = {7212041}, ['Type'] = 0, }, }, 
        },
        [70018043] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325522945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018043, ['Order'] = 1, ['TalkerID'] = {7212042}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325523201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018043, ['Order'] = 2, ['TalkerID'] = {7212042}, ['Type'] = 0, }, }, 
        },
        [70018044] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325523457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018044, ['Order'] = 1, ['TalkerID'] = {7212043}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325523713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018044, ['Order'] = 2, ['TalkerID'] = {7212043}, ['Type'] = 0, }, }, 
        },
        [70018045] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325523969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018045, ['Order'] = 1, ['TalkerID'] = {7212044}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325524225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018045, ['Order'] = 2, ['TalkerID'] = {7212044}, ['Type'] = 0, }, }, 
        },
        [70018046] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325524481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 1, ['TalkerID'] = {7212045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325524737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 2, ['TalkerID'] = {7212046}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325524993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 3, ['TalkerID'] = {7212045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325525249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 4, ['TalkerID'] = {7212046}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325366017'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 4, ['TalkerID'] = {7212047}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325525505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 5, ['TalkerID'] = {7212045}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325526017'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018046, ['Order'] = 5, ['TalkerID'] = {7212047}, ['Type'] = 0, }, }, 
        },
        [70018047] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325526273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018047, ['Order'] = 1, ['TalkerID'] = {7212045}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325526529'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018047, ['Order'] = 1, ['TalkerID'] = {7212046}, ['Type'] = 0, }, {['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325526785'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018047, ['Order'] = 1, ['TalkerID'] = {7212047}, ['Type'] = 0, }, }, 
        },
        [70018048] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325527041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018048, ['Order'] = 1, ['TalkerID'] = {7212048}, ['Type'] = 0, }, }, 
        },
        [70018049] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325527297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018049, ['Order'] = 1, ['TalkerID'] = {7212049}, ['Type'] = 0, }, }, 
        },
        [70018050] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325527553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018050, ['Order'] = 1, ['TalkerID'] = {7212050}, ['Type'] = 0, }, }, 
        },
        [70018051] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325527809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018051, ['Order'] = 1, ['TalkerID'] = {7212051}, ['Type'] = 0, }, }, 
        },
        [70018052] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325528065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018052, ['Order'] = 1, ['TalkerID'] = {7212052}, ['Type'] = 0, }, }, 
        },
        [70018053] = {{{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325514241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018053, ['Order'] = 1, ['TalkerID'] = {7212027}, ['Type'] = 0, }, }, 
        },
        [70018054] = {{[2] = {['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325514753'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018054, ['Order'] = 1, ['TalkerID'] = {7212027}, ['Type'] = 0, }, }, 
        },
        [70018055] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325528321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018055, ['Order'] = 1, ['TalkerID'] = {7212030}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325528577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018055, ['Order'] = 2, ['TalkerID'] = {7212031}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325528833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018055, ['Order'] = 3, ['TalkerID'] = {7212032}, ['Type'] = 0, }, }, 
        },
        [70018056] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325529089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018056, ['Order'] = 1, ['TalkerID'] = {7212053}, ['Type'] = 0, }, }, 
        },
        [70018057] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325529345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018057, ['Order'] = 1, ['TalkerID'] = {7212054}, ['Type'] = 0, }, }, 
        },
        [70018058] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325529601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018058, ['Order'] = 1, ['TalkerID'] = {7212076}, ['Type'] = 0, }, }, 
        },
        [70018059] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325529857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018059, ['Order'] = 1, ['TalkerID'] = {7212081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325530113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018059, ['Order'] = 2, ['TalkerID'] = {7212082}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325530369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018059, ['Order'] = 3, ['TalkerID'] = {7212081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325530625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018059, ['Order'] = 4, ['TalkerID'] = {7212081}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325530881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018059, ['Order'] = 5, ['TalkerID'] = {7212081}, ['Type'] = 0, }, }, 
        },
        [70018060] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325531137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3500}, ['ID'] = 70018060, ['Order'] = 1, ['TalkerID'] = {7212084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325531393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3500}, ['ID'] = 70018060, ['Order'] = 2, ['TalkerID'] = {7212084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325531649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3500}, ['ID'] = 70018060, ['Order'] = 3, ['TalkerID'] = {7212084}, ['Type'] = 0, }, }, 
        },
        [70018061] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325531905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 1, ['TalkerID'] = {7212092}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325533953'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1500, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 1, ['TalkerID'] = {7212095}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325532161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 2, ['TalkerID'] = {7212093}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325534209'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1500, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 2, ['TalkerID'] = {7212095}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325532417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 3, ['TalkerID'] = {7212092}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325534465'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1500, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 3, ['TalkerID'] = {7212095}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325532673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 4, ['TalkerID'] = {7212093}, ['Type'] = 0, }, {['BubbleGroupCD'] = 40000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325534721'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 1500, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 4, ['TalkerID'] = {7212095}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325532929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 5, ['TalkerID'] = {7212093}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325533185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 6, ['TalkerID'] = {7212092}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325533441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 7, ['TalkerID'] = {7212092}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325533697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018061, ['Order'] = 8, ['TalkerID'] = {7212093}, ['Type'] = 0, }, }, 
        },
        [70018062] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325534977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70018062, ['Order'] = 1, ['TalkerID'] = {7212088}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325535233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70018062, ['Order'] = 2, ['TalkerID'] = {7212088}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325535489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {2500}, ['ID'] = 70018062, ['Order'] = 3, ['TalkerID'] = {7212088}, ['Type'] = 0, }, }, 
        },
        [70018063] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325535745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018063, ['Order'] = 1, ['TalkerID'] = {7212071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325536001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018063, ['Order'] = 2, ['TalkerID'] = {7212071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325536257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018063, ['Order'] = 3, ['TalkerID'] = {7212071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325536513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018063, ['Order'] = 4, ['TalkerID'] = {7212071}, ['Type'] = 0, }, }, 
        },
        [70018064] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325536769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018064, ['Order'] = 1, ['TalkerID'] = {7212078}, ['Type'] = 0, }, }, 
        },
        [70018065] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325316097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018065, ['Order'] = 1, ['TalkerID'] = {7212101}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325537281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018065, ['Order'] = 2, ['TalkerID'] = {7212101}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325537537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018065, ['Order'] = 3, ['TalkerID'] = {7212101}, ['Type'] = 0, }, }, 
        },
        [70018066] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325537793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018066, ['Order'] = 1, ['TalkerID'] = {7212097}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325538049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018066, ['Order'] = 2, ['TalkerID'] = {7212098}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325538049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018066, ['Order'] = 3, ['TalkerID'] = {7212099}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325538561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018066, ['Order'] = 4, ['TalkerID'] = {7212100}, ['Type'] = 0, }, }, 
        },
        [70018067] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325538817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018067, ['Order'] = 1, ['TalkerID'] = {7212066}, ['Type'] = 0, }, }, 
        },
        [70018068] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325539073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018068, ['Order'] = 1, ['TalkerID'] = {7212067}, ['Type'] = 0, }, }, 
        },
        [70018069] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325539329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018069, ['Order'] = 1, ['TalkerID'] = {7212087}, ['Type'] = 0, }, }, 
        },
        [70018070] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325539585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018070, ['Order'] = 1, ['TalkerID'] = {7212085}, ['Type'] = 0, }, {['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325539841'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018070, ['Order'] = 1, ['TalkerID'] = {7212086}, ['Type'] = 0, }, }, 
        },
        [70018071] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325540097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018071, ['Order'] = 1, ['TalkerID'] = {7212094}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325540353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018071, ['Order'] = 2, ['TalkerID'] = {7212094}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325540609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018071, ['Order'] = 3, ['TalkerID'] = {7212094}, ['Type'] = 0, }, }, 
        },
        [70018072] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325540865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018072, ['Order'] = 1, ['TalkerID'] = {7212090}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325541121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018072, ['Order'] = 2, ['TalkerID'] = {7212090}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325541377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2000}, ['ID'] = 70018072, ['Order'] = 3, ['TalkerID'] = {7212090}, ['Type'] = 0, }, }, 
        },
        [70018073] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325541633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018073, ['Order'] = 1, ['TalkerID'] = {7212073}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325541889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018073, ['Order'] = 2, ['TalkerID'] = {7212073}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325542145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3500}, ['ID'] = 70018073, ['Order'] = 3, ['TalkerID'] = {7212073}, ['Type'] = 0, }, }, 
        },
        [70018074] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325542401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018074, ['Order'] = 1, ['TalkerID'] = {7212074}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325542657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018074, ['Order'] = 2, ['TalkerID'] = {7212075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325542913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018074, ['Order'] = 3, ['TalkerID'] = {7212074}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325543169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018074, ['Order'] = 4, ['TalkerID'] = {7212074}, ['Type'] = 0, }, }, 
        },
        [70018075] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325543425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018075, ['Order'] = 1, ['TalkerID'] = {7212068}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325543681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018075, ['Order'] = 2, ['TalkerID'] = {7212068}, ['Type'] = 0, }, }, 
        },
        [70018076] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325543937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018076, ['Order'] = 1, ['TalkerID'] = {7212110}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325544193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018076, ['Order'] = 2, ['TalkerID'] = {7212109}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325544449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018076, ['Order'] = 3, ['TalkerID'] = {7212109}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325544705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018076, ['Order'] = 4, ['TalkerID'] = {7212109}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325544961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018076, ['Order'] = 5, ['TalkerID'] = {7212110}, ['Type'] = 0, }, }, 
        },
        [70018077] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325545217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018077, ['Order'] = 1, ['TalkerID'] = {7212112}, ['Type'] = 0, }, }, 
        },
        [70018078] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325545473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018078, ['Order'] = 1, ['TalkerID'] = {7212114}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325545729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018078, ['Order'] = 2, ['TalkerID'] = {7212114}, ['Type'] = 0, }, }, 
        },
        [70018079] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325545985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018079, ['Order'] = 1, ['TalkerID'] = {7212115}, ['Type'] = 0, }, }, 
        },
        [70018080] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325546241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018080, ['Order'] = 1, ['TalkerID'] = {7212106}, ['Type'] = 0, }, }, 
        },
        [70018081] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325546497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018081, ['Order'] = 1, ['TalkerID'] = {7212105}, ['Type'] = 0, }, }, 
        },
        [70018082] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325546753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018082, ['Order'] = 1, ['TalkerID'] = {7212107}, ['Type'] = 0, }, }, 
        },
        [70018083] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325547009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018083, ['Order'] = 1, ['TalkerID'] = {7212077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325547265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018083, ['Order'] = 2, ['TalkerID'] = {7212077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325547521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018083, ['Order'] = 3, ['TalkerID'] = {7212077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325547777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018083, ['Order'] = 4, ['TalkerID'] = {7212077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325548033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018083, ['Order'] = 5, ['TalkerID'] = {7212077}, ['Type'] = 0, }, }, 
        },
        [70018084] = {{{['BubbleGroupCD'] = 12000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325548289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018084, ['Order'] = 1, ['TalkerID'] = {7212080}, ['Type'] = 0, }, }, 
        },
        [70018085] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325548545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018085, ['Order'] = 1, ['TalkerID'] = {7212108}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325548801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {3000}, ['ID'] = 70018085, ['Order'] = 2, ['TalkerID'] = {7212108}, ['Type'] = 0, }, }, 
        },
        [70018086] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325549057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 1, ['TalkerID'] = {7212116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325549313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 2, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325549569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 3, ['TalkerID'] = {7212116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325549825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 4, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325550081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 5, ['TalkerID'] = {7212116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325550337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 6, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325550593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 7, ['TalkerID'] = {7212116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325550849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 8, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325551105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 9, ['TalkerID'] = {7212116}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325551361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 10, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325551617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 11, ['TalkerID'] = {7212116}, ['Type'] = 0, }, {['BubbleGroupCD'] = 40000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325551617'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018086, ['Order'] = 11, ['TalkerID'] = {7212117}, ['Type'] = 0, }, }, 
        },
        [70018087] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325552129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018087, ['Order'] = 1, ['TalkerID'] = {7212132}, ['Type'] = 0, }, }, 
        },
        [70018088] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325552385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 300, ['Duration'] = {2500}, ['ID'] = 70018088, ['Order'] = 1, ['TalkerID'] = {7212142}, ['Type'] = 0, }, }, 
        },
        [70019001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325552641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019001, ['Order'] = 1, ['TalkerID'] = {7213001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325552897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019001, ['Order'] = 2, ['TalkerID'] = {7213001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325553153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019001, ['Order'] = 3, ['TalkerID'] = {7213001}, ['Type'] = 0, }, }, 
        },
        [70019002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325553409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019002, ['Order'] = 1, ['TalkerID'] = {7213002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325553665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019002, ['Order'] = 2, ['TalkerID'] = {7213002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325553921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019002, ['Order'] = 3, ['TalkerID'] = {7213002}, ['Type'] = 0, }, }, 
        },
        [70019003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325554177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019003, ['Order'] = 1, ['TalkerID'] = {7213003}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325554433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019003, ['Order'] = 2, ['TalkerID'] = {7213003}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325554689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019003, ['Order'] = 3, ['TalkerID'] = {7213003}, ['Type'] = 0, }, }, 
        },
        [70019004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325554945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019004, ['Order'] = 1, ['TalkerID'] = {7213004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325555201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019004, ['Order'] = 2, ['TalkerID'] = {7213005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325555457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019004, ['Order'] = 3, ['TalkerID'] = {7213006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325555713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019004, ['Order'] = 4, ['TalkerID'] = {7213007}, ['Type'] = 0, }, }, 
        },
        [70019008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325555969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70019008, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325556225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70019008, ['Order'] = 2, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70019010] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325556481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019010, ['Order'] = 1, ['TalkerID'] = {7213009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325556737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019010, ['Order'] = 2, ['TalkerID'] = {7213009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325556993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019010, ['Order'] = 3, ['TalkerID'] = {7213009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325557249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019010, ['Order'] = 4, ['TalkerID'] = {7213009}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325557505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019010, ['Order'] = 5, ['TalkerID'] = {7213009}, ['Type'] = 0, }, }, 
        },
        [70019016] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325557761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019016, ['Order'] = 1, ['TalkerID'] = {7213015}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325558017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019016, ['Order'] = 2, ['TalkerID'] = {7213015}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325558273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019016, ['Order'] = 3, ['TalkerID'] = {7213015}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325558529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019016, ['Order'] = 4, ['TalkerID'] = {7213015}, ['Type'] = 0, }, }, 
        },
        [70019017] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325558785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019017, ['Order'] = 1, ['TalkerID'] = {7213016}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325559041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019017, ['Order'] = 2, ['TalkerID'] = {7213016}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325559297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019017, ['Order'] = 3, ['TalkerID'] = {7213016}, ['Type'] = 0, }, }, 
        },
        [70019019] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325559553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019019, ['Order'] = 1, ['TalkerID'] = {7213018}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325559809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019019, ['Order'] = 2, ['TalkerID'] = {7213019}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325560065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019019, ['Order'] = 3, ['TalkerID'] = {7213020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325560321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019019, ['Order'] = 4, ['TalkerID'] = {7213021}, ['Type'] = 0, }, }, 
        },
        [70019023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325560577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019023, ['Order'] = 1, ['TalkerID'] = {7213022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325560833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019023, ['Order'] = 2, ['TalkerID'] = {7213022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325561089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019023, ['Order'] = 3, ['TalkerID'] = {7213022}, ['Type'] = 0, }, }, 
        },
        [70019024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325561857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019024, ['Order'] = 1, ['TalkerID'] = {7213025}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325561345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019024, ['Order'] = 2, ['TalkerID'] = {7213023}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325561601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019024, ['Order'] = 3, ['TalkerID'] = {7213023}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325562113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019024, ['Order'] = 4, ['TalkerID'] = {7213024}, ['Type'] = 0, }, }, 
        },
        [70019027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325562369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70019027, ['Order'] = 1, ['TalkerID'] = {7213026}, ['Type'] = 0, }, }, 
        },
        [70019029] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325562625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019029, ['Order'] = 1, ['TalkerID'] = {7213028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325563393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019029, ['Order'] = 2, ['TalkerID'] = {7213028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325562881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019029, ['Order'] = 3, ['TalkerID'] = {7213028}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325563137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019029, ['Order'] = 4, ['TalkerID'] = {7213028}, ['Type'] = 0, }, }, 
        },
        [70019030] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325564161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019030, ['Order'] = 1, ['TalkerID'] = {7213031}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325563649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019030, ['Order'] = 2, ['TalkerID'] = {7213029}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325563905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019030, ['Order'] = 3, ['TalkerID'] = {7213030}, ['Type'] = 0, }, }, 
        },
        [70019035] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325564417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019035, ['Order'] = 1, ['TalkerID'] = {7213034}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325564673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019035, ['Order'] = 2, ['TalkerID'] = {7213034}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325564929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019035, ['Order'] = 3, ['TalkerID'] = {7213034}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325565185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019035, ['Order'] = 4, ['TalkerID'] = {7213034}, ['Type'] = 0, }, }, 
        },
        [70019036] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325565441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019036, ['Order'] = 1, ['TalkerID'] = {7213035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325565697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019036, ['Order'] = 2, ['TalkerID'] = {7213035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325565953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019036, ['Order'] = 3, ['TalkerID'] = {7213035}, ['Type'] = 0, }, }, 
        },
        [70019038] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325566209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019038, ['Order'] = 1, ['TalkerID'] = {7213038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325566465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019038, ['Order'] = 2, ['TalkerID'] = {7213039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325566721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019038, ['Order'] = 3, ['TalkerID'] = {7213038}, ['Type'] = 0, }, }, 
        },
        [70019039] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325566977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70019039, ['Order'] = 1, ['TalkerID'] = {7213040}, ['Type'] = 0, }, }, 
        },
        [70019041] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325567233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019041, ['Order'] = 1, ['TalkerID'] = {7213042}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325567489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019041, ['Order'] = 2, ['TalkerID'] = {7213042}, ['Type'] = 0, }, }, 
        },
        [70019042] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325567745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70019042, ['Order'] = 1, ['TalkerID'] = {7213043}, ['Type'] = 0, }, }, 
        },
        [70019043] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325568001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019043, ['Order'] = 1, ['TalkerID'] = {7213044}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325568257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019043, ['Order'] = 2, ['TalkerID'] = {7213044}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325568513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019043, ['Order'] = 3, ['TalkerID'] = {7213044}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325568769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019043, ['Order'] = 4, ['TalkerID'] = {7213044}, ['Type'] = 0, }, }, 
        },
        [70019044] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325569025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019044, ['Order'] = 1, ['TalkerID'] = {7213046}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325569281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019044, ['Order'] = 2, ['TalkerID'] = {7213045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325569537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019044, ['Order'] = 3, ['TalkerID'] = {7213046}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325569793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019044, ['Order'] = 4, ['TalkerID'] = {7213045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325570049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019044, ['Order'] = 5, ['TalkerID'] = {7213046}, ['Type'] = 0, }, }, 
        },
        [70019045] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325570305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019045, ['Order'] = 1, ['TalkerID'] = {7213052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325570561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019045, ['Order'] = 2, ['TalkerID'] = {7213052}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325570817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019045, ['Order'] = 3, ['TalkerID'] = {7213052}, ['Type'] = 0, }, }, 
        },
        [70019046] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325571073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019046, ['Order'] = 1, ['TalkerID'] = {7213049}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325571329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019046, ['Order'] = 2, ['TalkerID'] = {7213048}, ['Type'] = 0, }, }, 
        },
        [70019047] = {{{['BubbleGroupCD'] = 25000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325571585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019047, ['Order'] = 1, ['TalkerID'] = {7213050}, ['Type'] = 0, }, }, 
        },
        [70019048] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325571841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019048, ['Order'] = 1, ['TalkerID'] = {7213051}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325572097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019048, ['Order'] = 2, ['TalkerID'] = {7213051}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325572353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019048, ['Order'] = 3, ['TalkerID'] = {7213051}, ['Type'] = 0, }, }, 
        },
        [70019051] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325572609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019051, ['Order'] = 1, ['TalkerID'] = {7213057}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325572865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019051, ['Order'] = 2, ['TalkerID'] = {7213057}, ['Type'] = 0, }, }, 
        },
        [70019052] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325573121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019052, ['Order'] = 1, ['TalkerID'] = {7213058}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325573377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019052, ['Order'] = 2, ['TalkerID'] = {7213058}, ['Type'] = 0, }, }, 
        },
        [70019053] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325573633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019053, ['Order'] = 1, ['TalkerID'] = {7213064}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325573889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019053, ['Order'] = 2, ['TalkerID'] = {7213064}, ['Type'] = 0, }, }, 
        },
        [70019054] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325574657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019054, ['Order'] = 1, ['TalkerID'] = {7213066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325575169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019054, ['Order'] = 2, ['TalkerID'] = {7213066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325574913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019054, ['Order'] = 3, ['TalkerID'] = {7213066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325575425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019054, ['Order'] = 4, ['TalkerID'] = {7213066}, ['Type'] = 0, }, }, 
        },
        [70019055] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325575681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019055, ['Order'] = 1, ['TalkerID'] = {7213059}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325575937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019055, ['Order'] = 2, ['TalkerID'] = {7213060}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325576193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019055, ['Order'] = 3, ['TalkerID'] = {7213059}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325576449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019055, ['Order'] = 4, ['TalkerID'] = {7213060}, ['Type'] = 0, }, }, 
        },
        [70019056] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325576705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019056, ['Order'] = 1, ['TalkerID'] = {7213063}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325576961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019056, ['Order'] = 2, ['TalkerID'] = {7213062}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325577217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019056, ['Order'] = 3, ['TalkerID'] = {7213063}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325577473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019056, ['Order'] = 4, ['TalkerID'] = {7213062}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325577729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019056, ['Order'] = 5, ['TalkerID'] = {7213063}, ['Type'] = 0, }, }, 
        },
        [70019057] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325577985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019057, ['Order'] = 1, ['TalkerID'] = {7213065}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325578241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019057, ['Order'] = 2, ['TalkerID'] = {7213065}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325578497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019057, ['Order'] = 3, ['TalkerID'] = {7213065}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325578753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {4000}, ['ID'] = 70019057, ['Order'] = 4, ['TalkerID'] = {7213065}, ['Type'] = 0, }, }, 
        },
        [70019058] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325579009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019058, ['Order'] = 1, ['TalkerID'] = {7213069}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325579265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019058, ['Order'] = 2, ['TalkerID'] = {7213069}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325579521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019058, ['Order'] = 3, ['TalkerID'] = {7213069}, ['Type'] = 0, }, }, 
        },
        [70019059] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325579777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019059, ['Order'] = 1, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325580033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019059, ['Order'] = 2, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325580289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019059, ['Order'] = 3, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, 
        },
        [70019060] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325580545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019060, ['Order'] = 1, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325580801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019060, ['Order'] = 2, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325581057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019060, ['Order'] = 3, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325581313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019060, ['Order'] = 4, ['TalkerID'] = {7213070}, ['Type'] = 0, }, }, 
        },
        [70019061] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325581569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019061, ['Order'] = 1, ['TalkerID'] = {7213074}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325581825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019061, ['Order'] = 2, ['TalkerID'] = {7213074}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325582081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019061, ['Order'] = 3, ['TalkerID'] = {7213074}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325582337'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019061, ['Order'] = 4, ['TalkerID'] = {7213074}, ['Type'] = 0, }, }, 
        },
        [70019062] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325582593'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019062, ['Order'] = 1, ['TalkerID'] = {7213075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325582849'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019062, ['Order'] = 2, ['TalkerID'] = {7213075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325583105'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019062, ['Order'] = 3, ['TalkerID'] = {7213075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325583361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019062, ['Order'] = 4, ['TalkerID'] = {7213075}, ['Type'] = 0, }, }, 
        },
        [70019063] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325583617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019063, ['Order'] = 1, ['TalkerID'] = {7213061}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325583873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019063, ['Order'] = 2, ['TalkerID'] = {7213061}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325584129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019063, ['Order'] = 3, ['TalkerID'] = {7213061}, ['Type'] = 0, }, }, 
        },
        [70019064] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325584385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019064, ['Order'] = 1, ['TalkerID'] = {7213071}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325584641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019064, ['Order'] = 2, ['TalkerID'] = {7213072}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325584897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019064, ['Order'] = 3, ['TalkerID'] = {7213073}, ['Type'] = 0, }, }, 
        },
        [70019065] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325585153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019065, ['Order'] = 1, ['TalkerID'] = {7213083}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325585409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019065, ['Order'] = 2, ['TalkerID'] = {7213083}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325585665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019065, ['Order'] = 3, ['TalkerID'] = {7213083}, ['Type'] = 0, }, }, 
        },
        [70019066] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325585921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019066, ['Order'] = 1, ['TalkerID'] = {7213087}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325586177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019066, ['Order'] = 2, ['TalkerID'] = {7213087}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325586433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019066, ['Order'] = 3, ['TalkerID'] = {7213087}, ['Type'] = 0, }, }, 
        },
        [70019067] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325586689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019067, ['Order'] = 1, ['TalkerID'] = {7213084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325586945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019067, ['Order'] = 2, ['TalkerID'] = {7213084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325587201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019067, ['Order'] = 3, ['TalkerID'] = {7213084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325587457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019067, ['Order'] = 4, ['TalkerID'] = {7213084}, ['Type'] = 0, }, }, 
        },
        [70019068] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325587713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019068, ['Order'] = 1, ['TalkerID'] = {7213079}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325587969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019068, ['Order'] = 2, ['TalkerID'] = {7213079}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325588225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019068, ['Order'] = 3, ['TalkerID'] = {7213079}, ['Type'] = 0, }, }, 
        },
        [70019069] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325588481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019069, ['Order'] = 1, ['TalkerID'] = {7213088}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325588737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019069, ['Order'] = 2, ['TalkerID'] = {7213088}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325588993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019069, ['Order'] = 3, ['TalkerID'] = {7213088}, ['Type'] = 0, }, }, 
        },
        [70019070] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325589249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019070, ['Order'] = 1, ['TalkerID'] = {7213089}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325589505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019070, ['Order'] = 2, ['TalkerID'] = {7213089}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325589761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019070, ['Order'] = 3, ['TalkerID'] = {7213089}, ['Type'] = 0, }, }, 
        },
        [70019071] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325590017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019071, ['Order'] = 1, ['TalkerID'] = {7213078}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325590273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019071, ['Order'] = 2, ['TalkerID'] = {7213076}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325590529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019071, ['Order'] = 3, ['TalkerID'] = {7213078}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325590785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019071, ['Order'] = 4, ['TalkerID'] = {7213076}, ['Type'] = 0, }, }, 
        },
        [70019072] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325591041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019072, ['Order'] = 1, ['TalkerID'] = {7213077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325591297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019072, ['Order'] = 2, ['TalkerID'] = {7213077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325591553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019072, ['Order'] = 3, ['TalkerID'] = {7213077}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325591809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70019072, ['Order'] = 4, ['TalkerID'] = {7213077}, ['Type'] = 0, }, }, 
        },
        [70020001] = {{{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325592065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70020001, ['Order'] = 1, ['TalkerID'] = {7233008}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325592321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {3000}, ['ID'] = 70020001, ['Order'] = 2, ['TalkerID'] = {7233005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325592577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 6000, ['Duration'] = {3000}, ['ID'] = 70020001, ['Order'] = 3, ['TalkerID'] = {7233006}, ['Type'] = 0, }, }, 
        },
        [70020002] = {{{['BubbleGroupCD'] = 10000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325592833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70020002, ['Order'] = 1, ['TalkerID'] = {7233009}, ['Type'] = 0, }, }, 
        },
        [70020003] = {{{['BubbleGroupCD'] = 20000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325593089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70020003, ['Order'] = 1, ['TalkerID'] = {7233003}, ['Type'] = 0, }, }, 
        },
        [70021001] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325593345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021001, ['Order'] = 1, ['TalkerID'] = {7235008}, ['Type'] = 0, }, }, 
        },
        [70021002] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325593601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021002, ['Order'] = 1, ['TalkerID'] = {7235009}, ['Type'] = 0, }, }, 
        },
        [70021003] = {{{['BubbleGroupCD'] = 60000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325593857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021003, ['Order'] = 1, ['TalkerID'] = {7235015}, ['Type'] = 0, }, }, 
        },
        [70021004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325594113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021004, ['Order'] = 1, ['TalkerID'] = {7235021}, ['Type'] = 0, }, }, 
        },
        [70021005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325594369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021005, ['Order'] = 1, ['TalkerID'] = {7235022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325594625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021005, ['Order'] = 2, ['TalkerID'] = {7235022}, ['Type'] = 0, }, }, 
        },
        [70021006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325594881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021006, ['Order'] = 1, ['TalkerID'] = {7235023}, ['Type'] = 0, }, }, 
        },
        [70021007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325595137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021007, ['Order'] = 1, ['TalkerID'] = {7235024}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325595393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021007, ['Order'] = 2, ['TalkerID'] = {7235024}, ['Type'] = 0, }, }, 
        },
        [70021008] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325595649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021008, ['Order'] = 1, ['TalkerID'] = {7235025}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325595905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021008, ['Order'] = 2, ['TalkerID'] = {7235025}, ['Type'] = 0, }, }, 
        },
        [70021009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325596161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021009, ['Order'] = 1, ['TalkerID'] = {7235017}, ['Type'] = 0, }, }, 
        },
        [70021010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325596417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021010, ['Order'] = 1, ['TalkerID'] = {7235026}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325596673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021010, ['Order'] = 2, ['TalkerID'] = {7235027}, ['Type'] = 0, }, }, 
        },
        [70021011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325596929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021011, ['Order'] = 1, ['TalkerID'] = {7235029}, ['Type'] = 0, }, }, 
        },
        [70021012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325597185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021012, ['Order'] = 1, ['TalkerID'] = {7235031}, ['Type'] = 0, }, }, 
        },
        [70021013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325597441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021013, ['Order'] = 1, ['TalkerID'] = {7235032}, ['Type'] = 0, }, }, 
        },
        [70021014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325597697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021014, ['Order'] = 1, ['TalkerID'] = {7235033}, ['Type'] = 0, }, }, 
        },
        [70021015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325597953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021015, ['Order'] = 1, ['TalkerID'] = {7235036}, ['Type'] = 0, }, }, 
        },
        [70021016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325598209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021016, ['Order'] = 1, ['TalkerID'] = {7235037}, ['Type'] = 0, }, }, 
        },
        [70021017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325598465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021017, ['Order'] = 1, ['TalkerID'] = {7235038}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325598721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021017, ['Order'] = 2, ['TalkerID'] = {7235039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325598977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021017, ['Order'] = 3, ['TalkerID'] = {7235039}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325599233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021017, ['Order'] = 4, ['TalkerID'] = {7235038}, ['Type'] = 0, }, }, 
        },
        [70021018] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325599489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021018, ['Order'] = 1, ['TalkerID'] = {7235040}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325599745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021018, ['Order'] = 2, ['TalkerID'] = {7235040}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325600001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021018, ['Order'] = 3, ['TalkerID'] = {7235040}, ['Type'] = 0, }, }, 
        },
        [70021019] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325600257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021019, ['Order'] = 1, ['TalkerID'] = {7235041}, ['Type'] = 0, }, }, 
        },
        [70021020] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325600513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021020, ['Order'] = 1, ['TalkerID'] = {7235043}, ['Type'] = 0, }, }, 
        },
        [70021021] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325600769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021021, ['Order'] = 1, ['TalkerID'] = {7235044}, ['Type'] = 0, }, }, 
        },
        [70021022] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325601025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021022, ['Order'] = 1, ['TalkerID'] = {7235045}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325601281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021022, ['Order'] = 2, ['TalkerID'] = {7235045}, ['Type'] = 0, }, }, 
        },
        [70021023] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325601537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021023, ['Order'] = 1, ['TalkerID'] = {7235048}, ['Type'] = 0, }, }, 
        },
        [70021024] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325601793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021024, ['Order'] = 1, ['TalkerID'] = {7235053}, ['Type'] = 0, }, }, 
        },
        [70021025] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325602049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021025, ['Order'] = 1, ['TalkerID'] = {7235055}, ['Type'] = 0, }, }, 
        },
        [70021026] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325602305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021026, ['Order'] = 1, ['TalkerID'] = {7235057}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325602561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021026, ['Order'] = 2, ['TalkerID'] = {7235057}, ['Type'] = 0, }, }, 
        },
        [70021027] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325602817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021027, ['Order'] = 1, ['TalkerID'] = {7235056}, ['Type'] = 0, }, }, 
        },
        [70021028] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325603073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021028, ['Order'] = 1, ['TalkerID'] = {7235059}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325603329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021028, ['Order'] = 2, ['TalkerID'] = {7235059}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325603585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021028, ['Order'] = 3, ['TalkerID'] = {7235059}, ['Type'] = 0, }, }, 
        },
        [70021029] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325603841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021029, ['Order'] = 1, ['TalkerID'] = {7235060}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325604097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021029, ['Order'] = 2, ['TalkerID'] = {7235060}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325604353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021029, ['Order'] = 3, ['TalkerID'] = {7235060}, ['Type'] = 0, }, }, 
        },
        [70021030] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325604609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021030, ['Order'] = 1, ['TalkerID'] = {7235062}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325604865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021030, ['Order'] = 2, ['TalkerID'] = {7235062}, ['Type'] = 0, }, }, 
        },
        [70021031] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325605121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021031, ['Order'] = 1, ['TalkerID'] = {7235063}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325605377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021031, ['Order'] = 2, ['TalkerID'] = {7235064}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325605633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021031, ['Order'] = 3, ['TalkerID'] = {7235065}, ['Type'] = 0, }, }, 
        },
        [70021032] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325605889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021032, ['Order'] = 1, ['TalkerID'] = {7235066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325606145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021032, ['Order'] = 2, ['TalkerID'] = {7235066}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325606401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021032, ['Order'] = 3, ['TalkerID'] = {7235066}, ['Type'] = 0, }, }, 
        },
        [70021033] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325606657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021033, ['Order'] = 1, ['TalkerID'] = {7235067}, ['Type'] = 0, }, }, 
        },
        [70021034] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325606913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 70021034, ['Order'] = 1, ['TalkerID'] = {7235068}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325607169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021034, ['Order'] = 2, ['TalkerID'] = {7235068}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325607425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021034, ['Order'] = 3, ['TalkerID'] = {7235068}, ['Type'] = 0, }, }, 
        },
        [70021035] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325607681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021035, ['Order'] = 1, ['TalkerID'] = {7235082}, ['Type'] = 0, }, }, 
        },
        [70021036] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325607937'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021036, ['Order'] = 1, ['TalkerID'] = {7235069}, ['Type'] = 0, }, }, 
        },
        [70021037] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325608193'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021037, ['Order'] = 1, ['TalkerID'] = {7235072}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325608449'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021037, ['Order'] = 2, ['TalkerID'] = {7235073}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325608705'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021037, ['Order'] = 3, ['TalkerID'] = {7235075}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325608961'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021037, ['Order'] = 4, ['TalkerID'] = {7235074}, ['Type'] = 0, }, }, 
        },
        [70021038] = {{{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325609217'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021038, ['Order'] = 1, ['TalkerID'] = {7235088}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325609473'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021038, ['Order'] = 2, ['TalkerID'] = {7235089}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 50000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325609729'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021038, ['Order'] = 3, ['TalkerID'] = {7235090}, ['Type'] = 0, }, }, 
        },
        [70021039] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325609985'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021039, ['Order'] = 1, ['TalkerID'] = {7235087}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325610241'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021039, ['Order'] = 2, ['TalkerID'] = {7235093}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325610497'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021039, ['Order'] = 3, ['TalkerID'] = {7235087}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325610753'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021039, ['Order'] = 4, ['TalkerID'] = {7235093}, ['Type'] = 0, }, }, 
        },
        [70021040] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325611009'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021040, ['Order'] = 1, ['TalkerID'] = {7235076}, ['Type'] = 0, }, }, 
        },
        [70021041] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325611265'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021041, ['Order'] = 1, ['TalkerID'] = {7235084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325611521'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021041, ['Order'] = 2, ['TalkerID'] = {7235084}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325611777'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021041, ['Order'] = 3, ['TalkerID'] = {7235084}, ['Type'] = 0, }, }, 
        },
        [70021042] = {{{['Anim'] = {'Cat_Idle_4'}, ['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325612033'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021042, ['Order'] = 1, ['TalkerID'] = {7235071}, ['Type'] = 0, }, }, {{['Anim'] = {'Cat_Attack_L'}, ['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325612289'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021042, ['Order'] = 2, ['TalkerID'] = {7235071}, ['Type'] = 0, }, }, 
        },
        [70021043] = {{{['Anim'] = {'A_Dog_Border_Idle2'}, ['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325612545'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021043, ['Order'] = 1, ['TalkerID'] = {7235077}, ['Type'] = 0, }, }, 
        },
        [70021044] = {{{['Anim'] = {'Big_Anger'}, ['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325612801'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021044, ['Order'] = 1, ['TalkerID'] = {7235102}, ['Type'] = 0, }, }, 
        },
        [70021045] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325613057'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021045, ['Order'] = 1, ['TalkerID'] = {7235091}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325613313'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021045, ['Order'] = 2, ['TalkerID'] = {7235091}, ['Type'] = 0, }, }, 
        },
        [70021046] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325613569'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021046, ['Order'] = 1, ['TalkerID'] = {7235085}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325613825'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021046, ['Order'] = 2, ['TalkerID'] = {7235085}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325614081'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 2000, ['Duration'] = {4000}, ['ID'] = 70021046, ['Order'] = 3, ['TalkerID'] = {7235085}, ['Type'] = 0, }, }, 
        },
        [70022001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325615361'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022001, ['Order'] = 1, ['TalkerID'] = {7239001}, ['Type'] = 0, }, }, 
        },
        [70022002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325615617'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022002, ['Order'] = 1, ['TalkerID'] = {7239002}, ['Type'] = 0, }, }, 
        },
        [70022003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325615873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022003, ['Order'] = 1, ['TalkerID'] = {7239001}, ['Type'] = 0, }, }, 
        },
        [70022004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325616129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022004, ['Order'] = 1, ['TalkerID'] = {7239002}, ['Type'] = 0, }, }, 
        },
        [70022005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325616385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022005, ['Order'] = 1, ['TalkerID'] = {7239002}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325616641'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022005, ['Order'] = 2, ['TalkerID'] = {7239001}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325616897'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022005, ['Order'] = 3, ['TalkerID'] = {7239003}, ['Type'] = 0, }, }, 
        },
        [70022006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325617153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70022006, ['Order'] = 1, ['TalkerID'] = {7239015}, ['Type'] = 0, }, }, 
        },
        [70022007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325617409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022007, ['Order'] = 1, ['TalkerID'] = {7239021}, ['Type'] = 0, }, }, 
        },
        [70022008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325459201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70022008, ['Order'] = 1, ['TalkerID'] = {7231033}, ['Type'] = 0, }, }, 
        },
        [70022009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325617921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70022009, ['Order'] = 1, ['TalkerID'] = {7239020}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325618177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022009, ['Order'] = 2, ['TalkerID'] = {7231035}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325618433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022009, ['Order'] = 3, ['TalkerID'] = {7239020}, ['Type'] = 0, }, }, 
        },
        [70022010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325618689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70022010, ['Order'] = 1, ['TalkerID'] = {7231040}, ['Type'] = 0, }, }, 
        },
        [70022011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325618945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {3000}, ['ID'] = 70022011, ['Order'] = 1, ['TalkerID'] = {7231041}, ['Type'] = 0, }, }, 
        },
        [70022012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325619201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022012, ['Order'] = 1, ['TalkerID'] = {7239016}, ['Type'] = 0, }, }, 
        },
        [70022013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325619457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70022013, ['Order'] = 1, ['TalkerID'] = {7231005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325619713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022013, ['Order'] = 2, ['TalkerID'] = {7231005}, ['Type'] = 0, }, }, 
        },
        [70022014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325619969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70022014, ['Order'] = 1, ['TalkerID'] = {7231007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325620225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022014, ['Order'] = 2, ['TalkerID'] = {7231007}, ['Type'] = 0, }, }, 
        },
        [70022015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325620481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022015, ['Order'] = 1, ['TalkerID'] = {7231022}, ['Type'] = 0, }, }, 
        },
        [70022016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325620737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70022016, ['Order'] = 1, ['TalkerID'] = {7239023}, ['Type'] = 0, }, }, 
        },
        [70023001] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325620993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70023001, ['Order'] = 1, ['TalkerID'] = {7237001}, ['Type'] = 0, }, }, 
        },
        [70023002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325621249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70023002, ['Order'] = 1, ['TalkerID'] = {7237003}, ['Type'] = 0, }, }, 
        },
        [70023003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325621505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3200}, ['ID'] = 70023003, ['Order'] = 1, ['TalkerID'] = {7237001}, ['Type'] = 0, }, }, 
        },
        [70023004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325621761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70023004, ['Order'] = 1, ['TalkerID'] = {-1}, ['Type'] = 0, }, }, 
        },
        [70023005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325622017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70023005, ['Order'] = 1, ['TalkerID'] = {7237003}, ['Type'] = 0, }, }, 
        },
        [70023006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325622273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3500}, ['ID'] = 70023006, ['Order'] = 1, ['TalkerID'] = {7237001}, ['Type'] = 0, }, }, 
        },
        [70024001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325622529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024001, ['Order'] = 1, ['TalkerID'] = {7241003}, ['Type'] = 0, }, }, 
        },
        [70024002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325622785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70024002, ['Order'] = 1, ['TalkerID'] = {7241007}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325623041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {3000}, ['ID'] = 70024002, ['Order'] = 2, ['TalkerID'] = {7241007}, ['Type'] = 0, }, }, 
        },
        [70024003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325623297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024003, ['Order'] = 1, ['TalkerID'] = {7241004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325623553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024003, ['Order'] = 2, ['TalkerID'] = {7241005}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325623809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024003, ['Order'] = 3, ['TalkerID'] = {7241004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325624065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024003, ['Order'] = 4, ['TalkerID'] = {7241004}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325624321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 1000, ['Duration'] = {5000}, ['ID'] = 70024003, ['Order'] = 5, ['TalkerID'] = {7241005}, ['Type'] = 0, }, }, 
        },
        [70025001] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325624577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025001, ['Order'] = 1, ['TalkerID'] = {7242001}, ['Type'] = 0, }, }, 
        },
        [70025002] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325624833'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025002, ['Order'] = 1, ['TalkerID'] = {7242002}, ['Type'] = 0, }, }, 
        },
        [70025003] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325625089'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025003, ['Order'] = 1, ['TalkerID'] = {7242004}, ['Type'] = 0, }, }, 
        },
        [70025004] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325625345'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025004, ['Order'] = 1, ['TalkerID'] = {7242005}, ['Type'] = 0, }, }, 
        },
        [70025005] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325625601'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025005, ['Order'] = 1, ['TalkerID'] = {7242006}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325625857'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025005, ['Order'] = 2, ['TalkerID'] = {7242006}, ['Type'] = 0, }, }, 
        },
        [70025006] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325626113'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025006, ['Order'] = 1, ['TalkerID'] = {7242007}, ['Type'] = 0, }, }, 
        },
        [70025007] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325626369'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025007, ['Order'] = 1, ['TalkerID'] = {7242008}, ['Type'] = 0, }, }, 
        },
        [70025008] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325626625'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025008, ['Order'] = 1, ['TalkerID'] = {7242010}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325626881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025008, ['Order'] = 2, ['TalkerID'] = {7242011}, ['Type'] = 0, }, }, 
        },
        [70025009] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325627137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025009, ['Order'] = 1, ['TalkerID'] = {7242012}, ['Type'] = 0, }, }, 
        },
        [70025010] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325627393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025010, ['Order'] = 1, ['TalkerID'] = {7242013}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325627649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025010, ['Order'] = 2, ['TalkerID'] = {7242014}, ['Type'] = 0, }, }, 
        },
        [70025011] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325627905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025011, ['Order'] = 1, ['TalkerID'] = {7242015}, ['Type'] = 0, }, }, 
        },
        [70025012] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325628161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025012, ['Order'] = 1, ['TalkerID'] = {7242018}, ['Type'] = 0, }, }, 
        },
        [70025013] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325628417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025013, ['Order'] = 1, ['TalkerID'] = {7242019}, ['Type'] = 0, }, }, 
        },
        [70025014] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325628673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025014, ['Order'] = 1, ['TalkerID'] = {7242020}, ['Type'] = 0, }, }, 
        },
        [70025015] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325628929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025015, ['Order'] = 1, ['TalkerID'] = {7242021}, ['Type'] = 0, }, }, 
        },
        [70025016] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325629185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025016, ['Order'] = 1, ['TalkerID'] = {7242022}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325629441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025016, ['Order'] = 2, ['TalkerID'] = {7242023}, ['Type'] = 0, }, }, 
        },
        [70025017] = {{{['BubbleGroupCD'] = 30000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325629697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025017, ['Order'] = 1, ['TalkerID'] = {7242025}, ['Type'] = 0, }, }, 
        },
        [70025018] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325629953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {10000}, ['ID'] = 70025018, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025019] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325630209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {10000}, ['ID'] = 70025019, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025020] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325630465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {10000}, ['ID'] = 70025020, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025021] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325630721'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {10000}, ['ID'] = 70025021, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025022] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325630977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025022, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325631233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025023, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325631489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025024, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025025] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325631745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025025, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325632001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70025026, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025027] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325632257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 70025027, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025100] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325632513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025100, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025101] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325632769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025101, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025102] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325633025'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025102, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025103] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325633281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025103, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025104] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325633537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025104, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025105] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325633793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025105, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025106] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325634049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025106, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025107] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325634305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025107, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025110] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325634561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025110, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025111] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325634817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025111, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025112] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325635073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025112, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025113] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325635329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025113, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025114] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325635585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025114, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025115] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325635841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025115, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025116] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325636097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025116, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025117] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325636353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025117, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025118] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325636609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025118, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025119] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325636865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025119, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025120] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325637121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025120, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025121] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325637377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025121, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025122] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325637633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025122, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025123] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325637889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025123, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025124] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325638145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025124, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025125] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325638401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025125, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025126] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325638657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 70025126, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [70025127] = {{{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325638913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025127, ['Order'] = 1, ['TalkerID'] = {7212086}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325639169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025127, ['Order'] = 2, ['TalkerID'] = {7212086}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 3000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325639425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 500, ['Duration'] = {5000}, ['ID'] = 70025127, ['Order'] = 3, ['TalkerID'] = {7242236}, ['Type'] = 0, }, }, 
        },
        [79000002] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325263873'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000002, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325264129'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000003, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325264385'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000004, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000005] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {'huh'}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000005, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000006] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {'brah'}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000006, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325265153'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000007, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325265409'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 79000008, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000010] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325265665'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000010, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 1, }, }, 
        },
        [79000011] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325265921'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000011, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000012] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325266177'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000012, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000013] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325266433'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000013, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000014] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325266689'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000014, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000015] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325266945'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000015, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000016] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325267201'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000016, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000017] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325267457'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000017, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000018] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325267713'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000018, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000019] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325267969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000019, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_01'}, }, }, 
        },
        [79000020] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268225'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000020, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_02'}, }, }, 
        },
        [79000021] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268481'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000021, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_03'}, }, }, 
        },
        [79000022] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268737'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000022, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_04'}, }, }, 
        },
        [79000023] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268993'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000023, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_05'}, }, }, 
        },
        [79000024] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269249'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000024, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, ['Voice'] = {'Play_Vo_NPC_Carter_Bubble_06'}, }, }, 
        },
        [79000025] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000025, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000026] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269761'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000026, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000027] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270017'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000027, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000028] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270273'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000028, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000029] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270529'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000029, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000030] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325270785'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000030, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000031] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271041'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000031, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000032] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271297'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000032, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000033] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271553'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000033, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000034] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325271809'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000034, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000035] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325272065'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {8000}, ['ID'] = 79000035, ['Order'] = 1, ['TalkerID'] = {7240005}, ['Type'] = 0, }, }, 
        },
        [79000036] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325272321'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 79000036, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000037] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325272577'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000037, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000038] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {'Cthulhu Fhtagn!'}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2000}, ['ID'] = 79000038, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000039] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325267969'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268225'),}, ['Condition'] = {}, ['CutID'] = 2, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268481'),}, ['Condition'] = {}, ['CutID'] = 3, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268737'),}, ['Condition'] = {}, ['CutID'] = 4, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325268993'),}, ['Condition'] = {}, ['CutID'] = 5, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, {['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269249'),}, ['Condition'] = {}, ['CutID'] = 6, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000039, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, }, 
        },
        [79000040] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325269505'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000040, ['Order'] = 1, ['TalkerID'] = {7400001}, ['Type'] = 0, }, }, 
        },
        [79000041] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325274881'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000041, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000042] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325275137'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000042, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000043] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325275393'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {6500}, ['ID'] = 79000043, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000044] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325275649'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {6500}, ['ID'] = 79000044, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000045] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325275905'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {6500}, ['ID'] = 79000045, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000046] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325276161'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000046, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000047] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325276417'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000047, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000048] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325276673'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000048, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000049] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325276929'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {12000}, ['ID'] = 79000049, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000050] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325277185'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {12000}, ['ID'] = 79000050, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000051] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325277441'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000051, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000052] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325277697'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000052, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000053] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325277953'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {2500}, ['ID'] = 79000053, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000054] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325278209'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000054, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000100] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325281281'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000100, ['Order'] = 1, ['TalkerID'] = {7240011}, ['Type'] = 0, }, }, 
        },
        [79000101] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325281537'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000101, ['Order'] = 1, ['TalkerID'] = {7240011}, ['Type'] = 0, }, }, 
        },
        [79000102] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325281793'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000102, ['Order'] = 1, ['TalkerID'] = {7240050}, ['Type'] = 0, }, }, 
        },
        [79000103] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325282049'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000103, ['Order'] = 1, ['TalkerID'] = {7240050}, ['Type'] = 0, }, }, 
        },
        [79000104] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325282305'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000104, ['Order'] = 1, ['TalkerID'] = {7240027}, ['Type'] = 0, }, }, 
        },
        [79000200] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325282561'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000200, ['Order'] = 1, ['TalkerID'] = {7240051}, ['Type'] = 0, }, }, 
        },
        [79000201] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325282817'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000201, ['Order'] = 1, ['TalkerID'] = {7240051}, ['Type'] = 0, }, }, {{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325283073'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000201, ['Order'] = 2, ['TalkerID'] = {7240051}, ['Type'] = 0, }, }, 
        },
        [79000202] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325283329'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000202, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000203] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325283585'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000203, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000204] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325283841'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000204, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000205] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325284097'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000205, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000206] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325284353'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000206, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000207] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325284609'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000207, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000208] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325284865'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000208, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000209] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325285121'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000209, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000210] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325285377'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {3000}, ['ID'] = 79000210, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000211] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325285633'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000211, ['Order'] = 1, ['TalkerID'] = {7240051}, ['Type'] = 0, }, }, 
        },
        [79000212] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325285889'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000212, ['Order'] = 1, ['TalkerID'] = {7240051}, ['Type'] = 0, }, }, 
        },
        [79000213] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325286145'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000213, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000214] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325286401'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000214, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000215] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325286657'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000215, ['Order'] = 1, ['TalkerID'] = {7240003}, ['Type'] = 0, }, }, 
        },
        [79000216] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325286913'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000216, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000217] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325287169'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000217, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000218] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325287425'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000218, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79000219] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325287681'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79000219, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79001000] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325278465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {10000}, ['ID'] = 79001000, ['Order'] = 1, ['TalkerID'] = {7240001}, ['Type'] = 0, }, }, 
        },
        [79001001] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325278465'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {10000}, ['ID'] = 79001001, ['Order'] = 1, ['TalkerID'] = {7240002}, ['Type'] = 0, }, }, 
        },
        [79001002] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325278977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {10000}, ['ID'] = 79001002, ['Order'] = 1, ['TalkerID'] = {7240014}, ['Type'] = 0, }, }, 
        },
        [79001003] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325279233'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1200}, ['ID'] = 79001003, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79001004] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325279489'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1200}, ['ID'] = 79001004, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79001005] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325279745'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79001005, ['Order'] = 1, ['TalkerID'] = {7240005}, ['Type'] = 0, }, }, 
        },
        [79001006] = {{{['BubbleGroupCD'] = 15000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325280001'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {5000}, ['ID'] = 79001006, ['Order'] = 1, ['TalkerID'] = {7240006}, ['Type'] = 0, }, }, 
        },
        [79001007] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325280257'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {4000}, ['ID'] = 79001007, ['Order'] = 1, ['TalkerID'] = {7240006}, ['Type'] = 0, }, }, 
        },
        [79001008] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325280513'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1200}, ['ID'] = 79001008, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79001009] = {{{['BubbleGroupCD'] = 0, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325280769'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 0, ['Duration'] = {1200}, ['ID'] = 79001009, ['Order'] = 1, ['TalkerID'] = {}, ['Type'] = 0, }, }, 
        },
        [79001010] = {{{['BubbleGroupCD'] = 5000, ['BubbleText'] = {Game.TableDataManager:GetLangStr('str_56970325278977'),}, ['Condition'] = {}, ['CutID'] = 1, ['Delay'] = 3000, ['Duration'] = {10000}, ['ID'] = 79001010, ['Order'] = 1, ['TalkerID'] = {7240107}, ['Type'] = 0, }, }, 
        },
    }
}
return TopData