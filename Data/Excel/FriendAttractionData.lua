--
-- 表名: $Friend_好友.xlsx  页名：$FriendAttractionData_好友度梯度
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["Level"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 20,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[2] = {
			["Id"] = 2,
			["Level"] = 2,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 60,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[3] = {
			["Id"] = 3,
			["Level"] = 3,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 100,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[4] = {
			["Id"] = 4,
			["Level"] = 4,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 140,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[5] = {
			["Id"] = 5,
			["Level"] = 5,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[6] = {
			["Id"] = 6,
			["Level"] = 6,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 220,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[7] = {
			["Id"] = 7,
			["Level"] = 7,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 260,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[8] = {
			["Id"] = 8,
			["Level"] = 8,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 300,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[9] = {
			["Id"] = 9,
			["Level"] = 9,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 340,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[10] = {
			["Id"] = 10,
			["Level"] = 10,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841182208'),
			["Range"] = 380,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[11] = {
			["Id"] = 11,
			["Level"] = 11,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 540,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[12] = {
			["Id"] = 12,
			["Level"] = 12,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 700,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[13] = {
			["Id"] = 13,
			["Level"] = 13,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 860,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[14] = {
			["Id"] = 14,
			["Level"] = 14,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1020,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[15] = {
			["Id"] = 15,
			["Level"] = 15,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[16] = {
			["Id"] = 16,
			["Level"] = 16,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1340,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[17] = {
			["Id"] = 17,
			["Level"] = 17,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1500,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[18] = {
			["Id"] = 18,
			["Level"] = 18,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1660,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[19] = {
			["Id"] = 19,
			["Level"] = 19,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1820,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[20] = {
			["Id"] = 20,
			["Level"] = 20,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841184768'),
			["Range"] = 1980,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[21] = {
			["Id"] = 21,
			["Level"] = 21,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 2620,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[22] = {
			["Id"] = 22,
			["Level"] = 22,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 3260,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[23] = {
			["Id"] = 23,
			["Level"] = 23,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 3900,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[24] = {
			["Id"] = 24,
			["Level"] = 24,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 4540,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[25] = {
			["Id"] = 25,
			["Level"] = 25,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 5180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[26] = {
			["Id"] = 26,
			["Level"] = 26,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 5820,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[27] = {
			["Id"] = 27,
			["Level"] = 27,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 6460,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[28] = {
			["Id"] = 28,
			["Level"] = 28,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 7100,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[29] = {
			["Id"] = 29,
			["Level"] = 29,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 7740,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[30] = {
			["Id"] = 30,
			["Level"] = 30,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841187328'),
			["Range"] = 8380,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[31] = {
			["Id"] = 31,
			["Level"] = 31,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 12220,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[32] = {
			["Id"] = 32,
			["Level"] = 32,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 16060,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[33] = {
			["Id"] = 33,
			["Level"] = 33,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 19900,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[34] = {
			["Id"] = 34,
			["Level"] = 34,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 23740,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[35] = {
			["Id"] = 35,
			["Level"] = 35,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 27580,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[36] = {
			["Id"] = 36,
			["Level"] = 36,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 31420,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[37] = {
			["Id"] = 37,
			["Level"] = 37,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 35260,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[38] = {
			["Id"] = 38,
			["Level"] = 38,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 39100,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[39] = {
			["Id"] = 39,
			["Level"] = 39,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 42940,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[40] = {
			["Id"] = 40,
			["Level"] = 40,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841189888'),
			["Range"] = 46780,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[41] = {
			["Id"] = 41,
			["Level"] = 41,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 62140,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[42] = {
			["Id"] = 42,
			["Level"] = 42,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 77500,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[43] = {
			["Id"] = 43,
			["Level"] = 43,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 92860,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[44] = {
			["Id"] = 44,
			["Level"] = 44,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 108220,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[45] = {
			["Id"] = 45,
			["Level"] = 45,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 123580,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[46] = {
			["Id"] = 46,
			["Level"] = 46,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 138940,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[47] = {
			["Id"] = 47,
			["Level"] = 47,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 154300,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[48] = {
			["Id"] = 48,
			["Level"] = 48,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 169660,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[49] = {
			["Id"] = 49,
			["Level"] = 49,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 185020,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[50] = {
			["Id"] = 50,
			["Level"] = 50,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841192448'),
			["Range"] = 200380,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[51] = {
			["Id"] = 51,
			["Level"] = 51,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 238780,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[52] = {
			["Id"] = 52,
			["Level"] = 52,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 277180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[53] = {
			["Id"] = 53,
			["Level"] = 53,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 315580,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[54] = {
			["Id"] = 54,
			["Level"] = 54,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 353980,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[55] = {
			["Id"] = 55,
			["Level"] = 55,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 392380,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[56] = {
			["Id"] = 56,
			["Level"] = 56,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 430780,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[57] = {
			["Id"] = 57,
			["Level"] = 57,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 469180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[58] = {
			["Id"] = 58,
			["Level"] = 58,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 507580,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[59] = {
			["Id"] = 59,
			["Level"] = 59,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 545980,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[60] = {
			["Id"] = 60,
			["Level"] = 60,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841195008'),
			["Range"] = 584380,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[61] = {
			["Id"] = 61,
			["Level"] = 61,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 657340,
			["Notify"] = 1,
			["Icon"] = "",
			["Color"] = "",
		},
		[62] = {
			["Id"] = 62,
			["Level"] = 62,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 730300,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[63] = {
			["Id"] = 63,
			["Level"] = 63,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 803260,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[64] = {
			["Id"] = 64,
			["Level"] = 64,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 876220,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[65] = {
			["Id"] = 65,
			["Level"] = 65,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 949180,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[66] = {
			["Id"] = 66,
			["Level"] = 66,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 1022140,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[67] = {
			["Id"] = 67,
			["Level"] = 67,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 1095100,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[68] = {
			["Id"] = 68,
			["Level"] = 68,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 1168060,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[69] = {
			["Id"] = 69,
			["Level"] = 69,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 1241020,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
		[70] = {
			["Id"] = 70,
			["Level"] = 70,
			["Name"] = Game.TableDataManager:GetLangStr('str_25289841197568'),
			["Range"] = 1314520,
			["Notify"] = 0,
			["Icon"] = "",
			["Color"] = "",
		},
	},
}

return TopData
