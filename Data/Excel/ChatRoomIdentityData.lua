--
-- 表名: ChatRoomType2Identity { RoomType: { Order:IdentityID } }
--

local TopData = {
    ChatRoomType2Identity = {{[0] = 1, }, {[0] = 1, }, {[0] = 1, }, {[0] = 1, }, {[0] = 1, }, {[0] = 3, [1] = 4, [2] = 5, [3] = 5, [4] = 5, [5] = 5, [6] = 5, [7] = 5, [8] = 5, }, {[0] = 3, [1] = 2, [2] = 2, [3] = 2, [4] = 2, [5] = 2, [6] = 2, [7] = 2, [8] = 2, }, {}, {[0] = 8, [1] = 9, [2] = 9, [3] = 9, [4] = 9, [5] = 9, [6] = 9, [7] = 9, [8] = 9, }, {[0] = 6, [1] = 7, [2] = 7, [3] = 7, [4] = 7, [5] = 7, [6] = 7, [7] = 7, [8] = 7, }, 
    },
    data = {
        [1] = {
            ['BelongRoomID'] = {1, 2, 3, 4, 5}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 1, 
            ['IsRoomOwner'] = true, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363446784'),
        },
        [2] = {
            ['BelongRoomID'] = {7}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 8, 
            ['ID'] = 2, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {1, 2, 3, 4, 5, 6, 7, 8}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead01_Sprite.UI_Chat_Img_TagHead01_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363447040'),
        },
        [3] = {
            ['BelongRoomID'] = {6, 7}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 3, 
            ['IsRoomOwner'] = true, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_38553505569792'),
        },
        [4] = {
            ['BelongRoomID'] = {6}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 4, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {1}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead01_Sprite.UI_Chat_Img_TagHead01_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363447552'),
        },
        [5] = {
            ['BelongRoomID'] = {6}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 8, 
            ['ID'] = 5, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {2, 3, 4, 5, 6, 7, 8}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead01_Sprite.UI_Chat_Img_TagHead01_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363447808'),
        },
        [6] = {
            ['BelongRoomID'] = {10}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 6, 
            ['IsRoomOwner'] = true, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363448064'),
        },
        [7] = {
            ['BelongRoomID'] = {10}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 8, 
            ['ID'] = 7, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {1, 2, 3, 4, 5, 6, 7, 8}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead01_Sprite.UI_Chat_Img_TagHead01_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363448320'),
        },
        [8] = {
            ['BelongRoomID'] = {9}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 8, 
            ['IsRoomOwner'] = true, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363448576'),
        },
        [9] = {
            ['BelongRoomID'] = {9}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 8, 
            ['ID'] = 9, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {1, 2, 3, 4, 5, 6, 7, 8}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead01_Sprite.UI_Chat_Img_TagHead01_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363448832'),
        },
        [10] = {
            ['BelongRoomID'] = {6, 7, 8, 9, 10}, 
            ['CanRoomOwnerAssign'] = true, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 1, 
            ['ID'] = 10, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead04_Sprite.UI_Chat_Img_TagHead04_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363449088'),
        },
        [11] = {
            ['BelongRoomID'] = {6, 7, 8, 9, 10}, 
            ['CanRoomOwnerAssign'] = true, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 2, 
            ['ID'] = 11, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead03_Sprite.UI_Chat_Img_TagHead03_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363449344'),
        },
        [12] = {
            ['BelongRoomID'] = {6, 7, 8, 9, 10}, 
            ['CanRoomOwnerAssign'] = true, 
            ['CanRoomOwnerRename'] = true, 
            ['CountLimit'] = 2, 
            ['ID'] = 12, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363449600'),
        },
        [13] = {
            ['BelongRoomID'] = {6, 7, 8, 9, 10}, 
            ['CanRoomOwnerAssign'] = true, 
            ['CanRoomOwnerRename'] = true, 
            ['CountLimit'] = 2, 
            ['ID'] = 13, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead02_Sprite.UI_Chat_Img_TagHead02_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_5498363449856'),
        },
        [14] = {
            ['BelongRoomID'] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}, 
            ['CanRoomOwnerAssign'] = false, 
            ['CanRoomOwnerRename'] = false, 
            ['CountLimit'] = 0, 
            ['ID'] = 14, 
            ['IsRoomOwner'] = false, 
            ['MicOrderDefinition'] = {}, 
            ['RoleTagBoard'] = '/Game/Arts/UI_2/Resource/Chat_2/Atlas/Sprite01/UI_Chat_Img_TagHead05_Sprite.UI_Chat_Img_TagHead05_Sprite', 
            ['TypeName'] = Game.TableDataManager:GetLangStr('str_39033468264192'),
        },
    }
}
return TopData