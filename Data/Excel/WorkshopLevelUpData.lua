--
-- 表名: $Manorlevelup_庄园升级表.xlsx  页名：$WorkshopLevelUp_工坊升级
--

local TopData = {
	data = {
		[1001] = {
			["ID"] = 1001,
			["buildingLevel"] = 1,
			["roleLevelLimit"] = 1,
			["levelUpConsume"] = 300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801409')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483236865'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324001,
			["jobLimit"] = 1,
			["ProductLimit"] = 1,
		},
		[1002] = {
			["ID"] = 1002,
			["buildingLevel"] = 2,
			["roleLevelLimit"] = 2,
			["levelUpConsume"] = 700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801665')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237121'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324002,
			["jobLimit"] = 1,
			["ProductLimit"] = 2,
		},
		[1003] = {
			["ID"] = 1003,
			["buildingLevel"] = 3,
			["roleLevelLimit"] = 3,
			["levelUpConsume"] = 1400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801921')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324003,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[1004] = {
			["ID"] = 1004,
			["buildingLevel"] = 4,
			["roleLevelLimit"] = 4,
			["levelUpConsume"] = 2200,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802177')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237633'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324004,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[1005] = {
			["ID"] = 1005,
			["buildingLevel"] = 5,
			["roleLevelLimit"] = 5,
			["levelUpConsume"] = 5400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802433')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324005,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[1006] = {
			["ID"] = 1006,
			["buildingLevel"] = 6,
			["roleLevelLimit"] = 6,
			["levelUpConsume"] = 9900,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802689')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483238145'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324006,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[1007] = {
			["ID"] = 1007,
			["buildingLevel"] = 7,
			["roleLevelLimit"] = 7,
			["levelUpConsume"] = 16600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802945')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324007,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1008] = {
			["ID"] = 1008,
			["buildingLevel"] = 8,
			["roleLevelLimit"] = 8,
			["levelUpConsume"] = 25600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803201')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324008,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1009] = {
			["ID"] = 1009,
			["buildingLevel"] = 9,
			["roleLevelLimit"] = 9,
			["levelUpConsume"] = 34300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803457')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324009,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1010] = {
			["ID"] = 1010,
			["buildingLevel"] = 10,
			["roleLevelLimit"] = 10,
			["levelUpConsume"] = 45700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803713')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324010,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1011] = {
			["ID"] = 1011,
			["buildingLevel"] = 11,
			["roleLevelLimit"] = 11,
			["levelUpConsume"] = 67500,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803969')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324011,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1012] = {
			["ID"] = 1012,
			["buildingLevel"] = 12,
			["roleLevelLimit"] = 12,
			["levelUpConsume"] = 87700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804225')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324012,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1013] = {
			["ID"] = 1013,
			["buildingLevel"] = 13,
			["roleLevelLimit"] = 13,
			["levelUpConsume"] = 110000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804481')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324013,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1014] = {
			["ID"] = 1014,
			["buildingLevel"] = 14,
			["roleLevelLimit"] = 14,
			["levelUpConsume"] = 125700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804737')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324014,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[1015] = {
			["ID"] = 1015,
			["buildingLevel"] = 15,
			["roleLevelLimit"] = 15,
			["levelUpConsume"] = 145000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804993')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324015,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2001] = {
			["ID"] = 2001,
			["buildingLevel"] = 1,
			["roleLevelLimit"] = 1,
			["levelUpConsume"] = 300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801409')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483236865'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324101,
			["jobLimit"] = 1,
			["ProductLimit"] = 1,
		},
		[2002] = {
			["ID"] = 2002,
			["buildingLevel"] = 2,
			["roleLevelLimit"] = 2,
			["levelUpConsume"] = 700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801665')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237121'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324102,
			["jobLimit"] = 1,
			["ProductLimit"] = 2,
		},
		[2003] = {
			["ID"] = 2003,
			["buildingLevel"] = 3,
			["roleLevelLimit"] = 3,
			["levelUpConsume"] = 1400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801921')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324103,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[2004] = {
			["ID"] = 2004,
			["buildingLevel"] = 4,
			["roleLevelLimit"] = 4,
			["levelUpConsume"] = 2200,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802177')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237633'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324104,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[2005] = {
			["ID"] = 2005,
			["buildingLevel"] = 5,
			["roleLevelLimit"] = 5,
			["levelUpConsume"] = 5400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802433')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324105,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[2006] = {
			["ID"] = 2006,
			["buildingLevel"] = 6,
			["roleLevelLimit"] = 6,
			["levelUpConsume"] = 9900,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802689')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483238145'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324106,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[2007] = {
			["ID"] = 2007,
			["buildingLevel"] = 7,
			["roleLevelLimit"] = 7,
			["levelUpConsume"] = 16600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802945')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324107,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2008] = {
			["ID"] = 2008,
			["buildingLevel"] = 8,
			["roleLevelLimit"] = 8,
			["levelUpConsume"] = 25600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803201')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324108,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2009] = {
			["ID"] = 2009,
			["buildingLevel"] = 9,
			["roleLevelLimit"] = 9,
			["levelUpConsume"] = 34300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803457')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324109,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2010] = {
			["ID"] = 2010,
			["buildingLevel"] = 10,
			["roleLevelLimit"] = 10,
			["levelUpConsume"] = 45700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803713')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324110,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2011] = {
			["ID"] = 2011,
			["buildingLevel"] = 11,
			["roleLevelLimit"] = 11,
			["levelUpConsume"] = 67500,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803969')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324111,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2012] = {
			["ID"] = 2012,
			["buildingLevel"] = 12,
			["roleLevelLimit"] = 12,
			["levelUpConsume"] = 87700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804225')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324112,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2013] = {
			["ID"] = 2013,
			["buildingLevel"] = 13,
			["roleLevelLimit"] = 13,
			["levelUpConsume"] = 110000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804481')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324113,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2014] = {
			["ID"] = 2014,
			["buildingLevel"] = 14,
			["roleLevelLimit"] = 14,
			["levelUpConsume"] = 125700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804737')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324114,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[2015] = {
			["ID"] = 2015,
			["buildingLevel"] = 15,
			["roleLevelLimit"] = 15,
			["levelUpConsume"] = 145000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804993')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324115,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3001] = {
			["ID"] = 3001,
			["buildingLevel"] = 1,
			["roleLevelLimit"] = 1,
			["levelUpConsume"] = 300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801409')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483236865'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324201,
			["jobLimit"] = 1,
			["ProductLimit"] = 1,
		},
		[3002] = {
			["ID"] = 3002,
			["buildingLevel"] = 2,
			["roleLevelLimit"] = 2,
			["levelUpConsume"] = 700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801665')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237121'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324202,
			["jobLimit"] = 1,
			["ProductLimit"] = 2,
		},
		[3003] = {
			["ID"] = 3003,
			["buildingLevel"] = 3,
			["roleLevelLimit"] = 3,
			["levelUpConsume"] = 1400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801921')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324203,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[3004] = {
			["ID"] = 3004,
			["buildingLevel"] = 4,
			["roleLevelLimit"] = 4,
			["levelUpConsume"] = 2200,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802177')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237633'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324204,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[3005] = {
			["ID"] = 3005,
			["buildingLevel"] = 5,
			["roleLevelLimit"] = 5,
			["levelUpConsume"] = 5400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802433')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324205,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[3006] = {
			["ID"] = 3006,
			["buildingLevel"] = 6,
			["roleLevelLimit"] = 6,
			["levelUpConsume"] = 9900,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802689')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483238145'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324206,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[3007] = {
			["ID"] = 3007,
			["buildingLevel"] = 7,
			["roleLevelLimit"] = 7,
			["levelUpConsume"] = 16600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802945')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324207,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3008] = {
			["ID"] = 3008,
			["buildingLevel"] = 8,
			["roleLevelLimit"] = 8,
			["levelUpConsume"] = 25600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803201')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324208,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3009] = {
			["ID"] = 3009,
			["buildingLevel"] = 9,
			["roleLevelLimit"] = 9,
			["levelUpConsume"] = 34300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803457')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324209,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3010] = {
			["ID"] = 3010,
			["buildingLevel"] = 10,
			["roleLevelLimit"] = 10,
			["levelUpConsume"] = 45700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803713')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324210,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3011] = {
			["ID"] = 3011,
			["buildingLevel"] = 11,
			["roleLevelLimit"] = 11,
			["levelUpConsume"] = 67500,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803969')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324211,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3012] = {
			["ID"] = 3012,
			["buildingLevel"] = 12,
			["roleLevelLimit"] = 12,
			["levelUpConsume"] = 87700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804225')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324212,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3013] = {
			["ID"] = 3013,
			["buildingLevel"] = 13,
			["roleLevelLimit"] = 13,
			["levelUpConsume"] = 110000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804481')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324213,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3014] = {
			["ID"] = 3014,
			["buildingLevel"] = 14,
			["roleLevelLimit"] = 14,
			["levelUpConsume"] = 125700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804737')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324214,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[3015] = {
			["ID"] = 3015,
			["buildingLevel"] = 15,
			["roleLevelLimit"] = 15,
			["levelUpConsume"] = 145000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804993')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324215,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4001] = {
			["ID"] = 4001,
			["buildingLevel"] = 1,
			["roleLevelLimit"] = 1,
			["levelUpConsume"] = 300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801409')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483236865'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324301,
			["jobLimit"] = 1,
			["ProductLimit"] = 1,
		},
		[4002] = {
			["ID"] = 4002,
			["buildingLevel"] = 2,
			["roleLevelLimit"] = 2,
			["levelUpConsume"] = 700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801665')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237121'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324302,
			["jobLimit"] = 1,
			["ProductLimit"] = 2,
		},
		[4003] = {
			["ID"] = 4003,
			["buildingLevel"] = 3,
			["roleLevelLimit"] = 3,
			["levelUpConsume"] = 1400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801921')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324303,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[4004] = {
			["ID"] = 4004,
			["buildingLevel"] = 4,
			["roleLevelLimit"] = 4,
			["levelUpConsume"] = 2200,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802177')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237633'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324304,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[4005] = {
			["ID"] = 4005,
			["buildingLevel"] = 5,
			["roleLevelLimit"] = 5,
			["levelUpConsume"] = 5400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802433')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324305,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[4006] = {
			["ID"] = 4006,
			["buildingLevel"] = 6,
			["roleLevelLimit"] = 6,
			["levelUpConsume"] = 9900,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802689')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483238145'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324306,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[4007] = {
			["ID"] = 4007,
			["buildingLevel"] = 7,
			["roleLevelLimit"] = 7,
			["levelUpConsume"] = 16600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802945')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324307,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4008] = {
			["ID"] = 4008,
			["buildingLevel"] = 8,
			["roleLevelLimit"] = 8,
			["levelUpConsume"] = 25600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803201')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324308,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4009] = {
			["ID"] = 4009,
			["buildingLevel"] = 9,
			["roleLevelLimit"] = 9,
			["levelUpConsume"] = 34300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803457')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324309,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4010] = {
			["ID"] = 4010,
			["buildingLevel"] = 10,
			["roleLevelLimit"] = 10,
			["levelUpConsume"] = 45700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803713')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324310,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4011] = {
			["ID"] = 4011,
			["buildingLevel"] = 11,
			["roleLevelLimit"] = 11,
			["levelUpConsume"] = 67500,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803969')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324311,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4012] = {
			["ID"] = 4012,
			["buildingLevel"] = 12,
			["roleLevelLimit"] = 12,
			["levelUpConsume"] = 87700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804225')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324312,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4013] = {
			["ID"] = 4013,
			["buildingLevel"] = 13,
			["roleLevelLimit"] = 13,
			["levelUpConsume"] = 110000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804481')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324313,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4014] = {
			["ID"] = 4014,
			["buildingLevel"] = 14,
			["roleLevelLimit"] = 14,
			["levelUpConsume"] = 125700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804737')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324314,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[4015] = {
			["ID"] = 4015,
			["buildingLevel"] = 15,
			["roleLevelLimit"] = 15,
			["levelUpConsume"] = 145000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804993')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324315,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5001] = {
			["ID"] = 5001,
			["buildingLevel"] = 1,
			["roleLevelLimit"] = 1,
			["levelUpConsume"] = 300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801409')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483236865'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324401,
			["jobLimit"] = 1,
			["ProductLimit"] = 1,
		},
		[5002] = {
			["ID"] = 5002,
			["buildingLevel"] = 2,
			["roleLevelLimit"] = 2,
			["levelUpConsume"] = 700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801665')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237121'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324402,
			["jobLimit"] = 1,
			["ProductLimit"] = 2,
		},
		[5003] = {
			["ID"] = 5003,
			["buildingLevel"] = 3,
			["roleLevelLimit"] = 3,
			["levelUpConsume"] = 1400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214801921')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324403,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[5004] = {
			["ID"] = 5004,
			["buildingLevel"] = 4,
			["roleLevelLimit"] = 4,
			["levelUpConsume"] = 2200,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802177')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237633'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324404,
			["jobLimit"] = 2,
			["ProductLimit"] = 2,
		},
		[5005] = {
			["ID"] = 5005,
			["buildingLevel"] = 5,
			["roleLevelLimit"] = 5,
			["levelUpConsume"] = 5400,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802433')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324405,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[5006] = {
			["ID"] = 5006,
			["buildingLevel"] = 6,
			["roleLevelLimit"] = 6,
			["levelUpConsume"] = 9900,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802689')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483238145'), Game.TableDataManager:GetLangStr('str_34705483236866')},
			["buildingID"] = 4324406,
			["jobLimit"] = 2,
			["ProductLimit"] = 3,
		},
		[5007] = {
			["ID"] = 5007,
			["buildingLevel"] = 7,
			["roleLevelLimit"] = 7,
			["levelUpConsume"] = 16600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214802945')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324407,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5008] = {
			["ID"] = 5008,
			["buildingLevel"] = 8,
			["roleLevelLimit"] = 8,
			["levelUpConsume"] = 25600,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803201')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324408,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5009] = {
			["ID"] = 5009,
			["buildingLevel"] = 9,
			["roleLevelLimit"] = 9,
			["levelUpConsume"] = 34300,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803457')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324409,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5010] = {
			["ID"] = 5010,
			["buildingLevel"] = 10,
			["roleLevelLimit"] = 10,
			["levelUpConsume"] = 45700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803713')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324410,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5011] = {
			["ID"] = 5011,
			["buildingLevel"] = 11,
			["roleLevelLimit"] = 11,
			["levelUpConsume"] = 67500,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214803969')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324411,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5012] = {
			["ID"] = 5012,
			["buildingLevel"] = 12,
			["roleLevelLimit"] = 12,
			["levelUpConsume"] = 87700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804225')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324412,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5013] = {
			["ID"] = 5013,
			["buildingLevel"] = 13,
			["roleLevelLimit"] = 13,
			["levelUpConsume"] = 110000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804481')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324413,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5014] = {
			["ID"] = 5014,
			["buildingLevel"] = 14,
			["roleLevelLimit"] = 14,
			["levelUpConsume"] = 125700,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804737')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324414,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
		[5015] = {
			["ID"] = 5015,
			["buildingLevel"] = 15,
			["roleLevelLimit"] = 15,
			["levelUpConsume"] = 145000,
			["levelUpConditionText"] = {Game.TableDataManager:GetLangStr('str_34705214804993')},
			["levelUpEffectText"] = {Game.TableDataManager:GetLangStr('str_34705483237377')},
			["buildingID"] = 4324415,
			["jobLimit"] = 3,
			["ProductLimit"] = 3,
		},
	},
}

return TopData
