--
-- 表名: $UITips_界面提醒.xlsx  页名：$DialogPopUp_弹窗提醒
--

local TopData = {
	data = {
		[6410001] = {
			["ID"] = 6410001,
			["Enum"] = "TEST",
			["Content"] = "TestContent",
			["Title"] = "TestTitle",
			["RefuseBtn"] = "Cancle",
			["AcceptBtn"] = "Ok",
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410002] = {
			["ID"] = 6410002,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM_WITH_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067002624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789353472'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603873536'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872308992'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410003] = {
			["ID"] = 6410003,
			["Enum"] = "ACCEPT_REVIVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067002880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789353216'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410004] = {
			["ID"] = 6410004,
			["Enum"] = "QUEST_ABANDON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067003136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789640704'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410005] = {
			["ID"] = 6410005,
			["Enum"] = "LEAVE_DUNGEONTEAM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067003392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335438848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410006] = {
			["ID"] = 6410006,
			["Enum"] = "QUIT_TEAM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067003648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_41095320834816'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410007] = {
			["ID"] = 6410007,
			["Enum"] = "SEND_FOLLOW_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067003904'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789602048'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410008] = {
			["ID"] = 6410008,
			["Enum"] = "SEND_APPLICATION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067004160'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789564416'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410009] = {
			["ID"] = 6410009,
			["Enum"] = "TEARDOWN_RARITY_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067004416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335439872'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410010] = {
			["ID"] = 6410010,
			["Enum"] = "HOT_PATCH_NOTICE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067004672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335440128'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603875584'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872311040'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410011] = {
			["ID"] = 6410011,
			["Enum"] = "COMBINE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067004928'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335440384'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410012] = {
			["ID"] = 6410012,
			["Enum"] = "HOT_PATCH_BIG_VERSION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335440640'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_54701508792832'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410013] = {
			["ID"] = 6410013,
			["Enum"] = "HOT_PATCH_SMAILL_VERSION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335440640'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887335440640'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410014] = {
			["ID"] = 6410014,
			["Enum"] = "HOT_PATCH_MOBILE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335441152'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410015] = {
			["ID"] = 6410015,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM_WITHOUT_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789353472'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410016] = {
			["ID"] = 6410016,
			["Enum"] = "CONFIRM_CANCEL_PVP_MATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067006208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54701508804096'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410017] = {
			["ID"] = 6410017,
			["Enum"] = "MIDLEAVE_3V3_CONFIRM_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067006464'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335441920'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410018] = {
			["ID"] = 6410018,
			["Enum"] = "EQUIP_HIGH_FORGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067006720'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335442176'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410019] = {
			["ID"] = 6410019,
			["Enum"] = "MATCH_NEWMATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067006976'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335442432'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410020] = {
			["ID"] = 6410020,
			["Enum"] = "DISCONNECT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067007232'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335442688'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603878144'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872313600'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410021] = {
			["ID"] = 6410021,
			["Enum"] = "DLC_NOT_DOWNLOADED_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067007488'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789357312'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410022] = {
			["ID"] = 6410022,
			["Enum"] = "ACCEPT_SELFREVIVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067007744'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789353216'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410023] = {
			["ID"] = 6410023,
			["Enum"] = "INVENTORY_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067008000'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335443456'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410024] = {
			["ID"] = 6410024,
			["Enum"] = "WAREHOUSE_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067008256'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335443712'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410025] = {
			["ID"] = 6410025,
			["Enum"] = "WAREHOUSE_UNLOCK_PAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067008512'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335443968'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410026] = {
			["ID"] = 6410026,
			["Enum"] = "INVENTORY_BIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067008768'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335444224'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410027] = {
			["ID"] = 6410027,
			["Enum"] = "BREAK_STUCK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067009024'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54495618804736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410028] = {
			["ID"] = 6410028,
			["Enum"] = "SWITCH_PLAYER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067009280'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789509120'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410029] = {
			["ID"] = 6410029,
			["Enum"] = "QUIT_GAME_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067009536'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789509376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410030] = {
			["ID"] = 6410030,
			["Enum"] = "ITEM_DECOMPOSE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067009792'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335439872'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410031] = {
			["ID"] = 6410031,
			["Enum"] = "GUILD_CREATE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067010048'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335445504'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410032] = {
			["ID"] = 6410032,
			["Enum"] = "GUILD_CREATE_CANCEL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067010304'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789841664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410033] = {
			["ID"] = 6410033,
			["Enum"] = "GUILD_RESPOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067010560'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446016'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410034] = {
			["ID"] = 6410034,
			["Enum"] = "EQUIP_TAB3_OUTCONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067010816'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446272'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410035] = {
			["ID"] = 6410035,
			["Enum"] = "EQUIP_TAB3_HIGHERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067011072'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410036] = {
			["ID"] = 6410036,
			["Enum"] = "EQUIP_TAB3_LOWERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067011328'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410037] = {
			["ID"] = 6410037,
			["Enum"] = "EQUIP_TAB3_RAREWORD",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067011584'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410038] = {
			["ID"] = 6410038,
			["Enum"] = "EQUIP_TAB4_RAREWORD",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067011840'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335447296'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410039] = {
			["ID"] = 6410039,
			["Enum"] = "EQUIP_WEARTOBIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067012096'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335447552'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410040] = {
			["ID"] = 6410040,
			["Enum"] = "EQUIP_TAB3_GIVEUP_HIGHERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067012352'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410041] = {
			["ID"] = 6410041,
			["Enum"] = "EQUIP_TAB3_GIVEUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067012608'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335446528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410042] = {
			["ID"] = 6410042,
			["Enum"] = "EQUIP_TAB3_HAVERARE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067012864'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335448320'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410043] = {
			["ID"] = 6410043,
			["Enum"] = "EQUIP_TAB2_CHOOSECONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067013120'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335448576'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410044] = {
			["ID"] = 6410044,
			["Enum"] = "ITEM_ABANDON_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067013376'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335448832'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410045] = {
			["ID"] = 6410045,
			["Enum"] = "GUILD_KICK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067013632'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789870592'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410046] = {
			["ID"] = 6410046,
			["Enum"] = "GUILD_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067013888'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335449344'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410047] = {
			["ID"] = 6410047,
			["Enum"] = "GUILD_LEAVE_PRESIDENT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067014144'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335449344'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410048] = {
			["ID"] = 6410048,
			["Enum"] = "GUILD_PRESIDENT_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067014400'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335449856'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410049] = {
			["ID"] = 6410049,
			["Enum"] = "GUILD_LEADER_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067014656'),
			["Title"] = Game.TableDataManager:GetLangStr('str_41095320837376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410050] = {
			["ID"] = 6410050,
			["Enum"] = "EXIT_PLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067014912'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335450368'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410051] = {
			["ID"] = 6410051,
			["Enum"] = "LEAVE_ARENA3V3_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067015168'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335450624'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410052] = {
			["ID"] = 6410052,
			["Enum"] = "LESS_GACHA_TICKET",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067015424'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335450880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410053] = {
			["ID"] = 6410053,
			["Enum"] = "GROUPCHAT_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067015680'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789524480'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410054] = {
			["ID"] = 6410054,
			["Enum"] = "CREATE_ROLE_ABANDON",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067015936'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335451392'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410055] = {
			["ID"] = 6410055,
			["Enum"] = "MAIL_DELETE_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067016192'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328570880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410056] = {
			["ID"] = 6410056,
			["Enum"] = "DEFEQUIP_REVERT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067016448'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335451904'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410057] = {
			["ID"] = 6410057,
			["Enum"] = "REFRESH_GACHA_SHOP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067016704'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60818615966464'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410058] = {
			["ID"] = 6410058,
			["Enum"] = "ENFORCE_FELLOWPIECE_COMPOSITION",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067016960'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335452416'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410059] = {
			["ID"] = 6410059,
			["Enum"] = "REPLACE_ONLINE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067017216'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335452672'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410060] = {
			["ID"] = 6410060,
			["Enum"] = "REPLACE_FORCED_OFFLINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067017472'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335452928'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410061] = {
			["ID"] = 6410061,
			["Enum"] = "ENTER_PIRATE_SCHOOL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067017728'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335453184'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410062] = {
			["ID"] = 6410062,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067017984'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789353472'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410063] = {
			["ID"] = 6410063,
			["Enum"] = "SEALED_RANK_UP_CONSUME_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067018240'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335453696'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410064] = {
			["ID"] = 6410064,
			["Enum"] = "SEALED_RANDOM_HIGH_RESON_ABANDON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067018496'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25564450659072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410065] = {
			["ID"] = 6410065,
			["Enum"] = "SEALED_RANDOM_LOW_RESON_RESERVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067018752'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25564450659072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410066] = {
			["ID"] = 6410066,
			["Enum"] = "SEALED_RANDOM_RESULT_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067019008'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25564450659072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603889920'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872325376'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410067] = {
			["ID"] = 6410067,
			["Enum"] = "EQUIP_TAB1_LOWER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067019264'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335454720'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410068] = {
			["ID"] = 6410068,
			["Enum"] = "EQUIP_TAB1_UNBIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067019520'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335454720'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410069] = {
			["ID"] = 6410069,
			["Enum"] = "EQUIP_TAB2_ALREADY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067019776'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335455232'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603890688'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_29756607199488'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410070] = {
			["ID"] = 6410070,
			["Enum"] = "EQUIP_WORDREVERT_SINGLE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067020032'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335455488'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410071] = {
			["ID"] = 6410071,
			["Enum"] = "EQUIP_WORDREVERT_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067020288'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335455488'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410072] = {
			["ID"] = 6410072,
			["Enum"] = "ITEM_PACK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067020544'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335456000'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410073] = {
			["ID"] = 6410073,
			["Enum"] = "TAB_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067020800'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789508864'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410074] = {
			["ID"] = 6410074,
			["Enum"] = "LEAVE_ARENA12V12_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067021056'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335456512'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415067] = {
			["ID"] = 6415067,
			["Enum"] = "SEFIROT_CORE_SWITCH_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067021312'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335456768'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415002] = {
			["ID"] = 6415002,
			["Enum"] = "HOT_PATCH_DISCONNECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067021568'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335457024'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415003] = {
			["ID"] = 6415003,
			["Enum"] = "HOT_PATCH_SPACE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067021824'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335457280'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415004] = {
			["ID"] = 6415004,
			["Enum"] = "CLIENT_ON_DISCONNECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067022080'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887067022080'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415005] = {
			["ID"] = 6415005,
			["Enum"] = "RECONNECT_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067022336'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887067022336'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415006] = {
			["ID"] = 6415006,
			["Enum"] = "CROSS_WORD_PUZZLE_CORRECT",
			["Content"] = "%s",
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335458048'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415007] = {
			["ID"] = 6415007,
			["Enum"] = "CONNECT_SERVER_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067022848'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887067022848'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415008] = {
			["ID"] = 6415008,
			["Enum"] = "MONEY_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067023104'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335458560'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415009] = {
			["ID"] = 6415009,
			["Enum"] = "FRIEND_CLUB_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067023360'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789524736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415010] = {
			["ID"] = 6415010,
			["Enum"] = "FRIEND_CLUB_CLEAR_MSG",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067023616'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335459072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415011] = {
			["ID"] = 6415011,
			["Enum"] = "FRIEND_SETTING_WARNING_PLEASE_ADD_FIRST",
			["Content"] = Game.TableDataManager:GetLangStr('str_54632789521152'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415012] = {
			["ID"] = 6415012,
			["Enum"] = "FRIEND_CLUB_KICK_OUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067024128'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335459584'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415013] = {
			["ID"] = 6415013,
			["Enum"] = "FRIEND_GROUP_DELETE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067024384'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335459840'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415014] = {
			["ID"] = 6415014,
			["Enum"] = "GUILD_INVITE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067024640'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335460096'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_39104603528448'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415015] = {
			["ID"] = 6415015,
			["Enum"] = "HOT_PATCH_FIX",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067024896'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335460352'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415016] = {
			["ID"] = 6415016,
			["Enum"] = "TEAM_CONVERT_TARGET_EMPTY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067025152'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615616'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415017] = {
			["ID"] = 6415017,
			["Enum"] = "TEAM_CONVERT_MORE_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067025408'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615616'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415018] = {
			["ID"] = 6415018,
			["Enum"] = "GUILD_MERGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067025664'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335461120'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415019] = {
			["ID"] = 6415019,
			["Enum"] = "MONEY_NEEDRMB",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067025920'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335461376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872332288'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415020] = {
			["ID"] = 6415020,
			["Enum"] = "MONEY_RMBEXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067026176'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789444352'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415021] = {
			["ID"] = 6415021,
			["Enum"] = "SDK_LOGOUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067026432'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335461888'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415022] = {
			["ID"] = 6415022,
			["Enum"] = "TEAM_INPLACE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067026688'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335462144'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415023] = {
			["ID"] = 6415023,
			["Enum"] = "TEAM_DISMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067026944'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415024] = {
			["ID"] = 6415024,
			["Enum"] = "TEAM_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067027200'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415025] = {
			["ID"] = 6415025,
			["Enum"] = "MONEY_NORMB",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067027456'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335461376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872332288'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415026] = {
			["ID"] = 6415026,
			["Enum"] = "PENCE_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067027712'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335463168'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872334080'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415027] = {
			["ID"] = 6415027,
			["Enum"] = "BLACKLIST_GIVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067027968'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415028] = {
			["ID"] = 6415028,
			["Enum"] = "FRIEND_GIVE_ATTRACTION_MAX",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067028224'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415029] = {
			["ID"] = 6415029,
			["Enum"] = "READY_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067028480'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 60,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415030] = {
			["ID"] = 6415030,
			["Enum"] = "SEND_APPLICATION_CONFIRM_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067028736'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415031] = {
			["ID"] = 6415031,
			["Enum"] = "GROUP_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067028992'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415032] = {
			["ID"] = 6415032,
			["Enum"] = "GROUP_LEADER_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067029248'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415033] = {
			["ID"] = 6415033,
			["Enum"] = "GROUP_KICK_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067029504'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415034] = {
			["ID"] = 6415034,
			["Enum"] = "GROUP_CONVENE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067029760'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415035] = {
			["ID"] = 6415035,
			["Enum"] = "MODULE_UNLOCK_TASK_PROMPT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067030016'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415036] = {
			["ID"] = 6415036,
			["Enum"] = "ENTER_CROSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067030272'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335465728'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415037] = {
			["ID"] = 6415037,
			["Enum"] = "SEND_APPLICATION_CONFIRM_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067028736'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789582080'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415038] = {
			["ID"] = 6415038,
			["Enum"] = "INVENTORY_SLOT_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067030784'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335466240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415039] = {
			["ID"] = 6415039,
			["Enum"] = "WAREHOUSE_SLOT_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067031040'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335466496'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415040] = {
			["ID"] = 6415040,
			["Enum"] = "GUILD_PARTY_INVITE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067031296'),
			["Title"] = Game.TableDataManager:GetLangStr('str_59236994318336'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 30,
			["TimeUPSelect"] = 1,
			["TodayNoPopUp"] = false,
		},
		[6415041] = {
			["ID"] = 6415041,
			["Enum"] = "GUILD_PARTY_BUFF_REFRESH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067031552'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415042] = {
			["ID"] = 6415042,
			["Enum"] = "GUILD_PARTY_RARE_BUFF_REFRESH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067031808'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415043] = {
			["ID"] = 6415043,
			["Enum"] = "GUILD_PARTY_BUFF_MONEY_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067032064'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335463168'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872334080'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415044] = {
			["ID"] = 6415044,
			["Enum"] = "CRAZY_MODE_DOUBLE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067032320'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415045] = {
			["ID"] = 6415045,
			["Enum"] = "DELETE_FRIEND",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067032576'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789531648'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415046] = {
			["ID"] = 6415046,
			["Enum"] = "ADDBLACKLIST",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067032832'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335468288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415047] = {
			["ID"] = 6415047,
			["Enum"] = "SDK_LOGIN_FAILED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067033088'),
			["Title"] = Game.TableDataManager:GetLangStr('str_32574642617344'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415048] = {
			["ID"] = 6415048,
			["Enum"] = "EXIT_ESCORTTASKPLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067033344'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335468800'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_29756607179264'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415049] = {
			["ID"] = 6415049,
			["Enum"] = "LEAVE_ARENA50V50_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067033600'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335469056'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415052] = {
			["ID"] = 6415052,
			["Enum"] = "CASHMARKET_CANCELCONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067033856'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335469312'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415053] = {
			["ID"] = 6415053,
			["Enum"] = "ACTIVITY_OPEN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067034112'),
			["Title"] = "",
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 2,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415054] = {
			["ID"] = 6415054,
			["Enum"] = "RECIPE_DELETE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067034368'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335469824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415055] = {
			["ID"] = 6415055,
			["Enum"] = "EXCHANGE_OVER50_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067034624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335470080'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415056] = {
			["ID"] = 6415056,
			["Enum"] = "BID_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067034880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335470336'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415057] = {
			["ID"] = 6415057,
			["Enum"] = "AUCTION_MAX_PRICE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067035136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335470592'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415058] = {
			["ID"] = 6415058,
			["Enum"] = "ROLE_CREATE_UNFINISHED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067035392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335470848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415059] = {
			["ID"] = 6415059,
			["Enum"] = "SCHEDULE_USED_TOTAL_DORPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067035648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_51128095999744'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415060] = {
			["ID"] = 6415060,
			["Enum"] = "ATALASHI_VERSION_IGNORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335471360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_54701508792832'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887335440640'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415061] = {
			["ID"] = 6415061,
			["Enum"] = "ATALASHI_VERSION_QUIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067005184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335471360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887335440640'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415062] = {
			["ID"] = 6415062,
			["Enum"] = "TEAM_FOLLOW_SINGLE_PLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067036416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415063] = {
			["ID"] = 6415063,
			["Enum"] = "GUILD_MANAGE_RESIGN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067036672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415064] = {
			["ID"] = 6415064,
			["Enum"] = "ROLEPLAY_PROMOTION",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067036928'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335472384'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415065] = {
			["ID"] = 6415065,
			["Enum"] = "GUILD_MATERIAL_TASK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067037184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335472640'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415066] = {
			["ID"] = 6415066,
			["Enum"] = "GUILD_MATERIAL_5TASK_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067037440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335472896'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415068] = {
			["ID"] = 6415068,
			["Enum"] = "SEALED_UPGRADE_RESET",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067037696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335473152'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415069] = {
			["ID"] = 6415069,
			["Enum"] = "SEALED_REFINE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067037952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335473408'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415070] = {
			["ID"] = 6415070,
			["Enum"] = "SEALED_UPGRADE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067038208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335473664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415071] = {
			["ID"] = 6415071,
			["Enum"] = "REFINE_REPLACE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067038464'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335473920'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415072] = {
			["ID"] = 6415072,
			["Enum"] = "SEALED_EQUIP_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067038720'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415073] = {
			["ID"] = 6415073,
			["Enum"] = "GUILDBATTLEFIELD_QUIT_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067038976'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335468800'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415074] = {
			["ID"] = 6415074,
			["Enum"] = "GUILDBATTLEFIELD_ENTER_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067039232'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335474688'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 2,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415075] = {
			["ID"] = 6415075,
			["Enum"] = "IOS_GPU_CAPTURE_FRAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067039488'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25908584991744'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603910400'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872345856'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415076] = {
			["ID"] = 6415076,
			["Enum"] = "MAIN_PLAYER_ENTITY_CREATE_FAILED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067039744'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335475200'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415077] = {
			["ID"] = 6415077,
			["Enum"] = "DANCINGPARTY_PARTNER_RELIEVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067040000'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335475456'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415078] = {
			["ID"] = 6415078,
			["Enum"] = "DANCINGPARTY_PARTNER_RELIEVE_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067040256'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415079] = {
			["ID"] = 6415079,
			["Enum"] = "CUSTOMROLE_BACK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067040512'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415080] = {
			["ID"] = 6415080,
			["Enum"] = "CUSTOMROLE_CONTINUELAST",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067040768'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415081] = {
			["ID"] = 6415081,
			["Enum"] = "COMMON_EMPTY_ENSURE_DIALOG",
			["Content"] = "%s",
			["Title"] = "%s",
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415082] = {
			["ID"] = 6415082,
			["Enum"] = "KICK_OUT_SERVER_SELECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067041280'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335476736'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415083] = {
			["ID"] = 6415083,
			["Enum"] = "KICK_OUT_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067041536'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335476736'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415084] = {
			["ID"] = 6415084,
			["Enum"] = "KICK_OUT_HOTFIX",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067041792'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335476736'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415085] = {
			["ID"] = 6415085,
			["Enum"] = "ACCOUNT_BAN_KICK_OUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067042048'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335476736'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415086] = {
			["ID"] = 6415086,
			["Enum"] = "FASHION_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067042304'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603913216'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789504512'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415087] = {
			["ID"] = 6415087,
			["Enum"] = "FASHION_PURCHASE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067042560'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415088] = {
			["ID"] = 6415088,
			["Enum"] = "FASHION_GO_PHOTOGRAPHING_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067042816'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603913728'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415089] = {
			["ID"] = 6415089,
			["Enum"] = "FASHION_PLAN_RESTORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067043072'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415090] = {
			["ID"] = 6415090,
			["Enum"] = "FASHION_ADJUST_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067043328'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415091] = {
			["ID"] = 6415091,
			["Enum"] = "FASHION_PLAN_REPLACE",
			["Content"] = Game.TableDataManager:GetLangStr('str_54632789789440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789789952'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54701508818688'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415092] = {
			["ID"] = 6415092,
			["Enum"] = "FASHION_PLAN_SAVE",
			["Content"] = "",
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789789696'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415093] = {
			["ID"] = 6415093,
			["Enum"] = "FASHION_MAKEUP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067044096'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415094] = {
			["ID"] = 6415094,
			["Enum"] = "FASHION_CANT_BUY_TIPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067044352'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335461376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415095] = {
			["ID"] = 6415095,
			["Enum"] = "FASHION_MAKEUP_QUIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067044608'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415096] = {
			["ID"] = 6415096,
			["Enum"] = "FASHION_MAKEUP_RESET",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067044864'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789332224'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415097] = {
			["ID"] = 6415097,
			["Enum"] = "CUSTOMROLE_PANEL_CLOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067045120'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415100] = {
			["ID"] = 6415100,
			["Enum"] = "WORLD_BOSS_KILL_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067045376'),
			["Title"] = Game.TableDataManager:GetLangStr('str_59236994271232'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415101] = {
			["ID"] = 6415101,
			["Enum"] = "WORLD_BOSS_LEAVE_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067045632'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789603072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872352000'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415102] = {
			["ID"] = 6415102,
			["Enum"] = "WORLD_BOSS_ENTER_NEW_BOSS_AREA",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067045888'),
			["Title"] = Game.TableDataManager:GetLangStr('str_59236994271232'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872352256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415103] = {
			["ID"] = 6415103,
			["Enum"] = "WORLD_BOSS_CAPTAIN_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067046144'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335481600'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872352512'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415104] = {
			["ID"] = 6415104,
			["Enum"] = "WORLD_BOSS_TEAM_MEMBER_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067046400'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335481600'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415105] = {
			["ID"] = 6415105,
			["Enum"] = "WORLD_BOSS_INDIVIDUAL_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067046656'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335481600'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872352512'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415106] = {
			["ID"] = 6415106,
			["Enum"] = "WORLD_BOSS_MERGE_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067046912'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335482368'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872353280'),
			["DialogType"] = 0,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415107] = {
			["ID"] = 6415107,
			["Enum"] = "ACTIVITY_OPEN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067034112'),
			["Title"] = "",
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 2,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415109] = {
			["ID"] = 6415109,
			["Enum"] = "WORLD_BOSS_CAPTAIN_TRANSFER_PERMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067047424'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335482880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415110] = {
			["ID"] = 6415110,
			["Enum"] = "WORLD_BOSS_TEAM_MEMBER_TRANSFER_PERMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067047680'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335482880'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415108] = {
			["ID"] = 6415108,
			["Enum"] = "EQUIPMENT_IN_SUIT_DECOMPOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067047936'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335483392'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415111] = {
			["ID"] = 6415111,
			["Enum"] = "EQUIPMENT_IN_SUIT_TO_WAREHOUSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067048192'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335483648'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415200] = {
			["ID"] = 6415200,
			["Enum"] = "CUSTOMROLE_RESETDATA",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067048448'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415300] = {
			["ID"] = 6415300,
			["Enum"] = "SUBMIT_FINAL_PRICE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067048704'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415301] = {
			["ID"] = 6415301,
			["Enum"] = "ARBITRATOR_GIVE_UP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067048960'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335484416'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415302] = {
			["ID"] = 6415302,
			["Enum"] = "DANCE_DOUBLE_MATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067049216'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415303] = {
			["ID"] = 6415303,
			["Enum"] = "DANCE_ENERGY_USE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067049472'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415304] = {
			["ID"] = 6415304,
			["Enum"] = "DANCE_EXIT_MIDWAY_CHALLENGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067049728'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415305] = {
			["ID"] = 6415305,
			["Enum"] = "DANCE_PARTNER_EXIT_MIDWAY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067049984'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415306] = {
			["ID"] = 6415306,
			["Enum"] = "DANCE_EXIT_MIDWAY_PERFORM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067050240'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415307] = {
			["ID"] = 6415307,
			["Enum"] = "DANCE_EXIT_MIDWAY_PERFORM_ENERGY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067050496'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415308] = {
			["ID"] = 6415308,
			["Enum"] = "GROUP_LEADER_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067050752'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415309] = {
			["ID"] = 6415309,
			["Enum"] = "GROUP_EXIT_IN_LEAGUE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067051008'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415310] = {
			["ID"] = 6415310,
			["Enum"] = "TEAM_EXIT_IN_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067051264'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415311] = {
			["ID"] = 6415311,
			["Enum"] = "LOCATION_TRACE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067051520'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415312] = {
			["ID"] = 6415312,
			["Enum"] = "SWITCH_BATTLE_LINE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067051776'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_60887603922688'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872358144'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415313] = {
			["ID"] = 6415313,
			["Enum"] = "MATCHCE_LOW_REMIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067052032'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415314] = {
			["ID"] = 6415314,
			["Enum"] = "NEWBIE_GUIDE_SKIP_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067052288'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415315] = {
			["ID"] = 6415315,
			["Enum"] = "SERVER_BUSY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067052544'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335488000'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415316] = {
			["ID"] = 6415316,
			["Enum"] = "SOCIAL_FRIEND_GIFT_BUY",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067052800'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335488256'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415318] = {
			["ID"] = 6415318,
			["Enum"] = "SOCIAL_FRIEND_GIFT_BUY_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067053056'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335488512'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415319] = {
			["ID"] = 6415319,
			["Enum"] = "DELETE_EMOJI_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067053312'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335488768'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415320] = {
			["ID"] = 6415320,
			["Enum"] = "REMOVE_GROUPCHAT_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067053568'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335489024'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415321] = {
			["ID"] = 6415321,
			["Enum"] = "REMOVE_LOT_GROUPCHAT_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067053824'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335489024'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415322] = {
			["ID"] = 6415322,
			["Enum"] = "CLEAR_CHAT_MESSAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067054080'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789524992'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415323] = {
			["ID"] = 6415323,
			["Enum"] = "WATERPIPE_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067054336'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415324] = {
			["ID"] = 6415324,
			["Enum"] = "MAP_CANCEL_INPUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067054592'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415325] = {
			["ID"] = 6415325,
			["Enum"] = "CHATROOM_CHANGE_ROOM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067054848'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415326] = {
			["ID"] = 6415326,
			["Enum"] = "CHATROOM_PERMISSION_NOT_SAVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067055104'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415327] = {
			["ID"] = 6415327,
			["Enum"] = "CHATROOM_BLACKLIST_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067055360'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415328] = {
			["ID"] = 6415328,
			["Enum"] = "CHATROOM_EXIST_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067055616'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415329] = {
			["ID"] = 6415329,
			["Enum"] = "CHATROOM_PIN_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067055872'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415330] = {
			["ID"] = 6415330,
			["Enum"] = "CHATROOM_PIN_CANCEL_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067056128'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415331] = {
			["ID"] = 6415331,
			["Enum"] = "TURNTABLE_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067056384'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415332] = {
			["ID"] = 6415332,
			["Enum"] = "CLUB_REMOVE_OFFICIAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067056640'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415333] = {
			["ID"] = 6415333,
			["Enum"] = "CLUB_EXCHANGE_OFFICIAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067056896'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415334] = {
			["ID"] = 6415334,
			["Enum"] = "CLUB_TRANSFER_BOSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067057152'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415335] = {
			["ID"] = 6415335,
			["Enum"] = "CLUB_RRSIGN_BOSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067057408'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415336] = {
			["ID"] = 6415336,
			["Enum"] = "CLUB_OFFICIAL_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067057664'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415337] = {
			["ID"] = 6415337,
			["Enum"] = "CLUB_RESPOND_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067057920'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415338] = {
			["ID"] = 6415338,
			["Enum"] = "CLUB_APPLY_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067058176'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415339] = {
			["ID"] = 6415339,
			["Enum"] = "MAIL_DETACH_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067058432'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415340] = {
			["ID"] = 6415340,
			["Enum"] = "MAIL_COLLECT_CANCEL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067058688'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415341] = {
			["ID"] = 6415341,
			["Enum"] = "GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_LIMIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067058944'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415342] = {
			["ID"] = 6415342,
			["Enum"] = "PVP_3V3_IN_TEAM_GROUP_MATCH_QUIT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067059200'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789615360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415343] = {
			["ID"] = 6415343,
			["Enum"] = "GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_TEAM_LIMIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067059456'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789603072'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415344] = {
			["ID"] = 6415344,
			["Enum"] = "CONSECRATION_MONUMENT_LEVELUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067059712'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335495168'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_60887872366080'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415345] = {
			["ID"] = 6415345,
			["Enum"] = "CHATROOM_OWNER_ASSIGNMENT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067059968'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335495424'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415346] = {
			["ID"] = 6415346,
			["Enum"] = "LOGIN_QUEUE_SWITCH_SERVER",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067060224'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335495680'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415347] = {
			["ID"] = 6415347,
			["Enum"] = "LOGIN_QUEUE_BACK_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067060480'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335495936'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415348] = {
			["ID"] = 6415348,
			["Enum"] = "LOGIN_QUEUE_FINISH_ENTER_GAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067060736'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789319424'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415349] = {
			["ID"] = 6415349,
			["Enum"] = "LOGIN_QUEUE_FINISH_CHECK_ENTER_GAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067060992'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789319424'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415350] = {
			["ID"] = 6415350,
			["Enum"] = "MANOR_RECYCLE_FURNITURE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067061248'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335496704'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415351] = {
			["ID"] = 6415351,
			["Enum"] = "MANOR_RECYCLE_PCG",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067061504'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335496960'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415352] = {
			["ID"] = 6415352,
			["Enum"] = "ELEMENT_POINT_RESET_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067061760'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335497216'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415353] = {
			["ID"] = 6415353,
			["Enum"] = "TAROTTEAM_LEAVE_RESPONDED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067062016'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335497472'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415354] = {
			["ID"] = 6415354,
			["Enum"] = "TAROTTEAM_KICK_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067062272'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335497728'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415355] = {
			["ID"] = 6415355,
			["Enum"] = "TAROTTEAM_LEAVE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067062528'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335497984'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415356] = {
			["ID"] = 6415356,
			["Enum"] = "TAROTTEAM_LEAVE_LASTONE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067062784'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335497984'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415357] = {
			["ID"] = 6415357,
			["Enum"] = "TAROTTEAM_UNCREATE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067063040'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789841664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415358] = {
			["ID"] = 6415358,
			["Enum"] = "TAROTTEAM_IMPEACH_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067063296'),
			["Title"] = Game.TableDataManager:GetLangStr('str_56007178872576'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415359] = {
			["ID"] = 6415359,
			["Enum"] = "TAROTTEAM_FOUND_CEREMONY_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067063552'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335499008'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415360] = {
			["ID"] = 6415360,
			["Enum"] = "TAROTTEAM_TRANSFER_CAPTAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067063808'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789621760'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415361] = {
			["ID"] = 6415361,
			["Enum"] = "TAROTTEAM_APPLY_LINK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067064064'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335499520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415362] = {
			["ID"] = 6415362,
			["Enum"] = "TASK_FAILURE_AGAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067064320'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25908584945408'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415363] = {
			["ID"] = 6415363,
			["Enum"] = "TAROTTEAM_IMPEACH_POPUP_CAPTAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067064576'),
			["Title"] = Game.TableDataManager:GetLangStr('str_56007178872576'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415364] = {
			["ID"] = 6415364,
			["Enum"] = "COLLECTIBLES_JUMP_TO_CUTSCENE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067064832'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335500288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415365] = {
			["ID"] = 6415365,
			["Enum"] = "LEAVE_ARENA_CHAMPION_PREPARE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067065088'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335500544'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415366] = {
			["ID"] = 6415366,
			["Enum"] = "LEAVE_ARENA_CHAMPION_MATCH_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067065344'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335500800'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415367] = {
			["ID"] = 6415367,
			["Enum"] = "IN_QUEUE_HINT_USE_SDK_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067065600'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415368] = {
			["ID"] = 6415368,
			["Enum"] = "CHATROOM_BAN_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067065856'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415369] = {
			["ID"] = 6415369,
			["Enum"] = "SETTING_RESET_PAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067066112'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789508864'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415370] = {
			["ID"] = 6415370,
			["Enum"] = "CHATROOM_PERMANENT_ROOM_RENEWAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067066368'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415371] = {
			["ID"] = 6415371,
			["Enum"] = "CHATROOM_PERMANENT_ROOM_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067066624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335502080'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415372] = {
			["ID"] = 6415372,
			["Enum"] = "CHATROOM_IDENTITY_TAG_REMOVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067066880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415373] = {
			["ID"] = 6415373,
			["Enum"] = "CHATROOM_IDENTITY_STATE_SWITCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067067136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415374] = {
			["ID"] = 6415374,
			["Enum"] = "PVP_CHAMPION_QUIT_TROOP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067067392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789905664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415375] = {
			["ID"] = 6415375,
			["Enum"] = "PVP_CHAMPION_DISBAND_TROOP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067067648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789905920'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415376] = {
			["ID"] = 6415376,
			["Enum"] = "PVP_CHAMPION_REMOVE_TROOP_MEMBER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067067904'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789906432'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415377] = {
			["ID"] = 6415377,
			["Enum"] = "PVP_CHAMPION_TRANSFER_LEADER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067068160'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789621760'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415378] = {
			["ID"] = 6415378,
			["Enum"] = "PVP_CHAMPION_DISBAND_TROOP_CONFIRM_SINGLE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067068416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789905664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415379] = {
			["ID"] = 6415379,
			["Enum"] = "CHATROOM_IDENTITY_CHANGE_NAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067068672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335504128'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 4,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415380] = {
			["ID"] = 6415380,
			["Enum"] = "MANOR_CHANG_NAME",
			["Content"] = "",
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335504384'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 4,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415381] = {
			["ID"] = 6415381,
			["Enum"] = "MONEY_CONSUME_BOUND_POUNDS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067069184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789444352'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415382] = {
			["ID"] = 6415382,
			["Enum"] = "TEAM_ALL_TEAM_APPLY_TIPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067069440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_60887335504896'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415383] = {
			["ID"] = 6415383,
			["Enum"] = "BASICDANCE_INITIATOR",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067069696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415384] = {
			["ID"] = 6415384,
			["Enum"] = "BASICDANCE_ACCEPTOR",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067069952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415385] = {
			["ID"] = 6415385,
			["Enum"] = "BASICDANCE_MULTIPLAYER_DANCE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067070208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415386] = {
			["ID"] = 6415386,
			["Enum"] = "BASICDANCE_TEAM_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067070464'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415387] = {
			["ID"] = 6415387,
			["Enum"] = "BASICDANCE_STAGE_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067070720'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415388] = {
			["ID"] = 6415388,
			["Enum"] = "MEMBER_ADJUST_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_60887067070976'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415389] = {
			["ID"] = 6415389,
			["Enum"] = "MAIL_REMOVE_FROM_FAV",
			["Content"] = Game.TableDataManager:GetLangStr('str_60955786547968'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328570880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415390] = {
			["ID"] = 6415390,
			["Enum"] = "MAIL_REMOVE_FROM_FAV_DELETE",
			["Content"] = Game.TableDataManager:GetLangStr('str_60955786548224'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328570880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
	},
}

return TopData
