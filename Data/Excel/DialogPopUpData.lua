--
-- 表名: $UITips_界面提醒.xlsx  页名：$DialogPopUp_弹窗提醒
--

local TopData = {
	data = {
		[6410001] = {
			["ID"] = 6410001,
			["Enum"] = "TEST",
			["Content"] = "TestContent",
			["Title"] = "TestTitle",
			["RefuseBtn"] = "Cancle",
			["AcceptBtn"] = "Ok",
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410002] = {
			["ID"] = 6410002,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM_WITH_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225432832'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762303744'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030739200'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410003] = {
			["ID"] = 6410003,
			["Enum"] = "ACCEPT_REVIVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225433088'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868544'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410004] = {
			["ID"] = 6410004,
			["Enum"] = "QUEST_ABANDON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225433344'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868800'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410005] = {
			["ID"] = 6410005,
			["Enum"] = "LEAVE_DUNGEONTEAM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225433600'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493869056'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410006] = {
			["ID"] = 6410006,
			["Enum"] = "QUIT_TEAM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225433856'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493869312'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410007] = {
			["ID"] = 6410007,
			["Enum"] = "SEND_FOLLOW_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225434112'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493869568'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410008] = {
			["ID"] = 6410008,
			["Enum"] = "SEND_APPLICATION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225434368'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493869824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410009] = {
			["ID"] = 6410009,
			["Enum"] = "TEARDOWN_RARITY_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225434624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870080'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410010] = {
			["ID"] = 6410010,
			["Enum"] = "HOT_PATCH_NOTICE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225434880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870336'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762305792'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741248'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410011] = {
			["ID"] = 6410011,
			["Enum"] = "COMBINE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870592'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410012] = {
			["ID"] = 6410012,
			["Enum"] = "HOT_PATCH_BIG_VERSION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762306304'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410013] = {
			["ID"] = 6410013,
			["Enum"] = "HOT_PATCH_SMAILL_VERSION_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61093493870848'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410014] = {
			["ID"] = 6410014,
			["Enum"] = "HOT_PATCH_MOBILE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435904'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493871360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410015] = {
			["ID"] = 6410015,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM_WITHOUT_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225436160'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410016] = {
			["ID"] = 6410016,
			["Enum"] = "CONFIRM_CANCEL_PVP_MATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225436416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493871872'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410017] = {
			["ID"] = 6410017,
			["Enum"] = "MIDLEAVE_3V3_CONFIRM_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225436672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493872128'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410018] = {
			["ID"] = 6410018,
			["Enum"] = "EQUIP_HIGH_FORGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225436928'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493872384'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410019] = {
			["ID"] = 6410019,
			["Enum"] = "MATCH_NEWMATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225437184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493872640'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410020] = {
			["ID"] = 6410020,
			["Enum"] = "DISCONNECT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225437440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493872896'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762308352'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030743808'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410021] = {
			["ID"] = 6410021,
			["Enum"] = "DLC_NOT_DOWNLOADED_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225437696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493873152'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410022] = {
			["ID"] = 6410022,
			["Enum"] = "ACCEPT_SELFREVIVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225437952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868544'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410023] = {
			["ID"] = 6410023,
			["Enum"] = "INVENTORY_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225438208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493873664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410024] = {
			["ID"] = 6410024,
			["Enum"] = "WAREHOUSE_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225438464'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493873920'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410025] = {
			["ID"] = 6410025,
			["Enum"] = "WAREHOUSE_UNLOCK_PAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225438720'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493874176'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410026] = {
			["ID"] = 6410026,
			["Enum"] = "INVENTORY_BIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225438976'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493874432'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410027] = {
			["ID"] = 6410027,
			["Enum"] = "BREAK_STUCK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225439232'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493874688'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410028] = {
			["ID"] = 6410028,
			["Enum"] = "SWITCH_PLAYER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225439488'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493874944'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410029] = {
			["ID"] = 6410029,
			["Enum"] = "QUIT_GAME_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225439744'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493875200'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410030] = {
			["ID"] = 6410030,
			["Enum"] = "ITEM_DECOMPOSE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225440000'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493870080'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410031] = {
			["ID"] = 6410031,
			["Enum"] = "GUILD_CREATE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225440256'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493875712'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410032] = {
			["ID"] = 6410032,
			["Enum"] = "GUILD_CREATE_CANCEL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225440512'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493875968'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410033] = {
			["ID"] = 6410033,
			["Enum"] = "GUILD_RESPOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225440768'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876224'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410034] = {
			["ID"] = 6410034,
			["Enum"] = "EQUIP_TAB3_OUTCONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225441024'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876480'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410035] = {
			["ID"] = 6410035,
			["Enum"] = "EQUIP_TAB3_HIGHERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225441280'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410036] = {
			["ID"] = 6410036,
			["Enum"] = "EQUIP_TAB3_LOWERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225441536'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410037] = {
			["ID"] = 6410037,
			["Enum"] = "EQUIP_TAB3_RAREWORD",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225441792'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410038] = {
			["ID"] = 6410038,
			["Enum"] = "EQUIP_TAB4_RAREWORD",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225442048'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493877504'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410039] = {
			["ID"] = 6410039,
			["Enum"] = "EQUIP_WEARTOBIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225442304'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493877760'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410040] = {
			["ID"] = 6410040,
			["Enum"] = "EQUIP_TAB3_GIVEUP_HIGHERSCORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225442560'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410041] = {
			["ID"] = 6410041,
			["Enum"] = "EQUIP_TAB3_GIVEUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225442816'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493876736'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410042] = {
			["ID"] = 6410042,
			["Enum"] = "EQUIP_TAB3_HAVERARE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225443072'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493878528'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410043] = {
			["ID"] = 6410043,
			["Enum"] = "EQUIP_TAB2_CHOOSECONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225443328'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493878784'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410044] = {
			["ID"] = 6410044,
			["Enum"] = "ITEM_ABANDON_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225443584'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493879040'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410045] = {
			["ID"] = 6410045,
			["Enum"] = "GUILD_KICK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225443840'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493879296'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410046] = {
			["ID"] = 6410046,
			["Enum"] = "GUILD_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225444096'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493879552'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410047] = {
			["ID"] = 6410047,
			["Enum"] = "GUILD_LEAVE_PRESIDENT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225444352'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493879552'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410048] = {
			["ID"] = 6410048,
			["Enum"] = "GUILD_PRESIDENT_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225444608'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493880064'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410049] = {
			["ID"] = 6410049,
			["Enum"] = "GUILD_LEADER_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225444864'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493880320'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410050] = {
			["ID"] = 6410050,
			["Enum"] = "EXIT_PLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225445120'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493880576'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410051] = {
			["ID"] = 6410051,
			["Enum"] = "LEAVE_ARENA3V3_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225445376'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493880832'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410052] = {
			["ID"] = 6410052,
			["Enum"] = "LESS_GACHA_TICKET",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225445632'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493881088'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410053] = {
			["ID"] = 6410053,
			["Enum"] = "GROUPCHAT_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225445888'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493881344'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410054] = {
			["ID"] = 6410054,
			["Enum"] = "CREATE_ROLE_ABANDON",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225446144'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493881600'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410055] = {
			["ID"] = 6410055,
			["Enum"] = "MAIL_DELETE_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225446400'),
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206477824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410056] = {
			["ID"] = 6410056,
			["Enum"] = "DEFEQUIP_REVERT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225446656'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493882112'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410057] = {
			["ID"] = 6410057,
			["Enum"] = "REFRESH_GACHA_SHOP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225446912'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024774396672'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410058] = {
			["ID"] = 6410058,
			["Enum"] = "ENFORCE_FELLOWPIECE_COMPOSITION",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225447168'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493882624'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410059] = {
			["ID"] = 6410059,
			["Enum"] = "REPLACE_ONLINE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225447424'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493882880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410060] = {
			["ID"] = 6410060,
			["Enum"] = "REPLACE_FORCED_OFFLINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225447680'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493883136'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410061] = {
			["ID"] = 6410061,
			["Enum"] = "ENTER_PIRATE_SCHOOL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225447936'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493883392'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410062] = {
			["ID"] = 6410062,
			["Enum"] = "LEAVE_DUNGEON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225448192'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493868288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410063] = {
			["ID"] = 6410063,
			["Enum"] = "SEALED_RANK_UP_CONSUME_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225448448'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493883904'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410064] = {
			["ID"] = 6410064,
			["Enum"] = "SEALED_RANDOM_HIGH_RESON_ABANDON_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225448704'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410065] = {
			["ID"] = 6410065,
			["Enum"] = "SEALED_RANDOM_LOW_RESON_RESERVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225448960'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410066] = {
			["ID"] = 6410066,
			["Enum"] = "SEALED_RANDOM_RESULT_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225449216'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566016'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762320128'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030755584'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410067] = {
			["ID"] = 6410067,
			["Enum"] = "EQUIP_TAB1_LOWER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225449472'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493884928'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410068] = {
			["ID"] = 6410068,
			["Enum"] = "EQUIP_TAB1_UNBIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225449728'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493884928'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6410069] = {
			["ID"] = 6410069,
			["Enum"] = "EQUIP_TAB2_ALREADY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225449984'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493885440'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762320896'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_32300569990656'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410070] = {
			["ID"] = 6410070,
			["Enum"] = "EQUIP_WORDREVERT_SINGLE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225450240'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493885696'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410071] = {
			["ID"] = 6410071,
			["Enum"] = "EQUIP_WORDREVERT_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225450496'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493885696'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410072] = {
			["ID"] = 6410072,
			["Enum"] = "ITEM_PACK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225450752'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493886208'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410073] = {
			["ID"] = 6410073,
			["Enum"] = "TAB_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225451008'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493886464'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6410074] = {
			["ID"] = 6410074,
			["Enum"] = "LEAVE_ARENA12V12_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225451264'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493886720'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415067] = {
			["ID"] = 6415067,
			["Enum"] = "SEFIROT_CORE_SWITCH_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225451520'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493886976'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415002] = {
			["ID"] = 6415002,
			["Enum"] = "HOT_PATCH_DISCONNECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225451776'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493887232'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415003] = {
			["ID"] = 6415003,
			["Enum"] = "HOT_PATCH_SPACE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225452032'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493887488'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415004] = {
			["ID"] = 6415004,
			["Enum"] = "CLIENT_ON_DISCONNECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225452288'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093225452288'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415005] = {
			["ID"] = 6415005,
			["Enum"] = "RECONNECT_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225452544'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093225452544'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415006] = {
			["ID"] = 6415006,
			["Enum"] = "CROSS_WORD_PUZZLE_CORRECT",
			["Content"] = "%s",
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493888256'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415007] = {
			["ID"] = 6415007,
			["Enum"] = "CONNECT_SERVER_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225453056'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093225453056'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415008] = {
			["ID"] = 6415008,
			["Enum"] = "MONEY_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225453312'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493888768'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415009] = {
			["ID"] = 6415009,
			["Enum"] = "FRIEND_CLUB_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225453568'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889024'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415010] = {
			["ID"] = 6415010,
			["Enum"] = "FRIEND_CLUB_CLEAR_MSG",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225453824'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889280'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415011] = {
			["ID"] = 6415011,
			["Enum"] = "FRIEND_SETTING_WARNING_PLEASE_ADD_FIRST",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225454080'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415012] = {
			["ID"] = 6415012,
			["Enum"] = "FRIEND_CLUB_KICK_OUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225454336'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889792'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415013] = {
			["ID"] = 6415013,
			["Enum"] = "FRIEND_GROUP_DELETE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225454592'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493890048'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415014] = {
			["ID"] = 6415014,
			["Enum"] = "GUILD_INVITE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225454848'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493890304'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030761216'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415015] = {
			["ID"] = 6415015,
			["Enum"] = "HOT_PATCH_FIX",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225455104'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493890560'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415016] = {
			["ID"] = 6415016,
			["Enum"] = "TEAM_CONVERT_TARGET_EMPTY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225455360'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493890816'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415017] = {
			["ID"] = 6415017,
			["Enum"] = "TEAM_CONVERT_MORE_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225455616'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493890816'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415018] = {
			["ID"] = 6415018,
			["Enum"] = "GUILD_MERGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225455872'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891328'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415019] = {
			["ID"] = 6415019,
			["Enum"] = "MONEY_NEEDRMB",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225456128'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891584'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030762496'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415020] = {
			["ID"] = 6415020,
			["Enum"] = "MONEY_RMBEXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225456384'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891840'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415021] = {
			["ID"] = 6415021,
			["Enum"] = "SDK_LOGOUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225456640'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892096'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415022] = {
			["ID"] = 6415022,
			["Enum"] = "TEAM_INPLACE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225456896'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892352'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415023] = {
			["ID"] = 6415023,
			["Enum"] = "TEAM_DISMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225457152'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892608'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415024] = {
			["ID"] = 6415024,
			["Enum"] = "TEAM_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225457408'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892864'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415025] = {
			["ID"] = 6415025,
			["Enum"] = "MONEY_NORMB",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225457664'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891584'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030762496'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415026] = {
			["ID"] = 6415026,
			["Enum"] = "PENCE_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225457920'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493893376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030764288'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415027] = {
			["ID"] = 6415027,
			["Enum"] = "BLACKLIST_GIVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225458176'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415028] = {
			["ID"] = 6415028,
			["Enum"] = "FRIEND_GIVE_ATTRACTION_MAX",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225458432'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415029] = {
			["ID"] = 6415029,
			["Enum"] = "READY_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225458688'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 60,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415030] = {
			["ID"] = 6415030,
			["Enum"] = "SEND_APPLICATION_CONFIRM_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225458944'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415031] = {
			["ID"] = 6415031,
			["Enum"] = "GROUP_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225459200'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415032] = {
			["ID"] = 6415032,
			["Enum"] = "GROUP_LEADER_TRANSFER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225459456'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415033] = {
			["ID"] = 6415033,
			["Enum"] = "GROUP_KICK_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225459712'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415034] = {
			["ID"] = 6415034,
			["Enum"] = "GROUP_CONVENE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225459968'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415035] = {
			["ID"] = 6415035,
			["Enum"] = "MODULE_UNLOCK_TASK_PROMPT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225460224'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415036] = {
			["ID"] = 6415036,
			["Enum"] = "ENTER_CROSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225460480'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493895936'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415037] = {
			["ID"] = 6415037,
			["Enum"] = "SEND_APPLICATION_CONFIRM_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225458944'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493896192'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415038] = {
			["ID"] = 6415038,
			["Enum"] = "INVENTORY_SLOT_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225460992'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493896448'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415039] = {
			["ID"] = 6415039,
			["Enum"] = "WAREHOUSE_SLOT_UNLOCK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225461248'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493896704'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415040] = {
			["ID"] = 6415040,
			["Enum"] = "GUILD_PARTY_INVITE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225461504'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493896960'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 30,
			["TimeUPSelect"] = 1,
			["TodayNoPopUp"] = false,
		},
		[6415041] = {
			["ID"] = 6415041,
			["Enum"] = "GUILD_PARTY_BUFF_REFRESH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225461760'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415042] = {
			["ID"] = 6415042,
			["Enum"] = "GUILD_PARTY_RARE_BUFF_REFRESH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225462016'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415043] = {
			["ID"] = 6415043,
			["Enum"] = "GUILD_PARTY_BUFF_MONEY_EXCHANGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225462272'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493893376'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030764288'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415044] = {
			["ID"] = 6415044,
			["Enum"] = "CRAZY_MODE_DOUBLE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225462528'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415045] = {
			["ID"] = 6415045,
			["Enum"] = "DELETE_FRIEND",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225462784'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493898240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415046] = {
			["ID"] = 6415046,
			["Enum"] = "ADDBLACKLIST",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225463040'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493898496'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415047] = {
			["ID"] = 6415047,
			["Enum"] = "SDK_LOGIN_FAILED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225463296'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493898752'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415048] = {
			["ID"] = 6415048,
			["Enum"] = "EXIT_ESCORTTASKPLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225463552'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493899008'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_58275190024192'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415049] = {
			["ID"] = 6415049,
			["Enum"] = "LEAVE_ARENA50V50_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225463808'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493899264'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415052] = {
			["ID"] = 6415052,
			["Enum"] = "CASHMARKET_CANCELCONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225464064'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493899520'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415053] = {
			["ID"] = 6415053,
			["Enum"] = "ACTIVITY_OPEN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225464320'),
			["Title"] = "",
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 2,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415054] = {
			["ID"] = 6415054,
			["Enum"] = "RECIPE_DELETE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225464576'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493900032'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415055] = {
			["ID"] = 6415055,
			["Enum"] = "EXCHANGE_OVER50_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225464832'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493900288'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415056] = {
			["ID"] = 6415056,
			["Enum"] = "BID_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225465088'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493900544'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415057] = {
			["ID"] = 6415057,
			["Enum"] = "AUCTION_MAX_PRICE_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225465344'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493900800'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415058] = {
			["ID"] = 6415058,
			["Enum"] = "ROLE_CREATE_UNFINISHED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225465600'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493901056'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415059] = {
			["ID"] = 6415059,
			["Enum"] = "SCHEDULE_USED_TOTAL_DORPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225465856'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61025848193280'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415060] = {
			["ID"] = 6415060,
			["Enum"] = "ATALASHI_VERSION_IGNORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493901568'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762306304'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61093493870848'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415061] = {
			["ID"] = 6415061,
			["Enum"] = "ATALASHI_VERSION_QUIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225435392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493901568'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61093493870848'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415062] = {
			["ID"] = 6415062,
			["Enum"] = "TEAM_FOLLOW_SINGLE_PLANE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225466624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415063] = {
			["ID"] = 6415063,
			["Enum"] = "GUILD_MANAGE_RESIGN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225466880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415064] = {
			["ID"] = 6415064,
			["Enum"] = "ROLEPLAY_PROMOTION",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225467136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493902592'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415065] = {
			["ID"] = 6415065,
			["Enum"] = "GUILD_MATERIAL_TASK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225467392'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493902848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415066] = {
			["ID"] = 6415066,
			["Enum"] = "GUILD_MATERIAL_5TASK_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225467648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493903104'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415068] = {
			["ID"] = 6415068,
			["Enum"] = "SEALED_UPGRADE_RESET",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225467904'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493903360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415069] = {
			["ID"] = 6415069,
			["Enum"] = "SEALED_REFINE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225468160'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493903616'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415070] = {
			["ID"] = 6415070,
			["Enum"] = "SEALED_UPGRADE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225468416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493903872'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415071] = {
			["ID"] = 6415071,
			["Enum"] = "REFINE_REPLACE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225468672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493904128'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415072] = {
			["ID"] = 6415072,
			["Enum"] = "SEALED_EQUIP_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225468928'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415073] = {
			["ID"] = 6415073,
			["Enum"] = "GUILDBATTLEFIELD_QUIT_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225469184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493899008'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415074] = {
			["ID"] = 6415074,
			["Enum"] = "GUILDBATTLEFIELD_ENTER_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225469440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493904896'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 2,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415075] = {
			["ID"] = 6415075,
			["Enum"] = "IOS_GPU_CAPTURE_FRAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225469696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493905152'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762340608'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030776064'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415076] = {
			["ID"] = 6415076,
			["Enum"] = "MAIN_PLAYER_ENTITY_CREATE_FAILED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225469952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493905408'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415077] = {
			["ID"] = 6415077,
			["Enum"] = "DANCINGPARTY_PARTNER_RELIEVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225470208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493905664'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415078] = {
			["ID"] = 6415078,
			["Enum"] = "DANCINGPARTY_PARTNER_RELIEVE_LEAVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225470464'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415079] = {
			["ID"] = 6415079,
			["Enum"] = "CUSTOMROLE_BACK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225470720'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415080] = {
			["ID"] = 6415080,
			["Enum"] = "CUSTOMROLE_CONTINUELAST",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225470976'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415081] = {
			["ID"] = 6415081,
			["Enum"] = "COMMON_EMPTY_ENSURE_DIALOG",
			["Content"] = "%s",
			["Title"] = "%s",
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415082] = {
			["ID"] = 6415082,
			["Enum"] = "KICK_OUT_SERVER_SELECT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225471488'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493906944'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415083] = {
			["ID"] = 6415083,
			["Enum"] = "KICK_OUT_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225471744'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493906944'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415084] = {
			["ID"] = 6415084,
			["Enum"] = "KICK_OUT_HOTFIX",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225472000'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493906944'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415085] = {
			["ID"] = 6415085,
			["Enum"] = "ACCOUNT_BAN_KICK_OUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225472256'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493906944'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415086] = {
			["ID"] = 6415086,
			["Enum"] = "FASHION_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225472512'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762343424'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030778880'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415087] = {
			["ID"] = 6415087,
			["Enum"] = "FASHION_PURCHASE_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225472768'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415088] = {
			["ID"] = 6415088,
			["Enum"] = "FASHION_GO_PHOTOGRAPHING_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225473024'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762343936'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415089] = {
			["ID"] = 6415089,
			["Enum"] = "FASHION_PLAN_RESTORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225473280'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415090] = {
			["ID"] = 6415090,
			["Enum"] = "FASHION_ADJUST_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225473536'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415091] = {
			["ID"] = 6415091,
			["Enum"] = "FASHION_PLAN_REPLACE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225473792'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493909248'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030780160'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415092] = {
			["ID"] = 6415092,
			["Enum"] = "FASHION_PLAN_SAVE",
			["Content"] = "",
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493909504'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415093] = {
			["ID"] = 6415093,
			["Enum"] = "FASHION_MAKEUP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225474304'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415094] = {
			["ID"] = 6415094,
			["Enum"] = "FASHION_CANT_BUY_TIPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225474560'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891584'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415095] = {
			["ID"] = 6415095,
			["Enum"] = "FASHION_MAKEUP_QUIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225474816'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415096] = {
			["ID"] = 6415096,
			["Enum"] = "FASHION_MAKEUP_RESET",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225475072'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_18212003515392'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415097] = {
			["ID"] = 6415097,
			["Enum"] = "CUSTOMROLE_PANEL_CLOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225475328'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415098] = {
			["ID"] = 6415098,
			["Enum"] = "SCENE_CUSTOM_CHANGE_NAME",
			["Content"] = "",
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493911040'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 4,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415100] = {
			["ID"] = 6415100,
			["Enum"] = "WORLD_BOSS_KILL_FAIL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225475840'),
			["Title"] = Game.TableDataManager:GetLangStr('str_32300033120256'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415101] = {
			["ID"] = 6415101,
			["Enum"] = "WORLD_BOSS_LEAVE_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225476096'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493911552'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030782464'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415102] = {
			["ID"] = 6415102,
			["Enum"] = "WORLD_BOSS_ENTER_NEW_BOSS_AREA",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225476352'),
			["Title"] = Game.TableDataManager:GetLangStr('str_32300033120256'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030782720'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415103] = {
			["ID"] = 6415103,
			["Enum"] = "WORLD_BOSS_CAPTAIN_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225476608'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493912064'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030782976'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415104] = {
			["ID"] = 6415104,
			["Enum"] = "WORLD_BOSS_TEAM_MEMBER_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225476864'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493912064'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415105] = {
			["ID"] = 6415105,
			["Enum"] = "WORLD_BOSS_INDIVIDUAL_CHANGE_LINE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225477120'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493912064'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030782976'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415106] = {
			["ID"] = 6415106,
			["Enum"] = "WORLD_BOSS_MERGE_TEAM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225477376'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493912832'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030783744'),
			["DialogType"] = 0,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415107] = {
			["ID"] = 6415107,
			["Enum"] = "ACTIVITY_OPEN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225464320'),
			["Title"] = "",
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 2,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415109] = {
			["ID"] = 6415109,
			["Enum"] = "WORLD_BOSS_CAPTAIN_TRANSFER_PERMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225477888'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493913344'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415110] = {
			["ID"] = 6415110,
			["Enum"] = "WORLD_BOSS_TEAM_MEMBER_TRANSFER_PERMISSION",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225478144'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493913344'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415108] = {
			["ID"] = 6415108,
			["Enum"] = "EQUIPMENT_IN_SUIT_DECOMPOSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225478400'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493913856'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415111] = {
			["ID"] = 6415111,
			["Enum"] = "EQUIPMENT_IN_SUIT_TO_WAREHOUSE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225478656'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493914112'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415200] = {
			["ID"] = 6415200,
			["Enum"] = "CUSTOMROLE_RESETDATA",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225478912'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415300] = {
			["ID"] = 6415300,
			["Enum"] = "SUBMIT_FINAL_PRICE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225479168'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415301] = {
			["ID"] = 6415301,
			["Enum"] = "ARBITRATOR_GIVE_UP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225479424'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493914880'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415302] = {
			["ID"] = 6415302,
			["Enum"] = "DANCE_DOUBLE_MATCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225479680'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415303] = {
			["ID"] = 6415303,
			["Enum"] = "DANCE_ENERGY_USE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225479936'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415304] = {
			["ID"] = 6415304,
			["Enum"] = "DANCE_EXIT_MIDWAY_CHALLENGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225480192'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415305] = {
			["ID"] = 6415305,
			["Enum"] = "DANCE_PARTNER_EXIT_MIDWAY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225480448'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415306] = {
			["ID"] = 6415306,
			["Enum"] = "DANCE_EXIT_MIDWAY_PERFORM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225480704'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415307] = {
			["ID"] = 6415307,
			["Enum"] = "DANCE_EXIT_MIDWAY_PERFORM_ENERGY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225480960'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415308] = {
			["ID"] = 6415308,
			["Enum"] = "GROUP_LEADER_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225481216'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415309] = {
			["ID"] = 6415309,
			["Enum"] = "GROUP_EXIT_IN_LEAGUE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225481472'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415310] = {
			["ID"] = 6415310,
			["Enum"] = "TEAM_EXIT_IN_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225481728'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415311] = {
			["ID"] = 6415311,
			["Enum"] = "LOCATION_TRACE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225481984'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415312] = {
			["ID"] = 6415312,
			["Enum"] = "SWITCH_BATTLE_LINE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225482240'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_61093762353152'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030788608'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415313] = {
			["ID"] = 6415313,
			["Enum"] = "MATCHCE_LOW_REMIND",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225482496'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415314] = {
			["ID"] = 6415314,
			["Enum"] = "NEWBIE_GUIDE_SKIP_GROUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225482752'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415315] = {
			["ID"] = 6415315,
			["Enum"] = "SERVER_BUSY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225483008'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493918464'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415316] = {
			["ID"] = 6415316,
			["Enum"] = "SOCIAL_FRIEND_GIFT_BUY",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225483264'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493918720'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415318] = {
			["ID"] = 6415318,
			["Enum"] = "SOCIAL_FRIEND_GIFT_BUY_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225483520'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493918976'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415319] = {
			["ID"] = 6415319,
			["Enum"] = "DELETE_EMOJI_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225483776'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493919232'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415320] = {
			["ID"] = 6415320,
			["Enum"] = "REMOVE_GROUPCHAT_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225484032'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493919488'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415321] = {
			["ID"] = 6415321,
			["Enum"] = "REMOVE_LOT_GROUPCHAT_MEMBER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225484288'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493919488'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415322] = {
			["ID"] = 6415322,
			["Enum"] = "CLEAR_CHAT_MESSAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225484544'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493920000'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415323] = {
			["ID"] = 6415323,
			["Enum"] = "WATERPIPE_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225484800'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415324] = {
			["ID"] = 6415324,
			["Enum"] = "MAP_CANCEL_INPUT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225485056'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415325] = {
			["ID"] = 6415325,
			["Enum"] = "CHATROOM_CHANGE_ROOM_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225485312'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415326] = {
			["ID"] = 6415326,
			["Enum"] = "CHATROOM_PERMISSION_NOT_SAVE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225485568'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415327] = {
			["ID"] = 6415327,
			["Enum"] = "CHATROOM_BLACKLIST_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225485824'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415328] = {
			["ID"] = 6415328,
			["Enum"] = "CHATROOM_EXIST_COMFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225486080'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415329] = {
			["ID"] = 6415329,
			["Enum"] = "CHATROOM_PIN_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225486336'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415330] = {
			["ID"] = 6415330,
			["Enum"] = "CHATROOM_PIN_CANCEL_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225486592'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415331] = {
			["ID"] = 6415331,
			["Enum"] = "TURNTABLE_RESET_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225486848'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415332] = {
			["ID"] = 6415332,
			["Enum"] = "CLUB_REMOVE_OFFICIAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225487104'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415333] = {
			["ID"] = 6415333,
			["Enum"] = "CLUB_EXCHANGE_OFFICIAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225487360'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415334] = {
			["ID"] = 6415334,
			["Enum"] = "CLUB_TRANSFER_BOSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225487616'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415335] = {
			["ID"] = 6415335,
			["Enum"] = "CLUB_RRSIGN_BOSS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225487872'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415336] = {
			["ID"] = 6415336,
			["Enum"] = "CLUB_OFFICIAL_CHANGE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225488128'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415337] = {
			["ID"] = 6415337,
			["Enum"] = "CLUB_RESPOND_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225488384'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415338] = {
			["ID"] = 6415338,
			["Enum"] = "CLUB_APPLY_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225488640'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415339] = {
			["ID"] = 6415339,
			["Enum"] = "MAIL_DETACH_ALL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225488896'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415340] = {
			["ID"] = 6415340,
			["Enum"] = "MAIL_COLLECT_CANCEL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225489152'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415341] = {
			["ID"] = 6415341,
			["Enum"] = "GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_LIMIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225489408'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892864'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415342] = {
			["ID"] = 6415342,
			["Enum"] = "PVP_3V3_IN_TEAM_GROUP_MATCH_QUIT_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225489664'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493892864'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415343] = {
			["ID"] = 6415343,
			["Enum"] = "GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_TEAM_LIMIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225489920'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493911552'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415344] = {
			["ID"] = 6415344,
			["Enum"] = "CONSECRATION_MONUMENT_LEVELUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225490176'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493925632'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030796544'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415345] = {
			["ID"] = 6415345,
			["Enum"] = "CHATROOM_OWNER_ASSIGNMENT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225490432'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493925888'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415346] = {
			["ID"] = 6415346,
			["Enum"] = "LOGIN_QUEUE_SWITCH_SERVER",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225490688'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493926144'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415347] = {
			["ID"] = 6415347,
			["Enum"] = "LOGIN_QUEUE_BACK_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225490944'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493926400'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415348] = {
			["ID"] = 6415348,
			["Enum"] = "LOGIN_QUEUE_FINISH_ENTER_GAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225491200'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54838947749632'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415349] = {
			["ID"] = 6415349,
			["Enum"] = "LOGIN_QUEUE_FINISH_CHECK_ENTER_GAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225491456'),
			["Title"] = Game.TableDataManager:GetLangStr('str_54838947749632'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415350] = {
			["ID"] = 6415350,
			["Enum"] = "MANOR_RECYCLE_FURNITURE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225491712'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493927168'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415351] = {
			["ID"] = 6415351,
			["Enum"] = "MANOR_RECYCLE_PCG",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225491968'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493927424'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415352] = {
			["ID"] = 6415352,
			["Enum"] = "ELEMENT_POINT_RESET_CHECK",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225492224'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493927680'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415353] = {
			["ID"] = 6415353,
			["Enum"] = "TAROTTEAM_LEAVE_RESPONDED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225492480'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493927936'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415354] = {
			["ID"] = 6415354,
			["Enum"] = "TAROTTEAM_KICK_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225492736'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493928192'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415355] = {
			["ID"] = 6415355,
			["Enum"] = "TAROTTEAM_LEAVE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225492992'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493928448'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415356] = {
			["ID"] = 6415356,
			["Enum"] = "TAROTTEAM_LEAVE_LASTONE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225493248'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493928448'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415357] = {
			["ID"] = 6415357,
			["Enum"] = "TAROTTEAM_UNCREATE_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225493504'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493875968'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415358] = {
			["ID"] = 6415358,
			["Enum"] = "TAROTTEAM_IMPEACH_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225493760'),
			["Title"] = Game.TableDataManager:GetLangStr('str_56213337302784'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415359] = {
			["ID"] = 6415359,
			["Enum"] = "TAROTTEAM_FOUND_CEREMONY_POPUP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225494016'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493929472'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415360] = {
			["ID"] = 6415360,
			["Enum"] = "TAROTTEAM_TRANSFER_CAPTAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225494272'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493929728'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415361] = {
			["ID"] = 6415361,
			["Enum"] = "TAROTTEAM_APPLY_LINK_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225494528'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493929984'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415362] = {
			["ID"] = 6415362,
			["Enum"] = "TASK_FAILURE_AGAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225494784'),
			["Title"] = Game.TableDataManager:GetLangStr('str_25839328566528'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415363] = {
			["ID"] = 6415363,
			["Enum"] = "TAROTTEAM_IMPEACH_POPUP_CAPTAIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225495040'),
			["Title"] = Game.TableDataManager:GetLangStr('str_56213337302784'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415364] = {
			["ID"] = 6415364,
			["Enum"] = "COLLECTIBLES_JUMP_TO_CUTSCENE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225495296'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493930752'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415365] = {
			["ID"] = 6415365,
			["Enum"] = "LEAVE_ARENA_CHAMPION_PREPARE_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225495552'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493931008'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415366] = {
			["ID"] = 6415366,
			["Enum"] = "LEAVE_ARENA_CHAMPION_MATCH_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225495808'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493931264'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415367] = {
			["ID"] = 6415367,
			["Enum"] = "IN_QUEUE_HINT_USE_SDK_LOGIN",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225496064'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415368] = {
			["ID"] = 6415368,
			["Enum"] = "CHATROOM_BAN_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225496320'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415369] = {
			["ID"] = 6415369,
			["Enum"] = "SETTING_RESET_PAGE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225496576'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493886464'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415370] = {
			["ID"] = 6415370,
			["Enum"] = "CHATROOM_PERMANENT_ROOM_RENEWAL",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225496832'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415371] = {
			["ID"] = 6415371,
			["Enum"] = "CHATROOM_PERMANENT_ROOM_DISSOLVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225497088'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493932544'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415372] = {
			["ID"] = 6415372,
			["Enum"] = "CHATROOM_IDENTITY_TAG_REMOVE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225497344'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415373] = {
			["ID"] = 6415373,
			["Enum"] = "CHATROOM_IDENTITY_STATE_SWITCH",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225497600'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = true,
		},
		[6415374] = {
			["ID"] = 6415374,
			["Enum"] = "PVP_CHAMPION_QUIT_TROOP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225497856'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493933312'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415375] = {
			["ID"] = 6415375,
			["Enum"] = "PVP_CHAMPION_DISBAND_TROOP_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225498112'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493933568'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415376] = {
			["ID"] = 6415376,
			["Enum"] = "PVP_CHAMPION_REMOVE_TROOP_MEMBER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225498368'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493933824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415377] = {
			["ID"] = 6415377,
			["Enum"] = "PVP_CHAMPION_TRANSFER_LEADER_CONFIRM",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225498624'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493929728'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415378] = {
			["ID"] = 6415378,
			["Enum"] = "PVP_CHAMPION_DISBAND_TROOP_CONFIRM_SINGLE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225498880'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493933312'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415379] = {
			["ID"] = 6415379,
			["Enum"] = "CHATROOM_IDENTITY_CHANGE_NAME",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225499136'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493934592'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 4,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415380] = {
			["ID"] = 6415380,
			["Enum"] = "MANOR_CHANG_NAME",
			["Content"] = "",
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493934848'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 4,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415381] = {
			["ID"] = 6415381,
			["Enum"] = "MONEY_CONSUME_BOUND_POUNDS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225499648'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493891840'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415382] = {
			["ID"] = 6415382,
			["Enum"] = "TEAM_ALL_TEAM_APPLY_TIPS",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225499904'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493935360'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415383] = {
			["ID"] = 6415383,
			["Enum"] = "BASICDANCE_INITIATOR",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225500160'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415384] = {
			["ID"] = 6415384,
			["Enum"] = "BASICDANCE_ACCEPTOR",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225500416'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415385] = {
			["ID"] = 6415385,
			["Enum"] = "BASICDANCE_MULTIPLAYER_DANCE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225500672'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415386] = {
			["ID"] = 6415386,
			["Enum"] = "BASICDANCE_TEAM_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225500928'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = "",
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415387] = {
			["ID"] = 6415387,
			["Enum"] = "BASICDANCE_STAGE_EXIT",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225501184'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415388] = {
			["ID"] = 6415388,
			["Enum"] = "MEMBER_ADJUST_QUIT_NOT_SAVED",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225501440'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415389] = {
			["ID"] = 6415389,
			["Enum"] = "MAIL_REMOVE_FROM_FAV",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225501696'),
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206477824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415390] = {
			["ID"] = 6415390,
			["Enum"] = "MAIL_REMOVE_FROM_FAV_DELETE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225501952'),
			["Title"] = Game.TableDataManager:GetLangStr('str_26114206477824'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
		[6415391] = {
			["ID"] = 6415391,
			["Enum"] = "PHOTOGRAPH_RESTORE",
			["Content"] = Game.TableDataManager:GetLangStr('str_61093225502208'),
			["Title"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["RefuseBtn"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["AcceptBtn"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["DialogType"] = 0,
			["CountDown"] = 0,
			["TimeUPSelect"] = 0,
			["TodayNoPopUp"] = false,
		},
	},
}

return TopData
