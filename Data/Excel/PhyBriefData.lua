--
-- 表名: $ShowProperty_属性展示.xlsx  页名：$PhyBrief_现实简要
--

local TopData = {
	data = {
		[2] = {
			["ID"] = 2,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329554432'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Con"},
			["ShowType"] = 0,
			["TipsID"] = 6421202,
		},
		[3] = {
			["ID"] = 3,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329554944'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Pow"},
			["ShowType"] = 0,
			["TipsID"] = 6421203,
		},
		[4] = {
			["ID"] = 4,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_46043391592192'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Str"},
			["ShowType"] = 0,
			["TipsID"] = 6421204,
		},
		[5] = {
			["ID"] = 5,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329555968'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Int"},
			["ShowType"] = 0,
			["TipsID"] = 6421205,
		},
		[6] = {
			["ID"] = 6,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329556480'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Dex"},
			["ShowType"] = 0,
			["TipsID"] = 6421206,
		},
		[7] = {
			["ID"] = 7,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992493569'),
			["CalculateType"] = 3,
			["ShowProperty"] = {"pAtkMin", "pAtkMax"},
			["ShowType"] = 0,
			["TipsID"] = 6421207,
		},
		[8] = {
			["ID"] = 8,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992493570'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pPierce"},
			["ShowType"] = 0,
			["TipsID"] = 6421208,
		},
		[9] = {
			["ID"] = 9,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992498178'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCrit"},
			["ShowType"] = 0,
			["TipsID"] = 6421209,
		},
		[10] = {
			["ID"] = 10,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210377216'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCritHurt"},
			["ShowType"] = 1,
			["TipsID"] = 6421210,
		},
		[11] = {
			["ID"] = 11,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_52915339267840'),
			["CalculateType"] = 5,
			["ShowProperty"] = {},
			["ShowType"] = 0,
			["TipsID"] = 0,
		},
		[12] = {
			["ID"] = 12,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
			["CalculateType"] = 3,
			["ShowProperty"] = {"pDef", "mDef"},
			["ShowType"] = 0,
			["TipsID"] = 6421216,
		},
		[13] = {
			["ID"] = 13,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210374144'),
			["CalculateType"] = 3,
			["ShowProperty"] = {"pBlock", "mBlock"},
			["ShowType"] = 0,
			["TipsID"] = 6421217,
		},
		[14] = {
			["ID"] = 14,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210376960'),
			["CalculateType"] = 3,
			["ShowProperty"] = {"pCritAnti", "mCritAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421218,
		},
		[15] = {
			["ID"] = 15,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210377984'),
			["CalculateType"] = 3,
			["ShowProperty"] = {"pCritDef", "mCritDef"},
			["ShowType"] = 1,
			["TipsID"] = 6421219,
		},
	},
}

return TopData
