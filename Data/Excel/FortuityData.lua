--
-- 表名: $Fortuity_奇遇.xlsx  页名：$Fortuity_奇遇触发
--

local TopData = {
	data = {
		[6490001] = {
			["ID"] = 6490001,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnReceive()",
			["TriggerRate"] = 1,
			["LevelMapID"] = 0,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490002] = {
			["ID"] = 6490002,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 40,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnCoordinatesPoint(5200002,-11846,-19170,-1297,500)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 0,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {1, 2},
			["TimeLimit"] = {6, 12},
			["WeatherLimit"] = {1},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490003] = {
			["ID"] = 6490003,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 50,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnTrigger(3530489326)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5209996,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {2},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490004] = {
			["ID"] = 6490004,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 60,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnDialogue(10006014)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 0,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490005] = {
			["ID"] = 6490005,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682753024'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 70,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnInteract(2433715724)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 0,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {1},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 970037,
			["EndTaskRing"] = {970037},
		},
		[6490007] = {
			["ID"] = 6490007,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 2,
			["CycleGap"] = 600,
			["FailGap"] = 90,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnInstanceBehavior(3530489326,500,3001)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5209996,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490008] = {
			["ID"] = 6490008,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 2,
			["CycleGap"] = 600,
			["FailGap"] = 0,
			["SuccessSystemAction"] = {
                {
                    {
                        ["Caller"] = "",
                        ["FuncName"] = "SendTaskItem",
                        ["FuncParamInfos"] = {
                            ["ItemID"] = 2003083,
                            ["Count"] = 1
                        },
                        ["FuncArgInfos"] = {2003083, 1}
                    }
                }
            },
			["Trigger"] = "OnItem(2003082)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5209996,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {1, 3},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490009] = {
			["ID"] = 6490009,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682752000'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 10,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnCoordinatesPoint(5200002,-11846,-19170,-1297,500)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 0,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990025,
			["EndTaskRing"] = {990025},
		},
		[6490010] = {
			["ID"] = 6490010,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_58962116497408'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnTrigger(1661288419)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990041,
			["EndTaskRing"] = {990041},
		},
		[6490011] = {
			["ID"] = 6490011,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_58962116498944'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnTrigger(3439197980)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {{{6490010, false}}},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990042,
			["EndTaskRing"] = {990042},
		},
		[6490012] = {
			["ID"] = 6490012,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_58962116502528'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnTrigger(2759303449)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {{{6490011, false}}},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990043,
			["EndTaskRing"] = {990043},
		},
		[6490013] = {
			["ID"] = 6490013,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_25083682754816'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnTrigger(1892384285)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990044,
			["EndTaskRing"] = {990044},
		},
		[6490014] = {
			["ID"] = 6490014,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_31405874642176'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnItem(2099003)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990040,
			["EndTaskRing"] = {990040},
		},
		[6490015] = {
			["ID"] = 6490015,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_56562034965760'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnCoordinatesPoint(5200085,-2566,-13250,30,500)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200085,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990045,
			["EndTaskRing"] = {990045},
		},
		[6490016] = {
			["ID"] = 6490016,
			["FortuityName"] = Game.TableDataManager:GetLangStr('str_29756607207680'),
			["FortuityType"] = 1,
			["CycleGap"] = 0,
			["FailGap"] = 30,
			["SuccessSystemAction"] = {},
			["Trigger"] = "OnCollect(2401001)",
			["TriggerRate"] = 1,
			["LevelMapID"] = 5200002,
			["Sex"] = -1,
			["ProfessionID"] = {},
			["RolePlayState"] = {},
			["TimeLimit"] = {},
			["WeatherLimit"] = {},
			["TaskRingLimit"] = {},
			["FortuityLimit"] = {},
			["bReceiveReminder"] = true,
			["bFinishReminder"] = true,
			["ActivateTaskRing"] = 990005,
			["EndTaskRing"] = {990005},
		},
	},
}

return TopData
