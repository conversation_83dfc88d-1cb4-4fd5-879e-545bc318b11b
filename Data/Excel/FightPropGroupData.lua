--
-- 表名: FightPropGroupData(后处理)
--

local TopData = {
    fightPropGroupSubKey2PropMap = {
        ['ProHurtMulti'] = {[1200001] = 'SunHurtMulti', [1200002] = 'VisionaryHurtMulti', [1200003] = 'FeatherwitHurtMulti', [1200004] = 'ArbiterHurtMulti', [1200005] = 'ApprenticeHurtMulti', [1200006] = 'WarriorHurtMulti', [1200007] = 'ProHurtMulti9', }, 
        ['ProHurtMulti_F'] = {[1200001] = 'SunHurtMulti_F', [1200002] = 'VisionaryHurtMulti_F', [1200003] = 'FeatherwitHurtMulti_F', [1200004] = 'ArbiterHurtMulti_F', [1200005] = 'ApprenticeHurtMulti_F', [1200006] = 'WarriorHurtMulti_F', [1200007] = 'ProHurtMulti9_F', }, 
        ['ProHurtMulti_N'] = {[1200001] = 'SunHurtMulti_N', [1200002] = 'VisionaryHurtMulti_N', [1200003] = 'FeatherwitHurtMulti_N', [1200004] = 'ArbiterHurtMulti_N', [1200005] = 'ApprenticeHurtMulti_N', [1200006] = 'WarriorHurtMulti_N', [1200007] = 'ProHurtMulti9_N', }, 
        ['ProHurtMulti_P'] = {[1200001] = 'SunHurtMulti_P', [1200002] = 'VisionaryHurtMulti_P', [1200003] = 'FeatherwitHurtMulti_P', [1200004] = 'ArbiterHurtMulti_P', [1200005] = 'ApprenticeHurtMulti_P', [1200006] = 'WarriorHurtMulti_P', [1200007] = 'ProHurtMulti9_P', }, 
        ['ProHurtReduce'] = {[1200001] = 'SunHurtReduce', [1200002] = 'VisionaryHurtReduce', [1200003] = 'FeatherwitHurtReduce', [1200004] = 'ArbiterHurtReduce', [1200005] = 'ApprenticeHurtReduce', [1200006] = 'WarriorHurtReduce', [1200007] = 'ProHurtReduce9', }, 
        ['ProHurtReduce_F'] = {[1200001] = 'SunHurtReduce_F', [1200002] = 'VisionaryHurtReduce_F', [1200003] = 'FeatherwitHurtReduce_F', [1200004] = 'ArbiterHurtReduce_F', [1200005] = 'ApprenticeHurtReduce_F', [1200006] = 'WarriorHurtReduce_F', [1200007] = 'ProHurtReduce9_F', }, 
        ['ProHurtReduce_N'] = {[1200001] = 'SunHurtReduce_N', [1200002] = 'VisionaryHurtReduce_N', [1200003] = 'FeatherwitHurtReduce_N', [1200004] = 'ArbiterHurtReduce_N', [1200005] = 'ApprenticeHurtReduce_N', [1200006] = 'WarriorHurtReduce_N', [1200007] = 'ProHurtReduce9_N', }, 
        ['ProHurtReduce_P'] = {[1200001] = 'SunHurtReduce_P', [1200002] = 'VisionaryHurtReduce_P', [1200003] = 'FeatherwitHurtReduce_P', [1200004] = 'ArbiterHurtReduce_P', [1200005] = 'ApprenticeHurtReduce_P', [1200006] = 'WarriorHurtReduce_P', [1200007] = 'ProHurtReduce9_P', }, 
        ['RaceHurtMulti'] = {[0] = 'RaceHurtMulti1', [1] = 'RaceHurtMulti2', [2] = 'RaceHurtMulti3', [3] = 'RaceHurtMulti4', [4] = 'RaceHurtMulti5', }, 
        ['RaceHurtMulti_F'] = {[0] = 'RaceHurtMulti1_F', [1] = 'RaceHurtMulti2_F', [2] = 'RaceHurtMulti3_F', [3] = 'RaceHurtMulti4_F', [4] = 'RaceHurtMulti5_F', }, 
        ['RaceHurtMulti_N'] = {[0] = 'RaceHurtMulti1_N', [1] = 'RaceHurtMulti2_N', [2] = 'RaceHurtMulti3_N', [3] = 'RaceHurtMulti4_N', [4] = 'RaceHurtMulti5_N', }, 
        ['RaceHurtMulti_P'] = {[0] = 'RaceHurtMulti1_P', [1] = 'RaceHurtMulti2_P', [2] = 'RaceHurtMulti3_P', [3] = 'RaceHurtMulti4_P', [4] = 'RaceHurtMulti5_P', }, 
        ['RaceHurtReduce'] = {[0] = 'RaceHurtReduce1', [1] = 'RaceHurtReduce2', [2] = 'RaceHurtReduce3', [3] = 'RaceHurtReduce4', [4] = 'RaceHurtReduce5', }, 
        ['RaceHurtReduce_F'] = {[0] = 'RaceHurtReduce1_F', [1] = 'RaceHurtReduce2_F', [2] = 'RaceHurtReduce3_F', [3] = 'RaceHurtReduce4_F', [4] = 'RaceHurtReduce5_F', }, 
        ['RaceHurtReduce_N'] = {[0] = 'RaceHurtReduce1_N', [1] = 'RaceHurtReduce2_N', [2] = 'RaceHurtReduce3_N', [3] = 'RaceHurtReduce4_N', [4] = 'RaceHurtReduce5_N', }, 
        ['RaceHurtReduce_P'] = {[0] = 'RaceHurtReduce1_P', [1] = 'RaceHurtReduce2_P', [2] = 'RaceHurtReduce3_P', [3] = 'RaceHurtReduce4_P', [4] = 'RaceHurtReduce5_P', }, 
    },
    data = {
        [1010] = {
            ['AuxiliaryKey'] = 0, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610077696'),
            ['Group'] = 'RaceHurtMulti', 
            ['ID'] = 1010, 
            ['Prop'] = 'RaceHurtMulti1', 
        },
        [1011] = {
            ['AuxiliaryKey'] = 1, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610077952'),
            ['Group'] = 'RaceHurtMulti', 
            ['ID'] = 1011, 
            ['Prop'] = 'RaceHurtMulti2', 
        },
        [1012] = {
            ['AuxiliaryKey'] = 2, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078208'),
            ['Group'] = 'RaceHurtMulti', 
            ['ID'] = 1012, 
            ['Prop'] = 'RaceHurtMulti3', 
        },
        [1013] = {
            ['AuxiliaryKey'] = 3, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078464'),
            ['Group'] = 'RaceHurtMulti', 
            ['ID'] = 1013, 
            ['Prop'] = 'RaceHurtMulti4', 
        },
        [1014] = {
            ['AuxiliaryKey'] = 4, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078720'),
            ['Group'] = 'RaceHurtMulti', 
            ['ID'] = 1014, 
            ['Prop'] = 'RaceHurtMulti5', 
        },
        [1020] = {
            ['AuxiliaryKey'] = 0, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078976'),
            ['Group'] = 'RaceHurtReduce', 
            ['ID'] = 1020, 
            ['Prop'] = 'RaceHurtReduce1', 
        },
        [1021] = {
            ['AuxiliaryKey'] = 1, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079232'),
            ['Group'] = 'RaceHurtReduce', 
            ['ID'] = 1021, 
            ['Prop'] = 'RaceHurtReduce2', 
        },
        [1022] = {
            ['AuxiliaryKey'] = 2, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079488'),
            ['Group'] = 'RaceHurtReduce', 
            ['ID'] = 1022, 
            ['Prop'] = 'RaceHurtReduce3', 
        },
        [1023] = {
            ['AuxiliaryKey'] = 3, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079744'),
            ['Group'] = 'RaceHurtReduce', 
            ['ID'] = 1023, 
            ['Prop'] = 'RaceHurtReduce4', 
        },
        [1024] = {
            ['AuxiliaryKey'] = 4, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080000'),
            ['Group'] = 'RaceHurtReduce', 
            ['ID'] = 1024, 
            ['Prop'] = 'RaceHurtReduce5', 
        },
        [1030] = {
            ['AuxiliaryKey'] = 1200001, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080256'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1030, 
            ['Prop'] = 'SunHurtMulti', 
        },
        [1031] = {
            ['AuxiliaryKey'] = 1200002, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080512'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1031, 
            ['Prop'] = 'VisionaryHurtMulti', 
        },
        [1032] = {
            ['AuxiliaryKey'] = 1200003, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080768'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1032, 
            ['Prop'] = 'FeatherwitHurtMulti', 
        },
        [1033] = {
            ['AuxiliaryKey'] = 1200004, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081024'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1033, 
            ['Prop'] = 'ArbiterHurtMulti', 
        },
        [1034] = {
            ['AuxiliaryKey'] = 1200006, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081280'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1034, 
            ['Prop'] = 'WarriorHurtMulti', 
        },
        [1035] = {
            ['AuxiliaryKey'] = 1200005, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081536'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1035, 
            ['Prop'] = 'ApprenticeHurtMulti', 
        },
        [1038] = {
            ['AuxiliaryKey'] = 1200007, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610082304'),
            ['Group'] = 'ProHurtMulti', 
            ['ID'] = 1038, 
            ['Prop'] = 'ProHurtMulti9', 
        },
        [1050] = {
            ['AuxiliaryKey'] = 1200001, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610082816'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1050, 
            ['Prop'] = 'SunHurtReduce', 
        },
        [1051] = {
            ['AuxiliaryKey'] = 1200002, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083072'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1051, 
            ['Prop'] = 'VisionaryHurtReduce', 
        },
        [1052] = {
            ['AuxiliaryKey'] = 1200003, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083328'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1052, 
            ['Prop'] = 'FeatherwitHurtReduce', 
        },
        [1053] = {
            ['AuxiliaryKey'] = 1200004, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083584'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1053, 
            ['Prop'] = 'ArbiterHurtReduce', 
        },
        [1054] = {
            ['AuxiliaryKey'] = 1200006, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083840'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1054, 
            ['Prop'] = 'WarriorHurtReduce', 
        },
        [1055] = {
            ['AuxiliaryKey'] = 1200005, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610084096'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1055, 
            ['Prop'] = 'ApprenticeHurtReduce', 
        },
        [1058] = {
            ['AuxiliaryKey'] = 1200007, 
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610084864'),
            ['Group'] = 'ProHurtReduce', 
            ['ID'] = 1058, 
            ['Prop'] = 'ProHurtReduce9', 
        },
    }
}
return TopData