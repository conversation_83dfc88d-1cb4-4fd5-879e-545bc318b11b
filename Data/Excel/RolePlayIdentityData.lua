--
-- 表名: $RolePlay_身份扮演.xlsx  页名：$Identity_扮演身份
--

local TopData = {
	data = {
		[2000101] = {
			[1] = {
				["ID"] = 2000101,
				["Level"] = 1,
				["Name"] = Game.TableDataManager:GetLangStr('str_46592878970368'),
				["UnlockCondition"] = "",
				["UnlockConditionAst"] = {},
				["UnlockConditionID"] = 0,
				["Reward"] = {{2001000, 1000, 1}},
				["SourceList"] = {1},
				["EffectID"] = {10001},
				["EffectParam"] = {{1}},
				["LimitExp"] = 20,
				["UpExp"] = 50,
			},
			[2] = {
				["ID"] = 2000101,
				["Level"] = 2,
				["Name"] = Game.TableDataManager:GetLangStr('str_46592878970624'),
				["UnlockCondition"] = "ROLEPLAY_IDENTITY_LEVEL,2000101,2",
				["UnlockConditionAst"] = {
                ["ConditionFuncInfo"] = {
                    ["Caller"] = "",
                    ["FuncName"] = "ROLEPLAY_IDENTITY_LEVEL",
                    ["FuncParamInfos"] = {
                        ["targetId"] = 2000101
                    },
                    ["FuncArgInfos"] = {2000101}
                },
                ["ConditionOp"] = "None",
                ["ConditionCmp"] = ">=",
                ["IsFFunc"] = false,
                ["ConditionCmpTarget"] = 2,
                ["RawInput"] = "ROLEPLAY_IDENTITY_LEVEL(2000101)>=2"
            },
				["UnlockConditionID"] = 0,
				["Reward"] = {{2001000, 2000, 1}},
				["SourceList"] = {1, 2},
				["EffectID"] = {10001},
				["EffectParam"] = {{1}},
				["LimitExp"] = 20,
				["UpExp"] = 100,
			},
		},
	},
}

return TopData
