--
-- 表名: $Inventory_背包页签.xlsx  页名：$Decompose_分解页签
--

local TopData = {
	data = {
		[101] = {
			["ID"] = 101,
			["decomposeInvName"] = Game.TableDataManager:GetLangStr('str_25839328562688'),
			["DecomposeSelectRarity"] = {1, 2, 3, 4, 5, 0},
			["sortFunc"] = {0, 1, 2},
		},
		[102] = {
			["ID"] = 102,
			["decomposeInvName"] = Game.TableDataManager:GetLangStr('str_54632789466880'),
			["DecomposeSelectRarity"] = {1, 2, 3, 4, 5},
			["sortFunc"] = {0, 1},
		},
	},
}

return TopData
