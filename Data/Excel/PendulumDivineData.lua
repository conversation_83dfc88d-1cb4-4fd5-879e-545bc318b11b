--
-- 表名: $PendulumDivine_灵摆占卜.xlsx  页名：$PendulumDivine_灵摆占卜
--

local TopData = {
	data = {
		[10001] = {
			["Key"] = 10001,
			["PerformType"] = 1,
			["ResultNum"] = 4,
			["Result"] = {1, 2, 3, 4},
			["ResultDec"] = {Game.TableDataManager:GetLangStr('str_40202772940289'), Game.TableDataManager:GetLangStr('str_40202772940290'), Game.TableDataManager:GetLangStr('str_40202772940291'), Game.TableDataManager:GetLangStr('str_40202772940292')},
			["ResultProb"] = {1, 1, 1, 1},
			["ResultError"] = 0,
		},
		[10002] = {
			["Key"] = 10002,
			["PerformType"] = 1,
			["ResultNum"] = 3,
			["Result"] = {1, 2, 2},
			["ResultDec"] = {Game.TableDataManager:GetLangStr('str_40202772940289'), Game.TableDataManager:GetLangStr('str_40202772940290'), Game.TableDataManager:GetLangStr('str_40202772940290')},
			["ResultProb"] = {1, 1, 1},
			["ResultError"] = 1,
		},
	},
}

return TopData
