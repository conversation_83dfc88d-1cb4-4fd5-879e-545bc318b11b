--
-- 表名: $Schedule.xlsx  页名：$Revelation
--

local TopData = {
	data = {
		[2711000] = {
			["Id"] = 2711000,
			["Title"] = Game.TableDataManager:GetLangStr('str_50785303924992'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_50785572360448'),
			["Condition"] = 6500110,
			["JumpUI"] = 1250031,
		},
		[2711002] = {
			["Id"] = 2711002,
			["Title"] = Game.TableDataManager:GetLangStr('str_50785303923456'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_50922206005248'),
			["Condition"] = 6500113,
			["JumpUI"] = 1250039,
		},
	},
}

return TopData
