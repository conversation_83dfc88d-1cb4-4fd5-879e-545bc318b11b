--
-- 表名: $Manor_庄园.xlsx  页名：$CantPlace
--

local TopData = {
	data = {
		[1001] = {
			["ID"] = 1001,
			["Enum"] = "Normal_Over",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495250944'),
		},
		[1002] = {
			["ID"] = 1002,
			["Enum"] = "Normal_Block",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495251200'),
		},
		[1003] = {
			["ID"] = 1003,
			["Enum"] = "Normal_RepeatBuild",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495251456'),
		},
		[1004] = {
			["ID"] = 1004,
			["Enum"] = "Normal_BlockByStair",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495251712'),
		},
		[1005] = {
			["ID"] = 1005,
			["Enum"] = "Normal_NeedFloor",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495251968'),
		},
		[1006] = {
			["ID"] = 1006,
			["Enum"] = "Normal_Cant",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495252224'),
		},
		[1007] = {
			["ID"] = 1007,
			["Enum"] = "Normal_Count",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495252480'),
		},
		[1301] = {
			["ID"] = 1301,
			["Enum"] = "Floor_Connect",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495252736'),
		},
		[1302] = {
			["ID"] = 1302,
			["Enum"] = "Floor_BlockEnvironment",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495252992'),
		},
		[1501] = {
			["ID"] = 1501,
			["Enum"] = "Stair_NeedFloorPath",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495253248'),
		},
		[1502] = {
			["ID"] = 1502,
			["Enum"] = "Stair_OccupiedByWall",
			["TypeDes"] = Game.TableDataManager:GetLangStr('str_33948495253504'),
		},
	},
}

return TopData
