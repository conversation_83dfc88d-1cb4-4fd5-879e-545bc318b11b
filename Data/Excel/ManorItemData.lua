--
-- 表名: 家园后处理，合并Furniture & Component & Workshop
--

local TopData = {
    data = {
        [4300000] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630720512'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141059584'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3101, 
            ['ID'] = 4300000, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_bot.SM_ManorNew_Wall01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_top.SM_ManorNew_Wall01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 30, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 63, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01.SM_ManorNew_Wall01', 
            ['quality'] = 0, 
        },
        [4300001] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630720768'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141059840'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3102, 
            ['ID'] = 4300001, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_bot.SM_ManorNew_Window01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_top.SM_ManorNew_Window01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 63, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01.SM_ManorNew_Window01', 
            ['quality'] = 0, 
        },
        [4300002] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630721024'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141060096'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3103, 
            ['ID'] = 4300002, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_bot.SM_ManorNew_Door01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_top.SM_ManorNew_Door01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 70, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01.SM_ManorNew_Door01', 
            ['quality'] = 0, 
        },
        [4300003] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141060352'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141060352'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300003, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot01.SM_ManorNew_Pillar01_bot01', 
            ['quality'] = 0, 
        },
        [4300004] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141060608'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141060608'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300004, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot02.SM_ManorNew_Pillar01_bot02', 
            ['quality'] = 0, 
        },
        [4300005] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141060864'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141060864'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300005, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_flair.SM_ManorNew_Pillar01_flair', 
            ['quality'] = 0, 
        },
        [4300006] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 4, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630722048'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141061120'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300006, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot.SM_ManorNew_Pillar01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01', 
            ['quality'] = 0, 
        },
        [4300007] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 5, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141061376'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141061376'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300007, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {131, 131, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid', 
            ['quality'] = 0, 
        },
        [4300008] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 6, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141061632'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141061632'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300008, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top', 
            ['quality'] = 0, 
        },
        [4300009] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 7, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = '', 
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 0, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300009, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid', 
            ['quality'] = 0, 
        },
        [4300010] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 8, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141062144'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141062144'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300010, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01', 
            ['quality'] = 0, 
        },
        [4300011] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141062400'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141062400'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2107, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300011, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {95, 95, 280}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing02.SM_ManorNew_RoofRailing02', 
            ['quality'] = 0, 
        },
        [4300012] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141062656'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141062656'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2106, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300012, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 60, 151}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing01.SM_ManorNew_RoofRailing01', 
            ['quality'] = 0, 
        },
        [4300013] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 11, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630723840'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141062912'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2103, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300013, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 48}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Floor01.SM_ManorNew_Floor01', 
            ['quality'] = 0, 
        },
        [4300014] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 46, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141063168'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141063168'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300014, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type01.SM_ManorNew_Type01', 
            ['quality'] = 0, 
        },
        [4300015] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141063424'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141063424'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300015, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type02.SM_ManorNew_Type02', 
            ['quality'] = 0, 
        },
        [4300016] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 5, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141063680'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141063680'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300016, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type03.SM_ManorNew_Type03', 
            ['quality'] = 0, 
        },
        [4300017] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 23, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141063936'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141063936'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300017, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type04.SM_ManorNew_Type04', 
            ['quality'] = 0, 
        },
        [4300018] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 11, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141064192'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141064192'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300018, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type05.SM_ManorNew_Type05', 
            ['quality'] = 0, 
        },
        [4300019] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 15, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141064448'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141064448'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300019, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type06.SM_ManorNew_Type06', 
            ['quality'] = 0, 
        },
        [4300020] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 9, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141064704'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141064704'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300020, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type07.SM_ManorNew_Type07', 
            ['quality'] = 0, 
        },
        [4300021] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 31, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141064960'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141064960'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300021, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type08.SM_ManorNew_Type08', 
            ['quality'] = 0, 
        },
        [4300022] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 19, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141065216'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141065216'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300022, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type09.SM_ManorNew_Type09', 
            ['quality'] = 0, 
        },
        [4300023] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 27, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141065472'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141065472'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300023, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type10.SM_ManorNew_Type10', 
            ['quality'] = 0, 
        },
        [4300024] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 32, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141065728'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141065728'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300024, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type11.SM_ManorNew_Type11', 
            ['quality'] = 0, 
        },
        [4300025] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 40, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141065984'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141065984'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300025, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type12.SM_ManorNew_Type12', 
            ['quality'] = 0, 
        },
        [4300026] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 36, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141066240'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141066240'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300026, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type13.SM_ManorNew_Type13', 
            ['quality'] = 0, 
        },
        [4300027] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 99, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141066496'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141066496'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300027, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 58}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type14.SM_ManorNew_Type14', 
            ['quality'] = 0, 
        },
        [4300028] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 42, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141066752'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141066752'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300028, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type15.SM_ManorNew_Type15', 
            ['quality'] = 0, 
        },
        [4300029] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34704141067008'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141067008'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3504, 
            ['ID'] = 4300029, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Double01_Stair.SM_ManorNew_Double01_Stair', 
            ['quality'] = 0, 
        },
        [4300030] = {
            ['AreaSize'] = 2, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630728192'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141067264'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3501, 
            ['ID'] = 4300030, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {1200, 600, 822}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Single01_Stair.SM_ManorNew_Single01_Stair', 
            ['quality'] = 0, 
        },
        [4300031] = {
            ['AreaSize'] = 3, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630728448'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141067520'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3502, 
            ['ID'] = 4300031, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn01_Stair.SM_ManorNew_Turn01_Stair', 
            ['quality'] = 0, 
        },
        [4300032] = {
            ['AreaSize'] = 3, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 4, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34707630728704'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34704141067776'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3503, 
            ['ID'] = 4300032, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn02_Stair.SM_ManorNew_Turn02_Stair', 
            ['quality'] = 0, 
        },
        [4320001] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203854848'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982629376'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4320002] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855104'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982629632'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {500, 500, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4320003] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855360'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982629888'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy001.SM_Toy001', 
            ['quality'] = 0, 
        },
        [4320004] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855616'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630144'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 500, 300}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A2.SM_BlackThorns_Company_2F_Piano_A2', 
            ['quality'] = 0, 
        },
        [4320005] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855872'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630400'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {100, 100, 200}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Glob.SM_BlackThorns_Company_2F_Glob', 
            ['quality'] = 0, 
        },
        [4320006] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856128'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630656'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Scarecrow.SM_Scarecrow', 
            ['quality'] = 0, 
        },
        [4320007] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856384'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630912'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 100}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bath001.SM_Bath001', 
            ['quality'] = 0, 
        },
        [4320008] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856640'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982631168'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Machine002.SM_Coffeeshop_Machine002', 
            ['quality'] = 0, 
        },
        [4320009] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856896'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982631424'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Angel.SM_Statue_Angel', 
            ['quality'] = 0, 
        },
        [4320010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982631680'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982631680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk01.SM_Desk01A', 
            ['quality'] = 0, 
        },
        [4320011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982631936'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982631936'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4320012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982629376'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982629376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4320013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203857920'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982632448'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet004.SM_BlackThorns_DunnOffice_Cabinet004', 
            ['quality'] = 0, 
        },
        [4320014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982632704'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982632704'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4320015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982632960'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982632960'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair04.SM_Chair04', 
            ['quality'] = 0, 
        },
        [4320016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982633216'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982633216'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair001.SM_Coffeeshop_Chair001', 
            ['quality'] = 0, 
        },
        [4320017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982633472'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982633472'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair002.SM_Coffeeshop_Chair002', 
            ['quality'] = 0, 
        },
        [4320018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982633728'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982633728'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair003.SM_Coffeeshop_Chair003', 
            ['quality'] = 0, 
        },
        [4320019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982633984'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982633984'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chair_001.SM_BlackThorns_Company_2F_Chair_001', 
            ['quality'] = 0, 
        },
        [4320020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982634240'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982634240'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa001.SM_Dragon_Bar_Sofa001', 
            ['quality'] = 0, 
        },
        [4320021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982634496'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982634496'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa002.SM_Dragon_Bar_Sofa002', 
            ['quality'] = 0, 
        },
        [4320022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982634752'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982634752'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Sofa001A.SM_Divination_Club_Sofa001A', 
            ['quality'] = 0, 
        },
        [4320023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982635008'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982635008'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Chair001.SM_Furniture_Rich_Chair001', 
            ['quality'] = 0, 
        },
        [4320024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982635264'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982635264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_CoffeeTable001.SM_CoffeeTable001A', 
            ['quality'] = 0, 
        },
        [4320025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982635520'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982635520'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bakeshop_Cupboards001.SM_Bakeshop_Cupboards001', 
            ['quality'] = 0, 
        },
        [4320026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982635776'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982635776'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_BookcaseHZ_003.SM_BlackThorns_Company_2F_BookcaseHZ_003', 
            ['quality'] = 0, 
        },
        [4320027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982636032'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982636032'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet003.SM_BlackThorns_DunnOffice_Cabinet003', 
            ['quality'] = 0, 
        },
        [4320028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982636288'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982636288'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet03.SM_Cabinet03', 
            ['quality'] = 0, 
        },
        [4320029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982636544'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982636544'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet04.SM_Cabinet04', 
            ['quality'] = 0, 
        },
        [4320030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982636800'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982636800'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet05.SM_Cabinet05', 
            ['quality'] = 0, 
        },
        [4320031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982637056'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982637056'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet001.SM_Furniture_Rich_Cabinet001', 
            ['quality'] = 0, 
        },
        [4320032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982637312'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982637312'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet003.SM_Furniture_Rich_Cabinet003', 
            ['quality'] = 0, 
        },
        [4320033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982637568'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982637568'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet004.SM_Furniture_Rich_Cabinet004', 
            ['quality'] = 0, 
        },
        [4320034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982637824'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982637824'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet007.SM_Furniture_Rich_Cabinet007', 
            ['quality'] = 0, 
        },
        [4320035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982638080'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982638080'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror001.SM_BlackThorns_DunnOffice_Mirror001', 
            ['quality'] = 0, 
        },
        [4320036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982638336'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982638336'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror003.SM_BlackThorns_DunnOffice_Mirror003', 
            ['quality'] = 0, 
        },
        [4320037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982638592'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982638592'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_FlowerpotHZ002.SM_Coffeeshop_FlowerpotHZ002', 
            ['quality'] = 0, 
        },
        [4320038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982638848'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982638848'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_FloorLampHZ_002.SM_BlackThorns_Company_FloorLampHZ_002', 
            ['quality'] = 0, 
        },
        [4320039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982639104'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982639104'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ005.SM_Divination_CandlestickHZ005', 
            ['quality'] = 0, 
        },
        [4320040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982639360'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982639360'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ006.SM_Divination_CandlestickHZ006', 
            ['quality'] = 0, 
        },
        [4320041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982639616'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982639616'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_InkPenHZ002.SM_Divination_InkPenHZ002', 
            ['quality'] = 0, 
        },
        [4320042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982639872'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982639872'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot003.SM_Flowerpot003', 
            ['quality'] = 0, 
        },
        [4320043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982640128'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982640128'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4320044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982640384'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982640384'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LampHZ001.SM_Furniture_Rich_LampHZ001', 
            ['quality'] = 0, 
        },
        [4320045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982640640'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982640640'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LightHZ001.SM_Furniture_Rich_LightHZ001', 
            ['quality'] = 0, 
        },
        [4320046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982640896'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982640896'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed02.SM_Bed02', 
            ['quality'] = 0, 
        },
        [4320047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982641152'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982641152'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4320048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982641408'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982641408'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed004.SM_Furniture_Rich_Bed004', 
            ['quality'] = 0, 
        },
        [4320049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982641664'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982641664'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed003.SM_Furniture_Rich_Bed003', 
            ['quality'] = 0, 
        },
        [4320050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982641920'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982641920'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props004.SM_Furniture_Rich_Props004', 
            ['quality'] = 0, 
        },
        [4320051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982642176'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982642176'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props012.SM_Furniture_Rich_Props012', 
            ['quality'] = 0, 
        },
        [4320052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982642432'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982642432'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_2F_Fireplace001_A.SM_2F_Fireplace001_A', 
            ['quality'] = 0, 
        },
        [4320053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982642688'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982642688'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet002.SM_BlackThorns_DunnOffice_Cabinet002', 
            ['quality'] = 0, 
        },
        [4320054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34497982642944'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982642944'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace02.SM_Fireplace02', 
            ['quality'] = 0, 
        },
        [4321000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982643200'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk01A.SM_Desk01A', 
            ['quality'] = 0, 
        },
        [4321001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982643456'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4321002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982643712'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_TableHZ03.SM_TableHZ03', 
            ['quality'] = 0, 
        },
        [4321003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982643968'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair01.SM_Chair01', 
            ['quality'] = 0, 
        },
        [4321004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982644224'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair02.SM_Chair02', 
            ['quality'] = 0, 
        },
        [4321005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982644480'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair04.SM_Chair04', 
            ['quality'] = 0, 
        },
        [4321006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982644736'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rockingchair.SM_Rockingchair', 
            ['quality'] = 0, 
        },
        [4321007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982644992'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair03.SM_Chair03', 
            ['quality'] = 0, 
        },
        [4321008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982635264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_Piano_Bench.SM_Octopus_Piano_Bench', 
            ['quality'] = 0, 
        },
        [4321009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982645504'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Chair001.SM_Furniture_Rich_Chair001', 
            ['quality'] = 0, 
        },
        [4321010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982645760'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa001.SM_Dragon_Bar_Sofa001', 
            ['quality'] = 0, 
        },
        [4321011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982646016'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Sofa001A.SM_Divination_Club_Sofa001A', 
            ['quality'] = 0, 
        },
        [4321012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982646272'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa002.SM_Dragon_Bar_Sofa002', 
            ['quality'] = 0, 
        },
        [4321013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982646528'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_A2_Cabinet008_E.SM_Dragon_Bar_A2_Cabinet008_E', 
            ['quality'] = 0, 
        },
        [4321014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203872256'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982646784'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet01.SM_Cabinet01', 
            ['quality'] = 0, 
        },
        [4321015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982647040'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_A2_Cabinet008_F.SM_Dragon_Bar_A2_Cabinet008_F', 
            ['quality'] = 0, 
        },
        [4321016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982647296'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet04.SM_Cabinet04', 
            ['quality'] = 0, 
        },
        [4321017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982647552'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet05.SM_Cabinet05', 
            ['quality'] = 0, 
        },
        [4321018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982647808'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet02.SM_Cabinet02', 
            ['quality'] = 0, 
        },
        [4321019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982648064'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet03.SM_Cabinet03', 
            ['quality'] = 0, 
        },
        [4321020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982648320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Table04.SM_Table04', 
            ['quality'] = 0, 
        },
        [4321021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982648576'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet001.SM_Cabinet001', 
            ['quality'] = 0, 
        },
        [4321022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203874304'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982648832'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed02.SM_Bed02', 
            ['quality'] = 0, 
        },
        [4321023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982649088'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed01.SM_Bed01', 
            ['quality'] = 0, 
        },
        [4321024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982649344'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed01_B.SM_Bed01_B', 
            ['quality'] = 0, 
        },
        [4321025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982649600'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed002.SM_Furniture_Rich_Bed002', 
            ['quality'] = 0, 
        },
        [4321026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982649856'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Candlestick001.SM_Candlestick001', 
            ['quality'] = 0, 
        },
        [4321027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982650112'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ006.SM_Divination_CandlestickHZ006', 
            ['quality'] = 0, 
        },
        [4321028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982650368'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ007.SM_Divination_CandlestickHZ007', 
            ['quality'] = 0, 
        },
        [4321029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982650624'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ005.SM_Divination_CandlestickHZ005', 
            ['quality'] = 0, 
        },
        [4321030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982650880'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Carpet001.SM_Rylbir_Houes_Carpet001', 
            ['quality'] = 0, 
        },
        [4321031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982651136'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Carpet002.SM_Rylbir_Houes_Carpet002', 
            ['quality'] = 0, 
        },
        [4321032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982651392'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_DivinationArea_Carpet001.SM_Divination_Club_DivinationArea_Carpet001', 
            ['quality'] = 0, 
        },
        [4321033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982651648'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Carpet002.SM_Divination_Club_Carpet002', 
            ['quality'] = 0, 
        },
        [4321034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982651904'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_LetherBbook.SM_LetherBbook', 
            ['quality'] = 0, 
        },
        [4321035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982652160'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile8.SM_BookPile8', 
            ['quality'] = 0, 
        },
        [4321036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982652416'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile11.SM_BookPile11', 
            ['quality'] = 0, 
        },
        [4321037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982652672'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile14.SM_BookPile14', 
            ['quality'] = 0, 
        },
        [4321038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982652928'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_TiengenPaper.SM_TiengenPaper', 
            ['quality'] = 0, 
        },
        [4321039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982653184'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_NotePlate.SM_NotePlate', 
            ['quality'] = 0, 
        },
        [4321040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982653440'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ06.SM_Divination_Club_BookHZ06', 
            ['quality'] = 0, 
        },
        [4321041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982653696'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ05.SM_Divination_Club_BookHZ05', 
            ['quality'] = 0, 
        },
        [4321042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982653952'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ08.SM_Divination_Club_BookHZ08', 
            ['quality'] = 0, 
        },
        [4321043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982654208'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ07.SM_Divination_Club_BookHZ07', 
            ['quality'] = 0, 
        },
        [4321044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982654464'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_InkPenHZ002.SM_Divination_InkPenHZ002', 
            ['quality'] = 0, 
        },
        [4321045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855872'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630400'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {100, 100, 200}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Glob.SM_BlackThorns_Company_2F_Glob', 
            ['quality'] = 0, 
        },
        [4321046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982654976'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pen.SM_Pen', 
            ['quality'] = 0, 
        },
        [4321047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982655232'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Map002.SM_Pirate_School_BernadeOffice_Map002', 
            ['quality'] = 0, 
        },
        [4321048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982655488'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4321049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982655744'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props011.SM_Furniture_Rich_Props011', 
            ['quality'] = 0, 
        },
        [4321050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982656000'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Poker002.SM_Poker002', 
            ['quality'] = 0, 
        },
        [4321051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982656256'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Map001.SM_Pirate_School_BernadeOffice_Map001', 
            ['quality'] = 0, 
        },
        [4321052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982656512'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props054.SM_Pirate_School_craftsroom_Props054', 
            ['quality'] = 0, 
        },
        [4321053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982656768'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props050.SM_Pirate_School_craftsroom_Props050', 
            ['quality'] = 0, 
        },
        [4321054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982657024'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_Library_BookShelfHZ001.SM_Pirate_School_Library_BookShelfHZ001', 
            ['quality'] = 0, 
        },
        [4321055] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982657280'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321055, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_SandGlass002.SM_Divination_SandGlass002', 
            ['quality'] = 0, 
        },
        [4321056] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982657536'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321056, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_RoleCreate_Typewriter.SM_RoleCreate_Typewriter', 
            ['quality'] = 0, 
        },
        [4321057] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982657792'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321057, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clinic_Prosp015.SM_Clinic_Prosp015', 
            ['quality'] = 0, 
        },
        [4321058] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982658048'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321058, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Sailboat.SM_Sailboat', 
            ['quality'] = 0, 
        },
        [4321059] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982658304'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321059, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props014.SM_Pirate_School_craftsroom_Props014', 
            ['quality'] = 0, 
        },
        [4321060] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982658560'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321060, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CrystalBall001.SM_Divination_CrystalBall001', 
            ['quality'] = 0, 
        },
        [4321061] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982658816'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321061, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Props/SM_Divination/SM_Candlestick004_7.SM_Candlestick004_7', 
            ['quality'] = 0, 
        },
        [4321062] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982659072'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321062, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Herbalpot_003.SM_Herbalpot_003', 
            ['quality'] = 0, 
        },
        [4321063] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982659328'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321063, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Gemstone003.SM_Gemstone003', 
            ['quality'] = 0, 
        },
        [4321064] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982659584'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321064, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Gemstone004.SM_Gemstone004', 
            ['quality'] = 0, 
        },
        [4321065] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982659840'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321065, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Mirror02.SM_Mirror02', 
            ['quality'] = 0, 
        },
        [4321066] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982660096'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321066, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace02.SM_Fireplace02', 
            ['quality'] = 0, 
        },
        [4321067] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856128'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630656'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321067, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Scarecrow.SM_Scarecrow', 
            ['quality'] = 0, 
        },
        [4321068] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982660608'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321068, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Hanger001.SM_Furniture_Rich_Hanger001', 
            ['quality'] = 0, 
        },
        [4321069] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982660864'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321069, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_Blackboard002.SM_Pirate_School_Blackboard002', 
            ['quality'] = 0, 
        },
        [4321070] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982661120'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321070, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_001.SM_Bike_001', 
            ['quality'] = 0, 
        },
        [4321071] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982661376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321071, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_005.SM_Bike_005', 
            ['quality'] = 0, 
        },
        [4321072] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982661632'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321072, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_003.SM_Bike_003', 
            ['quality'] = 0, 
        },
        [4321073] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982661888'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321073, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Camera.SM_Camera', 
            ['quality'] = 0, 
        },
        [4321074] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982662144'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321074, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_trashCan.SM_Component_trashCan', 
            ['quality'] = 0, 
        },
        [4321075] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982662400'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321075, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_Trashcan003.SM_Component_Trashcan003', 
            ['quality'] = 0, 
        },
        [4321076] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982662656'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321076, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_Faucet005.SM_Component_Faucet005', 
            ['quality'] = 0, 
        },
        [4321077] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856896'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982662912'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321077, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Angel.SM_Statue_Angel', 
            ['quality'] = 0, 
        },
        [4321078] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982663168'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321078, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Baby.SM_Statue_Baby', 
            ['quality'] = 0, 
        },
        [4321079] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982663424'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321079, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Oldman.SM_Statue_Oldman', 
            ['quality'] = 0, 
        },
        [4321080] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982663680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321080, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_StatueTFool.SM_StatueTFool', 
            ['quality'] = 0, 
        },
        [4321081] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982663936'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321081, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Weapon_Shield007.SM_Weapon_Shield007', 
            ['quality'] = 0, 
        },
        [4321082] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982664192'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321082, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Wheelchair_001.SM_Wheelchair_001', 
            ['quality'] = 0, 
        },
        [4321083] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982664448'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321083, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed03.SM_Bed03', 
            ['quality'] = 0, 
        },
        [4321084] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982664704'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321084, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed04.SM_Bed04', 
            ['quality'] = 0, 
        },
        [4321085] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982664960'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321085, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props013.SM_Furniture_Rich_Props013', 
            ['quality'] = 0, 
        },
        [4321086] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982665216'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321086, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props015.SM_Furniture_Rich_Props015', 
            ['quality'] = 0, 
        },
        [4321087] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982665472'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321087, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props014.SM_Furniture_Rich_Props014', 
            ['quality'] = 0, 
        },
        [4321088] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982665728'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321088, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props012.SM_Furniture_Rich_Props012', 
            ['quality'] = 0, 
        },
        [4321089] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982665984'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321089, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_Sofa001C.SM_Rich_house_Sofa001C', 
            ['quality'] = 0, 
        },
        [4321090] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982666240'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321090, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pillow001.SM_Pillow001', 
            ['quality'] = 0, 
        },
        [4321091] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855360'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982666496'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321091, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy001.SM_Toy001', 
            ['quality'] = 0, 
        },
        [4321092] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982666752'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321092, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy002.SM_Toy002', 
            ['quality'] = 0, 
        },
        [4321093] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982667008'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4206, 
            ['ID'] = 4321093, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_FlowerpotHZ002.SM_Coffeeshop_FlowerpotHZ002', 
            ['quality'] = 0, 
        },
        [4321094] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982667264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4206, 
            ['ID'] = 4321094, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005_D.SM_Flowerpot005_D', 
            ['quality'] = 0, 
        },
        [4321095] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982667520'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321095, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains007.SM_Rylbir_Houes_Curtains007', 
            ['quality'] = 0, 
        },
        [4321096] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982667776'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321096, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains008.SM_Rylbir_Houes_Curtains008', 
            ['quality'] = 0, 
        },
        [4321097] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982668032'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321097, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains009.SM_Rylbir_Houes_Curtains009', 
            ['quality'] = 0, 
        },
        [4321098] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982668288'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321098, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_Curtain001.SM_BlackThorns_Company_Curtain001', 
            ['quality'] = 0, 
        },
        [4321099] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982668544'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321099, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_Shelf001A.SM_Ferlanqi_Flats_Shelf001A', 
            ['quality'] = 0, 
        },
        [4321100] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982668800'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321100, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_WallPainting001A.SM_Ferlanqi_Flats_WallPainting001A', 
            ['quality'] = 0, 
        },
        [4321101] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982669056'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321101, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_WallPainting001C.SM_Ferlanqi_Flats_WallPainting001C', 
            ['quality'] = 0, 
        },
        [4321102] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982669312'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321102, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_PotionRoom_Painter004.SM_Pirate_School_PotionRoom_Painter004', 
            ['quality'] = 0, 
        },
        [4321103] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982669568'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321103, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_PotionRoom_Painter010.SM_Pirate_School_PotionRoom_Painter010', 
            ['quality'] = 0, 
        },
        [4321104] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982669824'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321104, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Painting001.SM_BlackThorns_DunnOffice_Painting001', 
            ['quality'] = 0, 
        },
        [4321105] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982670080'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321105, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WallPainting001.SM_WallPainting001', 
            ['quality'] = 0, 
        },
        [4321106] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982670336'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321106, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_PropsHZ065.SM_Pirate_School_craftsroom_PropsHZ065', 
            ['quality'] = 0, 
        },
        [4321107] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982670592'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321107, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clocklamp004.SM_Clocklamp004', 
            ['quality'] = 0, 
        },
        [4321108] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982670848'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321108, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clocklamp002.SM_Clocklamp002', 
            ['quality'] = 0, 
        },
        [4321109] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982671104'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321109, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Antler001.SM_Furniture_Rich_Antler001', 
            ['quality'] = 0, 
        },
        [4321110] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982671360'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321110, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props001.SM_Furniture_Rich_Props001', 
            ['quality'] = 0, 
        },
        [4321111] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982671616'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321111, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props016.SM_Furniture_Rich_Props016', 
            ['quality'] = 0, 
        },
        [4321112] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982671872'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321112, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_Washstand002.SM_Ferlanqi_Flats_Washstand002', 
            ['quality'] = 0, 
        },
        [4321113] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982672128'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321113, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toilet01.SM_Toilet01', 
            ['quality'] = 0, 
        },
        [4321114] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982672384'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321114, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props010.SM_Furniture_Rich_Props010', 
            ['quality'] = 0, 
        },
        [4321115] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982672640'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321115, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props002.SM_Furniture_Rich_Props002', 
            ['quality'] = 0, 
        },
        [4321116] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982672896'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321116, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props008.SM_Furniture_Rich_Props008', 
            ['quality'] = 0, 
        },
        [4321117] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982673152'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321117, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_shelf.SM_Octopus_shelf', 
            ['quality'] = 0, 
        },
        [4321118] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982673408'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321118, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_timpani.SM_Octopus_timpani', 
            ['quality'] = 0, 
        },
        [4321119] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982673664'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321119, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_basic.SM_Octopus_basic', 
            ['quality'] = 0, 
        },
        [4321120] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982673920'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321120, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_violin.SM_Octopus_violin', 
            ['quality'] = 0, 
        },
        [4321121] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_59443152792320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321121, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano.SM_BlackThorns_Company_2F_Piano', 
            ['quality'] = 0, 
        },
        [4321122] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982674432'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321122, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace01.SM_Fireplace01', 
            ['quality'] = 0, 
        },
        [4321123] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982674688'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321123, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coalstove01.SM_Coalstove01', 
            ['quality'] = 0, 
        },
        [4321124] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856640'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982631168'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321124, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Machine002.SM_Coffeeshop_Machine002', 
            ['quality'] = 0, 
        },
        [4321125] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982675200'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321125, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant002.SM_Coffeeshop_Plant002', 
            ['quality'] = 0, 
        },
        [4321126] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982675456'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321126, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant004.SM_Coffeeshop_Plant004', 
            ['quality'] = 0, 
        },
        [4321127] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982675712'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321127, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Tea_Set001.SM_Divination_Club_Tea_Set001', 
            ['quality'] = 0, 
        },
        [4321128] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982675968'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321128, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Soup_001.SM_Soup_001', 
            ['quality'] = 0, 
        },
        [4321129] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982676224'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321129, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Kettle03.SM_Kettle03', 
            ['quality'] = 0, 
        },
        [4321130] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982676480'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321130, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pot001.SM_Pot001', 
            ['quality'] = 0, 
        },
        [4321131] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_39239626629888'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321131, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Bottle002.SM_Coffeeshop_Bottle002', 
            ['quality'] = 0, 
        },
        [4321132] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982676992'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321132, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Plate001.SM_Plate001', 
            ['quality'] = 0, 
        },
        [4321133] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982677248'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321133, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Circus_Props016.SM_Circus_Props016', 
            ['quality'] = 0, 
        },
        [4321134] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982677504'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321134, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pumpkin_B.SM_Pumpkin_B', 
            ['quality'] = 0, 
        },
        [4321135] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982677760'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321135, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Apple.SM_Apple', 
            ['quality'] = 0, 
        },
        [4321136] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982678016'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4404, 
            ['ID'] = 4321136, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4322000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34501203857920'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet004.SM_BlackThorns_DunnOffice_Cabinet004', 
            ['quality'] = 0, 
        },
        [4322001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982678528'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_ShortCabinet001.SM_BlackThorns_Company_2F_ShortCabinet001', 
            ['quality'] = 0, 
        },
        [4322002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982678784'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_Table_001.SM_BlackThorns_Company_Table_001', 
            ['quality'] = 0, 
        },
        [4322003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982679040'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chair_001.SM_BlackThorns_Company_2F_Chair_001', 
            ['quality'] = 0, 
        },
        [4322004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982679296'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_BookcaseHZ_003.SM_BlackThorns_Company_2F_BookcaseHZ_003', 
            ['quality'] = 0, 
        },
        [4322005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982679552'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet003.SM_BlackThorns_DunnOffice_Cabinet003', 
            ['quality'] = 0, 
        },
        [4322006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982679808'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_2F_Fireplace001_A.SM_2F_Fireplace001_A', 
            ['quality'] = 0, 
        },
        [4322007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982680064'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet002.SM_BlackThorns_DunnOffice_Cabinet002', 
            ['quality'] = 0, 
        },
        [4322008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982680320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror002.SM_BlackThorns_DunnOffice_Mirror002', 
            ['quality'] = 0, 
        },
        [4322009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982680576'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror001.SM_BlackThorns_DunnOffice_Mirror001', 
            ['quality'] = 0, 
        },
        [4322010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982680832'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror003.SM_BlackThorns_DunnOffice_Mirror003', 
            ['quality'] = 0, 
        },
        [4322011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855616'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982681088'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 500, 300}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A2.SM_BlackThorns_Company_2F_Piano_A2', 
            ['quality'] = 0, 
        },
        [4323000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203854848'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982681344'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4323001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982681600'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_DiningTable002.SM_Furniture_Rich_DiningTable002', 
            ['quality'] = 0, 
        },
        [4323002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982681856'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Desk001.SM_Coffeeshop_Desk001', 
            ['quality'] = 0, 
        },
        [4323003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982682112'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002a.SM_Furniture_Rich_PocketMirror002a', 
            ['quality'] = 0, 
        },
        [4323004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982682368'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_CoffeeTable001A.SM_CoffeeTable001A', 
            ['quality'] = 0, 
        },
        [4323005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982682624'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Table.SM_Table', 
            ['quality'] = 0, 
        },
        [4323006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982682880'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Lobby_TableHZ002.SM_Divination_Club_Lobby_TableHZ002', 
            ['quality'] = 0, 
        },
        [4323007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982683136'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_RoundTable001.SM_RoundTable001', 
            ['quality'] = 0, 
        },
        [4323008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982683392'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet006.SM_Furniture_Rich_Cabinet006', 
            ['quality'] = 0, 
        },
        [4323009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982683648'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair002.SM_Coffeeshop_Chair002', 
            ['quality'] = 0, 
        },
        [4323010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982683904'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair001.SM_Coffeeshop_Chair001', 
            ['quality'] = 0, 
        },
        [4323011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982684160'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_SofaHZ001D.SM_Rich_house_SofaHZ001D', 
            ['quality'] = 0, 
        },
        [4323012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982684416'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_SofaHZ001A.SM_Rich_house_SofaHZ001A', 
            ['quality'] = 0, 
        },
        [4323013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982684672'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WYZY_Prop_Sofa_03_b.SM_WYZY_Prop_Sofa_03_b', 
            ['quality'] = 0, 
        },
        [4323014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982684928'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WYZY_Prop_Sofa_03_a.SM_WYZY_Prop_Sofa_03_a', 
            ['quality'] = 0, 
        },
        [4323015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982685184'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_ChairHZ002.SM_Divination_Club_ChairHZ002', 
            ['quality'] = 0, 
        },
        [4323016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982685440'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_StrategychairHZ001.SM_Pirate_School_BernadeOffice_StrategychairHZ001', 
            ['quality'] = 0, 
        },
        [4323017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982685696'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair003.SM_Coffeeshop_Chair003', 
            ['quality'] = 0, 
        },
        [4323018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982685952'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bakeshop_Cupboards001.SM_Bakeshop_Cupboards001', 
            ['quality'] = 0, 
        },
        [4323019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982686208'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet001.SM_Furniture_Rich_Cabinet001', 
            ['quality'] = 0, 
        },
        [4323020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982686464'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002c.SM_Furniture_Rich_PocketMirror002c', 
            ['quality'] = 0, 
        },
        [4323021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982686720'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet005.SM_Furniture_Rich_Cabinet005', 
            ['quality'] = 0, 
        },
        [4323022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982686976'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002b.SM_Furniture_Rich_PocketMirror002b', 
            ['quality'] = 0, 
        },
        [4323023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982687232'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cupboards002.SM_Furniture_Rich_Cupboards002', 
            ['quality'] = 0, 
        },
        [4323024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982687488'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet008.SM_Furniture_Rich_Cabinet008', 
            ['quality'] = 0, 
        },
        [4323025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982687744'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet007.SM_Furniture_Rich_Cabinet007', 
            ['quality'] = 0, 
        },
        [4323026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982688000'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet010.SM_Furniture_Rich_Cabinet010', 
            ['quality'] = 0, 
        },
        [4323027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982688256'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet009.SM_Furniture_Rich_Cabinet009', 
            ['quality'] = 0, 
        },
        [4323028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982688512'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet003.SM_Furniture_Rich_Cabinet003', 
            ['quality'] = 0, 
        },
        [4323029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982688768'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet004.SM_Furniture_Rich_Cabinet004', 
            ['quality'] = 0, 
        },
        [4323030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982689024'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Bookcase002.SM_Pirate_School_BernadeOffice_Bookcase002', 
            ['quality'] = 0, 
        },
        [4323031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982689280'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_library_BookShelf003.SM_Pirate_School_library_BookShelf003', 
            ['quality'] = 0, 
        },
        [4323032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982689536'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Interactor_Leatherbox001a.SM_Interactor_Leatherbox001a', 
            ['quality'] = 0, 
        },
        [4323033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982689792'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed004.SM_Furniture_Rich_Bed004', 
            ['quality'] = 0, 
        },
        [4323034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982690048'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed003.SM_Furniture_Rich_Bed003', 
            ['quality'] = 0, 
        },
        [4323035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203855104'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982629632'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {500, 500, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4323036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982690560'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LampHZ001.SM_Furniture_Rich_LampHZ001', 
            ['quality'] = 0, 
        },
        [4323037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982690816'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LightHZ001.SM_Furniture_Rich_LightHZ001', 
            ['quality'] = 0, 
        },
        [4323038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982691072'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Lamp_003.SM_Lamp_003', 
            ['quality'] = 0, 
        },
        [4323039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982691328'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_FloorLampHZ_002.SM_BlackThorns_Company_FloorLampHZ_002', 
            ['quality'] = 0, 
        },
        [4323040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982691584'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Light001a.SM_Coffeeshop_Light001a', 
            ['quality'] = 0, 
        },
        [4323041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982691840'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chandelier_Candle.SM_BlackThorns_Company_2F_Chandelier_Candle', 
            ['quality'] = 0, 
        },
        [4323042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982692096'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet001.SM_Carpet001', 
            ['quality'] = 0, 
        },
        [4323043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982692352'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet002.SM_Carpet002', 
            ['quality'] = 0, 
        },
        [4323044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982692608'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet005.SM_Carpet005', 
            ['quality'] = 0, 
        },
        [4323045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982692864'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Specimen02.SM_Divination_Club_Specimen02', 
            ['quality'] = 0, 
        },
        [4323046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982693120'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_011.SM_Perfume_Bottle_011', 
            ['quality'] = 0, 
        },
        [4323047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982693376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot001.SM_Flowerpot001', 
            ['quality'] = 0, 
        },
        [4323048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982693632'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot003.SM_Flowerpot003', 
            ['quality'] = 0, 
        },
        [4323049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982693888'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props006.SM_Furniture_Rich_Props006', 
            ['quality'] = 0, 
        },
        [4323050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982694144'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props009.SM_Furniture_Rich_Props009', 
            ['quality'] = 0, 
        },
        [4323051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982694400'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props005.SM_Furniture_Rich_Props005', 
            ['quality'] = 0, 
        },
        [4323052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982694656'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Jewellery008.SM_Jewellery008', 
            ['quality'] = 0, 
        },
        [4323053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982694912'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Jewellery017.SM_Jewellery017', 
            ['quality'] = 0, 
        },
        [4323054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982695168'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_001.SM_Perfume_Bottle_001', 
            ['quality'] = 0, 
        },
        [4323055] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982695424'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323055, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_003.SM_Perfume_Bottle_003', 
            ['quality'] = 0, 
        },
        [4323056] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982695680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323056, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_MirrorHZ002.SM_Divination_Club_MirrorHZ002', 
            ['quality'] = 0, 
        },
        [4323057] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982695936'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323057, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Mirror001.SM_Furniture_Rich_Mirror001', 
            ['quality'] = 0, 
        },
        [4323058] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982696192'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323058, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Fireplace001_a.SM_Furniture_Rich_Fireplace001_a', 
            ['quality'] = 0, 
        },
        [4323059] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982696448'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323059, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_ClockHZ01.SM_Divination_ClockHZ01', 
            ['quality'] = 0, 
        },
        [4323060] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982696704'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323060, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant001.SM_Coffeeshop_Plant001', 
            ['quality'] = 0, 
        },
        [4323061] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982696960'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323061, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005.SM_Flowerpot005', 
            ['quality'] = 0, 
        },
        [4323062] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982697216'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323062, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005.SM_Flowerpot005', 
            ['quality'] = 0, 
        },
        [4323063] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982697472'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323063, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4323064] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982697728'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323064, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4323065] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982697984'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323065, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Flowerpot001.SM_Coffeeshop_Flowerpot001', 
            ['quality'] = 0, 
        },
        [4323066] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982698240'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323066, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains003_A.SM_Rylbir_Houes_Curtains003_A', 
            ['quality'] = 0, 
        },
        [4323067] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982698496'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323067, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_WindowGauzeHZ002.SM_Divination_Club_WindowGauzeHZ002', 
            ['quality'] = 0, 
        },
        [4323068] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982698752'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323068, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ShuiXian_Villa_Curtain001A.SM_ShuiXian_Villa_Curtain001A', 
            ['quality'] = 0, 
        },
        [4323069] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34501203856384'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982630912'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323069, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 100}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bath001.SM_Bath001', 
            ['quality'] = 0, 
        },
        [4323070] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982699264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323070, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props007.SM_Furniture_Rich_Props007', 
            ['quality'] = 0, 
        },
        [4323071] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982699520'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323071, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Closestool001.SM_Furniture_Rich_Closestool001', 
            ['quality'] = 0, 
        },
        [4323072] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982699776'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323072, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketDesk001.SM_Furniture_Rich_PocketDesk001', 
            ['quality'] = 0, 
        },
        [4323073] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982700032'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323073, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A.SM_BlackThorns_Company_2F_Piano_A', 
            ['quality'] = 0, 
        },
        [4323074] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982700288'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323074, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeplate.SM_Coffeeplate', 
            ['quality'] = 0, 
        },
        [4323075] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982700544'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323075, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Showbox001.SM_Coffeeshop_Showbox001', 
            ['quality'] = 0, 
        },
        [4323076] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34497982700800'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323076, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Herbalpot_002.SM_Herbalpot_002', 
            ['quality'] = 0, 
        },
        [4324001] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702106112'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324001, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324002] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702106368'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324002, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_3/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324003] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702106624'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324003, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_4/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324004] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702106880'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324004, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_5/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324005] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702107136'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324005, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_6/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324006] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702107392'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324006, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_7/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324007] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702107648'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324007, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_8/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324008] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702107904'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324008, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_9/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324009] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702108160'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324009, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_10/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324010] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702108416'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324010, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_11/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324011] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702108672'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324011, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_12/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324012] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702108928'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324012, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_13/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324013] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702109184'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324013, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_14/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324014] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702109440'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324014, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_15/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324015] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702109696'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324015, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2301, 
            ['icon'] = '/Game/Arts/UI_16/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324101] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702109952'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324101, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_17/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324102] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702110208'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324102, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_18/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324103] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702110464'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324103, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_19/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324104] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702110720'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324104, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_20/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324105] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702110976'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324105, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_21/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324106] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702111232'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324106, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_22/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324107] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702111488'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324107, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_23/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324108] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702111744'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324108, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_24/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324109] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702112000'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324109, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_25/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324110] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702112256'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324110, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_26/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324111] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702112512'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324111, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_27/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324112] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702112768'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324112, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_28/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324113] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702113024'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324113, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_29/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324114] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702113280'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324114, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_30/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324115] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702113536'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324115, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2302, 
            ['icon'] = '/Game/Arts/UI_31/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324201] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702113792'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324201, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_32/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324202] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702114048'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324202, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_33/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324203] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702114304'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324203, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_34/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324204] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702114560'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324204, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_35/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324205] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702114816'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324205, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_36/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324206] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702115072'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324206, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_37/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324207] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702115328'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324207, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_38/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324208] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702115584'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324208, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_39/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324209] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702115840'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324209, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_40/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324210] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702116096'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324210, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_41/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324211] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702116352'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324211, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_42/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324212] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702116608'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324212, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_43/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324213] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702116864'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324213, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_44/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324214] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702117120'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324214, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_45/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324215] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702117376'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324215, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2303, 
            ['icon'] = '/Game/Arts/UI_46/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324301] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702117632'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324301, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_47/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324302] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702117888'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324302, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_48/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324303] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702118144'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324303, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_49/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324304] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702118400'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324304, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_50/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324305] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702118656'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324305, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_51/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324306] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702118912'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324306, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_52/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324307] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702119168'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324307, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_53/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324308] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702119424'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324308, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_54/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324309] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702119680'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324309, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_55/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324310] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702119936'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324310, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_56/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324311] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702120192'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324311, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_57/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324312] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702120448'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324312, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_58/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324313] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702120704'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324313, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_59/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324314] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702120960'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324314, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_60/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324315] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702121216'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324315, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2304, 
            ['icon'] = '/Game/Arts/UI_61/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324401] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702121472'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324401, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_62/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324402] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702121728'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324402, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_63/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324403] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702121984'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324403, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_64/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324404] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702122240'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324404, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_65/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324405] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702122496'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324405, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_66/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324406] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702122752'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324406, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_67/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324407] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702123008'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324407, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_68/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324408] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702123264'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324408, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_69/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324409] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702123520'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324409, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_70/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324410] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702123776'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324410, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_71/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324411] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702124032'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324411, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_72/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324412] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702124288'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324412, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_73/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324413] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702124544'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324413, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_74/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324414] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702124800'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324414, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_75/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4324415] = {
            ['BuyPrice'] = 300, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34566702125056'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2301, 
            ['GroupSubId'] = 5101, 
            ['ID'] = 4324415, 
            ['MinMoveGrid'] = 100, 
            ['TypeId'] = 1003, 
            ['UnlockLevel'] = 1, 
            ['Volume'] = {400, 500, 300}, 
            ['holdType'] = 2305, 
            ['icon'] = '/Game/Arts/UI_76/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
    }
}
return TopData