--
-- 表名: 家园后处理，合并Furniture和Component表格
--

local TopData = {
    data = {
        [4300000] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033336832'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543675904'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3101, 
            ['ID'] = 4300000, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_bot.SM_ManorNew_Wall01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_top.SM_ManorNew_Wall01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 30, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 63, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01.SM_ManorNew_Wall01', 
            ['quality'] = 0, 
        },
        [4300001] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033337088'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543676160'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3102, 
            ['ID'] = 4300001, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_bot.SM_ManorNew_Window01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_top.SM_ManorNew_Window01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 63, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01.SM_ManorNew_Window01', 
            ['quality'] = 0, 
        },
        [4300002] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '{(4300006),(300,0,0)}{(4300006),(-300,0,0)}', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033337344'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543676416'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2101, 
            ['GroupSubId'] = 3103, 
            ['ID'] = 4300002, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_bot.SM_ManorNew_Door01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_top.SM_ManorNew_Door01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 70, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01.SM_ManorNew_Door01', 
            ['quality'] = 0, 
        },
        [4300003] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543676672'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543676672'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300003, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot01.SM_ManorNew_Pillar01_bot01', 
            ['quality'] = 0, 
        },
        [4300004] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543676928'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543676928'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300004, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot02.SM_ManorNew_Pillar01_bot02', 
            ['quality'] = 0, 
        },
        [4300005] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543677184'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543677184'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300005, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_flair.SM_ManorNew_Pillar01_flair', 
            ['quality'] = 0, 
        },
        [4300006] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 4, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033338368'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543677440'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300006, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {'/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot.SM_ManorNew_Pillar01_bot', '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top'}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01', 
            ['quality'] = 0, 
        },
        [4300007] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 5, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543677696'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543677696'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300007, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {131, 131, 600}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid', 
            ['quality'] = 0, 
        },
        [4300008] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 6, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543677952'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543677952'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300008, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top', 
            ['quality'] = 0, 
        },
        [4300009] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 7, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = '', 
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 0, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300009, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid', 
            ['quality'] = 0, 
        },
        [4300010] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 8, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543678464'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543678464'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2102, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300010, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01', 
            ['quality'] = 0, 
        },
        [4300011] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543678720'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543678720'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2107, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300011, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {95, 95, 280}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing02.SM_ManorNew_RoofRailing02', 
            ['quality'] = 0, 
        },
        [4300012] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543678976'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543678976'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2106, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300012, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 60, 151}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing01.SM_ManorNew_RoofRailing01', 
            ['quality'] = 0, 
        },
        [4300013] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 11, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033340160'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543679232'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2103, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300013, 
            ['MinMoveGrid'] = 600, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 48}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Floor01.SM_ManorNew_Floor01', 
            ['quality'] = 0, 
        },
        [4300014] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 46, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543679488'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543679488'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300014, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type01.SM_ManorNew_Type01', 
            ['quality'] = 0, 
        },
        [4300015] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543679744'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543679744'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300015, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type02.SM_ManorNew_Type02', 
            ['quality'] = 0, 
        },
        [4300016] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 5, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543680000'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543680000'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300016, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 437}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type03.SM_ManorNew_Type03', 
            ['quality'] = 0, 
        },
        [4300017] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 23, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543680256'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543680256'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300017, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type04.SM_ManorNew_Type04', 
            ['quality'] = 0, 
        },
        [4300018] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 11, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543680512'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543680512'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300018, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type05.SM_ManorNew_Type05', 
            ['quality'] = 0, 
        },
        [4300019] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 15, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543680768'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543680768'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300019, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type06.SM_ManorNew_Type06', 
            ['quality'] = 0, 
        },
        [4300020] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 9, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543681024'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543681024'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300020, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type07.SM_ManorNew_Type07', 
            ['quality'] = 0, 
        },
        [4300021] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 31, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543681280'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543681280'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300021, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 345}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type08.SM_ManorNew_Type08', 
            ['quality'] = 0, 
        },
        [4300022] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 19, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543681536'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543681536'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300022, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type09.SM_ManorNew_Type09', 
            ['quality'] = 0, 
        },
        [4300023] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 27, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543681792'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543681792'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300023, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type10.SM_ManorNew_Type10', 
            ['quality'] = 0, 
        },
        [4300024] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 32, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543682048'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543682048'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300024, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type11.SM_ManorNew_Type11', 
            ['quality'] = 0, 
        },
        [4300025] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 40, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543682304'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543682304'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300025, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type12.SM_ManorNew_Type12', 
            ['quality'] = 0, 
        },
        [4300026] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 36, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543682560'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543682560'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300026, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type13.SM_ManorNew_Type13', 
            ['quality'] = 0, 
        },
        [4300027] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 99, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543682816'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543682816'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300027, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 58}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type14.SM_ManorNew_Type14', 
            ['quality'] = 0, 
        },
        [4300028] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 42, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543683072'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543683072'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2104, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4300028, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {600, 600, 644}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type15.SM_ManorNew_Type15', 
            ['quality'] = 0, 
        },
        [4300029] = {
            ['AreaSize'] = 0, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = false, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 1, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34360543683328'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543683328'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3504, 
            ['ID'] = 4300029, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Double01_Stair.SM_ManorNew_Double01_Stair', 
            ['quality'] = 0, 
        },
        [4300030] = {
            ['AreaSize'] = 2, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 2, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033344512'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543683584'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3501, 
            ['ID'] = 4300030, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 10, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {1200, 600, 822}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Single01_Stair.SM_ManorNew_Single01_Stair', 
            ['quality'] = 0, 
        },
        [4300031] = {
            ['AreaSize'] = 3, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 3, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033344768'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543683840'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3502, 
            ['ID'] = 4300031, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn01_Stair.SM_ManorNew_Turn01_Stair', 
            ['quality'] = 0, 
        },
        [4300032] = {
            ['AreaSize'] = 3, 
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 2, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 4, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34364033345024'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34360543684096'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2105, 
            ['GroupSubId'] = 3503, 
            ['ID'] = 4300032, 
            ['MinMoveGrid'] = 0, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1001, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn02_Stair.SM_ManorNew_Turn02_Stair', 
            ['quality'] = 0, 
        },
        [4320001] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325947904'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104722432'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4320002] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948160'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104722688'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {500, 500, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4320003] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948416'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104722944'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy001.SM_Toy001', 
            ['quality'] = 0, 
        },
        [4320004] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948672'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723200'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 500, 300}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A2.SM_BlackThorns_Company_2F_Piano_A2', 
            ['quality'] = 0, 
        },
        [4320005] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948928'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723456'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {100, 100, 200}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Glob.SM_BlackThorns_Company_2F_Glob', 
            ['quality'] = 0, 
        },
        [4320006] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949184'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723712'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Scarecrow.SM_Scarecrow', 
            ['quality'] = 0, 
        },
        [4320007] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949440'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723968'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 100}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bath001.SM_Bath001', 
            ['quality'] = 0, 
        },
        [4320008] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949696'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104724224'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Machine002.SM_Coffeeshop_Machine002', 
            ['quality'] = 0, 
        },
        [4320009] = {
            ['BuyPrice'] = 1000, 
            ['CanShow'] = true, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949952'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104724480'),
            ['FurnitureScore'] = 100, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 500, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 1000, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Angel.SM_Statue_Angel', 
            ['quality'] = 0, 
        },
        [4320010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104724736'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104724736'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk01.SM_Desk01A', 
            ['quality'] = 0, 
        },
        [4320011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104724992'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104724992'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4320012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104722432'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104722432'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4320013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325950976'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104725504'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet004.SM_BlackThorns_DunnOffice_Cabinet004', 
            ['quality'] = 0, 
        },
        [4320014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104725760'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104725760'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4320015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104726016'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104726016'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair04.SM_Chair04', 
            ['quality'] = 0, 
        },
        [4320016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104726272'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104726272'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair001.SM_Coffeeshop_Chair001', 
            ['quality'] = 0, 
        },
        [4320017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104726528'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104726528'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair002.SM_Coffeeshop_Chair002', 
            ['quality'] = 0, 
        },
        [4320018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104726784'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104726784'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair003.SM_Coffeeshop_Chair003', 
            ['quality'] = 0, 
        },
        [4320019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104727040'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104727040'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chair_001.SM_BlackThorns_Company_2F_Chair_001', 
            ['quality'] = 0, 
        },
        [4320020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104727296'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104727296'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa001.SM_Dragon_Bar_Sofa001', 
            ['quality'] = 0, 
        },
        [4320021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104727552'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104727552'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa002.SM_Dragon_Bar_Sofa002', 
            ['quality'] = 0, 
        },
        [4320022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104727808'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104727808'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Sofa001A.SM_Divination_Club_Sofa001A', 
            ['quality'] = 0, 
        },
        [4320023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104728064'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104728064'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Chair001.SM_Furniture_Rich_Chair001', 
            ['quality'] = 0, 
        },
        [4320024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104728320'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104728320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_CoffeeTable001.SM_CoffeeTable001A', 
            ['quality'] = 0, 
        },
        [4320025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104728576'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104728576'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bakeshop_Cupboards001.SM_Bakeshop_Cupboards001', 
            ['quality'] = 0, 
        },
        [4320026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104728832'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104728832'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_BookcaseHZ_003.SM_BlackThorns_Company_2F_BookcaseHZ_003', 
            ['quality'] = 0, 
        },
        [4320027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104729088'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104729088'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_icon_Woodcup_M.UI_Item_Icon_icon_Woodcup_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet003.SM_BlackThorns_DunnOffice_Cabinet003', 
            ['quality'] = 0, 
        },
        [4320028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104729344'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104729344'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet03.SM_Cabinet03', 
            ['quality'] = 0, 
        },
        [4320029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104729600'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104729600'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet04.SM_Cabinet04', 
            ['quality'] = 0, 
        },
        [4320030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104729856'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104729856'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet05.SM_Cabinet05', 
            ['quality'] = 0, 
        },
        [4320031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104730112'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104730112'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet001.SM_Furniture_Rich_Cabinet001', 
            ['quality'] = 0, 
        },
        [4320032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104730368'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104730368'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet003.SM_Furniture_Rich_Cabinet003', 
            ['quality'] = 0, 
        },
        [4320033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104730624'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104730624'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet004.SM_Furniture_Rich_Cabinet004', 
            ['quality'] = 0, 
        },
        [4320034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104730880'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104730880'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet007.SM_Furniture_Rich_Cabinet007', 
            ['quality'] = 0, 
        },
        [4320035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104731136'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104731136'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror001.SM_BlackThorns_DunnOffice_Mirror001', 
            ['quality'] = 0, 
        },
        [4320036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104731392'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104731392'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror003.SM_BlackThorns_DunnOffice_Mirror003', 
            ['quality'] = 0, 
        },
        [4320037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104731648'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104731648'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_FlowerpotHZ002.SM_Coffeeshop_FlowerpotHZ002', 
            ['quality'] = 0, 
        },
        [4320038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104731904'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104731904'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_FloorLampHZ_002.SM_BlackThorns_Company_FloorLampHZ_002', 
            ['quality'] = 0, 
        },
        [4320039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104732160'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104732160'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ005.SM_Divination_CandlestickHZ005', 
            ['quality'] = 0, 
        },
        [4320040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104732416'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104732416'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ006.SM_Divination_CandlestickHZ006', 
            ['quality'] = 0, 
        },
        [4320041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104732672'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104732672'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_InkPenHZ002.SM_Divination_InkPenHZ002', 
            ['quality'] = 0, 
        },
        [4320042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104732928'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104732928'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot003.SM_Flowerpot003', 
            ['quality'] = 0, 
        },
        [4320043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104733184'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104733184'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4320044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104733440'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104733440'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LampHZ001.SM_Furniture_Rich_LampHZ001', 
            ['quality'] = 0, 
        },
        [4320045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104733696'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104733696'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LightHZ001.SM_Furniture_Rich_LightHZ001', 
            ['quality'] = 0, 
        },
        [4320046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104733952'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104733952'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Camera_M.UI_Item_Icon_Camera_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed02.SM_Bed02', 
            ['quality'] = 0, 
        },
        [4320047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104734208'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104734208'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4320048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104734464'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104734464'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed004.SM_Furniture_Rich_Bed004', 
            ['quality'] = 0, 
        },
        [4320049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104734720'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104734720'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed003.SM_Furniture_Rich_Bed003', 
            ['quality'] = 0, 
        },
        [4320050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104734976'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104734976'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props004.SM_Furniture_Rich_Props004', 
            ['quality'] = 0, 
        },
        [4320051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104735232'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104735232'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props012.SM_Furniture_Rich_Props012', 
            ['quality'] = 0, 
        },
        [4320052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104735488'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104735488'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_2F_Fireplace001_A.SM_2F_Fireplace001_A', 
            ['quality'] = 0, 
        },
        [4320053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104735744'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104735744'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet002.SM_BlackThorns_DunnOffice_Cabinet002', 
            ['quality'] = 0, 
        },
        [4320054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34223104736000'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104736000'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 0, 
            ['ID'] = 4320054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace02.SM_Fireplace02', 
            ['quality'] = 0, 
        },
        [4321000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104736256'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk01A.SM_Desk01A', 
            ['quality'] = 0, 
        },
        [4321001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104736512'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Desk02.SM_Desk02', 
            ['quality'] = 0, 
        },
        [4321002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104736768'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4101, 
            ['ID'] = 4321002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_TableHZ03.SM_TableHZ03', 
            ['quality'] = 0, 
        },
        [4321003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104737024'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair01.SM_Chair01', 
            ['quality'] = 0, 
        },
        [4321004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104737280'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair02.SM_Chair02', 
            ['quality'] = 0, 
        },
        [4321005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104737536'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair04.SM_Chair04', 
            ['quality'] = 0, 
        },
        [4321006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104737792'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rockingchair.SM_Rockingchair', 
            ['quality'] = 0, 
        },
        [4321007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104738048'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Chair03.SM_Chair03', 
            ['quality'] = 0, 
        },
        [4321008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104728320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_Piano_Bench.SM_Octopus_Piano_Bench', 
            ['quality'] = 0, 
        },
        [4321009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104738560'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Chair001.SM_Furniture_Rich_Chair001', 
            ['quality'] = 0, 
        },
        [4321010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104738816'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa001.SM_Dragon_Bar_Sofa001', 
            ['quality'] = 0, 
        },
        [4321011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104739072'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Sofa001A.SM_Divination_Club_Sofa001A', 
            ['quality'] = 0, 
        },
        [4321012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104739328'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4102, 
            ['ID'] = 4321012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_Sofa002.SM_Dragon_Bar_Sofa002', 
            ['quality'] = 0, 
        },
        [4321013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104739584'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_A2_Cabinet008_E.SM_Dragon_Bar_A2_Cabinet008_E', 
            ['quality'] = 0, 
        },
        [4321014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325965312'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104739840'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet01.SM_Cabinet01', 
            ['quality'] = 0, 
        },
        [4321015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104740096'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Dragon_Bar_A2_Cabinet008_F.SM_Dragon_Bar_A2_Cabinet008_F', 
            ['quality'] = 0, 
        },
        [4321016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104740352'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet04.SM_Cabinet04', 
            ['quality'] = 0, 
        },
        [4321017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104740608'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet05.SM_Cabinet05', 
            ['quality'] = 0, 
        },
        [4321018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104740864'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet02.SM_Cabinet02', 
            ['quality'] = 0, 
        },
        [4321019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104741120'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet03.SM_Cabinet03', 
            ['quality'] = 0, 
        },
        [4321020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104741376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Table04.SM_Table04', 
            ['quality'] = 0, 
        },
        [4321021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104741632'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4103, 
            ['ID'] = 4321021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Cabinet001.SM_Cabinet001', 
            ['quality'] = 0, 
        },
        [4321022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325967360'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104741888'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed02.SM_Bed02', 
            ['quality'] = 0, 
        },
        [4321023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104742144'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed01.SM_Bed01', 
            ['quality'] = 0, 
        },
        [4321024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104742400'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed01_B.SM_Bed01_B', 
            ['quality'] = 0, 
        },
        [4321025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104742656'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2201, 
            ['GroupSubId'] = 4104, 
            ['ID'] = 4321025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed002.SM_Furniture_Rich_Bed002', 
            ['quality'] = 0, 
        },
        [4321026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104742912'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Candlestick001.SM_Candlestick001', 
            ['quality'] = 0, 
        },
        [4321027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104743168'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ006.SM_Divination_CandlestickHZ006', 
            ['quality'] = 0, 
        },
        [4321028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104743424'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ007.SM_Divination_CandlestickHZ007', 
            ['quality'] = 0, 
        },
        [4321029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104743680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4201, 
            ['ID'] = 4321029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CandlestickHZ005.SM_Divination_CandlestickHZ005', 
            ['quality'] = 0, 
        },
        [4321030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104743936'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Carpet001.SM_Rylbir_Houes_Carpet001', 
            ['quality'] = 0, 
        },
        [4321031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104744192'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Carpet002.SM_Rylbir_Houes_Carpet002', 
            ['quality'] = 0, 
        },
        [4321032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104744448'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_DivinationArea_Carpet001.SM_Divination_Club_DivinationArea_Carpet001', 
            ['quality'] = 0, 
        },
        [4321033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104744704'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4202, 
            ['ID'] = 4321033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Carpet002.SM_Divination_Club_Carpet002', 
            ['quality'] = 0, 
        },
        [4321034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104744960'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_LetherBbook.SM_LetherBbook', 
            ['quality'] = 0, 
        },
        [4321035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104745216'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile8.SM_BookPile8', 
            ['quality'] = 0, 
        },
        [4321036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104745472'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile11.SM_BookPile11', 
            ['quality'] = 0, 
        },
        [4321037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104745728'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BookPile14.SM_BookPile14', 
            ['quality'] = 0, 
        },
        [4321038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104745984'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_TiengenPaper.SM_TiengenPaper', 
            ['quality'] = 0, 
        },
        [4321039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104746240'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_NotePlate.SM_NotePlate', 
            ['quality'] = 0, 
        },
        [4321040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104746496'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ06.SM_Divination_Club_BookHZ06', 
            ['quality'] = 0, 
        },
        [4321041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104746752'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ05.SM_Divination_Club_BookHZ05', 
            ['quality'] = 0, 
        },
        [4321042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104747008'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ08.SM_Divination_Club_BookHZ08', 
            ['quality'] = 0, 
        },
        [4321043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104747264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_BookHZ07.SM_Divination_Club_BookHZ07', 
            ['quality'] = 0, 
        },
        [4321044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104747520'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_InkPenHZ002.SM_Divination_InkPenHZ002', 
            ['quality'] = 0, 
        },
        [4321045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948928'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723456'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {100, 100, 200}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Glob.SM_BlackThorns_Company_2F_Glob', 
            ['quality'] = 0, 
        },
        [4321046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104748032'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pen.SM_Pen', 
            ['quality'] = 0, 
        },
        [4321047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104748288'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Map002.SM_Pirate_School_BernadeOffice_Map002', 
            ['quality'] = 0, 
        },
        [4321048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104748544'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4321049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104748800'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props011.SM_Furniture_Rich_Props011', 
            ['quality'] = 0, 
        },
        [4321050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104749056'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Poker002.SM_Poker002', 
            ['quality'] = 0, 
        },
        [4321051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104749312'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Map001.SM_Pirate_School_BernadeOffice_Map001', 
            ['quality'] = 0, 
        },
        [4321052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104749568'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props054.SM_Pirate_School_craftsroom_Props054', 
            ['quality'] = 0, 
        },
        [4321053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104749824'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props050.SM_Pirate_School_craftsroom_Props050', 
            ['quality'] = 0, 
        },
        [4321054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104750080'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_Library_BookShelfHZ001.SM_Pirate_School_Library_BookShelfHZ001', 
            ['quality'] = 0, 
        },
        [4321055] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104750336'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321055, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_SandGlass002.SM_Divination_SandGlass002', 
            ['quality'] = 0, 
        },
        [4321056] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104750592'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321056, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_RoleCreate_Typewriter.SM_RoleCreate_Typewriter', 
            ['quality'] = 0, 
        },
        [4321057] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_31543313573376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321057, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clinic_Prosp015.SM_Clinic_Prosp015', 
            ['quality'] = 0, 
        },
        [4321058] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104751104'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321058, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Sailboat.SM_Sailboat', 
            ['quality'] = 0, 
        },
        [4321059] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104751360'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321059, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_Props014.SM_Pirate_School_craftsroom_Props014', 
            ['quality'] = 0, 
        },
        [4321060] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104751616'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321060, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_CrystalBall001.SM_Divination_CrystalBall001', 
            ['quality'] = 0, 
        },
        [4321061] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104751872'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321061, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Props/SM_Divination/SM_Candlestick004_7.SM_Candlestick004_7', 
            ['quality'] = 0, 
        },
        [4321062] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104752128'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321062, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Herbalpot_003.SM_Herbalpot_003', 
            ['quality'] = 0, 
        },
        [4321063] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104752384'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321063, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Gemstone003.SM_Gemstone003', 
            ['quality'] = 0, 
        },
        [4321064] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104752640'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321064, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Gemstone004.SM_Gemstone004', 
            ['quality'] = 0, 
        },
        [4321065] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104752896'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321065, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Mirror02.SM_Mirror02', 
            ['quality'] = 0, 
        },
        [4321066] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104753152'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321066, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace02.SM_Fireplace02', 
            ['quality'] = 0, 
        },
        [4321067] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949184'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723712'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321067, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Scarecrow.SM_Scarecrow', 
            ['quality'] = 0, 
        },
        [4321068] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104753664'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321068, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Hanger001.SM_Furniture_Rich_Hanger001', 
            ['quality'] = 0, 
        },
        [4321069] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104753920'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321069, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_Blackboard002.SM_Pirate_School_Blackboard002', 
            ['quality'] = 0, 
        },
        [4321070] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104754176'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321070, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_001.SM_Bike_001', 
            ['quality'] = 0, 
        },
        [4321071] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104754432'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321071, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_005.SM_Bike_005', 
            ['quality'] = 0, 
        },
        [4321072] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104754688'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321072, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bike_003.SM_Bike_003', 
            ['quality'] = 0, 
        },
        [4321073] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104754944'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321073, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Camera.SM_Camera', 
            ['quality'] = 0, 
        },
        [4321074] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104755200'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321074, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_trashCan.SM_Component_trashCan', 
            ['quality'] = 0, 
        },
        [4321075] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104755456'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321075, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_Trashcan003.SM_Component_Trashcan003', 
            ['quality'] = 0, 
        },
        [4321076] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104755712'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321076, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Component_Faucet005.SM_Component_Faucet005', 
            ['quality'] = 0, 
        },
        [4321077] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949952'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104755968'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321077, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Angel.SM_Statue_Angel', 
            ['quality'] = 0, 
        },
        [4321078] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104756224'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321078, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Baby.SM_Statue_Baby', 
            ['quality'] = 0, 
        },
        [4321079] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104756480'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321079, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Statue_Oldman.SM_Statue_Oldman', 
            ['quality'] = 0, 
        },
        [4321080] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104756736'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321080, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_StatueTFool.SM_StatueTFool', 
            ['quality'] = 0, 
        },
        [4321081] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104756992'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321081, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Weapon_Shield007.SM_Weapon_Shield007', 
            ['quality'] = 0, 
        },
        [4321082] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104757248'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4204, 
            ['ID'] = 4321082, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Wheelchair_001.SM_Wheelchair_001', 
            ['quality'] = 0, 
        },
        [4321083] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104757504'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321083, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed03.SM_Bed03', 
            ['quality'] = 0, 
        },
        [4321084] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104757760'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321084, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bed04.SM_Bed04', 
            ['quality'] = 0, 
        },
        [4321085] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104758016'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321085, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props013.SM_Furniture_Rich_Props013', 
            ['quality'] = 0, 
        },
        [4321086] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104758272'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321086, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props015.SM_Furniture_Rich_Props015', 
            ['quality'] = 0, 
        },
        [4321087] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104758528'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321087, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props014.SM_Furniture_Rich_Props014', 
            ['quality'] = 0, 
        },
        [4321088] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104758784'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321088, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props012.SM_Furniture_Rich_Props012', 
            ['quality'] = 0, 
        },
        [4321089] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104759040'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321089, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_Sofa001C.SM_Rich_house_Sofa001C', 
            ['quality'] = 0, 
        },
        [4321090] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104759296'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321090, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pillow001.SM_Pillow001', 
            ['quality'] = 0, 
        },
        [4321091] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948416'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104759552'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321091, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 200, 500}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy001.SM_Toy001', 
            ['quality'] = 0, 
        },
        [4321092] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104759808'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4205, 
            ['ID'] = 4321092, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toy002.SM_Toy002', 
            ['quality'] = 0, 
        },
        [4321093] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104760064'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4206, 
            ['ID'] = 4321093, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_FlowerpotHZ002.SM_Coffeeshop_FlowerpotHZ002', 
            ['quality'] = 0, 
        },
        [4321094] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104760320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4206, 
            ['ID'] = 4321094, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005_D.SM_Flowerpot005_D', 
            ['quality'] = 0, 
        },
        [4321095] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104760576'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321095, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains007.SM_Rylbir_Houes_Curtains007', 
            ['quality'] = 0, 
        },
        [4321096] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104760832'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321096, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains008.SM_Rylbir_Houes_Curtains008', 
            ['quality'] = 0, 
        },
        [4321097] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104761088'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321097, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains009.SM_Rylbir_Houes_Curtains009', 
            ['quality'] = 0, 
        },
        [4321098] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104761344'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4301, 
            ['ID'] = 4321098, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_Curtain001.SM_BlackThorns_Company_Curtain001', 
            ['quality'] = 0, 
        },
        [4321099] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104761600'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321099, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_Shelf001A.SM_Ferlanqi_Flats_Shelf001A', 
            ['quality'] = 0, 
        },
        [4321100] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104761856'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321100, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_WallPainting001A.SM_Ferlanqi_Flats_WallPainting001A', 
            ['quality'] = 0, 
        },
        [4321101] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104762112'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321101, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_WallPainting001C.SM_Ferlanqi_Flats_WallPainting001C', 
            ['quality'] = 0, 
        },
        [4321102] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104762368'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321102, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_PotionRoom_Painter004.SM_Pirate_School_PotionRoom_Painter004', 
            ['quality'] = 0, 
        },
        [4321103] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104762624'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321103, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_PotionRoom_Painter010.SM_Pirate_School_PotionRoom_Painter010', 
            ['quality'] = 0, 
        },
        [4321104] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104762880'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321104, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Painting001.SM_BlackThorns_DunnOffice_Painting001', 
            ['quality'] = 0, 
        },
        [4321105] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104763136'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321105, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WallPainting001.SM_WallPainting001', 
            ['quality'] = 0, 
        },
        [4321106] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104763392'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321106, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_craftsroom_PropsHZ065.SM_Pirate_School_craftsroom_PropsHZ065', 
            ['quality'] = 0, 
        },
        [4321107] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104763648'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321107, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clocklamp004.SM_Clocklamp004', 
            ['quality'] = 0, 
        },
        [4321108] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104763904'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321108, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Clocklamp002.SM_Clocklamp002', 
            ['quality'] = 0, 
        },
        [4321109] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104764160'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321109, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Antler001.SM_Furniture_Rich_Antler001', 
            ['quality'] = 0, 
        },
        [4321110] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104764416'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321110, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props001.SM_Furniture_Rich_Props001', 
            ['quality'] = 0, 
        },
        [4321111] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104764672'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2203, 
            ['GroupSubId'] = 4302, 
            ['ID'] = 4321111, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props016.SM_Furniture_Rich_Props016', 
            ['quality'] = 0, 
        },
        [4321112] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104764928'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321112, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Ferlanqi_Flats_Washstand002.SM_Ferlanqi_Flats_Washstand002', 
            ['quality'] = 0, 
        },
        [4321113] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104765184'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321113, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Toilet01.SM_Toilet01', 
            ['quality'] = 0, 
        },
        [4321114] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104765440'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321114, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props010.SM_Furniture_Rich_Props010', 
            ['quality'] = 0, 
        },
        [4321115] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104765696'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321115, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props002.SM_Furniture_Rich_Props002', 
            ['quality'] = 0, 
        },
        [4321116] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104765952'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4401, 
            ['ID'] = 4321116, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props008.SM_Furniture_Rich_Props008', 
            ['quality'] = 0, 
        },
        [4321117] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104766208'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321117, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_shelf.SM_Octopus_shelf', 
            ['quality'] = 0, 
        },
        [4321118] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104766464'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321118, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_timpani.SM_Octopus_timpani', 
            ['quality'] = 0, 
        },
        [4321119] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104766720'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321119, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_basic.SM_Octopus_basic', 
            ['quality'] = 0, 
        },
        [4321120] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104766976'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321120, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Octopus_violin.SM_Octopus_violin', 
            ['quality'] = 0, 
        },
        [4321121] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_59236994362112'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4402, 
            ['ID'] = 4321121, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano.SM_BlackThorns_Company_2F_Piano', 
            ['quality'] = 0, 
        },
        [4321122] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104767488'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321122, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Fireplace01.SM_Fireplace01', 
            ['quality'] = 0, 
        },
        [4321123] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104767744'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321123, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coalstove01.SM_Coalstove01', 
            ['quality'] = 0, 
        },
        [4321124] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949696'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104724224'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4403, 
            ['ID'] = 4321124, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 400}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Machine002.SM_Coffeeshop_Machine002', 
            ['quality'] = 0, 
        },
        [4321125] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104768256'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321125, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant002.SM_Coffeeshop_Plant002', 
            ['quality'] = 0, 
        },
        [4321126] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104768512'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321126, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant004.SM_Coffeeshop_Plant004', 
            ['quality'] = 0, 
        },
        [4321127] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104768768'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321127, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Tea_Set001.SM_Divination_Club_Tea_Set001', 
            ['quality'] = 0, 
        },
        [4321128] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104769024'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321128, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Soup_001.SM_Soup_001', 
            ['quality'] = 0, 
        },
        [4321129] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104769280'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321129, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Kettle03.SM_Kettle03', 
            ['quality'] = 0, 
        },
        [4321130] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104769536'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321130, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pot001.SM_Pot001', 
            ['quality'] = 0, 
        },
        [4321131] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_39033468199680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321131, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Bottle002.SM_Coffeeshop_Bottle002', 
            ['quality'] = 0, 
        },
        [4321132] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104770048'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321132, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Plate001.SM_Plate001', 
            ['quality'] = 0, 
        },
        [4321133] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104770304'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321133, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Circus_Props016.SM_Circus_Props016', 
            ['quality'] = 0, 
        },
        [4321134] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104770560'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321134, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pumpkin_B.SM_Pumpkin_B', 
            ['quality'] = 0, 
        },
        [4321135] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104770816'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2202, 
            ['GroupSubId'] = 4203, 
            ['ID'] = 4321135, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Apple.SM_Apple', 
            ['quality'] = 0, 
        },
        [4321136] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104771072'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2204, 
            ['GroupSubId'] = 4404, 
            ['ID'] = 4321136, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Alchemicalinstruments.SM_Alchemicalinstruments', 
            ['quality'] = 0, 
        },
        [4322000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34226325950976'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet004.SM_BlackThorns_DunnOffice_Cabinet004', 
            ['quality'] = 0, 
        },
        [4322001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104771584'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_ShortCabinet001.SM_BlackThorns_Company_2F_ShortCabinet001', 
            ['quality'] = 0, 
        },
        [4322002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104771840'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_Table_001.SM_BlackThorns_Company_Table_001', 
            ['quality'] = 0, 
        },
        [4322003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104772096'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chair_001.SM_BlackThorns_Company_2F_Chair_001', 
            ['quality'] = 0, 
        },
        [4322004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104772352'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_BookcaseHZ_003.SM_BlackThorns_Company_2F_BookcaseHZ_003', 
            ['quality'] = 0, 
        },
        [4322005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104772608'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet003.SM_BlackThorns_DunnOffice_Cabinet003', 
            ['quality'] = 0, 
        },
        [4322006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104772864'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_2F_Fireplace001_A.SM_2F_Fireplace001_A', 
            ['quality'] = 0, 
        },
        [4322007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104773120'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Cabinet002.SM_BlackThorns_DunnOffice_Cabinet002', 
            ['quality'] = 0, 
        },
        [4322008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104773376'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror002.SM_BlackThorns_DunnOffice_Mirror002', 
            ['quality'] = 0, 
        },
        [4322009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104773632'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror001.SM_BlackThorns_DunnOffice_Mirror001', 
            ['quality'] = 0, 
        },
        [4322010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104773888'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_DunnOffice_Mirror003.SM_BlackThorns_DunnOffice_Mirror003', 
            ['quality'] = 0, 
        },
        [4322011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948672'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104774144'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4501, 
            ['ID'] = 4322011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {400, 500, 300}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A2.SM_BlackThorns_Company_2F_Piano_A2', 
            ['quality'] = 0, 
        },
        [4323000] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325947904'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104774400'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323000, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {300, 200, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Backlund_Restaurant001_Table002.SM_Backlund_Restaurant001_Table002', 
            ['quality'] = 0, 
        },
        [4323001] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104774656'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323001, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_DiningTable002.SM_Furniture_Rich_DiningTable002', 
            ['quality'] = 0, 
        },
        [4323002] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104774912'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323002, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Desk001.SM_Coffeeshop_Desk001', 
            ['quality'] = 0, 
        },
        [4323003] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104775168'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323003, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002a.SM_Furniture_Rich_PocketMirror002a', 
            ['quality'] = 0, 
        },
        [4323004] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104775424'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323004, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_CoffeeTable001A.SM_CoffeeTable001A', 
            ['quality'] = 0, 
        },
        [4323005] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104775680'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323005, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Table.SM_Table', 
            ['quality'] = 0, 
        },
        [4323006] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104775936'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323006, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Lobby_TableHZ002.SM_Divination_Club_Lobby_TableHZ002', 
            ['quality'] = 0, 
        },
        [4323007] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104776192'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323007, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_RoundTable001.SM_RoundTable001', 
            ['quality'] = 0, 
        },
        [4323008] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104776448'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323008, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet006.SM_Furniture_Rich_Cabinet006', 
            ['quality'] = 0, 
        },
        [4323009] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104776704'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323009, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair002.SM_Coffeeshop_Chair002', 
            ['quality'] = 0, 
        },
        [4323010] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104776960'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323010, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair001.SM_Coffeeshop_Chair001', 
            ['quality'] = 0, 
        },
        [4323011] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104777216'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323011, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_SofaHZ001D.SM_Rich_house_SofaHZ001D', 
            ['quality'] = 0, 
        },
        [4323012] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104777472'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323012, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rich_house_SofaHZ001A.SM_Rich_house_SofaHZ001A', 
            ['quality'] = 0, 
        },
        [4323013] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104777728'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323013, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WYZY_Prop_Sofa_03_b.SM_WYZY_Prop_Sofa_03_b', 
            ['quality'] = 0, 
        },
        [4323014] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104777984'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323014, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_WYZY_Prop_Sofa_03_a.SM_WYZY_Prop_Sofa_03_a', 
            ['quality'] = 0, 
        },
        [4323015] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104778240'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323015, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_ChairHZ002.SM_Divination_Club_ChairHZ002', 
            ['quality'] = 0, 
        },
        [4323016] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104778496'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323016, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_StrategychairHZ001.SM_Pirate_School_BernadeOffice_StrategychairHZ001', 
            ['quality'] = 0, 
        },
        [4323017] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104778752'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323017, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Chair003.SM_Coffeeshop_Chair003', 
            ['quality'] = 0, 
        },
        [4323018] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104779008'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323018, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bakeshop_Cupboards001.SM_Bakeshop_Cupboards001', 
            ['quality'] = 0, 
        },
        [4323019] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104779264'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323019, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet001.SM_Furniture_Rich_Cabinet001', 
            ['quality'] = 0, 
        },
        [4323020] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104779520'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323020, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002c.SM_Furniture_Rich_PocketMirror002c', 
            ['quality'] = 0, 
        },
        [4323021] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104779776'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323021, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet005.SM_Furniture_Rich_Cabinet005', 
            ['quality'] = 0, 
        },
        [4323022] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104780032'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323022, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketMirror002b.SM_Furniture_Rich_PocketMirror002b', 
            ['quality'] = 0, 
        },
        [4323023] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104780288'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323023, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cupboards002.SM_Furniture_Rich_Cupboards002', 
            ['quality'] = 0, 
        },
        [4323024] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104780544'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323024, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet008.SM_Furniture_Rich_Cabinet008', 
            ['quality'] = 0, 
        },
        [4323025] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104780800'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323025, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet007.SM_Furniture_Rich_Cabinet007', 
            ['quality'] = 0, 
        },
        [4323026] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104781056'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323026, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet010.SM_Furniture_Rich_Cabinet010', 
            ['quality'] = 0, 
        },
        [4323027] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104781312'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323027, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet009.SM_Furniture_Rich_Cabinet009', 
            ['quality'] = 0, 
        },
        [4323028] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104781568'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323028, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet003.SM_Furniture_Rich_Cabinet003', 
            ['quality'] = 0, 
        },
        [4323029] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104781824'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323029, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Cabinet004.SM_Furniture_Rich_Cabinet004', 
            ['quality'] = 0, 
        },
        [4323030] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104782080'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323030, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_BernadeOffice_Bookcase002.SM_Pirate_School_BernadeOffice_Bookcase002', 
            ['quality'] = 0, 
        },
        [4323031] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104782336'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323031, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Pirate_School_library_BookShelf003.SM_Pirate_School_library_BookShelf003', 
            ['quality'] = 0, 
        },
        [4323032] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104782592'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323032, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Interactor_Leatherbox001a.SM_Interactor_Leatherbox001a', 
            ['quality'] = 0, 
        },
        [4323033] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104782848'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323033, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed004.SM_Furniture_Rich_Bed004', 
            ['quality'] = 0, 
        },
        [4323034] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104783104'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323034, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed003.SM_Furniture_Rich_Bed003', 
            ['quality'] = 0, 
        },
        [4323035] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325948160'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104722688'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323035, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {500, 500, 0}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Bed001.SM_Furniture_Rich_Bed001', 
            ['quality'] = 0, 
        },
        [4323036] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104783616'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323036, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LampHZ001.SM_Furniture_Rich_LampHZ001', 
            ['quality'] = 0, 
        },
        [4323037] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104783872'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323037, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_LightHZ001.SM_Furniture_Rich_LightHZ001', 
            ['quality'] = 0, 
        },
        [4323038] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104784128'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323038, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Lamp_003.SM_Lamp_003', 
            ['quality'] = 0, 
        },
        [4323039] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104784384'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323039, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_FloorLampHZ_002.SM_BlackThorns_Company_FloorLampHZ_002', 
            ['quality'] = 0, 
        },
        [4323040] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104784640'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323040, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Light001a.SM_Coffeeshop_Light001a', 
            ['quality'] = 0, 
        },
        [4323041] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104784896'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323041, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Chandelier_Candle.SM_BlackThorns_Company_2F_Chandelier_Candle', 
            ['quality'] = 0, 
        },
        [4323042] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104785152'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323042, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet001.SM_Carpet001', 
            ['quality'] = 0, 
        },
        [4323043] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104785408'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323043, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet002.SM_Carpet002', 
            ['quality'] = 0, 
        },
        [4323044] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104785664'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323044, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Carpet005.SM_Carpet005', 
            ['quality'] = 0, 
        },
        [4323045] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104785920'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323045, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_Specimen02.SM_Divination_Club_Specimen02', 
            ['quality'] = 0, 
        },
        [4323046] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104786176'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323046, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_011.SM_Perfume_Bottle_011', 
            ['quality'] = 0, 
        },
        [4323047] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104786432'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323047, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot001.SM_Flowerpot001', 
            ['quality'] = 0, 
        },
        [4323048] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104786688'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323048, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot003.SM_Flowerpot003', 
            ['quality'] = 0, 
        },
        [4323049] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104786944'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323049, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props006.SM_Furniture_Rich_Props006', 
            ['quality'] = 0, 
        },
        [4323050] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104787200'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323050, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props009.SM_Furniture_Rich_Props009', 
            ['quality'] = 0, 
        },
        [4323051] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104787456'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323051, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props005.SM_Furniture_Rich_Props005', 
            ['quality'] = 0, 
        },
        [4323052] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104787712'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323052, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Jewellery008.SM_Jewellery008', 
            ['quality'] = 0, 
        },
        [4323053] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104787968'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323053, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Jewellery017.SM_Jewellery017', 
            ['quality'] = 0, 
        },
        [4323054] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104788224'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323054, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_001.SM_Perfume_Bottle_001', 
            ['quality'] = 0, 
        },
        [4323055] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104788480'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323055, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Perfume_Bottle_003.SM_Perfume_Bottle_003', 
            ['quality'] = 0, 
        },
        [4323056] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104788736'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323056, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_MirrorHZ002.SM_Divination_Club_MirrorHZ002', 
            ['quality'] = 0, 
        },
        [4323057] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104788992'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323057, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Mirror001.SM_Furniture_Rich_Mirror001', 
            ['quality'] = 0, 
        },
        [4323058] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104789248'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323058, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Fireplace001_a.SM_Furniture_Rich_Fireplace001_a', 
            ['quality'] = 0, 
        },
        [4323059] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104789504'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323059, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_ClockHZ01.SM_Divination_ClockHZ01', 
            ['quality'] = 0, 
        },
        [4323060] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104789760'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323060, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Plant001.SM_Coffeeshop_Plant001', 
            ['quality'] = 0, 
        },
        [4323061] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104790016'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323061, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005.SM_Flowerpot005', 
            ['quality'] = 0, 
        },
        [4323062] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104790272'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323062, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot005.SM_Flowerpot005', 
            ['quality'] = 0, 
        },
        [4323063] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104790528'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323063, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4323064] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104790784'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323064, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Flowerpot004.SM_Flowerpot004', 
            ['quality'] = 0, 
        },
        [4323065] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104791040'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323065, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Flowerpot001.SM_Coffeeshop_Flowerpot001', 
            ['quality'] = 0, 
        },
        [4323066] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104791296'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323066, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Rylbir_Houes_Curtains003_A.SM_Rylbir_Houes_Curtains003_A', 
            ['quality'] = 0, 
        },
        [4323067] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104791552'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323067, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Divination_Club_WindowGauzeHZ002.SM_Divination_Club_WindowGauzeHZ002', 
            ['quality'] = 0, 
        },
        [4323068] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104791808'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323068, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ShuiXian_Villa_Curtain001A.SM_ShuiXian_Villa_Curtain001A', 
            ['quality'] = 0, 
        },
        [4323069] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 1, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = Game.TableDataManager:GetLangStr('str_34226325949440'),
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104723968'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323069, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {200, 100, 100}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Bath001.SM_Bath001', 
            ['quality'] = 0, 
        },
        [4323070] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104792320'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323070, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Props007.SM_Furniture_Rich_Props007', 
            ['quality'] = 0, 
        },
        [4323071] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104792576'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323071, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_Closestool001.SM_Furniture_Rich_Closestool001', 
            ['quality'] = 0, 
        },
        [4323072] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104792832'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323072, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Furniture_Rich_PocketDesk001.SM_Furniture_Rich_PocketDesk001', 
            ['quality'] = 0, 
        },
        [4323073] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104793088'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323073, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_BlackThorns_Company_2F_Piano_A.SM_BlackThorns_Company_2F_Piano_A', 
            ['quality'] = 0, 
        },
        [4323074] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104793344'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323074, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeplate.SM_Coffeeplate', 
            ['quality'] = 0, 
        },
        [4323075] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104793600'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323075, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Coffeeshop_Showbox001.SM_Coffeeshop_Showbox001', 
            ['quality'] = 0, 
        },
        [4323076] = {
            ['BuyPrice'] = 1, 
            ['CanShow'] = false, 
            ['CollisionType'] = 0, 
            ['CombineComponent'] = '', 
            ['FrameValue'] = 0, 
            ['FurnitureDes'] = '', 
            ['FurnitureName'] = Game.TableDataManager:GetLangStr('str_34223104793856'),
            ['FurnitureScore'] = 0, 
            ['GroupId'] = 2205, 
            ['GroupSubId'] = 4502, 
            ['ID'] = 4323076, 
            ['MinMoveGrid'] = 100, 
            ['ModelPartComponent'] = {}, 
            ['SellPrice'] = 1, 
            ['TypeId'] = 1002, 
            ['UnlockItem'] = 0, 
            ['UnlockLevel'] = 1, 
            ['UnlockPrice'] = 0, 
            ['UnlockWorkshp'] = 0, 
            ['Volume'] = {}, 
            ['icon'] = '/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M', 
            ['model'] = '/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_Herbalpot_002.SM_Herbalpot_002', 
            ['quality'] = 0, 
        },
    }
}
return TopData