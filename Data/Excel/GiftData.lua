--
-- 表名: $GiftData_社交礼物.xlsx  页名：$GiftData_社交礼物
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364897792'),
			["itemId"] = 2013004,
			["type"] = 1,
			["grade"] = 1,
			["popularityValue"] = 1,
			["attraction"] = 20,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780816896'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049252352'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317687808'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323586123264'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[2] = {
			["ID"] = 2,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364898048'),
			["itemId"] = 2013005,
			["type"] = 1,
			["grade"] = 2,
			["popularityValue"] = 10,
			["attraction"] = 100,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780817152'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049252608'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323586123520'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[3] = {
			["ID"] = 3,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364898304'),
			["itemId"] = 2013006,
			["type"] = 1,
			["grade"] = 3,
			["popularityValue"] = 200,
			["attraction"] = 500,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 9,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780817408'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049252608'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323586123520'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323586123776'),
			["singleEffect"] = "",
			["systemBulletin"] = 1,
			["screenEffect"] = "",
		},
		[4] = {
			["ID"] = 4,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364898560'),
			["itemId"] = 2013007,
			["type"] = 2,
			["grade"] = 1,
			["popularityValue"] = 1,
			["attraction"] = 20,
			["hotValue"] = 100,
			["hotDuration"] = 600,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780817664'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253120'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[5] = {
			["ID"] = 5,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364898816'),
			["itemId"] = 2013008,
			["type"] = 2,
			["grade"] = 2,
			["popularityValue"] = 10,
			["attraction"] = 100,
			["hotValue"] = 500,
			["hotDuration"] = 1200,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780817152'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253376'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[6] = {
			["ID"] = 6,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364899072'),
			["itemId"] = 2013009,
			["type"] = 2,
			["grade"] = 3,
			["popularityValue"] = 200,
			["attraction"] = 500,
			["hotValue"] = 1314,
			["hotDuration"] = 1800,
			["number"] = 9,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780818176'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253632'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317689088'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317689088'),
			["singleEffect"] = "",
			["systemBulletin"] = 1,
			["screenEffect"] = "",
		},
		[7] = {
			["ID"] = 7,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364899328'),
			["itemId"] = 2013010,
			["type"] = 3,
			["grade"] = 1,
			["popularityValue"] = 1,
			["attraction"] = 20,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780818432'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253888'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317689344'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317689344'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[8] = {
			["ID"] = 8,
			["name"] = Game.TableDataManager:GetLangStr('str_41782515599362'),
			["itemId"] = 2013011,
			["type"] = 3,
			["grade"] = 2,
			["popularityValue"] = 10,
			["attraction"] = 100,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 520,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780818688'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253888'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317689600'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317689600'),
			["singleEffect"] = "",
			["systemBulletin"] = 0,
			["screenEffect"] = "",
		},
		[9] = {
			["ID"] = 9,
			["name"] = Game.TableDataManager:GetLangStr('str_26320364899840'),
			["itemId"] = 2013012,
			["type"] = 3,
			["grade"] = 3,
			["popularityValue"] = 200,
			["attraction"] = 500,
			["hotValue"] = 0,
			["hotDuration"] = 0,
			["number"] = 9,
			["adjective"] = Game.TableDataManager:GetLangStr('str_26322780818944'),
			["classifier"] = Game.TableDataManager:GetLangStr('str_26323049253888'),
			["information"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["specialMessage"] = Game.TableDataManager:GetLangStr('str_26323317688064'),
			["singleEffect"] = "",
			["systemBulletin"] = 1,
			["screenEffect"] = "",
		},
	},
}

return TopData
