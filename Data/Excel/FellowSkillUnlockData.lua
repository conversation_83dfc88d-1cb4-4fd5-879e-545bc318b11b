--
-- 表名: FellowSkillUnlockData(后处理)
--

local TopData = {
    newRoleClassSkillListMap = {
        [0] = {[86020010] = {['ID'] = 110055, }, [86020020] = {['ID'] = 110056, }, [86020030] = {['ID'] = 110045, }, [86020040] = {['ID'] = 110058, }, [86020050] = {['ID'] = 110047, }, [86020060] = {['ID'] = 110048, }, }, 
    },
    newSkillId2UniqueIdMap = {
        [86020010] = {['ID'] = 110055, }, 
        [86020020] = {['ID'] = 110056, }, 
        [86020030] = {['ID'] = 110045, }, 
        [86020040] = {['ID'] = 110058, }, 
        [86020050] = {['ID'] = 110047, }, 
        [86020060] = {['ID'] = 110048, }, 
    },
    data = {
        [110019] = {
            ['ClassID'] = 0, 
            ['ID'] = 110019, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020010, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110020] = {
            ['ClassID'] = 0, 
            ['ID'] = 110020, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020020, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110021] = {
            ['ClassID'] = 0, 
            ['ID'] = 110021, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020030, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110022] = {
            ['ClassID'] = 0, 
            ['ID'] = 110022, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020040, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110023] = {
            ['ClassID'] = 0, 
            ['ID'] = 110023, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020050, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110024] = {
            ['ClassID'] = 0, 
            ['ID'] = 110024, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020060, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110025] = {
            ['ClassID'] = 0, 
            ['ID'] = 110025, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020010, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110026] = {
            ['ClassID'] = 0, 
            ['ID'] = 110026, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020020, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110027] = {
            ['ClassID'] = 0, 
            ['ID'] = 110027, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020030, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110028] = {
            ['ClassID'] = 0, 
            ['ID'] = 110028, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020040, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110029] = {
            ['ClassID'] = 0, 
            ['ID'] = 110029, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020050, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110030] = {
            ['ClassID'] = 0, 
            ['ID'] = 110030, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020060, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110031] = {
            ['ClassID'] = 0, 
            ['ID'] = 110031, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020010, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110032] = {
            ['ClassID'] = 0, 
            ['ID'] = 110032, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020020, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110033] = {
            ['ClassID'] = 0, 
            ['ID'] = 110033, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020030, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110034] = {
            ['ClassID'] = 0, 
            ['ID'] = 110034, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020040, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110035] = {
            ['ClassID'] = 0, 
            ['ID'] = 110035, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020050, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110036] = {
            ['ClassID'] = 0, 
            ['ID'] = 110036, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020060, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110043] = {
            ['ClassID'] = 0, 
            ['ID'] = 110043, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020010, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110044] = {
            ['ClassID'] = 0, 
            ['ID'] = 110044, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020020, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110045] = {
            ['ClassID'] = 0, 
            ['ID'] = 110045, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020030, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110046] = {
            ['ClassID'] = 0, 
            ['ID'] = 110046, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020040, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110047] = {
            ['ClassID'] = 0, 
            ['ID'] = 110047, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020050, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110048] = {
            ['ClassID'] = 0, 
            ['ID'] = 110048, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020060, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110055] = {
            ['ClassID'] = 0, 
            ['ID'] = 110055, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020010, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110056] = {
            ['ClassID'] = 0, 
            ['ID'] = 110056, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020020, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110057] = {
            ['ClassID'] = 0, 
            ['ID'] = 110057, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020030, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110058] = {
            ['ClassID'] = 0, 
            ['ID'] = 110058, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020040, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110059] = {
            ['ClassID'] = 0, 
            ['ID'] = 110059, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020050, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
        [110060] = {
            ['ClassID'] = 0, 
            ['ID'] = 110060, 
            ['IsDisplay'] = true, 
            ['LvlUpCostTid'] = 14, 
            ['LvlUpDes'] = Game.TableDataManager:GetLangStr('str_47487842785280'),
            ['MaxLvl'] = 1, 
            ['MinPlayerLvlTid'] = 14, 
            ['ScoreTid'] = 315, 
            ['SkillDecorate'] = '', 
            ['SkillID'] = 86020060, 
            ['SkillType'] = 7, 
            ['UnlockStage'] = 0, 
        },
    }
}
return TopData