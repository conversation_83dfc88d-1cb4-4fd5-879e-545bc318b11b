--
-- 表名: $Item_道具总表_Design.xlsx  页名：$Item_RMB道具
--

local TopData = {
	data = {
		[2013001] = {
			["ID"] = 2013001,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594088448'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130959360'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_60818616014592'),
			["EnglishName"] = "",
			["quality"] = 4,
			["type"] = 2,
			["subType"] = 2001,
			["Order"] = 10101,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_Megaphone_Normal",
			["DropTemplateID"] = 4,
			["operationGroupType"] = 11,
			["lvReq"] = 1,
			["canReuseTimes"] = -1,
			["quickUseBtnName"] = Game.TableDataManager:GetLangStr('str_31337155134976'),
			["SystemActionEnum"] = {
                {
                    {
                        ["Caller"] = "",
                        ["FuncName"] = "JumpUI",
                        ["FuncParamInfos"] = {
                            ["UIJumpID"] = 1250041
                        },
                        ["FuncArgInfos"] = {1250041}
                    }
                }
            },
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[10] = 1}},
			["decomposeType"] = 102,
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013002] = {
			["ID"] = 2013002,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594088704'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130959616'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_60818616014592'),
			["EnglishName"] = "creator babble",
			["quality"] = 6,
			["type"] = 2,
			["subType"] = 2001,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_Megaphone_Affection",
			["DropTemplateID"] = 6,
			["operationGroupType"] = 11,
			["lvReq"] = 1,
			["canReuseTimes"] = -1,
			["quickUseBtnName"] = Game.TableDataManager:GetLangStr('str_31337155134976'),
			["SystemActionEnum"] = {
                {
                    {
                        ["Caller"] = "",
                        ["FuncName"] = "JumpUI",
                        ["FuncParamInfos"] = {
                            ["UIJumpID"] = 1250041
                        },
                        ["FuncArgInfos"] = {1250041}
                    }
                }
            },
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[10] = 1}},
			["decomposeType"] = 102,
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013003] = {
			["ID"] = 2013003,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594088960'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130959872'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_60818616014592'),
			["EnglishName"] = "universe babble",
			["quality"] = 6,
			["type"] = 2,
			["subType"] = 2001,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_Megaphone_Fight",
			["DropTemplateID"] = 6,
			["operationGroupType"] = 11,
			["lvReq"] = 1,
			["canReuseTimes"] = -1,
			["quickUseBtnName"] = Game.TableDataManager:GetLangStr('str_31337155134976'),
			["SystemActionEnum"] = {
                {
                    {
                        ["Caller"] = "",
                        ["FuncName"] = "JumpUI",
                        ["FuncParamInfos"] = {
                            ["UIJumpID"] = 1250041
                        },
                        ["FuncArgInfos"] = {1250041}
                    }
                }
            },
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[10] = 1}},
			["decomposeType"] = 102,
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013004] = {
			["ID"] = 2013004,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609083904'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130960128'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "Seville Chrysanthemum",
			["quality"] = 4,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10111,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Chrysanthemum",
			["DropTemplateID"] = 4,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 2001001,
			["AuctionMinPrice"] = 100,
			["AuctionMaxPrice"] = 1000,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013005] = {
			["ID"] = 2013005,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609084160'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130960384'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "Flowery Smile",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10601,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Tulip",
			["DropTemplateID"] = 5,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 2001001,
			["AuctionMinPrice"] = 1000,
			["AuctionMaxPrice"] = 10000,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013006] = {
			["ID"] = 2013006,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609084416'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130960640'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "Dazzling Swear",
			["quality"] = 6,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10124,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Rose",
			["DropTemplateID"] = 6,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 2001001,
			["AuctionMinPrice"] = 10000,
			["AuctionMaxPrice"] = 100000,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013007] = {
			["ID"] = 2013007,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609084672'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130960896'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "sweet iced tea",
			["quality"] = 4,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Tea",
			["DropTemplateID"] = 4,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013008] = {
			["ID"] = 2013008,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609084928'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130961152'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "Desi pie",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10602,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Pie",
			["DropTemplateID"] = 5,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013009] = {
			["ID"] = 2013009,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609085184'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130961408'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "Intis truffle",
			["quality"] = 6,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10125,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Truffle",
			["DropTemplateID"] = 6,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013010] = {
			["ID"] = 2013010,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609085440'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130961664'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "amulet",
			["quality"] = 4,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Amulet_",
			["DropTemplateID"] = 4,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013011] = {
			["ID"] = 2013011,
			["itemName"] = Game.TableDataManager:GetLangStr('str_40957881878530'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130961920'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "paper crane",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10603,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Papercrane",
			["DropTemplateID"] = 5,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013012] = {
			["ID"] = 2013012,
			["itemName"] = Game.TableDataManager:GetLangStr('str_25770609085952'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130962176'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_31475936266496'),
			["EnglishName"] = "EnglishName",
			["quality"] = 6,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10126,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Angel_",
			["DropTemplateID"] = 6,
			["AchievePath"] = "1012",
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2013013] = {
			["ID"] = 2013013,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594091520'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130962432'),
			["FashionID"] = 0,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_54632789584640'),
			["EnglishName"] = "",
			["quality"] = 4,
			["type"] = 2,
			["subType"] = 2007,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Friend_Icon_Chrysanthemum",
			["DropTemplateID"] = 4,
			["operationGroupType"] = 14,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 101,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010016] = {
			["ID"] = 2010016,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594091776'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130962688'),
			["FashionID"] = 0,
			["itemDes"] = Game.TableDataManager:GetLangStr('str_31475667833600'),
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2009,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_TreasureChest02",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010017] = {
			["ID"] = 2010017,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594092032'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130962944'),
			["FashionID"] = 0,
			["itemDes"] = Game.TableDataManager:GetLangStr('str_31475667833600'),
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2009,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_TreasureChest02",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010018] = {
			["ID"] = 2010018,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594092288'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130963200'),
			["FashionID"] = 0,
			["itemDes"] = Game.TableDataManager:GetLangStr('str_31475667833600'),
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2009,
			["Order"] = 10114,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_TreasureChest02",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010019] = {
			["ID"] = 2010019,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594092544'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130963456'),
			["FashionID"] = 0,
			["itemDes"] = Game.TableDataManager:GetLangStr('str_31475667833600'),
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2004,
			["Order"] = 10115,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_TreasureChest02",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {
                {"DropAll", {
                        ["item"] = {2001000},
                        ["num"] = {6000},
                        ["bind"] = {1}
                    }}
            },
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010020] = {
			["ID"] = 2010020,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594092800'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130963712'),
			["FashionID"] = 0,
			["itemDes"] = Game.TableDataManager:GetLangStr('str_31475667833600'),
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 5,
			["type"] = 2,
			["subType"] = 2004,
			["Order"] = 10116,
			["invId"] = 2,
			["icon"] = "UI_Item_Icon_TreasureChest02",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {
                {"DropAll", {
                        ["item"] = {2001001},
                        ["num"] = {60000},
                        ["bind"] = {1}
                    }}
            },
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2012001] = {
			["ID"] = 2012001,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21578989443072'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130963968'),
			["FashionID"] = 4250001,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 7002,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010021] = {
			["ID"] = 2010021,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818951168'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818951168'),
			["FashionID"] = 4220155,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2006,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010022] = {
			["ID"] = 2010022,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818951424'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818951424'),
			["FashionID"] = 4220158,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2005,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010023] = {
			["ID"] = 2010023,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818949888'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818949888'),
			["FashionID"] = 4220150,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10112,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010024] = {
			["ID"] = 2010024,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594094080'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130964992'),
			["FashionID"] = 4220230,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010025] = {
			["ID"] = 2010025,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594094336'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130965248'),
			["FashionID"] = 4220235,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2004,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010026] = {
			["ID"] = 2010026,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594094592'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130965504'),
			["FashionID"] = 4220237,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010027] = {
			["ID"] = 2010027,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594094848'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31475130965760'),
			["FashionID"] = 4220242,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2004,
			["Order"] = 10113,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010029] = {
			["ID"] = 2010029,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594095360'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31474594095360'),
			["FashionID"] = 4220080,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10115,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010030] = {
			["ID"] = 2010030,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594095616'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31474594095616'),
			["FashionID"] = 4220088,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10116,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010034] = {
			["ID"] = 2010034,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594096640'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31474594096640'),
			["FashionID"] = 4220170,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10118,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010035] = {
			["ID"] = 2010035,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818954752'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818954752'),
			["FashionID"] = 4220175,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2003,
			["Order"] = 10118,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010036] = {
			["ID"] = 2010036,
			["itemName"] = Game.TableDataManager:GetLangStr('str_31474594097152'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_31474594097152'),
			["FashionID"] = 4220180,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10119,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure00",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010037] = {
			["ID"] = 2010037,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818956288'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818956288'),
			["FashionID"] = 4220185,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2003,
			["Order"] = 10119,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010038] = {
			["ID"] = 2010038,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818972672'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818972672'),
			["FashionID"] = 4220270,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10120,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010039] = {
			["ID"] = 2010039,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818973952'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818973952'),
			["FashionID"] = 4220275,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2006,
			["Order"] = 10120,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010040] = {
			["ID"] = 2010040,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818974208'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818974208'),
			["FashionID"] = 4220276,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2005,
			["Order"] = 10120,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010041] = {
			["ID"] = 2010041,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818974464'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818974464'),
			["FashionID"] = 4220277,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2002,
			["Order"] = 10120,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010042] = {
			["ID"] = 2010042,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818974976'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818974976'),
			["FashionID"] = 4220278,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010043] = {
			["ID"] = 2010043,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818976256'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818976256'),
			["FashionID"] = 4220283,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2006,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010044] = {
			["ID"] = 2010044,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818976512'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818976512'),
			["FashionID"] = 4220284,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2005,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010045] = {
			["ID"] = 2010045,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818976768'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818976768'),
			["FashionID"] = 4220285,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2002,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010046] = {
			["ID"] = 2010046,
			["itemName"] = Game.TableDataManager:GetLangStr('str_38071663865856'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_38071663865856'),
			["FashionID"] = 4220290,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010047] = {
			["ID"] = 2010047,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818978304'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818978304'),
			["FashionID"] = 4220294,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2006,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010048] = {
			["ID"] = 2010048,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818978560'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818978560'),
			["FashionID"] = 4220295,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2005,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010049] = {
			["ID"] = 2010049,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818978048'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818978048'),
			["FashionID"] = 4220296,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2008,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010060] = {
			["ID"] = 2010060,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818979072'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818979072'),
			["FashionID"] = 4220300,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 1001,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010061] = {
			["ID"] = 2010061,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818980352'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818980352'),
			["FashionID"] = 4220305,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2001,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010062] = {
			["ID"] = 2010062,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818980608'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818980608'),
			["FashionID"] = 4220306,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2005,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
		[2010063] = {
			["ID"] = 2010063,
			["itemName"] = Game.TableDataManager:GetLangStr('str_21441818980864'),
			["funcRep"] = Game.TableDataManager:GetLangStr('str_21441818980864'),
			["FashionID"] = 4220307,
			["funcSimpleRep"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["EnglishName"] = "",
			["quality"] = 3,
			["type"] = 7,
			["subType"] = 2002,
			["Order"] = 10121,
			["invId"] = 2,
			["icon"] = "UI_Fashion_Img_Figure01",
			["DropTemplateID"] = 99,
			["operationGroupType"] = 1,
			["lvReq"] = 1,
			["quickUseBtnName"] = "",
			["autoBombBox"] = 1,
			["DropActionEnum"] = {},
			["bindType"] = 1,
			["holdMax"] = 99999,
			["discardAfterFull"] = false,
			["mwrap"] = 9999,
			["resolvePoint"] = false,
			["decomposeItem"] = {[2001000] = {[1] = 1}},
			["rarely"] = false,
			["TeamRewardAllot"] = 1,
			["AuctionMoneyType"] = 0,
			["AuctionMinPrice"] = 0,
			["AuctionMaxPrice"] = 0,
			["TaskIDLimit"] = {},
			["DestroyLimit"] = {},
			["IsCheckReward"] = false,
			["hrefType"] = 0,
			["loopOrBatch"] = false,
			["lvPreview"] = 0,
			["freshFlag"] = false,
			["cdServerGroupId"] = 0,
			["useAwakenId"] = "",
			["preciousInstanceCriterion"] = 0,
			["shopApproachJumpItem"] = 0,
			["shopType"] = 0,
			["shopItemIndex"] = 0,
			["skillTypeReq"] = 0,
			["skillLvReq"] = 0,
			["socialLvReq"] = 0,
			["socProfessionReq"] = 0,
			["socProfessionLvReq"] = 0,
			["socSkillIdReq"] = 0,
			["socSkillLvReq"] = 0,
			["itemValue"] = 0,
			["itemValue1"] = 0,
		},
	},
}

return TopData
