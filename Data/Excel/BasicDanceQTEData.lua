--
-- 表名: $BasicDance_基础跳舞玩法.xlsx  页名：$QTE_QTE判定
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["Level"] = Game.TableDataManager:GetLangStr('str_2680864900608'),
			["Const"] = "PERFECT",
			["Determine"] = 80,
			["Score"] = 1000,
			["Accuracy"] = 1,
		},
		[2] = {
			["Id"] = 2,
			["Level"] = Game.TableDataManager:GetLangStr('str_2680864900864'),
			["Const"] = "GREAT",
			["Determine"] = 160,
			["Score"] = 800,
			["Accuracy"] = 0.8,
		},
		[3] = {
			["Id"] = 3,
			["Level"] = Game.TableDataManager:GetLangStr('str_2680864901120'),
			["Const"] = "GOOD",
			["Determine"] = 240,
			["Score"] = 700,
			["Accuracy"] = 0.7,
		},
		[4] = {
			["Id"] = 4,
			["Level"] = Game.TableDataManager:GetLangStr('str_2680864901376'),
			["Const"] = "COOL",
			["Determine"] = 320,
			["Score"] = 500,
			["Accuracy"] = 0.5,
		},
		[5] = {
			["Id"] = 5,
			["Level"] = Game.TableDataManager:GetLangStr('str_2680864901632'),
			["Const"] = "MISS",
			["Determine"] = 320,
			["Score"] = 0,
			["Accuracy"] = 0,
		},
	},
}

return TopData
