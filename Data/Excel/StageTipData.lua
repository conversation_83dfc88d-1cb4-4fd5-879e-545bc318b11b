--
-- 表名: $Stage_副本阶段.xlsx  页名：$Tip_阶段提示
--

local TopData = {
	data = {
		[1] = {
			["StageTipId"] = 1,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752975872'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289846784'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558282240'),
		},
		[2] = {
			["StageTipId"] = 2,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752976128'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289847040'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558282496'),
		},
		[3] = {
			["StageTipId"] = 3,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752976384'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289847296'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558282752'),
		},
		[4] = {
			["StageTipId"] = 4,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752976640'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289847552'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558283008'),
		},
		[5] = {
			["StageTipId"] = 5,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752976896'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289847808'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558283264'),
		},
		[6] = {
			["StageTipId"] = 6,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752977152'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289848064'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558283520'),
		},
		[7] = {
			["StageTipId"] = 7,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_60680371702272'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289848320'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558283776'),
		},
		[8] = {
			["StageTipId"] = 8,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752977664'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289848576'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558284032'),
		},
		[9] = {
			["StageTipId"] = 9,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752977920'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289848832'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558284288'),
		},
		[10] = {
			["StageTipId"] = 10,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752978176'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289849088'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558284544'),
		},
		[11] = {
			["StageTipId"] = 11,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752978432'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289849344'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558284800'),
		},
		[12] = {
			["StageTipId"] = 12,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752978688'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289849600'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558285056'),
		},
		[13] = {
			["StageTipId"] = 13,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752978944'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289849856'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558285312'),
		},
		[14] = {
			["StageTipId"] = 14,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752979200'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289850112'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558285568'),
		},
		[15] = {
			["StageTipId"] = 15,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752979456'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289850368'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558285824'),
		},
		[16] = {
			["StageTipId"] = 16,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752979712'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289850624'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558286080'),
		},
		[17] = {
			["StageTipId"] = 17,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752979968'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289850880'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558286336'),
		},
		[18] = {
			["StageTipId"] = 18,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752980224'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289851136'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558286592'),
		},
		[19] = {
			["StageTipId"] = 19,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752980480'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289851392'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558286848'),
		},
		[20] = {
			["StageTipId"] = 20,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752980736'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289851648'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558287104'),
		},
		[21] = {
			["StageTipId"] = 21,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752980992'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289851904'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558287360'),
		},
		[22] = {
			["StageTipId"] = 22,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752981248'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289852160'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558287616'),
		},
		[23] = {
			["StageTipId"] = 23,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752981504'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289852416'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558287872'),
		},
		[24] = {
			["StageTipId"] = 24,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752981760'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289852672'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558288128'),
		},
		[25] = {
			["StageTipId"] = 25,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752982016'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289852928'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558288128'),
		},
		[26] = {
			["StageTipId"] = 26,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752982272'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289853184'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558288640'),
		},
		[27] = {
			["StageTipId"] = 27,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752982528'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289853440'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558288640'),
		},
		[28] = {
			["StageTipId"] = 28,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752982784'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289853696'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558289152'),
		},
		[29] = {
			["StageTipId"] = 29,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752983040'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289853952'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558282496'),
		},
		[30] = {
			["StageTipId"] = 30,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752983296'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289854208'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558289664'),
		},
		[31] = {
			["StageTipId"] = 31,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752983552'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289854464'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558289920'),
		},
		[32] = {
			["StageTipId"] = 32,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752983808'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289854720'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558290176'),
		},
		[33] = {
			["StageTipId"] = 33,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752984064'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289854976'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558290432'),
		},
		[34] = {
			["StageTipId"] = 34,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752984320'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289855232'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558290688'),
		},
		[35] = {
			["StageTipId"] = 35,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752984576'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289855488'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558290944'),
		},
		[36] = {
			["StageTipId"] = 36,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752984832'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289855744'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558291200'),
		},
		[37] = {
			["StageTipId"] = 37,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752985088'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289856000'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558291456'),
		},
		[38] = {
			["StageTipId"] = 38,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752985344'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289856256'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558291712'),
		},
		[39] = {
			["StageTipId"] = 39,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752985600'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289856512'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558291968'),
		},
		[40] = {
			["StageTipId"] = 40,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752985856'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289856768'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558292224'),
		},
		[41] = {
			["StageTipId"] = 41,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752986112'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857024'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558292480'),
		},
		[42] = {
			["StageTipId"] = 42,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752986368'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857280'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558292736'),
		},
		[43] = {
			["StageTipId"] = 43,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752986624'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857536'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558292992'),
		},
		[44] = {
			["StageTipId"] = 44,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752986880'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[45] = {
			["StageTipId"] = 45,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752987136'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[46] = {
			["StageTipId"] = 46,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752987392'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[47] = {
			["StageTipId"] = 47,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752987648'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[48] = {
			["StageTipId"] = 48,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752987904'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[49] = {
			["StageTipId"] = 49,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752988160'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[50] = {
			["StageTipId"] = 50,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752988416'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[51] = {
			["StageTipId"] = 51,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752988672'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[52] = {
			["StageTipId"] = 52,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752988928'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[53] = {
			["StageTipId"] = 53,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752989184'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[54] = {
			["StageTipId"] = 54,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752989440'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[55] = {
			["StageTipId"] = 55,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752989696'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[56] = {
			["StageTipId"] = 56,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752989952'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289860864'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558296320'),
		},
		[57] = {
			["StageTipId"] = 57,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752990208'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289861120'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558296576'),
		},
		[58] = {
			["StageTipId"] = 58,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752990464'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289861376'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558296832'),
		},
		[59] = {
			["StageTipId"] = 59,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752990720'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289861632'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558297088'),
		},
		[60] = {
			["StageTipId"] = 60,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752990976'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289861888'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558297344'),
		},
		[61] = {
			["StageTipId"] = 61,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752991232'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[62] = {
			["StageTipId"] = 62,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_9484093116672'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289862400'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558297856'),
		},
		[63] = {
			["StageTipId"] = 63,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752991744'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289862656'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558298112'),
		},
		[64] = {
			["StageTipId"] = 64,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752992000'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289862912'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558298368'),
		},
		[65] = {
			["StageTipId"] = 65,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752992256'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289863168'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558298624'),
		},
		[66] = {
			["StageTipId"] = 66,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752992512'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289863424'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558298880'),
		},
		[67] = {
			["StageTipId"] = 67,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752992768'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289863680'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558299136'),
		},
		[68] = {
			["StageTipId"] = 68,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752993024'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[69] = {
			["StageTipId"] = 69,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752993280'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[70] = {
			["StageTipId"] = 70,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752993536'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[71] = {
			["StageTipId"] = 71,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752993792'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[72] = {
			["StageTipId"] = 72,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752994048'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
		[73] = {
			["StageTipId"] = 73,
			["StageTipsName"] = Game.TableDataManager:GetLangStr('str_54151752994304'),
			["StageTipsIcon"] = "/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_Boss01.UI_Dungeon_Img_Boss01",
			["StageTipDes"] = Game.TableDataManager:GetLangStr('str_54152289857792'),
			["StageTipResolve"] = Game.TableDataManager:GetLangStr('str_54152558293248'),
		},
	},
}

return TopData
