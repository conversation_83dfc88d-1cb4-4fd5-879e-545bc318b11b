--
-- 表名: $Achievement_成就表.xlsx  页名：$Setting_设置
--

local TopData = {
	data = {
		["ACHIEVEMENT_TEXT_TITLE"] = Game.TableDataManager:GetLangStr('str_35666213736704'),
		["ACHIEVEMENT_TEXT_LEVEL"] = Game.TableDataManager:GetLangStr('str_54632789327360'),
		["ACHIEVEMENT_TEXT_GET_ALL_REWARD"] = Game.TableDataManager:GetLangStr('str_56007178869504'),
		["ACHIEVEMENT_TEXT_COMPLETE_INFO"] = Game.TableDataManager:GetLangStr('str_756987988224'),
		["ACHIEVEMENT_TEXT_FILTER_ALL"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
		["ACHIEVEMENT_TEXT_FILTER_NOT_FINISH"] = Game.TableDataManager:GetLangStr('str_54632789771264'),
		["ACHIEVEMENT_TEXT_FILTER_FINISHED"] = Game.TableDataManager:GetLangStr('str_54632789674496'),
		["ACHIEVEMENT_TEXT_REMINDER_TITLE"] = Game.TableDataManager:GetLangStr('str_756987989248'),
		["ACHIEVEMENT_TEXT_SEARCH"] = Game.TableDataManager:GetLangStr('str_6735582464256'),
		["ACHIEVEMENT_REDPOINT_THRESHOLD"] = 2,
		["ACHIEVEMENT_SEARCH_MAX_WORD_COUNT"] = 10,
	},
}

return TopData
