--
-- 表名: FightActionData(后处理)
--

local TopData = {
    actionName2ActionIdMap = {
        ['AddHP_DuXin'] = 1720010, 
        ['AddHP_HitRecover'] = 1720005, 
        ['AddHP_OverHp'] = 1720008, 
        ['AddHP_Per_Skill'] = 1720003, 
        ['AddHP_Player'] = 1720000, 
        ['AddHP_Skill_TimeToHeal'] = 1720009, 
        ['AddHP_Warrior'] = 1720001, 
        ['AddHP_per'] = 1720002, 
        ['AddHp_Potion'] = 1720004, 
        ['AtkNew_E'] = 1710004, 
        ['BossUlorus_Counter'] = 1710015, 
        ['ChenXiYaoGuang_E'] = 1710009, 
        ['ConsumeHp_Mag'] = 1700001, 
        ['ConsumeHp_Phy'] = 1700000, 
        ['DeductHpForA'] = 1700101, 
        ['DeductHpForD'] = 1700100, 
        ['HealHpForA'] = 1700103, 
        ['HealHpForD'] = 1700102, 
        ['HengSao_Heal'] = 1720006, 
        ['LiMingKaiJia_AddHP'] = 1720007, 
        ['MagAtk'] = 1710003, 
        ['MagAtk_JiZhi'] = 1710006, 
        ['MagAtk_Player'] = 1710001, 
        ['MagAtk_Vision'] = 1710013, 
        ['PhyAtk'] = 1710002, 
        ['PhyAtk_JiZhi'] = 1710005, 
        ['PhyAtk_Player'] = 1710000, 
        ['PhyAtk_Warrior'] = 1710008, 
        ['PhyAtk_Warrior2'] = 1710007, 
        ['PhyAtk_per'] = 1710010, 
        ['Player_PhyAtk_Counter'] = 1710014, 
        ['ReduceHP_N'] = 1710011, 
        ['ReduceHP_per'] = 1710012, 
        ['StealHp'] = 1700104, 
    },
    elementReplacementActionMap = {
    },
    data = {
        [1700000] = {
            ['ActionName'] = 'ConsumeHp_Phy', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = '', 
            ['Formula'] = 'local hurt = $1\nlocal ShieldDelta = 0\nlocal HpDelta = 0\nShieldDelta, HpDelta, hurt = PhyShieldCalc(a, d, FDIn, FDOut, true, true, hurt)\n\nreturn Floor(HpDelta + ShieldDelta)', 
            ['ID'] = 1700000, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700001] = {
            ['ActionName'] = 'ConsumeHp_Mag', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = '', 
            ['Formula'] = 'local hurt = $1\nlocal ShieldDelta = 0\nlocal HpDelta = 0\nShieldDelta, HpDelta, hurt = MagShieldCalc(a, d, FDIn, FDOut, true, true, hurt)\n\nreturn Floor(HpDelta + ShieldDelta)', 
            ['ID'] = 1700001, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700100] = {
            ['ActionName'] = 'DeductHpForD', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = 'local hurt = $1\nd.Hp = d.Hp - hurt', 
            ['Formula'] = 'local hurt = $1\n\nDeductHpCalc(a, d, FDIn, FDOut, true, hurt)', 
            ['ID'] = 1700100, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700101] = {
            ['ActionName'] = 'DeductHpForA', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = 'local hurt = $1\na.Hp = a.Hp - hurt', 
            ['Formula'] = 'local hurt = $1\n\nDeductHpCalc(a, d, FDIn, FDOut, false, hurt)', 
            ['ID'] = 1700101, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700102] = {
            ['ActionName'] = 'HealHpForD', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = 'local healValue = $1\nlocal ignoreHealLimit = $2\n\nif d.IsHealLimit >= 1 and (not ignoreHealLimit) then\n    FDOut.IsImmune = true\nelse\n    d.Hp = d.Hp + healValue\nend', 
            ['Formula'] = 'local healValue = $1\nlocal ignoreHealLimit = $2\n\nHealHpCalc(a, d, FDIn, FDOut, true, healValue, ignoreHealLimit)', 
            ['ID'] = 1700102, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700103] = {
            ['ActionName'] = 'HealHpForA', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = 'local healValue = $1\nlocal ignoreHealLimit = $2\n\nif a.IsHealLimit >= 1 and (not ignoreHealLimit) then\n    FDOut.IsImmune = true\nelse\n    a.Hp = a.Hp + healValue\nend', 
            ['Formula'] = 'local healValue = $1\nlocal ignoreHealLimit = $2\n\nHealHpCalc(a, d, FDIn, FDOut, false, healValue, ignoreHealLimit)', 
            ['ID'] = 1700103, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1700104] = {
            ['ActionName'] = 'StealHp', 
            ['AttackType'] = {['Inner'] = 1, }, 
            ['DescFormula'] = 'local hurt = $1\nlocal transferRate = $2\n\nd.Hp = d.Hp - hurt\na.Hp = a.Hp + hurt * transferRate', 
            ['Formula'] = 'local hurt = $1\nlocal transferRate = $2\n\nStealHpCalc(a, d, FDIn, FDOut, hurt,  transferRate)', 
            ['ID'] = 1700104, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1710000] = {
            ['ActionName'] = 'PhyAtk_Player', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 + a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = 'local pAtk =$1* (GetpAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef()\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , pAtk*0.05*GetRaceDeltaRate(a, d) , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710000, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,100);#float(0,100);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710001] = {
            ['ActionName'] = 'MagAtk_Player', 
            ['AttackType'] = {['Mag'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 + a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = 'local mAtk =$1* (GetmAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetmDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef()\nlocal hurt = mAtk*getRaceAdjust_P() * GetmHurtMultiNew() * GetmBlockP()  *  GetmCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , mAtk*0.05*GetRaceDeltaRate(a, d) , 1)\n\n ConsumeHp_Mag(hurt)', 
            ['ID'] = 1710001, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,100);#float(0,100);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710002] = {
            ['ActionName'] = 'PhyAtk', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local pAtk = 1\npAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) * 0.5 * $1\nreturn pAtk', 
            ['Formula'] = 'local pAtk =$1* (GetpAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\n\nFDOut.TotalDamage= hurt;\n\nhurt = Max(hurt , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710002, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,100)', 
            ['PhyActionName'] = '', 
        },
        [1710003] = {
            ['ActionName'] = 'MagAtk', 
            ['AttackType'] = {['Mag'] = 1, }, 
            ['DescFormula'] = 'local mAtk = 1\nmAtk = (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) * 0.5 * $1\nreturn mAtk', 
            ['Formula'] = 'local mAtk =$1* (GetmAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetmDefPNew()\nlocal hurt = mAtk*getRaceAdjust_P() * GetmHurtMultiNew() * GetmBlockP()  *  GetmCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\n\nFDOut.TotalDamage= hurt;\n\nhurt = Max(hurt , 1)\n\nConsumeHp_Mag(hurt)', 
            ['ID'] = 1710003, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,100)', 
            ['PhyActionName'] = '', 
        },
        [1710004] = {
            ['ActionName'] = 'AtkNew_E', 
            ['AttackType'] = {}, 
            ['DescFormula'] = 'local mAtk = (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) *0.5\nlocal pAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) *0.5\nlocal Atk = Max(mAtk, pAtk) *$1 +  a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = '', 
            ['ID'] = 1710004, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = 'MagAtk_Player', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#float(0,80000)', 
            ['PhyActionName'] = 'PhyAtk_Player', 
        },
        [1710005] = {
            ['ActionName'] = 'PhyAtk_JiZhi', 
            ['AttackType'] = {['NoCheckBlock'] = 1, ['NoCheckCrit'] = 1, ['Phy'] = 1, }, 
            ['DescFormula'] = 'local pAtk = 1\npAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) * 0.5 * $1\nreturn pAtk', 
            ['Formula'] = 'local pAtk =$1* (GetpAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew()  *  GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\n\nFDOut.TotalDamage= hurt;\n\nhurt = Max(hurt , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710005, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,200)', 
            ['PhyActionName'] = '', 
        },
        [1710006] = {
            ['ActionName'] = 'MagAtk_JiZhi', 
            ['AttackType'] = {['Mag'] = 1, ['NoCheckBlock'] = 1, ['NoCheckCrit'] = 1, }, 
            ['DescFormula'] = 'local mAtk = 1\nmAtk = (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) * 0.5 * $1\nreturn mAtk', 
            ['Formula'] = 'local mAtk =$1* (GetmAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetmDefPNew()\nlocal hurt = mAtk*getRaceAdjust_P() * GetmHurtMultiNew()  * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\n\nFDOut.TotalDamage= hurt;\n\nhurt = Max(hurt , 1)\n\n ConsumeHp_Mag(hurt)', 
            ['ID'] = 1710006, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,200)', 
            ['PhyActionName'] = '', 
        },
        [1710007] = {
            ['ActionName'] = 'PhyAtk_Warrior2', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 + a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = 'local pAtk =($1* (GetpAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef())\nif GetUseProfessionProp(a) == 3 then\n    pAtk = 2*pAtk\nend\nif GetUseProfessionProp(a) == 2 then\n    pAtk = 1.5*pAtk\nend\nif GetUseProfessionProp(a) == 1 then\n    pAtk = 1.2*pAtk\nend\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710007, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710008] = {
            ['ActionName'] = 'PhyAtk_Warrior', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 + a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = 'local pAtk =($1* (GetpAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef())*(1+GetUseProfessionProp(a)/5)\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710008, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710009] = {
            ['ActionName'] = 'ChenXiYaoGuang_E', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = ((Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 + Min(a.MaxHp / 20, (a.pAtkMin + a.pAtkMax) * 0.3)) * $1 +a.AdditionalAtk * $2 + $3\nreturn Atk', 
            ['Formula'] = 'local pAtk =$1* (GetpAtk()+Min(a.MaxHp / 20, (a.pAtkMin + a.pAtkMax) * 0.3)+GetDeltaShield()+GetRaceAdjust_N()) *GetpDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef()\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , pAtk*0.05*GetRaceDeltaRate(a, d) , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710009, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,1);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710010] = {
            ['ActionName'] = 'PhyAtk_per', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local pAtk = d.MaxHp * $1 +$2\nlocal hurt = pAtk*getRaceAdjust_P() * GetpHurtMultiNew() * GetpBlockP()  *  GetpCritP() * GetSkillHurtMulti() * DamageCoefficient() + a.DeltaHurt_N - d.DeltaBeHurted_N;\n\nFDOut.TotalDamage= hurt;\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710010, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710011] = {
            ['ActionName'] = 'ReduceHP_N', 
            ['AttackType'] = {['NoCheckBlock'] = 1, ['NoCheckCrit'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local hurt = $1;\nFDOut.TotalDamage= hurt;\n\nDeductHpForD(hurt)', 
            ['ID'] = 1710011, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000)', 
            ['PhyActionName'] = '', 
        },
        [1710012] = {
            ['ActionName'] = 'ReduceHP_per', 
            ['AttackType'] = {['NoCheckBlock'] = 1, ['NoCheckCrit'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local hurt = d.MaxHp* $1;\nFDOut.TotalDamage= hurt;\nhurt = Max(hurt , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710012, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000)', 
            ['PhyActionName'] = '', 
        },
        [1710013] = {
            ['ActionName'] = 'MagAtk_Vision', 
            ['AttackType'] = {['Mag'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * $1 + $2 + $3\nreturn Atk', 
            ['Formula'] = 'local BuffIns = GetOneRunningBuffIns(d, 8100204)\nlocal BuffInsSelf = GetOneRunningBuffIns(a, 8103002)\nlocal coefficient = 1\nif BuffIns and BuffInsSelf then\n  coefficient = 1.1\nend\n\nlocal mAtk =($1* (GetmAtk()+GetDeltaShield()+GetRaceAdjust_N()) *GetmDefPNew()+$2*a.AdditionalAtk*GetAddDef()+$3*GetSkillDef())\nlocal hurt = mAtk*getRaceAdjust_P() * GetmHurtMultiNew() * GetmBlockP()  *  GetmCritP() * GetSkillHurtMulti() * DamageCoefficient()*coefficient + a.DeltaHurt_N - d.DeltaBeHurted_N;\nFDOut.TotalDamage= hurt;\nFDOut.AdditionalAtk=a.AdditionalAtk\nFDOut.$2=$2\nFDOut.ADDhurt=$2*a.AdditionalAtk*GetAddDef()\nFDOut.Skillhurt=$3*GetSkillDef()\nhurt = Max(hurt , mAtk*0.05*GetRaceDeltaRate(a, d) , 1)\n\n ConsumeHp_Mag(hurt)', 
            ['ID'] = 1710013, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,1);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1710014] = {
            ['ActionName'] = 'Player_PhyAtk_Counter', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 +$2 + $3\nreturn Atk', 
            ['Formula'] = 'local BasicHurt = $1 * FDIn.ExtraDamage\nlocal Atk = (a.pDef + a.mDef) / 2 * $2 * GetpBlockP()\nlocal hurt = Min(BasicHurt , (Atk + GetRaceAdjust_N()) * getRaceAdjust_P() * GetpDefPNew() * GetpCritP() * GetpHurtMultiNew() * FDIn.DamageRate + a.DeltaHurt_N - d.DeltaBeHurted_N);\nFDOut.TotalDamage= hurt;\nhurt = Max(hurt , Atk*0.05*GetRaceDeltaRate(a, d) , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710014, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10);#float(0,10)', 
            ['PhyActionName'] = '', 
        },
        [1710015] = {
            ['ActionName'] = 'BossUlorus_Counter', 
            ['AttackType'] = {['Phy'] = 1, }, 
            ['DescFormula'] = 'local Atk = 1\nAtk = (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * $1 + $2 + $3\nreturn Atk', 
            ['Formula'] = 'local BasicHurt = $1 * FDIn.ExtraDamage\nlocal Atk = (a.pDef + a.mDef) / 2 * $2 * GetpBlockP()\nlocal hurt = Min(BasicHurt , (Atk + GetRaceAdjust_N()) * getRaceAdjust_P() * GetpDefPNew() * GetpCritP() * GetpHurtMultiNew() * FDIn.DamageRate + a.DeltaHurt_N - d.DeltaBeHurted_N);\nFDOut.TotalDamage= hurt;\nhurt = Max(hurt , Atk*0.05*GetRaceDeltaRate(a, d) , 1)\n\nConsumeHp_Phy(hurt);', 
            ['ID'] = 1710015, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10);#float(0,10)', 
            ['PhyActionName'] = '', 
        },
        [1720000] = {
            ['ActionName'] = 'AddHP_Player', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'local mAtk = 1\nmAtk = $1*((Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) *0.5 * $2 + $3)\nreturn mAtk', 
            ['Formula'] = 'local Atk =$1* (GetAtk()*$2+a.DeltaHeal_N+$3)*(1+a.DeltaHeal_P);\nlocal recover = max(Atk*(1+d.DeltaBeHealed_P),0) * GethCritP();\nFDOut.TotalDamage= recover;\nrecover = Max(recover,1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720000, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1720001] = {
            ['ActionName'] = 'AddHP_Warrior', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'local mAtk = 1\nmAtk = $1*((Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) *0.5 * $2 + $3)\nreturn mAtk', 
            ['Formula'] = 'local Atk =$1* (GetAtk()*$2+a.DeltaHeal_N+$3)*(1+a.DeltaHeal_P);\nif GetUseProfessionProp(a) == 3 then\n    Atk = 2*Atk\nend\nif GetUseProfessionProp(a) == 2 then\n    Atk = 1.5*Atk\nend\nif GetUseProfessionProp(a) == 1 then\n    Atk = 1.2*Atk\nend\nlocal recover = max(Atk*(1+d.DeltaBeHealed_P),0) * GethCritP();\nFDOut.TotalDamage= recover;\nrecover = Max(recover,1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720001, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#float(0,80000)', 
            ['PhyActionName'] = '', 
        },
        [1720002] = {
            ['ActionName'] = 'AddHP_per', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local recover = d.MaxHp * $1;\nFDOut.TotalDamage= recover;\nrecover = Max(recover , 1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720002, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000)', 
            ['PhyActionName'] = '', 
        },
        [1720003] = {
            ['ActionName'] = 'AddHP_Per_Skill', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local Heal = d.MaxHp * $1\nlocal recover = (Heal+a.DeltaHeal_N)*(1+a.DeltaHeal_P);\nrecover = max((recover + d.DeltaBeHealed_N)*(1+d.DeltaBeHealed_P),0) * GethCritP();\nrecover = min(recover, a.MaxHp * 0.75);\nFDOut.TotalDamage= recover;\nrecover = Max(recover,1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720003, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000)', 
            ['PhyActionName'] = '', 
        },
        [1720004] = {
            ['ActionName'] = 'AddHp_Potion', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local hurt = $1\nFDOut.TotalDamage= hurt;\nHealHpForD(hurt, true)', 
            ['ID'] = 1720004, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1720005] = {
            ['ActionName'] = 'AddHP_HitRecover', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local recover = FDIn.ExtraDamage * $1\nlocal recover = (recover+a.DeltaHeal_N)*(1+a.DeltaHeal_P)\nrecover = max((recover + d.DeltaBeHealed_N)*(1+d.DeltaBeHealed_P),0) * GethCritP()\nFDOut.TotalDamage= recover\nrecover = Max(recover,1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720005, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,1)', 
            ['PhyActionName'] = '', 
        },
        [1720006] = {
            ['ActionName'] = 'HengSao_Heal', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'return $1', 
            ['Formula'] = 'local recover = min(max(a.MaxHp - a.Hp, 0) * $1, a.MaxHp * 0.25);\nFDOut.TotalDamage= recover;\nrecover = Max(recover, 1)\n\nHealHpForD(recover, false)', 
            ['ID'] = 1720006, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000)', 
            ['PhyActionName'] = '', 
        },
        [1720007] = {
            ['ActionName'] = 'LiMingKaiJia_AddHP', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = '', 
            ['Formula'] = 'local BuffLv = FDIn.InsLevel\n\nlocal hurt = d.MaxHp_N * Max(0, LiMingKaiJia_2(BuffLv) - LiMingKaiJia_1(BuffLv))\nFDOut.TotalDamage= hurt;\nhurt = Max(hurt , 1)\nHealHpForD(hurt, true)', 
            ['ID'] = 1720007, 
            ['IsFollowUserDamageType'] = false, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1720008] = {
            ['ActionName'] = 'AddHP_OverHp', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = '', 
            ['Formula'] = 'OverHpCalc(a, d, FDIn, FDOut, false)', 
            ['ID'] = 1720008, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '', 
            ['PhyActionName'] = '', 
        },
        [1720009] = {
            ['ActionName'] = 'AddHP_Skill_TimeToHeal', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'local mAtk = 1\nmAtk = (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) *0.5 * $1 + $2\nreturn mAtk', 
            ['Formula'] = 'local Atk = (GetAtk()*$1+$2+a.DeltaHeal_N)*(1+a.DeltaHeal_P);\nlocal resLifeTime = GetUnitResLifeTime(a, $3, $4)/1000;\nlocal recover = max((Atk + d.DeltaBeHealed_N)*resLifeTime*(1+d.DeltaBeHealed_P),0) * GethCritP();\nFDOut.TotalDamage= recover;\nrecover = Max(recover,1);\n\nHealHpForD(recover, false);\nRecordOverHeal(FDOut);', 
            ['ID'] = 1720009, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,10000);#float(0,80000);#int(1,4);#int(0,9999999999)', 
            ['PhyActionName'] = '', 
        },
        [1720010] = {
            ['ActionName'] = 'AddHP_DuXin', 
            ['AttackType'] = {['Heal'] = 1, }, 
            ['DescFormula'] = 'local tHealMax = 1 \nHealMax = GetAtk()*$2\nreturn $1, HealMax', 
            ['Formula'] = 'local recover = Min(GetApplyHealValue(FDIn) * $1, GetAtk()*$2);\nFDOut.TotalDamage= recover;\nrecover = Max(recover,1);\n\nHealHpForD(recover, false);', 
            ['ID'] = 1720010, 
            ['IsFollowUserDamageType'] = true, 
            ['MagActionName'] = '', 
            ['ParameterCheck'] = '#float(0,1);#float(0,100)', 
            ['PhyActionName'] = '', 
        },
    }
}
return TopData