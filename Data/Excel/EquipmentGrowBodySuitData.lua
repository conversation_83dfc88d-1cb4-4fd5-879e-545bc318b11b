--
-- 表名: EquipmentGrowBodySuitData(后处理)
--

local TopData = {
    EquipmentGrowBodySuitTypeLevelToInfo = {
        ['1;1'] = {['Info'] = {['ID'] = 1, ['Level'] = 1, ['Mark'] = 100, ['Require'] = 1, ['RequireLevel'] = 2, ['RequirePromote'] = 0, ['Season'] = 101, ['SuitProp'] = {['MaxHp_N'] = 284, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259861504'),['Type'] = 1, }, ['MaxLevel'] = 3, }, 
        ['1;2'] = {['Info'] = {['ID'] = 2, ['Level'] = 2, ['Mark'] = 400, ['Require'] = 1, ['RequireLevel'] = 4, ['RequirePromote'] = 0, ['Season'] = 101, ['SuitProp'] = {['Atk_N'] = 49, ['Def_N'] = 44, ['MaxHp_N'] = 567, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259861760'),['Type'] = 1, }, ['MaxLevel'] = 3, }, 
        ['1;3'] = {['Info'] = {['ID'] = 3, ['Level'] = 3, ['Mark'] = 1000, ['Require'] = 1, ['RequireLevel'] = 8, ['RequirePromote'] = 0, ['Season'] = 101, ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862016'),['Type'] = 1, }, ['MaxLevel'] = 3, }, 
        ['2;1'] = {['Info'] = {['ID'] = 4, ['Level'] = 1, ['Mark'] = 1000, ['Require'] = 1, ['RequireLevel'] = 2, ['RequirePromote'] = 2, ['Season'] = 101, ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),['Type'] = 2, }, ['MaxLevel'] = 3, }, 
        ['2;2'] = {['Info'] = {['ID'] = 5, ['Level'] = 2, ['Mark'] = 4000, ['Require'] = 1, ['RequireLevel'] = 4, ['RequirePromote'] = 4, ['Season'] = 101, ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),['Type'] = 2, }, ['MaxLevel'] = 3, }, 
        ['2;3'] = {['Info'] = {['ID'] = 6, ['Level'] = 3, ['Mark'] = 10000, ['Require'] = 1, ['RequireLevel'] = 8, ['RequirePromote'] = 8, ['Season'] = 101, ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),['Type'] = 2, }, ['MaxLevel'] = 3, }, 
    },
    data = {
        [1] = {
            ['ID'] = 1, 
            ['Level'] = 1, 
            ['Mark'] = 100, 
            ['Require'] = 1, 
            ['RequireLevel'] = 2, 
            ['RequirePromote'] = 0, 
            ['Season'] = 101, 
            ['SuitProp'] = {['MaxHp_N'] = 284, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259861504'),
            ['Type'] = 1, 
        },
        [2] = {
            ['ID'] = 2, 
            ['Level'] = 2, 
            ['Mark'] = 400, 
            ['Require'] = 1, 
            ['RequireLevel'] = 4, 
            ['RequirePromote'] = 0, 
            ['Season'] = 101, 
            ['SuitProp'] = {['Atk_N'] = 49, ['Def_N'] = 44, ['MaxHp_N'] = 567, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259861760'),
            ['Type'] = 1, 
        },
        [3] = {
            ['ID'] = 3, 
            ['Level'] = 3, 
            ['Mark'] = 1000, 
            ['Require'] = 1, 
            ['RequireLevel'] = 8, 
            ['RequirePromote'] = 0, 
            ['Season'] = 101, 
            ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862016'),
            ['Type'] = 1, 
        },
        [4] = {
            ['ID'] = 4, 
            ['Level'] = 1, 
            ['Mark'] = 1000, 
            ['Require'] = 1, 
            ['RequireLevel'] = 2, 
            ['RequirePromote'] = 2, 
            ['Season'] = 101, 
            ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),
            ['Type'] = 2, 
        },
        [5] = {
            ['ID'] = 5, 
            ['Level'] = 2, 
            ['Mark'] = 4000, 
            ['Require'] = 1, 
            ['RequireLevel'] = 4, 
            ['RequirePromote'] = 4, 
            ['Season'] = 101, 
            ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),
            ['Type'] = 2, 
        },
        [6] = {
            ['ID'] = 6, 
            ['Level'] = 3, 
            ['Mark'] = 10000, 
            ['Require'] = 1, 
            ['RequireLevel'] = 8, 
            ['RequirePromote'] = 8, 
            ['Season'] = 101, 
            ['SuitProp'] = {['AllEleAnti_N'] = 25, ['Atk_N'] = 73, ['Def_N'] = 65, ['IgnoreAllEle_N'] = 25, ['MaxHp_N'] = 851, }, 
            ['SuitPropAppear'] = Game.TableDataManager:GetLangStr('str_18281259862272'),
            ['Type'] = 2, 
        },
    }
}
return TopData