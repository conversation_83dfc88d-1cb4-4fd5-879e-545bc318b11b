--
-- 表名: $FightPropAndSettings_战斗属性及设定.xlsx  页名：$MediumScaleBattle_中规模战斗特效裁剪优先级
--

local TopData = {
	data = {
		[0] = {
			["SpawnerType"] = 0,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219549184'),
			["Skill"] = 0,
			["Appearance"] = 0,
			["Locomotion"] = 3,
			["Hit"] = 3,
			["Attachment"] = 3,
		},
		[1] = {
			["SpawnerType"] = 1,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219549440'),
			["Skill"] = -1,
			["Appearance"] = -1,
			["Locomotion"] = 2,
			["Hit"] = 2,
			["Attachment"] = 2,
		},
		[2] = {
			["SpawnerType"] = 2,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219549696'),
			["Skill"] = -1,
			["Appearance"] = -1,
			["Locomotion"] = 2,
			["Hit"] = 3,
			["Attachment"] = 2,
		},
		[3] = {
			["SpawnerType"] = 3,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219549952'),
			["Skill"] = -2,
			["Appearance"] = -2,
			["Locomotion"] = 2,
			["Hit"] = 2,
			["Attachment"] = 2,
		},
		[4] = {
			["SpawnerType"] = 4,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219550208'),
			["Skill"] = -3,
			["Appearance"] = -3,
			["Locomotion"] = 1,
			["Hit"] = 1,
			["Attachment"] = 1,
		},
		[5] = {
			["SpawnerType"] = 5,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219550464'),
			["Skill"] = -3,
			["Appearance"] = -3,
			["Locomotion"] = 1,
			["Hit"] = 1,
			["Attachment"] = 1,
		},
		[6] = {
			["SpawnerType"] = 6,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219550720'),
			["Skill"] = -3,
			["Appearance"] = -3,
			["Locomotion"] = 1,
			["Hit"] = 1,
			["Attachment"] = 1,
		},
		[7] = {
			["SpawnerType"] = 7,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219550976'),
			["Skill"] = 0,
			["Appearance"] = 0,
			["Locomotion"] = 3,
			["Hit"] = 3,
			["Attachment"] = 3,
		},
		[8] = {
			["SpawnerType"] = 8,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219551232'),
			["Skill"] = 0,
			["Appearance"] = 0,
			["Locomotion"] = 3,
			["Hit"] = 3,
			["Attachment"] = 3,
		},
		[9] = {
			["SpawnerType"] = 9,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219551488'),
			["Skill"] = -1,
			["Appearance"] = -1,
			["Locomotion"] = 2,
			["Hit"] = 2,
			["Attachment"] = 2,
		},
		[10] = {
			["SpawnerType"] = 10,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219551744'),
			["Skill"] = -1,
			["Appearance"] = -1,
			["Locomotion"] = 2,
			["Hit"] = 2,
			["Attachment"] = 2,
		},
		[11] = {
			["SpawnerType"] = 11,
			["Desc"] = Game.TableDataManager:GetLangStr('str_24396219552000'),
			["Skill"] = -2,
			["Appearance"] = -2,
			["Locomotion"] = 1,
			["Hit"] = 1,
			["Attachment"] = 1,
		},
	},
}

return TopData
