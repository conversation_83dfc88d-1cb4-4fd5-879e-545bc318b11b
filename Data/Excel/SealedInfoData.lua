--
-- 表名: SealedInfoData后处理
--

local TopData = {
    SeasonGroupMap = {
        [0] = {{1, 2, 3}, {4, 5, 6}, {7, 8}, }, 
        [1] = {[3] = {9}, }, 
    },
    data = {
        [1] = {
            ['AttributeImproveType'] = 1, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917442560'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722748928'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 1, 
            ['ID'] = 1, 
            ['Icon'] = 'UI_Item_Icon_WholeBrain', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501523456'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300001}, 
            ['UnlockTriggerID'] = 0, 
        },
        [2] = {
            ['AttributeImproveType'] = 1, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917442816'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722749184'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 1, 
            ['ID'] = 2, 
            ['Icon'] = 'UI_Item_Icon_Wine', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_38758590233344'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300002}, 
            ['UnlockTriggerID'] = 0, 
        },
        [3] = {
            ['AttributeImproveType'] = 1, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917443072'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722749440'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 1, 
            ['ID'] = 3, 
            ['Icon'] = 'UI_Item_Icon_WitcherPotion', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501523968'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300003}, 
            ['UnlockTriggerID'] = 0, 
        },
        [4] = {
            ['AttributeImproveType'] = 2, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917443328'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722749696'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 2, 
            ['ID'] = 4, 
            ['Icon'] = 'UI_Item_Icon_Wraiths', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501524224'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300004}, 
            ['UnlockTriggerID'] = 0, 
        },
        [5] = {
            ['AttributeImproveType'] = 2, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917443584'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722749952'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 2, 
            ['ID'] = 5, 
            ['Icon'] = 'UI_Item_Icon_WraithsDust', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501524480'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300005}, 
            ['UnlockTriggerID'] = 0, 
        },
        [6] = {
            ['AttributeImproveType'] = 2, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917443840'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722750208'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 2, 
            ['ID'] = 6, 
            ['Icon'] = 'UI_Item_Icon_Wine', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501524736'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300006}, 
            ['UnlockTriggerID'] = 0, 
        },
        [7] = {
            ['AttributeImproveType'] = 3, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917444096'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722750464'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 3, 
            ['ID'] = 7, 
            ['Icon'] = 'UI_Item_Icon_WitcherPotion', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501524992'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300007}, 
            ['UnlockTriggerID'] = 0, 
        },
        [8] = {
            ['AttributeImproveType'] = 3, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917444352'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722750720'),
            ['DisplayTriggerID'] = 0, 
            ['GroupId'] = 3, 
            ['ID'] = 8, 
            ['Icon'] = 'UI_Item_Icon_Wraiths', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380571648'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501525248'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {0}, 
            ['SkillList'] = {82300008}, 
            ['UnlockTriggerID'] = 0, 
        },
        [9] = {
            ['AttributeImproveType'] = 3, 
            ['BuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45151917444608'),
            ['DebuffDescribeText'] = Game.TableDataManager:GetLangStr('str_45152722750976'),
            ['DisplayTriggerID'] = 6500060, 
            ['GroupId'] = 3, 
            ['ID'] = 9, 
            ['Icon'] = 'UI_Item_Icon_WraithsDust', 
            ['ItemDes'] = Game.TableDataManager:GetLangStr('str_45150575265280'),
            ['LockedText'] = Game.TableDataManager:GetLangStr('str_45151380573696'),
            ['Mark'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_45149501525504'),
            ['NegativeSkillList'] = {82300201}, 
            ['SeasonIdList'] = {1}, 
            ['SkillList'] = {82300009}, 
            ['UnlockTriggerID'] = 6500076, 
        },
    }
}
return TopData