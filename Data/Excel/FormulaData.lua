--
-- 表名: $Formula_通用公式.xlsx  页名：$Formula_通用公式
--

local TopData = {
	data = {
		[1600001] = {
			["ID"] = 1600001,
			["Name"] = "MonsterExp_General",
			["IsFightFormula"] = false,
			["Formula"] = "return Min(999999,<PERSON>(3, $1*2, $1*3-4, 98*Pow(1.03,<PERSON>($1-34,0))))",
			["DescFormula"] = "",
		},
		[1600002] = {
			["ID"] = 1600002,
			["Name"] = "MonsterExp_Elite",
			["IsFightFormula"] = false,
			["Formula"] = "return Min(999999,Max(3, $1*2, $1*3-4, 98*<PERSON>w(1.03,Max($1-34,0))))",
			["DescFormula"] = "",
		},
		[1600003] = {
			["ID"] = 1600003,
			["Name"] = "MonsterExp_Boss",
			["IsFightFormula"] = false,
			["Formula"] = "return Min(999999,Max(3, $1*2, $1*3-4, 98*Pow(1.03,<PERSON>($1-34,0))))",
			["DescFormula"] = "",
		},
		[1600100] = {
			["ID"] = 1600100,
			["Name"] = "MonsterExp_0",
			["IsFightFormula"] = false,
			["Formula"] = "return 0",
			["DescFormula"] = "",
		},
		[1600150] = {
			["ID"] = 1600150,
			["Name"] = "EquipBreak_4",
			["IsFightFormula"] = false,
			["Formula"] = "local rate = Random(1,100000)/100000\nif $1 <= 79 and rate <= Max((Floor($1/20)-1)*2,1)/5 then\n    return 1\nelseif $1 > 79  and rate <= 0.2 then\n    return 2\nelseif $1 > 79  and rate > 0.2 then\n    return 1\nelse\n    return 0\nend",
			["DescFormula"] = "",
		},
		[1600201] = {
			["ID"] = 1600201,
			["Name"] = "Exp_DailyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local exp = 0\nexp = 188.8 * $3 * $3 - 4410 * $3 +95200\nreturn exp",
			["DescFormula"] = "",
		},
		[1600202] = {
			["ID"] = 1600202,
			["Name"] = "Exp_WeeklyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local exp = 44.8 * $3 * $3 - 1260 * $3 + 11200\nreturn exp",
			["DescFormula"] = "",
		},
		[1600203] = {
			["ID"] = 1600203,
			["Name"] = "Exp_WeeklyDungeon_General_half",
			["IsFightFormula"] = false,
			["Formula"] = "local exp = 22.4 * $3 * $3 - 630 * $3 + 5600\nreturn exp",
			["DescFormula"] = "",
		},
		[1600204] = {
			["ID"] = 1600204,
			["Name"] = "Exp_WeeklyDungeon_General_onethird",
			["IsFightFormula"] = false,
			["Formula"] = "local exp = 15 * $3 * $3 - 320 * $3 + 3700\nreturn exp",
			["DescFormula"] = "",
		},
		[1600301] = {
			["ID"] = 1600301,
			["Name"] = "Rinal_DailyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 90 * $3 * $3 - 4200 * $3 +150000\nreturn money",
			["DescFormula"] = "",
		},
		[1600302] = {
			["ID"] = 1600302,
			["Name"] = "Rinal_WeeklyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 171 * $3 * $3 - 7980 * $3 +285000\nreturn money",
			["DescFormula"] = "",
		},
		[1600303] = {
			["ID"] = 1600303,
			["Name"] = "Rinal_WeeklyDungeon_General_stageReward",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 68 * $3 * $3 - 3200 * $3 +114000\nreturn money",
			["DescFormula"] = "",
		},
		[1600304] = {
			["ID"] = 1600304,
			["Name"] = "Rinal_WeeklyDungeon_General_onethird",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 70 * $3 * $3 - 3080 * $3 +114000\nreturn money",
			["DescFormula"] = "",
		},
		[1600305] = {
			["ID"] = 1600305,
			["Name"] = "Rinal_Explore_Reward",
			["IsFightFormula"] = false,
			["Formula"] = "local money = Random(0,1)\nmoney = $1 * Round(money) +$2\nreturn money",
			["DescFormula"] = "",
		},
		[1600401] = {
			["ID"] = 1600401,
			["Name"] = "Pence_DailyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 288 * $3 * $3 - 13440 * $3 +480000\nreturn money",
			["DescFormula"] = "",
		},
		[1600402] = {
			["ID"] = 1600402,
			["Name"] = "Pence_WeeklyDungeon_General",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 342 * $3 * $3 - 16000 * $3 +570000\nreturn money",
			["DescFormula"] = "",
		},
		[1600403] = {
			["ID"] = 1600403,
			["Name"] = "Pence_WeeklyDungeon_General_stageReward",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 137 * $3 * $3 - 6350 * $3 +228000\nreturn money",
			["DescFormula"] = "",
		},
		[1600404] = {
			["ID"] = 1600404,
			["Name"] = "Pence_WeeklyDungeon_General_onethird",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = 114 * $3 * $3 - 5320 * $3 +190000\nreturn money",
			["DescFormula"] = "",
		},
		[1600405] = {
			["ID"] = 1600405,
			["Name"] = "Rinal_Explore_Box_Reward",
			["IsFightFormula"] = false,
			["Formula"] = "local money = Random(0,1)\nmoney = 1000 * Round(money) +1000\nreturn money",
			["DescFormula"] = "",
		},
		[1600406] = {
			["ID"] = 1600406,
			["Name"] = "Rinal_Explore_puzzle_Reward",
			["IsFightFormula"] = false,
			["Formula"] = "local money = Random(0,1)\nmoney = 300 * Round(money) +1200\nreturn money",
			["DescFormula"] = "",
		},
		[1600004] = {
			["ID"] = 1600004,
			["Name"] = "AtkEquip_BaseFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return 0.15*Max(-1,Min(1,Pow(-2*Ln(Random(1,100000)/100000),0.5)*Cos(2*3.14*Random(1,100000)/100000)*0.6))+1",
			["DescFormula"] = "",
		},
		[1600005] = {
			["ID"] = 1600005,
			["Name"] = "AtkFixedWord_GrowFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return Floor($1+Max(1 , $2 /2))",
			["DescFormula"] = "",
		},
		[1600006] = {
			["ID"] = 1600006,
			["Name"] = "AtkFixedWord_ReplaceFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return Random(Min($1,$2),Max($1,$2))",
			["DescFormula"] = "",
		},
		[1600007] = {
			["ID"] = 1600007,
			["Name"] = "Guild_Level_WageFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return 1 + $1 * 0.1",
			["DescFormula"] = "",
		},
		[1600008] = {
			["ID"] = 1600008,
			["Name"] = "Guild_Contri_WageFormula",
			["IsFightFormula"] = false,
			["Formula"] = "if $1 >= 1000 then\n    return 1.1\nelseif $1 >= 2000 then\n    return 1.2\nelseif $1 >= 5000 then\n    return 1.3\nelseif $1 >= 10000 then\n    return 1.5\nelseif $1 >= 20000 then\n    return 1.8\nelseif $1 >= 50000 then\n    return 2.1\nelseif $1 >= 100000 then\n    return 2.4\nelse\n    return 1\nend",
			["DescFormula"] = "",
		},
		[1600009] = {
			["ID"] = 1600009,
			["Name"] = "Guild_Max_FundsFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return 1000000 + 1000000 * $1",
			["DescFormula"] = "",
		},
		[1600010] = {
			["ID"] = 1600010,
			["Name"] = "Guild_Max_Member_NumFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return 80 + $1 * 2",
			["DescFormula"] = "",
		},
		[1600011] = {
			["ID"] = 1600011,
			["Name"] = "Guild_Total_Max_Member_NumFormula",
			["IsFightFormula"] = false,
			["Formula"] = "return 80 + $1 * 2  + 60",
			["DescFormula"] = "",
		},
		[1600012] = {
			["ID"] = 1600012,
			["Name"] = "Guild_Day_Max_Kick_Member_NumFormula",
			["IsFightFormula"] = false,
			["Formula"] = "if $1 == 1  then\n       return 30\n   elseif $1 == 2  then\n       return 40\n   elseif $1 == 3  then\n       return 50 \n   elseif $1 == 4  then\n       return 60\n   elseif $1 == 5  then\n       return 70\n   elseif $1 == 6  then\n       return 80\nend",
			["DescFormula"] = "",
		},
		[1600013] = {
			["ID"] = 1600013,
			["Name"] = "Fellow_PropGrow1",
			["IsFightFormula"] = false,
			["Formula"] = "if $2 <= 50  then\n       return Floor($1 * (0.4 * $2 + 0.5) * 0.6)\n   else\n       return Floor($1 * (0.14 * Pow($2 - 50 , 1.4) + 0.4 * ($2 - 50) + 20.5) * 0.6)\nend",
			["DescFormula"] = "",
		},
		[1600014] = {
			["ID"] = 1600014,
			["Name"] = "Fellow_PropGrow2",
			["IsFightFormula"] = false,
			["Formula"] = "if $2 <= 50  then\n       return Floor($1 * (0.5 * $2) * 0.6)\n   else\n       return Floor($1 * (1.5 * ($2 - 50 ) + 25) * 0.6)\nend",
			["DescFormula"] = "",
		},
		[1600015] = {
			["ID"] = 1600015,
			["Name"] = "Fellow_PropGrow3",
			["IsFightFormula"] = false,
			["Formula"] = "if $2 <= 50  then\n       return Floor($1 * (0.4 * $2 + 0.5) * 0.7)\n   else\n       return Floor($1 * (0.14 * Pow($2 - 50 , 1.4) + 0.4 * ($2 - 50) + 20.5) * 0.7)\nend",
			["DescFormula"] = "",
		},
		[1600016] = {
			["ID"] = 1600016,
			["Name"] = "Fellow_PropGrow4",
			["IsFightFormula"] = false,
			["Formula"] = "if $2 <= 50  then\n       return Floor($1 * (0.5 * $2) * 0.7)\n   else\n       return Floor($1 * (1.5 * ($2 - 50 ) + 25) * 0.7)\nend",
			["DescFormula"] = "",
		},
		[1600017] = {
			["ID"] = 1600017,
			["Name"] = "DefEquip_BaseGrow",
			["IsFightFormula"] = false,
			["Formula"] = "return Round(($1 + 197 / 45) * $2)",
			["DescFormula"] = "",
		},
		[1600018] = {
			["ID"] = 1600018,
			["Name"] = "Sealed_Deline",
			["IsFightFormula"] = false,
			["Formula"] = "local currentLevel=$1\nlocal minLevel=$2\nlocal Interval=3\nlocal common=1000\nlocal para1 = 0\nlocal para2= 47\nlocal para3 = -328\nlocal para4 = 654\nlocal LvGap=currentLevel-minLevel\nlocal maxRate=currentLevel*common\nlocal DelineRate = para1* Pow(LvGap,3) + para2* Pow(LvGap,2)+para3* LvGap+para4\nif LvGap< Interval then\n    return 0\nend \nreturn min(DelineRate,maxRate)",
			["DescFormula"] = "local currentLevel=$1\nlocal minLevel=$2\nlocal Interval=3\nlocal common=1000\nlocal para1 = 0\nlocal para2= 47\nlocal para3 = -328\nlocal para4 = 654\nlocal LvGap=currentLevel-minLevel\nlocal maxRate=currentLevel*common\nlocal DelineRate = para1* Pow(LvGap,3) + para2* Pow(LvGap,2)+para3* LvGap+para4\nif LvGap< Interval then\n    return 0\nend \nreturn min(DelineRate,maxRate)",
		},
		[1600020] = {
			["ID"] = 1600020,
			["Name"] = "Sealed_FKKJ_BUFF_MAXLAYER",
			["IsFightFormula"] = true,
			["Formula"] = "local skillRank=$2\nif skillRank == nil or skillRank == 0 then\n       skillRank=$1\nend\nlocal MaxLayer=skillRank\n\nif skillRank <=5  then\n       return 3\n   else\n       return 5\nend",
			["DescFormula"] = "local skillRank=$2\nif skillRank == nil or skillRank == 0 then\n       skillRank=$1\nend\nlocal MaxLayer=skillRank\n\nif skillRank <=5  then\n       return 3\n   else\n       return 5\nend",
		},
		[1600021] = {
			["ID"] = 1600021,
			["Name"] = "Expect_Combat_Time",
			["IsFightFormula"] = false,
			["Formula"] = "local expectTime = Max(2 -Ln($2,100), 20 * (1-Ln($2,100))) * $1\nreturn expectTime",
			["DescFormula"] = "",
		},
		[1600022] = {
			["ID"] = 1600022,
			["Name"] = "RobotLevel_1",
			["IsFightFormula"] = false,
			["Formula"] = "return Max(1 , Min($2 / 1000 , $1 + 10))",
			["DescFormula"] = "",
		},
		[1600023] = {
			["ID"] = 1600023,
			["Name"] = "XueZhiHua_BuffLayer",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(5 , Floor((1 - a.Hp / a.MaxHp) * 10))",
			["DescFormula"] = "",
		},
		[1600104] = {
			["ID"] = 1600104,
			["Name"] = "AtkEquip_BaseFormula_New",
			["IsFightFormula"] = false,
			["Formula"] = "return 1",
			["DescFormula"] = "",
		},
		[1610000] = {
			["ID"] = 1610000,
			["Name"] = "BuffLevel_Test",
			["IsFightFormula"] = true,
			["Formula"] = "return $1 + 1",
			["DescFormula"] = "return $1 + 1",
		},
		[1610001] = {
			["ID"] = 1610001,
			["Name"] = "BuffDuration_Test",
			["IsFightFormula"] = true,
			["Formula"] = "return $1 + 1",
			["DescFormula"] = "return $1 + 1",
		},
		[1610002] = {
			["ID"] = 1610002,
			["Name"] = "BuffProbability_Test",
			["IsFightFormula"] = true,
			["Formula"] = "if a.Level > d.Level then\n    return 1.0\nend\nreturn 0.5",
			["DescFormula"] = "return 1.0",
		},
		[1610003] = {
			["ID"] = 1610003,
			["Name"] = "PropModify_Test",
			["IsFightFormula"] = true,
			["Formula"] = "if a.Level > d.Level then\n    return 1.0 * $1\nend\nreturn $1",
			["DescFormula"] = "return $1",
		},
		[1610004] = {
			["ID"] = 1610004,
			["Name"] = "BuffProbability_Test2",
			["IsFightFormula"] = true,
			["Formula"] = "return 1",
			["DescFormula"] = "return 1",
		},
		[1610005] = {
			["ID"] = 1610005,
			["Name"] = "SkillCD_Test",
			["IsFightFormula"] = true,
			["Formula"] = "return max(14-$1,5)",
			["DescFormula"] = "return max(14-$1,5)",
		},
		[1610100] = {
			["ID"] = 1610100,
			["Name"] = "BuffPropModify_01",
			["IsFightFormula"] = true,
			["Formula"] = "return a.ProfessionProp1 * ($1 *0.02)/100",
			["DescFormula"] = "return ($1 *0.02)/100",
		},
		[1610101] = {
			["ID"] = 1610101,
			["Name"] = "BuffPropModify_02",
			["IsFightFormula"] = true,
			["Formula"] = "return a.ProfessionProp1 * ($1 *0.005 )",
			["DescFormula"] = "return ($1 *0.005 )",
		},
		[1610102] = {
			["ID"] = 1610102,
			["Name"] = "BuffSheildModify_aAtk",
			["IsFightFormula"] = true,
			["Formula"] = "return Round((a.pAtkMin + a.pAtkMax) * 0.5 * $2)",
			["DescFormula"] = "return Round((a.pAtkMin + a.pAtkMax) * 0.5 * $2)",
		},
		[1610103] = {
			["ID"] = 1610103,
			["Name"] = "BuffSheildModify_dMaxHp",
			["IsFightFormula"] = true,
			["Formula"] = "return  d.MaxHp * $2",
			["DescFormula"] = "return  a.MaxHp * $2",
		},
		[1610104] = {
			["ID"] = 1610104,
			["Name"] = "PullHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhancePull)-d.PullAnti)*1.5/200)+a.PullHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610105] = {
			["ID"] = 1610105,
			["Name"] = "DownHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceDown)-d.DownAnti)*1.5/200)+a.DownHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610106] = {
			["ID"] = 1610106,
			["Name"] = "AirborneHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceAirborne)-d.AirborneAnti)*1.5/200)+a.AirborneHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610107] = {
			["ID"] = 1610107,
			["Name"] = "SleepHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceSleep)-d.SleepAnti)*1.5/200)+a.SleepHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610108] = {
			["ID"] = 1610108,
			["Name"] = "TiedHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceTied)-d.TiedAnti)*1.5/200)+a.TiedHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610109] = {
			["ID"] = 1610109,
			["Name"] = "FearHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceFear)-d.FearAnti)*1.5/200)+a.FearHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610110] = {
			["ID"] = 1610110,
			["Name"] = "DizzyHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceDizzy)-d.DizzyAnti)*1.5/200)+a.DizzyHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610111] = {
			["ID"] = 1610111,
			["Name"] = "SilentHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceSilence)-d.SilenceAnti)*1.5/200)+a.SilenceHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610112] = {
			["ID"] = 1610112,
			["Name"] = "SlowHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "if d.IsPlayer then\nreturn  Max(0,((76+4*$1+a.EnhanceSlow)-d.SlowAnti)*1.5/200)+a.SlowHitRate\nelse\n return 1\nend",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)\nreturn Min(rate,1)",
		},
		[1610113] = {
			["ID"] = 1610113,
			["Name"] = "SlowDuration",
			["IsFightFormula"] = true,
			["Formula"] = "return  Max(0.5 , ($1*4+(a.EnhanceSlow-d.SlowAnti)*1.5)/105+1)",
			["DescFormula"] = "local rate = Max(0.5 , $1*4/105+1)\nreturn Min(rate,1)",
		},
		[1610114] = {
			["ID"] = 1610114,
			["Name"] = "DizzyDuration",
			["IsFightFormula"] = true,
			["Formula"] = "return  Max(0.5 , ($1*4+(a.EnhanceDizzy-d.DizzyAnti)*1.5)/105+1)",
			["DescFormula"] = "local rate = Max(0.5 , $1*4/105+1)\nreturn Min(rate,1)",
		},
		[1610115] = {
			["ID"] = 1610115,
			["Name"] = "TiedDuration",
			["IsFightFormula"] = true,
			["Formula"] = "return  Max(0.5 , ($1*4+(a.EnhanceTied-d.TiedAnti)*1.5)/105+1)",
			["DescFormula"] = "local rate = Max(0.5 , $1*4/105+1)\nreturn Min(rate,1)",
		},
		[1610116] = {
			["ID"] = 1610116,
			["Name"] = "AirborneContiHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Max(0,((76+4*$1+a.EnhanceAirborne)-d.AirborneAnti)*1.5/200)+a.AirborneHitRate)*2",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)*2\nreturn Min(rate,1)",
		},
		[1610117] = {
			["ID"] = 1610117,
			["Name"] = "DownContiHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Max(0,((76+4*$1+a.EnhanceDown)-d.DownAnti)*1.5/200)+a.DownHitRate)*2",
			["DescFormula"] = "local rate = Max(0,(76+4*$1)*1.5/200)*2\nreturn Min(rate,1)",
		},
		[1610130] = {
			["ID"] = 1610130,
			["Name"] = "ChaosHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.ChaosRate",
			["DescFormula"] = "return  a.ChaosRate",
		},
		[1610131] = {
			["ID"] = 1610131,
			["Name"] = "MysteryHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.MysteryRate",
			["DescFormula"] = "return  a.MysteryRate",
		},
		[1610132] = {
			["ID"] = 1610132,
			["Name"] = "AbundanceHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.AbundanceRate",
			["DescFormula"] = "return  a.AbundanceRate",
		},
		[1610133] = {
			["ID"] = 1610133,
			["Name"] = "DarknessHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.DarknessRate",
			["DescFormula"] = "return  a.DarknessRate",
		},
		[1610134] = {
			["ID"] = 1610134,
			["Name"] = "CalamityHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.CalamityRate",
			["DescFormula"] = "return  a.CalamityRate",
		},
		[1610135] = {
			["ID"] = 1610135,
			["Name"] = "DisorderHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.DisorderRate",
			["DescFormula"] = "return  a.DisorderRate",
		},
		[1610136] = {
			["ID"] = 1610136,
			["Name"] = "TenebrousHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.TenebrousRate",
			["DescFormula"] = "return  a.TenebrousRate",
		},
		[1610137] = {
			["ID"] = 1610137,
			["Name"] = "KnowledgeHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.KnowledgeRate",
			["DescFormula"] = "return  a.KnowledgeRate",
		},
		[1610138] = {
			["ID"] = 1610138,
			["Name"] = "FateHitRate",
			["IsFightFormula"] = true,
			["Formula"] = "return  a.FateRate",
			["DescFormula"] = "return  a.FateRate",
		},
		[1610150] = {
			["ID"] = 1610150,
			["Name"] = "BuffSheildModify_aAtk15",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = (a.pAtkMin + a.pAtkMax) * 0.5 * (0.125 + 0.0125 * $1)\nreturn Round(shield)",
			["DescFormula"] = "local shield = (a.pAtkMin + a.pAtkMax) * 0.5 * (0.125 + 0.0125 * $1)\nreturn Round(shield)",
		},
		[1610160] = {
			["ID"] = 1610160,
			["Name"] = "BuffSheildModify_dMaxHp15",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = a.MaxHp * (0.05 + 0.003 * ($1-1))\nreturn Round(shield)",
			["DescFormula"] = "local shield = a.MaxHp * (0.05 + 0.003 * ($1-1))\nreturn Round(shield)",
		},
		[1610161] = {
			["ID"] = 1610161,
			["Name"] = "BuffSheildModify_dMaxHp10",
			["IsFightFormula"] = true,
			["Formula"] = "local shield =  d.MaxHp * (0.05 + 0.003 * ($1-1))\nreturn Round(shield)",
			["DescFormula"] = "return 0.05 + 0.003 * ($1-1)",
		},
		[1610162] = {
			["ID"] = 1610162,
			["Name"] = "BuffSheildModify_dMaxHp05",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.05\nreturn Round(shield)",
			["DescFormula"] = "return 0.05",
		},
		[1610163] = {
			["ID"] = 1610163,
			["Name"] = "BuffSheildModify_dMaxHp07",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.07\nreturn Round(shield)",
			["DescFormula"] = "return 0.07",
		},
		[1610164] = {
			["ID"] = 1610164,
			["Name"] = "BuffSheildModify_8130501",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.1\nreturn Round(shield)",
			["DescFormula"] = "return 0.1",
		},
		[1610165] = {
			["ID"] = 1610165,
			["Name"] = "BuffSheildModify_8130502",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.15\nreturn Round(shield)",
			["DescFormula"] = "return 0.15",
		},
		[1610166] = {
			["ID"] = 1610166,
			["Name"] = "BuffShieldModify_8130701",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.1\nreturn Round(shield)",
			["DescFormula"] = "return 0.1",
		},
		[1610167] = {
			["ID"] = 1610167,
			["Name"] = "BuffShieldModify_8130702",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.12\nreturn Round(shield)",
			["DescFormula"] = "return 0.12",
		},
		[1610168] = {
			["ID"] = 1610168,
			["Name"] = "BuffShieldModify_8141501",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.03\nreturn Round(shield)",
			["DescFormula"] = "return 0.03",
		},
		[1610169] = {
			["ID"] = 1610169,
			["Name"] = "BuffShieldModify_8141502",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.04\nreturn Round(shield)",
			["DescFormula"] = "return 0.04",
		},
		[1610170] = {
			["ID"] = 1610170,
			["Name"] = "BuffShieldModify_8141503",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.05\nreturn Round(shield)",
			["DescFormula"] = "return 0.05",
		},
		[1610171] = {
			["ID"] = 1610171,
			["Name"] = "BuffShieldModify_8141504",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * 0.06\nreturn Round(shield)",
			["DescFormula"] = "return 0.06",
		},
		[1610172] = {
			["ID"] = 1610172,
			["Name"] = "BuffShieldModify_8100203_1",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = 350.48 + 13.14 * ($1-1)\nreturn Round(shield)",
			["DescFormula"] = "local shield = 350.48 + 13.14 * ($1-1)\nreturn Round(shield)",
		},
		[1610173] = {
			["ID"] = 1610173,
			["Name"] = "BuffShieldModify_8100203_2",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.8\nreturn Round(shield)",
			["DescFormula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.8\nreturn Round(shield)",
		},
		[1610174] = {
			["ID"] = 1610174,
			["Name"] = "BuffShieldModify_8100203_3",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.8\nreturn Round(shield)",
			["DescFormula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.8\nreturn Round(shield)",
		},
		[1610175] = {
			["ID"] = 1610175,
			["Name"] = "BuffShieldModify_8100203_4",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.7\nreturn Round(shield)",
			["DescFormula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.7\nreturn Round(shield)",
		},
		[1610176] = {
			["ID"] = 1610176,
			["Name"] = "BuffShieldModify_8100203_5",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.7\nreturn Round(shield)",
			["DescFormula"] = "local shield = (350.48 + 13.14 * ($1-1)) *0.7\nreturn Round(shield)",
		},
		[1610178] = {
			["ID"] = 1610178,
			["Name"] = "BuffShieldModify_8100229",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = 700.96\nreturn Round(shield)",
			["DescFormula"] = "local shield = 700.96\nreturn Round(shield)",
		},
		[1610179] = {
			["ID"] = 1610179,
			["Name"] = "BuffShieldModify_84005511",
			["IsFightFormula"] = true,
			["Formula"] = "return Round(0.02 * a.MaxHp)",
			["DescFormula"] = "return Round(0.02 * a.MaxHp)",
		},
		[1610180] = {
			["ID"] = 1610180,
			["Name"] = "Milgongen_LockedHp_074",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.74 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610181] = {
			["ID"] = 1610181,
			["Name"] = "Milgongen_LockedHp_070",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.7 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610182] = {
			["ID"] = 1610182,
			["Name"] = "Milgongen_LockedHp_050",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.5 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610183] = {
			["ID"] = 1610183,
			["Name"] = "Milgongen_LockedHp_030",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.3 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610184] = {
			["ID"] = 1610184,
			["Name"] = "Milgongen_LockedHp_010",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.1 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610185] = {
			["ID"] = 1610185,
			["Name"] = "Milgongen_LockedHp_001",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.01 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610186] = {
			["ID"] = 1610186,
			["Name"] = "Ulorus_LockedHp_001",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.01 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610187] = {
			["ID"] = 1610187,
			["Name"] = "BuffSheildModify_DuXin",
			["IsFightFormula"] = true,
			["Formula"] = "return Min((a.mAtkMin + a.mAtkMax)*5,Round(d.MaxHp * 0.2))",
			["DescFormula"] = "",
		},
		[1610188] = {
			["ID"] = 1610188,
			["Name"] = "Milgongen_LockedHp_055",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.55 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610189] = {
			["ID"] = 1610189,
			["Name"] = "Milgongen_LockedHp_035",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.35 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610190] = {
			["ID"] = 1610190,
			["Name"] = "Milgongen_LockedHp_015",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610200] = {
			["ID"] = 1610200,
			["Name"] = "BuffShieldModify_8100318",
			["IsFightFormula"] = true,
			["Formula"] = "local shield = d.MaxHp * (0.05 + 0.003 * ($1-1))\nreturn Round(shield)",
			["DescFormula"] = "return Round(0.05 * a.MaxHp)",
		},
		[1610201] = {
			["ID"] = 1610201,
			["Name"] = "BuffShieldModify_8100318_2",
			["IsFightFormula"] = true,
			["Formula"] = "return a.ProfessionProp1 *0.05",
			["DescFormula"] = "return 0.05",
		},
		[1610202] = {
			["ID"] = 1610202,
			["Name"] = "BuffShieldModify_8100318_3",
			["IsFightFormula"] = true,
			["Formula"] = "if a.ProfessionProp1 == 5 then\n    return -2\nend\n    return -5",
			["DescFormula"] = "if a.ProfessionProp1 == 5 then\n    return -2\nend\n    return -5",
		},
		[1610203] = {
			["ID"] = 1610203,
			["Name"] = "BuffShieldModify_8100318_4",
			["IsFightFormula"] = true,
			["Formula"] = "return a.ProfessionProp1",
			["DescFormula"] = "return a.ProfessionProp1",
		},
		[1610204] = {
			["ID"] = 1610204,
			["Name"] = "BuffShieldModify_8000226",
			["IsFightFormula"] = true,
			["Formula"] = "local Instance = FDIn.Instance\nif Instance and Instance.Controller then\n    return Instance.Controller:GetDynamicData(0, \"DamageValue\")\nend\nreturn 0.0",
			["DescFormula"] = "local Instance = FDIn.Instance\nif Instance and Instance.Controller then\n    return Instance.Controller:GetDynamicData(0, \"DamageValue\")\nend\nreturn 0.0",
		},
		[1610220] = {
			["ID"] = 1610220,
			["Name"] = "IgnoreDef_DescOnly",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15+$1*0.0075",
			["DescFormula"] = "return 0.15+$1*0.0075",
		},
		[1610301] = {
			["ID"] = 1610301,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv1",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.02 + 0.02, 0.1)",
			["DescFormula"] = "",
		},
		[1610302] = {
			["ID"] = 1610302,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv2",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.02 + 0.02, 0.12)",
			["DescFormula"] = "",
		},
		[1610303] = {
			["ID"] = 1610303,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv3",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.02 + 0.02, 0.14)",
			["DescFormula"] = "",
		},
		[1610304] = {
			["ID"] = 1610304,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv4",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.03 + 0.03, 0.21)",
			["DescFormula"] = "",
		},
		[1610305] = {
			["ID"] = 1610305,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv5",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.03 + 0.03, 0.24)",
			["DescFormula"] = "",
		},
		[1610306] = {
			["ID"] = 1610306,
			["Name"] = "XUEZHIHUA_HurtReduce_Lv6",
			["IsFightFormula"] = true,
			["Formula"] = "return Min(Floor((1-d.Hp/d.MaxHp)*10) *0.04 + 0.04, 0.32)",
			["DescFormula"] = "",
		},
		[1610307] = {
			["ID"] = 1610307,
			["Name"] = "KUIMIYUYAN_Lv1",
			["IsFightFormula"] = true,
			["Formula"] = "return 12+3*$1",
			["DescFormula"] = "return 12+3*$1",
		},
		[1610308] = {
			["ID"] = 1610308,
			["Name"] = "LiMingKaiJia_1",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.1",
			["DescFormula"] = "return 0.1",
		},
		[1610309] = {
			["ID"] = 1610309,
			["Name"] = "LiMingKaiJia_2",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.2",
			["DescFormula"] = "return 0.2",
		},
		[1610310] = {
			["ID"] = 1610310,
			["Name"] = "SealedSkillToBuff",
			["IsFightFormula"] = false,
			["Formula"] = "return Round(0.5 * $1 * $1 + 3.5 * $1 + 1)",
			["DescFormula"] = "return Round(0.5 * $1 * $1 + 3.5 * $1 + 1)",
		},
		[1610311] = {
			["ID"] = 1610311,
			["Name"] = "FengKuangKuiJia_Condition",
			["IsFightFormula"] = false,
			["Formula"] = "return 0.1",
			["DescFormula"] = "return 0.1",
		},
		[1610312] = {
			["ID"] = 1610312,
			["Name"] = "FengKuangKuiJia_Effect",
			["IsFightFormula"] = false,
			["Formula"] = "local delta = 0\nif $1 < 16 then\n  delta = 0.65\n elseif $1 < 31 then\n  delta =  0.65\nelse\n  delta = 0.65\nend\n\nreturn delta",
			["DescFormula"] = "local delta = 0\nif $1 < 16 then\n  delta = 0.65\n elseif $1 < 31 then\n  delta =  0.65\nelse\n  delta = 0.65\nend\n\nreturn delta",
		},
		[1610313] = {
			["ID"] = 1610313,
			["Name"] = "Zhanshi_ShuaibaiBuffLv",
			["IsFightFormula"] = false,
			["Formula"] = "local a = $2\nif a.skillList[8000501] == nil then\n    return 1\nend\n    return a.skillList[8000501].SkillLvl",
			["DescFormula"] = "local a = $2\nif a.skillList[8000501] == nil then\n    return 1\nend\n    return a.skillList[8000501].SkillLvl",
		},
		[1610314] = {
			["ID"] = 1610314,
			["Name"] = "BuffShield_LiMingKaiJia",
			["IsFightFormula"] = true,
			["Formula"] = "return Round(d.MaxHp * (0.15 + ($1-1)/193))",
			["DescFormula"] = "return 0.15 + ($1-1)/193",
		},
		[1610315] = {
			["ID"] = 1610315,
			["Name"] = "Zhanshi_SkillDes1",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * 0.246*2 + a.DarknessAtk * 0.246*2 + (78*$1*0.246*2)",
			["DescFormula"] = "return  (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * 0.246*2 + a.DarknessAtk * 0.246*2 + (78*$1*0.246*2)",
		},
		[1610316] = {
			["ID"] = 1610316,
			["Name"] = "Zhanshi_SkillDes2",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * 0.383*2 + a.DarknessAtk * 0.383*2 + (78*$1*0.383*2)",
			["DescFormula"] = "return  (Min(a.pAtkMin,a.pAtkMax) + a.pAtkMax) / 2 * 0.383*2 + a.DarknessAtk * 0.383*2 + (78*$1*0.383*2)",
		},
		[1610317] = {
			["ID"] = 1610317,
			["Name"] = "Xuetu_SkillDes1",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96*2 + a.MysteryAtk * 0.96*2 + (78*$1*0.96*2)",
			["DescFormula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96*2 + a.MysteryAtk * 0.96*2 + (102*$1*0.96*2)",
		},
		[1610318] = {
			["ID"] = 1610318,
			["Name"] = "Xuetu_SkillDes2",
			["IsFightFormula"] = true,
			["Formula"] = "return  0.25",
			["DescFormula"] = "return  0.25",
		},
		[1610319] = {
			["ID"] = 1610319,
			["Name"] = "Xuetu_SkillDes3",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96 + a.MysteryAtk * 0.96 + (78*$1*0.96)",
			["DescFormula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96 + a.MysteryAtk * 0.96 + (102*$1*0.96)",
		},
		[1610320] = {
			["ID"] = 1610320,
			["Name"] = "Xuetu_SkillDes4",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96 + a.MysteryAtk * 0.96 + (78*$1*0.96)",
			["DescFormula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 3.2 + a.MysteryAtk * 3.2+ (102*$1*3.2)",
		},
		[1610321] = {
			["ID"] = 1610321,
			["Name"] = "Xuetu_SkillDes5",
			["IsFightFormula"] = true,
			["Formula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 0.96 + a.MysteryAtk * 0.96 + (78*$1*0.96)",
			["DescFormula"] = "return  (Min(a.mAtkMin,a.mAtkMax) + a.mAtkMax) / 2 * 4.8 + a.MysteryAtk * 4.8 + (102*$1*4.8)",
		},
		[1610322] = {
			["ID"] = 1610322,
			["Name"] = "Zhanshi_JueJi",
			["IsFightFormula"] = true,
			["Formula"] = "return Round(a.MaxHp * (0.2 + ($1-1)/125))",
			["DescFormula"] = "return 0.2 + ($1-1)/125",
		},
		[1610323] = {
			["ID"] = 1610323,
			["Name"] = "HSQZ_BuffDes",
			["IsFightFormula"] = true,
			["Formula"] = "return  Random(-0.6,-0.3)",
			["DescFormula"] = "return  Random(-0.6,-0.3)",
		},
		[1610325] = {
			["ID"] = 1610325,
			["Name"] = "XGXT_BuffDes",
			["IsFightFormula"] = true,
			["Formula"] = "return  0.01",
			["DescFormula"] = "return  0.01",
		},
		[1610324] = {
			["ID"] = 1610324,
			["Name"] = "Guanzhong_WenyiBuffLv",
			["IsFightFormula"] = false,
			["Formula"] = "local a = $2\nif a.skillList[8000201] == nil then\n    return 1\nend\n    return a.skillList[8000201].SkillLvl",
			["DescFormula"] = "local a = $2\nif a.skillList[8000201] == nil then\n    return 1\nend\n    return a.skillList[8000201].SkillLvl",
		},
		[1610326] = {
			["ID"] = 1610326,
			["Name"] = "LockedHp_Lv001",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.01 * $1 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610327] = {
			["ID"] = 1610327,
			["Name"] = "LockedHp_04",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.4 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610328] = {
			["ID"] = 1610328,
			["Name"] = "LockedHp_005",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.05 * d.MaxHp",
			["DescFormula"] = "",
		},
		[1610330] = {
			["ID"] = 1610330,
			["Name"] = "FengKuangKuiJia_HurtValueTest",
			["IsFightFormula"] = true,
			["Formula"] = "return d.MaxHp * 0.1",
			["DescFormula"] = "return d.MaxHp * 0.1",
		},
		[1610331] = {
			["ID"] = 1610331,
			["Name"] = "FengKuangKuiJia_HurtReviseTest",
			["IsFightFormula"] = true,
			["Formula"] = "return d.MaxHp * 0.1",
			["DescFormula"] = "local delta = $1 * 0.15 + 0.5\nlocal hurtValue = $2\nhurtValue = hurtValue * Min(1, Max(0.25,1 - delta))\n\nreturn hurtValue",
		},
		[1610332] = {
			["ID"] = 1610332,
			["Name"] = "ZhanYiNingJie_AddAtk",
			["IsFightFormula"] = true,
			["Formula"] = "return Max(Min(a.WarProp1,5),1)* (10 + 10 * ($1 + 1))",
			["DescFormula"] = "return Max(Min(a.WarProp1,5),1)* (10 + 10 * ($1 + 1))",
		},
		[1610333] = {
			["ID"] = 1610333,
			["Name"] = "XuanFengLingYu_CD",
			["IsFightFormula"] = false,
			["Formula"] = "return -Min(Floor($2 - $1) * 2 , $3)",
			["DescFormula"] = "return -Min(Floor($2 - $1) * 2 , $3)",
		},
		[1610334] = {
			["ID"] = 1610334,
			["Name"] = "ShunXiZhiLv_CD",
			["IsFightFormula"] = false,
			["Formula"] = "return -2",
			["DescFormula"] = "return -2",
		},
		[1610335] = {
			["ID"] = 1610335,
			["Name"] = "LiMingKaiJia_Shield",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15 * (0.8+ $1* 0.2) * a.MaxHp",
			["DescFormula"] = "return 0.15 * (0.8+ $1* 0.2) * a.MaxHp",
		},
		[1610336] = {
			["ID"] = 1610336,
			["Name"] = "TunShiZhe_QTEShield",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.12 * $1 * a.MaxHp",
			["DescFormula"] = "",
		},
		[1610337] = {
			["ID"] = 1610337,
			["Name"] = "OverHeal",
			["IsFightFormula"] = true,
			["Formula"] = "return GetOverHealValue(FDIn)",
			["DescFormula"] = "",
		},
		[1610338] = {
			["ID"] = 1610338,
			["Name"] = "Warrior_Shield_86061060",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15 * (0.8+ $1* 0.2) * a.MaxHp",
			["DescFormula"] = "",
		},
		[1610339] = {
			["ID"] = 1610339,
			["Name"] = "Warrior_HurtReduce_86061060",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15 * (0.8+ $1* 0.2)",
			["DescFormula"] = "return 0.15 * (0.8+ $1* 0.2)",
		},
		[1610340] = {
			["ID"] = 1610340,
			["Name"] = "Warrior_Shield_86061080",
			["IsFightFormula"] = true,
			["Formula"] = "local Atk = 1\nif GetUseProfessionProp(a) == 3 then\n    Atk = 2*Atk\nend\nif GetUseProfessionProp(a) == 2 then\n    Atk = 1.5*Atk\nend\nif GetUseProfessionProp(a) == 1 then\n    Atk = 1.2*Atk\nend\nreturn Atk* 0.05 * (0.8+ $1* 0.2)* a.MaxHp",
			["DescFormula"] = "return 0.05 * (0.8+ $1* 0.2)* a.MaxHp",
		},
		[1610341] = {
			["ID"] = 1610341,
			["Name"] = "Warrior_Shield_86061061",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.15 * (0.8+ $1* 0.2) * a.MaxHp*(1+GetUseProfessionProp(a)/5)",
			["DescFormula"] = "return 0.15 * (0.8+ $1* 0.2) * a.MaxHp",
		},
		[1610342] = {
			["ID"] = 1610342,
			["Name"] = "Warrior_HurtReduce_86061061",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.3 * (0.8+ $1* 0.2) + GetUseProfessionProp(a)/20",
			["DescFormula"] = "return 0.3 * (0.8+ $1* 0.2)",
		},
		[1612001] = {
			["ID"] = 1612001,
			["Name"] = "Intervention_1",
			["IsFightFormula"] = false,
			["Formula"] = "return 5",
			["DescFormula"] = "",
		},
		[1612002] = {
			["ID"] = 1612002,
			["Name"] = "Intervention_2",
			["IsFightFormula"] = false,
			["Formula"] = "return 100",
			["DescFormula"] = "",
		},
		[1612003] = {
			["ID"] = 1612003,
			["Name"] = "Intervention_3",
			["IsFightFormula"] = false,
			["Formula"] = "return 10000",
			["DescFormula"] = "",
		},
		[1612004] = {
			["ID"] = 1612004,
			["Name"] = "Rec_Price_Cash_Market",
			["IsFightFormula"] = false,
			["Formula"] = "return $3 * $2 / $1",
			["DescFormula"] = "",
		},
		[1612101] = {
			["ID"] = 1612101,
			["Name"] = "Dividend_Guild",
			["IsFightFormula"] = false,
			["Formula"] = "local dividendNum = max($1, 50)\nreturn ($2 + $3 * 0.8) * (1-0.08) / dividendNum",
			["DescFormula"] = "",
		},
		[1612201] = {
			["ID"] = 1612201,
			["Name"] = "ServerLevel_Test",
			["IsFightFormula"] = false,
			["Formula"] = "local delta  = $1 - $2\nif delta > 5 then\n  return 2\nelseif delta >2 then\n return 1.5\nelse\n return 1\nend",
			["DescFormula"] = "",
		},
		[1612299] = {
			["ID"] = 1612299,
			["Name"] = "ServeLevel_ExpTrans",
			["IsFightFormula"] = false,
			["Formula"] = "local exp = $2\nlocal Eexp = 256 * $1* $1 -6000 * $1 +128000\nif exp >= Eexp then\n return 0\nelse\n return 0\nend",
			["DescFormula"] = "",
		},
		[1612301] = {
			["ID"] = 1612301,
			["Name"] = "ModeDieScore",
			["IsFightFormula"] = false,
			["Formula"] = "local step=$1\nlocal score=$1+Max(40,$1*0.5)\n\nreturn score",
			["DescFormula"] = "",
		},
		[1612302] = {
			["ID"] = 1612302,
			["Name"] = "PVP_5v5BattleReward",
			["IsFightFormula"] = false,
			["Formula"] = "local step=$1\nlocal rank=$2\nlocal reward=$1+$2\n\n\n\nreturn reward",
			["DescFormula"] = "",
		},
		[1612303] = {
			["ID"] = 1612303,
			["Name"] = "TotalScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TotalScore = Round(Min(150, 20 + $1 * 0.5))\nreturn TotalScore",
			["DescFormula"] = "",
		},
		[1612304] = {
			["ID"] = 1612304,
			["Name"] = "DebuffScoreSumCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local DebufScoreSum = 0.08 * Min(1,  $1 / 300) * $2\nreturn DebufScoreSum",
			["DescFormula"] = "",
		},
		[1612305] = {
			["ID"] = 1612305,
			["Name"] = "DebuffScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local DebuffScoreX = 0\nif $2 > 0.00001 then\n    DebuffScoreX = $1 / $2 * $3\nend\nreturn DebuffScoreX",
			["DescFormula"] = "",
		},
		[1612306] = {
			["ID"] = 1612306,
			["Name"] = "TDamageScoreSumCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TDamageScoreSum = $1 - $2\nreturn TDamageScoreSum",
			["DescFormula"] = "",
		},
		[1612307] = {
			["ID"] = 1612307,
			["Name"] = "TDamageScore_KillerCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TDamageScore_Killer = 0\nif $2 > 0.00001 then\n    TDamageScore_Killer = Max(1, $1 / $2 * $3)\nend\nreturn TDamageScore_Killer",
			["DescFormula"] = "",
		},
		[1612308] = {
			["ID"] = 1612308,
			["Name"] = "TDamageScore_AssistCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TDamageScore_Assist = 0\nif $2 - $3 > 0.00001 then\n    TDamageScore_Assist = $1 / ($2 -$3) * ($4 - $5)\nend\nreturn TDamageScore_Assist",
			["DescFormula"] = "",
		},
		[1612309] = {
			["ID"] = 1612309,
			["Name"] = "HealScoreToXCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local HealScoreToX = 0.21 * Min( 1, $1 / $2) * $3\nreturn HealScoreToX",
			["DescFormula"] = "",
		},
		[1612310] = {
			["ID"] = 1612310,
			["Name"] = "HealScoreToX_ACalc",
			["IsFightFormula"] = false,
			["Formula"] = "local HealScoreToX_A = 0\nif $2 > 0.00001 then\n    HealScoreToX_A  = $1 / $2 * $3\nend\nreturn HealScoreToX_A",
			["DescFormula"] = "",
		},
		[1612311] = {
			["ID"] = 1612311,
			["Name"] = "BuffScoreToXCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local BuffScoreToX = 0.1 * Min( 1, $1 / 300) * $2\nreturn BuffScoreToX",
			["DescFormula"] = "",
		},
		[1612312] = {
			["ID"] = 1612312,
			["Name"] = "BuffScoreToX_ACalc",
			["IsFightFormula"] = false,
			["Formula"] = "local BuffScoreToX_A = 0\nif $2 > 0 then\n    BuffScoreToX_A = $1 / $2 * $3\nend\nreturn BuffScoreToX_A",
			["DescFormula"] = "",
		},
		[1612313] = {
			["ID"] = 1612313,
			["Name"] = "RDamageScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local RDamageScoreX = $1 - $2 - $3\nreturn RDamageScoreX",
			["DescFormula"] = "",
		},
		[1612314] = {
			["ID"] = 1612314,
			["Name"] = "SufferAndDeathScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local SufferAndDeathScore = Round( $1 / $2 * 8 - $3 * 10)\nreturn SufferAndDeathScore",
			["DescFormula"] = "",
		},
		[1612315] = {
			["ID"] = 1612315,
			["Name"] = "FinalScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local FinalScore = Max(1, $1 + $2)\nreturn FinalScore",
			["DescFormula"] = "",
		},
		[1612316] = {
			["ID"] = 1612316,
			["Name"] = "BuffStrCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local BuffStr = $1 * $2\nreturn BuffStr",
			["DescFormula"] = "",
		},
		[1612317] = {
			["ID"] = 1612317,
			["Name"] = "WinPoint_WinScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local WinPoint_WinScore =  $1/ $2 * $3 * 0.1 * $4\nreturn WinPoint_WinScore",
			["DescFormula"] = "",
		},
		[1612318] = {
			["ID"] = 1612318,
			["Name"] = "WinPoint_FailScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local WinPoint_FailScore =  $1/ $2 * $3 * 0.08 * $4\nreturn WinPoint_FailScore",
			["DescFormula"] = "",
		},
		[1612319] = {
			["ID"] = 1612319,
			["Name"] = "WinPoint_WinCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local WinPoint_Win = Round(($1 + $2 + $3 + $4) * (1 + $5 + $6))\nreturn WinPoint_Win",
			["DescFormula"] = "",
		},
		[1612320] = {
			["ID"] = 1612320,
			["Name"] = "WinPoint_FailCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local WinPoint_Fail = Round(($1 + $2 + $3) * (1 + $5 + $6)) + $4\nreturn WinPoint_Fail",
			["DescFormula"] = "",
		},
		[1612321] = {
			["ID"] = 1612321,
			["Name"] = "MVP_PointCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local MVP_Point = ($1 +$2 +$3 * 0.6) *0.8 +$4 *0.6 \nreturn Round(MVP_Point)",
			["DescFormula"] = "",
		},
		[1612322] = {
			["ID"] = 1612322,
			["Name"] = "Arena_3V3ScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local ScoreHigh =  $1*$1*$1+ $2*$2*$2 + $3*$3*$3\nlocal ScoreLow  =  $1*$1 +$2*$2 + $3*$3\n\nif ScoreLow == 0 then\n   return 0\nend\n\nreturn ScoreHigh/ScoreLow",
			["DescFormula"] = "",
		},
		[1612323] = {
			["ID"] = 1612323,
			["Name"] = "Arena_5V5ScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local ScoreHigh = $1*$1*$1+ $2*$2*$2+ $3*$3*$3 + $4*$4*$4  + $5*$5*$5\nlocal ScoreLow  = $1*$1 +$2*$2 + $3*$3 + $4*$4 + $5*$5\n\nif ScoreLow == 0 then\n   return 0\nend\n\nreturn ScoreHigh/ScoreLow",
			["DescFormula"] = "",
		},
		[1612324] = {
			["ID"] = 1612324,
			["Name"] = "Arena_5V5ForceCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local ForceCalc = ($1*$1*$1+ $2*$2*$2+ $3*$3*$3 + $4*$4*$4  + $5*$5*$5)/( $1*$1 +$2*$2 + $3*$3 + $4*$4 + $5*$5)\nreturn ForceCalc",
			["DescFormula"] = "",
		},
		[1612325] = {
			["ID"] = 1612325,
			["Name"] = "GuildBattleFiled_BackToFightTimeCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local progress = $1 / 2400\nlocal respawnTime = 5 + (progress * progress) * (90 - 5)\nlocal BackToFightTime = math.floor(respawnTime + 0.5)\nreturn BackToFightTime",
			["DescFormula"] = "",
		},
		[1612326] = {
			["ID"] = 1612326,
			["Name"] = "GuildBattleFiled_SourceAreaCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local totalNum = $1 + $2\nlocal towerNum1 = 0 \nlocal towerNum2 = 0\n\nif totalNum >= 80 then\n   towerNum1 = 1 \n   towerNum2 = 2\nelseif totalNum >= 30 then\n   towerNum1 = 1\n   towerNum2 = 1\nend\n\nreturn towerNum1, towerNum2",
			["DescFormula"] = "",
		},
		[1612327] = {
			["ID"] = 1612327,
			["Name"] = "RED_PACKET_MAXLIMIT",
			["IsFightFormula"] = false,
			["Formula"] = "return 6666",
			["DescFormula"] = "",
		},
		[1612328] = {
			["ID"] = 1612328,
			["Name"] = "PVP_12V12Battle",
			["IsFightFormula"] = false,
			["Formula"] = "return $1*10+$2*5",
			["DescFormula"] = "",
		},
		[1612329] = {
			["ID"] = 1612329,
			["Name"] = "Arena_12V12ScoreCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local ScoreHigh = $1*$1*$1+ $2*$2*$2+ $3*$3*$3 + $4*$4*$4  + $5*$5*$5 + $6*$6*$6\nlocal ScoreLow  = $1*$1 +$2*$2 + $3*$3 + $4*$4 + $5*$5 + $6*$6\n\nif ScoreLow == 0 then\n   return 0\nend\n\nreturn ScoreHigh/ScoreLow",
			["DescFormula"] = "",
		},
		[1612330] = {
			["ID"] = 1612330,
			["Name"] = "TwelvePlayer_WinPoint_WinCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TwelvePlayer_WinPoint_Win = Round(($1 + $2 + $3 + $4) * (1 + $5 + $6))+$7\nreturn TwelvePlayer_WinPoint_Win",
			["DescFormula"] = "",
		},
		[1612331] = {
			["ID"] = 1612331,
			["Name"] = "TwelvePlayer_WinPoint_FailCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local TwelvePlayer_WinPoint_Fail = Round(($1 + $2 + $3) * (1 + $5 + $6)) + $4+$7\nreturn TwelvePlayer_WinPoint_Fail",
			["DescFormula"] = "",
		},
		[1620001] = {
			["ID"] = 1620001,
			["Name"] = "NpcWarning_SpeedCalc",
			["IsFightFormula"] = false,
			["Formula"] = "local NpcWarning_Speed = (1 - $1/$2 ) * $3\nreturn NpcWarning_Speed",
			["DescFormula"] = "",
		},
		[1620002] = {
			["ID"] = 1620002,
			["Name"] = "BuffSheildModify_dMaxHp_REBB",
			["IsFightFormula"] = true,
			["Formula"] = "return  d.MaxHp * 0.03",
			["DescFormula"] = "return 0.0045",
		},
		[1620003] = {
			["ID"] = 1620003,
			["Name"] = "RolePlaySheriff_DiceAddBounty",
			["IsFightFormula"] = false,
			["Formula"] = "local rest = $1 - $2\nif rest < 0 then\n   rest = 0\nend\nlocal bounty = rest * 10\nreturn bounty",
			["DescFormula"] = "",
		},
		[1620004] = {
			["ID"] = 1620004,
			["Name"] = "PrimaryPlayerCount",
			["IsFightFormula"] = false,
			["Formula"] = "local scPlayerCount = $1\nlocal clientGraphLevel = $2\nlocal AOINum\n\nif clientGraphLevel == 0 then\n   AOINum = scPlayerCount + 3\nelseif clientGraphLevel == 1 then\n   AOINum = scPlayerCount + 5\nelseif clientGraphLevel == 2 then\n   AOINum = scPlayerCount + 8\nelseif clientGraphLevel == 3 then\n   AOINum = scPlayerCount + 10\nelseif clientGraphLevel == 4 then\n   AOINum = scPlayerCount + 12\nelseif clientGraphLevel == 5 then\n   AOINum = scPlayerCount + 10\nelse\n   return  scPlayerCount + 6\nend\n\nreturn AOINum",
			["DescFormula"] = "",
		},
		[1620005] = {
			["ID"] = 1620005,
			["Name"] = "QuitSanctionTime_3V3",
			["IsFightFormula"] = false,
			["Formula"] = "local CancelTime = $1\nlocal PunishTime\n\nif CancelTime <= 1 then\n   PunishTime = 0\nelse\n   PunishTime = CancelTime * 30 + 10\nend\n\nreturn PunishTime",
			["DescFormula"] = "",
		},
		[1620006] = {
			["ID"] = 1620006,
			["Name"] = "QuitSanctionTime_5V5",
			["IsFightFormula"] = false,
			["Formula"] = "local CancelTime = $1\nlocal PunishTime\n\nif CancelTime == 0 then\n   PunishTime = 0\nelse\n   PunishTime = (CancelTime-1) * 30 + 10\nend\n\nreturn PunishTime",
			["DescFormula"] = "",
		},
		[1620007] = {
			["ID"] = 1620007,
			["Name"] = "ReadyConfirmSanctionTime_3V3",
			["IsFightFormula"] = false,
			["Formula"] = "local CancelTime = $1\nlocal PunishTime\n\nif CancelTime <= 1 then\n   PunishTime = 0\nelse\n   PunishTime = CancelTime * 30 + 10\nend\n\nreturn PunishTime",
			["DescFormula"] = "",
		},
		[1620008] = {
			["ID"] = 1620008,
			["Name"] = "ReadyConfirmSanctionTime_5V5",
			["IsFightFormula"] = false,
			["Formula"] = "local CancelTime = $1\nlocal PunishTime\n\nif CancelTime <= 1 then\n   PunishTime = 0\nelse\n   PunishTime = CancelTime * 30 + 10\nend\n\nreturn PunishTime",
			["DescFormula"] = "",
		},
		[1620009] = {
			["ID"] = 1620009,
			["Name"] = "WeeklyWage_TarotTeam",
			["IsFightFormula"] = false,
			["Formula"] = "local money = 0\nmoney = $1 + $2 * $3 * $4\nreturn money",
			["DescFormula"] = "",
		},
		[1620010] = {
			["ID"] = 1620010,
			["Name"] = "BuffSheildModify_dMaxHp_REBB_HERO",
			["IsFightFormula"] = true,
			["Formula"] = "return  d.MaxHp * 0.03",
			["DescFormula"] = "return 0.0045",
		},
		[1620011] = {
			["ID"] = 1620011,
			["Name"] = "ExtraDamage_DMaxHp",
			["IsFightFormula"] = true,
			["Formula"] = "return 0.04 + (1 - d.Hp /d.MaxHp) * 0.08",
			["DescFormula"] = "",
		},
		[1620012] = {
			["ID"] = 1620012,
			["Name"] = "ELIMINATION_SCORE_FORMULA_ID",
			["IsFightFormula"] = false,
			["Formula"] = "return $1 *((10000 - $2 )+ $3 *100- $4 *100)",
			["DescFormula"] = "",
		},
		[1620013] = {
			["ID"] = 1620013,
			["Name"] = "CHAMPION_GROUP_BATTLE_SCORE_2_SUPPORT_SCORE_FORMULA_ID",
			["IsFightFormula"] = false,
			["Formula"] = "return $1 *2+ $2 *10",
			["DescFormula"] = "",
		},
		[1620014] = {
			["ID"] = 1620014,
			["Name"] = "COMMIT_TRENDING_DEGREE",
			["IsFightFormula"] = false,
			["Formula"] = "return $1*2 + $2 *2 + $3 * 2+ $4 * 5",
			["DescFormula"] = "",
		},
		[1620015] = {
			["ID"] = 1620015,
			["Name"] = "COMMIT_TRENDING_DEGREE_ORDER",
			["IsFightFormula"] = false,
			["Formula"] = "local TimeParams\n\nif $2 <=24 * 60 then\n   TimeParams = 2\nelse\n   TimeParams =1\nend\n\nreturn ( ( $1 / $2 ) * 2 +  ( $4 / $3 ) * 5 ) * TimeParams",
			["DescFormula"] = "",
		},
	},
}

return TopData
