--
-- 表名: $Animation.xlsx  页名：$TurnAround
--

local TopData = {
	data = {
		[0] = {
			["ID"] = 0,
			["Clickwise90"] = "/Game/Arts/Character/Animation/Common/Story/Player/Male/A_Base_M_TurnR90.A_Base_M_TurnR90",
			["Clickwise180"] = "/Game/Arts/Character/Animation/Common/Story/Player/Male/A_Base_M_TurnR180.A_Base_M_TurnR180",
			["AntiClickwise90"] = "/Game/Arts/Character/Animation/Common/Story/Player/Male/A_Base_M_TurnL90.A_Base_M_TurnL90",
			["AntiClickwise180"] = "/Game/Arts/Character/Animation/Common/Story/Player/Male/A_Base_M_TurnL180.A_Base_M_TurnL180",
		},
		[1] = {
			["ID"] = 1,
			["Clickwise90"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/A_Base_F_TurnR90.A_Base_F_TurnR90",
			["Clickwise180"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/A_Base_F_TurnR180.A_Base_F_TurnR180",
			["AntiClickwise90"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/A_Base_F_TurnL90.A_Base_F_TurnL90",
			["AntiClickwise180"] = "/Game/Arts/Character/Animation/Common/Story/Player/Female/A_Base_F_TurnL180.A_Base_F_TurnL180",
		},
	},
}

return TopData
