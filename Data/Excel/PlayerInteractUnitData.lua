--
-- 表名: $PlayerInteractMenu_交互名片菜单表.xlsx  页名：$PlayerInteractUnit_交互单元关系表
--

local TopData = {
	data = {
		["ViewInformation"] = {
			["Key"] = "ViewInformation",
			["Rank"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954552320'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["SendMessage"] = {
			["Key"] = "SendMessage",
			["Rank"] = 2,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954552576'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["Addfriend"] = {
			["Key"] = "Addfriend",
			["Rank"] = 3,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954552832'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 2,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["DeleteFriend"] = {
			["Key"] = "DeleteFriend",
			["Rank"] = 4,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954553088'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 1,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteGuild"] = {
			["Key"] = "InviteGuild",
			["Rank"] = 5,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954553344'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 1,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 2,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyGuild"] = {
			["Key"] = "ApplyGuild",
			["Rank"] = 6,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954553600'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 2,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 1,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 2,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteTeam"] = {
			["Key"] = "InviteTeam",
			["Rank"] = 7,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954553856'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 1,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 2,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 2,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyTeam"] = {
			["Key"] = "ApplyTeam",
			["Rank"] = 8,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954554112'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 2,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 1,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 2,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyMerge"] = {
			["Key"] = "ApplyMerge",
			["Rank"] = 8,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954554368'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 1,
			["IsTeamGroupLeader"] = 2,
			["HasTeamGroup"] = 2,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 1,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["AppointTeamLeader"] = {
			["Key"] = "AppointTeamLeader",
			["Rank"] = 9,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954554624'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 1,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 2,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 2,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["SwitchTeamLeader"] = {
			["Key"] = "SwitchTeamLeader",
			["Rank"] = 10,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954554880'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 1,
			["IsTeamGroupLeader"] = 2,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 2,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 2,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyTeamLeader"] = {
			["Key"] = "ApplyTeamLeader",
			["Rank"] = 11,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954555136'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 1,
			["IsTeamLeader"] = 2,
			["IsTeamGroupLeader"] = 2,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 1,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 1,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["KickoffTeam"] = {
			["Key"] = "KickoffTeam",
			["Rank"] = 12,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954555392'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 1,
			["IsTeamGroupLeader"] = 2,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 1,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 1,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["LeaveTeam"] = {
			["Key"] = "LeaveTeam",
			["Rank"] = 13,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954555648'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 1,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteTeamGroup"] = {
			["Key"] = "InviteTeamGroup",
			["Rank"] = 14,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954555904'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 1,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 2,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 2,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyTeamGroup"] = {
			["Key"] = "ApplyTeamGroup",
			["Rank"] = 15,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954556160'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 2,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 1,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["SwitchTeamGroupLeader"] = {
			["Key"] = "SwitchTeamGroupLeader",
			["Rank"] = 16,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954556416'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 1,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 2,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 1,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["MoveMember"] = {
			["Key"] = "MoveMember",
			["Rank"] = 17,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954556672'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 1,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteTeamJoinGroup"] = {
			["Key"] = "InviteTeamJoinGroup",
			["Rank"] = 18,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954555904'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 1,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 1,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["QuitTeamGroup"] = {
			["Key"] = "QuitTeamGroup",
			["Rank"] = 21,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954557184'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 1,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["KickoffTeamGroup"] = {
			["Key"] = "KickoffTeamGroup",
			["Rank"] = 22,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954557440'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 1,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 1,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["SendGift"] = {
			["Key"] = "SendGift",
			["Rank"] = 23,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954557696'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 2,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["SendTeaRoomGift"] = {
			["Key"] = "SendTeaRoomGift",
			["Rank"] = 23,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954557952'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 1,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["TransferGuildLeader"] = {
			["Key"] = "TransferGuildLeader",
			["Rank"] = 24,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954558208'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 1,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 2,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["LeagueApply"] = {
			["Key"] = "LeagueApply",
			["Rank"] = 24,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954558464'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 1,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 2,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 1,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["KickoffGuild"] = {
			["Key"] = "KickoffGuild",
			["Rank"] = 25,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954558720'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 1,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 1,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ChangePosition"] = {
			["Key"] = "ChangePosition",
			["Rank"] = 26,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954558976'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 1,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 1,
		},
		["ReportPlayer"] = {
			["Key"] = "ReportPlayer",
			["Rank"] = 27,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954559232'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["MuteVoice"] = {
			["Key"] = "MuteVoice",
			["Rank"] = 28,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954559488'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222994944'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 1,
			["TargetIsInSameTeam"] = 1,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 2,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["FriendNameModify"] = {
			["Key"] = "FriendNameModify",
			["Rank"] = 29,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954559744'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 1,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteTeamUp"] = {
			["Key"] = "InviteTeamUp",
			["Rank"] = 30,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954560000'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988800'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 2,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 2,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 2,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 2,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["BlockPlayer"] = {
			["Key"] = "BlockPlayer",
			["Rank"] = 31,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954560256'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222995712'),
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["RemoveFromEnmityList"] = {
			["Key"] = "RemoveFromEnmityList",
			["Rank"] = 32,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954560512'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 1,
			["TargetIsInFoeList"] = 1,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 2,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["InviteBattle"] = {
			["Key"] = "InviteBattle",
			["Rank"] = 33,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954560768'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 1,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 1,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["WhisperToGuildChairMan"] = {
			["Key"] = "WhisperToGuildChairMan",
			["Rank"] = 33,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954561024'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["PersonalHomePage"] = {
			["Key"] = "PersonalHomePage",
			["Rank"] = 34,
			["Name"] = Game.TableDataManager:GetLangStr('str_36971883805696'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ApplyResponseGuild"] = {
			["Key"] = "ApplyResponseGuild",
			["Rank"] = 35,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954561536'),
			["NameState1"] = Game.TableDataManager:GetLangStr('str_41920222988288'),
			["OpenLv"] = 1,
			["HasGuild"] = 2,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 1,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 1,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["KickoffTarotTeam"] = {
			["Key"] = "KickoffTarotTeam",
			["Rank"] = 36,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954561792'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 1,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["TransferTarotTeamLeader"] = {
			["Key"] = "TransferTarotTeamLeader",
			["Rank"] = 37,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954554880'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 1,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["ReportClub"] = {
			["Key"] = "ReportClub",
			["Rank"] = 38,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954562304'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["VisitManor"] = {
			["Key"] = "VisitManor",
			["Rank"] = 39,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954562560'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
		["KickoffManor"] = {
			["Key"] = "KickoffManor",
			["Rank"] = 40,
			["Name"] = Game.TableDataManager:GetLangStr('str_41919954562816'),
			["NameState1"] = "",
			["OpenLv"] = 1,
			["HasGuild"] = 0,
			["HasTeam"] = 0,
			["IsTeamLeader"] = 0,
			["IsTeamGroupLeader"] = 0,
			["HasTeamGroup"] = 0,
			["IsCanHandleEnterGuildApply"] = 0,
			["IsGuildChairMan"] = 0,
			["IsCanKickGuildPlayer"] = 0,
			["PlayerInSafeZone"] = 0,
			["TargetIsFriend"] = 0,
			["TargetHasGuild"] = 0,
			["TargetHasTeam"] = 0,
			["TargetIsTeamLeader"] = 0,
			["TargetIsTeamGroupLeader"] = 0,
			["TargetIsInSameGroup"] = 0,
			["TargetIsInSameTeam"] = 0,
			["TargetHasTeamGroup"] = 0,
			["TargetIsGuildChairMan"] = 0,
			["TargetIsInSeamGuild"] = 0,
			["TargetIsInEnmityList"] = 0,
			["TargetIsInFoeList"] = 0,
			["TargetInSafeZone"] = 0,
			["TargetLimitRelation"] = 1,
			["CheckFunc"] = 0,
			["SceneTypeBlackList"] = {},
			["TagetInSameTeaRoom"] = 0,
			["TagetNotInSameTeaRoom"] = 0,
			["IsTarotTeamLeader"] = 0,
			["TargetGuildIsInResponse"] = 0,
			["IsCanHandleSetGuildPosition"] = 0,
		},
	},
}

return TopData
