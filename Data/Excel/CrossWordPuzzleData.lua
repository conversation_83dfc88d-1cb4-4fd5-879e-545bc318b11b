--
-- 表名: CrossWordPuzzleData后处理
--

local TopData = {
    PuzzleIDList = {1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090
    },
    data = {
        [1001] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081537'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081538'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081539'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081541'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081543'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081544'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1001, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738516993'),Game.TableDataManager:GetLangStr('str_35735738516994'),Game.TableDataManager:GetLangStr('str_35735738516995'),Game.TableDataManager:GetLangStr('str_35735738516996'),Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735738516998'),Game.TableDataManager:GetLangStr('str_35735470081538'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470081539'),}, 
            ['TipsID'] = 1, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933210624'),
        },
        [1002] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081793'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081794'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081795'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081796'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081797'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081799'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081800'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081801'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1002, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081794'),Game.TableDataManager:GetLangStr('str_35735738517250'),Game.TableDataManager:GetLangStr('str_35735470081796'),Game.TableDataManager:GetLangStr('str_35735738517252'),Game.TableDataManager:GetLangStr('str_35735470081795'),Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735738517255'),Game.TableDataManager:GetLangStr('str_54632789328128'),Game.TableDataManager:GetLangStr('str_35735470081797'),}, 
            ['TipsID'] = 2, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933210880'),
        },
        [1003] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082049'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082050'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082051'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082052'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082053'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082055'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082056'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082057'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082058'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1003, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082049'),Game.TableDataManager:GetLangStr('str_35735738517506'),Game.TableDataManager:GetLangStr('str_35735470082058'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470082053'),Game.TableDataManager:GetLangStr('str_35735738517510'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735470082051'),Game.TableDataManager:GetLangStr('str_35735738517513'),}, 
            ['TipsID'] = 3, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933211136'),
        },
        [1004] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082305'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082306'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082307'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082308'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082309'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082310'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082311'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082312'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082313'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1004, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738517761'),Game.TableDataManager:GetLangStr('str_35735470082308'),Game.TableDataManager:GetLangStr('str_35735738517763'),Game.TableDataManager:GetLangStr('str_35735738517764'),Game.TableDataManager:GetLangStr('str_35735738517765'),Game.TableDataManager:GetLangStr('str_35735470082309'),Game.TableDataManager:GetLangStr('str_35735738517767'),Game.TableDataManager:GetLangStr('str_35735738517768'),Game.TableDataManager:GetLangStr('str_35735470082312'),}, 
            ['TipsID'] = 4, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933211392'),
        },
        [1005] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082561'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082562'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54632789338624'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082564'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082565'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082566'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082568'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1005, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082561'),Game.TableDataManager:GetLangStr('str_35735738518018'),Game.TableDataManager:GetLangStr('str_35735738518019'),Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735470082565'),Game.TableDataManager:GetLangStr('str_35735470081543'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735738518024'),Game.TableDataManager:GetLangStr('str_35735738518025'),}, 
            ['TipsID'] = 5, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933211648'),
        },
        [1006] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082817'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082818'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082819'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082820'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082821'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082822'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082823'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082824'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082825'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082826'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1006, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738518273'),Game.TableDataManager:GetLangStr('str_35735470082820'),Game.TableDataManager:GetLangStr('str_35735738518275'),Game.TableDataManager:GetLangStr('str_35735470082821'),Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735470082562'),Game.TableDataManager:GetLangStr('str_35735738518279'),Game.TableDataManager:GetLangStr('str_38071663868928'),Game.TableDataManager:GetLangStr('str_35735470082824'),}, 
            ['TipsID'] = 6, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933211904'),
        },
        [1007] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082307'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738516998'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083075'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083078'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083080'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083081'),}, }, 
            ['PuzzleID'] = 1007, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738516998'),Game.TableDataManager:GetLangStr('str_35735738518530'),Game.TableDataManager:GetLangStr('str_35735738518531'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735470083080'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735470083078'),Game.TableDataManager:GetLangStr('str_35735738518536'),Game.TableDataManager:GetLangStr('str_35735470083079'),}, 
            ['TipsID'] = 7, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933212160'),
        },
        [1008] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083329'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083330'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083331'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083332'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083333'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517506'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083335'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083336'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083337'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083338'),}, }, 
            ['PuzzleID'] = 1008, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083332'),Game.TableDataManager:GetLangStr('str_35735470082049'),Game.TableDataManager:GetLangStr('str_35735738518787'),Game.TableDataManager:GetLangStr('str_35735738518788'),Game.TableDataManager:GetLangStr('str_35735470083333'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735470083337'),Game.TableDataManager:GetLangStr('str_35735738518792'),Game.TableDataManager:GetLangStr('str_35735470083335'),}, 
            ['TipsID'] = 8, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933212416'),
        },
        [1009] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083585'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789322496'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082053'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083589'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083590'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083591'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083592'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083592'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083594'),}, }, 
            ['PuzzleID'] = 1009, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735738519042'),Game.TableDataManager:GetLangStr('str_35735470083592'),Game.TableDataManager:GetLangStr('str_35735738519044'),Game.TableDataManager:GetLangStr('str_35735470082053'),Game.TableDataManager:GetLangStr('str_35735738519046'),Game.TableDataManager:GetLangStr('str_35735470083591'),Game.TableDataManager:GetLangStr('str_35735738519048'),Game.TableDataManager:GetLangStr('str_35735738518792'),}, 
            ['TipsID'] = 9, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933212672'),
        },
        [1010] = {
            ['CrossWordList'] = {{['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082826'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39033468183808'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083843'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083844'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083845'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083846'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083847'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083848'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083849'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083850'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083851'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1010, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738519297'),Game.TableDataManager:GetLangStr('str_35735470083845'),Game.TableDataManager:GetLangStr('str_35735470083850'),Game.TableDataManager:GetLangStr('str_39033468183808'),Game.TableDataManager:GetLangStr('str_35735738519301'),Game.TableDataManager:GetLangStr('str_35735470083848'),Game.TableDataManager:GetLangStr('str_35735738519303'),Game.TableDataManager:GetLangStr('str_35735738519304'),Game.TableDataManager:GetLangStr('str_35735738519305'),}, 
            ['TipsID'] = 10, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933212928'),
        },
        [1011] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084098'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084101'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084102'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084103'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_13609140749824'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082825'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084106'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084107'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084108'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084109'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1011, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083079'),Game.TableDataManager:GetLangStr('str_54632789338624'),Game.TableDataManager:GetLangStr('str_35735470084106'),Game.TableDataManager:GetLangStr('str_35735470084109'),Game.TableDataManager:GetLangStr('str_35735470084101'),Game.TableDataManager:GetLangStr('str_35735470081544'),Game.TableDataManager:GetLangStr('str_35735470082825'),Game.TableDataManager:GetLangStr('str_35735738519560'),Game.TableDataManager:GetLangStr('str_54632789321728'),}, 
            ['TipsID'] = 11, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933213184'),
        },
        [1012] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084353'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084354'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084355'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082310'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084357'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082824'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084361'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1012, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084353'),Game.TableDataManager:GetLangStr('str_35735738519810'),Game.TableDataManager:GetLangStr('str_35735470082824'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735738518531'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470084357'),Game.TableDataManager:GetLangStr('str_35735470084361'),Game.TableDataManager:GetLangStr('str_35735470084355'),}, 
            ['TipsID'] = 12, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933213440'),
        },
        [1013] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084609'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084610'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084611'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084612'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084614'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084618'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1013, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084610'),Game.TableDataManager:GetLangStr('str_35735738520066'),Game.TableDataManager:GetLangStr('str_35735738519046'),Game.TableDataManager:GetLangStr('str_35735470084611'),Game.TableDataManager:GetLangStr('str_35735470084613'),Game.TableDataManager:GetLangStr('str_35735738520070'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735738520072'),Game.TableDataManager:GetLangStr('str_35735470084614'),}, 
            ['TipsID'] = 13, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933213696'),
        },
        [1014] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084865'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084866'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084867'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084868'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084355'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084872'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789322496'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084874'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517506'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084876'),}, }, 
            ['PuzzleID'] = 1014, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084865'),Game.TableDataManager:GetLangStr('str_35735470084868'),Game.TableDataManager:GetLangStr('str_35735738520323'),Game.TableDataManager:GetLangStr('str_35735470084866'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470084867'),Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735738520328'),Game.TableDataManager:GetLangStr('str_35735470084876'),}, 
            ['TipsID'] = 14, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933213952'),
        },
        [1015] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085121'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085122'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081543'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085127'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518019'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1015, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470085122'),Game.TableDataManager:GetLangStr('str_35735470085121'),Game.TableDataManager:GetLangStr('str_35735738520579'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735738518531'),Game.TableDataManager:GetLangStr('str_35735738520582'),Game.TableDataManager:GetLangStr('str_35735470081543'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470085127'),}, 
            ['TipsID'] = 15, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933214208'),
        },
        [1016] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085377'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085379'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085380'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085381'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085382'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085383'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789320960'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54632789316096'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085387'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085388'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_16975589816064'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085390'),}, }, 
            ['PuzzleID'] = 1016, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735470085383'),Game.TableDataManager:GetLangStr('str_35735470085390'),Game.TableDataManager:GetLangStr('str_35735470085380'),Game.TableDataManager:GetLangStr('str_35735470085381'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_16975589816064'),Game.TableDataManager:GetLangStr('str_35735738520840'),Game.TableDataManager:GetLangStr('str_54632789320960'),}, 
            ['TipsID'] = 16, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933214464'),
        },
        [1017] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085633'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082312'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085637'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085638'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085639'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085641'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085642'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085643'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085644'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517768'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085646'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085647'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1017, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081542'),Game.TableDataManager:GetLangStr('str_35735470085642'),Game.TableDataManager:GetLangStr('str_35735470085646'),Game.TableDataManager:GetLangStr('str_35735470085638'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735470085639'),Game.TableDataManager:GetLangStr('str_35735470085641'),Game.TableDataManager:GetLangStr('str_35735470085643'),Game.TableDataManager:GetLangStr('str_35735470082312'),}, 
            ['TipsID'] = 17, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933214720'),
        },
        [1018] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085889'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085891'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085892'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085893'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789322496'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085896'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084101'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085899'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085901'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083844'),}, }, 
            ['PuzzleID'] = 1018, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735470085899'),Game.TableDataManager:GetLangStr('str_35735470084101'),Game.TableDataManager:GetLangStr('str_35735470085893'),Game.TableDataManager:GetLangStr('str_35735470083079'),Game.TableDataManager:GetLangStr('str_35735470085891'),Game.TableDataManager:GetLangStr('str_35735470085901'),Game.TableDataManager:GetLangStr('str_35735470081798'),}, 
            ['TipsID'] = 18, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933214976'),
        },
        [1019] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086145'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086147'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086149'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518530'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789316096'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086153'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086154'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083081'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1019, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470086147'),Game.TableDataManager:GetLangStr('str_54632789316096'),Game.TableDataManager:GetLangStr('str_35735738518530'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735470086149'),Game.TableDataManager:GetLangStr('str_35735470086154'),Game.TableDataManager:GetLangStr('str_35735470084357'),Game.TableDataManager:GetLangStr('str_35735470083079'),Game.TableDataManager:GetLangStr('str_35735470083076'),}, 
            ['TipsID'] = 19, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933215232'),
        },
        [1020] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086401'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085127'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084866'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520323'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084867'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086408'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084868'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086411'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086413'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086414'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1020, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470085127'),Game.TableDataManager:GetLangStr('str_35735470084867'),Game.TableDataManager:GetLangStr('str_35735738520323'),Game.TableDataManager:GetLangStr('str_35735470084866'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470086408'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735470086411'),Game.TableDataManager:GetLangStr('str_35735470081542'),}, 
            ['TipsID'] = 20, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933215488'),
        },
        [1021] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517506'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086658'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086659'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789321984'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086661'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086662'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519810'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789351168'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086668'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1021, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470086658'),Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_35735470086668'),Game.TableDataManager:GetLangStr('str_54632789321984'),Game.TableDataManager:GetLangStr('str_35735738522117'),Game.TableDataManager:GetLangStr('str_35735738519810'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_54632789351168'),Game.TableDataManager:GetLangStr('str_35735470086661'),}, 
            ['TipsID'] = 21, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933215744'),
        },
        [1022] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086913'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086914'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086915'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086916'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086917'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086918'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086919'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086920'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086921'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086922'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086923'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1022, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470086914'),Game.TableDataManager:GetLangStr('str_35735470086921'),Game.TableDataManager:GetLangStr('str_35735470086918'),Game.TableDataManager:GetLangStr('str_35735470086920'),Game.TableDataManager:GetLangStr('str_35735470086916'),Game.TableDataManager:GetLangStr('str_35735470086922'),Game.TableDataManager:GetLangStr('str_35735470086919'),Game.TableDataManager:GetLangStr('str_35735470086915'),Game.TableDataManager:GetLangStr('str_35735470086917'),}, 
            ['TipsID'] = 22, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933216000'),
        },
        [1023] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086149'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087170'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082312'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082820'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082821'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082824'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087175'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1023, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738522625'),Game.TableDataManager:GetLangStr('str_35735470082821'),Game.TableDataManager:GetLangStr('str_35735470082824'),Game.TableDataManager:GetLangStr('str_35735470087175'),Game.TableDataManager:GetLangStr('str_35735738522629'),Game.TableDataManager:GetLangStr('str_35735470082820'),Game.TableDataManager:GetLangStr('str_35735470087170'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735470082312'),}, 
            ['TipsID'] = 23, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933216256'),
        },
        [1024] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087425'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087427'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087428'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1024, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738517506'),Game.TableDataManager:GetLangStr('str_35735738516998'),Game.TableDataManager:GetLangStr('str_35735738522883'),Game.TableDataManager:GetLangStr('str_35735470087427'),Game.TableDataManager:GetLangStr('str_35735738522885'),Game.TableDataManager:GetLangStr('str_35735470087425'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735470086145'),}, 
            ['TipsID'] = 24, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933216512'),
        },
        [1025] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36628554847232'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36628554847232'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087683'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087684'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087685'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1025, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36628554847232'),Game.TableDataManager:GetLangStr('str_35735738517250'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470087685'),Game.TableDataManager:GetLangStr('str_35735738523141'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735738523143'),Game.TableDataManager:GetLangStr('str_35735738523144'),Game.TableDataManager:GetLangStr('str_35735470087683'),}, 
            ['TipsID'] = 25, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933216768'),
        },
        [1026] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518536'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738516998'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083078'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083080'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082817'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084098'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1026, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_35735738523394'),Game.TableDataManager:GetLangStr('str_35735738518536'),Game.TableDataManager:GetLangStr('str_35735738523396'),Game.TableDataManager:GetLangStr('str_35735738523397'),Game.TableDataManager:GetLangStr('str_35735738516998'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735738523400'),Game.TableDataManager:GetLangStr('str_35735470087425'),}, 
            ['TipsID'] = 26, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933217024'),
        },
        [1027] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517506'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738516993'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520582'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088198'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738523141'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082310'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1027, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738516993'),Game.TableDataManager:GetLangStr('str_35735470082310'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735738520582'),Game.TableDataManager:GetLangStr('str_35735738523141'),Game.TableDataManager:GetLangStr('str_35735738517506'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470088198'),Game.TableDataManager:GetLangStr('str_35735470087426'),}, 
            ['TipsID'] = 27, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933217280'),
        },
        [1028] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088449'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088451'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088452'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1028, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735738523906'),Game.TableDataManager:GetLangStr('str_35735470088452'),Game.TableDataManager:GetLangStr('str_35735470088451'),Game.TableDataManager:GetLangStr('str_35735738523909'),Game.TableDataManager:GetLangStr('str_35735738523910'),Game.TableDataManager:GetLangStr('str_35735738523911'),Game.TableDataManager:GetLangStr('str_35735738523912'),Game.TableDataManager:GetLangStr('str_35735470087426'),}, 
            ['TipsID'] = 28, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933217536'),
        },
        [1029] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088705'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518275'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084611'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088709'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088710'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088711'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088712'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088713'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088714'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088715'),}, }, 
            ['PuzzleID'] = 1029, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084611'),Game.TableDataManager:GetLangStr('str_35735470088715'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470088711'),Game.TableDataManager:GetLangStr('str_35735470088705'),Game.TableDataManager:GetLangStr('str_35735470088713'),Game.TableDataManager:GetLangStr('str_35735470088714'),Game.TableDataManager:GetLangStr('str_35735470088710'),Game.TableDataManager:GetLangStr('str_35735470088709'),}, 
            ['TipsID'] = 29, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933217792'),
        },
        [1030] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084611'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088713'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084354'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088965'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789320960'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088967'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088968'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1030, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084354'),Game.TableDataManager:GetLangStr('str_35735470088968'),Game.TableDataManager:GetLangStr('str_35735470088965'),Game.TableDataManager:GetLangStr('str_35735470088713'),Game.TableDataManager:GetLangStr('str_35735470084614'),Game.TableDataManager:GetLangStr('str_35735738524422'),Game.TableDataManager:GetLangStr('str_35735470088710'),Game.TableDataManager:GetLangStr('str_35735470088967'),Game.TableDataManager:GetLangStr('str_54632789320960'),}, 
            ['TipsID'] = 30, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933218048'),
        },
        [1031] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088710'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089218'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089219'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089220'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089222'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081796'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089225'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39033468098304'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1031, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735470081796'),Game.TableDataManager:GetLangStr('str_35735470088710'),Game.TableDataManager:GetLangStr('str_39033468098304'),Game.TableDataManager:GetLangStr('str_35735470089222'),Game.TableDataManager:GetLangStr('str_35735470089218'),Game.TableDataManager:GetLangStr('str_35735470089220'),Game.TableDataManager:GetLangStr('str_35735470089219'),Game.TableDataManager:GetLangStr('str_35735738524681'),}, 
            ['TipsID'] = 31, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933218304'),
        },
        [1032] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089473'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089474'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089475'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089476'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089477'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089479'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082823'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089481'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089482'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089483'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089484'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089485'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089486'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1032, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470089475'),Game.TableDataManager:GetLangStr('str_35735470089479'),Game.TableDataManager:GetLangStr('str_35735470089483'),Game.TableDataManager:GetLangStr('str_35735470089477'),Game.TableDataManager:GetLangStr('str_35735470089485'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470089474'),Game.TableDataManager:GetLangStr('str_35735470089482'),Game.TableDataManager:GetLangStr('str_35735470082823'),}, 
            ['TipsID'] = 32, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933218560'),
        },
        [1033] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089729'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089730'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089731'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089732'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089733'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089734'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089735'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084098'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089737'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089739'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089740'),}, }, 
            ['PuzzleID'] = 1033, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084098'),Game.TableDataManager:GetLangStr('str_35735470089734'),Game.TableDataManager:GetLangStr('str_35735738525187'),Game.TableDataManager:GetLangStr('str_35735470089739'),Game.TableDataManager:GetLangStr('str_35735470089732'),Game.TableDataManager:GetLangStr('str_35735470085382'),Game.TableDataManager:GetLangStr('str_35735470089731'),Game.TableDataManager:GetLangStr('str_35735738519046'),Game.TableDataManager:GetLangStr('str_35735470089733'),}, 
            ['TipsID'] = 33, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933218816'),
        },
        [1034] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517506'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089988'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089989'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518536'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089991'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089992'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089993'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089994'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089995'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089996'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1034, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470089995'),Game.TableDataManager:GetLangStr('str_35735470089988'),Game.TableDataManager:GetLangStr('str_35735738518536'),Game.TableDataManager:GetLangStr('str_35735470089992'),Game.TableDataManager:GetLangStr('str_35735470085382'),Game.TableDataManager:GetLangStr('str_35735470089994'),Game.TableDataManager:GetLangStr('str_35735470085127'),Game.TableDataManager:GetLangStr('str_35735470081542'),Game.TableDataManager:GetLangStr('str_35735470089991'),}, 
            ['TipsID'] = 34, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933219072'),
        },
        [1035] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090241'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090242'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090243'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090244'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090245'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089733'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090247'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090248'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090250'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090251'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1035, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470090245'),Game.TableDataManager:GetLangStr('str_35735470081542'),Game.TableDataManager:GetLangStr('str_35735470090247'),Game.TableDataManager:GetLangStr('str_35735470090248'),Game.TableDataManager:GetLangStr('str_35735738525701'),Game.TableDataManager:GetLangStr('str_35735738525702'),Game.TableDataManager:GetLangStr('str_35735470090244'),Game.TableDataManager:GetLangStr('str_35735470089733'),Game.TableDataManager:GetLangStr('str_35735470090242'),}, 
            ['TipsID'] = 35, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933219328'),
        },
        [1036] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084867'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089733'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789321984'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089729'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520840'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086661'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090504'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090505'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086662'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090243'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1036, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470085382'),Game.TableDataManager:GetLangStr('str_35735470086661'),Game.TableDataManager:GetLangStr('str_35735470084613'),Game.TableDataManager:GetLangStr('str_54632789360640'),Game.TableDataManager:GetLangStr('str_35735470084867'),Game.TableDataManager:GetLangStr('str_35735738525701'),Game.TableDataManager:GetLangStr('str_35735470086662'),Game.TableDataManager:GetLangStr('str_35735470090504'),Game.TableDataManager:GetLangStr('str_54632789321984'),}, 
            ['TipsID'] = 36, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933219584'),
        },
        [1037] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082561'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090755'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090756'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090757'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090758'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090759'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520840'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083846'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1037, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738526209'),Game.TableDataManager:GetLangStr('str_35735738526210'),Game.TableDataManager:GetLangStr('str_35735470082561'),Game.TableDataManager:GetLangStr('str_35735470090756'),Game.TableDataManager:GetLangStr('str_35735738526213'),Game.TableDataManager:GetLangStr('str_35735738520840'),Game.TableDataManager:GetLangStr('str_35735470083338'),Game.TableDataManager:GetLangStr('str_35735470090758'),Game.TableDataManager:GetLangStr('str_35735470090755'),}, 
            ['TipsID'] = 37, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933219840'),
        },
        [1038] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091009'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086145'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091011'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091012'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089739'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091016'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526209'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789316096'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086154'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091020'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1038, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738526465'),Game.TableDataManager:GetLangStr('str_35735470091016'),Game.TableDataManager:GetLangStr('str_35735470086154'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735738519046'),Game.TableDataManager:GetLangStr('str_35735738526470'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735470086145'),Game.TableDataManager:GetLangStr('str_54632789316096'),}, 
            ['TipsID'] = 38, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933220096'),
        },
        [1039] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091265'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091266'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091267'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091268'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_39033468123392'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084101'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091273'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1039, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470091268'),Game.TableDataManager:GetLangStr('str_35735738526722'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470083847'),Game.TableDataManager:GetLangStr('str_39033468123392'),Game.TableDataManager:GetLangStr('str_35735470089739'),Game.TableDataManager:GetLangStr('str_35735470091267'),Game.TableDataManager:GetLangStr('str_35735738526728'),Game.TableDataManager:GetLangStr('str_35735738517000'),}, 
            ['TipsID'] = 39, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933220352'),
        },
        [1040] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082826'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085127'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091523'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091526'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091527'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091528'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526465'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1040, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083844'),Game.TableDataManager:GetLangStr('str_35735738518019'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735738526980'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470091527'),Game.TableDataManager:GetLangStr('str_35735470091523'),Game.TableDataManager:GetLangStr('str_35735738526465'),Game.TableDataManager:GetLangStr('str_35735470085127'),}, 
            ['TipsID'] = 40, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933220608'),
        },
        [1041] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39033468183808'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083845'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086154'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091020'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083847'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091782'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091783'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091784'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091785'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091786'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091787'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1041, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738527233'),Game.TableDataManager:GetLangStr('str_35735470091020'),Game.TableDataManager:GetLangStr('str_35735470083847'),Game.TableDataManager:GetLangStr('str_35735470091786'),Game.TableDataManager:GetLangStr('str_35735738527237'),Game.TableDataManager:GetLangStr('str_35735470091784'),Game.TableDataManager:GetLangStr('str_35735470091782'),Game.TableDataManager:GetLangStr('str_35735470086153'),Game.TableDataManager:GetLangStr('str_39033468183808'),}, 
            ['TipsID'] = 41, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933220864'),
        },
        [1042] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092033'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789322496'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092035'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092036'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092037'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082053'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084103'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083591'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092042'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092043'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083592'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1042, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735470092035'),Game.TableDataManager:GetLangStr('str_35735470083592'),Game.TableDataManager:GetLangStr('str_35735738519044'),Game.TableDataManager:GetLangStr('str_35735470082053'),Game.TableDataManager:GetLangStr('str_35735470084103'),Game.TableDataManager:GetLangStr('str_35735470083591'),Game.TableDataManager:GetLangStr('str_35735738519048'),Game.TableDataManager:GetLangStr('str_35735738518792'),}, 
            ['TipsID'] = 42, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933221120'),
        },
        [1043] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092289'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789316096'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517510'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092293'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092294'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092295'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092296'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092297'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092299'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092300'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1043, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470090249'),Game.TableDataManager:GetLangStr('str_35735470082307'),Game.TableDataManager:GetLangStr('str_35735470092293'),Game.TableDataManager:GetLangStr('str_35735738517510'),Game.TableDataManager:GetLangStr('str_35735470092295'),Game.TableDataManager:GetLangStr('str_35735738527750'),Game.TableDataManager:GetLangStr('str_35735470089996'),Game.TableDataManager:GetLangStr('str_54632789316096'),Game.TableDataManager:GetLangStr('str_35735470082054'),}, 
            ['TipsID'] = 43, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933221376'),
        },
        [1044] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092545'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092546'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082564'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082565'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520582'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084361'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084357'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082824'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092554'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1044, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082824'),Game.TableDataManager:GetLangStr('str_35735470082565'),Game.TableDataManager:GetLangStr('str_35735470090249'),Game.TableDataManager:GetLangStr('str_35735738528004'),Game.TableDataManager:GetLangStr('str_35735470084357'),Game.TableDataManager:GetLangStr('str_35735738528006'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735738518531'),Game.TableDataManager:GetLangStr('str_35735738520582'),}, 
            ['TipsID'] = 44, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933221632'),
        },
        [1045] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092801'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092802'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092803'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092804'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092805'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092806'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_13609140749824'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_34292092637696'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082562'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092810'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092811'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1045, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470092801'),Game.TableDataManager:GetLangStr('str_13609140749824'),Game.TableDataManager:GetLangStr('str_35735470090249'),Game.TableDataManager:GetLangStr('str_35735470082565'),Game.TableDataManager:GetLangStr('str_35735738528261'),Game.TableDataManager:GetLangStr('str_35735470092811'),Game.TableDataManager:GetLangStr('str_35735738520840'),Game.TableDataManager:GetLangStr('str_35735470092805'),Game.TableDataManager:GetLangStr('str_35735470092810'),}, 
            ['TipsID'] = 45, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933221888'),
        },
        [1046] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082823'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082825'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789322496'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085896'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084101'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085899'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093065'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085901'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083844'),}, }, 
            ['PuzzleID'] = 1046, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083079'),Game.TableDataManager:GetLangStr('str_35735470085899'),Game.TableDataManager:GetLangStr('str_35735738528515'),Game.TableDataManager:GetLangStr('str_35735470093065'),Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_35735470084101'),Game.TableDataManager:GetLangStr('str_35735470081801'),Game.TableDataManager:GetLangStr('str_35735470085901'),}, 
            ['TipsID'] = 46, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933222144'),
        },
        [1047] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087425'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738523396'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093315'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1047, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470087425'),Game.TableDataManager:GetLangStr('str_35735738519810'),Game.TableDataManager:GetLangStr('str_35735738528771'),Game.TableDataManager:GetLangStr('str_35735738516998'),Game.TableDataManager:GetLangStr('str_54632789322496'),Game.TableDataManager:GetLangStr('str_35735470082824'),Game.TableDataManager:GetLangStr('str_35735738528775'),Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735470087426'),}, 
            ['TipsID'] = 47, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933222400'),
        },
        [1048] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789321984'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089729'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520840'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086661'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520072'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086662'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090243'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1048, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470086661'),Game.TableDataManager:GetLangStr('str_35735470084613'),Game.TableDataManager:GetLangStr('str_35735738529027'),Game.TableDataManager:GetLangStr('str_35735738529028'),Game.TableDataManager:GetLangStr('str_35735470089733'),Game.TableDataManager:GetLangStr('str_35735470086662'),Game.TableDataManager:GetLangStr('str_35735738529031'),Game.TableDataManager:GetLangStr('str_54632789321984'),Game.TableDataManager:GetLangStr('str_35735470087426'),}, 
            ['TipsID'] = 48, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933222656'),
        },
        [1049] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093825'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089740'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093827'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091523'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1049, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083076'),Game.TableDataManager:GetLangStr('str_35735470084613'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735738529028'),Game.TableDataManager:GetLangStr('str_35735470089733'),Game.TableDataManager:GetLangStr('str_35735470091016'),Game.TableDataManager:GetLangStr('str_35735738529287'),Game.TableDataManager:GetLangStr('str_35735470091523'),Game.TableDataManager:GetLangStr('str_35735470089740'),}, 
            ['TipsID'] = 49, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933222912'),
        },
        [1050] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090241'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090242'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090244'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090245'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090248'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090251'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1050, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470090248'),Game.TableDataManager:GetLangStr('str_35735738529538'),Game.TableDataManager:GetLangStr('str_35735470089476'),Game.TableDataManager:GetLangStr('str_35735738529540'),Game.TableDataManager:GetLangStr('str_35735470090244'),Game.TableDataManager:GetLangStr('str_35735738528006'),Game.TableDataManager:GetLangStr('str_35735470090245'),Game.TableDataManager:GetLangStr('str_35735470081542'),Game.TableDataManager:GetLangStr('str_35735738529545'),}, 
            ['TipsID'] = 50, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933223168'),
        },
        [1051] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094337'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089730'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094339'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094340'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094342'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094343'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_29756607242496'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094345'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738528004'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086411'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1051, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_54632789320960'),Game.TableDataManager:GetLangStr('str_35735470094342'),Game.TableDataManager:GetLangStr('str_35735470094340'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470083078'),Game.TableDataManager:GetLangStr('str_35735738529799'),Game.TableDataManager:GetLangStr('str_35735470094339'),Game.TableDataManager:GetLangStr('str_35735470094343'),}, 
            ['TipsID'] = 51, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933223424'),
        },
        [1052] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082307'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094594'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520840'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086661'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738523144'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082058'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094600'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094601'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1052, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082817'),Game.TableDataManager:GetLangStr('str_35735738523144'),Game.TableDataManager:GetLangStr('str_35735738530051'),Game.TableDataManager:GetLangStr('str_35735738530052'),Game.TableDataManager:GetLangStr('str_35735738520840'),Game.TableDataManager:GetLangStr('str_35735738530054'),Game.TableDataManager:GetLangStr('str_35735470086661'),Game.TableDataManager:GetLangStr('str_35735470094601'),Game.TableDataManager:GetLangStr('str_35735470082054'),}, 
            ['TipsID'] = 52, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933223680'),
        },
        [1053] = {
            ['CrossWordList'] = {{['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094849'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094850'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094851'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094852'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086920'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094854'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094855'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094856'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094857'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094858'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094859'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094860'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526722'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094862'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1053, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470094857'),Game.TableDataManager:GetLangStr('str_35735738530306'),Game.TableDataManager:GetLangStr('str_35735738524681'),Game.TableDataManager:GetLangStr('str_35735470094849'),Game.TableDataManager:GetLangStr('str_35735470094858'),Game.TableDataManager:GetLangStr('str_35735470094854'),Game.TableDataManager:GetLangStr('str_35735470094862'),Game.TableDataManager:GetLangStr('str_35735738530312'),Game.TableDataManager:GetLangStr('str_35735470094850'),}, 
            ['TipsID'] = 53, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933223936'),
        },
        [1054] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095105'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095106'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089219'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095108'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095109'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095110'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095111'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095112'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095112'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095114'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095115'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1054, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470095110'),Game.TableDataManager:GetLangStr('str_35735738530562'),Game.TableDataManager:GetLangStr('str_35735470095105'),Game.TableDataManager:GetLangStr('str_35735738530564'),Game.TableDataManager:GetLangStr('str_35735470089219'),Game.TableDataManager:GetLangStr('str_35735470095112'),Game.TableDataManager:GetLangStr('str_35735470095111'),Game.TableDataManager:GetLangStr('str_35735470095114'),Game.TableDataManager:GetLangStr('str_35735738530569'),}, 
            ['TipsID'] = 54, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933224192'),
        },
        [1055] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093825'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520582'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091523'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091527'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086659'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081801'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738523397'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095370'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1055, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_13609140751360'),Game.TableDataManager:GetLangStr('str_35735738530818'),Game.TableDataManager:GetLangStr('str_35735738530819'),Game.TableDataManager:GetLangStr('str_35735470091523'),Game.TableDataManager:GetLangStr('str_35735470081801'),Game.TableDataManager:GetLangStr('str_35735738520582'),Game.TableDataManager:GetLangStr('str_35735738523397'),Game.TableDataManager:GetLangStr('str_35735470086659'),Game.TableDataManager:GetLangStr('str_35735738517508'),}, 
            ['TipsID'] = 55, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933224448'),
        },
        [1056] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095617'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095618'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095619'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738520579'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095621'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095622'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092037'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095625'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1056, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738531073'),Game.TableDataManager:GetLangStr('str_35735738520579'),Game.TableDataManager:GetLangStr('str_35735738531075'),Game.TableDataManager:GetLangStr('str_35735470090249'),Game.TableDataManager:GetLangStr('str_35735470095619'),Game.TableDataManager:GetLangStr('str_35735470095621'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470094849'),Game.TableDataManager:GetLangStr('str_35735470095622'),}, 
            ['TipsID'] = 56, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933224704'),
        },
        [1057] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095873'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088713'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088714'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095877'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095878'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083076'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738522625'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095881'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095882'),}, }, 
            ['PuzzleID'] = 1057, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738531329'),Game.TableDataManager:GetLangStr('str_35735738522625'),Game.TableDataManager:GetLangStr('str_35735470095881'),Game.TableDataManager:GetLangStr('str_54632789321984'),Game.TableDataManager:GetLangStr('str_35735470088714'),Game.TableDataManager:GetLangStr('str_35735738531334'),Game.TableDataManager:GetLangStr('str_35735470088713'),Game.TableDataManager:GetLangStr('str_35735470095878'),Game.TableDataManager:GetLangStr('str_35735470081540'),}, 
            ['TipsID'] = 57, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933224960'),
        },
        [1058] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519560'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096130'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096131'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096132'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086923'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096134'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096135'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1058, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470085637'),Game.TableDataManager:GetLangStr('str_35735470096130'),Game.TableDataManager:GetLangStr('str_35735738531587'),Game.TableDataManager:GetLangStr('str_35735470096134'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735738531590'),Game.TableDataManager:GetLangStr('str_35735738531591'),Game.TableDataManager:GetLangStr('str_35735738531592'),Game.TableDataManager:GetLangStr('str_35735470096132'),}, 
            ['TipsID'] = 58, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933225216'),
        },
        [1059] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096385'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096386'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096387'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096388'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096389'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083332'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096391'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096392'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096393'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096394'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1059, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470096389'),Game.TableDataManager:GetLangStr('str_35735738531842'),Game.TableDataManager:GetLangStr('str_35735470096392'),Game.TableDataManager:GetLangStr('str_35735738526728'),Game.TableDataManager:GetLangStr('str_35735738531845'),Game.TableDataManager:GetLangStr('str_35735738531846'),Game.TableDataManager:GetLangStr('str_35735470096386'),Game.TableDataManager:GetLangStr('str_35735470083332'),Game.TableDataManager:GetLangStr('str_35735470096391'),}, 
            ['TipsID'] = 59, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933225472'),
        },
        [1060] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096641'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096642'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789321984'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096644'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091016'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526209'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089729'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096648'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096649'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096651'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1060, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738532097'),Game.TableDataManager:GetLangStr('str_35735470096641'),Game.TableDataManager:GetLangStr('str_54632789321984'),Game.TableDataManager:GetLangStr('str_35735470096651'),Game.TableDataManager:GetLangStr('str_35735738526209'),Game.TableDataManager:GetLangStr('str_35735738532102'),Game.TableDataManager:GetLangStr('str_35735470091016'),Game.TableDataManager:GetLangStr('str_35735470096648'),Game.TableDataManager:GetLangStr('str_35735470084613'),}, 
            ['TipsID'] = 60, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933225728'),
        },
        [1061] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096897'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096898'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096899'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096900'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096901'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096902'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096903'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096904'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096905'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086408'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089992'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1061, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470096899'),Game.TableDataManager:GetLangStr('str_35735470096903'),Game.TableDataManager:GetLangStr('str_35735470086408'),Game.TableDataManager:GetLangStr('str_35735738532356'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735738532358'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735738526213'),Game.TableDataManager:GetLangStr('str_35735470096901'),}, 
            ['TipsID'] = 61, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933225984'),
        },
        [1062] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091011'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097154'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094850'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096132'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517764'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097158'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097159'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097160'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086921'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097163'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094856'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1062, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738517764'),Game.TableDataManager:GetLangStr('str_35735470097159'),Game.TableDataManager:GetLangStr('str_35735470095114'),Game.TableDataManager:GetLangStr('str_35735470086921'),Game.TableDataManager:GetLangStr('str_35735738526722'),Game.TableDataManager:GetLangStr('str_35735470094850'),Game.TableDataManager:GetLangStr('str_35735470096132'),Game.TableDataManager:GetLangStr('str_35735470097158'),Game.TableDataManager:GetLangStr('str_35735738528006'),}, 
            ['TipsID'] = 62, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933226240'),
        },
        [1063] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092811'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097410'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094594'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097412'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097413'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086920'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097415'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54632789316096'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092295'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1063, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470094594'),Game.TableDataManager:GetLangStr('str_35735738532866'),Game.TableDataManager:GetLangStr('str_35735470097413'),Game.TableDataManager:GetLangStr('str_35735470086920'),Game.TableDataManager:GetLangStr('str_35735470083078'),Game.TableDataManager:GetLangStr('str_35735470092295'),Game.TableDataManager:GetLangStr('str_35735738531842'),Game.TableDataManager:GetLangStr('str_35735470097415'),Game.TableDataManager:GetLangStr('str_35735470097410'),}, 
            ['TipsID'] = 63, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933226496'),
        },
        [1064] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097665'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097666'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097667'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089220'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097669'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517250'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087683'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092295'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1064, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470089220'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735470097666'),Game.TableDataManager:GetLangStr('str_35735738517250'),Game.TableDataManager:GetLangStr('str_35735470086915'),Game.TableDataManager:GetLangStr('str_35735738533126'),Game.TableDataManager:GetLangStr('str_35735738531842'),Game.TableDataManager:GetLangStr('str_35735738526465'),Game.TableDataManager:GetLangStr('str_35735470087683'),}, 
            ['TipsID'] = 64, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933226752'),
        },
        [1065] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097921'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519042'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097923'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091528'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526465'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097926'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089740'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097928'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097930'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1065, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470097930'),Game.TableDataManager:GetLangStr('str_35735738530054'),Game.TableDataManager:GetLangStr('str_35735738526465'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735738533381'),Game.TableDataManager:GetLangStr('str_35735738533382'),Game.TableDataManager:GetLangStr('str_35735738520840'),Game.TableDataManager:GetLangStr('str_35735470091528'),Game.TableDataManager:GetLangStr('str_35735470097923'),}, 
            ['TipsID'] = 65, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933227008'),
        },
        [1066] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098177'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098178'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098179'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519560'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097158'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098182'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098183'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098184'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098185'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518279'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098187'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084612'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517252'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098190'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098191'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1066, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470097158'),Game.TableDataManager:GetLangStr('str_35735738533634'),Game.TableDataManager:GetLangStr('str_35735470098177'),Game.TableDataManager:GetLangStr('str_35735470098187'),Game.TableDataManager:GetLangStr('str_35735470098191'),Game.TableDataManager:GetLangStr('str_35735470083079'),Game.TableDataManager:GetLangStr('str_35735738517252'),Game.TableDataManager:GetLangStr('str_35735738533640'),Game.TableDataManager:GetLangStr('str_35735470098182'),}, 
            ['TipsID'] = 66, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933227264'),
        },
        [1067] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098433'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098434'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098436'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738522885'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098438'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519048'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092554'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089733'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1067, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470092554'),Game.TableDataManager:GetLangStr('str_35735470092293'),Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735470098433'),Game.TableDataManager:GetLangStr('str_35735738533893'),Game.TableDataManager:GetLangStr('str_35735738519048'),Game.TableDataManager:GetLangStr('str_35735738522885'),Game.TableDataManager:GetLangStr('str_54632789321472'),Game.TableDataManager:GetLangStr('str_35735470098438'),}, 
            ['TipsID'] = 67, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933227520'),
        },
        [1068] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091011'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098690'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098691'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098692'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098693'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098694'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098695'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098696'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098697'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098698'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098699'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098700'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1068, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470083847'),Game.TableDataManager:GetLangStr('str_35735470095625'),Game.TableDataManager:GetLangStr('str_35735470098692'),Game.TableDataManager:GetLangStr('str_35735470098695'),Game.TableDataManager:GetLangStr('str_35735738529799'),Game.TableDataManager:GetLangStr('str_35735470098700'),Game.TableDataManager:GetLangStr('str_35735470098693'),Game.TableDataManager:GetLangStr('str_35735470098690'),Game.TableDataManager:GetLangStr('str_35735470098694'),}, 
            ['TipsID'] = 68, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933227776'),
        },
        [1069] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098945'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519560'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098947'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086915'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096132'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098950'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086920'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084098'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095114'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097415'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098955'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098956'),}, }, 
            ['PuzzleID'] = 1069, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470096132'),Game.TableDataManager:GetLangStr('str_54632789360640'),Game.TableDataManager:GetLangStr('str_35735738519560'),Game.TableDataManager:GetLangStr('str_35735470086920'),Game.TableDataManager:GetLangStr('str_35735738534405'),Game.TableDataManager:GetLangStr('str_35735470089218'),Game.TableDataManager:GetLangStr('str_35735470098955'),Game.TableDataManager:GetLangStr('str_35735470098950'),Game.TableDataManager:GetLangStr('str_35735470086915'),}, 
            ['TipsID'] = 69, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933228032'),
        },
        [1070] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099201'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099203'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098434'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082825'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54632789321472'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099207'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789351168'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085642'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099210'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099211'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1070, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470098434'),Game.TableDataManager:GetLangStr('str_35735738534658'),Game.TableDataManager:GetLangStr('str_35735470099203'),Game.TableDataManager:GetLangStr('str_35735470096900'),Game.TableDataManager:GetLangStr('str_35735470085642'),Game.TableDataManager:GetLangStr('str_35735470093827'),Game.TableDataManager:GetLangStr('str_54632789351168'),Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735738517000'),}, 
            ['TipsID'] = 70, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933228288'),
        },
        [1071] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099457'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099458'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099459'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099460'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519042'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083336'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099463'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089219'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095108'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099466'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1071, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470099459'),Game.TableDataManager:GetLangStr('str_35735470099463'),Game.TableDataManager:GetLangStr('str_35735470089219'),Game.TableDataManager:GetLangStr('str_35735470089225'),Game.TableDataManager:GetLangStr('str_35735738519810'),Game.TableDataManager:GetLangStr('str_35735470095370'),Game.TableDataManager:GetLangStr('str_35735470083336'),Game.TableDataManager:GetLangStr('str_35735470095108'),}, 
            ['TipsID'] = 71, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933228544'),
        },
        [1072] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099713'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099715'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519048'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517764'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099719'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099720'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099721'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1072, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738535169'),Game.TableDataManager:GetLangStr('str_35735470083589'),Game.TableDataManager:GetLangStr('str_35735738517764'),Game.TableDataManager:GetLangStr('str_35735470089733'),Game.TableDataManager:GetLangStr('str_35735470099721'),Game.TableDataManager:GetLangStr('str_35735738519048'),Game.TableDataManager:GetLangStr('str_35735470099719'),Game.TableDataManager:GetLangStr('str_35735470089739'),Game.TableDataManager:GetLangStr('str_35735738517000'),}, 
            ['TipsID'] = 72, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933228800'),
        },
        [1073] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099969'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091012'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099971'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099972'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099973'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099974'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085633'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081542'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738516995'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1073, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470085633'),Game.TableDataManager:GetLangStr('str_35735738535426'),Game.TableDataManager:GetLangStr('str_35735470091012'),Game.TableDataManager:GetLangStr('str_35735470099971'),Game.TableDataManager:GetLangStr('str_35735470081542'),Game.TableDataManager:GetLangStr('str_35735738535430'),Game.TableDataManager:GetLangStr('str_35735738516995'),Game.TableDataManager:GetLangStr('str_35735470099973'),Game.TableDataManager:GetLangStr('str_35735738517508'),}, 
            ['TipsID'] = 73, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933229056'),
        },
        [1074] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094339'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094340'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089991'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089992'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100229'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100230'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100231'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100232'),}, }, 
            ['PuzzleID'] = 1074, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470096644'),Game.TableDataManager:GetLangStr('str_35735470100229'),Game.TableDataManager:GetLangStr('str_35735470094340'),Game.TableDataManager:GetLangStr('str_35735470089991'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470098436'),Game.TableDataManager:GetLangStr('str_35735738535687'),Game.TableDataManager:GetLangStr('str_35735470100230'),Game.TableDataManager:GetLangStr('str_35735470100232'),}, 
            ['TipsID'] = 74, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933229312'),
        },
        [1075] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084098'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100482'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088449'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095114'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100485'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089989'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54632789338368'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100488'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_38071663867392'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1075, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470095114'),Game.TableDataManager:GetLangStr('str_35735470084098'),Game.TableDataManager:GetLangStr('str_54632789338368'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470100485'),Game.TableDataManager:GetLangStr('str_54632789338624'),Game.TableDataManager:GetLangStr('str_35735470094858'),Game.TableDataManager:GetLangStr('str_35735470097928'),Game.TableDataManager:GetLangStr('str_35735470089989'),}, 
            ['TipsID'] = 75, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933229568'),
        },
        [1076] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100737'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096898'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100739'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086914'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100741'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100742'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100743'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100744'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090757'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083338'),}, }, 
            ['PuzzleID'] = 1076, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470100744'),Game.TableDataManager:GetLangStr('str_35735738536194'),Game.TableDataManager:GetLangStr('str_35735470086914'),Game.TableDataManager:GetLangStr('str_35735738536196'),Game.TableDataManager:GetLangStr('str_35735470100741'),Game.TableDataManager:GetLangStr('str_35735470100742'),Game.TableDataManager:GetLangStr('str_35735470083338'),Game.TableDataManager:GetLangStr('str_35735470100739'),Game.TableDataManager:GetLangStr('str_38071663868928'),}, 
            ['TipsID'] = 76, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933229824'),
        },
        [1077] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082049'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100994'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083330'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470100996'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083080'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092289'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082054'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084614'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101002'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1077, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084613'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470083330'),Game.TableDataManager:GetLangStr('str_35735470082049'),Game.TableDataManager:GetLangStr('str_35735470096393'),Game.TableDataManager:GetLangStr('str_35735470084614'),Game.TableDataManager:GetLangStr('str_35735470082054'),Game.TableDataManager:GetLangStr('str_35735738520072'),Game.TableDataManager:GetLangStr('str_35735470098955'),}, 
            ['TipsID'] = 77, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933230080'),
        },
        [1078] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101249'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082562'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101251'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101252'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39033468129280'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738530819'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101255'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096900'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101257'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101258'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101259'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1078, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_39033468129280'),Game.TableDataManager:GetLangStr('str_35735470082562'),Game.TableDataManager:GetLangStr('str_35735470101255'),Game.TableDataManager:GetLangStr('str_35735470089484'),Game.TableDataManager:GetLangStr('str_35735470096900'),Game.TableDataManager:GetLangStr('str_35735738536710'),Game.TableDataManager:GetLangStr('str_35735738536711'),Game.TableDataManager:GetLangStr('str_35735738530819'),Game.TableDataManager:GetLangStr('str_35735470101259'),}, 
            ['TipsID'] = 78, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933230336'),
        },
        [1079] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095114'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101506'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089740'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097928'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087428'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084867'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101512'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1079, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470095114'),Game.TableDataManager:GetLangStr('str_35735738536962'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470084867'),Game.TableDataManager:GetLangStr('str_35735470089740'),Game.TableDataManager:GetLangStr('str_35735470089729'),Game.TableDataManager:GetLangStr('str_35735470087428'),Game.TableDataManager:GetLangStr('str_54632789316096'),Game.TableDataManager:GetLangStr('str_35735738536969'),}, 
            ['TipsID'] = 79, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933230592'),
        },
        [1080] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789338880'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101762'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085901'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54632789339648'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090241'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101766'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470093827'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101768'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096903'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101770'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1080, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54632789338880'),Game.TableDataManager:GetLangStr('str_54632789316096'),Game.TableDataManager:GetLangStr('str_35735470090241'),Game.TableDataManager:GetLangStr('str_35735738534658'),Game.TableDataManager:GetLangStr('str_54632789339648'),Game.TableDataManager:GetLangStr('str_35735470083844'),Game.TableDataManager:GetLangStr('str_35735470101768'),Game.TableDataManager:GetLangStr('str_35735470101770'),Game.TableDataManager:GetLangStr('str_35735470101766'),}, 
            ['TipsID'] = 80, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933230848'),
        },
        [1081] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102017'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085637'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102019'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084611'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102021'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102022'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470095111'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1081, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470084098'),Game.TableDataManager:GetLangStr('str_35735738537474'),Game.TableDataManager:GetLangStr('str_35735470085637'),Game.TableDataManager:GetLangStr('str_35735738537476'),Game.TableDataManager:GetLangStr('str_35735470084611'),Game.TableDataManager:GetLangStr('str_35735470102022'),Game.TableDataManager:GetLangStr('str_35735470102021'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735470085379'),}, 
            ['TipsID'] = 81, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933231104'),
        },
        [1082] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102273'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102274'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092801'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098433'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102277'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102278'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1082, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470098433'),Game.TableDataManager:GetLangStr('str_35735470094340'),Game.TableDataManager:GetLangStr('str_35735470098434'),Game.TableDataManager:GetLangStr('str_35735738537732'),Game.TableDataManager:GetLangStr('str_35735738537733'),Game.TableDataManager:GetLangStr('str_35735470102274'),Game.TableDataManager:GetLangStr('str_35735738537735'),Game.TableDataManager:GetLangStr('str_35735470092801'),Game.TableDataManager:GetLangStr('str_35735738537737'),}, 
            ['TipsID'] = 82, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933231360'),
        },
        [1083] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102529'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102530'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470097158'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470092545'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102533'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090758'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098438'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519046'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738525701'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470086409'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1083, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470086409'),Game.TableDataManager:GetLangStr('str_35735470092545'),Game.TableDataManager:GetLangStr('str_35735470098438'),Game.TableDataManager:GetLangStr('str_35735738519046'),Game.TableDataManager:GetLangStr('str_35735470090758'),Game.TableDataManager:GetLangStr('str_35735738525702'),Game.TableDataManager:GetLangStr('str_35735470097158'),Game.TableDataManager:GetLangStr('str_35735738537992'),Game.TableDataManager:GetLangStr('str_35735470102530'),}, 
            ['TipsID'] = 83, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933231616'),
        },
        [1084] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102785'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102786'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470083079'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470102788'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089991'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081540'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089994'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089995'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098184'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470101762'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470090249'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1084, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470089995'),Game.TableDataManager:GetLangStr('str_35735470089996'),Game.TableDataManager:GetLangStr('str_35735470101762'),Game.TableDataManager:GetLangStr('str_35735738538244'),Game.TableDataManager:GetLangStr('str_35735470102788'),Game.TableDataManager:GetLangStr('str_35735470081540'),Game.TableDataManager:GetLangStr('str_35735470089994'),Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_35735470083079'),}, 
            ['TipsID'] = 84, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933231872'),
        },
        [1085] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103041'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470098945'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103043'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738523141'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082310'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518532'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470082313'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103048'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087427'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099713'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1085, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470082310'),Game.TableDataManager:GetLangStr('str_35735470103043'),Game.TableDataManager:GetLangStr('str_35735738538499'),Game.TableDataManager:GetLangStr('str_35735470087427'),Game.TableDataManager:GetLangStr('str_35735738523141'),Game.TableDataManager:GetLangStr('str_35735738517000'),Game.TableDataManager:GetLangStr('str_35735738538503'),Game.TableDataManager:GetLangStr('str_35735738518532'),Game.TableDataManager:GetLangStr('str_35735470103048'),}, 
            ['TipsID'] = 85, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933232128'),
        },
        [1086] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103297'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089219'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470087426'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103301'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103302'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470085379'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738519042'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1086, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470102019'),Game.TableDataManager:GetLangStr('str_35735470087426'),Game.TableDataManager:GetLangStr('str_35735470082313'),Game.TableDataManager:GetLangStr('str_35735470089219'),Game.TableDataManager:GetLangStr('str_35735470082313'),Game.TableDataManager:GetLangStr('str_35735470085379'),Game.TableDataManager:GetLangStr('str_35735470103301'),Game.TableDataManager:GetLangStr('str_35735738519301'),Game.TableDataManager:GetLangStr('str_38071663900928'),}, 
            ['TipsID'] = 86, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933232384'),
        },
        [1087] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081798'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103555'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103557'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103558'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517000'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470099715'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091268'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1087, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470094856'),Game.TableDataManager:GetLangStr('str_35735470103557'),Game.TableDataManager:GetLangStr('str_35735470081798'),Game.TableDataManager:GetLangStr('str_35735738539012'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735470103558'),Game.TableDataManager:GetLangStr('str_35735470094859'),Game.TableDataManager:GetLangStr('str_35735470099715'),Game.TableDataManager:GetLangStr('str_35735470103555'),}, 
            ['TipsID'] = 87, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933232640'),
        },
        [1088] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103809'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103810'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470091016'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103812'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103813'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470089729'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096900'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470103816'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1088, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735470091016'),Game.TableDataManager:GetLangStr('str_35735470103813'),Game.TableDataManager:GetLangStr('str_35735470103810'),Game.TableDataManager:GetLangStr('str_35735738526209'),Game.TableDataManager:GetLangStr('str_35735470102277'),Game.TableDataManager:GetLangStr('str_54632789316096'),Game.TableDataManager:GetLangStr('str_35735738539271'),Game.TableDataManager:GetLangStr('str_35735470089729'),Game.TableDataManager:GetLangStr('str_35735470103812'),}, 
            ['TipsID'] = 88, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933232896'),
        },
        [1089] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104065'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104066'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084357'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738518531'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470081537'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735738517508'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470094601'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104072'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104073'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1089, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738539521'),Game.TableDataManager:GetLangStr('str_35735470104072'),Game.TableDataManager:GetLangStr('str_35735738518531'),Game.TableDataManager:GetLangStr('str_35735470091784'),Game.TableDataManager:GetLangStr('str_35735738517508'),Game.TableDataManager:GetLangStr('str_35735738539526'),Game.TableDataManager:GetLangStr('str_35735470084357'),Game.TableDataManager:GetLangStr('str_35735738539528'),Game.TableDataManager:GetLangStr('str_35735470081537'),}, 
            ['TipsID'] = 89, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933233152'),
        },
        [1090] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104321'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084611'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470088711'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738539521'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470084613'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470096651'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104327'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104328'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_35735470104329'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_35735738526465'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1090, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_35735738539521'),Game.TableDataManager:GetLangStr('str_35735470089730'),Game.TableDataManager:GetLangStr('str_35735470104329'),Game.TableDataManager:GetLangStr('str_35735738526465'),Game.TableDataManager:GetLangStr('str_35735738539781'),Game.TableDataManager:GetLangStr('str_35735470096651'),Game.TableDataManager:GetLangStr('str_35735738539783'),Game.TableDataManager:GetLangStr('str_35735470084611'),Game.TableDataManager:GetLangStr('str_35735470104327'),}, 
            ['TipsID'] = 90, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_35734933233408'),
        },
    }
}
return TopData