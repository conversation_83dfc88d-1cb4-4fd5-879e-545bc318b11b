--
-- 表名: CrossWordPuzzleData后处理
--

local TopData = {
    PuzzleIDList = {1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090
    },
    data = {
        [1001] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465217'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465218'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465219'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465221'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465223'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465224'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1001, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335900673'),Game.TableDataManager:GetLangStr('str_36079335900674'),Game.TableDataManager:GetLangStr('str_36079335900675'),Game.TableDataManager:GetLangStr('str_36079335900676'),Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079335900678'),Game.TableDataManager:GetLangStr('str_36079067465218'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067465219'),}, 
            ['TipsID'] = 1, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530594304'),
        },
        [1002] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465473'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465474'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465475'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465476'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465477'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465479'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465480'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465481'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1002, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465474'),Game.TableDataManager:GetLangStr('str_36079335900930'),Game.TableDataManager:GetLangStr('str_36079067465476'),Game.TableDataManager:GetLangStr('str_36079335900932'),Game.TableDataManager:GetLangStr('str_36079067465475'),Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079335900935'),Game.TableDataManager:GetLangStr('str_36079335900936'),Game.TableDataManager:GetLangStr('str_36079067465477'),}, 
            ['TipsID'] = 2, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530594560'),
        },
        [1003] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465729'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465730'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465731'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465732'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465733'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465735'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465736'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465737'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465738'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1003, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465729'),Game.TableDataManager:GetLangStr('str_36079335901186'),Game.TableDataManager:GetLangStr('str_36079067465738'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067465733'),Game.TableDataManager:GetLangStr('str_36079335901190'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079067465731'),Game.TableDataManager:GetLangStr('str_36079335901193'),}, 
            ['TipsID'] = 3, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530594816'),
        },
        [1004] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465985'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465986'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465987'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465988'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465989'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465990'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465991'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465992'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465993'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1004, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335901441'),Game.TableDataManager:GetLangStr('str_36079067465988'),Game.TableDataManager:GetLangStr('str_36079335901443'),Game.TableDataManager:GetLangStr('str_36079335901444'),Game.TableDataManager:GetLangStr('str_36079335901445'),Game.TableDataManager:GetLangStr('str_36079067465989'),Game.TableDataManager:GetLangStr('str_36079335901447'),Game.TableDataManager:GetLangStr('str_36079335901448'),Game.TableDataManager:GetLangStr('str_36079067465992'),}, 
            ['TipsID'] = 4, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530595072'),
        },
        [1005] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466241'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466242'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466243'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466244'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466245'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466246'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466248'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1005, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466241'),Game.TableDataManager:GetLangStr('str_36079335901698'),Game.TableDataManager:GetLangStr('str_36079335901699'),Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079067466245'),Game.TableDataManager:GetLangStr('str_36079067465223'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079335901704'),Game.TableDataManager:GetLangStr('str_36079335901705'),}, 
            ['TipsID'] = 5, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530595328'),
        },
        [1006] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466497'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466498'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466499'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466500'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466501'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466502'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466503'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466504'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466505'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466506'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1006, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335901953'),Game.TableDataManager:GetLangStr('str_36079067466500'),Game.TableDataManager:GetLangStr('str_36079335901955'),Game.TableDataManager:GetLangStr('str_36079067466501'),Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079067466242'),Game.TableDataManager:GetLangStr('str_36079335901959'),Game.TableDataManager:GetLangStr('str_38277822299136'),Game.TableDataManager:GetLangStr('str_36079067466504'),}, 
            ['TipsID'] = 6, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530595584'),
        },
        [1007] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465987'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900678'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466755'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466758'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466760'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466761'),}, }, 
            ['PuzzleID'] = 1007, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335900678'),Game.TableDataManager:GetLangStr('str_36079335902210'),Game.TableDataManager:GetLangStr('str_36079335902211'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079067466760'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079067466758'),Game.TableDataManager:GetLangStr('str_36079335902216'),Game.TableDataManager:GetLangStr('str_36079067466759'),}, 
            ['TipsID'] = 7, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530595840'),
        },
        [1008] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467009'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467010'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467011'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467012'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467013'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901186'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467015'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467016'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467017'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467018'),}, }, 
            ['PuzzleID'] = 1008, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067467012'),Game.TableDataManager:GetLangStr('str_36079067465729'),Game.TableDataManager:GetLangStr('str_36079335902467'),Game.TableDataManager:GetLangStr('str_36079335902468'),Game.TableDataManager:GetLangStr('str_36079067467013'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079067467017'),Game.TableDataManager:GetLangStr('str_36079335902472'),Game.TableDataManager:GetLangStr('str_36079067467015'),}, 
            ['TipsID'] = 8, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530596096'),
        },
        [1009] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467265'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752704'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465733'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467269'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467270'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467271'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467272'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467272'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467274'),}, }, 
            ['PuzzleID'] = 1009, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079335902722'),Game.TableDataManager:GetLangStr('str_36079067467272'),Game.TableDataManager:GetLangStr('str_36079335902724'),Game.TableDataManager:GetLangStr('str_36079067465733'),Game.TableDataManager:GetLangStr('str_36079335902726'),Game.TableDataManager:GetLangStr('str_36079067467271'),Game.TableDataManager:GetLangStr('str_36079335902728'),Game.TableDataManager:GetLangStr('str_36079335902472'),}, 
            ['TipsID'] = 9, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530596352'),
        },
        [1010] = {
            ['CrossWordList'] = {{['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466506'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467522'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467523'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467524'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467525'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467526'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467527'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467528'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467529'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467530'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467531'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1010, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335902977'),Game.TableDataManager:GetLangStr('str_36079067467525'),Game.TableDataManager:GetLangStr('str_36079067467530'),Game.TableDataManager:GetLangStr('str_36079067467522'),Game.TableDataManager:GetLangStr('str_36079335902981'),Game.TableDataManager:GetLangStr('str_36079067467528'),Game.TableDataManager:GetLangStr('str_36079335902983'),Game.TableDataManager:GetLangStr('str_36079335902984'),Game.TableDataManager:GetLangStr('str_36079335902985'),}, 
            ['TipsID'] = 10, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530596608'),
        },
        [1011] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467778'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467781'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467782'),}, {['type'] = 1, ['word'] = '', }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467783'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_15054128809984'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466505'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = '', }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467786'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467787'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467788'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467789'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1011, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466759'),Game.TableDataManager:GetLangStr('str_36079067466243'),Game.TableDataManager:GetLangStr('str_36079067467786'),Game.TableDataManager:GetLangStr('str_36079067467789'),Game.TableDataManager:GetLangStr('str_36079067467781'),Game.TableDataManager:GetLangStr('str_36079067465224'),Game.TableDataManager:GetLangStr('str_36079067466505'),Game.TableDataManager:GetLangStr('str_36079335903240'),Game.TableDataManager:GetLangStr('str_54838947751936'),}, 
            ['TipsID'] = 11, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530596864'),
        },
        [1012] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468033'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468034'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468035'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465990'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468037'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466504'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468041'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1012, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468033'),Game.TableDataManager:GetLangStr('str_36079335903490'),Game.TableDataManager:GetLangStr('str_36079067466504'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079335902211'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067468037'),Game.TableDataManager:GetLangStr('str_36079067468041'),Game.TableDataManager:GetLangStr('str_36079067468035'),}, 
            ['TipsID'] = 12, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530597120'),
        },
        [1013] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468289'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468290'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468291'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468292'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468294'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468298'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1013, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468290'),Game.TableDataManager:GetLangStr('str_36079335903746'),Game.TableDataManager:GetLangStr('str_36079335902726'),Game.TableDataManager:GetLangStr('str_36079067468291'),Game.TableDataManager:GetLangStr('str_36079067468293'),Game.TableDataManager:GetLangStr('str_36079335903750'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079335903752'),Game.TableDataManager:GetLangStr('str_36079067468294'),}, 
            ['TipsID'] = 13, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530597376'),
        },
        [1014] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468545'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468546'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468547'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468548'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468035'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468552'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752704'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468554'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901186'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468556'),}, }, 
            ['PuzzleID'] = 1014, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468545'),Game.TableDataManager:GetLangStr('str_36079067468548'),Game.TableDataManager:GetLangStr('str_36079335904003'),Game.TableDataManager:GetLangStr('str_36079067468546'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067468547'),Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079335904008'),Game.TableDataManager:GetLangStr('str_36079067468556'),}, 
            ['TipsID'] = 14, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530597632'),
        },
        [1015] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468801'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468802'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465223'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468807'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901699'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1015, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468802'),Game.TableDataManager:GetLangStr('str_36079067468801'),Game.TableDataManager:GetLangStr('str_36079335904259'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079335902211'),Game.TableDataManager:GetLangStr('str_36079335904262'),Game.TableDataManager:GetLangStr('str_36079067465223'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067468807'),}, 
            ['TipsID'] = 15, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530597888'),
        },
        [1016] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469057'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469059'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469060'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469061'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469062'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469063'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947751168'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54838947746304'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469067'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469068'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_10995921592320'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469070'),}, }, 
            ['PuzzleID'] = 1016, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079067469063'),Game.TableDataManager:GetLangStr('str_36079067469070'),Game.TableDataManager:GetLangStr('str_36079067469060'),Game.TableDataManager:GetLangStr('str_36079067469061'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_10995921592320'),Game.TableDataManager:GetLangStr('str_36079335904520'),Game.TableDataManager:GetLangStr('str_54838947751168'),}, 
            ['TipsID'] = 16, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530598144'),
        },
        [1017] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469313'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465992'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469317'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469318'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469319'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469321'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469322'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469323'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469324'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901448'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469326'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469327'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1017, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465222'),Game.TableDataManager:GetLangStr('str_36079067469322'),Game.TableDataManager:GetLangStr('str_36079067469326'),Game.TableDataManager:GetLangStr('str_36079067469318'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079067469319'),Game.TableDataManager:GetLangStr('str_36079067469321'),Game.TableDataManager:GetLangStr('str_36079067469323'),Game.TableDataManager:GetLangStr('str_36079067465992'),}, 
            ['TipsID'] = 17, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530598400'),
        },
        [1018] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469569'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469571'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469572'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469573'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752704'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469576'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467781'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469579'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469581'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467524'),}, }, 
            ['PuzzleID'] = 1018, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079067469579'),Game.TableDataManager:GetLangStr('str_36079067467781'),Game.TableDataManager:GetLangStr('str_36079067469573'),Game.TableDataManager:GetLangStr('str_36079067466759'),Game.TableDataManager:GetLangStr('str_36079067469571'),Game.TableDataManager:GetLangStr('str_36079067469581'),Game.TableDataManager:GetLangStr('str_36079067465478'),}, 
            ['TipsID'] = 18, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530598656'),
        },
        [1019] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469825'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469827'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469829'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902210'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947746304'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469833'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469834'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466761'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1019, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067469827'),Game.TableDataManager:GetLangStr('str_54838947746304'),Game.TableDataManager:GetLangStr('str_36079335902210'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079067469829'),Game.TableDataManager:GetLangStr('str_36079067469834'),Game.TableDataManager:GetLangStr('str_36079067468037'),Game.TableDataManager:GetLangStr('str_36079067466759'),Game.TableDataManager:GetLangStr('str_36079067466756'),}, 
            ['TipsID'] = 19, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530598912'),
        },
        [1020] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470081'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468807'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468546'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904003'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468547'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470088'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468548'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470091'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470093'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470094'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1020, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468807'),Game.TableDataManager:GetLangStr('str_36079067468547'),Game.TableDataManager:GetLangStr('str_36079335904003'),Game.TableDataManager:GetLangStr('str_36079067468546'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067470088'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079067470091'),Game.TableDataManager:GetLangStr('str_36079067465222'),}, 
            ['TipsID'] = 20, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530599168'),
        },
        [1021] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901186'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470338'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470339'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752192'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470341'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470342'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335903490'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947781376'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470348'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1021, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067470338'),Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_36079067470348'),Game.TableDataManager:GetLangStr('str_54838947752192'),Game.TableDataManager:GetLangStr('str_36079335905797'),Game.TableDataManager:GetLangStr('str_36079335903490'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_54838947781376'),Game.TableDataManager:GetLangStr('str_36079067470341'),}, 
            ['TipsID'] = 21, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530599424'),
        },
        [1022] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470593'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470594'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470595'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470596'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470597'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470598'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470599'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470600'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470601'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470602'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470603'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1022, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067470594'),Game.TableDataManager:GetLangStr('str_36079067470601'),Game.TableDataManager:GetLangStr('str_36079067470598'),Game.TableDataManager:GetLangStr('str_36079067470600'),Game.TableDataManager:GetLangStr('str_36079067470596'),Game.TableDataManager:GetLangStr('str_36079067470602'),Game.TableDataManager:GetLangStr('str_36079067470599'),Game.TableDataManager:GetLangStr('str_36079067470595'),Game.TableDataManager:GetLangStr('str_36079067470597'),}, 
            ['TipsID'] = 22, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530599680'),
        },
        [1023] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469829'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470850'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465992'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466500'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466501'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466504'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470855'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1023, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335906305'),Game.TableDataManager:GetLangStr('str_36079067466501'),Game.TableDataManager:GetLangStr('str_36079067466504'),Game.TableDataManager:GetLangStr('str_36079067470855'),Game.TableDataManager:GetLangStr('str_36079335906309'),Game.TableDataManager:GetLangStr('str_36079067466500'),Game.TableDataManager:GetLangStr('str_36079067470850'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079067465992'),}, 
            ['TipsID'] = 23, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530599936'),
        },
        [1024] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471105'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471107'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471108'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1024, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335901186'),Game.TableDataManager:GetLangStr('str_36079335900678'),Game.TableDataManager:GetLangStr('str_36079335906563'),Game.TableDataManager:GetLangStr('str_36079067471107'),Game.TableDataManager:GetLangStr('str_36079335906565'),Game.TableDataManager:GetLangStr('str_36079067471105'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079067469825'),}, 
            ['TipsID'] = 24, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530600192'),
        },
        [1025] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36972152230912'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36972152230912'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471363'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471364'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471365'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1025, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36972152230912'),Game.TableDataManager:GetLangStr('str_36079335900930'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067471365'),Game.TableDataManager:GetLangStr('str_36079335906821'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079335906823'),Game.TableDataManager:GetLangStr('str_36079335906824'),Game.TableDataManager:GetLangStr('str_36079067471363'),}, 
            ['TipsID'] = 25, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530600448'),
        },
        [1026] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902216'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900678'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466758'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466760'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466497'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467778'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1026, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_36079335907074'),Game.TableDataManager:GetLangStr('str_36079335902216'),Game.TableDataManager:GetLangStr('str_36079335907076'),Game.TableDataManager:GetLangStr('str_36079335907077'),Game.TableDataManager:GetLangStr('str_36079335900678'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079335907080'),Game.TableDataManager:GetLangStr('str_36079067471105'),}, 
            ['TipsID'] = 26, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530600704'),
        },
        [1027] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901186'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900673'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904262'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471878'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335906821'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465990'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1027, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335900673'),Game.TableDataManager:GetLangStr('str_36079067465990'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079335904262'),Game.TableDataManager:GetLangStr('str_36079335906821'),Game.TableDataManager:GetLangStr('str_36079335901186'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067471878'),Game.TableDataManager:GetLangStr('str_36079067471106'),}, 
            ['TipsID'] = 27, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530600960'),
        },
        [1028] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472129'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472131'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472132'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1028, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079335907586'),Game.TableDataManager:GetLangStr('str_36079067472132'),Game.TableDataManager:GetLangStr('str_36079067472131'),Game.TableDataManager:GetLangStr('str_36079335907589'),Game.TableDataManager:GetLangStr('str_36079335907590'),Game.TableDataManager:GetLangStr('str_36079335907591'),Game.TableDataManager:GetLangStr('str_36079335907592'),Game.TableDataManager:GetLangStr('str_36079067471106'),}, 
            ['TipsID'] = 28, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530601216'),
        },
        [1029] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472385'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901955'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468291'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472389'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472390'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472391'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472392'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472393'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472394'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472395'),}, }, 
            ['PuzzleID'] = 1029, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468291'),Game.TableDataManager:GetLangStr('str_36079067472395'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067472391'),Game.TableDataManager:GetLangStr('str_36079067472385'),Game.TableDataManager:GetLangStr('str_36079067472393'),Game.TableDataManager:GetLangStr('str_36079067472394'),Game.TableDataManager:GetLangStr('str_36079067472390'),Game.TableDataManager:GetLangStr('str_36079067472389'),}, 
            ['TipsID'] = 29, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530601472'),
        },
        [1030] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468291'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472393'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468034'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472645'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947751168'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472647'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472648'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1030, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468034'),Game.TableDataManager:GetLangStr('str_36079067472648'),Game.TableDataManager:GetLangStr('str_36079067472645'),Game.TableDataManager:GetLangStr('str_36079067472393'),Game.TableDataManager:GetLangStr('str_36079067468294'),Game.TableDataManager:GetLangStr('str_36079335908102'),Game.TableDataManager:GetLangStr('str_36079067472390'),Game.TableDataManager:GetLangStr('str_36079067472647'),Game.TableDataManager:GetLangStr('str_54838947751168'),}, 
            ['TipsID'] = 30, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530601728'),
        },
        [1031] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472390'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472898'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472899'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_56625654138624'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472902'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465476'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472905'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39239626528512'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1031, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079067465476'),Game.TableDataManager:GetLangStr('str_36079067472390'),Game.TableDataManager:GetLangStr('str_39239626528512'),Game.TableDataManager:GetLangStr('str_36079067472902'),Game.TableDataManager:GetLangStr('str_36079067472898'),Game.TableDataManager:GetLangStr('str_56625654138624'),Game.TableDataManager:GetLangStr('str_36079067472899'),Game.TableDataManager:GetLangStr('str_36079335908361'),}, 
            ['TipsID'] = 31, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530601984'),
        },
        [1032] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473153'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473154'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473155'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473156'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473157'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473159'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466503'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473161'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473162'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473163'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473164'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473165'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473166'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1032, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473155'),Game.TableDataManager:GetLangStr('str_36079067473159'),Game.TableDataManager:GetLangStr('str_36079067473163'),Game.TableDataManager:GetLangStr('str_36079067473157'),Game.TableDataManager:GetLangStr('str_36079067473165'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067473154'),Game.TableDataManager:GetLangStr('str_36079067473162'),Game.TableDataManager:GetLangStr('str_36079067466503'),}, 
            ['TipsID'] = 32, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530602240'),
        },
        [1033] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473409'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473410'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473411'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473412'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473413'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473414'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473415'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467778'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473417'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473419'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473420'),}, }, 
            ['PuzzleID'] = 1033, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067467778'),Game.TableDataManager:GetLangStr('str_36079067473414'),Game.TableDataManager:GetLangStr('str_36079335908867'),Game.TableDataManager:GetLangStr('str_36079067473419'),Game.TableDataManager:GetLangStr('str_36079067473412'),Game.TableDataManager:GetLangStr('str_36079067469062'),Game.TableDataManager:GetLangStr('str_36079067473411'),Game.TableDataManager:GetLangStr('str_36079335902726'),Game.TableDataManager:GetLangStr('str_36079067473413'),}, 
            ['TipsID'] = 33, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530602496'),
        },
        [1034] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901186'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473668'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473669'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902216'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473671'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473672'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473673'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473674'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473675'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473676'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1034, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473675'),Game.TableDataManager:GetLangStr('str_36079067473668'),Game.TableDataManager:GetLangStr('str_36079335902216'),Game.TableDataManager:GetLangStr('str_36079067473672'),Game.TableDataManager:GetLangStr('str_36079067469062'),Game.TableDataManager:GetLangStr('str_36079067473674'),Game.TableDataManager:GetLangStr('str_36079067468807'),Game.TableDataManager:GetLangStr('str_36079067465222'),Game.TableDataManager:GetLangStr('str_36079067473671'),}, 
            ['TipsID'] = 34, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530602752'),
        },
        [1035] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473921'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473922'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473923'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473924'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473925'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473413'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473927'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473928'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473930'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473931'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1035, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473925'),Game.TableDataManager:GetLangStr('str_36079067465222'),Game.TableDataManager:GetLangStr('str_36079067473927'),Game.TableDataManager:GetLangStr('str_36079067473928'),Game.TableDataManager:GetLangStr('str_36079335909381'),Game.TableDataManager:GetLangStr('str_36079335909382'),Game.TableDataManager:GetLangStr('str_36079067473924'),Game.TableDataManager:GetLangStr('str_36079067473413'),Game.TableDataManager:GetLangStr('str_36079067473922'),}, 
            ['TipsID'] = 35, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530603008'),
        },
        [1036] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468547'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473413'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752192'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473409'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904520'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470341'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474184'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474185'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470342'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473923'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1036, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067469062'),Game.TableDataManager:GetLangStr('str_36079067470341'),Game.TableDataManager:GetLangStr('str_36079067468293'),Game.TableDataManager:GetLangStr('str_54838947790848'),Game.TableDataManager:GetLangStr('str_36079067468547'),Game.TableDataManager:GetLangStr('str_36079335909381'),Game.TableDataManager:GetLangStr('str_36079067470342'),Game.TableDataManager:GetLangStr('str_36079067474184'),Game.TableDataManager:GetLangStr('str_54838947752192'),}, 
            ['TipsID'] = 36, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530603264'),
        },
        [1037] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466241'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474435'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474436'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474437'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474438'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474439'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904520'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467526'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1037, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335909889'),Game.TableDataManager:GetLangStr('str_36079335909890'),Game.TableDataManager:GetLangStr('str_36079067466241'),Game.TableDataManager:GetLangStr('str_36079067474436'),Game.TableDataManager:GetLangStr('str_36079335909893'),Game.TableDataManager:GetLangStr('str_36079335904520'),Game.TableDataManager:GetLangStr('str_36079067467018'),Game.TableDataManager:GetLangStr('str_36079067474438'),Game.TableDataManager:GetLangStr('str_36079067474435'),}, 
            ['TipsID'] = 37, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530603520'),
        },
        [1038] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474689'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469825'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474691'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474692'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473419'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474696'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335909889'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947746304'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469834'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474700'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1038, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335910145'),Game.TableDataManager:GetLangStr('str_36079067474696'),Game.TableDataManager:GetLangStr('str_36079067469834'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079335902726'),Game.TableDataManager:GetLangStr('str_36079335910150'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079067469825'),Game.TableDataManager:GetLangStr('str_54838947746304'),}, 
            ['TipsID'] = 38, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530603776'),
        },
        [1039] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474945'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474946'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474947'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474948'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_39239626553600'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467781'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474953'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1039, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067474948'),Game.TableDataManager:GetLangStr('str_36079335910402'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067467527'),Game.TableDataManager:GetLangStr('str_39239626553600'),Game.TableDataManager:GetLangStr('str_36079067473419'),Game.TableDataManager:GetLangStr('str_36079067474947'),Game.TableDataManager:GetLangStr('str_36079335910408'),Game.TableDataManager:GetLangStr('str_36079335900680'),}, 
            ['TipsID'] = 39, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530604032'),
        },
        [1040] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466506'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468807'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475203'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475206'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475207'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475208'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335910145'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1040, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067467524'),Game.TableDataManager:GetLangStr('str_36079335901699'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079335910660'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067475207'),Game.TableDataManager:GetLangStr('str_36079067475203'),Game.TableDataManager:GetLangStr('str_36079335910145'),Game.TableDataManager:GetLangStr('str_36079067468807'),}, 
            ['TipsID'] = 40, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530604288'),
        },
        [1041] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467522'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467525'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469834'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474700'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467527'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475462'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475463'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475464'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475465'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475466'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475467'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1041, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335910913'),Game.TableDataManager:GetLangStr('str_36079067474700'),Game.TableDataManager:GetLangStr('str_36079067467527'),Game.TableDataManager:GetLangStr('str_36079067475466'),Game.TableDataManager:GetLangStr('str_36079335910917'),Game.TableDataManager:GetLangStr('str_36079067475464'),Game.TableDataManager:GetLangStr('str_36079067475462'),Game.TableDataManager:GetLangStr('str_36079067469833'),Game.TableDataManager:GetLangStr('str_36079067467522'),}, 
            ['TipsID'] = 41, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530604544'),
        },
        [1042] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475713'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752704'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475715'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475716'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475717'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465733'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467783'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467271'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475722'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475723'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467272'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1042, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079067475715'),Game.TableDataManager:GetLangStr('str_36079067467272'),Game.TableDataManager:GetLangStr('str_36079335902724'),Game.TableDataManager:GetLangStr('str_36079067465733'),Game.TableDataManager:GetLangStr('str_36079067467783'),Game.TableDataManager:GetLangStr('str_36079067467271'),Game.TableDataManager:GetLangStr('str_36079335902728'),Game.TableDataManager:GetLangStr('str_36079335902472'),}, 
            ['TipsID'] = 42, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530604800'),
        },
        [1043] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475969'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947746304'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901190'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475973'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475974'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475975'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475976'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475977'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475979'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475980'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1043, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473929'),Game.TableDataManager:GetLangStr('str_36079067465987'),Game.TableDataManager:GetLangStr('str_36079067475973'),Game.TableDataManager:GetLangStr('str_36079335901190'),Game.TableDataManager:GetLangStr('str_36079067475975'),Game.TableDataManager:GetLangStr('str_36079335911430'),Game.TableDataManager:GetLangStr('str_36079067473676'),Game.TableDataManager:GetLangStr('str_54838947746304'),Game.TableDataManager:GetLangStr('str_36079067465734'),}, 
            ['TipsID'] = 43, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530605056'),
        },
        [1044] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476225'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476226'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466244'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466245'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904262'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468041'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468037'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466504'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476234'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1044, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466504'),Game.TableDataManager:GetLangStr('str_36079067466245'),Game.TableDataManager:GetLangStr('str_36079067473929'),Game.TableDataManager:GetLangStr('str_36079335911684'),Game.TableDataManager:GetLangStr('str_36079067468037'),Game.TableDataManager:GetLangStr('str_36079335911686'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079335902211'),Game.TableDataManager:GetLangStr('str_36079335904262'),}, 
            ['TipsID'] = 44, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530605312'),
        },
        [1045] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476481'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476482'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476483'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476484'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476485'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476486'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_15054128809984'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_34635690021376'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466242'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476490'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476491'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1045, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067476481'),Game.TableDataManager:GetLangStr('str_15054128809984'),Game.TableDataManager:GetLangStr('str_36079067473929'),Game.TableDataManager:GetLangStr('str_36079067466245'),Game.TableDataManager:GetLangStr('str_36079335911941'),Game.TableDataManager:GetLangStr('str_36079067476491'),Game.TableDataManager:GetLangStr('str_36079335904520'),Game.TableDataManager:GetLangStr('str_36079067476485'),Game.TableDataManager:GetLangStr('str_36079067476490'),}, 
            ['TipsID'] = 45, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530605568'),
        },
        [1046] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466503'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466505'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752704'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469576'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467781'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469579'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476745'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469581'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467524'),}, }, 
            ['PuzzleID'] = 1046, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466759'),Game.TableDataManager:GetLangStr('str_36079067469579'),Game.TableDataManager:GetLangStr('str_36079335912195'),Game.TableDataManager:GetLangStr('str_36079067476745'),Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_36079067467781'),Game.TableDataManager:GetLangStr('str_36079067465481'),Game.TableDataManager:GetLangStr('str_36079067469581'),}, 
            ['TipsID'] = 46, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530605824'),
        },
        [1047] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471105'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335907076'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476995'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1047, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067471105'),Game.TableDataManager:GetLangStr('str_36079335903490'),Game.TableDataManager:GetLangStr('str_36079335912451'),Game.TableDataManager:GetLangStr('str_36079335900678'),Game.TableDataManager:GetLangStr('str_54838947752704'),Game.TableDataManager:GetLangStr('str_36079067466504'),Game.TableDataManager:GetLangStr('str_36079335912455'),Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079067471106'),}, 
            ['TipsID'] = 47, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530606080'),
        },
        [1048] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752192'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473409'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904520'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470341'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335903752'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470342'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473923'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1048, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067470341'),Game.TableDataManager:GetLangStr('str_36079067468293'),Game.TableDataManager:GetLangStr('str_36079335912707'),Game.TableDataManager:GetLangStr('str_36079335912708'),Game.TableDataManager:GetLangStr('str_36079067473413'),Game.TableDataManager:GetLangStr('str_36079067470342'),Game.TableDataManager:GetLangStr('str_36079335912711'),Game.TableDataManager:GetLangStr('str_54838947752192'),Game.TableDataManager:GetLangStr('str_36079067471106'),}, 
            ['TipsID'] = 48, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530606336'),
        },
        [1049] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067477505'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473420'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067477507'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475203'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1049, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466756'),Game.TableDataManager:GetLangStr('str_36079067468293'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079335912708'),Game.TableDataManager:GetLangStr('str_36079067473413'),Game.TableDataManager:GetLangStr('str_36079067474696'),Game.TableDataManager:GetLangStr('str_36079335912967'),Game.TableDataManager:GetLangStr('str_36079067475203'),Game.TableDataManager:GetLangStr('str_36079067473420'),}, 
            ['TipsID'] = 49, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530606592'),
        },
        [1050] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473921'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473922'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473924'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473925'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473928'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473931'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1050, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473928'),Game.TableDataManager:GetLangStr('str_36079335913218'),Game.TableDataManager:GetLangStr('str_36079067473156'),Game.TableDataManager:GetLangStr('str_36079335913220'),Game.TableDataManager:GetLangStr('str_36079067473924'),Game.TableDataManager:GetLangStr('str_36079335911686'),Game.TableDataManager:GetLangStr('str_36079067473925'),Game.TableDataManager:GetLangStr('str_36079067465222'),Game.TableDataManager:GetLangStr('str_36079335913225'),}, 
            ['TipsID'] = 50, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530606848'),
        },
        [1051] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478017'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473410'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478019'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478020'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478022'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478023'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_30031485149440'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478025'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335911684'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470091'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1051, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_54838947751168'),Game.TableDataManager:GetLangStr('str_36079067478022'),Game.TableDataManager:GetLangStr('str_36079067478020'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067466758'),Game.TableDataManager:GetLangStr('str_36079335913479'),Game.TableDataManager:GetLangStr('str_36079067478019'),Game.TableDataManager:GetLangStr('str_36079067478023'),}, 
            ['TipsID'] = 51, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530607104'),
        },
        [1052] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465987'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478274'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904520'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470341'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335906824'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465738'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478280'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478281'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1052, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067466497'),Game.TableDataManager:GetLangStr('str_36079335906824'),Game.TableDataManager:GetLangStr('str_36079335913731'),Game.TableDataManager:GetLangStr('str_36079335913732'),Game.TableDataManager:GetLangStr('str_36079335904520'),Game.TableDataManager:GetLangStr('str_36079335913734'),Game.TableDataManager:GetLangStr('str_36079067470341'),Game.TableDataManager:GetLangStr('str_36079067478281'),Game.TableDataManager:GetLangStr('str_36079067465734'),}, 
            ['TipsID'] = 52, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530607360'),
        },
        [1053] = {
            ['CrossWordList'] = {{['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478529'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478530'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478531'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478532'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470600'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478534'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478535'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478536'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478537'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478538'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478539'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478540'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335910402'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478542'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1053, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478537'),Game.TableDataManager:GetLangStr('str_36079335913986'),Game.TableDataManager:GetLangStr('str_36079335908361'),Game.TableDataManager:GetLangStr('str_36079067478529'),Game.TableDataManager:GetLangStr('str_36079067478538'),Game.TableDataManager:GetLangStr('str_36079067478534'),Game.TableDataManager:GetLangStr('str_36079067478542'),Game.TableDataManager:GetLangStr('str_36079335913992'),Game.TableDataManager:GetLangStr('str_36079067478530'),}, 
            ['TipsID'] = 53, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530607616'),
        },
        [1054] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478785'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478786'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472899'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478788'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478789'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478790'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478791'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478792'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478792'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478794'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478795'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1054, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478790'),Game.TableDataManager:GetLangStr('str_36079335914242'),Game.TableDataManager:GetLangStr('str_36079067478785'),Game.TableDataManager:GetLangStr('str_36079335914244'),Game.TableDataManager:GetLangStr('str_36079067472899'),Game.TableDataManager:GetLangStr('str_36079067478792'),Game.TableDataManager:GetLangStr('str_36079067478791'),Game.TableDataManager:GetLangStr('str_36079067478794'),Game.TableDataManager:GetLangStr('str_36079335914249'),}, 
            ['TipsID'] = 54, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530607872'),
        },
        [1055] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067477505'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904262'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475203'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475207'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470339'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465481'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335907077'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479050'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1055, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_15054128810496'),Game.TableDataManager:GetLangStr('str_36079335914498'),Game.TableDataManager:GetLangStr('str_36079335914499'),Game.TableDataManager:GetLangStr('str_36079067475203'),Game.TableDataManager:GetLangStr('str_36079067465481'),Game.TableDataManager:GetLangStr('str_36079335904262'),Game.TableDataManager:GetLangStr('str_36079335907077'),Game.TableDataManager:GetLangStr('str_36079067470339'),Game.TableDataManager:GetLangStr('str_36079335901188'),}, 
            ['TipsID'] = 55, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530608128'),
        },
        [1056] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479297'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479298'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479299'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335904259'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479301'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479302'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475717'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479305'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1056, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335914753'),Game.TableDataManager:GetLangStr('str_36079335904259'),Game.TableDataManager:GetLangStr('str_36079335914755'),Game.TableDataManager:GetLangStr('str_36079067473929'),Game.TableDataManager:GetLangStr('str_36079067479299'),Game.TableDataManager:GetLangStr('str_36079067479301'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067478529'),Game.TableDataManager:GetLangStr('str_36079067479302'),}, 
            ['TipsID'] = 56, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530608384'),
        },
        [1057] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479553'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472393'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472394'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479557'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479558'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466756'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335906305'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479561'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479562'),}, }, 
            ['PuzzleID'] = 1057, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335915009'),Game.TableDataManager:GetLangStr('str_36079335906305'),Game.TableDataManager:GetLangStr('str_36079067479561'),Game.TableDataManager:GetLangStr('str_54838947752192'),Game.TableDataManager:GetLangStr('str_36079067472394'),Game.TableDataManager:GetLangStr('str_36079335915014'),Game.TableDataManager:GetLangStr('str_36079067472393'),Game.TableDataManager:GetLangStr('str_36079067479558'),Game.TableDataManager:GetLangStr('str_36079067465220'),}, 
            ['TipsID'] = 57, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530608640'),
        },
        [1058] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335903240'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479810'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479811'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479812'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470603'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479814'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479815'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1058, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067469317'),Game.TableDataManager:GetLangStr('str_36079067479810'),Game.TableDataManager:GetLangStr('str_36079335915267'),Game.TableDataManager:GetLangStr('str_36079067479814'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079335915270'),Game.TableDataManager:GetLangStr('str_36079335915271'),Game.TableDataManager:GetLangStr('str_36079335915272'),Game.TableDataManager:GetLangStr('str_36079067479812'),}, 
            ['TipsID'] = 58, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530608896'),
        },
        [1059] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480065'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480066'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480067'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480068'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480069'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467012'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480071'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480072'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480073'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480074'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1059, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067480069'),Game.TableDataManager:GetLangStr('str_36079335915522'),Game.TableDataManager:GetLangStr('str_36079067480072'),Game.TableDataManager:GetLangStr('str_36079335910408'),Game.TableDataManager:GetLangStr('str_36079335915525'),Game.TableDataManager:GetLangStr('str_36079335915526'),Game.TableDataManager:GetLangStr('str_36079067480066'),Game.TableDataManager:GetLangStr('str_36079067467012'),Game.TableDataManager:GetLangStr('str_36079067480071'),}, 
            ['TipsID'] = 59, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530609152'),
        },
        [1060] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480321'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480322'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947752192'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480324'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474696'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335909889'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473409'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480328'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480329'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480331'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1060, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335915777'),Game.TableDataManager:GetLangStr('str_36079067480321'),Game.TableDataManager:GetLangStr('str_54838947752192'),Game.TableDataManager:GetLangStr('str_36079067480331'),Game.TableDataManager:GetLangStr('str_36079335909889'),Game.TableDataManager:GetLangStr('str_36079335915782'),Game.TableDataManager:GetLangStr('str_36079067474696'),Game.TableDataManager:GetLangStr('str_36079067480328'),Game.TableDataManager:GetLangStr('str_36079067468293'),}, 
            ['TipsID'] = 60, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530609408'),
        },
        [1061] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480577'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480578'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480579'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480580'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480581'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480582'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480583'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480584'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480585'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470088'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473672'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1061, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067480579'),Game.TableDataManager:GetLangStr('str_36079067480583'),Game.TableDataManager:GetLangStr('str_36079067470088'),Game.TableDataManager:GetLangStr('str_36079335916036'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079335916038'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079335909893'),Game.TableDataManager:GetLangStr('str_36079067480581'),}, 
            ['TipsID'] = 61, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530609664'),
        },
        [1062] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474691'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480834'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478530'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479812'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901444'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480838'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480839'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480840'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470601'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480843'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478536'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1062, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335901444'),Game.TableDataManager:GetLangStr('str_36079067480839'),Game.TableDataManager:GetLangStr('str_36079067478794'),Game.TableDataManager:GetLangStr('str_36079067470601'),Game.TableDataManager:GetLangStr('str_36079335910402'),Game.TableDataManager:GetLangStr('str_36079067478530'),Game.TableDataManager:GetLangStr('str_36079067479812'),Game.TableDataManager:GetLangStr('str_36079067480838'),Game.TableDataManager:GetLangStr('str_36079335911686'),}, 
            ['TipsID'] = 62, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530609920'),
        },
        [1063] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476491'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481090'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478274'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481092'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481093'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470600'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481095'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54838947746304'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475975'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1063, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478274'),Game.TableDataManager:GetLangStr('str_57862873165056'),Game.TableDataManager:GetLangStr('str_36079067481093'),Game.TableDataManager:GetLangStr('str_36079067470600'),Game.TableDataManager:GetLangStr('str_36079067466758'),Game.TableDataManager:GetLangStr('str_36079067475975'),Game.TableDataManager:GetLangStr('str_36079335915522'),Game.TableDataManager:GetLangStr('str_36079067481095'),Game.TableDataManager:GetLangStr('str_36079067481090'),}, 
            ['TipsID'] = 63, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530610176'),
        },
        [1064] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481345'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481346'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481347'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_56625654138624'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_59443152796928'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900930'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471363'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475975'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1064, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_56625654138624'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079067481346'),Game.TableDataManager:GetLangStr('str_36079335900930'),Game.TableDataManager:GetLangStr('str_36079067470595'),Game.TableDataManager:GetLangStr('str_36079335916806'),Game.TableDataManager:GetLangStr('str_36079335915522'),Game.TableDataManager:GetLangStr('str_36079335910145'),Game.TableDataManager:GetLangStr('str_36079067471363'),}, 
            ['TipsID'] = 64, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530610432'),
        },
        [1065] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481601'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902722'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481603'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475208'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335910145'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481606'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473420'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481608'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481610'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1065, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067481610'),Game.TableDataManager:GetLangStr('str_36079335913734'),Game.TableDataManager:GetLangStr('str_36079335910145'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079335917061'),Game.TableDataManager:GetLangStr('str_36079335917062'),Game.TableDataManager:GetLangStr('str_36079335904520'),Game.TableDataManager:GetLangStr('str_36079067475208'),Game.TableDataManager:GetLangStr('str_36079067481603'),}, 
            ['TipsID'] = 65, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530610688'),
        },
        [1066] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481857'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481858'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481859'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335903240'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480838'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481862'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481863'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481864'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481865'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901959'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481867'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468292'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900932'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481870'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481871'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1066, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067480838'),Game.TableDataManager:GetLangStr('str_36079335917314'),Game.TableDataManager:GetLangStr('str_36079067481857'),Game.TableDataManager:GetLangStr('str_36079067481867'),Game.TableDataManager:GetLangStr('str_36079067481871'),Game.TableDataManager:GetLangStr('str_36079067466759'),Game.TableDataManager:GetLangStr('str_36079335900932'),Game.TableDataManager:GetLangStr('str_36079335917320'),Game.TableDataManager:GetLangStr('str_36079067481862'),}, 
            ['TipsID'] = 66, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530610944'),
        },
        [1067] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482113'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482114'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482116'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335906565'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482118'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902728'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476234'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473413'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1067, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067476234'),Game.TableDataManager:GetLangStr('str_36079067475973'),Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079067482113'),Game.TableDataManager:GetLangStr('str_36079335917573'),Game.TableDataManager:GetLangStr('str_36079335902728'),Game.TableDataManager:GetLangStr('str_36079335906565'),Game.TableDataManager:GetLangStr('str_54838947751680'),Game.TableDataManager:GetLangStr('str_36079067482118'),}, 
            ['TipsID'] = 67, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530611200'),
        },
        [1068] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474691'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482370'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482371'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482372'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482373'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482374'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482375'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482376'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482377'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482378'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482379'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482380'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1068, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067467527'),Game.TableDataManager:GetLangStr('str_36079067479305'),Game.TableDataManager:GetLangStr('str_36079067482372'),Game.TableDataManager:GetLangStr('str_36079067482375'),Game.TableDataManager:GetLangStr('str_36079335913479'),Game.TableDataManager:GetLangStr('str_36079067482380'),Game.TableDataManager:GetLangStr('str_36079067482373'),Game.TableDataManager:GetLangStr('str_36079067482370'),Game.TableDataManager:GetLangStr('str_36079067482374'),}, 
            ['TipsID'] = 68, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530611456'),
        },
        [1069] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482625'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335903240'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482627'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470595'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067479812'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482630'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470600'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467778'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478794'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481095'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482635'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482636'),}, }, 
            ['PuzzleID'] = 1069, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067479812'),Game.TableDataManager:GetLangStr('str_54838947790848'),Game.TableDataManager:GetLangStr('str_36079335903240'),Game.TableDataManager:GetLangStr('str_36079067470600'),Game.TableDataManager:GetLangStr('str_36079335918085'),Game.TableDataManager:GetLangStr('str_36079067472898'),Game.TableDataManager:GetLangStr('str_36079067482635'),Game.TableDataManager:GetLangStr('str_36079067482630'),Game.TableDataManager:GetLangStr('str_36079067470595'),}, 
            ['TipsID'] = 69, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530611712'),
        },
        [1070] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482881'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482883'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482114'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466505'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_54838947751680'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482887'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947781376'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469322'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482890'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482891'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1070, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067482114'),Game.TableDataManager:GetLangStr('str_36079335918338'),Game.TableDataManager:GetLangStr('str_36079067482883'),Game.TableDataManager:GetLangStr('str_36079067480580'),Game.TableDataManager:GetLangStr('str_36079067469322'),Game.TableDataManager:GetLangStr('str_36079067477507'),Game.TableDataManager:GetLangStr('str_54838947781376'),Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079335900680'),}, 
            ['TipsID'] = 70, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530611968'),
        },
        [1071] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483137'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483138'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483139'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483140'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902722'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467016'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483143'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472899'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478788'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483146'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1071, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067483139'),Game.TableDataManager:GetLangStr('str_36079067483143'),Game.TableDataManager:GetLangStr('str_36079067472899'),Game.TableDataManager:GetLangStr('str_36079067472905'),Game.TableDataManager:GetLangStr('str_36079335903490'),Game.TableDataManager:GetLangStr('str_36079067479050'),Game.TableDataManager:GetLangStr('str_36079067467016'),Game.TableDataManager:GetLangStr('str_36079067478788'),}, 
            ['TipsID'] = 71, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530612224'),
        },
        [1072] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483393'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483395'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902728'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901444'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483399'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483400'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483401'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1072, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335918849'),Game.TableDataManager:GetLangStr('str_36079067467269'),Game.TableDataManager:GetLangStr('str_36079335901444'),Game.TableDataManager:GetLangStr('str_36079067473413'),Game.TableDataManager:GetLangStr('str_36079067483401'),Game.TableDataManager:GetLangStr('str_36079335902728'),Game.TableDataManager:GetLangStr('str_36079067483399'),Game.TableDataManager:GetLangStr('str_36079067473419'),Game.TableDataManager:GetLangStr('str_36079335900680'),}, 
            ['TipsID'] = 72, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530612480'),
        },
        [1073] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483649'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474692'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483651'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483652'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483653'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483654'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469313'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465222'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900675'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1073, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067469313'),Game.TableDataManager:GetLangStr('str_36079335919106'),Game.TableDataManager:GetLangStr('str_36079067474692'),Game.TableDataManager:GetLangStr('str_36079067483651'),Game.TableDataManager:GetLangStr('str_36079067465222'),Game.TableDataManager:GetLangStr('str_36079335919110'),Game.TableDataManager:GetLangStr('str_36079335900675'),Game.TableDataManager:GetLangStr('str_36079067483653'),Game.TableDataManager:GetLangStr('str_36079335901188'),}, 
            ['TipsID'] = 73, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530612736'),
        },
        [1074] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478019'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478020'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473671'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473672'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483909'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483910'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483911'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483912'),}, }, 
            ['PuzzleID'] = 1074, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067480324'),Game.TableDataManager:GetLangStr('str_36079067483909'),Game.TableDataManager:GetLangStr('str_36079067478020'),Game.TableDataManager:GetLangStr('str_36079067473671'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067482116'),Game.TableDataManager:GetLangStr('str_36079335919367'),Game.TableDataManager:GetLangStr('str_36079067483910'),Game.TableDataManager:GetLangStr('str_36079067483912'),}, 
            ['TipsID'] = 74, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530612992'),
        },
        [1075] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467778'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484162'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472129'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478794'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484165'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473669'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_54838947768576'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484168'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_38277822297600'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1075, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478794'),Game.TableDataManager:GetLangStr('str_36079067467778'),Game.TableDataManager:GetLangStr('str_54838947768576'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067484165'),Game.TableDataManager:GetLangStr('str_36079067466243'),Game.TableDataManager:GetLangStr('str_36079067478538'),Game.TableDataManager:GetLangStr('str_36079067481608'),Game.TableDataManager:GetLangStr('str_36079067473669'),}, 
            ['TipsID'] = 75, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530613248'),
        },
        [1076] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484417'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480578'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484419'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470594'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484421'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484422'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484423'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484424'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474437'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467018'),}, }, 
            ['PuzzleID'] = 1076, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067484424'),Game.TableDataManager:GetLangStr('str_36079335919874'),Game.TableDataManager:GetLangStr('str_36079067470594'),Game.TableDataManager:GetLangStr('str_36079335919876'),Game.TableDataManager:GetLangStr('str_36079067484421'),Game.TableDataManager:GetLangStr('str_36079067484422'),Game.TableDataManager:GetLangStr('str_36079067467018'),Game.TableDataManager:GetLangStr('str_36079067484419'),Game.TableDataManager:GetLangStr('str_38277822299136'),}, 
            ['TipsID'] = 76, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530613504'),
        },
        [1077] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465729'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484674'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067467010'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484676'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466760'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067475969'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465734'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468294'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484682'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1077, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067468293'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067467010'),Game.TableDataManager:GetLangStr('str_36079067465729'),Game.TableDataManager:GetLangStr('str_36079067480073'),Game.TableDataManager:GetLangStr('str_36079067468294'),Game.TableDataManager:GetLangStr('str_36079067465734'),Game.TableDataManager:GetLangStr('str_36079335903752'),Game.TableDataManager:GetLangStr('str_36079067482635'),}, 
            ['TipsID'] = 77, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530613760'),
        },
        [1078] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484929'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466242'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484931'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484932'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_39239626559488'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335914499'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484935'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480580'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484937'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484938'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067484939'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1078, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_39239626559488'),Game.TableDataManager:GetLangStr('str_36079067466242'),Game.TableDataManager:GetLangStr('str_36079067484935'),Game.TableDataManager:GetLangStr('str_36079067473164'),Game.TableDataManager:GetLangStr('str_36079067480580'),Game.TableDataManager:GetLangStr('str_36079335920390'),Game.TableDataManager:GetLangStr('str_36079335920391'),Game.TableDataManager:GetLangStr('str_36079335914499'),Game.TableDataManager:GetLangStr('str_36079067484939'),}, 
            ['TipsID'] = 78, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530614016'),
        },
        [1079] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478794'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485186'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473420'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481608'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471108'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468547'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485192'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1079, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478794'),Game.TableDataManager:GetLangStr('str_36079335920642'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067468547'),Game.TableDataManager:GetLangStr('str_36079067473420'),Game.TableDataManager:GetLangStr('str_36079067473409'),Game.TableDataManager:GetLangStr('str_36079067471108'),Game.TableDataManager:GetLangStr('str_54838947746304'),Game.TableDataManager:GetLangStr('str_36079335920649'),}, 
            ['TipsID'] = 79, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530614272'),
        },
        [1080] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947769088'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485442'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469581'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_54838947769856'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473921'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485446'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067477507'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485448'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480583'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485450'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1080, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_54838947769088'),Game.TableDataManager:GetLangStr('str_54838947746304'),Game.TableDataManager:GetLangStr('str_36079067473921'),Game.TableDataManager:GetLangStr('str_36079335918338'),Game.TableDataManager:GetLangStr('str_54838947769856'),Game.TableDataManager:GetLangStr('str_36079067467524'),Game.TableDataManager:GetLangStr('str_36079067485448'),Game.TableDataManager:GetLangStr('str_36079067485450'),Game.TableDataManager:GetLangStr('str_36079067485446'),}, 
            ['TipsID'] = 80, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530614528'),
        },
        [1081] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485697'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469317'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485699'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468291'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485701'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485702'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478791'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1081, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067467778'),Game.TableDataManager:GetLangStr('str_36079335921154'),Game.TableDataManager:GetLangStr('str_36079067469317'),Game.TableDataManager:GetLangStr('str_36079335921156'),Game.TableDataManager:GetLangStr('str_36079067468291'),Game.TableDataManager:GetLangStr('str_36079067485702'),Game.TableDataManager:GetLangStr('str_36079067485701'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079067469059'),}, 
            ['TipsID'] = 81, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530614784'),
        },
        [1082] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485953'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485954'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476481'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482113'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485957'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485958'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1082, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067482113'),Game.TableDataManager:GetLangStr('str_36079067478020'),Game.TableDataManager:GetLangStr('str_36079067482114'),Game.TableDataManager:GetLangStr('str_36079335921412'),Game.TableDataManager:GetLangStr('str_36079335921413'),Game.TableDataManager:GetLangStr('str_36079067485954'),Game.TableDataManager:GetLangStr('str_36079335921415'),Game.TableDataManager:GetLangStr('str_36079067476481'),Game.TableDataManager:GetLangStr('str_36079335921417'),}, 
            ['TipsID'] = 82, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530615040'),
        },
        [1083] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486209'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486210'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480838'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067476225'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486213'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474438'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482118'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902726'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335909381'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067470089'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1083, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067470089'),Game.TableDataManager:GetLangStr('str_36079067476225'),Game.TableDataManager:GetLangStr('str_36079067482118'),Game.TableDataManager:GetLangStr('str_36079335902726'),Game.TableDataManager:GetLangStr('str_36079067474438'),Game.TableDataManager:GetLangStr('str_36079335909382'),Game.TableDataManager:GetLangStr('str_36079067480838'),Game.TableDataManager:GetLangStr('str_36079335921672'),Game.TableDataManager:GetLangStr('str_36079067486210'),}, 
            ['TipsID'] = 83, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530615296'),
        },
        [1084] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486465'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486466'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067466759'),}, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486468'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473671'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465220'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473674'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473675'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067481864'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067485442'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473929'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1084, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067473675'),Game.TableDataManager:GetLangStr('str_36079067473676'),Game.TableDataManager:GetLangStr('str_36079067485442'),Game.TableDataManager:GetLangStr('str_36079335921924'),Game.TableDataManager:GetLangStr('str_36079067486468'),Game.TableDataManager:GetLangStr('str_36079067465220'),Game.TableDataManager:GetLangStr('str_36079067473674'),Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_36079067466759'),}, 
            ['TipsID'] = 84, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530615552'),
        },
        [1085] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486721'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067482625'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486723'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335906821'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465990'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902212'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465993'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486728'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471107'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483393'),}, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1085, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067465990'),Game.TableDataManager:GetLangStr('str_36079067486723'),Game.TableDataManager:GetLangStr('str_36079335922179'),Game.TableDataManager:GetLangStr('str_36079067471107'),Game.TableDataManager:GetLangStr('str_36079335906821'),Game.TableDataManager:GetLangStr('str_36079335900680'),Game.TableDataManager:GetLangStr('str_36079335922183'),Game.TableDataManager:GetLangStr('str_36079335902212'),Game.TableDataManager:GetLangStr('str_36079067486728'),}, 
            ['TipsID'] = 85, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530615808'),
        },
        [1086] = {
            ['CrossWordList'] = {{['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486977'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472899'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067471106'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486981'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067486982'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067469059'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902722'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1086, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067485699'),Game.TableDataManager:GetLangStr('str_36079067471106'),Game.TableDataManager:GetLangStr('str_36079067465993'),Game.TableDataManager:GetLangStr('str_36079067472899'),Game.TableDataManager:GetLangStr('str_36079067465993'),Game.TableDataManager:GetLangStr('str_36079067469059'),Game.TableDataManager:GetLangStr('str_36079067486981'),Game.TableDataManager:GetLangStr('str_36079335902981'),Game.TableDataManager:GetLangStr('str_38277822331136'),}, 
            ['TipsID'] = 86, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530616064'),
        },
        [1087] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465478'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487235'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487237'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487238'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079335900680'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067483395'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474948'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1087, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067478536'),Game.TableDataManager:GetLangStr('str_36079067487237'),Game.TableDataManager:GetLangStr('str_36079067465478'),Game.TableDataManager:GetLangStr('str_36079335922692'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079067487238'),Game.TableDataManager:GetLangStr('str_36079067478539'),Game.TableDataManager:GetLangStr('str_36079067483395'),Game.TableDataManager:GetLangStr('str_36079067487235'),}, 
            ['TipsID'] = 87, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530616320'),
        },
        [1088] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487489'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487490'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067474696'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487492'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487493'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067473409'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480580'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487496'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1088, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079067474696'),Game.TableDataManager:GetLangStr('str_36079067487493'),Game.TableDataManager:GetLangStr('str_36079067487490'),Game.TableDataManager:GetLangStr('str_36079335909889'),Game.TableDataManager:GetLangStr('str_36079067485957'),Game.TableDataManager:GetLangStr('str_54838947746304'),Game.TableDataManager:GetLangStr('str_36079335922951'),Game.TableDataManager:GetLangStr('str_36079067473409'),Game.TableDataManager:GetLangStr('str_36079067487492'),}, 
            ['TipsID'] = 88, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530616576'),
        },
        [1089] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487745'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487746'),}, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468037'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335902211'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067465217'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079335901188'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067478281'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487752'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067487753'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1089, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335923201'),Game.TableDataManager:GetLangStr('str_36079067487752'),Game.TableDataManager:GetLangStr('str_36079335902211'),Game.TableDataManager:GetLangStr('str_36079067475464'),Game.TableDataManager:GetLangStr('str_36079335901188'),Game.TableDataManager:GetLangStr('str_36079335923206'),Game.TableDataManager:GetLangStr('str_36079067468037'),Game.TableDataManager:GetLangStr('str_36079335923208'),Game.TableDataManager:GetLangStr('str_36079067465217'),}, 
            ['TipsID'] = 89, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530616832'),
        },
        [1090] = {
            ['CrossWordList'] = {{['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067488001'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468291'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067472391'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335923201'),}, {['type'] = 0, }, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067468293'),}, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067480331'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067488007'),}, {['type'] = 2, ['word'] = Game.TableDataManager:GetLangStr('str_36079067488008'),}, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 0, }, {['type'] = 3, ['word'] = Game.TableDataManager:GetLangStr('str_36079067488009'),}, {['type'] = 1, ['word'] = Game.TableDataManager:GetLangStr('str_36079335910145'),}, {['type'] = 0, }, {['type'] = 0, }, }, 
            ['PuzzleID'] = 1090, 
            ['SelectWordList'] = {Game.TableDataManager:GetLangStr('str_36079335923201'),Game.TableDataManager:GetLangStr('str_36079067473410'),Game.TableDataManager:GetLangStr('str_36079067488009'),Game.TableDataManager:GetLangStr('str_36079335910145'),Game.TableDataManager:GetLangStr('str_36079335923461'),Game.TableDataManager:GetLangStr('str_36079067480331'),Game.TableDataManager:GetLangStr('str_36079335923463'),Game.TableDataManager:GetLangStr('str_36079067468291'),Game.TableDataManager:GetLangStr('str_36079067488007'),}, 
            ['TipsID'] = 90, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_36078530617088'),
        },
    }
}
return TopData