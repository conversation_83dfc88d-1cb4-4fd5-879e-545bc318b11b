--
-- 表名: $ShowProperty_属性展示.xlsx  页名：$MagDetail_神秘详细
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["CalculateType"] = 2,
			["ShowProperty"] = {"Hp", "MaxHp"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421001,
		},
		[2] = {
			["ID"] = 2,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329557760'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"HpReg"},
			["ShowType"] = 2,
			["IsShowChange"] = false,
			["TipsID"] = 6421002,
		},
		[10] = {
			["ID"] = 10,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329554432'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Con"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421006,
		},
		[11] = {
			["ID"] = 11,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329554944'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Pow"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421007,
		},
		[12] = {
			["ID"] = 12,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_46043391592192'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Str"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421008,
		},
		[13] = {
			["ID"] = 13,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329555968'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Int"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421009,
		},
		[14] = {
			["ID"] = 14,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329556480'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Dex"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421010,
		},
		[20] = {
			["ID"] = 20,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992494081'),
			["CalculateType"] = 1,
			["ShowProperty"] = {"mAtkMin", "mAtkMax"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421013,
		},
		[21] = {
			["ID"] = 21,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992494082'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mPierce"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421017,
		},
		[22] = {
			["ID"] = 22,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992498434'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCrit"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421019,
		},
		[23] = {
			["ID"] = 23,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210377472'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritHurt"},
			["ShowType"] = 1,
			["IsShowChange"] = true,
			["TipsID"] = 6421021,
		},
		[40] = {
			["ID"] = 40,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789314816'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pDef"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421032,
		},
		[41] = {
			["ID"] = 41,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789315072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mDef"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421033,
		},
		[42] = {
			["ID"] = 42,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329572864'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pBlock"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421034,
		},
		[43] = {
			["ID"] = 43,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329580288'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mBlock"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421035,
		},
		[44] = {
			["ID"] = 44,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368807936'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCritAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421036,
		},
		[45] = {
			["ID"] = 45,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368808192'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421037,
		},
		[46] = {
			["ID"] = 46,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368808960'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCritDef"},
			["ShowType"] = 1,
			["IsShowChange"] = true,
			["TipsID"] = 6421038,
		},
		[47] = {
			["ID"] = 47,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368809216'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritDef"},
			["ShowType"] = 1,
			["IsShowChange"] = true,
			["TipsID"] = 6421039,
		},
		[60] = {
			["ID"] = 60,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210370816'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ChaosAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421045,
		},
		[61] = {
			["ID"] = 61,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329582336'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreChaosAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421046,
		},
		[62] = {
			["ID"] = 62,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210371072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"MysteryAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421047,
		},
		[63] = {
			["ID"] = 63,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329585152'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreMysteryAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421048,
		},
		[64] = {
			["ID"] = 64,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210371328'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AbundanceAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421049,
		},
		[65] = {
			["ID"] = 65,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329587968'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreAbundanceAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421050,
		},
		[66] = {
			["ID"] = 66,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210371584'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DarknessAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421051,
		},
		[67] = {
			["ID"] = 67,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329590784'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreDarknessAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421052,
		},
		[68] = {
			["ID"] = 68,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210371840'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"CalamityAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421053,
		},
		[69] = {
			["ID"] = 69,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329593600'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreCalamityAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421054,
		},
		[70] = {
			["ID"] = 70,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210372096'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DisorderAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421055,
		},
		[71] = {
			["ID"] = 71,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329596416'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreDisorderAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421056,
		},
		[72] = {
			["ID"] = 72,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210372352'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TenebrousAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421057,
		},
		[73] = {
			["ID"] = 73,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329599232'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreTenebrousAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421058,
		},
		[74] = {
			["ID"] = 74,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210372608'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"KnowledgeAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421059,
		},
		[75] = {
			["ID"] = 75,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329602048'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreKnowledgeAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421060,
		},
		[76] = {
			["ID"] = 76,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210372864'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FateAtk"},
			["ShowType"] = 0,
			["IsShowChange"] = true,
			["TipsID"] = 6421061,
		},
		[77] = {
			["ID"] = 77,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329604864'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreFateAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421062,
		},
		[78] = {
			["ID"] = 78,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329581824'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ChaosAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421063,
		},
		[79] = {
			["ID"] = 79,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329584640'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"MysteryAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421064,
		},
		[80] = {
			["ID"] = 80,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329587456'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AbundanceAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421065,
		},
		[81] = {
			["ID"] = 81,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329590272'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DarknessAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421066,
		},
		[82] = {
			["ID"] = 82,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329593088'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"CalamityAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421067,
		},
		[83] = {
			["ID"] = 83,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329595904'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DisorderAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421068,
		},
		[84] = {
			["ID"] = 84,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329598720'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TenebrousAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421069,
		},
		[85] = {
			["ID"] = 85,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329601536'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"KnowledgeAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421070,
		},
		[86] = {
			["ID"] = 86,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329604352'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FateAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421071,
		},
		[100] = {
			["ID"] = 100,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821248'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceAirborne"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421072,
		},
		[101] = {
			["ID"] = 101,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329606144'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AirborneAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421090,
		},
		[102] = {
			["ID"] = 102,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821504'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceDown"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421074,
		},
		[103] = {
			["ID"] = 103,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329607424'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DownAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421091,
		},
		[104] = {
			["ID"] = 104,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821760'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhancePull"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421076,
		},
		[105] = {
			["ID"] = 105,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329608704'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"PullAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421092,
		},
		[106] = {
			["ID"] = 106,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822016'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceTied"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421078,
		},
		[107] = {
			["ID"] = 107,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329609984'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TiedAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421093,
		},
		[108] = {
			["ID"] = 108,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822272'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSleep"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421080,
		},
		[109] = {
			["ID"] = 109,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329611264'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SleepAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421094,
		},
		[110] = {
			["ID"] = 110,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822528'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceDizzy"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421082,
		},
		[111] = {
			["ID"] = 111,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329612544'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DizzyAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421095,
		},
		[112] = {
			["ID"] = 112,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822784'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceFear"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421084,
		},
		[113] = {
			["ID"] = 113,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329613824'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FearAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421096,
		},
		[114] = {
			["ID"] = 114,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368823040'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSilence"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421086,
		},
		[115] = {
			["ID"] = 115,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329615104'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SilenceAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421097,
		},
		[116] = {
			["ID"] = 116,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368823296'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSlow"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421088,
		},
		[117] = {
			["ID"] = 117,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329616384'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SlowAnti"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421098,
		},
		[152] = {
			["ID"] = 152,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610080512'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"VisionaryHurtMulti_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421101,
		},
		[153] = {
			["ID"] = 153,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610080512'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"VisionaryHurtMulti_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421102,
		},
		[154] = {
			["ID"] = 154,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610080768'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FeatherwitHurtMulti_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421103,
		},
		[155] = {
			["ID"] = 155,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610080768'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FeatherwitHurtMulti_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421104,
		},
		[158] = {
			["ID"] = 158,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610081280'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"WarriorHurtMulti_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421119,
		},
		[159] = {
			["ID"] = 159,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610081280'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"WarriorHurtMulti_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421120,
		},
		[160] = {
			["ID"] = 160,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610081536'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ApprenticeHurtMulti_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421121,
		},
		[161] = {
			["ID"] = 161,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610081536'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ApprenticeHurtMulti_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421122,
		},
		[162] = {
			["ID"] = 162,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610078464'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"RaceHurtMulti4_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421107,
		},
		[163] = {
			["ID"] = 163,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610078464'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"RaceHurtMulti4_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421108,
		},
		[166] = {
			["ID"] = 166,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"VisionaryHurtReduce_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421111,
		},
		[167] = {
			["ID"] = 167,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"VisionaryHurtReduce_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421112,
		},
		[168] = {
			["ID"] = 168,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083328'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FeatherwitHurtReduce_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421113,
		},
		[169] = {
			["ID"] = 169,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083328'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FeatherwitHurtReduce_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421114,
		},
		[172] = {
			["ID"] = 172,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083840'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"WarriorHurtReduce_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421123,
		},
		[173] = {
			["ID"] = 173,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610083840'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"WarriorHurtReduce_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421124,
		},
		[174] = {
			["ID"] = 174,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610084096'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ApprenticeHurtReduce_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421125,
		},
		[175] = {
			["ID"] = 175,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610084096'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ApprenticeHurtReduce_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421126,
		},
		[176] = {
			["ID"] = 176,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610079744'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"RaceHurtReduce4_N"},
			["ShowType"] = 0,
			["IsShowChange"] = false,
			["TipsID"] = 6421117,
		},
		[177] = {
			["ID"] = 177,
			["Title"] = 6,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24121610079744'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"RaceHurtReduce4_P"},
			["ShowType"] = 1,
			["IsShowChange"] = false,
			["TipsID"] = 6421118,
		},
	},
}

return TopData
