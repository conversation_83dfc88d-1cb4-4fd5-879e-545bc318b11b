--
-- 表名: $Player_角色.xlsx  页名：$PlayerSocialDisplay_社交展示
--

local TopData = {
	data = {
		[1200001] = {
			[0] = {
				["ClassID"] = 1200001,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40683540841984'),
				["PositionType"] = {1},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Eulogist.UI_Character_Icon_Eulogist",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Eulogist.UI_CreateRole_Icon_Name_Eulogist",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head02.UI_Character_Icon_Head02",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Sun.UI_Pro_Bg_Arbitrator_Sun",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Sun.UI_Partner_Img_Sun",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["HeadColor"] = "HeadSun",
				["SkillIconColor"] = "Sun",
				["StatColor"] = "3",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Sun01.UI_Character_Img_Bust_Sun01",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecSun_Sprite.UI_Social_Img_ContactsLabelDecSun_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation05.UI_Com_Img_Occupation05",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
			[1] = {
				["ClassID"] = 1200001,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40683540841984'),
				["PositionType"] = {1},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Eulogist.UI_Character_Icon_Eulogist",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Eulogist.UI_CreateRole_Icon_Name_Eulogist",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head02.UI_Character_Icon_Head02",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Sun.UI_Pro_Bg_Arbitrator_Sun",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Sun.UI_Partner_Img_Sun",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["HeadColor"] = "HeadSun",
				["SkillIconColor"] = "Sun",
				["StatColor"] = "3",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Sun02.UI_Character_Img_Bust_Sun02",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecSun_Sprite.UI_Social_Img_ContactsLabelDecSun_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation05.UI_Com_Img_Occupation05",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200002] = {
			[1] = {
				["ClassID"] = 1200002,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_36972689108224'),
				["PositionType"] = {2},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Audience.UI_Character_Icon_Audience",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Audience.UI_CreateRole_Icon_Name_Audience",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head03.UI_Character_Icon_Head03",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Vision.UI_Pro_Bg_Arbitrator_Vision",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Audience.UI_Partner_Img_Audience",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["HeadColor"] = "HeadUtopian",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "2",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Visionary.UI_Character_Img_Bust_Visionary",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecGroup_Sprite.UI_Social_Img_ContactsLabelDecGroup_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo04_Sprite.UI_Tips_Img_Logo04_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation02.UI_Com_Img_Occupation02",
				["RolePlayIdleAnim"] = "/Game/Arts/Character/Animation/Player/Act_HighRes/Visionary/SK/A_Vis_Mask_Idle.A_Vis_Mask_Idle",
				["NeedArmRotateOffset"] = true,
			},
			[0] = {
				["ClassID"] = 1200002,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_36972689108224'),
				["PositionType"] = {2},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Audience.UI_Character_Icon_Audience",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Audience.UI_CreateRole_Icon_Name_Audience",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head03.UI_Character_Icon_Head03",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Vision.UI_Pro_Bg_Arbitrator_Vision",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Audience.UI_Partner_Img_Audience",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadVisionary.UI_Character_Icon_HeadVisionary",
				["HeadColor"] = "HeadUtopian",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "2",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Bust/UI_Character_Icon_UtopianDrawing_Bust.UI_Character_Icon_UtopianDrawing_Bust",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecGroup_Sprite.UI_Social_Img_ContactsLabelDecGroup_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo04_Sprite.UI_Tips_Img_Logo04_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation02.UI_Com_Img_Occupation02",
				["RolePlayIdleAnim"] = "/Game/Arts/Character/Animation/Player/Act_HighRes/Visionary/SK/A_Vis_Mask_Idle.A_Vis_Mask_Idle",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200003] = {
			[0] = {
				["ClassID"] = 1200003,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_57862873170432'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_FortuneTeller.UI_Character_Icon_FortuneTeller",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_FortuneTeller.UI_CreateRole_Icon_Name_FortuneTeller",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head01.UI_Character_Icon_Head01",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Fool.UI_Pro_Bg_Arbitrator_Fool",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Fool.UI_Partner_Img_Fool",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["HeadColor"] = "HeadFool",
				["SkillIconColor"] = "Fool",
				["StatColor"] = "1",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Fool01.UI_Character_Img_Bust_Fool01",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecFool_Sprite.UI_Social_Img_ContactsLabelDecFool_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo03_Sprite.UI_Tips_Img_Logo03_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation03.UI_Com_Img_Occupation03",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
			[1] = {
				["ClassID"] = 1200003,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_57862873170432'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_FortuneTeller.UI_Character_Icon_FortuneTeller",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_FortuneTeller.UI_CreateRole_Icon_Name_FortuneTeller",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head01.UI_Character_Icon_Head01",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Fool.UI_Pro_Bg_Arbitrator_Fool",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Fool.UI_Partner_Img_Fool",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadFool.UI_Character_Icon_HeadFool",
				["HeadColor"] = "HeadFool",
				["SkillIconColor"] = "Fool",
				["StatColor"] = "1",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Fool02.UI_Character_Img_Bust_Fool02",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecFool_Sprite.UI_Social_Img_ContactsLabelDecFool_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo03_Sprite.UI_Tips_Img_Logo03_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation03.UI_Com_Img_Occupation03",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200004] = {
			[1] = {
				["ClassID"] = 1200004,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40683540843520'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Arbitrator.UI_Character_Icon_Arbitrator",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Arbitrator.UI_CreateRole_Icon_Name_Arbitrator",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head04.UI_Character_Icon_Head04",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "4",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Arbiter02.UI_Character_Img_Bust_Arbiter02",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation04.UI_Com_Img_Occupation04",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
			[0] = {
				["ClassID"] = 1200004,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_40683540843520'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Arbitrator.UI_Character_Icon_Arbitrator",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Arbitrator.UI_CreateRole_Icon_Name_Arbitrator",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head04.UI_Character_Icon_Head04",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadArbiter.UI_Character_Icon_HeadArbiter",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "4",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Bust/UI_Character_Icon_ArbitratorDrawing_Bust.UI_Character_Icon_ArbitratorDrawing_Bust",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation04.UI_Com_Img_Occupation04",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200005] = {
			[1] = {
				["ClassID"] = 1200005,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_38277822295552'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Apprentice.UI_Character_Icon_Apprentice",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Apprentice.UI_CreateRole_Icon_Name_Apprentice",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head05.UI_Character_Icon_Head05",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "0",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Apprentice_02.UI_Character_Img_Bust_Apprentice_02",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo01_Sprite.UI_Tips_Img_Logo01_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation01.UI_Com_Img_Occupation01",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
			[0] = {
				["ClassID"] = 1200005,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_38277822295552'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Apprentice.UI_Character_Icon_Apprentice",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Apprentice.UI_CreateRole_Icon_Name_Apprentice",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head05.UI_Character_Icon_Head05",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadApprentice.UI_Character_Icon_HeadApprentice",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "0",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Apprentice01.UI_Character_Img_Bust_Apprentice01",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo01_Sprite.UI_Tips_Img_Logo01_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation01.UI_Com_Img_Occupation01",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200006] = {
			[0] = {
				["ClassID"] = 1200006,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_38277822295808'),
				["PositionType"] = {1},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Warrior.UI_Character_Icon_Warrior",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Warrior.UI_CreateRole_Icon_Name_Warrior",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head06.UI_Character_Icon_Head06",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "5",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Warrior01.UI_Character_Img_Bust_Warrior01",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo02_Sprite.UI_Tips_Img_Logo02_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation06.UI_Com_Img_Occupation06",
				["RolePlayIdleAnim"] = "/Game/Arts/Character/Animation/Player/Act_HighRes/Warrior/SK/A_War_Mask_Idle.A_War_Mask_Idle",
				["NeedArmRotateOffset"] = false,
			},
			[1] = {
				["ClassID"] = 1200006,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_38277822295808'),
				["PositionType"] = {1},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Warrior.UI_Character_Icon_Warrior",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Warrior.UI_CreateRole_Icon_Name_Warrior",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head06.UI_Character_Icon_Head06",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Arbitrator.UI_Pro_Bg_Arbitrator_Arbitrator",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Arbitrator.UI_Partner_Img_Arbitrator",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadWarrior.UI_Character_Icon_HeadWarrior",
				["HeadColor"] = "HeadArbitrator",
				["SkillIconColor"] = "Utopian",
				["StatColor"] = "5",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Bust/UI_Character_Icon_ArbitratorDrawing_Bust.UI_Character_Icon_ArbitratorDrawing_Bust",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecArbitrator_Sprite.UI_Social_Img_ContactsLabelDecArbitrator_Sprite",
				["Card"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Img_Logo02_Sprite.UI_Tips_Img_Logo02_Sprite",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Character/PlayerCharacter/UI_Com_Img_Occupation06.UI_Com_Img_Occupation06",
				["RolePlayIdleAnim"] = "/Game/Arts/Character/Animation/Player/Act_HighRes/Warrior/SK/A_War_Mask_Idle.A_War_Mask_Idle",
				["NeedArmRotateOffset"] = false,
			},
		},
		[1200007] = {
			[0] = {
				["ClassID"] = 1200007,
				["Sex"] = 0,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_57862873169920'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Eulogist.UI_Character_Icon_Eulogist",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Eulogist.UI_CreateRole_Icon_Name_Eulogist",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head02.UI_Character_Icon_Head02",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Sun.UI_Pro_Bg_Arbitrator_Sun",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Sun.UI_Partner_Img_Sun",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["HeadColor"] = "HeadSun",
				["SkillIconColor"] = "Sun",
				["StatColor"] = "3",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Sun01.UI_Character_Img_Bust_Sun01",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecSun_Sprite.UI_Social_Img_ContactsLabelDecSun_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
			[1] = {
				["ClassID"] = 1200007,
				["Sex"] = 1,
				["ClassName"] = Game.TableDataManager:GetLangStr('str_57862873169920'),
				["PositionType"] = {0},
				["ClassLogoHud"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["ClassLogo"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Profession/UI_Character_Icon_Eulogist.UI_Character_Icon_Eulogist",
				["ClassLogoBG"] = "/Game/Arts/UI_2/Resource/CreateRole_2/NotAtlas/UI_CreateRole_Icon_Name_Eulogist.UI_CreateRole_Icon_Name_Eulogist",
				["SoloHeadIcon"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Head/UI_Character_Icon_Head02.UI_Character_Icon_Head02",
				["TeamHeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["TeamPagePath"] = "/Game/Arts/UI_2/Resource/Character/NotAtlas/Pro/UI_Pro_Bg_Arbitrator_Sun.UI_Pro_Bg_Arbitrator_Sun",
				["FriendRecommend"] = "/Game/Arts/UI_2/Resource/Partner_2/NotAtlas/UI_Partner_Img_Sun.UI_Partner_Img_Sun",
				["HeadIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["HeadColor"] = "HeadSun",
				["SkillIconColor"] = "Sun",
				["StatColor"] = "3",
				["Drawing"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/Bust/UI_Character_Img_Bust_Sun02.UI_Character_Img_Bust_Sun02",
				["InitialSceneCustomIcon"] = "/Game/Arts/UI_2/Resource/Fashion/Atlas/Sprite01/UI_Fashion_Icon_Costume14_Sprite.UI_Fashion_Icon_Costume14_Sprite",
				["FriendList"] = "/Game/Arts/UI_2/Resource/Social/Atlas/Sprite01/UI_Social_Img_ContactsLabelDecSun_Sprite.UI_Social_Img_ContactsLabelDecSun_Sprite",
				["Card"] = "",
				["SocialSmallIcon"] = "/Game/Arts/UI_2/Resource/Character_2/NotAtlas/SimplifyCareers/UI_Character_Icon_HeadSun.UI_Character_Icon_HeadSun",
				["RolePlayIdleAnim"] = "",
				["NeedArmRotateOffset"] = false,
			},
		},
	},
}

return TopData
