--
-- 表名: RegionClimateData后处理
--

local TopData = {
    LevelBelongRegionMap = {
        [5200004] = 5, 
        [5200009] = 5, 
        [5200010] = 5, 
        [5200050] = 6, 
    },
    data = {
        [5] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {5200004, 5200009, 5200010}, 
            ['LevelMapID'] = 5200002, 
            ['MapFieldId'] = 1, 
            ['RegionID'] = 5, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_34983850806016'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = true, 
        },
        [6] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {5200050}, 
            ['LevelMapID'] = 5200002, 
            ['MapFieldId'] = 2, 
            ['RegionID'] = 6, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_34983850805504'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = true, 
        },
        [7] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {}, 
            ['LevelMapID'] = 5200002, 
            ['MapFieldId'] = 3, 
            ['RegionID'] = 7, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_34983850805760'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = true, 
        },
        [8] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {}, 
            ['LevelMapID'] = 5200007, 
            ['MapFieldId'] = 0, 
            ['RegionID'] = 8, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_34983850814720'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = true, 
        },
        [9] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {}, 
            ['LevelMapID'] = 5200027, 
            ['MapFieldId'] = 0, 
            ['RegionID'] = 9, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_32574642591488'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = false, 
        },
        [10] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {}, 
            ['LevelMapID'] = 5200021, 
            ['MapFieldId'] = 0, 
            ['RegionID'] = 10, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_5636339273728'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = false, 
        },
        [11] = {
            ['AccumSwitchWeight'] = {{{['accumWeight'] = 1, ['climate'] = 1, }, }, }, 
            ['DefaultClimateID'] = 1, 
            ['IndoorID'] = {}, 
            ['LevelMapID'] = 5200102, 
            ['MapFieldId'] = 0, 
            ['RegionID'] = 11, 
            ['RegionName'] = Game.TableDataManager:GetLangStr('str_5636339273984'),
            ['SwitchTime'] = {0.1, 0.15}, 
            ['SwitchTrigger'] = {}, 
            ['SwitchWeight'] = {{1}, }, 
            ['Tag'] = '', 
            ['bShowinList'] = false, 
        },
    }
}
return TopData