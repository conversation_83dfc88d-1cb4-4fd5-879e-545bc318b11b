--
-- 表名: $ShowProperty_属性展示.xlsx  页名：$CommonBrief_通用简要
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_56007178852096'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Level"},
			["ShowType"] = 0,
		},
		[2] = {
			["ID"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789326848'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"Exp"},
			["ShowType"] = 0,
		},
		[3] = {
			["ID"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["CalculateType"] = 2,
			["ShowProperty"] = {"Hp", "MaxHp"},
			["ShowType"] = 0,
		},
	},
}

return TopData
