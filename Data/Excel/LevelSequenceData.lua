--
-- 表名: $Cutscene.xlsx  页名：$LevelSequence
--

local TopData = {
	data = {
		[1000001] = {
			["ID"] = 1000001,
			["AssetPath"] = "/Game/Arts/Cinematics/Dungeon/LV_Factory/bbcc/Sequence/C7_F_bbcc.C7_F_bbcc",
		},
		[1000002] = {
			["ID"] = 1000002,
			["AssetPath"] = "/Game/Arts/Maps/5V5Arena/SQ_putong_rexue.SQ_putong_rexue",
		},
		[1000003] = {
			["ID"] = 1000003,
			["AssetPath"] = "/Game/Arts/Maps/5V5Arena/SQ_rexue_putong.SQ_rexue_putong",
		},
		[1000004] = {
			["ID"] = 1000004,
			["AssetPath"] = "/Game/Arts/Maps/5V5Arena/SQ_5V5.SQ_5V5",
		},
		[1100004] = {
			["ID"] = 1100004,
			["AssetPath"] = "/Game/Arts/Effects/FX_Common/Intelligence/Seq_Intelligence.Seq_Intelligence",
		},
		[1000005] = {
			["ID"] = 1000005,
			["AssetPath"] = "/Game/Arts/Effects/FX_Envrinment/12V12/MenPosui/Seq_Posui.Seq_Posui",
		},
		[1000007] = {
			["ID"] = 1000007,
			["AssetPath"] = "/Game/Arts/Effects/FX_Common/MysteryEvent/Seq_MysteryEvent.Seq_MysteryEvent",
		},
		[1000008] = {
			["ID"] = 1000008,
			["AssetPath"] = "/Game/Arts/Cinematics/BlessingInscription/InscriptionUnlocking_Night.InscriptionUnlocking_Night",
		},
		[1000009] = {
			["ID"] = 1000009,
			["AssetPath"] = "/Game/Arts/Cinematics/BlessingInscription/InscriptionUp_Night.InscriptionUp_Night",
		},
		[1000010] = {
			["ID"] = 1000010,
			["AssetPath"] = "/Game/Arts/Cinematics/BlessingInscription/InscriptionUnlocking_Strom.InscriptionUnlocking_Strom",
		},
		[1000011] = {
			["ID"] = 1000011,
			["AssetPath"] = "/Game/Arts/Cinematics/BlessingInscription/InscriptionUp_Storm.InscriptionUp_Storm",
		},
		[1000023] = {
			["ID"] = 1000023,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/SideStory/UnfulfilledDreams/UnfulfilledDreams_MemberChange2.UnfulfilledDreams_MemberChange2",
		},
		[1000024] = {
			["ID"] = 1000024,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/SideStory/UnfulfilledDreams/UnfulfilledDreams_MemberChange.UnfulfilledDreams_MemberChange",
		},
		[1000025] = {
			["ID"] = 1000025,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/AutumnLake_OpenGate.AutumnLake_OpenGate",
		},
		[1000026] = {
			["ID"] = 1000026,
			["AssetPath"] = "/Game/Arts/Environment/Animations/GrassGrow/GrassGrow.GrassGrow",
		},
		[1000027] = {
			["ID"] = 1000027,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/AutumnLake_Bearwoman.AutumnLake_Bearwoman",
		},
		[1000028] = {
			["ID"] = 1000028,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Environment/Tiengen_Fenwei_TieShizi/Zhuzi_001.Zhuzi_001",
		},
		[1000029] = {
			["ID"] = 1000029,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Environment/Tiengen_Fenwei_TieShizi/Zhuzi_002.Zhuzi_002",
		},
		[1000030] = {
			["ID"] = 1000030,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Environment/Tiengen_Fenwei_TieShizi/Zhuzi_004.Zhuzi_004",
		},
		[1000031] = {
			["ID"] = 1000031,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Environment/Tiengen_Fenwei_TieShizi/Zhuzi_005.Zhuzi_005",
		},
		[1000032] = {
			["ID"] = 1000032,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/GrassGrow/GrassGrow_A.GrassGrow_A",
		},
		[1000033] = {
			["ID"] = 1000033,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/GrassGrow/GrassGrow_B.GrassGrow_B",
		},
		[1000034] = {
			["ID"] = 1000034,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/GrassGrow/GrassGrow_C.GrassGrow_C",
		},
		[1000035] = {
			["ID"] = 1000035,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/AutumnLake/GrassGrow/GrassGrow_End.GrassGrow_End",
		},
		[1000036] = {
			["ID"] = 1000036,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/DreamDark/Fog_Dreem.Fog_Dreem",
		},
		[1000037] = {
			["ID"] = 1000037,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_Knight/Seq_Boss_Knight_Skill07_01.Seq_Boss_Knight_Skill07_01",
		},
		[1000038] = {
			["ID"] = 1000038,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_Knight/Seq_Boss_Knight_Skill07_02.Seq_Boss_Knight_Skill07_02",
		},
		[1000039] = {
			["ID"] = 1000039,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_Knight/Seq_Boss_Knight_Skill07_03.Seq_Boss_Knight_Skill07_03",
		},
		[1000040] = {
			["ID"] = 1000040,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/DungeonTest/LTZX/Box_Drop.Box_Drop",
		},
		[1000041] = {
			["ID"] = 1000041,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier1_Memory.Nier1_Memory",
		},
		[1000042] = {
			["ID"] = 1000042,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier2_Memory.Nier2_Memory",
		},
		[1000043] = {
			["ID"] = 1000043,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/MainStory/000_Prologue/Taskcollect_Fuqiyinide.Taskcollect_Fuqiyinide",
		},
		[1000044] = {
			["ID"] = 1000044,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_Knight/Seq_Boss_Knight_Die.Seq_Boss_Knight_Die",
		},
		[1000045] = {
			["ID"] = 1000045,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier5_Memory.Nier5_Memory",
		},
		[1000046] = {
			["ID"] = 1000046,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier6_Memory.Nier6_Memory",
		},
		[1000047] = {
			["ID"] = 1000047,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier7_Memory.Nier7_Memory",
		},
		[1000048] = {
			["ID"] = 1000048,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier3_Memory.Nier3_Memory",
		},
		[1000049] = {
			["ID"] = 1000049,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Dungeon_Memory/LTZX_Test/Nier4_Memory.Nier4_Memory",
		},
		[1000050] = {
			["ID"] = 1000050,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_CF_01.Boss_Battle_Skill_CF_01",
		},
		[1000051] = {
			["ID"] = 1000051,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_CF_02.Boss_Battle_Skill_CF_02",
		},
		[1000052] = {
			["ID"] = 1000052,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_CF_03.Boss_Battle_Skill_CF_03",
		},
		[1000053] = {
			["ID"] = 1000053,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_CF_04.Boss_Battle_Skill_CF_04",
		},
		[1000054] = {
			["ID"] = 1000054,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/SampleAndTest/LookAtDesignSample.LookAtDesignSample",
		},
		[1000055] = {
			["ID"] = 1000055,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/SampleAndTest/BackToMarkSample.BackToMarkSample",
		},
		[1000056] = {
			["ID"] = 1000056,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_LJ_01.Boss_Battle_Skill_LJ_01",
		},
		[1000057] = {
			["ID"] = 1000057,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_LJ_02.Boss_Battle_Skill_LJ_02",
		},
		[1000058] = {
			["ID"] = 1000058,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Tiengen_Memroy/Klein_Moving.Klein_Moving",
		},
		[1000059] = {
			["ID"] = 1000059,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Memory/Tiengen_Memroy/Klein_dead.Klein_dead",
		},
		[1000060] = {
			["ID"] = 1000060,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/Boss_XSRQ/Boss_Battle_Skill_LJ_03.Boss_Battle_Skill_LJ_03",
		},
		[1000061] = {
			["ID"] = 1000061,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/MainStory/000_Bridge/Feifanjishi_Bridge.Feifanjishi_Bridge",
		},
		[1000062] = {
			["ID"] = 1000062,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/MainStory/000_Bridge/Feifanjishi_Bookshelf_2.Feifanjishi_Bookshelf_2",
		},
		[1000063] = {
			["ID"] = 1000063,
			["AssetPath"] = "/Game/Arts/Cinematics/Design/Others/SampleAndTest/NPCLocalDriveSample.NPCLocalDriveSample",
		},
	},
}

return TopData
