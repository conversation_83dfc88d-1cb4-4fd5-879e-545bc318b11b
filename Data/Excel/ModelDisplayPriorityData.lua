--
-- 表名: NEW Inputs: (AoiLevel.xlsx, ModelDisplayPriority); Outputs: ModelDisplayPriorityData; Def:data_post_export_ModelDisplayPriority.lua
--

local TopData = {
    ModelClipShowPriorityMap = {{5, 6, 2, 3, 4}, {2, 3, 5, 6, 7}, {5, 6, 4, 7, 2}, 
    },
    data = {
        [1] = {
            ['Distance'] = 7, 
            ['Enemy'] = 4, 
            ['GroupMate'] = 6, 
            ['ID'] = 1, 
            ['Lover'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319360'),
            ['ShowMode'] = 'SMART_MODE', 
            ['SwornBrothers'] = 3, 
            ['TeamMate'] = 5, 
        },
        [2] = {
            ['Distance'] = 4, 
            ['Enemy'] = 7, 
            ['GroupMate'] = 3, 
            ['ID'] = 2, 
            ['Lover'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319616'),
            ['ShowMode'] = 'FRIENDLY_FIRST_MODE', 
            ['SwornBrothers'] = 6, 
            ['TeamMate'] = 2, 
        },
        [3] = {
            ['Distance'] = 3, 
            ['Enemy'] = 2, 
            ['GroupMate'] = 6, 
            ['ID'] = 3, 
            ['Lover'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319872'),
            ['ShowMode'] = 'ENEMY_FIRST_MODE', 
            ['SwornBrothers'] = 7, 
            ['TeamMate'] = 5, 
        },
    }
}
return TopData