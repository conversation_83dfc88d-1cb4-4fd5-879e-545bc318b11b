--
-- 表名: $NewBieGuide_新手引导.xlsx  页名：$GuideImgText_图文引导
--

local TopData = {
	data = {
		[6720001] = {
			["ID"] = 6720001,
			["Title"] = Game.TableDataManager:GetLangStr('str_32025155209984'),
			["UIID"] = "P_HUDBaseView",
			["Img"] = {"/Game/Arts/UI_2/Resource/HUD/NotAtlas/HUD_MiniMap/UI_HUD_MiniMap_Img_Arena.UI_HUD_MiniMap_Img_Arena"},
			["GuideText"] = Game.TableDataManager:GetLangStr('str_37797322819072'),
		},
		[6720002] = {
			["ID"] = 6720002,
			["Title"] = Game.TableDataManager:GetLangStr('str_37796517512960'),
			["UIID"] = "P_HUDBaseView",
			["Img"] = {"/Game/Arts/UI_2/Resource/HUD/NotAtlas/HUD_MiniMap/UI_HUD_MiniMap_Img_Arena.UI_HUD_MiniMap_Img_Arena", "/Game/Arts/UI_2/Resource/HUD/NotAtlas/HUD_MiniMap/UI_HUD_MiniMap_Img_Arena.UI_HUD_MiniMap_Img_Arena"},
			["GuideText"] = Game.TableDataManager:GetLangStr('str_37797322819328'),
		},
	},
}

return TopData
