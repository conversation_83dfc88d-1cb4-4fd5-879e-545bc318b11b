--
-- 表名: $RoleCreate.xlsx  页名：$QandA
--

local TopData = {
	data = {
		[1] = {
			["QuestionID"] = 1,
			["Option1"] = Game.TableDataManager:GetLangStr('str_40957881879041'),
			["Option1Eng"] = "Sanity",
			["Option2"] = Game.TableDataManager:GetLangStr('str_40957881879042'),
			["Option2Eng"] = "Emotion",
		},
		[2] = {
			["QuestionID"] = 2,
			["Option1"] = Game.TableDataManager:GetLangStr('str_46042854721280'),
			["Option1Eng"] = "Knowledge",
			["Option2"] = Game.TableDataManager:GetLangStr('str_46043391592192'),
			["Option2Eng"] = "Power",
		},
		[3] = {
			["QuestionID"] = 3,
			["Option1"] = Game.TableDataManager:GetLangStr('str_46042854721536'),
			["Option1Eng"] = "Chaos",
			["Option2"] = Game.TableDataManager:GetLangStr('str_40957881879297'),
			["Option2Eng"] = "Order",
		},
	},
}

return TopData
