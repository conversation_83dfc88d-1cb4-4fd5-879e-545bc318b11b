--
-- 表名: DigestionConditionData后处理
--

local TopData = {
    data = {
        [1] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132336640'),
            ['ID'] = 1, 
            ['Para1'] = 2, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 2, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [2] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132336896'),
            ['ID'] = 2, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 400, 
            ['Target'] = 1, 
            ['TargetNum'] = 1, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 1, 
        },
        [3] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132337152'),
            ['ID'] = 3, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 401, 
            ['Target'] = 2, 
            ['TargetNum'] = 2, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 1, 
        },
        [4] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132337408'),
            ['ID'] = 4, 
            ['Para1'] = 3, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 3, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [5] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132337664'),
            ['ID'] = 5, 
            ['Para1'] = 4, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 4, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [6] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132337920'),
            ['ID'] = 6, 
            ['Para1'] = 5, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 5, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [7] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132338176'),
            ['ID'] = 7, 
            ['Para1'] = 12, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 12, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [8] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132338432'),
            ['ID'] = 8, 
            ['Para1'] = 13, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 13, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [9] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132338688'),
            ['ID'] = 9, 
            ['Para1'] = 14, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 14, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [10] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132338944'),
            ['ID'] = 10, 
            ['Para1'] = 22, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 22, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [11] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132339200'),
            ['ID'] = 11, 
            ['Para1'] = 23, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 23, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [12] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132339456'),
            ['ID'] = 12, 
            ['Para1'] = 24, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 24, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [13] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132339712'),
            ['ID'] = 13, 
            ['Para1'] = 25, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 25, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [14] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132339968'),
            ['ID'] = 14, 
            ['Para1'] = 26, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 26, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [15] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132340224'),
            ['ID'] = 15, 
            ['Para1'] = 27, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 27, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [16] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132340480'),
            ['ID'] = 16, 
            ['Para1'] = 32, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 32, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [17] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132340736'),
            ['ID'] = 17, 
            ['Para1'] = 33, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 33, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [18] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132340992'),
            ['ID'] = 18, 
            ['Para1'] = 34, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 34, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [19] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132341248'),
            ['ID'] = 19, 
            ['Para1'] = 35, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 35, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [20] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132341504'),
            ['ID'] = 20, 
            ['Para1'] = 36, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 36, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [21] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132341760'),
            ['ID'] = 21, 
            ['Para1'] = 42, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 42, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [22] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132342016'),
            ['ID'] = 22, 
            ['Para1'] = 43, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 43, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [23] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132342272'),
            ['ID'] = 23, 
            ['Para1'] = 44, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 44, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [24] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132342528'),
            ['ID'] = 24, 
            ['Para1'] = 52, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 52, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [25] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132342784'),
            ['ID'] = 25, 
            ['Para1'] = 53, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 53, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [26] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132343040'),
            ['ID'] = 26, 
            ['Para1'] = 54, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 54, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [27] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132343296'),
            ['ID'] = 27, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [28] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132343552'),
            ['ID'] = 28, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [29] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132343808'),
            ['ID'] = 29, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [30] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132344064'),
            ['ID'] = 30, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [31] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132344320'),
            ['ID'] = 31, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [32] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132344576'),
            ['ID'] = 32, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [33] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132344832'),
            ['ID'] = 33, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [34] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132345088'),
            ['ID'] = 34, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [35] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132345344'),
            ['ID'] = 35, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [36] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132345600'),
            ['ID'] = 36, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [37] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132345856'),
            ['ID'] = 37, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
        [38] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_51609132346112'),
            ['ID'] = 38, 
            ['Para1'] = 0, 
            ['Para2'] = 0, 
            ['Para4'] = 0, 
            ['StatisticsID'] = 0, 
            ['Target'] = 0, 
            ['TargetNum'] = 0, 
            ['TriggerID'] = 6500104, 
            ['Type'] = 2, 
        },
    }
}
return TopData