--
-- 表名: $PVPBase_PVP玩法配置表.xlsx  页名：$PVPEntranceInfo_竞技入口表
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["PVPType"] = 1,
			["IsLock"] = false,
			["Name"] = Game.TableDataManager:GetLangStr('str_32574642590208'),
			["SubName"] = Game.TableDataManager:GetLangStr('str_42676405667328'),
			["UIJump"] = 1250022,
			["TimeDesc"] = Game.TableDataManager:GetLangStr('str_42676942538240'),
			["IsDisplayRankIcon"] = true,
			["GameMode"] = 5500002,
		},
		[2] = {
			["ID"] = 2,
			["PVPType"] = 1,
			["IsLock"] = false,
			["Name"] = Game.TableDataManager:GetLangStr('str_44599745710848'),
			["SubName"] = Game.TableDataManager:GetLangStr('str_42676405667584'),
			["UIJump"] = 1250062,
			["TimeDesc"] = Game.TableDataManager:GetLangStr('str_42676942538240'),
			["IsDisplayRankIcon"] = true,
			["GameMode"] = 5500003,
		},
		[3] = {
			["ID"] = 3,
			["PVPType"] = 1,
			["IsLock"] = true,
			["Name"] = Game.TableDataManager:GetLangStr('str_42676137232384'),
			["SubName"] = Game.TableDataManager:GetLangStr('str_42676405667840'),
			["UIJump"] = 1250022,
			["TimeDesc"] = "",
			["IsDisplayRankIcon"] = false,
			["GameMode"] = 0,
		},
	},
}

return TopData
