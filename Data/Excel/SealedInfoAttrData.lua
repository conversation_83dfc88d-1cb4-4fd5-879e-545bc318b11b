--
-- 表名: SealedInfoAttrData后处理
--

local TopData = {
    GroupCondMap = {{{['Condition'] = 1, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477347840'),['FightProp'] = {['MaxHp_N'] = 336, }, ['Group'] = 1, ['ID'] = 1, ['Level'] = 1, ['Mark'] = 1, }, {['Condition'] = 2, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348096'),['FightProp'] = {['MaxHp_N'] = 672, }, ['Group'] = 1, ['ID'] = 2, ['Level'] = 2, ['Mark'] = 2, }, {['Condition'] = 3, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348352'),['FightProp'] = {['MaxHp_N'] = 840, }, ['Group'] = 1, ['ID'] = 3, ['Level'] = 3, ['Mark'] = 3, }, {['Condition'] = 4, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348608'),['FightProp'] = {['MaxHp_N'] = 1008, }, ['Group'] = 1, ['ID'] = 4, ['Level'] = 4, ['Mark'] = 4, }, {['Condition'] = 5, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348864'),['FightProp'] = {['MaxHp_N'] = 1176, }, ['Group'] = 1, ['ID'] = 5, ['Level'] = 5, ['Mark'] = 5, }, {['Condition'] = 6, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349120'),['FightProp'] = {['MaxHp_N'] = 1345, }, ['Group'] = 1, ['ID'] = 6, ['Level'] = 6, ['Mark'] = 6, }, }, {{['Condition'] = 1, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349376'),['FightProp'] = {['MaxHp_N'] = 1261, }, ['Group'] = 2, ['ID'] = 7, ['Level'] = 1, ['Mark'] = 1, }, {['Condition'] = 2, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349632'),['FightProp'] = {['MaxHp_N'] = 1345, }, ['Group'] = 2, ['ID'] = 8, ['Level'] = 2, ['Mark'] = 2, }, {['Condition'] = 3, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349888'),['FightProp'] = {['MaxHp_N'] = 1429, }, ['Group'] = 2, ['ID'] = 9, ['Level'] = 3, ['Mark'] = 3, }, {['Condition'] = 4, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350144'),['FightProp'] = {['MaxHp_N'] = 1513, }, ['Group'] = 2, ['ID'] = 10, ['Level'] = 4, ['Mark'] = 4, }, {['Condition'] = 5, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350400'),['FightProp'] = {['MaxHp_N'] = 1597, }, ['Group'] = 2, ['ID'] = 11, ['Level'] = 5, ['Mark'] = 5, }, {['Condition'] = 6, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350656'),['FightProp'] = {['MaxHp_N'] = 1681, }, ['Group'] = 2, ['ID'] = 12, ['Level'] = 6, ['Mark'] = 6, }, }, {{['Condition'] = 1, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350912'),['FightProp'] = {['MaxHp_P'] = 0.01, }, ['Group'] = 3, ['ID'] = 13, ['Level'] = 1, ['Mark'] = 1, }, {['Condition'] = 2, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351168'),['FightProp'] = {['MaxHp_P'] = 0.02, }, ['Group'] = 3, ['ID'] = 14, ['Level'] = 2, ['Mark'] = 2, }, {['Condition'] = 3, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351424'),['FightProp'] = {['MaxHp_P'] = 0.025, }, ['Group'] = 3, ['ID'] = 15, ['Level'] = 3, ['Mark'] = 3, }, {['Condition'] = 4, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351680'),['FightProp'] = {['MaxHp_P'] = 0.03, }, ['Group'] = 3, ['ID'] = 16, ['Level'] = 4, ['Mark'] = 4, }, {['Condition'] = 5, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351936'),['FightProp'] = {['MaxHp_P'] = 0.035, }, ['Group'] = 3, ['ID'] = 17, ['Level'] = 5, ['Mark'] = 5, }, {['Condition'] = 6, ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477352192'),['FightProp'] = {['MaxHp_P'] = 0.04, }, ['Group'] = 3, ['ID'] = 18, ['Level'] = 6, ['Mark'] = 6, }, }, 
    },
    data = {
        [1] = {
            ['Condition'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477347840'),
            ['FightProp'] = {['MaxHp_N'] = 336, }, 
            ['Group'] = 1, 
            ['ID'] = 1, 
            ['Level'] = 1, 
            ['Mark'] = 1, 
        },
        [2] = {
            ['Condition'] = 2, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348096'),
            ['FightProp'] = {['MaxHp_N'] = 672, }, 
            ['Group'] = 1, 
            ['ID'] = 2, 
            ['Level'] = 2, 
            ['Mark'] = 2, 
        },
        [3] = {
            ['Condition'] = 3, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348352'),
            ['FightProp'] = {['MaxHp_N'] = 840, }, 
            ['Group'] = 1, 
            ['ID'] = 3, 
            ['Level'] = 3, 
            ['Mark'] = 3, 
        },
        [4] = {
            ['Condition'] = 4, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348608'),
            ['FightProp'] = {['MaxHp_N'] = 1008, }, 
            ['Group'] = 1, 
            ['ID'] = 4, 
            ['Level'] = 4, 
            ['Mark'] = 4, 
        },
        [5] = {
            ['Condition'] = 5, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477348864'),
            ['FightProp'] = {['MaxHp_N'] = 1176, }, 
            ['Group'] = 1, 
            ['ID'] = 5, 
            ['Level'] = 5, 
            ['Mark'] = 5, 
        },
        [6] = {
            ['Condition'] = 6, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349120'),
            ['FightProp'] = {['MaxHp_N'] = 1345, }, 
            ['Group'] = 1, 
            ['ID'] = 6, 
            ['Level'] = 6, 
            ['Mark'] = 6, 
        },
        [7] = {
            ['Condition'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349376'),
            ['FightProp'] = {['MaxHp_N'] = 1261, }, 
            ['Group'] = 2, 
            ['ID'] = 7, 
            ['Level'] = 1, 
            ['Mark'] = 1, 
        },
        [8] = {
            ['Condition'] = 2, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349632'),
            ['FightProp'] = {['MaxHp_N'] = 1345, }, 
            ['Group'] = 2, 
            ['ID'] = 8, 
            ['Level'] = 2, 
            ['Mark'] = 2, 
        },
        [9] = {
            ['Condition'] = 3, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477349888'),
            ['FightProp'] = {['MaxHp_N'] = 1429, }, 
            ['Group'] = 2, 
            ['ID'] = 9, 
            ['Level'] = 3, 
            ['Mark'] = 3, 
        },
        [10] = {
            ['Condition'] = 4, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350144'),
            ['FightProp'] = {['MaxHp_N'] = 1513, }, 
            ['Group'] = 2, 
            ['ID'] = 10, 
            ['Level'] = 4, 
            ['Mark'] = 4, 
        },
        [11] = {
            ['Condition'] = 5, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350400'),
            ['FightProp'] = {['MaxHp_N'] = 1597, }, 
            ['Group'] = 2, 
            ['ID'] = 11, 
            ['Level'] = 5, 
            ['Mark'] = 5, 
        },
        [12] = {
            ['Condition'] = 6, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350656'),
            ['FightProp'] = {['MaxHp_N'] = 1681, }, 
            ['Group'] = 2, 
            ['ID'] = 12, 
            ['Level'] = 6, 
            ['Mark'] = 6, 
        },
        [13] = {
            ['Condition'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477350912'),
            ['FightProp'] = {['MaxHp_P'] = 0.01, }, 
            ['Group'] = 3, 
            ['ID'] = 13, 
            ['Level'] = 1, 
            ['Mark'] = 1, 
        },
        [14] = {
            ['Condition'] = 2, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351168'),
            ['FightProp'] = {['MaxHp_P'] = 0.02, }, 
            ['Group'] = 3, 
            ['ID'] = 14, 
            ['Level'] = 2, 
            ['Mark'] = 2, 
        },
        [15] = {
            ['Condition'] = 3, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351424'),
            ['FightProp'] = {['MaxHp_P'] = 0.025, }, 
            ['Group'] = 3, 
            ['ID'] = 15, 
            ['Level'] = 3, 
            ['Mark'] = 3, 
        },
        [16] = {
            ['Condition'] = 4, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351680'),
            ['FightProp'] = {['MaxHp_P'] = 0.03, }, 
            ['Group'] = 3, 
            ['ID'] = 16, 
            ['Level'] = 4, 
            ['Mark'] = 4, 
        },
        [17] = {
            ['Condition'] = 5, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477351936'),
            ['FightProp'] = {['MaxHp_P'] = 0.035, }, 
            ['Group'] = 3, 
            ['ID'] = 17, 
            ['Level'] = 5, 
            ['Mark'] = 5, 
        },
        [18] = {
            ['Condition'] = 6, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_45287477352192'),
            ['FightProp'] = {['MaxHp_P'] = 0.04, }, 
            ['Group'] = 3, 
            ['ID'] = 18, 
            ['Level'] = 6, 
            ['Mark'] = 6, 
        },
    }
}
return TopData