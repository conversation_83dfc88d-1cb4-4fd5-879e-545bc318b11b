--
-- 表名: $SocialAction_社交动作.xlsx  页名：$SocialAction_社交动作
--

local TopData = {
	data = {
		[1001] = {
			["ID"] = 1001,
			["sortOder"] = 1001,
			["name"] = Game.TableDataManager:GetLangStr('str_38762079952128'),
			["icon"] = "UI_SocialAction_Action_Hello_a",
			["actionTag"] = 0,
			["ActionBaseID"] = {1001},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = true,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 1,
			["InheritHipRotation"] = true,
		},
		[1002] = {
			["ID"] = 1002,
			["sortOder"] = 1002,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406587648'),
			["icon"] = "UI_SocialAction_Action_Angry",
			["actionTag"] = 0,
			["ActionBaseID"] = {1002},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 1,
			["InheritHipRotation"] = true,
		},
		[1003] = {
			["ID"] = 1003,
			["sortOder"] = 1003,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406587904'),
			["icon"] = "UI_SocialAction_Action_Sad",
			["actionTag"] = 0,
			["ActionBaseID"] = {1003},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 1,
			["InheritHipRotation"] = true,
		},
		[1004] = {
			["ID"] = 1004,
			["sortOder"] = 1004,
			["name"] = Game.TableDataManager:GetLangStr('str_29756607268352'),
			["icon"] = "UI_SocialAction_Action_Salute",
			["actionTag"] = 0,
			["ActionBaseID"] = {1004},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 0,
			["InheritHipRotation"] = true,
		},
		[1005] = {
			["ID"] = 1005,
			["sortOder"] = 1005,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406588416'),
			["icon"] = "UI_SocialAction_Action_Happy",
			["actionTag"] = 0,
			["ActionBaseID"] = {1005},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = true,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 1,
			["InheritHipRotation"] = true,
		},
		[1006] = {
			["ID"] = 1006,
			["sortOder"] = 1006,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406588672'),
			["icon"] = "UI_SocialAction_Action_Holdumbrealla",
			["actionTag"] = 0,
			["ActionBaseID"] = {1006},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 1,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 1,
			["InheritHipRotation"] = true,
		},
		[2001] = {
			["ID"] = 2001,
			["sortOder"] = 2001,
			["name"] = Game.TableDataManager:GetLangStr('str_53533546122240'),
			["icon"] = "UI_SocialAction_Action_Hug",
			["actionTag"] = 0,
			["ActionBaseID"] = {2001, 2002, 2003},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 2,
			["class"] = 2,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = false,
			["UpperBlendType"] = 0,
			["InheritHipRotation"] = true,
		},
		[2002] = {
			["ID"] = 2002,
			["sortOder"] = 2002,
			["name"] = Game.TableDataManager:GetLangStr('str_53533546122496'),
			["icon"] = "UI_SocialAction_Action_Princess",
			["actionTag"] = 0,
			["ActionBaseID"] = {2004, 2005, 2006, 2007},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 2,
			["class"] = 2,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = false,
			["UpperBlendType"] = 0,
			["InheritHipRotation"] = true,
		},
		[2003] = {
			["ID"] = 2003,
			["sortOder"] = 2009,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406599424'),
			["icon"] = "UI_SocialAction_Action_Hug",
			["actionTag"] = 0,
			["ActionBaseID"] = {5001},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 2,
			["class"] = 2,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = false,
			["UpperBlendType"] = 0,
			["InheritHipRotation"] = true,
		},
		[3001] = {
			["ID"] = 3001,
			["sortOder"] = 3001,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406593024'),
			["icon"] = "UI_SocialAction_Emoji_Smile",
			["actionTag"] = 0,
			["ActionBaseID"] = {3001},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 3,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 3,
			["InheritHipRotation"] = true,
		},
		[3002] = {
			["ID"] = 3002,
			["sortOder"] = 3002,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406593280'),
			["icon"] = "UI_SocialAction_Emoji_Sad",
			["actionTag"] = 0,
			["ActionBaseID"] = {3002},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 3,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 3,
			["InheritHipRotation"] = true,
		},
		[3003] = {
			["ID"] = 3003,
			["sortOder"] = 3003,
			["name"] = Game.TableDataManager:GetLangStr('str_40339406593536'),
			["icon"] = "UI_SocialAction_Emoji_Angry",
			["actionTag"] = 0,
			["ActionBaseID"] = {3003},
			["defaultUnlock"] = true,
			["condition"] = 0,
			["unlockTips"] = 0,
			["useCondition"] = 0,
			["useConditionReminder"] = 0,
			["interactTargetCheckCondition"] = 0,
			["targetConditionReminder"] = 0,
			["type"] = 1,
			["class"] = 3,
			["autoAccept"] = false,
			["autoCancel"] = false,
			["interruptLoopPlayNew"] = true,
			["UpperBlendType"] = 3,
			["InheritHipRotation"] = true,
		},
	},
}

return TopData
