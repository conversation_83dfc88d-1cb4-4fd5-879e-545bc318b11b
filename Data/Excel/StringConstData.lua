--
-- 表名: $StringConst.xlsx  页名：$LangString_语言文本
--

local TopData = {
	data = {
		["CONFIRM"] = {
			["Key"] = "CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["StringValueList"] = {},
		},
		["CANCLE"] = {
			["Key"] = "CANCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["START"] = {
			["Key"] = "START",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947743744'),
			["StringValueList"] = {},
		},
		["RETRACT"] = {
			["Key"] = "RETRACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947744000'),
			["StringValueList"] = {},
		},
		["EXIT"] = {
			["Key"] = "EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["StringValueList"] = {},
		},
		["PATK"] = {
			["Key"] = "PATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18625662551553'),
			["StringValueList"] = {},
		},
		["MATK"] = {
			["Key"] = "MATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18625662552065'),
			["StringValueList"] = {},
		},
		["PDEF"] = {
			["Key"] = "PDEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27832461821952'),
			["StringValueList"] = {},
		},
		["MDEF"] = {
			["Key"] = "MDEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27832461822208'),
			["StringValueList"] = {},
		},
		["HPMAX"] = {
			["Key"] = "HPMAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947745536'),
			["StringValueList"] = {},
		},
		["MINATK"] = {
			["Key"] = "MINATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947745792'),
			["StringValueList"] = {},
		},
		["MAXATK"] = {
			["Key"] = "MAXATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746048'),
			["StringValueList"] = {},
		},
		["DAY"] = {
			["Key"] = "DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746304'),
			["StringValueList"] = {},
		},
		["HOUR"] = {
			["Key"] = "HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746560'),
			["StringValueList"] = {},
		},
		["MINUTE"] = {
			["Key"] = "MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746816'),
			["StringValueList"] = {},
		},
		["SECOND"] = {
			["Key"] = "SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947747072'),
			["StringValueList"] = {},
		},
		["COMMON"] = {
			["Key"] = "COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47829561116160'),
			["StringValueList"] = {},
		},
		["HP"] = {
			["Key"] = "HP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27832461821696'),
			["StringValueList"] = {},
		},
		["BASE"] = {
			["Key"] = "BASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53464558208512'),
			["StringValueList"] = {},
		},
		["ATK"] = {
			["Key"] = "ATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27832461821440'),
			["StringValueList"] = {},
		},
		["DEF"] = {
			["Key"] = "DEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402759680'),
			["StringValueList"] = {},
		},
		["COLON"] = {
			["Key"] = "COLON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947748608'),
			["StringValueList"] = {},
		},
		["DAMAGE"] = {
			["Key"] = "DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947748864'),
			["StringValueList"] = {},
		},
		["HEAL"] = {
			["Key"] = "HEAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402760192'),
			["StringValueList"] = {},
		},
		["WEAPON"] = {
			["Key"] = "WEAPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18692502980096'),
			["StringValueList"] = {},
		},
		["ENTER_GAME"] = {
			["Key"] = "ENTER_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947749632'),
			["StringValueList"] = {},
		},
		["SEQUENCE"] = {
			["Key"] = "SEQUENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947749888'),
			["StringValueList"] = {},
		},
		["SEQUENCE2"] = {
			["Key"] = "SEQUENCE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947750144'),
			["StringValueList"] = {},
		},
		["COUNT"] = {
			["Key"] = "COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947750400'),
			["StringValueList"] = {},
		},
		["FreeOB"] = {
			["Key"] = "FreeOB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947750656'),
			["StringValueList"] = {},
		},
		["PURPLE"] = {
			["Key"] = "PURPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947750912'),
			["StringValueList"] = {},
		},
		["BLUE"] = {
			["Key"] = "BLUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947751168'),
			["StringValueList"] = {},
		},
		["ORANGE"] = {
			["Key"] = "ORANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947751424'),
			["StringValueList"] = {},
		},
		["GREEN"] = {
			["Key"] = "GREEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947751680'),
			["StringValueList"] = {},
		},
		["YELLOW"] = {
			["Key"] = "YELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947751936'),
			["StringValueList"] = {},
		},
		["WHITE"] = {
			["Key"] = "WHITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752192'),
			["StringValueList"] = {},
		},
		["GREY"] = {
			["Key"] = "GREY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752448'),
			["StringValueList"] = {},
		},
		["NONE"] = {
			["Key"] = "NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752704'),
			["StringValueList"] = {},
		},
		["RECONNECTING"] = {
			["Key"] = "RECONNECTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752960'),
			["StringValueList"] = {},
		},
		["SERVER_CONNECTING"] = {
			["Key"] = "SERVER_CONNECTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947753216'),
			["StringValueList"] = {},
		},
		["FOLLOWING"] = {
			["Key"] = "FOLLOWING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947753472'),
			["StringValueList"] = {},
		},
		["HINT"] = {
			["Key"] = "HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["StringValueList"] = {},
		},
		["SUNDAY"] = {
			["Key"] = "SUNDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947753984'),
			["StringValueList"] = {},
		},
		["MONDAY"] = {
			["Key"] = "MONDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947754240'),
			["StringValueList"] = {},
		},
		["TUESDAY"] = {
			["Key"] = "TUESDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947754496'),
			["StringValueList"] = {},
		},
		["WEDNESDAY"] = {
			["Key"] = "WEDNESDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947754752'),
			["StringValueList"] = {},
		},
		["THURSDAY"] = {
			["Key"] = "THURSDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947755008'),
			["StringValueList"] = {},
		},
		["FRIDAY"] = {
			["Key"] = "FRIDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947755264'),
			["StringValueList"] = {},
		},
		["SATURDAY"] = {
			["Key"] = "SATURDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947755520'),
			["StringValueList"] = {},
		},
		["YESTERDAY"] = {
			["Key"] = "YESTERDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947755776'),
			["StringValueList"] = {},
		},
		["SEND"] = {
			["Key"] = "SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756032'),
			["StringValueList"] = {},
		},
		["YOU"] = {
			["Key"] = "YOU",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756288'),
			["StringValueList"] = {},
		},
		["LOADING"] = {
			["Key"] = "LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756544'),
			["StringValueList"] = {},
		},
		["OBDungeon"] = {
			["Key"] = "OBDungeon",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756800'),
			["StringValueList"] = {},
		},
		["EXP"] = {
			["Key"] = "EXP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53395838732032'),
			["StringValueList"] = {},
		},
		["REJECT"] = {
			["Key"] = "REJECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947757312'),
			["StringValueList"] = {},
		},
		["NEWBIEPAGE"] = {
			["Key"] = "NEWBIEPAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987987712'),
			["StringValueList"] = {},
		},
		["NEWBIETITLE"] = {
			["Key"] = "NEWBIETITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947757824'),
			["StringValueList"] = {},
		},
		["NEWBIETARGETLOCK"] = {
			["Key"] = "NEWBIETARGETLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947758080'),
			["StringValueList"] = {},
		},
		["TEN_THOUSAND"] = {
			["Key"] = "TEN_THOUSAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079335900936'),
			["StringValueList"] = {},
		},
		["PLEASE_WAIT"] = {
			["Key"] = "PLEASE_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947758592'),
			["StringValueList"] = {},
		},
		["TOPUP"] = {
			["Key"] = "TOPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947758848'),
			["StringValueList"] = {},
		},
		["CONTINUE"] = {
			["Key"] = "CONTINUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947759104'),
			["StringValueList"] = {},
		},
		["CLOSE"] = {
			["Key"] = "CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947759360'),
			["StringValueList"] = {},
		},
		["YEARPAST"] = {
			["Key"] = "YEARPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947759616'),
			["StringValueList"] = {},
		},
		["MONTHPAST"] = {
			["Key"] = "MONTHPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947759872'),
			["StringValueList"] = {},
		},
		["WEEKPAST"] = {
			["Key"] = "WEEKPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947760128'),
			["StringValueList"] = {},
		},
		["DAYPAST"] = {
			["Key"] = "DAYPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947760384'),
			["StringValueList"] = {},
		},
		["HOURPAST"] = {
			["Key"] = "HOURPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947760640'),
			["StringValueList"] = {},
		},
		["MINUTEPAST"] = {
			["Key"] = "MINUTEPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947760896'),
			["StringValueList"] = {},
		},
		["SECONDPAST"] = {
			["Key"] = "SECONDPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947761152'),
			["StringValueList"] = {},
		},
		["ONEMINUTE"] = {
			["Key"] = "ONEMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947761408'),
			["StringValueList"] = {},
		},
		["JUST"] = {
			["Key"] = "JUST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947761664'),
			["StringValueList"] = {},
		},
		["OPEN"] = {
			["Key"] = "OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485077760'),
			["StringValueList"] = {},
		},
		["WEEKNESS"] = {
			["Key"] = "WEEKNESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947762176'),
			["StringValueList"] = {},
		},
		["REVERT"] = {
			["Key"] = "REVERT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18212003515392'),
			["StringValueList"] = {},
		},
		["IMMUNE"] = {
			["Key"] = "IMMUNE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947762688'),
			["StringValueList"] = {},
		},
		["SKIPDIALOGUE"] = {
			["Key"] = "SKIPDIALOGUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947762944'),
			["StringValueList"] = {},
		},
		["DIALOGUEAUTOPLAY"] = {
			["Key"] = "DIALOGUEAUTOPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947763200'),
			["StringValueList"] = {},
		},
		["HINT_CURRENT_VERSION"] = {
			["Key"] = "HINT_CURRENT_VERSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947763456'),
			["StringValueList"] = {},
		},
		["UNCONTROLLED_STATE"] = {
			["Key"] = "UNCONTROLLED_STATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947763712'),
			["StringValueList"] = {},
		},
		["MEDICINE_SETTING"] = {
			["Key"] = "MEDICINE_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947763968'),
			["StringValueList"] = {},
		},
		["FORTUNE_POPUP"] = {
			["Key"] = "FORTUNE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947764224'),
			["StringValueList"] = {},
		},
		["PVP_3V3_NAME"] = {
			["Key"] = "PVP_3V3_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497152'),
			["StringValueList"] = {},
		},
		["AHUNDREDMILLION"] = {
			["Key"] = "AHUNDREDMILLION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947764736'),
			["StringValueList"] = {},
		},
		["NEWSTICKERTAG"] = {
			["Key"] = "NEWSTICKERTAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947764992'),
			["StringValueList"] = {},
		},
		["DUNGEON_TREASURY"] = {
			["Key"] = "DUNGEON_TREASURY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947765248'),
			["StringValueList"] = {},
		},
		["DUNGEON_CHALLENGE"] = {
			["Key"] = "DUNGEON_CHALLENGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947765504'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD"] = {
			["Key"] = "DUNGEON_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947765760'),
			["StringValueList"] = {},
		},
		["DUNGEON_RANK"] = {
			["Key"] = "DUNGEON_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568832'),
			["StringValueList"] = {},
		},
		["DUNGEON_OTHER"] = {
			["Key"] = "DUNGEON_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206468864'),
			["StringValueList"] = {},
		},
		["FATE_CONTRACT_TITLE"] = {
			["Key"] = "FATE_CONTRACT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947766528'),
			["StringValueList"] = {},
		},
		["FATE_CONTRACT_DESC"] = {
			["Key"] = "FATE_CONTRACT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947766784'),
			["StringValueList"] = {},
		},
		["ME"] = {
			["Key"] = "ME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947767040'),
			["StringValueList"] = {},
		},
		["DIALOGUE_DEFAULT_SKIP_TIPS"] = {
			["Key"] = "DIALOGUE_DEFAULT_SKIP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947767296'),
			["StringValueList"] = {},
		},
		["DIALOGUE_REVIEW_UI_TITLE"] = {
			["Key"] = "DIALOGUE_REVIEW_UI_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206478336'),
			["StringValueList"] = {},
		},
		["SKIP_NEWBIE_GUIDE"] = {
			["Key"] = "SKIP_NEWBIE_GUIDE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947767808'),
			["StringValueList"] = {},
		},
		["DIALOGUE_UNKNOWN_NAME"] = {
			["Key"] = "DIALOGUE_UNKNOWN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947768064'),
			["StringValueList"] = {},
		},
		["DIALOGUE_UNKNOWN_CONTENT"] = {
			["Key"] = "DIALOGUE_UNKNOWN_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947768064'),
			["StringValueList"] = {},
		},
		["ZERO_DESC"] = {
			["Key"] = "ZERO_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947768576'),
			["StringValueList"] = {},
		},
		["ONE_DESC"] = {
			["Key"] = "ONE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067466243'),
			["StringValueList"] = {},
		},
		["TWO_DESC"] = {
			["Key"] = "TWO_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769088'),
			["StringValueList"] = {},
		},
		["THREE_DESC"] = {
			["Key"] = "THREE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769344'),
			["StringValueList"] = {},
		},
		["FOUR_DESC"] = {
			["Key"] = "FOUR_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769600'),
			["StringValueList"] = {},
		},
		["FIVE_DESC"] = {
			["Key"] = "FIVE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769856'),
			["StringValueList"] = {},
		},
		["SIX_DESC"] = {
			["Key"] = "SIX_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947770112'),
			["StringValueList"] = {},
		},
		["SEVEN_DESC"] = {
			["Key"] = "SEVEN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947770368'),
			["StringValueList"] = {},
		},
		["EIGHT_DESC"] = {
			["Key"] = "EIGHT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947770624'),
			["StringValueList"] = {},
		},
		["NIN_DESC"] = {
			["Key"] = "NIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947770880'),
			["StringValueList"] = {},
		},
		["RANKING_SPECIFIC_TIME"] = {
			["Key"] = "RANKING_SPECIFIC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947771136'),
			["StringValueList"] = {},
		},
		["LEFTDAY"] = {
			["Key"] = "LEFTDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947771392'),
			["StringValueList"] = {},
		},
		["LEFTHOUR"] = {
			["Key"] = "LEFTHOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947771648'),
			["StringValueList"] = {},
		},
		["LEFTMINUTE"] = {
			["Key"] = "LEFTMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947771904'),
			["StringValueList"] = {},
		},
		["LEFTLASTMINUTE"] = {
			["Key"] = "LEFTLASTMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947772160'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_TITLE"] = {
			["Key"] = "TITLESYSTEM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947772416'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_HONORIFIC"] = {
			["Key"] = "TITLESYSTEM_HONORIFIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947772672'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_WEAR"] = {
			["Key"] = "TITLESYSTEM_WEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947772928'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_LOCK"] = {
			["Key"] = "TITLESYSTEM_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947773184'),
			["StringValueList"] = {},
		},
		["HUD_DISPLAY_FUNCTION_BUTTON"] = {
			["Key"] = "HUD_DISPLAY_FUNCTION_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947773440'),
			["StringValueList"] = {},
		},
		["LOSTSIGNALSTRING"] = {
			["Key"] = "LOSTSIGNALSTRING",
			["StringValue"] = "999ms",
			["StringValueList"] = {},
		},
		["ROLESELECT_SELECT"] = {
			["Key"] = "ROLESELECT_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947773952'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_ROLE"] = {
			["Key"] = "ROLESELECT_CREATE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947774208'),
			["StringValueList"] = {},
		},
		["ROLESELECT_ENTER_GAME"] = {
			["Key"] = "ROLESELECT_ENTER_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947749632'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE"] = {
			["Key"] = "ROLESELECT_CREATE_FACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947774720'),
			["StringValueList"] = {},
		},
		["ROLECREATE_DELETE_ROLE"] = {
			["Key"] = "ROLECREATE_DELETE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26183462831360'),
			["StringValueList"] = {},
		},
		["ROLESELECT_DELETE_ROLE_CONFIRM"] = {
			["Key"] = "ROLESELECT_DELETE_ROLE_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947775232'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_DELETE_ROLE"] = {
			["Key"] = "ROLESELECT_INPUT_DELETE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947775488'),
			["StringValueList"] = {},
		},
		["ROLESELECT_DELETE_ROLE_WARNING"] = {
			["Key"] = "ROLESELECT_DELETE_ROLE_WARNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947775744'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_1"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947776000'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_2"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947776256'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_3"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947776512'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_4"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947776768'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_5"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947777024'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_6"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947777280'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_7"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947777536'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_8"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_8",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947777792'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_9"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_9",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947778048'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_10"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_10",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947778304'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_11"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_11",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947778560'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_12"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_12",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947778816'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_13"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_13",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947779072'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_14"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_14",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947779328'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_NAME_TITLE"] = {
			["Key"] = "ROLESELECT_INPUT_NAME_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947779584'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_NAME"] = {
			["Key"] = "ROLESELECT_INPUT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947779840'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CONFIRM_NAME"] = {
			["Key"] = "ROLESELECT_CONFIRM_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947780096'),
			["StringValueList"] = {},
		},
		["ROLESELECT_LEVEL"] = {
			["Key"] = "ROLESELECT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53395838731776'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE"] = {
			["Key"] = "ROLESELECT_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947780608'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CHOOSE"] = {
			["Key"] = "ROLESELECT_CHOOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947780864'),
			["StringValueList"] = {},
		},
		["ROLESELECT_MALE"] = {
			["Key"] = "ROLESELECT_MALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781120'),
			["StringValueList"] = {},
		},
		["ROLESELECT_FEMALE"] = {
			["Key"] = "ROLESELECT_FEMALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781376'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_SELF"] = {
			["Key"] = "ANNOUNCEMENT_SELF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781632'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_TITLE_LOADING"] = {
			["Key"] = "ANNOUNCEMENT_TITLE_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781888'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_CONTENT_LOADING"] = {
			["Key"] = "ANNOUNCEMENT_CONTENT_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947782144'),
			["StringValueList"] = {},
		},
		["HUD_IMMUNITY"] = {
			["Key"] = "HUD_IMMUNITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947762688'),
			["StringValueList"] = {},
		},
		["HUD_FOLLOW"] = {
			["Key"] = "HUD_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947782656'),
			["StringValueList"] = {},
		},
		["HUD_CANCLE_FOLLOW"] = {
			["Key"] = "HUD_CANCLE_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947782912'),
			["StringValueList"] = {},
		},
		["HUD_TASK_COMPLETE"] = {
			["Key"] = "HUD_TASK_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56352118448896'),
			["StringValueList"] = {},
		},
		["HUD_REVIVE"] = {
			["Key"] = "HUD_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493868544'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_DUNGEON"] = {
			["Key"] = "HUD_EXIT_DUNGEON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493868288'),
			["StringValueList"] = {},
		},
		["HUD_RETURN_DUNGEON"] = {
			["Key"] = "HUD_RETURN_DUNGEON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31543045129728'),
			["StringValueList"] = {},
		},
		["HUD_TEAMER"] = {
			["Key"] = "HUD_TEAMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947784192'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_GUILD"] = {
			["Key"] = "HUD_EXIT_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947784448'),
			["StringValueList"] = {},
		},
		["HUD_WORLD_BOSS_TITLE"] = {
			["Key"] = "HUD_WORLD_BOSS_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947784704'),
			["StringValueList"] = {},
		},
		["WORLD_BOSS_EXPLAIN"] = {
			["Key"] = "WORLD_BOSS_EXPLAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947784960'),
			["StringValueList"] = {},
		},
		["ACTIVITY_PRE_PUSH"] = {
			["Key"] = "ACTIVITY_PRE_PUSH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785216'),
			["StringValueList"] = {},
		},
		["ACTIVITY_OPEN_PUSH"] = {
			["Key"] = "ACTIVITY_OPEN_PUSH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785472'),
			["StringValueList"] = {},
		},
		["TRANSFERING"] = {
			["Key"] = "TRANSFERING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785728'),
			["StringValueList"] = {},
		},
		["HOTPATCH_DOWNLOAD_MAP"] = {
			["Key"] = "HOTPATCH_DOWNLOAD_MAP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785984'),
			["StringValueList"] = {},
		},
		["HOTPATCH_DOWNLOAD_RES"] = {
			["Key"] = "HOTPATCH_DOWNLOAD_RES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947786240'),
			["StringValueList"] = {},
		},
		["HOTPATCH_SPEED_MB"] = {
			["Key"] = "HOTPATCH_SPEED_MB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947786496'),
			["StringValueList"] = {},
		},
		["HOTPATCH_REMAIN_TIME"] = {
			["Key"] = "HOTPATCH_REMAIN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947786752'),
			["StringValueList"] = {},
		},
		["HOTPATCH_REMAIN_MIN"] = {
			["Key"] = "HOTPATCH_REMAIN_MIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947787008'),
			["StringValueList"] = {},
		},
		["HOTPATCH_SPEED_KB"] = {
			["Key"] = "HOTPATCH_SPEED_KB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947787264'),
			["StringValueList"] = {},
		},
		["DLC_UPDATE_TITLE"] = {
			["Key"] = "DLC_UPDATE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493873152'),
			["StringValueList"] = {},
		},
		["DLC_EXTERN_GROUP"] = {
			["Key"] = "DLC_EXTERN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947787776'),
			["StringValueList"] = {},
		},
		["DLC_CUSTOM_GROUP"] = {
			["Key"] = "DLC_CUSTOM_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947788032'),
			["StringValueList"] = {},
		},
		["DLC_DELETABLE_CHUNKS"] = {
			["Key"] = "DLC_DELETABLE_CHUNKS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947788288'),
			["StringValueList"] = {},
		},
		["DLC_DOWNLOAD"] = {
			["Key"] = "DLC_DOWNLOAD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947788544'),
			["StringValueList"] = {},
		},
		["DLC_DELETE"] = {
			["Key"] = "DLC_DELETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947788800'),
			["StringValueList"] = {},
		},
		["NEW_SERVER"] = {
			["Key"] = "NEW_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789056'),
			["StringValueList"] = {},
		},
		["RECOMMEND_SERVER"] = {
			["Key"] = "RECOMMEND_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789312'),
			["StringValueList"] = {},
		},
		["SELECT_SERVER_TITLE"] = {
			["Key"] = "SELECT_SERVER_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789568'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_UNOBTRUCTED"] = {
			["Key"] = "SERVER_STATUS_UNOBTRUCTED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789824'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_CROWDED"] = {
			["Key"] = "SERVER_STATUS_CROWDED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947790080'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_OVERCROWDED"] = {
			["Key"] = "SERVER_STATUS_OVERCROWDED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947790336'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_MAINTAINING"] = {
			["Key"] = "SERVER_STATUS_MAINTAINING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947790592'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_NEW"] = {
			["Key"] = "SERVER_TAG_NEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947790848'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_RECOMMEND"] = {
			["Key"] = "SERVER_TAG_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789312'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_HOT"] = {
			["Key"] = "SERVER_TAG_HOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947791360'),
			["StringValueList"] = {},
		},
		["AGE_APPROPRIATE_INDICATION"] = {
			["Key"] = "AGE_APPROPRIATE_INDICATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947791616'),
			["StringValueList"] = {},
		},
		["AGE_APPROPRIATE_TITLE"] = {
			["Key"] = "AGE_APPROPRIATE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947791872'),
			["StringValueList"] = {},
		},
		["ONE_MINUTE_AGO"] = {
			["Key"] = "ONE_MINUTE_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792128'),
			["StringValueList"] = {},
		},
		["SEVERAL_MINUTES_AGO"] = {
			["Key"] = "SEVERAL_MINUTES_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792384'),
			["StringValueList"] = {},
		},
		["SEVERAL_HOURS_AGO"] = {
			["Key"] = "SEVERAL_HOURS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792640'),
			["StringValueList"] = {},
		},
		["SEVERAL_DAYS_AGO"] = {
			["Key"] = "SEVERAL_DAYS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792896'),
			["StringValueList"] = {},
		},
		["SEVERAL_WEEKS_AGO"] = {
			["Key"] = "SEVERAL_WEEKS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947793152'),
			["StringValueList"] = {},
		},
		["SEVERAL_MONTHS_AGO"] = {
			["Key"] = "SEVERAL_MONTHS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947793408'),
			["StringValueList"] = {},
		},
		["ONE_YEAR_AGO"] = {
			["Key"] = "ONE_YEAR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947793664'),
			["StringValueList"] = {},
		},
		["LOG_IN"] = {
			["Key"] = "LOG_IN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493898752'),
			["StringValueList"] = {},
		},
		["LOG_OUT"] = {
			["Key"] = "LOG_OUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947794176'),
			["StringValueList"] = {},
		},
		["EXIST_ROLE"] = {
			["Key"] = "EXIST_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947794432'),
			["StringValueList"] = {},
		},
		["HEALTH_GAME_HINT"] = {
			["Key"] = "HEALTH_GAME_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947794688'),
			["StringValueList"] = {},
		},
		["ENTER_SERVER_PROGRESS_HINT"] = {
			["Key"] = "ENTER_SERVER_PROGRESS_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947794944'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_IN_QUEUE"] = {
			["Key"] = "SERVER_QUEUE_STATUS_IN_QUEUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30032290454272'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_NEED_QUEUE"] = {
			["Key"] = "SERVER_QUEUE_STATUS_NEED_QUEUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947795456'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_CIRCUIT_BREAKER"] = {
			["Key"] = "SERVER_QUEUE_STATUS_CIRCUIT_BREAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947795712'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_PROTECT_TIPS"] = {
			["Key"] = "LOGIN_QUEUE_PROTECT_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947795968'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_TIME_FORMAT"] = {
			["Key"] = "LOGIN_QUEUE_TIME_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947796224'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_RANK_FORMAT"] = {
			["Key"] = "LOGIN_QUEUE_RANK_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947796480'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_DETAIL"] = {
			["Key"] = "LOGIN_QUEUE_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947796736'),
			["StringValueList"] = {},
		},
		["LOGIN_SWITCH_SERVER_TIPS"] = {
			["Key"] = "LOGIN_SWITCH_SERVER_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947796992'),
			["StringValueList"] = {},
		},
		["LOGIN_BEFOREHAND_CREATE_ROLE"] = {
			["Key"] = "LOGIN_BEFOREHAND_CREATE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947797248'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TITLE_1"] = {
			["Key"] = "RED_PACKET_TITLE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947797504'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_1"] = {
			["Key"] = "RED_PACKET_TAB_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947797760'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_2"] = {
			["Key"] = "RED_PACKET_TAB_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947798016'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_3"] = {
			["Key"] = "RED_PACKET_TAB_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947798272'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_SNED_CHANNAL"] = {
			["Key"] = "RED_PACKET_CHOOSE_SNED_CHANNAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947798528'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_TYPE"] = {
			["Key"] = "RED_PACKET_CHOOSE_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947798784'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TYPE_1"] = {
			["Key"] = "RED_PACKET_TYPE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947799040'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TYPE_2"] = {
			["Key"] = "RED_PACKET_TYPE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947799296'),
			["StringValueList"] = {},
		},
		["RED_PACKET_MONEY_TYPE"] = {
			["Key"] = "RED_PACKET_MONEY_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947799552'),
			["StringValueList"] = {},
		},
		["RED_PACKET_LUCKY"] = {
			["Key"] = "RED_PACKET_LUCKY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947799808'),
			["StringValueList"] = {},
		},
		["RED_PACKET_VOICE"] = {
			["Key"] = "RED_PACKET_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947800064'),
			["StringValueList"] = {},
		},
		["RED_PACKET_PASSWORD"] = {
			["Key"] = "RED_PACKET_PASSWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947800320'),
			["StringValueList"] = {},
		},
		["RED_PACKET_AMOUNT"] = {
			["Key"] = "RED_PACKET_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947800576'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COST"] = {
			["Key"] = "RED_PACKET_COST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947800832'),
			["StringValueList"] = {},
		},
		["RED_PACKET_NUM"] = {
			["Key"] = "RED_PACKET_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947801088'),
			["StringValueList"] = {},
		},
		["RED_PACKET_PUT_NUM"] = {
			["Key"] = "RED_PACKET_PUT_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947801344'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947801600'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_HINT_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_HINT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947801856'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_HINT_SECRET_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_HINT_SECRET_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947802112'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COMMEMNT__SECRET_WORD"] = {
			["Key"] = "RED_PACKET_COMMEMNT__SECRET_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947802368'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COMMEMNT_PASSWORD"] = {
			["Key"] = "RED_PACKET_COMMEMNT_PASSWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947802624'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HINT_PASSWD"] = {
			["Key"] = "RED_PACKET_HINT_PASSWD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947802880'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_GIVE"] = {
			["Key"] = "RED_PACKET_BUTTON_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947803136'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TITLE_2"] = {
			["Key"] = "RED_PACKET_TITLE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947803392'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_NEXT"] = {
			["Key"] = "RED_PACKET_BUTTON_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947803648'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_BANNED"] = {
			["Key"] = "RED_PACKET_BUTTON_BANNED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947803904'),
			["StringValueList"] = {},
		},
		["RED_PACKET_AROUND"] = {
			["Key"] = "RED_PACKET_AROUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947804160'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEND_AMOUNT"] = {
			["Key"] = "RED_PACKET_SEND_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947804416'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL_SEND"] = {
			["Key"] = "RED_PACKET_TOTAL_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947804672'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_NUM"] = {
			["Key"] = "RED_PACKET_RECEIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947804928'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_AMOUNT"] = {
			["Key"] = "RED_PACKET_RECEIVE_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947805184'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL_RECEIVE"] = {
			["Key"] = "RED_PACKET_TOTAL_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947805440'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BEST_LUCK"] = {
			["Key"] = "RED_PACKET_BEST_LUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947805696'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEND_EMPTY"] = {
			["Key"] = "RED_PACKET_SEND_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947805952'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_EMPTY"] = {
			["Key"] = "RED_PACKET_RECEIVE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947806208'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_RECEIVE_CHANNAL"] = {
			["Key"] = "RED_PACKET_CHOOSE_RECEIVE_CHANNAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947806464'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL"] = {
			["Key"] = "RED_PACKET_TOTAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947806720'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE"] = {
			["Key"] = "RED_PACKET_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947806976'),
			["StringValueList"] = {},
		},
		["RED_PACKET_FOLLOW"] = {
			["Key"] = "RED_PACKET_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947807232'),
			["StringValueList"] = {},
		},
		["RED_PACKET_THANK"] = {
			["Key"] = "RED_PACKET_THANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947807488'),
			["StringValueList"] = {},
		},
		["RED_PACKET_OPEN"] = {
			["Key"] = "RED_PACKET_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947807744'),
			["StringValueList"] = {},
		},
		["RED_PACKET_REPORT"] = {
			["Key"] = "RED_PACKET_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947808000'),
			["StringValueList"] = {},
		},
		["RED_PACKET_MONEY"] = {
			["Key"] = "RED_PACKET_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947799040'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECORD_SEND"] = {
			["Key"] = "RED_PACKET_RECORD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756032'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECORD_RECEIVE"] = {
			["Key"] = "RED_PACKET_RECORD_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947808768'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEASON"] = {
			["Key"] = "RED_PACKET_SEASON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_28451205547520'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HISTORY_MONEY"] = {
			["Key"] = "RED_PACKET_HISTORY_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947809280'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HISTORY_SKIN"] = {
			["Key"] = "RED_PACKET_HISTORY_SKIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328566784'),
			["StringValueList"] = {},
		},
		["RED_DEFAULT_MESSAGE"] = {
			["Key"] = "RED_DEFAULT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58345251669504'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_TEAM"] = {
			["Key"] = "RED_CHANNEL_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328576512'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_GUILD"] = {
			["Key"] = "RED_CHANNEL_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_WORLD"] = {
			["Key"] = "RED_CHANNEL_WORLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947810560'),
			["StringValueList"] = {},
		},
		["RED_SKIN_NUM"] = {
			["Key"] = "RED_SKIN_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947810816'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SOURCE"] = {
			["Key"] = "RED_PACKET_SOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947811072'),
			["StringValueList"] = {},
		},
		["STATISTICS_TITLE"] = {
			["Key"] = "STATISTICS_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947811328'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE"] = {
			["Key"] = "STATISTICS_SUB_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402759936'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_2"] = {
			["Key"] = "STATISTICS_SUB_TITLE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947811840'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_3"] = {
			["Key"] = "STATISTICS_SUB_TITLE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402760192'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_4"] = {
			["Key"] = "STATISTICS_SUB_TITLE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947812352'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_5"] = {
			["Key"] = "STATISTICS_SUB_TITLE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947812608'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_DAMAGE"] = {
			["Key"] = "STATISTICS_TOTAL_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947812864'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_HEALING"] = {
			["Key"] = "STATISTICS_TOTAL_HEALING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947813120'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEALING_SECOND"] = {
			["Key"] = "STATISTICS_HEALING_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947813376'),
			["StringValueList"] = {},
		},
		["STATISTICS_DPS"] = {
			["Key"] = "STATISTICS_DPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947813632'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_TAKEDAMAGE"] = {
			["Key"] = "STATISTICS_TOTAL_TAKEDAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947813888'),
			["StringValueList"] = {},
		},
		["STATISTICS_NOW_BOSS"] = {
			["Key"] = "STATISTICS_NOW_BOSS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814144'),
			["StringValueList"] = {},
		},
		["STATISTICS_ALL"] = {
			["Key"] = "STATISTICS_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946749440'),
			["StringValueList"] = {},
		},
		["STATISTICS_DETAIL"] = {
			["Key"] = "STATISTICS_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814656'),
			["StringValueList"] = {},
		},
		["STATISTICS_HITCOUNT"] = {
			["Key"] = "STATISTICS_HITCOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814912'),
			["StringValueList"] = {},
		},
		["STATISTICS_DAMAGE"] = {
			["Key"] = "STATISTICS_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947748864'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEALING"] = {
			["Key"] = "STATISTICS_HEALING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402760192'),
			["StringValueList"] = {},
		},
		["STATISTICS_PERCENT"] = {
			["Key"] = "STATISTICS_PERCENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947815680'),
			["StringValueList"] = {},
		},
		["STATISTICS_NEXT"] = {
			["Key"] = "STATISTICS_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947803648'),
			["StringValueList"] = {},
		},
		["STATISTICS_CLEAR"] = {
			["Key"] = "STATISTICS_CLEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947816192'),
			["StringValueList"] = {},
		},
		["STATISTICS_ELEMENT_DAMAGE"] = {
			["Key"] = "STATISTICS_ELEMENT_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947816448'),
			["StringValueList"] = {},
		},
		["STATISTICS_CRITICAL"] = {
			["Key"] = "STATISTICS_CRITICAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27832461822464'),
			["StringValueList"] = {},
		},
		["STATISTICS_DAMAGE_RESOURCE"] = {
			["Key"] = "STATISTICS_DAMAGE_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947816960'),
			["StringValueList"] = {},
		},
		["STATISTICS_COUNT"] = {
			["Key"] = "STATISTICS_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947817216'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEAL_RESOURCE"] = {
			["Key"] = "STATISTICS_HEAL_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947817472'),
			["StringValueList"] = {},
		},
		["STATISTICS_PERCENT_TITLE"] = {
			["Key"] = "STATISTICS_PERCENT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947817728'),
			["StringValueList"] = {},
		},
		["STATISTICS_TITLE_MEMBER"] = {
			["Key"] = "STATISTICS_TITLE_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574784'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_EMPTY"] = {
			["Key"] = "DUNGEON_REWARD_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947818240'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_TIME"] = {
			["Key"] = "DUNGEON_SETTLEMENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947818496'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_REWARDS"] = {
			["Key"] = "DUNGEON_SETTLEMENT_REWARDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947818752'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_TIME_DETAIL"] = {
			["Key"] = "DUNGEON_SETTLEMENT_TIME_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947819008'),
			["StringValueList"] = {},
		},
		["DUNGEON_SUCCESS"] = {
			["Key"] = "DUNGEON_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61163018652928'),
			["StringValueList"] = {},
		},
		["DUNGEON_FAIL"] = {
			["Key"] = "DUNGEON_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947819520'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_SUCCESS"] = {
			["Key"] = "DUNGEON_STAGE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61163018653184'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_FAIL"] = {
			["Key"] = "DUNGEON_STAGE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947820032'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_HIGHEST"] = {
			["Key"] = "DUNGEON_AWARD_HIGHEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947820288'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_NOPRICE"] = {
			["Key"] = "DUNGEON_AWARD_NOPRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947820544'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_PREBID"] = {
			["Key"] = "DUNGEON_AWARD_PREBID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947820800'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_WAIT_ASSIGNMENT"] = {
			["Key"] = "DUNGEON_AWARD_WAIT_ASSIGNMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947821056'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_ME"] = {
			["Key"] = "DUNGEON_AWARD_ME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947821312'),
			["StringValueList"] = {},
		},
		["DUNGEON_UNLOCK_LEVEL"] = {
			["Key"] = "DUNGEON_UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947821568'),
			["StringValueList"] = {},
		},
		["DUNGEON_SUGGEST_TEAM_NUM"] = {
			["Key"] = "DUNGEON_SUGGEST_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947821824'),
			["StringValueList"] = {},
		},
		["DUNGEON_MAX_LEVEL_TO_ATTEND"] = {
			["Key"] = "DUNGEON_MAX_LEVEL_TO_ATTEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947822080'),
			["StringValueList"] = {},
		},
		["DUNGEON_LOADING"] = {
			["Key"] = "DUNGEON_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947822336'),
			["StringValueList"] = {},
		},
		["DUNGEON_QUICK_TEAM"] = {
			["Key"] = "DUNGEON_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26183462816256'),
			["StringValueList"] = {},
		},
		["DUNGEON_START_MATCH"] = {
			["Key"] = "DUNGEON_START_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947822848'),
			["StringValueList"] = {},
		},
		["DUNGEON_MY_TEAM"] = {
			["Key"] = "DUNGEON_MY_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823104'),
			["StringValueList"] = {},
		},
		["DUNGEON_MY_GROUP"] = {
			["Key"] = "DUNGEON_MY_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823360'),
			["StringValueList"] = {},
		},
		["DUNGEON_OPEN_GATE"] = {
			["Key"] = "DUNGEON_OPEN_GATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823616'),
			["StringValueList"] = {},
		},
		["DUNGEON_IS_MATCHING"] = {
			["Key"] = "DUNGEON_IS_MATCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823872'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_CAPTAIN"] = {
			["Key"] = "DUNGEON_WAITING_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947824128'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_GROUP_CAPTAIN"] = {
			["Key"] = "DUNGEON_WAITING_GROUP_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947824384'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_CONFIRM"] = {
			["Key"] = "DUNGEON_WAITING_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947824640'),
			["StringValueList"] = {},
		},
		["DUNGEON_BACK_TO_GAME"] = {
			["Key"] = "DUNGEON_BACK_TO_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947824896'),
			["StringValueList"] = {},
		},
		["DUNGEON_PVP_WAITING_CONFIRM"] = {
			["Key"] = "DUNGEON_PVP_WAITING_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947825152'),
			["StringValueList"] = {},
		},
		["DUNGEON_MATCHING_TIME"] = {
			["Key"] = "DUNGEON_MATCHING_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947825408'),
			["StringValueList"] = {},
		},
		["DUNGEON_IS_IN_PVP_GAMING"] = {
			["Key"] = "DUNGEON_IS_IN_PVP_GAMING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947825664'),
			["StringValueList"] = {},
		},
		["DUNGEON_CARRIAGE"] = {
			["Key"] = "DUNGEON_CARRIAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36697005886208'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_UNFINISH"] = {
			["Key"] = "DUNGEON_STAGE_UNFINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947826176'),
			["StringValueList"] = {},
		},
		["DUNGEON_PRE_BID"] = {
			["Key"] = "DUNGEON_PRE_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947826432'),
			["StringValueList"] = {},
		},
		["DUNGEON_HIGHEST_BID"] = {
			["Key"] = "DUNGEON_HIGHEST_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947826688'),
			["StringValueList"] = {},
		},
		["DUNGEON_RESTART"] = {
			["Key"] = "DUNGEON_RESTART",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947826944'),
			["StringValueList"] = {},
		},
		["DUNGEON_ALL_NEED"] = {
			["Key"] = "DUNGEON_ALL_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947827200'),
			["StringValueList"] = {},
		},
		["DUNGEON_ALL_GIVE_UP"] = {
			["Key"] = "DUNGEON_ALL_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947827456'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_LIST"] = {
			["Key"] = "DUNGEON_AUCTION_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947827712'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_HISTORY"] = {
			["Key"] = "DUNGEON_AUCTION_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947827968'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_GIVE_UP"] = {
			["Key"] = "DUNGEON_AUCTION_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947828224'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_HAS_GIVE_UP"] = {
			["Key"] = "DUNGEON_AUCTION_HAS_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947828480'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58275190031360'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_NO_ONE_AUCTION"] = {
			["Key"] = "DUNGEON_AUCTION_NO_ONE_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947828992'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_FAILED"] = {
			["Key"] = "DUNGEON_AUCTION_FAILED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947829248'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE_PEOPLE"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE_PEOPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947829504'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE_MAX"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947829760'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_CANCEL_GIVE"] = {
			["Key"] = "DUNGEON_AUCTION_CANCEL_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947830016'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_TITLE"] = {
			["Key"] = "DUNGEON_AUCTION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947830272'),
			["StringValueList"] = {},
		},
		["DUNGEON_TINGENHISTORY"] = {
			["Key"] = "DUNGEON_TINGENHISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_826244336128'),
			["StringValueList"] = {},
		},
		["DUNGEON_TINGENHISTORY_CONTINUE"] = {
			["Key"] = "DUNGEON_TINGENHISTORY_CONTINUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947830784'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_GAIN"] = {
			["Key"] = "DUNGEON_REWARD_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947831040'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_REWARD"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947831552'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_GIVE_UP"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947828224'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_NEED"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947832064'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_GREEDY"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_GREEDY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947832320'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_TITLE"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947832576'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUTO_BATTLE"] = {
			["Key"] = "DUNGEON_AUTO_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947832832'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUTO_SKILL"] = {
			["Key"] = "DUNGEON_AUTO_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947833088'),
			["StringValueList"] = {},
		},
		["DUNGEON_CHANGE_CLASS"] = {
			["Key"] = "DUNGEON_CHANGE_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947833344'),
			["StringValueList"] = {},
		},
		["DUNGEON_NORMAL"] = {
			["Key"] = "DUNGEON_NORMAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56625654139136'),
			["StringValueList"] = {},
		},
		["DUNGEON_HARD"] = {
			["Key"] = "DUNGEON_HARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947833856'),
			["StringValueList"] = {},
		},
		["DUNGEON_SINGLE"] = {
			["Key"] = "DUNGEON_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947834112'),
			["StringValueList"] = {},
		},
		["DUNGEON_TEAM"] = {
			["Key"] = "DUNGEON_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206470912'),
			["StringValueList"] = {},
		},
		["DUNGEON_FIRST_PASS_REWARD"] = {
			["Key"] = "DUNGEON_FIRST_PASS_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947834624'),
			["StringValueList"] = {},
		},
		["DUNGEON_DROP_REWARD"] = {
			["Key"] = "DUNGEON_DROP_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947834880'),
			["StringValueList"] = {},
		},
		["DUNGEON_CURRENT_TEAM_NUM"] = {
			["Key"] = "DUNGEON_CURRENT_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947835136'),
			["StringValueList"] = {},
		},
		["DUNGEON_TOTAL_REWARD_LIMIT"] = {
			["Key"] = "DUNGEON_TOTAL_REWARD_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947835392'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_DAILY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947835648'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_WEEKLY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_WEEKLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947835904'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_MONTHLY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_MONTHLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947836160'),
			["StringValueList"] = {},
		},
		["DUNGEON_FIRST_PASS_TEAM"] = {
			["Key"] = "DUNGEON_FIRST_PASS_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947836416'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_DATA_RANK"] = {
			["Key"] = "DUNGEON_STATS_DATA_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947836672'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_DAMAGE_RANK"] = {
			["Key"] = "DUNGEON_STATS_DAMAGE_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947836928'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_HEAL_RANK"] = {
			["Key"] = "DUNGEON_STATS_HEAL_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947837184'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_BATTLE_TIME"] = {
			["Key"] = "DUNGEON_STATS_BATTLE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947837440'),
			["StringValueList"] = {},
		},
		["BAG_CONFIRM_RESOLVE"] = {
			["Key"] = "BAG_CONFIRM_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947837696'),
			["StringValueList"] = {},
		},
		["BAG_CANCLE_RESOLVE"] = {
			["Key"] = "BAG_CANCLE_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947837952'),
			["StringValueList"] = {},
		},
		["BAG_BATCH_RESOLVE"] = {
			["Key"] = "BAG_BATCH_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947838208'),
			["StringValueList"] = {},
		},
		["BAG_GET_SKILL"] = {
			["Key"] = "BAG_GET_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947838464'),
			["StringValueList"] = {},
		},
		["BAG_EQUIPED"] = {
			["Key"] = "BAG_EQUIPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947838720'),
			["StringValueList"] = {},
		},
		["BAG_TRADABLE"] = {
			["Key"] = "BAG_TRADABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947838976'),
			["StringValueList"] = {},
		},
		["BAG_WASH_ENTRY"] = {
			["Key"] = "BAG_WASH_ENTRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947839232'),
			["StringValueList"] = {},
		},
		["BAG_HISTORY"] = {
			["Key"] = "BAG_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947839488'),
			["StringValueList"] = {},
		},
		["BAG_UNTRADABLE"] = {
			["Key"] = "BAG_UNTRADABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947839744'),
			["StringValueList"] = {},
		},
		["BAG_DAY_COUNT"] = {
			["Key"] = "BAG_DAY_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947840000'),
			["StringValueList"] = {},
		},
		["BAG_WEEK_COUNT"] = {
			["Key"] = "BAG_WEEK_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947840256'),
			["StringValueList"] = {},
		},
		["BAG_USE_INSTRUCTION"] = {
			["Key"] = "BAG_USE_INSTRUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947840512'),
			["StringValueList"] = {},
		},
		["BAG_REWARD"] = {
			["Key"] = "BAG_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947840768'),
			["StringValueList"] = {},
		},
		["BAG_PICKPACKTITLE"] = {
			["Key"] = "BAG_PICKPACKTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947841024'),
			["StringValueList"] = {},
		},
		["BAG_PICKGET"] = {
			["Key"] = "BAG_PICKGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11202348635392'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NUM"] = {
			["Key"] = "BAG_SYNTHESIS_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947841536'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NEED_NUM"] = {
			["Key"] = "BAG_SYNTHESIS_NEED_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947841792'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_ADDITION"] = {
			["Key"] = "BAG_ONECLICK_ADDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947842048'),
			["StringValueList"] = {},
		},
		["BAG_MEDICINE"] = {
			["Key"] = "BAG_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947842304'),
			["StringValueList"] = {},
		},
		["BAG_HP_VALUE"] = {
			["Key"] = "BAG_HP_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947842560'),
			["StringValueList"] = {},
		},
		["BAG_HP_VALUE_TIME"] = {
			["Key"] = "BAG_HP_VALUE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947842816'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_BUY"] = {
			["Key"] = "BAG_ONECLICK_BUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947843072'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_USE"] = {
			["Key"] = "BAG_ONECLICK_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947843328'),
			["StringValueList"] = {},
		},
		["BAG_REMINDER"] = {
			["Key"] = "BAG_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493889536'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NEED"] = {
			["Key"] = "BAG_SYNTHESIS_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947843840'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS"] = {
			["Key"] = "BAG_SYNTHESIS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033043200'),
			["StringValueList"] = {},
		},
		["BAG_BOUND_OBTAIN"] = {
			["Key"] = "BAG_BOUND_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11202348634624'),
			["StringValueList"] = {},
		},
		["BAG_POSSIBLE_OBTAIN"] = {
			["Key"] = "BAG_POSSIBLE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11202348634880'),
			["StringValueList"] = {},
		},
		["BAG_USE"] = {
			["Key"] = "BAG_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033041920'),
			["StringValueList"] = {},
		},
		["BAG_CANCLE"] = {
			["Key"] = "BAG_CANCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["BAG_CONFIRM_OBTAIN"] = {
			["Key"] = "BAG_CONFIRM_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947845376'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_OPEN_NEED"] = {
			["Key"] = "BAG_CHEST_OPEN_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947845632'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_UNLOCK"] = {
			["Key"] = "BAG_CHEST_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947845888'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_OPEN"] = {
			["Key"] = "BAG_CHEST_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947846144'),
			["StringValueList"] = {},
		},
		["BAG_STOREHOUSE"] = {
			["Key"] = "BAG_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32918239975680'),
			["StringValueList"] = {},
		},
		["BAG_RESOLVE"] = {
			["Key"] = "BAG_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033043968'),
			["StringValueList"] = {},
		},
		["BAG_TIDY"] = {
			["Key"] = "BAG_TIDY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485164032'),
			["StringValueList"] = {},
		},
		["BAG_EXIT"] = {
			["Key"] = "BAG_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_RESOLVE"] = {
			["Key"] = "BAG_ONECLICK_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947847424'),
			["StringValueList"] = {},
		},
		["BAG_GOLD_EQUIP"] = {
			["Key"] = "BAG_GOLD_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947847680'),
			["StringValueList"] = {},
		},
		["BAG_TRADABLE_PURPLE_EQUIP"] = {
			["Key"] = "BAG_TRADABLE_PURPLE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947847936'),
			["StringValueList"] = {},
		},
		["BAG_UNDER_PURPLE_EQUIP"] = {
			["Key"] = "BAG_UNDER_PURPLE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947848192'),
			["StringValueList"] = {},
		},
		["BAG_TEMPORARY_BAG"] = {
			["Key"] = "BAG_TEMPORARY_BAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947848448'),
			["StringValueList"] = {},
		},
		["BAG_RETRIEVE_ALL"] = {
			["Key"] = "BAG_RETRIEVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947848704'),
			["StringValueList"] = {},
		},
		["BAG_TIDY_STOREHOUSE"] = {
			["Key"] = "BAG_TIDY_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947848960'),
			["StringValueList"] = {},
		},
		["BAG_CLOSE_STOREHOUSE"] = {
			["Key"] = "BAG_CLOSE_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947849216'),
			["StringValueList"] = {},
		},
		["BAG_Level"] = {
			["Key"] = "BAG_Level",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947849472'),
			["StringValueList"] = {},
		},
		["BAG_ALL_RARITY"] = {
			["Key"] = "BAG_ALL_RARITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31062545663488'),
			["StringValueList"] = {},
		},
		["BAG_ACHIEVE_PATH"] = {
			["Key"] = "BAG_ACHIEVE_PATH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337301504'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_MEDICINE"] = {
			["Key"] = "BAG_AUTO_USE_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947850240'),
			["StringValueList"] = {},
		},
		["BAG_QUICK_MEDICINE"] = {
			["Key"] = "BAG_QUICK_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947850496'),
			["StringValueList"] = {},
		},
		["BAG_QUICK_MEDICINE_DES"] = {
			["Key"] = "BAG_QUICK_MEDICINE_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947850752'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_MEDICINE_DES"] = {
			["Key"] = "BAG_AUTO_USE_MEDICINE_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851008'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_FOOD_DES"] = {
			["Key"] = "BAG_AUTO_USE_FOOD_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851008'),
			["StringValueList"] = {},
		},
		["ITEM_PICKCHEST_HOLDNUM"] = {
			["Key"] = "ITEM_PICKCHEST_HOLDNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851520'),
			["StringValueList"] = {},
		},
		["OPEN_BOX_RESULT"] = {
			["Key"] = "OPEN_BOX_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["CHEST_RESULT"] = {
			["Key"] = "CHEST_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947852032'),
			["StringValueList"] = {},
		},
		["SYNTHESIS_RESULT"] = {
			["Key"] = "SYNTHESIS_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947852288'),
			["StringValueList"] = {},
		},
		["DECOMPOSE_RESULT"] = {
			["Key"] = "DECOMPOSE_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947852544'),
			["StringValueList"] = {},
		},
		["DROP_GET_ITEMS"] = {
			["Key"] = "DROP_GET_ITEMS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["MAIL_RESULT"] = {
			["Key"] = "MAIL_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["SHOP_BUY_RESULT"] = {
			["Key"] = "SHOP_BUY_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853312'),
			["StringValueList"] = {},
		},
		["NEWBIE_TASK_RESULT"] = {
			["Key"] = "NEWBIE_TASK_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["AUCTION_RESULT"] = {
			["Key"] = "AUCTION_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ROLL_RESULT"] = {
			["Key"] = "ROLL_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["STALL_BUY_RESULT"] = {
			["Key"] = "STALL_BUY_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["GUILD_MATERIAL_TASK_RESULT"] = {
			["Key"] = "GUILD_MATERIAL_TASK_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["SCHEDULE_RESULT"] = {
			["Key"] = "SCHEDULE_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["DROP_CLICK_EMPTY_CLOSE"] = {
			["Key"] = "DROP_CLICK_EMPTY_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947855104'),
			["StringValueList"] = {},
		},
		["DROP_CURVE"] = {
			["Key"] = "DROP_CURVE",
			["StringValue"] = "/Game/Blueprint/SceneActor/Curve/Curve_DropItem.Curve_DropItem",
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_OPEN_BOX"] = {
			["Key"] = "ITEM_SOURCE_OPEN_BOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_OPEN_CHEST"] = {
			["Key"] = "ITEM_SOURCE_OPEN_CHEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SYNTHESIS"] = {
			["Key"] = "ITEM_SOURCE_SYNTHESIS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947852288'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_DROP"] = {
			["Key"] = "ITEM_SOURCE_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_MAIL"] = {
			["Key"] = "ITEM_SOURCE_MAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_BASIC_SHOP_BUY"] = {
			["Key"] = "ITEM_SOURCE_BASIC_SHOP_BUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_NEWBIE_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_NEWBIE_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_AUCTION"] = {
			["Key"] = "ITEM_SOURCE_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ROLL"] = {
			["Key"] = "ITEM_SOURCE_ROLL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_STALL_BUY_IN"] = {
			["Key"] = "ITEM_SOURCE_STALL_BUY_IN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947851776'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_GUILD_MATERIAL_TASK"] = {
			["Key"] = "ITEM_SOURCE_GUILD_MATERIAL_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SCHEDULE_REWARD"] = {
			["Key"] = "ITEM_SOURCE_SCHEDULE_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PHARMACIST_EXPLORE_PRESCRIPTION"] = {
			["Key"] = "ITEM_SOURCE_PHARMACIST_EXPLORE_PRESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947858688'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PHARMACIST_QUICK_MAKE_MEDICINE"] = {
			["Key"] = "ITEM_SOURCE_PHARMACIST_QUICK_MAKE_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947858944'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TASK_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_TASK_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_ARBITRATOR_DICE_SUCCESS"] = {
			["Key"] = "ITEM_SOURCE_RP_ARBITRATOR_DICE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_SHERIFF_ATTACK_MONSTER"] = {
			["Key"] = "ITEM_SOURCE_RP_SHERIFF_ATTACK_MONSTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_ARBITRATOR_WIN_AFTER_DICE"] = {
			["Key"] = "ITEM_SOURCE_RP_ARBITRATOR_WIN_AFTER_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_COLLECTIBLES"] = {
			["Key"] = "ITEM_SOURCE_COLLECTIBLES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ACHIEVEMENT_LEVEL_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_ACHIEVEMENT_LEVEL_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ACHIEVEMENT_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_ACHIEVEMENT_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TEAM33_RANK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_TEAM33_RANK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947860992'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TEAM55_RANK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_TEAM55_RANK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947860992'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SEND_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_SEND_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_CONVERT_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_CONVERT_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_FASHION_REWARD"] = {
			["Key"] = "ITEM_SOURCE_FASHION_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_DANCE_LIFE"] = {
			["Key"] = "ITEM_SOURCE_DANCE_LIFE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_GUILD_LEAGUE_SETTLEMENT"] = {
			["Key"] = "ITEM_SOURCE_GUILD_LEAGUE_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_CHAT_ANON_DROP"] = {
			["Key"] = "ITEM_SOURCE_CHAT_ANON_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PLOT_RECAP_REWARD_SEND"] = {
			["Key"] = "ITEM_SOURCE_PLOT_RECAP_REWARD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PLOT_RECAP_LEVEL_REWARD_SEND"] = {
			["Key"] = "ITEM_SOURCE_PLOT_RECAP_LEVEL_REWARD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["MALL_TITLE"] = {
			["Key"] = "MALL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328571136'),
			["StringValueList"] = {},
		},
		["NO_WAY_TO_OBTAIN"] = {
			["Key"] = "NO_WAY_TO_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947863808'),
			["StringValueList"] = {},
		},
		["REFRESH_EVERY_MORNING"] = {
			["Key"] = "REFRESH_EVERY_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947864064'),
			["StringValueList"] = {},
		},
		["REFRESH_WEEK_MORNING"] = {
			["Key"] = "REFRESH_WEEK_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947864320'),
			["StringValueList"] = {},
		},
		["REFRESH_MONTH_MORNING"] = {
			["Key"] = "REFRESH_MONTH_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947864576'),
			["StringValueList"] = {},
		},
		["HUD_SHOPMALL"] = {
			["Key"] = "HUD_SHOPMALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574016'),
			["StringValueList"] = {},
		},
		["PAGE_TITLE"] = {
			["Key"] = "PAGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505966848'),
			["StringValueList"] = {},
		},
		["INFORM_TITLE"] = {
			["Key"] = "INFORM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947865344'),
			["StringValueList"] = {},
		},
		["INFORM_RATE"] = {
			["Key"] = "INFORM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947865600'),
			["StringValueList"] = {},
		},
		["INFORM_MONEY"] = {
			["Key"] = "INFORM_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947865856'),
			["StringValueList"] = {},
		},
		["SELL_TITLE"] = {
			["Key"] = "SELL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947866112'),
			["StringValueList"] = {},
		},
		["SELL_TRANSNAME"] = {
			["Key"] = "SELL_TRANSNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947866368'),
			["StringValueList"] = {},
		},
		["SELL_SETRATE"] = {
			["Key"] = "SELL_SETRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947866624'),
			["StringValueList"] = {},
		},
		["SELL_SETSELLNUM"] = {
			["Key"] = "SELL_SETSELLNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947866880'),
			["StringValueList"] = {},
		},
		["SELL_EXPECTGET"] = {
			["Key"] = "SELL_EXPECTGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947867136'),
			["StringValueList"] = {},
		},
		["SELL_TAXTIP"] = {
			["Key"] = "SELL_TAXTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947867392'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM"] = {
			["Key"] = "SELL_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505966848'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_TITLE"] = {
			["Key"] = "SELL_CONFIRM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947867904'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_RATE"] = {
			["Key"] = "SELL_CONFIRM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947868160'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_NUM"] = {
			["Key"] = "SELL_CONFIRM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947868416'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_GET"] = {
			["Key"] = "SELL_CONFIRM_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947868672'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_CONTENT"] = {
			["Key"] = "SELL_CONFIRM_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947868928'),
			["StringValueList"] = {},
		},
		["SELREC_TITLE"] = {
			["Key"] = "SELREC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947869184'),
			["StringValueList"] = {},
		},
		["SELREC_SELLING"] = {
			["Key"] = "SELREC_SELLING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947869440'),
			["StringValueList"] = {},
		},
		["SELREC_SELLTIME"] = {
			["Key"] = "SELREC_SELLTIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947869696'),
			["StringValueList"] = {},
		},
		["SELREC_NUM"] = {
			["Key"] = "SELREC_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947865856'),
			["StringValueList"] = {},
		},
		["SELREC_TIMEOUT"] = {
			["Key"] = "SELREC_TIMEOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947870208'),
			["StringValueList"] = {},
		},
		["SELREC_CANCEL"] = {
			["Key"] = "SELREC_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947870464'),
			["StringValueList"] = {},
		},
		["SELREC_CANCELCONFIRM"] = {
			["Key"] = "SELREC_CANCELCONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947870720'),
			["StringValueList"] = {},
		},
		["SELREC_SELLINGTIP"] = {
			["Key"] = "SELREC_SELLINGTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947870976'),
			["StringValueList"] = {},
		},
		["SELREC_SOLD"] = {
			["Key"] = "SELREC_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947871232'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDTIME"] = {
			["Key"] = "SELREC_SOLDTIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947871488'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDNUM"] = {
			["Key"] = "SELREC_SOLDNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947871744'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDTIP"] = {
			["Key"] = "SELREC_SOLDTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947872000'),
			["StringValueList"] = {},
		},
		["BUY_NAME"] = {
			["Key"] = "BUY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947872256'),
			["StringValueList"] = {},
		},
		["BUY_TRANSNAME"] = {
			["Key"] = "BUY_TRANSNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947872512'),
			["StringValueList"] = {},
		},
		["BUY_CURRATE"] = {
			["Key"] = "BUY_CURRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947872768'),
			["StringValueList"] = {},
		},
		["BUY_EXPRATE"] = {
			["Key"] = "BUY_EXPRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947873024'),
			["StringValueList"] = {},
		},
		["BUY_EXPRATETIP"] = {
			["Key"] = "BUY_EXPRATETIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947873280'),
			["StringValueList"] = {},
		},
		["BUY_MYCOIN"] = {
			["Key"] = "BUY_MYCOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947873536'),
			["StringValueList"] = {},
		},
		["BUY_SETBUYNUM"] = {
			["Key"] = "BUY_SETBUYNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690022912'),
			["StringValueList"] = {},
		},
		["BUY_EXPECTCONSUME"] = {
			["Key"] = "BUY_EXPECTCONSUME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947874048'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM"] = {
			["Key"] = "BUY_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32097364356608'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_TITLE"] = {
			["Key"] = "BUY_CONFIRM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493891840'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_RATE"] = {
			["Key"] = "BUY_CONFIRM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947874816'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_NUM"] = {
			["Key"] = "BUY_CONFIRM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947875072'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_CONSUME"] = {
			["Key"] = "BUY_CONFIRM_CONSUME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947875328'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_CONTENT"] = {
			["Key"] = "BUY_CONFIRM_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947875584'),
			["StringValueList"] = {},
		},
		["BUYREC_TITLE"] = {
			["Key"] = "BUYREC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947875840'),
			["StringValueList"] = {},
		},
		["BUYREC_NUM"] = {
			["Key"] = "BUYREC_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947876096'),
			["StringValueList"] = {},
		},
		["BUYREC_TIME"] = {
			["Key"] = "BUYREC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947876352'),
			["StringValueList"] = {},
		},
		["CASHMARKET_EMPTY_CONTENT"] = {
			["Key"] = "CASHMARKET_EMPTY_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947876608'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_NOT_ALLOW"] = {
			["Key"] = "TOWERCLIMB_FELLOW_NOT_ALLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947876864'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_EXIT"] = {
			["Key"] = "TOWERCLIMB_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947877120'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CHANGEFELLOW"] = {
			["Key"] = "TOWERCLIMB_CHANGEFELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947877376'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_SUCCESS"] = {
			["Key"] = "TOWERCLIMB_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947877632'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW"] = {
			["Key"] = "TOWERCLIMB_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947877888'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FAILURE"] = {
			["Key"] = "TOWERCLIMB_FAILURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947878144'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_RETRY"] = {
			["Key"] = "TOWERCLIMB_RETRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947878400'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NEXTLEVEL"] = {
			["Key"] = "TOWERCLIMB_NEXTLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947878656'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_INFO"] = {
			["Key"] = "TOWERCLIMB_CURRENT_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947878912'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_LAYER"] = {
			["Key"] = "TOWERCLIMB_CURRENT_LAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947879168'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_PLAYERLEVEL"] = {
			["Key"] = "TOWERCLIMB_CURRENT_PLAYERLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947879424'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENTER_PLAYERLEVEL"] = {
			["Key"] = "TOWERCLIMB_ENTER_PLAYERLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947879680'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_COMBAT"] = {
			["Key"] = "TOWERCLIMB_CURRENT_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947879936'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_RECOMMEND_COMBAT"] = {
			["Key"] = "TOWERCLIMB_RECOMMEND_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947880192'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LAYER_INDEX"] = {
			["Key"] = "TOWERCLIMB_LAYER_INDEX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947880448'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FIRST_REWARD"] = {
			["Key"] = "TOWERCLIMB_FIRST_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947834624'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_GET_REWARD"] = {
			["Key"] = "TOWERCLIMB_GET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947880960'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ALREADYGET_REWARD"] = {
			["Key"] = "TOWERCLIMB_ALREADYGET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947881216'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_NOT_USED"] = {
			["Key"] = "TOWERCLIMB_FELLOW_NOT_USED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947881472'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ADD_FELLOW"] = {
			["Key"] = "TOWERCLIMB_ADD_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947881728'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_UNLOCK"] = {
			["Key"] = "TOWERCLIMB_FELLOW_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947773184'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NEXT_INFO"] = {
			["Key"] = "TOWERCLIMB_NEXT_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947882240'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ALL_LEVEL_SUCCESS"] = {
			["Key"] = "TOWERCLIMB_ALL_LEVEL_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947882496'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LEVEL_NOT_MATCH"] = {
			["Key"] = "TOWERCLIMB_LEVEL_NOT_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947882752'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_DISABLE_SUPPORT"] = {
			["Key"] = "TOWERCLIMB_DISABLE_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883008'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENABLE_SUPPORT"] = {
			["Key"] = "TOWERCLIMB_ENABLE_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883264'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENTER"] = {
			["Key"] = "TOWERCLIMB_ENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883520'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LAYER_LOCK"] = {
			["Key"] = "TOWERCLIMB_LAYER_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883776'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NAME"] = {
			["Key"] = "TOWERCLIMB_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520500992'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_USED_TIME"] = {
			["Key"] = "TOWERCLIMB_USED_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947884288'),
			["StringValueList"] = {},
		},
		["POPUP_TODAY_NO_CHECKBOX"] = {
			["Key"] = "POPUP_TODAY_NO_CHECKBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947884544'),
			["StringValueList"] = {},
		},
		["GACHA_DESC1"] = {
			["Key"] = "GACHA_DESC1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947884800'),
			["StringValueList"] = {},
		},
		["GACHA_DESC2"] = {
			["Key"] = "GACHA_DESC2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574528'),
			["StringValueList"] = {},
		},
		["GACHA_DESC3"] = {
			["Key"] = "GACHA_DESC3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947885312'),
			["StringValueList"] = {},
		},
		["GACHA_DESC4"] = {
			["Key"] = "GACHA_DESC4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947885568'),
			["StringValueList"] = {},
		},
		["GACHA_DESC5"] = {
			["Key"] = "GACHA_DESC5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947885824'),
			["StringValueList"] = {},
		},
		["GACHA_DESC6"] = {
			["Key"] = "GACHA_DESC6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947885312'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY"] = {
			["Key"] = "GACHA_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947886336'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL"] = {
			["Key"] = "GACHA_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814656'),
			["StringValueList"] = {},
		},
		["GACHA_SHOP"] = {
			["Key"] = "GACHA_SHOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947886848'),
			["StringValueList"] = {},
		},
		["GACHA_FREE"] = {
			["Key"] = "GACHA_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947887104'),
			["StringValueList"] = {},
		},
		["GACHA_ONCE"] = {
			["Key"] = "GACHA_ONCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947887360'),
			["StringValueList"] = {},
		},
		["GACHA_TEN"] = {
			["Key"] = "GACHA_TEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947887616'),
			["StringValueList"] = {},
		},
		["GACHA_LIMIT"] = {
			["Key"] = "GACHA_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947887872'),
			["StringValueList"] = {},
		},
		["GACHA_FREETIMER"] = {
			["Key"] = "GACHA_FREETIMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947888128'),
			["StringValueList"] = {},
		},
		["GACHA_TITIE"] = {
			["Key"] = "GACHA_TITIE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328567040'),
			["StringValueList"] = {},
		},
		["GACHA_RATE"] = {
			["Key"] = "GACHA_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947888640'),
			["StringValueList"] = {},
		},
		["GACHA_RATE1"] = {
			["Key"] = "GACHA_RATE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947888896'),
			["StringValueList"] = {},
		},
		["GACHA_RATE2"] = {
			["Key"] = "GACHA_RATE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947889152'),
			["StringValueList"] = {},
		},
		["GACHA_UR"] = {
			["Key"] = "GACHA_UR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947889408'),
			["StringValueList"] = {},
		},
		["GACHA_SSR"] = {
			["Key"] = "GACHA_SSR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947889664'),
			["StringValueList"] = {},
		},
		["GACHA_SR"] = {
			["Key"] = "GACHA_SR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947889920'),
			["StringValueList"] = {},
		},
		["GACHA_R"] = {
			["Key"] = "GACHA_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947890176'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_UR"] = {
			["Key"] = "GACHA_UP_DESC_UR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947890432'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_SSR"] = {
			["Key"] = "GACHA_UP_DESC_SSR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947890688'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_SR"] = {
			["Key"] = "GACHA_UP_DESC_SR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947890944'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_R"] = {
			["Key"] = "GACHA_UP_DESC_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947891200'),
			["StringValueList"] = {},
		},
		["GACHA_OVERALL_RATE_UR"] = {
			["Key"] = "GACHA_OVERALL_RATE_UR",
			["StringValue"] = "2.53%",
			["StringValueList"] = {},
		},
		["GACHA_OVERALL_RATE_SSR"] = {
			["Key"] = "GACHA_OVERALL_RATE_SSR",
			["StringValue"] = "14.67%",
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_UP_ITEM"] = {
			["Key"] = "GACHA_DETAIL_UP_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947891968'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_RULES"] = {
			["Key"] = "GACHA_DETAIL_RULES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947892224'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_LIST"] = {
			["Key"] = "GACHA_DETAIL_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947892480'),
			["StringValueList"] = {},
		},
		["GACHA_SHOP_TITLE"] = {
			["Key"] = "GACHA_SHOP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947886848'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TIME"] = {
			["Key"] = "GACHA_HISTORY_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947892992'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_COMMON"] = {
			["Key"] = "GACHA_POOL_COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947893248'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_UP"] = {
			["Key"] = "GACHA_POOL_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947893504'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_SEALED_COMMON"] = {
			["Key"] = "GACHA_POOL_SEALED_COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947893760'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_SEALED_UP"] = {
			["Key"] = "GACHA_POOL_SEALED_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947894016'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TITLE"] = {
			["Key"] = "GACHA_HISTORY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947886336'),
			["StringValueList"] = {},
		},
		["GACHA_UR_SEALED"] = {
			["Key"] = "GACHA_UR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947894528'),
			["StringValueList"] = {},
		},
		["GACHA_SSR_SEALED"] = {
			["Key"] = "GACHA_SSR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947894784'),
			["StringValueList"] = {},
		},
		["GACHA_SR_SEALED"] = {
			["Key"] = "GACHA_SR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947895040'),
			["StringValueList"] = {},
		},
		["GACHA_DESC1_SEALED"] = {
			["Key"] = "GACHA_DESC1_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947895296'),
			["StringValueList"] = {},
		},
		["GACHA_DESC2_SEALED"] = {
			["Key"] = "GACHA_DESC2_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574528'),
			["StringValueList"] = {},
		},
		["GACHA_DESC3_SEALED"] = {
			["Key"] = "GACHA_DESC3_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947895808'),
			["StringValueList"] = {},
		},
		["GACHA_DESC4_SEALED"] = {
			["Key"] = "GACHA_DESC4_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947885568'),
			["StringValueList"] = {},
		},
		["GACHA_DESC5_SEALED"] = {
			["Key"] = "GACHA_DESC5_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947896320'),
			["StringValueList"] = {},
		},
		["GACHA_DESC6_SEALED"] = {
			["Key"] = "GACHA_DESC6_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947895808'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_FELLOW"] = {
			["Key"] = "GACHA_HISTORY_TAB_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_ITEM"] = {
			["Key"] = "GACHA_HISTORY_TAB_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947897088'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_SEALED"] = {
			["Key"] = "GACHA_HISTORY_TAB_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["GACHA_RESULT_CONVERT"] = {
			["Key"] = "GACHA_RESULT_CONVERT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947897600'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TIPS"] = {
			["Key"] = "GACHA_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947897856'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_TITLE"] = {
			["Key"] = "FELLOW_LANGUAGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_AUTOFILL"] = {
			["Key"] = "FELLOW_LANGUAGE_AUTOFILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947898368'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_JOINCOMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_JOINCOMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947898624'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_ASSISTCOMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_ASSISTCOMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947898880'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_KICK"] = {
			["Key"] = "FELLOW_LANGUAGE_KICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947899136'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_COMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774397952'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_LEVEL"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53395838731776'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_RARITY"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_RARITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947899904'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_FAVORABILITY"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_FAVORABILITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947900160'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_STARUP_MAXIMUM"] = {
			["Key"] = "FELLOW_LANGUAGE_STARUP_MAXIMUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947900416'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_NONEXISTENCE"] = {
			["Key"] = "FELLOW_LANGUAGE_NONEXISTENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947900672'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_I"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_I",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947900928'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_II"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_II",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901184'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_III"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_III",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901440'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_IV"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_IV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901696'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_V"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_V",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901952'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_VI"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_VI",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947902208'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_VII"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_VII",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947902208'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_TYPE1"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_TYPE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987987712'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_TYPE2"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_TYPE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947902976'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_UNLOCK"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947903232'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_CD"] = {
			["Key"] = "FELLOW_SKILL_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947903488'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_STARUP_EFFECT"] = {
			["Key"] = "FELLOW_SKILL_STARUP_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947903744'),
			["StringValueList"] = {},
		},
		["FELLOW_MAINTAB_1"] = {
			["Key"] = "FELLOW_MAINTAB_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947904000'),
			["StringValueList"] = {},
		},
		["FELLOW_MAINTAB_2"] = {
			["Key"] = "FELLOW_MAINTAB_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947904256'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_1"] = {
			["Key"] = "FELLOW_TYPE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38277822295808'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_2"] = {
			["Key"] = "FELLOW_TYPE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947904768'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_3"] = {
			["Key"] = "FELLOW_TYPE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947905024'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_COMPLEMENT"] = {
			["Key"] = "FELLOW_LANGUAGE_COMPLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947905280'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_1_NEXT"] = {
			["Key"] = "FELLOW_GACHA_1_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947905536'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_10_NEXT"] = {
			["Key"] = "FELLOW_GACHA_10_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947905792'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_HISTORY_TIPS"] = {
			["Key"] = "FELLOW_GACHA_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947897856'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_LEVELUP"] = {
			["Key"] = "FELLOW_BTN_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836364800'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_STARLEVELUP"] = {
			["Key"] = "FELLOW_BTN_STARLEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863681024'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_COMBINE"] = {
			["Key"] = "FELLOW_BTN_COMBINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033043200'),
			["StringValueList"] = {},
		},
		["FELLOW_ZHANLI_ADD"] = {
			["Key"] = "FELLOW_ZHANLI_ADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947907072'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_CLEARALL"] = {
			["Key"] = "FELLOW_LANGUAGE_CLEARALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947907328'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_1"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947907584'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_2"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947907840'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_3"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947908096'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_4"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883264'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_5"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883264'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_6"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947883008'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_7"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947909120'),
			["StringValueList"] = {},
		},
		["SEALED_ITEM"] = {
			["Key"] = "SEALED_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE"] = {
			["Key"] = "SEFIROT_CORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_10173435356416'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_TABS"] = {
			["Key"] = "SEALED_ENHANCE_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_TABS"] = {
			["Key"] = "SEALED_RANKUP_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863681024'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_TABS"] = {
			["Key"] = "SEALED_RANDOM_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025848139520'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_BUTTON"] = {
			["Key"] = "SEALED_ENHANCE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_BUTTON"] = {
			["Key"] = "SEALED_RANKUP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61026385010432'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025848139520'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_INPUT_BUTTON"] = {
			["Key"] = "SEALED_RANK_UP_INPUT_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911424'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_RESERVE_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_RESERVE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911680'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_REPLACE_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_REPLACE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_AGAIN_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_AGAIN_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947912192'),
			["StringValueList"] = {},
		},
		["SEALED_EQUIP_BUTTON"] = {
			["Key"] = "SEALED_EQUIP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033042688'),
			["StringValueList"] = {},
		},
		["SEALED_MODIFY_BUTTON"] = {
			["Key"] = "SEALED_MODIFY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033042944'),
			["StringValueList"] = {},
		},
		["SEALED_DISEQUIP_BUTTON"] = {
			["Key"] = "SEALED_DISEQUIP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033045760'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR"] = {
			["Key"] = "SEALED_FIXED_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947913216'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR"] = {
			["Key"] = "SEALED_RANDOM_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947913472'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_POS"] = {
			["Key"] = "SEALED_FIXED_ATTR_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947913728'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_NEG"] = {
			["Key"] = "SEALED_FIXED_ATTR_NEG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947913984'),
			["StringValueList"] = {},
		},
		["SEALED_NEXT_FIXED_ATTR"] = {
			["Key"] = "SEALED_NEXT_FIXED_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947914240'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_STATUS"] = {
			["Key"] = "SEALED_RANK_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863681024'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_STATUS"] = {
			["Key"] = "SEALED_FIXED_ATTR_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947914752'),
			["StringValueList"] = {},
		},
		["SEALED_RESON_STATUS"] = {
			["Key"] = "SEALED_RESON_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915008'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_UP_PREVIEW"] = {
			["Key"] = "SEALED_ENHANCE_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_UP_PREVIEW"] = {
			["Key"] = "SEALED_FIXED_ATTR_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915520'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_PREVIEW"] = {
			["Key"] = "SEALED_RANK_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863681024'),
			["StringValueList"] = {},
		},
		["SEALED_RESON_UP_PREVIEW"] = {
			["Key"] = "SEALED_RESON_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915008'),
			["StringValueList"] = {},
		},
		["SEALED_SORTED_BY_QUALITY"] = {
			["Key"] = "SEALED_SORTED_BY_QUALITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947916288'),
			["StringValueList"] = {},
		},
		["SEALED_SORTED_BY_RESON"] = {
			["Key"] = "SEALED_SORTED_BY_RESON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915008'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_TOTAL_TIMES"] = {
			["Key"] = "SEALED_RANDOM_TOTAL_TIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947916800'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_LAST_TIME_RESULT"] = {
			["Key"] = "SEALED_RANDOM_LAST_TIME_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947917056'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_SHOW"] = {
			["Key"] = "SEALED_RANDOM_ATTR_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947917312'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_CURRENT"] = {
			["Key"] = "SEALED_RANDOM_ATTR_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947917568'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_NEW"] = {
			["Key"] = "SEALED_RANDOM_ATTR_NEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947917824'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_SELECT_REMINDER"] = {
			["Key"] = "SEALED_ENHANCE_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947918080'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_SELECT_REMINDER"] = {
			["Key"] = "SEALED_RANK_UP_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947918336'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_SELECT_REMINDER"] = {
			["Key"] = "SEALED_RANDOM_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947918592'),
			["StringValueList"] = {},
		},
		["SEALED_EQUIP_SELECT_REMINDER"] = {
			["Key"] = "SEALED_EQUIP_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947918848'),
			["StringValueList"] = {},
		},
		["SEALED_NONE_REMINDER"] = {
			["Key"] = "SEALED_NONE_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947919104'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RESON"] = {
			["Key"] = "SEFIROT_CORE_RESON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947919360'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_SWITCH_BUTTON"] = {
			["Key"] = "SEFIROT_CORE_SWITCH_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_SLOT_UNLOCK_CONDITION"] = {
			["Key"] = "SEFIROT_CORE_SLOT_UNLOCK_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947919872'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_CURRENT_ACTIVE"] = {
			["Key"] = "SEFIROT_CORE_CURRENT_ACTIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947920128'),
			["StringValueList"] = {},
		},
		["SEALED_RANK1"] = {
			["Key"] = "SEALED_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947900928'),
			["StringValueList"] = {},
		},
		["SEALED_RANK2"] = {
			["Key"] = "SEALED_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901184'),
			["StringValueList"] = {},
		},
		["SEALED_RANK3"] = {
			["Key"] = "SEALED_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901440'),
			["StringValueList"] = {},
		},
		["SEALED_RANK4"] = {
			["Key"] = "SEALED_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901696'),
			["StringValueList"] = {},
		},
		["SEALED_RANK5"] = {
			["Key"] = "SEALED_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947901952'),
			["StringValueList"] = {},
		},
		["SEALED_RANK6"] = {
			["Key"] = "SEALED_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947902208'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_TITLE"] = {
			["Key"] = "SEALED_RANK_UP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947921920'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RANK"] = {
			["Key"] = "SEFIROT_CORE_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774397440'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RATE"] = {
			["Key"] = "SEFIROT_CORE_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947922432'),
			["StringValueList"] = {},
		},
		["SEFIROT_SELECT_TITLE"] = {
			["Key"] = "SEFIROT_SELECT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947922688'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_CURRENT"] = {
			["Key"] = "SEFIROT_CORE_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947922944'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_OTHER"] = {
			["Key"] = "SEFIROT_CORE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947923200'),
			["StringValueList"] = {},
		},
		["SEFIROT_BUTTON_REPLACE"] = {
			["Key"] = "SEFIROT_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947923456'),
			["StringValueList"] = {},
		},
		["BANNER_TITLE_L"] = {
			["Key"] = "BANNER_TITLE_L",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_10173435356416'),
			["StringValueList"] = {},
		},
		["BANNER_TITLE_R"] = {
			["Key"] = "BANNER_TITLE_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["BAR_INITIAL"] = {
			["Key"] = "BAR_INITIAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947924224'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_BANNER"] = {
			["Key"] = "SEFIROT_CORE_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947924480'),
			["StringValueList"] = {},
		},
		["SEALED_BANNER"] = {
			["Key"] = "SEALED_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947924736'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_ALL"] = {
			["Key"] = "SEALED_BUTTON_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946749440'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_PILE"] = {
			["Key"] = "SEALED_BUTTON_PILE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033046784'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER"] = {
			["Key"] = "SEALED_SELECT_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947925504'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER1"] = {
			["Key"] = "SEALED_SELECT_BANNER1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947925760'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER2"] = {
			["Key"] = "SEALED_SELECT_BANNER2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947926016'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER3"] = {
			["Key"] = "SEALED_SELECT_BANNER3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947926272'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY1_TEXT"] = {
			["Key"] = "SEALED_RARITY1_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947925760'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY2_TEXT"] = {
			["Key"] = "SEALED_RARITY2_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947926784'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY3_TEXT"] = {
			["Key"] = "SEALED_RARITY3_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947927040'),
			["StringValueList"] = {},
		},
		["SEALED_COMON_TEXT"] = {
			["Key"] = "SEALED_COMON_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47829561116160'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_RESPON"] = {
			["Key"] = "SEALED_TIP_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915008'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_POSITIVE"] = {
			["Key"] = "SEALED_TIP_POSITIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947927808'),
			["StringValueList"] = {},
		},
		["SEALED_TIPS_NEGATIVE"] = {
			["Key"] = "SEALED_TIPS_NEGATIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947928064'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_POSITIVE_BREIF"] = {
			["Key"] = "SEALED_TIP_POSITIVE_BREIF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947928320'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_NEGATIVE_BREIF"] = {
			["Key"] = "SEALED_TIP_NEGATIVE_BREIF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947928576'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_EQUIP"] = {
			["Key"] = "SEALED_BUTTON_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947928832'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_EQUIPOFF"] = {
			["Key"] = "SEALED_BUTTON_EQUIPOFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033045760'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_REPLACE"] = {
			["Key"] = "SEALED_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_EMPTY"] = {
			["Key"] = "SEALED_REFINE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947929600'),
			["StringValueList"] = {},
		},
		["SEALED_UPGRADE_TITLE"] = {
			["Key"] = "SEALED_UPGRADE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265604608'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_TITLE"] = {
			["Key"] = "SEALED_RANKUP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61026385010432'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_TITLE"] = {
			["Key"] = "SEALED_REFINE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025848139520'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_NEXT"] = {
			["Key"] = "REFINE_BUTTON_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025848139520'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_RESET"] = {
			["Key"] = "REFINE_BUTTON_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18212003515392'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_REPLACE"] = {
			["Key"] = "REFINE_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["UPGRADE_TEXT_UP"] = {
			["Key"] = "UPGRADE_TEXT_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947931392'),
			["StringValueList"] = {},
		},
		["UPGRADE_TEXT_DOWN"] = {
			["Key"] = "UPGRADE_TEXT_DOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947931648'),
			["StringValueList"] = {},
		},
		["UPGRADE_LEVEL"] = {
			["Key"] = "UPGRADE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947931904'),
			["StringValueList"] = {},
		},
		["UPGRADE_PREVIEW"] = {
			["Key"] = "UPGRADE_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947932160'),
			["StringValueList"] = {},
		},
		["UPGRADE_RESPON"] = {
			["Key"] = "UPGRADE_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947932416'),
			["StringValueList"] = {},
		},
		["RESPON_TEXT_RATE"] = {
			["Key"] = "RESPON_TEXT_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947932672'),
			["StringValueList"] = {},
		},
		["RESPON_TEXT_VALUE"] = {
			["Key"] = "RESPON_TEXT_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947915008'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_LUCK"] = {
			["Key"] = "SEALED_REFINE_LUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774399232'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE1"] = {
			["Key"] = "SEFIROT_CORE_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947933440'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE2"] = {
			["Key"] = "SEFIROT_CORE_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947933696'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE3"] = {
			["Key"] = "SEFIROT_CORE_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947933952'),
			["StringValueList"] = {},
		},
		["UPGRADE_BUTTON_RESPON"] = {
			["Key"] = "UPGRADE_BUTTON_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265604608'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_EMPTY"] = {
			["Key"] = "SEALED_RANKUP_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947934464'),
			["StringValueList"] = {},
		},
		["RANKUP_EMPTY_BUTTON"] = {
			["Key"] = "RANKUP_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030778880'),
			["StringValueList"] = {},
		},
		["RANKUP_BUTTON_AUTOFILL"] = {
			["Key"] = "RANKUP_BUTTON_AUTOFILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947898368'),
			["StringValueList"] = {},
		},
		["SEALED_UPGRADE_MAX_TEXT"] = {
			["Key"] = "SEALED_UPGRADE_MAX_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947935232'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_ELEMENT_RATE"] = {
			["Key"] = "SEFIROT_CORE_ELEMENT_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947922432'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_CURRENT"] = {
			["Key"] = "SEALED_REFINE_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947935744'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_MAX_TEXT"] = {
			["Key"] = "SEFIROT_CORE_MAX_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947936000'),
			["StringValueList"] = {},
		},
		["SEALED_EMPTY_TEXT"] = {
			["Key"] = "SEALED_EMPTY_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947936256'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY_TITLE_TEXT"] = {
			["Key"] = "SEALED_RARITY_TITLE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947936512'),
			["StringValueList"] = {},
		},
		["RELIVE_FREE"] = {
			["Key"] = "RELIVE_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947887104'),
			["StringValueList"] = {},
		},
		["RELIVE_COOLDOWN"] = {
			["Key"] = "RELIVE_COOLDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947937024'),
			["StringValueList"] = {},
		},
		["RELIVE_UNAVAILABLE"] = {
			["Key"] = "RELIVE_UNAVAILABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947937280'),
			["StringValueList"] = {},
		},
		["ASK_REVIVE"] = {
			["Key"] = "ASK_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947937536'),
			["StringValueList"] = {},
		},
		["DEFEATED_BY"] = {
			["Key"] = "DEFEATED_BY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947937792'),
			["StringValueList"] = {},
		},
		["CLIENT_AUTO_REVIVE"] = {
			["Key"] = "CLIENT_AUTO_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947938048'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_EXCHANGE"] = {
			["Key"] = "SETTING_BTN_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947938304'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_CANCEL"] = {
			["Key"] = "SETTING_BTN_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["SETTING_TITLE"] = {
			["Key"] = "SETTING_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_DEFAULT"] = {
			["Key"] = "SETTING_FUNC_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493886464'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_EXCHANGE"] = {
			["Key"] = "SETTING_FUNC_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493874944'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_LOGOUT"] = {
			["Key"] = "SETTING_FUNC_LOGOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493875200'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_UNSTUCK"] = {
			["Key"] = "SETTING_FUNC_UNSTUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493874688'),
			["StringValueList"] = {},
		},
		["SETTING_RES_FULLSCREENWINDOW"] = {
			["Key"] = "SETTING_RES_FULLSCREENWINDOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947940096'),
			["StringValueList"] = {},
		},
		["SETTING_RES_WINDOW"] = {
			["Key"] = "SETTING_RES_WINDOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947940352'),
			["StringValueList"] = {},
		},
		["SETTING_RES_CUSTOM"] = {
			["Key"] = "SETTING_RES_CUSTOM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35185445832960'),
			["StringValueList"] = {},
		},
		["SETTING_CHOICE_NONE"] = {
			["Key"] = "SETTING_CHOICE_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752704'),
			["StringValueList"] = {},
		},
		["SETTING_CHOICE_AUTO"] = {
			["Key"] = "SETTING_CHOICE_AUTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947941120'),
			["StringValueList"] = {},
		},
		["SETTING_SR_NATIVE"] = {
			["Key"] = "SETTING_SR_NATIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947941376'),
			["StringValueList"] = {},
		},
		["SETTING_SR_ULTRA"] = {
			["Key"] = "SETTING_SR_ULTRA",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947941632'),
			["StringValueList"] = {},
		},
		["SETTING_SR_QUALITY"] = {
			["Key"] = "SETTING_SR_QUALITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947941888'),
			["StringValueList"] = {},
		},
		["SETTING_SR_BALANCED"] = {
			["Key"] = "SETTING_SR_BALANCED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947942144'),
			["StringValueList"] = {},
		},
		["SETTING_SR_PERFORMANCE"] = {
			["Key"] = "SETTING_SR_PERFORMANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947942400'),
			["StringValueList"] = {},
		},
		["SETTING_SR_ULTRAPERF"] = {
			["Key"] = "SETTING_SR_ULTRAPERF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789824'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_ANNOUNCEMENT"] = {
			["Key"] = "SETTING_BTN_ANNOUNCEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506023680'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_USERCENTER"] = {
			["Key"] = "SETTING_BTN_USERCENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947943168'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_REPORT"] = {
			["Key"] = "SETTING_BTN_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328582656'),
			["StringValueList"] = {},
		},
		["SOCIAL_WORLD"] = {
			["Key"] = "SOCIAL_WORLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947810560'),
			["StringValueList"] = {},
		},
		["SOCIAL_NEAR"] = {
			["Key"] = "SOCIAL_NEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947943936'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM"] = {
			["Key"] = "SOCIAL_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328576512'),
			["StringValueList"] = {},
		},
		["SOCIAL_SYSTEM"] = {
			["Key"] = "SOCIAL_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946750464'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_FRIEND"] = {
			["Key"] = "SOCIAL_DELETE_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947944704'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_CLUB_MEMBER"] = {
			["Key"] = "SOCIAL_DELETE_CLUB_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947944960'),
			["StringValueList"] = {},
		},
		["SOCIAL_CREATE_GROUP"] = {
			["Key"] = "SOCIAL_CREATE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947945216'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAMING"] = {
			["Key"] = "SOCIAL_TEAMING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947945472'),
			["StringValueList"] = {},
		},
		["SOCIAL_LEISURE"] = {
			["Key"] = "SOCIAL_LEISURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947945728'),
			["StringValueList"] = {},
		},
		["SOCIAL_PLAYMODE"] = {
			["Key"] = "SOCIAL_PLAYMODE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947945984'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANGE_GROUP_NAME"] = {
			["Key"] = "SOCIAL_CHANGE_GROUP_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947946240'),
			["StringValueList"] = {},
		},
		["SOCIAL_GROUP_EDIT"] = {
			["Key"] = "SOCIAL_GROUP_EDIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947946496'),
			["StringValueList"] = {},
		},
		["SOCIAL_HAS_SELECT_FRIEND_NUM"] = {
			["Key"] = "SOCIAL_HAS_SELECT_FRIEND_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947946752'),
			["StringValueList"] = {},
		},
		["SOCIAL_TOP"] = {
			["Key"] = "SOCIAL_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947008'),
			["StringValueList"] = {},
		},
		["SOCIAL_BLOCK"] = {
			["Key"] = "SOCIAL_BLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947264'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANGE_REMARK"] = {
			["Key"] = "SOCIAL_CHANGE_REMARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947520'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANNEL_SETTING"] = {
			["Key"] = "SOCIAL_CHANNEL_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947776'),
			["StringValueList"] = {},
		},
		["SOCIAL_BLACK_FRIEND"] = {
			["Key"] = "SOCIAL_BLACK_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947948032'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_NAME_BLACK_LIST"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_NAME_BLACK_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947948288'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_NAME_DEFAULT"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_NAME_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947948544'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_BLACKLIST"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_BLACKLIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947948800'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_REMOVE_BLACKLIST"] = {
			["Key"] = "SOCIAL_FRIEND_REMOVE_BLACKLIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947949056'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_OFFLINE"] = {
			["Key"] = "SOCIAL_FRIEND_OFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947949312'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ONLINE"] = {
			["Key"] = "SOCIAL_FRIEND_ONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947949568'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_AFK"] = {
			["Key"] = "SOCIAL_FRIEND_AFK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947949824'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_EMPTY"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947950080'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_GROUP"] = {
			["Key"] = "SOCIAL_DELETE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947950336'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_GROUP_BTN"] = {
			["Key"] = "SOCIAL_DELETE_GROUP_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947950592'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SET_TOP_TIPS"] = {
			["Key"] = "SOCIAL_FRIEND_SET_TOP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947950848'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SETTING_BULLET"] = {
			["Key"] = "SOCIAL_FRIEND_SETTING_BULLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947951104'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_PLEASE_FRIEND_FIRST"] = {
			["Key"] = "SOCIAL_FRIEND_PLEASE_FRIEND_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093225454080'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TIPS_ALREADY_APPLY"] = {
			["Key"] = "SOCIAL_FRIEND_TIPS_ALREADY_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337284864'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947951872'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_CIRCLE"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_CIRCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947952384'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_MASTER"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947952640'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_RELATION"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_RELATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947952896'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_EMPTY_NOTICE"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_EMPTY_NOTICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947953152'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_FILTER_ALLFRIENDS"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_FILTER_ALLFRIENDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947953408'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_AVAILABLE_NOTICE_COUNT"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_AVAILABLE_NOTICE_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947953664'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_DEFAULT_NAME"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_DEFAULT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947953920'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_CREATE"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947954176'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_MODIFY_SETTING"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_MODIFY_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947954432'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_DISBAND_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_DISBAND_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493881344'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_EXIT_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_EXIT_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493889024'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_CLEAR_MSG"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_CLEAR_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493920000'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_REMARK_PLACEHOLDER"] = {
			["Key"] = "SOCIAL_FRIEND_REMARK_PLACEHOLDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947955456'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_THIS_SERVER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_THIS_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947955712'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_IGNORE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_IGNORE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947955968'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_MALE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_MALE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947956224'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_FEMALE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_FEMALE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947956480'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_RECOMMEND"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947956736'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_TEAMMATE"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_TEAMMATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947956992'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_APPLE"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_APPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947957248'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_RECOMMEND"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947957504'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_RECENT_TEAM"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_RECENT_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947957760'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_APPLY"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947958016'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_FIND_PARTNER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_FIND_PARTNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947958272'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SETTING"] = {
			["Key"] = "SOCIAL_FRIEND_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_FLOWER"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_FLOWER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947958784'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_FOOD"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_FOOD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947959040'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_ITEM"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947959296'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947959552'),
			["StringValueList"] = {},
		},
		["SOCIAL_SAVE_EDIT_GROUP"] = {
			["Key"] = "SOCIAL_SAVE_EDIT_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947959808'),
			["StringValueList"] = {},
		},
		["SOCIAL_NEWMSG_NUM"] = {
			["Key"] = "SOCIAL_NEWMSG_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947960064'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_ENTER"] = {
			["Key"] = "SOCIAL_TEAM_ENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61163018680320'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_LEADER"] = {
			["Key"] = "SOCIAL_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61163018681088'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_EXIT"] = {
			["Key"] = "SOCIAL_TEAM_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947960832'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_REWARD"] = {
			["Key"] = "SOCIAL_TEAM_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947961088'),
			["StringValueList"] = {},
		},
		["SOCIAL_NO_TEAM"] = {
			["Key"] = "SOCIAL_NO_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947961344'),
			["StringValueList"] = {},
		},
		["SOCIAL_NO_SYSTEM"] = {
			["Key"] = "SOCIAL_NO_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947961600'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_CHAT_FRIEND"] = {
			["Key"] = "SOCIAL_DELETE_CHAT_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493898240'),
			["StringValueList"] = {},
		},
		["SOCIAL_CLEAR_MESSAGE"] = {
			["Key"] = "SOCIAL_CLEAR_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947962112'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_LOUDSPEAKER"] = {
			["Key"] = "SOCIAL_CHAT_LOUDSPEAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947962368'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_1"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947962624'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_2"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947962880'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_3"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947963136'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947963392'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_EMPTY_BUTTON"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947963648'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_AT_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_AT_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947963904'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_HISTORY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947964160'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PREFIX_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_PREFIX_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947964416'),
			["StringValueList"] = {},
		},
		["SOCIAL_REVIVE_CD"] = {
			["Key"] = "SOCIAL_REVIVE_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947964672'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_APPLY_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_APPLY_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947964928'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SEARCH_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_SEARCH_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947965184'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_GUILD_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947965440'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD_EMPTY_BUTTON"] = {
			["Key"] = "SOCIAL_CHAT_GUILD_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947965696'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHOOSE_AT"] = {
			["Key"] = "SOCIAL_CHAT_CHOOSE_AT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947965952'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_HISTORY"] = {
			["Key"] = "SOCIAL_CHAT_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947966208'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SETTING"] = {
			["Key"] = "SOCIAL_CHAT_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947966464'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_MY_INFORMATION"] = {
			["Key"] = "SOCIAL_CHAT_MY_INFORMATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947966720'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL_SETTING"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947776'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PREFIX"] = {
			["Key"] = "SOCIAL_CHAT_PREFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947967232'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_RECRUIT"] = {
			["Key"] = "SOCIAL_CHAT_SET_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947967488'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_URL"] = {
			["Key"] = "SOCIAL_CHAT_SET_URL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947967744'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_SMALLWINS"] = {
			["Key"] = "SOCIAL_CHAT_SET_SMALLWINS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947968000'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_CHATOPEN"] = {
			["Key"] = "SOCIAL_CHAT_SET_CHATOPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947968256'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_PATHFIND"] = {
			["Key"] = "SOCIAL_CHAT_SET_PATHFIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947968512'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_CHANNEL_SHOW"] = {
			["Key"] = "SOCIAL_CHAT_SET_CHANNEL_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947968768'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_WINS"] = {
			["Key"] = "SOCIAL_CHAT_SET_WINS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947969024'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_OTHERS"] = {
			["Key"] = "SOCIAL_CHAT_SET_OTHERS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947969280'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_COMMON_CHANNEL"] = {
			["Key"] = "SOCIAL_CHAT_SET_COMMON_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947969536'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_RECUIRT"] = {
			["Key"] = "SOCIAL_CHAT_SET_RECUIRT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947969792'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SPEAKER"] = {
			["Key"] = "SOCIAL_CHAT_SPEAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947970048'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_ACTIVE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_ACTIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206467840'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_PASSIVE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_PASSIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947970560'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD"] = {
			["Key"] = "SOCIAL_CHAT_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506023680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_EMOTION"] = {
			["Key"] = "SOCIAL_CHAT_EMOTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53808155592704'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_ITEM"] = {
			["Key"] = "SOCIAL_CHAT_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947897088'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REDPACK"] = {
			["Key"] = "SOCIAL_CHAT_REDPACK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947971584'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_COLLECT"] = {
			["Key"] = "SOCIAL_CHAT_COLLECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485163008'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_MISSION"] = {
			["Key"] = "SOCIAL_CHAT_MISSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328566528'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_STALL"] = {
			["Key"] = "SOCIAL_CHAT_SET_STALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_10310874304512'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER"] = {
			["Key"] = "SOCIAL_CHAT_STICKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947972608'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947972864'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947973120'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY_CE"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947973376'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY_CE"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947973632'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_NPC"] = {
			["Key"] = "SOCIAL_CHAT_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947973888'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_AUDITION"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_AUDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947974144'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_FINISH"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947974400'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_RECORDING"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_RECORDING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947974656'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_COUNTDOWN"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947974912'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_IDENTIFY_FAIL"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_IDENTIFY_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947975168'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_NEW_MESSAGE"] = {
			["Key"] = "SOCIAL_CHAT_NEW_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947975424'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_PRESS"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_PRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947975680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_CONTROL"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_CONTROL",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_6872752981504'), Game.TableDataManager:GetLangStr('str_54839216411394'), Game.TableDataManager:GetLangStr('str_54839216411395'), Game.TableDataManager:GetLangStr('str_54839216411396'), Game.TableDataManager:GetLangStr('str_54839216411397'), Game.TableDataManager:GetLangStr('str_54839216411398'), Game.TableDataManager:GetLangStr('str_54839216411399'), Game.TableDataManager:GetLangStr('str_54839216411400'), Game.TableDataManager:GetLangStr('str_6872752982528'), Game.TableDataManager:GetLangStr('str_6872752983040')},
		},
		["SOCIAL_CHAT_IGNORE"] = {
			["Key"] = "SOCIAL_CHAT_IGNORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947976192'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REPLY"] = {
			["Key"] = "SOCIAL_CHAT_REPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947976448'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_ATREPLY"] = {
			["Key"] = "SOCIAL_CHAT_ATREPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947976448'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_COPY"] = {
			["Key"] = "SOCIAL_CHAT_COPY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947976960'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REPORT"] = {
			["Key"] = "SOCIAL_CHAT_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947808000'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_DAMAGE_TYPE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_DAMAGE_TYPE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216412929'), Game.TableDataManager:GetLangStr('str_54839216412930')},
		},
		["SOCIAL_CHAT_BATTLE_ELEMENT_TYPE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_ELEMENT_TYPE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_36079335904520'), Game.TableDataManager:GetLangStr('str_36079067473410'), Game.TableDataManager:GetLangStr('str_36079335910145'), Game.TableDataManager:GetLangStr('str_56625654138624')},
		},
		["SOCIAL_CHAT_BATTLE_CRITICAL_STRIKE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_CRITICAL_STRIKE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947977984'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VALUABLE_SHOW"] = {
			["Key"] = "SOCIAL_CHAT_VALUABLE_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947978240'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PRIVATE_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_PRIVATE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947978496'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947978752'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_DETAIL"] = {
			["Key"] = "SOCIAL_CHAT_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814656'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_FRIEND_INVITE_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_FRIEND_INVITE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947979264'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_INVITE_CONDITION"] = {
			["Key"] = "SOCIAL_GUILD_INVITE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947979520'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_FRIEND_INVITE"] = {
			["Key"] = "SOCIAL_GUILD_FRIEND_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337289216'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_CREATE_PLAYER_LEVEL"] = {
			["Key"] = "SOCIAL_GUILD_CREATE_PLAYER_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947980032'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_CREATE_MONEY"] = {
			["Key"] = "SOCIAL_GUILD_CREATE_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947980288'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_CONDITION"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947980544'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_FAIL"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947980800'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MERGE_TITLE"] = {
			["Key"] = "SOCIAL_GUILD_MERGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947981056'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_LIMIT"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947981312'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_AFTER"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_AFTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947981568'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_CD"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947981824'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_BUILDING_LEVEL_MAX"] = {
			["Key"] = "SOCIAL_GUILD_BUILDING_LEVEL_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947982080'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947982336'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947982592'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947982848'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_PUB_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947983104'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947983360'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947983616'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_3"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947983872'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947984128'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947984384'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947984640'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_3"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947984896'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_4"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947985152'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947985408'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947985664'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947985920'),
			["StringValueList"] = {},
		},
		["SOCIAL_DUNGEON_STAGE_TIPS"] = {
			["Key"] = "SOCIAL_DUNGEON_STAGE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947986176'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_NO_DESCRIPTION"] = {
			["Key"] = "SOCIAL_FRIEND_NO_DESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947986432'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUPMESSAGE_HINT"] = {
			["Key"] = "SOCIAL_FRIEND_GROUPMESSAGE_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947986688'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_RELATION_CHANGE"] = {
			["Key"] = "SOCIAL_FRIEND_RELATION_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947986944'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_OPEN_BARRIAGE"] = {
			["Key"] = "SOCIAL_FRIEND_OPEN_BARRIAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947987200'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_PUT_TOP"] = {
			["Key"] = "SOCIAL_FRIEND_PUT_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947987456'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TODAY"] = {
			["Key"] = "SOCIAL_FRIEND_TODAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947987712'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_YESTER++"] = {
			["Key"] = "SOCIAL_FRIEND_YESTER++",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947755776'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_BEFORE_YESTERDAY"] = {
			["Key"] = "SOCIAL_FRIEND_BEFORE_YESTERDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947988224'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_NEW_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_NEW_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947951872'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_MODIFY_GROUP_INFO_BTN_OK"] = {
			["Key"] = "SOCIAL_FRIEND_MODIFY_GROUP_INFO_BTN_OK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_TITLE"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947988992'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_MODIFY"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947989248'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_INPUT_HINT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_INPUT_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947989504'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CHAT_STATE_TEMP"] = {
			["Key"] = "SOCIAL_FRIEND_CHAT_STATE_TEMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947989760'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_RECENT"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_RECENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947990016'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947990272'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_CONTACT"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_CONTACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_MODIFY_GROUP_ALREADY_SELECT"] = {
			["Key"] = "SOCIAL_FRIEND_MODIFY_GROUP_ALREADY_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947990784'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_ACCEPT_ALL"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_ACCEPT_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337287936'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_IGNORE_ALL"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_IGNORE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947991296'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_NEXT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947991552'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_ADD_BTN"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_ADD_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947991808'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_EMPTY_GUILD_NAME"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_EMPTY_GUILD_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752704'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_APPLY_MSG_COUNT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_APPLY_MSG_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947992320'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_FAVOR_LEVEL"] = {
			["Key"] = "SOCIAL_FRIEND_FAVOR_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337297408'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCENE"] = {
			["Key"] = "SOCIAL_FRIEND_SCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947992832'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_PATTERN"] = {
			["Key"] = "SOCIAL_TEAM_PATTERN",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216428545'), Game.TableDataManager:GetLangStr('str_54838947907584'), Game.TableDataManager:GetLangStr('str_54839216428547'), Game.TableDataManager:GetLangStr('str_54839216428548'), Game.TableDataManager:GetLangStr('str_54839216428549'), Game.TableDataManager:GetLangStr('str_54839216428550'), Game.TableDataManager:GetLangStr('str_54839216428551'), Game.TableDataManager:GetLangStr('str_54839216428552'), Game.TableDataManager:GetLangStr('str_54839216428553'), Game.TableDataManager:GetLangStr('str_54839216428554'), "%+%+"},
		},
		["SOCIAL_TEAM_PATTERN2"] = {
			["Key"] = "SOCIAL_TEAM_PATTERN2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947993344'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_SUPPORT_BULLET"] = {
			["Key"] = "SOCIAL_TEAM_SUPPORT_BULLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947993600'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL_PICK"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL_PICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947969536'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_NUM"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947994112'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_NUM"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947994368'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY_CLICK"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493869824'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY_CLICK"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947994880'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_INVITE"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947995136'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE_TIPS"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947995392'),
			["StringValueList"] = {},
		},
		["SOCIAL_DISCLOSE_TAG"] = {
			["Key"] = "SOCIAL_DISCLOSE_TAG",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216431105'), Game.TableDataManager:GetLangStr('str_54839216431106')},
		},
		["SOCIAL_DISCLOSE"] = {
			["Key"] = "SOCIAL_DISCLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947995904'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANONYMOUS_DISCLOSE"] = {
			["Key"] = "SOCIAL_ANONYMOUS_DISCLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947996160'),
			["StringValueList"] = {},
		},
		["SOCIAL_BEDISCLOSED"] = {
			["Key"] = "SOCIAL_BEDISCLOSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947996416'),
			["StringValueList"] = {},
		},
		["SOCIAL_DISCLOSED"] = {
			["Key"] = "SOCIAL_DISCLOSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947996672'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_BUY_BUTTON"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_BUY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32097364356608'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE_BUTTON"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997184'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_CONFIRM_NOTE2"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_CONFIRM_NOTE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997440'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SAVE"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997696'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLE"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997952'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLE_TIP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLE_TIP",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947998464'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947998720'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLEFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLEFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947998976'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947999232'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947999488'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLEGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLEGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947999744'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948000000'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948000256'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLECLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLECLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948000512'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948000768'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVEFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVEFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948001024'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948001280'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVEGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVEGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948001536'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948001792'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVECLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVECLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948002048'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_TAG"] = {
			["Key"] = "SOCIAL_ANOY_TAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948002304'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_DISCLOSE_SUCCESS"] = {
			["Key"] = "SOCIAL_ANOY_DISCLOSE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948002560'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_DISCLOSE_FAIL"] = {
			["Key"] = "SOCIAL_ANOY_DISCLOSE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948002816'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARE"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948003072'),
			["StringValueList"] = {},
		},
		["SOCIAL_CREATE_GROUP_BUTTON"] = {
			["Key"] = "SOCIAL_CREATE_GROUP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948003328'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_NODISTURB"] = {
			["Key"] = "SOCIAL_WHISPER_SET_NODISTURB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948003584'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_TOP"] = {
			["Key"] = "SOCIAL_WHISPER_SET_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947987456'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_CHANGE_REMARK"] = {
			["Key"] = "SOCIAL_WHISPER_SET_CHANGE_REMARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947520'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_GROUP"] = {
			["Key"] = "SOCIAL_WHISPER_SET_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948004352'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_CLEAR_MESSAGE"] = {
			["Key"] = "SOCIAL_WHISPER_SET_CLEAR_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493920000'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_ALLONLINE"] = {
			["Key"] = "SOCIAL_RANGE_ALLONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948004864'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_PARTONLINE"] = {
			["Key"] = "SOCIAL_RANGE_PARTONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948005120'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_PARTOFFLINE"] = {
			["Key"] = "SOCIAL_RANGE_PARTOFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948005376'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_ALLOFFLINE"] = {
			["Key"] = "SOCIAL_RANGE_ALLOFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948005632'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_SETTING"] = {
			["Key"] = "SOCIAL_RANGE_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948005888'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TOPMSG"] = {
			["Key"] = "SOCIAL_CHATROOM_TOPMSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948006144'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMLIST_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMLIST_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948006400'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948006656'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_RETURNROOM_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_RETURNROOM_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948006912'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_FASTENTER_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_FASTENTER_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948007168'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CANCELCREATE_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_CANCELCREATE_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_NAMETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_NAMETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948007680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948007936'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948008192'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMMAIN_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMMAIN_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948008448'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SEND_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_SEND_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756032'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MEMBERS_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MEMBERS_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948008960'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_POWERSETTING_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_POWERSETTING_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948009216'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARETOCHANNEL"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARETOCHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948009472'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARETOFRIEND"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARETOFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948009728'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMNAME_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMNAME_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948007680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMTYPE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMTYPE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948007936'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_INVITE"] = {
			["Key"] = "SOCIAL_CHATROOM_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_10310874306560'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MICMANAGE_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MICMANAGE_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948010752'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_FORBIDSPEECH_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_FORBIDSPEECH_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011008'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_BLACKLIST_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_BLACKLIST_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011264'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_TITLE"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011520'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011776'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_HINTTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_HINTTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948012032'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493896192'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MASTER_NAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MASTER_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948012544'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MEMBERLIST_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MEMBERLIST_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948012800'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLICATION_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLICATION_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948013056'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ACCEPT_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ACCEPT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948013312'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_REJECT_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_REJECT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948013568'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_HINTTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_HINTTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948013824'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_TITLETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_TITLETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948014080'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_NOTICE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_NOTICE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948014336'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_AUTODESTROY_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_AUTODESTROY_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948014592'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948014848'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMINFO_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMINFO_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948015104'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_EXITROOM_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_EXITROOM_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948015360'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SAVESET_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_SAVESET_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011776'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TRANSFORM_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TRANSFORM_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948015872'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TRANSFORMMASTER_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TRANSFORMMASTER_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948016128'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_INVITE_CARD"] = {
			["Key"] = "SOCIAL_CHAT_INVITE_CARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948016384'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CURONMIC_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CURONMIC_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948016640'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MICOFF_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_MICOFF_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948016896'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_DENOISE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_DENOISE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948017152'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SIMPLE_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_SIMPLE_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948014848'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TOTOP"] = {
			["Key"] = "SOCIAL_CHATROOM_TOTOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947008'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GOTO_END"] = {
			["Key"] = "SOCIAL_CHAT_GOTO_END",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948017920'),
			["StringValueList"] = {},
		},
		["MAIL_TITLE"] = {
			["Key"] = "MAIL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206477824'),
			["StringValueList"] = {},
		},
		["MAIL_NAV_TITLE"] = {
			["Key"] = "MAIL_NAV_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206477824'),
			["StringValueList"] = {},
		},
		["MAIL_SENDER_DESC"] = {
			["Key"] = "MAIL_SENDER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948018688'),
			["StringValueList"] = {},
		},
		["MAIL_TIME_DESC"] = {
			["Key"] = "MAIL_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948018944'),
			["StringValueList"] = {},
		},
		["MAIL_DELETE"] = {
			["Key"] = "MAIL_DELETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947788800'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVE"] = {
			["Key"] = "MAIL_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948019456'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVED_DESC"] = {
			["Key"] = "MAIL_RECEIVED_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948019712'),
			["StringValueList"] = {},
		},
		["MAIL_DAY_EXPIRE"] = {
			["Key"] = "MAIL_DAY_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948019968'),
			["StringValueList"] = {},
		},
		["MAIL_HOUR_EXPIRE"] = {
			["Key"] = "MAIL_HOUR_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948020224'),
			["StringValueList"] = {},
		},
		["MAIL_MIN_EXPIRE"] = {
			["Key"] = "MAIL_MIN_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948020480'),
			["StringValueList"] = {},
		},
		["MAIL_ONE_MIN_EXPIRE"] = {
			["Key"] = "MAIL_ONE_MIN_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948020736'),
			["StringValueList"] = {},
		},
		["MAIL_EXPIRE"] = {
			["Key"] = "MAIL_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948020992'),
			["StringValueList"] = {},
		},
		["MAIL_YMDHM_FORMAT"] = {
			["Key"] = "MAIL_YMDHM_FORMAT",
			["StringValue"] = "%s-%02d-%02d %02d:%02d",
			["StringValueList"] = {},
		},
		["MAIL_YMD_FORMAT"] = {
			["Key"] = "MAIL_YMD_FORMAT",
			["StringValue"] = "%s-%02d-%02d",
			["StringValueList"] = {},
		},
		["MAIL_CHURCH"] = {
			["Key"] = "MAIL_CHURCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["StringValueList"] = {},
		},
		["MAIL_TRADE_DESC"] = {
			["Key"] = "MAIL_TRADE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948022016'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVE_ALL"] = {
			["Key"] = "MAIL_RECEIVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948022272'),
			["StringValueList"] = {},
		},
		["MAIL_DELETE_READ"] = {
			["Key"] = "MAIL_DELETE_READ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948022528'),
			["StringValueList"] = {},
		},
		["MAIL_INBOX"] = {
			["Key"] = "MAIL_INBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948022784'),
			["StringValueList"] = {},
		},
		["MAIL_OUTBOX"] = {
			["Key"] = "MAIL_OUTBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948023040'),
			["StringValueList"] = {},
		},
		["MAIL_TEST"] = {
			["Key"] = "MAIL_TEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948023296'),
			["StringValueList"] = {},
		},
		["MAIL_BLANK"] = {
			["Key"] = "MAIL_BLANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948023552'),
			["StringValueList"] = {},
		},
		["MAIL_SYSTEM"] = {
			["Key"] = "MAIL_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946750464'),
			["StringValueList"] = {},
		},
		["MAIL_NPC"] = {
			["Key"] = "MAIL_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948024064'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION"] = {
			["Key"] = "MAIL_COLLECTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505957632'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_REMOVE_ALL"] = {
			["Key"] = "MAIL_COLLECTION_REMOVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948024576'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_REMOVE_SINGLE"] = {
			["Key"] = "MAIL_COLLECTION_REMOVE_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948024832'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_COUNT"] = {
			["Key"] = "MAIL_COLLECTION_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948025088'),
			["StringValueList"] = {},
		},
		["LetterSlideOpenTips"] = {
			["Key"] = "LetterSlideOpenTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948025344'),
			["StringValueList"] = {},
		},
		["BookOpenTips"] = {
			["Key"] = "BookOpenTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948025600'),
			["StringValueList"] = {},
		},
		["DateDesc"] = {
			["Key"] = "DateDesc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948025856'),
			["StringValueList"] = {},
		},
		["Detaildesc"] = {
			["Key"] = "Detaildesc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948026112'),
			["StringValueList"] = {},
		},
		["PartyA_Desc"] = {
			["Key"] = "PartyA_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948026368'),
			["StringValueList"] = {},
		},
		["PartyB_Desc"] = {
			["Key"] = "PartyB_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948026624'),
			["StringValueList"] = {},
		},
		["PVP_ENTRANCE_SEASON_DESC"] = {
			["Key"] = "PVP_ENTRANCE_SEASON_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_28451205547520'),
			["StringValueList"] = {},
		},
		["PVP_ENTRANCE_SEASON_TIME_DESC"] = {
			["Key"] = "PVP_ENTRANCE_SEASON_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948027136'),
			["StringValueList"] = {},
		},
		["TEAM_SHOW_ENJOYABLE"] = {
			["Key"] = "TEAM_SHOW_ENJOYABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948027392'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_TEAM"] = {
			["Key"] = "TEAM_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26183462816256'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_TEAM_1"] = {
			["Key"] = "TEAM_QUICK_TEAM_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948027904'),
			["StringValueList"] = {},
		},
		["TEAM_CREATE_TEAM"] = {
			["Key"] = "TEAM_CREATE_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948028160'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_ALL_NEARBY_TEAM"] = {
			["Key"] = "TEAM_APPLY_ALL_NEARBY_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948028416'),
			["StringValueList"] = {},
		},
		["TEAM_ALLTARGET"] = {
			["Key"] = "TEAM_ALLTARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55526142510592'),
			["StringValueList"] = {},
		},
		["TEAM_NOTARGET"] = {
			["Key"] = "TEAM_NOTARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55526142510848'),
			["StringValueList"] = {},
		},
		["TEAM_INVITED"] = {
			["Key"] = "TEAM_INVITED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029184'),
			["StringValueList"] = {},
		},
		["TEAM_APPLIED"] = {
			["Key"] = "TEAM_APPLIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337284864'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_LEADER"] = {
			["Key"] = "TEAM_APPLY_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029696'),
			["StringValueList"] = {},
		},
		["TEAM_MATCHING"] = {
			["Key"] = "TEAM_MATCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029952'),
			["StringValueList"] = {},
		},
		["TEAM_AROUND_TEAM"] = {
			["Key"] = "TEAM_AROUND_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948030208'),
			["StringValueList"] = {},
		},
		["TEAM_AROUND_TEAM_ONLY"] = {
			["Key"] = "TEAM_AROUND_TEAM_ONLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948030464'),
			["StringValueList"] = {},
		},
		["TEAM_TARGET_SCREEN"] = {
			["Key"] = "TEAM_TARGET_SCREEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948030720'),
			["StringValueList"] = {},
		},
		["TEAM_WHOSTEAM"] = {
			["Key"] = "TEAM_WHOSTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947836416'),
			["StringValueList"] = {},
		},
		["TEAM_NOLIMIT"] = {
			["Key"] = "TEAM_NOLIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948031232'),
			["StringValueList"] = {},
		},
		["TEAM_ALLIGNORE"] = {
			["Key"] = "TEAM_ALLIGNORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947991296'),
			["StringValueList"] = {},
		},
		["TEAM_FOLLOW"] = {
			["Key"] = "TEAM_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948031744'),
			["StringValueList"] = {},
		},
		["TEAM_IS FOLLOW"] = {
			["Key"] = "TEAM_IS FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948032000'),
			["StringValueList"] = {},
		},
		["TEAM_START_FOLLOW"] = {
			["Key"] = "TEAM_START_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493869568'),
			["StringValueList"] = {},
		},
		["TEAM_MATCHING_TIME"] = {
			["Key"] = "TEAM_MATCHING_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948032512'),
			["StringValueList"] = {},
		},
		["TEAM_CANCLE_FOLLOW"] = {
			["Key"] = "TEAM_CANCLE_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947782912'),
			["StringValueList"] = {},
		},
		["TEAM_MYTEAM"] = {
			["Key"] = "TEAM_MYTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823104'),
			["StringValueList"] = {},
		},
		["TEAM_EXIT_TEAM"] = {
			["Key"] = "TEAM_EXIT_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493911552'),
			["StringValueList"] = {},
		},
		["TEAM_START_PLAY"] = {
			["Key"] = "TEAM_START_PLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948033536'),
			["StringValueList"] = {},
		},
		["TEAM_AUTO_MATCH"] = {
			["Key"] = "TEAM_AUTO_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948033792'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_MATCH"] = {
			["Key"] = "TEAM_QUICK_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948034048'),
			["StringValueList"] = {},
		},
		["TEAM_MATCH_TEAMER"] = {
			["Key"] = "TEAM_MATCH_TEAMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948034304'),
			["StringValueList"] = {},
		},
		["TEAM_LOCATION"] = {
			["Key"] = "TEAM_LOCATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948034560'),
			["StringValueList"] = {},
		},
		["TEAM_NEIBOR_NO_SINGLE"] = {
			["Key"] = "TEAM_NEIBOR_NO_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948034816'),
			["StringValueList"] = {},
		},
		["TEAM_NO_FRIEND"] = {
			["Key"] = "TEAM_NO_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948035072'),
			["StringValueList"] = {},
		},
		["TEAM_GUILD_FRUIEND"] = {
			["Key"] = "TEAM_GUILD_FRUIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948035328'),
			["StringValueList"] = {},
		},
		["TEAM_FRIEND"] = {
			["Key"] = "TEAM_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["StringValueList"] = {},
		},
		["TEAM_GUILD"] = {
			["Key"] = "TEAM_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["StringValueList"] = {},
		},
		["TEAM_NEIBOR"] = {
			["Key"] = "TEAM_NEIBOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947943936'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_TO_BE_LEADER"] = {
			["Key"] = "TEAM_APPLY_TO_BE_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948036352'),
			["StringValueList"] = {},
		},
		["TEAM_REPLY_LEADER"] = {
			["Key"] = "TEAM_REPLY_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948036608'),
			["StringValueList"] = {},
		},
		["TEAM_START_GATHER"] = {
			["Key"] = "TEAM_START_GATHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948036864'),
			["StringValueList"] = {},
		},
		["TEAM_CANT_ENJION"] = {
			["Key"] = "TEAM_CANT_ENJION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948037120'),
			["StringValueList"] = {},
		},
		["TEAM_CANT_INVITE"] = {
			["Key"] = "TEAM_CANT_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948037376'),
			["StringValueList"] = {},
		},
		["TEAM_YOUHAD"] = {
			["Key"] = "TEAM_YOUHAD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948037632'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_FRIEND"] = {
			["Key"] = "TEAM_APPLY_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947951872'),
			["StringValueList"] = {},
		},
		["TEAM_CE"] = {
			["Key"] = "TEAM_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774397952'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_CREATETEAM"] = {
			["Key"] = "TEAM_INVITE_CREATETEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947995136'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_COMBINETEAM"] = {
			["Key"] = "TEAM_APPLY_COMBINETEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948038656'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_JOINTEAM"] = {
			["Key"] = "TEAM_APPLY_JOINTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493869824'),
			["StringValueList"] = {},
		},
		["TEAM_CE_UNLIMITED"] = {
			["Key"] = "TEAM_CE_UNLIMITED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948039168'),
			["StringValueList"] = {},
		},
		["TEAM_RECRUIT_MSG"] = {
			["Key"] = "TEAM_RECRUIT_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948039424'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216474881'), Game.TableDataManager:GetLangStr('str_54839216474882'), Game.TableDataManager:GetLangStr('str_54839216474883')},
		},
		["TEAM_RECRUIT_ROW_MSG"] = {
			["Key"] = "TEAM_RECRUIT_ROW_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948039680'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216475137'), Game.TableDataManager:GetLangStr('str_54839216475138'), Game.TableDataManager:GetLangStr('str_54839216475139')},
		},
		["TEAM_FRIEND_NO_SINGLE"] = {
			["Key"] = "TEAM_FRIEND_NO_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948039936'),
			["StringValueList"] = {},
		},
		["TEAM_TRANSFFORM_CAPTAIN"] = {
			["Key"] = "TEAM_TRANSFFORM_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948040192'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_CAPTAIN"] = {
			["Key"] = "TEAM_APPLY_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029696'),
			["StringValueList"] = {},
		},
		["TEAM_KICK_TEAM"] = {
			["Key"] = "TEAM_KICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948040704'),
			["StringValueList"] = {},
		},
		["TEAM_TEAM"] = {
			["Key"] = "TEAM_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948040960'),
			["StringValueList"] = {},
		},
		["TEAM_OUTPUT"] = {
			["Key"] = "TEAM_OUTPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402759936'),
			["StringValueList"] = {},
		},
		["TEAM_HEAL"] = {
			["Key"] = "TEAM_HEAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402760192'),
			["StringValueList"] = {},
		},
		["TEAM_DEFEND"] = {
			["Key"] = "TEAM_DEFEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48311402759680'),
			["StringValueList"] = {},
		},
		["TEAM_INPUT"] = {
			["Key"] = "TEAM_INPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948041984'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_YOUR_JOIN_TEAM"] = {
			["Key"] = "TEAM_INVITE_YOUR_JOIN_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948042240'),
			["StringValueList"] = {},
		},
		["TEAM_AUTO_RECEIVE_FOLLOW_APPLY"] = {
			["Key"] = "TEAM_AUTO_RECEIVE_FOLLOW_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948042496'),
			["StringValueList"] = {},
		},
		["GROUP_FIRST"] = {
			["Key"] = "GROUP_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067466243'),
			["StringValueList"] = {},
		},
		["GROUP_SECOND"] = {
			["Key"] = "GROUP_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769088'),
			["StringValueList"] = {},
		},
		["GROUP_THIRD"] = {
			["Key"] = "GROUP_THIRD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769344'),
			["StringValueList"] = {},
		},
		["GROUP_FOURTH"] = {
			["Key"] = "GROUP_FOURTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769600'),
			["StringValueList"] = {},
		},
		["GROUP_FIFTH"] = {
			["Key"] = "GROUP_FIFTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947769856'),
			["StringValueList"] = {},
		},
		["GROUP_ALL"] = {
			["Key"] = "GROUP_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946749440'),
			["StringValueList"] = {},
		},
		["GROUP_GROUP"] = {
			["Key"] = "GROUP_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948044288'),
			["StringValueList"] = {},
		},
		["GROUP_CREATE_GROUP"] = {
			["Key"] = "GROUP_CREATE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948044544'),
			["StringValueList"] = {},
		},
		["GROUP_MY_GROUP"] = {
			["Key"] = "GROUP_MY_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947823360'),
			["StringValueList"] = {},
		},
		["GROUP_VIEW_GROUP"] = {
			["Key"] = "GROUP_VIEW_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948045056'),
			["StringValueList"] = {},
		},
		["GROUP_X_GROUP"] = {
			["Key"] = "GROUP_X_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948045312'),
			["StringValueList"] = {},
		},
		["GROUP_DISBAND_GROUP"] = {
			["Key"] = "GROUP_DISBAND_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493892608'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_GROUP"] = {
			["Key"] = "GROUP_QUIT_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493892864'),
			["StringValueList"] = {},
		},
		["TEAM_TO_GROUP"] = {
			["Key"] = "TEAM_TO_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493890816'),
			["StringValueList"] = {},
		},
		["GROUP_AUTO_MATCH"] = {
			["Key"] = "GROUP_AUTO_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948033792'),
			["StringValueList"] = {},
		},
		["GROUP_READY_CHECK"] = {
			["Key"] = "GROUP_READY_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948046592'),
			["StringValueList"] = {},
		},
		["GROUP_ADJUST_POS"] = {
			["Key"] = "GROUP_ADJUST_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948046848'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_ADJUST_POS"] = {
			["Key"] = "GROUP_QUIT_ADJUST_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948047104'),
			["StringValueList"] = {},
		},
		["GROUP_IN_CHECK"] = {
			["Key"] = "GROUP_IN_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948047360'),
			["StringValueList"] = {},
		},
		["GROUP_CHECK_INFO"] = {
			["Key"] = "GROUP_CHECK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948047616'),
			["StringValueList"] = {},
		},
		["GROUP_SET_MANAGER"] = {
			["Key"] = "GROUP_SET_MANAGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948047872'),
			["StringValueList"] = {},
		},
		["GROUP_UNSET_MANAGER"] = {
			["Key"] = "GROUP_UNSET_MANAGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948048128'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT"] = {
			["Key"] = "GROUP_SHOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948048384'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_RECRUIT"] = {
			["Key"] = "GROUP_SHOUT_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948048640'),
			["StringValueList"] = {},
		},
		["GROUP_GUILD_CHANNEL"] = {
			["Key"] = "GROUP_GUILD_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948048896'),
			["StringValueList"] = {},
		},
		["GROUP_RECRUIT_CHANNEL"] = {
			["Key"] = "GROUP_RECRUIT_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948049152'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_DEFAULT_MESSAGE"] = {
			["Key"] = "GROUP_SHOUT_DEFAULT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948049408'),
			["StringValueList"] = {},
		},
		["GROUP_HISTORT_MESSAGE"] = {
			["Key"] = "GROUP_HISTORT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947966208'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_HINT"] = {
			["Key"] = "GROUP_SHOUT_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948049920'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_HISTORY"] = {
			["Key"] = "GROUP_SHOUT_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948050176'),
			["StringValueList"] = {},
		},
		["GROUP_REVIVE"] = {
			["Key"] = "GROUP_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493868544'),
			["StringValueList"] = {},
		},
		["GROUP_VIEW_TEAM"] = {
			["Key"] = "GROUP_VIEW_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948050688'),
			["StringValueList"] = {},
		},
		["GROUP_CHARACTER_INFO"] = {
			["Key"] = "GROUP_CHARACTER_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948050944'),
			["StringValueList"] = {},
		},
		["GROUP_KICK_GROUP"] = {
			["Key"] = "GROUP_KICK_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948051200'),
			["StringValueList"] = {},
		},
		["GROUP_TRANS_GROUP_LEADER"] = {
			["Key"] = "GROUP_TRANS_GROUP_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948051456'),
			["StringValueList"] = {},
		},
		["GROUP_BAN_VOICE"] = {
			["Key"] = "GROUP_BAN_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948051712'),
			["StringValueList"] = {},
		},
		["GROUP_CANCEL_VOICE"] = {
			["Key"] = "GROUP_CANCEL_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948051968'),
			["StringValueList"] = {},
		},
		["GROUP_TRANS_TEAM_LEADER"] = {
			["Key"] = "GROUP_TRANS_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493929728'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_JOIN_GROUP"] = {
			["Key"] = "GROUP_APPLY_JOIN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947994880'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_GROUP_TEAM_LEADER"] = {
			["Key"] = "GROUP_APPLY_GROUP_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029696'),
			["StringValueList"] = {},
		},
		["GROUP_INVITE_JOIN_TEAM"] = {
			["Key"] = "GROUP_INVITE_JOIN_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948052992'),
			["StringValueList"] = {},
		},
		["GROUP_KICK_GROUPMEMBER"] = {
			["Key"] = "GROUP_KICK_GROUPMEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948053248'),
			["StringValueList"] = {},
		},
		["GROUP_PROMOTE_TEAMLEADER"] = {
			["Key"] = "GROUP_PROMOTE_TEAMLEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948053504'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_JOIN_MESSAGE"] = {
			["Key"] = "GROUP_APPLY_JOIN_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948053760'),
			["StringValueList"] = {},
		},
		["GROUP_INVITE_YOUR_JOIN_GROUP"] = {
			["Key"] = "GROUP_INVITE_YOUR_JOIN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054016'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_LISTEN"] = {
			["Key"] = "TEAM_VOICE_LISTEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054272'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_REFUSE"] = {
			["Key"] = "TEAM_VOICE_REFUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054528'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_OPENMIC"] = {
			["Key"] = "TEAM_VOICE_OPENMIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054784'),
			["StringValueList"] = {},
		},
		["GROUP_AUTO_AGREE_APPLY"] = {
			["Key"] = "GROUP_AUTO_AGREE_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948055040'),
			["StringValueList"] = {},
		},
		["GROUP_LEFT_COUNT"] = {
			["Key"] = "GROUP_LEFT_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337281280'),
			["StringValueList"] = {},
		},
		["TEAM_RECENT"] = {
			["Key"] = "TEAM_RECENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947990016'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_LIST"] = {
			["Key"] = "TEAM_APPLY_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948055808'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_1"] = {
			["Key"] = "TEAM_SUPPORT_LINE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948056064'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_2"] = {
			["Key"] = "TEAM_SUPPORT_LINE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948056320'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_3"] = {
			["Key"] = "TEAM_SUPPORT_LINE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948056576'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_4"] = {
			["Key"] = "TEAM_SUPPORT_LINE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948056832'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_5"] = {
			["Key"] = "TEAM_SUPPORT_LINE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948057088'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_THANKS_1"] = {
			["Key"] = "TEAM_SUPPORT_THANKS_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948057344'),
			["StringValueList"] = {},
		},
		["TEAM_MESSAGE_NUM_LIMIT"] = {
			["Key"] = "TEAM_MESSAGE_NUM_LIMIT",
			["StringValue"] = "%s /%s",
			["StringValueList"] = {},
		},
		["TEAM_LIST_EMPTY"] = {
			["Key"] = "TEAM_LIST_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948057856'),
			["StringValueList"] = {},
		},
		["TEAM_CANCEL_MATCH"] = {
			["Key"] = "TEAM_CANCEL_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948058112'),
			["StringValueList"] = {},
		},
		["FRIEND_APPLY_TIP"] = {
			["Key"] = "FRIEND_APPLY_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948058368'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_EMPTY"] = {
			["Key"] = "TEAM_INVITE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948058624'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_TAB_FRIEND"] = {
			["Key"] = "TEAM_INVITE_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_TAB_STRANGER"] = {
			["Key"] = "TEAM_INVITE_TAB_STRANGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948059136'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_MESSAGE"] = {
			["Key"] = "TEAM_APPLY_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863694080'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_MESSAGE_SAVE"] = {
			["Key"] = "TEAM_APPLY_MESSAGE_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997696'),
			["StringValueList"] = {},
		},
		["TEAM_REBIRTH"] = {
			["Key"] = "TEAM_REBIRTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493868544'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_SCENE"] = {
			["Key"] = "TEAM_MARK_SCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948060160'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_SCENE_DESC"] = {
			["Key"] = "TEAM_MARK_SCENE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948060416'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_MEMBER"] = {
			["Key"] = "TEAM_MARK_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948060672'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_MEMBER_DESC"] = {
			["Key"] = "TEAM_MARK_MEMBER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948060928'),
			["StringValueList"] = {},
		},
		["TEAM_EXPECTED_MATCH_TIME"] = {
			["Key"] = "TEAM_EXPECTED_MATCH_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947804160'),
			["StringValueList"] = {},
		},
		["TEAM_HASUSED_MATCH_TIME"] = {
			["Key"] = "TEAM_HASUSED_MATCH_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948061440'),
			["StringValueList"] = {},
		},
		["MORE_DETAIL"] = {
			["Key"] = "MORE_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485147392'),
			["StringValueList"] = {},
		},
		["PERSON"] = {
			["Key"] = "PERSON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079335900680'),
			["StringValueList"] = {},
		},
		["TEAM_INDIVIDUALPVP"] = {
			["Key"] = "TEAM_INDIVIDUALPVP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948062208'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_FIRST"] = {
			["Key"] = "TEAM_APPLY_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948062464'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_AFFIX"] = {
			["Key"] = "TEAM_APPLY_AFFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948062720'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_FIRST"] = {
			["Key"] = "TEAM_INVITE_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948062976'),
			["StringValueList"] = {},
		},
		["TEAM_INVITATION_AFFIX"] = {
			["Key"] = "TEAM_INVITATION_AFFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948063232'),
			["StringValueList"] = {},
		},
		["TEAM_MATCH_PAGE"] = {
			["Key"] = "TEAM_MATCH_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948063488'),
			["StringValueList"] = {},
		},
		["TEAM_SETMANSGER_HINT"] = {
			["Key"] = "TEAM_SETMANSGER_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948063744'),
			["StringValueList"] = {},
		},
		["TEAM_GO_TARGET"] = {
			["Key"] = "TEAM_GO_TARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948064000'),
			["StringValueList"] = {},
		},
		["TEAM_NO_APPLY"] = {
			["Key"] = "TEAM_NO_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948064256'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_DPS"] = {
			["Key"] = "TARGET_NEED_DPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948064512'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_TANK"] = {
			["Key"] = "TARGET_NEED_TANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948064768'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_SUPPORT"] = {
			["Key"] = "TARGET_NEED_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948065024'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_NO_GUILD"] = {
			["Key"] = "TEAM_INVITE_NO_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948065280'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_NO_GUILD_BUTTON"] = {
			["Key"] = "TEAM_INVITE_NO_GUILD_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947965696'),
			["StringValueList"] = {},
		},
		["TEAM_SCENE_MARK_INFO"] = {
			["Key"] = "TEAM_SCENE_MARK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948065792'),
			["StringValueList"] = {},
		},
		["TEAM_MEMBER_MARK_INFO"] = {
			["Key"] = "TEAM_MEMBER_MARK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948066048'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_TO_TEAM"] = {
			["Key"] = "GROUP_QUIT_TO_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948066304'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_TO_GROUP"] = {
			["Key"] = "GROUP_QUIT_TO_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948066560'),
			["StringValueList"] = {},
		},
		["LEAGUE_INVITE"] = {
			["Key"] = "LEAGUE_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948066816'),
			["StringValueList"] = {},
		},
		["APPLY_GROUP_LEADER"] = {
			["Key"] = "APPLY_GROUP_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948067072'),
			["StringValueList"] = {},
		},
		["INVITE_SEND_TO_RECRUIT"] = {
			["Key"] = "INVITE_SEND_TO_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948067328'),
			["StringValueList"] = {},
		},
		["INVITE_SEND_TO_GUILD"] = {
			["Key"] = "INVITE_SEND_TO_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948067584'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE_SLOGAN"] = {
			["Key"] = "TEAM_RESCUE_SLOGAN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948067840'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE"] = {
			["Key"] = "TEAM_RESCUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948068096'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE_DEFAULT"] = {
			["Key"] = "TEAM_RESCUE_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948068352'),
			["StringValueList"] = {},
		},
		["TEAM_QUIKE_TEAM_UP"] = {
			["Key"] = "TEAM_QUIKE_TEAM_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26183462816256'),
			["StringValueList"] = {},
		},
		["TEAM_SET_TRARGET"] = {
			["Key"] = "TEAM_SET_TRARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948068864'),
			["StringValueList"] = {},
		},
		["GROUP_MEMBER_HINT"] = {
			["Key"] = "GROUP_MEMBER_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948069120'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_APPLY_HINT"] = {
			["Key"] = "TEAM_INVITE_APPLY_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948069376'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_APPLY_GROUP_HINT"] = {
			["Key"] = "TEAM_INVITE_APPLY_GROUP_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948069632'),
			["StringValueList"] = {},
		},
		["TASK_TASK"] = {
			["Key"] = "TASK_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328566528'),
			["StringValueList"] = {},
		},
		["TASK_TRACE_TASK"] = {
			["Key"] = "TASK_TRACE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070144'),
			["StringValueList"] = {},
		},
		["TASK_CANCEL_TRACE"] = {
			["Key"] = "TASK_CANCEL_TRACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070400'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_TASK_GAIN"] = {
			["Key"] = "TASK_COMPLETE_TASK_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070656'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_TASKS_GAIN"] = {
			["Key"] = "TASK_COMPLETE_TASKS_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070912'),
			["StringValueList"] = {},
		},
		["TASK_FORGIVE_TASK"] = {
			["Key"] = "TASK_FORGIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493868800'),
			["StringValueList"] = {},
		},
		["TASK_MAIN_TASK"] = {
			["Key"] = "TASK_MAIN_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56352118445568'),
			["StringValueList"] = {},
		},
		["TASK_BRANCH_TASK"] = {
			["Key"] = "TASK_BRANCH_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56352118445824'),
			["StringValueList"] = {},
		},
		["SUBMIT_ITEM_TITLE"] = {
			["Key"] = "SUBMIT_ITEM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948071936'),
			["StringValueList"] = {},
		},
		["SUBMIT_ITEM_CONFIRM"] = {
			["Key"] = "SUBMIT_ITEM_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948072192'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_PLANE"] = {
			["Key"] = "HUD_EXIT_PLANE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55664923673856'),
			["StringValueList"] = {},
		},
		["TASK_BACK_PLANE"] = {
			["Key"] = "TASK_BACK_PLANE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948072704'),
			["StringValueList"] = {},
		},
		["TASK_TRACE_DISTANCE"] = {
			["Key"] = "TASK_TRACE_DISTANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948072960'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_REMINDER"] = {
			["Key"] = "TASK_COMPLETE_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56352118448896'),
			["StringValueList"] = {},
		},
		["TASK_GUILD_WAITING"] = {
			["Key"] = "TASK_GUILD_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948073472'),
			["StringValueList"] = {},
		},
		["TOWER_CLIMB_TIME_SURVIVAL"] = {
			["Key"] = "TOWER_CLIMB_TIME_SURVIVAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948073728'),
			["StringValueList"] = {},
		},
		["TASK_CLICK_GOTO"] = {
			["Key"] = "TASK_CLICK_GOTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948073984'),
			["StringValueList"] = {},
		},
		["TASK_MAP_ARBITRARY"] = {
			["Key"] = "TASK_MAP_ARBITRARY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948074240'),
			["StringValueList"] = {},
		},
		["TASK_TAG_ALL"] = {
			["Key"] = "TASK_TAG_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946749440'),
			["StringValueList"] = {},
		},
		["QUEST_REPLAY_CUTSCENE"] = {
			["Key"] = "QUEST_REPLAY_CUTSCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948074752'),
			["StringValueList"] = {},
		},
		["QUEST_REPLAY_DIALOGUE"] = {
			["Key"] = "QUEST_REPLAY_DIALOGUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948075008'),
			["StringValueList"] = {},
		},
		["REMAIN_TIME"] = {
			["Key"] = "REMAIN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948075264'),
			["StringValueList"] = {},
		},
		["TASK_REMAINTIME_HOUR"] = {
			["Key"] = "TASK_REMAINTIME_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948075520'),
			["StringValueList"] = {},
		},
		["TASK_REMAINTIME_DAY"] = {
			["Key"] = "TASK_REMAINTIME_DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948075776'),
			["StringValueList"] = {},
		},
		["TOBECONTINUED"] = {
			["Key"] = "TOBECONTINUED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076032'),
			["StringValueList"] = {},
		},
		["PLAYAGAIN"] = {
			["Key"] = "PLAYAGAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076288'),
			["StringValueList"] = {},
		},
		["TASK_TAG_PROGRESS"] = {
			["Key"] = "TASK_TAG_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076544'),
			["StringValueList"] = {},
		},
		["TASK_TAG_ACCEPTABLE"] = {
			["Key"] = "TASK_TAG_ACCEPTABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076800'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILLCUSTOMIZER"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILLCUSTOMIZER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_MAIN_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_MAIN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEQUENCE_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_SEQUENCE_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948077568'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_LEVEL_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_LEVEL_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948077824'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_ALREADY_MAX_LEVEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_ALREADY_MAX_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948078080'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_UPGRADE"] = {
			["Key"] = "SKILLCUSTOMIZER_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836364800'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_UNLOCK"] = {
			["Key"] = "SKILLCUSTOMIZER_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690024960'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_LEVEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948078848'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_LOCKED_LEVEL_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_LOCKED_LEVEL_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948079104'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEQUENCE_SKILL"] = {
			["Key"] = "SKILLCUSTOMIZER_SEQUENCE_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948079360'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEPCIAL_SKILL"] = {
			["Key"] = "SKILLCUSTOMIZER_SEPCIAL_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948079616'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_EQUIPED"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_EQUIPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947838720'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_EQUIPED_NOT_REQUIRED"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_EQUIPED_NOT_REQUIRED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948080128'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_BUTTON"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948080384'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_BUTTON_USING"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_BUTTON_USING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948080640'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948080896'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948081152'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_CONFIRM"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_CANCEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_COLDDOWN_TIME"] = {
			["Key"] = "SKILLCUSTOMIZER_COLDDOWN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948081920'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_TAB_UPGRADE"] = {
			["Key"] = "SKILLCUSTOMIZER_TAB_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836364800'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_TAB_EQUIP"] = {
			["Key"] = "SKILLCUSTOMIZER_TAB_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948082432'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_COLDDOWN_TIME_FORMAT"] = {
			["Key"] = "SKILLCUSTOMIZER_COLDDOWN_TIME_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948082688'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_LEVEL_FORMAT"] = {
			["Key"] = "SKILLCUSTOMIZER_LEVEL_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987987712'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_CE_ADD_UP"] = {
			["Key"] = "SKILLCUSTOMIZER_CE_ADD_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083200'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK1"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083456'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK2"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083712'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK3"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083968'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK4"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084224'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK5"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084480'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK6"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084736'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK1"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083456'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK2"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083712'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK3"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083968'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK4"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084224'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK5"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084480'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK6"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948084736'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_ROLESKILL"] = {
			["Key"] = "SKILL_TAB_ROLESKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206467840'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_FELLOWSKILL"] = {
			["Key"] = "SKILL_TAB_FELLOWSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328567296'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_DISCOVERYSKILL"] = {
			["Key"] = "SKILL_TAB_DISCOVERYSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605632'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_IDENTITYSKILL"] = {
			["Key"] = "SKILL_TAB_IDENTITYSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605888'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_Preset"] = {
			["Key"] = "SKILL_TAB_Preset",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948087552'),
			["StringValueList"] = {},
		},
		["SKILL_CE_TEXT"] = {
			["Key"] = "SKILL_CE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948083200'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TITLE"] = {
			["Key"] = "SKILL_PRESET_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948087552'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_COUNT_TEXT"] = {
			["Key"] = "SKILL_PRESET_COUNT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948088320'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TEXT"] = {
			["Key"] = "SKILL_PRESET_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948088576'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_BUTTON_USE"] = {
			["Key"] = "SKILL_PRESET_BUTTON_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948080384'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TITLE_ADVICE"] = {
			["Key"] = "SKILL_PRESET_TITLE_ADVICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948089088'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_BUTTON_SAVE"] = {
			["Key"] = "SKILL_PRESET_BUTTON_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997696'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON_UNLOCK"] = {
			["Key"] = "SKILL_FELLOW_BUTTON_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948089600'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON"] = {
			["Key"] = "SKILL_FELLOW_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948089856'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON_GO"] = {
			["Key"] = "SKILL_FELLOW_BUTTON_GO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948090112'),
			["StringValueList"] = {},
		},
		["SKILL_IDENTITY_BUTTON_GO"] = {
			["Key"] = "SKILL_IDENTITY_BUTTON_GO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948090368'),
			["StringValueList"] = {},
		},
		["PRESET_SKILL"] = {
			["Key"] = "PRESET_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328562944'),
			["StringValueList"] = {},
		},
		["PRESET_SEALED"] = {
			["Key"] = "PRESET_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["PRESET_TRAITS"] = {
			["Key"] = "PRESET_TRAITS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948091136'),
			["StringValueList"] = {},
		},
		["PRESET_REPLACE_TITLE"] = {
			["Key"] = "PRESET_REPLACE_TITLE",
			["StringValue"] = "%s",
			["StringValueList"] = {},
		},
		["SKILL_TAB1_NAME"] = {
			["Key"] = "SKILL_TAB1_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206467840'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE1"] = {
			["Key"] = "SKILL_TAB1_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948079360'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE2"] = {
			["Key"] = "SKILL_TAB1_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948079616'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE3"] = {
			["Key"] = "SKILL_TAB1_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47761110076928'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_NAME"] = {
			["Key"] = "SKILL_TAB2_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948092672'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE1"] = {
			["Key"] = "SKILL_TAB2_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47761110076672'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE2"] = {
			["Key"] = "SKILL_TAB2_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE3"] = {
			["Key"] = "SKILL_TAB2_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_NAME"] = {
			["Key"] = "SKILL_TAB3_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093696'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE1"] = {
			["Key"] = "SKILL_TAB3_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE2"] = {
			["Key"] = "SKILL_TAB3_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE3"] = {
			["Key"] = "SKILL_TAB3_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_NAME"] = {
			["Key"] = "SKILL_TAB4_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948094720'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE1"] = {
			["Key"] = "SKILL_TAB4_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605632'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE2"] = {
			["Key"] = "SKILL_TAB4_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE3"] = {
			["Key"] = "SKILL_TAB4_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_NAME"] = {
			["Key"] = "SKILL_TAB5_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47761110076416'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE1"] = {
			["Key"] = "SKILL_TAB5_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605888'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE2"] = {
			["Key"] = "SKILL_TAB5_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE3"] = {
			["Key"] = "SKILL_TAB5_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948093440'),
			["StringValueList"] = {},
		},
		["SKILL_UNLOCK_UIJUMP"] = {
			["Key"] = "SKILL_UNLOCK_UIJUMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948096768'),
			["StringValueList"] = {},
		},
		["SKILL_AUTO_ON"] = {
			["Key"] = "SKILL_AUTO_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948097024'),
			["StringValueList"] = {},
		},
		["SKILL_AUTO_OFF"] = {
			["Key"] = "SKILL_AUTO_OFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948097280'),
			["StringValueList"] = {},
		},
		["SKILL_TIP_LV"] = {
			["Key"] = "SKILL_TIP_LV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947849472'),
			["StringValueList"] = {},
		},
		["SKILL_TIP_ADDITIONAL_LV"] = {
			["Key"] = "SKILL_TIP_ADDITIONAL_LV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337297408'),
			["StringValueList"] = {},
		},
		["SKILL_INFO_BRIEF"] = {
			["Key"] = "SKILL_INFO_BRIEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948098048'),
			["StringValueList"] = {},
		},
		["SKILL_INFO_DETAIL"] = {
			["Key"] = "SKILL_INFO_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947814656'),
			["StringValueList"] = {},
		},
		["SKILL_ELEMENT_RESONATE_LEVEL"] = {
			["Key"] = "SKILL_ELEMENT_RESONATE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947931904'),
			["StringValueList"] = {},
		},
		["EQUIP_TITLE"] = {
			["Key"] = "EQUIP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948098816'),
			["StringValueList"] = {},
		},
		["EQUIP_LEVEL"] = {
			["Key"] = "EQUIP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337297408'),
			["StringValueList"] = {},
		},
		["EQUIP_SCORE"] = {
			["Key"] = "EQUIP_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_44805904139776'),
			["StringValueList"] = {},
		},
		["EQUIP_TC"] = {
			["Key"] = "EQUIP_TC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948099584'),
			["StringValueList"] = {},
		},
		["EQUIP_MYSTERY_ENTRY"] = {
			["Key"] = "EQUIP_MYSTERY_ENTRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948099840'),
			["StringValueList"] = {},
		},
		["EQUIP_WAIT_TO_OPENED"] = {
			["Key"] = "EQUIP_WAIT_TO_OPENED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948100096'),
			["StringValueList"] = {},
		},
		["EQUIP_RANDOM_PROP_SYMBOL"] = {
			["Key"] = "EQUIP_RANDOM_PROP_SYMBOL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948100352'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_BASIC"] = {
			["Key"] = "EQUIP_TIPS_BASIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774402560'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_FIX"] = {
			["Key"] = "EQUIP_TIPS_FIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948100864'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_RANDOM"] = {
			["Key"] = "EQUIP_TIPS_RANDOM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948101120'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_ADVANCE"] = {
			["Key"] = "EQUIP_TIPS_ADVANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948101376'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_RANDOMTIMES"] = {
			["Key"] = "EQUIP_TIPS_RANDOMTIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948101632'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_TC"] = {
			["Key"] = "EQUIP_TIPS_TC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948101888'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_ITEMTC"] = {
			["Key"] = "EQUIP_TIPS_ITEMTC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948102144'),
			["StringValueList"] = {},
		},
		["EQUIPITEM_BIND"] = {
			["Key"] = "EQUIPITEM_BIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033043712'),
			["StringValueList"] = {},
		},
		["EQUIPITEM_UNBIND"] = {
			["Key"] = "EQUIPITEM_UNBIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948102656'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1"] = {
			["Key"] = "EQUIP_TAB1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2"] = {
			["Key"] = "EQUIP_TAB2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505957120'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3"] = {
			["Key"] = "EQUIP_TAB3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505965056'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4"] = {
			["Key"] = "EQUIP_TAB4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_19793356785152'),
			["StringValueList"] = {},
		},
		["EQUIP_WORD"] = {
			["Key"] = "EQUIP_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948103936'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_TIP1"] = {
			["Key"] = "EQUIP_TAB1_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948104192'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_STATUS1"] = {
			["Key"] = "EQUIP_TAB1_STATUS1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948104448'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_BUTTON"] = {
			["Key"] = "EQUIP_TAB1_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265601024'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_STAGEFULL"] = {
			["Key"] = "EQUIP_TAB1_STAGEFULL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987988992'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_FULL"] = {
			["Key"] = "EQUIP_TAB1_FULL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948105216'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_LEVEL"] = {
			["Key"] = "EQUIP_TAB1_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948105472'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_SUITE"] = {
			["Key"] = "EQUIP_TAB1_SUITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948105728'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_NONE"] = {
			["Key"] = "EQUIP_TAB1_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948105984'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP1"] = {
			["Key"] = "EQUIP_TAB2_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948106240'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP2"] = {
			["Key"] = "EQUIP_TAB2_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948106496'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP3"] = {
			["Key"] = "EQUIP_TAB2_TIP3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948106752'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP4"] = {
			["Key"] = "EQUIP_TAB2_TIP4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948107008'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_NONE"] = {
			["Key"] = "EQUIP_TAB2_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948107264'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_TIP1"] = {
			["Key"] = "EQUIP_TAB3_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948107520'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_TIP2"] = {
			["Key"] = "EQUIP_TAB3_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948107776'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BUTTON1"] = {
			["Key"] = "EQUIP_TAB3_BUTTON1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505965056'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BUTTON2"] = {
			["Key"] = "EQUIP_TAB3_BUTTON2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BANWORD"] = {
			["Key"] = "EQUIP_TAB3_BANWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025311271424'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP1"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948108800'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TITLE"] = {
			["Key"] = "EQUIP_TAB3_BAN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025311271424'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP2"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948109312'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP3"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948109568'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_SUBTITLE"] = {
			["Key"] = "EQUIP_TAB3_BAN_SUBTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948109824'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_BUTTON1"] = {
			["Key"] = "EQUIP_TAB3_BAN_BUTTON1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948011776'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_NONE"] = {
			["Key"] = "EQUIP_TAB3_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948110336'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_TIP1"] = {
			["Key"] = "EQUIP_TAB4_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948110592'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_SELECTWORD"] = {
			["Key"] = "EQUIP_TAB4_SELECTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948110848'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_SELECTITEM"] = {
			["Key"] = "EQUIP_TAB4_SELECTITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948111104'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_TIP2"] = {
			["Key"] = "EQUIP_TAB4_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948111360'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_ITEMWORD"] = {
			["Key"] = "EQUIP_TAB4_ITEMWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948111616'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_BUTTON"] = {
			["Key"] = "EQUIP_TAB4_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024505965312'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_NONE"] = {
			["Key"] = "EQUIP_TAB4_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948112128'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TITLE"] = {
			["Key"] = "EQUIP_TAB4S_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948111104'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_NONE"] = {
			["Key"] = "EQUIP_TAB4S_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948112640'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT1"] = {
			["Key"] = "EQUIP_TAB4S_SELECT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948112896'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT2"] = {
			["Key"] = "EQUIP_TAB4S_SELECT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948102656'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT3"] = {
			["Key"] = "EQUIP_TAB4S_SELECT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948113408'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TIP1"] = {
			["Key"] = "EQUIP_TAB4S_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948113664'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TIP2"] = {
			["Key"] = "EQUIP_TAB4S_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948113920'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_BUTTON"] = {
			["Key"] = "EQUIP_TAB4S_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948114176'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_TITLE"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948114432'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_CLOSE"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948114688'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_LEVEL"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948102144'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_TIMES"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_TIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948115200'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948115456'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD1"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948115712'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD2"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948115968'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_CLEAR"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_CLEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948116224'),
			["StringValueList"] = {},
		},
		["STORE_BUY_LIMIT"] = {
			["Key"] = "STORE_BUY_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948116480'),
			["StringValueList"] = {},
		},
		["STORE_NPC_TITLE"] = {
			["Key"] = "STORE_NPC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948116736'),
			["StringValueList"] = {},
		},
		["STORE_ACHIEVE_LEVEL_UNLOCK"] = {
			["Key"] = "STORE_ACHIEVE_LEVEL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948116992'),
			["StringValueList"] = {},
		},
		["STORE_COMPLETE_TASK"] = {
			["Key"] = "STORE_COMPLETE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948117248'),
			["StringValueList"] = {},
		},
		["STORE_CLASS"] = {
			["Key"] = "STORE_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948117504'),
			["StringValueList"] = {},
		},
		["STORE_SHOP_SOLDOUT"] = {
			["Key"] = "STORE_SHOP_SOLDOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948117760'),
			["StringValueList"] = {},
		},
		["STORE_GUILD_SHOP_LEVEL"] = {
			["Key"] = "STORE_GUILD_SHOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948118016'),
			["StringValueList"] = {},
		},
		["STORE_ACHIEVE_LEVEL_UNLOCK_LUEN"] = {
			["Key"] = "STORE_ACHIEVE_LEVEL_UNLOCK_LUEN",
			["StringValue"] = "Character Level",
			["StringValueList"] = {},
		},
		["STORE_COMPLETE_TASK_LUEN"] = {
			["Key"] = "STORE_COMPLETE_TASK_LUEN",
			["StringValue"] = "TaskComplete",
			["StringValueList"] = {},
		},
		["STORE_CLASS_LUEN"] = {
			["Key"] = "STORE_CLASS_LUEN",
			["StringValue"] = "Profession",
			["StringValueList"] = {},
		},
		["STORE_PURCHASE_LIMIT_LUEN"] = {
			["Key"] = "STORE_PURCHASE_LIMIT_LUEN",
			["StringValue"] = "Purchase Limit",
			["StringValueList"] = {},
		},
		["STORE_GUILD_SHOP_LEVEL_LUEN"] = {
			["Key"] = "STORE_GUILD_SHOP_LEVEL_LUEN",
			["StringValue"] = "Guildshop Level",
			["StringValueList"] = {},
		},
		["STORE_SOLD_OUT_LUEN"] = {
			["Key"] = "STORE_SOLD_OUT_LUEN",
			["StringValue"] = "Sold Out",
			["StringValueList"] = {},
		},
		["STORE_DEPARTMENT_STORE"] = {
			["Key"] = "STORE_DEPARTMENT_STORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31543045131520'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_NOLIMIT"] = {
			["Key"] = "STORE_REFRESH_NOLIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948120064'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_MINUTE"] = {
			["Key"] = "STORE_REFRESH_MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746816'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_HOUR"] = {
			["Key"] = "STORE_REFRESH_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079335900930'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_DAILY"] = {
			["Key"] = "STORE_REFRESH_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947746304'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_DAY"] = {
			["Key"] = "STORE_REFRESH_DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067473409'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_WEEK"] = {
			["Key"] = "STORE_REFRESH_WEEK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38277822298368'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_MONTH"] = {
			["Key"] = "STORE_REFRESH_MONTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067478022'),
			["StringValueList"] = {},
		},
		["STORE_BUY_PERMANENT_LIMIT"] = {
			["Key"] = "STORE_BUY_PERMANENT_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948121856'),
			["StringValueList"] = {},
		},
		["STORE_BUY_PERMANENT"] = {
			["Key"] = "STORE_BUY_PERMANENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948122112'),
			["StringValueList"] = {},
		},
		["STORE_EVERY"] = {
			["Key"] = "STORE_EVERY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948122368'),
			["StringValueList"] = {},
		},
		["STORE_BOUDN_OBTAIN"] = {
			["Key"] = "STORE_BOUDN_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11202348634624'),
			["StringValueList"] = {},
		},
		["STORE_POSSIBLE_OBTAIN"] = {
			["Key"] = "STORE_POSSIBLE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11202348634880'),
			["StringValueList"] = {},
		},
		["STORE_FREE_OBTAIN"] = {
			["Key"] = "STORE_FREE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948123136'),
			["StringValueList"] = {},
		},
		["STORE_HAVE_RECEIVED"] = {
			["Key"] = "STORE_HAVE_RECEIVED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948123392'),
			["StringValueList"] = {},
		},
		["STORE_PURCHASE"] = {
			["Key"] = "STORE_PURCHASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32097364356608'),
			["StringValueList"] = {},
		},
		["STORE_INEVITABLE"] = {
			["Key"] = "STORE_INEVITABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948123904'),
			["StringValueList"] = {},
		},
		["FUNCTION_MALL"] = {
			["Key"] = "FUNCTION_MALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328571136'),
			["StringValueList"] = {},
		},
		["FUNCTION_STORE"] = {
			["Key"] = "FUNCTION_STORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31543045131520'),
			["StringValueList"] = {},
		},
		["FUNCTION_EXCHANGE"] = {
			["Key"] = "FUNCTION_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328574720'),
			["StringValueList"] = {},
		},
		["FUNCTION_AUCTION"] = {
			["Key"] = "FUNCTION_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948124928'),
			["StringValueList"] = {},
		},
		["FUNCTION_MALL_LUEN"] = {
			["Key"] = "FUNCTION_MALL_LUEN",
			["StringValue"] = "MALL",
			["StringValueList"] = {},
		},
		["FUNCTION_STORE_LUEN"] = {
			["Key"] = "FUNCTION_STORE_LUEN",
			["StringValue"] = "STORE",
			["StringValueList"] = {},
		},
		["FUNCTION_EXCHANGE_LUEN"] = {
			["Key"] = "FUNCTION_EXCHANGE_LUEN",
			["StringValue"] = "EXCHANGE",
			["StringValueList"] = {},
		},
		["FUNCTION_AUCTION_LUEN"] = {
			["Key"] = "FUNCTION_AUCTION_LUEN",
			["StringValue"] = "BID",
			["StringValueList"] = {},
		},
		["EXCHANGE_CONCERN"] = {
			["Key"] = "EXCHANGE_CONCERN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36353408507648'),
			["StringValueList"] = {},
		},
		["EXCHANGE_MARKET"] = {
			["Key"] = "EXCHANGE_MARKET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948126464'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELL"] = {
			["Key"] = "EXCHANGE_SELL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033044992'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE_ITEM"] = {
			["Key"] = "EXCHANGE_ON_SALE_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948126976'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELL_NONE"] = {
			["Key"] = "EXCHANGE_SELL_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948127232'),
			["StringValueList"] = {},
		},
		["EXCAHNGE_NO_PLAYER_SOLD"] = {
			["Key"] = "EXCAHNGE_NO_PLAYER_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948127232'),
			["StringValueList"] = {},
		},
		["EXCHANGE_CAN_SELL_NONE"] = {
			["Key"] = "EXCHANGE_CAN_SELL_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948127744'),
			["StringValueList"] = {},
		},
		["EXCHANGE_FLLOW_NONE"] = {
			["Key"] = "EXCHANGE_FLLOW_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948128000'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELLING"] = {
			["Key"] = "EXCHANGE_SELLING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948128256'),
			["StringValueList"] = {},
		},
		["PUBLICITY"] = {
			["Key"] = "PUBLICITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948128512'),
			["StringValueList"] = {},
		},
		["LEGEND"] = {
			["Key"] = "LEGEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_23983902689792'),
			["StringValueList"] = {},
		},
		["MYTH"] = {
			["Key"] = "MYTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_23983902690048'),
			["StringValueList"] = {},
		},
		["INCOME"] = {
			["Key"] = "INCOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948129280'),
			["StringValueList"] = {},
		},
		["WITHDRAW_TO_WALLET"] = {
			["Key"] = "WITHDRAW_TO_WALLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948129536'),
			["StringValueList"] = {},
		},
		["TIME_OUT"] = {
			["Key"] = "TIME_OUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948129792'),
			["StringValueList"] = {},
		},
		["PUT_ON"] = {
			["Key"] = "PUT_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948130048'),
			["StringValueList"] = {},
		},
		["PUT_OFF"] = {
			["Key"] = "PUT_OFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947870720'),
			["StringValueList"] = {},
		},
		["EXCHANGE_DOUBLE_CHECK"] = {
			["Key"] = "EXCHANGE_DOUBLE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948130560'),
			["StringValueList"] = {},
		},
		["EXCHANGE_RESELL"] = {
			["Key"] = "EXCHANGE_RESELL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948130816'),
			["StringValueList"] = {},
		},
		["EXCHANGE_CANCEL"] = {
			["Key"] = "EXCHANGE_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE"] = {
			["Key"] = "EXCHANGE_ON_SALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948131328'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE_TIP"] = {
			["Key"] = "EXCHANGE_ON_SALE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948129792'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_PUBLIC"] = {
			["Key"] = "EXCHANGE_ON_PUBLIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948131840'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_PUBLIC_TIP"] = {
			["Key"] = "EXCHANGE_ON_PUBLIC_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948132096'),
			["StringValueList"] = {},
		},
		["EXCHANGE_OVERDUE"] = {
			["Key"] = "EXCHANGE_OVERDUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948132352'),
			["StringValueList"] = {},
		},
		["EXCHANGE_OVERDUE_TIP"] = {
			["Key"] = "EXCHANGE_OVERDUE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948132608'),
			["StringValueList"] = {},
		},
		["EXCHANGE_PUBLIC_TIME"] = {
			["Key"] = "EXCHANGE_PUBLIC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948132864'),
			["StringValueList"] = {},
		},
		["EXCHANGE_AFTER_TAX_INCOME"] = {
			["Key"] = "EXCHANGE_AFTER_TAX_INCOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948133120'),
			["StringValueList"] = {},
		},
		["EXCHANGE_NO_PLAYER_PUBLICITY"] = {
			["Key"] = "EXCHANGE_NO_PLAYER_PUBLICITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948133376'),
			["StringValueList"] = {},
		},
		["EXCHANGE_PRICE"] = {
			["Key"] = "EXCHANGE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948133632'),
			["StringValueList"] = {},
		},
		["DEPOSIT"] = {
			["Key"] = "DEPOSIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948133888'),
			["StringValueList"] = {},
		},
		["SHELVE_PRICE"] = {
			["Key"] = "SHELVE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948134144'),
			["StringValueList"] = {},
		},
		["ALL_PRICR"] = {
			["Key"] = "ALL_PRICR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948134400'),
			["StringValueList"] = {},
		},
		["TRADE_HISTORY"] = {
			["Key"] = "TRADE_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948134656'),
			["StringValueList"] = {},
		},
		["TRADE_HISTORY_TIPS"] = {
			["Key"] = "TRADE_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948134912'),
			["StringValueList"] = {},
		},
		["TRADE_PRICE"] = {
			["Key"] = "TRADE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948135168'),
			["StringValueList"] = {},
		},
		["TRADE_NAME"] = {
			["Key"] = "TRADE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948135424'),
			["StringValueList"] = {},
		},
		["BAG_HMS"] = {
			["Key"] = "BAG_HMS",
			["StringValue"] = "Bag",
			["StringValueList"] = {},
		},
		["CARGO_HMS"] = {
			["Key"] = "CARGO_HMS",
			["StringValue"] = "Cargo",
			["StringValueList"] = {},
		},
		["INCOME_HMS"] = {
			["Key"] = "INCOME_HMS",
			["StringValue"] = "Revenue",
			["StringValueList"] = {},
		},
		["NO_PURCHASE_TIME_LIMIT"] = {
			["Key"] = "NO_PURCHASE_TIME_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948136448'),
			["StringValueList"] = {},
		},
		["PERMANENT_PURCHASE_LIMIT"] = {
			["Key"] = "PERMANENT_PURCHASE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948136704'),
			["StringValueList"] = {},
		},
		["AUCTION_NO_BID"] = {
			["Key"] = "AUCTION_NO_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948136960'),
			["StringValueList"] = {},
		},
		["AUCTION_RECORD"] = {
			["Key"] = "AUCTION_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948137216'),
			["StringValueList"] = {},
		},
		["AUCTION_NO_RECORD"] = {
			["Key"] = "AUCTION_NO_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948137472'),
			["StringValueList"] = {},
		},
		["AUCTION_DOUBLE_CHECK"] = {
			["Key"] = "AUCTION_DOUBLE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948137728'),
			["StringValueList"] = {},
		},
		["AUCTION_CANCEL"] = {
			["Key"] = "AUCTION_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["AUCTION_ENSURE"] = {
			["Key"] = "AUCTION_ENSURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["StringValueList"] = {},
		},
		["AUCTION_BID_CHECK"] = {
			["Key"] = "AUCTION_BID_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948138496'),
			["StringValueList"] = {},
		},
		["AUCTION_WORLD_BID"] = {
			["Key"] = "AUCTION_WORLD_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948138752'),
			["StringValueList"] = {},
		},
		["AUCTION_GUILD_BID"] = {
			["Key"] = "AUCTION_GUILD_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948139008'),
			["StringValueList"] = {},
		},
		["EXCHANGE_MARKET_NO_GOODS"] = {
			["Key"] = "EXCHANGE_MARKET_NO_GOODS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948139264'),
			["StringValueList"] = {},
		},
		["EXCHANGE_DISCOUNT"] = {
			["Key"] = "EXCHANGE_DISCOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067467529'),
			["StringValueList"] = {},
		},
		["NO_TRADE_HISTORY"] = {
			["Key"] = "NO_TRADE_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948139776'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ITEM_SEARCH"] = {
			["Key"] = "EXCHANGE_ITEM_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948140032'),
			["StringValueList"] = {},
		},
		["NO_SEARCH_HISTORY"] = {
			["Key"] = "NO_SEARCH_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948140288'),
			["StringValueList"] = {},
		},
		["STORE_BUY_ALLSERVER_LIMIT"] = {
			["Key"] = "STORE_BUY_ALLSERVER_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948140544'),
			["StringValueList"] = {},
		},
		["MAP_VISIT"] = {
			["Key"] = "MAP_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948140800'),
			["StringValueList"] = {},
		},
		["MAP_CANEL_VISIT"] = {
			["Key"] = "MAP_CANEL_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070400'),
			["StringValueList"] = {},
		},
		["MAP_LINE"] = {
			["Key"] = "MAP_LINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948141312'),
			["StringValueList"] = {},
		},
		["MAP_TASK_VISIT"] = {
			["Key"] = "MAP_TASK_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["MAP_TASK_CANCEL_VISIT"] = {
			["Key"] = "MAP_TASK_CANCEL_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948070400'),
			["StringValueList"] = {},
		},
		["MAP_SUBWAY_VISIT"] = {
			["Key"] = "MAP_SUBWAY_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["MAP_ACTIVITY_VISIT"] = {
			["Key"] = "MAP_ACTIVITY_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["ACTIVITY_NOT_OPEN"] = {
			["Key"] = "ACTIVITY_NOT_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948142592'),
			["StringValueList"] = {},
		},
		["SEARCH_TITLE"] = {
			["Key"] = "SEARCH_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948142848'),
			["StringValueList"] = {},
		},
		["MAP_TRACE_UP"] = {
			["Key"] = "MAP_TRACE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948143104'),
			["StringValueList"] = {},
		},
		["MAP_TRACE_DOWN"] = {
			["Key"] = "MAP_TRACE_DOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948143360'),
			["StringValueList"] = {},
		},
		["MAP_CLICK_MARK"] = {
			["Key"] = "MAP_CLICK_MARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948143616'),
			["StringValueList"] = {},
		},
		["MAP_MARKER_TRACKING"] = {
			["Key"] = "MAP_MARKER_TRACKING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948143872'),
			["StringValueList"] = {},
		},
		["MAP__HORSESTATION"] = {
			["Key"] = "MAP__HORSESTATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35327716653312'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_TREASURE"] = {
			["Key"] = "SPIRITUAL_TREASURE",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_TreasureTag",
			["StringValueList"] = {},
		},
		["SPIRITUAL_QUESTION"] = {
			["Key"] = "SPIRITUAL_QUESTION",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_QuestionMarkTag",
			["StringValueList"] = {},
		},
		["SPIRITUAL_KEY"] = {
			["Key"] = "SPIRITUAL_KEY",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_KeyTag",
			["StringValueList"] = {},
		},
		["MAP_TRACE"] = {
			["Key"] = "MAP_TRACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948140800'),
			["StringValueList"] = {},
		},
		["MAP_FMT_LAYER"] = {
			["Key"] = "MAP_FMT_LAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948145408'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_ROLE"] = {
			["Key"] = "ROLEDISPLAY_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206467584'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DETAIL"] = {
			["Key"] = "ROLEDISPLAY_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948145920'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_CHECK_ATTR"] = {
			["Key"] = "ROLEDISPLAY_CHECK_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948146176'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_REPLACE_EQUIP"] = {
			["Key"] = "ROLEDISPLAY_REPLACE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948146432'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_SAN"] = {
			["Key"] = "ROLEDISPLAY_SAN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774451968'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_1"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948146944'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_2"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948147200'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_3"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948147456'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_4"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948147712'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_5"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948147968'),
			["StringValueList"] = {},
		},
		["NOGUILD"] = {
			["Key"] = "NOGUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948148224'),
			["StringValueList"] = {},
		},
		["NOTITLE"] = {
			["Key"] = "NOTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948148480'),
			["StringValueList"] = {},
		},
		["NORANK"] = {
			["Key"] = "NORANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948148736'),
			["StringValueList"] = {},
		},
		["BID_BID"] = {
			["Key"] = "BID_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948148992'),
			["StringValueList"] = {},
		},
		["BID_MAX_PRICE"] = {
			["Key"] = "BID_MAX_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947826688'),
			["StringValueList"] = {},
		},
		["BID_COUNTDOWN"] = {
			["Key"] = "BID_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948149504'),
			["StringValueList"] = {},
		},
		["BID_MY_OFFER"] = {
			["Key"] = "BID_MY_OFFER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948149760'),
			["StringValueList"] = {},
		},
		["BID_NO_BID"] = {
			["Key"] = "BID_NO_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948150016'),
			["StringValueList"] = {},
		},
		["BID_OPEN_TIME"] = {
			["Key"] = "BID_OPEN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948150272'),
			["StringValueList"] = {},
		},
		["BID_FIXED_PRICE"] = {
			["Key"] = "BID_FIXED_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948150528'),
			["StringValueList"] = {},
		},
		["BID_FAIL"] = {
			["Key"] = "BID_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948150784'),
			["StringValueList"] = {},
		},
		["BID_GET"] = {
			["Key"] = "BID_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948151040'),
			["StringValueList"] = {},
		},
		["BID_SOLD"] = {
			["Key"] = "BID_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948151296'),
			["StringValueList"] = {},
		},
		["BID_WAIT"] = {
			["Key"] = "BID_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948151552'),
			["StringValueList"] = {},
		},
		["BID_ON"] = {
			["Key"] = "BID_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948151808'),
			["StringValueList"] = {},
		},
		["BID_HISTORY"] = {
			["Key"] = "BID_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948152064'),
			["StringValueList"] = {},
		},
		["BID_TIME"] = {
			["Key"] = "BID_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948152320'),
			["StringValueList"] = {},
		},
		["BID_CONFIRM_PRICE"] = {
			["Key"] = "BID_CONFIRM_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948137728'),
			["StringValueList"] = {},
		},
		["BID_CHOICE"] = {
			["Key"] = "BID_CHOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948152832'),
			["StringValueList"] = {},
		},
		["BID_FINISH"] = {
			["Key"] = "BID_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_51265803390976'),
			["StringValueList"] = {},
		},
		["BID_NO_HISTORY"] = {
			["Key"] = "BID_NO_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948153344'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TITLE"] = {
			["Key"] = "GUILD_MT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TASKSETTLE"] = {
			["Key"] = "GUILD_MT_TASKSETTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948153856'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TASKREWARD"] = {
			["Key"] = "GUILD_MT_TASKREWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948154112'),
			["StringValueList"] = {},
		},
		["GUILD_MT_COMPLETE"] = {
			["Key"] = "GUILD_MT_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948154368'),
			["StringValueList"] = {},
		},
		["GUILD_MT_FINISHED"] = {
			["Key"] = "GUILD_MT_FINISHED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948154624'),
			["StringValueList"] = {},
		},
		["GUILD_MT_FINISH"] = {
			["Key"] = "GUILD_MT_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_39995809204992'),
			["StringValueList"] = {},
		},
		["GUILD_HYPERLINK"] = {
			["Key"] = "GUILD_HYPERLINK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948155136'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION_STAGE"] = {
			["Key"] = "SEQUENCE_DIGESTION_STAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948155392'),
			["StringValueList"] = {},
		},
		["SEQUENCE_TITLE"] = {
			["Key"] = "SEQUENCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206473216'),
			["StringValueList"] = {},
		},
		["SEQUENCE_BACK_BTN"] = {
			["Key"] = "SEQUENCE_BACK_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948155904'),
			["StringValueList"] = {},
		},
		["SEQUENCE_FINISH_STAGE"] = {
			["Key"] = "SEQUENCE_FINISH_STAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948156160'),
			["StringValueList"] = {},
		},
		["SEQUENCE_LOCK_TITLE"] = {
			["Key"] = "SEQUENCE_LOCK_TITLE",
			["StringValue"] = "???",
			["StringValueList"] = {},
		},
		["SEQUENCE_GOD"] = {
			["Key"] = "SEQUENCE_GOD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948156672'),
			["StringValueList"] = {},
		},
		["SEQUENCE_REFINE_TITLE"] = {
			["Key"] = "SEQUENCE_REFINE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59443152836608'),
			["StringValueList"] = {},
		},
		["SEQUENCE_REFINE_BTN"] = {
			["Key"] = "SEQUENCE_REFINE_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59449863722752'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION_BTN"] = {
			["Key"] = "SEQUENCE_DIGESTION_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948157440'),
			["StringValueList"] = {},
		},
		["SEQUENCE_NO_DIGESTION"] = {
			["Key"] = "SEQUENCE_NO_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948157696'),
			["StringValueList"] = {},
		},
		["SEQUENCE_HAVE_DIGESTION"] = {
			["Key"] = "SEQUENCE_HAVE_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948157952'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CAN_RECEIVE_TASK"] = {
			["Key"] = "SEQUENCE_CAN_RECEIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948158208'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CAN_FINISH_TASK"] = {
			["Key"] = "SEQUENCE_CAN_FINISH_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948158464'),
			["StringValueList"] = {},
		},
		["SEQUENCE_RECEIVE_TASK"] = {
			["Key"] = "SEQUENCE_RECEIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948158720'),
			["StringValueList"] = {},
		},
		["SEQUENCE_LEVEL"] = {
			["Key"] = "SEQUENCE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948158976'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION"] = {
			["Key"] = "SEQUENCE_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948159232'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_ONE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_ONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948159488'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_BOTTLE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_BOTTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067469326'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_DROP"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948160000'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_PAIR"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_PAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067467265'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_DUO"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_DUO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948160512'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_SLICE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_SLICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067483143'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_ROOT"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_ROOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36079067470594'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_STRIP"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_STRIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948161280'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_NIL"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_NIL",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["SEQUENCE_SANCHECK_TITLE"] = {
			["Key"] = "SEQUENCE_SANCHECK_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948161792'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CRAZYTALK_DESC"] = {
			["Key"] = "SEQUENCE_CRAZYTALK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948162048'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SANCHECK_DESC"] = {
			["Key"] = "SEQUENCE_SANCHECK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948162304'),
			["StringValueList"] = {},
		},
		["SEQUENCE_OPERAT_DESC"] = {
			["Key"] = "SEQUENCE_OPERAT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948162560'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_CLOCKWISE"] = {
			["Key"] = "SEQUENCE_SRIRRING_CLOCKWISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948162816'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_ANTICLOCKWISE"] = {
			["Key"] = "SEQUENCE_SRIRRING_ANTICLOCKWISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948163072'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_COUNT"] = {
			["Key"] = "SEQUENCE_SRIRRING_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948163328'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_PREADD"] = {
			["Key"] = "SEQUENCE_SRIRRING_PREADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948163584'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_ADDING"] = {
			["Key"] = "SEQUENCE_SRIRRING_ADDING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948163840'),
			["StringValueList"] = {},
		},
		["SEQUENCE_GAIN_TITLE"] = {
			["Key"] = "SEQUENCE_GAIN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948164096'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SPIRIT_COLLECT_L"] = {
			["Key"] = "SEQUENCE_SPIRIT_COLLECT_L",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948164352'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SPIRIT_COLLECT_R"] = {
			["Key"] = "SEQUENCE_SPIRIT_COLLECT_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948164608'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_GAP"] = {
			["Key"] = "SEQUENCE_ADJUST_GAP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948164864'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_X_TRANSFORM"] = {
			["Key"] = "SEQUENCE_ADJUST_X_TRANSFORM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948165120'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_Y_TRANSFORM"] = {
			["Key"] = "SEQUENCE_ADJUST_Y_TRANSFORM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948165376'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_DESC"] = {
			["Key"] = "SPIRITUAL_POWER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948165632'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_RECOVER_ITEM"] = {
			["Key"] = "SPIRITUAL_POWER_RECOVER_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948165888'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_TITLE"] = {
			["Key"] = "ROLEPLAY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605888'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_RECOVER_DAILY"] = {
			["Key"] = "SPIRITUAL_POWER_RECOVER_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948166400'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TITLE"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948166656'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_UNLOCKJUMP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_UNLOCKJUMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948096768'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_LOCK"] = {
			["Key"] = "ROLEPLAY_IDENTITY_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947773184'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_EQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265605888'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_ONEQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_ONEQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948167680'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_CANTEQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_CANTEQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948167936'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_TITLE"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774454272'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_UNLOCKED"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_UNLOCKED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948168448'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_UNLOCK"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948168704'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_LOCK"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948168960'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_SKILL_TITLE"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_SKILL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948169216'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_CONDITION"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948169472'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TABLE_PUBLIC"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TABLE_PUBLIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948169728'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TABLE_HIDEN"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TABLE_HIDEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948169984'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_TITLE"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948170240'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_TALK"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_TALK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948170496'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_REJECT"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_REJECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948170752'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_ACCEPT"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_ACCEPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948171008'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_DICE"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948171264'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_CHAIR_INTERACT"] = {
			["Key"] = "ROLEPLAY_WITCH_CHAIR_INTERACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61505273862400'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_TRAPED"] = {
			["Key"] = "ROLEPLAY_WITCH_TRAPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948171776'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_SKIP"] = {
			["Key"] = "ROLEPLAY_WITCH_SKIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948172032'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_TITLE"] = {
			["Key"] = "ROLEPLAY_WITCH_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948172288'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_1"] = {
			["Key"] = "ROLEPLAY_FORTUNE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948172544'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_2"] = {
			["Key"] = "ROLEPLAY_FORTUNE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948172800'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_3"] = {
			["Key"] = "ROLEPLAY_FORTUNE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948173056'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_4"] = {
			["Key"] = "ROLEPLAY_FORTUNE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948173312'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_5"] = {
			["Key"] = "ROLEPLAY_FORTUNE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948173568'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_6"] = {
			["Key"] = "ROLEPLAY_FORTUNE_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61505273862144'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_7"] = {
			["Key"] = "ROLEPLAY_FORTUNE_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948174080'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_8"] = {
			["Key"] = "ROLEPLAY_FORTUNE_8",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948174336'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_9"] = {
			["Key"] = "ROLEPLAY_FORTUNE_9",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948174592'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_10"] = {
			["Key"] = "ROLEPLAY_FORTUNE_10",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948174848'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_11"] = {
			["Key"] = "ROLEPLAY_FORTUNE_11",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948175104'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_12"] = {
			["Key"] = "ROLEPLAY_FORTUNE_12",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948175360'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_13"] = {
			["Key"] = "ROLEPLAY_FORTUNE_13",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948175616'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_14"] = {
			["Key"] = "ROLEPLAY_FORTUNE_14",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948175872'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_15"] = {
			["Key"] = "ROLEPLAY_FORTUNE_15",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948176128'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_16"] = {
			["Key"] = "ROLEPLAY_FORTUNE_16",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948176384'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_17"] = {
			["Key"] = "ROLEPLAY_FORTUNE_17",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948176640'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_UPRIGHT"] = {
			["Key"] = "ROLEPLAY_FORTUNE_UPRIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948176896'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_REVERSED"] = {
			["Key"] = "ROLEPLAY_FORTUNE_REVERSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948177152'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_ADD"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_ADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948177408'),
			["StringValueList"] = {},
		},
		["WITCH_INVITE_FAIL_POPTXT"] = {
			["Key"] = "WITCH_INVITE_FAIL_POPTXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948177664'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_UNLOCK"] = {
			["Key"] = "ROLEPLAY_SKILL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948177920'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_LEVELUP"] = {
			["Key"] = "ROLEPLAY_SKILL_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948178176'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_PREPOSITION_LOCK"] = {
			["Key"] = "ROLEPLAY_PREPOSITION_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948178432'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_CONDITION_LOCK"] = {
			["Key"] = "ROLEPLAY_CONDITION_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948178688'),
			["StringValueList"] = {},
		},
		["MATERIALS_LACK"] = {
			["Key"] = "MATERIALS_LACK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948178944'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_FULL_LEVEL"] = {
			["Key"] = "ROLEPLAY_SKILL_FULL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179200'),
			["StringValueList"] = {},
		},
		["CLICK_DICE"] = {
			["Key"] = "CLICK_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179456'),
			["StringValueList"] = {},
		},
		["DICE_ADDITION"] = {
			["Key"] = "DICE_ADDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179712'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179968'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948180224'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948180480'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948180736'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948180992'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948181248'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948181504'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948181760'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182016'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182272'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182528'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182784'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183040'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183296'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183552'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182272'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182528'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182784'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183040'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183296'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183552'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182272'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182528'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948182784'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183040'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183296'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948183552'),
			["StringValueList"] = {},
		},
		["FORTUNE_RIDER_PLAY_TEXT1"] = {
			["Key"] = "FORTUNE_RIDER_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948186880'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_FORTUNE"] = {
			["Key"] = "ROLEPLAY_HUD_FORTUNE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58275190019328'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_JOKER"] = {
			["Key"] = "ROLEPLAY_HUD_JOKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36697005886464'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_WITCH"] = {
			["Key"] = "ROLEPLAY_HUD_WITCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38277822295040'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_ARBITRATOR"] = {
			["Key"] = "ROLEPLAY_HUD_ARBITRATOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948187904'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_MANAGE"] = {
			["Key"] = "SOCIALACTION_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337298688'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_FAVOR"] = {
			["Key"] = "SOCIALACTION_FAVOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948188416'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_SAVE"] = {
			["Key"] = "SOCIALACTION_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947997696'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_PLAYER_NUM"] = {
			["Key"] = "SOCIALACTION_PLAYER_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948188928'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_INVITE_NAME"] = {
			["Key"] = "SOCIALACTION_INVITE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948189184'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_NO_PLAYER"] = {
			["Key"] = "SOCIALACTION_NO_PLAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948189440'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_SEARCH"] = {
			["Key"] = "SOCIALACTION_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948189696'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216625153')},
		},
		["SOCIALACTION_WAIT_REMIND"] = {
			["Key"] = "SOCIALACTION_WAIT_REMIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61505273861120'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216625409')},
		},
		["SOCIALACTION_INVITE_REMIND"] = {
			["Key"] = "SOCIALACTION_INVITE_REMIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61505273861376'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216625409')},
		},
		["SOCIALACTION_CANCEL"] = {
			["Key"] = "SOCIALACTION_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_CONFIRM"] = {
			["Key"] = "SOCIALACTION_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["StringValueList"] = {},
		},
		["CP_ExcitedSoul"] = {
			["Key"] = "CP_ExcitedSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948190976'),
			["StringValueList"] = {},
		},
		["CP_HappySoul"] = {
			["Key"] = "CP_HappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191232'),
			["StringValueList"] = {},
		},
		["CP_PeaceSoul"] = {
			["Key"] = "CP_PeaceSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191488'),
			["StringValueList"] = {},
		},
		["CP_CalmSoul"] = {
			["Key"] = "CP_CalmSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191744'),
			["StringValueList"] = {},
		},
		["CP_UpsetSoul"] = {
			["Key"] = "CP_UpsetSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192000'),
			["StringValueList"] = {},
		},
		["CP_UnhappySoul"] = {
			["Key"] = "CP_UnhappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192256'),
			["StringValueList"] = {},
		},
		["CP_AngrySoul"] = {
			["Key"] = "CP_AngrySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192512'),
			["StringValueList"] = {},
		},
		["ML_ExcitedSoul"] = {
			["Key"] = "ML_ExcitedSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948190976'),
			["StringValueList"] = {},
		},
		["ML_HappySoul"] = {
			["Key"] = "ML_HappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191232'),
			["StringValueList"] = {},
		},
		["ML_PeaceSoul"] = {
			["Key"] = "ML_PeaceSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191488'),
			["StringValueList"] = {},
		},
		["ML_CalmSoul"] = {
			["Key"] = "ML_CalmSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948191744'),
			["StringValueList"] = {},
		},
		["ML_UpsetSoul"] = {
			["Key"] = "ML_UpsetSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192000'),
			["StringValueList"] = {},
		},
		["ML_UnhappySoul"] = {
			["Key"] = "ML_UnhappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192256'),
			["StringValueList"] = {},
		},
		["ML_AngrySoul"] = {
			["Key"] = "ML_AngrySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948192512'),
			["StringValueList"] = {},
		},
		["CP_Quit"] = {
			["Key"] = "CP_Quit",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_12095701654784'),
			["StringValueList"] = {},
		},
		["CP_Confirm"] = {
			["Key"] = "CP_Confirm",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030741760'),
			["StringValueList"] = {},
		},
		["CP_Bargain"] = {
			["Key"] = "CP_Bargain",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948195072'),
			["StringValueList"] = {},
		},
		["ExcitedSoul_Desc"] = {
			["Key"] = "ExcitedSoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948195328'),
			["StringValueList"] = {},
		},
		["HappySoul_Desc"] = {
			["Key"] = "HappySoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["PeaceSoul_Desc"] = {
			["Key"] = "PeaceSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["CalmSoul_Desc"] = {
			["Key"] = "CalmSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["UpsetSoul_Desc"] = {
			["Key"] = "UpsetSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["UnhappySoul_Desc"] = {
			["Key"] = "UnhappySoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948196608'),
			["StringValueList"] = {},
		},
		["AngrySoul_Desc"] = {
			["Key"] = "AngrySoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948196864'),
			["StringValueList"] = {},
		},
		["MOOD_LEVELUP"] = {
			["Key"] = "MOOD_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948197120'),
			["StringValueList"] = {},
		},
		["MOOD_LEVELDOWN"] = {
			["Key"] = "MOOD_LEVELDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948197376'),
			["StringValueList"] = {},
		},
		["Excited_CutPriceTips"] = {
			["Key"] = "Excited_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948197632'),
			["StringValueList"] = {},
		},
		["HappySoul_CutPriceTips"] = {
			["Key"] = "HappySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948197632'),
			["StringValueList"] = {},
		},
		["PeaceSoul_CutPriceTips"] = {
			["Key"] = "PeaceSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_12989054850816'),
			["StringValueList"] = {},
		},
		["CalmSoul_CutPriceTips"] = {
			["Key"] = "CalmSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_12989054850816'),
			["StringValueList"] = {},
		},
		["UpsetSoul_CutPriceTips"] = {
			["Key"] = "UpsetSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_12989054850816'),
			["StringValueList"] = {},
		},
		["UnhappySoul_CutPriceTips"] = {
			["Key"] = "UnhappySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_12989054850816'),
			["StringValueList"] = {},
		},
		["AngrySoul_CutPriceTips"] = {
			["Key"] = "AngrySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948199168'),
			["StringValueList"] = {},
		},
		["CROSS_WORD_PUZZLE_TIPS"] = {
			["Key"] = "CROSS_WORD_PUZZLE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948199424'),
			["StringValueList"] = {},
		},
		["JIGSAW_PUZZLE_NAME"] = {
			["Key"] = "JIGSAW_PUZZLE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948199680'),
			["StringValueList"] = {},
		},
		["GRADE_SIMPLE"] = {
			["Key"] = "GRADE_SIMPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948199936'),
			["StringValueList"] = {},
		},
		["GRADE_HARD"] = {
			["Key"] = "GRADE_HARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947833856'),
			["StringValueList"] = {},
		},
		["GRADE_MASTER"] = {
			["Key"] = "GRADE_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948200448'),
			["StringValueList"] = {},
		},
		["DANCE_TAB_MUSIC"] = {
			["Key"] = "DANCE_TAB_MUSIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948200704'),
			["StringValueList"] = {},
		},
		["DANCE_TAB_DEVELOP"] = {
			["Key"] = "DANCE_TAB_DEVELOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947904000'),
			["StringValueList"] = {},
		},
		["DANCE_IDENTITY_NAME"] = {
			["Key"] = "DANCE_IDENTITY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948201216'),
			["StringValueList"] = {},
		},
		["UNLOCK_LEVEL"] = {
			["Key"] = "UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948201472'),
			["StringValueList"] = {},
		},
		["MUSIC_UNFINISHED"] = {
			["Key"] = "MUSIC_UNFINISHED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987988736'),
			["StringValueList"] = {},
		},
		["DANCE_TYPE_DOUBLE"] = {
			["Key"] = "DANCE_TYPE_DOUBLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53808155592448'),
			["StringValueList"] = {},
		},
		["DANCE_TYPE_MULTIPLAYER"] = {
			["Key"] = "DANCE_TYPE_MULTIPLAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948202240'),
			["StringValueList"] = {},
		},
		["CHALLENGE_RECORD"] = {
			["Key"] = "CHALLENGE_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948202496'),
			["StringValueList"] = {},
		},
		["RANKING_DIGIT"] = {
			["Key"] = "RANKING_DIGIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948202752'),
			["StringValueList"] = {},
		},
		["MASTER_DEGREE"] = {
			["Key"] = "MASTER_DEGREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948203008'),
			["StringValueList"] = {},
		},
		["ON_THE_LIST_NOT_YET"] = {
			["Key"] = "ON_THE_LIST_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948203264'),
			["StringValueList"] = {},
		},
		["COMPLETE_NOT_YET"] = {
			["Key"] = "COMPLETE_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948203520'),
			["StringValueList"] = {},
		},
		["COMPLETE_ALREADY"] = {
			["Key"] = "COMPLETE_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948203776'),
			["StringValueList"] = {},
		},
		["CHALLENGE_REPEAT"] = {
			["Key"] = "CHALLENGE_REPEAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948204032'),
			["StringValueList"] = {},
		},
		["ENERGY_USE_TIP"] = {
			["Key"] = "ENERGY_USE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948204288'),
			["StringValueList"] = {},
		},
		["PERFORM_BEGIN"] = {
			["Key"] = "PERFORM_BEGIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948204544'),
			["StringValueList"] = {},
		},
		["UNLOCK_REQUIRE"] = {
			["Key"] = "UNLOCK_REQUIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948204800'),
			["StringValueList"] = {},
		},
		["SOLO_INPLACE"] = {
			["Key"] = "SOLO_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948205056'),
			["StringValueList"] = {},
		},
		["DOUBLE_INPLACE"] = {
			["Key"] = "DOUBLE_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948205312'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_INPLACE"] = {
			["Key"] = "MULTIDANCE_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948205568'),
			["StringValueList"] = {},
		},
		["CHALLENGE_ENTRANCE"] = {
			["Key"] = "CHALLENGE_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948205824'),
			["StringValueList"] = {},
		},
		["CHALLENGE_SET"] = {
			["Key"] = "CHALLENGE_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948206080'),
			["StringValueList"] = {},
		},
		["ENERGY_USE"] = {
			["Key"] = "ENERGY_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948206336'),
			["StringValueList"] = {},
		},
		["CHALLENGE_BEGIN"] = {
			["Key"] = "CHALLENGE_BEGIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948206592'),
			["StringValueList"] = {},
		},
		["COMING_SOON"] = {
			["Key"] = "COMING_SOON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_15531675492352'),
			["StringValueList"] = {},
		},
		["DEGREE_SELECT"] = {
			["Key"] = "DEGREE_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948207104'),
			["StringValueList"] = {},
		},
		["CHALLENGE_RECORD_SET"] = {
			["Key"] = "CHALLENGE_RECORD_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948207360'),
			["StringValueList"] = {},
		},
		["CHALLENGE_NOT_YET"] = {
			["Key"] = "CHALLENGE_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948207616'),
			["StringValueList"] = {},
		},
		["SELECT_ALREADY"] = {
			["Key"] = "SELECT_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948207872'),
			["StringValueList"] = {},
		},
		["SELECT_CONFIRM"] = {
			["Key"] = "SELECT_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948208128'),
			["StringValueList"] = {},
		},
		["INPLACE_CONFIRM_COUNTDOWN"] = {
			["Key"] = "INPLACE_CONFIRM_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948208384'),
			["StringValueList"] = {},
		},
		["INPLACE_CONFIRM"] = {
			["Key"] = "INPLACE_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948208640'),
			["StringValueList"] = {},
		},
		["INPLACE_WAITING"] = {
			["Key"] = "INPLACE_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948208896'),
			["StringValueList"] = {},
		},
		["SELECT_WAITING"] = {
			["Key"] = "SELECT_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948209152'),
			["StringValueList"] = {},
		},
		["CHALLENGE_BEGIN_REMINDER"] = {
			["Key"] = "CHALLENGE_BEGIN_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948209408'),
			["StringValueList"] = {},
		},
		["ENERGY_USE_SETTLEMENT"] = {
			["Key"] = "ENERGY_USE_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948209664'),
			["StringValueList"] = {},
		},
		["IDENTITY_EXP"] = {
			["Key"] = "IDENTITY_EXP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948209920'),
			["StringValueList"] = {},
		},
		["LEVEL_PROGRESS"] = {
			["Key"] = "LEVEL_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948210176'),
			["StringValueList"] = {},
		},
		["REWARD_MONEY"] = {
			["Key"] = "REWARD_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948210432'),
			["StringValueList"] = {},
		},
		["REWARD_ITEM"] = {
			["Key"] = "REWARD_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948210688'),
			["StringValueList"] = {},
		},
		["CHALLENGE_REPEAT_SETTLEMENT"] = {
			["Key"] = "CHALLENGE_REPEAT_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948210944'),
			["StringValueList"] = {},
		},
		["CHALLENGE_FAIL"] = {
			["Key"] = "CHALLENGE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_40408931370498'),
			["StringValueList"] = {},
		},
		["ENERGY_RETURN"] = {
			["Key"] = "ENERGY_RETURN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948211456'),
			["StringValueList"] = {},
		},
		["ASSIST_VALUE"] = {
			["Key"] = "ASSIST_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948211712'),
			["StringValueList"] = {},
		},
		["PERFORM_SET"] = {
			["Key"] = "PERFORM_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948211968'),
			["StringValueList"] = {},
		},
		["TAB_MUSIC"] = {
			["Key"] = "TAB_MUSIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948200704'),
			["StringValueList"] = {},
		},
		["TAB_VEHICLE"] = {
			["Key"] = "TAB_VEHICLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_39170907048192'),
			["StringValueList"] = {},
		},
		["TAB_EFFECT"] = {
			["Key"] = "TAB_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948212736'),
			["StringValueList"] = {},
		},
		["STAGE_EDIT"] = {
			["Key"] = "STAGE_EDIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948212992'),
			["StringValueList"] = {},
		},
		["SET_ENTRANCE"] = {
			["Key"] = "SET_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328569088'),
			["StringValueList"] = {},
		},
		["VIEW_FREE"] = {
			["Key"] = "VIEW_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948213504'),
			["StringValueList"] = {},
		},
		["VIEW_FIX"] = {
			["Key"] = "VIEW_FIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948213760'),
			["StringValueList"] = {},
		},
		["SHIELD_ENTRANCE"] = {
			["Key"] = "SHIELD_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947947264'),
			["StringValueList"] = {},
		},
		["SHIELD_TITLE"] = {
			["Key"] = "SHIELD_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948214272'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY"] = {
			["Key"] = "ROLE_DISPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948214528'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_OPEN"] = {
			["Key"] = "ROLE_DISPLAY_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485077760'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_CLOSE"] = {
			["Key"] = "ROLE_DISPLAY_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947759360'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_TEAMMATE"] = {
			["Key"] = "ROLE_DISPLAY_TEAMMATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947784192'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_GUILD"] = {
			["Key"] = "ROLE_DISPLAY_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948215552'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_NPC"] = {
			["Key"] = "ROLE_DISPLAY_NPC",
			["StringValue"] = "NPC",
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_FORMATION"] = {
			["Key"] = "ROLE_DISPLAY_FORMATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948216064'),
			["StringValueList"] = {},
		},
		["UI_HIDE"] = {
			["Key"] = "UI_HIDE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948169984'),
			["StringValueList"] = {},
		},
		["UI_DISPLAY"] = {
			["Key"] = "UI_DISPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_52503022406144'),
			["StringValueList"] = {},
		},
		["HUD_EXIT"] = {
			["Key"] = "HUD_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093762306560'),
			["StringValueList"] = {},
		},
		["HUD_COUNTDOWN"] = {
			["Key"] = "HUD_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948217088'),
			["StringValueList"] = {},
		},
		["PERFORM_SET_CONFIRM"] = {
			["Key"] = "PERFORM_SET_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["StringValueList"] = {},
		},
		["PERFORM_END"] = {
			["Key"] = "PERFORM_END",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948217600'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_CLOSE_TIP"] = {
			["Key"] = "SETTLEMENT_CLOSE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948217856'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_REWARD"] = {
			["Key"] = "SETTLEMENT_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947853568'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_FORMATION_NPC"] = {
			["Key"] = "MULTIDANCE_FORMATION_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948218368'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_LOOP"] = {
			["Key"] = "MULTIDANCE_LOOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948218624'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_JOIN"] = {
			["Key"] = "MULTIDANCE_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948218880'),
			["StringValueList"] = {},
		},
		["TOUR_BEGIN_CHAT_TIP"] = {
			["Key"] = "TOUR_BEGIN_CHAT_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948219136'),
			["StringValueList"] = {},
		},
		["TOUR_JOIN"] = {
			["Key"] = "TOUR_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948219392'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_NAME"] = {
			["Key"] = "FASHION_PLAN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948219648'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_REPLACE"] = {
			["Key"] = "FASHION_PLAN_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093225473792'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_RENAME_POPUP"] = {
			["Key"] = "FASHION_PLAN_RENAME_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493909504'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_REPLACE_POPUP"] = {
			["Key"] = "FASHION_PLAN_REPLACE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493909248'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TITLE"] = {
			["Key"] = "FASHION_CHECKLIST_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948220672'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TIP1"] = {
			["Key"] = "FASHION_CHECKLIST_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948220928'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TIP2"] = {
			["Key"] = "FASHION_CHECKLIST_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948221184'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKED_CANBUY"] = {
			["Key"] = "FASHION_LOCKED_CANBUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948221440'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKED_CANTBUY"] = {
			["Key"] = "FASHION_LOCKED_CANTBUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948221696'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_CANCEL"] = {
			["Key"] = "FASHION_STAIN_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948221952'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SAVE"] = {
			["Key"] = "FASHION_STAIN_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948222208'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SAVE_POPUP"] = {
			["Key"] = "FASHION_STAIN_SAVE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948222464'),
			["StringValueList"] = {},
		},
		["FASHION_COLOR_LOCKPLACE"] = {
			["Key"] = "FASHION_COLOR_LOCKPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948222720'),
			["StringValueList"] = {},
		},
		["FASHION_GETNEW_REMINDER"] = {
			["Key"] = "FASHION_GETNEW_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948222976'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKPLACE_POPUP"] = {
			["Key"] = "FASHION_LOCKPLACE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948223232'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_NOMONEY"] = {
			["Key"] = "FASHION_STAIN_NOMONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948223488'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SUCCESS"] = {
			["Key"] = "FASHION_STAIN_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948223744'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_BUTTON_NAME"] = {
			["Key"] = "FASHION_DYEING_BUTTON_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948224000'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_UPPER"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_UPPER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948224256'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_PANTS"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_PANTS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948224512'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_HAIR"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_HAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948224768'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_SUIT"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_SUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948225024'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_UPPER"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_UPPER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948225280'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_PANTS"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_PANTS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948225536'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_HAIR"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_HAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948225792'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_SUIT"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_SUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948226048'),
			["StringValueList"] = {},
		},
		["FASHION_GETNEW__AGAIN_REMINDER"] = {
			["Key"] = "FASHION_GETNEW__AGAIN_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948226304'),
			["StringValueList"] = {},
		},
		["Ongoing"] = {
			["Key"] = "Ongoing",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076544'),
			["StringValueList"] = {},
		},
		["To_Be_Opened"] = {
			["Key"] = "To_Be_Opened",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948100096'),
			["StringValueList"] = {},
		},
		["Close_Time_Remind"] = {
			["Key"] = "Close_Time_Remind",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785472'),
			["StringValueList"] = {},
		},
		["Open_Time_Remind"] = {
			["Key"] = "Open_Time_Remind",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947785216'),
			["StringValueList"] = {},
		},
		["The_Which_One_Of_Month"] = {
			["Key"] = "The_Which_One_Of_Month",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948227584'),
			["StringValueList"] = {},
		},
		["GUILD_LEAGUE_ACTIVITY_NAME"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520506112'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216663297')},
		},
		["GUILD_LEAGUE_GROUP_SCHEDULE_NAME"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_SCHEDULE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948228096'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216663553')},
		},
		["GUILD_LEAGUE_DEFAULT_DIVISION_NAME"] = {
			["Key"] = "GUILD_LEAGUE_DEFAULT_DIVISION_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948228352'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216663809')},
		},
		["GUILD_LEAGUE_ACTIVITY_START_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_START_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948228608'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216664065')},
		},
		["GUILD_LEAGUE_BID_START_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_BID_START_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948228864'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216664321')},
		},
		["GUILD_LEAGUE_TBD_DESC"] = {
			["Key"] = "GUILD_LEAGUE_TBD_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5567082923520'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216664577')},
		},
		["GUILD_LEAGUE_GROUP_DESC"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948229376'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216664833')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_NOT_START_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_NOT_START_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948229632'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216665089')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_IN_PROGRESS_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_IN_PROGRESS_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948076544'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216665345')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948230144'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216665601')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948230400'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216665857')},
		},
		["GUILD_LEAGUE_NO_QUALIFICATION_DESC"] = {
			["Key"] = "GUILD_LEAGUE_NO_QUALIFICATION_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948230656'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216666113')},
		},
		["GUILD_LEAGUE_WAIT_NEXT_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_WAIT_NEXT_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948230912'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216666369')},
		},
		["GUILD_LEAGUE_START_TIME_DETAIL_DESC"] = {
			["Key"] = "GUILD_LEAGUE_START_TIME_DETAIL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948231168'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216666625')},
		},
		["GUILD_LEAGUE_ACTIVITY_END_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_END_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_51265803390976'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216666881')},
		},
		["GUILD_LEAGUE_NO_GROUP_NAME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_NO_GROUP_NAME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948231680'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216667137')},
		},
		["GUILD_LEAGUE_OVER_MAX_GROUP_NAME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_OVER_MAX_GROUP_NAME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56144617809664'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216667393')},
		},
		["GUILD_LEAGUE_PRELIMINARY_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_PRELIMINARY_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948232192'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216667649')},
		},
		["GUILD_LEAGUE_FINAL_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_FINAL_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948232448'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216667905')},
		},
		["GUILD_LEAGUE_GO_TO_LEAGUE_DESC"] = {
			["Key"] = "GUILD_LEAGUE_GO_TO_LEAGUE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948232704'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216668161')},
		},
		["GUILD_LEAGUE_ROUND_DISPLAY_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ROUND_DISPLAY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948232960'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216668417')},
		},
		["GUILD_LEAGUE_TAB_COMMAND_STRATEGY"] = {
			["Key"] = "GUILD_LEAGUE_TAB_COMMAND_STRATEGY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948233216'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216668673')},
		},
		["GUILD_LEAGUE_TAB_TAG"] = {
			["Key"] = "GUILD_LEAGUE_TAB_TAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948233472'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216668673')},
		},
		["GUILD_LEAGUE_FIRST_ROUND_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_FIRST_ROUND_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948233728'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216669185')},
		},
		["GUILD_LEAGUE_SECOND_ROUND_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_SECOND_ROUND_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948233984'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216669441')},
		},
		["GUILD_LEAGUE_GROUP_TAB_ALL"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_TAB_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38758590255104'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216669697')},
		},
		["GUILD_LEAGUE_GROUP_NEED_RESOURCE"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_NEED_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948234496'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216669953')},
		},
		["GUILD_LEAGUE_GROUP_COURAGE_MAX"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_COURAGE_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948234752'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216670209')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TEXT_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TEXT_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948235008'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216670465')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TEXT_TITLE"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TEXT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948235264'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216670721')},
		},
		["GUILD_LEAGUE_GROUP_NAME_CONST"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_NAME_CONST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506010112'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216670977')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_NOT_ACTIVATE"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_NOT_ACTIVATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61025042838785'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216671233')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_OCCUPY"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_OCCUPY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948236032'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216671489')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_BATTLE"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948236288'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216671745')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_OCCUPIED"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_OCCUPIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948236544'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216672001')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948236800'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216672257')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_CLICK_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_CLICK_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948237056'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216672257')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948237312'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216672769')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_GROUP_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_GROUP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948237568'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216672257')},
		},
		["GUILD_LEAGUE_RESOURCE_DEC"] = {
			["Key"] = "GUILD_LEAGUE_RESOURCE_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948237824'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216673281')},
		},
		["GUILD_LEAGUE_OCCUPY_DEC"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36697005901824'),
			["StringValueList"] = {},
		},
		["INVITE_LIST_RANGE"] = {
			["Key"] = "INVITE_LIST_RANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948238336'),
			["StringValueList"] = {},
		},
		["PARTNER_LIKE"] = {
			["Key"] = "PARTNER_LIKE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948238592'),
			["StringValueList"] = {},
		},
		["COMBO_SCORE_TIP"] = {
			["Key"] = "COMBO_SCORE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53602265604352'),
			["StringValueList"] = {},
		},
		["ASSIST_SCORE_TIP"] = {
			["Key"] = "ASSIST_SCORE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948239104'),
			["StringValueList"] = {},
		},
		["ASSIST_CLICK_TIP"] = {
			["Key"] = "ASSIST_CLICK_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948239360'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_CD"] = {
			["Key"] = "SETTLEMENT_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948239616'),
			["StringValueList"] = {},
		},
		["INVITE_COUNTDOWN"] = {
			["Key"] = "INVITE_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948239872'),
			["StringValueList"] = {},
		},
		["MATCH_WAIT"] = {
			["Key"] = "MATCH_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948240128'),
			["StringValueList"] = {},
		},
		["MATCH_COUNTDOWN"] = {
			["Key"] = "MATCH_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948240384'),
			["StringValueList"] = {},
		},
		["MUSIC_NAME_1"] = {
			["Key"] = "MUSIC_NAME_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948240640'),
			["StringValueList"] = {},
		},
		["REWARD_TIPS"] = {
			["Key"] = "REWARD_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948240896'),
			["StringValueList"] = {},
		},
		["INVITE_ALREEADY"] = {
			["Key"] = "INVITE_ALREEADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948241152'),
			["StringValueList"] = {},
		},
		["INVITE_ENABLE"] = {
			["Key"] = "INVITE_ENABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948241408'),
			["StringValueList"] = {},
		},
		["IN_PROCESS"] = {
			["Key"] = "IN_PROCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948241664'),
			["StringValueList"] = {},
		},
		["REJECT_ALREADY"] = {
			["Key"] = "REJECT_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948013568'),
			["StringValueList"] = {},
		},
		["TEAM_REWARD"] = {
			["Key"] = "TEAM_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948242176'),
			["StringValueList"] = {},
		},
		["GUILD_REWARD"] = {
			["Key"] = "GUILD_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948242432'),
			["StringValueList"] = {},
		},
		["PARTNER_INVITE"] = {
			["Key"] = "PARTNER_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61505273861632'),
			["StringValueList"] = {},
		},
		["CASH_GIFT_CONTENT"] = {
			["Key"] = "CASH_GIFT_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948242944'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_1"] = {
			["Key"] = "INVITATION_LETTER_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948243200'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_2"] = {
			["Key"] = "INVITATION_LETTER_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948243456'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_3"] = {
			["Key"] = "INVITATION_LETTER_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948243712'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_4"] = {
			["Key"] = "INVITATION_LETTER_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948243968'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_5"] = {
			["Key"] = "INVITATION_LETTER_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947855104'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_6"] = {
			["Key"] = "INVITATION_LETTER_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948244480'),
			["StringValueList"] = {},
		},
		["DANCE_TITLE"] = {
			["Key"] = "DANCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948244736'),
			["StringValueList"] = {},
		},
		["DANCE_HOT_BAR_1"] = {
			["Key"] = "DANCE_HOT_BAR_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948244992'),
			["StringValueList"] = {},
		},
		["DANCE_HOT_BAR_2"] = {
			["Key"] = "DANCE_HOT_BAR_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948245248'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_1"] = {
			["Key"] = "DANCE_FIRE_TIP_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948245504'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_2"] = {
			["Key"] = "DANCE_FIRE_TIP_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948245760'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_3"] = {
			["Key"] = "DANCE_FIRE_TIP_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948246016'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_4"] = {
			["Key"] = "DANCE_FIRE_TIP_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948246272'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_5"] = {
			["Key"] = "DANCE_FIRE_TIP_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948246528'),
			["StringValueList"] = {},
		},
		["INVITATION_ACTION_TEXT"] = {
			["Key"] = "INVITATION_ACTION_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948246784'),
			["StringValueList"] = {},
		},
		["DANCE_NPC_NAME"] = {
			["Key"] = "DANCE_NPC_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_28176864511488'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_RANKING_NO_SCORE"] = {
			["Key"] = "GUILD_DANCE_RANKING_NO_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948247296'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_RANKING_TITLE"] = {
			["Key"] = "GUILD_DANCE_RANKING_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948247552'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TEXT_1"] = {
			["Key"] = "RED_PACKET_TEXT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948247808'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_TEXT_1"] = {
			["Key"] = "SETTLEMENT_TEXT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948248064'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_TEXT_2"] = {
			["Key"] = "SETTLEMENT_TEXT_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948242176'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_OPENMIC"] = {
			["Key"] = "GUILD_DANCE_VOICE_OPENMIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054784'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_MUTE"] = {
			["Key"] = "GUILD_DANCE_VOICE_MUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948248832'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_LISTEN"] = {
			["Key"] = "GUILD_DANCE_VOICE_LISTEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948054272'),
			["StringValueList"] = {},
		},
		["COMBO_SCORE_MARK"] = {
			["Key"] = "COMBO_SCORE_MARK",
			["StringValue"] = "×",
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_DESC"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948249600'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_NAME"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328577792'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_OPR"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_TIME"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948250368'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_ALREADY_APPLIED"] = {
			["Key"] = "GUILD_APPLY_ALREADY_APPLIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337284864'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_APPLY"] = {
			["Key"] = "GUILD_APPLY_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493896192'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_APPLY_COUNT"] = {
			["Key"] = "GUILD_APPLYLIST_APPLY_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948251136'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_AUTORECV"] = {
			["Key"] = "GUILD_APPLYLIST_AUTORECV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948251392'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_NAME"] = {
			["Key"] = "GUILD_APPLYLIST_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948251648'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_ACCEPT"] = {
			["Key"] = "GUILD_APPLY_ACCEPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337287936'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_MEMBER"] = {
			["Key"] = "GUILD_APPLY_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948252160'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_CLASS"] = {
			["Key"] = "GUILD_APPLYLIST_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_44874623616768'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_ALL"] = {
			["Key"] = "GUILD_APPLYLIST_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56487946749440'),
			["StringValueList"] = {},
		},
		["GUILD_BADGE_CHANGE"] = {
			["Key"] = "GUILD_BADGE_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948252928'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_CONDITION"] = {
			["Key"] = "GUILD_BUILD_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836364544'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_DESC_A"] = {
			["Key"] = "GUILD_BUILD_FOUR_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948253440'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_DESC_B"] = {
			["Key"] = "GUILD_BUILD_FOUR_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948253696'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_EFFECT"] = {
			["Key"] = "GUILD_BUILD_FOUR_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948253952'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_NAME"] = {
			["Key"] = "GUILD_BUILD_FOUR_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948254208'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FUNDS"] = {
			["Key"] = "GUILD_BUILD_FUNDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948254464'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_LEVEL"] = {
			["Key"] = "GUILD_BUILD_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947849472'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_MAIN_CONDITION"] = {
			["Key"] = "GUILD_BUILD_MAIN_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948254976'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_MAX_LEVEL"] = {
			["Key"] = "GUILD_BUILD_MAX_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179200'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_DESC_A"] = {
			["Key"] = "GUILD_BUILD_ONE_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27420681831937'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_DESC_B"] = {
			["Key"] = "GUILD_BUILD_ONE_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27420681831938'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_EFFECT"] = {
			["Key"] = "GUILD_BUILD_ONE_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948256000'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_NAME"] = {
			["Key"] = "GUILD_BUILD_ONE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35327179763456'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_SUB_CONDITION"] = {
			["Key"] = "GUILD_BUILD_SUB_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948256512'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_A"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27420681832449'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_B"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27420681832450'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_C"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_C",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948257280'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_D"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_D",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27420681832452'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_EFFECT"] = {
			["Key"] = "GUILD_BUILD_THREE_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948257792'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_NAME"] = {
			["Key"] = "GUILD_BUILD_THREE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948258048'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TIME"] = {
			["Key"] = "GUILD_BUILD_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948258304'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_A"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948258560'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_B"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947983616'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_C"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_C",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948259072'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_EFFECT"] = {
			["Key"] = "GUILD_BUILD_TWO_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948259328'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_NAME"] = {
			["Key"] = "GUILD_BUILD_TWO_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520506368'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UPGRADE"] = {
			["Key"] = "GUILD_BUILD_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836364800'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UPGRADE_INFO"] = {
			["Key"] = "GUILD_BUILD_UPGRADE_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948260096'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UNLOCK"] = {
			["Key"] = "GUILD_BUILD_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948260352'),
			["StringValueList"] = {},
		},
		["GUILD_CANCEL"] = {
			["Key"] = "GUILD_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023680'),
			["StringValueList"] = {},
		},
		["GUILD_CONFIRM"] = {
			["Key"] = "GUILD_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34635690023936'),
			["StringValueList"] = {},
		},
		["GUILD_SEND"] = {
			["Key"] = "GUILD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947756032'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_DECLERATION_PROMPT"] = {
			["Key"] = "GUILD_CREATE_DECLERATION_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948261376'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_EMPTY"] = {
			["Key"] = "GUILD_CREATE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948261632'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_NAME_PROMPT"] = {
			["Key"] = "GUILD_CREATE_NAME_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948261888'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_REQUIREMENT"] = {
			["Key"] = "GUILD_CREATE_REQUIREMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262144'),
			["StringValueList"] = {},
		},
		["GUILD_EVENT_NAME"] = {
			["Key"] = "GUILD_EVENT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262400'),
			["StringValueList"] = {},
		},
		["GUILD_EVENT_TIME"] = {
			["Key"] = "GUILD_EVENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_57862873195776'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_SEND_NAME"] = {
			["Key"] = "GUILD_GROUP_SEND_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262912'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_SEND_TIP"] = {
			["Key"] = "GUILD_GROUP_SEND_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948263168'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_BONUS"] = {
			["Key"] = "GUILD_HOME_BONUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_33879507343872'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_CHANGE_DEC"] = {
			["Key"] = "GUILD_HOME_CHANGE_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948263680'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_CHANGE_DEC_PROMPT"] = {
			["Key"] = "GUILD_HOME_CHANGE_DEC_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948263936'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_EVENT"] = {
			["Key"] = "GUILD_HOME_EVENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948264192'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_FORMAL_COUNT"] = {
			["Key"] = "GUILD_HOME_FORMAL_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948264448'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_FUND"] = {
			["Key"] = "GUILD_HOME_FUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506024960'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_ID"] = {
			["Key"] = "GUILD_HOME_ID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948264960'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_LIVE"] = {
			["Key"] = "GUILD_HOME_LIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_44805904140544'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MAINTAIN"] = {
			["Key"] = "GUILD_HOME_MAINTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948265472'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MASTER"] = {
			["Key"] = "GUILD_HOME_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948265728'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MEMBER_COUNT"] = {
			["Key"] = "GUILD_HOME_MEMBER_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948265984'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_ROLE"] = {
			["Key"] = "GUILD_HOME_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262400'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_TARGET"] = {
			["Key"] = "GUILD_HOME_TARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506010368'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_LEAVE"] = {
			["Key"] = "GUILD_LIST_LEAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948266752'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_LIST"] = {
			["Key"] = "GUILD_LIST_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948267008'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_RESIGN"] = {
			["Key"] = "GUILD_LIST_RESIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948267264'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_ID"] = {
			["Key"] = "GUILD_MEMBER_ID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948267520'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_LIST"] = {
			["Key"] = "GUILD_MEMBER_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948012800'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_SET_NAME"] = {
			["Key"] = "GUILD_MEMBER_SET_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948268032'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_STRUCTURE"] = {
			["Key"] = "GUILD_MEMBER_STRUCTURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948268288'),
			["StringValueList"] = {},
		},
		["GUILD_MERGE_APPLY"] = {
			["Key"] = "GUILD_MERGE_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948038656'),
			["StringValueList"] = {},
		},
		["GUILD_MERGE_NAME"] = {
			["Key"] = "GUILD_MERGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948268800'),
			["StringValueList"] = {},
		},
		["GUILD_NAME"] = {
			["Key"] = "GUILD_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497408'),
			["StringValueList"] = {},
		},
		["GUILD_NAME_COLON"] = {
			["Key"] = "GUILD_NAME_COLON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948269312'),
			["StringValueList"] = {},
		},
		["GUILD_PERMISSION_NAME"] = {
			["Key"] = "GUILD_PERMISSION_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948269568'),
			["StringValueList"] = {},
		},
		["GUILD_POSITION_SET"] = {
			["Key"] = "GUILD_POSITION_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506026240'),
			["StringValueList"] = {},
		},
		["GUILD_POS"] = {
			["Key"] = "GUILD_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948270080'),
			["StringValueList"] = {},
		},
		["GUILD_RANKING_NAME"] = {
			["Key"] = "GUILD_RANKING_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948270336'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_NAME"] = {
			["Key"] = "GUILD_RENAME_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948270592'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_NO_USED_NAME"] = {
			["Key"] = "GUILD_RENAME_NO_USED_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948270848'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_USED_NAME"] = {
			["Key"] = "GUILD_RENAME_USED_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948271104'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_NAME"] = {
			["Key"] = "GUILD_REPORT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947808000'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONDS_EMPTY"] = {
			["Key"] = "GUILD_RESPONDS_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948271616'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_ALREADY_RESPOND"] = {
			["Key"] = "GUILD_RESPONSE_ALREADY_RESPOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337285120'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_CANCEL"] = {
			["Key"] = "GUILD_RESPONSE_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337285888'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_CANCEL_CREATE"] = {
			["Key"] = "GUILD_RESPONSE_CANCEL_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493875968'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_RESPONSE"] = {
			["Key"] = "GUILD_RESPONSE_RESPONSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948272640'),
			["StringValueList"] = {},
		},
		["GUILD_SAVE"] = {
			["Key"] = "GUILD_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948272896'),
			["StringValueList"] = {},
		},
		["GUILD_SETPOS_CURRENT"] = {
			["Key"] = "GUILD_SETPOS_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948273152'),
			["StringValueList"] = {},
		},
		["GUILD_SETPOS_NAME"] = {
			["Key"] = "GUILD_SETPOS_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948273408'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_INTRO"] = {
			["Key"] = "GUILD_SIGNIN_INTRO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948273664'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_NAME"] = {
			["Key"] = "GUILD_SIGNIN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948273920'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_PROMPT"] = {
			["Key"] = "GUILD_SIGNIN_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948274176'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BUILDING_REQ"] = {
			["Key"] = "GUILD_SKILL_BUILDING_REQ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948274432'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BUILDING_REQ_RED"] = {
			["Key"] = "GUILD_SKILL_BUILDING_REQ_RED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948274688'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_EXERCISE"] = {
			["Key"] = "GUILD_SKILL_EXERCISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948274944'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL"] = {
			["Key"] = "GUILD_SKILL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948275200'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL_REQ"] = {
			["Key"] = "GUILD_SKILL_LEVEL_REQ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948275456'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL_REQ_RED"] = {
			["Key"] = "GUILD_SKILL_LEVEL_REQ_RED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948275712'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_NAME"] = {
			["Key"] = "GUILD_SKILL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328578048'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_NEXT"] = {
			["Key"] = "GUILD_SKILL_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948276224'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_SKILL_TOP_LEVEL"] = {
			["Key"] = "GUILD_SKILL_SKILL_TOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948276480'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TOP_LEVEL"] = {
			["Key"] = "GUILD_SKILL_TOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948179200'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_ONE"] = {
			["Key"] = "GUILD_SKILL_TYPE_ONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948276992'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_THREE"] = {
			["Key"] = "GUILD_SKILL_TYPE_THREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948277248'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_TWO"] = {
			["Key"] = "GUILD_SKILL_TYPE_TWO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948277504'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_DAY_AGO"] = {
			["Key"] = "GUILD_STATUS_DAY_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792896'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_HOUR_AGO"] = {
			["Key"] = "GUILD_STATUS_HOUR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792640'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_MIN_AGO"] = {
			["Key"] = "GUILD_STATUS_MIN_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947792384'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_MONTH_AGO"] = {
			["Key"] = "GUILD_STATUS_MONTH_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948278528'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_ONLINE"] = {
			["Key"] = "GUILD_STATUS_ONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947949568'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_YEAR_AGO"] = {
			["Key"] = "GUILD_STATUS_YEAR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948279040'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_ACTIVITY_PAGE"] = {
			["Key"] = "GUILD_TAG_ACTIVITY_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755573760'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_ACTIVITY_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_ACTIVITY_PAGE_ENG",
			["StringValue"] = "Activity",
			["StringValueList"] = {},
		},
		["GUILD_TAG_OVERVIEW"] = {
			["Key"] = "GUILD_TAG_OVERVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_138781135616'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_APPLY"] = {
			["Key"] = "GUILD_TAG_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493896192'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_APPLY_ENG"] = {
			["Key"] = "GUILD_TAG_APPLY_ENG",
			["StringValue"] = "Apply",
			["StringValueList"] = {},
		},
		["GUILD_TAG_BUILD_PAGE"] = {
			["Key"] = "GUILD_TAG_BUILD_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948280576'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_CREATE"] = {
			["Key"] = "GUILD_TAG_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337278720'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_CREATE_ENG"] = {
			["Key"] = "GUILD_TAG_CREATE_ENG",
			["StringValue"] = "Found",
			["StringValueList"] = {},
		},
		["GUILD_TAG_HOME_PAGE"] = {
			["Key"] = "GUILD_TAG_HOME_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36353408501504'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_HOME_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_HOME_PAGE_ENG",
			["StringValue"] = "Homepage",
			["StringValueList"] = {},
		},
		["GUILD_TAG_IN_RESPONSE"] = {
			["Key"] = "GUILD_TAG_IN_RESPONSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948281856'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_IN_RESPONSE_ENG"] = {
			["Key"] = "GUILD_TAG_IN_RESPONSE_ENG",
			["StringValue"] = "Respond",
			["StringValueList"] = {},
		},
		["GUILD_TAG_MEMBER_LIST"] = {
			["Key"] = "GUILD_TAG_MEMBER_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574784'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_MEMBER_LIST_ENG"] = {
			["Key"] = "GUILD_TAG_MEMBER_LIST_ENG",
			["StringValue"] = "Member",
			["StringValueList"] = {},
		},
		["GUILD_TAG_RESPONSE_OTHER"] = {
			["Key"] = "GUILD_TAG_RESPONSE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948281856'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_RESPONSE_OTHER_ENG"] = {
			["Key"] = "GUILD_TAG_RESPONSE_OTHER_ENG",
			["StringValue"] = "Respond",
			["StringValueList"] = {},
		},
		["GUILD_TAG_WELFARE_PAGE"] = {
			["Key"] = "GUILD_TAG_WELFARE_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27421755574272'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_WELFARE_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_WELFARE_PAGE_ENG",
			["StringValue"] = "Welfare",
			["StringValueList"] = {},
		},
		["GUILD_TARGET_ALREADY_ACQUIRE"] = {
			["Key"] = "GUILD_TARGET_ALREADY_ACQUIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_6735582465024'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_ALREADY_GET"] = {
			["Key"] = "GUILD_TARGET_ALREADY_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948284160'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CLAIMED"] = {
			["Key"] = "GUILD_TARGET_CLAIMED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948123392'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_COMPLETE"] = {
			["Key"] = "GUILD_TARGET_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987988992'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_COMPLETE_WEEK"] = {
			["Key"] = "GUILD_TARGET_COMPLETE_WEEK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948284928'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CURRENT_COUNT"] = {
			["Key"] = "GUILD_TARGET_CURRENT_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948285184'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_GET_REWARD"] = {
			["Key"] = "GUILD_TARGET_GET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948019456'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_GOTO"] = {
			["Key"] = "GUILD_TARGET_GOTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_HONOR_WALL"] = {
			["Key"] = "GUILD_TARGET_HONOR_WALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948285952'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_MEDAL"] = {
			["Key"] = "GUILD_TARGET_MEDAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948286208'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_NAME"] = {
			["Key"] = "GUILD_TARGET_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506010368'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_PERSONAL_NAME"] = {
			["Key"] = "GUILD_TARGET_PERSONAL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948286720'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_RANK"] = {
			["Key"] = "GUILD_TARGET_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948286976'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_TOTAL_MEDAL"] = {
			["Key"] = "GUILD_TARGET_TOTAL_MEDAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948287232'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CONTRIBUTION"] = {
			["Key"] = "GUILD_TARGET_CONTRIBUTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948287488'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CONTRIBUTION_RESET"] = {
			["Key"] = "GUILD_TARGET_CONTRIBUTION_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948287744'),
			["StringValueList"] = {},
		},
		["GUILD_TIME_HOUR"] = {
			["Key"] = "GUILD_TIME_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948288000'),
			["StringValueList"] = {},
		},
		["GUILD_TIP_CONTACT_NAME"] = {
			["Key"] = "GUILD_TIP_CONTACT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948288256'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_DESC"] = {
			["Key"] = "GUILD_WELFARE_SHOP_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948288512'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_NAME"] = {
			["Key"] = "GUILD_WELFARE_SHOP_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328572928'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_OPR"] = {
			["Key"] = "GUILD_WELFARE_SHOP_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61094030775296'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_DESC"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948289280'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_NAME"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_50991462354944'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_OPR"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948273920'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_DESC"] = {
			["Key"] = "GUILD_WELFARE_SKILL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948290048'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_NAME"] = {
			["Key"] = "GUILD_WELFARE_SKILL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948290304'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_OPR"] = {
			["Key"] = "GUILD_WELFARE_SKILL_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_30031485131264'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_DESC"] = {
			["Key"] = "GUILD_WELFARE_WAGE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948290816'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_NAME"] = {
			["Key"] = "GUILD_WELFARE_WAGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948291072'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_OPR"] = {
			["Key"] = "GUILD_WELFARE_WAGE_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948019456'),
			["StringValueList"] = {},
		},
		["GUILD_BACK_TO_GUILD_RESIDENCE"] = {
			["Key"] = "GUILD_BACK_TO_GUILD_RESIDENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948291584'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_LIST"] = {
			["Key"] = "GUILD_APPLY_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948055808'),
			["StringValueList"] = {},
		},
		["GUILD_BATCH_SEND_MESSAGE"] = {
			["Key"] = "GUILD_BATCH_SEND_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262912'),
			["StringValueList"] = {},
		},
		["GUILD_START_CHAT"] = {
			["Key"] = "GUILD_START_CHAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948292352'),
			["StringValueList"] = {},
		},
		["GUILD_CLEAR_LIST"] = {
			["Key"] = "GUILD_CLEAR_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337287680'),
			["StringValueList"] = {},
		},
		["GUILD_RESTORE_DEFAULT_SETTINGS"] = {
			["Key"] = "GUILD_RESTORE_DEFAULT_SETTINGS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493886464'),
			["StringValueList"] = {},
		},
		["GUILD_SET_ROLE_POSITION"] = {
			["Key"] = "GUILD_SET_ROLE_POSITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948293120'),
			["StringValueList"] = {},
		},
		["GUILD_DISMISS_ROLE_POSITION"] = {
			["Key"] = "GUILD_DISMISS_ROLE_POSITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948293376'),
			["StringValueList"] = {},
		},
		["GUILD_AUTO_SET_REGULAR_ROLE"] = {
			["Key"] = "GUILD_AUTO_SET_REGULAR_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948293632'),
			["StringValueList"] = {},
		},
		["GUILD_CONFIRM_RELEASE"] = {
			["Key"] = "GUILD_CONFIRM_RELEASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948293888'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_VOICE"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948294144'),
			["StringValueList"] = {},
		},
		["GUILD_PARTY_NAME"] = {
			["Key"] = "GUILD_PARTY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774446336'),
			["StringValueList"] = {},
		},
		["GUILD_PARTY_DESC"] = {
			["Key"] = "GUILD_PARTY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948294656'),
			["StringValueList"] = {},
		},
		["GUILE_MATERIAL_NAME"] = {
			["Key"] = "GUILE_MATERIAL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948294912'),
			["StringValueList"] = {},
		},
		["GUILE_MATERIAL_DESC"] = {
			["Key"] = "GUILE_MATERIAL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948295168'),
			["StringValueList"] = {},
		},
		["GUILD_PUZZLE_START_QUESTION"] = {
			["Key"] = "GUILD_PUZZLE_START_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948295424'),
			["StringValueList"] = {},
		},
		["GUILD_PUZZLE_ALUODESI"] = {
			["Key"] = "GUILD_PUZZLE_ALUODESI",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38964748620544'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_AWARD_SCHEDULE"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_AWARD_SCHEDULE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948295936'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_BLESS"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_BLESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948296192'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_NEARLY_START"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_NEARLY_START",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948296448'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_ANSWER_NO_QUESTION"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_ANSWER_NO_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948296704'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_ANSWER_CERTAIN_QUESTION"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_ANSWER_CERTAIN_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948296960'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_WITHOUT_BLESS"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_WITHOUT_BLESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948297216'),
			["StringValueList"] = {},
		},
		["GUILD_NORMAL_CREATED"] = {
			["Key"] = "GUILD_NORMAL_CREATED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948297472'),
			["StringValueList"] = {},
		},
		["GUILD_ADVANCED_CREATED"] = {
			["Key"] = "GUILD_ADVANCED_CREATED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948297728'),
			["StringValueList"] = {},
		},
		["GUILD_COMMEN_SECOND"] = {
			["Key"] = "GUILD_COMMEN_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5910948745984'),
			["StringValueList"] = {},
		},
		["GUILD_LESS_THAN_CERTAIN_MINUTE"] = {
			["Key"] = "GUILD_LESS_THAN_CERTAIN_MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948298240'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BASE"] = {
			["Key"] = "GUILD_RIGHT_BASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948298496'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MANAGE"] = {
			["Key"] = "GUILD_RIGHT_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38759663934464'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_PRESIDENT"] = {
			["Key"] = "GUILD_RIGHT_PRESIDENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948299008'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BABY"] = {
			["Key"] = "GUILD_RIGHT_BABY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948299264'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_POSITION_SET"] = {
			["Key"] = "GUILD_RIGHT_POSITION_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948299520'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_ANNOUNCE"] = {
			["Key"] = "GUILD_RIGHT_ANNOUNCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948299776'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_CONSTRUCTION"] = {
			["Key"] = "GUILD_RIGHT_CONSTRUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024506025472'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_OPEN_ACTIVITY"] = {
			["Key"] = "GUILD_RIGHT_OPEN_ACTIVITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948300288'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_ACCEPT_MEMBER"] = {
			["Key"] = "GUILD_RIGHT_ACCEPT_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948300544'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MERGE"] = {
			["Key"] = "GUILD_RIGHT_MERGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948300800'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_APPRENTICE"] = {
			["Key"] = "GUILD_RIGHT_APPRENTICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948301056'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_KICKOUT"] = {
			["Key"] = "GUILD_RIGHT_KICKOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493879296'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MODIFY_NAME"] = {
			["Key"] = "GUILD_RIGHT_MODIFY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948270592'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_GROUP_MESSAGE"] = {
			["Key"] = "GUILD_RIGHT_GROUP_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948301824'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_RESIGN"] = {
			["Key"] = "GUILD_RIGHT_RESIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948267264'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_AUTO_RECEIVE"] = {
			["Key"] = "GUILD_RIGHT_AUTO_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948251392'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SKILL"] = {
			["Key"] = "GUILD_RIGHT_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948302592'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_COMMAND"] = {
			["Key"] = "GUILD_RIGHT_SET_COMMAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948302848'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_LOGO"] = {
			["Key"] = "GUILD_RIGHT_SET_LOGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948303104'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MODIFY_PRESIDENT_STATUE"] = {
			["Key"] = "GUILD_RIGHT_MODIFY_PRESIDENT_STATUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948303360'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_BADGE_FRAME"] = {
			["Key"] = "GUILD_RIGHT_SET_BADGE_FRAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948303616'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_ELITE"] = {
			["Key"] = "GUILD_RIGHT_SET_ELITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948303872'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BAN_ROOKIE_BID"] = {
			["Key"] = "GUILD_RIGHT_BAN_ROOKIE_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948304128'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_GUILD_LEAGUE_ELITE"] = {
			["Key"] = "GUILD_RIGHT_SET_GUILD_LEAGUE_ELITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948304384'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_ILLEGAL"] = {
			["Key"] = "GUILD_REPORT_TYPE_ILLEGAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948304640'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_STUDIO"] = {
			["Key"] = "GUILD_REPORT_TYPE_STUDIO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948304896'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_CHEATER"] = {
			["Key"] = "GUILD_REPORT_TYPE_CHEATER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948305152'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_OTHER"] = {
			["Key"] = "GUILD_REPORT_TYPE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206468864'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BATTLE_SCORE"] = {
			["Key"] = "GUILD_SKILL_BATTLE_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947907072'),
			["StringValueList"] = {},
		},
		["GUILD_MAIL_SEND_FREE"] = {
			["Key"] = "GUILD_MAIL_SEND_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948305920'),
			["StringValueList"] = {},
		},
		["GUILD_BACKGROUND_IMAGE"] = {
			["Key"] = "GUILD_BACKGROUND_IMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948306176'),
			["StringValueList"] = {},
		},
		["GUILD_MASCOT_NAME"] = {
			["Key"] = "GUILD_MASCOT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948306432'),
			["StringValueList"] = {},
		},
		["GUILD_MASCOT_NAME_COUNT"] = {
			["Key"] = "GUILD_MASCOT_NAME_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948306688'),
			["StringValueList"] = {},
		},
		["GUILD_INFORMATION_MODIFY"] = {
			["Key"] = "GUILD_INFORMATION_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948299776'),
			["StringValueList"] = {},
		},
		["GUILD_TYPE"] = {
			["Key"] = "GUILD_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948307200'),
			["StringValueList"] = {},
		},
		["GUILD_NAME_SHOW"] = {
			["Key"] = "GUILD_NAME_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948307456'),
			["StringValueList"] = {},
		},
		["GUILD_DEC"] = {
			["Key"] = "GUILD_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948307712'),
			["StringValueList"] = {},
		},
		["GUILD_RESET"] = {
			["Key"] = "GUILD_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18212003515392'),
			["StringValueList"] = {},
		},
		["GUILD_SET_CONFIRM"] = {
			["Key"] = "GUILD_SET_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948308224'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE"] = {
			["Key"] = "GUILD_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206467840'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE_ENG"] = {
			["Key"] = "GUILD_BATTLE_ENG",
			["StringValue"] = "Battle",
			["StringValueList"] = {},
		},
		["GUILD_FRIEND"] = {
			["Key"] = "GUILD_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36353408504832'),
			["StringValueList"] = {},
		},
		["GUILD_FRIEND_ENG"] = {
			["Key"] = "GUILD_FRIEND_ENG",
			["StringValue"] = "Social",
			["StringValueList"] = {},
		},
		["GUILD_EASY"] = {
			["Key"] = "GUILD_EASY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["StringValueList"] = {},
		},
		["GUILD_EASY_ENG"] = {
			["Key"] = "GUILD_EASY_ENG",
			["StringValue"] = "Relax",
			["StringValueList"] = {},
		},
		["GUILD_PRESIDENT"] = {
			["Key"] = "GUILD_PRESIDENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948310016'),
			["StringValueList"] = {},
		},
		["GUILD_TOTAL"] = {
			["Key"] = "GUILD_TOTAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948310272'),
			["StringValueList"] = {},
		},
		["GUILD_POSITION_MANAGE"] = {
			["Key"] = "GUILD_POSITION_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38759663934464'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_SHOW_VACANCY"] = {
			["Key"] = "GUILD_LIST_SHOW_VACANCY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948310784'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_NAME"] = {
			["Key"] = "GUILD_MEMBER_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948311040'),
			["StringValueList"] = {},
		},
		["GUILD_TYPE_SHORT"] = {
			["Key"] = "GUILD_TYPE_SHORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948311296'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE_DESC"] = {
			["Key"] = "GUILD_BATTLE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948311552'),
			["StringValueList"] = {},
		},
		["GUILD_FRIEND_DESC"] = {
			["Key"] = "GUILD_FRIEND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948311808'),
			["StringValueList"] = {},
		},
		["GUILD_EASY_DESC"] = {
			["Key"] = "GUILD_EASY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948312064'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_NUMBER"] = {
			["Key"] = "GUILD_RESPONSE_NUMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948312320'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_PREVIEW"] = {
			["Key"] = "GUILD_BUILD_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948312576'),
			["StringValueList"] = {},
		},
		["GUILD_CREATED_CONDITION"] = {
			["Key"] = "GUILD_CREATED_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948312832'),
			["StringValueList"] = {},
		},
		["GUILD_NUM_HIGHLIGHT"] = {
			["Key"] = "GUILD_NUM_HIGHLIGHT",
			["StringValue"] = "<Title_Light>%d</>",
			["StringValueList"] = {},
		},
		["GUILD_NAME_INPUT"] = {
			["Key"] = "GUILD_NAME_INPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948313344'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_SEARCH"] = {
			["Key"] = "GUILD_MEMBER_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948313600'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_MASCOT"] = {
			["Key"] = "GUILD_RENAME_MASCOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948313856'),
			["StringValueList"] = {},
		},
		["GUILD_SEND_MAIL"] = {
			["Key"] = "GUILD_SEND_MAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948262912'),
			["StringValueList"] = {},
		},
		["GUILD_BACKGROUND_MODIFY"] = {
			["Key"] = "GUILD_BACKGROUND_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948314368'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_ALREADY"] = {
			["Key"] = "GUILD_SIGNIN_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948314624'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_1"] = {
			["Key"] = "GUILD_GROUP_NAME_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948314880'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_2"] = {
			["Key"] = "GUILD_GROUP_NAME_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948315136'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_3"] = {
			["Key"] = "GUILD_GROUP_NAME_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948315392'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_4"] = {
			["Key"] = "GUILD_GROUP_NAME_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948315648'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_5"] = {
			["Key"] = "GUILD_GROUP_NAME_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948315904'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_6"] = {
			["Key"] = "GUILD_GROUP_NAME_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948316160'),
			["StringValueList"] = {},
		},
		["CLUB_SEARCH_BOX_TXT"] = {
			["Key"] = "CLUB_SEARCH_BOX_TXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948316416'),
			["StringValueList"] = {},
		},
		["CLUB_INVITE_FRIENDS"] = {
			["Key"] = "CLUB_INVITE_FRIENDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337289216'),
			["StringValueList"] = {},
		},
		["CLUB_BADGE"] = {
			["Key"] = "CLUB_BADGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948316928'),
			["StringValueList"] = {},
		},
		["GROUP_NAME_SET"] = {
			["Key"] = "GROUP_NAME_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948317184'),
			["StringValueList"] = {},
		},
		["GROUP_CONTROL_SET"] = {
			["Key"] = "GROUP_CONTROL_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948317440'),
			["StringValueList"] = {},
		},
		["GUID_CHAT_RESPONSE_PROG"] = {
			["Key"] = "GUID_CHAT_RESPONSE_PROG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948317696'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_CLICK"] = {
			["Key"] = "GUID_RESPONSE_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948317952'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSED"] = {
			["Key"] = "GUID_RESPONSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337285120'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_LEFT_TIME_INFO"] = {
			["Key"] = "GUID_RESPONSE_LEFT_TIME_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948318464'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_LEFT_TIME"] = {
			["Key"] = "GUID_RESPONSE_LEFT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56213337283328'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_CHECK"] = {
			["Key"] = "GUID_RESPONSE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32300569990656'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_JOIN"] = {
			["Key"] = "GUID_RESPONSE_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58275190027008'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_OUTOFDATE"] = {
			["Key"] = "GUID_RESPONSE_OUTOFDATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948020992'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_NONE"] = {
			["Key"] = "GUID_RESPONSE_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947752704'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_VISIT"] = {
			["Key"] = "GUID_SHOW_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948320000'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_DAYTIME_LIVE_NUM"] = {
			["Key"] = "GUID_SHOW_DAYTIME_LIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948320256'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_NIGHTTIME_LIVE_NUM"] = {
			["Key"] = "GUID_SHOW_NIGHTTIME_LIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948320512'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_PROFESSION_NUM"] = {
			["Key"] = "GUID_SHOW_PROFESSION_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948320768'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_NUMER"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_NUMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948321024'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_PROGRESS"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948321280'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_TIME"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948321536'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_WORLD_RECRUITMENT"] = {
			["Key"] = "GUID_SHOW_WORLD_RECRUITMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948321792'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RECRUITMENT_TIME"] = {
			["Key"] = "GUID_SHOW_RECRUITMENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948322048'),
			["StringValueList"] = {},
		},
		["ARENA3V3_TITLE"] = {
			["Key"] = "ARENA3V3_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520497152'),
			["StringValueList"] = {},
		},
		["ARENA3V3_CREATE_TEAM"] = {
			["Key"] = "ARENA3V3_CREATE_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948028160'),
			["StringValueList"] = {},
		},
		["ARENA3V3_START_MATCH"] = {
			["Key"] = "ARENA3V3_START_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54701777227008'),
			["StringValueList"] = {},
		},
		["ARENA3V3_QUICK_TEAM"] = {
			["Key"] = "ARENA3V3_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26183462816256'),
			["StringValueList"] = {},
		},
		["ARENA3V3_MARTCHING"] = {
			["Key"] = "ARENA3V3_MARTCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948029952'),
			["StringValueList"] = {},
		},
		["ARENA3V3_CONFIRM_READY"] = {
			["Key"] = "ARENA3V3_CONFIRM_READY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948323584'),
			["StringValueList"] = {},
		},
		["SHOW_PROPERTY_EQUIP_CHANGE"] = {
			["Key"] = "SHOW_PROPERTY_EQUIP_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948323840'),
			["StringValueList"] = {},
		},
		["ACHIEVEMENT_LEVEL_BAR_TITLE"] = {
			["Key"] = "ACHIEVEMENT_LEVEL_BAR_TITLE",
			["StringValue"] = "%s<AchieveTileSmall>%s</>",
			["StringValueList"] = {},
		},
		["CHATROOM_BAN_TIPS"] = {
			["Key"] = "CHATROOM_BAN_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924493312'),
			["StringValueList"] = {},
		},
		["CHATROOM_BLACKLIST_TIPS"] = {
			["Key"] = "CHATROOM_BLACKLIST_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924493568'),
			["StringValueList"] = {},
		},
		["PVP_STATS_RANK_POINT_CHANGE"] = {
			["Key"] = "PVP_STATS_RANK_POINT_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948324864'),
			["StringValueList"] = {},
		},
		["LOCK_TARGET_DISTANCE"] = {
			["Key"] = "LOCK_TARGET_DISTANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948325120'),
			["StringValueList"] = {},
		},
		["WEATHER_HUMIDITY"] = {
			["Key"] = "WEATHER_HUMIDITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948325376'),
			["StringValueList"] = {},
		},
		["WEATHER_FOG"] = {
			["Key"] = "WEATHER_FOG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948325632'),
			["StringValueList"] = {},
		},
		["WEATHER_LIGHT"] = {
			["Key"] = "WEATHER_LIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948325888'),
			["StringValueList"] = {},
		},
		["WEATHER_CLOUD"] = {
			["Key"] = "WEATHER_CLOUD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948326144'),
			["StringValueList"] = {},
		},
		["WEATHER_MOON_PHASE"] = {
			["Key"] = "WEATHER_MOON_PHASE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216761857'), Game.TableDataManager:GetLangStr('str_54839216761858'), Game.TableDataManager:GetLangStr('str_54839216761859'), Game.TableDataManager:GetLangStr('str_54839216761860'), Game.TableDataManager:GetLangStr('str_54839216761861'), Game.TableDataManager:GetLangStr('str_54839216761862'), Game.TableDataManager:GetLangStr('str_54839216761863'), Game.TableDataManager:GetLangStr('str_54839216761864')},
		},
		["WEATHER_CLOCK_NEXT_TIME"] = {
			["Key"] = "WEATHER_CLOCK_NEXT_TIME",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54839216762113'), Game.TableDataManager:GetLangStr('str_54839216762114'), Game.TableDataManager:GetLangStr('str_54839216762115'), Game.TableDataManager:GetLangStr('str_54839216762116')},
		},
		["WEATHER_BLOOD_MOON"] = {
			["Key"] = "WEATHER_BLOOD_MOON",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_12439567554304'), Game.TableDataManager:GetLangStr('str_54839216762370')},
		},
		["JOURNEY"] = {
			["Key"] = "JOURNEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948327168'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_UNLOCK_LEVEL"] = {
			["Key"] = "MANOR_BUILD_UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_756987987712'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_ITEM_HOLDMAX"] = {
			["Key"] = "MANOR_BUILD_ITEM_HOLDMAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948327680'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_UNLOCK_ITEM_NAME"] = {
			["Key"] = "MANOR_BUILD_UNLOCK_ITEM_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948327936'),
			["StringValueList"] = {},
		},
		["RANK_RANK_NO"] = {
			["Key"] = "RANK_RANK_NO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948328192'),
			["StringValueList"] = {},
		},
		["RANK_NOT_IN_RANK"] = {
			["Key"] = "RANK_NOT_IN_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948328448'),
			["StringValueList"] = {},
		},
		["RANK_NAME"] = {
			["Key"] = "RANK_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568832'),
			["StringValueList"] = {},
		},
		["RANK_SETTLE_TIME"] = {
			["Key"] = "RANK_SETTLE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948328960'),
			["StringValueList"] = {},
		},
		["RANK_SEX_0"] = {
			["Key"] = "RANK_SEX_0",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781120'),
			["StringValueList"] = {},
		},
		["RANK_SEX_1"] = {
			["Key"] = "RANK_SEX_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947781376'),
			["StringValueList"] = {},
		},
		["RANK_ONLY_FRIEND"] = {
			["Key"] = "RANK_ONLY_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948329728'),
			["StringValueList"] = {},
		},
		["RANK_REWARD_PREVIEW"] = {
			["Key"] = "RANK_REWARD_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948329984'),
			["StringValueList"] = {},
		},
		["RANK_SERVER_DESC"] = {
			["Key"] = "RANK_SERVER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948330240'),
			["StringValueList"] = {},
		},
		["RANK_CITY_DESC"] = {
			["Key"] = "RANK_CITY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948330496'),
			["StringValueList"] = {},
		},
		["RANK_ALL_PROFESSION"] = {
			["Key"] = "RANK_ALL_PROFESSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948330752'),
			["StringValueList"] = {},
		},
		["MANOR_AREASIZE"] = {
			["Key"] = "MANOR_AREASIZE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948331008'),
			["StringValueList"] = {},
		},
		["MANOR_BUILDING_CNT"] = {
			["Key"] = "MANOR_BUILDING_CNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948331264'),
			["StringValueList"] = {},
		},
		["MANOR_BUILDING_HEIGHT"] = {
			["Key"] = "MANOR_BUILDING_HEIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948325120'),
			["StringValueList"] = {},
		},
		["HUD_SKILL_BRIEFDESCRIPTION"] = {
			["Key"] = "HUD_SKILL_BRIEFDESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948331776'),
			["StringValueList"] = {},
		},
		["HUD_SKILL_DESCRIPTION"] = {
			["Key"] = "HUD_SKILL_DESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948332032'),
			["StringValueList"] = {},
		},
		["MANOR_TOTAL_SCORE"] = {
			["Key"] = "MANOR_TOTAL_SCORE",
			["StringValue"] = "<Green>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_COIN_LIMIT"] = {
			["Key"] = "MANOR_COIN_LIMIT",
			["StringValue"] = "<Moleculus>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_CURRENCY_ACQUIRED_THIS_WEEK"] = {
			["Key"] = "MANOR_CURRENCY_ACQUIRED_THIS_WEEK",
			["StringValue"] = "<Moleculus>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["SUBMIT_OPTIONAL_TEXT_FORMAT"] = {
			["Key"] = "SUBMIT_OPTIONAL_TEXT_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948333056'),
			["StringValueList"] = {},
		},
		["SUBMIT_DISABLED_PROGRESS_FORMAT"] = {
			["Key"] = "SUBMIT_DISABLED_PROGRESS_FORMAT",
			["StringValue"] = "<T_Disable>%d</>/%d",
			["StringValueList"] = {},
		},
		["SUBMIT_ENABLED_PROGRESS_FORMAT"] = {
			["Key"] = "SUBMIT_ENABLED_PROGRESS_FORMAT",
			["StringValue"] = "%d/%d",
			["StringValueList"] = {},
		},
		["MANOR_ROLE_LEVEL_LIMIT"] = {
			["Key"] = "MANOR_ROLE_LEVEL_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61024774446848'),
			["StringValueList"] = {},
		},
		["MANOR_TOTAL_SCORE_LIMIT"] = {
			["Key"] = "MANOR_TOTAL_SCORE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836363264'),
			["StringValueList"] = {},
		},
		["MANOR_PUT_SCORE_LIMIT"] = {
			["Key"] = "MANOR_PUT_SCORE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34910836362240'),
			["StringValueList"] = {},
		},
		["MANOR_WORKSHOP_LIMIT"] = {
			["Key"] = "MANOR_WORKSHOP_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948334592'),
			["StringValueList"] = {},
		},
		["MANOR_HOME_COIN_LIMIT"] = {
			["Key"] = "MANOR_HOME_COIN_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948334848'),
			["StringValueList"] = {},
		},
		["MANOR_SATISFIED"] = {
			["Key"] = "MANOR_SATISFIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948335104'),
			["StringValueList"] = {},
		},
		["MANOR_NOTSATISFIED"] = {
			["Key"] = "MANOR_NOTSATISFIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948335360'),
			["StringValueList"] = {},
		},
		["MANOR_SATISFIED_VALUE"] = {
			["Key"] = "MANOR_SATISFIED_VALUE",
			["StringValue"] = "<UpgradGreen>%s</><Default>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_NOTSATISFIED_VALUE"] = {
			["Key"] = "MANOR_NOTSATISFIED_VALUE",
			["StringValue"] = "<UpgradRed>%s</><Default>/%s</>",
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CREATE"] = {
			["Key"] = "CHAMPION_OP_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948336128'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_QUIT"] = {
			["Key"] = "CHAMPION_OP_QUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493933312'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_DISBAND"] = {
			["Key"] = "CHAMPION_OP_DISBAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493933568'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_JOIN"] = {
			["Key"] = "CHAMPION_OP_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948336896'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_KICK"] = {
			["Key"] = "CHAMPION_OP_KICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493933824'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_SIGN_UP"] = {
			["Key"] = "CHAMPION_OP_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948337408'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CHANGE_NAME"] = {
			["Key"] = "CHAMPION_OP_CHANGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948337664'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CHANGE_LEADER"] = {
			["Key"] = "CHAMPION_OP_CHANGE_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61093493929728'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_CLOSE"] = {
			["Key"] = "CHAMPION_STAGE_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948338176'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_SIGN_UP"] = {
			["Key"] = "CHAMPION_STAGE_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948338432'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_SIGN_UP_LOCK"] = {
			["Key"] = "CHAMPION_STAGE_SIGN_UP_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948338688'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_PREPARE"] = {
			["Key"] = "CHAMPION_STAGE_PREPARE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948338944'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_BATTLE"] = {
			["Key"] = "CHAMPION_STAGE_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948339200'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_CALCULATE"] = {
			["Key"] = "CHAMPION_STAGE_CALCULATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948339456'),
			["StringValueList"] = {},
		},
		["MANOR_SOCIAL_TAB_FRIEND"] = {
			["Key"] = "MANOR_SOCIAL_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_26114206475520'),
			["StringValueList"] = {},
		},
		["MANOR_SOCIAL_TAB_RECOMMENDATION"] = {
			["Key"] = "MANOR_SOCIAL_TAB_RECOMMENDATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947789312'),
			["StringValueList"] = {},
		},
		["MANOR_VISIT_HOME"] = {
			["Key"] = "MANOR_VISIT_HOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948340224'),
			["StringValueList"] = {},
		},
		["CHAMPION_TROOP_STATUS_NOT_SIGN"] = {
			["Key"] = "CHAMPION_TROOP_STATUS_NOT_SIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948340480'),
			["StringValueList"] = {},
		},
		["CHAMPION_TROOP_STATUS_SIGN_UP"] = {
			["Key"] = "CHAMPION_TROOP_STATUS_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948340736'),
			["StringValueList"] = {},
		},
		["SealMaterial_MainInterface_Title"] = {
			["Key"] = "SealMaterial_MainInterface_Title",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_58343641060864'),
			["StringValueList"] = {},
		},
		["SealMaterial_Label1_Text"] = {
			["Key"] = "SealMaterial_Label1_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56625654139136'),
			["StringValueList"] = {},
		},
		["SealMaterial_Label2_Text"] = {
			["Key"] = "SealMaterial_Label2_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948341504'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type1_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type1_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948341760'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type2_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type2_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948342016'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type3_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type3_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948342272'),
			["StringValueList"] = {},
		},
		["SealMaterial_Unassembled_Text"] = {
			["Key"] = "SealMaterial_Unassembled_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948342528'),
			["StringValueList"] = {},
		},
		["SealMaterial_Assembly_Button"] = {
			["Key"] = "SealMaterial_Assembly_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948082432'),
			["StringValueList"] = {},
		},
		["SealMaterial_Replace_Button"] = {
			["Key"] = "SealMaterial_Replace_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947911936'),
			["StringValueList"] = {},
		},
		["SealMaterial_Remove_Button"] = {
			["Key"] = "SealMaterial_Remove_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31612033045760'),
			["StringValueList"] = {},
		},
		["SealMaterial_Extraordinary_abilities"] = {
			["Key"] = "SealMaterial_Extraordinary_abilities",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_57862873192704'),
			["StringValueList"] = {},
		},
		["SealMaterial_Negative_Effects"] = {
			["Key"] = "SealMaterial_Negative_Effects",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838947928064'),
			["StringValueList"] = {},
		},
		["SealMaterial_Extraordinary_Substance"] = {
			["Key"] = "SealMaterial_Extraordinary_Substance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948344064'),
			["StringValueList"] = {},
		},
		["SealMaterial_Synthetic_Entrance"] = {
			["Key"] = "SealMaterial_Synthetic_Entrance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948344320'),
			["StringValueList"] = {},
		},
		["SealMaterial_Backpack_Entrance"] = {
			["Key"] = "SealMaterial_Backpack_Entrance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948344064'),
			["StringValueList"] = {},
		},
		["MANOR_COIN_LIMIT_UPGRADE"] = {
			["Key"] = "MANOR_COIN_LIMIT_UPGRADE",
			["StringValue"] = "<TitleDefault>%s+</><TitleHighlight>%s</>",
			["StringValueList"] = {},
		},
		["CutsceneText_CotardDefeat"] = {
			["Key"] = "CutsceneText_CotardDefeat",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948345088'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R1"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948345344'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R2"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948345600'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R3"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948345856'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R4"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948346112'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R1"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948346368'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R2"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948346624'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R3"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948346880'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R4"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948347136'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_GROUP_TEXT"] = {
			["Key"] = "PVP_CHAMPION_GROUP_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948347392'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_GROUP_TEXT_WITH_ROUND"] = {
			["Key"] = "PVP_CHAMPION_GROUP_TEXT_WITH_ROUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948347648'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_DEFEAT"] = {
			["Key"] = "PVP_CHAMPION_STATS_DEFEAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948347904'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_SURVIVE"] = {
			["Key"] = "PVP_CHAMPION_STATS_SURVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948348160'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_DAMAGE"] = {
			["Key"] = "PVP_CHAMPION_STATS_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948348416'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_UP"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948348672'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_AWAIT_SIGN"] = {
			["Key"] = "PVP_CHAMPION_TEAM_AWAIT_SIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948348928'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_GAME"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948349184'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_1"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948349440'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_2"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948349696'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_3"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948349952'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_4"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948347392'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TITLE"] = {
			["Key"] = "PVP_CHAMPION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32849520517632'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_SIGN_TIPS"] = {
			["Key"] = "PVP_CHAMPION_SIGN_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948350720'),
			["StringValueList"] = {},
		},
		["REMOVE_DEADLOCK_READING_TEXT"] = {
			["Key"] = "REMOVE_DEADLOCK_READING_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54838948350976'),
			["StringValueList"] = {},
		},
	},
}

return TopData
