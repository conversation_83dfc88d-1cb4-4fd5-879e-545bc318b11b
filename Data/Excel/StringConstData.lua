--
-- 表名: $StringConst.xlsx  页名：$LangString_语言文本
--

local TopData = {
	data = {
		["CONFIRM"] = {
			["Key"] = "CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["StringValueList"] = {},
		},
		["CANCLE"] = {
			["Key"] = "CANCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["START"] = {
			["Key"] = "START",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789313536'),
			["StringValueList"] = {},
		},
		["RETRACT"] = {
			["Key"] = "RETRACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789313792'),
			["StringValueList"] = {},
		},
		["EXIT"] = {
			["Key"] = "EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["StringValueList"] = {},
		},
		["PATK"] = {
			["Key"] = "PATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_17319992493569'),
			["StringValueList"] = {},
		},
		["MATK"] = {
			["Key"] = "MATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_17319992494081'),
			["StringValueList"] = {},
		},
		["PDEF"] = {
			["Key"] = "PDEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789314816'),
			["StringValueList"] = {},
		},
		["MDEF"] = {
			["Key"] = "MDEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789315072'),
			["StringValueList"] = {},
		},
		["HPMAX"] = {
			["Key"] = "HPMAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789315328'),
			["StringValueList"] = {},
		},
		["MINATK"] = {
			["Key"] = "MINATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789315584'),
			["StringValueList"] = {},
		},
		["MAXATK"] = {
			["Key"] = "MAXATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789315840'),
			["StringValueList"] = {},
		},
		["DAY"] = {
			["Key"] = "DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316096'),
			["StringValueList"] = {},
		},
		["HOUR"] = {
			["Key"] = "HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316352'),
			["StringValueList"] = {},
		},
		["MINUTE"] = {
			["Key"] = "MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316608'),
			["StringValueList"] = {},
		},
		["SECOND"] = {
			["Key"] = "SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316864'),
			["StringValueList"] = {},
		},
		["COMMON"] = {
			["Key"] = "COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789317120'),
			["StringValueList"] = {},
		},
		["HP"] = {
			["Key"] = "HP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["StringValueList"] = {},
		},
		["BASE"] = {
			["Key"] = "BASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789317632'),
			["StringValueList"] = {},
		},
		["ATK"] = {
			["Key"] = "ATK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789317888'),
			["StringValueList"] = {},
		},
		["DEF"] = {
			["Key"] = "DEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
			["StringValueList"] = {},
		},
		["COLON"] = {
			["Key"] = "COLON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318400'),
			["StringValueList"] = {},
		},
		["DAMAGE"] = {
			["Key"] = "DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318656'),
			["StringValueList"] = {},
		},
		["HEAL"] = {
			["Key"] = "HEAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318912'),
			["StringValueList"] = {},
		},
		["WEAPON"] = {
			["Key"] = "WEAPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_17386832922112'),
			["StringValueList"] = {},
		},
		["ENTER_GAME"] = {
			["Key"] = "ENTER_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789319424'),
			["StringValueList"] = {},
		},
		["SEQUENCE"] = {
			["Key"] = "SEQUENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789319680'),
			["StringValueList"] = {},
		},
		["SEQUENCE2"] = {
			["Key"] = "SEQUENCE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789319936'),
			["StringValueList"] = {},
		},
		["COUNT"] = {
			["Key"] = "COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789320192'),
			["StringValueList"] = {},
		},
		["FreeOB"] = {
			["Key"] = "FreeOB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789320448'),
			["StringValueList"] = {},
		},
		["PURPLE"] = {
			["Key"] = "PURPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789320704'),
			["StringValueList"] = {},
		},
		["BLUE"] = {
			["Key"] = "BLUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789320960'),
			["StringValueList"] = {},
		},
		["ORANGE"] = {
			["Key"] = "ORANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789321216'),
			["StringValueList"] = {},
		},
		["GREEN"] = {
			["Key"] = "GREEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789321472'),
			["StringValueList"] = {},
		},
		["YELLOW"] = {
			["Key"] = "YELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789321728'),
			["StringValueList"] = {},
		},
		["WHITE"] = {
			["Key"] = "WHITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789321984'),
			["StringValueList"] = {},
		},
		["GREY"] = {
			["Key"] = "GREY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322240'),
			["StringValueList"] = {},
		},
		["NONE"] = {
			["Key"] = "NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322496'),
			["StringValueList"] = {},
		},
		["RECONNECTING"] = {
			["Key"] = "RECONNECTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322752'),
			["StringValueList"] = {},
		},
		["SERVER_CONNECTING"] = {
			["Key"] = "SERVER_CONNECTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789323008'),
			["StringValueList"] = {},
		},
		["FOLLOWING"] = {
			["Key"] = "FOLLOWING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789323264'),
			["StringValueList"] = {},
		},
		["HINT"] = {
			["Key"] = "HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["StringValueList"] = {},
		},
		["SUNDAY"] = {
			["Key"] = "SUNDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789323776'),
			["StringValueList"] = {},
		},
		["MONDAY"] = {
			["Key"] = "MONDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789324032'),
			["StringValueList"] = {},
		},
		["TUESDAY"] = {
			["Key"] = "TUESDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789324288'),
			["StringValueList"] = {},
		},
		["WEDNESDAY"] = {
			["Key"] = "WEDNESDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789324544'),
			["StringValueList"] = {},
		},
		["THURSDAY"] = {
			["Key"] = "THURSDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789324800'),
			["StringValueList"] = {},
		},
		["FRIDAY"] = {
			["Key"] = "FRIDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325056'),
			["StringValueList"] = {},
		},
		["SATURDAY"] = {
			["Key"] = "SATURDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325312'),
			["StringValueList"] = {},
		},
		["YESTERDAY"] = {
			["Key"] = "YESTERDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325568'),
			["StringValueList"] = {},
		},
		["SEND"] = {
			["Key"] = "SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325824'),
			["StringValueList"] = {},
		},
		["YOU"] = {
			["Key"] = "YOU",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789326080'),
			["StringValueList"] = {},
		},
		["LOADING"] = {
			["Key"] = "LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789326336'),
			["StringValueList"] = {},
		},
		["OBDungeon"] = {
			["Key"] = "OBDungeon",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789326592'),
			["StringValueList"] = {},
		},
		["EXP"] = {
			["Key"] = "EXP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789326848'),
			["StringValueList"] = {},
		},
		["REJECT"] = {
			["Key"] = "REJECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327104'),
			["StringValueList"] = {},
		},
		["NEWBIEPAGE"] = {
			["Key"] = "NEWBIEPAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327360'),
			["StringValueList"] = {},
		},
		["NEWBIETITLE"] = {
			["Key"] = "NEWBIETITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327616'),
			["StringValueList"] = {},
		},
		["NEWBIETARGETLOCK"] = {
			["Key"] = "NEWBIETARGETLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327872'),
			["StringValueList"] = {},
		},
		["TEN_THOUSAND"] = {
			["Key"] = "TEN_THOUSAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789328128'),
			["StringValueList"] = {},
		},
		["PLEASE_WAIT"] = {
			["Key"] = "PLEASE_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789328384'),
			["StringValueList"] = {},
		},
		["TOPUP"] = {
			["Key"] = "TOPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789328640'),
			["StringValueList"] = {},
		},
		["CONTINUE"] = {
			["Key"] = "CONTINUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789328896'),
			["StringValueList"] = {},
		},
		["CLOSE"] = {
			["Key"] = "CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789329152'),
			["StringValueList"] = {},
		},
		["YEARPAST"] = {
			["Key"] = "YEARPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789329408'),
			["StringValueList"] = {},
		},
		["MONTHPAST"] = {
			["Key"] = "MONTHPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789329664'),
			["StringValueList"] = {},
		},
		["WEEKPAST"] = {
			["Key"] = "WEEKPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789329920'),
			["StringValueList"] = {},
		},
		["DAYPAST"] = {
			["Key"] = "DAYPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789330176'),
			["StringValueList"] = {},
		},
		["HOURPAST"] = {
			["Key"] = "HOURPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789330432'),
			["StringValueList"] = {},
		},
		["MINUTEPAST"] = {
			["Key"] = "MINUTEPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789330688'),
			["StringValueList"] = {},
		},
		["SECONDPAST"] = {
			["Key"] = "SECONDPAST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789330944'),
			["StringValueList"] = {},
		},
		["ONEMINUTE"] = {
			["Key"] = "ONEMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789331200'),
			["StringValueList"] = {},
		},
		["JUST"] = {
			["Key"] = "JUST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789331456'),
			["StringValueList"] = {},
		},
		["OPEN"] = {
			["Key"] = "OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607170816'),
			["StringValueList"] = {},
		},
		["WEEKNESS"] = {
			["Key"] = "WEEKNESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789331968'),
			["StringValueList"] = {},
		},
		["REVERT"] = {
			["Key"] = "REVERT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332224'),
			["StringValueList"] = {},
		},
		["IMMUNE"] = {
			["Key"] = "IMMUNE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332480'),
			["StringValueList"] = {},
		},
		["SKIPDIALOGUE"] = {
			["Key"] = "SKIPDIALOGUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332736'),
			["StringValueList"] = {},
		},
		["DIALOGUEAUTOPLAY"] = {
			["Key"] = "DIALOGUEAUTOPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332992'),
			["StringValueList"] = {},
		},
		["HINT_CURRENT_VERSION"] = {
			["Key"] = "HINT_CURRENT_VERSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789333248'),
			["StringValueList"] = {},
		},
		["UNCONTROLLED_STATE"] = {
			["Key"] = "UNCONTROLLED_STATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789333504'),
			["StringValueList"] = {},
		},
		["MEDICINE_SETTING"] = {
			["Key"] = "MEDICINE_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789333760'),
			["StringValueList"] = {},
		},
		["FORTUNE_POPUP"] = {
			["Key"] = "FORTUNE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789334016'),
			["StringValueList"] = {},
		},
		["PVP_3V3_NAME"] = {
			["Key"] = "PVP_3V3_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590208'),
			["StringValueList"] = {},
		},
		["AHUNDREDMILLION"] = {
			["Key"] = "AHUNDREDMILLION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789334528'),
			["StringValueList"] = {},
		},
		["NEWSTICKERTAG"] = {
			["Key"] = "NEWSTICKERTAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789334784'),
			["StringValueList"] = {},
		},
		["DUNGEON_TREASURY"] = {
			["Key"] = "DUNGEON_TREASURY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789335040'),
			["StringValueList"] = {},
		},
		["DUNGEON_CHALLENGE"] = {
			["Key"] = "DUNGEON_CHALLENGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789335296'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD"] = {
			["Key"] = "DUNGEON_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789335552'),
			["StringValueList"] = {},
		},
		["DUNGEON_RANK"] = {
			["Key"] = "DUNGEON_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789335808'),
			["StringValueList"] = {},
		},
		["DUNGEON_OTHER"] = {
			["Key"] = "DUNGEON_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["StringValueList"] = {},
		},
		["FATE_CONTRACT_TITLE"] = {
			["Key"] = "FATE_CONTRACT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789336320'),
			["StringValueList"] = {},
		},
		["FATE_CONTRACT_DESC"] = {
			["Key"] = "FATE_CONTRACT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789336576'),
			["StringValueList"] = {},
		},
		["ME"] = {
			["Key"] = "ME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789336832'),
			["StringValueList"] = {},
		},
		["DIALOGUE_DEFAULT_SKIP_TIPS"] = {
			["Key"] = "DIALOGUE_DEFAULT_SKIP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789337088'),
			["StringValueList"] = {},
		},
		["DIALOGUE_REVIEW_UI_TITLE"] = {
			["Key"] = "DIALOGUE_REVIEW_UI_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328571392'),
			["StringValueList"] = {},
		},
		["SKIP_NEWBIE_GUIDE"] = {
			["Key"] = "SKIP_NEWBIE_GUIDE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789337600'),
			["StringValueList"] = {},
		},
		["DIALOGUE_UNKNOWN_NAME"] = {
			["Key"] = "DIALOGUE_UNKNOWN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789337856'),
			["StringValueList"] = {},
		},
		["DIALOGUE_UNKNOWN_CONTENT"] = {
			["Key"] = "DIALOGUE_UNKNOWN_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789337856'),
			["StringValueList"] = {},
		},
		["ZERO_DESC"] = {
			["Key"] = "ZERO_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789338368'),
			["StringValueList"] = {},
		},
		["ONE_DESC"] = {
			["Key"] = "ONE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789338624'),
			["StringValueList"] = {},
		},
		["TWO_DESC"] = {
			["Key"] = "TWO_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789338880'),
			["StringValueList"] = {},
		},
		["THREE_DESC"] = {
			["Key"] = "THREE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339136'),
			["StringValueList"] = {},
		},
		["FOUR_DESC"] = {
			["Key"] = "FOUR_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339392'),
			["StringValueList"] = {},
		},
		["FIVE_DESC"] = {
			["Key"] = "FIVE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339648'),
			["StringValueList"] = {},
		},
		["SIX_DESC"] = {
			["Key"] = "SIX_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339904'),
			["StringValueList"] = {},
		},
		["SEVEN_DESC"] = {
			["Key"] = "SEVEN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789340160'),
			["StringValueList"] = {},
		},
		["EIGHT_DESC"] = {
			["Key"] = "EIGHT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789340416'),
			["StringValueList"] = {},
		},
		["NIN_DESC"] = {
			["Key"] = "NIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789340672'),
			["StringValueList"] = {},
		},
		["RANKING_SPECIFIC_TIME"] = {
			["Key"] = "RANKING_SPECIFIC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789340928'),
			["StringValueList"] = {},
		},
		["LEFTDAY"] = {
			["Key"] = "LEFTDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789341184'),
			["StringValueList"] = {},
		},
		["LEFTHOUR"] = {
			["Key"] = "LEFTHOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789341440'),
			["StringValueList"] = {},
		},
		["LEFTMINUTE"] = {
			["Key"] = "LEFTMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789341696'),
			["StringValueList"] = {},
		},
		["LEFTLASTMINUTE"] = {
			["Key"] = "LEFTLASTMINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789341952'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_TITLE"] = {
			["Key"] = "TITLESYSTEM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342208'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_HONORIFIC"] = {
			["Key"] = "TITLESYSTEM_HONORIFIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342464'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_WEAR"] = {
			["Key"] = "TITLESYSTEM_WEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342720'),
			["StringValueList"] = {},
		},
		["TITLESYSTEM_LOCK"] = {
			["Key"] = "TITLESYSTEM_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342976'),
			["StringValueList"] = {},
		},
		["HUD_DISPLAY_FUNCTION_BUTTON"] = {
			["Key"] = "HUD_DISPLAY_FUNCTION_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789343232'),
			["StringValueList"] = {},
		},
		["LOSTSIGNALSTRING"] = {
			["Key"] = "LOSTSIGNALSTRING",
			["StringValue"] = "999ms",
			["StringValueList"] = {},
		},
		["ROLESELECT_SELECT"] = {
			["Key"] = "ROLESELECT_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789343744'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_ROLE"] = {
			["Key"] = "ROLESELECT_CREATE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789344000'),
			["StringValueList"] = {},
		},
		["ROLESELECT_ENTER_GAME"] = {
			["Key"] = "ROLESELECT_ENTER_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789319424'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE"] = {
			["Key"] = "ROLESELECT_CREATE_FACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789344512'),
			["StringValueList"] = {},
		},
		["ROLECREATE_DELETE_ROLE"] = {
			["Key"] = "ROLECREATE_DELETE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789344768'),
			["StringValueList"] = {},
		},
		["ROLESELECT_DELETE_ROLE_CONFIRM"] = {
			["Key"] = "ROLESELECT_DELETE_ROLE_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789345024'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_DELETE_ROLE"] = {
			["Key"] = "ROLESELECT_INPUT_DELETE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789345280'),
			["StringValueList"] = {},
		},
		["ROLESELECT_DELETE_ROLE_WARNING"] = {
			["Key"] = "ROLESELECT_DELETE_ROLE_WARNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789345536'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_1"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789345792'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_2"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789346048'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_3"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789346304'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_4"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789346560'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_5"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789346816'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_6"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789347072'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_7"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789347328'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_8"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_8",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789347584'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_9"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_9",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789347840'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_10"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_10",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789348096'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_11"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_11",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789348352'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_12"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_12",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789348608'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_13"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_13",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789348864'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE_FACE_14"] = {
			["Key"] = "ROLESELECT_CREATE_FACE_14",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789349120'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_NAME_TITLE"] = {
			["Key"] = "ROLESELECT_INPUT_NAME_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789349376'),
			["StringValueList"] = {},
		},
		["ROLESELECT_INPUT_NAME"] = {
			["Key"] = "ROLESELECT_INPUT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789349632'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CONFIRM_NAME"] = {
			["Key"] = "ROLESELECT_CONFIRM_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789349888'),
			["StringValueList"] = {},
		},
		["ROLESELECT_LEVEL"] = {
			["Key"] = "ROLESELECT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178852096'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CREATE"] = {
			["Key"] = "ROLESELECT_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789350400'),
			["StringValueList"] = {},
		},
		["ROLESELECT_CHOOSE"] = {
			["Key"] = "ROLESELECT_CHOOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789350656'),
			["StringValueList"] = {},
		},
		["ROLESELECT_MALE"] = {
			["Key"] = "ROLESELECT_MALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789350912'),
			["StringValueList"] = {},
		},
		["ROLESELECT_FEMALE"] = {
			["Key"] = "ROLESELECT_FEMALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789351168'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_SELF"] = {
			["Key"] = "ANNOUNCEMENT_SELF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789351424'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_TITLE_LOADING"] = {
			["Key"] = "ANNOUNCEMENT_TITLE_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789351680'),
			["StringValueList"] = {},
		},
		["ANNOUNCEMENT_CONTENT_LOADING"] = {
			["Key"] = "ANNOUNCEMENT_CONTENT_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789351936'),
			["StringValueList"] = {},
		},
		["HUD_IMMUNITY"] = {
			["Key"] = "HUD_IMMUNITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332480'),
			["StringValueList"] = {},
		},
		["HUD_FOLLOW"] = {
			["Key"] = "HUD_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789352448'),
			["StringValueList"] = {},
		},
		["HUD_CANCLE_FOLLOW"] = {
			["Key"] = "HUD_CANCLE_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789352704'),
			["StringValueList"] = {},
		},
		["HUD_TASK_COMPLETE"] = {
			["Key"] = "HUD_TASK_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789352960'),
			["StringValueList"] = {},
		},
		["HUD_REVIVE"] = {
			["Key"] = "HUD_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353216'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_DUNGEON"] = {
			["Key"] = "HUD_EXIT_DUNGEON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353472'),
			["StringValueList"] = {},
		},
		["HUD_RETURN_DUNGEON"] = {
			["Key"] = "HUD_RETURN_DUNGEON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55869739894784'),
			["StringValueList"] = {},
		},
		["HUD_TEAMER"] = {
			["Key"] = "HUD_TEAMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353984'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_GUILD"] = {
			["Key"] = "HUD_EXIT_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789354240'),
			["StringValueList"] = {},
		},
		["HUD_WORLD_BOSS_TITLE"] = {
			["Key"] = "HUD_WORLD_BOSS_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789354496'),
			["StringValueList"] = {},
		},
		["WORLD_BOSS_EXPLAIN"] = {
			["Key"] = "WORLD_BOSS_EXPLAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789354752'),
			["StringValueList"] = {},
		},
		["ACTIVITY_PRE_PUSH"] = {
			["Key"] = "ACTIVITY_PRE_PUSH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355008'),
			["StringValueList"] = {},
		},
		["ACTIVITY_OPEN_PUSH"] = {
			["Key"] = "ACTIVITY_OPEN_PUSH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355264'),
			["StringValueList"] = {},
		},
		["TRANSFERING"] = {
			["Key"] = "TRANSFERING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355520'),
			["StringValueList"] = {},
		},
		["HOTPATCH_DOWNLOAD_MAP"] = {
			["Key"] = "HOTPATCH_DOWNLOAD_MAP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355776'),
			["StringValueList"] = {},
		},
		["HOTPATCH_DOWNLOAD_RES"] = {
			["Key"] = "HOTPATCH_DOWNLOAD_RES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789356032'),
			["StringValueList"] = {},
		},
		["HOTPATCH_SPEED_MB"] = {
			["Key"] = "HOTPATCH_SPEED_MB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789356288'),
			["StringValueList"] = {},
		},
		["HOTPATCH_REMAIN_TIME"] = {
			["Key"] = "HOTPATCH_REMAIN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789356544'),
			["StringValueList"] = {},
		},
		["HOTPATCH_REMAIN_MIN"] = {
			["Key"] = "HOTPATCH_REMAIN_MIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789356800'),
			["StringValueList"] = {},
		},
		["HOTPATCH_SPEED_KB"] = {
			["Key"] = "HOTPATCH_SPEED_KB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789357056'),
			["StringValueList"] = {},
		},
		["DLC_UPDATE_TITLE"] = {
			["Key"] = "DLC_UPDATE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789357312'),
			["StringValueList"] = {},
		},
		["DLC_EXTERN_GROUP"] = {
			["Key"] = "DLC_EXTERN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789357568'),
			["StringValueList"] = {},
		},
		["DLC_CUSTOM_GROUP"] = {
			["Key"] = "DLC_CUSTOM_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789357824'),
			["StringValueList"] = {},
		},
		["DLC_DELETABLE_CHUNKS"] = {
			["Key"] = "DLC_DELETABLE_CHUNKS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789358080'),
			["StringValueList"] = {},
		},
		["DLC_DOWNLOAD"] = {
			["Key"] = "DLC_DOWNLOAD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789358336'),
			["StringValueList"] = {},
		},
		["DLC_DELETE"] = {
			["Key"] = "DLC_DELETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789358592'),
			["StringValueList"] = {},
		},
		["NEW_SERVER"] = {
			["Key"] = "NEW_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789358848'),
			["StringValueList"] = {},
		},
		["RECOMMEND_SERVER"] = {
			["Key"] = "RECOMMEND_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359104'),
			["StringValueList"] = {},
		},
		["SELECT_SERVER_TITLE"] = {
			["Key"] = "SELECT_SERVER_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359360'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_UNOBTRUCTED"] = {
			["Key"] = "SERVER_STATUS_UNOBTRUCTED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359616'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_CROWDED"] = {
			["Key"] = "SERVER_STATUS_CROWDED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359872'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_OVERCROWDED"] = {
			["Key"] = "SERVER_STATUS_OVERCROWDED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789360128'),
			["StringValueList"] = {},
		},
		["SERVER_STATUS_MAINTAINING"] = {
			["Key"] = "SERVER_STATUS_MAINTAINING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789360384'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_NEW"] = {
			["Key"] = "SERVER_TAG_NEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789360640'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_RECOMMEND"] = {
			["Key"] = "SERVER_TAG_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359104'),
			["StringValueList"] = {},
		},
		["SERVER_TAG_HOT"] = {
			["Key"] = "SERVER_TAG_HOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789361152'),
			["StringValueList"] = {},
		},
		["AGE_APPROPRIATE_INDICATION"] = {
			["Key"] = "AGE_APPROPRIATE_INDICATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789361408'),
			["StringValueList"] = {},
		},
		["AGE_APPROPRIATE_TITLE"] = {
			["Key"] = "AGE_APPROPRIATE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789361664'),
			["StringValueList"] = {},
		},
		["ONE_MINUTE_AGO"] = {
			["Key"] = "ONE_MINUTE_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789361920'),
			["StringValueList"] = {},
		},
		["SEVERAL_MINUTES_AGO"] = {
			["Key"] = "SEVERAL_MINUTES_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362176'),
			["StringValueList"] = {},
		},
		["SEVERAL_HOURS_AGO"] = {
			["Key"] = "SEVERAL_HOURS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362432'),
			["StringValueList"] = {},
		},
		["SEVERAL_DAYS_AGO"] = {
			["Key"] = "SEVERAL_DAYS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362688'),
			["StringValueList"] = {},
		},
		["SEVERAL_WEEKS_AGO"] = {
			["Key"] = "SEVERAL_WEEKS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362944'),
			["StringValueList"] = {},
		},
		["SEVERAL_MONTHS_AGO"] = {
			["Key"] = "SEVERAL_MONTHS_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789363200'),
			["StringValueList"] = {},
		},
		["ONE_YEAR_AGO"] = {
			["Key"] = "ONE_YEAR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789363456'),
			["StringValueList"] = {},
		},
		["LOG_IN"] = {
			["Key"] = "LOG_IN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642617344'),
			["StringValueList"] = {},
		},
		["LOG_OUT"] = {
			["Key"] = "LOG_OUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789363968'),
			["StringValueList"] = {},
		},
		["EXIST_ROLE"] = {
			["Key"] = "EXIST_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789364224'),
			["StringValueList"] = {},
		},
		["HEALTH_GAME_HINT"] = {
			["Key"] = "HEALTH_GAME_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789364480'),
			["StringValueList"] = {},
		},
		["ENTER_SERVER_PROGRESS_HINT"] = {
			["Key"] = "ENTER_SERVER_PROGRESS_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789364736'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_IN_QUEUE"] = {
			["Key"] = "SERVER_QUEUE_STATUS_IN_QUEUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789364992'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_NEED_QUEUE"] = {
			["Key"] = "SERVER_QUEUE_STATUS_NEED_QUEUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789365248'),
			["StringValueList"] = {},
		},
		["SERVER_QUEUE_STATUS_CIRCUIT_BREAKER"] = {
			["Key"] = "SERVER_QUEUE_STATUS_CIRCUIT_BREAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789365504'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_PROTECT_TIPS"] = {
			["Key"] = "LOGIN_QUEUE_PROTECT_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789365760'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_TIME_FORMAT"] = {
			["Key"] = "LOGIN_QUEUE_TIME_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789366016'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_RANK_FORMAT"] = {
			["Key"] = "LOGIN_QUEUE_RANK_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789366272'),
			["StringValueList"] = {},
		},
		["LOGIN_QUEUE_DETAIL"] = {
			["Key"] = "LOGIN_QUEUE_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789366528'),
			["StringValueList"] = {},
		},
		["LOGIN_SWITCH_SERVER_TIPS"] = {
			["Key"] = "LOGIN_SWITCH_SERVER_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789366784'),
			["StringValueList"] = {},
		},
		["LOGIN_BEFOREHAND_CREATE_ROLE"] = {
			["Key"] = "LOGIN_BEFOREHAND_CREATE_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789367040'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TITLE_1"] = {
			["Key"] = "RED_PACKET_TITLE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789367296'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_1"] = {
			["Key"] = "RED_PACKET_TAB_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789367552'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_2"] = {
			["Key"] = "RED_PACKET_TAB_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789367808'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TAB_3"] = {
			["Key"] = "RED_PACKET_TAB_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789368064'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_SNED_CHANNAL"] = {
			["Key"] = "RED_PACKET_CHOOSE_SNED_CHANNAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789368320'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_TYPE"] = {
			["Key"] = "RED_PACKET_CHOOSE_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789368576'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TYPE_1"] = {
			["Key"] = "RED_PACKET_TYPE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789368832'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TYPE_2"] = {
			["Key"] = "RED_PACKET_TYPE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789369088'),
			["StringValueList"] = {},
		},
		["RED_PACKET_MONEY_TYPE"] = {
			["Key"] = "RED_PACKET_MONEY_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789369344'),
			["StringValueList"] = {},
		},
		["RED_PACKET_LUCKY"] = {
			["Key"] = "RED_PACKET_LUCKY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789369600'),
			["StringValueList"] = {},
		},
		["RED_PACKET_VOICE"] = {
			["Key"] = "RED_PACKET_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789369856'),
			["StringValueList"] = {},
		},
		["RED_PACKET_PASSWORD"] = {
			["Key"] = "RED_PACKET_PASSWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789370112'),
			["StringValueList"] = {},
		},
		["RED_PACKET_AMOUNT"] = {
			["Key"] = "RED_PACKET_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789370368'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COST"] = {
			["Key"] = "RED_PACKET_COST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789370624'),
			["StringValueList"] = {},
		},
		["RED_PACKET_NUM"] = {
			["Key"] = "RED_PACKET_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908853448960'),
			["StringValueList"] = {},
		},
		["RED_PACKET_PUT_NUM"] = {
			["Key"] = "RED_PACKET_PUT_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789371136'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789371392'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_HINT_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_HINT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789371648'),
			["StringValueList"] = {},
		},
		["RED_PACKET_GIFT_HINT_SECRET_WORD"] = {
			["Key"] = "RED_PACKET_GIFT_HINT_SECRET_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789371904'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COMMEMNT__SECRET_WORD"] = {
			["Key"] = "RED_PACKET_COMMEMNT__SECRET_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789372160'),
			["StringValueList"] = {},
		},
		["RED_PACKET_COMMEMNT_PASSWORD"] = {
			["Key"] = "RED_PACKET_COMMEMNT_PASSWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789372416'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HINT_PASSWD"] = {
			["Key"] = "RED_PACKET_HINT_PASSWD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789372672'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_GIVE"] = {
			["Key"] = "RED_PACKET_BUTTON_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789372928'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TITLE_2"] = {
			["Key"] = "RED_PACKET_TITLE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373184'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_NEXT"] = {
			["Key"] = "RED_PACKET_BUTTON_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373440'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BUTTON_BANNED"] = {
			["Key"] = "RED_PACKET_BUTTON_BANNED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373696'),
			["StringValueList"] = {},
		},
		["RED_PACKET_AROUND"] = {
			["Key"] = "RED_PACKET_AROUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373952'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEND_AMOUNT"] = {
			["Key"] = "RED_PACKET_SEND_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789374208'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL_SEND"] = {
			["Key"] = "RED_PACKET_TOTAL_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789374464'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_NUM"] = {
			["Key"] = "RED_PACKET_RECEIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789374720'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_AMOUNT"] = {
			["Key"] = "RED_PACKET_RECEIVE_AMOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789374976'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL_RECEIVE"] = {
			["Key"] = "RED_PACKET_TOTAL_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789375232'),
			["StringValueList"] = {},
		},
		["RED_PACKET_BEST_LUCK"] = {
			["Key"] = "RED_PACKET_BEST_LUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789375488'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEND_EMPTY"] = {
			["Key"] = "RED_PACKET_SEND_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789375744'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE_EMPTY"] = {
			["Key"] = "RED_PACKET_RECEIVE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789376000'),
			["StringValueList"] = {},
		},
		["RED_PACKET_CHOOSE_RECEIVE_CHANNAL"] = {
			["Key"] = "RED_PACKET_CHOOSE_RECEIVE_CHANNAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789376256'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TOTAL"] = {
			["Key"] = "RED_PACKET_TOTAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789376512'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECEIVE"] = {
			["Key"] = "RED_PACKET_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789376768'),
			["StringValueList"] = {},
		},
		["RED_PACKET_FOLLOW"] = {
			["Key"] = "RED_PACKET_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789377024'),
			["StringValueList"] = {},
		},
		["RED_PACKET_THANK"] = {
			["Key"] = "RED_PACKET_THANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789377280'),
			["StringValueList"] = {},
		},
		["RED_PACKET_OPEN"] = {
			["Key"] = "RED_PACKET_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789377536'),
			["StringValueList"] = {},
		},
		["RED_PACKET_REPORT"] = {
			["Key"] = "RED_PACKET_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924500992'),
			["StringValueList"] = {},
		},
		["RED_PACKET_MONEY"] = {
			["Key"] = "RED_PACKET_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789368832'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECORD_SEND"] = {
			["Key"] = "RED_PACKET_RECORD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325824'),
			["StringValueList"] = {},
		},
		["RED_PACKET_RECORD_RECEIVE"] = {
			["Key"] = "RED_PACKET_RECORD_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789378560'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SEASON"] = {
			["Key"] = "RED_PACKET_SEASON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_28176327640576'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HISTORY_MONEY"] = {
			["Key"] = "RED_PACKET_HISTORY_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789379072'),
			["StringValueList"] = {},
		},
		["RED_PACKET_HISTORY_SKIN"] = {
			["Key"] = "RED_PACKET_HISTORY_SKIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_39652211821568'),
			["StringValueList"] = {},
		},
		["RED_DEFAULT_MESSAGE"] = {
			["Key"] = "RED_DEFAULT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789379584'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_TEAM"] = {
			["Key"] = "RED_CHANNEL_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115428352'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_GUILD"] = {
			["Key"] = "RED_CHANNEL_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["StringValueList"] = {},
		},
		["RED_CHANNEL_WORLD"] = {
			["Key"] = "RED_CHANNEL_WORLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789380352'),
			["StringValueList"] = {},
		},
		["RED_SKIN_NUM"] = {
			["Key"] = "RED_SKIN_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789380608'),
			["StringValueList"] = {},
		},
		["RED_PACKET_SOURCE"] = {
			["Key"] = "RED_PACKET_SOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789380864'),
			["StringValueList"] = {},
		},
		["STATISTICS_TITLE"] = {
			["Key"] = "STATISTICS_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789381120'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE"] = {
			["Key"] = "STATISTICS_SUB_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48105244329728'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_2"] = {
			["Key"] = "STATISTICS_SUB_TITLE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789381632'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_3"] = {
			["Key"] = "STATISTICS_SUB_TITLE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318912'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_4"] = {
			["Key"] = "STATISTICS_SUB_TITLE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789382144'),
			["StringValueList"] = {},
		},
		["STATISTICS_SUB_TITLE_5"] = {
			["Key"] = "STATISTICS_SUB_TITLE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789382400'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_DAMAGE"] = {
			["Key"] = "STATISTICS_TOTAL_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789382656'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_HEALING"] = {
			["Key"] = "STATISTICS_TOTAL_HEALING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789382912'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEALING_SECOND"] = {
			["Key"] = "STATISTICS_HEALING_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789383168'),
			["StringValueList"] = {},
		},
		["STATISTICS_DPS"] = {
			["Key"] = "STATISTICS_DPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789383424'),
			["StringValueList"] = {},
		},
		["STATISTICS_TOTAL_TAKEDAMAGE"] = {
			["Key"] = "STATISTICS_TOTAL_TAKEDAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789383680'),
			["StringValueList"] = {},
		},
		["STATISTICS_NOW_BOSS"] = {
			["Key"] = "STATISTICS_NOW_BOSS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789383936'),
			["StringValueList"] = {},
		},
		["STATISTICS_ALL"] = {
			["Key"] = "STATISTICS_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
			["StringValueList"] = {},
		},
		["STATISTICS_DETAIL"] = {
			["Key"] = "STATISTICS_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789384448'),
			["StringValueList"] = {},
		},
		["STATISTICS_HITCOUNT"] = {
			["Key"] = "STATISTICS_HITCOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789384704'),
			["StringValueList"] = {},
		},
		["STATISTICS_DAMAGE"] = {
			["Key"] = "STATISTICS_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318656'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEALING"] = {
			["Key"] = "STATISTICS_HEALING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318912'),
			["StringValueList"] = {},
		},
		["STATISTICS_PERCENT"] = {
			["Key"] = "STATISTICS_PERCENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789385472'),
			["StringValueList"] = {},
		},
		["STATISTICS_NEXT"] = {
			["Key"] = "STATISTICS_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373440'),
			["StringValueList"] = {},
		},
		["STATISTICS_CLEAR"] = {
			["Key"] = "STATISTICS_CLEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789385984'),
			["StringValueList"] = {},
		},
		["STATISTICS_ELEMENT_DAMAGE"] = {
			["Key"] = "STATISTICS_ELEMENT_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789386240'),
			["StringValueList"] = {},
		},
		["STATISTICS_CRITICAL"] = {
			["Key"] = "STATISTICS_CRITICAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18830210376704'),
			["StringValueList"] = {},
		},
		["STATISTICS_DAMAGE_RESOURCE"] = {
			["Key"] = "STATISTICS_DAMAGE_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789386752'),
			["StringValueList"] = {},
		},
		["STATISTICS_COUNT"] = {
			["Key"] = "STATISTICS_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789387008'),
			["StringValueList"] = {},
		},
		["STATISTICS_HEAL_RESOURCE"] = {
			["Key"] = "STATISTICS_HEAL_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789387264'),
			["StringValueList"] = {},
		},
		["STATISTICS_PERCENT_TITLE"] = {
			["Key"] = "STATISTICS_PERCENT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789387520'),
			["StringValueList"] = {},
		},
		["STATISTICS_TITLE_MEMBER"] = {
			["Key"] = "STATISTICS_TITLE_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667840'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_EMPTY"] = {
			["Key"] = "DUNGEON_REWARD_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789388032'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_TIME"] = {
			["Key"] = "DUNGEON_SETTLEMENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789388288'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_REWARDS"] = {
			["Key"] = "DUNGEON_SETTLEMENT_REWARDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789388544'),
			["StringValueList"] = {},
		},
		["DUNGEON_SETTLEMENT_TIME_DETAIL"] = {
			["Key"] = "DUNGEON_SETTLEMENT_TIME_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789388800'),
			["StringValueList"] = {},
		},
		["DUNGEON_SUCCESS"] = {
			["Key"] = "DUNGEON_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789389056'),
			["StringValueList"] = {},
		},
		["DUNGEON_FAIL"] = {
			["Key"] = "DUNGEON_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789389312'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_SUCCESS"] = {
			["Key"] = "DUNGEON_STAGE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789389568'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_FAIL"] = {
			["Key"] = "DUNGEON_STAGE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789389824'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_HIGHEST"] = {
			["Key"] = "DUNGEON_AWARD_HIGHEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789390080'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_NOPRICE"] = {
			["Key"] = "DUNGEON_AWARD_NOPRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789390336'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_PREBID"] = {
			["Key"] = "DUNGEON_AWARD_PREBID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789390592'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_WAIT_ASSIGNMENT"] = {
			["Key"] = "DUNGEON_AWARD_WAIT_ASSIGNMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789390848'),
			["StringValueList"] = {},
		},
		["DUNGEON_AWARD_ME"] = {
			["Key"] = "DUNGEON_AWARD_ME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789391104'),
			["StringValueList"] = {},
		},
		["DUNGEON_UNLOCK_LEVEL"] = {
			["Key"] = "DUNGEON_UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789391360'),
			["StringValueList"] = {},
		},
		["DUNGEON_SUGGEST_TEAM_NUM"] = {
			["Key"] = "DUNGEON_SUGGEST_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789391616'),
			["StringValueList"] = {},
		},
		["DUNGEON_MAX_LEVEL_TO_ATTEND"] = {
			["Key"] = "DUNGEON_MAX_LEVEL_TO_ATTEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789391872'),
			["StringValueList"] = {},
		},
		["DUNGEON_LOADING"] = {
			["Key"] = "DUNGEON_LOADING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789392128'),
			["StringValueList"] = {},
		},
		["DUNGEON_QUICK_TEAM"] = {
			["Key"] = "DUNGEON_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584909312'),
			["StringValueList"] = {},
		},
		["DUNGEON_START_MATCH"] = {
			["Key"] = "DUNGEON_START_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789392640'),
			["StringValueList"] = {},
		},
		["DUNGEON_MY_TEAM"] = {
			["Key"] = "DUNGEON_MY_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789392896'),
			["StringValueList"] = {},
		},
		["DUNGEON_MY_GROUP"] = {
			["Key"] = "DUNGEON_MY_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789393152'),
			["StringValueList"] = {},
		},
		["DUNGEON_OPEN_GATE"] = {
			["Key"] = "DUNGEON_OPEN_GATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789393408'),
			["StringValueList"] = {},
		},
		["DUNGEON_IS_MATCHING"] = {
			["Key"] = "DUNGEON_IS_MATCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789393664'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_CAPTAIN"] = {
			["Key"] = "DUNGEON_WAITING_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789393920'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_GROUP_CAPTAIN"] = {
			["Key"] = "DUNGEON_WAITING_GROUP_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789394176'),
			["StringValueList"] = {},
		},
		["DUNGEON_WAITING_CONFIRM"] = {
			["Key"] = "DUNGEON_WAITING_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789394432'),
			["StringValueList"] = {},
		},
		["DUNGEON_BACK_TO_GAME"] = {
			["Key"] = "DUNGEON_BACK_TO_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789394688'),
			["StringValueList"] = {},
		},
		["DUNGEON_PVP_WAITING_CONFIRM"] = {
			["Key"] = "DUNGEON_PVP_WAITING_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789394944'),
			["StringValueList"] = {},
		},
		["DUNGEON_MATCHING_TIME"] = {
			["Key"] = "DUNGEON_MATCHING_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789395200'),
			["StringValueList"] = {},
		},
		["DUNGEON_IS_IN_PVP_GAMING"] = {
			["Key"] = "DUNGEON_IS_IN_PVP_GAMING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789395456'),
			["StringValueList"] = {},
		},
		["DUNGEON_CARRIAGE"] = {
			["Key"] = "DUNGEON_CARRIAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607225088'),
			["StringValueList"] = {},
		},
		["DUNGEON_STAGE_UNFINISH"] = {
			["Key"] = "DUNGEON_STAGE_UNFINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789395968'),
			["StringValueList"] = {},
		},
		["DUNGEON_PRE_BID"] = {
			["Key"] = "DUNGEON_PRE_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789396224'),
			["StringValueList"] = {},
		},
		["DUNGEON_HIGHEST_BID"] = {
			["Key"] = "DUNGEON_HIGHEST_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789396480'),
			["StringValueList"] = {},
		},
		["DUNGEON_RESTART"] = {
			["Key"] = "DUNGEON_RESTART",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789396736'),
			["StringValueList"] = {},
		},
		["DUNGEON_ALL_NEED"] = {
			["Key"] = "DUNGEON_ALL_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789396992'),
			["StringValueList"] = {},
		},
		["DUNGEON_ALL_GIVE_UP"] = {
			["Key"] = "DUNGEON_ALL_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789397248'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_LIST"] = {
			["Key"] = "DUNGEON_AUCTION_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789397504'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_HISTORY"] = {
			["Key"] = "DUNGEON_AUCTION_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789397760'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_GIVE_UP"] = {
			["Key"] = "DUNGEON_AUCTION_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789398016'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_HAS_GIVE_UP"] = {
			["Key"] = "DUNGEON_AUCTION_HAS_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789398272'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789398528'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_NO_ONE_AUCTION"] = {
			["Key"] = "DUNGEON_AUCTION_NO_ONE_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789398784'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_FAILED"] = {
			["Key"] = "DUNGEON_AUCTION_FAILED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789399040'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE_PEOPLE"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE_PEOPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789399296'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_PRICE_MAX"] = {
			["Key"] = "DUNGEON_AUCTION_PRICE_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789399552'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_CANCEL_GIVE"] = {
			["Key"] = "DUNGEON_AUCTION_CANCEL_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789399808'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUCTION_TITLE"] = {
			["Key"] = "DUNGEON_AUCTION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789400064'),
			["StringValueList"] = {},
		},
		["DUNGEON_TINGENHISTORY"] = {
			["Key"] = "DUNGEON_TINGENHISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789400320'),
			["StringValueList"] = {},
		},
		["DUNGEON_TINGENHISTORY_CONTINUE"] = {
			["Key"] = "DUNGEON_TINGENHISTORY_CONTINUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789400576'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_GAIN"] = {
			["Key"] = "DUNGEON_REWARD_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789400832'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_REWARD"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789401344'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_GIVE_UP"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_GIVE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789398016'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_NEED"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789401856'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_GREEDY"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_GREEDY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789402112'),
			["StringValueList"] = {},
		},
		["DUNGEON_ASSIGNMENT_TITLE"] = {
			["Key"] = "DUNGEON_ASSIGNMENT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789402368'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUTO_BATTLE"] = {
			["Key"] = "DUNGEON_AUTO_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789402624'),
			["StringValueList"] = {},
		},
		["DUNGEON_AUTO_SKILL"] = {
			["Key"] = "DUNGEON_AUTO_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789402880'),
			["StringValueList"] = {},
		},
		["DUNGEON_CHANGE_CLASS"] = {
			["Key"] = "DUNGEON_CHANGE_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789403136'),
			["StringValueList"] = {},
		},
		["DUNGEON_NORMAL"] = {
			["Key"] = "DUNGEON_NORMAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_22472074200832'),
			["StringValueList"] = {},
		},
		["DUNGEON_HARD"] = {
			["Key"] = "DUNGEON_HARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789403648'),
			["StringValueList"] = {},
		},
		["DUNGEON_SINGLE"] = {
			["Key"] = "DUNGEON_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789403904'),
			["StringValueList"] = {},
		},
		["DUNGEON_TEAM"] = {
			["Key"] = "DUNGEON_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328563968'),
			["StringValueList"] = {},
		},
		["DUNGEON_FIRST_PASS_REWARD"] = {
			["Key"] = "DUNGEON_FIRST_PASS_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789404416'),
			["StringValueList"] = {},
		},
		["DUNGEON_DROP_REWARD"] = {
			["Key"] = "DUNGEON_DROP_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789404672'),
			["StringValueList"] = {},
		},
		["DUNGEON_CURRENT_TEAM_NUM"] = {
			["Key"] = "DUNGEON_CURRENT_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789404928'),
			["StringValueList"] = {},
		},
		["DUNGEON_TOTAL_REWARD_LIMIT"] = {
			["Key"] = "DUNGEON_TOTAL_REWARD_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789405184'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_DAILY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789405440'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_WEEKLY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_WEEKLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789405696'),
			["StringValueList"] = {},
		},
		["DUNGEON_REWARD_REFRESH_MONTHLY"] = {
			["Key"] = "DUNGEON_REWARD_REFRESH_MONTHLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789405952'),
			["StringValueList"] = {},
		},
		["DUNGEON_FIRST_PASS_TEAM"] = {
			["Key"] = "DUNGEON_FIRST_PASS_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789406208'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_DATA_RANK"] = {
			["Key"] = "DUNGEON_STATS_DATA_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789406464'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_DAMAGE_RANK"] = {
			["Key"] = "DUNGEON_STATS_DAMAGE_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789406720'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_HEAL_RANK"] = {
			["Key"] = "DUNGEON_STATS_HEAL_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789406976'),
			["StringValueList"] = {},
		},
		["DUNGEON_STATS_BATTLE_TIME"] = {
			["Key"] = "DUNGEON_STATS_BATTLE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789407232'),
			["StringValueList"] = {},
		},
		["BAG_CONFIRM_RESOLVE"] = {
			["Key"] = "BAG_CONFIRM_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789407488'),
			["StringValueList"] = {},
		},
		["BAG_CANCLE_RESOLVE"] = {
			["Key"] = "BAG_CANCLE_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789407744'),
			["StringValueList"] = {},
		},
		["BAG_BATCH_RESOLVE"] = {
			["Key"] = "BAG_BATCH_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789408000'),
			["StringValueList"] = {},
		},
		["BAG_GET_SKILL"] = {
			["Key"] = "BAG_GET_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789408256'),
			["StringValueList"] = {},
		},
		["BAG_EQUIPED"] = {
			["Key"] = "BAG_EQUIPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789408512'),
			["StringValueList"] = {},
		},
		["BAG_TRADABLE"] = {
			["Key"] = "BAG_TRADABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789408768'),
			["StringValueList"] = {},
		},
		["BAG_WASH_ENTRY"] = {
			["Key"] = "BAG_WASH_ENTRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789409024'),
			["StringValueList"] = {},
		},
		["BAG_HISTORY"] = {
			["Key"] = "BAG_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789409280'),
			["StringValueList"] = {},
		},
		["BAG_UNTRADABLE"] = {
			["Key"] = "BAG_UNTRADABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789409536'),
			["StringValueList"] = {},
		},
		["BAG_DAY_COUNT"] = {
			["Key"] = "BAG_DAY_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789409792'),
			["StringValueList"] = {},
		},
		["BAG_WEEK_COUNT"] = {
			["Key"] = "BAG_WEEK_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789410048'),
			["StringValueList"] = {},
		},
		["BAG_USE_INSTRUCTION"] = {
			["Key"] = "BAG_USE_INSTRUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789410304'),
			["StringValueList"] = {},
		},
		["BAG_REWARD"] = {
			["Key"] = "BAG_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789410560'),
			["StringValueList"] = {},
		},
		["BAG_PICKPACKTITLE"] = {
			["Key"] = "BAG_PICKPACKTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789410816'),
			["StringValueList"] = {},
		},
		["BAG_PICKGET"] = {
			["Key"] = "BAG_PICKGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9965398054144'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NUM"] = {
			["Key"] = "BAG_SYNTHESIS_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789411328'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NEED_NUM"] = {
			["Key"] = "BAG_SYNTHESIS_NEED_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789411584'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_ADDITION"] = {
			["Key"] = "BAG_ONECLICK_ADDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789411840'),
			["StringValueList"] = {},
		},
		["BAG_MEDICINE"] = {
			["Key"] = "BAG_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_39652211820544'),
			["StringValueList"] = {},
		},
		["BAG_HP_VALUE"] = {
			["Key"] = "BAG_HP_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789412352'),
			["StringValueList"] = {},
		},
		["BAG_HP_VALUE_TIME"] = {
			["Key"] = "BAG_HP_VALUE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789412608'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_BUY"] = {
			["Key"] = "BAG_ONECLICK_BUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789412864'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_USE"] = {
			["Key"] = "BAG_ONECLICK_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789413120'),
			["StringValueList"] = {},
		},
		["BAG_REMINDER"] = {
			["Key"] = "BAG_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789323520'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS_NEED"] = {
			["Key"] = "BAG_SYNTHESIS_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789413632'),
			["StringValueList"] = {},
		},
		["BAG_SYNTHESIS"] = {
			["Key"] = "BAG_SYNTHESIS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155136256'),
			["StringValueList"] = {},
		},
		["BAG_BOUND_OBTAIN"] = {
			["Key"] = "BAG_BOUND_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9965398053376'),
			["StringValueList"] = {},
		},
		["BAG_POSSIBLE_OBTAIN"] = {
			["Key"] = "BAG_POSSIBLE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9965398053632'),
			["StringValueList"] = {},
		},
		["BAG_USE"] = {
			["Key"] = "BAG_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155134976'),
			["StringValueList"] = {},
		},
		["BAG_CANCLE"] = {
			["Key"] = "BAG_CANCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["BAG_CONFIRM_OBTAIN"] = {
			["Key"] = "BAG_CONFIRM_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789415168'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_OPEN_NEED"] = {
			["Key"] = "BAG_CHEST_OPEN_NEED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789415424'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_UNLOCK"] = {
			["Key"] = "BAG_CHEST_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789415680'),
			["StringValueList"] = {},
		},
		["BAG_CHEST_OPEN"] = {
			["Key"] = "BAG_CHEST_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789415936'),
			["StringValueList"] = {},
		},
		["BAG_STOREHOUSE"] = {
			["Key"] = "BAG_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32643362068736'),
			["StringValueList"] = {},
		},
		["BAG_RESOLVE"] = {
			["Key"] = "BAG_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155137024'),
			["StringValueList"] = {},
		},
		["BAG_TIDY"] = {
			["Key"] = "BAG_TIDY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607257088'),
			["StringValueList"] = {},
		},
		["BAG_EXIT"] = {
			["Key"] = "BAG_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["StringValueList"] = {},
		},
		["BAG_ONECLICK_RESOLVE"] = {
			["Key"] = "BAG_ONECLICK_RESOLVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789417216'),
			["StringValueList"] = {},
		},
		["BAG_GOLD_EQUIP"] = {
			["Key"] = "BAG_GOLD_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789417472'),
			["StringValueList"] = {},
		},
		["BAG_TRADABLE_PURPLE_EQUIP"] = {
			["Key"] = "BAG_TRADABLE_PURPLE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789417728'),
			["StringValueList"] = {},
		},
		["BAG_UNDER_PURPLE_EQUIP"] = {
			["Key"] = "BAG_UNDER_PURPLE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789417984'),
			["StringValueList"] = {},
		},
		["BAG_TEMPORARY_BAG"] = {
			["Key"] = "BAG_TEMPORARY_BAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789418240'),
			["StringValueList"] = {},
		},
		["BAG_RETRIEVE_ALL"] = {
			["Key"] = "BAG_RETRIEVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789418496'),
			["StringValueList"] = {},
		},
		["BAG_TIDY_STOREHOUSE"] = {
			["Key"] = "BAG_TIDY_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789418752'),
			["StringValueList"] = {},
		},
		["BAG_CLOSE_STOREHOUSE"] = {
			["Key"] = "BAG_CLOSE_STOREHOUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789419008'),
			["StringValueList"] = {},
		},
		["BAG_Level"] = {
			["Key"] = "BAG_Level",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789419264'),
			["StringValueList"] = {},
		},
		["BAG_ALL_RARITY"] = {
			["Key"] = "BAG_ALL_RARITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789419520'),
			["StringValueList"] = {},
		},
		["BAG_ACHIEVE_PATH"] = {
			["Key"] = "BAG_ACHIEVE_PATH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178871296'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_MEDICINE"] = {
			["Key"] = "BAG_AUTO_USE_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789420032'),
			["StringValueList"] = {},
		},
		["BAG_QUICK_MEDICINE"] = {
			["Key"] = "BAG_QUICK_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789420288'),
			["StringValueList"] = {},
		},
		["BAG_QUICK_MEDICINE_DES"] = {
			["Key"] = "BAG_QUICK_MEDICINE_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789420544'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_MEDICINE_DES"] = {
			["Key"] = "BAG_AUTO_USE_MEDICINE_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789420800'),
			["StringValueList"] = {},
		},
		["BAG_AUTO_USE_FOOD_DES"] = {
			["Key"] = "BAG_AUTO_USE_FOOD_DES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789420800'),
			["StringValueList"] = {},
		},
		["ITEM_PICKCHEST_HOLDNUM"] = {
			["Key"] = "ITEM_PICKCHEST_HOLDNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421312'),
			["StringValueList"] = {},
		},
		["OPEN_BOX_RESULT"] = {
			["Key"] = "OPEN_BOX_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["CHEST_RESULT"] = {
			["Key"] = "CHEST_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421824'),
			["StringValueList"] = {},
		},
		["SYNTHESIS_RESULT"] = {
			["Key"] = "SYNTHESIS_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789422080'),
			["StringValueList"] = {},
		},
		["DECOMPOSE_RESULT"] = {
			["Key"] = "DECOMPOSE_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789422336'),
			["StringValueList"] = {},
		},
		["DROP_GET_ITEMS"] = {
			["Key"] = "DROP_GET_ITEMS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["MAIL_RESULT"] = {
			["Key"] = "MAIL_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["SHOP_BUY_RESULT"] = {
			["Key"] = "SHOP_BUY_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423104'),
			["StringValueList"] = {},
		},
		["NEWBIE_TASK_RESULT"] = {
			["Key"] = "NEWBIE_TASK_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["AUCTION_RESULT"] = {
			["Key"] = "AUCTION_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ROLL_RESULT"] = {
			["Key"] = "ROLL_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["STALL_BUY_RESULT"] = {
			["Key"] = "STALL_BUY_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["GUILD_MATERIAL_TASK_RESULT"] = {
			["Key"] = "GUILD_MATERIAL_TASK_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["SCHEDULE_RESULT"] = {
			["Key"] = "SCHEDULE_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["DROP_CLICK_EMPTY_CLOSE"] = {
			["Key"] = "DROP_CLICK_EMPTY_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789424896'),
			["StringValueList"] = {},
		},
		["DROP_CURVE"] = {
			["Key"] = "DROP_CURVE",
			["StringValue"] = "/Game/Blueprint/SceneActor/Curve/Curve_DropItem.Curve_DropItem",
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_OPEN_BOX"] = {
			["Key"] = "ITEM_SOURCE_OPEN_BOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_OPEN_CHEST"] = {
			["Key"] = "ITEM_SOURCE_OPEN_CHEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SYNTHESIS"] = {
			["Key"] = "ITEM_SOURCE_SYNTHESIS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789422080'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_DROP"] = {
			["Key"] = "ITEM_SOURCE_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_MAIL"] = {
			["Key"] = "ITEM_SOURCE_MAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_BASIC_SHOP_BUY"] = {
			["Key"] = "ITEM_SOURCE_BASIC_SHOP_BUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_NEWBIE_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_NEWBIE_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_AUCTION"] = {
			["Key"] = "ITEM_SOURCE_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ROLL"] = {
			["Key"] = "ITEM_SOURCE_ROLL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_STALL_BUY_IN"] = {
			["Key"] = "ITEM_SOURCE_STALL_BUY_IN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789421568'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_GUILD_MATERIAL_TASK"] = {
			["Key"] = "ITEM_SOURCE_GUILD_MATERIAL_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SCHEDULE_REWARD"] = {
			["Key"] = "ITEM_SOURCE_SCHEDULE_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PHARMACIST_EXPLORE_PRESCRIPTION"] = {
			["Key"] = "ITEM_SOURCE_PHARMACIST_EXPLORE_PRESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789428480'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PHARMACIST_QUICK_MAKE_MEDICINE"] = {
			["Key"] = "ITEM_SOURCE_PHARMACIST_QUICK_MAKE_MEDICINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789428736'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TASK_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_TASK_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_ARBITRATOR_DICE_SUCCESS"] = {
			["Key"] = "ITEM_SOURCE_RP_ARBITRATOR_DICE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_SHERIFF_ATTACK_MONSTER"] = {
			["Key"] = "ITEM_SOURCE_RP_SHERIFF_ATTACK_MONSTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_RP_ARBITRATOR_WIN_AFTER_DICE"] = {
			["Key"] = "ITEM_SOURCE_RP_ARBITRATOR_WIN_AFTER_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_COLLECTIBLES"] = {
			["Key"] = "ITEM_SOURCE_COLLECTIBLES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ACHIEVEMENT_LEVEL_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_ACHIEVEMENT_LEVEL_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_ACHIEVEMENT_TRIGGER_SEND"] = {
			["Key"] = "ITEM_SOURCE_ACHIEVEMENT_TRIGGER_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TEAM33_RANK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_TEAM33_RANK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789430784'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_TEAM55_RANK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_TEAM55_RANK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789430784'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_SEND_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_SEND_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_CONVERT_TASK_REWARD"] = {
			["Key"] = "ITEM_SOURCE_CONVERT_TASK_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_FASHION_REWARD"] = {
			["Key"] = "ITEM_SOURCE_FASHION_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_DANCE_LIFE"] = {
			["Key"] = "ITEM_SOURCE_DANCE_LIFE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_GUILD_LEAGUE_SETTLEMENT"] = {
			["Key"] = "ITEM_SOURCE_GUILD_LEAGUE_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_CHAT_ANON_DROP"] = {
			["Key"] = "ITEM_SOURCE_CHAT_ANON_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PLOT_RECAP_REWARD_SEND"] = {
			["Key"] = "ITEM_SOURCE_PLOT_RECAP_REWARD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["ITEM_SOURCE_PLOT_RECAP_LEVEL_REWARD_SEND"] = {
			["Key"] = "ITEM_SOURCE_PLOT_RECAP_LEVEL_REWARD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["MALL_TITLE"] = {
			["Key"] = "MALL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450664192'),
			["StringValueList"] = {},
		},
		["NO_WAY_TO_OBTAIN"] = {
			["Key"] = "NO_WAY_TO_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789433600'),
			["StringValueList"] = {},
		},
		["REFRESH_EVERY_MORNING"] = {
			["Key"] = "REFRESH_EVERY_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789433856'),
			["StringValueList"] = {},
		},
		["REFRESH_WEEK_MORNING"] = {
			["Key"] = "REFRESH_WEEK_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789434112'),
			["StringValueList"] = {},
		},
		["REFRESH_MONTH_MORNING"] = {
			["Key"] = "REFRESH_MONTH_MORNING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789434368'),
			["StringValueList"] = {},
		},
		["HUD_SHOPMALL"] = {
			["Key"] = "HUD_SHOPMALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667072'),
			["StringValueList"] = {},
		},
		["PAGE_TITLE"] = {
			["Key"] = "PAGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789434880'),
			["StringValueList"] = {},
		},
		["INFORM_TITLE"] = {
			["Key"] = "INFORM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789435136'),
			["StringValueList"] = {},
		},
		["INFORM_RATE"] = {
			["Key"] = "INFORM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789435392'),
			["StringValueList"] = {},
		},
		["INFORM_MONEY"] = {
			["Key"] = "INFORM_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789435648'),
			["StringValueList"] = {},
		},
		["SELL_TITLE"] = {
			["Key"] = "SELL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789435904'),
			["StringValueList"] = {},
		},
		["SELL_TRANSNAME"] = {
			["Key"] = "SELL_TRANSNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789436160'),
			["StringValueList"] = {},
		},
		["SELL_SETRATE"] = {
			["Key"] = "SELL_SETRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789436416'),
			["StringValueList"] = {},
		},
		["SELL_SETSELLNUM"] = {
			["Key"] = "SELL_SETSELLNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789436672'),
			["StringValueList"] = {},
		},
		["SELL_EXPECTGET"] = {
			["Key"] = "SELL_EXPECTGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789436928'),
			["StringValueList"] = {},
		},
		["SELL_TAXTIP"] = {
			["Key"] = "SELL_TAXTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789437184'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM"] = {
			["Key"] = "SELL_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789434880'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_TITLE"] = {
			["Key"] = "SELL_CONFIRM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789437696'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_RATE"] = {
			["Key"] = "SELL_CONFIRM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789437952'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_NUM"] = {
			["Key"] = "SELL_CONFIRM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789438208'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_GET"] = {
			["Key"] = "SELL_CONFIRM_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789438464'),
			["StringValueList"] = {},
		},
		["SELL_CONFIRM_CONTENT"] = {
			["Key"] = "SELL_CONFIRM_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789438720'),
			["StringValueList"] = {},
		},
		["SELREC_TITLE"] = {
			["Key"] = "SELREC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789438976'),
			["StringValueList"] = {},
		},
		["SELREC_SELLING"] = {
			["Key"] = "SELREC_SELLING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789439232'),
			["StringValueList"] = {},
		},
		["SELREC_SELLTIME"] = {
			["Key"] = "SELREC_SELLTIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789439488'),
			["StringValueList"] = {},
		},
		["SELREC_NUM"] = {
			["Key"] = "SELREC_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789435648'),
			["StringValueList"] = {},
		},
		["SELREC_TIMEOUT"] = {
			["Key"] = "SELREC_TIMEOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789440000'),
			["StringValueList"] = {},
		},
		["SELREC_CANCEL"] = {
			["Key"] = "SELREC_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789440256'),
			["StringValueList"] = {},
		},
		["SELREC_CANCELCONFIRM"] = {
			["Key"] = "SELREC_CANCELCONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789440512'),
			["StringValueList"] = {},
		},
		["SELREC_SELLINGTIP"] = {
			["Key"] = "SELREC_SELLINGTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789440768'),
			["StringValueList"] = {},
		},
		["SELREC_SOLD"] = {
			["Key"] = "SELREC_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789441024'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDTIME"] = {
			["Key"] = "SELREC_SOLDTIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789441280'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDNUM"] = {
			["Key"] = "SELREC_SOLDNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789441536'),
			["StringValueList"] = {},
		},
		["SELREC_SOLDTIP"] = {
			["Key"] = "SELREC_SOLDTIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789441792'),
			["StringValueList"] = {},
		},
		["BUY_NAME"] = {
			["Key"] = "BUY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789442048'),
			["StringValueList"] = {},
		},
		["BUY_TRANSNAME"] = {
			["Key"] = "BUY_TRANSNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789442304'),
			["StringValueList"] = {},
		},
		["BUY_CURRATE"] = {
			["Key"] = "BUY_CURRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789442560'),
			["StringValueList"] = {},
		},
		["BUY_EXPRATE"] = {
			["Key"] = "BUY_EXPRATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789442816'),
			["StringValueList"] = {},
		},
		["BUY_EXPRATETIP"] = {
			["Key"] = "BUY_EXPRATETIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789443072'),
			["StringValueList"] = {},
		},
		["BUY_MYCOIN"] = {
			["Key"] = "BUY_MYCOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789443328'),
			["StringValueList"] = {},
		},
		["BUY_SETBUYNUM"] = {
			["Key"] = "BUY_SETBUYNUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092639232'),
			["StringValueList"] = {},
		},
		["BUY_EXPECTCONSUME"] = {
			["Key"] = "BUY_EXPECTCONSUME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789443840'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM"] = {
			["Key"] = "BUY_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31822486449664'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_TITLE"] = {
			["Key"] = "BUY_CONFIRM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789444352'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_RATE"] = {
			["Key"] = "BUY_CONFIRM_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789444608'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_NUM"] = {
			["Key"] = "BUY_CONFIRM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789444864'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_CONSUME"] = {
			["Key"] = "BUY_CONFIRM_CONSUME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789445120'),
			["StringValueList"] = {},
		},
		["BUY_CONFIRM_CONTENT"] = {
			["Key"] = "BUY_CONFIRM_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789445376'),
			["StringValueList"] = {},
		},
		["BUYREC_TITLE"] = {
			["Key"] = "BUYREC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789445632'),
			["StringValueList"] = {},
		},
		["BUYREC_NUM"] = {
			["Key"] = "BUYREC_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789445888'),
			["StringValueList"] = {},
		},
		["BUYREC_TIME"] = {
			["Key"] = "BUYREC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789446144'),
			["StringValueList"] = {},
		},
		["CASHMARKET_EMPTY_CONTENT"] = {
			["Key"] = "CASHMARKET_EMPTY_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789446400'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_NOT_ALLOW"] = {
			["Key"] = "TOWERCLIMB_FELLOW_NOT_ALLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789446656'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_EXIT"] = {
			["Key"] = "TOWERCLIMB_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789446912'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CHANGEFELLOW"] = {
			["Key"] = "TOWERCLIMB_CHANGEFELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789447168'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_SUCCESS"] = {
			["Key"] = "TOWERCLIMB_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789447424'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW"] = {
			["Key"] = "TOWERCLIMB_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789447680'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FAILURE"] = {
			["Key"] = "TOWERCLIMB_FAILURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789447936'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_RETRY"] = {
			["Key"] = "TOWERCLIMB_RETRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789448192'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NEXTLEVEL"] = {
			["Key"] = "TOWERCLIMB_NEXTLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789448448'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_INFO"] = {
			["Key"] = "TOWERCLIMB_CURRENT_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789448704'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_LAYER"] = {
			["Key"] = "TOWERCLIMB_CURRENT_LAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789448960'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_PLAYERLEVEL"] = {
			["Key"] = "TOWERCLIMB_CURRENT_PLAYERLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789449216'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENTER_PLAYERLEVEL"] = {
			["Key"] = "TOWERCLIMB_ENTER_PLAYERLEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789449472'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_CURRENT_COMBAT"] = {
			["Key"] = "TOWERCLIMB_CURRENT_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789449728'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_RECOMMEND_COMBAT"] = {
			["Key"] = "TOWERCLIMB_RECOMMEND_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789449984'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LAYER_INDEX"] = {
			["Key"] = "TOWERCLIMB_LAYER_INDEX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789450240'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FIRST_REWARD"] = {
			["Key"] = "TOWERCLIMB_FIRST_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789404416'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_GET_REWARD"] = {
			["Key"] = "TOWERCLIMB_GET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789450752'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ALREADYGET_REWARD"] = {
			["Key"] = "TOWERCLIMB_ALREADYGET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789451008'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_NOT_USED"] = {
			["Key"] = "TOWERCLIMB_FELLOW_NOT_USED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789451264'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ADD_FELLOW"] = {
			["Key"] = "TOWERCLIMB_ADD_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789451520'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_FELLOW_UNLOCK"] = {
			["Key"] = "TOWERCLIMB_FELLOW_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342976'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NEXT_INFO"] = {
			["Key"] = "TOWERCLIMB_NEXT_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789452032'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ALL_LEVEL_SUCCESS"] = {
			["Key"] = "TOWERCLIMB_ALL_LEVEL_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789452288'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LEVEL_NOT_MATCH"] = {
			["Key"] = "TOWERCLIMB_LEVEL_NOT_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789452544'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_DISABLE_SUPPORT"] = {
			["Key"] = "TOWERCLIMB_DISABLE_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789452800'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENABLE_SUPPORT"] = {
			["Key"] = "TOWERCLIMB_ENABLE_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789453056'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_ENTER"] = {
			["Key"] = "TOWERCLIMB_ENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789453312'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_LAYER_LOCK"] = {
			["Key"] = "TOWERCLIMB_LAYER_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789453568'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_NAME"] = {
			["Key"] = "TOWERCLIMB_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642594048'),
			["StringValueList"] = {},
		},
		["TOWERCLIMB_USED_TIME"] = {
			["Key"] = "TOWERCLIMB_USED_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789454080'),
			["StringValueList"] = {},
		},
		["POPUP_TODAY_NO_CHECKBOX"] = {
			["Key"] = "POPUP_TODAY_NO_CHECKBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789454336'),
			["StringValueList"] = {},
		},
		["GACHA_DESC1"] = {
			["Key"] = "GACHA_DESC1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789454592'),
			["StringValueList"] = {},
		},
		["GACHA_DESC2"] = {
			["Key"] = "GACHA_DESC2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667584'),
			["StringValueList"] = {},
		},
		["GACHA_DESC3"] = {
			["Key"] = "GACHA_DESC3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789455104'),
			["StringValueList"] = {},
		},
		["GACHA_DESC4"] = {
			["Key"] = "GACHA_DESC4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789455360'),
			["StringValueList"] = {},
		},
		["GACHA_DESC5"] = {
			["Key"] = "GACHA_DESC5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789455616'),
			["StringValueList"] = {},
		},
		["GACHA_DESC6"] = {
			["Key"] = "GACHA_DESC6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789455104'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY"] = {
			["Key"] = "GACHA_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456128'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL"] = {
			["Key"] = "GACHA_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789384448'),
			["StringValueList"] = {},
		},
		["GACHA_SHOP"] = {
			["Key"] = "GACHA_SHOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456640'),
			["StringValueList"] = {},
		},
		["GACHA_FREE"] = {
			["Key"] = "GACHA_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456896'),
			["StringValueList"] = {},
		},
		["GACHA_ONCE"] = {
			["Key"] = "GACHA_ONCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789457152'),
			["StringValueList"] = {},
		},
		["GACHA_TEN"] = {
			["Key"] = "GACHA_TEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789457408'),
			["StringValueList"] = {},
		},
		["GACHA_LIMIT"] = {
			["Key"] = "GACHA_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789457664'),
			["StringValueList"] = {},
		},
		["GACHA_FREETIMER"] = {
			["Key"] = "GACHA_FREETIMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789457920'),
			["StringValueList"] = {},
		},
		["GACHA_TITIE"] = {
			["Key"] = "GACHA_TITIE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450660096'),
			["StringValueList"] = {},
		},
		["GACHA_RATE"] = {
			["Key"] = "GACHA_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789458432'),
			["StringValueList"] = {},
		},
		["GACHA_RATE1"] = {
			["Key"] = "GACHA_RATE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789458688'),
			["StringValueList"] = {},
		},
		["GACHA_RATE2"] = {
			["Key"] = "GACHA_RATE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789458944'),
			["StringValueList"] = {},
		},
		["GACHA_UR"] = {
			["Key"] = "GACHA_UR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789459200'),
			["StringValueList"] = {},
		},
		["GACHA_SSR"] = {
			["Key"] = "GACHA_SSR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789459456'),
			["StringValueList"] = {},
		},
		["GACHA_SR"] = {
			["Key"] = "GACHA_SR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789459712'),
			["StringValueList"] = {},
		},
		["GACHA_R"] = {
			["Key"] = "GACHA_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789459968'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_UR"] = {
			["Key"] = "GACHA_UP_DESC_UR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789460224'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_SSR"] = {
			["Key"] = "GACHA_UP_DESC_SSR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789460480'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_SR"] = {
			["Key"] = "GACHA_UP_DESC_SR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789460736'),
			["StringValueList"] = {},
		},
		["GACHA_UP_DESC_R"] = {
			["Key"] = "GACHA_UP_DESC_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789460992'),
			["StringValueList"] = {},
		},
		["GACHA_OVERALL_RATE_UR"] = {
			["Key"] = "GACHA_OVERALL_RATE_UR",
			["StringValue"] = "2.53%",
			["StringValueList"] = {},
		},
		["GACHA_OVERALL_RATE_SSR"] = {
			["Key"] = "GACHA_OVERALL_RATE_SSR",
			["StringValue"] = "14.67%",
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_UP_ITEM"] = {
			["Key"] = "GACHA_DETAIL_UP_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789461760'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_RULES"] = {
			["Key"] = "GACHA_DETAIL_RULES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789462016'),
			["StringValueList"] = {},
		},
		["GACHA_DETAIL_LIST"] = {
			["Key"] = "GACHA_DETAIL_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789462272'),
			["StringValueList"] = {},
		},
		["GACHA_SHOP_TITLE"] = {
			["Key"] = "GACHA_SHOP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456640'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TIME"] = {
			["Key"] = "GACHA_HISTORY_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789462784'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_COMMON"] = {
			["Key"] = "GACHA_POOL_COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789463040'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_UP"] = {
			["Key"] = "GACHA_POOL_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789463296'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_SEALED_COMMON"] = {
			["Key"] = "GACHA_POOL_SEALED_COMMON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789463552'),
			["StringValueList"] = {},
		},
		["GACHA_POOL_SEALED_UP"] = {
			["Key"] = "GACHA_POOL_SEALED_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789463808'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TITLE"] = {
			["Key"] = "GACHA_HISTORY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456128'),
			["StringValueList"] = {},
		},
		["GACHA_UR_SEALED"] = {
			["Key"] = "GACHA_UR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789464320'),
			["StringValueList"] = {},
		},
		["GACHA_SSR_SEALED"] = {
			["Key"] = "GACHA_SSR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789464576'),
			["StringValueList"] = {},
		},
		["GACHA_SR_SEALED"] = {
			["Key"] = "GACHA_SR_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789464832'),
			["StringValueList"] = {},
		},
		["GACHA_DESC1_SEALED"] = {
			["Key"] = "GACHA_DESC1_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789465088'),
			["StringValueList"] = {},
		},
		["GACHA_DESC2_SEALED"] = {
			["Key"] = "GACHA_DESC2_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667584'),
			["StringValueList"] = {},
		},
		["GACHA_DESC3_SEALED"] = {
			["Key"] = "GACHA_DESC3_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789465600'),
			["StringValueList"] = {},
		},
		["GACHA_DESC4_SEALED"] = {
			["Key"] = "GACHA_DESC4_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789455360'),
			["StringValueList"] = {},
		},
		["GACHA_DESC5_SEALED"] = {
			["Key"] = "GACHA_DESC5_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789466112'),
			["StringValueList"] = {},
		},
		["GACHA_DESC6_SEALED"] = {
			["Key"] = "GACHA_DESC6_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789465600'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_FELLOW"] = {
			["Key"] = "GACHA_HISTORY_TAB_FELLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450660352'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_ITEM"] = {
			["Key"] = "GACHA_HISTORY_TAB_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789466880'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TAB_SEALED"] = {
			["Key"] = "GACHA_HISTORY_TAB_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["GACHA_RESULT_CONVERT"] = {
			["Key"] = "GACHA_RESULT_CONVERT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789467392'),
			["StringValueList"] = {},
		},
		["GACHA_HISTORY_TIPS"] = {
			["Key"] = "GACHA_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789467648'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_TITLE"] = {
			["Key"] = "FELLOW_LANGUAGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450660352'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_AUTOFILL"] = {
			["Key"] = "FELLOW_LANGUAGE_AUTOFILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789468160'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_JOINCOMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_JOINCOMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789468416'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_ASSISTCOMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_ASSISTCOMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789468672'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_KICK"] = {
			["Key"] = "FELLOW_LANGUAGE_KICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789468928'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_COMBAT"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_COMBAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789469184'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_LEVEL"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178852096'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_RARITY"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_RARITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789469696'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_SORT_FAVORABILITY"] = {
			["Key"] = "FELLOW_LANGUAGE_SORT_FAVORABILITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789469952'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_STARUP_MAXIMUM"] = {
			["Key"] = "FELLOW_LANGUAGE_STARUP_MAXIMUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470208'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_NONEXISTENCE"] = {
			["Key"] = "FELLOW_LANGUAGE_NONEXISTENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470464'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_I"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_I",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470720'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_II"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_II",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470976'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_III"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_III",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471232'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_IV"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_IV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471488'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_V"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_V",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471744'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_VI"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_VI",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789472000'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_VII"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_VII",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789472000'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_TYPE1"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_TYPE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327360'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_TYPE2"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_TYPE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789472768'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_LEVEL_UNLOCK"] = {
			["Key"] = "FELLOW_SKILL_LEVEL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789473024'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_CD"] = {
			["Key"] = "FELLOW_SKILL_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789473280'),
			["StringValueList"] = {},
		},
		["FELLOW_SKILL_STARUP_EFFECT"] = {
			["Key"] = "FELLOW_SKILL_STARUP_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789473536'),
			["StringValueList"] = {},
		},
		["FELLOW_MAINTAB_1"] = {
			["Key"] = "FELLOW_MAINTAB_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505504512'),
			["StringValueList"] = {},
		},
		["FELLOW_MAINTAB_2"] = {
			["Key"] = "FELLOW_MAINTAB_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789474048'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_1"] = {
			["Key"] = "FELLOW_TYPE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_40477382414336'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_2"] = {
			["Key"] = "FELLOW_TYPE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789474560'),
			["StringValueList"] = {},
		},
		["FELLOW_TYPE_3"] = {
			["Key"] = "FELLOW_TYPE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505629952'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_COMPLEMENT"] = {
			["Key"] = "FELLOW_LANGUAGE_COMPLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789475072'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_1_NEXT"] = {
			["Key"] = "FELLOW_GACHA_1_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789475328'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_10_NEXT"] = {
			["Key"] = "FELLOW_GACHA_10_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789475584'),
			["StringValueList"] = {},
		},
		["FELLOW_GACHA_HISTORY_TIPS"] = {
			["Key"] = "FELLOW_GACHA_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789467648'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_LEVELUP"] = {
			["Key"] = "FELLOW_BTN_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_STARLEVELUP"] = {
			["Key"] = "FELLOW_BTN_STARLEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476352'),
			["StringValueList"] = {},
		},
		["FELLOW_BTN_COMBINE"] = {
			["Key"] = "FELLOW_BTN_COMBINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155136256'),
			["StringValueList"] = {},
		},
		["FELLOW_ZHANLI_ADD"] = {
			["Key"] = "FELLOW_ZHANLI_ADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476864'),
			["StringValueList"] = {},
		},
		["FELLOW_LANGUAGE_CLEARALL"] = {
			["Key"] = "FELLOW_LANGUAGE_CLEARALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789477120'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_1"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789477376'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_2"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789477632'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_3"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789477888'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_4"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789453056'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_5"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789453056'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_6"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789452800'),
			["StringValueList"] = {},
		},
		["FELLOW_FRIEND_ASSIST_7"] = {
			["Key"] = "FELLOW_FRIEND_ASSIST_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789478912'),
			["StringValueList"] = {},
		},
		["SEALED_ITEM"] = {
			["Key"] = "SEALED_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE"] = {
			["Key"] = "SEFIROT_CORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789479424'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_TABS"] = {
			["Key"] = "SEALED_ENHANCE_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_TABS"] = {
			["Key"] = "SEALED_RANKUP_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476352'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_TABS"] = {
			["Key"] = "SEALED_RANDOM_TABS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480192'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_BUTTON"] = {
			["Key"] = "SEALED_ENHANCE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_BUTTON"] = {
			["Key"] = "SEALED_RANKUP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480704'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480192'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_INPUT_BUTTON"] = {
			["Key"] = "SEALED_RANK_UP_INPUT_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481216'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_RESERVE_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_RESERVE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481472'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_REPLACE_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_REPLACE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_AGAIN_BUTTON"] = {
			["Key"] = "SEALED_RANDOM_ATTR_AGAIN_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481984'),
			["StringValueList"] = {},
		},
		["SEALED_EQUIP_BUTTON"] = {
			["Key"] = "SEALED_EQUIP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328562688'),
			["StringValueList"] = {},
		},
		["SEALED_MODIFY_BUTTON"] = {
			["Key"] = "SEALED_MODIFY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155136000'),
			["StringValueList"] = {},
		},
		["SEALED_DISEQUIP_BUTTON"] = {
			["Key"] = "SEALED_DISEQUIP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155138816'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR"] = {
			["Key"] = "SEALED_FIXED_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789483008'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR"] = {
			["Key"] = "SEALED_RANDOM_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789483264'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_POS"] = {
			["Key"] = "SEALED_FIXED_ATTR_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789483520'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_NEG"] = {
			["Key"] = "SEALED_FIXED_ATTR_NEG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789483776'),
			["StringValueList"] = {},
		},
		["SEALED_NEXT_FIXED_ATTR"] = {
			["Key"] = "SEALED_NEXT_FIXED_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484032'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_STATUS"] = {
			["Key"] = "SEALED_RANK_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476352'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_STATUS"] = {
			["Key"] = "SEALED_FIXED_ATTR_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484544'),
			["StringValueList"] = {},
		},
		["SEALED_RESON_STATUS"] = {
			["Key"] = "SEALED_RESON_STATUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484800'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_UP_PREVIEW"] = {
			["Key"] = "SEALED_ENHANCE_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["StringValueList"] = {},
		},
		["SEALED_FIXED_ATTR_UP_PREVIEW"] = {
			["Key"] = "SEALED_FIXED_ATTR_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789485312'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_PREVIEW"] = {
			["Key"] = "SEALED_RANK_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476352'),
			["StringValueList"] = {},
		},
		["SEALED_RESON_UP_PREVIEW"] = {
			["Key"] = "SEALED_RESON_UP_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484800'),
			["StringValueList"] = {},
		},
		["SEALED_SORTED_BY_QUALITY"] = {
			["Key"] = "SEALED_SORTED_BY_QUALITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789486080'),
			["StringValueList"] = {},
		},
		["SEALED_SORTED_BY_RESON"] = {
			["Key"] = "SEALED_SORTED_BY_RESON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484800'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_TOTAL_TIMES"] = {
			["Key"] = "SEALED_RANDOM_TOTAL_TIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789486592'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_LAST_TIME_RESULT"] = {
			["Key"] = "SEALED_RANDOM_LAST_TIME_RESULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789486848'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_SHOW"] = {
			["Key"] = "SEALED_RANDOM_ATTR_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789487104'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_CURRENT"] = {
			["Key"] = "SEALED_RANDOM_ATTR_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789487360'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_ATTR_NEW"] = {
			["Key"] = "SEALED_RANDOM_ATTR_NEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789487616'),
			["StringValueList"] = {},
		},
		["SEALED_ENHANCE_SELECT_REMINDER"] = {
			["Key"] = "SEALED_ENHANCE_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789487872'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_SELECT_REMINDER"] = {
			["Key"] = "SEALED_RANK_UP_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789488128'),
			["StringValueList"] = {},
		},
		["SEALED_RANDOM_SELECT_REMINDER"] = {
			["Key"] = "SEALED_RANDOM_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789488384'),
			["StringValueList"] = {},
		},
		["SEALED_EQUIP_SELECT_REMINDER"] = {
			["Key"] = "SEALED_EQUIP_SELECT_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789488640'),
			["StringValueList"] = {},
		},
		["SEALED_NONE_REMINDER"] = {
			["Key"] = "SEALED_NONE_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789488896'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RESON"] = {
			["Key"] = "SEFIROT_CORE_RESON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789489152'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_SWITCH_BUTTON"] = {
			["Key"] = "SEFIROT_CORE_SWITCH_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_SLOT_UNLOCK_CONDITION"] = {
			["Key"] = "SEFIROT_CORE_SLOT_UNLOCK_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789489664'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_CURRENT_ACTIVE"] = {
			["Key"] = "SEFIROT_CORE_CURRENT_ACTIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789489920'),
			["StringValueList"] = {},
		},
		["SEALED_RANK1"] = {
			["Key"] = "SEALED_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470720'),
			["StringValueList"] = {},
		},
		["SEALED_RANK2"] = {
			["Key"] = "SEALED_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789470976'),
			["StringValueList"] = {},
		},
		["SEALED_RANK3"] = {
			["Key"] = "SEALED_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471232'),
			["StringValueList"] = {},
		},
		["SEALED_RANK4"] = {
			["Key"] = "SEALED_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471488'),
			["StringValueList"] = {},
		},
		["SEALED_RANK5"] = {
			["Key"] = "SEALED_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789471744'),
			["StringValueList"] = {},
		},
		["SEALED_RANK6"] = {
			["Key"] = "SEALED_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789472000'),
			["StringValueList"] = {},
		},
		["SEALED_RANK_UP_TITLE"] = {
			["Key"] = "SEALED_RANK_UP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789491712'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RANK"] = {
			["Key"] = "SEFIROT_CORE_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789491968'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_RATE"] = {
			["Key"] = "SEFIROT_CORE_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789492224'),
			["StringValueList"] = {},
		},
		["SEFIROT_SELECT_TITLE"] = {
			["Key"] = "SEFIROT_SELECT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789492480'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_CURRENT"] = {
			["Key"] = "SEFIROT_CORE_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789492736'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_OTHER"] = {
			["Key"] = "SEFIROT_CORE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789492992'),
			["StringValueList"] = {},
		},
		["SEFIROT_BUTTON_REPLACE"] = {
			["Key"] = "SEFIROT_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789493248'),
			["StringValueList"] = {},
		},
		["BANNER_TITLE_L"] = {
			["Key"] = "BANNER_TITLE_L",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789479424'),
			["StringValueList"] = {},
		},
		["BANNER_TITLE_R"] = {
			["Key"] = "BANNER_TITLE_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["BAR_INITIAL"] = {
			["Key"] = "BAR_INITIAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789494016'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_BANNER"] = {
			["Key"] = "SEFIROT_CORE_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789494272'),
			["StringValueList"] = {},
		},
		["SEALED_BANNER"] = {
			["Key"] = "SEALED_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789494528'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_ALL"] = {
			["Key"] = "SEALED_BUTTON_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_PILE"] = {
			["Key"] = "SEALED_BUTTON_PILE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155139840'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER"] = {
			["Key"] = "SEALED_SELECT_BANNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789495296'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER1"] = {
			["Key"] = "SEALED_SELECT_BANNER1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789495552'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER2"] = {
			["Key"] = "SEALED_SELECT_BANNER2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789495808'),
			["StringValueList"] = {},
		},
		["SEALED_SELECT_BANNER3"] = {
			["Key"] = "SEALED_SELECT_BANNER3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789496064'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY1_TEXT"] = {
			["Key"] = "SEALED_RARITY1_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789495552'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY2_TEXT"] = {
			["Key"] = "SEALED_RARITY2_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789496576'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY3_TEXT"] = {
			["Key"] = "SEALED_RARITY3_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789496832'),
			["StringValueList"] = {},
		},
		["SEALED_COMON_TEXT"] = {
			["Key"] = "SEALED_COMON_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789317120'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_RESPON"] = {
			["Key"] = "SEALED_TIP_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484800'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_POSITIVE"] = {
			["Key"] = "SEALED_TIP_POSITIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789497600'),
			["StringValueList"] = {},
		},
		["SEALED_TIPS_NEGATIVE"] = {
			["Key"] = "SEALED_TIPS_NEGATIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789497856'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_POSITIVE_BREIF"] = {
			["Key"] = "SEALED_TIP_POSITIVE_BREIF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789498112'),
			["StringValueList"] = {},
		},
		["SEALED_TIP_NEGATIVE_BREIF"] = {
			["Key"] = "SEALED_TIP_NEGATIVE_BREIF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789498368'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_EQUIP"] = {
			["Key"] = "SEALED_BUTTON_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789498624'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_EQUIPOFF"] = {
			["Key"] = "SEALED_BUTTON_EQUIPOFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155138816'),
			["StringValueList"] = {},
		},
		["SEALED_BUTTON_REPLACE"] = {
			["Key"] = "SEALED_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_EMPTY"] = {
			["Key"] = "SEALED_REFINE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789499392'),
			["StringValueList"] = {},
		},
		["SEALED_UPGRADE_TITLE"] = {
			["Key"] = "SEALED_UPGRADE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107174400'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_TITLE"] = {
			["Key"] = "SEALED_RANKUP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480704'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_TITLE"] = {
			["Key"] = "SEALED_REFINE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480192'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_NEXT"] = {
			["Key"] = "REFINE_BUTTON_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789480192'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_RESET"] = {
			["Key"] = "REFINE_BUTTON_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332224'),
			["StringValueList"] = {},
		},
		["REFINE_BUTTON_REPLACE"] = {
			["Key"] = "REFINE_BUTTON_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["UPGRADE_TEXT_UP"] = {
			["Key"] = "UPGRADE_TEXT_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789501184'),
			["StringValueList"] = {},
		},
		["UPGRADE_TEXT_DOWN"] = {
			["Key"] = "UPGRADE_TEXT_DOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789501440'),
			["StringValueList"] = {},
		},
		["UPGRADE_LEVEL"] = {
			["Key"] = "UPGRADE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789501696'),
			["StringValueList"] = {},
		},
		["UPGRADE_PREVIEW"] = {
			["Key"] = "UPGRADE_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789501952'),
			["StringValueList"] = {},
		},
		["UPGRADE_RESPON"] = {
			["Key"] = "UPGRADE_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789502208'),
			["StringValueList"] = {},
		},
		["RESPON_TEXT_RATE"] = {
			["Key"] = "RESPON_TEXT_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789502464'),
			["StringValueList"] = {},
		},
		["RESPON_TEXT_VALUE"] = {
			["Key"] = "RESPON_TEXT_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789484800'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_LUCK"] = {
			["Key"] = "SEALED_REFINE_LUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789502976'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE1"] = {
			["Key"] = "SEFIROT_CORE_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789503232'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE2"] = {
			["Key"] = "SEFIROT_CORE_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789503488'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_TITLE3"] = {
			["Key"] = "SEFIROT_CORE_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789503744'),
			["StringValueList"] = {},
		},
		["UPGRADE_BUTTON_RESPON"] = {
			["Key"] = "UPGRADE_BUTTON_RESPON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107174400'),
			["StringValueList"] = {},
		},
		["SEALED_RANKUP_EMPTY"] = {
			["Key"] = "SEALED_RANKUP_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789504256'),
			["StringValueList"] = {},
		},
		["RANKUP_EMPTY_BUTTON"] = {
			["Key"] = "RANKUP_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789504512'),
			["StringValueList"] = {},
		},
		["RANKUP_BUTTON_AUTOFILL"] = {
			["Key"] = "RANKUP_BUTTON_AUTOFILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789468160'),
			["StringValueList"] = {},
		},
		["SEALED_UPGRADE_MAX_TEXT"] = {
			["Key"] = "SEALED_UPGRADE_MAX_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789505024'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_ELEMENT_RATE"] = {
			["Key"] = "SEFIROT_CORE_ELEMENT_RATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789492224'),
			["StringValueList"] = {},
		},
		["SEALED_REFINE_CURRENT"] = {
			["Key"] = "SEALED_REFINE_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789505536'),
			["StringValueList"] = {},
		},
		["SEFIROT_CORE_MAX_TEXT"] = {
			["Key"] = "SEFIROT_CORE_MAX_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789505792'),
			["StringValueList"] = {},
		},
		["SEALED_EMPTY_TEXT"] = {
			["Key"] = "SEALED_EMPTY_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789506048'),
			["StringValueList"] = {},
		},
		["SEALED_RARITY_TITLE_TEXT"] = {
			["Key"] = "SEALED_RARITY_TITLE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789506304'),
			["StringValueList"] = {},
		},
		["RELIVE_FREE"] = {
			["Key"] = "RELIVE_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789456896'),
			["StringValueList"] = {},
		},
		["RELIVE_COOLDOWN"] = {
			["Key"] = "RELIVE_COOLDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789506816'),
			["StringValueList"] = {},
		},
		["RELIVE_UNAVAILABLE"] = {
			["Key"] = "RELIVE_UNAVAILABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789507072'),
			["StringValueList"] = {},
		},
		["ASK_REVIVE"] = {
			["Key"] = "ASK_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789507328'),
			["StringValueList"] = {},
		},
		["DEFEATED_BY"] = {
			["Key"] = "DEFEATED_BY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789507584'),
			["StringValueList"] = {},
		},
		["CLIENT_AUTO_REVIVE"] = {
			["Key"] = "CLIENT_AUTO_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789507840'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_EXCHANGE"] = {
			["Key"] = "SETTING_BTN_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789508096'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_CANCEL"] = {
			["Key"] = "SETTING_BTN_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["SETTING_TITLE"] = {
			["Key"] = "SETTING_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450662144'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_DEFAULT"] = {
			["Key"] = "SETTING_FUNC_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789508864'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_EXCHANGE"] = {
			["Key"] = "SETTING_FUNC_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789509120'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_LOGOUT"] = {
			["Key"] = "SETTING_FUNC_LOGOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789509376'),
			["StringValueList"] = {},
		},
		["SETTING_FUNC_UNSTUCK"] = {
			["Key"] = "SETTING_FUNC_UNSTUCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54495618804736'),
			["StringValueList"] = {},
		},
		["SETTING_RES_FULLSCREENWINDOW"] = {
			["Key"] = "SETTING_RES_FULLSCREENWINDOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789509888'),
			["StringValueList"] = {},
		},
		["SETTING_RES_WINDOW"] = {
			["Key"] = "SETTING_RES_WINDOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789510144'),
			["StringValueList"] = {},
		},
		["SETTING_RES_CUSTOM"] = {
			["Key"] = "SETTING_RES_CUSTOM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34841848449280'),
			["StringValueList"] = {},
		},
		["SETTING_CHOICE_NONE"] = {
			["Key"] = "SETTING_CHOICE_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322496'),
			["StringValueList"] = {},
		},
		["SETTING_CHOICE_AUTO"] = {
			["Key"] = "SETTING_CHOICE_AUTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789510912'),
			["StringValueList"] = {},
		},
		["SETTING_SR_NATIVE"] = {
			["Key"] = "SETTING_SR_NATIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789511168'),
			["StringValueList"] = {},
		},
		["SETTING_SR_ULTRA"] = {
			["Key"] = "SETTING_SR_ULTRA",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789511424'),
			["StringValueList"] = {},
		},
		["SETTING_SR_QUALITY"] = {
			["Key"] = "SETTING_SR_QUALITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789511680'),
			["StringValueList"] = {},
		},
		["SETTING_SR_BALANCED"] = {
			["Key"] = "SETTING_SR_BALANCED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_46523891058944'),
			["StringValueList"] = {},
		},
		["SETTING_SR_PERFORMANCE"] = {
			["Key"] = "SETTING_SR_PERFORMANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789512192'),
			["StringValueList"] = {},
		},
		["SETTING_SR_ULTRAPERF"] = {
			["Key"] = "SETTING_SR_ULTRAPERF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359616'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_ANNOUNCEMENT"] = {
			["Key"] = "SETTING_BTN_ANNOUNCEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789512704'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_USERCENTER"] = {
			["Key"] = "SETTING_BTN_USERCENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789512960'),
			["StringValueList"] = {},
		},
		["SETTING_BTN_REPORT"] = {
			["Key"] = "SETTING_BTN_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450675712'),
			["StringValueList"] = {},
		},
		["SOCIAL_WORLD"] = {
			["Key"] = "SOCIAL_WORLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789380352'),
			["StringValueList"] = {},
		},
		["SOCIAL_NEAR"] = {
			["Key"] = "SOCIAL_NEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789513728'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM"] = {
			["Key"] = "SOCIAL_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115428352'),
			["StringValueList"] = {},
		},
		["SOCIAL_SYSTEM"] = {
			["Key"] = "SOCIAL_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_33673348910592'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_FRIEND"] = {
			["Key"] = "SOCIAL_DELETE_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789514496'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_CLUB_MEMBER"] = {
			["Key"] = "SOCIAL_DELETE_CLUB_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789514752'),
			["StringValueList"] = {},
		},
		["SOCIAL_CREATE_GROUP"] = {
			["Key"] = "SOCIAL_CREATE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789515008'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAMING"] = {
			["Key"] = "SOCIAL_TEAMING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789515264'),
			["StringValueList"] = {},
		},
		["SOCIAL_LEISURE"] = {
			["Key"] = "SOCIAL_LEISURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789515520'),
			["StringValueList"] = {},
		},
		["SOCIAL_PLAYMODE"] = {
			["Key"] = "SOCIAL_PLAYMODE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789515776'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANGE_GROUP_NAME"] = {
			["Key"] = "SOCIAL_CHANGE_GROUP_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789516032'),
			["StringValueList"] = {},
		},
		["SOCIAL_GROUP_EDIT"] = {
			["Key"] = "SOCIAL_GROUP_EDIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789516288'),
			["StringValueList"] = {},
		},
		["SOCIAL_HAS_SELECT_FRIEND_NUM"] = {
			["Key"] = "SOCIAL_HAS_SELECT_FRIEND_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789516544'),
			["StringValueList"] = {},
		},
		["SOCIAL_TOP"] = {
			["Key"] = "SOCIAL_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789516800'),
			["StringValueList"] = {},
		},
		["SOCIAL_BLOCK"] = {
			["Key"] = "SOCIAL_BLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517056'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANGE_REMARK"] = {
			["Key"] = "SOCIAL_CHANGE_REMARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517312'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHANNEL_SETTING"] = {
			["Key"] = "SOCIAL_CHANNEL_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517568'),
			["StringValueList"] = {},
		},
		["SOCIAL_BLACK_FRIEND"] = {
			["Key"] = "SOCIAL_BLACK_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517824'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_NAME_BLACK_LIST"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_NAME_BLACK_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789518080'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_NAME_DEFAULT"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_NAME_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789518336'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_BLACKLIST"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_BLACKLIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789518592'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_REMOVE_BLACKLIST"] = {
			["Key"] = "SOCIAL_FRIEND_REMOVE_BLACKLIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789518848'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_OFFLINE"] = {
			["Key"] = "SOCIAL_FRIEND_OFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924501504'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ONLINE"] = {
			["Key"] = "SOCIAL_FRIEND_ONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789519360'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_AFK"] = {
			["Key"] = "SOCIAL_FRIEND_AFK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924501760'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUP_EMPTY"] = {
			["Key"] = "SOCIAL_FRIEND_GROUP_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789519872'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_GROUP"] = {
			["Key"] = "SOCIAL_DELETE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789520128'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_GROUP_BTN"] = {
			["Key"] = "SOCIAL_DELETE_GROUP_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789520384'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SET_TOP_TIPS"] = {
			["Key"] = "SOCIAL_FRIEND_SET_TOP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789520640'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SETTING_BULLET"] = {
			["Key"] = "SOCIAL_FRIEND_SETTING_BULLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789520896'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_PLEASE_FRIEND_FIRST"] = {
			["Key"] = "SOCIAL_FRIEND_PLEASE_FRIEND_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789521152'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TIPS_ALREADY_APPLY"] = {
			["Key"] = "SOCIAL_FRIEND_TIPS_ALREADY_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178854656'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789521664'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_CIRCLE"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_CIRCLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811119104'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_MASTER"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789522432'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TAB_RELATION"] = {
			["Key"] = "SOCIAL_FRIEND_TAB_RELATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789522688'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_EMPTY_NOTICE"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_EMPTY_NOTICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789522944'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_FILTER_ALLFRIENDS"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_FILTER_ALLFRIENDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789523200'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_AVAILABLE_NOTICE_COUNT"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_AVAILABLE_NOTICE_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789523456'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_DEFAULT_NAME"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_DEFAULT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789523712'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_CREATE"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789523968'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_MODIFY_SETTING"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_MODIFY_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789524224'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_DISBAND_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_DISBAND_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789524480'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_EXIT_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_EXIT_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789524736'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CLUB_CLEAR_MSG"] = {
			["Key"] = "SOCIAL_FRIEND_CLUB_CLEAR_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789524992'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_REMARK_PLACEHOLDER"] = {
			["Key"] = "SOCIAL_FRIEND_REMARK_PLACEHOLDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789525248'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_THIS_SERVER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_THIS_SERVER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789525504'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_IGNORE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_IGNORE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789525760'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_MALE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_MALE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789526016'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_FEMALE_GENDER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_FEMALE_GENDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789526272'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_RECOMMEND"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789526528'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_TEAMMATE"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_TEAMMATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789526784'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_NO_APPLE"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_NO_APPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789527040'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_RECOMMEND"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_RECOMMEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789527296'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_RECENT_TEAM"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_RECENT_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789527552'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_APPLY"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789527808'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SEARCH_TAB_FIND_PARTNER"] = {
			["Key"] = "SOCIAL_FRIEND_SEARCH_TAB_FIND_PARTNER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789528064'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SETTING"] = {
			["Key"] = "SOCIAL_FRIEND_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450662144'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_FLOWER"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_FLOWER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789528576'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_FOOD"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_FOOD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789528832'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_ITEM"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789529088'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789529344'),
			["StringValueList"] = {},
		},
		["SOCIAL_SAVE_EDIT_GROUP"] = {
			["Key"] = "SOCIAL_SAVE_EDIT_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789529600'),
			["StringValueList"] = {},
		},
		["SOCIAL_NEWMSG_NUM"] = {
			["Key"] = "SOCIAL_NEWMSG_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789529856'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_ENTER"] = {
			["Key"] = "SOCIAL_TEAM_ENTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_4949144503808'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_LEADER"] = {
			["Key"] = "SOCIAL_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_4949144504320'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_EXIT"] = {
			["Key"] = "SOCIAL_TEAM_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_4949144504064'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_REWARD"] = {
			["Key"] = "SOCIAL_TEAM_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789530880'),
			["StringValueList"] = {},
		},
		["SOCIAL_NO_TEAM"] = {
			["Key"] = "SOCIAL_NO_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789531136'),
			["StringValueList"] = {},
		},
		["SOCIAL_NO_SYSTEM"] = {
			["Key"] = "SOCIAL_NO_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789531392'),
			["StringValueList"] = {},
		},
		["SOCIAL_DELETE_CHAT_FRIEND"] = {
			["Key"] = "SOCIAL_DELETE_CHAT_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789531648'),
			["StringValueList"] = {},
		},
		["SOCIAL_CLEAR_MESSAGE"] = {
			["Key"] = "SOCIAL_CLEAR_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789531904'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_LOUDSPEAKER"] = {
			["Key"] = "SOCIAL_CHAT_LOUDSPEAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789532160'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_1"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789532416'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_2"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789532672'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER_NAME_PACK_3"] = {
			["Key"] = "SOCIAL_CHAT_STICKER_NAME_PACK_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789532928'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789533184'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_EMPTY_BUTTON"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789533440'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_AT_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_AT_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789533696'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_HISTORY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789533952'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PREFIX_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_PREFIX_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789534208'),
			["StringValueList"] = {},
		},
		["SOCIAL_REVIVE_CD"] = {
			["Key"] = "SOCIAL_REVIVE_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789534464'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_APPLY_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_APPLY_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789534720'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SEARCH_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_SEARCH_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789534976'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_GUILD_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789535232'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD_EMPTY_BUTTON"] = {
			["Key"] = "SOCIAL_CHAT_GUILD_EMPTY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789535488'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHOOSE_AT"] = {
			["Key"] = "SOCIAL_CHAT_CHOOSE_AT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789535744'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_HISTORY"] = {
			["Key"] = "SOCIAL_CHAT_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789536000'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SETTING"] = {
			["Key"] = "SOCIAL_CHAT_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789536256'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_MY_INFORMATION"] = {
			["Key"] = "SOCIAL_CHAT_MY_INFORMATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789536512'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL_SETTING"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517568'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PREFIX"] = {
			["Key"] = "SOCIAL_CHAT_PREFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789537024'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_RECRUIT"] = {
			["Key"] = "SOCIAL_CHAT_SET_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789537280'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_URL"] = {
			["Key"] = "SOCIAL_CHAT_SET_URL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789537536'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_SMALLWINS"] = {
			["Key"] = "SOCIAL_CHAT_SET_SMALLWINS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789537792'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_CHATOPEN"] = {
			["Key"] = "SOCIAL_CHAT_SET_CHATOPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789538048'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_PATHFIND"] = {
			["Key"] = "SOCIAL_CHAT_SET_PATHFIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789538304'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_CHANNEL_SHOW"] = {
			["Key"] = "SOCIAL_CHAT_SET_CHANNEL_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789538560'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_WINS"] = {
			["Key"] = "SOCIAL_CHAT_SET_WINS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789538816'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_OTHERS"] = {
			["Key"] = "SOCIAL_CHAT_SET_OTHERS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789539072'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_COMMON_CHANNEL"] = {
			["Key"] = "SOCIAL_CHAT_SET_COMMON_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789539328'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_RECUIRT"] = {
			["Key"] = "SOCIAL_CHAT_SET_RECUIRT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789539584'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SPEAKER"] = {
			["Key"] = "SOCIAL_CHAT_SPEAKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789539840'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_ACTIVE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_ACTIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_PASSIVE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_PASSIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789540352'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GUILD"] = {
			["Key"] = "SOCIAL_CHAT_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789512704'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_EMOTION"] = {
			["Key"] = "SOCIAL_CHAT_EMOTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53601997162496'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_ITEM"] = {
			["Key"] = "SOCIAL_CHAT_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789466880'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REDPACK"] = {
			["Key"] = "SOCIAL_CHAT_REDPACK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789541376'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_COLLECT"] = {
			["Key"] = "SOCIAL_CHAT_COLLECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607256064'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_MISSION"] = {
			["Key"] = "SOCIAL_CHAT_MISSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584945408'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_SET_STALL"] = {
			["Key"] = "SOCIAL_CHAT_SET_STALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789542144'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_STICKER"] = {
			["Key"] = "SOCIAL_CHAT_STICKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789542400'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789542656'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789542912'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY_CE"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789543168'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY_CE"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789543424'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_NPC"] = {
			["Key"] = "SOCIAL_CHAT_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789543680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_AUDITION"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_AUDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789543936'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_FINISH"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789544192'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_RECORDING"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_RECORDING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789544448'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_COUNTDOWN"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789544704'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_IDENTIFY_FAIL"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_IDENTIFY_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789544960'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_NEW_MESSAGE"] = {
			["Key"] = "SOCIAL_CHAT_NEW_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789545216'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VOICE_PRESS"] = {
			["Key"] = "SOCIAL_CHAT_VOICE_PRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789545472'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_CONTROL"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_CONTROL",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_6872752981504'), Game.TableDataManager:GetLangStr('str_54633057981186'), Game.TableDataManager:GetLangStr('str_54633057981187'), Game.TableDataManager:GetLangStr('str_54633057981188'), Game.TableDataManager:GetLangStr('str_54633057981189'), Game.TableDataManager:GetLangStr('str_54633057981190'), Game.TableDataManager:GetLangStr('str_54633057981191'), Game.TableDataManager:GetLangStr('str_54633057981192'), Game.TableDataManager:GetLangStr('str_6872752982528'), Game.TableDataManager:GetLangStr('str_6872752983040')},
		},
		["SOCIAL_CHAT_IGNORE"] = {
			["Key"] = "SOCIAL_CHAT_IGNORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789545984'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REPLY"] = {
			["Key"] = "SOCIAL_CHAT_REPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811135232'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_ATREPLY"] = {
			["Key"] = "SOCIAL_CHAT_ATREPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811135232'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_COPY"] = {
			["Key"] = "SOCIAL_CHAT_COPY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789546752'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_REPORT"] = {
			["Key"] = "SOCIAL_CHAT_REPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924500992'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_BATTLE_DAMAGE_TYPE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_DAMAGE_TYPE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633057982721'), Game.TableDataManager:GetLangStr('str_54633057982722')},
		},
		["SOCIAL_CHAT_BATTLE_ELEMENT_TYPE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_ELEMENT_TYPE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_35735738520840'), Game.TableDataManager:GetLangStr('str_35735470089730'), Game.TableDataManager:GetLangStr('str_35735738526465'), Game.TableDataManager:GetLangStr('str_35735470089220')},
		},
		["SOCIAL_CHAT_BATTLE_CRITICAL_STRIKE"] = {
			["Key"] = "SOCIAL_CHAT_BATTLE_CRITICAL_STRIKE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789547776'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_VALUABLE_SHOW"] = {
			["Key"] = "SOCIAL_CHAT_VALUABLE_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789548032'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_PRIVATE_EMPTY"] = {
			["Key"] = "SOCIAL_CHAT_PRIVATE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789548288'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789548544'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_DETAIL"] = {
			["Key"] = "SOCIAL_CHAT_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789384448'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_FRIEND_INVITE_EMPTY"] = {
			["Key"] = "SOCIAL_GUILD_FRIEND_INVITE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789549056'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_INVITE_CONDITION"] = {
			["Key"] = "SOCIAL_GUILD_INVITE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789549312'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_FRIEND_INVITE"] = {
			["Key"] = "SOCIAL_GUILD_FRIEND_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178859008'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_CREATE_PLAYER_LEVEL"] = {
			["Key"] = "SOCIAL_GUILD_CREATE_PLAYER_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789549824'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_CREATE_MONEY"] = {
			["Key"] = "SOCIAL_GUILD_CREATE_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789550080'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_CONDITION"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789550336'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_FAIL"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789550592'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MERGE_TITLE"] = {
			["Key"] = "SOCIAL_GUILD_MERGE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789550848'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_LIMIT"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789551104'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_AFTER"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_AFTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789551360'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_RESPOSE_DETAILS_CD"] = {
			["Key"] = "SOCIAL_GUILD_RESPOSE_DETAILS_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789551616'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_BUILDING_LEVEL_MAX"] = {
			["Key"] = "SOCIAL_GUILD_BUILDING_LEVEL_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789551872'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789552128'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789552384'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_MAIN_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_MAIN_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789552640'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_PUB_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789552896'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789553152'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789553408'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_PUB_DESC_3"] = {
			["Key"] = "SOCIAL_GUILD_PUB_DESC_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789553664'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789553920'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789554176'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789554432'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_3"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789554688'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_VAULT_DESC_4"] = {
			["Key"] = "SOCIAL_GUILD_VAULT_DESC_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789554944'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_EFFECT"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789555200'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_DESC_1"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_DESC_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789555456'),
			["StringValueList"] = {},
		},
		["SOCIAL_GUILD_SCHOOL_DESC_2"] = {
			["Key"] = "SOCIAL_GUILD_SCHOOL_DESC_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789555712'),
			["StringValueList"] = {},
		},
		["SOCIAL_DUNGEON_STAGE_TIPS"] = {
			["Key"] = "SOCIAL_DUNGEON_STAGE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789555968'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_NO_DESCRIPTION"] = {
			["Key"] = "SOCIAL_FRIEND_NO_DESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789556224'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GROUPMESSAGE_HINT"] = {
			["Key"] = "SOCIAL_FRIEND_GROUPMESSAGE_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789556480'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_RELATION_CHANGE"] = {
			["Key"] = "SOCIAL_FRIEND_RELATION_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789556736'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_OPEN_BARRIAGE"] = {
			["Key"] = "SOCIAL_FRIEND_OPEN_BARRIAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789556992'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_PUT_TOP"] = {
			["Key"] = "SOCIAL_FRIEND_PUT_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789557248'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TODAY"] = {
			["Key"] = "SOCIAL_FRIEND_TODAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789557504'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_YESTER++"] = {
			["Key"] = "SOCIAL_FRIEND_YESTER++",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325568'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_BEFORE_YESTERDAY"] = {
			["Key"] = "SOCIAL_FRIEND_BEFORE_YESTERDAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789558016'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_NEW_FRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_NEW_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789521664'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_MODIFY_GROUP_INFO_BTN_OK"] = {
			["Key"] = "SOCIAL_FRIEND_MODIFY_GROUP_INFO_BTN_OK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_TITLE"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789558784'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_MODIFY"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789559040'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_GROUP_INPUT_HINT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_GROUP_INPUT_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789559296'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_CHAT_STATE_TEMP"] = {
			["Key"] = "SOCIAL_FRIEND_CHAT_STATE_TEMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789559552'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_RECENT"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_RECENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789559808'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_CLUB"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_CLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789560064'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_TOP_TAB_CONTACT"] = {
			["Key"] = "SOCIAL_FRIEND_TOP_TAB_CONTACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_MODIFY_GROUP_ALREADY_SELECT"] = {
			["Key"] = "SOCIAL_FRIEND_MODIFY_GROUP_ALREADY_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789560576'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_ACCEPT_ALL"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_ACCEPT_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178857728'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_IGNORE_ALL"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_IGNORE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789561088'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_NEXT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789561344'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_ADD_BTN"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_ADD_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789561600'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_EMPTY_GUILD_NAME"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_EMPTY_GUILD_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322496'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_ADD_FRIEND_APPLY_MSG_COUNT"] = {
			["Key"] = "SOCIAL_FRIEND_ADD_FRIEND_APPLY_MSG_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789562112'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_FAVOR_LEVEL"] = {
			["Key"] = "SOCIAL_FRIEND_FAVOR_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178867200'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCENE"] = {
			["Key"] = "SOCIAL_FRIEND_SCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789562624'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_PATTERN"] = {
			["Key"] = "SOCIAL_TEAM_PATTERN",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633057998337'), Game.TableDataManager:GetLangStr('str_54632789477376'), Game.TableDataManager:GetLangStr('str_54633057998339'), Game.TableDataManager:GetLangStr('str_54633057998340'), Game.TableDataManager:GetLangStr('str_54633057998341'), Game.TableDataManager:GetLangStr('str_54633057998342'), Game.TableDataManager:GetLangStr('str_54633057998343'), Game.TableDataManager:GetLangStr('str_54633057998344'), Game.TableDataManager:GetLangStr('str_54633057998345'), Game.TableDataManager:GetLangStr('str_54633057998346'), "%+%+"},
		},
		["SOCIAL_TEAM_PATTERN2"] = {
			["Key"] = "SOCIAL_TEAM_PATTERN2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789563136'),
			["StringValueList"] = {},
		},
		["SOCIAL_TEAM_SUPPORT_BULLET"] = {
			["Key"] = "SOCIAL_TEAM_SUPPORT_BULLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789563392'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_CHANNEL_PICK"] = {
			["Key"] = "SOCIAL_CHAT_CHANNEL_PICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789539328'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_NUM"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789563904'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_NUM"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789564160'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_APPLY_CLICK"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_APPLY_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789564416'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GROUP_APPLY_CLICK"] = {
			["Key"] = "SOCIAL_CHAT_GROUP_APPLY_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115428608'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_TEAM_INVITE"] = {
			["Key"] = "SOCIAL_CHAT_TEAM_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789564928'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE_TIPS"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789565184'),
			["StringValueList"] = {},
		},
		["SOCIAL_DISCLOSE_TAG"] = {
			["Key"] = "SOCIAL_DISCLOSE_TAG",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058000897'), Game.TableDataManager:GetLangStr('str_54633058000898')},
		},
		["SOCIAL_DISCLOSE"] = {
			["Key"] = "SOCIAL_DISCLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789565696'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANONYMOUS_DISCLOSE"] = {
			["Key"] = "SOCIAL_ANONYMOUS_DISCLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789565952'),
			["StringValueList"] = {},
		},
		["SOCIAL_BEDISCLOSED"] = {
			["Key"] = "SOCIAL_BEDISCLOSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789566208'),
			["StringValueList"] = {},
		},
		["SOCIAL_DISCLOSED"] = {
			["Key"] = "SOCIAL_DISCLOSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789566464'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_BUY_BUTTON"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_BUY_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31822486449664'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_GIVE_BUTTON"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_GIVE_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789566976'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_GIFT_CONFIRM_NOTE2"] = {
			["Key"] = "SOCIAL_FRIEND_GIFT_CONFIRM_NOTE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567232'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SAVE"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567488'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLE"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567744'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLE_TIP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLE_TIP",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789568256'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789568512'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLEFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLEFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789568768'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789569024'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789569280'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLEGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLEGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789569536'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_SELECTCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_SELECTCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789569792'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EMPTYCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EMPTYCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789570048'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_TITLECLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_TITLECLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789570304'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789570560'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVEFRIEND"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVEFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789570816'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789571072'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVEGROUP"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVEGROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789571328'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_ADDCLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_ADDCLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789571584'),
			["StringValueList"] = {},
		},
		["SOCIAL_FRIEND_SCOPE_EDIT_REMOVECLUB"] = {
			["Key"] = "SOCIAL_FRIEND_SCOPE_EDIT_REMOVECLUB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789571840'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_TAG"] = {
			["Key"] = "SOCIAL_ANOY_TAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789572096'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_DISCLOSE_SUCCESS"] = {
			["Key"] = "SOCIAL_ANOY_DISCLOSE_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789572352'),
			["StringValueList"] = {},
		},
		["SOCIAL_ANOY_DISCLOSE_FAIL"] = {
			["Key"] = "SOCIAL_ANOY_DISCLOSE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789572608'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARE"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789572864'),
			["StringValueList"] = {},
		},
		["SOCIAL_CREATE_GROUP_BUTTON"] = {
			["Key"] = "SOCIAL_CREATE_GROUP_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789573120'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_NODISTURB"] = {
			["Key"] = "SOCIAL_WHISPER_SET_NODISTURB",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789573376'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_TOP"] = {
			["Key"] = "SOCIAL_WHISPER_SET_TOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789557248'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_CHANGE_REMARK"] = {
			["Key"] = "SOCIAL_WHISPER_SET_CHANGE_REMARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517312'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_GROUP"] = {
			["Key"] = "SOCIAL_WHISPER_SET_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789574144'),
			["StringValueList"] = {},
		},
		["SOCIAL_WHISPER_SET_CLEAR_MESSAGE"] = {
			["Key"] = "SOCIAL_WHISPER_SET_CLEAR_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789524992'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_ALLONLINE"] = {
			["Key"] = "SOCIAL_RANGE_ALLONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789574656'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_PARTONLINE"] = {
			["Key"] = "SOCIAL_RANGE_PARTONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789574912'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_PARTOFFLINE"] = {
			["Key"] = "SOCIAL_RANGE_PARTOFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789575168'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_ALLOFFLINE"] = {
			["Key"] = "SOCIAL_RANGE_ALLOFFLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789575424'),
			["StringValueList"] = {},
		},
		["SOCIAL_RANGE_SETTING"] = {
			["Key"] = "SOCIAL_RANGE_SETTING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789575680'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TOPMSG"] = {
			["Key"] = "SOCIAL_CHATROOM_TOPMSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789575936'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMLIST_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMLIST_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789576192'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789576448'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_RETURNROOM_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_RETURNROOM_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789576704'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_FASTENTER_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_FASTENTER_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789576960'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CANCELCREATE_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_CANCELCREATE_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_NAMETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_NAMETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789577472'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789577728'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CREATEROOM_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_CREATEROOM_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789577984'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMMAIN_HINTWORD"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMMAIN_HINTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789578240'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SEND_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_SEND_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325824'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MEMBERS_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MEMBERS_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789578752'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_POWERSETTING_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_POWERSETTING_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789579008'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARETOCHANNEL"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARETOCHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789579264'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SHARETOFRIEND"] = {
			["Key"] = "SOCIAL_CHATROOM_SHARETOFRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789579520'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMNAME_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMNAME_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789577472'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMTYPE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMTYPE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789577728'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_INVITE"] = {
			["Key"] = "SOCIAL_CHATROOM_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789580288'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MICMANAGE_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MICMANAGE_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789580544'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_FORBIDSPEECH_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_FORBIDSPEECH_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789580800'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_BLACKLIST_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_BLACKLIST_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581056'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_TITLE"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581312'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581568'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_HINTTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_HINTTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581824'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_BTNNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_BTNNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582080'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MASTER_NAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MASTER_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5498363446784'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MEMBERLIST_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_MEMBERLIST_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582592'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLICATION_TABNAME"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLICATION_TABNAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582848'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ACCEPT_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ACCEPT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789583104'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_REJECT_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_REJECT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789583360'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_PERMISSION_HINTTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_PERMISSION_HINTTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789583616'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_APPLY_TITLETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_APPLY_TITLETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789583872'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_NOTICE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_NOTICE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789584128'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_AUTODESTROY_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_AUTODESTROY_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789584384'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789584640'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_ROOMINFO_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_ROOMINFO_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789584896'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_EXITROOM_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_EXITROOM_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789585152'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SAVESET_BTNTEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_SAVESET_BTNTEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581568'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TRANSFORM_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TRANSFORM_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789585664'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TRANSFORMMASTER_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_TRANSFORMMASTER_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789585920'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_INVITE_CARD"] = {
			["Key"] = "SOCIAL_CHAT_INVITE_CARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789586176'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_CURONMIC_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_CURONMIC_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789586432'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_MICOFF_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_MICOFF_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789586688'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_DENOISE_TEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_DENOISE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789586944'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_SIMPLE_TYPETEXT"] = {
			["Key"] = "SOCIAL_CHATROOM_SIMPLE_TYPETEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789584640'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHATROOM_TOTOP"] = {
			["Key"] = "SOCIAL_CHATROOM_TOTOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789516800'),
			["StringValueList"] = {},
		},
		["SOCIAL_CHAT_GOTO_END"] = {
			["Key"] = "SOCIAL_CHAT_GOTO_END",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789587712'),
			["StringValueList"] = {},
		},
		["MAIL_TITLE"] = {
			["Key"] = "MAIL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328570880'),
			["StringValueList"] = {},
		},
		["MAIL_NAV_TITLE"] = {
			["Key"] = "MAIL_NAV_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328570880'),
			["StringValueList"] = {},
		},
		["MAIL_SENDER_DESC"] = {
			["Key"] = "MAIL_SENDER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789588480'),
			["StringValueList"] = {},
		},
		["MAIL_TIME_DESC"] = {
			["Key"] = "MAIL_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789588736'),
			["StringValueList"] = {},
		},
		["MAIL_DELETE"] = {
			["Key"] = "MAIL_DELETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789358592'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVE"] = {
			["Key"] = "MAIL_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789589248'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVED_DESC"] = {
			["Key"] = "MAIL_RECEIVED_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789589504'),
			["StringValueList"] = {},
		},
		["MAIL_DAY_EXPIRE"] = {
			["Key"] = "MAIL_DAY_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789589760'),
			["StringValueList"] = {},
		},
		["MAIL_HOUR_EXPIRE"] = {
			["Key"] = "MAIL_HOUR_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789590016'),
			["StringValueList"] = {},
		},
		["MAIL_MIN_EXPIRE"] = {
			["Key"] = "MAIL_MIN_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789590272'),
			["StringValueList"] = {},
		},
		["MAIL_ONE_MIN_EXPIRE"] = {
			["Key"] = "MAIL_ONE_MIN_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789590528'),
			["StringValueList"] = {},
		},
		["MAIL_EXPIRE"] = {
			["Key"] = "MAIL_EXPIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789590784'),
			["StringValueList"] = {},
		},
		["MAIL_YMDHM_FORMAT"] = {
			["Key"] = "MAIL_YMDHM_FORMAT",
			["StringValue"] = "%s-%02d-%02d %02d:%02d",
			["StringValueList"] = {},
		},
		["MAIL_YMD_FORMAT"] = {
			["Key"] = "MAIL_YMD_FORMAT",
			["StringValue"] = "%s-%02d-%02d",
			["StringValueList"] = {},
		},
		["MAIL_CHURCH"] = {
			["Key"] = "MAIL_CHURCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["StringValueList"] = {},
		},
		["MAIL_TRADE_DESC"] = {
			["Key"] = "MAIL_TRADE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789591808'),
			["StringValueList"] = {},
		},
		["MAIL_RECEIVE_ALL"] = {
			["Key"] = "MAIL_RECEIVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789592064'),
			["StringValueList"] = {},
		},
		["MAIL_DELETE_READ"] = {
			["Key"] = "MAIL_DELETE_READ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789592320'),
			["StringValueList"] = {},
		},
		["MAIL_INBOX"] = {
			["Key"] = "MAIL_INBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789592576'),
			["StringValueList"] = {},
		},
		["MAIL_OUTBOX"] = {
			["Key"] = "MAIL_OUTBOX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789592832'),
			["StringValueList"] = {},
		},
		["MAIL_TEST"] = {
			["Key"] = "MAIL_TEST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789593088'),
			["StringValueList"] = {},
		},
		["MAIL_BLANK"] = {
			["Key"] = "MAIL_BLANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789593344'),
			["StringValueList"] = {},
		},
		["MAIL_SYSTEM"] = {
			["Key"] = "MAIL_SYSTEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_33673348910592'),
			["StringValueList"] = {},
		},
		["MAIL_NPC"] = {
			["Key"] = "MAIL_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789593856'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION"] = {
			["Key"] = "MAIL_COLLECTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924497664'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_REMOVE_ALL"] = {
			["Key"] = "MAIL_COLLECTION_REMOVE_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789594368'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_REMOVE_SINGLE"] = {
			["Key"] = "MAIL_COLLECTION_REMOVE_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789594624'),
			["StringValueList"] = {},
		},
		["MAIL_COLLECTION_COUNT"] = {
			["Key"] = "MAIL_COLLECTION_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789594880'),
			["StringValueList"] = {},
		},
		["LetterSlideOpenTips"] = {
			["Key"] = "LetterSlideOpenTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789595136'),
			["StringValueList"] = {},
		},
		["BookOpenTips"] = {
			["Key"] = "BookOpenTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789595392'),
			["StringValueList"] = {},
		},
		["DateDesc"] = {
			["Key"] = "DateDesc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789595648'),
			["StringValueList"] = {},
		},
		["Detaildesc"] = {
			["Key"] = "Detaildesc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789595904'),
			["StringValueList"] = {},
		},
		["PartyA_Desc"] = {
			["Key"] = "PartyA_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789596160'),
			["StringValueList"] = {},
		},
		["PartyB_Desc"] = {
			["Key"] = "PartyB_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789596416'),
			["StringValueList"] = {},
		},
		["PVP_ENTRANCE_SEASON_DESC"] = {
			["Key"] = "PVP_ENTRANCE_SEASON_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_28176327640576'),
			["StringValueList"] = {},
		},
		["PVP_ENTRANCE_SEASON_TIME_DESC"] = {
			["Key"] = "PVP_ENTRANCE_SEASON_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789596928'),
			["StringValueList"] = {},
		},
		["TEAM_SHOW_ENJOYABLE"] = {
			["Key"] = "TEAM_SHOW_ENJOYABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789597184'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_TEAM"] = {
			["Key"] = "TEAM_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584909312'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_TEAM_1"] = {
			["Key"] = "TEAM_QUICK_TEAM_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789597696'),
			["StringValueList"] = {},
		},
		["TEAM_CREATE_TEAM"] = {
			["Key"] = "TEAM_CREATE_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789597952'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_ALL_NEARBY_TEAM"] = {
			["Key"] = "TEAM_APPLY_ALL_NEARBY_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789598208'),
			["StringValueList"] = {},
		},
		["TEAM_ALLTARGET"] = {
			["Key"] = "TEAM_ALLTARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789598464'),
			["StringValueList"] = {},
		},
		["TEAM_NOTARGET"] = {
			["Key"] = "TEAM_NOTARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789598720'),
			["StringValueList"] = {},
		},
		["TEAM_INVITED"] = {
			["Key"] = "TEAM_INVITED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789598976'),
			["StringValueList"] = {},
		},
		["TEAM_APPLIED"] = {
			["Key"] = "TEAM_APPLIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178854656'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_LEADER"] = {
			["Key"] = "TEAM_APPLY_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789599488'),
			["StringValueList"] = {},
		},
		["TEAM_MATCHING"] = {
			["Key"] = "TEAM_MATCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789599744'),
			["StringValueList"] = {},
		},
		["TEAM_AROUND_TEAM"] = {
			["Key"] = "TEAM_AROUND_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789600000'),
			["StringValueList"] = {},
		},
		["TEAM_AROUND_TEAM_ONLY"] = {
			["Key"] = "TEAM_AROUND_TEAM_ONLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789600256'),
			["StringValueList"] = {},
		},
		["TEAM_TARGET_SCREEN"] = {
			["Key"] = "TEAM_TARGET_SCREEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789600512'),
			["StringValueList"] = {},
		},
		["TEAM_WHOSTEAM"] = {
			["Key"] = "TEAM_WHOSTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789406208'),
			["StringValueList"] = {},
		},
		["TEAM_NOLIMIT"] = {
			["Key"] = "TEAM_NOLIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789601024'),
			["StringValueList"] = {},
		},
		["TEAM_ALLIGNORE"] = {
			["Key"] = "TEAM_ALLIGNORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789561088'),
			["StringValueList"] = {},
		},
		["TEAM_FOLLOW"] = {
			["Key"] = "TEAM_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_3299608627200'),
			["StringValueList"] = {},
		},
		["TEAM_IS FOLLOW"] = {
			["Key"] = "TEAM_IS FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789601792'),
			["StringValueList"] = {},
		},
		["TEAM_START_FOLLOW"] = {
			["Key"] = "TEAM_START_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789602048'),
			["StringValueList"] = {},
		},
		["TEAM_MATCHING_TIME"] = {
			["Key"] = "TEAM_MATCHING_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789602304'),
			["StringValueList"] = {},
		},
		["TEAM_CANCLE_FOLLOW"] = {
			["Key"] = "TEAM_CANCLE_FOLLOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789352704'),
			["StringValueList"] = {},
		},
		["TEAM_MYTEAM"] = {
			["Key"] = "TEAM_MYTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789392896'),
			["StringValueList"] = {},
		},
		["TEAM_EXIT_TEAM"] = {
			["Key"] = "TEAM_EXIT_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789603072'),
			["StringValueList"] = {},
		},
		["TEAM_START_PLAY"] = {
			["Key"] = "TEAM_START_PLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789603328'),
			["StringValueList"] = {},
		},
		["TEAM_AUTO_MATCH"] = {
			["Key"] = "TEAM_AUTO_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789603584'),
			["StringValueList"] = {},
		},
		["TEAM_QUICK_MATCH"] = {
			["Key"] = "TEAM_QUICK_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789603840'),
			["StringValueList"] = {},
		},
		["TEAM_MATCH_TEAMER"] = {
			["Key"] = "TEAM_MATCH_TEAMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789604096'),
			["StringValueList"] = {},
		},
		["TEAM_LOCATION"] = {
			["Key"] = "TEAM_LOCATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789604352'),
			["StringValueList"] = {},
		},
		["TEAM_NEIBOR_NO_SINGLE"] = {
			["Key"] = "TEAM_NEIBOR_NO_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789604608'),
			["StringValueList"] = {},
		},
		["TEAM_NO_FRIEND"] = {
			["Key"] = "TEAM_NO_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789604864'),
			["StringValueList"] = {},
		},
		["TEAM_GUILD_FRUIEND"] = {
			["Key"] = "TEAM_GUILD_FRUIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789605120'),
			["StringValueList"] = {},
		},
		["TEAM_FRIEND"] = {
			["Key"] = "TEAM_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["StringValueList"] = {},
		},
		["TEAM_GUILD"] = {
			["Key"] = "TEAM_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["StringValueList"] = {},
		},
		["TEAM_NEIBOR"] = {
			["Key"] = "TEAM_NEIBOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789513728'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_TO_BE_LEADER"] = {
			["Key"] = "TEAM_APPLY_TO_BE_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115430144'),
			["StringValueList"] = {},
		},
		["TEAM_REPLY_LEADER"] = {
			["Key"] = "TEAM_REPLY_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789606400'),
			["StringValueList"] = {},
		},
		["TEAM_START_GATHER"] = {
			["Key"] = "TEAM_START_GATHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789606656'),
			["StringValueList"] = {},
		},
		["TEAM_CANT_ENJION"] = {
			["Key"] = "TEAM_CANT_ENJION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789606912'),
			["StringValueList"] = {},
		},
		["TEAM_CANT_INVITE"] = {
			["Key"] = "TEAM_CANT_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789607168'),
			["StringValueList"] = {},
		},
		["TEAM_YOUHAD"] = {
			["Key"] = "TEAM_YOUHAD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789607424'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_FRIEND"] = {
			["Key"] = "TEAM_APPLY_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789521664'),
			["StringValueList"] = {},
		},
		["TEAM_CE"] = {
			["Key"] = "TEAM_CE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789469184'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_CREATETEAM"] = {
			["Key"] = "TEAM_INVITE_CREATETEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789564928'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_COMBINETEAM"] = {
			["Key"] = "TEAM_APPLY_COMBINETEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789608448'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_JOINTEAM"] = {
			["Key"] = "TEAM_APPLY_JOINTEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789564416'),
			["StringValueList"] = {},
		},
		["TEAM_CE_UNLIMITED"] = {
			["Key"] = "TEAM_CE_UNLIMITED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789608960'),
			["StringValueList"] = {},
		},
		["TEAM_RECRUIT_MSG"] = {
			["Key"] = "TEAM_RECRUIT_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789609216'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058044673'), Game.TableDataManager:GetLangStr('str_54633058044674'), Game.TableDataManager:GetLangStr('str_54633058044675')},
		},
		["TEAM_RECRUIT_ROW_MSG"] = {
			["Key"] = "TEAM_RECRUIT_ROW_MSG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789609472'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058044929'), Game.TableDataManager:GetLangStr('str_54633058044930'), Game.TableDataManager:GetLangStr('str_54633058044931')},
		},
		["TEAM_FRIEND_NO_SINGLE"] = {
			["Key"] = "TEAM_FRIEND_NO_SINGLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789609728'),
			["StringValueList"] = {},
		},
		["TEAM_TRANSFFORM_CAPTAIN"] = {
			["Key"] = "TEAM_TRANSFFORM_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789609984'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_CAPTAIN"] = {
			["Key"] = "TEAM_APPLY_CAPTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789599488'),
			["StringValueList"] = {},
		},
		["TEAM_KICK_TEAM"] = {
			["Key"] = "TEAM_KICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789610496'),
			["StringValueList"] = {},
		},
		["TEAM_TEAM"] = {
			["Key"] = "TEAM_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789610752'),
			["StringValueList"] = {},
		},
		["TEAM_OUTPUT"] = {
			["Key"] = "TEAM_OUTPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_48105244329728'),
			["StringValueList"] = {},
		},
		["TEAM_HEAL"] = {
			["Key"] = "TEAM_HEAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318912'),
			["StringValueList"] = {},
		},
		["TEAM_DEFEND"] = {
			["Key"] = "TEAM_DEFEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
			["StringValueList"] = {},
		},
		["TEAM_INPUT"] = {
			["Key"] = "TEAM_INPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789611776'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_YOUR_JOIN_TEAM"] = {
			["Key"] = "TEAM_INVITE_YOUR_JOIN_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789612032'),
			["StringValueList"] = {},
		},
		["TEAM_AUTO_RECEIVE_FOLLOW_APPLY"] = {
			["Key"] = "TEAM_AUTO_RECEIVE_FOLLOW_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789612288'),
			["StringValueList"] = {},
		},
		["GROUP_FIRST"] = {
			["Key"] = "GROUP_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789338624'),
			["StringValueList"] = {},
		},
		["GROUP_SECOND"] = {
			["Key"] = "GROUP_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789338880'),
			["StringValueList"] = {},
		},
		["GROUP_THIRD"] = {
			["Key"] = "GROUP_THIRD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339136'),
			["StringValueList"] = {},
		},
		["GROUP_FOURTH"] = {
			["Key"] = "GROUP_FOURTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339392'),
			["StringValueList"] = {},
		},
		["GROUP_FIFTH"] = {
			["Key"] = "GROUP_FIFTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789339648'),
			["StringValueList"] = {},
		},
		["GROUP_GROUP"] = {
			["Key"] = "GROUP_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789613824'),
			["StringValueList"] = {},
		},
		["GROUP_CREATE_GROUP"] = {
			["Key"] = "GROUP_CREATE_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789614080'),
			["StringValueList"] = {},
		},
		["GROUP_MY_GROUP"] = {
			["Key"] = "GROUP_MY_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789393152'),
			["StringValueList"] = {},
		},
		["GROUP_VIEW_GROUP"] = {
			["Key"] = "GROUP_VIEW_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789614592'),
			["StringValueList"] = {},
		},
		["GROUP_X_GROUP"] = {
			["Key"] = "GROUP_X_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789614848'),
			["StringValueList"] = {},
		},
		["GROUP_DISBAND_GROUP"] = {
			["Key"] = "GROUP_DISBAND_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789615104'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_GROUP"] = {
			["Key"] = "GROUP_QUIT_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789615360'),
			["StringValueList"] = {},
		},
		["TEAM_TO_GROUP"] = {
			["Key"] = "TEAM_TO_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789615616'),
			["StringValueList"] = {},
		},
		["GROUP_AUTO_MATCH"] = {
			["Key"] = "GROUP_AUTO_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789603584'),
			["StringValueList"] = {},
		},
		["GROUP_READY_CHECK"] = {
			["Key"] = "GROUP_READY_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789616128'),
			["StringValueList"] = {},
		},
		["GROUP_ADJUST_POS"] = {
			["Key"] = "GROUP_ADJUST_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789616384'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_ADJUST_POS"] = {
			["Key"] = "GROUP_QUIT_ADJUST_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789616640'),
			["StringValueList"] = {},
		},
		["GROUP_IN_CHECK"] = {
			["Key"] = "GROUP_IN_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789616896'),
			["StringValueList"] = {},
		},
		["GROUP_CHECK_INFO"] = {
			["Key"] = "GROUP_CHECK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789617152'),
			["StringValueList"] = {},
		},
		["GROUP_SET_MANAGER"] = {
			["Key"] = "GROUP_SET_MANAGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789617408'),
			["StringValueList"] = {},
		},
		["GROUP_UNSET_MANAGER"] = {
			["Key"] = "GROUP_UNSET_MANAGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924498944'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT"] = {
			["Key"] = "GROUP_SHOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789617920'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_RECRUIT"] = {
			["Key"] = "GROUP_SHOUT_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789618176'),
			["StringValueList"] = {},
		},
		["GROUP_GUILD_CHANNEL"] = {
			["Key"] = "GROUP_GUILD_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789618432'),
			["StringValueList"] = {},
		},
		["GROUP_RECRUIT_CHANNEL"] = {
			["Key"] = "GROUP_RECRUIT_CHANNEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789618688'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_DEFAULT_MESSAGE"] = {
			["Key"] = "GROUP_SHOUT_DEFAULT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789618944'),
			["StringValueList"] = {},
		},
		["GROUP_HISTORT_MESSAGE"] = {
			["Key"] = "GROUP_HISTORT_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789536000'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_HINT"] = {
			["Key"] = "GROUP_SHOUT_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789619456'),
			["StringValueList"] = {},
		},
		["GROUP_SHOUT_HISTORY"] = {
			["Key"] = "GROUP_SHOUT_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789619712'),
			["StringValueList"] = {},
		},
		["GROUP_REVIVE"] = {
			["Key"] = "GROUP_REVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353216'),
			["StringValueList"] = {},
		},
		["GROUP_VIEW_TEAM"] = {
			["Key"] = "GROUP_VIEW_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789620224'),
			["StringValueList"] = {},
		},
		["GROUP_CHARACTER_INFO"] = {
			["Key"] = "GROUP_CHARACTER_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811132672'),
			["StringValueList"] = {},
		},
		["GROUP_KICK_GROUP"] = {
			["Key"] = "GROUP_KICK_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789620736'),
			["StringValueList"] = {},
		},
		["GROUP_TRANS_GROUP_LEADER"] = {
			["Key"] = "GROUP_TRANS_GROUP_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789620992'),
			["StringValueList"] = {},
		},
		["GROUP_BAN_VOICE"] = {
			["Key"] = "GROUP_BAN_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789621248'),
			["StringValueList"] = {},
		},
		["GROUP_CANCEL_VOICE"] = {
			["Key"] = "GROUP_CANCEL_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789621504'),
			["StringValueList"] = {},
		},
		["GROUP_TRANS_TEAM_LEADER"] = {
			["Key"] = "GROUP_TRANS_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789621760'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_JOIN_GROUP"] = {
			["Key"] = "GROUP_APPLY_JOIN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115428608'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_GROUP_TEAM_LEADER"] = {
			["Key"] = "GROUP_APPLY_GROUP_TEAM_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789599488'),
			["StringValueList"] = {},
		},
		["GROUP_INVITE_JOIN_TEAM"] = {
			["Key"] = "GROUP_INVITE_JOIN_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789622528'),
			["StringValueList"] = {},
		},
		["GROUP_KICK_GROUPMEMBER"] = {
			["Key"] = "GROUP_KICK_GROUPMEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789622784'),
			["StringValueList"] = {},
		},
		["GROUP_PROMOTE_TEAMLEADER"] = {
			["Key"] = "GROUP_PROMOTE_TEAMLEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789623040'),
			["StringValueList"] = {},
		},
		["GROUP_APPLY_JOIN_MESSAGE"] = {
			["Key"] = "GROUP_APPLY_JOIN_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789623296'),
			["StringValueList"] = {},
		},
		["GROUP_INVITE_YOUR_JOIN_GROUP"] = {
			["Key"] = "GROUP_INVITE_YOUR_JOIN_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789623552'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_LISTEN"] = {
			["Key"] = "TEAM_VOICE_LISTEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789623808'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_REFUSE"] = {
			["Key"] = "TEAM_VOICE_REFUSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789624064'),
			["StringValueList"] = {},
		},
		["TEAM_VOICE_OPENMIC"] = {
			["Key"] = "TEAM_VOICE_OPENMIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789624320'),
			["StringValueList"] = {},
		},
		["GROUP_AUTO_AGREE_APPLY"] = {
			["Key"] = "GROUP_AUTO_AGREE_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789624576'),
			["StringValueList"] = {},
		},
		["GROUP_LEFT_COUNT"] = {
			["Key"] = "GROUP_LEFT_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178851072'),
			["StringValueList"] = {},
		},
		["TEAM_RECENT"] = {
			["Key"] = "TEAM_RECENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789559808'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_LIST"] = {
			["Key"] = "TEAM_APPLY_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789625344'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_1"] = {
			["Key"] = "TEAM_SUPPORT_LINE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789625600'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_2"] = {
			["Key"] = "TEAM_SUPPORT_LINE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789625856'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_3"] = {
			["Key"] = "TEAM_SUPPORT_LINE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789626112'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_4"] = {
			["Key"] = "TEAM_SUPPORT_LINE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789626368'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_LINE_5"] = {
			["Key"] = "TEAM_SUPPORT_LINE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789626624'),
			["StringValueList"] = {},
		},
		["TEAM_SUPPORT_THANKS_1"] = {
			["Key"] = "TEAM_SUPPORT_THANKS_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789626880'),
			["StringValueList"] = {},
		},
		["TEAM_MESSAGE_NUM_LIMIT"] = {
			["Key"] = "TEAM_MESSAGE_NUM_LIMIT",
			["StringValue"] = "%s /%s",
			["StringValueList"] = {},
		},
		["TEAM_LIST_EMPTY"] = {
			["Key"] = "TEAM_LIST_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789627392'),
			["StringValueList"] = {},
		},
		["TEAM_CANCEL_MATCH"] = {
			["Key"] = "TEAM_CANCEL_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789627648'),
			["StringValueList"] = {},
		},
		["FRIEND_APPLY_TIP"] = {
			["Key"] = "FRIEND_APPLY_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789627904'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_EMPTY"] = {
			["Key"] = "TEAM_INVITE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789628160'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_TAB_FRIEND"] = {
			["Key"] = "TEAM_INVITE_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_TAB_STRANGER"] = {
			["Key"] = "TEAM_INVITE_TAB_STRANGER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789628672'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_MESSAGE"] = {
			["Key"] = "TEAM_APPLY_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789628928'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_MESSAGE_SAVE"] = {
			["Key"] = "TEAM_APPLY_MESSAGE_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567488'),
			["StringValueList"] = {},
		},
		["TEAM_REBIRTH"] = {
			["Key"] = "TEAM_REBIRTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353216'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_SCENE"] = {
			["Key"] = "TEAM_MARK_SCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789629696'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_SCENE_DESC"] = {
			["Key"] = "TEAM_MARK_SCENE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789629952'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_MEMBER"] = {
			["Key"] = "TEAM_MARK_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789630208'),
			["StringValueList"] = {},
		},
		["TEAM_MARK_MEMBER_DESC"] = {
			["Key"] = "TEAM_MARK_MEMBER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789630464'),
			["StringValueList"] = {},
		},
		["TEAM_EXPECTED_MATCH_TIME"] = {
			["Key"] = "TEAM_EXPECTED_MATCH_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789373952'),
			["StringValueList"] = {},
		},
		["TEAM_HASUSED_MATCH_TIME"] = {
			["Key"] = "TEAM_HASUSED_MATCH_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789630976'),
			["StringValueList"] = {},
		},
		["MORE_DETAIL"] = {
			["Key"] = "MORE_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607240448'),
			["StringValueList"] = {},
		},
		["PERSON"] = {
			["Key"] = "PERSON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735738517000'),
			["StringValueList"] = {},
		},
		["TEAM_INDIVIDUALPVP"] = {
			["Key"] = "TEAM_INDIVIDUALPVP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115431680'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_FIRST"] = {
			["Key"] = "TEAM_APPLY_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789632000'),
			["StringValueList"] = {},
		},
		["TEAM_APPLY_AFFIX"] = {
			["Key"] = "TEAM_APPLY_AFFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789632256'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_FIRST"] = {
			["Key"] = "TEAM_INVITE_FIRST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789632512'),
			["StringValueList"] = {},
		},
		["TEAM_INVITATION_AFFIX"] = {
			["Key"] = "TEAM_INVITATION_AFFIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789632768'),
			["StringValueList"] = {},
		},
		["TEAM_MATCH_PAGE"] = {
			["Key"] = "TEAM_MATCH_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789633024'),
			["StringValueList"] = {},
		},
		["TEAM_SETMANSGER_HINT"] = {
			["Key"] = "TEAM_SETMANSGER_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789633280'),
			["StringValueList"] = {},
		},
		["TEAM_GO_TARGET"] = {
			["Key"] = "TEAM_GO_TARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789633536'),
			["StringValueList"] = {},
		},
		["TEAM_NO_APPLY"] = {
			["Key"] = "TEAM_NO_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789633792'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_DPS"] = {
			["Key"] = "TARGET_NEED_DPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789634048'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_TANK"] = {
			["Key"] = "TARGET_NEED_TANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789634304'),
			["StringValueList"] = {},
		},
		["TARGET_NEED_SUPPORT"] = {
			["Key"] = "TARGET_NEED_SUPPORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789634560'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_NO_GUILD"] = {
			["Key"] = "TEAM_INVITE_NO_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789634816'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_NO_GUILD_BUTTON"] = {
			["Key"] = "TEAM_INVITE_NO_GUILD_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789535488'),
			["StringValueList"] = {},
		},
		["TEAM_SCENE_MARK_INFO"] = {
			["Key"] = "TEAM_SCENE_MARK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789635328'),
			["StringValueList"] = {},
		},
		["TEAM_MEMBER_MARK_INFO"] = {
			["Key"] = "TEAM_MEMBER_MARK_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789635584'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_TO_TEAM"] = {
			["Key"] = "GROUP_QUIT_TO_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789635840'),
			["StringValueList"] = {},
		},
		["GROUP_QUIT_TO_GROUP"] = {
			["Key"] = "GROUP_QUIT_TO_GROUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789636096'),
			["StringValueList"] = {},
		},
		["LEAGUE_INVITE"] = {
			["Key"] = "LEAGUE_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115429632'),
			["StringValueList"] = {},
		},
		["APPLY_GROUP_LEADER"] = {
			["Key"] = "APPLY_GROUP_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789636608'),
			["StringValueList"] = {},
		},
		["INVITE_SEND_TO_RECRUIT"] = {
			["Key"] = "INVITE_SEND_TO_RECRUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789636864'),
			["StringValueList"] = {},
		},
		["INVITE_SEND_TO_GUILD"] = {
			["Key"] = "INVITE_SEND_TO_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789637120'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE_SLOGAN"] = {
			["Key"] = "TEAM_RESCUE_SLOGAN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789637376'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE"] = {
			["Key"] = "TEAM_RESCUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789637632'),
			["StringValueList"] = {},
		},
		["TEAM_RESCUE_DEFAULT"] = {
			["Key"] = "TEAM_RESCUE_DEFAULT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789637888'),
			["StringValueList"] = {},
		},
		["TEAM_QUIKE_TEAM_UP"] = {
			["Key"] = "TEAM_QUIKE_TEAM_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584909312'),
			["StringValueList"] = {},
		},
		["TEAM_SET_TRARGET"] = {
			["Key"] = "TEAM_SET_TRARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789638400'),
			["StringValueList"] = {},
		},
		["GROUP_MEMBER_HINT"] = {
			["Key"] = "GROUP_MEMBER_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789638656'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_APPLY_HINT"] = {
			["Key"] = "TEAM_INVITE_APPLY_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789638912'),
			["StringValueList"] = {},
		},
		["TEAM_INVITE_APPLY_GROUP_HINT"] = {
			["Key"] = "TEAM_INVITE_APPLY_GROUP_HINT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789639168'),
			["StringValueList"] = {},
		},
		["TASK_TASK"] = {
			["Key"] = "TASK_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584945408'),
			["StringValueList"] = {},
		},
		["TASK_TRACE_TASK"] = {
			["Key"] = "TASK_TRACE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789639680'),
			["StringValueList"] = {},
		},
		["TASK_CANCEL_TRACE"] = {
			["Key"] = "TASK_CANCEL_TRACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789639936'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_TASK_GAIN"] = {
			["Key"] = "TASK_COMPLETE_TASK_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789640192'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_TASKS_GAIN"] = {
			["Key"] = "TASK_COMPLETE_TASKS_GAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789640448'),
			["StringValueList"] = {},
		},
		["TASK_FORGIVE_TASK"] = {
			["Key"] = "TASK_FORGIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789640704'),
			["StringValueList"] = {},
		},
		["TASK_MAIN_TASK"] = {
			["Key"] = "TASK_MAIN_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789640960'),
			["StringValueList"] = {},
		},
		["TASK_BRANCH_TASK"] = {
			["Key"] = "TASK_BRANCH_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789641216'),
			["StringValueList"] = {},
		},
		["SUBMIT_ITEM_TITLE"] = {
			["Key"] = "SUBMIT_ITEM_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789641472'),
			["StringValueList"] = {},
		},
		["SUBMIT_ITEM_CONFIRM"] = {
			["Key"] = "SUBMIT_ITEM_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789641728'),
			["StringValueList"] = {},
		},
		["HUD_EXIT_PLANE"] = {
			["Key"] = "HUD_EXIT_PLANE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789641984'),
			["StringValueList"] = {},
		},
		["TASK_BACK_PLANE"] = {
			["Key"] = "TASK_BACK_PLANE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789642240'),
			["StringValueList"] = {},
		},
		["TASK_TRACE_DISTANCE"] = {
			["Key"] = "TASK_TRACE_DISTANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789642496'),
			["StringValueList"] = {},
		},
		["TASK_COMPLETE_REMINDER"] = {
			["Key"] = "TASK_COMPLETE_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789352960'),
			["StringValueList"] = {},
		},
		["TASK_GUILD_WAITING"] = {
			["Key"] = "TASK_GUILD_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789643008'),
			["StringValueList"] = {},
		},
		["TOWER_CLIMB_TIME_SURVIVAL"] = {
			["Key"] = "TOWER_CLIMB_TIME_SURVIVAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789643264'),
			["StringValueList"] = {},
		},
		["TASK_CLICK_GOTO"] = {
			["Key"] = "TASK_CLICK_GOTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789643520'),
			["StringValueList"] = {},
		},
		["TASK_MAP_ARBITRARY"] = {
			["Key"] = "TASK_MAP_ARBITRARY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789643776'),
			["StringValueList"] = {},
		},
		["TASK_TAG_ALL"] = {
			["Key"] = "TASK_TAG_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
			["StringValueList"] = {},
		},
		["QUEST_REPLAY_CUTSCENE"] = {
			["Key"] = "QUEST_REPLAY_CUTSCENE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789644288'),
			["StringValueList"] = {},
		},
		["QUEST_REPLAY_DIALOGUE"] = {
			["Key"] = "QUEST_REPLAY_DIALOGUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789644544'),
			["StringValueList"] = {},
		},
		["REMAIN_TIME"] = {
			["Key"] = "REMAIN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789644800'),
			["StringValueList"] = {},
		},
		["TASK_REMAINTIME_HOUR"] = {
			["Key"] = "TASK_REMAINTIME_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789645056'),
			["StringValueList"] = {},
		},
		["TASK_REMAINTIME_DAY"] = {
			["Key"] = "TASK_REMAINTIME_DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789645312'),
			["StringValueList"] = {},
		},
		["TOBECONTINUED"] = {
			["Key"] = "TOBECONTINUED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789645568'),
			["StringValueList"] = {},
		},
		["PLAYAGAIN"] = {
			["Key"] = "PLAYAGAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789645824'),
			["StringValueList"] = {},
		},
		["TASK_TAG_PROGRESS"] = {
			["Key"] = "TASK_TAG_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789646080'),
			["StringValueList"] = {},
		},
		["TASK_TAG_ACCEPTABLE"] = {
			["Key"] = "TASK_TAG_ACCEPTABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789646336'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILLCUSTOMIZER"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILLCUSTOMIZER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450656000'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_MAIN_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_MAIN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450656000'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEQUENCE_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_SEQUENCE_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789647104'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_LEVEL_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_LEVEL_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789647360'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_ALREADY_MAX_LEVEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_ALREADY_MAX_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789647616'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_UPGRADE"] = {
			["Key"] = "SKILLCUSTOMIZER_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_UNLOCK"] = {
			["Key"] = "SKILLCUSTOMIZER_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092641280'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_LEVEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789648384'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_LOCKED_LEVEL_NOT_QUALIFY"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_LOCKED_LEVEL_NOT_QUALIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789648640'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEQUENCE_SKILL"] = {
			["Key"] = "SKILLCUSTOMIZER_SEQUENCE_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789648896'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SEPCIAL_SKILL"] = {
			["Key"] = "SKILLCUSTOMIZER_SEPCIAL_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789649152'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_EQUIPED"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_EQUIPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789408512'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_EQUIPED_NOT_REQUIRED"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_EQUIPED_NOT_REQUIRED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789649664'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_BUTTON"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789649920'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_BUTTON_USING"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_BUTTON_USING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650176'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_PRESET_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_PRESET_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650432'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_TITLE"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650688'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_CONFIRM"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RENAME_CANCEL"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RENAME_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_COLDDOWN_TIME"] = {
			["Key"] = "SKILLCUSTOMIZER_COLDDOWN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789651456'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_TAB_UPGRADE"] = {
			["Key"] = "SKILLCUSTOMIZER_TAB_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_TAB_EQUIP"] = {
			["Key"] = "SKILLCUSTOMIZER_TAB_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789651968'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_COLDDOWN_TIME_FORMAT"] = {
			["Key"] = "SKILLCUSTOMIZER_COLDDOWN_TIME_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789652224'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_LEVEL_FORMAT"] = {
			["Key"] = "SKILLCUSTOMIZER_LEVEL_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327360'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_CE_ADD_UP"] = {
			["Key"] = "SKILLCUSTOMIZER_CE_ADD_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789652736'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK1"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789652992'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK2"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653248'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK3"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653504'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK4"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653760'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK5"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789654016'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_SKILL_RANK6"] = {
			["Key"] = "SKILLCUSTOMIZER_SKILL_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789654272'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK1"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789652992'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK2"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653248'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK3"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653504'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK4"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789653760'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK5"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789654016'),
			["StringValueList"] = {},
		},
		["SKILLCUSTOMIZER_FELLOWSKILL_RANK6"] = {
			["Key"] = "SKILLCUSTOMIZER_FELLOWSKILL_RANK6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789654272'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_ROLESKILL"] = {
			["Key"] = "SKILL_TAB_ROLESKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_FELLOWSKILL"] = {
			["Key"] = "SKILL_TAB_FELLOWSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450660352'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_DISCOVERYSKILL"] = {
			["Key"] = "SKILL_TAB_DISCOVERYSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175424'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_IDENTITYSKILL"] = {
			["Key"] = "SKILL_TAB_IDENTITYSKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175680'),
			["StringValueList"] = {},
		},
		["SKILL_TAB_Preset"] = {
			["Key"] = "SKILL_TAB_Preset",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789657088'),
			["StringValueList"] = {},
		},
		["SKILL_CE_TEXT"] = {
			["Key"] = "SKILL_CE_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789652736'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TITLE"] = {
			["Key"] = "SKILL_PRESET_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789657088'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_COUNT_TEXT"] = {
			["Key"] = "SKILL_PRESET_COUNT_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789657856'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TEXT"] = {
			["Key"] = "SKILL_PRESET_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789658112'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_BUTTON_USE"] = {
			["Key"] = "SKILL_PRESET_BUTTON_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789649920'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_TITLE_ADVICE"] = {
			["Key"] = "SKILL_PRESET_TITLE_ADVICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789658624'),
			["StringValueList"] = {},
		},
		["SKILL_PRESET_BUTTON_SAVE"] = {
			["Key"] = "SKILL_PRESET_BUTTON_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567488'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON_UNLOCK"] = {
			["Key"] = "SKILL_FELLOW_BUTTON_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789659136'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON"] = {
			["Key"] = "SKILL_FELLOW_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789659392'),
			["StringValueList"] = {},
		},
		["SKILL_FELLOW_BUTTON_GO"] = {
			["Key"] = "SKILL_FELLOW_BUTTON_GO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789659648'),
			["StringValueList"] = {},
		},
		["SKILL_IDENTITY_BUTTON_GO"] = {
			["Key"] = "SKILL_IDENTITY_BUTTON_GO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789659904'),
			["StringValueList"] = {},
		},
		["PRESET_SKILL"] = {
			["Key"] = "PRESET_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450656000'),
			["StringValueList"] = {},
		},
		["PRESET_SEALED"] = {
			["Key"] = "PRESET_SEALED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["PRESET_TRAITS"] = {
			["Key"] = "PRESET_TRAITS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789660672'),
			["StringValueList"] = {},
		},
		["PRESET_REPLACE_TITLE"] = {
			["Key"] = "PRESET_REPLACE_TITLE",
			["StringValue"] = "%s",
			["StringValueList"] = {},
		},
		["SKILL_TAB1_NAME"] = {
			["Key"] = "SKILL_TAB1_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE1"] = {
			["Key"] = "SKILL_TAB1_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789648896'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE2"] = {
			["Key"] = "SKILL_TAB1_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789649152'),
			["StringValueList"] = {},
		},
		["SKILL_TAB1_TITLE3"] = {
			["Key"] = "SKILL_TAB1_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646720'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_NAME"] = {
			["Key"] = "SKILL_TAB2_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662208'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE1"] = {
			["Key"] = "SKILL_TAB2_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646464'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE2"] = {
			["Key"] = "SKILL_TAB2_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB2_TITLE3"] = {
			["Key"] = "SKILL_TAB2_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_NAME"] = {
			["Key"] = "SKILL_TAB3_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789663232'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE1"] = {
			["Key"] = "SKILL_TAB3_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE2"] = {
			["Key"] = "SKILL_TAB3_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB3_TITLE3"] = {
			["Key"] = "SKILL_TAB3_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_NAME"] = {
			["Key"] = "SKILL_TAB4_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789664256'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE1"] = {
			["Key"] = "SKILL_TAB4_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175424'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE2"] = {
			["Key"] = "SKILL_TAB4_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB4_TITLE3"] = {
			["Key"] = "SKILL_TAB4_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_NAME"] = {
			["Key"] = "SKILL_TAB5_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646208'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE1"] = {
			["Key"] = "SKILL_TAB5_TITLE1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175680'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE2"] = {
			["Key"] = "SKILL_TAB5_TITLE2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_TAB5_TITLE3"] = {
			["Key"] = "SKILL_TAB5_TITLE3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789662976'),
			["StringValueList"] = {},
		},
		["SKILL_UNLOCK_UIJUMP"] = {
			["Key"] = "SKILL_UNLOCK_UIJUMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789666304'),
			["StringValueList"] = {},
		},
		["SKILL_AUTO_ON"] = {
			["Key"] = "SKILL_AUTO_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789666560'),
			["StringValueList"] = {},
		},
		["SKILL_AUTO_OFF"] = {
			["Key"] = "SKILL_AUTO_OFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789666816'),
			["StringValueList"] = {},
		},
		["SKILL_TIP_LV"] = {
			["Key"] = "SKILL_TIP_LV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789419264'),
			["StringValueList"] = {},
		},
		["SKILL_TIP_ADDITIONAL_LV"] = {
			["Key"] = "SKILL_TIP_ADDITIONAL_LV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178867200'),
			["StringValueList"] = {},
		},
		["SKILL_INFO_BRIEF"] = {
			["Key"] = "SKILL_INFO_BRIEF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789667584'),
			["StringValueList"] = {},
		},
		["SKILL_INFO_DETAIL"] = {
			["Key"] = "SKILL_INFO_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789384448'),
			["StringValueList"] = {},
		},
		["SKILL_ELEMENT_RESONATE_LEVEL"] = {
			["Key"] = "SKILL_ELEMENT_RESONATE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789501696'),
			["StringValueList"] = {},
		},
		["EQUIP_TITLE"] = {
			["Key"] = "EQUIP_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789668352'),
			["StringValueList"] = {},
		},
		["EQUIP_LEVEL"] = {
			["Key"] = "EQUIP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178867200'),
			["StringValueList"] = {},
		},
		["EQUIP_SCORE"] = {
			["Key"] = "EQUIP_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789668864'),
			["StringValueList"] = {},
		},
		["EQUIP_TC"] = {
			["Key"] = "EQUIP_TC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789669120'),
			["StringValueList"] = {},
		},
		["EQUIP_MYSTERY_ENTRY"] = {
			["Key"] = "EQUIP_MYSTERY_ENTRY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789669376'),
			["StringValueList"] = {},
		},
		["EQUIP_WAIT_TO_OPENED"] = {
			["Key"] = "EQUIP_WAIT_TO_OPENED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789669632'),
			["StringValueList"] = {},
		},
		["EQUIP_RANDOM_PROP_SYMBOL"] = {
			["Key"] = "EQUIP_RANDOM_PROP_SYMBOL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789669888'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_BASIC"] = {
			["Key"] = "EQUIP_TIPS_BASIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789670144'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_FIX"] = {
			["Key"] = "EQUIP_TIPS_FIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789670400'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_RANDOM"] = {
			["Key"] = "EQUIP_TIPS_RANDOM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789670656'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_ADVANCE"] = {
			["Key"] = "EQUIP_TIPS_ADVANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789670912'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_RANDOMTIMES"] = {
			["Key"] = "EQUIP_TIPS_RANDOMTIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789671168'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_TC"] = {
			["Key"] = "EQUIP_TIPS_TC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789671424'),
			["StringValueList"] = {},
		},
		["EQUIP_TIPS_ITEMTC"] = {
			["Key"] = "EQUIP_TIPS_ITEMTC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789671680'),
			["StringValueList"] = {},
		},
		["EQUIPITEM_BIND"] = {
			["Key"] = "EQUIPITEM_BIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155136768'),
			["StringValueList"] = {},
		},
		["EQUIPITEM_UNBIND"] = {
			["Key"] = "EQUIPITEM_UNBIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789672192'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1"] = {
			["Key"] = "EQUIP_TAB1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2"] = {
			["Key"] = "EQUIP_TAB2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789672704'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3"] = {
			["Key"] = "EQUIP_TAB3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789672960'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4"] = {
			["Key"] = "EQUIP_TAB4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_18487686727168'),
			["StringValueList"] = {},
		},
		["EQUIP_WORD"] = {
			["Key"] = "EQUIP_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789673472'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_TIP1"] = {
			["Key"] = "EQUIP_TAB1_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789673728'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_STATUS1"] = {
			["Key"] = "EQUIP_TAB1_STATUS1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789673984'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_BUTTON"] = {
			["Key"] = "EQUIP_TAB1_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107170816'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_STAGEFULL"] = {
			["Key"] = "EQUIP_TAB1_STAGEFULL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789674496'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_FULL"] = {
			["Key"] = "EQUIP_TAB1_FULL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789674752'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_LEVEL"] = {
			["Key"] = "EQUIP_TAB1_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789675008'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_SUITE"] = {
			["Key"] = "EQUIP_TAB1_SUITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789675264'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB1_NONE"] = {
			["Key"] = "EQUIP_TAB1_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789675520'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP1"] = {
			["Key"] = "EQUIP_TAB2_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789675776'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP2"] = {
			["Key"] = "EQUIP_TAB2_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789676032'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP3"] = {
			["Key"] = "EQUIP_TAB2_TIP3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789676288'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_TIP4"] = {
			["Key"] = "EQUIP_TAB2_TIP4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789676544'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB2_NONE"] = {
			["Key"] = "EQUIP_TAB2_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789676800'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_TIP1"] = {
			["Key"] = "EQUIP_TAB3_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789677056'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_TIP2"] = {
			["Key"] = "EQUIP_TAB3_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789677312'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BUTTON1"] = {
			["Key"] = "EQUIP_TAB3_BUTTON1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789672960'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BUTTON2"] = {
			["Key"] = "EQUIP_TAB3_BUTTON2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BANWORD"] = {
			["Key"] = "EQUIP_TAB3_BANWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789678080'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP1"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789678336'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TITLE"] = {
			["Key"] = "EQUIP_TAB3_BAN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789678080'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP2"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789678848'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_TIP3"] = {
			["Key"] = "EQUIP_TAB3_BAN_TIP3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789679104'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_SUBTITLE"] = {
			["Key"] = "EQUIP_TAB3_BAN_SUBTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789679360'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_BAN_BUTTON1"] = {
			["Key"] = "EQUIP_TAB3_BAN_BUTTON1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789581568'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB3_NONE"] = {
			["Key"] = "EQUIP_TAB3_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789679872'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_TIP1"] = {
			["Key"] = "EQUIP_TAB4_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789680128'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_SELECTWORD"] = {
			["Key"] = "EQUIP_TAB4_SELECTWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789680384'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_SELECTITEM"] = {
			["Key"] = "EQUIP_TAB4_SELECTITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789680640'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_TIP2"] = {
			["Key"] = "EQUIP_TAB4_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789680896'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_ITEMWORD"] = {
			["Key"] = "EQUIP_TAB4_ITEMWORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789681152'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_BUTTON"] = {
			["Key"] = "EQUIP_TAB4_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789681408'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4_NONE"] = {
			["Key"] = "EQUIP_TAB4_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789681664'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TITLE"] = {
			["Key"] = "EQUIP_TAB4S_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789680640'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_NONE"] = {
			["Key"] = "EQUIP_TAB4S_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789682176'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT1"] = {
			["Key"] = "EQUIP_TAB4S_SELECT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789682432'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT2"] = {
			["Key"] = "EQUIP_TAB4S_SELECT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789672192'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT3"] = {
			["Key"] = "EQUIP_TAB4S_SELECT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789682944'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TIP1"] = {
			["Key"] = "EQUIP_TAB4S_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789683200'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_TIP2"] = {
			["Key"] = "EQUIP_TAB4S_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789683456'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_BUTTON"] = {
			["Key"] = "EQUIP_TAB4S_BUTTON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789683712'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_TITLE"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789683968'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_CLOSE"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789684224'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_LEVEL"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789671680'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_TIMES"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_TIMES",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789684736'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789684992'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD1"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789685248'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_WORD2"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_WORD2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789685504'),
			["StringValueList"] = {},
		},
		["EQUIP_TAB4S_SELECT_CLEAR"] = {
			["Key"] = "EQUIP_TAB4S_SELECT_CLEAR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789685760'),
			["StringValueList"] = {},
		},
		["STORE_BUY_LIMIT"] = {
			["Key"] = "STORE_BUY_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789686016'),
			["StringValueList"] = {},
		},
		["STORE_NPC_TITLE"] = {
			["Key"] = "STORE_NPC_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789686272'),
			["StringValueList"] = {},
		},
		["STORE_ACHIEVE_LEVEL_UNLOCK"] = {
			["Key"] = "STORE_ACHIEVE_LEVEL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789686528'),
			["StringValueList"] = {},
		},
		["STORE_COMPLETE_TASK"] = {
			["Key"] = "STORE_COMPLETE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789686784'),
			["StringValueList"] = {},
		},
		["STORE_CLASS"] = {
			["Key"] = "STORE_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789687040'),
			["StringValueList"] = {},
		},
		["STORE_SHOP_SOLDOUT"] = {
			["Key"] = "STORE_SHOP_SOLDOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789687296'),
			["StringValueList"] = {},
		},
		["STORE_GUILD_SHOP_LEVEL"] = {
			["Key"] = "STORE_GUILD_SHOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789687552'),
			["StringValueList"] = {},
		},
		["STORE_ACHIEVE_LEVEL_UNLOCK_LUEN"] = {
			["Key"] = "STORE_ACHIEVE_LEVEL_UNLOCK_LUEN",
			["StringValue"] = "Character Level",
			["StringValueList"] = {},
		},
		["STORE_COMPLETE_TASK_LUEN"] = {
			["Key"] = "STORE_COMPLETE_TASK_LUEN",
			["StringValue"] = "TaskComplete",
			["StringValueList"] = {},
		},
		["STORE_CLASS_LUEN"] = {
			["Key"] = "STORE_CLASS_LUEN",
			["StringValue"] = "Profession",
			["StringValueList"] = {},
		},
		["STORE_PURCHASE_LIMIT_LUEN"] = {
			["Key"] = "STORE_PURCHASE_LIMIT_LUEN",
			["StringValue"] = "Purchase Limit",
			["StringValueList"] = {},
		},
		["STORE_GUILD_SHOP_LEVEL_LUEN"] = {
			["Key"] = "STORE_GUILD_SHOP_LEVEL_LUEN",
			["StringValue"] = "Guildshop Level",
			["StringValueList"] = {},
		},
		["STORE_SOLD_OUT_LUEN"] = {
			["Key"] = "STORE_SOLD_OUT_LUEN",
			["StringValue"] = "Sold Out",
			["StringValueList"] = {},
		},
		["STORE_DEPARTMENT_STORE"] = {
			["Key"] = "STORE_DEPARTMENT_STORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31268167224576'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_NOLIMIT"] = {
			["Key"] = "STORE_REFRESH_NOLIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789689600'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_MINUTE"] = {
			["Key"] = "STORE_REFRESH_MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316608'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_HOUR"] = {
			["Key"] = "STORE_REFRESH_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735738517250'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_DAILY"] = {
			["Key"] = "STORE_REFRESH_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789316096'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_DAY"] = {
			["Key"] = "STORE_REFRESH_DAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470089729'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_WEEK"] = {
			["Key"] = "STORE_REFRESH_WEEK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38071663868160'),
			["StringValueList"] = {},
		},
		["STORE_REFRESH_MONTH"] = {
			["Key"] = "STORE_REFRESH_MONTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470094342'),
			["StringValueList"] = {},
		},
		["STORE_BUY_PERMANENT_LIMIT"] = {
			["Key"] = "STORE_BUY_PERMANENT_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789691392'),
			["StringValueList"] = {},
		},
		["STORE_BUY_PERMANENT"] = {
			["Key"] = "STORE_BUY_PERMANENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789691648'),
			["StringValueList"] = {},
		},
		["STORE_EVERY"] = {
			["Key"] = "STORE_EVERY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789691904'),
			["StringValueList"] = {},
		},
		["STORE_BOUDN_OBTAIN"] = {
			["Key"] = "STORE_BOUDN_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9965398053376'),
			["StringValueList"] = {},
		},
		["STORE_POSSIBLE_OBTAIN"] = {
			["Key"] = "STORE_POSSIBLE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9965398053632'),
			["StringValueList"] = {},
		},
		["STORE_FREE_OBTAIN"] = {
			["Key"] = "STORE_FREE_OBTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789692672'),
			["StringValueList"] = {},
		},
		["STORE_HAVE_RECEIVED"] = {
			["Key"] = "STORE_HAVE_RECEIVED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789692928'),
			["StringValueList"] = {},
		},
		["STORE_PURCHASE"] = {
			["Key"] = "STORE_PURCHASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31822486449664'),
			["StringValueList"] = {},
		},
		["STORE_INEVITABLE"] = {
			["Key"] = "STORE_INEVITABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789693440'),
			["StringValueList"] = {},
		},
		["FUNCTION_MALL"] = {
			["Key"] = "FUNCTION_MALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450664192'),
			["StringValueList"] = {},
		},
		["FUNCTION_STORE"] = {
			["Key"] = "FUNCTION_STORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31268167224576'),
			["StringValueList"] = {},
		},
		["FUNCTION_EXCHANGE"] = {
			["Key"] = "FUNCTION_EXCHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450667776'),
			["StringValueList"] = {},
		},
		["FUNCTION_AUCTION"] = {
			["Key"] = "FUNCTION_AUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789694464'),
			["StringValueList"] = {},
		},
		["FUNCTION_MALL_LUEN"] = {
			["Key"] = "FUNCTION_MALL_LUEN",
			["StringValue"] = "MALL",
			["StringValueList"] = {},
		},
		["FUNCTION_STORE_LUEN"] = {
			["Key"] = "FUNCTION_STORE_LUEN",
			["StringValue"] = "STORE",
			["StringValueList"] = {},
		},
		["FUNCTION_EXCHANGE_LUEN"] = {
			["Key"] = "FUNCTION_EXCHANGE_LUEN",
			["StringValue"] = "EXCHANGE",
			["StringValueList"] = {},
		},
		["FUNCTION_AUCTION_LUEN"] = {
			["Key"] = "FUNCTION_AUCTION_LUEN",
			["StringValue"] = "BID",
			["StringValueList"] = {},
		},
		["EXCHANGE_CONCERN"] = {
			["Key"] = "EXCHANGE_CONCERN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811123968'),
			["StringValueList"] = {},
		},
		["EXCHANGE_MARKET"] = {
			["Key"] = "EXCHANGE_MARKET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_19723295131392'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELL"] = {
			["Key"] = "EXCHANGE_SELL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155138048'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE_ITEM"] = {
			["Key"] = "EXCHANGE_ON_SALE_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789696512'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELL_NONE"] = {
			["Key"] = "EXCHANGE_SELL_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789696768'),
			["StringValueList"] = {},
		},
		["EXCAHNGE_NO_PLAYER_SOLD"] = {
			["Key"] = "EXCAHNGE_NO_PLAYER_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789696768'),
			["StringValueList"] = {},
		},
		["EXCHANGE_CAN_SELL_NONE"] = {
			["Key"] = "EXCHANGE_CAN_SELL_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789697280'),
			["StringValueList"] = {},
		},
		["EXCHANGE_FLLOW_NONE"] = {
			["Key"] = "EXCHANGE_FLLOW_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789697536'),
			["StringValueList"] = {},
		},
		["EXCHANGE_SELLING"] = {
			["Key"] = "EXCHANGE_SELLING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789697792'),
			["StringValueList"] = {},
		},
		["PUBLICITY"] = {
			["Key"] = "PUBLICITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789698048'),
			["StringValueList"] = {},
		},
		["LEGEND"] = {
			["Key"] = "LEGEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_22472074201600'),
			["StringValueList"] = {},
		},
		["MYTH"] = {
			["Key"] = "MYTH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_22472074201856'),
			["StringValueList"] = {},
		},
		["INCOME"] = {
			["Key"] = "INCOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789698816'),
			["StringValueList"] = {},
		},
		["WITHDRAW_TO_WALLET"] = {
			["Key"] = "WITHDRAW_TO_WALLET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789699072'),
			["StringValueList"] = {},
		},
		["TIME_OUT"] = {
			["Key"] = "TIME_OUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789699328'),
			["StringValueList"] = {},
		},
		["PUT_ON"] = {
			["Key"] = "PUT_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789699584'),
			["StringValueList"] = {},
		},
		["PUT_OFF"] = {
			["Key"] = "PUT_OFF",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789440512'),
			["StringValueList"] = {},
		},
		["EXCHANGE_DOUBLE_CHECK"] = {
			["Key"] = "EXCHANGE_DOUBLE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789700096'),
			["StringValueList"] = {},
		},
		["EXCHANGE_RESELL"] = {
			["Key"] = "EXCHANGE_RESELL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789700352'),
			["StringValueList"] = {},
		},
		["EXCHANGE_CANCEL"] = {
			["Key"] = "EXCHANGE_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE"] = {
			["Key"] = "EXCHANGE_ON_SALE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789700864'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_SALE_TIP"] = {
			["Key"] = "EXCHANGE_ON_SALE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789699328'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_PUBLIC"] = {
			["Key"] = "EXCHANGE_ON_PUBLIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789701376'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ON_PUBLIC_TIP"] = {
			["Key"] = "EXCHANGE_ON_PUBLIC_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789701632'),
			["StringValueList"] = {},
		},
		["EXCHANGE_OVERDUE"] = {
			["Key"] = "EXCHANGE_OVERDUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789701888'),
			["StringValueList"] = {},
		},
		["EXCHANGE_OVERDUE_TIP"] = {
			["Key"] = "EXCHANGE_OVERDUE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789702144'),
			["StringValueList"] = {},
		},
		["EXCHANGE_PUBLIC_TIME"] = {
			["Key"] = "EXCHANGE_PUBLIC_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789702400'),
			["StringValueList"] = {},
		},
		["EXCHANGE_AFTER_TAX_INCOME"] = {
			["Key"] = "EXCHANGE_AFTER_TAX_INCOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789702656'),
			["StringValueList"] = {},
		},
		["EXCHANGE_NO_PLAYER_PUBLICITY"] = {
			["Key"] = "EXCHANGE_NO_PLAYER_PUBLICITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789702912'),
			["StringValueList"] = {},
		},
		["EXCHANGE_PRICE"] = {
			["Key"] = "EXCHANGE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789703168'),
			["StringValueList"] = {},
		},
		["DEPOSIT"] = {
			["Key"] = "DEPOSIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789703424'),
			["StringValueList"] = {},
		},
		["SHELVE_PRICE"] = {
			["Key"] = "SHELVE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789703680'),
			["StringValueList"] = {},
		},
		["ALL_PRICR"] = {
			["Key"] = "ALL_PRICR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789703936'),
			["StringValueList"] = {},
		},
		["TRADE_HISTORY"] = {
			["Key"] = "TRADE_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789704192'),
			["StringValueList"] = {},
		},
		["TRADE_HISTORY_TIPS"] = {
			["Key"] = "TRADE_HISTORY_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789704448'),
			["StringValueList"] = {},
		},
		["TRADE_PRICE"] = {
			["Key"] = "TRADE_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789704704'),
			["StringValueList"] = {},
		},
		["TRADE_NAME"] = {
			["Key"] = "TRADE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789704960'),
			["StringValueList"] = {},
		},
		["BAG_HMS"] = {
			["Key"] = "BAG_HMS",
			["StringValue"] = "Bag",
			["StringValueList"] = {},
		},
		["CARGO_HMS"] = {
			["Key"] = "CARGO_HMS",
			["StringValue"] = "Cargo",
			["StringValueList"] = {},
		},
		["INCOME_HMS"] = {
			["Key"] = "INCOME_HMS",
			["StringValue"] = "Revenue",
			["StringValueList"] = {},
		},
		["NO_PURCHASE_TIME_LIMIT"] = {
			["Key"] = "NO_PURCHASE_TIME_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789705984'),
			["StringValueList"] = {},
		},
		["PERMANENT_PURCHASE_LIMIT"] = {
			["Key"] = "PERMANENT_PURCHASE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789706240'),
			["StringValueList"] = {},
		},
		["AUCTION_NO_BID"] = {
			["Key"] = "AUCTION_NO_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789706496'),
			["StringValueList"] = {},
		},
		["AUCTION_RECORD"] = {
			["Key"] = "AUCTION_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789706752'),
			["StringValueList"] = {},
		},
		["AUCTION_NO_RECORD"] = {
			["Key"] = "AUCTION_NO_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789707008'),
			["StringValueList"] = {},
		},
		["AUCTION_DOUBLE_CHECK"] = {
			["Key"] = "AUCTION_DOUBLE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789707264'),
			["StringValueList"] = {},
		},
		["AUCTION_CANCEL"] = {
			["Key"] = "AUCTION_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["AUCTION_ENSURE"] = {
			["Key"] = "AUCTION_ENSURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["StringValueList"] = {},
		},
		["AUCTION_BID_CHECK"] = {
			["Key"] = "AUCTION_BID_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789708032'),
			["StringValueList"] = {},
		},
		["AUCTION_WORLD_BID"] = {
			["Key"] = "AUCTION_WORLD_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789708288'),
			["StringValueList"] = {},
		},
		["AUCTION_GUILD_BID"] = {
			["Key"] = "AUCTION_GUILD_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789708544'),
			["StringValueList"] = {},
		},
		["EXCHANGE_MARKET_NO_GOODS"] = {
			["Key"] = "EXCHANGE_MARKET_NO_GOODS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789708800'),
			["StringValueList"] = {},
		},
		["EXCHANGE_DISCOUNT"] = {
			["Key"] = "EXCHANGE_DISCOUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470083849'),
			["StringValueList"] = {},
		},
		["NO_TRADE_HISTORY"] = {
			["Key"] = "NO_TRADE_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789709312'),
			["StringValueList"] = {},
		},
		["EXCHANGE_ITEM_SEARCH"] = {
			["Key"] = "EXCHANGE_ITEM_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789709568'),
			["StringValueList"] = {},
		},
		["NO_SEARCH_HISTORY"] = {
			["Key"] = "NO_SEARCH_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789709824'),
			["StringValueList"] = {},
		},
		["STORE_BUY_ALLSERVER_LIMIT"] = {
			["Key"] = "STORE_BUY_ALLSERVER_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789710080'),
			["StringValueList"] = {},
		},
		["MAP_VISIT"] = {
			["Key"] = "MAP_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_46523891058432'),
			["StringValueList"] = {},
		},
		["MAP_CANEL_VISIT"] = {
			["Key"] = "MAP_CANEL_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789639936'),
			["StringValueList"] = {},
		},
		["MAP_LINE"] = {
			["Key"] = "MAP_LINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789710848'),
			["StringValueList"] = {},
		},
		["MAP_TASK_VISIT"] = {
			["Key"] = "MAP_TASK_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["MAP_TASK_CANCEL_VISIT"] = {
			["Key"] = "MAP_TASK_CANCEL_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789639936'),
			["StringValueList"] = {},
		},
		["MAP_SUBWAY_VISIT"] = {
			["Key"] = "MAP_SUBWAY_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["MAP_ACTIVITY_VISIT"] = {
			["Key"] = "MAP_ACTIVITY_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["ACTIVITY_NOT_OPEN"] = {
			["Key"] = "ACTIVITY_NOT_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789712128'),
			["StringValueList"] = {},
		},
		["SEARCH_TITLE"] = {
			["Key"] = "SEARCH_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789712384'),
			["StringValueList"] = {},
		},
		["MAP_TRACE_UP"] = {
			["Key"] = "MAP_TRACE_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789712640'),
			["StringValueList"] = {},
		},
		["MAP_TRACE_DOWN"] = {
			["Key"] = "MAP_TRACE_DOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789712896'),
			["StringValueList"] = {},
		},
		["MAP_CLICK_MARK"] = {
			["Key"] = "MAP_CLICK_MARK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789713152'),
			["StringValueList"] = {},
		},
		["MAP_MARKER_TRACKING"] = {
			["Key"] = "MAP_MARKER_TRACKING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789713408'),
			["StringValueList"] = {},
		},
		["MAP__HORSESTATION"] = {
			["Key"] = "MAP__HORSESTATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34984119269632'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_TREASURE"] = {
			["Key"] = "SPIRITUAL_TREASURE",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_TreasureTag",
			["StringValueList"] = {},
		},
		["SPIRITUAL_QUESTION"] = {
			["Key"] = "SPIRITUAL_QUESTION",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_QuestionMarkTag",
			["StringValueList"] = {},
		},
		["SPIRITUAL_KEY"] = {
			["Key"] = "SPIRITUAL_KEY",
			["StringValue"] = "/Game/Arts/UI_2/Resource/HUD_2/Icon/UI_HUD_Icon_KeyTag",
			["StringValueList"] = {},
		},
		["MAP_TRACE"] = {
			["Key"] = "MAP_TRACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_46523891058432'),
			["StringValueList"] = {},
		},
		["MAP_FMT_LAYER"] = {
			["Key"] = "MAP_FMT_LAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789714944'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_ROLE"] = {
			["Key"] = "ROLEDISPLAY_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328560640'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DETAIL"] = {
			["Key"] = "ROLEDISPLAY_DETAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789715456'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_CHECK_ATTR"] = {
			["Key"] = "ROLEDISPLAY_CHECK_ATTR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789715712'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_REPLACE_EQUIP"] = {
			["Key"] = "ROLEDISPLAY_REPLACE_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789715968'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_SAN"] = {
			["Key"] = "ROLEDISPLAY_SAN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789716224'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_1"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789716480'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_2"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789716736'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_3"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789716992'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_4"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789717248'),
			["StringValueList"] = {},
		},
		["ROLEDISPLAY_DEFAULT_SUIT_5"] = {
			["Key"] = "ROLEDISPLAY_DEFAULT_SUIT_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789717504'),
			["StringValueList"] = {},
		},
		["NOGUILD"] = {
			["Key"] = "NOGUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789717760'),
			["StringValueList"] = {},
		},
		["NOTITLE"] = {
			["Key"] = "NOTITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789718016'),
			["StringValueList"] = {},
		},
		["NORANK"] = {
			["Key"] = "NORANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789718272'),
			["StringValueList"] = {},
		},
		["BID_BID"] = {
			["Key"] = "BID_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789718528'),
			["StringValueList"] = {},
		},
		["BID_MAX_PRICE"] = {
			["Key"] = "BID_MAX_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789396480'),
			["StringValueList"] = {},
		},
		["BID_COUNTDOWN"] = {
			["Key"] = "BID_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789719040'),
			["StringValueList"] = {},
		},
		["BID_MY_OFFER"] = {
			["Key"] = "BID_MY_OFFER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789719296'),
			["StringValueList"] = {},
		},
		["BID_NO_BID"] = {
			["Key"] = "BID_NO_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789719552'),
			["StringValueList"] = {},
		},
		["BID_OPEN_TIME"] = {
			["Key"] = "BID_OPEN_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789719808'),
			["StringValueList"] = {},
		},
		["BID_FIXED_PRICE"] = {
			["Key"] = "BID_FIXED_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789720064'),
			["StringValueList"] = {},
		},
		["BID_FAIL"] = {
			["Key"] = "BID_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789720320'),
			["StringValueList"] = {},
		},
		["BID_GET"] = {
			["Key"] = "BID_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789720576'),
			["StringValueList"] = {},
		},
		["BID_SOLD"] = {
			["Key"] = "BID_SOLD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789720832'),
			["StringValueList"] = {},
		},
		["BID_WAIT"] = {
			["Key"] = "BID_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789721088'),
			["StringValueList"] = {},
		},
		["BID_ON"] = {
			["Key"] = "BID_ON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789721344'),
			["StringValueList"] = {},
		},
		["BID_HISTORY"] = {
			["Key"] = "BID_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789721600'),
			["StringValueList"] = {},
		},
		["BID_TIME"] = {
			["Key"] = "BID_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789721856'),
			["StringValueList"] = {},
		},
		["BID_CONFIRM_PRICE"] = {
			["Key"] = "BID_CONFIRM_PRICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789707264'),
			["StringValueList"] = {},
		},
		["BID_CHOICE"] = {
			["Key"] = "BID_CHOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789722368'),
			["StringValueList"] = {},
		},
		["BID_FINISH"] = {
			["Key"] = "BID_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789722624'),
			["StringValueList"] = {},
		},
		["BID_NO_HISTORY"] = {
			["Key"] = "BID_NO_HISTORY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789722880'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TITLE"] = {
			["Key"] = "GUILD_MT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TASKSETTLE"] = {
			["Key"] = "GUILD_MT_TASKSETTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789723392'),
			["StringValueList"] = {},
		},
		["GUILD_MT_TASKREWARD"] = {
			["Key"] = "GUILD_MT_TASKREWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789723648'),
			["StringValueList"] = {},
		},
		["GUILD_MT_COMPLETE"] = {
			["Key"] = "GUILD_MT_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789723904'),
			["StringValueList"] = {},
		},
		["GUILD_MT_FINISHED"] = {
			["Key"] = "GUILD_MT_FINISHED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789724160'),
			["StringValueList"] = {},
		},
		["GUILD_MT_FINISH"] = {
			["Key"] = "GUILD_MT_FINISH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31822486439424'),
			["StringValueList"] = {},
		},
		["GUILD_HYPERLINK"] = {
			["Key"] = "GUILD_HYPERLINK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789724672'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION_STAGE"] = {
			["Key"] = "SEQUENCE_DIGESTION_STAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789724928'),
			["StringValueList"] = {},
		},
		["SEQUENCE_TITLE"] = {
			["Key"] = "SEQUENCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328566272'),
			["StringValueList"] = {},
		},
		["SEQUENCE_BACK_BTN"] = {
			["Key"] = "SEQUENCE_BACK_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789725440'),
			["StringValueList"] = {},
		},
		["SEQUENCE_FINISH_STAGE"] = {
			["Key"] = "SEQUENCE_FINISH_STAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789725696'),
			["StringValueList"] = {},
		},
		["SEQUENCE_LOCK_TITLE"] = {
			["Key"] = "SEQUENCE_LOCK_TITLE",
			["StringValue"] = "???",
			["StringValueList"] = {},
		},
		["SEQUENCE_GOD"] = {
			["Key"] = "SEQUENCE_GOD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789726208'),
			["StringValueList"] = {},
		},
		["SEQUENCE_REFINE_TITLE"] = {
			["Key"] = "SEQUENCE_REFINE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789726464'),
			["StringValueList"] = {},
		},
		["SEQUENCE_REFINE_BTN"] = {
			["Key"] = "SEQUENCE_REFINE_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789726720'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION_BTN"] = {
			["Key"] = "SEQUENCE_DIGESTION_BTN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789726976'),
			["StringValueList"] = {},
		},
		["SEQUENCE_NO_DIGESTION"] = {
			["Key"] = "SEQUENCE_NO_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789727232'),
			["StringValueList"] = {},
		},
		["SEQUENCE_HAVE_DIGESTION"] = {
			["Key"] = "SEQUENCE_HAVE_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789727488'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CAN_RECEIVE_TASK"] = {
			["Key"] = "SEQUENCE_CAN_RECEIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789727744'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CAN_FINISH_TASK"] = {
			["Key"] = "SEQUENCE_CAN_FINISH_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789728000'),
			["StringValueList"] = {},
		},
		["SEQUENCE_RECEIVE_TASK"] = {
			["Key"] = "SEQUENCE_RECEIVE_TASK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789728256'),
			["StringValueList"] = {},
		},
		["SEQUENCE_LEVEL"] = {
			["Key"] = "SEQUENCE_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789728512'),
			["StringValueList"] = {},
		},
		["SEQUENCE_DIGESTION"] = {
			["Key"] = "SEQUENCE_DIGESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789728768'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_ONE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_ONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25773293440000'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_BOTTLE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_BOTTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470085646'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_DROP"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_DROP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789729536'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_PAIR"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_PAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470083585'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_DUO"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_DUO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25773293438464'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_SLICE"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_SLICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470099463'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_ROOT"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_ROOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_35735470086914'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_STRIP"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_STRIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789730816'),
			["StringValueList"] = {},
		},
		["SEQUENCE_QUANTIFIER_NIL"] = {
			["Key"] = "SEQUENCE_QUANTIFIER_NIL",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["SEQUENCE_SANCHECK_TITLE"] = {
			["Key"] = "SEQUENCE_SANCHECK_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789731328'),
			["StringValueList"] = {},
		},
		["SEQUENCE_CRAZYTALK_DESC"] = {
			["Key"] = "SEQUENCE_CRAZYTALK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789731584'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SANCHECK_DESC"] = {
			["Key"] = "SEQUENCE_SANCHECK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789731840'),
			["StringValueList"] = {},
		},
		["SEQUENCE_OPERAT_DESC"] = {
			["Key"] = "SEQUENCE_OPERAT_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789732096'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_CLOCKWISE"] = {
			["Key"] = "SEQUENCE_SRIRRING_CLOCKWISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789732352'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_ANTICLOCKWISE"] = {
			["Key"] = "SEQUENCE_SRIRRING_ANTICLOCKWISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789732608'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_COUNT"] = {
			["Key"] = "SEQUENCE_SRIRRING_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789732864'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_PREADD"] = {
			["Key"] = "SEQUENCE_SRIRRING_PREADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789733120'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SRIRRING_ADDING"] = {
			["Key"] = "SEQUENCE_SRIRRING_ADDING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789733376'),
			["StringValueList"] = {},
		},
		["SEQUENCE_GAIN_TITLE"] = {
			["Key"] = "SEQUENCE_GAIN_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789733632'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SPIRIT_COLLECT_L"] = {
			["Key"] = "SEQUENCE_SPIRIT_COLLECT_L",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789733888'),
			["StringValueList"] = {},
		},
		["SEQUENCE_SPIRIT_COLLECT_R"] = {
			["Key"] = "SEQUENCE_SPIRIT_COLLECT_R",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789734144'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_GAP"] = {
			["Key"] = "SEQUENCE_ADJUST_GAP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789734400'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_X_TRANSFORM"] = {
			["Key"] = "SEQUENCE_ADJUST_X_TRANSFORM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789734656'),
			["StringValueList"] = {},
		},
		["SEQUENCE_ADJUST_Y_TRANSFORM"] = {
			["Key"] = "SEQUENCE_ADJUST_Y_TRANSFORM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789734912'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_DESC"] = {
			["Key"] = "SPIRITUAL_POWER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789735168'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_RECOVER_ITEM"] = {
			["Key"] = "SPIRITUAL_POWER_RECOVER_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789735424'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_TITLE"] = {
			["Key"] = "ROLEPLAY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175680'),
			["StringValueList"] = {},
		},
		["SPIRITUAL_POWER_RECOVER_DAILY"] = {
			["Key"] = "SPIRITUAL_POWER_RECOVER_DAILY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789735936'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TITLE"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789736192'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_UNLOCKJUMP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_UNLOCKJUMP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789666304'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_LOCK"] = {
			["Key"] = "ROLEPLAY_IDENTITY_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789342976'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_EQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_EQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107175680'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_ONEQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_ONEQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789737216'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_CANTEQUIP"] = {
			["Key"] = "ROLEPLAY_IDENTITY_CANTEQUIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789737472'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_TITLE"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789737728'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_UNLOCKED"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_UNLOCKED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789737984'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_UNLOCK"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789738240'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_LOCK"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789738496'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_SKILL_TITLE"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_SKILL_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789738752'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SEQUENCE_CONDITION"] = {
			["Key"] = "ROLEPLAY_SEQUENCE_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789739008'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TABLE_PUBLIC"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TABLE_PUBLIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789739264'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_IDENTITY_TABLE_HIDEN"] = {
			["Key"] = "ROLEPLAY_IDENTITY_TABLE_HIDEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789739520'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_TITLE"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789739776'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_TALK"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_TALK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789740032'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_REJECT"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_REJECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789740288'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_ACCEPT"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_ACCEPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789740544'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_DICE"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789740800'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_CHAIR_INTERACT"] = {
			["Key"] = "ROLEPLAY_WITCH_CHAIR_INTERACT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115432192'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_TRAPED"] = {
			["Key"] = "ROLEPLAY_WITCH_TRAPED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789741312'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_SKIP"] = {
			["Key"] = "ROLEPLAY_WITCH_SKIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789741568'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_TITLE"] = {
			["Key"] = "ROLEPLAY_WITCH_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789741824'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_1"] = {
			["Key"] = "ROLEPLAY_FORTUNE_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789742080'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_2"] = {
			["Key"] = "ROLEPLAY_FORTUNE_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789742336'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_3"] = {
			["Key"] = "ROLEPLAY_FORTUNE_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789742592'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_4"] = {
			["Key"] = "ROLEPLAY_FORTUNE_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789742848'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_5"] = {
			["Key"] = "ROLEPLAY_FORTUNE_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789743104'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_6"] = {
			["Key"] = "ROLEPLAY_FORTUNE_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115431936'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_7"] = {
			["Key"] = "ROLEPLAY_FORTUNE_7",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789743616'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_8"] = {
			["Key"] = "ROLEPLAY_FORTUNE_8",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789743872'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_9"] = {
			["Key"] = "ROLEPLAY_FORTUNE_9",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789744128'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_10"] = {
			["Key"] = "ROLEPLAY_FORTUNE_10",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789744384'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_11"] = {
			["Key"] = "ROLEPLAY_FORTUNE_11",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789744640'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_12"] = {
			["Key"] = "ROLEPLAY_FORTUNE_12",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789744896'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_13"] = {
			["Key"] = "ROLEPLAY_FORTUNE_13",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789745152'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_14"] = {
			["Key"] = "ROLEPLAY_FORTUNE_14",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789745408'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_15"] = {
			["Key"] = "ROLEPLAY_FORTUNE_15",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789745664'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_16"] = {
			["Key"] = "ROLEPLAY_FORTUNE_16",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789745920'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_17"] = {
			["Key"] = "ROLEPLAY_FORTUNE_17",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789746176'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_UPRIGHT"] = {
			["Key"] = "ROLEPLAY_FORTUNE_UPRIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789746432'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_FORTUNE_REVERSED"] = {
			["Key"] = "ROLEPLAY_FORTUNE_REVERSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789746688'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_WITCH_INVITE_ADD"] = {
			["Key"] = "ROLEPLAY_WITCH_INVITE_ADD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789746944'),
			["StringValueList"] = {},
		},
		["WITCH_INVITE_FAIL_POPTXT"] = {
			["Key"] = "WITCH_INVITE_FAIL_POPTXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789747200'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_UNLOCK"] = {
			["Key"] = "ROLEPLAY_SKILL_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789747456'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_LEVELUP"] = {
			["Key"] = "ROLEPLAY_SKILL_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789747712'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_PREPOSITION_LOCK"] = {
			["Key"] = "ROLEPLAY_PREPOSITION_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789747968'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_CONDITION_LOCK"] = {
			["Key"] = "ROLEPLAY_CONDITION_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748224'),
			["StringValueList"] = {},
		},
		["MATERIALS_LACK"] = {
			["Key"] = "MATERIALS_LACK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748480'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_SKILL_FULL_LEVEL"] = {
			["Key"] = "ROLEPLAY_SKILL_FULL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748736'),
			["StringValueList"] = {},
		},
		["CLICK_DICE"] = {
			["Key"] = "CLICK_DICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748992'),
			["StringValueList"] = {},
		},
		["DICE_ADDITION"] = {
			["Key"] = "DICE_ADDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789749248'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789749504'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789749760'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789750016'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789750272'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789750528'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789750784'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751040'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751296'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751552'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751808'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752064'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752320'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752576'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752832'),
			["StringValueList"] = {},
		},
		["JOKER_BALL_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_BALL_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789753088'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751808'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752064'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752320'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752576'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752832'),
			["StringValueList"] = {},
		},
		["JOKER_HAT_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_HAT_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789753088'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789751808'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752064'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_SUCCESS_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_SUCCESS_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752320'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT1"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752576'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT2"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789752832'),
			["StringValueList"] = {},
		},
		["JOKER_RIDER_PLAY_FAIL_TEXT3"] = {
			["Key"] = "JOKER_RIDER_PLAY_FAIL_TEXT3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789753088'),
			["StringValueList"] = {},
		},
		["FORTUNE_RIDER_PLAY_TEXT1"] = {
			["Key"] = "FORTUNE_RIDER_PLAY_TEXT1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789756416'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_FORTUNE"] = {
			["Key"] = "ROLEPLAY_HUD_FORTUNE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607200768'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_JOKER"] = {
			["Key"] = "ROLEPLAY_HUD_JOKER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38071663864576'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_WITCH"] = {
			["Key"] = "ROLEPLAY_HUD_WITCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38071663864832'),
			["StringValueList"] = {},
		},
		["ROLEPLAY_HUD_ARBITRATOR"] = {
			["Key"] = "ROLEPLAY_HUD_ARBITRATOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789757440'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_MANAGE"] = {
			["Key"] = "SOCIALACTION_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178868480'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_FAVOR"] = {
			["Key"] = "SOCIALACTION_FAVOR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789757952'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_SAVE"] = {
			["Key"] = "SOCIALACTION_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789567488'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_PLAYER_NUM"] = {
			["Key"] = "SOCIALACTION_PLAYER_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789758464'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_INVITE_NAME"] = {
			["Key"] = "SOCIALACTION_INVITE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789758720'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_NO_PLAYER"] = {
			["Key"] = "SOCIALACTION_NO_PLAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789758976'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_SEARCH"] = {
			["Key"] = "SOCIALACTION_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789759232'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058194689')},
		},
		["SOCIALACTION_WAIT_REMIND"] = {
			["Key"] = "SOCIALACTION_WAIT_REMIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115430912'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058194945')},
		},
		["SOCIALACTION_INVITE_REMIND"] = {
			["Key"] = "SOCIALACTION_INVITE_REMIND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115431168'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058194945')},
		},
		["SOCIALACTION_CANCEL"] = {
			["Key"] = "SOCIALACTION_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["SOCIALACTION_CONFIRM"] = {
			["Key"] = "SOCIALACTION_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["StringValueList"] = {},
		},
		["CP_ExcitedSoul"] = {
			["Key"] = "CP_ExcitedSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789760512'),
			["StringValueList"] = {},
		},
		["CP_HappySoul"] = {
			["Key"] = "CP_HappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789760768'),
			["StringValueList"] = {},
		},
		["CP_PeaceSoul"] = {
			["Key"] = "CP_PeaceSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761024'),
			["StringValueList"] = {},
		},
		["CP_CalmSoul"] = {
			["Key"] = "CP_CalmSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761280'),
			["StringValueList"] = {},
		},
		["CP_UpsetSoul"] = {
			["Key"] = "CP_UpsetSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761536'),
			["StringValueList"] = {},
		},
		["CP_UnhappySoul"] = {
			["Key"] = "CP_UnhappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761792'),
			["StringValueList"] = {},
		},
		["CP_AngrySoul"] = {
			["Key"] = "CP_AngrySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789762048'),
			["StringValueList"] = {},
		},
		["ML_ExcitedSoul"] = {
			["Key"] = "ML_ExcitedSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789760512'),
			["StringValueList"] = {},
		},
		["ML_HappySoul"] = {
			["Key"] = "ML_HappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789760768'),
			["StringValueList"] = {},
		},
		["ML_PeaceSoul"] = {
			["Key"] = "ML_PeaceSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761024'),
			["StringValueList"] = {},
		},
		["ML_CalmSoul"] = {
			["Key"] = "ML_CalmSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761280'),
			["StringValueList"] = {},
		},
		["ML_UpsetSoul"] = {
			["Key"] = "ML_UpsetSoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761536'),
			["StringValueList"] = {},
		},
		["ML_UnhappySoul"] = {
			["Key"] = "ML_UnhappySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789761792'),
			["StringValueList"] = {},
		},
		["ML_AngrySoul"] = {
			["Key"] = "ML_AngrySoul",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789762048'),
			["StringValueList"] = {},
		},
		["CP_Quit"] = {
			["Key"] = "CP_Quit",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_10858751073536'),
			["StringValueList"] = {},
		},
		["CP_Confirm"] = {
			["Key"] = "CP_Confirm",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789650944'),
			["StringValueList"] = {},
		},
		["CP_Bargain"] = {
			["Key"] = "CP_Bargain",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789764608'),
			["StringValueList"] = {},
		},
		["ExcitedSoul_Desc"] = {
			["Key"] = "ExcitedSoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789764864'),
			["StringValueList"] = {},
		},
		["HappySoul_Desc"] = {
			["Key"] = "HappySoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["PeaceSoul_Desc"] = {
			["Key"] = "PeaceSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["CalmSoul_Desc"] = {
			["Key"] = "CalmSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["UpsetSoul_Desc"] = {
			["Key"] = "UpsetSoul_Desc",
			["StringValue"] = "",
			["StringValueList"] = {},
		},
		["UnhappySoul_Desc"] = {
			["Key"] = "UnhappySoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789766144'),
			["StringValueList"] = {},
		},
		["AngrySoul_Desc"] = {
			["Key"] = "AngrySoul_Desc",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789766400'),
			["StringValueList"] = {},
		},
		["MOOD_LEVELUP"] = {
			["Key"] = "MOOD_LEVELUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789766656'),
			["StringValueList"] = {},
		},
		["MOOD_LEVELDOWN"] = {
			["Key"] = "MOOD_LEVELDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789766912'),
			["StringValueList"] = {},
		},
		["Excited_CutPriceTips"] = {
			["Key"] = "Excited_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789767168'),
			["StringValueList"] = {},
		},
		["HappySoul_CutPriceTips"] = {
			["Key"] = "HappySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789767168'),
			["StringValueList"] = {},
		},
		["PeaceSoul_CutPriceTips"] = {
			["Key"] = "PeaceSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11752104269568'),
			["StringValueList"] = {},
		},
		["CalmSoul_CutPriceTips"] = {
			["Key"] = "CalmSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11752104269568'),
			["StringValueList"] = {},
		},
		["UpsetSoul_CutPriceTips"] = {
			["Key"] = "UpsetSoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11752104269568'),
			["StringValueList"] = {},
		},
		["UnhappySoul_CutPriceTips"] = {
			["Key"] = "UnhappySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_11752104269568'),
			["StringValueList"] = {},
		},
		["AngrySoul_CutPriceTips"] = {
			["Key"] = "AngrySoul_CutPriceTips",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789768704'),
			["StringValueList"] = {},
		},
		["CROSS_WORD_PUZZLE_TIPS"] = {
			["Key"] = "CROSS_WORD_PUZZLE_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789768960'),
			["StringValueList"] = {},
		},
		["JIGSAW_PUZZLE_NAME"] = {
			["Key"] = "JIGSAW_PUZZLE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789769216'),
			["StringValueList"] = {},
		},
		["GRADE_SIMPLE"] = {
			["Key"] = "GRADE_SIMPLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789769472'),
			["StringValueList"] = {},
		},
		["GRADE_HARD"] = {
			["Key"] = "GRADE_HARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789403648'),
			["StringValueList"] = {},
		},
		["GRADE_MASTER"] = {
			["Key"] = "GRADE_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789769984'),
			["StringValueList"] = {},
		},
		["DANCE_TAB_MUSIC"] = {
			["Key"] = "DANCE_TAB_MUSIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789770240'),
			["StringValueList"] = {},
		},
		["DANCE_TAB_DEVELOP"] = {
			["Key"] = "DANCE_TAB_DEVELOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505504512'),
			["StringValueList"] = {},
		},
		["DANCE_IDENTITY_NAME"] = {
			["Key"] = "DANCE_IDENTITY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789770752'),
			["StringValueList"] = {},
		},
		["UNLOCK_LEVEL"] = {
			["Key"] = "UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789771008'),
			["StringValueList"] = {},
		},
		["MUSIC_UNFINISHED"] = {
			["Key"] = "MUSIC_UNFINISHED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789771264'),
			["StringValueList"] = {},
		},
		["DANCE_TYPE_DOUBLE"] = {
			["Key"] = "DANCE_TYPE_DOUBLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53601997162240'),
			["StringValueList"] = {},
		},
		["DANCE_TYPE_MULTIPLAYER"] = {
			["Key"] = "DANCE_TYPE_MULTIPLAYER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789771776'),
			["StringValueList"] = {},
		},
		["CHALLENGE_RECORD"] = {
			["Key"] = "CHALLENGE_RECORD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789772032'),
			["StringValueList"] = {},
		},
		["RANKING_DIGIT"] = {
			["Key"] = "RANKING_DIGIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789772288'),
			["StringValueList"] = {},
		},
		["MASTER_DEGREE"] = {
			["Key"] = "MASTER_DEGREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789772544'),
			["StringValueList"] = {},
		},
		["ON_THE_LIST_NOT_YET"] = {
			["Key"] = "ON_THE_LIST_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789772800'),
			["StringValueList"] = {},
		},
		["COMPLETE_NOT_YET"] = {
			["Key"] = "COMPLETE_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789773056'),
			["StringValueList"] = {},
		},
		["COMPLETE_ALREADY"] = {
			["Key"] = "COMPLETE_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789773312'),
			["StringValueList"] = {},
		},
		["CHALLENGE_REPEAT"] = {
			["Key"] = "CHALLENGE_REPEAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789773568'),
			["StringValueList"] = {},
		},
		["ENERGY_USE_TIP"] = {
			["Key"] = "ENERGY_USE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789773824'),
			["StringValueList"] = {},
		},
		["PERFORM_BEGIN"] = {
			["Key"] = "PERFORM_BEGIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_2612682301952'),
			["StringValueList"] = {},
		},
		["UNLOCK_REQUIRE"] = {
			["Key"] = "UNLOCK_REQUIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789774336'),
			["StringValueList"] = {},
		},
		["SOLO_INPLACE"] = {
			["Key"] = "SOLO_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789774592'),
			["StringValueList"] = {},
		},
		["DOUBLE_INPLACE"] = {
			["Key"] = "DOUBLE_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789774848'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_INPLACE"] = {
			["Key"] = "MULTIDANCE_INPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789775104'),
			["StringValueList"] = {},
		},
		["CHALLENGE_ENTRANCE"] = {
			["Key"] = "CHALLENGE_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789775360'),
			["StringValueList"] = {},
		},
		["CHALLENGE_SET"] = {
			["Key"] = "CHALLENGE_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789775616'),
			["StringValueList"] = {},
		},
		["ENERGY_USE"] = {
			["Key"] = "ENERGY_USE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789775872'),
			["StringValueList"] = {},
		},
		["CHALLENGE_BEGIN"] = {
			["Key"] = "CHALLENGE_BEGIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789776128'),
			["StringValueList"] = {},
		},
		["COMING_SOON"] = {
			["Key"] = "COMING_SOON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789776384'),
			["StringValueList"] = {},
		},
		["DEGREE_SELECT"] = {
			["Key"] = "DEGREE_SELECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789776640'),
			["StringValueList"] = {},
		},
		["CHALLENGE_RECORD_SET"] = {
			["Key"] = "CHALLENGE_RECORD_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789776896'),
			["StringValueList"] = {},
		},
		["CHALLENGE_NOT_YET"] = {
			["Key"] = "CHALLENGE_NOT_YET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789777152'),
			["StringValueList"] = {},
		},
		["SELECT_ALREADY"] = {
			["Key"] = "SELECT_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789777408'),
			["StringValueList"] = {},
		},
		["SELECT_CONFIRM"] = {
			["Key"] = "SELECT_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789777664'),
			["StringValueList"] = {},
		},
		["INPLACE_CONFIRM_COUNTDOWN"] = {
			["Key"] = "INPLACE_CONFIRM_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789777920'),
			["StringValueList"] = {},
		},
		["INPLACE_CONFIRM"] = {
			["Key"] = "INPLACE_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789778176'),
			["StringValueList"] = {},
		},
		["INPLACE_WAITING"] = {
			["Key"] = "INPLACE_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789778432'),
			["StringValueList"] = {},
		},
		["SELECT_WAITING"] = {
			["Key"] = "SELECT_WAITING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789778688'),
			["StringValueList"] = {},
		},
		["CHALLENGE_BEGIN_REMINDER"] = {
			["Key"] = "CHALLENGE_BEGIN_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789778944'),
			["StringValueList"] = {},
		},
		["ENERGY_USE_SETTLEMENT"] = {
			["Key"] = "ENERGY_USE_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789779200'),
			["StringValueList"] = {},
		},
		["IDENTITY_EXP"] = {
			["Key"] = "IDENTITY_EXP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789779456'),
			["StringValueList"] = {},
		},
		["LEVEL_PROGRESS"] = {
			["Key"] = "LEVEL_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789779712'),
			["StringValueList"] = {},
		},
		["REWARD_MONEY"] = {
			["Key"] = "REWARD_MONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789779968'),
			["StringValueList"] = {},
		},
		["REWARD_ITEM"] = {
			["Key"] = "REWARD_ITEM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789780224'),
			["StringValueList"] = {},
		},
		["CHALLENGE_REPEAT_SETTLEMENT"] = {
			["Key"] = "CHALLENGE_REPEAT_SETTLEMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789780480'),
			["StringValueList"] = {},
		},
		["CHALLENGE_FAIL"] = {
			["Key"] = "CHALLENGE_FAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_40202772940290'),
			["StringValueList"] = {},
		},
		["ENERGY_RETURN"] = {
			["Key"] = "ENERGY_RETURN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789780992'),
			["StringValueList"] = {},
		},
		["ASSIST_VALUE"] = {
			["Key"] = "ASSIST_VALUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789781248'),
			["StringValueList"] = {},
		},
		["PERFORM_SET"] = {
			["Key"] = "PERFORM_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789781504'),
			["StringValueList"] = {},
		},
		["TAB_MUSIC"] = {
			["Key"] = "TAB_MUSIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789770240'),
			["StringValueList"] = {},
		},
		["TAB_VEHICLE"] = {
			["Key"] = "TAB_VEHICLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_59236994273280'),
			["StringValueList"] = {},
		},
		["TAB_EFFECT"] = {
			["Key"] = "TAB_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789782272'),
			["StringValueList"] = {},
		},
		["STAGE_EDIT"] = {
			["Key"] = "STAGE_EDIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789782528'),
			["StringValueList"] = {},
		},
		["SET_ENTRANCE"] = {
			["Key"] = "SET_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450662144'),
			["StringValueList"] = {},
		},
		["VIEW_FREE"] = {
			["Key"] = "VIEW_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789783040'),
			["StringValueList"] = {},
		},
		["VIEW_FIX"] = {
			["Key"] = "VIEW_FIX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789783296'),
			["StringValueList"] = {},
		},
		["SHIELD_ENTRANCE"] = {
			["Key"] = "SHIELD_ENTRANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789517056'),
			["StringValueList"] = {},
		},
		["SHIELD_TITLE"] = {
			["Key"] = "SHIELD_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789783808'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY"] = {
			["Key"] = "ROLE_DISPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789784064'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_OPEN"] = {
			["Key"] = "ROLE_DISPLAY_OPEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607170816'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_CLOSE"] = {
			["Key"] = "ROLE_DISPLAY_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789329152'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_TEAMMATE"] = {
			["Key"] = "ROLE_DISPLAY_TEAMMATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789353984'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_GUILD"] = {
			["Key"] = "ROLE_DISPLAY_GUILD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789785088'),
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_NPC"] = {
			["Key"] = "ROLE_DISPLAY_NPC",
			["StringValue"] = "NPC",
			["StringValueList"] = {},
		},
		["ROLE_DISPLAY_FORMATION"] = {
			["Key"] = "ROLE_DISPLAY_FORMATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789785600'),
			["StringValueList"] = {},
		},
		["UI_HIDE"] = {
			["Key"] = "UI_HIDE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789739520'),
			["StringValueList"] = {},
		},
		["UI_DISPLAY"] = {
			["Key"] = "UI_DISPLAY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_52296863975936'),
			["StringValueList"] = {},
		},
		["HUD_EXIT"] = {
			["Key"] = "HUD_EXIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789314048'),
			["StringValueList"] = {},
		},
		["HUD_COUNTDOWN"] = {
			["Key"] = "HUD_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789786624'),
			["StringValueList"] = {},
		},
		["PERFORM_SET_CONFIRM"] = {
			["Key"] = "PERFORM_SET_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["StringValueList"] = {},
		},
		["PERFORM_END"] = {
			["Key"] = "PERFORM_END",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789787136'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_CLOSE_TIP"] = {
			["Key"] = "SETTLEMENT_CLOSE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789787392'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_REWARD"] = {
			["Key"] = "SETTLEMENT_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789423360'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_FORMATION_NPC"] = {
			["Key"] = "MULTIDANCE_FORMATION_NPC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789787904'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_LOOP"] = {
			["Key"] = "MULTIDANCE_LOOP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789788160'),
			["StringValueList"] = {},
		},
		["MULTIDANCE_JOIN"] = {
			["Key"] = "MULTIDANCE_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789788416'),
			["StringValueList"] = {},
		},
		["TOUR_BEGIN_CHAT_TIP"] = {
			["Key"] = "TOUR_BEGIN_CHAT_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789788672'),
			["StringValueList"] = {},
		},
		["TOUR_JOIN"] = {
			["Key"] = "TOUR_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789788928'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_NAME"] = {
			["Key"] = "FASHION_PLAN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789789184'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_REPLACE"] = {
			["Key"] = "FASHION_PLAN_REPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789789440'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_RENAME_POPUP"] = {
			["Key"] = "FASHION_PLAN_RENAME_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789789696'),
			["StringValueList"] = {},
		},
		["FASHION_PLAN_REPLACE_POPUP"] = {
			["Key"] = "FASHION_PLAN_REPLACE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789789952'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TITLE"] = {
			["Key"] = "FASHION_CHECKLIST_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789790208'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TIP1"] = {
			["Key"] = "FASHION_CHECKLIST_TIP1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789790464'),
			["StringValueList"] = {},
		},
		["FASHION_CHECKLIST_TIP2"] = {
			["Key"] = "FASHION_CHECKLIST_TIP2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789790720'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKED_CANBUY"] = {
			["Key"] = "FASHION_LOCKED_CANBUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789790976'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKED_CANTBUY"] = {
			["Key"] = "FASHION_LOCKED_CANTBUY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789791232'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_CANCEL"] = {
			["Key"] = "FASHION_STAIN_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789791488'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SAVE"] = {
			["Key"] = "FASHION_STAIN_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789791744'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SAVE_POPUP"] = {
			["Key"] = "FASHION_STAIN_SAVE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789792000'),
			["StringValueList"] = {},
		},
		["FASHION_COLOR_LOCKPLACE"] = {
			["Key"] = "FASHION_COLOR_LOCKPLACE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789792256'),
			["StringValueList"] = {},
		},
		["FASHION_GETNEW_REMINDER"] = {
			["Key"] = "FASHION_GETNEW_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789792512'),
			["StringValueList"] = {},
		},
		["FASHION_LOCKPLACE_POPUP"] = {
			["Key"] = "FASHION_LOCKPLACE_POPUP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789792768'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_NOMONEY"] = {
			["Key"] = "FASHION_STAIN_NOMONEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789793024'),
			["StringValueList"] = {},
		},
		["FASHION_STAIN_SUCCESS"] = {
			["Key"] = "FASHION_STAIN_SUCCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789793280'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_BUTTON_NAME"] = {
			["Key"] = "FASHION_DYEING_BUTTON_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789793536'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_UPPER"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_UPPER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789793792'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_PANTS"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_PANTS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789794048'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_HAIR"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_HAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789794304'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_BUTTON_SUIT"] = {
			["Key"] = "FASHION_DYEING_PANEL_BUTTON_SUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789794560'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_UPPER"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_UPPER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789794816'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_PANTS"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_PANTS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789795072'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_HAIR"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_HAIR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789795328'),
			["StringValueList"] = {},
		},
		["FASHION_DYEING_PANEL_TITLE_SUIT"] = {
			["Key"] = "FASHION_DYEING_PANEL_TITLE_SUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789795584'),
			["StringValueList"] = {},
		},
		["FASHION_GETNEW__AGAIN_REMINDER"] = {
			["Key"] = "FASHION_GETNEW__AGAIN_REMINDER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789795840'),
			["StringValueList"] = {},
		},
		["Ongoing"] = {
			["Key"] = "Ongoing",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789646080'),
			["StringValueList"] = {},
		},
		["To_Be_Opened"] = {
			["Key"] = "To_Be_Opened",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789669632'),
			["StringValueList"] = {},
		},
		["Close_Time_Remind"] = {
			["Key"] = "Close_Time_Remind",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355264'),
			["StringValueList"] = {},
		},
		["Open_Time_Remind"] = {
			["Key"] = "Open_Time_Remind",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789355008'),
			["StringValueList"] = {},
		},
		["The_Which_One_Of_Month"] = {
			["Key"] = "The_Which_One_Of_Month",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789797120'),
			["StringValueList"] = {},
		},
		["GUILD_LEAGUE_ACTIVITY_NAME"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642599168'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058232833')},
		},
		["GUILD_LEAGUE_GROUP_SCHEDULE_NAME"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_SCHEDULE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789797632'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058233089')},
		},
		["GUILD_LEAGUE_DEFAULT_DIVISION_NAME"] = {
			["Key"] = "GUILD_LEAGUE_DEFAULT_DIVISION_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789797888'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058233345')},
		},
		["GUILD_LEAGUE_ACTIVITY_START_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_START_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789798144'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058233601')},
		},
		["GUILD_LEAGUE_BID_START_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_BID_START_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789798400'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058233857')},
		},
		["GUILD_LEAGUE_TBD_DESC"] = {
			["Key"] = "GUILD_LEAGUE_TBD_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5567082923520'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058234113')},
		},
		["GUILD_LEAGUE_GROUP_DESC"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789798912'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058234369')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_NOT_START_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_NOT_START_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789799168'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058234625')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_IN_PROGRESS_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_IN_PROGRESS_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789646080'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058234881')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_WIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789799680'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058235137')},
		},
		["GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_PANEL_LOSE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789799936'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058235393')},
		},
		["GUILD_LEAGUE_NO_QUALIFICATION_DESC"] = {
			["Key"] = "GUILD_LEAGUE_NO_QUALIFICATION_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789800192'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058235649')},
		},
		["GUILD_LEAGUE_WAIT_NEXT_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_WAIT_NEXT_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789800448'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058235905')},
		},
		["GUILD_LEAGUE_START_TIME_DETAIL_DESC"] = {
			["Key"] = "GUILD_LEAGUE_START_TIME_DETAIL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789800704'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058236161')},
		},
		["GUILD_LEAGUE_ACTIVITY_END_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ACTIVITY_END_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789722624'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058236417')},
		},
		["GUILD_LEAGUE_NO_GROUP_NAME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_NO_GROUP_NAME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789801216'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058236673')},
		},
		["GUILD_LEAGUE_OVER_MAX_GROUP_NAME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_OVER_MAX_GROUP_NAME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55938459379456'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058236929')},
		},
		["GUILD_LEAGUE_PRELIMINARY_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_PRELIMINARY_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789801728'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058237185')},
		},
		["GUILD_LEAGUE_FINAL_ROUND_DESC"] = {
			["Key"] = "GUILD_LEAGUE_FINAL_ROUND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789801984'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058237441')},
		},
		["GUILD_LEAGUE_GO_TO_LEAGUE_DESC"] = {
			["Key"] = "GUILD_LEAGUE_GO_TO_LEAGUE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789802240'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058237697')},
		},
		["GUILD_LEAGUE_ROUND_DISPLAY_DESC"] = {
			["Key"] = "GUILD_LEAGUE_ROUND_DISPLAY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789802496'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058237953')},
		},
		["GUILD_LEAGUE_TAB_COMMAND_STRATEGY"] = {
			["Key"] = "GUILD_LEAGUE_TAB_COMMAND_STRATEGY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789802752'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058238209')},
		},
		["GUILD_LEAGUE_TAB_TAG"] = {
			["Key"] = "GUILD_LEAGUE_TAB_TAG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789803008'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058238209')},
		},
		["GUILD_LEAGUE_FIRST_ROUND_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_FIRST_ROUND_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789803264'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058238721')},
		},
		["GUILD_LEAGUE_SECOND_ROUND_TIME_DESC"] = {
			["Key"] = "GUILD_LEAGUE_SECOND_ROUND_TIME_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789803520'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058238977')},
		},
		["GUILD_LEAGUE_GROUP_TAB_ALL"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_TAB_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38552431824896'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058239233')},
		},
		["GUILD_LEAGUE_GROUP_NEED_RESOURCE"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_NEED_RESOURCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789804032'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058239489')},
		},
		["GUILD_LEAGUE_GROUP_COURAGE_MAX"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_COURAGE_MAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789804288'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058239745')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TEXT_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TEXT_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789804544'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058240001')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TEXT_TITLE"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TEXT_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789804800'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058240257')},
		},
		["GUILD_LEAGUE_GROUP_NAME_CONST"] = {
			["Key"] = "GUILD_LEAGUE_GROUP_NAME_CONST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_4811168680704'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058240513')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_NOT_ACTIVATE"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_NOT_ACTIVATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789805312'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058240769')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_OCCUPY"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_OCCUPY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789805568'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241025')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_BATTLE"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_IN_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789805824'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241281')},
		},
		["GUILD_LEAGUE_OCCUPY_DETECT_AREA_OCCUPIED"] = {
			["Key"] = "GUILD_LEAGUE_OCCUPY_DETECT_AREA_OCCUPIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789806080'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241537')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789806336'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241793')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_CLICK_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_CLICK_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789806592'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241793')},
		},
		["GUILD_LEAGUE_MODIFY_TAG_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_MODIFY_TAG_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789806848'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058242305')},
		},
		["GUILD_LEAGUE_BATTLE_FLAG_GROUP_TIPS"] = {
			["Key"] = "GUILD_LEAGUE_BATTLE_FLAG_GROUP_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789807104'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058241793')},
		},
		["GUILD_LEAGUE_RESOURCE_DEC"] = {
			["Key"] = "GUILD_LEAGUE_RESOURCE_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789807360'),
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058242817')},
		},
		["INVITE_LIST_RANGE"] = {
			["Key"] = "INVITE_LIST_RANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789807616'),
			["StringValueList"] = {},
		},
		["PARTNER_LIKE"] = {
			["Key"] = "PARTNER_LIKE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789807872'),
			["StringValueList"] = {},
		},
		["COMBO_SCORE_TIP"] = {
			["Key"] = "COMBO_SCORE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_53396107174144'),
			["StringValueList"] = {},
		},
		["ASSIST_SCORE_TIP"] = {
			["Key"] = "ASSIST_SCORE_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789808384'),
			["StringValueList"] = {},
		},
		["ASSIST_CLICK_TIP"] = {
			["Key"] = "ASSIST_CLICK_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789808640'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_CD"] = {
			["Key"] = "SETTLEMENT_CD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789808896'),
			["StringValueList"] = {},
		},
		["INVITE_COUNTDOWN"] = {
			["Key"] = "INVITE_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789809152'),
			["StringValueList"] = {},
		},
		["MATCH_WAIT"] = {
			["Key"] = "MATCH_WAIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789809408'),
			["StringValueList"] = {},
		},
		["MATCH_COUNTDOWN"] = {
			["Key"] = "MATCH_COUNTDOWN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789809664'),
			["StringValueList"] = {},
		},
		["MUSIC_NAME_1"] = {
			["Key"] = "MUSIC_NAME_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789809920'),
			["StringValueList"] = {},
		},
		["REWARD_TIPS"] = {
			["Key"] = "REWARD_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789810176'),
			["StringValueList"] = {},
		},
		["INVITE_ALREEADY"] = {
			["Key"] = "INVITE_ALREEADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789810432'),
			["StringValueList"] = {},
		},
		["INVITE_ENABLE"] = {
			["Key"] = "INVITE_ENABLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789810688'),
			["StringValueList"] = {},
		},
		["IN_PROCESS"] = {
			["Key"] = "IN_PROCESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789810944'),
			["StringValueList"] = {},
		},
		["REJECT_ALREADY"] = {
			["Key"] = "REJECT_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789583360'),
			["StringValueList"] = {},
		},
		["TEAM_REWARD"] = {
			["Key"] = "TEAM_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789811456'),
			["StringValueList"] = {},
		},
		["GUILD_REWARD"] = {
			["Key"] = "GUILD_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789811712'),
			["StringValueList"] = {},
		},
		["PARTNER_INVITE"] = {
			["Key"] = "PARTNER_INVITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_61299115431424'),
			["StringValueList"] = {},
		},
		["CASH_GIFT_CONTENT"] = {
			["Key"] = "CASH_GIFT_CONTENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789812224'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_1"] = {
			["Key"] = "INVITATION_LETTER_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789812480'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_2"] = {
			["Key"] = "INVITATION_LETTER_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789812736'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_3"] = {
			["Key"] = "INVITATION_LETTER_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789812992'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_4"] = {
			["Key"] = "INVITATION_LETTER_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789813248'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_5"] = {
			["Key"] = "INVITATION_LETTER_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789424896'),
			["StringValueList"] = {},
		},
		["INVITATION_LETTER_6"] = {
			["Key"] = "INVITATION_LETTER_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789813760'),
			["StringValueList"] = {},
		},
		["DANCE_TITLE"] = {
			["Key"] = "DANCE_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789814016'),
			["StringValueList"] = {},
		},
		["DANCE_HOT_BAR_1"] = {
			["Key"] = "DANCE_HOT_BAR_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789814272'),
			["StringValueList"] = {},
		},
		["DANCE_HOT_BAR_2"] = {
			["Key"] = "DANCE_HOT_BAR_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789814528'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_1"] = {
			["Key"] = "DANCE_FIRE_TIP_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789814784'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_2"] = {
			["Key"] = "DANCE_FIRE_TIP_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789815040'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_3"] = {
			["Key"] = "DANCE_FIRE_TIP_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789815296'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_4"] = {
			["Key"] = "DANCE_FIRE_TIP_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789815552'),
			["StringValueList"] = {},
		},
		["DANCE_FIRE_TIP_5"] = {
			["Key"] = "DANCE_FIRE_TIP_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789815808'),
			["StringValueList"] = {},
		},
		["INVITATION_ACTION_TEXT"] = {
			["Key"] = "INVITATION_ACTION_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789816064'),
			["StringValueList"] = {},
		},
		["DANCE_NPC_NAME"] = {
			["Key"] = "DANCE_NPC_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27901986604544'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_RANKING_NO_SCORE"] = {
			["Key"] = "GUILD_DANCE_RANKING_NO_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789816576'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_RANKING_TITLE"] = {
			["Key"] = "GUILD_DANCE_RANKING_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789816832'),
			["StringValueList"] = {},
		},
		["RED_PACKET_TEXT_1"] = {
			["Key"] = "RED_PACKET_TEXT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789817088'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_TEXT_1"] = {
			["Key"] = "SETTLEMENT_TEXT_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789817344'),
			["StringValueList"] = {},
		},
		["SETTLEMENT_TEXT_2"] = {
			["Key"] = "SETTLEMENT_TEXT_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789811456'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_OPENMIC"] = {
			["Key"] = "GUILD_DANCE_VOICE_OPENMIC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789624320'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_MUTE"] = {
			["Key"] = "GUILD_DANCE_VOICE_MUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789818112'),
			["StringValueList"] = {},
		},
		["GUILD_DANCE_VOICE_LISTEN"] = {
			["Key"] = "GUILD_DANCE_VOICE_LISTEN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789623808'),
			["StringValueList"] = {},
		},
		["COMBO_SCORE_MARK"] = {
			["Key"] = "COMBO_SCORE_MARK",
			["StringValue"] = "×",
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_DESC"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789818880'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_NAME"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25564450670848'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_OPR"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["GUILD_ACTIVITY_TASK_TIME"] = {
			["Key"] = "GUILD_ACTIVITY_TASK_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789819648'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_ALREADY_APPLIED"] = {
			["Key"] = "GUILD_APPLY_ALREADY_APPLIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178854656'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_APPLY"] = {
			["Key"] = "GUILD_APPLY_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582080'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_APPLY_COUNT"] = {
			["Key"] = "GUILD_APPLYLIST_APPLY_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789820416'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_AUTORECV"] = {
			["Key"] = "GUILD_APPLYLIST_AUTORECV",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789820672'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_NAME"] = {
			["Key"] = "GUILD_APPLYLIST_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789820928'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_ACCEPT"] = {
			["Key"] = "GUILD_APPLY_ACCEPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178857728'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_MEMBER"] = {
			["Key"] = "GUILD_APPLY_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789821440'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_CLASS"] = {
			["Key"] = "GUILD_APPLYLIST_CLASS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789821696'),
			["StringValueList"] = {},
		},
		["GUILD_APPLYLIST_ALL"] = {
			["Key"] = "GUILD_APPLYLIST_ALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
			["StringValueList"] = {},
		},
		["GUILD_BADGE_CHANGE"] = {
			["Key"] = "GUILD_BADGE_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789822208'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_CONDITION"] = {
			["Key"] = "GUILD_BUILD_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238980864'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_DESC_A"] = {
			["Key"] = "GUILD_BUILD_FOUR_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789822720'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_DESC_B"] = {
			["Key"] = "GUILD_BUILD_FOUR_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789822976'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_EFFECT"] = {
			["Key"] = "GUILD_BUILD_FOUR_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789823232'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FOUR_NAME"] = {
			["Key"] = "GUILD_BUILD_FOUR_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789823488'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_FUNDS"] = {
			["Key"] = "GUILD_BUILD_FUNDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789823744'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_LEVEL"] = {
			["Key"] = "GUILD_BUILD_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789419264'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_MAIN_CONDITION"] = {
			["Key"] = "GUILD_BUILD_MAIN_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789824256'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_MAX_LEVEL"] = {
			["Key"] = "GUILD_BUILD_MAX_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748736'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_DESC_A"] = {
			["Key"] = "GUILD_BUILD_ONE_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27145803924993'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_DESC_B"] = {
			["Key"] = "GUILD_BUILD_ONE_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27145803924994'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_EFFECT"] = {
			["Key"] = "GUILD_BUILD_ONE_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789825280'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_ONE_NAME"] = {
			["Key"] = "GUILD_BUILD_ONE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34983582379776'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_SUB_CONDITION"] = {
			["Key"] = "GUILD_BUILD_SUB_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789825792'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_A"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27145803925505'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_B"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27145803925506'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_C"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_C",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789826560'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_DESC_D"] = {
			["Key"] = "GUILD_BUILD_THREE_DESC_D",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27145803925508'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_EFFECT"] = {
			["Key"] = "GUILD_BUILD_THREE_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789827072'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_THREE_NAME"] = {
			["Key"] = "GUILD_BUILD_THREE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789827328'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TIME"] = {
			["Key"] = "GUILD_BUILD_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789827584'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_A"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_A",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789827840'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_B"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_B",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789553408'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_DESC_C"] = {
			["Key"] = "GUILD_BUILD_TWO_DESC_C",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789828352'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_EFFECT"] = {
			["Key"] = "GUILD_BUILD_TWO_EFFECT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789828608'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_TWO_NAME"] = {
			["Key"] = "GUILD_BUILD_TWO_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642599424'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UPGRADE"] = {
			["Key"] = "GUILD_BUILD_UPGRADE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UPGRADE_INFO"] = {
			["Key"] = "GUILD_BUILD_UPGRADE_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789829376'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_UNLOCK"] = {
			["Key"] = "GUILD_BUILD_UNLOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789829632'),
			["StringValueList"] = {},
		},
		["GUILD_CANCEL"] = {
			["Key"] = "GUILD_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
			["StringValueList"] = {},
		},
		["GUILD_CONFIRM"] = {
			["Key"] = "GUILD_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
			["StringValueList"] = {},
		},
		["GUILD_SEND"] = {
			["Key"] = "GUILD_SEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789325824'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_DECLERATION_PROMPT"] = {
			["Key"] = "GUILD_CREATE_DECLERATION_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789830656'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_EMPTY"] = {
			["Key"] = "GUILD_CREATE_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789830912'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_NAME_PROMPT"] = {
			["Key"] = "GUILD_CREATE_NAME_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789831168'),
			["StringValueList"] = {},
		},
		["GUILD_CREATE_REQUIREMENT"] = {
			["Key"] = "GUILD_CREATE_REQUIREMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789831424'),
			["StringValueList"] = {},
		},
		["GUILD_EVENT_NAME"] = {
			["Key"] = "GUILD_EVENT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789831680'),
			["StringValueList"] = {},
		},
		["GUILD_EVENT_TIME"] = {
			["Key"] = "GUILD_EVENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789831936'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_SEND_NAME"] = {
			["Key"] = "GUILD_GROUP_SEND_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789832192'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_SEND_TIP"] = {
			["Key"] = "GUILD_GROUP_SEND_TIP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789832448'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_BONUS"] = {
			["Key"] = "GUILD_HOME_BONUS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_33604629436928'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_CHANGE_DEC"] = {
			["Key"] = "GUILD_HOME_CHANGE_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789832960'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_CHANGE_DEC_PROMPT"] = {
			["Key"] = "GUILD_HOME_CHANGE_DEC_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789833216'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_EVENT"] = {
			["Key"] = "GUILD_HOME_EVENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789833472'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_FORMAL_COUNT"] = {
			["Key"] = "GUILD_HOME_FORMAL_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789833728'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_FUND"] = {
			["Key"] = "GUILD_HOME_FUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789833984'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_ID"] = {
			["Key"] = "GUILD_HOME_ID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789834240'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_LIVE"] = {
			["Key"] = "GUILD_HOME_LIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789834496'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MAINTAIN"] = {
			["Key"] = "GUILD_HOME_MAINTAIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789834752'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MASTER"] = {
			["Key"] = "GUILD_HOME_MASTER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789835008'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_MEMBER_COUNT"] = {
			["Key"] = "GUILD_HOME_MEMBER_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789835264'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_ROLE"] = {
			["Key"] = "GUILD_HOME_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789831680'),
			["StringValueList"] = {},
		},
		["GUILD_HOME_TARGET"] = {
			["Key"] = "GUILD_HOME_TARGET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789835776'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_LEAVE"] = {
			["Key"] = "GUILD_LIST_LEAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789836032'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_LIST"] = {
			["Key"] = "GUILD_LIST_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789836288'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_RESIGN"] = {
			["Key"] = "GUILD_LIST_RESIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789836544'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_ID"] = {
			["Key"] = "GUILD_MEMBER_ID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789836800'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_LIST"] = {
			["Key"] = "GUILD_MEMBER_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582592'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_SET_NAME"] = {
			["Key"] = "GUILD_MEMBER_SET_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789837312'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_STRUCTURE"] = {
			["Key"] = "GUILD_MEMBER_STRUCTURE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789837568'),
			["StringValueList"] = {},
		},
		["GUILD_MERGE_APPLY"] = {
			["Key"] = "GUILD_MERGE_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789608448'),
			["StringValueList"] = {},
		},
		["GUILD_MERGE_NAME"] = {
			["Key"] = "GUILD_MERGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789838080'),
			["StringValueList"] = {},
		},
		["GUILD_NAME"] = {
			["Key"] = "GUILD_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590464'),
			["StringValueList"] = {},
		},
		["GUILD_NAME_COLON"] = {
			["Key"] = "GUILD_NAME_COLON",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789838592'),
			["StringValueList"] = {},
		},
		["GUILD_PERMISSION_NAME"] = {
			["Key"] = "GUILD_PERMISSION_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789838848'),
			["StringValueList"] = {},
		},
		["GUILD_POSITION_SET"] = {
			["Key"] = "GUILD_POSITION_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789839104'),
			["StringValueList"] = {},
		},
		["GUILD_POS"] = {
			["Key"] = "GUILD_POS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789839360'),
			["StringValueList"] = {},
		},
		["GUILD_RANKING_NAME"] = {
			["Key"] = "GUILD_RANKING_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789839616'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_NAME"] = {
			["Key"] = "GUILD_RENAME_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789839872'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_NO_USED_NAME"] = {
			["Key"] = "GUILD_RENAME_NO_USED_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789840128'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_USED_NAME"] = {
			["Key"] = "GUILD_RENAME_USED_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789840384'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_NAME"] = {
			["Key"] = "GUILD_REPORT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924500992'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONDS_EMPTY"] = {
			["Key"] = "GUILD_RESPONDS_EMPTY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789840896'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_ALREADY_RESPOND"] = {
			["Key"] = "GUILD_RESPONSE_ALREADY_RESPOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178854912'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_CANCEL"] = {
			["Key"] = "GUILD_RESPONSE_CANCEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178855680'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_CANCEL_CREATE"] = {
			["Key"] = "GUILD_RESPONSE_CANCEL_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789841664'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_RESPONSE"] = {
			["Key"] = "GUILD_RESPONSE_RESPONSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789841920'),
			["StringValueList"] = {},
		},
		["GUILD_SAVE"] = {
			["Key"] = "GUILD_SAVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789842176'),
			["StringValueList"] = {},
		},
		["GUILD_SETPOS_CURRENT"] = {
			["Key"] = "GUILD_SETPOS_CURRENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789842432'),
			["StringValueList"] = {},
		},
		["GUILD_SETPOS_NAME"] = {
			["Key"] = "GUILD_SETPOS_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789842688'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_INTRO"] = {
			["Key"] = "GUILD_SIGNIN_INTRO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789842944'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_NAME"] = {
			["Key"] = "GUILD_SIGNIN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789843200'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_PROMPT"] = {
			["Key"] = "GUILD_SIGNIN_PROMPT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789843456'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BUILDING_REQ"] = {
			["Key"] = "GUILD_SKILL_BUILDING_REQ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789843712'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BUILDING_REQ_RED"] = {
			["Key"] = "GUILD_SKILL_BUILDING_REQ_RED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789843968'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_EXERCISE"] = {
			["Key"] = "GUILD_SKILL_EXERCISE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789844224'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL"] = {
			["Key"] = "GUILD_SKILL_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789844480'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL_REQ"] = {
			["Key"] = "GUILD_SKILL_LEVEL_REQ",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789844736'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_LEVEL_REQ_RED"] = {
			["Key"] = "GUILD_SKILL_LEVEL_REQ_RED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789844992'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_NAME"] = {
			["Key"] = "GUILD_SKILL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505506304'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_NEXT"] = {
			["Key"] = "GUILD_SKILL_NEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789845504'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_SKILL_TOP_LEVEL"] = {
			["Key"] = "GUILD_SKILL_SKILL_TOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789845760'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TOP_LEVEL"] = {
			["Key"] = "GUILD_SKILL_TOP_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789748736'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_ONE"] = {
			["Key"] = "GUILD_SKILL_TYPE_ONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789846272'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_THREE"] = {
			["Key"] = "GUILD_SKILL_TYPE_THREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789846528'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_TYPE_TWO"] = {
			["Key"] = "GUILD_SKILL_TYPE_TWO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789846784'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_DAY_AGO"] = {
			["Key"] = "GUILD_STATUS_DAY_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362688'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_HOUR_AGO"] = {
			["Key"] = "GUILD_STATUS_HOUR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362432'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_MIN_AGO"] = {
			["Key"] = "GUILD_STATUS_MIN_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789362176'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_MONTH_AGO"] = {
			["Key"] = "GUILD_STATUS_MONTH_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789847808'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_ONLINE"] = {
			["Key"] = "GUILD_STATUS_ONLINE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789519360'),
			["StringValueList"] = {},
		},
		["GUILD_STATUS_YEAR_AGO"] = {
			["Key"] = "GUILD_STATUS_YEAR_AGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789848320'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_ACTIVITY_PAGE"] = {
			["Key"] = "GUILD_TAG_ACTIVITY_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328564736'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_ACTIVITY_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_ACTIVITY_PAGE_ENG",
			["StringValue"] = "Activity",
			["StringValueList"] = {},
		},
		["GUILD_TAG_OVERVIEW"] = {
			["Key"] = "GUILD_TAG_OVERVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_138781135616'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_APPLY"] = {
			["Key"] = "GUILD_TAG_APPLY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789582080'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_APPLY_ENG"] = {
			["Key"] = "GUILD_TAG_APPLY_ENG",
			["StringValue"] = "Apply",
			["StringValueList"] = {},
		},
		["GUILD_TAG_BUILD_PAGE"] = {
			["Key"] = "GUILD_TAG_BUILD_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789849856'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_CREATE"] = {
			["Key"] = "GUILD_TAG_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178848512'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_CREATE_ENG"] = {
			["Key"] = "GUILD_TAG_CREATE_ENG",
			["StringValue"] = "Found",
			["StringValueList"] = {},
		},
		["GUILD_TAG_HOME_PAGE"] = {
			["Key"] = "GUILD_TAG_HOME_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811117824'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_HOME_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_HOME_PAGE_ENG",
			["StringValue"] = "Homepage",
			["StringValueList"] = {},
		},
		["GUILD_TAG_IN_RESPONSE"] = {
			["Key"] = "GUILD_TAG_IN_RESPONSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789851136'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_IN_RESPONSE_ENG"] = {
			["Key"] = "GUILD_TAG_IN_RESPONSE_ENG",
			["StringValue"] = "Respond",
			["StringValueList"] = {},
		},
		["GUILD_TAG_MEMBER_LIST"] = {
			["Key"] = "GUILD_TAG_MEMBER_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667840'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_MEMBER_LIST_ENG"] = {
			["Key"] = "GUILD_TAG_MEMBER_LIST_ENG",
			["StringValue"] = "Member",
			["StringValueList"] = {},
		},
		["GUILD_TAG_RESPONSE_OTHER"] = {
			["Key"] = "GUILD_TAG_RESPONSE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789851136'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_RESPONSE_OTHER_ENG"] = {
			["Key"] = "GUILD_TAG_RESPONSE_OTHER_ENG",
			["StringValue"] = "Respond",
			["StringValueList"] = {},
		},
		["GUILD_TAG_WELFARE_PAGE"] = {
			["Key"] = "GUILD_TAG_WELFARE_PAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_27146877667328'),
			["StringValueList"] = {},
		},
		["GUILD_TAG_WELFARE_PAGE_ENG"] = {
			["Key"] = "GUILD_TAG_WELFARE_PAGE_ENG",
			["StringValue"] = "Welfare",
			["StringValueList"] = {},
		},
		["GUILD_TARGET_ALREADY_ACQUIRE"] = {
			["Key"] = "GUILD_TARGET_ALREADY_ACQUIRE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_6735582465024'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_ALREADY_GET"] = {
			["Key"] = "GUILD_TARGET_ALREADY_GET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789853440'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CLAIMED"] = {
			["Key"] = "GUILD_TARGET_CLAIMED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789692928'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_COMPLETE"] = {
			["Key"] = "GUILD_TARGET_COMPLETE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789674496'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_COMPLETE_WEEK"] = {
			["Key"] = "GUILD_TARGET_COMPLETE_WEEK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789854208'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CURRENT_COUNT"] = {
			["Key"] = "GUILD_TARGET_CURRENT_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789854464'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_GET_REWARD"] = {
			["Key"] = "GUILD_TARGET_GET_REWARD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789589248'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_GOTO"] = {
			["Key"] = "GUILD_TARGET_GOTO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_HONOR_WALL"] = {
			["Key"] = "GUILD_TARGET_HONOR_WALL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789855232'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_MEDAL"] = {
			["Key"] = "GUILD_TARGET_MEDAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789855488'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_NAME"] = {
			["Key"] = "GUILD_TARGET_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789835776'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_PERSONAL_NAME"] = {
			["Key"] = "GUILD_TARGET_PERSONAL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789856000'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_RANK"] = {
			["Key"] = "GUILD_TARGET_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789856256'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_TOTAL_MEDAL"] = {
			["Key"] = "GUILD_TARGET_TOTAL_MEDAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789856512'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CONTRIBUTION"] = {
			["Key"] = "GUILD_TARGET_CONTRIBUTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789856768'),
			["StringValueList"] = {},
		},
		["GUILD_TARGET_CONTRIBUTION_RESET"] = {
			["Key"] = "GUILD_TARGET_CONTRIBUTION_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789857024'),
			["StringValueList"] = {},
		},
		["GUILD_TIME_HOUR"] = {
			["Key"] = "GUILD_TIME_HOUR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789857280'),
			["StringValueList"] = {},
		},
		["GUILD_TIP_CONTACT_NAME"] = {
			["Key"] = "GUILD_TIP_CONTACT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789857536'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_DESC"] = {
			["Key"] = "GUILD_WELFARE_SHOP_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789857792'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_NAME"] = {
			["Key"] = "GUILD_WELFARE_SHOP_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505504000'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SHOP_OPR"] = {
			["Key"] = "GUILD_WELFARE_SHOP_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811128832'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_DESC"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789858560'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_NAME"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789858816'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_OPR"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789843200'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_DESC"] = {
			["Key"] = "GUILD_WELFARE_SKILL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789859328'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_NAME"] = {
			["Key"] = "GUILD_WELFARE_SKILL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789859584'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SKILL_OPR"] = {
			["Key"] = "GUILD_WELFARE_SKILL_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_9488656505088'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_DESC"] = {
			["Key"] = "GUILD_WELFARE_WAGE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789860096'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_NAME"] = {
			["Key"] = "GUILD_WELFARE_WAGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789860352'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_WAGE_OPR"] = {
			["Key"] = "GUILD_WELFARE_WAGE_OPR",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789589248'),
			["StringValueList"] = {},
		},
		["GUILD_BACK_TO_GUILD_RESIDENCE"] = {
			["Key"] = "GUILD_BACK_TO_GUILD_RESIDENCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789860864'),
			["StringValueList"] = {},
		},
		["GUILD_APPLY_LIST"] = {
			["Key"] = "GUILD_APPLY_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789625344'),
			["StringValueList"] = {},
		},
		["GUILD_BATCH_SEND_MESSAGE"] = {
			["Key"] = "GUILD_BATCH_SEND_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789832192'),
			["StringValueList"] = {},
		},
		["GUILD_START_CHAT"] = {
			["Key"] = "GUILD_START_CHAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789861632'),
			["StringValueList"] = {},
		},
		["GUILD_CLEAR_LIST"] = {
			["Key"] = "GUILD_CLEAR_LIST",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178857472'),
			["StringValueList"] = {},
		},
		["GUILD_RESTORE_DEFAULT_SETTINGS"] = {
			["Key"] = "GUILD_RESTORE_DEFAULT_SETTINGS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789508864'),
			["StringValueList"] = {},
		},
		["GUILD_SET_ROLE_POSITION"] = {
			["Key"] = "GUILD_SET_ROLE_POSITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789862400'),
			["StringValueList"] = {},
		},
		["GUILD_DISMISS_ROLE_POSITION"] = {
			["Key"] = "GUILD_DISMISS_ROLE_POSITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789862656'),
			["StringValueList"] = {},
		},
		["GUILD_AUTO_SET_REGULAR_ROLE"] = {
			["Key"] = "GUILD_AUTO_SET_REGULAR_ROLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789862912'),
			["StringValueList"] = {},
		},
		["GUILD_CONFIRM_RELEASE"] = {
			["Key"] = "GUILD_CONFIRM_RELEASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789863168'),
			["StringValueList"] = {},
		},
		["GUILD_WELFARE_SIGNIN_VOICE"] = {
			["Key"] = "GUILD_WELFARE_SIGNIN_VOICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789863424'),
			["StringValueList"] = {},
		},
		["GUILD_PARTY_NAME"] = {
			["Key"] = "GUILD_PARTY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_1031865896448'),
			["StringValueList"] = {},
		},
		["GUILD_PARTY_DESC"] = {
			["Key"] = "GUILD_PARTY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789863936'),
			["StringValueList"] = {},
		},
		["GUILE_MATERIAL_NAME"] = {
			["Key"] = "GUILE_MATERIAL_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789864192'),
			["StringValueList"] = {},
		},
		["GUILE_MATERIAL_DESC"] = {
			["Key"] = "GUILE_MATERIAL_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789864448'),
			["StringValueList"] = {},
		},
		["GUILD_PUZZLE_START_QUESTION"] = {
			["Key"] = "GUILD_PUZZLE_START_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789864704'),
			["StringValueList"] = {},
		},
		["GUILD_PUZZLE_ALUODESI"] = {
			["Key"] = "GUILD_PUZZLE_ALUODESI",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38758590190336'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_AWARD_SCHEDULE"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_AWARD_SCHEDULE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789865216'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_BLESS"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_BLESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789865472'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_NEARLY_START"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_NEARLY_START",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789865728'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_ANSWER_NO_QUESTION"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_ANSWER_NO_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789865984'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_ANSWER_CERTAIN_QUESTION"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_ANSWER_CERTAIN_QUESTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789866240'),
			["StringValueList"] = {},
		},
		["GUILD_AFK_ACTIVITY_WITHOUT_BLESS"] = {
			["Key"] = "GUILD_AFK_ACTIVITY_WITHOUT_BLESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789866496'),
			["StringValueList"] = {},
		},
		["GUILD_NORMAL_CREATED"] = {
			["Key"] = "GUILD_NORMAL_CREATED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789866752'),
			["StringValueList"] = {},
		},
		["GUILD_ADVANCED_CREATED"] = {
			["Key"] = "GUILD_ADVANCED_CREATED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789867008'),
			["StringValueList"] = {},
		},
		["GUILD_COMMEN_SECOND"] = {
			["Key"] = "GUILD_COMMEN_SECOND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789867264'),
			["StringValueList"] = {},
		},
		["GUILD_LESS_THAN_CERTAIN_MINUTE"] = {
			["Key"] = "GUILD_LESS_THAN_CERTAIN_MINUTE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789867520'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BASE"] = {
			["Key"] = "GUILD_RIGHT_BASE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789867776'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MANAGE"] = {
			["Key"] = "GUILD_RIGHT_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505504256'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_PRESIDENT"] = {
			["Key"] = "GUILD_RIGHT_PRESIDENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789868288'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BABY"] = {
			["Key"] = "GUILD_RIGHT_BABY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789868544'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_POSITION_SET"] = {
			["Key"] = "GUILD_RIGHT_POSITION_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789868800'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_ANNOUNCE"] = {
			["Key"] = "GUILD_RIGHT_ANNOUNCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789869056'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_CONSTRUCTION"] = {
			["Key"] = "GUILD_RIGHT_CONSTRUCTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789869312'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_OPEN_ACTIVITY"] = {
			["Key"] = "GUILD_RIGHT_OPEN_ACTIVITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789869568'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_ACCEPT_MEMBER"] = {
			["Key"] = "GUILD_RIGHT_ACCEPT_MEMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789869824'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MERGE"] = {
			["Key"] = "GUILD_RIGHT_MERGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789870080'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_APPRENTICE"] = {
			["Key"] = "GUILD_RIGHT_APPRENTICE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789870336'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_KICKOUT"] = {
			["Key"] = "GUILD_RIGHT_KICKOUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789870592'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MODIFY_NAME"] = {
			["Key"] = "GUILD_RIGHT_MODIFY_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789839872'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_GROUP_MESSAGE"] = {
			["Key"] = "GUILD_RIGHT_GROUP_MESSAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789871104'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_RESIGN"] = {
			["Key"] = "GUILD_RIGHT_RESIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789836544'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_AUTO_RECEIVE"] = {
			["Key"] = "GUILD_RIGHT_AUTO_RECEIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789820672'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SKILL"] = {
			["Key"] = "GUILD_RIGHT_SKILL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789871872'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_COMMAND"] = {
			["Key"] = "GUILD_RIGHT_SET_COMMAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789872128'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_LOGO"] = {
			["Key"] = "GUILD_RIGHT_SET_LOGO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789872384'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_MODIFY_PRESIDENT_STATUE"] = {
			["Key"] = "GUILD_RIGHT_MODIFY_PRESIDENT_STATUE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789872640'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_BADGE_FRAME"] = {
			["Key"] = "GUILD_RIGHT_SET_BADGE_FRAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789872896'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_ELITE"] = {
			["Key"] = "GUILD_RIGHT_SET_ELITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789873152'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_BAN_ROOKIE_BID"] = {
			["Key"] = "GUILD_RIGHT_BAN_ROOKIE_BID",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789873408'),
			["StringValueList"] = {},
		},
		["GUILD_RIGHT_SET_GUILD_LEAGUE_ELITE"] = {
			["Key"] = "GUILD_RIGHT_SET_GUILD_LEAGUE_ELITE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789873664'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_ILLEGAL"] = {
			["Key"] = "GUILD_REPORT_TYPE_ILLEGAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789873920'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_STUDIO"] = {
			["Key"] = "GUILD_REPORT_TYPE_STUDIO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789874176'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_CHEATER"] = {
			["Key"] = "GUILD_REPORT_TYPE_CHEATER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789874432'),
			["StringValueList"] = {},
		},
		["GUILD_REPORT_TYPE_OTHER"] = {
			["Key"] = "GUILD_REPORT_TYPE_OTHER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328561920'),
			["StringValueList"] = {},
		},
		["GUILD_SKILL_BATTLE_SCORE"] = {
			["Key"] = "GUILD_SKILL_BATTLE_SCORE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789476864'),
			["StringValueList"] = {},
		},
		["GUILD_MAIL_SEND_FREE"] = {
			["Key"] = "GUILD_MAIL_SEND_FREE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789875200'),
			["StringValueList"] = {},
		},
		["GUILD_BACKGROUND_IMAGE"] = {
			["Key"] = "GUILD_BACKGROUND_IMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789875456'),
			["StringValueList"] = {},
		},
		["GUILD_MASCOT_NAME"] = {
			["Key"] = "GUILD_MASCOT_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789875712'),
			["StringValueList"] = {},
		},
		["GUILD_MASCOT_NAME_COUNT"] = {
			["Key"] = "GUILD_MASCOT_NAME_COUNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789875968'),
			["StringValueList"] = {},
		},
		["GUILD_INFORMATION_MODIFY"] = {
			["Key"] = "GUILD_INFORMATION_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789869056'),
			["StringValueList"] = {},
		},
		["GUILD_TYPE"] = {
			["Key"] = "GUILD_TYPE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789876480'),
			["StringValueList"] = {},
		},
		["GUILD_NAME_SHOW"] = {
			["Key"] = "GUILD_NAME_SHOW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789876736'),
			["StringValueList"] = {},
		},
		["GUILD_DEC"] = {
			["Key"] = "GUILD_DEC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789876992'),
			["StringValueList"] = {},
		},
		["GUILD_RESET"] = {
			["Key"] = "GUILD_RESET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789332224'),
			["StringValueList"] = {},
		},
		["GUILD_SET_CONFIRM"] = {
			["Key"] = "GUILD_SET_CONFIRM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789877504'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE"] = {
			["Key"] = "GUILD_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328560896'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE_ENG"] = {
			["Key"] = "GUILD_BATTLE_ENG",
			["StringValue"] = "Battle",
			["StringValueList"] = {},
		},
		["GUILD_FRIEND"] = {
			["Key"] = "GUILD_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_36009811121152'),
			["StringValueList"] = {},
		},
		["GUILD_FRIEND_ENG"] = {
			["Key"] = "GUILD_FRIEND_ENG",
			["StringValue"] = "Social",
			["StringValueList"] = {},
		},
		["GUILD_EASY"] = {
			["Key"] = "GUILD_EASY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_55869739894528'),
			["StringValueList"] = {},
		},
		["GUILD_EASY_ENG"] = {
			["Key"] = "GUILD_EASY_ENG",
			["StringValue"] = "Relax",
			["StringValueList"] = {},
		},
		["GUILD_PRESIDENT"] = {
			["Key"] = "GUILD_PRESIDENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789879296'),
			["StringValueList"] = {},
		},
		["GUILD_TOTAL"] = {
			["Key"] = "GUILD_TOTAL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789879552'),
			["StringValueList"] = {},
		},
		["GUILD_POSITION_MANAGE"] = {
			["Key"] = "GUILD_POSITION_MANAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_38553505504256'),
			["StringValueList"] = {},
		},
		["GUILD_LIST_SHOW_VACANCY"] = {
			["Key"] = "GUILD_LIST_SHOW_VACANCY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789880064'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_NAME"] = {
			["Key"] = "GUILD_MEMBER_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789880320'),
			["StringValueList"] = {},
		},
		["GUILD_TYPE_SHORT"] = {
			["Key"] = "GUILD_TYPE_SHORT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789880576'),
			["StringValueList"] = {},
		},
		["GUILD_BATTLE_DESC"] = {
			["Key"] = "GUILD_BATTLE_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789880832'),
			["StringValueList"] = {},
		},
		["GUILD_FRIEND_DESC"] = {
			["Key"] = "GUILD_FRIEND_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789881088'),
			["StringValueList"] = {},
		},
		["GUILD_EASY_DESC"] = {
			["Key"] = "GUILD_EASY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789881344'),
			["StringValueList"] = {},
		},
		["GUILD_RESPONSE_NUMBER"] = {
			["Key"] = "GUILD_RESPONSE_NUMBER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789881600'),
			["StringValueList"] = {},
		},
		["GUILD_BUILD_PREVIEW"] = {
			["Key"] = "GUILD_BUILD_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789881856'),
			["StringValueList"] = {},
		},
		["GUILD_CREATED_CONDITION"] = {
			["Key"] = "GUILD_CREATED_CONDITION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789882112'),
			["StringValueList"] = {},
		},
		["GUILD_NUM_HIGHLIGHT"] = {
			["Key"] = "GUILD_NUM_HIGHLIGHT",
			["StringValue"] = "<Title_Light>%d</>",
			["StringValueList"] = {},
		},
		["GUILD_NAME_INPUT"] = {
			["Key"] = "GUILD_NAME_INPUT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789882624'),
			["StringValueList"] = {},
		},
		["GUILD_MEMBER_SEARCH"] = {
			["Key"] = "GUILD_MEMBER_SEARCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789882880'),
			["StringValueList"] = {},
		},
		["GUILD_RENAME_MASCOT"] = {
			["Key"] = "GUILD_RENAME_MASCOT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789883136'),
			["StringValueList"] = {},
		},
		["GUILD_SEND_MAIL"] = {
			["Key"] = "GUILD_SEND_MAIL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789832192'),
			["StringValueList"] = {},
		},
		["GUILD_BACKGROUND_MODIFY"] = {
			["Key"] = "GUILD_BACKGROUND_MODIFY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789883648'),
			["StringValueList"] = {},
		},
		["GUILD_SIGNIN_ALREADY"] = {
			["Key"] = "GUILD_SIGNIN_ALREADY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789883904'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_1"] = {
			["Key"] = "GUILD_GROUP_NAME_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789884160'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_2"] = {
			["Key"] = "GUILD_GROUP_NAME_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789884416'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_3"] = {
			["Key"] = "GUILD_GROUP_NAME_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789884672'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_4"] = {
			["Key"] = "GUILD_GROUP_NAME_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789884928'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_5"] = {
			["Key"] = "GUILD_GROUP_NAME_5",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789885184'),
			["StringValueList"] = {},
		},
		["GUILD_GROUP_NAME_6"] = {
			["Key"] = "GUILD_GROUP_NAME_6",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789885440'),
			["StringValueList"] = {},
		},
		["CLUB_SEARCH_BOX_TXT"] = {
			["Key"] = "CLUB_SEARCH_BOX_TXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789885696'),
			["StringValueList"] = {},
		},
		["CLUB_INVITE_FRIENDS"] = {
			["Key"] = "CLUB_INVITE_FRIENDS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178859008'),
			["StringValueList"] = {},
		},
		["CLUB_BADGE"] = {
			["Key"] = "CLUB_BADGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789886208'),
			["StringValueList"] = {},
		},
		["GROUP_NAME_SET"] = {
			["Key"] = "GROUP_NAME_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789886464'),
			["StringValueList"] = {},
		},
		["GROUP_CONTROL_SET"] = {
			["Key"] = "GROUP_CONTROL_SET",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789886720'),
			["StringValueList"] = {},
		},
		["GUID_CHAT_RESPONSE_PROG"] = {
			["Key"] = "GUID_CHAT_RESPONSE_PROG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789886976'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_CLICK"] = {
			["Key"] = "GUID_RESPONSE_CLICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789887232'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSED"] = {
			["Key"] = "GUID_RESPONSED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178854912'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_LEFT_TIME_INFO"] = {
			["Key"] = "GUID_RESPONSE_LEFT_TIME_INFO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789887744'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_LEFT_TIME"] = {
			["Key"] = "GUID_RESPONSE_LEFT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_56007178853120'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_CHECK"] = {
			["Key"] = "GUID_RESPONSE_CHECK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_29756607199488'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_JOIN"] = {
			["Key"] = "GUID_RESPONSE_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789888512'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_OUTOFDATE"] = {
			["Key"] = "GUID_RESPONSE_OUTOFDATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789590784'),
			["StringValueList"] = {},
		},
		["GUID_RESPONSE_NONE"] = {
			["Key"] = "GUID_RESPONSE_NONE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789322496'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_VISIT"] = {
			["Key"] = "GUID_SHOW_VISIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789889280'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_DAYTIME_LIVE_NUM"] = {
			["Key"] = "GUID_SHOW_DAYTIME_LIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789889536'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_NIGHTTIME_LIVE_NUM"] = {
			["Key"] = "GUID_SHOW_NIGHTTIME_LIVE_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789889792'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_PROFESSION_NUM"] = {
			["Key"] = "GUID_SHOW_PROFESSION_NUM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789890048'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_NUMER"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_NUMER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789890304'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_PROGRESS"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_PROGRESS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789890560'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RESPONSED_INFO_TIME"] = {
			["Key"] = "GUID_SHOW_RESPONSED_INFO_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789890816'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_WORLD_RECRUITMENT"] = {
			["Key"] = "GUID_SHOW_WORLD_RECRUITMENT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789891072'),
			["StringValueList"] = {},
		},
		["GUID_SHOW_RECRUITMENT_TIME"] = {
			["Key"] = "GUID_SHOW_RECRUITMENT_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789891328'),
			["StringValueList"] = {},
		},
		["ARENA3V3_TITLE"] = {
			["Key"] = "ARENA3V3_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642590208'),
			["StringValueList"] = {},
		},
		["ARENA3V3_CREATE_TEAM"] = {
			["Key"] = "ARENA3V3_CREATE_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789597952'),
			["StringValueList"] = {},
		},
		["ARENA3V3_START_MATCH"] = {
			["Key"] = "ARENA3V3_START_MATCH",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54495618796800'),
			["StringValueList"] = {},
		},
		["ARENA3V3_QUICK_TEAM"] = {
			["Key"] = "ARENA3V3_QUICK_TEAM",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25908584909312'),
			["StringValueList"] = {},
		},
		["ARENA3V3_MARTCHING"] = {
			["Key"] = "ARENA3V3_MARTCHING",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789599744'),
			["StringValueList"] = {},
		},
		["ARENA3V3_CONFIRM_READY"] = {
			["Key"] = "ARENA3V3_CONFIRM_READY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789892864'),
			["StringValueList"] = {},
		},
		["SHOW_PROPERTY_EQUIP_CHANGE"] = {
			["Key"] = "SHOW_PROPERTY_EQUIP_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789893120'),
			["StringValueList"] = {},
		},
		["ACHIEVEMENT_LEVEL_BAR_TITLE"] = {
			["Key"] = "ACHIEVEMENT_LEVEL_BAR_TITLE",
			["StringValue"] = "%s<AchieveTileSmall>%s</>",
			["StringValueList"] = {},
		},
		["CHATROOM_BAN_TIPS"] = {
			["Key"] = "CHATROOM_BAN_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924493312'),
			["StringValueList"] = {},
		},
		["CHATROOM_BLACKLIST_TIPS"] = {
			["Key"] = "CHATROOM_BLACKLIST_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_5360924493568'),
			["StringValueList"] = {},
		},
		["PVP_STATS_RANK_POINT_CHANGE"] = {
			["Key"] = "PVP_STATS_RANK_POINT_CHANGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789894144'),
			["StringValueList"] = {},
		},
		["LOCK_TARGET_DISTANCE"] = {
			["Key"] = "LOCK_TARGET_DISTANCE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789894400'),
			["StringValueList"] = {},
		},
		["WEATHER_HUMIDITY"] = {
			["Key"] = "WEATHER_HUMIDITY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789894656'),
			["StringValueList"] = {},
		},
		["WEATHER_FOG"] = {
			["Key"] = "WEATHER_FOG",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789894912'),
			["StringValueList"] = {},
		},
		["WEATHER_LIGHT"] = {
			["Key"] = "WEATHER_LIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789895168'),
			["StringValueList"] = {},
		},
		["WEATHER_CLOUD"] = {
			["Key"] = "WEATHER_CLOUD",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789895424'),
			["StringValueList"] = {},
		},
		["WEATHER_MOON_PHASE"] = {
			["Key"] = "WEATHER_MOON_PHASE",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058331137'), Game.TableDataManager:GetLangStr('str_54633058331138'), Game.TableDataManager:GetLangStr('str_54633058331139'), Game.TableDataManager:GetLangStr('str_54633058331140'), Game.TableDataManager:GetLangStr('str_54633058331141'), Game.TableDataManager:GetLangStr('str_54633058331142'), Game.TableDataManager:GetLangStr('str_54633058331143'), Game.TableDataManager:GetLangStr('str_54633058331144')},
		},
		["WEATHER_CLOCK_NEXT_TIME"] = {
			["Key"] = "WEATHER_CLOCK_NEXT_TIME",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_54633058331393'), Game.TableDataManager:GetLangStr('str_54633058331394'), Game.TableDataManager:GetLangStr('str_54633058331395'), Game.TableDataManager:GetLangStr('str_54633058331396')},
		},
		["WEATHER_BLOOD_MOON"] = {
			["Key"] = "WEATHER_BLOOD_MOON",
			["StringValue"] = "",
			["StringValueList"] = {Game.TableDataManager:GetLangStr('str_11202616973056'), Game.TableDataManager:GetLangStr('str_54633058331650')},
		},
		["JOURNEY"] = {
			["Key"] = "JOURNEY",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789896448'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_UNLOCK_LEVEL"] = {
			["Key"] = "MANOR_BUILD_UNLOCK_LEVEL",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789327360'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_ITEM_HOLDMAX"] = {
			["Key"] = "MANOR_BUILD_ITEM_HOLDMAX",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789896960'),
			["StringValueList"] = {},
		},
		["MANOR_BUILD_UNLOCK_ITEM_NAME"] = {
			["Key"] = "MANOR_BUILD_UNLOCK_ITEM_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789897216'),
			["StringValueList"] = {},
		},
		["RANK_RANK_NO"] = {
			["Key"] = "RANK_RANK_NO",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789897472'),
			["StringValueList"] = {},
		},
		["RANK_NOT_IN_RANK"] = {
			["Key"] = "RANK_NOT_IN_RANK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789897728'),
			["StringValueList"] = {},
		},
		["RANK_NAME"] = {
			["Key"] = "RANK_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789335808'),
			["StringValueList"] = {},
		},
		["RANK_SETTLE_TIME"] = {
			["Key"] = "RANK_SETTLE_TIME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789898240'),
			["StringValueList"] = {},
		},
		["RANK_SEX_0"] = {
			["Key"] = "RANK_SEX_0",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789350912'),
			["StringValueList"] = {},
		},
		["RANK_SEX_1"] = {
			["Key"] = "RANK_SEX_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789351168'),
			["StringValueList"] = {},
		},
		["RANK_ONLY_FRIEND"] = {
			["Key"] = "RANK_ONLY_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789899008'),
			["StringValueList"] = {},
		},
		["RANK_REWARD_PREVIEW"] = {
			["Key"] = "RANK_REWARD_PREVIEW",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789899264'),
			["StringValueList"] = {},
		},
		["RANK_SERVER_DESC"] = {
			["Key"] = "RANK_SERVER_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789899520'),
			["StringValueList"] = {},
		},
		["RANK_CITY_DESC"] = {
			["Key"] = "RANK_CITY_DESC",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789899776'),
			["StringValueList"] = {},
		},
		["RANK_ALL_PROFESSION"] = {
			["Key"] = "RANK_ALL_PROFESSION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789900032'),
			["StringValueList"] = {},
		},
		["MANOR_AREASIZE"] = {
			["Key"] = "MANOR_AREASIZE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789900288'),
			["StringValueList"] = {},
		},
		["MANOR_BUILDING_CNT"] = {
			["Key"] = "MANOR_BUILDING_CNT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789900544'),
			["StringValueList"] = {},
		},
		["MANOR_BUILDING_HEIGHT"] = {
			["Key"] = "MANOR_BUILDING_HEIGHT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789894400'),
			["StringValueList"] = {},
		},
		["HUD_SKILL_BRIEFDESCRIPTION"] = {
			["Key"] = "HUD_SKILL_BRIEFDESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789901056'),
			["StringValueList"] = {},
		},
		["HUD_SKILL_DESCRIPTION"] = {
			["Key"] = "HUD_SKILL_DESCRIPTION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789901312'),
			["StringValueList"] = {},
		},
		["MANOR_TOTAL_SCORE"] = {
			["Key"] = "MANOR_TOTAL_SCORE",
			["StringValue"] = "<Green>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_COIN_LIMIT"] = {
			["Key"] = "MANOR_COIN_LIMIT",
			["StringValue"] = "<Moleculus>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_CURRENCY_ACQUIRED_THIS_WEEK"] = {
			["Key"] = "MANOR_CURRENCY_ACQUIRED_THIS_WEEK",
			["StringValue"] = "<Moleculus>%s</><Denominator>/%s</>",
			["StringValueList"] = {},
		},
		["SUBMIT_OPTIONAL_TEXT_FORMAT"] = {
			["Key"] = "SUBMIT_OPTIONAL_TEXT_FORMAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789902336'),
			["StringValueList"] = {},
		},
		["SUBMIT_DISABLED_PROGRESS_FORMAT"] = {
			["Key"] = "SUBMIT_DISABLED_PROGRESS_FORMAT",
			["StringValue"] = "<T_Disable>%d</>/%d",
			["StringValueList"] = {},
		},
		["SUBMIT_ENABLED_PROGRESS_FORMAT"] = {
			["Key"] = "SUBMIT_ENABLED_PROGRESS_FORMAT",
			["StringValue"] = "%d/%d",
			["StringValueList"] = {},
		},
		["MANOR_ROLE_LEVEL_LIMIT"] = {
			["Key"] = "MANOR_ROLE_LEVEL_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789903104'),
			["StringValueList"] = {},
		},
		["MANOR_TOTAL_SCORE_LIMIT"] = {
			["Key"] = "MANOR_TOTAL_SCORE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238979584'),
			["StringValueList"] = {},
		},
		["MANOR_PUT_SCORE_LIMIT"] = {
			["Key"] = "MANOR_PUT_SCORE_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_34567238978560'),
			["StringValueList"] = {},
		},
		["MANOR_WORKSHOP_LIMIT"] = {
			["Key"] = "MANOR_WORKSHOP_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789903872'),
			["StringValueList"] = {},
		},
		["MANOR_HOME_COIN_LIMIT"] = {
			["Key"] = "MANOR_HOME_COIN_LIMIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789904128'),
			["StringValueList"] = {},
		},
		["MANOR_SATISFIED"] = {
			["Key"] = "MANOR_SATISFIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789904384'),
			["StringValueList"] = {},
		},
		["MANOR_NOTSATISFIED"] = {
			["Key"] = "MANOR_NOTSATISFIED",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789904640'),
			["StringValueList"] = {},
		},
		["MANOR_SATISFIED_VALUE"] = {
			["Key"] = "MANOR_SATISFIED_VALUE",
			["StringValue"] = "<UpgradGreen>%s</><Default>/%s</>",
			["StringValueList"] = {},
		},
		["MANOR_NOTSATISFIED_VALUE"] = {
			["Key"] = "MANOR_NOTSATISFIED_VALUE",
			["StringValue"] = "<UpgradRed>%s</><Default>/%s</>",
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CREATE"] = {
			["Key"] = "CHAMPION_OP_CREATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789905408'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_QUIT"] = {
			["Key"] = "CHAMPION_OP_QUIT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789905664'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_DISBAND"] = {
			["Key"] = "CHAMPION_OP_DISBAND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789905920'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_JOIN"] = {
			["Key"] = "CHAMPION_OP_JOIN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789906176'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_KICK"] = {
			["Key"] = "CHAMPION_OP_KICK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789906432'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_SIGN_UP"] = {
			["Key"] = "CHAMPION_OP_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789906688'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CHANGE_NAME"] = {
			["Key"] = "CHAMPION_OP_CHANGE_NAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789906944'),
			["StringValueList"] = {},
		},
		["CHAMPION_OP_CHANGE_LEADER"] = {
			["Key"] = "CHAMPION_OP_CHANGE_LEADER",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789621760'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_CLOSE"] = {
			["Key"] = "CHAMPION_STAGE_CLOSE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789907456'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_SIGN_UP"] = {
			["Key"] = "CHAMPION_STAGE_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789907712'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_SIGN_UP_LOCK"] = {
			["Key"] = "CHAMPION_STAGE_SIGN_UP_LOCK",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789907968'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_PREPARE"] = {
			["Key"] = "CHAMPION_STAGE_PREPARE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789908224'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_BATTLE"] = {
			["Key"] = "CHAMPION_STAGE_BATTLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789908480'),
			["StringValueList"] = {},
		},
		["CHAMPION_STAGE_CALCULATE"] = {
			["Key"] = "CHAMPION_STAGE_CALCULATE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789908736'),
			["StringValueList"] = {},
		},
		["MANOR_SOCIAL_TAB_FRIEND"] = {
			["Key"] = "MANOR_SOCIAL_TAB_FRIEND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_25839328568576'),
			["StringValueList"] = {},
		},
		["MANOR_SOCIAL_TAB_RECOMMENDATION"] = {
			["Key"] = "MANOR_SOCIAL_TAB_RECOMMENDATION",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789359104'),
			["StringValueList"] = {},
		},
		["MANOR_VISIT_HOME"] = {
			["Key"] = "MANOR_VISIT_HOME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789909504'),
			["StringValueList"] = {},
		},
		["CHAMPION_TROOP_STATUS_NOT_SIGN"] = {
			["Key"] = "CHAMPION_TROOP_STATUS_NOT_SIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789909760'),
			["StringValueList"] = {},
		},
		["CHAMPION_TROOP_STATUS_SIGN_UP"] = {
			["Key"] = "CHAMPION_TROOP_STATUS_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789910016'),
			["StringValueList"] = {},
		},
		["SealMaterial_MainInterface_Title"] = {
			["Key"] = "SealMaterial_MainInterface_Title",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["StringValueList"] = {},
		},
		["SealMaterial_Label1_Text"] = {
			["Key"] = "SealMaterial_Label1_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_22472074200832'),
			["StringValueList"] = {},
		},
		["SealMaterial_Label2_Text"] = {
			["Key"] = "SealMaterial_Label2_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789910784'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type1_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type1_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789911040'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type2_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type2_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789911296'),
			["StringValueList"] = {},
		},
		["SealMaterial_Ordinary_Type3_Text"] = {
			["Key"] = "SealMaterial_Ordinary_Type3_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789911552'),
			["StringValueList"] = {},
		},
		["SealMaterial_Unassembled_Text"] = {
			["Key"] = "SealMaterial_Unassembled_Text",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789911808'),
			["StringValueList"] = {},
		},
		["SealMaterial_Assembly_Button"] = {
			["Key"] = "SealMaterial_Assembly_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789651968'),
			["StringValueList"] = {},
		},
		["SealMaterial_Replace_Button"] = {
			["Key"] = "SealMaterial_Replace_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789481728'),
			["StringValueList"] = {},
		},
		["SealMaterial_Remove_Button"] = {
			["Key"] = "SealMaterial_Remove_Button",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_31337155138816'),
			["StringValueList"] = {},
		},
		["SealMaterial_Extraordinary_abilities"] = {
			["Key"] = "SealMaterial_Extraordinary_abilities",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789912832'),
			["StringValueList"] = {},
		},
		["SealMaterial_Negative_Effects"] = {
			["Key"] = "SealMaterial_Negative_Effects",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789497856'),
			["StringValueList"] = {},
		},
		["SealMaterial_Extraordinary_Substance"] = {
			["Key"] = "SealMaterial_Extraordinary_Substance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789913344'),
			["StringValueList"] = {},
		},
		["SealMaterial_Synthetic_Entrance"] = {
			["Key"] = "SealMaterial_Synthetic_Entrance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789913600'),
			["StringValueList"] = {},
		},
		["SealMaterial_Backpack_Entrance"] = {
			["Key"] = "SealMaterial_Backpack_Entrance",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789913344'),
			["StringValueList"] = {},
		},
		["MANOR_COIN_LIMIT_UPGRADE"] = {
			["Key"] = "MANOR_COIN_LIMIT_UPGRADE",
			["StringValue"] = "<TitleDefault>%s+</><TitleHighlight>%s</>",
			["StringValueList"] = {},
		},
		["CutsceneText_CotardDefeat"] = {
			["Key"] = "CutsceneText_CotardDefeat",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789914368'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R1"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789914624'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R2"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789914880'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R3"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789915136'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_R4"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_R4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789915392'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R1"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789915648'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R2"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789915904'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R3"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789916160'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_PLAYOFF_TEXT_FULL_R4"] = {
			["Key"] = "PVP_CHAMPION_PLAYOFF_TEXT_FULL_R4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789916416'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_GROUP_TEXT"] = {
			["Key"] = "PVP_CHAMPION_GROUP_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789916672'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_GROUP_TEXT_WITH_ROUND"] = {
			["Key"] = "PVP_CHAMPION_GROUP_TEXT_WITH_ROUND",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789916928'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_DEFEAT"] = {
			["Key"] = "PVP_CHAMPION_STATS_DEFEAT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789917184'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_SURVIVE"] = {
			["Key"] = "PVP_CHAMPION_STATS_SURVIVE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789917440'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_STATS_DAMAGE"] = {
			["Key"] = "PVP_CHAMPION_STATS_DAMAGE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789917696'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_UP"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_UP",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789917952'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_AWAIT_SIGN"] = {
			["Key"] = "PVP_CHAMPION_TEAM_AWAIT_SIGN",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789918208'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_GAME"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_GAME",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789918464'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_1"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_1",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789918720'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_2"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_2",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789918976'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_3"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_3",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789919232'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TEAM_SIGN_PROGRESS_4"] = {
			["Key"] = "PVP_CHAMPION_TEAM_SIGN_PROGRESS_4",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789916672'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_TITLE"] = {
			["Key"] = "PVP_CHAMPION_TITLE",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_32574642610688'),
			["StringValueList"] = {},
		},
		["PVP_CHAMPION_SIGN_TIPS"] = {
			["Key"] = "PVP_CHAMPION_SIGN_TIPS",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789920000'),
			["StringValueList"] = {},
		},
		["REMOVE_DEADLOCK_READING_TEXT"] = {
			["Key"] = "REMOVE_DEADLOCK_READING_TEXT",
			["StringValue"] = Game.TableDataManager:GetLangStr('str_54632789920256'),
			["StringValueList"] = {},
		},
	},
}

return TopData
