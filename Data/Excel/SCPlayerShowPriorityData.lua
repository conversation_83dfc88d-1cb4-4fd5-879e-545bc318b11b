--
-- 表名: NEW Inputs: (AoiLevel.xlsx, SCPlayerShowPriority); Outputs: SCPlayerShowPriorityData; Def:data_post_export_aoi_level_sc_player_show_priority.lua
--

local TopData = {
    SCPlayerShowPriorityMap = {{3, 5, 2, 4, 6}, {2, 3, 4, 5, 6}, {3, 5, 2, 4, 2}, 
    },
    data = {
        [1] = {
            ['Enemy'] = 6, 
            ['GroupMate'] = 5, 
            ['ID'] = 1, 
            ['Lover'] = 2, 
            ['MinEnemyCount'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319360'),
            ['ShowMode'] = 'SMART_MODE', 
            ['SwornBrothers'] = 4, 
            ['TeamMate'] = 3, 
        },
        [2] = {
            ['Enemy'] = 6, 
            ['GroupMate'] = 3, 
            ['ID'] = 2, 
            ['Lover'] = 4, 
            ['MinEnemyCount'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319616'),
            ['ShowMode'] = 'FRIENDLY_FIRST_MODE', 
            ['SwornBrothers'] = 5, 
            ['TeamMate'] = 2, 
        },
        [3] = {
            ['Enemy'] = 2, 
            ['GroupMate'] = 5, 
            ['ID'] = 3, 
            ['Lover'] = 2, 
            ['MinEnemyCount'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_1443914319872'),
            ['ShowMode'] = 'ENEMY_FIRST_MODE', 
            ['SwornBrothers'] = 4, 
            ['TeamMate'] = 3, 
        },
    }
}
return TopData