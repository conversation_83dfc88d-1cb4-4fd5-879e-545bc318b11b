--
-- 表名: $RoleSkill_技能管理.xlsx  页名：$SkillType_技能类型
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["SkillType"] = "NormalAttack",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951644672'),
			["SlotList"] = {},
			["EquipMax"] = 0,
			["UnlockRequirement"] = "",
			["Tab"] = 1,
			["SubTitle"] = 2,
		},
		[2] = {
			["ID"] = 2,
			["SkillType"] = "SequenceSkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951644928'),
			["SlotList"] = {1, 2, 3, 4, 5},
			["EquipMax"] = 10,
			["UnlockRequirement"] = "",
			["Tab"] = 1,
			["SubTitle"] = 1,
		},
		[3] = {
			["ID"] = 3,
			["SkillType"] = "SpecialSkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951645184'),
			["SlotList"] = {},
			["EquipMax"] = 0,
			["UnlockRequirement"] = "",
			["Tab"] = 1,
			["SubTitle"] = 2,
		},
		[10] = {
			["ID"] = 10,
			["SkillType"] = "Other",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951645440'),
			["SlotList"] = {},
			["EquipMax"] = 0,
			["UnlockRequirement"] = "",
			["Tab"] = 1,
			["SubTitle"] = 3,
		},
		[4] = {
			["ID"] = 4,
			["SkillType"] = "DiscoverySkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951645696'),
			["SlotList"] = {1, 2, 3, 4, 5, 17, 18},
			["EquipMax"] = 10,
			["UnlockRequirement"] = "",
			["Tab"] = 4,
			["SubTitle"] = 1,
		},
		[5] = {
			["ID"] = 5,
			["SkillType"] = "IdentityFightSkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951645952'),
			["SlotList"] = {1, 2, 3, 4, 5, 17, 18},
			["EquipMax"] = 10,
			["UnlockRequirement"] = "",
			["Tab"] = 5,
			["SubTitle"] = 0,
		},
		[6] = {
			["ID"] = 6,
			["SkillType"] = "IdentityPlaySkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951646208'),
			["SlotList"] = {1, 2, 3, 4, 5, 17, 18},
			["EquipMax"] = 10,
			["UnlockRequirement"] = "",
			["Tab"] = 5,
			["SubTitle"] = 1,
		},
		[7] = {
			["ID"] = 7,
			["SkillType"] = "FellowSkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951646464'),
			["SlotList"] = {17, 18},
			["EquipMax"] = 2,
			["UnlockRequirement"] = "",
			["Tab"] = 2,
			["SubTitle"] = 1,
		},
		[8] = {
			["ID"] = 8,
			["SkillType"] = "ExtraordinarySkill",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951646720'),
			["SlotList"] = {20},
			["EquipMax"] = 1,
			["UnlockRequirement"] = "",
			["Tab"] = 1,
			["SubTitle"] = 3,
		},
		[16] = {
			["ID"] = 16,
			["SkillType"] = "SealedSkill_2D",
			["SkillTypeDesc"] = Game.TableDataManager:GetLangStr('str_47554951646976'),
			["SlotList"] = {},
			["EquipMax"] = 0,
			["UnlockRequirement"] = "",
			["Tab"] = 2,
			["SubTitle"] = 2,
		},
	},
}

return TopData
