--
-- 表名: $RoleCreate.xlsx  页名：$ParamConst
--

local TopData = {
	data = {
		["Subtitling1"] = Game.TableDataManager:GetLangStr('str_46249281586688'),
		["Subtitling1DurationTime"] = 5,
		["Subtitling2"] = Game.TableDataManager:GetLangStr('str_46249281587200'),
		["Subtitling2DurationTime"] = 5,
		["Subtitling3"] = Game.TableDataManager:GetLangStr('str_46249281587712'),
		["Subtitling3DurationTime"] = 5,
		["SubtitlingCount"] = 3,
		["SubtitlingBlockContinueTime"] = 1.5,
		["QandATip"] = Game.TableDataManager:GetLangStr('str_46249281588736'),
		["TouchWeaponTip"] = Game.TableDataManager:GetLangStr('str_46249281588992'),
		["Round1"] = {1},
		["Round2"] = {3, 2},
		["Round3"] = {6, 7, 5, 4},
		["Round4"] = {1200002, 1200003, 1200003, 1200006, 1200003, 1200005, 1200005, 1200006},
		["Round0Sequence"] = 1000136,
		["Round1Sequence"] = 1000121,
		["Round2Sequence"] = 1000122,
		["Round3Sequence"] = 1000123,
		["Round4Sequence"] = 1000130,
		["RoleSwitchMultiplePlayTime"] = 1,
		["SwitchProfessionCurve"] = "/Game/Arts/Character/Animation/Player/Warrior_HighRes/Male/Switch_Role_In1.Switch_Role_In1",
		["SwitchProfessionQuickCurve"] = "/Game/Arts/Character/Animation/Player/Warrior_HighRes/Male/Switch_Quick_In.Switch_Quick_In",
		["SwitchProfessionInEffect"] = "/Game/Arts/Effects/CinematicsFX/CharacterCreationFX/NS_CharacteChange_02.NS_CharacteChange_02",
		["SwitchProfessionOutEffect"] = "/Game/Arts/Effects/CinematicsFX/CharacterCreationFX/NS_CharacteChange_01.NS_CharacteChange_01",
		["SwitchProfessionRoomWallCurve"] = "/Game/Arts/Maps/CharacterShowRoom/BP/RoomWallCurve.RoomWallCurve",
		["CameraParamCurve"] = "/Game/Arts/Cinematics/CharacterCreation/CreateBPLogic/C_CameraSetting.C_CameraSetting",
		["ProfessionLightInCurve"] = "/Game/Arts/Cinematics/CharacterCreation/CreateBPLogic/C_FadeIn.C_FadeIn",
	},
}

return TopData
