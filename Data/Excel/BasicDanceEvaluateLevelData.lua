--
-- 表名: BasicDanceEvaluateLevelData后处理
--

local TopData = {
    data = {
        [1] = {
            ['Accuracy'] = {100, 101}, 
            ['Const'] = 'PERFECT', 
            ['Id'] = 1, 
            ['Level'] = Game.TableDataManager:GetLangStr('str_2680864900608'),
            ['Reward'] = {{2004106, 10, 1}, }, 
        },
        [2] = {
            ['Accuracy'] = {80, 100}, 
            ['Const'] = 'GREAT', 
            ['Id'] = 2, 
            ['Level'] = Game.TableDataManager:GetLangStr('str_2749584377600'),
            ['Reward'] = {{2004106, 5, 1}, }, 
        },
        [3] = {
            ['Accuracy'] = {50, 80}, 
            ['Const'] = 'GOOD', 
            ['Id'] = 3, 
            ['Level'] = Game.TableDataManager:GetLangStr('str_2680864901120'),
            ['Reward'] = {{2004105, 10, 1}, }, 
        },
        [4] = {
            ['Accuracy'] = {0, 50}, 
            ['Const'] = 'COOL', 
            ['Id'] = 4, 
            ['Level'] = Game.TableDataManager:GetLangStr('str_2749584378112'),
            ['Reward'] = {{2004104, 10, 1}, }, 
        },
    }
}
return TopData