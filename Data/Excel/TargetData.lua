--
-- 表名: TargetData后处理
--

local TopData = {
    TrickIDToMarkID = {
        [3] = {[5100040] = {['MarkId'] = 5300003, }, [5100042] = {['MarkId'] = 5300006, }, [5100043] = {['MarkId'] = 5300007, }, [5100045] = {['MarkId'] = 5300008, }, [5100047] = {['MarkId'] = 5300009, }, }, 
    },
    data = {
        [5300001] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300001, 
            ['MatchCE'] = 0, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54632789598464'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem02.UI_Team_Img_TPlayItem02', 
            ['ProfessionNeed'] = {}, 
            ['Tab'] = 5390001, 
            ['TeamLimit'] = 0, 
            ['Trick'] = 0, 
            ['Type'] = 0, 
            ['bAllowMatch'] = false, 
            ['bSupportGroup'] = true, 
        },
        [5300002] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300002, 
            ['MatchCE'] = 0, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54632789598720'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem02.UI_Team_Img_TPlayItem02', 
            ['ProfessionNeed'] = {}, 
            ['Tab'] = 5390001, 
            ['TeamLimit'] = 0, 
            ['Trick'] = 0, 
            ['Type'] = 0, 
            ['bAllowMatch'] = false, 
            ['bSupportGroup'] = true, 
        },
        [5300003] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300003, 
            ['MatchCE'] = 3600, 
            ['MatchPositionNeed'] = {[0] = 3, [1] = 1, [2] = 1, }, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54083570370304'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem01.UI_Team_Img_TPlayItem01', 
            ['ProfessionNeed'] = {0, 0, 0, 1, 2}, 
            ['Tab'] = 5390004, 
            ['TeamLimit'] = 1, 
            ['Trick'] = 5100040, 
            ['Type'] = 3, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = false, 
        },
        [5300005] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 1241601, 
            ['JumpPanel'] = 1250022, 
            ['MarkId'] = 5300005, 
            ['MatchCE'] = 0, 
            ['MatchPositionNeed'] = {[0] = 1, [2] = 1, }, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642590208'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem04.UI_Team_Img_TPlayItem04', 
            ['ProfessionNeed'] = {0, 2}, 
            ['Tab'] = 5390006, 
            ['TeamLimit'] = 1, 
            ['Trick'] = 5500002, 
            ['Type'] = 4, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = false, 
        },
        [5300006] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300006, 
            ['MatchCE'] = 5200, 
            ['MatchPositionNeed'] = {1, 1}, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597888'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem01.UI_Team_Img_TPlayItem01', 
            ['ProfessionNeed'] = {1, 2}, 
            ['Tab'] = 5390005, 
            ['TeamLimit'] = 2, 
            ['Trick'] = 5100042, 
            ['Type'] = 3, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = true, 
        },
        [5300007] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300007, 
            ['MatchCE'] = 5200, 
            ['MatchPositionNeed'] = {1, 1}, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597632'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem06.UI_Team_Img_TPlayItem06', 
            ['ProfessionNeed'] = {1, 2}, 
            ['Tab'] = 5390005, 
            ['TeamLimit'] = 2, 
            ['Trick'] = 5100043, 
            ['Type'] = 3, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = true, 
        },
        [5300008] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300008, 
            ['MatchCE'] = 5200, 
            ['MatchPositionNeed'] = {1, 1}, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642603776'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem03.UI_Team_Img_TPlayItem03', 
            ['ProfessionNeed'] = {1, 2}, 
            ['Tab'] = 5390005, 
            ['TeamLimit'] = 2, 
            ['Trick'] = 5100045, 
            ['Type'] = 3, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = true, 
        },
        [5300009] = {
            ['CrossParam'] = 0, 
            ['CrossType'] = 0, 
            ['ExpectTime'] = '05:00', 
            ['Function'] = 0, 
            ['JumpPanel'] = 0, 
            ['MarkId'] = 5300009, 
            ['MatchCE'] = 5200, 
            ['MatchPositionNeed'] = {1, 1}, 
            ['MatchStrategy'] = {{['Strategy'] = 0, ['Time'] = 10, }, {['Strategy'] = 1, ['Time'] = 10, }, {['Strategy'] = 2, ['Time'] = 20, }, }, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642608640'),
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Team/NotAtlas/UI_Team_Img_TPlayItem03.UI_Team_Img_TPlayItem03', 
            ['ProfessionNeed'] = {1, 2}, 
            ['Tab'] = 5390005, 
            ['TeamLimit'] = 2, 
            ['Trick'] = 5100047, 
            ['Type'] = 3, 
            ['bAllowMatch'] = true, 
            ['bSupportGroup'] = true, 
        },
    }
}
return TopData