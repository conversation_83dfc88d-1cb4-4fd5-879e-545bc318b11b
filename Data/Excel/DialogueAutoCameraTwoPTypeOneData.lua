--
-- 表名: $DialogueAutoCamera.xlsx  页名：$2P-Type1
--

local TopData = {
	data = {
		[101] = {
			["AutoCameraID"] = 101,
			["AutoCameraCutDesc"] = "越肩模式1（测试）",
			["H1"] = 0.7,
			["V1"] = 0.33,
			["H2"] = 0.2,
			["V2"] = 0.3,
			["HFov"] = 30,
			["theta"] = -30,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140749824'),
			["Weight"] = 7,
		},
		[102] = {
			["AutoCameraID"] = 102,
			["AutoCameraCutDesc"] = "正打-越肩视角",
			["H1"] = 0.7,
			["V1"] = 0.43,
			["H2"] = 0.13,
			["V2"] = 0.4,
			["HFov"] = 30,
			["theta"] = -18,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140749824'),
			["Weight"] = 7,
		},
		[103] = {
			["AutoCameraID"] = 103,
			["AutoCameraCutDesc"] = "正打-背视视角",
			["H1"] = 0.63,
			["V1"] = 0.34,
			["H2"] = 0.2,
			["V2"] = 0.35,
			["HFov"] = 25,
			["theta"] = -20,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140749824'),
			["Weight"] = 7,
		},
		[104] = {
			["AutoCameraID"] = 104,
			["AutoCameraCutDesc"] = "正打-腰部视角",
			["H1"] = 0.68,
			["V1"] = 0.4,
			["H2"] = 0.22,
			["V2"] = -0.15,
			["HFov"] = 15,
			["theta"] = -24,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140750592'),
			["Weight"] = 3,
		},
		[105] = {
			["AutoCameraID"] = 105,
			["AutoCameraCutDesc"] = "正打-同向视角1",
			["H1"] = 0.65,
			["V1"] = 0.38,
			["H2"] = 0.26,
			["V2"] = 0.35,
			["HFov"] = 40,
			["theta"] = -30,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[106] = {
			["AutoCameraID"] = 106,
			["AutoCameraCutDesc"] = "正打-同向视角2",
			["H1"] = 0.72,
			["V1"] = 0.3,
			["H2"] = 0.3,
			["V2"] = 0.35,
			["HFov"] = 40,
			["theta"] = -45,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[107] = {
			["AutoCameraID"] = 107,
			["AutoCameraCutDesc"] = "正打-平视1",
			["H1"] = 0.7,
			["V1"] = 0.34,
			["H2"] = 0.3,
			["V2"] = 0.34,
			["HFov"] = 40,
			["theta"] = -35,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[108] = {
			["AutoCameraID"] = 108,
			["AutoCameraCutDesc"] = "正打-平视2",
			["H1"] = 0.7,
			["V1"] = 0.34,
			["H2"] = 0.3,
			["V2"] = 0.34,
			["HFov"] = 30,
			["theta"] = -50,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[109] = {
			["AutoCameraID"] = 109,
			["AutoCameraCutDesc"] = "反打-越肩视角",
			["H1"] = 0.3,
			["V1"] = 0.43,
			["H2"] = 0.87,
			["V2"] = 0.4,
			["HFov"] = 30,
			["theta"] = 18,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140749824'),
			["Weight"] = 7,
		},
		[110] = {
			["AutoCameraID"] = 110,
			["AutoCameraCutDesc"] = "反打-背视视角",
			["H1"] = 0.37,
			["V1"] = 0.34,
			["H2"] = 0.8,
			["V2"] = 0.35,
			["HFov"] = 25,
			["theta"] = 20,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140749824'),
			["Weight"] = 7,
		},
		[111] = {
			["AutoCameraID"] = 111,
			["AutoCameraCutDesc"] = "反打-腰部视角",
			["H1"] = 0.32,
			["V1"] = 0.4,
			["H2"] = 1.22,
			["V2"] = -0.15,
			["HFov"] = 15,
			["theta"] = 24,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140750592'),
			["Weight"] = 3,
		},
		[112] = {
			["AutoCameraID"] = 112,
			["AutoCameraCutDesc"] = "反打-同向视角1",
			["H1"] = 0.35,
			["V1"] = 0.38,
			["H2"] = 0.74,
			["V2"] = 0.35,
			["HFov"] = 40,
			["theta"] = 30,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[113] = {
			["AutoCameraID"] = 113,
			["AutoCameraCutDesc"] = "反打-同向视角2",
			["H1"] = 0.28,
			["V1"] = 0.3,
			["H2"] = 0.7,
			["V2"] = 0.35,
			["HFov"] = 40,
			["theta"] = 45,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[114] = {
			["AutoCameraID"] = 114,
			["AutoCameraCutDesc"] = "反打-平视1",
			["H1"] = 0.3,
			["V1"] = 0.34,
			["H2"] = 0.7,
			["V2"] = 0.34,
			["HFov"] = 40,
			["theta"] = 35,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
		[115] = {
			["AutoCameraID"] = 115,
			["AutoCameraCutDesc"] = "反打-平视2",
			["H1"] = 0.3,
			["V1"] = 0.34,
			["H2"] = 0.7,
			["V2"] = 0.34,
			["HFov"] = 30,
			["theta"] = 50,
			["Tag"] = Game.TableDataManager:GetLangStr('str_13609140751360'),
			["Weight"] = 4,
		},
	},
}

return TopData
