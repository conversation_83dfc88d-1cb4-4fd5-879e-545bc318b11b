--
-- 表名: $RolePlay_扮演.xlsx  页名：$DiceSkill_检定分类
--

local TopData = {
	data = {
		[7320101] = {
			["ID"] = 7320101,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891058176'),
			["AdditionProp"] = 7320001,
		},
		[7320102] = {
			["ID"] = 7320102,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891058432'),
			["AdditionProp"] = 7320001,
		},
		[7320103] = {
			["ID"] = 7320103,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891058688'),
			["AdditionProp"] = 7320001,
		},
		[7320104] = {
			["ID"] = 7320104,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891058944'),
			["AdditionProp"] = 7320002,
		},
		[7320105] = {
			["ID"] = 7320105,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891059200'),
			["AdditionProp"] = 7320002,
		},
		[7320106] = {
			["ID"] = 7320106,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891059456'),
			["AdditionProp"] = 7320002,
		},
		[7320107] = {
			["ID"] = 7320107,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891059712'),
			["AdditionProp"] = 7320003,
		},
		[7320108] = {
			["ID"] = 7320108,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891059968'),
			["AdditionProp"] = 7320003,
		},
		[7320109] = {
			["ID"] = 7320109,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891060224'),
			["AdditionProp"] = 7320003,
		},
		[7320110] = {
			["ID"] = 7320110,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891060480'),
			["AdditionProp"] = 7320004,
		},
		[7320111] = {
			["ID"] = 7320111,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891060736'),
			["AdditionProp"] = 7320004,
		},
		[7320112] = {
			["ID"] = 7320112,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891060992'),
			["AdditionProp"] = 7320004,
		},
		[7320113] = {
			["ID"] = 7320113,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891061248'),
			["AdditionProp"] = 7320005,
		},
		[7320114] = {
			["ID"] = 7320114,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_2612682301696'),
			["AdditionProp"] = 7320005,
		},
		[7320115] = {
			["ID"] = 7320115,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891061760'),
			["AdditionProp"] = 7320005,
		},
		[7320116] = {
			["ID"] = 7320116,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_44943611528960'),
			["AdditionProp"] = 7320006,
		},
		[7320117] = {
			["ID"] = 7320117,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891062272'),
			["AdditionProp"] = 7320006,
		},
		[7320118] = {
			["ID"] = 7320118,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891062528'),
			["AdditionProp"] = 7320006,
		},
		[7320119] = {
			["ID"] = 7320119,
			["DiceSkillName"] = Game.TableDataManager:GetLangStr('str_46523891062784'),
			["AdditionProp"] = 0,
		},
	},
}

return TopData
