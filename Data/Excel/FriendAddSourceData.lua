--
-- 表名: $Friend_好友.xlsx  页名：$FriendAddSource_添加来源
--

local TopData = {
	data = {
		[1] = {
			["SourceId"] = 1,
			["AddSourceName"] = "DUNGEON",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999612416'),
		},
		[2] = {
			["SourceId"] = 2,
			["AddSourceName"] = "SERVER_RECOMMEND",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_54632789527296'),
		},
		[3] = {
			["SourceId"] = 3,
			["AddSourceName"] = "DECISIVE_ARENA",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999612928'),
		},
		[4] = {
			["SourceId"] = 4,
			["AddSourceName"] = "CHAT",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999613184'),
		},
		[5] = {
			["SourceId"] = 5,
			["AddSourceName"] = "TEAM",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999613440'),
		},
		[6] = {
			["SourceId"] = 6,
			["AddSourceName"] = "TEAM_GROUP",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999613696'),
		},
		[7] = {
			["SourceId"] = 7,
			["AddSourceName"] = "GUILD",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999613952'),
		},
		[8] = {
			["SourceId"] = 8,
			["AddSourceName"] = "WORLD",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999614208'),
		},
		[9] = {
			["SourceId"] = 9,
			["AddSourceName"] = "TEAM_PLATFORM",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999614464'),
		},
		[10] = {
			["SourceId"] = 10,
			["AddSourceName"] = "DATABASE_SYNC",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999614720'),
		},
		[11] = {
			["SourceId"] = 11,
			["AddSourceName"] = "PVPGAME",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999614976'),
		},
		[12] = {
			["SourceId"] = 12,
			["AddSourceName"] = "MOMENTS",
			["AddSourceDes"] = Game.TableDataManager:GetLangStr('str_25495999615232'),
		},
	},
}

return TopData
