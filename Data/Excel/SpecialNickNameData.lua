--
-- 表名: NEW Inputs: (Nickname_昵称表.xlsx, SpecialNickNameData); Outputs: SpecialNickNameData; Def:data_post_export_SpecialNickNameData.lua
--

local TopData = {
    SpecialNickNameMap = {{5000001, 5000002, 5000005, 5000003, 5000004}, {6000003, 6000001, 6000002}, 
    },
    data = {
        [5000001] = {
            ['Nickname'] = '诡秘之主', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639679488'),
            ['SNicknameID'] = 5000001, 
            ['Type'] = 1, 
        },
        [5000002] = {
            ['Nickname'] = '黄黑之王', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639679488'),
            ['SNicknameID'] = 5000002, 
            ['Type'] = 1, 
        },
        [5000003] = {
            ['Nickname'] = '黑夜女神', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639679488'),
            ['SNicknameID'] = 5000003, 
            ['Type'] = 1, 
        },
        [5000004] = {
            ['Nickname'] = '远古太阳神', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639679488'),
            ['SNicknameID'] = 5000004, 
            ['Type'] = 1, 
        },
        [5000005] = {
            ['Nickname'] = '克莱恩', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639679488'),
            ['SNicknameID'] = 5000005, 
            ['Type'] = 1, 
        },
        [6000001] = {
            ['Nickname'] = '周明瑞', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639680768'),
            ['SNicknameID'] = 6000001, 
            ['Type'] = 2, 
        },
        [6000002] = {
            ['Nickname'] = '黄涛', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639680768'),
            ['SNicknameID'] = 6000002, 
            ['Type'] = 2, 
        },
        [6000003] = {
            ['Nickname'] = '克莱恩', 
            ['Reminder'] = Game.TableDataManager:GetLangStr('str_38209639680768'),
            ['SNicknameID'] = 6000003, 
            ['Type'] = 2, 
        },
    }
}
return TopData