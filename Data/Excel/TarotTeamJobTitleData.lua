--
-- 表名: $TarotTeam_塔罗小队表.xlsx  页名：$TarotTeamJobTitle_塔罗小队职位名表
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324480'),
			["UIShowType"] = 0,
		},
		[2] = {
			["ID"] = 2,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["UIShowType"] = 1,
		},
		[3] = {
			["ID"] = 3,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["UIShowType"] = 1,
		},
		[4] = {
			["ID"] = 4,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["UIShowType"] = 1,
		},
		[5] = {
			["ID"] = 5,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["UIShowType"] = 1,
		},
		[6] = {
			["ID"] = 6,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_56075898324736'),
			["UIShowType"] = 1,
		},
		[7] = {
			["ID"] = 7,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
		[8] = {
			["ID"] = 8,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
		[9] = {
			["ID"] = 9,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
		[10] = {
			["ID"] = 10,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
		[11] = {
			["ID"] = 11,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
		[12] = {
			["ID"] = 12,
			["JobTitle"] = Game.TableDataManager:GetLangStr('str_38071663863040'),
			["UIShowType"] = 2,
		},
	},
}

return TopData
