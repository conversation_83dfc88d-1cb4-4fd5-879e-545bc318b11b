--
-- 表名: $FightPropAndSettings_战斗属性及设定.xlsx  页名：$FightPropModeSet_属性集合
--

local TopData = {
	data = {
		[1016001] = {
			["ID"] = 1016001,
			["Prop"] = "AllEleAtk_N",
			["PropSet"] = {1014001, 1014101, 1014201, 1014301, 1014401, 1014501, 1014601, 1014701, 1014801},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329617664'),
		},
		[1016002] = {
			["ID"] = 1016002,
			["Prop"] = "AllEleAtk_P",
			["PropSet"] = {1014002, 1014102, 1014202, 1014302, 1014402, 1014502, 1014602, 1014702, 1014802},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329617664'),
		},
		[1016011] = {
			["ID"] = 1016011,
			["Prop"] = "AllEleHurtMulti_N",
			["PropSet"] = {1014011, 1014111, 1014211, 1014311, 1014411, 1014511, 1014611, 1014711, 1014811},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329618176'),
		},
		[1016021] = {
			["ID"] = 1016021,
			["Prop"] = "AllEleHurtReduce_N",
			["PropSet"] = {1014021, 1014121, 1014221, 1014321, 1014421, 1014521, 1014621, 1014721, 1014821},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329618432'),
		},
		[1016031] = {
			["ID"] = 1016031,
			["Prop"] = "AllControlAnti_N",
			["PropSet"] = {1015001, 1015101, 1015201, 1015301, 1015401, 1015501, 1015601, 1015701, 1015801},
			["Discription"] = Game.TableDataManager:GetLangStr('str_19036368823808'),
		},
		[1016032] = {
			["ID"] = 1016032,
			["Prop"] = "AllControlAnti_P",
			["PropSet"] = {1015002, 1015102, 1015202, 1015302, 1015402, 1015502, 1015602, 1015702, 1015802},
			["Discription"] = Game.TableDataManager:GetLangStr('str_19036368823808'),
		},
		[1016041] = {
			["ID"] = 1016041,
			["Prop"] = "EnhanceAllControl_N",
			["PropSet"] = {1015011, 1015111, 1015211, 1015311, 1015411, 1015511, 1015611, 1015711, 1015811},
			["Discription"] = Game.TableDataManager:GetLangStr('str_19242258810368'),
		},
		[1016042] = {
			["ID"] = 1016042,
			["Prop"] = "EnhanceAllControl_P",
			["PropSet"] = {1015012, 1015112, 1015212, 1015312, 1015412, 1015512, 1015612, 1015712, 1015812},
			["Discription"] = Game.TableDataManager:GetLangStr('str_19242258810368'),
		},
		[1016051] = {
			["ID"] = 1016051,
			["Prop"] = "AllControlHitRate_N",
			["PropSet"] = {1015021, 1015121, 1015221, 1015321, 1015421, 1015521, 1015621, 1015721, 1015821},
			["Discription"] = Game.TableDataManager:GetLangStr('str_19036368823552'),
		},
		[1016061] = {
			["ID"] = 1016061,
			["Prop"] = "AllEleAnti_N",
			["PropSet"] = {1014031, 1014131, 1014231, 1014331, 1014431, 1014531, 1014631, 1014731, 1014831},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329619968'),
		},
		[1016062] = {
			["ID"] = 1016062,
			["Prop"] = "AllEleAnti_P",
			["PropSet"] = {1014032, 1014132, 1014232, 1014332, 1014432, 1014532, 1014632, 1014732, 1014832},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329619968'),
		},
		[1016071] = {
			["ID"] = 1016071,
			["Prop"] = "IgnoreAllEle_N",
			["PropSet"] = {1014041, 1014141, 1014241, 1014341, 1014441, 1014541, 1014641, 1014741, 1014841},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329620480'),
		},
		[1016072] = {
			["ID"] = 1016072,
			["Prop"] = "IgnoreAllEle_P",
			["PropSet"] = {1014042, 1014142, 1014242, 1014342, 1014442, 1014542, 1014642, 1014742, 1014842},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24190329620480'),
		},
		[1016081] = {
			["ID"] = 1016081,
			["Prop"] = "Def_N",
			["PropSet"] = {1012041, 1013041},
			["Discription"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
		},
		[1016082] = {
			["ID"] = 1016082,
			["Prop"] = "Def_P",
			["PropSet"] = {1012042, 1013042},
			["Discription"] = Game.TableDataManager:GetLangStr('str_54632789318144'),
		},
		[1016091] = {
			["ID"] = 1016091,
			["Prop"] = "Block_N",
			["PropSet"] = {1012151, 1013151},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210374144'),
		},
		[1016092] = {
			["ID"] = 1016092,
			["Prop"] = "Block_P",
			["PropSet"] = {1012152, 1013152},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210374144'),
		},
		[1016101] = {
			["ID"] = 1016101,
			["Prop"] = "Atk_N",
			["PropSet"] = {1012001, 1012011, 1013001, 1013011},
			["Discription"] = Game.TableDataManager:GetLangStr('str_54632789317888'),
		},
		[1016102] = {
			["ID"] = 1016102,
			["Prop"] = "Atk_P",
			["PropSet"] = {1012002, 1012012, 1013002, 1013012},
			["Discription"] = Game.TableDataManager:GetLangStr('str_54632789317888'),
		},
		[1016121] = {
			["ID"] = 1016121,
			["Prop"] = "Pierce_N",
			["PropSet"] = {1012141, 1013141},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210373888'),
		},
		[1016122] = {
			["ID"] = 1016122,
			["Prop"] = "Pierce_P",
			["PropSet"] = {1012142, 1013142},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210373888'),
		},
		[1016131] = {
			["ID"] = 1016131,
			["Prop"] = "AllProHurtMulti_N",
			["PropSet"] = {1019001, 1019011, 1019021, 1019031, 1019041, 1019051},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890641920'),
		},
		[1016132] = {
			["ID"] = 1016132,
			["Prop"] = "AllProHurtMulti_P",
			["PropSet"] = {1019002, 1019012, 1019022, 1019032, 1019042, 1019052},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890641920'),
		},
		[1016141] = {
			["ID"] = 1016141,
			["Prop"] = "AllProHurtReduce_N",
			["PropSet"] = {1019201, 1019211, 1019221, 1019231, 1019241, 1019251},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642176'),
		},
		[1016142] = {
			["ID"] = 1016142,
			["Prop"] = "AllProHurtReduce_P",
			["PropSet"] = {1019202, 1019212, 1019222, 1019232, 1019242, 1019252},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642176'),
		},
		[1016151] = {
			["ID"] = 1016151,
			["Prop"] = "AllRaceHurtMulti_N",
			["PropSet"] = {1018001, 1018011, 1018021, 1018031},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642432'),
		},
		[1016152] = {
			["ID"] = 1016152,
			["Prop"] = "AllRaceHurtMulti_P",
			["PropSet"] = {1018002, 1018012, 1018022, 1018032},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642432'),
		},
		[1016161] = {
			["ID"] = 1016161,
			["Prop"] = "AllRaceHurtReduce_N",
			["PropSet"] = {1018101, 1018111, 1018121, 1018131},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642688'),
		},
		[1016162] = {
			["ID"] = 1016162,
			["Prop"] = "AllRaceHurtReduce_P",
			["PropSet"] = {1018102, 1018112, 1018122, 1018132},
			["Discription"] = Game.TableDataManager:GetLangStr('str_24052890642688'),
		},
		[1016171] = {
			["ID"] = 1016171,
			["Prop"] = "CritAnti_N",
			["PropSet"] = {1012091, 1013091},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210376960'),
		},
		[1016181] = {
			["ID"] = 1016181,
			["Prop"] = "CritDef_N",
			["PropSet"] = {1012111, 1013111},
			["Discription"] = Game.TableDataManager:GetLangStr('str_18830210377984'),
		},
	},
}

return TopData
