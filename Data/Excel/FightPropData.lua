--
-- 表名: FightPropData(后处理)
--

local TopData = {
    primaryPropSet = {'Int_N', 'Pow_N', 'Str_N', 'Dex_N', 'Con_N'
    },
    propRangeMap = {
        ['AbundanceAnti'] = {{0, true}, {99999999, true}, }, 
        ['AbundanceAtk'] = {{0, true}, {99999999, true}, }, 
        ['AbundanceHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['AbundanceHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['AbundanceLevel'] = {{0, true}, {10, true}, }, 
        ['AbundanceRate'] = {{0, true}, {1, true}, }, 
        ['AdditionalAnti'] = {{0, true}, {99999999, true}, }, 
        ['AdditionalAtk'] = {{0, true}, {99999999, true}, }, 
        ['AdditionalIgnore'] = {{0, true}, {99999999, true}, }, 
        ['AggroPercent'] = {{0, true}, {99999999, true}, }, 
        ['AirShield'] = {{0, true}, {99999999, true}, }, 
        ['AirborneAnti'] = {{0, true}, {99999999, true}, }, 
        ['AirborneHitRate'] = {{0, true}, {99999999, true}, }, 
        ['AllControlAnti'] = {{0, true}, {99999999, true}, }, 
        ['AllControlHitRate'] = {{0, true}, {99999999, true}, }, 
        ['AllEleAnti'] = {{0, true}, {99999999, true}, }, 
        ['AllEleAtk'] = {{0, true}, {99999999, true}, }, 
        ['AllEleHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['AllEleHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['AllProHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['AllProHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['AllRaceHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['AllRaceHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['ApprenticeHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['ApprenticeHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['ArbiterHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['ArbiterHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['Atk'] = {{0, true}, {99999999, true}, }, 
        ['AtkRange'] = {{0, true}, {99999999, true}, }, 
        ['Block'] = {{0, true}, {99999999, true}, }, 
        ['CalamityAnti'] = {{0, true}, {99999999, true}, }, 
        ['CalamityAtk'] = {{0, true}, {99999999, true}, }, 
        ['CalamityHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['CalamityHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['CalamityLevel'] = {{0, true}, {10, true}, }, 
        ['CalamityRate'] = {{0, true}, {1, true}, }, 
        ['ChaosAnti'] = {{0, true}, {99999999, true}, }, 
        ['ChaosAtk'] = {{0, true}, {99999999, true}, }, 
        ['ChaosHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['ChaosHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['ChaosLevel'] = {{0, true}, {10, true}, }, 
        ['ChaosRate'] = {{0, true}, {1, true}, }, 
        ['Con'] = {{0, true}, {99999999, true}, }, 
        ['CritAbundanceLevel'] = {{0, true}, {10, true}, }, 
        ['CritAnti'] = {{0, true}, {99999999, true}, }, 
        ['CritCalamityLevel'] = {{0, true}, {10, true}, }, 
        ['CritChaosLevel'] = {{0, true}, {10, true}, }, 
        ['CritDarknessLevel'] = {{0, true}, {10, true}, }, 
        ['CritDarknessSteal'] = {{0, true}, {99999999, true}, }, 
        ['CritDef'] = {{0, true}, {99999999, true}, }, 
        ['CritDisorderLevel'] = {{0, true}, {10, true}, }, 
        ['CritFateAtkMax'] = {{0, true}, {99999999, true}, }, 
        ['CritFateAtkMin'] = {{0, true}, {99999999, true}, }, 
        ['CritFateLevel'] = {{0, true}, {10, true}, }, 
        ['CritKnowledgeLevel'] = {{0, true}, {10, true}, }, 
        ['CritMysteryLevel'] = {{0, true}, {10, true}, }, 
        ['CritTenebrousIgnoreDef'] = {{0, true}, {99999999, true}, }, 
        ['CritTenebrousLevel'] = {{0, true}, {10, true}, }, 
        ['DarknessAnti'] = {{0, true}, {99999999, true}, }, 
        ['DarknessAtk'] = {{0, true}, {99999999, true}, }, 
        ['DarknessHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['DarknessHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['DarknessLevel'] = {{0, true}, {10, true}, }, 
        ['DarknessRate'] = {{0, true}, {1, true}, }, 
        ['DarknessSteal'] = {{0, true}, {99999999, true}, }, 
        ['Def'] = {{0, true}, {99999999, true}, }, 
        ['DeltaBeHealed'] = {{-999, true}, {9999, true}, }, 
        ['DeltaBeHurted'] = {{-9999, true}, {9999, true}, }, 
        ['DeltaHeal'] = {{-999, true}, {999, true}, }, 
        ['DeltaHurt'] = {{-9999, true}, {9999, true}, }, 
        ['Dex'] = {{0, true}, {99999999, true}, }, 
        ['DisorderAnti'] = {{0, true}, {99999999, true}, }, 
        ['DisorderAtk'] = {{0, true}, {99999999, true}, }, 
        ['DisorderHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['DisorderHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['DisorderLevel'] = {{0, true}, {10, true}, }, 
        ['DisorderRate'] = {{0, true}, {1, true}, }, 
        ['DizzyAnti'] = {{0, true}, {99999999, true}, }, 
        ['DizzyHitRate'] = {{0, true}, {99999999, true}, }, 
        ['DownAnti'] = {{0, true}, {99999999, true}, }, 
        ['DownHitRate'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceAirborne'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceAllControl'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceDizzy'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceDown'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceFear'] = {{0, true}, {99999999, true}, }, 
        ['EnhancePull'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceSilence'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceSleep'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceSlow'] = {{0, true}, {99999999, true}, }, 
        ['EnhanceTied'] = {{0, true}, {99999999, true}, }, 
        ['FateAnti'] = {{0, true}, {99999999, true}, }, 
        ['FateAtk'] = {{0, true}, {99999999, true}, }, 
        ['FateHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['FateHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['FateLevel'] = {{0, true}, {10, true}, }, 
        ['FateRate'] = {{0, true}, {1, true}, }, 
        ['FearAnti'] = {{0, true}, {99999999, true}, }, 
        ['FearHitRate'] = {{0, true}, {99999999, true}, }, 
        ['FeatherwitHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['FeatherwitHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['HpReg'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreAbundanceAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreAllEle'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreCalamityAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreChaosAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreDarknessAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreDisorderAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreFateAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreKnowledgeAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreMysteryAnti'] = {{0, true}, {99999999, true}, }, 
        ['IgnoreTenebrousAnti'] = {{0, true}, {99999999, true}, }, 
        ['Int'] = {{0, true}, {99999999, true}, }, 
        ['KnowledgeAnti'] = {{0, true}, {99999999, true}, }, 
        ['KnowledgeAtk'] = {{0, true}, {99999999, true}, }, 
        ['KnowledgeHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['KnowledgeHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['KnowledgeLevel'] = {{0, true}, {10, true}, }, 
        ['KnowledgeRate'] = {{0, true}, {1, true}, }, 
        ['LockedMaxHp'] = {{0, true}, {99999999, true}, }, 
        ['MaxHp'] = {{1, true}, {9999999999, true}, }, 
        ['MaxStaminaValue'] = {{1, true}, {200, true}, }, 
        ['MysteryAnti'] = {{0, true}, {99999999, true}, }, 
        ['MysteryAtk'] = {{0, true}, {99999999, true}, }, 
        ['MysteryHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['MysteryHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['MysteryLevel'] = {{0, true}, {10, true}, }, 
        ['MysteryRate'] = {{0, true}, {1, true}, }, 
        ['Pierce'] = {{0, true}, {99999999, true}, }, 
        ['Pow'] = {{0, true}, {99999999, true}, }, 
        ['ProHurtMulti9'] = {{0, true}, {99999999, true}, }, 
        ['ProHurtReduce9'] = {{0, true}, {99999999, true}, }, 
        ['PullAnti'] = {{0, true}, {99999999, true}, }, 
        ['PullHitRate'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtMulti1'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtMulti2'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtMulti3'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtMulti4'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtMulti5'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtReduce1'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtReduce2'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtReduce3'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtReduce4'] = {{0, true}, {99999999, true}, }, 
        ['RaceHurtReduce5'] = {{0, true}, {99999999, true}, }, 
        ['ShieldBreak'] = {{0, true}, {99999999, true}, }, 
        ['SilenceAnti'] = {{0, true}, {99999999, true}, }, 
        ['SilenceHitRate'] = {{0, true}, {99999999, true}, }, 
        ['SkillAnti'] = {{0, true}, {99999999, true}, }, 
        ['SkillEnhance'] = {{0, true}, {99999999, true}, }, 
        ['SleepAnti'] = {{0, true}, {99999999, true}, }, 
        ['SleepHitRate'] = {{0, true}, {99999999, true}, }, 
        ['SlowAnti'] = {{0, true}, {99999999, true}, }, 
        ['SlowHitRate'] = {{0, true}, {99999999, true}, }, 
        ['Speed'] = {{0, true}, {99999999, true}, }, 
        ['Str'] = {{0, true}, {99999999, true}, }, 
        ['SunHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['SunHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['TenebrousAnti'] = {{0, true}, {99999999, true}, }, 
        ['TenebrousAtk'] = {{0, true}, {99999999, true}, }, 
        ['TenebrousHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['TenebrousHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['TenebrousIgnoreDef'] = {{0, true}, {99999999, true}, }, 
        ['TenebrousLevel'] = {{0, true}, {10, true}, }, 
        ['TenebrousRate'] = {{0, true}, {1, true}, }, 
        ['TiedAnti'] = {{0, true}, {99999999, true}, }, 
        ['TiedHitRate'] = {{0, true}, {99999999, true}, }, 
        ['UltimatePointSpeed'] = {{0, true}, {99999999, true}, }, 
        ['VisionaryHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['VisionaryHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['WarriorHurtMulti'] = {{0, true}, {99999999, true}, }, 
        ['WarriorHurtReduce'] = {{0, true}, {99999999, true}, }, 
        ['mAtkMax'] = {{0, true}, {99999999, true}, }, 
        ['mAtkMin'] = {{0, true}, {'mAtkMax', false}, }, 
        ['mAtkSpd'] = {{0, true}, {99999999, true}, }, 
        ['mBlock'] = {{0, true}, {99999999, true}, }, 
        ['mCrit'] = {{0, true}, {99999999, true}, }, 
        ['mCritAnti'] = {{0, true}, {99999999, true}, }, 
        ['mCritDef'] = {{0, true}, {99999999, true}, }, 
        ['mCritHurt'] = {{0, true}, {99999999, true}, }, 
        ['mDef'] = {{0, true}, {99999999, true}, }, 
        ['mHurtMulti'] = {{-1, true}, {99999999, true}, }, 
        ['mHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['mIgnoreDef'] = {{0, true}, {99999999, true}, }, 
        ['mPierce'] = {{0, true}, {99999999, true}, }, 
        ['pAtkMax'] = {{0, true}, {99999999, true}, }, 
        ['pAtkMin'] = {{0, true}, {'pAtkMax', false}, }, 
        ['pAtkSpd'] = {{0, true}, {99999999, true}, }, 
        ['pBlock'] = {{0, true}, {99999999, true}, }, 
        ['pCrit'] = {{0, true}, {99999999, true}, }, 
        ['pCritAnti'] = {{0, true}, {99999999, true}, }, 
        ['pCritDef'] = {{0, true}, {99999999, true}, }, 
        ['pCritHurt'] = {{0, true}, {99999999, true}, }, 
        ['pDef'] = {{0, true}, {99999999, true}, }, 
        ['pHurtMulti'] = {{-1, true}, {99999999, true}, }, 
        ['pHurtReduce'] = {{-1, true}, {99999999, true}, }, 
        ['pIgnoreDef'] = {{0, true}, {99999999, true}, }, 
        ['pPierce'] = {{0, true}, {99999999, true}, }, 
    },
    rangePropChgEffectMap = {
        ['mAtkMax'] = {'mAtkMin', true}, 
        ['pAtkMax'] = {'pAtkMin', true}, 
    },
    regPropMap = {
        ['Hp'] = 'HpReg', 
    },
    data = {
        [1010000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329554432'),
            ['ID'] = 1010000, 
            ['IsPrimary'] = true, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Con', 
            ['ShowType'] = 0, 
        },
        [1010010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329554944'),
            ['ID'] = 1010010, 
            ['IsPrimary'] = true, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Pow', 
            ['ShowType'] = 0, 
        },
        [1010020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_46043391592192'),
            ['ID'] = 1010020, 
            ['IsPrimary'] = true, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Str', 
            ['ShowType'] = 0, 
        },
        [1010030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329555968'),
            ['ID'] = 1010030, 
            ['IsPrimary'] = true, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Int', 
            ['ShowType'] = 0, 
        },
        [1010040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329556480'),
            ['ID'] = 1010040, 
            ['IsPrimary'] = true, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Dex', 
            ['ShowType'] = 0, 
        },
        [1011000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329556992'),
            ['ID'] = 1011000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '9999999999', 
            ['MinValue'] = '1', 
            ['Prop'] = 'MaxHp', 
            ['ShowType'] = 0, 
        },
        [1011010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329557760'),
            ['ID'] = 1011010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'HpReg', 
            ['ShowType'] = 2, 
        },
        [1011040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329558272'),
            ['ID'] = 1011040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'LockedMaxHp', 
            ['ShowType'] = 0, 
        },
        [1011060] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329558784'),
            ['ID'] = 1011060, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '200', 
            ['MinValue'] = '1', 
            ['Prop'] = 'MaxStaminaValue', 
            ['ShowType'] = 0, 
        },
        [1011070] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329559552'),
            ['ID'] = 1011070, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AtkRange', 
            ['ShowType'] = 2, 
        },
        [1011080] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329560064'),
            ['ID'] = 1011080, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '999', 
            ['MinValue'] = '-999', 
            ['Prop'] = 'DeltaHeal', 
            ['ShowType'] = 1, 
        },
        [1011090] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329560576'),
            ['ID'] = 1011090, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '9999', 
            ['MinValue'] = '-999', 
            ['Prop'] = 'DeltaBeHealed', 
            ['ShowType'] = 1, 
        },
        [1011100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329561088'),
            ['ID'] = 1011100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '0', 
            ['Prop'] = 'Speed', 
            ['ShowType'] = 0, 
        },
        [1011230] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329561600'),
            ['ID'] = 1011230, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '0', 
            ['Prop'] = 'AggroPercent', 
            ['ShowType'] = 0, 
        },
        [1011240] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329561856'),
            ['ID'] = 1011240, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '9999', 
            ['MinValue'] = '-9999', 
            ['Prop'] = 'DeltaHurt', 
            ['ShowType'] = 0, 
        },
        [1011250] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329562112'),
            ['ID'] = 1011250, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '9999', 
            ['MinValue'] = '-9999', 
            ['Prop'] = 'DeltaBeHurted', 
            ['ShowType'] = 0, 
        },
        [1011320] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329562368'),
            ['ID'] = 1011320, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DarknessSteal', 
            ['ShowType'] = 0, 
        },
        [1011330] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329562880'),
            ['ID'] = 1011330, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritDarknessSteal', 
            ['ShowType'] = 0, 
        },
        [1011340] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329563392'),
            ['ID'] = 1011340, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TenebrousIgnoreDef', 
            ['ShowType'] = 0, 
        },
        [1011350] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329563904'),
            ['ID'] = 1011350, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritTenebrousIgnoreDef', 
            ['ShowType'] = 0, 
        },
        [1011360] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329564416'),
            ['ID'] = 1011360, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritFateAtkMin', 
            ['ShowType'] = 0, 
        },
        [1011370] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329564928'),
            ['ID'] = 1011370, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritFateAtkMax', 
            ['ShowType'] = 0, 
        },
        [1011380] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329565440'),
            ['ID'] = 1011380, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '0', 
            ['Prop'] = 'UltimatePointSpeed', 
            ['ShowType'] = 2, 
        },
        [1012000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368802816'),
            ['ID'] = 1012000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = 'pAtkMax', 
            ['MinValue'] = '', 
            ['Prop'] = 'pAtkMin', 
            ['ShowType'] = 0, 
        },
        [1012010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368801792'),
            ['ID'] = 1012010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pAtkMax', 
            ['ShowType'] = 0, 
        },
        [1012030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329567488'),
            ['ID'] = 1012030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pAtkSpd', 
            ['ShowType'] = 2, 
        },
        [1012040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_54632789314816'),
            ['ID'] = 1012040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pDef', 
            ['ShowType'] = 1, 
        },
        [1012050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210380288'),
            ['ID'] = 1012050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pIgnoreDef', 
            ['ShowType'] = 0, 
        },
        [1012080] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_17319992498178'),
            ['ID'] = 1012080, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pCrit', 
            ['ShowType'] = 0, 
        },
        [1012090] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368807936'),
            ['ID'] = 1012090, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pCritAnti', 
            ['ShowType'] = 0, 
        },
        [1012100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210377216'),
            ['ID'] = 1012100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pCritHurt', 
            ['ShowType'] = 1, 
        },
        [1012110] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368808960'),
            ['ID'] = 1012110, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pCritDef', 
            ['ShowType'] = 1, 
        },
        [1012120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329571328'),
            ['ID'] = 1012120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'pHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1012130] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329571840'),
            ['ID'] = 1012130, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'pHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1012140] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_17319992493570'),
            ['ID'] = 1012140, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pPierce', 
            ['ShowType'] = 0, 
        },
        [1012150] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329572864'),
            ['ID'] = 1012150, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'pBlock', 
            ['ShowType'] = 0, 
        },
        [1013000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368803072'),
            ['ID'] = 1013000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = 'mAtkMax', 
            ['MinValue'] = '', 
            ['Prop'] = 'mAtkMin', 
            ['ShowType'] = 0, 
        },
        [1013010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368802048'),
            ['ID'] = 1013010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mAtkMax', 
            ['ShowType'] = 0, 
        },
        [1013030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329574912'),
            ['ID'] = 1013030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mAtkSpd', 
            ['ShowType'] = 2, 
        },
        [1013040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_54632789315072'),
            ['ID'] = 1013040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mDef', 
            ['ShowType'] = 1, 
        },
        [1013050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210380544'),
            ['ID'] = 1013050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mIgnoreDef', 
            ['ShowType'] = 0, 
        },
        [1013080] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_17319992498434'),
            ['ID'] = 1013080, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mCrit', 
            ['ShowType'] = 0, 
        },
        [1013090] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368808192'),
            ['ID'] = 1013090, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mCritAnti', 
            ['ShowType'] = 0, 
        },
        [1013100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210377472'),
            ['ID'] = 1013100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mCritHurt', 
            ['ShowType'] = 1, 
        },
        [1013110] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368809216'),
            ['ID'] = 1013110, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mCritDef', 
            ['ShowType'] = 1, 
        },
        [1013120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329578752'),
            ['ID'] = 1013120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'mHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1013130] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329579264'),
            ['ID'] = 1013130, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'mHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1013140] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_17319992494082'),
            ['ID'] = 1013140, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mPierce', 
            ['ShowType'] = 0, 
        },
        [1013150] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329580288'),
            ['ID'] = 1013150, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'mBlock', 
            ['ShowType'] = 0, 
        },
        [1014000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210370816'),
            ['ID'] = 1014000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ChaosAtk', 
            ['ShowType'] = 0, 
        },
        [1014010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329581312'),
            ['ID'] = 1014010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ChaosHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329581568'),
            ['ID'] = 1014020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'ChaosHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329581824'),
            ['ID'] = 1014030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ChaosAnti', 
            ['ShowType'] = 0, 
        },
        [1014040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329582336'),
            ['ID'] = 1014040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreChaosAnti', 
            ['ShowType'] = 0, 
        },
        [1014050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329582848'),
            ['ID'] = 1014050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'ChaosRate', 
            ['ShowType'] = 1, 
        },
        [1014060] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329583104'),
            ['ID'] = 1014060, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'ChaosLevel', 
            ['ShowType'] = 0, 
        },
        [1014070] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329583360'),
            ['ID'] = 1014070, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritChaosLevel', 
            ['ShowType'] = 0, 
        },
        [1014100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210371072'),
            ['ID'] = 1014100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'MysteryAtk', 
            ['ShowType'] = 0, 
        },
        [1014110] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329584128'),
            ['ID'] = 1014110, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'MysteryHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329584384'),
            ['ID'] = 1014120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'MysteryHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014130] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329584640'),
            ['ID'] = 1014130, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'MysteryAnti', 
            ['ShowType'] = 0, 
        },
        [1014140] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329585152'),
            ['ID'] = 1014140, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreMysteryAnti', 
            ['ShowType'] = 0, 
        },
        [1014150] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329585664'),
            ['ID'] = 1014150, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'MysteryRate', 
            ['ShowType'] = 1, 
        },
        [1014160] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329585920'),
            ['ID'] = 1014160, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'MysteryLevel', 
            ['ShowType'] = 0, 
        },
        [1014170] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329586176'),
            ['ID'] = 1014170, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritMysteryLevel', 
            ['ShowType'] = 0, 
        },
        [1014200] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210371328'),
            ['ID'] = 1014200, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AbundanceAtk', 
            ['ShowType'] = 0, 
        },
        [1014210] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329586944'),
            ['ID'] = 1014210, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AbundanceHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014220] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329587200'),
            ['ID'] = 1014220, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'AbundanceHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014230] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329587456'),
            ['ID'] = 1014230, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AbundanceAnti', 
            ['ShowType'] = 0, 
        },
        [1014240] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329587968'),
            ['ID'] = 1014240, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreAbundanceAnti', 
            ['ShowType'] = 0, 
        },
        [1014250] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329588480'),
            ['ID'] = 1014250, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'AbundanceRate', 
            ['ShowType'] = 1, 
        },
        [1014260] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329588736'),
            ['ID'] = 1014260, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'AbundanceLevel', 
            ['ShowType'] = 0, 
        },
        [1014270] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329588992'),
            ['ID'] = 1014270, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritAbundanceLevel', 
            ['ShowType'] = 0, 
        },
        [1014300] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210371584'),
            ['ID'] = 1014300, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DarknessAtk', 
            ['ShowType'] = 0, 
        },
        [1014310] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329589760'),
            ['ID'] = 1014310, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DarknessHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014320] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329590016'),
            ['ID'] = 1014320, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'DarknessHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014330] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329590272'),
            ['ID'] = 1014330, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DarknessAnti', 
            ['ShowType'] = 0, 
        },
        [1014340] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329590784'),
            ['ID'] = 1014340, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreDarknessAnti', 
            ['ShowType'] = 0, 
        },
        [1014350] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329591296'),
            ['ID'] = 1014350, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'DarknessRate', 
            ['ShowType'] = 1, 
        },
        [1014360] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329591552'),
            ['ID'] = 1014360, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'DarknessLevel', 
            ['ShowType'] = 0, 
        },
        [1014370] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329591808'),
            ['ID'] = 1014370, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritDarknessLevel', 
            ['ShowType'] = 0, 
        },
        [1014400] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210371840'),
            ['ID'] = 1014400, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CalamityAtk', 
            ['ShowType'] = 0, 
        },
        [1014410] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329592576'),
            ['ID'] = 1014410, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CalamityHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014420] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329592832'),
            ['ID'] = 1014420, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'CalamityHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014430] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329593088'),
            ['ID'] = 1014430, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CalamityAnti', 
            ['ShowType'] = 0, 
        },
        [1014440] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329593600'),
            ['ID'] = 1014440, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreCalamityAnti', 
            ['ShowType'] = 0, 
        },
        [1014450] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329594112'),
            ['ID'] = 1014450, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CalamityRate', 
            ['ShowType'] = 1, 
        },
        [1014460] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329594368'),
            ['ID'] = 1014460, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CalamityLevel', 
            ['ShowType'] = 0, 
        },
        [1014470] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329594624'),
            ['ID'] = 1014470, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritCalamityLevel', 
            ['ShowType'] = 0, 
        },
        [1014500] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210372096'),
            ['ID'] = 1014500, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DisorderAtk', 
            ['ShowType'] = 0, 
        },
        [1014510] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329595392'),
            ['ID'] = 1014510, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DisorderHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014520] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329595648'),
            ['ID'] = 1014520, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'DisorderHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014530] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329595904'),
            ['ID'] = 1014530, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DisorderAnti', 
            ['ShowType'] = 0, 
        },
        [1014540] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329596416'),
            ['ID'] = 1014540, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreDisorderAnti', 
            ['ShowType'] = 0, 
        },
        [1014550] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329596928'),
            ['ID'] = 1014550, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'DisorderRate', 
            ['ShowType'] = 1, 
        },
        [1014560] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329597184'),
            ['ID'] = 1014560, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'DisorderLevel', 
            ['ShowType'] = 0, 
        },
        [1014570] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329597440'),
            ['ID'] = 1014570, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritDisorderLevel', 
            ['ShowType'] = 0, 
        },
        [1014600] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210372352'),
            ['ID'] = 1014600, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TenebrousAtk', 
            ['ShowType'] = 0, 
        },
        [1014610] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329598208'),
            ['ID'] = 1014610, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TenebrousHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014620] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329598464'),
            ['ID'] = 1014620, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'TenebrousHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014630] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329598720'),
            ['ID'] = 1014630, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TenebrousAnti', 
            ['ShowType'] = 0, 
        },
        [1014640] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329599232'),
            ['ID'] = 1014640, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreTenebrousAnti', 
            ['ShowType'] = 0, 
        },
        [1014650] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329599744'),
            ['ID'] = 1014650, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'TenebrousRate', 
            ['ShowType'] = 1, 
        },
        [1014660] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329600000'),
            ['ID'] = 1014660, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'TenebrousLevel', 
            ['ShowType'] = 0, 
        },
        [1014670] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329600256'),
            ['ID'] = 1014670, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritTenebrousLevel', 
            ['ShowType'] = 0, 
        },
        [1014700] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210372608'),
            ['ID'] = 1014700, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'KnowledgeAtk', 
            ['ShowType'] = 0, 
        },
        [1014710] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329601024'),
            ['ID'] = 1014710, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'KnowledgeHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014720] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329601280'),
            ['ID'] = 1014720, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'KnowledgeHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014730] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329601536'),
            ['ID'] = 1014730, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'KnowledgeAnti', 
            ['ShowType'] = 0, 
        },
        [1014740] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329602048'),
            ['ID'] = 1014740, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreKnowledgeAnti', 
            ['ShowType'] = 0, 
        },
        [1014750] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329602560'),
            ['ID'] = 1014750, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'KnowledgeRate', 
            ['ShowType'] = 1, 
        },
        [1014760] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329602816'),
            ['ID'] = 1014760, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'KnowledgeLevel', 
            ['ShowType'] = 0, 
        },
        [1014770] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329603072'),
            ['ID'] = 1014770, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritKnowledgeLevel', 
            ['ShowType'] = 0, 
        },
        [1014800] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210372864'),
            ['ID'] = 1014800, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FateAtk', 
            ['ShowType'] = 0, 
        },
        [1014810] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329603840'),
            ['ID'] = 1014810, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FateHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1014820] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329604096'),
            ['ID'] = 1014820, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '-1', 
            ['Prop'] = 'FateHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1014830] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329604352'),
            ['ID'] = 1014830, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FateAnti', 
            ['ShowType'] = 0, 
        },
        [1014840] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329604864'),
            ['ID'] = 1014840, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreFateAnti', 
            ['ShowType'] = 0, 
        },
        [1014850] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329605376'),
            ['ID'] = 1014850, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '1', 
            ['MinValue'] = '0', 
            ['Prop'] = 'FateRate', 
            ['ShowType'] = 1, 
        },
        [1014860] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329605632'),
            ['ID'] = 1014860, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'FateLevel', 
            ['ShowType'] = 0, 
        },
        [1014870] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329605888'),
            ['ID'] = 1014870, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '10', 
            ['MinValue'] = '0', 
            ['Prop'] = 'CritFateLevel', 
            ['ShowType'] = 0, 
        },
        [1015000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329606144'),
            ['ID'] = 1015000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AirborneAnti', 
            ['ShowType'] = 0, 
        },
        [1015010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368821248'),
            ['ID'] = 1015010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceAirborne', 
            ['ShowType'] = 0, 
        },
        [1015020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329607168'),
            ['ID'] = 1015020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AirborneHitRate', 
            ['ShowType'] = 1, 
        },
        [1015100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329607424'),
            ['ID'] = 1015100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DownAnti', 
            ['ShowType'] = 0, 
        },
        [1015110] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368821504'),
            ['ID'] = 1015110, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceDown', 
            ['ShowType'] = 0, 
        },
        [1015120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329608448'),
            ['ID'] = 1015120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DownHitRate', 
            ['ShowType'] = 1, 
        },
        [1015200] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329608704'),
            ['ID'] = 1015200, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'PullAnti', 
            ['ShowType'] = 0, 
        },
        [1015210] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368821760'),
            ['ID'] = 1015210, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhancePull', 
            ['ShowType'] = 0, 
        },
        [1015220] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329609728'),
            ['ID'] = 1015220, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'PullHitRate', 
            ['ShowType'] = 1, 
        },
        [1015300] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329609984'),
            ['ID'] = 1015300, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TiedAnti', 
            ['ShowType'] = 0, 
        },
        [1015310] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368822016'),
            ['ID'] = 1015310, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceTied', 
            ['ShowType'] = 0, 
        },
        [1015320] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329611008'),
            ['ID'] = 1015320, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'TiedHitRate', 
            ['ShowType'] = 1, 
        },
        [1015400] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329611264'),
            ['ID'] = 1015400, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SleepAnti', 
            ['ShowType'] = 0, 
        },
        [1015410] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368822272'),
            ['ID'] = 1015410, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceSleep', 
            ['ShowType'] = 0, 
        },
        [1015420] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329612288'),
            ['ID'] = 1015420, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SleepHitRate', 
            ['ShowType'] = 1, 
        },
        [1015500] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329612544'),
            ['ID'] = 1015500, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DizzyAnti', 
            ['ShowType'] = 0, 
        },
        [1015510] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368822528'),
            ['ID'] = 1015510, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceDizzy', 
            ['ShowType'] = 0, 
        },
        [1015520] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329613568'),
            ['ID'] = 1015520, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'DizzyHitRate', 
            ['ShowType'] = 1, 
        },
        [1015600] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329613824'),
            ['ID'] = 1015600, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FearAnti', 
            ['ShowType'] = 0, 
        },
        [1015610] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368822784'),
            ['ID'] = 1015610, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceFear', 
            ['ShowType'] = 0, 
        },
        [1015620] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329614848'),
            ['ID'] = 1015620, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FearHitRate', 
            ['ShowType'] = 1, 
        },
        [1015700] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329615104'),
            ['ID'] = 1015700, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SilenceAnti', 
            ['ShowType'] = 0, 
        },
        [1015710] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368823040'),
            ['ID'] = 1015710, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceSilence', 
            ['ShowType'] = 0, 
        },
        [1015720] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329616128'),
            ['ID'] = 1015720, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SilenceHitRate', 
            ['ShowType'] = 1, 
        },
        [1015800] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329616384'),
            ['ID'] = 1015800, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SlowAnti', 
            ['ShowType'] = 0, 
        },
        [1015810] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368823296'),
            ['ID'] = 1015810, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceSlow', 
            ['ShowType'] = 0, 
        },
        [1015820] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329617408'),
            ['ID'] = 1015820, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SlowHitRate', 
            ['ShowType'] = 1, 
        },
        [1016000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329617664'),
            ['ID'] = 1016000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllEleAtk', 
            ['ShowType'] = 0, 
        },
        [1016010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329618176'),
            ['ID'] = 1016010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllEleHurtMulti', 
            ['ShowType'] = 1, 
        },
        [1016020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329618432'),
            ['ID'] = 1016020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllEleHurtReduce', 
            ['ShowType'] = 1, 
        },
        [1016030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368823808'),
            ['ID'] = 1016030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllControlAnti', 
            ['ShowType'] = 0, 
        },
        [1016040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19242258810368'),
            ['ID'] = 1016040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'EnhanceAllControl', 
            ['ShowType'] = 0, 
        },
        [1016050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_19036368823552'),
            ['ID'] = 1016050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllControlHitRate', 
            ['ShowType'] = 1, 
        },
        [1016060] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329619968'),
            ['ID'] = 1016060, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllEleAnti', 
            ['ShowType'] = 0, 
        },
        [1016070] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24190329620480'),
            ['ID'] = 1016070, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'IgnoreAllEle', 
            ['ShowType'] = 0, 
        },
        [1016080] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_54632789318144'),
            ['ID'] = 1016080, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Def', 
            ['ShowType'] = 0, 
        },
        [1016090] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210374144'),
            ['ID'] = 1016090, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Block', 
            ['ShowType'] = 0, 
        },
        [1016100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_54632789317888'),
            ['ID'] = 1016100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Atk', 
            ['ShowType'] = 0, 
        },
        [1016120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210373888'),
            ['ID'] = 1016120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'Pierce', 
            ['ShowType'] = 0, 
        },
        [1016130] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890641920'),
            ['ID'] = 1016130, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllProHurtMulti', 
            ['ShowType'] = 0, 
        },
        [1016140] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890642176'),
            ['ID'] = 1016140, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllProHurtReduce', 
            ['ShowType'] = 0, 
        },
        [1016150] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890642432'),
            ['ID'] = 1016150, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllRaceHurtMulti', 
            ['ShowType'] = 0, 
        },
        [1016160] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890642688'),
            ['ID'] = 1016160, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AllRaceHurtReduce', 
            ['ShowType'] = 0, 
        },
        [1016170] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210376960'),
            ['ID'] = 1016170, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritAnti', 
            ['ShowType'] = 0, 
        },
        [1016180] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_18830210377984'),
            ['ID'] = 1016180, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'CritDef', 
            ['ShowType'] = 1, 
        },
        [1017010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890643456'),
            ['ID'] = 1017010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ShieldBreak', 
            ['ShowType'] = 0, 
        },
        [1017020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890643712'),
            ['ID'] = 1017020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AirShield', 
            ['ShowType'] = 0, 
        },
        [1017030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890643968'),
            ['ID'] = 1017030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SkillAnti', 
            ['ShowType'] = 0, 
        },
        [1017040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890644224'),
            ['ID'] = 1017040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SkillEnhance', 
            ['ShowType'] = 0, 
        },
        [1017050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890644480'),
            ['ID'] = 1017050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AdditionalAtk', 
            ['ShowType'] = 0, 
        },
        [1017060] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890644736'),
            ['ID'] = 1017060, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AdditionalAnti', 
            ['ShowType'] = 0, 
        },
        [1017070] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24052890644992'),
            ['ID'] = 1017070, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'AdditionalIgnore', 
            ['ShowType'] = 0, 
        },
        [1018000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610077696'),
            ['ID'] = 1018000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtMulti1', 
            ['ShowType'] = 2, 
        },
        [1018010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610077952'),
            ['ID'] = 1018010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtMulti2', 
            ['ShowType'] = 2, 
        },
        [1018020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078208'),
            ['ID'] = 1018020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtMulti3', 
            ['ShowType'] = 2, 
        },
        [1018030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078464'),
            ['ID'] = 1018030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtMulti4', 
            ['ShowType'] = 2, 
        },
        [1018040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078720'),
            ['ID'] = 1018040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtMulti5', 
            ['ShowType'] = 2, 
        },
        [1018100] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610078976'),
            ['ID'] = 1018100, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtReduce1', 
            ['ShowType'] = 2, 
        },
        [1018110] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079232'),
            ['ID'] = 1018110, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtReduce2', 
            ['ShowType'] = 2, 
        },
        [1018120] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079488'),
            ['ID'] = 1018120, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtReduce3', 
            ['ShowType'] = 2, 
        },
        [1018130] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610079744'),
            ['ID'] = 1018130, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtReduce4', 
            ['ShowType'] = 2, 
        },
        [1018140] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080000'),
            ['ID'] = 1018140, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'RaceHurtReduce5', 
            ['ShowType'] = 2, 
        },
        [1019000] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080256'),
            ['ID'] = 1019000, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SunHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019010] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080512'),
            ['ID'] = 1019010, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'VisionaryHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019020] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610080768'),
            ['ID'] = 1019020, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FeatherwitHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019030] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081024'),
            ['ID'] = 1019030, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ArbiterHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019040] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081280'),
            ['ID'] = 1019040, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'WarriorHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019050] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610081536'),
            ['ID'] = 1019050, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ApprenticeHurtMulti', 
            ['ShowType'] = 2, 
        },
        [1019080] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610082304'),
            ['ID'] = 1019080, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ProHurtMulti9', 
            ['ShowType'] = 2, 
        },
        [1019200] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610082816'),
            ['ID'] = 1019200, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'SunHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019210] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083072'),
            ['ID'] = 1019210, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'VisionaryHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019220] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083328'),
            ['ID'] = 1019220, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'FeatherwitHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019230] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083584'),
            ['ID'] = 1019230, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ArbiterHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019240] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610083840'),
            ['ID'] = 1019240, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'WarriorHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019250] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610084096'),
            ['ID'] = 1019250, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ApprenticeHurtReduce', 
            ['ShowType'] = 2, 
        },
        [1019280] = {
            ['Discription'] = Game.TableDataManager:GetLangStr('str_24121610084864'),
            ['ID'] = 1019280, 
            ['IsPrimary'] = false, 
            ['MaxValue'] = '99999999', 
            ['MinValue'] = '', 
            ['Prop'] = 'ProHurtReduce9', 
            ['ShowType'] = 2, 
        },
    }
}
return TopData