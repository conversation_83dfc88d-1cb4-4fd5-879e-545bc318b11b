--
-- 表名: $MiniGame_迷你游戏.xlsx  页名：$KnowledgeTips_提示词
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652687360'),
		},
		[2] = {
			["ID"] = 2,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652687616'),
		},
		[3] = {
			["ID"] = 3,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652687872'),
		},
		[4] = {
			["ID"] = 4,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652688128'),
		},
		[5] = {
			["ID"] = 5,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652688384'),
		},
		[6] = {
			["ID"] = 6,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652688640'),
		},
		[7] = {
			["ID"] = 7,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652688896'),
		},
		[8] = {
			["ID"] = 8,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652689152'),
		},
		[9] = {
			["ID"] = 9,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652689408'),
		},
		[10] = {
			["ID"] = 10,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652689664'),
		},
		[11] = {
			["ID"] = 11,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652689920'),
		},
		[12] = {
			["ID"] = 12,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652690176'),
		},
		[13] = {
			["ID"] = 13,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652690432'),
		},
		[14] = {
			["ID"] = 14,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652690688'),
		},
		[15] = {
			["ID"] = 15,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652690944'),
		},
		[16] = {
			["ID"] = 16,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652691200'),
		},
		[17] = {
			["ID"] = 17,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652691456'),
		},
		[18] = {
			["ID"] = 18,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652691712'),
		},
		[19] = {
			["ID"] = 19,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652691968'),
		},
		[20] = {
			["ID"] = 20,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652692224'),
		},
		[21] = {
			["ID"] = 21,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652692480'),
		},
		[22] = {
			["ID"] = 22,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652692736'),
		},
		[23] = {
			["ID"] = 23,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652692992'),
		},
		[24] = {
			["ID"] = 24,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652693248'),
		},
		[25] = {
			["ID"] = 25,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652693504'),
		},
		[26] = {
			["ID"] = 26,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652693760'),
		},
		[27] = {
			["ID"] = 27,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652694016'),
		},
		[28] = {
			["ID"] = 28,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652694272'),
		},
		[29] = {
			["ID"] = 29,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652694528'),
		},
		[30] = {
			["ID"] = 30,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652694784'),
		},
		[31] = {
			["ID"] = 31,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652695040'),
		},
		[32] = {
			["ID"] = 32,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652695296'),
		},
		[33] = {
			["ID"] = 33,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652695552'),
		},
		[34] = {
			["ID"] = 34,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652695808'),
		},
		[35] = {
			["ID"] = 35,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652696064'),
		},
		[36] = {
			["ID"] = 36,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652696320'),
		},
		[37] = {
			["ID"] = 37,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652696576'),
		},
		[38] = {
			["ID"] = 38,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652696832'),
		},
		[39] = {
			["ID"] = 39,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652697088'),
		},
		[40] = {
			["ID"] = 40,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652697344'),
		},
		[41] = {
			["ID"] = 41,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652697600'),
		},
		[42] = {
			["ID"] = 42,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652697856'),
		},
		[43] = {
			["ID"] = 43,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652698112'),
		},
		[44] = {
			["ID"] = 44,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652698368'),
		},
		[45] = {
			["ID"] = 45,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652698624'),
		},
		[46] = {
			["ID"] = 46,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652698880'),
		},
		[47] = {
			["ID"] = 47,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652693504'),
		},
		[48] = {
			["ID"] = 48,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652699392'),
		},
		[49] = {
			["ID"] = 49,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652699648'),
		},
		[50] = {
			["ID"] = 50,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652696320'),
		},
		[51] = {
			["ID"] = 51,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652700160'),
		},
		[52] = {
			["ID"] = 52,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652700416'),
		},
		[53] = {
			["ID"] = 53,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652700672'),
		},
		[54] = {
			["ID"] = 54,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652700928'),
		},
		[55] = {
			["ID"] = 55,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652701184'),
		},
		[56] = {
			["ID"] = 56,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652701440'),
		},
		[57] = {
			["ID"] = 57,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652701696'),
		},
		[58] = {
			["ID"] = 58,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652701952'),
		},
		[59] = {
			["ID"] = 59,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652702208'),
		},
		[60] = {
			["ID"] = 60,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652702464'),
		},
		[61] = {
			["ID"] = 61,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652702720'),
		},
		[62] = {
			["ID"] = 62,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652702976'),
		},
		[63] = {
			["ID"] = 63,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652703232'),
		},
		[64] = {
			["ID"] = 64,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652703488'),
		},
		[65] = {
			["ID"] = 65,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652703744'),
		},
		[66] = {
			["ID"] = 66,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652704000'),
		},
		[67] = {
			["ID"] = 67,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652704256'),
		},
		[68] = {
			["ID"] = 68,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652704512'),
		},
		[69] = {
			["ID"] = 69,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652704768'),
		},
		[70] = {
			["ID"] = 70,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652705024'),
		},
		[71] = {
			["ID"] = 71,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652705280'),
		},
		[72] = {
			["ID"] = 72,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652705536'),
		},
		[73] = {
			["ID"] = 73,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652705792'),
		},
		[74] = {
			["ID"] = 74,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652706048'),
		},
		[75] = {
			["ID"] = 75,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652706304'),
		},
		[76] = {
			["ID"] = 76,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652706560'),
		},
		[77] = {
			["ID"] = 77,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652706816'),
		},
		[78] = {
			["ID"] = 78,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652707072'),
		},
		[79] = {
			["ID"] = 79,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652707328'),
		},
		[80] = {
			["ID"] = 80,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652707584'),
		},
		[81] = {
			["ID"] = 81,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652707840'),
		},
		[82] = {
			["ID"] = 82,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652708096'),
		},
		[83] = {
			["ID"] = 83,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652708352'),
		},
		[84] = {
			["ID"] = 84,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652708608'),
		},
		[85] = {
			["ID"] = 85,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652708864'),
		},
		[86] = {
			["ID"] = 86,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652709120'),
		},
		[87] = {
			["ID"] = 87,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652709376'),
		},
		[88] = {
			["ID"] = 88,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652709632'),
		},
		[89] = {
			["ID"] = 89,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652709888'),
		},
		[90] = {
			["ID"] = 90,
			["Text"] = Game.TableDataManager:GetLangStr('str_35803652710144'),
		},
	},
}

return TopData
