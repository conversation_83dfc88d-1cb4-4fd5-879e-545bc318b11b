--
-- 表名: $PVPBase_PVP玩法配置表.xlsx  页名：$OccupyDetectAreaType_占点区域类型
--

local TopData = {
	data = {
		[11001] = {
			["AreaId"] = 11001,
			["IsAutoActive"] = false,
			["FullyOccupyValue"] = 1000,
			["ActiveOccupyMinNum"] = 3,
			["CampDiffNum"] = 1,
			["BasicOccupySpeed"] = 40,
			["GrowthSpeedPerCapita"] = 1,
			["MaxGrowthSpeed"] = 10,
			["DetectInterval"] = 1,
			["DetectIntervalInterpolate"] = 10,
			["FullyOccupyReminder"] = 0,
			["UnclaimedEffect"] = 1000010,
			["ClaimingEffect"] = {{1000011, 1000012}, {1000013, 1000014}},
			["OccupiedEffect"] = {{1000015, 1000016}, {1000017, 1000018}},
		},
		[11002] = {
			["AreaId"] = 11002,
			["IsAutoActive"] = false,
			["FullyOccupyValue"] = 1000,
			["ActiveOccupyMinNum"] = 6,
			["CampDiffNum"] = 1,
			["BasicOccupySpeed"] = 40,
			["GrowthSpeedPerCapita"] = 1,
			["MaxGrowthSpeed"] = 15,
			["DetectInterval"] = 1,
			["DetectIntervalInterpolate"] = 10,
			["FullyOccupyReminder"] = 0,
			["UnclaimedEffect"] = 1000010,
			["ClaimingEffect"] = {{1000011, 1000012}, {1000013, 1000014}},
			["OccupiedEffect"] = {{1000015, 1000016}, {1000017, 1000018}},
		},
		[11003] = {
			["AreaId"] = 11003,
			["IsAutoActive"] = false,
			["FullyOccupyValue"] = 1000,
			["ActiveOccupyMinNum"] = 1,
			["CampDiffNum"] = 1,
			["BasicOccupySpeed"] = 30,
			["GrowthSpeedPerCapita"] = 1,
			["MaxGrowthSpeed"] = 10,
			["DetectInterval"] = 1,
			["DetectIntervalInterpolate"] = 10,
			["FullyOccupyReminder"] = 0,
			["UnclaimedEffect"] = 1000010,
			["ClaimingEffect"] = {{1000011, 1000012}, {1000013, 1000014}},
			["OccupiedEffect"] = {{1000015, 1000016}, {1000017, 1000018}},
		},
		[11004] = {
			["AreaId"] = 11004,
			["IsAutoActive"] = false,
			["FullyOccupyValue"] = 1000,
			["ActiveOccupyMinNum"] = 1,
			["CampDiffNum"] = 1,
			["BasicOccupySpeed"] = 30,
			["GrowthSpeedPerCapita"] = 1,
			["MaxGrowthSpeed"] = 10,
			["DetectInterval"] = 1,
			["DetectIntervalInterpolate"] = 10,
			["FullyOccupyReminder"] = 0,
			["UnclaimedEffect"] = 1000019,
			["ClaimingEffect"] = {{1000020, 1000021}, {1000022, 1000023}},
			["OccupiedEffect"] = {{1000024, 1000025}, {1000026, 1000027}},
		},
		[11005] = {
			["AreaId"] = 11005,
			["IsAutoActive"] = false,
			["FullyOccupyValue"] = 1000,
			["ActiveOccupyMinNum"] = 1,
			["CampDiffNum"] = 1,
			["BasicOccupySpeed"] = 28,
			["GrowthSpeedPerCapita"] = 1,
			["MaxGrowthSpeed"] = 10,
			["DetectInterval"] = 1,
			["DetectIntervalInterpolate"] = 10,
			["FullyOccupyReminder"] = 0,
			["UnclaimedEffect"] = 1000019,
			["ClaimingEffect"] = {{1000020, 1000021}, {1000022, 1000023}},
			["OccupiedEffect"] = {{1000024, 1000025}, {1000026, 1000027}},
		},
	},
}

return TopData
