--
-- 表名: BookContentData后处理
--

local TopData = {
    data = {
        [70001] = {
            ['CloseType'] = 2, 
            ['IPTitle'] = 'GUIMIZHIZHU', 
            ['MaxPage'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289463296'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {[1] = {['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215721472'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947286016'),}, [2] = {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215721984'),}, [4] = {['BHandWriting'] = 2, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 1, }, [5] = {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215722240'),}, }, 
        },
        [70002] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = 'GUIMIZHIZHU', 
            ['MaxPage'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289464320'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215721472'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947287040'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215722752'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215723520'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215723008'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947287040'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, }, 
        },
        [70003] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 6, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289465600'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215724032'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947288576'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215724288'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947288832'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215724544'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947289088'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215724800'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947289344'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215725056'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947289600'),}, }, 
        },
        [70004] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289467136'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215725312'),['Title'] = Game.TableDataManager:GetLangStr('str_59443152692992'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/ConfigImage/Book/UI_ConfigImg_Book_Img_Photo02.UI_ConfigImg_Book_Img_Photo02', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215725568'),}, }, 
        },
        [70005] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289467136'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215725824'),}, }, 
        },
        [70006] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289467904'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215726080'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215726336'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215726592'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215726848'),}, }, 
        },
        [70007] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289468928'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215727104'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947291648'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215727360'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947291904'),}, }, 
        },
        [70008] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289468928'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215727616'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947292160'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215727872'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947292160'),}, }, 
        },
        [70009] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289468928'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215728128'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215728384'),}, }, 
        },
        [70010] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289470464'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215728640'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215728896'),}, }, 
        },
        [70011] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32300301553920'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215729152'),}, }, 
        },
        [70012] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289471232'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215729408'),}, }, 
        },
        [70013] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289471488'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215729664'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947294208'),}, }, 
        },
        [70014] = {
            ['CloseType'] = 0, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289471744'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215729920'),}, }, 
        },
        [70015] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289472000'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 0, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299713'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215730432'),['Title'] = '1', }, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_56970325299713'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215730944'),['Title'] = '2', }, }, 
        },
        [70016] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289473024'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215731200'),}, }, 
        },
        [70017] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289473280'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215731456'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215731712'),}, }, 
        },
        [70018] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289473280'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215732224'),}, }, 
        },
        [70019] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289473280'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215732480'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215732736'),}, }, 
        },
        [70020] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289474816'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215732992'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215733248'),}, }, 
        },
        [70021] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289475328'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215733504'),}, }, 
        },
        [70022] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289475584'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215733760'),}, }, 
        },
        [70023] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289475584'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215734016'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215734272'),}, }, 
        },
        [70024] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289476352'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215734528'),}, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215734784'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947299328'),}, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215735040'),['Title'] = Game.TableDataManager:GetLangStr('str_57862873204224'),}, }, 
        },
        [70025] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289477120'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215735296'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947299840'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset02.UI_Newspaper_Img_Inset02', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215735552'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947300096'),}, }, 
        },
        [70026] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289477632'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215735808'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947300352'),}, }, 
        },
        [70027] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289477888'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215736064'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947300352'),}, }, 
        },
        [70028] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289478144'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215736320'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947300352'),}, }, 
        },
        [70029] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289478400'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215736576'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947301120'),}, }, 
        },
        [70030] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289478656'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215736832'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947301376'),}, }, 
        },
        [70031] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289478912'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215737088'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947301632'),}, }, 
        },
        [70032] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289479168'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, }, }, 
        },
        [70033] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289479424'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215737600'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947302144'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215737856'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947302400'),}, }, 
        },
        [70034] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289479936'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215738112'),}, }, 
        },
        [70035] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289480192'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215738368'),}, }, 
        },
        [70036] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289480448'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215738624'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215738880'),}, }, 
        },
        [70037] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289480960'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215739136'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947303680'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215739392'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947303680'),}, }, 
        },
        [70038] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32367947304192'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215739648'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947304192'),}, }, 
        },
        [70039] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 7, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289481728'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215739904'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947304448'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215740160'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947304704'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215740416'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947304960'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215740672'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947305216'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215740928'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947305472'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215741184'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947305728'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215741440'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947305984'),}, }, 
        },
        [70040] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = '', 
            ['NotifyClose'] = 1, 
            ['OpenType'] = 0, 
            ['Pages'] = {{['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215741696'),}, {['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215741952'),}, }, 
        },
        [70041] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289479168'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215742208'),}, }, 
        },
        [70042] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32369289478400'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32368215742464'),['Title'] = Game.TableDataManager:GetLangStr('str_32367947307008'),}, }, 
        },
    }
}
return TopData