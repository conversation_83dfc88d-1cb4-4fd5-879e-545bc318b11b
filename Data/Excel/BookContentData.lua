--
-- 表名: BookContentData后处理
--

local TopData = {
    data = {
        [70001] = {
            ['CloseType'] = 2, 
            ['IPTitle'] = 'GUIMIZHIZHU', 
            ['MaxPage'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411556352'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {[1] = {['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337814528'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069379072'),}, [2] = {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337815040'),}, [4] = {['BHandWriting'] = 2, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 1, }, [5] = {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337815296'),}, }, 
        },
        [70002] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = 'GUIMIZHIZHU', 
            ['MaxPage'] = 5, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411557376'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337814528'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069380096'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337815808'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337816576'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337816064'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069380096'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, }, 
        },
        [70003] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 6, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994274816'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337817088'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069381632'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337817344'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069381888'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337817600'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069382144'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337817856'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069382400'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337818112'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069382656'),}, }, 
        },
        [70004] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411560192'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337818368'),['Title'] = Game.TableDataManager:GetLangStr('str_59236994262784'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/ConfigImage/Book/UI_ConfigImg_Book_Img_Photo02.UI_ConfigImg_Book_Img_Photo02', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337818624'),}, }, 
        },
        [70005] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411560192'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337818880'),}, }, 
        },
        [70006] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411560960'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337819136'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337819392'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337819648'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337819904'),}, }, 
        },
        [70007] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411561984'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337820160'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069384704'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337820416'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069384960'),}, }, 
        },
        [70008] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411561984'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337820672'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069385216'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337820928'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069385216'),}, }, 
        },
        [70009] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411561984'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337821184'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337821440'),}, }, 
        },
        [70010] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411563520'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337821696'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337821952'),}, }, 
        },
        [70011] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32025423646976'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337822208'),}, }, 
        },
        [70012] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411564288'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337822464'),}, }, 
        },
        [70013] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411564544'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337822720'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069387264'),}, }, 
        },
        [70014] = {
            ['CloseType'] = 0, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411564800'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337822976'),}, }, 
        },
        [70015] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411565056'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 0, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961088'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337823488'),['Title'] = '1', }, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_26734023961088'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337824000'),['Title'] = '2', }, }, 
        },
        [70016] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994361344'),
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337824256'),}, }, 
        },
        [70017] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994368768'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337824512'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337824768'),}, }, 
        },
        [70018] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994368768'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, }, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337825280'),}, }, 
        },
        [70019] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994368768'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337825536'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Common_2/NotAtlas/BackGround/UI_Com_Bg_Skill01.UI_Com_Bg_Skill01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337825792'),}, }, 
        },
        [70020] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411567872'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337826048'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337826304'),}, }, 
        },
        [70021] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411568384'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337826560'),}, }, 
        },
        [70022] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411568640'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337826816'),}, }, 
        },
        [70023] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411568640'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337827072'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337827328'),}, }, 
        },
        [70024] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 3, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59243705278208'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337827584'),}, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337827840'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069392384'),}, {['BHandWriting'] = 0, ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337828096'),['Title'] = Game.TableDataManager:GetLangStr('str_5154766125568'),}, }, 
        },
        [70025] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994363648'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset01.UI_Newspaper_Img_Inset01', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337828352'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069392896'),}, {['BHandWriting'] = 0, ['Image'] = '/Game/Arts/UI_2/Resource/Newspaper/NotAtlas/UI_Newspaper_Img_Inset02.UI_Newspaper_Img_Inset02', ['Layout'] = 2, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337828608'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069393152'),}, }, 
        },
        [70026] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994411008'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337828864'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069393408'),}, }, 
        },
        [70027] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994411520'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337829120'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069393408'),}, }, 
        },
        [70028] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994412032'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337829376'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069393408'),}, }, 
        },
        [70029] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411571456'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337829632'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069394176'),}, }, 
        },
        [70030] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411571712'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337829888'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069394432'),}, }, 
        },
        [70031] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411571968'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 2, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337830144'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069394688'),}, }, 
        },
        [70032] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994450688'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, }, }, 
        },
        [70033] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411572480'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337830656'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069395200'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337830912'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069395456'),}, }, 
        },
        [70034] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411572992'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337831168'),}, }, 
        },
        [70035] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411573248'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337831424'),}, }, 
        },
        [70036] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411573504'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337831680'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337831936'),}, }, 
        },
        [70037] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994489088'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337832192'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069396736'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337832448'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069396736'),}, }, 
        },
        [70038] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32093069397248'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337832704'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069397248'),}, }, 
        },
        [70039] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 7, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411574784'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337832960'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069397504'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337833216'),['Title'] = Game.TableDataManager:GetLangStr('str_58549799614976'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337833472'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069398016'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337833728'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069398272'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337833984'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069398528'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337834240'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069398784'),}, {['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337834496'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069399040'),}, }, 
        },
        [70040] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 2, 
            ['Name'] = '', 
            ['NotifyClose'] = 1, 
            ['OpenType'] = 0, 
            ['Pages'] = {{['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337834752'),}, {['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337835008'),}, }, 
        },
        [70041] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_59236994450688'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 1, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337835264'),}, }, 
        },
        [70042] = {
            ['CloseType'] = 1, 
            ['IPTitle'] = '', 
            ['MaxPage'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32094411571456'),
            ['NotifyClose'] = 1, 
            ['OpenType'] = 1, 
            ['Pages'] = {{['BHandWriting'] = 0, ['Layout'] = 1, ['Text'] = Game.TableDataManager:GetLangStr('str_32093337835520'),['Title'] = Game.TableDataManager:GetLangStr('str_32093069400064'),}, }, 
        },
    }
}
return TopData