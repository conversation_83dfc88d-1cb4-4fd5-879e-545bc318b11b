--
-- 表名: NEW Inputs: (Aura.xlsx, Aura); Outputs: AuraData; Def:data_post_export_aura.lua
--

local TopData = {
    data = {
        [1] = {
            ['AuraDisc'] = Game.TableDataManager:GetLangStr('str_7629204096768'),
            ['AuraDiscParam'] = 'AddBuffNewID;\nEffect1,t1,2', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 1, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 5, 
            ['EffectPriority'] = 2, 
            ['ID'] = 1, 
            ['Level'] = 10, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 3, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {1000, 1000, 100, 0, 100}, ['ShapeType'] = 1, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 3, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 3, 
        },
        [2] = {
            ['AuraDisc'] = Game.TableDataManager:GetLangStr('str_7629204097024'),
            ['AuraDiscParam'] = 'Duration', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 2, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 20, 
            ['EffectPriority'] = 2, 
            ['ID'] = 2, 
            ['Level'] = 15, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 3, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {300, 100, 0, 100}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [3] = {
            ['AuraDisc'] = Game.TableDataManager:GetLangStr('str_7629204097280'),
            ['AuraDiscParam'] = '82050007', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935661824'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 2, 
            ['ID'] = 3, 
            ['Level'] = 1, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [4] = {
            ['AuraDisc'] = Game.TableDataManager:GetLangStr('str_7629204097536'),
            ['AuraDiscParam'] = 'Buff,82050006,Lv,BuffName', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935662080'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 2, 
            ['ID'] = 4, 
            ['Level'] = 1, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [5] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935662336'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 30, 
            ['EffectPriority'] = 2, 
            ['ID'] = 5, 
            ['Level'] = 10, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 1, ['Sign'] = 1, ['Value'] = 0.5, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 2, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {500, 1000, -500, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 4124, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 2, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 2, 
        },
        [100] = {
            ['AuraDisc'] = Game.TableDataManager:GetLangStr('str_7629204096512'),
            ['AuraDiscParam'] = 'Duration', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935661056'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 30, 
            ['EffectPriority'] = 2, 
            ['ID'] = 100, 
            ['Level'] = 10, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667225600'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 1, ['Sign'] = 1, ['Value'] = 0.5, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 2, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {500, 1000, -500, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 4124, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 2, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 2, 
        },
        [20601] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0.23, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_8865617822208'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 6.6, 
            ['EffectPriority'] = 2, 
            ['ID'] = 20601, 
            ['Level'] = 40, 
            ['Name'] = '', 
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {700, 100, 0, 100}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [823000021] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935662848'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 2, 
            ['ID'] = 823000021, 
            ['Level'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667227392'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [823011011] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935663104'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 2, 
            ['ID'] = 823011011, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667227648'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [860200051] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0.67, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 6, 
            ['EffectPriority'] = 2, 
            ['ID'] = 860200051, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667228160'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {50, 700, 1000, 0, 1000}, ['ShapeType'] = 5, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 4124, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [860200062] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0.233, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 6.8, 
            ['EffectPriority'] = 2, 
            ['ID'] = 860200062, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667233024'),
            ['OnDestroyAction_Desc'] = {['1'] = {['1'] = {['FuncName'] = 'CastAttack', ['SkillID'] = 86021033, }, }, ['2'] = {['1'] = {['FuncName'] = 'KillMyEntity', ['ID'] = 860200061, ['Type'] = 4, }, }, }, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {700, 100, 0, 100}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [860200181] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 860200181, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 18, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860200181, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667228416'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 1, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 1, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {700, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 1, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 1, 
        },
        [860200351] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 9.34, 
            ['EffectPriority'] = 2, 
            ['ID'] = 860200351, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667228672'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 12, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {900, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 12, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 12, 
        },
        [860340112] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 5, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860340112, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667228928'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {800, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [860510801] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935663360'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 5.1, 
            ['EffectPriority'] = 2, 
            ['ID'] = 860510801, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667227904'),
            ['OnDestroyAction_Desc'] = {['1'] = {['1'] = {['ByID'] = 82050011, ['FuncName'] = 'DelBuff', ['Target'] = 'self', }, }, }, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {500, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [860600091] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935664640'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860600091, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667229184'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [860600101] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935664896'),
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 0, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860600101, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667229184'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['TargetsNum'] = 0, 
            ['bTeamAura'] = true, 
        },
        [860600111] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 860600111, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935665152'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 5, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860600111, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667229696'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 1, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 2, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {470, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [860600112] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_7628935665408'),
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 5, 
            ['EffectPriority'] = 3, 
            ['ID'] = 860600112, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667229696'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 1, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 1, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {470, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880052011] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 880052017, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 2, 
            ['ID'] = 880052011, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667230208'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 3, ['ID'] = 85005204, ['Layer'] = 1, ['Sign'] = 0, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 1, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {950, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880052012] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 2, 
            ['ID'] = 880052012, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667230464'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 3, ['ID'] = 85005230, ['Layer'] = 1, ['Sign'] = 0, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {950, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880052013] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 2, 
            ['ID'] = 880052013, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667230720'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 3, ['ID'] = 85005204, ['Layer'] = 1, ['Sign'] = 0, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 1, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {900, 2450, 500, 31, 0, 500}, ['ShapeType'] = 3, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880052014] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 2, 
            ['ID'] = 880052014, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667230976'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 3, ['ID'] = 85005230, ['Layer'] = 1, ['Sign'] = 0, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {900, 2450, 500, 31, 0, 500}, ['ShapeType'] = 3, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880052015] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 2, 
            ['ID'] = 880052015, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667231232'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['Condition'] = {['ConditionList'] = {{['ConditionTargetType'] = 0, ['ConditionType'] = 3, ['ID'] = 85005204, ['Layer'] = 1, ['Sign'] = 0, }, }, ['ConditionRelationType'] = 0, }, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 8, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {900, 2450, 500, 31, 0, 500}, ['ShapeType'] = 3, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880053211] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 1, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880053211, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667231488'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {600, 10000, 1000, 0, 1000}, ['ShapeType'] = 5, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880075011] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 15, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880075011, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667234304'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {500, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880075021] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880075021, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667234560'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 9, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {1200, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = false, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880080711] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880080711, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667233280'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {800, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880080712] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880080712, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667233536'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {800, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880080713] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880080713, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667233792'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {800, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [880080714] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 9999, 
            ['EffectPriority'] = 3, 
            ['ID'] = 880080714, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667234048'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {800, 500, 0, 500}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [881051121] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 1, 
            ['EffectPriority'] = 3, 
            ['ID'] = 881051121, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667231744'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {600, 10000, 1000, 0, 1000}, ['ShapeType'] = 5, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [890003091] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 890003091, 
            ['DeployTime'] = 0.5, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = false, 
            ['Duration'] = 9, 
            ['EffectPriority'] = 3, 
            ['ID'] = 890003091, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_8865617871104'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 4, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {500, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [890011341] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 7, 
            ['EffectPriority'] = 2, 
            ['ID'] = 890011341, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667232256'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 1, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {600, 150, 0, 150}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [890021121] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 3.3, 
            ['EffectPriority'] = 3, 
            ['ID'] = 890021121, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667232512'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 2, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {300, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 1, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
        [890023042] = {
            ['AuraDisc'] = '', 
            ['AuraDiscParam'] = '', 
            ['ClientTimelineID'] = 0, 
            ['DeployTime'] = 0.5, 
            ['Desc'] = '', 
            ['DestroyWhenOwnerDie'] = true, 
            ['Duration'] = 6, 
            ['EffectPriority'] = 2, 
            ['ID'] = 890023042, 
            ['Level'] = 40, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_7628667232768'),
            ['OnDestroyAction_Desc'] = {}, 
            ['OriginalDirectionType'] = 3, 
            ['SelectorRuleData'] = {0, {{['BackupMode'] = 0, ['BackupRuleID'] = 0, ['ClassType'] = 0, ['ClockwiseRotation'] = 0, ['CoordinateOffset'] = {['X'] = 0, ['Y'] = 0, ['Z'] = 0, }, ['CoordinatePointType'] = 0, ['Faction'] = 32, ['IgnoreCheckMask'] = 0, ['MaxNum'] = 0, ['MaxNumInPvP'] = 0, ['ShapeArgs'] = {1500, 1000, 0, 1000}, ['ShapeType'] = 2, ['SortParameter'] = {0, 0}, ['SortType'] = 0, ['TargetAliveType'] = 1, ['TargetType'] = 0, ['bIgnoreSelf'] = true, ['bOnlyRoot'] = false, }, }, {['MaxNum'] = 0, ['MaxPlayer'] = 0, }, }, 
            ['TargetsNum'] = 0, 
        },
    }
}
return TopData