--
-- 表名: 收藏品后处理
--

local TopData = {
    data = {
        [6290000] = {
            ['CanModityDesc'] = true, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339344896'),
            ['Hide'] = false, 
            ['ID'] = 6290000, 
            ['Icon'] = 'UI_Item_Icon_DragonBark', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6324070909440'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 101, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 1, 
        },
        [6290001] = {
            ['CanModityDesc'] = true, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = false, 
            ['ID'] = 6290001, 
            ['Icon'] = 'UI_Item_Icon_DragonBark', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6324070909696'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 101, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590001, 
            ['Type'] = 1, 
        },
        [6290002] = {
            ['CanModityDesc'] = true, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = false, 
            ['ID'] = 6290002, 
            ['Icon'] = 'UI_Item_Icon_DragonBark', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_31822218014976'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 102, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590002, 
            ['Type'] = 1, 
        },
        [6290003] = {
            ['CanModityDesc'] = true, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = false, 
            ['ID'] = 6290003, 
            ['Icon'] = 'UI_Item_Icon_DragonBark', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_31822218015232'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 102, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590003, 
            ['Type'] = 1, 
        },
        [6290004] = {
            ['CanModityDesc'] = true, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = true, 
            ['ID'] = 6290004, 
            ['Icon'] = 'UI_Item_Icon_DragonBark', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_31822218015488'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 102, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590004, 
            ['Type'] = 1, 
        },
        [6291006] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291006, 
            ['Icon'] = 'UI_Item_Icon_HollyBook', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058823168'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790387712'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327258624'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291007] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291007, 
            ['Icon'] = 'UI_Item_Icon_BlueCollar', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058823424'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790387968'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595694336'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327258880'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291008] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291008, 
            ['Icon'] = 'UI_Item_Icon_Announcer', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058823680'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_36628286415616'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595694592'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327259136'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291009] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291009, 
            ['Icon'] = 'UI_Item_Icon_Bag', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058823936'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790388480'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327259392'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291010] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291010, 
            ['Icon'] = 'UI_Item_Icon_ComedyMask', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058824192'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790388736'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595695104'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327259648'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291011] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291011, 
            ['Icon'] = 'UI_Item_Icon_FoolBless', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058824448'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_38758590237440'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327259904'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291012] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291012, 
            ['Icon'] = 'UI_Item_Icon_Furnace', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058824704'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790389248'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327260160'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291013] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291013, 
            ['Icon'] = 'UI_Item_Icon_Gate', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058824960'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790389504'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595695872'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327260416'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291014] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291014, 
            ['Icon'] = 'UI_Item_Icon_GoldenMobiusRing', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058825216'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_38758590232320'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595696128'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327260672'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291015] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291015, 
            ['Icon'] = 'UI_Item_Icon_Shaozi01', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058825472'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790390016'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595696384'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327260928'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291016] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291016, 
            ['Icon'] = 'UI_Item_Icon_TragedyMask', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058825728'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790390272'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595696640'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327259648'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291017] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291017, 
            ['Icon'] = 'UI_Item_Icon_SealPuppet', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058825984'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790390528'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595696896'),
            ['Order'] = 1, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291018] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291018, 
            ['Icon'] = 'UI_Item_Icon_Dice', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058826240'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790390784'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595697152'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327261696'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291019] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291019, 
            ['Icon'] = 'UI_Item_Icon_Fragments', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058826496'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_6392790391040'),
            ['Order'] = 1, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6291020] = {
            ['ConditionAst'] = {}, 
            ['Hide'] = false, 
            ['ID'] = 6291020, 
            ['Icon'] = 'UI_Item_Icon_Gloves02', 
            ['Introduction_Description'] = Game.TableDataManager:GetLangStr('str_6393058826752'),
            ['IsImport'] = true, 
            ['JumpName'] = Game.TableDataManager:GetLangStr('str_6396280048640'),
            ['Name'] = Game.TableDataManager:GetLangStr('str_38758590232576'),
            ['Negative_effect'] = Game.TableDataManager:GetLangStr('str_6393595697664'),
            ['Order'] = 1, 
            ['Positive_effect'] = Game.TableDataManager:GetLangStr('str_6393327262208'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Risk'] = 4, 
            ['Source'] = 1250199, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590005, 
            ['Type'] = 2, 
        },
        [6292000] = {
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 5, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=5', }, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339344896'),
            ['Hide'] = false, 
            ['ID'] = 6292000, 
            ['Icon'] = 'UI_Constable_Img_monster06', 
            ['IsImport'] = true, 
            ['Item'] = {2001000, 2001001}, 
            ['ModelID'] = 7900010, 
            ['ModelPostion'] = {-50, -260, 160}, 
            ['ModelRotationY'] = 90, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_5154766098944'),
            ['Position'] = {Game.TableDataManager:GetLangStr('str_6463388911105'),Game.TableDataManager:GetLangStr('str_6463388911106'),}, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 10, 
            ['TriggerNumber'] = 6292000, 
            ['Type'] = 3, 
        },
        [6292001] = {
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 15, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=15', }, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = false, 
            ['ID'] = 6292001, 
            ['Icon'] = 'UI_Constable_Img_monster01', 
            ['IsImport'] = true, 
            ['Item'] = {2001000, 2001001}, 
            ['ModelID'] = 7900014, 
            ['ModelPostion'] = {-90, -620, 150}, 
            ['ModelRotationY'] = 70, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6461509863168'),
            ['Position'] = {Game.TableDataManager:GetLangStr('str_6463388911361'),Game.TableDataManager:GetLangStr('str_6463388911362'),}, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 10, 
            ['TriggerNumber'] = 6292001, 
            ['Type'] = 3, 
        },
        [6293000] = {
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6531034646016'),
            ['Hide'] = false, 
            ['ID'] = 6293000, 
            ['Icon'] = 'UI_Item_Icon_ManipulatorPotions', 
            ['IsImport'] = true, 
            ['Item'] = 2003082, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6530229339648'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 4, 
        },
        [6293001] = {
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6531034646016'),
            ['Hide'] = false, 
            ['ID'] = 6293001, 
            ['Icon'] = '', 
            ['IsImport'] = true, 
            ['Item'] = 2003083, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6530229339904'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590001, 
            ['Type'] = 4, 
        },
        [6294000] = {
            ['ConditionAst'] = {}, 
            ['CutSceneID'] = 1000064, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6599754122752'),
            ['Hide'] = false, 
            ['ID'] = 6294000, 
            ['Icon'] = 'UI_Item_Icon_StrengthBuff', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6598948816384'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364735488'),
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 5, 
        },
        [6294001] = {
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6324339345152'),
            ['Hide'] = false, 
            ['ID'] = 6294001, 
            ['Icon'] = 'UI_Item_Icon_IntelligenceBuff', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6598948816640'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364735488'),
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590001, 
            ['Type'] = 5, 
            ['Video'] = './Movies/Loading_RailroadCar.bk2', 
        },
        [6294002] = {
            ['ConditionAst'] = {}, 
            ['CutSceneID'] = 1000026, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6599754123264'),
            ['Hide'] = false, 
            ['ID'] = 6294002, 
            ['Icon'] = 'UI_Item_Icon_StrengthBuff', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6598948816896'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364736000'),
            ['Subclass_ID'] = 502, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 5, 
        },
        [6294003] = {
            ['ConditionAst'] = {}, 
            ['CutSceneID'] = 1000027, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6599754123520'),
            ['Hide'] = false, 
            ['ID'] = 6294003, 
            ['Icon'] = 'UI_Item_Icon_IntelligenceBuff', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6598948817152'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364736000'),
            ['Subclass_ID'] = 502, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 5, 
        },
        [6294004] = {
            ['ConditionAst'] = {}, 
            ['CutSceneID'] = 1000028, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6599754123776'),
            ['Hide'] = false, 
            ['ID'] = 6294004, 
            ['Icon'] = 'UI_Item_Icon_StrengthBuff', 
            ['IsImport'] = true, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6598948817408'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364736000'),
            ['Subclass_ID'] = 502, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 5, 
        },
        [6295000] = {
            ['Category_Name'] = Game.TableDataManager:GetLangStr('str_6667668293120'),
            ['Category_Number'] = 1, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6669010470400'),
            ['Hide'] = false, 
            ['ID'] = 6295000, 
            ['IsImport'] = true, 
            ['Item'] = 70003, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6667668293120'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364735488'),
            ['Subclass_ID'] = 601, 
            ['TextNo'] = 0, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590000, 
            ['Type'] = 6, 
        },
        [6295001] = {
            ['Category_Name'] = Game.TableDataManager:GetLangStr('str_6667668293376'),
            ['Category_Number'] = 3, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6669010470656'),
            ['Hide'] = false, 
            ['ID'] = 6295001, 
            ['IsImport'] = true, 
            ['Item'] = 70017, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6668205164288'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364735488'),
            ['Subclass_ID'] = 601, 
            ['TextNo'] = 0, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590001, 
            ['Type'] = 6, 
        },
        [6295002] = {
            ['Category_Name'] = Game.TableDataManager:GetLangStr('str_6667668293632'),
            ['Category_Number'] = 2, 
            ['ConditionAst'] = {}, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_6669010470912'),
            ['Hide'] = false, 
            ['ID'] = 6295002, 
            ['IsImport'] = true, 
            ['Item'] = 0, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_6667668293632'),
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Source'] = Game.TableDataManager:GetLangStr('str_6601364735488'),
            ['Subclass_ID'] = 601, 
            ['TextNo'] = 6276023, 
            ['Token'] = 20, 
            ['TriggerNumber'] = 6590002, 
            ['Type'] = 6, 
        },
    }
}
return TopData