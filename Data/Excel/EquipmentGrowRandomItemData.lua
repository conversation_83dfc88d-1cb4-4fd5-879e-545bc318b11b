--
-- 表名: EquipmentGrow,RandomItem(后处理)
--

local TopData = {
    data = {
        [3300000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307200'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344549888'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_3', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307456'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344550144'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_5', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307712'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344550400'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300003] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300003, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_6', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307968'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344550656'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300004] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300004, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992605}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307200'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344550912'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300005] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300005, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_3', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992606}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307456'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344551168'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300006] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300006, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_5', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992606}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307712'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344551424'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300007] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3300007, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_6', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992605}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307968'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344551680'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300008] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3300008, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307200'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344551936'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300009] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3300009, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_3', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307456'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344552192'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300010] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3300010, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_5', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307712'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344552448'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3300011] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3300011, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type1_6', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592307968'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344552704'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8000, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3301000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3301000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type2_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592310272'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344552960'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8001, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3301001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3301001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type2_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992645}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592310272'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344553216'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8001, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3301002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3301002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type2_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592310272'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344553472'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8001, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3302000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311040'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344553728'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3302001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992612}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311040'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344553984'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3302002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311040'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344554240'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302003] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3302003, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311808'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344554496'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302004] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3302004, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992613}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311808'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344554752'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3302005] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3302005, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type3_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592311808'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344555008'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8002, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3303000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3303000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type4_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592312576'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344555264'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8003, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3303001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3303001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type4_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992607}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592312576'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344555520'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8003, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3303002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3303002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type4_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592312576'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344555776'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8003, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3304000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592313344'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344556032'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3304001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992645}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592313344'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344556288'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3304002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200003, 1200006}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592313344'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344556544'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304003] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3304003, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314112'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344556800'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304004] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3304004, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992645}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314112'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344557056'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3304005] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3304005, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type5_2', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['classlimit'] = {1200002, 1200005}, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314112'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344557312'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8004, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3305000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3305000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type6_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314880'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344557568'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8005, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3305001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3305001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type6_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992639}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314880'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344557824'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8005, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3305002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3305002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type6_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592314880'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344558080'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8005, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3306000] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 4, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3306000, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type7_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 10, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592315648'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686727168'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Sg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418291712'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344558336'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 4, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8006, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3306001] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 5, 
            ['ID'] = 3306001, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type7_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {3992639}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 5, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592315648'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344558592'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8006, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
        [3306002] = {
            ['AuctionMaxPrice'] = 0, 
            ['AuctionMinPrice'] = 0, 
            ['AuctionMoneyType'] = 0, 
            ['DestroyLimit'] = {}, 
            ['DropTemplateID'] = 5, 
            ['EnglishName'] = '', 
            ['FashionID'] = 0, 
            ['GroupNum'] = 4, 
            ['ID'] = 3306002, 
            ['IsCheckReward'] = false, 
            ['Order'] = 10, 
            ['RandomGroup'] = 'Type7_1', 
            ['SetId'] = 4, 
            ['SetWordGroup'] = {}, 
            ['SystemActionEnum'] = {{{['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'ChangeEquipRandomWord', ['FuncParamInfos'] = {}, }, }, }, 
            ['TC'] = 35, 
            ['TaskIDLimit'] = {}, 
            ['TeamRewardAllot'] = 1, 
            ['bindType'] = -1, 
            ['canReuseTimes'] = 20, 
            ['cdServerGroupId'] = 0, 
            ['decomposeItem'] = {[2001000] = {[1500] = 1, }, }, 
            ['decomposeType'] = 102, 
            ['discardAfterFull'] = false, 
            ['freshFlag'] = false, 
            ['funcRep'] = Game.TableDataManager:GetLangStr('str_18493592315648'),
            ['funcSimpleRep'] = Game.TableDataManager:GetLangStr('str_18487686728192'),
            ['holdMax'] = 99999, 
            ['hrefType'] = 101, 
            ['icon'] = 'UI_Item_Icon_Xysg', 
            ['invId'] = 13, 
            ['itemDes'] = Game.TableDataManager:GetLangStr('str_18487418292736'),
            ['itemName'] = Game.TableDataManager:GetLangStr('str_18486344558848'),
            ['itemValue'] = 0, 
            ['itemValue1'] = 0, 
            ['loopOrBatch'] = true, 
            ['lvPreview'] = 0, 
            ['lvReq'] = 1, 
            ['mwrap'] = 1, 
            ['operationGroupType'] = 11, 
            ['preciousInstanceCriterion'] = 0, 
            ['quality'] = 5, 
            ['quickUseBtnName'] = '', 
            ['rarely'] = false, 
            ['resolvePoint'] = false, 
            ['shopApproachJumpItem'] = 0, 
            ['shopItemIndex'] = 0, 
            ['shopType'] = 0, 
            ['skillLvReq'] = 0, 
            ['skillTypeReq'] = 0, 
            ['socProfessionLvReq'] = 0, 
            ['socProfessionReq'] = 0, 
            ['socSkillIdReq'] = 0, 
            ['socSkillLvReq'] = 0, 
            ['socialLvReq'] = 0, 
            ['subType'] = 8006, 
            ['type'] = 10, 
            ['useAwakenId'] = '', 
        },
    }
}
return TopData