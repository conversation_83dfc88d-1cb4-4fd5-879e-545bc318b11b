--
-- 表名: $Fellow.xlsx  页名：$FellowConstInt
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Const"] = "COMBAT_TYPE_MIN",
			["Value"] = 0,
			["EnumDesc"] = "",
		},
		[2] = {
			["ID"] = 2,
			["Const"] = "COMBAT_TYPE_JOIN",
			["Value"] = 1,
			["EnumDesc"] = "",
		},
		[3] = {
			["ID"] = 3,
			["Const"] = "COMBAT_TYPE_ASSIST",
			["Value"] = 2,
			["EnumDesc"] = "",
		},
		[4] = {
			["ID"] = 4,
			["Const"] = "COMBAT_TYPE_MAX",
			["Value"] = 3,
			["EnumDesc"] = "",
		},
		[5] = {
			["ID"] = 5,
			["Const"] = "SLOT_MIN",
			["Value"] = 0,
			["EnumDesc"] = "",
		},
		[6] = {
			["ID"] = 6,
			["Const"] = "SLOT_1",
			["Value"] = 1,
			["EnumDesc"] = "",
		},
		[7] = {
			["ID"] = 7,
			["Const"] = "SLOT_2",
			["Value"] = 2,
			["EnumDesc"] = "",
		},
		[8] = {
			["ID"] = 8,
			["Const"] = "SLOT_3",
			["Value"] = 3,
			["EnumDesc"] = "",
		},
		[9] = {
			["ID"] = 9,
			["Const"] = "SLOT_4",
			["Value"] = 4,
			["EnumDesc"] = "",
		},
		[10] = {
			["ID"] = 10,
			["Const"] = "SLOT_MAX",
			["Value"] = 5,
			["EnumDesc"] = "",
		},
		[11] = {
			["ID"] = 11,
			["Const"] = "LEVEL_INIT",
			["Value"] = 1,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488980992'),
		},
		[12] = {
			["ID"] = 12,
			["Const"] = "FIRST_STAR_UP_LEVEL_INIT",
			["Value"] = 1,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488981248'),
		},
		[13] = {
			["ID"] = 13,
			["Const"] = "SECOND_STAR_UP_LEVEL_INIT",
			["Value"] = 0,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488981504'),
		},
		[14] = {
			["ID"] = 14,
			["Const"] = "STAR_LEVEL_UP_TYPE_FIRST",
			["Value"] = 1,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488981760'),
		},
		[15] = {
			["ID"] = 15,
			["Const"] = "STAR_LEVEL_UP_TYPE_SECOND",
			["Value"] = 2,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488982016'),
		},
		[16] = {
			["ID"] = 16,
			["Const"] = "MAX_STAR_UP_LEVEL",
			["Value"] = 6,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488982272'),
		},
		[17] = {
			["ID"] = 17,
			["Const"] = "STAR_UP_CONFIG_ID_FACTOR",
			["Value"] = 100000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488982528'),
		},
		[18] = {
			["ID"] = 18,
			["Const"] = "JOIN_COMBAT_PROP_TRANS",
			["Value"] = 50,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488982784'),
		},
		[19] = {
			["ID"] = 19,
			["Const"] = "ASIST_COMBAT_PROP_TRANS",
			["Value"] = 25,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488983040'),
		},
		[97] = {
			["ID"] = 97,
			["Const"] = "JOIN_COMBAT_CE_TRANS",
			["Value"] = 50,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488983296'),
		},
		[98] = {
			["ID"] = 98,
			["Const"] = "ASIST_COMBAT_CE_TRANS",
			["Value"] = 25,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488983552'),
		},
		[20] = {
			["ID"] = 20,
			["Const"] = "EXP_INPUT_LONG_PRESS_DELAY",
			["Value"] = 200,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488983808'),
		},
		[21] = {
			["ID"] = 21,
			["Const"] = "EXP_INPUT_LONG_PRESS_ACCEL1",
			["Value"] = 1000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488984064'),
		},
		[22] = {
			["ID"] = 22,
			["Const"] = "EXP_INPUT_LONG_PRESS_ACCEL2",
			["Value"] = 3000,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488984320'),
		},
		[23] = {
			["ID"] = 23,
			["Const"] = "EXP_INPUT_LONG_PRESS_SPEED",
			["Value"] = 5,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488984576'),
		},
		[24] = {
			["ID"] = 24,
			["Const"] = "EXP_INPUT_LONG_PRESS_SPEED_ACCEL",
			["Value"] = 20,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488984832'),
		},
		[25] = {
			["ID"] = 25,
			["Const"] = "EXP_INPUT_LONG_PRESS_SPEED_ACCEL2",
			["Value"] = 100,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488985088'),
		},
		[26] = {
			["ID"] = 26,
			["Const"] = "FRIEND_ASIST_SLOT_MIN",
			["Value"] = 4,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488985344'),
		},
		[27] = {
			["ID"] = 27,
			["Const"] = "FRIEND_ASIST_SLOT_MAX",
			["Value"] = 4,
			["EnumDesc"] = Game.TableDataManager:GetLangStr('str_22747488985600'),
		},
	},
}

return TopData
