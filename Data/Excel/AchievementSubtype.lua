--
-- 表名: AchievementSubtype后处理
--

local TopData = {
    data = {
        [101] = {
            ['Achievements'] = {6220000, 6220001}, 
            ['Subclass_ID'] = 101, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_57795227420160'),
            ['Type'] = 1, 
        },
        [102] = {
            ['Achievements'] = {}, 
            ['Subclass_ID'] = 102, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781132544'),
            ['Type'] = 1, 
        },
        [103] = {
            ['Achievements'] = {}, 
            ['Subclass_ID'] = 103, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781132800'),
            ['Type'] = 1, 
        },
        [201] = {
            ['Achievements'] = {6221000}, 
            ['Subclass_ID'] = 201, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_1100585371136'),
            ['Type'] = 2, 
        },
        [202] = {
            ['Achievements'] = {6221001, 6221002, 6221003}, 
            ['Subclass_ID'] = 202, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781133312'),
            ['Type'] = 2, 
        },
        [203] = {
            ['Achievements'] = {}, 
            ['Subclass_ID'] = 203, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781133312'),
            ['Type'] = 2, 
        },
        [301] = {
            ['Achievements'] = {6222000, 6222001, 6222002, 6222003}, 
            ['Subclass_ID'] = 301, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_345476433408'),
            ['Type'] = 3, 
        },
        [302] = {
            ['Achievements'] = {}, 
            ['Subclass_ID'] = 302, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781134080'),
            ['Type'] = 3, 
        },
        [303] = {
            ['Achievements'] = {}, 
            ['Subclass_ID'] = 303, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781134336'),
            ['Type'] = 3, 
        },
        [401] = {
            ['Achievements'] = {6223006, 6223008, 6223007, 6223000, 6223001, 6223005, 6223004}, 
            ['Subclass_ID'] = 401, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Type'] = 4, 
        },
        [501] = {
            ['Achievements'] = {6224003, 6224000, 6224001, 6224002, 6224006, 6224005, 6224004}, 
            ['Subclass_ID'] = 501, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Type'] = 5, 
        },
        [601] = {
            ['Achievements'] = {6225002, 6225003}, 
            ['Subclass_ID'] = 601, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_34983850824960'),
            ['Type'] = 6, 
        },
        [602] = {
            ['Achievements'] = {6225001}, 
            ['Subclass_ID'] = 602, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_34983850814720'),
            ['Type'] = 6, 
        },
        [701] = {
            ['Achievements'] = {6226000}, 
            ['Subclass_ID'] = 701, 
            ['Subclass_Name'] = Game.TableDataManager:GetLangStr('str_138781135616'),
            ['Type'] = 7, 
        },
    }
}
return TopData