--
-- 表名: $FellowShowProperty.xlsx  页名：$PFellowPropBrief
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789317888'),
			["CalculateType"] = 1,
			["ShowProperty"] = {"pAtkMin", "pAtkMax"},
			["ShowType"] = 0,
		},
		[2] = {
			["ID"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"MaxHp"},
			["ShowType"] = 0,
		},
		[3] = {
			["ID"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789314816'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pDef"},
			["ShowType"] = 0,
		},
		[4] = {
			["ID"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789315072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mDef"},
			["ShowType"] = 0,
		},
	},
}

return TopData
