--
-- 表名: $PVPRank_PVP段位表.xlsx  页名：$PVPSeasonData_PVP赛季数据统计表
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783040512'),
			["DataEnum"] = "gameTimes",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_Kills_Sprite.UI_PVP_Icon_Kills_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 0,
		},
		[2] = {
			["ID"] = 2,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783040768'),
			["DataEnum"] = "winTimes",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfWins_Sprite.UI_PVP_Icon_NumberOfWins_Sprite",
			["ShowInChangeUtopian"] = 1,
			["ShowInChange"] = 1,
		},
		[3] = {
			["ID"] = 3,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783041024'),
			["DataEnum"] = "killNum",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_Kills_Sprite.UI_PVP_Icon_Kills_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 1,
		},
		[4] = {
			["ID"] = 4,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783041280'),
			["DataEnum"] = "dieNum",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_Kills_Sprite.UI_PVP_Icon_Kills_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 0,
		},
		[5] = {
			["ID"] = 5,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783041536'),
			["DataEnum"] = "kd",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_Kills_Sprite.UI_PVP_Icon_Kills_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 0,
		},
		[6] = {
			["ID"] = 6,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783041792'),
			["DataEnum"] = "curWinStreak",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfWins_Sprite.UI_PVP_Icon_NumberOfWins_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 0,
		},
		[7] = {
			["ID"] = 7,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783042048'),
			["DataEnum"] = "maxWinStreak",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfWins_Sprite.UI_PVP_Icon_NumberOfWins_Sprite",
			["ShowInChangeUtopian"] = 1,
			["ShowInChange"] = 1,
		},
		[8] = {
			["ID"] = 8,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783042304'),
			["DataEnum"] = "maxDamage",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfGames_Sprite.UI_PVP_Icon_NumberOfGames_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 0,
		},
		[9] = {
			["ID"] = 9,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783042560'),
			["DataEnum"] = "maxHeal",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfGames_Sprite.UI_PVP_Icon_NumberOfGames_Sprite",
			["ShowInChangeUtopian"] = 1,
			["ShowInChange"] = 0,
		},
		[10] = {
			["ID"] = 10,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783042816'),
			["DataEnum"] = "maxBear",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfGames_Sprite.UI_PVP_Icon_NumberOfGames_Sprite",
			["ShowInChangeUtopian"] = 1,
			["ShowInChange"] = 0,
		},
		[11] = {
			["ID"] = 11,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783043072'),
			["DataEnum"] = "mvpCnt",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfGames_Sprite.UI_PVP_Icon_NumberOfGames_Sprite",
			["ShowInChangeUtopian"] = 1,
			["ShowInChange"] = 1,
		},
		[12] = {
			["ID"] = 12,
			["DataName"] = Game.TableDataManager:GetLangStr('str_43431783043328'),
			["DataEnum"] = "avgKill",
			["IconResource"] = "/Game/Arts/UI_2/Resource/PVP_2/Atlas/Sprite01/UI_PVP_Icon_NumberOfGames_Sprite.UI_PVP_Icon_NumberOfGames_Sprite",
			["ShowInChangeUtopian"] = 0,
			["ShowInChange"] = 1,
		},
	},
}

return TopData
