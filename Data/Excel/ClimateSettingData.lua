--
-- 表名: $Climate_天气表.xlsx  页名：$ClimateSetting_天气设置
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Name"] = "Sunny",
			["Remark"] = Game.TableDataManager:GetLangStr('str_10102836823552'),
			["Temperature"] = 30,
			["Humidity"] = 10,
			["SolarIrradiance"] = 3.14,
			["BaseSunny"] = 1,
			["Cloud"] = {0, 0, 0, 0},
			["Fog"] = {0, 0, 0, 0},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "None",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Sunny.UI_Weather_Img_Sunny",
		},
		[2] = {
			["ID"] = 2,
			["Name"] = "HeavyRain",
			["Remark"] = Game.TableDataManager:GetLangStr('str_10102836823808'),
			["Temperature"] = 20,
			["Humidity"] = 40,
			["SolarIrradiance"] = 3,
			["BaseSunny"] = 1,
			["Cloud"] = {0, 0, 0, 0},
			["Fog"] = {0, 0, 0, 0},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {1, 1, 1, 1},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "Rain",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Rain.UI_Weather_Img_Rain",
		},
		[3] = {
			["ID"] = 3,
			["Name"] = "Foggy",
			["Remark"] = Game.TableDataManager:GetLangStr('str_10102836824064'),
			["Temperature"] = 20,
			["Humidity"] = 26,
			["SolarIrradiance"] = 2.8,
			["BaseSunny"] = 1,
			["Cloud"] = {0.1, 0.1, 0.1, 0.1},
			["Fog"] = {0.25, 0.25, 0.25, 0.25},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "Fog",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Fog.UI_Weather_Img_Fog",
		},
		[4] = {
			["ID"] = 4,
			["Name"] = "HeavyFog",
			["Remark"] = Game.TableDataManager:GetLangStr('str_10102836824320'),
			["Temperature"] = 20,
			["Humidity"] = 30,
			["SolarIrradiance"] = 2.5,
			["BaseSunny"] = 1,
			["Cloud"] = {0.2, 0.2, 0.2, 0.2},
			["Fog"] = {0.4, 0.4, 0.4, 0.4},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "Fog",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Fog.UI_Weather_Img_Fog",
		},
		[5] = {
			["ID"] = 5,
			["Name"] = "Cloudy",
			["Remark"] = Game.TableDataManager:GetLangStr('str_5704790313472'),
			["Temperature"] = 20,
			["Humidity"] = 12,
			["SolarIrradiance"] = 2.5,
			["BaseSunny"] = 1,
			["Cloud"] = {0.5, 0.5, 0.5, 0.5},
			["Fog"] = {0, 0, 0, 0},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "Cloudy",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Cloud.UI_Weather_Img_Cloud",
		},
		[6] = {
			["ID"] = 6,
			["Name"] = "DenseCloud",
			["Remark"] = Game.TableDataManager:GetLangStr('str_5704790313728'),
			["Temperature"] = 20,
			["Humidity"] = 25,
			["SolarIrradiance"] = 2,
			["BaseSunny"] = 1,
			["Cloud"] = {0.8, 0.8, 0.8, 0.8},
			["Fog"] = {0, 0, 0, 0},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "Cloudy",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Cloud.UI_Weather_Img_Cloud",
		},
		[13] = {
			["ID"] = 13,
			["Name"] = "Rainbow",
			["Remark"] = Game.TableDataManager:GetLangStr('str_5704790315520'),
			["Temperature"] = 30,
			["Humidity"] = 10,
			["SolarIrradiance"] = 3.14,
			["BaseSunny"] = 1,
			["Cloud"] = {0, 0, 0, 0},
			["Fog"] = {0, 0, 0, 0},
			["Wind"] = {0, 0, 0, 0},
			["Rain"] = {0, 0, 0, 0},
			["Snow"] = {0, 0, 0, 0},
			["Thunderstorm"] = {0, 0, 0, 0},
			["AkStateGroup"] = "State_Weather",
			["AkStateValue"] = "None",
			["Icon"] = "/Game/Arts/UI_2/Resource/Weather/Atlas/Texture01/UI_Weather_Img_Cloud.UI_Weather_Img_Cloud",
		},
	},
}

return TopData
