--
-- 表名: $NpcSPInteract.xlsx  页名：$interact
--

local TopData = {
	data = {
		[3] = {
			["BUTTONID"] = 3,
			["Content"] = Game.TableDataManager:GetLangStr('str_39789650774016'),
			["IconPath"] = "",
			["DiceID"] = 4,
			["Success"] = {},
			["Fail"] = {},
			["CD"] = -1,
		},
		[4] = {
			["BUTTONID"] = 4,
			["Content"] = Game.TableDataManager:GetLangStr('str_39789650774272'),
			["IconPath"] = "",
			["DiceID"] = 5,
			["Success"] = {},
			["Fail"] = {},
			["CD"] = -1,
		},
		[5] = {
			["BUTTONID"] = 5,
			["Content"] = Game.TableDataManager:GetLangStr('str_39789650774528'),
			["IconPath"] = "",
			["DiceID"] = 6,
			["Success"] = {},
			["Fail"] = {},
			["CD"] = -1,
		},
		[6] = {
			["BUTTONID"] = 6,
			["Content"] = Game.TableDataManager:GetLangStr('str_31822486439424'),
			["IconPath"] = "",
			["ItemSubmitID"] = 6250039,
			["Success"] = {},
			["Fail"] = {},
			["CD"] = 0,
		},
		[11] = {
			["BUTTONID"] = 11,
			["Content"] = Game.TableDataManager:GetLangStr('str_39789650776064'),
			["IconPath"] = "",
			["Success"] = {},
			["Fail"] = {},
			["CD"] = -1,
		},
		[12] = {
			["BUTTONID"] = 12,
			["Content"] = Game.TableDataManager:GetLangStr('str_29756607249152'),
			["IconPath"] = "",
			["Success"] = {},
			["Fail"] = {},
			["CD"] = 0,
		},
		[13] = {
			["BUTTONID"] = 13,
			["Content"] = Game.TableDataManager:GetLangStr('str_39789650776576'),
			["IconPath"] = "",
			["DiceID"] = 18,
			["Success"] = {},
			["Fail"] = {},
			["CD"] = -1,
		},
	},
}

return TopData
