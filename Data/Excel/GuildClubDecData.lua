--
-- 表名: $Guild_公会.xlsx  页名：$ClubDec_创建随机宣言
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["ClubType"] = 1,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742344704'),
		},
		[2] = {
			["Id"] = 2,
			["ClubType"] = 1,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742344960'),
		},
		[3] = {
			["Id"] = 3,
			["ClubType"] = 1,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742345216'),
		},
		[4] = {
			["Id"] = 4,
			["ClubType"] = 1,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742345472'),
		},
		[5] = {
			["Id"] = 5,
			["ClubType"] = 1,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742345728'),
		},
		[6] = {
			["Id"] = 6,
			["ClubType"] = 2,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742345984'),
		},
		[7] = {
			["Id"] = 7,
			["ClubType"] = 2,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742346240'),
		},
		[8] = {
			["Id"] = 8,
			["ClubType"] = 2,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742346496'),
		},
		[9] = {
			["Id"] = 9,
			["ClubType"] = 2,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742346752'),
		},
		[10] = {
			["Id"] = 10,
			["ClubType"] = 2,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742347008'),
		},
		[11] = {
			["Id"] = 11,
			["ClubType"] = 3,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742347264'),
		},
		[12] = {
			["Id"] = 12,
			["ClubType"] = 3,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742347520'),
		},
		[13] = {
			["Id"] = 13,
			["ClubType"] = 3,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742347776'),
		},
		[14] = {
			["Id"] = 14,
			["ClubType"] = 3,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742348032'),
		},
		[15] = {
			["Id"] = 15,
			["ClubType"] = 3,
			["TypeDeclaration"] = Game.TableDataManager:GetLangStr('str_27763742348288'),
		},
	},
}

return TopData
