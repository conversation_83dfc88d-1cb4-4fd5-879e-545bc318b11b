--
-- 表名: $Manor_庄园.xlsx  页名：$Setting
--

local TopData = {
	data = {
		["GridActorPath"] = "/Game/Blueprint/SceneActor/BP_ManorGrid.BP_ManorGrid_C",
		["ComponentBoxCollisionPath"] = "/Game/Blueprint/Manor/BP_BuildBoxTrigger.BP_BuildBoxTrigger_C",
		["ComponentMeshCollisionPath"] = "/Game/Blueprint/Manor/BP_BuildMeshTrigger.BP_BuildMeshTrigger_C",
		["FurnitureMeshPath"] = "/Game/Blueprint/Manor/BP_MeshFurniture.BP_MeshFurniture_C",
		["EditActorPath"] = "/Game/Blueprint/Manor/BP_ManorPlaceHolder.BP_ManorPlaceHolder_C",
		["ManorInstancePath"] = "/Game/Blueprint/Manor/BP_ManorInstance.BP_ManorInstance_C",
		["PlayerLayerPercent"] = 0.8,
		["CanDeleteStr"] = Game.TableDataManager:GetLangStr('str_34292092636416'),
		["CanBuildStr"] = Game.TableDataManager:GetLangStr('str_34292092636672'),
		["PlaceStr"] = Game.TableDataManager:GetLangStr('str_34292092636928'),
		["LineNiagaraPath"] = "/Game/Blueprint/Manor/BP_ManorLineEffect.BP_ManorLineEffect_C",
		["CameraDissolvePath"] = "/Game/Arts/MainMaterialLibrary/MF/MF_Environment/MF_SeeThrough/MPC_SeeThrough.MPC_SeeThrough",
		["PCGRoomStr"] = Game.TableDataManager:GetLangStr('str_34292092637696'),
		["PCGFloorStr"] = Game.TableDataManager:GetLangStr('str_34292092637952'),
		["PCGBtnStr"] = Game.TableDataManager:GetLangStr('str_34292092638208'),
		["PCGTitle"] = Game.TableDataManager:GetLangStr('str_34292092638464'),
		["SellTitleStr"] = Game.TableDataManager:GetLangStr('str_34292092638720'),
		["BuyTitleStr"] = Game.TableDataManager:GetLangStr('str_34292092638976'),
		["BuyNumStr"] = Game.TableDataManager:GetLangStr('str_34292092639232'),
		["BuyPriceStr"] = Game.TableDataManager:GetLangStr('str_34292092639488'),
		["BuyTotalPriceStr"] = Game.TableDataManager:GetLangStr('str_34292092639744'),
		["CancelStr"] = Game.TableDataManager:GetLangStr('str_34292092640000'),
		["ComfirmStr"] = Game.TableDataManager:GetLangStr('str_34292092640256'),
		["SpecialGetStr"] = Game.TableDataManager:GetLangStr('str_34292092640512'),
		["UnitPriceStr"] = Game.TableDataManager:GetLangStr('str_34292092640768'),
		["UnlockText1Str"] = Game.TableDataManager:GetLangStr('str_34292092641024'),
		["UnlockText2Str"] = Game.TableDataManager:GetLangStr('str_34292092641280'),
		["UnlockText3Str"] = Game.TableDataManager:GetLangStr('str_34292092641536'),
	},
}

return TopData
