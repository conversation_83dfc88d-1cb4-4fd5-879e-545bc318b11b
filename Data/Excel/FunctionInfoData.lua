--
-- 表名: FunctionInfoData后处理
--

local TopData = {
    FunctionEnumToLockData = {
        [1240000] = 1240000, 
        [1240100] = 1240100, 
        [1240101] = 1240101, 
        [1240102] = 1240102, 
        [1240103] = 1240103, 
        [1240104] = 1240104, 
        [1240105] = 1240105, 
        [1240106] = 1240106, 
        [1240107] = 1240107, 
        [1240200] = 1240200, 
        [1240201] = 1240201, 
        [1240203] = 1240203, 
        [1240204] = 1240204, 
        [1240205] = 1240205, 
        [1240206] = 1240206, 
        [1240207] = 1240207, 
        [1240208] = 1240208, 
        [1240209] = 1240209, 
        [1240210] = 1240210, 
        [1240300] = 1240300, 
        [1240301] = 1240301, 
        [1240400] = 1240400, 
        [1240500] = 1240500, 
        [1240600] = 1240600, 
        [1240700] = 1240700, 
        [1240800] = 1240800, 
        [1240900] = 1240900, 
        [1241000] = 1241000, 
        [1241001] = 1241001, 
        [1241100] = 1241100, 
        [1241200] = 1241200, 
        [1241300] = 1241300, 
        [1241400] = 1241400, 
        [1241500] = 1241500, 
        [1241600] = 1241600, 
        [1241601] = 1241601, 
        [1241602] = 1241602, 
        [1241603] = 1241603, 
        [1241700] = 1241700, 
        [1241800] = 1241800, 
        [1241801] = 1241801, 
        [1241802] = 1241802, 
        [1241803] = 1241803, 
        [1241804] = 1241804, 
        [1241805] = 1241805, 
        [1241810] = 1241810, 
        [1241811] = 1241811, 
        [1241820] = 1241820, 
        [1241821] = 1241821, 
        [1241822] = 1241822, 
        [1241823] = 1241823, 
        [1241824] = 1241824, 
        [1241825] = 1241825, 
        [1241826] = 1241826, 
        [1241827] = 1241827, 
        [1241828] = 1241828, 
        [1241829] = 1241829, 
        [1242000] = 1242000, 
        [1242100] = 1242100, 
        [1242200] = 1242200, 
        [1242300] = 1242300, 
        [1242400] = 1242400, 
        [1242500] = 1242500, 
        [1242600] = 1242600, 
        [1242700] = 1242700, 
        [1242701] = 1242701, 
        [1242702] = 1242702, 
        [1242703] = 1242703, 
        [1242704] = 1242704, 
        [1242800] = 1242800, 
        [1242900] = 1242900, 
        [1243000] = 1243000, 
        [1243100] = 1243100, 
        [1243101] = 1243101, 
        [1243200] = 1243200, 
        [1243300] = 1243300, 
        [1243400] = 1243400, 
        [1243501] = 1243501, 
        [1243502] = 1243502, 
        [1243600] = 1243600, 
        [1243601] = 1243601, 
        [1243602] = 1243602, 
        [1243603] = 1243603, 
        [1243701] = 1243701, 
        [1243702] = 1243702, 
        [1243800] = 1243800, 
    },
    FunctionLevelToLockData = {
        [1] = {1243701, 1240400, 1243100, 1243101, 1241200, 1242600, 1240200, 1240203, 1240204, 1240205, 1240206, 1240207, 1240208, 1240210, 1241100, 1242500, 1240600, 1240101, 1242400, 1243702, 1240500, 1243603, 1243601, 1243600, 1243300, 1243200, 1240000, 1243000, 1242900, 1241400, 1242800, 1241827, 1241603, 1242300}, 
        [10] = {1240201, 1243400}, 
        [20] = {1243501}, 
        [25] = {1240100, 1240107}, 
        [30] = {1240209, 1240106}, 
        [35] = {1240105}, 
        [39] = {1241001, 1243502, 1241500}, 
        [40] = {1241800, 1241801, 1241802, 1241803, 1241804, 1241805, 1241810, 1241811, 1241820, 1241821, 1241822, 1241823, 1240300, 1240301, 1241828, 1241829, 1240102, 1240103, 1240104, 1243602, 1241825, 1240900}, 
        [42] = {1240800, 1242200, 1240700, 1242100}, 
        [45] = {1241300, 1241700, 1243800, 1241600, 1241602, 1241601}, 
        [46] = {1242701, 1242703, 1242700}, 
        [48] = {1242702}, 
        [50] = {1241824}, 
        [55] = {1242704}, 
        [99] = {1241826, 1242000, 1241000}, 
    },
    FuntionQuestToLockData = {
        [60000309] = {1243100, 1243101, 1243000}, 
        [60000632] = {1240900}, 
        [99000922] = {1243200}, 
    },
    RobotUnlockFunction = {1240200, 1240201, 1240203, 1240204, 1240205, 1240206, 1240207, 1240208, 1240209, 1240210, 1241600, 1241603, 1241602, 1241601, 1242300, 1241500
    },
    data = {
        [1240000] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206467584'),
            ['ID'] = 1240000, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Menu', 
            ['ModuleEnum'] = 'MODULE_LOCK_MENU', 
            ['ModuleValue'] = 1240000, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240100] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_31886910949632'),
            ['ID'] = 1240100, 
            ['Icon'] = '', 
            ['Lvl'] = 25, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP', 
            ['ModuleValue'] = 1240100, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1240101] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206468096'),
            ['ID'] = 1240101, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_INFO', 
            ['ModuleValue'] = 1240101, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240102] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206468352'),
            ['ID'] = 1240102, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_Dungeon_Sprite.UI_NPC_Icon_Dungeon_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_REFORGE', 
            ['ModuleValue'] = 1240102, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 0, 
        },
        [1240103] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206468608'),
            ['ID'] = 1240103, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_POS_REFORGE', 
            ['ModuleValue'] = 1240103, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1240104] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206468864'),
            ['ID'] = 1240104, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_DEF_EQUIP', 
            ['ModuleValue'] = 1240104, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1240105] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206469120'),
            ['ID'] = 1240105, 
            ['Icon'] = '', 
            ['Lvl'] = 35, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_RANDOM', 
            ['ModuleValue'] = 1240105, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240106] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206469376'),
            ['ID'] = 1240106, 
            ['Icon'] = '', 
            ['Lvl'] = 30, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_ADVANCE', 
            ['ModuleValue'] = 1240106, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240107] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206469632'),
            ['ID'] = 1240107, 
            ['Icon'] = '', 
            ['Lvl'] = 25, 
            ['Module'] = 'Equip', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIP_ENHANCE', 
            ['ModuleValue'] = 1240107, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240200] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206469888'),
            ['ID'] = 1240200, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL', 
            ['ModuleValue'] = 1240200, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1240201] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206470144'),
            ['ID'] = 1240201, 
            ['Icon'] = '', 
            ['Lvl'] = 10, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM', 
            ['ModuleValue'] = 1240201, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240203] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206470656'),
            ['ID'] = 1240203, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM_TAB1', 
            ['ModuleValue'] = 1240203, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240204] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206470912'),
            ['ID'] = 1240204, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM_TAB2', 
            ['ModuleValue'] = 1240204, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240205] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206471168'),
            ['ID'] = 1240205, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM_TAB3', 
            ['ModuleValue'] = 1240205, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240206] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206471424'),
            ['ID'] = 1240206, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM_TAB4', 
            ['ModuleValue'] = 1240206, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240207] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206471680'),
            ['ID'] = 1240207, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Skill', 
            ['ModuleEnum'] = 'MODULE_LOCK_SKILL_CUSTOM_TAB5', 
            ['ModuleValue'] = 1240207, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240208] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206471936'),
            ['ID'] = 1240208, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Element', 
            ['ModuleEnum'] = 'MODULE_LOCK_ELEMENT_MAIN', 
            ['ModuleValue'] = 1240208, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240209] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206472192'),
            ['ID'] = 1240209, 
            ['Icon'] = '', 
            ['Lvl'] = 30, 
            ['Module'] = 'Element', 
            ['ModuleEnum'] = 'MODULE_LOCK_ELEMENT_VICE', 
            ['ModuleValue'] = 1240209, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240210] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206472448'),
            ['ID'] = 1240210, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'SkillQ', 
            ['ModuleEnum'] = 'MODULE_LOCK_ROLEMECHANISM_Q', 
            ['ModuleValue'] = 1240210, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1240300] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206472704'),
            ['ID'] = 1240300, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Menu/Atlas/Sprite01/UI_Menu_Icon_Sealed_Sprite.UI_Menu_Icon_Sealed_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Sealed', 
            ['ModuleEnum'] = 'MODULE_LOCK_SEALED', 
            ['ModuleValue'] = 1240300, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1240301] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206472960'),
            ['ID'] = 1240301, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'Sealed', 
            ['ModuleEnum'] = 'MODULE_LOCK_SEALED_RANDOM', 
            ['ModuleValue'] = 1240301, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1240400] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206473216'),
            ['ID'] = 1240400, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Character', 
            ['ModuleEnum'] = 'MODULE_LOCK_CHARACTER', 
            ['ModuleValue'] = 1240400, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1240500] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206473472'),
            ['ID'] = 1240500, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Task', 
            ['ModuleEnum'] = 'MODULE_LOCK_TASK', 
            ['ModuleValue'] = 1240500, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1240600] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206473728'),
            ['ID'] = 1240600, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Fashion', 
            ['ModuleEnum'] = 'MODULE_LOCK_FASHION_SHOP', 
            ['ModuleValue'] = 1240600, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1240700] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206473984'),
            ['ID'] = 1240700, 
            ['Icon'] = '', 
            ['Lvl'] = 42, 
            ['Module'] = 'Gacha', 
            ['ModuleEnum'] = 'MODULE_LOCK_GACHA', 
            ['ModuleValue'] = 1240700, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1240800] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206474240'),
            ['ID'] = 1240800, 
            ['Icon'] = '', 
            ['Lvl'] = 42, 
            ['Module'] = 'Fellow', 
            ['ModuleEnum'] = 'MODULE_LOCK_FELLOW', 
            ['ModuleValue'] = 1240800, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1240900] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206474496'),
            ['ID'] = 1240900, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'Sequence', 
            ['ModuleEnum'] = 'MODULE_LOCK_SEQUENCE', 
            ['ModuleValue'] = 1240900, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 60000632, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1241000] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_55182813570816'),
            ['ID'] = 1241000, 
            ['Icon'] = '', 
            ['Lvl'] = 99, 
            ['Module'] = 'ROLEPLAY', 
            ['ModuleEnum'] = 'MODULE_LOCK_ROLEPLAY', 
            ['ModuleValue'] = 1241000, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1241001] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206475008'),
            ['ID'] = 1241001, 
            ['Icon'] = '', 
            ['Lvl'] = 39, 
            ['Module'] = 'ROLEPLAY', 
            ['ModuleEnum'] = 'MODULE_LOCK_PHARAMCIST', 
            ['ModuleValue'] = 1241001, 
            ['ReminderId'] = 6405003, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1241100] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206475264'),
            ['ID'] = 1241100, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Achevement', 
            ['ModuleEnum'] = 'MODULE_LOCK_ACHIEVEMENT', 
            ['ModuleValue'] = 1241100, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241200] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206475520'),
            ['ID'] = 1241200, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Collectibles', 
            ['ModuleEnum'] = 'MODULE_LOCK_COLLECTIBLES', 
            ['ModuleValue'] = 1241200, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241300] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206475776'),
            ['ID'] = 1241300, 
            ['Icon'] = '', 
            ['Lvl'] = 45, 
            ['Module'] = 'RankingList', 
            ['ModuleEnum'] = 'MODULE_LOCK_RANKINGLIST', 
            ['ModuleValue'] = 1241300, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1241400] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206476032'),
            ['ID'] = 1241400, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Setting', 
            ['ModuleEnum'] = 'MODULE_LOCK_SETTING', 
            ['ModuleValue'] = 1241400, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1241500] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_31817923036672'),
            ['ID'] = 1241500, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_Dungeon_Sprite.UI_NPC_Icon_Dungeon_Sprite', 
            ['Lvl'] = 39, 
            ['Module'] = 'Dungeon', 
            ['ModuleEnum'] = 'MODULE_LOCK_DUNGEON', 
            ['ModuleValue'] = 1241500, 
            ['ReminderId'] = 6401720, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1241600] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_57656446289408'),
            ['ID'] = 1241600, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_Dungeon_Sprite.UI_NPC_Icon_Dungeon_Sprite', 
            ['Lvl'] = 45, 
            ['Module'] = 'Combat', 
            ['ModuleEnum'] = 'MODULE_LOCK_PVP', 
            ['ModuleValue'] = 1241600, 
            ['ReminderId'] = 6401719, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1241601] = {
            ['FunctionInfo'] = '3v3', 
            ['ID'] = 1241601, 
            ['Icon'] = '', 
            ['Lvl'] = 45, 
            ['Module'] = 'Combat', 
            ['ModuleEnum'] = 'MODULE_LOCK_COMBAT_3V3', 
            ['ModuleValue'] = 1241601, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1241602] = {
            ['FunctionInfo'] = '5V5', 
            ['ID'] = 1241602, 
            ['Icon'] = '', 
            ['Lvl'] = 45, 
            ['Module'] = 'Combat', 
            ['ModuleEnum'] = 'MODULE_LOCK_COMBAT_5V5', 
            ['ModuleValue'] = 1241602, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1241603] = {
            ['FunctionInfo'] = '50v50', 
            ['ID'] = 1241603, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Combat', 
            ['ModuleEnum'] = 'MODULE_LOCK_COMBAT_50V50', 
            ['ModuleValue'] = 1241603, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1241700] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206477568'),
            ['ID'] = 1241700, 
            ['Icon'] = '', 
            ['Lvl'] = 45, 
            ['Module'] = 'TowerClimb', 
            ['ModuleEnum'] = 'MODULE_LOCK_TOWERCLIMB', 
            ['ModuleValue'] = 1241700, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1241800] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206477824'),
            ['ID'] = 1241800, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_HUD_SHOP', 
            ['ModuleValue'] = 1241800, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 1, 
        },
        [1241801] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206478080'),
            ['ID'] = 1241801, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_shop_Sprite.UI_Trade_Btn_shop_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'ShoppingMall', 
            ['ModuleEnum'] = 'MODULE_LOCK_SHOPPING_MALL', 
            ['ModuleValue'] = 1241801, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241802] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_31817923038464'),
            ['ID'] = 1241802, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_SpriteUI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_SHOP', 
            ['ModuleValue'] = 1241802, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241803] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206481664'),
            ['ID'] = 1241803, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_Exchange_Sprite.UI_Trade_Btn_Exchange_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Exchange', 
            ['ModuleEnum'] = 'MODULE_LOCK_EXCHANGE_HOUSE', 
            ['ModuleValue'] = 1241803, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241804] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206481920'),
            ['ID'] = 1241804, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'CashMarket', 
            ['ModuleEnum'] = 'MODULE_LOCK_CASHMARKET', 
            ['ModuleValue'] = 1241804, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241805] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206482176'),
            ['ID'] = 1241805, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Icon_auction_Sprite.UI_Trade_Icon_auction_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Auction', 
            ['ModuleEnum'] = 'MODULE_LOCK_AUCTION', 
            ['ModuleValue'] = 1241805, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241810] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_54426899318272'),
            ['ID'] = 1241810, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_shop_Sprite.UI_Trade_Btn_shop_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'ShoppingMall', 
            ['ModuleEnum'] = 'MODULE_LOCK_SM_BANGJIN', 
            ['ModuleValue'] = 1241810, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241811] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_54426899318528'),
            ['ID'] = 1241811, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_shop_Sprite.UI_Trade_Btn_shop_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'ShoppingMall', 
            ['ModuleEnum'] = 'MODULE_LOCK_SM_JINBANG', 
            ['ModuleValue'] = 1241811, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241820] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206479104'),
            ['ID'] = 1241820, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIPMENT_SHOP', 
            ['ModuleValue'] = 1241820, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241821] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206479360'),
            ['ID'] = 1241821, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_EQUIPMENT_REFORGE_SHOP', 
            ['ModuleValue'] = 1241821, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241822] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206479616'),
            ['ID'] = 1241822, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_DRUG_SHOP', 
            ['ModuleValue'] = 1241822, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241823] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206479872'),
            ['ID'] = 1241823, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Guild/Atlas/Sprite01/UI_Guild_Icon_Shop_Sprite.UI_Guild_Icon_Shop_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILD_SHOP', 
            ['ModuleValue'] = 1241823, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 2, 
        },
        [1241824] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206480128'),
            ['ID'] = 1241824, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 50, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_SHOP_5', 
            ['ModuleValue'] = 1241824, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241825] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206480384'),
            ['ID'] = 1241825, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_SHOP_6', 
            ['ModuleValue'] = 1241825, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241826] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206480640'),
            ['ID'] = 1241826, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 99, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_SHOP_GACHA', 
            ['ModuleValue'] = 1241826, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241827] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206480896'),
            ['ID'] = 1241827, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 1, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_ClOTH_SHOP', 
            ['ModuleValue'] = 1241827, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241828] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206481152'),
            ['ID'] = 1241828, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_GIFT_SHOP', 
            ['ModuleValue'] = 1241828, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1241829] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206481408'),
            ['ID'] = 1241829, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Trade/Atlas/Sprite01/UI_Trade_Btn_department_store_Sprite.UI_Trade_Btn_department_store_Sprite', 
            ['Lvl'] = 40, 
            ['Module'] = 'Shop', 
            ['ModuleEnum'] = 'MODULE_LOCK_TAROTTEAM_SHOP', 
            ['ModuleValue'] = 1241829, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242000] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_52846351356160'),
            ['ID'] = 1242000, 
            ['Icon'] = '', 
            ['Lvl'] = 99, 
            ['Module'] = 'Schedule', 
            ['ModuleEnum'] = 'MODULE_LOCK_SCHEDULE', 
            ['ModuleValue'] = 1242000, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242100] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206482944'),
            ['ID'] = 1242100, 
            ['Icon'] = '', 
            ['Lvl'] = 42, 
            ['Module'] = 'Photograph', 
            ['ModuleEnum'] = 'MODULE_LOCK_PHOTOGRAPH', 
            ['ModuleValue'] = 1242100, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1242200] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206483200'),
            ['ID'] = 1242200, 
            ['Icon'] = '', 
            ['Lvl'] = 42, 
            ['Module'] = 'SocialAction', 
            ['ModuleEnum'] = 'MODULE_LOCK_SOCIAL_ACTION', 
            ['ModuleValue'] = 1242200, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1242300] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_4811168679680'),
            ['ID'] = 1242300, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Team', 
            ['ModuleEnum'] = 'MODULE_LOCK_TEAM', 
            ['ModuleValue'] = 1242300, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1242400] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206483712'),
            ['ID'] = 1242400, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Chat', 
            ['ModuleEnum'] = 'MODULE_LOCK_CHAT', 
            ['ModuleValue'] = 1242400, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1242500] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_36971883794176'),
            ['ID'] = 1242500, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Friend', 
            ['ModuleEnum'] = 'MODULE_LOCK_FRIEND', 
            ['ModuleValue'] = 1242500, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 1, 
        },
        [1242600] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206484224'),
            ['ID'] = 1242600, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = '', 
            ['ModuleEnum'] = 'MODULE_LOCK_MAIL', 
            ['ModuleValue'] = 1242600, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242700] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_33261837357824'),
            ['ID'] = 1242700, 
            ['Icon'] = '', 
            ['Lvl'] = 46, 
            ['Module'] = 'Guild', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILD', 
            ['ModuleValue'] = 1242700, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 6405927, 
            ['UnlockType'] = 3, 
        },
        [1242701] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_52572010317824'),
            ['ID'] = 1242701, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Guild/Atlas/Sprite01/UI_Guild_Icon_Build_Sprite.UI_Guild_Icon_Build_Sprite', 
            ['Lvl'] = 46, 
            ['Module'] = 'Guild', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILD_BUILD', 
            ['ModuleValue'] = 1242701, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242702] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_52572010318336'),
            ['ID'] = 1242702, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Guild/Atlas/Sprite01/UI_Guild_Icon_Practice_Sprite.UI_Guild_Icon_Practice_Sprite', 
            ['Lvl'] = 48, 
            ['Module'] = 'Guild', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILD_PRACTICE', 
            ['ModuleValue'] = 1242702, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242703] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206485248'),
            ['ID'] = 1242703, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/Guild/Atlas/Sprite01/UI_Guild_Icon_Wages_Sprite.UI_Guild_Icon_Wages_Sprite', 
            ['Lvl'] = 46, 
            ['Module'] = 'Guild', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILD_WAGES', 
            ['ModuleValue'] = 1242703, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1242704] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_29069949274112'),
            ['ID'] = 1242704, 
            ['Icon'] = '', 
            ['Lvl'] = 55, 
            ['Module'] = 'GuildLeague', 
            ['ModuleEnum'] = 'MODULE_LOCK_GUILDLEAGUE', 
            ['ModuleValue'] = 1242704, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1242800] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206485760'),
            ['ID'] = 1242800, 
            ['Icon'] = '/Game/Arts/UI_2/Resource/NPC/Atlas/Sprite01/UI_NPC_Icon_Dungeon_Sprite.UI_NPC_Icon_Dungeon_Sprite', 
            ['Lvl'] = 1, 
            ['Module'] = 'CrossWordPuzzle', 
            ['ModuleEnum'] = 'MODULE_LOCK_CROSS_WORD_PUZZLE', 
            ['ModuleValue'] = 1242800, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1242900] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206486016'),
            ['ID'] = 1242900, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'ItemSubmit', 
            ['ModuleEnum'] = 'MODULE_LOCK_ITEMSUBMIT', 
            ['ModuleValue'] = 1242900, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243000] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206486272'),
            ['ID'] = 1243000, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'LeadMode', 
            ['ModuleEnum'] = 'MODULE_LOCK_LEADMODE', 
            ['ModuleValue'] = 1243000, 
            ['ReminderId'] = 0, 
            ['RingID'] = 980016, 
            ['Task'] = 60000309, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243100] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206486528'),
            ['ID'] = 1243100, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'AutoNavigation', 
            ['ModuleEnum'] = 'MODULE_LOCK_AUTONAVIGATION', 
            ['ModuleValue'] = 1243100, 
            ['ReminderId'] = 6401729, 
            ['RingID'] = 980016, 
            ['Task'] = 60000309, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243101] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206486784'),
            ['ID'] = 1243101, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'AutoNavigation_Mew', 
            ['ModuleEnum'] = 'MODULE_LOCK_AUTONAVIGATION_MEW', 
            ['ModuleValue'] = 1243101, 
            ['ReminderId'] = 0, 
            ['RingID'] = 980016, 
            ['Task'] = 60000309, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243200] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206487040'),
            ['ID'] = 1243200, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Home', 
            ['ModuleEnum'] = 'MODULE_LOCK_HOME', 
            ['ModuleValue'] = 1243200, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 99000922, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243300] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206487040'),
            ['ID'] = 1243300, 
            ['Icon'] = '', 
            ['Lvl'] = 1, 
            ['Module'] = 'Moments', 
            ['ModuleEnum'] = 'MOUDLE_LOCK_MOMENT', 
            ['ModuleValue'] = 1243300, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243400] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206487552'),
            ['ID'] = 1243400, 
            ['Icon'] = '', 
            ['Lvl'] = 10, 
            ['Module'] = 'TarotTeam', 
            ['ModuleEnum'] = 'MODULE_LOCK_TAROTTEAM', 
            ['ModuleValue'] = 1243400, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 3, 
        },
        [1243501] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206487808'),
            ['ID'] = 1243501, 
            ['Icon'] = '', 
            ['Lvl'] = 20, 
            ['Module'] = 'Score', 
            ['ModuleEnum'] = 'MODULE_LOCK_CE_SCORE', 
            ['ModuleValue'] = 1243501, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243502] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206487808'),
            ['ID'] = 1243502, 
            ['Icon'] = '', 
            ['Lvl'] = 39, 
            ['Module'] = 'Property', 
            ['ModuleEnum'] = 'MODULE_LOCK_CE_property', 
            ['ModuleValue'] = 1243502, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243600] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206488320'),
            ['ID'] = 1243600, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'PlotRecap', 
            ['ModuleEnum'] = 'MODULE_LOCK_PLOTCAP', 
            ['ModuleValue'] = 1243600, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243601] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206488576'),
            ['ID'] = 1243601, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'PlotRecap', 
            ['ModuleEnum'] = 'MODULE_LOCK_FACTMAINRECAP', 
            ['ModuleValue'] = 1243601, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243602] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206488832'),
            ['ID'] = 1243602, 
            ['Icon'] = '', 
            ['Lvl'] = 40, 
            ['Module'] = 'PlotRecap', 
            ['ModuleEnum'] = 'MODULE_LOCK_MISTMAINRECAP', 
            ['ModuleValue'] = 1243602, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243603] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206489088'),
            ['ID'] = 1243603, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'PlotRecap', 
            ['ModuleEnum'] = 'MODULE_LOCK_SIDEQUESTRECAP', 
            ['ModuleValue'] = 1243603, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243701] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206489344'),
            ['ID'] = 1243701, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'MENU', 
            ['ModuleEnum'] = 'MODULE_LOCK_GETOUT', 
            ['ModuleValue'] = 1243701, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243702] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206489600'),
            ['ID'] = 1243702, 
            ['Icon'] = '', 
            ['Lvl'] = 0, 
            ['Module'] = 'MENU', 
            ['ModuleEnum'] = 'MODULE_LOCK_REPORT', 
            ['ModuleValue'] = 1243702, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
        [1243800] = {
            ['FunctionInfo'] = Game.TableDataManager:GetLangStr('str_26114206489856'),
            ['ID'] = 1243800, 
            ['Icon'] = '', 
            ['Lvl'] = 45, 
            ['Module'] = 'MOUNT', 
            ['ModuleEnum'] = 'MOUDLE_LOCK_MOUNT', 
            ['ModuleValue'] = 1243800, 
            ['ReminderId'] = 0, 
            ['RingID'] = 0, 
            ['Task'] = 0, 
            ['UnlockTip'] = 0, 
            ['UnlockType'] = 0, 
        },
    }
}
return TopData