--
-- 表名: $NewsTicker_跑马灯.xlsx  页名：$NewsTicker_跑马灯
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Enum"] = "LOUD_SPEAKER_ITEM_ID1",
			["MarqueeType"] = 1,
			["NewsTickerText"] = Game.TableDataManager:GetLangStr('str_37866042295808'),
			["CycleTimes"] = 2,
			["GapTime"] = 1,
			["Priority"] = 3,
			["DisableMapMarquee"] = {},
		},
		[2] = {
			["ID"] = 2,
			["Enum"] = "LOUD_SPEAKER_ITEM_ID2",
			["MarqueeType"] = 2,
			["NewsTickerText"] = Game.TableDataManager:GetLangStr('str_37866042295808'),
			["CycleTimes"] = 2,
			["GapTime"] = 1,
			["Priority"] = 2,
			["DisableMapMarquee"] = {},
		},
		[3] = {
			["ID"] = 3,
			["Enum"] = "LOUD_SPEAKER_ITEM_ID3",
			["MarqueeType"] = 3,
			["NewsTickerText"] = Game.TableDataManager:GetLangStr('str_37866042295808'),
			["CycleTimes"] = 2,
			["GapTime"] = 1,
			["Priority"] = 1,
			["DisableMapMarquee"] = {},
		},
		[4] = {
			["ID"] = 4,
			["Enum"] = "WORLD_CHANNEL_QUIZ_NOTICE",
			["MarqueeType"] = 4,
			["NewsTickerText"] = Game.TableDataManager:GetLangStr('str_62192200189440'),
			["CycleTimes"] = 1,
			["GapTime"] = 1,
			["Priority"] = 0,
			["DisableMapMarquee"] = {},
		},
		[5] = {
			["ID"] = 5,
			["Enum"] = "WORLD_CHANNEL_QUIZ_START",
			["MarqueeType"] = 4,
			["NewsTickerText"] = Game.TableDataManager:GetLangStr('str_37866042296832'),
			["CycleTimes"] = 1,
			["GapTime"] = 1,
			["Priority"] = 0,
			["DisableMapMarquee"] = {},
		},
	},
}

return TopData
