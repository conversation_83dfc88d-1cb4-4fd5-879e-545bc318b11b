--
-- 表名: RolePlayPropertyData后处理
--

local TopData = {
    data = {
        [101] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_46937013224960'),
            ['ID'] = 101, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14019578562304'),
            ['TalentToken'] = 2001000, 
            ['Talents'] = {101001, 101002, 101003, 101004, 101005, 101006, 101007, 101008, 101009, 101010}, 
            ['Token'] = 2002518, 
        },
        [102] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_46937013225216'),
            ['ID'] = 102, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14019578562560'),
            ['TalentToken'] = 2001001, 
            ['Talents'] = {102001, 102002, 102003, 102004, 102005, 102006, 102007, 102008, 102009, 102010}, 
            ['Token'] = 2002519, 
        },
        [103] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_46937013225472'),
            ['ID'] = 103, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14019578562816'),
            ['TalentToken'] = 2001002, 
            ['Talents'] = {103001, 103002, 103003, 103004, 103005, 103006, 103007, 103008, 103009, 103010}, 
            ['Token'] = 2002520, 
        },
        [104] = {
            ['Desc'] = Game.TableDataManager:GetLangStr('str_46937013225728'),
            ['ID'] = 104, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14019578563072'),
            ['TalentToken'] = 2001003, 
            ['Talents'] = {}, 
            ['Token'] = 2002521, 
        },
    }
}
return TopData