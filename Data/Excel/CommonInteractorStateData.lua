--
-- 表名: $CommonInteractor_通用交互物.xlsx  页名：$InteractorState_状态
--

local TopData = {
	data = {
		[1000101] = {
			["ID"] = 1000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574208'),
			["Desc"] = "",
			["NextState"] = {{100010201, 1000102}},
			["ActionList"] = {100010101, 100010201},
			["EnableDefaultProps"] = true,
		},
		[1000102] = {
			["ID"] = 1000102,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574464'),
			["Desc"] = "",
			["NextState"] = {{100010301, 1000103}},
			["ActionList"] = {100010301},
			["EnableDefaultProps"] = true,
		},
		[1000103] = {
			["ID"] = 1000103,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574720'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = true,
		},
		[1000104] = {
			["ID"] = 1000104,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574208'),
			["Desc"] = "",
			["NextState"] = {{100010401, 1000105}},
			["ActionList"] = {100010401},
			["EnableDefaultProps"] = false,
		},
		[1000105] = {
			["ID"] = 1000105,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574464'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100010401, 100010501, 100010601},
			["EnableDefaultProps"] = true,
		},
		[1000601] = {
			["ID"] = 1000601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100060101},
			["EnableDefaultProps"] = true,
		},
		[1000801] = {
			["ID"] = 1000801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575744'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100080101, 100080102},
			["EnableDefaultProps"] = true,
		},
		[1000901] = {
			["ID"] = 1000901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812576000'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100090101, 100090102},
			["EnableDefaultProps"] = true,
		},
		[1001001] = {
			["ID"] = 1001001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812576256'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100090101, 100100101},
			["EnableDefaultProps"] = true,
		},
		[1001101] = {
			["ID"] = 1001101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812576512'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100110101},
			["EnableDefaultProps"] = true,
		},
		[1001201] = {
			["ID"] = 1001201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812576768'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100120101, 100120102},
			["EnableDefaultProps"] = true,
		},
		[1001301] = {
			["ID"] = 1001301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812577024'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100130101},
			["EnableDefaultProps"] = true,
		},
		[1001401] = {
			["ID"] = 1001401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812577280'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100140101, 100140102},
			["EnableDefaultProps"] = true,
		},
		[1001501] = {
			["ID"] = 1001501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812577536'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100150101},
			["EnableDefaultProps"] = true,
		},
		[1001601] = {
			["ID"] = 1001601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812577792'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100160101, 100160102},
			["EnableDefaultProps"] = true,
		},
		[1001701] = {
			["ID"] = 1001701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812578048'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100170101},
			["EnableDefaultProps"] = true,
		},
		[1001801] = {
			["ID"] = 1001801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812578304'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100180101},
			["EnableDefaultProps"] = true,
		},
		[1001901] = {
			["ID"] = 1001901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812578560'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100190101, 100190102},
			["EnableDefaultProps"] = true,
		},
		[1002001] = {
			["ID"] = 1002001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812578816'),
			["Desc"] = "",
			["NextState"] = {{100200102, 1002002}},
			["ActionList"] = {100200101, 100200102},
			["EnableDefaultProps"] = false,
		},
		[1002002] = {
			["ID"] = 1002002,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579072'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100200201},
			["EnableDefaultProps"] = true,
		},
		[1002101] = {
			["ID"] = 1002101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579328'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100210101},
			["EnableDefaultProps"] = true,
		},
		[1002201] = {
			["ID"] = 1002201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100220101},
			["EnableDefaultProps"] = true,
		},
		[1002301] = {
			["ID"] = 1002301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100230101},
			["EnableDefaultProps"] = true,
		},
		[1002401] = {
			["ID"] = 1002401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100240101},
			["EnableDefaultProps"] = true,
		},
		[1002501] = {
			["ID"] = 1002501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = true,
		},
		[1002601] = {
			["ID"] = 1002601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100260101},
			["EnableDefaultProps"] = true,
		},
		[1002701] = {
			["ID"] = 1002701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = true,
		},
		[1002801] = {
			["ID"] = 1002801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100280101, 100280102, 100280103},
			["EnableDefaultProps"] = true,
		},
		[1002901] = {
			["ID"] = 1002901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100290101},
			["EnableDefaultProps"] = true,
		},
		[1003001] = {
			["ID"] = 1003001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100300101},
			["EnableDefaultProps"] = true,
		},
		[1003101] = {
			["ID"] = 1003101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100310101},
			["EnableDefaultProps"] = true,
		},
		[1003201] = {
			["ID"] = 1003201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100320101},
			["EnableDefaultProps"] = true,
		},
		[1003301] = {
			["ID"] = 1003301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100330101, 100330103, 100330104},
			["EnableDefaultProps"] = true,
		},
		[1003401] = {
			["ID"] = 1003401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100340101},
			["EnableDefaultProps"] = true,
		},
		[1003501] = {
			["ID"] = 1003501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100350101},
			["EnableDefaultProps"] = true,
		},
		[1003601] = {
			["ID"] = 1003601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100360101},
			["EnableDefaultProps"] = true,
		},
		[1003801] = {
			["ID"] = 1003801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100380101},
			["EnableDefaultProps"] = true,
		},
		[1003901] = {
			["ID"] = 1003901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100390101},
			["EnableDefaultProps"] = true,
		},
		[1004001] = {
			["ID"] = 1004001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812579584'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {100400101},
			["EnableDefaultProps"] = true,
		},
		[1004101] = {
			["ID"] = 1004101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812584192'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812584192'),
			["NextState"] = {},
			["ActionList"] = {100410101},
			["EnableDefaultProps"] = false,
		},
		[1200101] = {
			["ID"] = 1200101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812584448'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {120010101, 120010102},
			["EnableDefaultProps"] = true,
		},
		[1210101] = {
			["ID"] = 1210101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812584448'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {121010101},
			["EnableDefaultProps"] = true,
		},
		[1210102] = {
			["ID"] = 1210102,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812584448'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {121010201},
			["EnableDefaultProps"] = true,
		},
		[2000101] = {
			["ID"] = 2000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {200010102, 200010103},
			["EnableDefaultProps"] = true,
		},
		[2000201] = {
			["ID"] = 2000201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {200010102, 200010103},
			["EnableDefaultProps"] = true,
		},
		[2000301] = {
			["ID"] = 2000301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {200030102, 200030103},
			["EnableDefaultProps"] = true,
		},
		[2000401] = {
			["ID"] = 2000401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {200040101, 200040102, 200040103, 200040104},
			["EnableDefaultProps"] = true,
		},
		[2000501] = {
			["ID"] = 2000501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {200050101, 200050102},
			["EnableDefaultProps"] = true,
		},
		[3000101] = {
			["ID"] = 3000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812586496'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[3000102] = {
			["ID"] = 3000102,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812586752'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {300010201},
			["EnableDefaultProps"] = false,
		},
		[3000201] = {
			["ID"] = 3000201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812587008'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {300020202},
			["EnableDefaultProps"] = false,
		},
		[3000202] = {
			["ID"] = 3000202,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812587264'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {300020201},
			["EnableDefaultProps"] = false,
		},
		[3000301] = {
			["ID"] = 3000301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812587520'),
			["Desc"] = "",
			["NextState"] = {{300030102, 3000302}},
			["ActionList"] = {300030101, 300030102},
			["EnableDefaultProps"] = false,
		},
		[3000302] = {
			["ID"] = 3000302,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812587776'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {300030201},
			["EnableDefaultProps"] = false,
		},
		[4000101] = {
			["ID"] = 4000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812574208'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {400010101},
			["EnableDefaultProps"] = true,
		},
		[4000201] = {
			["ID"] = 4000201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812588288'),
			["Desc"] = "",
			["NextState"] = {{400020102, 4000301}},
			["ActionList"] = {400020101, 400020102},
			["EnableDefaultProps"] = true,
		},
		[4000301] = {
			["ID"] = 4000301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812588544'),
			["Desc"] = "",
			["NextState"] = {{400030102, 4000201}},
			["ActionList"] = {400030101, 400030102},
			["EnableDefaultProps"] = true,
		},
		[4000401] = {
			["ID"] = 4000401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812588800'),
			["Desc"] = "",
			["NextState"] = {{400040102, 4000402}},
			["ActionList"] = {400040101, 400040102},
			["EnableDefaultProps"] = true,
		},
		[4000402] = {
			["ID"] = 4000402,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812589056'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {400040201},
			["EnableDefaultProps"] = false,
		},
		[4000501] = {
			["ID"] = 4000501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812589312'),
			["Desc"] = "",
			["NextState"] = {{400050102, 4000502}},
			["ActionList"] = {400050101, 400050102},
			["EnableDefaultProps"] = true,
		},
		[4000502] = {
			["ID"] = 4000502,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812589568'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {400050201},
			["EnableDefaultProps"] = false,
		},
		[5000101] = {
			["ID"] = 5000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812589824'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081025280'),
			["NextState"] = {},
			["ActionList"] = {500010101},
			["EnableDefaultProps"] = false,
		},
		[5000102] = {
			["ID"] = 5000102,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812590080'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081025536'),
			["NextState"] = {},
			["ActionList"] = {500010102},
			["EnableDefaultProps"] = false,
		},
		[5000201] = {
			["ID"] = 5000201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812590336'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081025792'),
			["NextState"] = {},
			["ActionList"] = {500020101},
			["EnableDefaultProps"] = false,
		},
		[5000301] = {
			["ID"] = 5000301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812590592'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081025792'),
			["NextState"] = {{500030102, 5000302}, {500030103, 5000303}},
			["ActionList"] = {500030101, 500030102, 500030103},
			["EnableDefaultProps"] = false,
		},
		[5000302] = {
			["ID"] = 5000302,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812590848'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081026304'),
			["NextState"] = {{500030201, 5000303}},
			["ActionList"] = {500030201},
			["EnableDefaultProps"] = false,
		},
		[5000303] = {
			["ID"] = 5000303,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812591104'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081026560'),
			["NextState"] = {},
			["ActionList"] = {500030301},
			["EnableDefaultProps"] = false,
		},
		[5000401] = {
			["ID"] = 5000401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812591360'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081026816'),
			["NextState"] = {},
			["ActionList"] = {500040101},
			["EnableDefaultProps"] = true,
		},
		[5000501] = {
			["ID"] = 5000501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812591616'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["NextState"] = {},
			["ActionList"] = {500050101},
			["EnableDefaultProps"] = true,
		},
		[5000601] = {
			["ID"] = 5000601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812591872'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["NextState"] = {},
			["ActionList"] = {500060101},
			["EnableDefaultProps"] = true,
		},
		[5000701] = {
			["ID"] = 5000701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812592128'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {500070101},
			["EnableDefaultProps"] = true,
		},
		[5000801] = {
			["ID"] = 5000801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812592384'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {500080101},
			["EnableDefaultProps"] = false,
		},
		[5000802] = {
			["ID"] = 5000802,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812592384'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081028096'),
			["NextState"] = {},
			["ActionList"] = {500080201},
			["EnableDefaultProps"] = false,
		},
		[5000901] = {
			["ID"] = 5000901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093114112'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {500090101},
			["EnableDefaultProps"] = true,
		},
		[5010001] = {
			["ID"] = 5010001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812593152'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081028608'),
			["NextState"] = {{501000102, 5010002}},
			["ActionList"] = {501000101, 501000102},
			["EnableDefaultProps"] = false,
		},
		[5010002] = {
			["ID"] = 5010002,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812593408'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081028864'),
			["NextState"] = {{501000103, 5010003}, {501000101, 5010001}},
			["ActionList"] = {501000103, 501000101},
			["EnableDefaultProps"] = true,
		},
		[5010003] = {
			["ID"] = 5010003,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812593664'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081029120'),
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[6000101] = {
			["ID"] = 6000101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117184'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600010101},
			["EnableDefaultProps"] = true,
		},
		[6000201] = {
			["ID"] = 6000201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117440'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600020101},
			["EnableDefaultProps"] = true,
		},
		[6000301] = {
			["ID"] = 6000301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117696'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600030101},
			["EnableDefaultProps"] = true,
		},
		[6000401] = {
			["ID"] = 6000401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117184'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600040101},
			["EnableDefaultProps"] = true,
		},
		[6000501] = {
			["ID"] = 6000501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117440'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600050101},
			["EnableDefaultProps"] = true,
		},
		[6000601] = {
			["ID"] = 6000601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093117696'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600060101},
			["EnableDefaultProps"] = true,
		},
		[6000701] = {
			["ID"] = 6000701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093118720'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600070101},
			["EnableDefaultProps"] = true,
		},
		[6000801] = {
			["ID"] = 6000801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093118720'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600080101},
			["EnableDefaultProps"] = true,
		},
		[6000901] = {
			["ID"] = 6000901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093119232'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600090101},
			["EnableDefaultProps"] = true,
		},
		[6001001] = {
			["ID"] = 6001001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093119232'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {600100101},
			["EnableDefaultProps"] = true,
		},
		[6011001] = {
			["ID"] = 6011001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812596480'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081031936'),
			["NextState"] = {},
			["ActionList"] = {601100101},
			["EnableDefaultProps"] = true,
		},
		[6011101] = {
			["ID"] = 6011101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812596480'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081031936'),
			["NextState"] = {},
			["ActionList"] = {601110101},
			["EnableDefaultProps"] = true,
		},
		[6011201] = {
			["ID"] = 6011201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812596480'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081031936'),
			["NextState"] = {},
			["ActionList"] = {601120101},
			["EnableDefaultProps"] = true,
		},
		[6011301] = {
			["ID"] = 6011301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812596480'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081031936'),
			["NextState"] = {},
			["ActionList"] = {601130101},
			["EnableDefaultProps"] = true,
		},
		[6011401] = {
			["ID"] = 6011401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812596480'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081031936'),
			["NextState"] = {},
			["ActionList"] = {601140101},
			["EnableDefaultProps"] = true,
		},
		[5011001] = {
			["ID"] = 5011001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812597760'),
			["Desc"] = "",
			["NextState"] = {{501100102, 5011002}},
			["ActionList"] = {501100101, 501100102},
			["EnableDefaultProps"] = false,
		},
		[5011002] = {
			["ID"] = 5011002,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812598016'),
			["Desc"] = "",
			["NextState"] = {{501100201, 5011003}},
			["ActionList"] = {501100201},
			["EnableDefaultProps"] = true,
		},
		[5011003] = {
			["ID"] = 5011003,
			["Name"] = Game.TableDataManager:GetLangStr('str_9488656505088'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[5011601] = {
			["ID"] = 5011601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812598528'),
			["Desc"] = "",
			["NextState"] = {{501160101, 5011602}},
			["ActionList"] = {501160101},
			["EnableDefaultProps"] = false,
		},
		[5011602] = {
			["ID"] = 5011602,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812598784'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[5011101] = {
			["ID"] = 5011101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812599040'),
			["Desc"] = "",
			["NextState"] = {{501110101, 5011102}},
			["ActionList"] = {501110101},
			["EnableDefaultProps"] = false,
		},
		[5011102] = {
			["ID"] = 5011102,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812599296'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502000101},
			["EnableDefaultProps"] = true,
		},
		[5011201] = {
			["ID"] = 5011201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812599552'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081035008'),
			["NextState"] = {{501120101, 5011202}},
			["ActionList"] = {501120101},
			["EnableDefaultProps"] = false,
		},
		[5011202] = {
			["ID"] = 5011202,
			["Name"] = Game.TableDataManager:GetLangStr('str_34292092641280'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081035264'),
			["NextState"] = {{501120201, 5011203}, {501120202, 5011201}},
			["ActionList"] = {501120201, 501120202},
			["EnableDefaultProps"] = true,
		},
		[5011203] = {
			["ID"] = 5011203,
			["Name"] = Game.TableDataManager:GetLangStr('str_9488656505088'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081035520'),
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[5011301] = {
			["ID"] = 5011301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812600320'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812575488'),
			["NextState"] = {},
			["ActionList"] = {501130101},
			["EnableDefaultProps"] = true,
		},
		[5011401] = {
			["ID"] = 5011401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812600576'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {501140101, 501140102, 501140103, 501140104},
			["EnableDefaultProps"] = true,
		},
		[5011501] = {
			["ID"] = 5011501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812586496'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {501150101},
			["EnableDefaultProps"] = true,
		},
		[5011701] = {
			["ID"] = 5011701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812601088'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {501170101, 501170102},
			["EnableDefaultProps"] = true,
		},
		[5020001] = {
			["ID"] = 5020001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812601344'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502000101},
			["EnableDefaultProps"] = true,
		},
		[6010101] = {
			["ID"] = 6010101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093120256'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610010101},
			["EnableDefaultProps"] = true,
		},
		[6010201] = {
			["ID"] = 6010201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093120512'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610020101},
			["EnableDefaultProps"] = true,
		},
		[6010301] = {
			["ID"] = 6010301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093120768'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610030101},
			["EnableDefaultProps"] = true,
		},
		[6010401] = {
			["ID"] = 6010401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093121024'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610040101},
			["EnableDefaultProps"] = true,
		},
		[6010501] = {
			["ID"] = 6010501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093121280'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610050101},
			["EnableDefaultProps"] = true,
		},
		[6010601] = {
			["ID"] = 6010601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9484093121536'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {610060101},
			["EnableDefaultProps"] = true,
		},
		[5020301] = {
			["ID"] = 5020301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812603136'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502030101},
			["EnableDefaultProps"] = true,
		},
		[5020401] = {
			["ID"] = 5020401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812601344'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502040101},
			["EnableDefaultProps"] = true,
		},
		[5011801] = {
			["ID"] = 5011801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812603648'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081039104'),
			["NextState"] = {{501180101, 5011802}},
			["ActionList"] = {501180101, 501180102, 501180103, 501180104, 501180105},
			["EnableDefaultProps"] = true,
		},
		[5011802] = {
			["ID"] = 5011802,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812603904'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081039360'),
			["NextState"] = {{501180201, 5011801}},
			["ActionList"] = {501180201, 501180202, 501180203},
			["EnableDefaultProps"] = true,
		},
		[6020101] = {
			["ID"] = 6020101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812604160'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {602010101},
			["EnableDefaultProps"] = true,
		},
		[6020201] = {
			["ID"] = 6020201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812604416'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {602020101},
			["EnableDefaultProps"] = true,
		},
		[6020301] = {
			["ID"] = 6020301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812604672'),
			["Desc"] = "",
			["NextState"] = {{602030102, 6020302}, {602030103, 6020303}, {602030104, 6020304}},
			["ActionList"] = {602030101, 602030102, 602030103, 602030104},
			["EnableDefaultProps"] = true,
		},
		[6020302] = {
			["ID"] = 6020302,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812604928'),
			["Desc"] = "",
			["NextState"] = {{602030202, 6020303}, {602030203, 6020304}},
			["ActionList"] = {602030201, 602030202, 602030203, 602030204},
			["EnableDefaultProps"] = true,
		},
		[6020303] = {
			["ID"] = 6020303,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605184'),
			["Desc"] = "",
			["NextState"] = {{602030302, 6020304}},
			["ActionList"] = {602030301, 602030302, 602030303},
			["EnableDefaultProps"] = true,
		},
		[6020304] = {
			["ID"] = 6020304,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605440'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {602030401},
			["EnableDefaultProps"] = true,
		},
		[5020501] = {
			["ID"] = 5020501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605696'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502050101},
			["EnableDefaultProps"] = false,
		},
		[5020601] = {
			["ID"] = 5020601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605952'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502060101},
			["EnableDefaultProps"] = true,
		},
		[5020701] = {
			["ID"] = 5020701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605952'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502070101},
			["EnableDefaultProps"] = true,
		},
		[5020801] = {
			["ID"] = 5020801,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812606464'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081041920'),
			["NextState"] = {},
			["ActionList"] = {502080101},
			["EnableDefaultProps"] = true,
		},
		[5020901] = {
			["ID"] = 5020901,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812606720'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081042176'),
			["NextState"] = {},
			["ActionList"] = {502090101},
			["EnableDefaultProps"] = true,
		},
		[5021001] = {
			["ID"] = 5021001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812606976'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502100101},
			["EnableDefaultProps"] = true,
		},
		[5021101] = {
			["ID"] = 5021101,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812606976'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502110101},
			["EnableDefaultProps"] = true,
		},
		[5021201] = {
			["ID"] = 5021201,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812607488'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081042944'),
			["NextState"] = {{502120101, 5021202}},
			["ActionList"] = {502120101},
			["EnableDefaultProps"] = true,
		},
		[5021202] = {
			["ID"] = 5021202,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812607488'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081043200'),
			["NextState"] = {{502120201, 5021203}},
			["ActionList"] = {502120201},
			["EnableDefaultProps"] = true,
		},
		[5021203] = {
			["ID"] = 5021203,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812608000'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081043456'),
			["NextState"] = {{502120301, 5021204}},
			["ActionList"] = {502120301},
			["EnableDefaultProps"] = true,
		},
		[5021204] = {
			["ID"] = 5021204,
			["Name"] = Game.TableDataManager:GetLangStr('str_9553081035008'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {},
			["EnableDefaultProps"] = false,
		},
		[5021301] = {
			["ID"] = 5021301,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812608512'),
			["Desc"] = "",
			["NextState"] = {{502130101, 5021302}},
			["ActionList"] = {502130101},
			["EnableDefaultProps"] = false,
		},
		[5021302] = {
			["ID"] = 5021302,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812608768'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502130201},
			["EnableDefaultProps"] = true,
		},
		[5021401] = {
			["ID"] = 5021401,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812609024'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502140101},
			["EnableDefaultProps"] = true,
		},
		[5021501] = {
			["ID"] = 5021501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812609280'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502150101},
			["EnableDefaultProps"] = false,
		},
		[1210501] = {
			["ID"] = 1210501,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812609536'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812609536'),
			["NextState"] = {{121050102, 1210502}},
			["ActionList"] = {121050101, 121050102, 121050104},
			["EnableDefaultProps"] = true,
		},
		[1210502] = {
			["ID"] = 1210502,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812609792'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812609792'),
			["NextState"] = {{121050103, 1210503}, {121050101, 1210501}},
			["ActionList"] = {121050101, 121050102, 121050104, 121050103},
			["EnableDefaultProps"] = true,
		},
		[1210503] = {
			["ID"] = 1210503,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812610048'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9552812610048'),
			["NextState"] = {{121050101, 1210501}, {121050101, 1210501}},
			["ActionList"] = {121050103, 121050104, 121050101},
			["EnableDefaultProps"] = true,
		},
		[1210504] = {
			["ID"] = 1210504,
			["Name"] = Game.TableDataManager:GetLangStr('str_54564338271744'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {121050104},
			["EnableDefaultProps"] = true,
		},
		[5021601] = {
			["ID"] = 5021601,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605952'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502160101},
			["EnableDefaultProps"] = true,
		},
		[5021701] = {
			["ID"] = 5021701,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812605952'),
			["Desc"] = "",
			["NextState"] = {},
			["ActionList"] = {502170101},
			["EnableDefaultProps"] = true,
		},
		[3010001] = {
			["ID"] = 3010001,
			["Name"] = Game.TableDataManager:GetLangStr('str_9552812611072'),
			["Desc"] = Game.TableDataManager:GetLangStr('str_9553081046528'),
			["NextState"] = {},
			["ActionList"] = {301000001},
			["EnableDefaultProps"] = false,
		},
	},
}

return TopData
