--
-- 表名: $Manorlevelup_庄园升级表.xlsx  页名：$Const_常量
--

local TopData = {
	data = {
		["HOME_COIN_ID"] = 2001027,
		["HOME_CANT_BACK_WORLD_TYPE"] = {2, 4},
		["HOME_NAME_MAX_LEN"] = 10,
		["HOME_INTRODUCTION_MAX_LEN"] = 40,
		["HOME_MODIFY_NAME_COST"] = {2001002, 40},
		["HOME_MAX_LEVEL"] = 10,
		["PutScoreStr"] = Game.TableDataManager:GetLangStr('str_34567238978560'),
		["AreaSizeStr"] = Game.TableDataManager:GetLangStr('str_34567238978816'),
		["ReservedText1Str"] = Game.TableDataManager:GetLangStr('str_34567238979072'),
		["ReservedText2Str"] = Game.TableDataManager:GetLangStr('str_34567238979328'),
		["TotalScoreStr"] = Game.TableDataManager:GetLangStr('str_34567238979584'),
		["CoinStr"] = Game.TableDataManager:GetLangStr('str_34567238979840'),
		["CoinLimitStr"] = Game.TableDataManager:GetLangStr('str_34567238980096'),
		["AcquiredThisWeekStr"] = Game.TableDataManager:GetLangStr('str_34567238980352'),
		["DefaultIntroductionStr"] = Game.TableDataManager:GetLangStr('str_34567238980608'),
		["UpgradeConditionStr"] = Game.TableDataManager:GetLangStr('str_34567238980864'),
		["UpgradeStr"] = Game.TableDataManager:GetLangStr('str_34567238981120'),
		["MaxLevelStr"] = Game.TableDataManager:GetLangStr('str_34567238981376'),
		["Tab1Str"] = Game.TableDataManager:GetLangStr('str_34567238981632'),
		["Tab2Str"] = Game.TableDataManager:GetLangStr('str_34567238981888'),
		["DefaultRenameStr"] = Game.TableDataManager:GetLangStr('str_34567238982144'),
	},
}

return TopData
