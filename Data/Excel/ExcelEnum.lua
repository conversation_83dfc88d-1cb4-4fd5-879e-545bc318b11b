Enum = Enum or {} 

Enum.BuildCantDeleteType = {
    ['Floor_Connect'] = 1301, 
    ['Normal_Block'] = 1001, 
    ['Pillar_Connect'] = 1201, 
}

Enum.BuildCantPlaceType = {
    ['Floor_BlockEnvironment'] = 1302, 
    ['Floor_Connect'] = 1301, 
    ['Normal_Block'] = 1002, 
    ['Normal_BlockByStair'] = 1004, 
    ['Normal_Cant'] = 1006, 
    ['Normal_Count'] = 1007, 
    ['Normal_NeedFloor'] = 1005, 
    ['Normal_Over'] = 1001, 
    ['Normal_RepeatBuild'] = 1003, 
    ['Stair_NeedFloorPath'] = 1501, 
    ['Stair_OccupiedByWall'] = 1502, 
}

Enum.BuildFrameType = {
    [2101] = {4300000, 4300001, 4300002}, 
    [2102] = {[1] = 4300003, [2] = 4300004, [3] = 4300005, [4] = 4300006, [5] = 4300007, [6] = 4300008, [8] = 4300010, }, 
    [2103] = {[11] = 4300013, }, 
    [2104] = {[1] = 4300015, [5] = 4300016, [9] = 4300020, [11] = 4300018, [15] = 4300019, [19] = 4300022, [23] = 4300017, [27] = 4300023, [31] = 4300021, [32] = 4300024, [36] = 4300026, [40] = 4300025, [42] = 4300028, [46] = 4300014, [99] = 4300027, }, 
    [2105] = {4300029, 4300030, 4300031, 4300032}, 
    [2106] = {4300012}, 
    [2107] = {4300011}, 
    [2108] = {}, 
    [2201] = {}, 
    [2202] = {}, 
    [2203] = {}, 
    [2204] = {}, 
    [2205] = {}, 
    [2301] = {}, 
}

Enum.BuildGroupList = {
    [2101] = {4300000, 4300001, 4300002}, 
    [2102] = {4300003, 4300006, 4300010, 4300008, 4300007, 4300005, 4300004}, 
    [2103] = {4300013}, 
    [2104] = {4300028, 4300027, 4300025, 4300024, 4300023, 4300021, 4300020, 4300019, 4300015, 4300017, 4300016, 4300014, 4300018, 4300022, 4300026}, 
    [2105] = {4300032, 4300031, 4300029, 4300030}, 
    [2106] = {4300012}, 
    [2107] = {4300011}, 
    [2108] = {}, 
    [2201] = {4321003, 4321007, 4321011, 4321015, 4321019, 4321023, 4321000, 4321004, 4321008, 4321012, 4321016, 4321020, 4321024, 4321001, 4321005, 4321009, 4321013, 4321017, 4321021, 4321025, 4321002, 4321006, 4321010, 4321014, 4321018, 4321022, 4320007, 4320011, 4320012, 4320001, 4320005, 4320013, 4320002, 4320010, 4320014}, 
    [2202] = {4321027, 4321031, 4321028, 4321029, 4321026, 4321030, 4321034, 4321038, 4321042, 4321046, 4321050, 4321054, 4321058, 4321062, 4321066, 4321070, 4321074, 4321078, 4321082, 4321086, 4321090, 4321094, 4321126, 4321130, 4321134, 4320003, 4320015, 4320019, 4320023, 4320004, 4320008, 4320016, 4320020, 4320024, 4320009, 4320017, 4320021, 4320006, 4320018, 4320022, 4321135, 4321133, 4321132, 4321131, 4321129, 4321128, 4321127, 4321125, 4321093, 4321092, 4321091, 4321089, 4321088, 4321087, 4321085, 4321084, 4321083, 4321081, 4321080, 4321079, 4321077, 4321076, 4321075, 4321073, 4321072, 4321071, 4321069, 4321068, 4321067, 4321065, 4321064, 4321063, 4321061, 4321060, 4321059, 4321057, 4321056, 4321055, 4321053, 4321052, 4321051, 4321049, 4321048, 4321047, 4321045, 4321044, 4321043, 4321041, 4321040, 4321039, 4321037, 4321036, 4321035, 4321033, 4321032}, 
    [2203] = {4321098, 4321102, 4321106, 4321110, 4320027, 4320031, 4320035, 4320028, 4320025, 4320029, 4320026, 4320030, 4320034, 4321111, 4321109, 4321108, 4321107, 4321105, 4321104, 4321103, 4321101, 4321100, 4321099, 4321097, 4321096, 4321095, 4320036, 4320033, 4320032}, 
    [2204] = {4321114, 4321118, 4321122, 4320038, 4320042, 4320046, 4320050, 4320054, 4321136, 4321124, 4321123, 4321121, 4321120, 4321119, 4321117, 4321116, 4321115, 4321113, 4321112, 4320053, 4320052, 4320051, 4320049, 4320048, 4320047, 4320045, 4320044, 4320043, 4320041, 4320040, 4320039, 4320037}, 
    [2205] = {4323003, 4323007, 4323011, 4323015, 4323019, 4323023, 4323027, 4323031, 4323000, 4323004, 4323008, 4323076, 4323075, 4323074, 4323073, 4323072, 4323071, 4323070, 4323069, 4323068, 4323067, 4323066, 4323065, 4323064, 4323063, 4323062, 4323061, 4323060, 4323059, 4323058, 4323057, 4323056, 4323055, 4323054, 4323053, 4323052, 4322003, 4322007, 4322011, 4323051, 4323050, 4323049, 4323048, 4323047, 4322000, 4322004, 4322008, 4323046, 4323045, 4323044, 4323043, 4323042, 4323041, 4323040, 4323039, 4323038, 4323037, 4323036, 4323035, 4323034, 4323033, 4323032, 4323030, 4323029, 4323028, 4323026, 4323025, 4323024, 4323022, 4323021, 4323020, 4323018, 4323017, 4323016, 4323014, 4323013, 4323012, 4323010, 4323009, 4323006, 4323005, 4323002, 4323001, 4322010, 4322009, 4322006, 4322005, 4322002, 4322001}, 
    [2301] = {}, 
}

Enum.BuildHighLightEffectType = {
    ['Place_Blue'] = 1002, 
    ['Place_Red'] = 1001, 
    ['Select_Blue'] = 1004, 
    ['Select_Red'] = 1003, 
    ['Select_Yellow'] = 1005, 
}

Enum.BuildItemGroup = {
    ['Floor'] = 2103, 
    ['Furniture1'] = 2201, 
    ['Furniture2'] = 2202, 
    ['Furniture3'] = 2203, 
    ['Furniture4'] = 2204, 
    ['Furniture5'] = 2205, 
    ['Pillar'] = 2102, 
    ['Railing'] = 2106, 
    ['RailingPillar'] = 2107, 
    ['Roof'] = 2104, 
    ['Spire'] = 2108, 
    ['Stair'] = 2105, 
    ['Wall'] = 2101, 
    ['WorkshopWine'] = 2301, 
}

Enum.BuildItemSubGroup = {
    ['Bed'] = 4104, 
    ['BlackThornsFurniture'] = 4501, 
    ['Carpet'] = 4202, 
    ['Chair'] = 4102, 
    ['CookWare'] = 4403, 
    ['HangingCurtain'] = 4301, 
    ['HangingDecoration'] = 4302, 
    ['LightingFixture'] = 4201, 
    ['Mattress'] = 4205, 
    ['MusicalInstrument'] = 4402, 
    ['OntheGround'] = 4204, 
    ['Onthetable'] = 4203, 
    ['Plant'] = 4206, 
    ['RichFurniture'] = 4502, 
    ['SanitaryWare'] = 4401, 
    ['StairBig'] = 3504, 
    ['StairSmall'] = 3501, 
    ['StairTurnLeft'] = 3502, 
    ['StairTurnRight'] = 3503, 
    ['Storage'] = 4103, 
    ['Table'] = 4101, 
    ['WallDoor'] = 3103, 
    ['WallNormal'] = 3101, 
    ['WallWindow'] = 3102, 
    ['Workbench'] = 4404, 
}

Enum.BuildItemType = {
    ['Component'] = 1001, 
    ['Furniture'] = 1002, 
    ['Workshop'] = 1003, 
}

Enum.CommonInteractorClassType = {
    ['Breakable'] = 2, 
    ['CombatGameplay'] = 6, 
    ['CommonArea'] = 8, 
    ['CustomizedGameplay'] = 4, 
    ['ExplorationGameplay'] = 5, 
    ['HomelandBarrier'] = 9, 
    ['Portal'] = 3, 
    ['Task'] = 7, 
    ['Test'] = 0, 
    ['TreasureBox'] = 1, 
}

Enum.CommonInteractorEventType = {
    ['ACCEPT_TASK'] = 1010, 
    ['ATTACKED_COUNT'] = 10019, 
    ['BE_ATTACKED'] = 10018, 
    ['BUFF_START'] = 10008, 
    ['COMPLETE_TASK'] = 1011, 
    ['DICE_CHECK_FINISH'] = 10014, 
    ['DICE_CHECK_START'] = 10013, 
    ['ENTER_OR_LEAVE_RANGE'] = 10006, 
    ['ENTITY_DEAD_WITH_DESIGN_TAG'] = 10015, 
    ['FIRST_LEVEL_AREA_EXPLORATION_PROGRESS'] = 10012, 
    ['INTERACT_ATTACK'] = 10004, 
    ['INTERACT_FINISHED'] = 10003, 
    ['INTERACT_INTERRUPT'] = 10002, 
    ['INTERACT_START'] = 10001, 
    ['LEVEL_UP'] = 10005, 
    ['MIRROR_REFLECT_LIGHT_REACH_TARGET'] = 10017, 
    ['PAINTING_SCRATCH_PROGRESS_FINISH'] = 10016, 
    ['RECEIVE_MSG'] = 1009, 
    ['SPIRITUAL_VISION_CHANGE'] = 10007, 
}

Enum.CustomizedGameplayType = {
    ['HIDDEN_FLOOR'] = 0, 
    ['MIRROR_REFLECTION'] = 1, 
    ['SINGLE_LINE_DRAW'] = 2, 
}

Enum.EAchievementMenuMainIndex = {
    ['ACHIEVEMENT_MENU_RECENT_FINISH'] = 2, 
    ['ACHIEVEMENT_MENU_TO_FINISH'] = 1, 
    ['ACHIEVEMENT_MENU_TYPE_1'] = 3, 
    ['ACHIEVEMENT_MENU_TYPE_2'] = 4, 
    ['ACHIEVEMENT_MENU_TYPE_3'] = 5, 
    ['ACHIEVEMENT_MENU_TYPE_4'] = 6, 
    ['ACHIEVEMENT_MENU_TYPE_5'] = 7, 
    ['ACHIEVEMENT_MENU_TYPE_6'] = 8, 
    ['ACHIEVEMENT_MENU_TYPE_7'] = 9, 
}

Enum.EActivityTypeData = {
    ['ACTIVITY_CHAMPION'] = 12, 
    ['ACTIVITY_DUNGEON'] = 3, 
    ['ACTIVITY_GUILD_DANCING'] = 11, 
    ['ACTIVITY_GUILD_LEAGUE'] = 10, 
    ['ACTIVITY_GUILD_PARTY'] = 9, 
    ['ACTIVITY_GUILD_QUIZ'] = 6, 
    ['ACTIVITY_PVP'] = 4, 
    ['ACTIVITY_RECYCLE'] = 8, 
    ['ACTIVITY_TARVELS'] = 7, 
    ['ACTIVITY_TEST'] = 9999, 
    ['ACTIVITY_TOWERCLIMB'] = 5, 
    ['WORLD_BOSS'] = 1, 
    ['WORLD_CHANNEL_QUIZ'] = 2, 
}

Enum.EAdditionalSkillConstData = {
    ['Dowsing01_Path'] = '/Game/Arts/Effects/FX_Common/Dowsing/NS_Dowsing_01.NS_Dowsing_01', 
    ['Dowsing01_Time'] = '0.3', 
    ['Dowsing01_XBIAS'] = '30', 
    ['Dowsing01_YAWBIAS'] = '-90', 
    ['Dowsing01_YBIAS'] = '0', 
    ['Dowsing01_ZBIAS'] = '-85', 
    ['Dowsing02_Path'] = '/Game/Arts/Effects/FX_Common/Dowsing/NS_Dowsing_02.NS_Dowsing_02', 
    ['Dowsing02_Time'] = '0.3', 
    ['Dowsing02_XBIAS'] = '30', 
    ['Dowsing02_YAWBIAS'] = '-90', 
    ['Dowsing02_YBIAS'] = '0', 
    ['Dowsing02_ZBIAS'] = '-85', 
    ['Dowsing03_Path'] = '/Game/Arts/Effects/FX_Common/Dowsing/NS_Dowsing_03.NS_Dowsing_03', 
    ['Dowsing03_Time'] = '0.3', 
    ['Dowsing03_XBIAS'] = '0', 
    ['Dowsing03_YAWBIAS'] = '0', 
    ['Dowsing03_YBIAS'] = '0', 
    ['Dowsing03_ZBIAS'] = '0', 
    ['DowsingLineAk'] = 'Play_GamePlay_Guide_Path', 
    ['DowsingLineFinishRadius'] = '1000', 
    ['DowsingLineRefresh'] = '2', 
    ['DowsingLineRefreshDistance'] = '600', 
    ['DowsingLineRefreshNum'] = '4', 
    ['DowsingLineRefreshRadius'] = '500', 
    ['DowsingLine_Path'] = '/Game/Arts/Effects/FX_Common/Dowsing/NS_Dowsing_Line.NS_Dowsing_Line', 
    ['DowsingLine_Time'] = '0.2', 
    ['DowsingTattoo_Path'] = '/Game/Arts/Effects/FX_Common/Dowsing/NS_Dowsing_Tattoo.NS_Dowsing_Tattoo', 
    ['DowsingTattoo_Time'] = '0.3', 
    ['DowsingTattoo_XBIAS'] = '30', 
    ['DowsingTattoo_YAWBIAS'] = '-90', 
    ['DowsingTattoo_YBIAS'] = '0', 
    ['DowsingTattoo_ZBIAS'] = '-85', 
}

Enum.EAppearanceAroundID = {
    ['BodyEffect'] = 7001, 
    ['FashionIdle01'] = 7003, 
    ['FootPrint'] = 7002, 
}

Enum.EAppearanceItemSubType2ID = {
    ['Fashion'] = 7001, 
    ['Mount'] = 7002, 
    ['SceneCustom'] = 7003, 
}

Enum.EAppearanceSubType2ID = {
    ['BackAcc'] = 2010, 
    ['BodyEffect'] = 7001, 
    ['Cloak'] = 2011, 
    ['EarAcc'] = 2005, 
    ['FacialAcc'] = 2004, 
    ['FashionIdle01'] = 7003, 
    ['FootPrint'] = 7002, 
    ['Hair'] = 1006, 
    ['HairAcc'] = 2002, 
    ['HandAcc'] = 2009, 
    ['Hat'] = 2003, 
    ['Mount'] = 10001, 
    ['NeckAcc'] = 2006, 
    ['Pants'] = 1003, 
    ['Shoes'] = 1005, 
    ['ShoulderAcc'] = 2007, 
    ['Suit'] = 1001, 
    ['TopAcc'] = 2001, 
    ['Upper'] = 1002, 
    ['WaistAcc'] = 2008, 
}

Enum.EAppearanceType2ID = {
    ['Accessory'] = 2, 
    ['Around'] = 3, 
    ['Fashion'] = 1, 
    ['Mount'] = 5, 
}

Enum.EArenaBattleMode = {
    ['DeathMode'] = 2, 
    ['HotBloodMode'] = 1, 
}

Enum.EArtAssetIconData = {
    ['ElementSkill_Ctritick'] = 10116, 
    ['ElementSkill_Normal'] = 10115, 
    ['ElementSkill_Type'] = 10114, 
    ['FELLOW_FIGHT'] = 10031, 
    ['FELLOW_FOSTER'] = 10032, 
    ['GROUP_LEADER_FLAG'] = 10086, 
    ['GROUP_UP'] = 10066, 
    ['GROUP_UP_HOVER'] = 10076, 
    ['GROUP_WAIT'] = 10067, 
    ['GROUP_WAIT_HOVER'] = 10080, 
    ['GUILD_ACTIVITY'] = 10002, 
    ['GUILD_APPLY'] = 10006, 
    ['GUILD_AUTHORITY'] = 10003, 
    ['GUILD_BUILDING'] = 10005, 
    ['GUILD_CONTRIBUTION_RANK1'] = 10036, 
    ['GUILD_CONTRIBUTION_RANK2'] = 10037, 
    ['GUILD_CONTRIBUTION_RANK3'] = 10038, 
    ['GUILD_CREATE'] = 10004, 
    ['GUILD_DANCING_AUDREY'] = 10054, 
    ['GUILD_DANCING_INVITE'] = 10052, 
    ['GUILD_DANCING_VIEW'] = 10053, 
    ['GUILD_EVENT'] = 10007, 
    ['GUILD_FUND'] = 10018, 
    ['GUILD_GIFT'] = 10008, 
    ['GUILD_HOME'] = 10009, 
    ['GUILD_MEDAL'] = 10010, 
    ['GUILD_MEMBER'] = 10011, 
    ['GUILD_PARTY'] = 10035, 
    ['GUILD_PRACTICE'] = 10029, 
    ['GUILD_RANK'] = 10012, 
    ['GUILD_RESPOSE'] = 10013, 
    ['GUILD_SHOP'] = 10028, 
    ['GUILD_SIGNIN'] = 10026, 
    ['GUILD_TASK'] = 10030, 
    ['GUILD_WAGES'] = 10027, 
    ['GUILE_REMATERIAL'] = 10051, 
    ['IS_TRADABLE'] = 10001, 
    ['LOG_IN'] = 10019, 
    ['LOG_OUT'] = 10020, 
    ['MARK_HOVER'] = 10078, 
    ['QUEST_HOVER'] = 10113, 
    ['QUEST_OPEN'] = 10112, 
    ['REDPACKET_CLOTHES'] = 10094, 
    ['REDPACKET_FOLLOW'] = 10097, 
    ['REDPACKET_MONEY'] = 10095, 
    ['REDPACKET_THANKS'] = 10096, 
    ['SCENE_MARK'] = 10070, 
    ['SERVER_TAG_NEW'] = 10021, 
    ['SERVER_TAG_RECOMMEND'] = 10022, 
    ['SEVER_LIST_CROWD'] = 10015, 
    ['SEVER_LIST_MAINTAINING'] = 10017, 
    ['SEVER_LIST_OVERCROWDED'] = 10016, 
    ['SEVER_LIST_UNOBTRUCTED'] = 10014, 
    ['SHOP_ITEM_QUAILITY_1'] = 10106, 
    ['SHOP_ITEM_QUAILITY_2'] = 10107, 
    ['SHOP_ITEM_QUAILITY_3'] = 10108, 
    ['SHOP_ITEM_QUAILITY_4'] = 10109, 
    ['SHOP_ITEM_QUAILITY_5'] = 10110, 
    ['SHOP_ITEM_QUAILITY_6'] = 10111, 
    ['SHOP_LOCK_1'] = 10104, 
    ['SHOP_LOCK_2'] = 10105, 
    ['SHOP_QUAILITY_1'] = 10098, 
    ['SHOP_QUAILITY_2'] = 10099, 
    ['SHOP_QUAILITY_3'] = 10100, 
    ['SHOP_QUAILITY_4'] = 10101, 
    ['SHOP_QUAILITY_5'] = 10102, 
    ['SHOP_QUAILITY_6'] = 10103, 
    ['SIDEBAR_RANKING_ICON'] = 10133, 
    ['SKILL_CUSTOMISER_EQUIP'] = 10025, 
    ['SKILL_CUSTOMIZER_PREVIEW'] = 10024, 
    ['TEAMMATE_MARK'] = 10071, 
    ['TEAM_BLOCK_MIC'] = 10064, 
    ['TEAM_CLOSE_MIC'] = 10063, 
    ['TEAM_CONFIRM_NO'] = 10093, 
    ['TEAM_CONFIRM_YES'] = 10092, 
    ['TEAM_EXIT'] = 10072, 
    ['TEAM_EXIT_HOVER'] = 10079, 
    ['TEAM_FAST_MAKEUP'] = 10055, 
    ['TEAM_FAST_MAKEUP_HOVER'] = 10084, 
    ['TEAM_FOLLOW'] = 10069, 
    ['TEAM_FOLLOW_HOVER'] = 10075, 
    ['TEAM_HUD_GROUP_LEADER'] = 10090, 
    ['TEAM_HUD_GROUP_TEAM_LEADER'] = 10091, 
    ['TEAM_IN_TEAM'] = 10033, 
    ['TEAM_LEADER_FLAG'] = 10087, 
    ['TEAM_LISTEN'] = 10089, 
    ['TEAM_LISTEN1'] = 10058, 
    ['TEAM_LISTEN_HOVER'] = 10074, 
    ['TEAM_MARK'] = 10085, 
    ['TEAM_MARK_LOCATION1'] = 10045, 
    ['TEAM_MARK_LOCATION2'] = 10046, 
    ['TEAM_MARK_LOCATION3'] = 10047, 
    ['TEAM_MARK_LOCATION4'] = 10048, 
    ['TEAM_MARK_LOCATION5'] = 10049, 
    ['TEAM_MARK_LOCATION6'] = 10050, 
    ['TEAM_MARK_MEMBER1'] = 10039, 
    ['TEAM_MARK_MEMBER2'] = 10040, 
    ['TEAM_MARK_MEMBER3'] = 10041, 
    ['TEAM_MARK_MEMBER4'] = 10042, 
    ['TEAM_MARK_MEMBER5'] = 10043, 
    ['TEAM_MARK_MEMBER6'] = 10044, 
    ['TEAM_NOT_LISTEN'] = 10059, 
    ['TEAM_NOT_LISTEN1'] = 10060, 
    ['TEAM_NOT_LISTEN_HOVER'] = 10082, 
    ['TEAM_OPEN_MIC'] = 10061, 
    ['TEAM_OPEN_MIC1'] = 10062, 
    ['TEAM_OPEN_MIC_HOVER'] = 10083, 
    ['TEAM_QUICK_TEAM_UP'] = 10034, 
    ['TEAM_TEAM'] = 10056, 
    ['TEAM_TEAM_HOVER'] = 10073, 
    ['TEAM_UP'] = 10065, 
    ['TEAM_UP_HOVER'] = 10077, 
    ['TEAM_VOICE'] = 10088, 
    ['TEAM_WAIT'] = 10068, 
    ['TEAM_WAIT_HOVER'] = 10081, 
    ['TalentPoint_Atk'] = 10120, 
    ['TalentPoint_Crit'] = 10117, 
    ['TalentPoint_Def'] = 10118, 
    ['TalentPoint_ElementAtk'] = 10125, 
    ['TalentPoint_ElementDef'] = 10123, 
    ['TalentPoint_ElementEffect1'] = 10127, 
    ['TalentPoint_ElementEffect2'] = 10128, 
    ['TalentPoint_ElementEffect3'] = 10129, 
    ['TalentPoint_ElementEffect4'] = 10130, 
    ['TalentPoint_ElementEffect5'] = 10131, 
    ['TalentPoint_ElementEffect6'] = 10132, 
    ['TalentPoint_ElementRate'] = 10122, 
    ['TalentPoint_HP'] = 10121, 
    ['TalentPoint_IgnoreElementDef'] = 10124, 
    ['TalentPoint_Pierce'] = 10119, 
}

Enum.EAttackTypeSetting = {
    ['CONTROL_PROTECTION_BUFF_ID'] = 82030007, 
    ['CONTROL_PROTECTION_CD_TIME'] = 12, 
    ['CONTROL_PROTECTION_GAP_IGNORE_TIME'] = 0.3, 
    ['CONTROL_PROTECTION_TRIGGER_TIME'] = 6, 
    ['DEFAULT_LIEDOWN_DURATION'] = 1.4, 
    ['DRAG_IMPULSE_ORIGIN_RANDOM_OFFSET'] = 0, 
    ['DeControlLevel'] = 1, 
    ['FLOAT_HIT_HEIGHT_CORRECTION_VALUE'] = 0, 
    ['FLOAT_HIT_HOVER_TIME'] = 0, 
    ['HIT_FEEDBACK_CD_TIME'] = 0.1, 
    ['HitAir_GetUpTime'] = 1.2, 
    ['HitBack_HitTime'] = 0.3, 
    ['HitBack_MoveTime'] = 0.2, 
    ['HitDown_HitTime'] = 1.33, 
    ['HitDown_MoveTime'] = 0.2, 
    ['HitDrag_HitTime'] = 0.5, 
    ['HitDrag_MoveTime'] = 0.2, 
    ['HitFly_GetUpTime'] = 1.2, 
    ['HitStiff_HitTime'] = 0.5, 
    ['MONSTER_FLOAT_PROTECTION_TIME'] = 3, 
    ['MONSTER_LIEDOWN_PROTECTION_TIME'] = 3, 
    ['PLAYER_FLOAT_PROTECTION_TIME'] = 0, 
    ['PLAYER_LIEDOWN_PROTECTION_TIME'] = 0, 
    ['STAND_UP_REMAINING_TIME'] = 0.5, 
    ['SWITCH_BEATEN_TREE_REMAINING_TIME'] = 0, 
    ['TO_LIEDOWN_DURATION'] = 1.4, 
}

Enum.EAudioConstData = {
    ['ALIVE_STATE'] = 'Alive', 
    ['ALIVE_STATE_GROUP'] = 'Player_State', 
    ['ARBITRATOR_ENTER_AMB_EVENT'] = 'Play_UI_Arbitrator_AmbLoop', 
    ['ARBITRATOR_FADEIN_EVENT'] = 'Play_UI_Arbitrator_FadeIn', 
    ['ARBITRATOR_LEAVE_AMB_EVENT'] = 'Stop_UI_Arbitrator_AmbLoop', 
    ['BATTLE_MAX_PLAYBACK_LIMIT'] = '30', 
    ['BATTLE_STATE_GROUP'] = 'MX_Battle_InBattle', 
    ['CAMERA_ARM_LENGTH_RTPC'] = 'Camera_Distance', 
    ['CARRIAGE_BUS_EVENT'] = 'Play_Bus_Carriage', 
    ['CHARACTER_FOOTSTEP_BANK_PREFIX'] = 'Footstep_Shoe_', 
    ['CHARACTER_GENDER_SWITCH'] = 'Gender_World', 
    ['CREATE_ROLE_ENTER_AMB_EVENT'] = 'Play_UI_Character_Amb_Loop', 
    ['CREATE_ROLE_LEAVE_AMB_EVENT'] = 'Stop_UI_Character_Amb_Loop', 
    ['ClOWN_LEAVE_ACTION_EVENT'] = 'Stop_Clown_Animation', 
    ['DEAD_STATE'] = 'Dead', 
    ['DIALOGUE_GROUP'] = 'Dialogue', 
    ['DIVINATION_CARDCHOSEN_EVENT'] = 'Play_UI_Divination_CardChosen', 
    ['DIVINATION_CARDCLOSE_EVENT'] = 'Play_UI_Divination_CardClose', 
    ['DIVINATION_CARDOPEN_EVENT'] = 'Play_UI_Divination_CardOpen', 
    ['DIVINATION_ENTER_AMB_EVENT'] = 'Play_UI_Divination_AmbLoop', 
    ['DIVINATION_FIRE_ENTER_LOOP_EVENT'] = 'Play_UI_Divination_Fire_Loop', 
    ['DIVINATION_FIRE_EVENT'] = 'Play_UI_Divination_Fire', 
    ['DIVINATION_FIRE_LEAVE_LOOP_EVENT'] = 'Stop_UI_Divination_Fire_Loop', 
    ['DIVINATION_LEAVE_ACTION_EVENT'] = 'Stop_Divination_Animation', 
    ['DIVINATION_LEAVE_AMB_EVENT'] = 'Stop_UI_Divination_AmbLoop', 
    ['ENTER_RAIN_AMB_EVENT'] = 'Play_Amb_Weather_Rain', 
    ['FEMALE_STATE'] = 'Female', 
    ['FOOTSTEP_N_ACTION_LIMIT'] = '30', 
    ['GAME_TIME_RTPC'] = 'DayTime', 
    ['GLOBAL_CLOSE_EVENT'] = 'Set_State_UI_Interface_Global_Close', 
    ['GLOBAL_OPEN_EVENT'] = 'Set_State_UI_Interface_Global_Open', 
    ['GUILD_DANCING_CLICK'] = 'Play_UI_Dancing_Click', 
    ['GUILD_DANCING_CLICK_LONG'] = 'Play_UI_Dancing_Click_Long', 
    ['GUILD_DANCING_CLICK_LONG_STOP'] = 'Stop_UI_Dancing_Click_Long', 
    ['GUILD_DANCING_CLICK_SLIDE'] = 'Play_UI_Dancing_Click_Slide', 
    ['GUILD_DANCING_PLAY_BANK'] = 'MX_Dancing_Guild_Waltz_NobleSteps', 
    ['GUILD_DANCING_UI_BANK'] = 'UI_Dance', 
    ['IN_BATTLE_STATE'] = 'InBattle', 
    ['IN_DIALOGUE_STATE'] = 'DialogueEnter', 
    ['IN_INTERFACE_GLOBAL_STATE'] = 'Interface_Global', 
    ['IN_INTERFACE_HALF_STATE'] = 'Interface_Half', 
    ['IN_OTHER_BATTLE_STATE'] = 'OtherInBattle', 
    ['LEAVE_RAIN_AMB_EVENT'] = 'Stop_Amb_Weather_Rain', 
    ['LOGIN_STAGE_BANK'] = 'Login', 
    ['LOGIN_STAGE_ENTER_EVENT'] = 'Play_MX_Login', 
    ['LOGIN_STAGE_LEAVE_EVENT'] = 'Stop_MX_Login', 
    ['MAIL_CAT_CLICK'] = 'Play_UI_Mail_Cat_Click', 
    ['MAIL_CAT_DOUBLECLICK'] = 'Play_UI_Mail_Cat_DoubleClick', 
    ['MAIL_CAT_IDLE_CLICK'] = 'Play_UI_Mail_Cat_Idle_Click', 
    ['MAIL_CAT_IN'] = 'Play_UI_Mail_Cat_In', 
    ['MAIN_PLAYER_GENDER_GROUP'] = 'Player_Gender', 
    ['MALE_STATE'] = 'Male', 
    ['MX_Volume_RTPC'] = 'MX_Volume', 
    ['NPC_COMMON_FOOTSTEP_BANK'] = 'Footstep_Common_Monster', 
    ['OTHER_BATTLE_STATE_GROUP'] = 'MX_Battle_OtherInBattle', 
    ['OUT_BATTLE_STATE'] = 'None', 
    ['OUT_INTERFACE_GLOBAL_STATE'] = 'None', 
    ['OUT_INTERFACE_HALF_STATE'] = 'None', 
    ['OUT_OTHER_BATTLE_STATE'] = 'None', 
    ['PirateSchool_Portal_Appear'] = 'Play_Amb_PirateSchool_Portal_Appear', 
    ['PirateSchool_Portal_Disappear'] = 'Play_Amb_PirateSchool_Portal_Disappear', 
    ['PirateSchool_Portal_Idle'] = 'Play_Amb_PirateSchool_Portal_Idle', 
    ['Player_Height'] = 'Player_Height', 
    ['ROLE_CUSTOMIZATION_STAGE_ENTER_EVENT'] = 'Play_MX_CharacterCustomization', 
    ['ROLE_CUSTOMIZATION_STAGE_LEAVE_EVENT'] = 'Stop_MX_CharacterCustomization', 
    ['SELECT_ROLE_STAGE_ENTER_EVENT'] = 'Play_MX_CharacterSelection', 
    ['SELECT_ROLE_STAGE_LEAVE_EVENT'] = 'Stop_MX_CharacterSelection', 
    ['SFX_Volume_RTPC'] = 'SFX_Volume', 
    ['STAMINA_RTPC'] = 'Stamina_Bar', 
    ['Skill_Battle_Lock_RTPC'] = 'Skill_Battle_Lock', 
    ['Skill_Battle_Playback_RTPC'] = 'Skill_Battle_Playback', 
    ['TIME_CHESS_LAND_EVENT'] = 'Play_Amb_PirateSchool_ChessFall', 
    ['TRACE_BE_FOUND_EVENT'] = 'Play_GamePlay_Trace_BeFound', 
    ['TRACE_WARNING_EVENT'] = 'Play_GamePlay_Trace_Warning', 
    ['UI_ARBITRATOR_MOOD_RTPC'] = 'UI_Arbitrator_Mood', 
    ['UI_INTERFACE_GLOBAL_GROUP'] = 'UI_Interface_Global', 
    ['UI_INTERFACE_HALF_GROUP'] = 'UI_Interface_Half', 
    ['UI_QTE_Pan_RTPC'] = 'UI_QTE_Pan', 
    ['Vo_Volume_RTPC'] = 'Vo_Volume', 
    ['WITCH_ENTER_AMB_EVENT'] = 'Play_UI_Witch_AmbLoop', 
    ['WITCH_LEAVE_ACTION_EVENT'] = 'Stop_Witch_Animaion', 
    ['WITCH_LEAVE_AMB_EVENT'] = 'Stop_UI_Witch_AmbLoop', 
}

Enum.EAvatarInteractData = {
    ['ARBITRATOR_TRIAL_PLAYER'] = 4240008, 
    ['FORTUNE_REQUEST'] = 4240005, 
    ['FORTUNE_SWITCH'] = 4240006, 
    ['JOIN_WITCH_SLAVE'] = 4240002, 
    ['JOKER_EGG'] = 4240004, 
    ['JOKER_FLOWER'] = 4240003, 
    ['JOKER_REWARD'] = 4240001, 
    ['WITCH_JOIN_CHAIR'] = 4240007, 
}

Enum.EBasicDanceEvaluateLevelData = {
    ['COOL'] = 4, 
    ['GOOD'] = 3, 
    ['GREAT'] = 2, 
    ['PERFECT'] = 1, 
}

Enum.EBasicDanceQTEData = {
    ['COOL'] = 4, 
    ['GOOD'] = 3, 
    ['GREAT'] = 2, 
    ['MISS'] = 5, 
    ['PERFECT'] = 1, 
}

Enum.EBattleModeData = {
    ['FIGHT_MODE'] = 3, 
    ['PEACE_MODE'] = 1, 
    ['TRIAL_MODE'] = 2, 
}

Enum.EBossMechanismCoolDownData = {
    ['AmonBossRage'] = 6430003, 
    ['CommonBossRage'] = 6430002, 
    ['CotardBossRage'] = 6430009, 
    ['JokerBossRage'] = 6430006, 
    ['MilgongenBossRage'] = 6430010, 
    ['RayBiberBossRage'] = 6430005, 
    ['RayBiberBossRage_Hero'] = 6430007, 
    ['RoseGhostBossRage'] = 6430011, 
    ['SasrielRage'] = 6430008, 
    ['WorldBossContributionDisappear'] = 6430004, 
    ['WorldBossInBattle'] = 6430001, 
}

Enum.ECameraModes = {
    ['CustomRole'] = 100010, 
    ['CutScene'] = 100011, 
    ['DebugFree'] = 100003, 
    ['Dialogue'] = 100005, 
    ['GameControl'] = 100004, 
    ['Interactive'] = 100002, 
    ['ManorBuild'] = 100012, 
    ['Photo'] = 100008, 
    ['RoleShow'] = 100006, 
    ['RoleShowFaceCloseUp'] = 300001, 
    ['SimpleDialogue'] = 100013, 
    ['Third'] = 100001, 
    ['ThirdDialogue'] = 200003, 
    ['ThirdFace'] = 200001, 
    ['ThirdHorse'] = 200004, 
    ['UIView'] = 100007, 
}

Enum.ECameraModifierPriority = {
    ['ACTSet'] = 100, 
    ['BaseSet'] = 0, 
    ['BattleLock'] = 500, 
    ['CameraAnimationSet'] = 9999, 
    ['FireJumpSet'] = 9000, 
    ['InDoorSet'] = 200, 
    ['JoyStickSet'] = 1000, 
    ['OverrideSet'] = 9000, 
    ['SettingSet_Battle'] = 50, 
    ['SettingSet_MaxZoom'] = 50, 
    ['SystemViewOffSet'] = 9500, 
}

Enum.ECampDefineData = {
    ['CampEnforcerTeam'] = 1400005, 
    ['CampNightGoddessChurch'] = 1400002, 
    ['CampNightWatchTeam'] = 1400001, 
    ['CampSecretSociety'] = 1400003, 
    ['Carrige'] = 1400004, 
    ['CompetitionCampA'] = 1400023, 
    ['CompetitionCampB'] = 1400024, 
    ['GVGGroup1'] = 1400030, 
    ['GVGGroup2'] = 1400031, 
    ['GVGGroup3'] = 1400032, 
    ['GVGGroup4'] = 1400033, 
    ['GuildColleagueNeutralCamp'] = 1400012, 
    ['MultiPvpCampA'] = 1400007, 
    ['MultiPvpCampB'] = 1400008, 
    ['TaskEnemyCamp1'] = 1400016, 
    ['TaskEnemyCamp2'] = 1400017, 
    ['TaskEnemyCamp3'] = 1400018, 
    ['TaskFriendlyCamp1'] = 1400020, 
    ['TaskFriendlyCamp2'] = 1400021, 
    ['TaskFriendlyCamp3'] = 1400022, 
    ['TaskNeutralCamp'] = 1400019, 
    ['camp'] = 1400006, 
}

Enum.ECampEnumData = {
    ['Enemy'] = 1, 
    ['Friendly'] = 0, 
    ['Neutral'] = 2, 
}

Enum.ECashMarketConstIntData = {
    ['COIN_AMOUNT_MAX'] = 10000, 
    ['COIN_AMOUNT_MIN'] = 1, 
    ['COIN_RECORD_LASTMAX'] = 4320, 
    ['COIN_RECORD_MAX'] = 20, 
    ['INFORM_ORDER_MAX'] = 20, 
    ['INFORM_REFRESH_CD'] = 5, 
    ['RMB_AMOUNT_MAX'] = 10000, 
    ['RMB_AMOUNT_MIN'] = 10, 
    ['RMB_ORDER_CANCLECD'] = 60, 
    ['RMB_ORDER_LAST'] = 1440, 
    ['RMB_ORDER_MAX'] = 10, 
    ['RMB_RATE_MAX'] = 2000, 
    ['RMB_RATE_MIN'] = 50, 
    ['RMB_RECORD_LASTMAX'] = 4320, 
    ['RMB_RECORD_MAX'] = 20, 
    ['RMB_TAXPERCENT'] = 5, 
    ['STANDARD_ACTIVITY_MONEY'] = 10000, 
    ['STANDARD_CASH_RANGE'] = 20, 
    ['STANDARD_RATE_RANGE'] = 5, 
    ['SYSTEMBUY_CASHRANGE1'] = 5, 
    ['SYSTEMBUY_CASHRANGE2'] = 20, 
    ['SYSTEMBUY_CASHRANGE3'] = 30, 
    ['SYSTEMBUY_CD'] = 10, 
    ['SYSTEMBUY_OUT_CASHRANGE1'] = 0, 
    ['SYSTEMBUY_OUT_CASHRANGE2'] = 10, 
    ['SYSTEMBUY_OUT_CASHRANGE3'] = 15, 
    ['SYSTEMBUY_OUT_CD'] = 15, 
    ['SYSTEMBUY_OUT_SUMMAX'] = 80, 
    ['SYSTEMBUY_RATEMAX'] = 110, 
    ['SYSTEMBUY_SUMMAX'] = 120, 
    ['SYSTEMSELL_AUTO_NUM'] = 5, 
    ['SYSTEMSELL_AUTO_RATE'] = 1, 
    ['SYSTEMSELL_DELAYTIME'] = 10, 
    ['SYSTEMSELL_REQUIRENUM'] = 25, 
}

Enum.ECEModuleTypeData = {
    ['BASE'] = 1, 
    ['EQUIP'] = 4, 
    ['FELLOW'] = 6, 
    ['GUILD_EXERCISE'] = 2, 
    ['SEALED'] = 5, 
    ['SEQUENCE'] = 7, 
    ['SKILL'] = 3, 
}

Enum.EChatChannelData = {
    ['ANNOUNCEMENT'] = 22, 
    ['ANONYMITY'] = 15, 
    ['BATTLE'] = 7, 
    ['CARDGAME'] = 19, 
    ['CHATROOM'] = 26, 
    ['COMBAT'] = 8, 
    ['COMBAT_ACTIVE'] = 11, 
    ['COMBAT_PASSIVE'] = 12, 
    ['COMMON'] = 23, 
    ['FRIEND'] = 10, 
    ['FRIEND_CLUB'] = 25, 
    ['GROUP'] = 6, 
    ['GUILD'] = 3, 
    ['LOUDSPEAKER'] = 24, 
    ['MAPGAME'] = 18, 
    ['NEARBY'] = 1, 
    ['PARTNER'] = 17, 
    ['RECRUIT'] = 21, 
    ['SCHOOL'] = 4, 
    ['SYSTEM'] = 9, 
    ['TEAM'] = 2, 
    ['TROOP'] = 16, 
    ['UNKNOWN_1'] = 13, 
    ['UNKNOWN_2'] = 14, 
    ['WORLD'] = 5, 
    ['X9_TROOP'] = 20, 
}

Enum.EChatRoomConstData = {
    ['ApplicationListNumberMax'] = 80, 
    ['BanSpeakListNumberMax'] = 50, 
    ['BlacklistNumberMax'] = 50, 
    ['ChatRoomCustomIdentityLength'] = 4, 
    ['ChatRoomHyperlinkCD'] = 172800, 
    ['ChatRoomRenewalMailId'] = 6260054, 
    ['ChatRoomRenewalMailTime'] = 86400, 
    ['DefenceQueryMemberListDuration'] = 500, 
    ['DividePullMemberMax'] = 50, 
    ['DonationDisplayCount'] = 50, 
    ['ExtendedTimeItemId'] = 2013013, 
    ['FirstTimePullMemberMax'] = 100, 
    ['MaxExtendedTime'] = 7776000, 
    ['MaxRoomFavorites'] = 20, 
    ['NoticeShowDuration'] = 15000, 
    ['PopularRoomPopularityCriteria'] = 1000, 
    ['QueryMemberListDuration'] = 1000, 
    ['RoomAutoAissolveCountdown'] = 300, 
    ['RoomAutoAissolveTriggerTime'] = 600000, 
    ['RoomMaxNumber'] = 1000, 
    ['RoomMemeberMaxNumber'] = 1000, 
    ['RoomMicApplyMaxLength'] = 16, 
    ['RoomNameMaxLength'] = 18, 
    ['RoomOwnerAssignRoleCountLimit'] = 4, 
    ['RoomOwnerAssignRolePositionLimit'] = 2, 
    ['RoomOwnerAssignmentCD'] = 600, 
    ['RoomSpeakersMaxNumber'] = 30, 
    ['RoomSpeakersOperateMax'] = 50, 
    ['SingleItemExtendedTime'] = 2592000, 
}

Enum.EChatRoomTypeData = {
    ['BookParty'] = 10, 
    ['CourtParty'] = 8, 
    ['DateParty'] = 6, 
    ['Gossip'] = 3, 
    ['Guide'] = 4, 
    ['GuideParty'] = 9, 
    ['Music'] = 2, 
    ['MusicParty'] = 7, 
    ['Other'] = 5, 
    ['SmallTalk'] = 1, 
}

Enum.EChatSystemData = {
    ['ALLOCATION_ALL_GIVEUP'] = 50, 
    ['ALLOCATION_COMPLETE_GREED'] = 48, 
    ['ALLOCATION_COMPLETE_NEED'] = 8, 
    ['ALLOCATION_THROW'] = 7, 
    ['AUCTION_ALL_GIVEUP'] = 47, 
    ['AUCTION_BID'] = 5, 
    ['AUCTION_COMPLETE'] = 6, 
    ['AUCTION_GIVEUP'] = 4, 
    ['CHAT_GROUP_INVITE'] = 13, 
    ['CHAT_GROUP_LEAVE'] = 14, 
    ['FIGHT_ACT'] = 26, 
    ['FIGHT_ATK_ACTIVE'] = 19, 
    ['FIGHT_ATK_PASSIVE'] = 21, 
    ['FIGHT_BUFF_DISAPPEAR'] = 24, 
    ['FIGHT_BUFF_GET'] = 23, 
    ['FIGHT_HPREG'] = 22, 
    ['FIGHT_INTO'] = 17, 
    ['FIGHT_LEAVE'] = 18, 
    ['FIGHT_REG'] = 20, 
    ['FRIEND_ATTRACTION_LEVEL_NOTICE'] = 46, 
    ['GET_EXP'] = 10, 
    ['GET_ITEM'] = 12, 
    ['GET_MONEY'] = 11, 
    ['GIFT_SHOW'] = 16, 
    ['GUILD_CANCEL_ROLE'] = 38, 
    ['GUILD_CHANGE_NAME'] = 40, 
    ['GUILD_CHANGE_NAME_SYS_FRIEND'] = 41, 
    ['GUILD_CHANGE_ROLE'] = 39, 
    ['GUILD_DECLARATION_MODIFY'] = 42, 
    ['GUILD_DEGRADE_NOTICE'] = 44, 
    ['GUILD_FOUNDER_CHANGE_CANDIDATE'] = 34, 
    ['GUILD_FOUNDER_FINISH_IMPEACH'] = 32, 
    ['GUILD_FOUNDER_START_CANDIDATE'] = 33, 
    ['GUILD_GUILD_BADGE_FRAME_CHANGE_NOTIFY'] = 45, 
    ['GUILD_MASCOT'] = 25, 
    ['GUILD_MERGE_SUCCESS_EVENT'] = 43, 
    ['GUILD_ROLE_ASSIGN_TO_OTHER'] = 35, 
    ['GUILD_SET_ROLE'] = 37, 
    ['OTHER_QUIT_GUILD'] = 27, 
    ['PVP_INDIVIDUAL_RESULT'] = 49, 
    ['QUIT_CUT_CANDIDATE_SYS_FRIEND'] = 36, 
    ['SOMEONE_ASIGN_GUILD'] = 30, 
    ['SOMEONE_CREATE_GUILD'] = 28, 
    ['SOMEONE_JOIN_GUILD'] = 29, 
    ['SOMEONE_TAKE_OVER_GUILD_ROLE'] = 31, 
    ['TEAM_CAPTAIN'] = 3, 
    ['TEAM_JOIN'] = 1, 
    ['TEAM_LEAVE'] = 2, 
    ['TEAM_TARGET'] = 9, 
}

Enum.EClimateType = {
    ['Cloudy'] = 5, 
    ['DenseCloud'] = 6, 
    ['Foggy'] = 3, 
    ['HeavyFog'] = 4, 
    ['HeavyRain'] = 2, 
    ['Rainbow'] = 13, 
    ['Sunny'] = 1, 
}

Enum.ECrowdNPCConst = {
    ['AvoidAnimPlayTime'] = 3, 
    ['AvoidStandTime'] = 4, 
    ['BikeHitRadius'] = 1, 
    ['BikeMassAgentCompPath'] = 6, 
    ['CarriageHitRadius'] = 2, 
    ['InteractorTime'] = 14, 
    ['NPCMassAgentCompPath'] = 5, 
    ['PlayerHitAnim'] = 8, 
    ['PlayerHitBubbleText'] = 9, 
    ['PlayerHitBubbleVoice'] = 10, 
    ['PlayerPerformanceAnim'] = 11, 
    ['PlayerPerformanceText'] = 12, 
    ['PlayerPerformanceVoice'] = 13, 
    ['VehicleStaticSpeed'] = 7, 
}

Enum.ECustomRoleCameraType = {
    ['Camera_Body'] = 'Camera_Body', 
    ['Camera_Ear'] = 'Camera_Ear', 
    ['Camera_Eye'] = 'Camera_Eye', 
    ['Camera_Face'] = 'Camera_Face', 
    ['Camera_Makeup'] = 'Camera_Makeup', 
    ['Camera_Mouth'] = 'Camera_Mouth', 
    ['Camera_Nose'] = 'Camera_Nose', 
}

Enum.ECustomRoleDataType = {
    ['Animation'] = 'Animation', 
    ['BodyPreset'] = 'BodyPreset', 
    ['Face'] = 'Face', 
    ['Fashion'] = 'Fashion', 
    ['HairColor'] = 'HairColor', 
    ['HeadPreset'] = 'HeadPreset', 
    ['MakeUp'] = 'MakeUp', 
    ['Preset'] = 'Preset', 
    ['Scene'] = 'Scene', 
}

Enum.ECustomRoleSliderType = {
    ['Curve'] = 'Curve', 
    ['Param'] = 'Param', 
    ['Preset'] = 'Preset', 
}

Enum.ECustomRoleWidgetDiffType = {
    ['Profession'] = 'Profession', 
    ['Same'] = 'Same', 
    ['Sex'] = 'Sex', 
}

Enum.EDancingPartyEvaluateLevelData = {
    ['COOL'] = 4, 
    ['GOOD'] = 3, 
    ['GREAT'] = 2, 
    ['PERFECT'] = 1, 
}

Enum.EDancingPartyQTEData = {
    ['COOL'] = 4, 
    ['GOOD'] = 3, 
    ['GREAT'] = 2, 
    ['MISS'] = 5, 
    ['PERFECT'] = 1, 
}

Enum.EDancingPartyStageData = {
    ['CLOSE'] = 0, 
    ['DANCE'] = 4, 
    ['INVITE'] = 1, 
    ['MATCH'] = 2, 
    ['REWARD'] = 6, 
    ['SETTLE'] = 5, 
    ['SHOW'] = 3, 
}

Enum.EDialogPopUpData = {
    ['ACCEPT_REVIVE_CONFIRM'] = 6410003, 
    ['ACCEPT_SELFREVIVE_CONFIRM'] = 6410022, 
    ['ACCOUNT_BAN_KICK_OUT'] = 6415085, 
    ['ACTIVITY_OPEN'] = 6415107, 
    ['ADDBLACKLIST'] = 6415046, 
    ['ARBITRATOR_GIVE_UP'] = 6415301, 
    ['ATALASHI_VERSION_IGNORE'] = 6415060, 
    ['ATALASHI_VERSION_QUIT'] = 6415061, 
    ['AUCTION_MAX_PRICE_COMFIRM'] = 6415057, 
    ['BASICDANCE_ACCEPTOR'] = 6415384, 
    ['BASICDANCE_INITIATOR'] = 6415383, 
    ['BASICDANCE_MULTIPLAYER_DANCE'] = 6415385, 
    ['BASICDANCE_STAGE_EXIT'] = 6415387, 
    ['BASICDANCE_TEAM_EXIT'] = 6415386, 
    ['BID_COMFIRM'] = 6415056, 
    ['BLACKLIST_GIVE'] = 6415027, 
    ['BREAK_STUCK_CONFIRM'] = 6410027, 
    ['CASHMARKET_CANCELCONFIRM'] = 6415052, 
    ['CHATROOM_BAN_CONFIRM'] = 6415368, 
    ['CHATROOM_BLACKLIST_CONFIRM'] = 6415327, 
    ['CHATROOM_CHANGE_ROOM_CONFIRM'] = 6415325, 
    ['CHATROOM_EXIST_COMFIRM'] = 6415328, 
    ['CHATROOM_IDENTITY_CHANGE_NAME'] = 6415379, 
    ['CHATROOM_IDENTITY_STATE_SWITCH'] = 6415373, 
    ['CHATROOM_IDENTITY_TAG_REMOVE'] = 6415372, 
    ['CHATROOM_OWNER_ASSIGNMENT'] = 6415345, 
    ['CHATROOM_PERMANENT_ROOM_DISSOLVE'] = 6415371, 
    ['CHATROOM_PERMANENT_ROOM_RENEWAL'] = 6415370, 
    ['CHATROOM_PERMISSION_NOT_SAVE_CONFIRM'] = 6415326, 
    ['CHATROOM_PIN_CANCEL_CONFIRM'] = 6415330, 
    ['CHATROOM_PIN_CHANGE_CONFIRM'] = 6415329, 
    ['CLEAR_CHAT_MESSAGE'] = 6415322, 
    ['CLIENT_ON_DISCONNECT'] = 6415004, 
    ['CLUB_APPLY_CONFIRM'] = 6415338, 
    ['CLUB_EXCHANGE_OFFICIAL'] = 6415333, 
    ['CLUB_OFFICIAL_CHANGE_CONFIRM'] = 6415336, 
    ['CLUB_REMOVE_OFFICIAL'] = 6415332, 
    ['CLUB_RESPOND_CONFIRM'] = 6415337, 
    ['CLUB_RRSIGN_BOSS'] = 6415335, 
    ['CLUB_TRANSFER_BOSS'] = 6415334, 
    ['COLLECTIBLES_JUMP_TO_CUTSCENE'] = 6415364, 
    ['COMBINE_CONFIRM'] = 6410011, 
    ['COMMON_EMPTY_ENSURE_DIALOG'] = 6415081, 
    ['CONFIRM_CANCEL_PVP_MATCH'] = 6410016, 
    ['CONNECT_SERVER_FAIL'] = 6415007, 
    ['CONSECRATION_MONUMENT_LEVELUP'] = 6415344, 
    ['CRAZY_MODE_DOUBLE_CHECK'] = 6415044, 
    ['CREATE_ROLE_ABANDON'] = 6410054, 
    ['CROSS_WORD_PUZZLE_CORRECT'] = 6415006, 
    ['CUSTOMROLE_BACK'] = 6415079, 
    ['CUSTOMROLE_CONTINUELAST'] = 6415080, 
    ['CUSTOMROLE_PANEL_CLOSE'] = 6415097, 
    ['CUSTOMROLE_RESETDATA'] = 6415200, 
    ['DANCE_DOUBLE_MATCH'] = 6415302, 
    ['DANCE_ENERGY_USE'] = 6415303, 
    ['DANCE_EXIT_MIDWAY_CHALLENGE'] = 6415304, 
    ['DANCE_EXIT_MIDWAY_PERFORM'] = 6415306, 
    ['DANCE_EXIT_MIDWAY_PERFORM_ENERGY'] = 6415307, 
    ['DANCE_PARTNER_EXIT_MIDWAY'] = 6415305, 
    ['DANCINGPARTY_PARTNER_RELIEVE'] = 6415077, 
    ['DANCINGPARTY_PARTNER_RELIEVE_LEAVE'] = 6415078, 
    ['DEFEQUIP_REVERT_CONFIRM'] = 6410056, 
    ['DELETE_EMOJI_CHECK'] = 6415319, 
    ['DELETE_FRIEND'] = 6415045, 
    ['DISCONNECT_CONFIRM'] = 6410020, 
    ['DLC_NOT_DOWNLOADED_CONFIRM'] = 6410021, 
    ['ELEMENT_POINT_RESET_CHECK'] = 6415352, 
    ['ENFORCE_FELLOWPIECE_COMPOSITION'] = 6410058, 
    ['ENTER_CROSS'] = 6415036, 
    ['ENTER_PIRATE_SCHOOL'] = 6410061, 
    ['EQUIPMENT_IN_SUIT_DECOMPOSE'] = 6415108, 
    ['EQUIPMENT_IN_SUIT_TO_WAREHOUSE'] = 6415111, 
    ['EQUIP_HIGH_FORGE'] = 6410018, 
    ['EQUIP_TAB1_LOWER'] = 6410067, 
    ['EQUIP_TAB1_UNBIND'] = 6410068, 
    ['EQUIP_TAB2_ALREADY'] = 6410069, 
    ['EQUIP_TAB2_CHOOSECONFIRM'] = 6410043, 
    ['EQUIP_TAB3_GIVEUP'] = 6410041, 
    ['EQUIP_TAB3_GIVEUP_HIGHERSCORE'] = 6410040, 
    ['EQUIP_TAB3_HAVERARE'] = 6410042, 
    ['EQUIP_TAB3_HIGHERSCORE'] = 6410035, 
    ['EQUIP_TAB3_LOWERSCORE'] = 6410036, 
    ['EQUIP_TAB3_OUTCONFIRM'] = 6410034, 
    ['EQUIP_TAB3_RAREWORD'] = 6410037, 
    ['EQUIP_TAB4_RAREWORD'] = 6410038, 
    ['EQUIP_WEARTOBIND'] = 6410039, 
    ['EQUIP_WORDREVERT_ALL'] = 6410071, 
    ['EQUIP_WORDREVERT_SINGLE'] = 6410070, 
    ['EXCHANGE_OVER50_COMFIRM'] = 6415055, 
    ['EXIT_ESCORTTASKPLANE'] = 6415048, 
    ['EXIT_PLANE'] = 6410050, 
    ['FASHION_ADJUST_QUIT_NOT_SAVED'] = 6415090, 
    ['FASHION_CANT_BUY_TIPS'] = 6415094, 
    ['FASHION_GO_PHOTOGRAPHING_CHECK'] = 6415088, 
    ['FASHION_MAKEUP_CONFIRM'] = 6415093, 
    ['FASHION_MAKEUP_QUIT'] = 6415095, 
    ['FASHION_MAKEUP_RESET'] = 6415096, 
    ['FASHION_PLAN_REPLACE'] = 6415091, 
    ['FASHION_PLAN_RESTORE'] = 6415089, 
    ['FASHION_PLAN_SAVE'] = 6415092, 
    ['FASHION_PURCHASE_CHECK'] = 6415087, 
    ['FASHION_QUIT_NOT_SAVED'] = 6415086, 
    ['FRIEND_CLUB_CLEAR_MSG'] = 6415010, 
    ['FRIEND_CLUB_KICK_OUT'] = 6415012, 
    ['FRIEND_CLUB_LEAVE'] = 6415009, 
    ['FRIEND_GIVE_ATTRACTION_MAX'] = 6415028, 
    ['FRIEND_GROUP_DELETE'] = 6415013, 
    ['FRIEND_SETTING_WARNING_PLEASE_ADD_FIRST'] = 6415011, 
    ['GROUPCHAT_DISSOLVE'] = 6410053, 
    ['GROUP_CONVENE'] = 6415034, 
    ['GROUP_DISSOLVE'] = 6415031, 
    ['GROUP_EXIT_IN_LEAGUE'] = 6415309, 
    ['GROUP_KICK_MEMBER'] = 6415033, 
    ['GROUP_LEADER_CHANGE_CONFIRM'] = 6415308, 
    ['GROUP_LEADER_TRANSFER'] = 6415032, 
    ['GUILDBATTLEFIELD_ENTER_CHECK'] = 6415074, 
    ['GUILDBATTLEFIELD_QUIT_CHECK'] = 6415073, 
    ['GUILD_CREATE'] = 6410031, 
    ['GUILD_CREATE_CANCEL'] = 6410032, 
    ['GUILD_INVITE'] = 6415014, 
    ['GUILD_KICK'] = 6410045, 
    ['GUILD_LEADER_TRANSFER'] = 6410049, 
    ['GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_LIMIT'] = 6415341, 
    ['GUILD_LEAGUE_JOIN_NOT_SAME_CAMP_TEAM_LIMIT'] = 6415343, 
    ['GUILD_LEAVE'] = 6410046, 
    ['GUILD_LEAVE_PRESIDENT'] = 6410047, 
    ['GUILD_MANAGE_RESIGN'] = 6415063, 
    ['GUILD_MATERIAL_5TASK_CHECK'] = 6415066, 
    ['GUILD_MATERIAL_TASK'] = 6415065, 
    ['GUILD_MERGE'] = 6415018, 
    ['GUILD_PARTY_BUFF_MONEY_EXCHANGE'] = 6415043, 
    ['GUILD_PARTY_BUFF_REFRESH'] = 6415041, 
    ['GUILD_PARTY_INVITE'] = 6415040, 
    ['GUILD_PARTY_RARE_BUFF_REFRESH'] = 6415042, 
    ['GUILD_PRESIDENT_TRANSFER'] = 6410048, 
    ['GUILD_RESPOSE'] = 6410033, 
    ['HOT_PATCH_BIG_VERSION_CONFIRM'] = 6410012, 
    ['HOT_PATCH_DISCONNECT'] = 6415002, 
    ['HOT_PATCH_FIX'] = 6415015, 
    ['HOT_PATCH_MOBILE_CONFIRM'] = 6410014, 
    ['HOT_PATCH_NOTICE_CONFIRM'] = 6410010, 
    ['HOT_PATCH_SMAILL_VERSION_CONFIRM'] = 6410013, 
    ['HOT_PATCH_SPACE_CONFIRM'] = 6415003, 
    ['INVENTORY_BIND'] = 6410026, 
    ['INVENTORY_SLOT_UNLOCK'] = 6415038, 
    ['INVENTORY_UNLOCK'] = 6410023, 
    ['IN_QUEUE_HINT_USE_SDK_LOGIN'] = 6415367, 
    ['IOS_GPU_CAPTURE_FRAME'] = 6415075, 
    ['ITEM_ABANDON_COMFIRM'] = 6410044, 
    ['ITEM_DECOMPOSE_CONFIRM'] = 6410030, 
    ['ITEM_PACK_CONFIRM'] = 6410072, 
    ['KICK_OUT_HOTFIX'] = 6415084, 
    ['KICK_OUT_LOGIN'] = 6415083, 
    ['KICK_OUT_SERVER_SELECT'] = 6415082, 
    ['LEAVE_ARENA12V12_CONFIRM'] = 6410074, 
    ['LEAVE_ARENA3V3_CONFIRM'] = 6410051, 
    ['LEAVE_ARENA50V50_CONFIRM'] = 6415049, 
    ['LEAVE_ARENA_CHAMPION_MATCH_CONFIRM'] = 6415366, 
    ['LEAVE_ARENA_CHAMPION_PREPARE_CONFIRM'] = 6415365, 
    ['LEAVE_DUNGEONTEAM_CONFIRM'] = 6410005, 
    ['LEAVE_DUNGEON_CONFIRM'] = 6410062, 
    ['LEAVE_DUNGEON_CONFIRM_WITHOUT_TEAM'] = 6410015, 
    ['LEAVE_DUNGEON_CONFIRM_WITH_TEAM'] = 6410002, 
    ['LESS_GACHA_TICKET'] = 6410052, 
    ['LOCATION_TRACE'] = 6415311, 
    ['LOGIN_QUEUE_BACK_LOGIN'] = 6415347, 
    ['LOGIN_QUEUE_FINISH_CHECK_ENTER_GAME'] = 6415349, 
    ['LOGIN_QUEUE_FINISH_ENTER_GAME'] = 6415348, 
    ['LOGIN_QUEUE_SWITCH_SERVER'] = 6415346, 
    ['MAIL_COLLECT_CANCEL'] = 6415340, 
    ['MAIL_DELETE_ALL'] = 6410055, 
    ['MAIL_DETACH_ALL'] = 6415339, 
    ['MAIL_REMOVE_FROM_FAV'] = 6415389, 
    ['MAIL_REMOVE_FROM_FAV_DELETE'] = 6415390, 
    ['MAIN_PLAYER_ENTITY_CREATE_FAILED'] = 6415076, 
    ['MANOR_CHANG_NAME'] = 6415380, 
    ['MANOR_RECYCLE_FURNITURE'] = 6415350, 
    ['MANOR_RECYCLE_PCG'] = 6415351, 
    ['MAP_CANCEL_INPUT'] = 6415324, 
    ['MATCHCE_LOW_REMIND'] = 6415313, 
    ['MATCH_NEWMATCH'] = 6410019, 
    ['MEMBER_ADJUST_QUIT_NOT_SAVED'] = 6415388, 
    ['MIDLEAVE_3V3_CONFIRM_TEAM'] = 6410017, 
    ['MODULE_UNLOCK_TASK_PROMPT'] = 6415035, 
    ['MONEY_CONSUME_BOUND_POUNDS'] = 6415381, 
    ['MONEY_EXCHANGE'] = 6415008, 
    ['MONEY_NEEDRMB'] = 6415019, 
    ['MONEY_NORMB'] = 6415025, 
    ['MONEY_RMBEXCHANGE'] = 6415020, 
    ['NEWBIE_GUIDE_SKIP_GROUP'] = 6415314, 
    ['PENCE_EXCHANGE'] = 6415026, 
    ['PHOTOGRAPH_RESTORE'] = 6415391, 
    ['PVP_3V3_IN_TEAM_GROUP_MATCH_QUIT_CONFIRM'] = 6415342, 
    ['PVP_CHAMPION_DISBAND_TROOP_CONFIRM'] = 6415375, 
    ['PVP_CHAMPION_DISBAND_TROOP_CONFIRM_SINGLE'] = 6415378, 
    ['PVP_CHAMPION_QUIT_TROOP_CONFIRM'] = 6415374, 
    ['PVP_CHAMPION_REMOVE_TROOP_MEMBER_CONFIRM'] = 6415376, 
    ['PVP_CHAMPION_TRANSFER_LEADER_CONFIRM'] = 6415377, 
    ['QUEST_ABANDON_CONFIRM'] = 6410004, 
    ['QUIT_GAME_CONFIRM'] = 6410029, 
    ['QUIT_TEAM_CONFIRM'] = 6410006, 
    ['READY_CHECK'] = 6415029, 
    ['RECIPE_DELETE_COMFIRM'] = 6415054, 
    ['RECONNECT_FAIL'] = 6415005, 
    ['REFINE_REPLACE_CHECK'] = 6415071, 
    ['REFRESH_GACHA_SHOP'] = 6410057, 
    ['REMOVE_GROUPCHAT_MEMBER'] = 6415320, 
    ['REMOVE_LOT_GROUPCHAT_MEMBER'] = 6415321, 
    ['REPLACE_FORCED_OFFLINE'] = 6410060, 
    ['REPLACE_ONLINE_COMFIRM'] = 6410059, 
    ['ROLEPLAY_PROMOTION'] = 6415064, 
    ['ROLE_CREATE_UNFINISHED'] = 6415058, 
    ['SCENE_CUSTOM_CHANGE_NAME'] = 6415098, 
    ['SCHEDULE_USED_TOTAL_DORPS'] = 6415059, 
    ['SDK_LOGIN_FAILED'] = 6415047, 
    ['SDK_LOGOUT'] = 6415021, 
    ['SEALED_EQUIP_CHECK'] = 6415072, 
    ['SEALED_RANDOM_HIGH_RESON_ABANDON_CONFIRM'] = 6410064, 
    ['SEALED_RANDOM_LOW_RESON_RESERVE_CONFIRM'] = 6410065, 
    ['SEALED_RANDOM_RESULT_CHECK'] = 6410066, 
    ['SEALED_RANK_UP_CONSUME_CONFIRM'] = 6410063, 
    ['SEALED_REFINE_CHECK'] = 6415069, 
    ['SEALED_UPGRADE_CHECK'] = 6415070, 
    ['SEALED_UPGRADE_RESET'] = 6415068, 
    ['SEFIROT_CORE_SWITCH_CONFIRM'] = 6415067, 
    ['SEND_APPLICATION_CONFIRM'] = 6410008, 
    ['SEND_APPLICATION_CONFIRM_GROUP'] = 6415037, 
    ['SEND_APPLICATION_CONFIRM_TEAM'] = 6415030, 
    ['SEND_FOLLOW_CONFIRM'] = 6410007, 
    ['SERVER_BUSY'] = 6415315, 
    ['SETTING_RESET_PAGE'] = 6415369, 
    ['SOCIAL_FRIEND_GIFT_BUY'] = 6415316, 
    ['SOCIAL_FRIEND_GIFT_BUY_CHECK'] = 6415318, 
    ['SUBMIT_FINAL_PRICE'] = 6415300, 
    ['SWITCH_BATTLE_LINE_CONFIRM'] = 6415312, 
    ['SWITCH_PLAYER_CONFIRM'] = 6410028, 
    ['TAB_RESET_CONFIRM'] = 6410073, 
    ['TAROTTEAM_APPLY_LINK_CONFIRM'] = 6415361, 
    ['TAROTTEAM_FOUND_CEREMONY_POPUP'] = 6415359, 
    ['TAROTTEAM_IMPEACH_POPUP'] = 6415358, 
    ['TAROTTEAM_IMPEACH_POPUP_CAPTAIN'] = 6415363, 
    ['TAROTTEAM_KICK_POPUP'] = 6415354, 
    ['TAROTTEAM_LEAVE_LASTONE_POPUP'] = 6415356, 
    ['TAROTTEAM_LEAVE_POPUP'] = 6415355, 
    ['TAROTTEAM_LEAVE_RESPONDED'] = 6415353, 
    ['TAROTTEAM_TRANSFER_CAPTAIN'] = 6415360, 
    ['TAROTTEAM_UNCREATE_POPUP'] = 6415357, 
    ['TASK_FAILURE_AGAIN'] = 6415362, 
    ['TEAM_ALL_TEAM_APPLY_TIPS'] = 6415382, 
    ['TEAM_CONVERT_MORE_MEMBER'] = 6415017, 
    ['TEAM_CONVERT_TARGET_EMPTY'] = 6415016, 
    ['TEAM_DISMISSION'] = 6415023, 
    ['TEAM_EXIT'] = 6415024, 
    ['TEAM_EXIT_IN_GROUP'] = 6415310, 
    ['TEAM_FOLLOW_SINGLE_PLANE'] = 6415062, 
    ['TEAM_INPLACE_CONFIRM'] = 6415022, 
    ['TEARDOWN_RARITY_CONFIRM'] = 6410009, 
    ['TEST'] = 6410001, 
    ['TURNTABLE_RESET_CONFIRM'] = 6415331, 
    ['WAREHOUSE_SLOT_UNLOCK'] = 6415039, 
    ['WAREHOUSE_UNLOCK'] = 6410024, 
    ['WAREHOUSE_UNLOCK_PAGE'] = 6410025, 
    ['WATERPIPE_RESET_CONFIRM'] = 6415323, 
    ['WORLD_BOSS_CAPTAIN_CHANGE_LINE'] = 6415103, 
    ['WORLD_BOSS_CAPTAIN_TRANSFER_PERMISSION'] = 6415109, 
    ['WORLD_BOSS_ENTER_NEW_BOSS_AREA'] = 6415102, 
    ['WORLD_BOSS_INDIVIDUAL_CHANGE_LINE'] = 6415105, 
    ['WORLD_BOSS_KILL_FAIL'] = 6415100, 
    ['WORLD_BOSS_LEAVE_TEAM'] = 6415101, 
    ['WORLD_BOSS_MERGE_TEAM'] = 6415106, 
    ['WORLD_BOSS_TEAM_MEMBER_CHANGE_LINE'] = 6415104, 
    ['WORLD_BOSS_TEAM_MEMBER_TRANSFER_PERMISSION'] = 6415110, 
}

Enum.EDiceData = {
    ['ARBITRATOR_DICE'] = 11, 
    ['CHESS_DICE'] = 10, 
    ['EXTRA_RECIPE_DICE'] = 17, 
    ['JOKER_BALL_PLAY'] = 12, 
    ['JOKER_CYCLE_PLAY'] = 13, 
    ['JOKER_HAT_PLAY'] = 14, 
    ['MAIN_CHECK_DICE_FIGHT'] = 16, 
    ['MAIN_CHECK_DICE_TALK'] = 15, 
    ['MUST_FAIL_ADVANCED_TEST'] = 26, 
    ['MUST_FAIL_TEST'] = 24, 
    ['MUST_SUCCESS_ADVANCED_TEST'] = 25, 
    ['MUST_SUCCESS_TEST'] = 23, 
    ['NPC_DICE'] = 3, 
    ['PENGQUAN_DICE'] = 7, 
    ['PLANT_DICE'] = 8, 
    ['QUICK_RECIPE_DICE'] = 2, 
    ['RECIPE_EXPLORE_DICE'] = 1, 
    ['SCENEACTOR_LOAD_DICE_CHECK'] = 19, 
    ['SCENEACTOR_LOAD_DICE_CHECK2'] = 20, 
    ['SCENEACTOR_LOAD_DICE_CHECK3'] = 21, 
    ['SCENEACTOR_LOAD_DICE_CHECK4'] = 22, 
    ['SHERIFF_CHECK_DICE_INVEST'] = 5, 
    ['SHERIFF_CHECK_DICE_SPORT'] = 4, 
    ['SHERIFF_CHECK_DICE_THREAT'] = 6, 
    ['SHERIFF_DEAD_BODY_CHECK'] = 18, 
    ['WITCH_DOUBLE_CHECK'] = 9, 
}

Enum.EDigestionConditionTypeData = {
    ['LEVEL'] = 2, 
    ['STATISTICS'] = 1, 
}

Enum.EElasticStripData = {
    ['ApplyGroupLeader'] = 20011, 
    ['ApplyIndividualPVP'] = 130000, 
    ['ApplyTeamLeader'] = 20010, 
    ['DanceInvite'] = 60001, 
    ['FriendInvite'] = 20000, 
    ['GroupTeamMember'] = 20009, 
    ['GuildDanceInvite'] = 60000, 
    ['InviteOthersInGroup'] = 20007, 
    ['InviteOthersInTeam'] = 20006, 
    ['InviteToChampionTroop'] = 70000, 
    ['InviteToRideTogether'] = 80000, 
    ['LeagueApply'] = 20008, 
    ['NewPrivateChat'] = 10000, 
    ['ReceivedCombineGroupApplication'] = 20012, 
    ['ReceivedCombineTeamApplication'] = 20003, 
    ['ReceivedGroupInvitation'] = 20005, 
    ['ReceivedJoinGroupApplication'] = 20004, 
    ['ReceivedJoinTeamApplication'] = 20001, 
    ['ReceivedSocialAction'] = 30001, 
    ['ReceivedTeamInvitation'] = 20002, 
    ['RoleplayFortuneInvite'] = 140000, 
    ['SendSocialAction'] = 30000, 
    ['WitchChairApplication'] = 150000, 
    ['WitchInvite'] = 160000, 
}

Enum.EEleEffectConstData = {
    ['CRITEFFECT_CORESCORE'] = 0, 
    ['NORMALEFFECT_CORESCORE'] = 0, 
}

Enum.EEleTalentConstData = {
    ['ELEMENT_AUTO_POINT'] = Game.TableDataManager:GetLangStr('str_18212003515136'),
    ['ELEMENT_OFF'] = Game.TableDataManager:GetLangStr('str_18212003514368'),
    ['ELEMENT_ON'] = Game.TableDataManager:GetLangStr('str_18212003514112'),
    ['ELEMENT_POINT_LVMAX_DESC'] = Game.TableDataManager:GetLangStr('str_18212003515648'),
    ['ELEMENT_POINT_RESET'] = Game.TableDataManager:GetLangStr('str_18212003515392'),
    ['ELEMENT_TALENTPOINT'] = Game.TableDataManager:GetLangStr('str_18212003514880'),
    ['ELEMENT_TITLE'] = Game.TableDataManager:GetLangStr('str_61024506028800'),
    ['PROFESSION_DEFAULT_STATE'] = 'DPS', 
}

Enum.EEquipmentConstData = {
    ['EQUIPBAR_MAX_SLOT'] = 12, 
    ['EQUIP_LOG_RARITY'] = 4, 
    ['EQUIP_RANDOMWORDTOBASE'] = 0, 
    ['ITEM_OPERATIONGROUPTYPE'] = 9, 
}

Enum.EEquipmentGrowConstData = {
    ['ADVANCE_PROMOTE_LOWER'] = 100, 
    ['ADVANCE_PROMOTE_UPPER'] = 500, 
    ['ADVANCE_REQUIREMENT'] = 4, 
    ['ENHANCE_PROGRESS_MAX'] = 100, 
    ['ENHANCE_PROPSUITE_MAXLEVEL'] = 3, 
    ['ENHANCE_STAGE_MAX'] = 8, 
    ['EQUIP_ENHANCE_STAGE_LEVEL_STEP'] = 10, 
    ['EQUIP_SUIT_TYPE_MAX'] = 1, 
    ['ITEM_RANDOM_ID'] = 2004000, 
    ['RANDOMITEM_REQUIREMENT'] = 5, 
    ['RANDOM_BATCH_ENHANCE_COUNT'] = 5, 
    ['RANDOM_CONSUMEITEM'] = 2004000, 
    ['RANDOM_DAILYTIMESLIMIT'] = 200, 
    ['RANDOM_REQUIREMENT'] = 5, 
    ['RANDOM_WORD_DOWNLIMIT'] = 5, 
    ['RANDOM_WORD_DOWNMULTI'] = 25, 
    ['RANDOM_WORD_LIMIT'] = 2, 
    ['RANDOM_WORD_UPLIMIT'] = 5, 
    ['RANDOM_WORD_UPMULTI'] = 50, 
}

Enum.EExploreAreafieldIDToSecondLevelAreaID = {
    [1] = 1001001, 
    [2] = 1001002, 
    [3] = 1001003, 
    [1002001] = 1002001, 
    [1002002] = 1002002, 
    [1002003] = 1002003, 
}

Enum.EExploreSecondLevelAreaData = {
    ['AUTUMN_REMAINS'] = 1002003, 
    ['AUTUMN_SOUTH'] = 1002002, 
    ['AUTUMN_VILLAGE'] = 1002001, 
    ['TIENGEN_DARK'] = 1001001, 
    ['TIENGEN_STEAM'] = 1001003, 
    ['TIENGEN_STORM'] = 1001002, 
}

Enum.EExploreSoulItemIDToSoulID = {
    [2006037] = 3, 
    [2099100] = 1, 
    [2099103] = 2, 
    [2099105] = 100201, 
}

Enum.EExploreSoulTaskRingIDToSteleID = {
    [990012] = 1, 
}

Enum.EExploreSteleTaskRingIDToSteleID = {
    [63012] = 3, 
}

Enum.EExploreType2SoulData = {
    [2] = 'SOUL_DARK', 
    [3] = 'SOUL_STEAM', 
    [4] = 'SOUL_STORM', 
}

Enum.EExploreTypeData = {
    ['BOX'] = 1, 
    ['CITY_EVENT'] = 12, 
    ['COLLECTION'] = 15, 
    ['DOUNGEN'] = 13, 
    ['EVENT'] = 8, 
    ['FENGWU_TINGGEN'] = 16, 
    ['LANDMARK'] = 9, 
    ['LOSECONTROL'] = 10, 
    ['MAIN_EVENT'] = 11, 
    ['PUZZLE'] = 5, 
    ['SCHOOL_PUZZLE'] = 7, 
    ['SOUL_DARK'] = 2, 
    ['SOUL_STEAM'] = 3, 
    ['SOUL_STORM'] = 4, 
    ['STELE'] = 6, 
    ['SUB_PUZZLE'] = 14, 
}

Enum.EFashionConstCostData = {
    ['FACE_CHANGE_ITEM_COST'] = {[2001000] = 1000, }, 
    ['STAINHAIR_ADVANCED_COLOR_COST'] = {[2001000] = 1000, }, 
    ['STAINHAIR_NORMAL_COLOR_COST'] = {[2001000] = 500, }, 
    ['STAIN_ADVANCED_COLOR_COST'] = {[2001000] = 1000, }, 
    ['STAIN_NORMAL_COLOR_COST'] = {[2001000] = 500, }, 
    ['STAIN_SLOT_UNLOCK_COST'] = {[2001000] = 1001, }, 
}

Enum.EFashionInitialComp = {
    [1200001] = {[0] = {[1002] = 4220266, [1003] = 4220267, [1006] = 4220265, }, [1] = {[1002] = 4220262, [1003] = 4220263, [1006] = 4220261, }, }, 
    [1200002] = {[0] = {[1002] = 4220206, [1003] = 4220207, [1006] = 4220205, }, [1] = {[1002] = 4220252, [1003] = 4220253, [1005] = 4220254, [1006] = 4220251, }, }, 
    [1200003] = {[0] = {[1002] = 4220217, [1003] = 4220218, [1006] = 4220216, }, [1] = {[1002] = 4220212, [1003] = 4220213, [1005] = 4220214, [1006] = 4220211, }, }, 
    [1200004] = {[0] = {}, [1] = {}, }, 
    [1200005] = {[0] = {[1002] = 4220196, [1003] = 4220197, [1006] = 4220195, }, [1] = {[1002] = 4220192, [1003] = 4220193, [1006] = 4220191, }, }, 
    [1200006] = {[0] = {[1002] = 4220227, [1003] = 4220228, [1006] = 4220226, }, [1] = {[1002] = 4220222, [1003] = 4220223, [1005] = 4220224, [1006] = 4220221, }, }, 
    [1200007] = {[0] = {[1002] = 4220239, [1003] = 4220240, [1006] = 4220238, [2004] = 4220242, [2005] = 4220241, [2011] = 4220243, }, [1] = {[1002] = 4220232, [1003] = 4220233, [1005] = 4220234, [1006] = 4220231, [2004] = 4220235, [2011] = 4220236, }, }, 
}

Enum.EFellowConstIntData = {
    ['ASIST_COMBAT_CE_TRANS'] = 25, 
    ['ASIST_COMBAT_PROP_TRANS'] = 25, 
    ['COMBAT_TYPE_ASSIST'] = 2, 
    ['COMBAT_TYPE_JOIN'] = 1, 
    ['COMBAT_TYPE_MAX'] = 3, 
    ['COMBAT_TYPE_MIN'] = 0, 
    ['EXP_INPUT_LONG_PRESS_ACCEL1'] = 1000, 
    ['EXP_INPUT_LONG_PRESS_ACCEL2'] = 3000, 
    ['EXP_INPUT_LONG_PRESS_DELAY'] = 200, 
    ['EXP_INPUT_LONG_PRESS_SPEED'] = 5, 
    ['EXP_INPUT_LONG_PRESS_SPEED_ACCEL'] = 20, 
    ['EXP_INPUT_LONG_PRESS_SPEED_ACCEL2'] = 100, 
    ['FIRST_STAR_UP_LEVEL_INIT'] = 1, 
    ['FRIEND_ASIST_SLOT_MAX'] = 4, 
    ['FRIEND_ASIST_SLOT_MIN'] = 4, 
    ['JOIN_COMBAT_CE_TRANS'] = 50, 
    ['JOIN_COMBAT_PROP_TRANS'] = 50, 
    ['LEVEL_INIT'] = 1, 
    ['MAX_STAR_UP_LEVEL'] = 6, 
    ['SECOND_STAR_UP_LEVEL_INIT'] = 0, 
    ['SLOT_1'] = 1, 
    ['SLOT_2'] = 2, 
    ['SLOT_3'] = 3, 
    ['SLOT_4'] = 4, 
    ['SLOT_MAX'] = 5, 
    ['SLOT_MIN'] = 0, 
    ['STAR_LEVEL_UP_TYPE_FIRST'] = 1, 
    ['STAR_LEVEL_UP_TYPE_SECOND'] = 2, 
    ['STAR_UP_CONFIG_ID_FACTOR'] = 100000, 
}

Enum.EFellowConstStrData = {
    ['STAR_UP_COMMON_COST_PREFIX'] = 'StarUpCommonCost', 
    ['STAR_UP_PIECE_COST_PREFIX'] = 'StarUpPieceCost', 
    ['STAR_UP_PROP_PREFIX'] = 'StarUpProp', 
}

Enum.EFellowGachaConstIntData = {
    ['FELLOW_GACHA_LIMIT_TIMES'] = 999999, 
    ['FELLOW_GACHA_R_KEY'] = 10, 
    ['FELLOW_GACHA_SR_KEY'] = 11, 
    ['FELLOW_GACHA_SSR_KEY'] = 12, 
    ['FELLOW_GACHA_TYPE_SINGLE'] = 1, 
    ['FELLOW_GACHA_TYPE_TEN'] = 10, 
    ['FELLOW_GACHA_UR_KEY'] = 13, 
    ['GACHA_RECORD_TIME'] = 500, 
    ['GUARANTEE_ID_FACTOR'] = 10000, 
    ['UR_GUARANTEE_TIMES_INIT'] = 90, 
}

Enum.EFellowLVData = {
    ['MAX_FELLOW_LEVEL'] = 100, 
}

Enum.EFellowPropConstData = {
    ['ChaosAnti_N'] = 21, 
    ['ChaosAtk_N'] = 20, 
    ['IgnoreChaosAnti_N'] = 22, 
    ['MaxHp_N'] = 1, 
    ['mAtkMax_N'] = 12, 
    ['mAtkMin_N'] = 11, 
    ['mBlock_N'] = 19, 
    ['mCritAnti_N'] = 15, 
    ['mCritDef_N'] = 17, 
    ['mCritHurt_N'] = 16, 
    ['mCrit_N'] = 14, 
    ['mDef_N'] = 13, 
    ['mPierce_N'] = 18, 
    ['pAtkMax_N'] = 3, 
    ['pAtkMin_N'] = 2, 
    ['pBlock_N'] = 10, 
    ['pCritAnti_N'] = 6, 
    ['pCritDef_N'] = 8, 
    ['pCritHurt_N'] = 7, 
    ['pCrit_N'] = 5, 
    ['pDef_N'] = 4, 
    ['pPierce_N'] = 9, 
}

Enum.EffectConditionType = {
    ['CheckActionState'] = 30, 
    ['CheckBBV'] = 9, 
    ['CheckBattleInOut'] = 14, 
    ['CheckBuffLayer'] = 3, 
    ['CheckBuffRemainingLife'] = 38, 
    ['CheckEventCD'] = 29, 
    ['CheckHasNoParallelSkillProcessing'] = 20, 
    ['CheckHasOverflowHeal'] = 39, 
    ['CheckHp'] = 1, 
    ['CheckHurtValue'] = 28, 
    ['CheckInState'] = 11, 
    ['CheckInStateByParaID'] = 34, 
    ['CheckInputDir'] = 37, 
    ['CheckInputDistance'] = 22, 
    ['CheckIsAttachedTarget'] = 27, 
    ['CheckIsInstigator'] = 33, 
    ['CheckIsLockTarget'] = 15, 
    ['CheckIsTeamMember'] = 31, 
    ['CheckLocomotionState'] = 36, 
    ['CheckMovePosture'] = 46, 
    ['CheckPreSkill'] = 13, 
    ['CheckProbability'] = 5, 
    ['CheckProfession'] = 32, 
    ['CheckProfessionState'] = 35, 
    ['CheckProp'] = 2, 
    ['CheckSelfOrientation'] = 19, 
    ['CheckShallowGrave'] = 12, 
    ['CheckSkillEquipped'] = 10, 
    ['CheckSkillMiss'] = 7, 
    ['CheckTargetAngle'] = 8, 
    ['CheckTargetCampAndEntityType'] = 21, 
    ['CheckTargetDistance'] = 6, 
    ['CheckTargetNum'] = 16, 
    ['CheckTargetOrientation'] = 18, 
    ['CheckUsedProfessionProp'] = 49, 
    ['CompareMutiBuffLayer'] = 48, 
    ['HasBuff'] = 45, 
    ['IsBoss'] = 24, 
    ['IsEliteMonster'] = 26, 
    ['IsHitState'] = 47, 
    ['IsNormalMonster'] = 25, 
    ['IsPlayer'] = 23, 
    ['IsPlayerDPS'] = 43, 
    ['IsPlayerHealer'] = 44, 
    ['IsPlayerMelee'] = 40, 
    ['IsPlayerRanged'] = 41, 
    ['IsPlayerTank'] = 42, 
}

Enum.EffectEventType = {
    ['OnAbNormalState'] = 23, 
    ['OnAfterApplyDamage'] = 2, 
    ['OnAfterReceiveDamage'] = 1, 
    ['OnApplyHeal'] = 12, 
    ['OnBattleButtonInteract'] = 38, 
    ['OnBattleInOut'] = 20, 
    ['OnBeforeReceiveDamage'] = 26, 
    ['OnBuffAdd'] = 30, 
    ['OnBuffChange'] = 33, 
    ['OnBuffLayerMatch'] = 29, 
    ['OnBuffRemove'] = 31, 
    ['OnFlowChartMsg'] = 39, 
    ['OnHPChanged'] = 21, 
    ['OnHealOver'] = 22, 
    ['OnHeroInRange'] = 32, 
    ['OnHitAttack'] = 8, 
    ['OnHitBullet'] = 10, 
    ['OnLocalShieldBoom'] = 16, 
    ['OnMovement'] = 25, 
    ['OnPreDead'] = 4, 
    ['OnPreHurt'] = 36, 
    ['OnProfessionProp1Changed'] = 24, 
    ['OnQTEEnd'] = 28, 
    ['OnQTESuccess'] = 27, 
    ['OnReceiveCrit'] = 41, 
    ['OnReceiveHeal'] = 13, 
    ['OnReceiveHitAttack'] = 9, 
    ['OnReceiveHitBullet'] = 11, 
    ['OnRecvSignal'] = 35, 
    ['OnSkillCast'] = 17, 
    ['OnSkillEnd'] = 19, 
    ['OnSkillHit'] = 18, 
    ['OnSkillPress'] = 7, 
    ['OnSkillSocketChange'] = 15, 
    ['OnSkillTriggerCD'] = 14, 
    ['OnSummonClear'] = 37, 
    ['OnToGround'] = 34, 
    ['Tick'] = 40, 
}

Enum.EffectTaskType = {
    ['AddAttachment'] = 130, 
    ['AddAura'] = 20, 
    ['AddBattleButton'] = 195, 
    ['AddBubble'] = 94, 
    ['AddBuff'] = 4, 
    ['AddComboWindow'] = 194, 
    ['AddCounterAttack'] = 175, 
    ['AddDecal'] = 6, 
    ['AddFStateProp'] = 179, 
    ['AddFStatePropRange'] = 180, 
    ['AddField'] = 34, 
    ['AddFoolCardTrailEffectOnUI'] = 210, 
    ['AddGhost'] = 95, 
    ['AddLineBuff'] = 190, 
    ['AddMesh'] = 8, 
    ['AddProfessionProp'] = 104, 
    ['AddRandomBuff'] = 123, 
    ['AddSkillAgent'] = 166, 
    ['AddTrap'] = 11, 
    ['AnimFreezeFrame'] = 254, 
    ['AnimInstCrossFade'] = 122, 
    ['AttackBetweenPos'] = 241, 
    ['AttackFilterByHero'] = 135, 
    ['AttackRange'] = 242, 
    ['BackSwing'] = 71, 
    ['BasicMoveAdjust'] = 131, 
    ['BoneTrackActor'] = 252, 
    ['BreakDefense'] = 202, 
    ['BuffChangeParam'] = 19, 
    ['BuffDistribution'] = 147, 
    ['BuffSetLifeTime'] = 66, 
    ['CD'] = 36, 
    ['CameraDirMove'] = 255, 
    ['CameraNiagara'] = 92, 
    ['CameraParamModify'] = 61, 
    ['CameraParamSet'] = 60, 
    ['CameraRadialBlur'] = 62, 
    ['CameraRotateFrom'] = 266, 
    ['CameraShake'] = 59, 
    ['CameraTurnToLock'] = 267, 
    ['CastAttack'] = 226, 
    ['ChainAttack'] = 263, 
    ['ChangeAggroValue'] = 201, 
    ['ChangeBreakDefenseLock'] = 105, 
    ['ChangeCurrentSkillCDByFormula'] = 198, 
    ['ChangeIcon'] = 79, 
    ['ChangeIconContinuous'] = 77, 
    ['ChangeLockTargetFromOwnerToSelf'] = 216, 
    ['ChangeMinInFightPropModeList'] = 259, 
    ['ChangePlayerState'] = 204, 
    ['ChangePropByHp'] = 250, 
    ['ChangeWeapon'] = 68, 
    ['ClearAllHit'] = 256, 
    ['ClientTimeline'] = 124, 
    ['CreatFieldRandFromPool'] = 244, 
    ['CreatRandFields'] = 230, 
    ['CreateAura'] = 233, 
    ['CreateBPEffect'] = 223, 
    ['CreateBullet'] = 22, 
    ['CreateBullets'] = 23, 
    ['CreateBulletsRepeat'] = 127, 
    ['CreateField'] = 234, 
    ['CreateInteractor'] = 217, 
    ['CreatePerformBullet'] = 162, 
    ['CreatePerformBulletGroup'] = 163, 
    ['CreatePerformBulletRepeat'] = 164, 
    ['CreateSkillAgent'] = 237, 
    ['CreateSummon'] = 231, 
    ['CreateTrap'] = 232, 
    ['CreateVelField'] = 235, 
    ['DelBuff'] = 27, 
    ['DestroyEntity'] = 54, 
    ['DetectActor'] = 208, 
    ['DisableBattleAbility'] = 265, 
    ['DisableMove'] = 137, 
    ['DisableSkill'] = 134, 
    ['DispelBuffs'] = 165, 
    ['DivideAttack'] = 268, 
    ['DizzinessMove'] = 209, 
    ['DoFightAction'] = 2, 
    ['DoLaserAttack'] = 192, 
    ['DoLineFightAction'] = 191, 
    ['DragonHit'] = 257, 
    ['ExploreElement'] = 96, 
    ['ExploreElementRange'] = 189, 
    ['FlashToLocation'] = 182, 
    ['FollowActor'] = 113, 
    ['HideBone'] = 253, 
    ['HidePet'] = 145, 
    ['HideWeapon'] = 86, 
    ['HitAction'] = 249, 
    ['HurtReviseRealTime'] = 184, 
    ['IconCountDown'] = 76, 
    ['IconHighlight'] = 75, 
    ['ImmuneBuff'] = 120, 
    ['ImmuneDamage'] = 148, 
    ['ImmuneLockAndSearch'] = 258, 
    ['ImmuneStagger'] = 110, 
    ['InfectionBuff'] = 125, 
    ['KeepAura'] = 119, 
    ['KeepBuff'] = 49, 
    ['KeepExpandState'] = 15, 
    ['KeepExtraDamage'] = 224, 
    ['KeepWeaponChange'] = 69, 
    ['KillMyEntity'] = 56, 
    ['KillTarget'] = 156, 
    ['KillTargetEntity'] = 220, 
    ['LimitFightAction'] = 200, 
    ['ListenMoveModeChanged'] = 161, 
    ['ListenMoveModeChangedNew'] = 171, 
    ['MaterialEffect'] = 193, 
    ['MeshFollowBone'] = 100, 
    ['MeshVisibility'] = 14, 
    ['ModelScaleChange'] = 65, 
    ['MovableAttack'] = 239, 
    ['Move'] = 228, 
    ['MoveByAnim'] = 74, 
    ['MoveCast'] = 99, 
    ['MultiMove'] = 245, 
    ['NewPostProcessMaterial'] = 126, 
    ['OpenUI'] = 18, 
    ['PP_ColorFringing'] = 48, 
    ['PP_Grayscale'] = 45, 
    ['PP_InvertColor'] = 46, 
    ['PP_SetInteractiveFog'] = 260, 
    ['PP_Vignette'] = 47, 
    ['PlayAnimationSequence'] = 199, 
    ['PlayAnimationWithLRFoot'] = 160, 
    ['PlayAnimationWithMotionWarp'] = 102, 
    ['PlayAudio'] = 42, 
    ['PlayDecal'] = 40, 
    ['PlayFluidNinja'] = 168, 
    ['PlayJumpAnimation'] = 121, 
    ['PlayLevelSequence'] = 111, 
    ['PlayLinkNiagara'] = 213, 
    ['PlayMontage'] = 25, 
    ['PlayMontageNew'] = 101, 
    ['PlayNiagara'] = 26, 
    ['PlayNiagaraByBuffLayer'] = 247, 
    ['PlayNiagaraInSpellField'] = 206, 
    ['PlayPerformPetAnimation'] = 261, 
    ['PlayRandomNiagara'] = 151, 
    ['PlayWeaponAnimation'] = 82, 
    ['QTE'] = 129, 
    ['RandomMove'] = 240, 
    ['RandomNiagara'] = 38, 
    ['RecoverBreakDefense'] = 243, 
    ['RemindUseSkill'] = 109, 
    ['Revive'] = 78, 
    ['RolePlaySync'] = 181, 
    ['Rotate'] = 37, 
    ['RotateInstant'] = 118, 
    ['RotateWithMotionWarp'] = 172, 
    ['ScaleAttack'] = 251, 
    ['SealMaterialEntryInvalid'] = 262, 
    ['SearchBuffTarget'] = 225, 
    ['SearchMyEntity'] = 138, 
    ['SearchMyEntityPos'] = 139, 
    ['SearchPosNew'] = 229, 
    ['SearchTarget'] = 72, 
    ['SearchTargetEntityPos'] = 219, 
    ['SearchTargetPos'] = 73, 
    ['SearchTargetPosSimple'] = 238, 
    ['SearchTargetSimple'] = 236, 
    ['SendFlowchartEvent'] = 159, 
    ['SendSignal'] = 169, 
    ['SetActorVisible'] = 212, 
    ['SetCamp'] = 116, 
    ['SetCollision'] = 64, 
    ['SetFStateProp'] = 178, 
    ['SetFStatePropRange'] = 183, 
    ['SetHitAndLockLimit'] = 115, 
    ['SetLockTarget'] = 136, 
    ['SetMoLangInvisible'] = 264, 
    ['SetTimeDilation'] = 150, 
    ['ShowAsideTalk'] = 157, 
    ['ShowSkillBtnCountDown'] = 211, 
    ['SkillMapping'] = 80, 
    ['SkillModifyBasicCD'] = 51, 
    ['SkillModifyCurrentCD'] = 50, 
    ['SkillSlotsChange'] = 17, 
    ['SpawnIceField'] = 83, 
    ['SpawnLocalWind'] = 85, 
    ['SpawnWaterWave'] = 143, 
    ['StartMorph'] = 112, 
    ['Suicide'] = 44, 
    ['TargetCastSkill'] = 246, 
    ['TerminateSelf'] = 154, 
    ['ThrowingSkill'] = 214, 
    ['TransferDamageToEntity'] = 248, 
    ['TriggerSkillCD'] = 84, 
    ['ViolentRotation'] = 146, 
    ['WoodenManTask'] = 187, 
}

Enum.EFightPropData = {
    ['AbundanceAnti'] = 1014230, 
    ['AbundanceAtk'] = 1014200, 
    ['AbundanceHurtMulti'] = 1014210, 
    ['AbundanceHurtReduce'] = 1014220, 
    ['AbundanceLevel'] = 1014260, 
    ['AbundanceRate'] = 1014250, 
    ['AdditionalAnti'] = 1017060, 
    ['AdditionalAtk'] = 1017050, 
    ['AdditionalIgnore'] = 1017070, 
    ['AggroPercent'] = 1011230, 
    ['AirShield'] = 1017020, 
    ['AirborneAnti'] = 1015000, 
    ['AirborneHitRate'] = 1015020, 
    ['AllControlAnti'] = 1016030, 
    ['AllControlHitRate'] = 1016050, 
    ['AllEleAnti'] = 1016060, 
    ['AllEleAtk'] = 1016000, 
    ['AllEleHurtMulti'] = 1016010, 
    ['AllEleHurtReduce'] = 1016020, 
    ['AllProHurtMulti'] = 1016130, 
    ['AllProHurtReduce'] = 1016140, 
    ['AllRaceHurtMulti'] = 1016150, 
    ['AllRaceHurtReduce'] = 1016160, 
    ['ApprenticeHurtMulti'] = 1019050, 
    ['ApprenticeHurtReduce'] = 1019250, 
    ['ArbiterHurtMulti'] = 1019030, 
    ['ArbiterHurtReduce'] = 1019230, 
    ['Atk'] = 1016100, 
    ['AtkRange'] = 1011070, 
    ['Block'] = 1016090, 
    ['CalamityAnti'] = 1014430, 
    ['CalamityAtk'] = 1014400, 
    ['CalamityHurtMulti'] = 1014410, 
    ['CalamityHurtReduce'] = 1014420, 
    ['CalamityLevel'] = 1014460, 
    ['CalamityRate'] = 1014450, 
    ['ChaosAnti'] = 1014030, 
    ['ChaosAtk'] = 1014000, 
    ['ChaosHurtMulti'] = 1014010, 
    ['ChaosHurtReduce'] = 1014020, 
    ['ChaosLevel'] = 1014060, 
    ['ChaosRate'] = 1014050, 
    ['Con'] = 1010000, 
    ['CritAbundanceLevel'] = 1014270, 
    ['CritAnti'] = 1016170, 
    ['CritCalamityLevel'] = 1014470, 
    ['CritChaosLevel'] = 1014070, 
    ['CritDarknessLevel'] = 1014370, 
    ['CritDarknessSteal'] = 1011330, 
    ['CritDef'] = 1016180, 
    ['CritDisorderLevel'] = 1014570, 
    ['CritFateAtkMax'] = 1011370, 
    ['CritFateAtkMin'] = 1011360, 
    ['CritFateLevel'] = 1014870, 
    ['CritKnowledgeLevel'] = 1014770, 
    ['CritMysteryLevel'] = 1014170, 
    ['CritTenebrousIgnoreDef'] = 1011350, 
    ['CritTenebrousLevel'] = 1014670, 
    ['DarknessAnti'] = 1014330, 
    ['DarknessAtk'] = 1014300, 
    ['DarknessHurtMulti'] = 1014310, 
    ['DarknessHurtReduce'] = 1014320, 
    ['DarknessLevel'] = 1014360, 
    ['DarknessRate'] = 1014350, 
    ['DarknessSteal'] = 1011320, 
    ['Def'] = 1016080, 
    ['DeltaBeHealed'] = 1011090, 
    ['DeltaBeHurted'] = 1011250, 
    ['DeltaHeal'] = 1011080, 
    ['DeltaHurt'] = 1011240, 
    ['Dex'] = 1010040, 
    ['DisorderAnti'] = 1014530, 
    ['DisorderAtk'] = 1014500, 
    ['DisorderHurtMulti'] = 1014510, 
    ['DisorderHurtReduce'] = 1014520, 
    ['DisorderLevel'] = 1014560, 
    ['DisorderRate'] = 1014550, 
    ['DizzyAnti'] = 1015500, 
    ['DizzyHitRate'] = 1015520, 
    ['DownAnti'] = 1015100, 
    ['DownHitRate'] = 1015120, 
    ['EnhanceAirborne'] = 1015010, 
    ['EnhanceAllControl'] = 1016040, 
    ['EnhanceDizzy'] = 1015510, 
    ['EnhanceDown'] = 1015110, 
    ['EnhanceFear'] = 1015610, 
    ['EnhancePull'] = 1015210, 
    ['EnhanceSilence'] = 1015710, 
    ['EnhanceSleep'] = 1015410, 
    ['EnhanceSlow'] = 1015810, 
    ['EnhanceTied'] = 1015310, 
    ['FateAnti'] = 1014830, 
    ['FateAtk'] = 1014800, 
    ['FateHurtMulti'] = 1014810, 
    ['FateHurtReduce'] = 1014820, 
    ['FateLevel'] = 1014860, 
    ['FateRate'] = 1014850, 
    ['FearAnti'] = 1015600, 
    ['FearHitRate'] = 1015620, 
    ['FeatherwitHurtMulti'] = 1019020, 
    ['FeatherwitHurtReduce'] = 1019220, 
    ['HpReg'] = 1011010, 
    ['IgnoreAbundanceAnti'] = 1014240, 
    ['IgnoreAllEle'] = 1016070, 
    ['IgnoreCalamityAnti'] = 1014440, 
    ['IgnoreChaosAnti'] = 1014040, 
    ['IgnoreDarknessAnti'] = 1014340, 
    ['IgnoreDisorderAnti'] = 1014540, 
    ['IgnoreFateAnti'] = 1014840, 
    ['IgnoreKnowledgeAnti'] = 1014740, 
    ['IgnoreMysteryAnti'] = 1014140, 
    ['IgnoreTenebrousAnti'] = 1014640, 
    ['Int'] = 1010030, 
    ['KnowledgeAnti'] = 1014730, 
    ['KnowledgeAtk'] = 1014700, 
    ['KnowledgeHurtMulti'] = 1014710, 
    ['KnowledgeHurtReduce'] = 1014720, 
    ['KnowledgeLevel'] = 1014760, 
    ['KnowledgeRate'] = 1014750, 
    ['LockedMaxHp'] = 1011040, 
    ['MaxHp'] = 1011000, 
    ['MaxStaminaValue'] = 1011060, 
    ['MysteryAnti'] = 1014130, 
    ['MysteryAtk'] = 1014100, 
    ['MysteryHurtMulti'] = 1014110, 
    ['MysteryHurtReduce'] = 1014120, 
    ['MysteryLevel'] = 1014160, 
    ['MysteryRate'] = 1014150, 
    ['Pierce'] = 1016120, 
    ['Pow'] = 1010010, 
    ['ProHurtMulti9'] = 1019080, 
    ['ProHurtReduce9'] = 1019280, 
    ['PullAnti'] = 1015200, 
    ['PullHitRate'] = 1015220, 
    ['RaceHurtMulti1'] = 1018000, 
    ['RaceHurtMulti2'] = 1018010, 
    ['RaceHurtMulti3'] = 1018020, 
    ['RaceHurtMulti4'] = 1018030, 
    ['RaceHurtMulti5'] = 1018040, 
    ['RaceHurtReduce1'] = 1018100, 
    ['RaceHurtReduce2'] = 1018110, 
    ['RaceHurtReduce3'] = 1018120, 
    ['RaceHurtReduce4'] = 1018130, 
    ['RaceHurtReduce5'] = 1018140, 
    ['ShieldBreak'] = 1017010, 
    ['SilenceAnti'] = 1015700, 
    ['SilenceHitRate'] = 1015720, 
    ['SkillAnti'] = 1017030, 
    ['SkillEnhance'] = 1017040, 
    ['SleepAnti'] = 1015400, 
    ['SleepHitRate'] = 1015420, 
    ['SlowAnti'] = 1015800, 
    ['SlowHitRate'] = 1015820, 
    ['Speed'] = 1011100, 
    ['Str'] = 1010020, 
    ['SunHurtMulti'] = 1019000, 
    ['SunHurtReduce'] = 1019200, 
    ['TenebrousAnti'] = 1014630, 
    ['TenebrousAtk'] = 1014600, 
    ['TenebrousHurtMulti'] = 1014610, 
    ['TenebrousHurtReduce'] = 1014620, 
    ['TenebrousIgnoreDef'] = 1011340, 
    ['TenebrousLevel'] = 1014660, 
    ['TenebrousRate'] = 1014650, 
    ['TiedAnti'] = 1015300, 
    ['TiedHitRate'] = 1015320, 
    ['UltimatePointSpeed'] = 1011380, 
    ['VisionaryHurtMulti'] = 1019010, 
    ['VisionaryHurtReduce'] = 1019210, 
    ['WarriorHurtMulti'] = 1019040, 
    ['WarriorHurtReduce'] = 1019240, 
    ['mAtkMax'] = 1013010, 
    ['mAtkMin'] = 1013000, 
    ['mAtkSpd'] = 1013030, 
    ['mBlock'] = 1013150, 
    ['mCrit'] = 1013080, 
    ['mCritAnti'] = 1013090, 
    ['mCritDef'] = 1013110, 
    ['mCritHurt'] = 1013100, 
    ['mDef'] = 1013040, 
    ['mHurtMulti'] = 1013120, 
    ['mHurtReduce'] = 1013130, 
    ['mIgnoreDef'] = 1013050, 
    ['mPierce'] = 1013140, 
    ['pAtkMax'] = 1012010, 
    ['pAtkMin'] = 1012000, 
    ['pAtkSpd'] = 1012030, 
    ['pBlock'] = 1012150, 
    ['pCrit'] = 1012080, 
    ['pCritAnti'] = 1012090, 
    ['pCritDef'] = 1012110, 
    ['pCritHurt'] = 1012100, 
    ['pDef'] = 1012040, 
    ['pHurtMulti'] = 1012120, 
    ['pHurtReduce'] = 1012130, 
    ['pIgnoreDef'] = 1012050, 
    ['pPierce'] = 1012140, 
}

Enum.EFightPropModeData = {
    ['AbundanceAnti_N'] = 1014231, 
    ['AbundanceAnti_P'] = 1014232, 
    ['AbundanceAtk_N'] = 1014201, 
    ['AbundanceAtk_P'] = 1014202, 
    ['AbundanceHurtMulti_N'] = 1014211, 
    ['AbundanceHurtReduce_N'] = 1014221, 
    ['AbundanceLevel_N'] = 1014261, 
    ['AbundanceRate_N'] = 1014251, 
    ['AdditionalAnti_N'] = 1017061, 
    ['AdditionalAnti_P'] = 1017062, 
    ['AdditionalAtk_N'] = 1017051, 
    ['AdditionalAtk_P'] = 1017052, 
    ['AdditionalIgnore_N'] = 1017071, 
    ['AdditionalIgnore_P'] = 1017072, 
    ['AggroPercent_N'] = 1011231, 
    ['AirShield_N'] = 1017021, 
    ['AirborneAnti_N'] = 1015001, 
    ['AirborneAnti_P'] = 1015002, 
    ['AirborneHitRate_N'] = 1015021, 
    ['AllControlAnti_N'] = 1016031, 
    ['AllControlAnti_P'] = 1016032, 
    ['AllControlHitRate_N'] = 1016051, 
    ['AllEleAnti_N'] = 1016061, 
    ['AllEleAnti_P'] = 1016062, 
    ['AllEleAtk_N'] = 1016001, 
    ['AllEleAtk_P'] = 1016002, 
    ['AllEleHurtMulti_N'] = 1016011, 
    ['AllEleHurtReduce_N'] = 1016021, 
    ['AllProHurtMulti_N'] = 1016131, 
    ['AllProHurtMulti_P'] = 1016132, 
    ['AllProHurtReduce_N'] = 1016141, 
    ['AllProHurtReduce_P'] = 1016142, 
    ['AllRaceHurtMulti_N'] = 1016151, 
    ['AllRaceHurtMulti_P'] = 1016152, 
    ['AllRaceHurtReduce_N'] = 1016161, 
    ['AllRaceHurtReduce_P'] = 1016162, 
    ['ApprenticeHurtMulti_N'] = 1019051, 
    ['ApprenticeHurtMulti_P'] = 1019052, 
    ['ApprenticeHurtReduce_N'] = 1019251, 
    ['ApprenticeHurtReduce_P'] = 1019252, 
    ['ArbiterHurtMulti_N'] = 1019031, 
    ['ArbiterHurtMulti_P'] = 1019032, 
    ['ArbiterHurtReduce_N'] = 1019231, 
    ['ArbiterHurtReduce_P'] = 1019232, 
    ['AtkRange_N'] = 1011071, 
    ['AtkRange_P'] = 1011072, 
    ['Atk_N'] = 1016101, 
    ['Atk_P'] = 1016102, 
    ['Block_N'] = 1016091, 
    ['Block_P'] = 1016092, 
    ['CalamityAnti_N'] = 1014431, 
    ['CalamityAnti_P'] = 1014432, 
    ['CalamityAtk_N'] = 1014401, 
    ['CalamityAtk_P'] = 1014402, 
    ['CalamityHurtMulti_N'] = 1014411, 
    ['CalamityHurtReduce_N'] = 1014421, 
    ['CalamityLevel_N'] = 1014461, 
    ['CalamityRate_N'] = 1014451, 
    ['ChaosAnti_N'] = 1014031, 
    ['ChaosAnti_P'] = 1014032, 
    ['ChaosAtk_N'] = 1014001, 
    ['ChaosAtk_P'] = 1014002, 
    ['ChaosHurtMulti_N'] = 1014011, 
    ['ChaosHurtReduce_N'] = 1014021, 
    ['ChaosLevel_N'] = 1014061, 
    ['ChaosRate_N'] = 1014051, 
    ['Con_N'] = 1010001, 
    ['Con_P'] = 1010002, 
    ['CritAbundanceLevel_N'] = 1014271, 
    ['CritAnti_N'] = 1016171, 
    ['CritAnti_P'] = 1016172, 
    ['CritCalamityLevel_N'] = 1014471, 
    ['CritChaosLevel_N'] = 1014071, 
    ['CritDarknessLevel_N'] = 1014371, 
    ['CritDarknessSteal_N'] = 1011331, 
    ['CritDarknessSteal_P'] = 1011332, 
    ['CritDef_N'] = 1016181, 
    ['CritDef_P'] = 1016182, 
    ['CritDisorderLevel_N'] = 1014571, 
    ['CritFateAtkMax_N'] = 1011371, 
    ['CritFateAtkMax_P'] = 1011372, 
    ['CritFateAtkMin_N'] = 1011361, 
    ['CritFateAtkMin_P'] = 1011362, 
    ['CritFateLevel_N'] = 1014871, 
    ['CritKnowledgeLevel_N'] = 1014771, 
    ['CritMysteryLevel_N'] = 1014171, 
    ['CritTenebrousIgnoreDef_N'] = 1011351, 
    ['CritTenebrousIgnoreDef_P'] = 1011352, 
    ['CritTenebrousLevel_N'] = 1014671, 
    ['DarknessAnti_N'] = 1014331, 
    ['DarknessAnti_P'] = 1014332, 
    ['DarknessAtk_N'] = 1014301, 
    ['DarknessAtk_P'] = 1014302, 
    ['DarknessHurtMulti_N'] = 1014311, 
    ['DarknessHurtReduce_N'] = 1014321, 
    ['DarknessLevel_N'] = 1014361, 
    ['DarknessRate_N'] = 1014351, 
    ['DarknessSteal_N'] = 1011321, 
    ['DarknessSteal_P'] = 1011322, 
    ['Def_N'] = 1016081, 
    ['Def_P'] = 1016082, 
    ['DeltaBeHealed_N'] = 1011091, 
    ['DeltaBeHealed_P'] = 1011092, 
    ['DeltaBeHurted_N'] = 1011251, 
    ['DeltaHeal_N'] = 1011081, 
    ['DeltaHeal_P'] = 1011082, 
    ['DeltaHurt_N'] = 1011241, 
    ['Dex_N'] = 1010041, 
    ['Dex_P'] = 1010042, 
    ['DisorderAnti_N'] = 1014531, 
    ['DisorderAnti_P'] = 1014532, 
    ['DisorderAtk_N'] = 1014501, 
    ['DisorderAtk_P'] = 1014502, 
    ['DisorderHurtMulti_N'] = 1014511, 
    ['DisorderHurtReduce_N'] = 1014521, 
    ['DisorderLevel_N'] = 1014561, 
    ['DisorderRate_N'] = 1014551, 
    ['DizzyAnti_N'] = 1015501, 
    ['DizzyAnti_P'] = 1015502, 
    ['DizzyHitRate_N'] = 1015521, 
    ['DownAnti_N'] = 1015101, 
    ['DownAnti_P'] = 1015102, 
    ['DownHitRate_N'] = 1015121, 
    ['EnhanceAirborne_N'] = 1015011, 
    ['EnhanceAirborne_P'] = 1015012, 
    ['EnhanceAllControl_N'] = 1016041, 
    ['EnhanceAllControl_P'] = 1016042, 
    ['EnhanceDizzy_N'] = 1015511, 
    ['EnhanceDizzy_P'] = 1015512, 
    ['EnhanceDown_N'] = 1015111, 
    ['EnhanceDown_P'] = 1015112, 
    ['EnhanceFear_N'] = 1015611, 
    ['EnhanceFear_P'] = 1015612, 
    ['EnhancePull_N'] = 1015211, 
    ['EnhancePull_P'] = 1015212, 
    ['EnhanceSilence_N'] = 1015711, 
    ['EnhanceSilence_P'] = 1015712, 
    ['EnhanceSleep_N'] = 1015411, 
    ['EnhanceSleep_P'] = 1015412, 
    ['EnhanceSlow_N'] = 1015811, 
    ['EnhanceSlow_P'] = 1015812, 
    ['EnhanceTied_N'] = 1015311, 
    ['EnhanceTied_P'] = 1015312, 
    ['FateAnti_N'] = 1014831, 
    ['FateAnti_P'] = 1014832, 
    ['FateAtk_N'] = 1014801, 
    ['FateAtk_P'] = 1014802, 
    ['FateHurtMulti_N'] = 1014811, 
    ['FateHurtReduce_N'] = 1014821, 
    ['FateLevel_N'] = 1014861, 
    ['FateRate_N'] = 1014851, 
    ['FearAnti_N'] = 1015601, 
    ['FearAnti_P'] = 1015602, 
    ['FearHitRate_N'] = 1015621, 
    ['FeatherwitHurtMulti_N'] = 1019021, 
    ['FeatherwitHurtMulti_P'] = 1019022, 
    ['FeatherwitHurtReduce_N'] = 1019221, 
    ['FeatherwitHurtReduce_P'] = 1019222, 
    ['HpReg_N'] = 1011011, 
    ['HpReg_P'] = 1011012, 
    ['IgnoreAbundanceAnti_N'] = 1014241, 
    ['IgnoreAbundanceAnti_P'] = 1014242, 
    ['IgnoreAllEle_N'] = 1016071, 
    ['IgnoreAllEle_P'] = 1016072, 
    ['IgnoreCalamityAnti_N'] = 1014441, 
    ['IgnoreCalamityAnti_P'] = 1014442, 
    ['IgnoreChaosAnti_N'] = 1014041, 
    ['IgnoreChaosAnti_P'] = 1014042, 
    ['IgnoreDarknessAnti_N'] = 1014341, 
    ['IgnoreDarknessAnti_P'] = 1014342, 
    ['IgnoreDisorderAnti_N'] = 1014541, 
    ['IgnoreDisorderAnti_P'] = 1014542, 
    ['IgnoreFateAnti_N'] = 1014841, 
    ['IgnoreFateAnti_P'] = 1014842, 
    ['IgnoreKnowledgeAnti_N'] = 1014741, 
    ['IgnoreKnowledgeAnti_P'] = 1014742, 
    ['IgnoreMysteryAnti_N'] = 1014141, 
    ['IgnoreMysteryAnti_P'] = 1014142, 
    ['IgnoreTenebrousAnti_N'] = 1014641, 
    ['IgnoreTenebrousAnti_P'] = 1014642, 
    ['Int_N'] = 1010031, 
    ['Int_P'] = 1010032, 
    ['KnowledgeAnti_N'] = 1014731, 
    ['KnowledgeAnti_P'] = 1014732, 
    ['KnowledgeAtk_N'] = 1014701, 
    ['KnowledgeAtk_P'] = 1014702, 
    ['KnowledgeHurtMulti_N'] = 1014711, 
    ['KnowledgeHurtReduce_N'] = 1014721, 
    ['KnowledgeLevel_N'] = 1014761, 
    ['KnowledgeRate_N'] = 1014751, 
    ['LockedMaxHp_N'] = 1011041, 
    ['LockedMaxHp_P'] = 1011042, 
    ['MaxHp_F'] = 1011003, 
    ['MaxHp_N'] = 1011001, 
    ['MaxHp_P'] = 1011002, 
    ['MaxStaminaValue_F'] = 1011063, 
    ['MaxStaminaValue_N'] = 1011061, 
    ['MaxStaminaValue_P'] = 1011062, 
    ['MysteryAnti_N'] = 1014131, 
    ['MysteryAnti_P'] = 1014132, 
    ['MysteryAtk_N'] = 1014101, 
    ['MysteryAtk_P'] = 1014102, 
    ['MysteryHurtMulti_N'] = 1014111, 
    ['MysteryHurtReduce_N'] = 1014121, 
    ['MysteryLevel_N'] = 1014161, 
    ['MysteryRate_N'] = 1014151, 
    ['Pierce_N'] = 1016121, 
    ['Pierce_P'] = 1016122, 
    ['Pow_N'] = 1010011, 
    ['Pow_P'] = 1010012, 
    ['ProHurtMulti9_N'] = 1019081, 
    ['ProHurtMulti9_P'] = 1019082, 
    ['ProHurtReduce9_N'] = 1019281, 
    ['ProHurtReduce9_P'] = 1019282, 
    ['PullAnti_N'] = 1015201, 
    ['PullAnti_P'] = 1015202, 
    ['PullHitRate_N'] = 1015221, 
    ['RaceHurtMulti1_N'] = 1018001, 
    ['RaceHurtMulti1_P'] = 1018002, 
    ['RaceHurtMulti2_N'] = 1018011, 
    ['RaceHurtMulti2_P'] = 1018012, 
    ['RaceHurtMulti3_N'] = 1018021, 
    ['RaceHurtMulti3_P'] = 1018022, 
    ['RaceHurtMulti4_N'] = 1018031, 
    ['RaceHurtMulti4_P'] = 1018032, 
    ['RaceHurtMulti5_N'] = 1018041, 
    ['RaceHurtMulti5_P'] = 1018042, 
    ['RaceHurtReduce1_N'] = 1018101, 
    ['RaceHurtReduce1_P'] = 1018102, 
    ['RaceHurtReduce2_N'] = 1018111, 
    ['RaceHurtReduce2_P'] = 1018112, 
    ['RaceHurtReduce3_N'] = 1018121, 
    ['RaceHurtReduce3_P'] = 1018122, 
    ['RaceHurtReduce4_N'] = 1018131, 
    ['RaceHurtReduce4_P'] = 1018132, 
    ['RaceHurtReduce5_N'] = 1018141, 
    ['RaceHurtReduce5_P'] = 1018142, 
    ['ShieldBreak_N'] = 1017011, 
    ['SilenceAnti_N'] = 1015701, 
    ['SilenceAnti_P'] = 1015702, 
    ['SilenceHitRate_N'] = 1015721, 
    ['SkillAnti_N'] = 1017031, 
    ['SkillAnti_P'] = 1017032, 
    ['SkillEnhance_N'] = 1017041, 
    ['SkillEnhance_P'] = 1017042, 
    ['SleepAnti_N'] = 1015401, 
    ['SleepAnti_P'] = 1015402, 
    ['SleepHitRate_N'] = 1015421, 
    ['SlowAnti_N'] = 1015801, 
    ['SlowAnti_P'] = 1015802, 
    ['SlowHitRate_N'] = 1015821, 
    ['Speed_N'] = 1011101, 
    ['Speed_P'] = 1011102, 
    ['Str_N'] = 1010021, 
    ['Str_P'] = 1010022, 
    ['SunHurtMulti_N'] = 1019001, 
    ['SunHurtMulti_P'] = 1019002, 
    ['SunHurtReduce_N'] = 1019201, 
    ['SunHurtReduce_P'] = 1019202, 
    ['TenebrousAnti_N'] = 1014631, 
    ['TenebrousAnti_P'] = 1014632, 
    ['TenebrousAtk_N'] = 1014601, 
    ['TenebrousAtk_P'] = 1014602, 
    ['TenebrousHurtMulti_N'] = 1014611, 
    ['TenebrousHurtReduce_N'] = 1014621, 
    ['TenebrousIgnoreDef_N'] = 1011341, 
    ['TenebrousIgnoreDef_P'] = 1011342, 
    ['TenebrousLevel_N'] = 1014661, 
    ['TenebrousRate_N'] = 1014651, 
    ['TiedAnti_N'] = 1015301, 
    ['TiedAnti_P'] = 1015302, 
    ['TiedHitRate_N'] = 1015321, 
    ['UltimatePointSpeed_N'] = 1011381, 
    ['UltimatePointSpeed_P'] = 1011382, 
    ['VisionaryHurtMulti_N'] = 1019011, 
    ['VisionaryHurtMulti_P'] = 1019012, 
    ['VisionaryHurtReduce_N'] = 1019211, 
    ['VisionaryHurtReduce_P'] = 1019212, 
    ['WarriorHurtMulti_N'] = 1019041, 
    ['WarriorHurtMulti_P'] = 1019042, 
    ['WarriorHurtReduce_N'] = 1019241, 
    ['WarriorHurtReduce_P'] = 1019242, 
    ['mAtkMax_F'] = 1013013, 
    ['mAtkMax_N'] = 1013011, 
    ['mAtkMax_P'] = 1013012, 
    ['mAtkMin_F'] = 1013003, 
    ['mAtkMin_N'] = 1013001, 
    ['mAtkMin_P'] = 1013002, 
    ['mAtkSpd_N'] = 1013031, 
    ['mAtkSpd_P'] = 1013032, 
    ['mBlock_N'] = 1013151, 
    ['mBlock_P'] = 1013152, 
    ['mCritAnti_N'] = 1013091, 
    ['mCritAnti_P'] = 1013092, 
    ['mCritDef_N'] = 1013111, 
    ['mCritDef_P'] = 1013112, 
    ['mCritHurt_N'] = 1013101, 
    ['mCritHurt_P'] = 1013102, 
    ['mCrit_N'] = 1013081, 
    ['mCrit_P'] = 1013082, 
    ['mDef_F'] = 1013043, 
    ['mDef_N'] = 1013041, 
    ['mDef_P'] = 1013042, 
    ['mHurtMulti_N'] = 1013121, 
    ['mHurtMulti_P'] = 1013122, 
    ['mHurtReduce_N'] = 1013131, 
    ['mHurtReduce_P'] = 1013132, 
    ['mIgnoreDef_N'] = 1013051, 
    ['mIgnoreDef_P'] = 1013052, 
    ['mPierce_N'] = 1013141, 
    ['mPierce_P'] = 1013142, 
    ['pAtkMax_F'] = 1012013, 
    ['pAtkMax_N'] = 1012011, 
    ['pAtkMax_P'] = 1012012, 
    ['pAtkMin_F'] = 1012003, 
    ['pAtkMin_N'] = 1012001, 
    ['pAtkMin_P'] = 1012002, 
    ['pAtkSpd_N'] = 1012031, 
    ['pAtkSpd_P'] = 1012032, 
    ['pBlock_N'] = 1012151, 
    ['pBlock_P'] = 1012152, 
    ['pCritAnti_N'] = 1012091, 
    ['pCritAnti_P'] = 1012092, 
    ['pCritDef_N'] = 1012111, 
    ['pCritDef_P'] = 1012112, 
    ['pCritHurt_N'] = 1012101, 
    ['pCritHurt_P'] = 1012102, 
    ['pCrit_N'] = 1012081, 
    ['pCrit_P'] = 1012082, 
    ['pDef_F'] = 1012043, 
    ['pDef_N'] = 1012041, 
    ['pDef_P'] = 1012042, 
    ['pHurtMulti_N'] = 1012121, 
    ['pHurtMulti_P'] = 1012122, 
    ['pHurtReduce_N'] = 1012131, 
    ['pHurtReduce_P'] = 1012132, 
    ['pIgnoreDef_N'] = 1012051, 
    ['pIgnoreDef_P'] = 1012052, 
    ['pPierce_N'] = 1012141, 
    ['pPierce_P'] = 1012142, 
}

Enum.EFormulaData = {
    ['AbundanceHitRate'] = 1610132, 
    ['AirborneContiHitRate'] = 1610116, 
    ['AirborneHitRate'] = 1610106, 
    ['Arena_12V12ScoreCalc'] = 1612329, 
    ['Arena_3V3ScoreCalc'] = 1612322, 
    ['Arena_5V5ForceCalc'] = 1612324, 
    ['Arena_5V5ScoreCalc'] = 1612323, 
    ['AtkEquip_BaseFormula'] = 1600004, 
    ['AtkEquip_BaseFormula_New'] = 1600104, 
    ['AtkFixedWord_GrowFormula'] = 1600005, 
    ['AtkFixedWord_ReplaceFormula'] = 1600006, 
    ['BuffDuration_Test'] = 1610001, 
    ['BuffLevel_Test'] = 1610000, 
    ['BuffProbability_Test'] = 1610002, 
    ['BuffProbability_Test2'] = 1610004, 
    ['BuffPropModify_01'] = 1610100, 
    ['BuffPropModify_02'] = 1610101, 
    ['BuffScoreToXCalc'] = 1612311, 
    ['BuffScoreToX_ACalc'] = 1612312, 
    ['BuffSheildModify_8130501'] = 1610164, 
    ['BuffSheildModify_8130502'] = 1610165, 
    ['BuffSheildModify_DuXin'] = 1610187, 
    ['BuffSheildModify_aAtk'] = 1610102, 
    ['BuffSheildModify_aAtk15'] = 1610150, 
    ['BuffSheildModify_dMaxHp'] = 1610103, 
    ['BuffSheildModify_dMaxHp05'] = 1610162, 
    ['BuffSheildModify_dMaxHp07'] = 1610163, 
    ['BuffSheildModify_dMaxHp10'] = 1610161, 
    ['BuffSheildModify_dMaxHp15'] = 1610160, 
    ['BuffSheildModify_dMaxHp_REBB'] = 1620002, 
    ['BuffSheildModify_dMaxHp_REBB_HERO'] = 1620010, 
    ['BuffShieldModify_8000226'] = 1610204, 
    ['BuffShieldModify_8100203_1'] = 1610172, 
    ['BuffShieldModify_8100203_2'] = 1610173, 
    ['BuffShieldModify_8100203_3'] = 1610174, 
    ['BuffShieldModify_8100203_4'] = 1610175, 
    ['BuffShieldModify_8100203_5'] = 1610176, 
    ['BuffShieldModify_8100229'] = 1610178, 
    ['BuffShieldModify_8100318'] = 1610200, 
    ['BuffShieldModify_8100318_2'] = 1610201, 
    ['BuffShieldModify_8100318_3'] = 1610202, 
    ['BuffShieldModify_8100318_4'] = 1610203, 
    ['BuffShieldModify_8130701'] = 1610166, 
    ['BuffShieldModify_8130702'] = 1610167, 
    ['BuffShieldModify_8141501'] = 1610168, 
    ['BuffShieldModify_8141502'] = 1610169, 
    ['BuffShieldModify_8141503'] = 1610170, 
    ['BuffShieldModify_8141504'] = 1610171, 
    ['BuffShieldModify_84005511'] = 1610179, 
    ['BuffShield_LiMingKaiJia'] = 1610314, 
    ['BuffStrCalc'] = 1612316, 
    ['CHAMPION_GROUP_BATTLE_SCORE_2_SUPPORT_SCORE_FORMULA_ID'] = 1620013, 
    ['COMMIT_TRENDING_DEGREE'] = 1620014, 
    ['COMMIT_TRENDING_DEGREE_ORDER'] = 1620015, 
    ['CalamityHitRate'] = 1610134, 
    ['ChaosHitRate'] = 1610130, 
    ['DarknessHitRate'] = 1610133, 
    ['DebuffScoreCalc'] = 1612305, 
    ['DebuffScoreSumCalc'] = 1612304, 
    ['DefEquip_BaseGrow'] = 1600017, 
    ['DisorderHitRate'] = 1610135, 
    ['Dividend_Guild'] = 1612101, 
    ['DizzyDuration'] = 1610114, 
    ['DizzyHitRate'] = 1610110, 
    ['DownContiHitRate'] = 1610117, 
    ['DownHitRate'] = 1610105, 
    ['ELIMINATION_SCORE_FORMULA_ID'] = 1620012, 
    ['EquipBreak_4'] = 1600150, 
    ['Exp_DailyDungeon_General'] = 1600201, 
    ['Exp_WeeklyDungeon_General'] = 1600202, 
    ['Exp_WeeklyDungeon_General_half'] = 1600203, 
    ['Exp_WeeklyDungeon_General_onethird'] = 1600204, 
    ['Expect_Combat_Time'] = 1600021, 
    ['ExtraDamage_DMaxHp'] = 1620011, 
    ['FateHitRate'] = 1610138, 
    ['FearHitRate'] = 1610109, 
    ['Fellow_PropGrow1'] = 1600013, 
    ['Fellow_PropGrow2'] = 1600014, 
    ['Fellow_PropGrow3'] = 1600015, 
    ['Fellow_PropGrow4'] = 1600016, 
    ['FengKuangKuiJia_Condition'] = 1610311, 
    ['FengKuangKuiJia_Effect'] = 1610312, 
    ['FengKuangKuiJia_HurtReviseTest'] = 1610331, 
    ['FengKuangKuiJia_HurtValueTest'] = 1610330, 
    ['FinalScoreCalc'] = 1612315, 
    ['Guanzhong_WenyiBuffLv'] = 1610324, 
    ['GuildBattleFiled_BackToFightTimeCalc'] = 1612325, 
    ['GuildBattleFiled_SourceAreaCalc'] = 1612326, 
    ['Guild_Contri_WageFormula'] = 1600008, 
    ['Guild_Day_Max_Kick_Member_NumFormula'] = 1600012, 
    ['Guild_Level_WageFormula'] = 1600007, 
    ['Guild_Max_FundsFormula'] = 1600009, 
    ['Guild_Max_Member_NumFormula'] = 1600010, 
    ['Guild_Total_Max_Member_NumFormula'] = 1600011, 
    ['HSQZ_BuffDes'] = 1610323, 
    ['HealScoreToXCalc'] = 1612309, 
    ['HealScoreToX_ACalc'] = 1612310, 
    ['IgnoreDef_DescOnly'] = 1610220, 
    ['Intervention_1'] = 1612001, 
    ['Intervention_2'] = 1612002, 
    ['Intervention_3'] = 1612003, 
    ['KUIMIYUYAN_Lv1'] = 1610307, 
    ['KnowledgeHitRate'] = 1610137, 
    ['LiMingKaiJia_1'] = 1610308, 
    ['LiMingKaiJia_2'] = 1610309, 
    ['LiMingKaiJia_Shield'] = 1610335, 
    ['LockedHp_005'] = 1610328, 
    ['LockedHp_04'] = 1610327, 
    ['LockedHp_Lv001'] = 1610326, 
    ['MVP_PointCalc'] = 1612321, 
    ['Milgongen_LockedHp_001'] = 1610185, 
    ['Milgongen_LockedHp_010'] = 1610184, 
    ['Milgongen_LockedHp_015'] = 1610190, 
    ['Milgongen_LockedHp_030'] = 1610183, 
    ['Milgongen_LockedHp_035'] = 1610189, 
    ['Milgongen_LockedHp_050'] = 1610182, 
    ['Milgongen_LockedHp_055'] = 1610188, 
    ['Milgongen_LockedHp_070'] = 1610181, 
    ['Milgongen_LockedHp_074'] = 1610180, 
    ['ModeDieScore'] = 1612301, 
    ['MonsterExp_0'] = 1600100, 
    ['MonsterExp_Boss'] = 1600003, 
    ['MonsterExp_Elite'] = 1600002, 
    ['MonsterExp_General'] = 1600001, 
    ['MysteryHitRate'] = 1610131, 
    ['NpcWarning_SpeedCalc'] = 1620001, 
    ['OverHeal'] = 1610337, 
    ['PVP_12V12Battle'] = 1612328, 
    ['PVP_5v5BattleReward'] = 1612302, 
    ['Pence_DailyDungeon_General'] = 1600401, 
    ['Pence_WeeklyDungeon_General'] = 1600402, 
    ['Pence_WeeklyDungeon_General_onethird'] = 1600404, 
    ['Pence_WeeklyDungeon_General_stageReward'] = 1600403, 
    ['PrimaryPlayerCount'] = 1620004, 
    ['PropModify_Test'] = 1610003, 
    ['PullHitRate'] = 1610104, 
    ['QuitSanctionTime_3V3'] = 1620005, 
    ['QuitSanctionTime_5V5'] = 1620006, 
    ['RDamageScoreCalc'] = 1612313, 
    ['RED_PACKET_MAXLIMIT'] = 1612327, 
    ['ReadyConfirmSanctionTime_3V3'] = 1620007, 
    ['ReadyConfirmSanctionTime_5V5'] = 1620008, 
    ['Rec_Price_Cash_Market'] = 1612004, 
    ['Rinal_DailyDungeon_General'] = 1600301, 
    ['Rinal_Explore_Box_Reward'] = 1600405, 
    ['Rinal_Explore_Reward'] = 1600305, 
    ['Rinal_Explore_puzzle_Reward'] = 1600406, 
    ['Rinal_WeeklyDungeon_General'] = 1600302, 
    ['Rinal_WeeklyDungeon_General_onethird'] = 1600304, 
    ['Rinal_WeeklyDungeon_General_stageReward'] = 1600303, 
    ['RobotLevel_1'] = 1600022, 
    ['RolePlaySheriff_DiceAddBounty'] = 1620003, 
    ['SealedSkillToBuff'] = 1610310, 
    ['Sealed_Deline'] = 1600018, 
    ['Sealed_FKKJ_BUFF_MAXLAYER'] = 1600020, 
    ['ServeLevel_ExpTrans'] = 1612299, 
    ['ServerLevel_Test'] = 1612201, 
    ['ShunXiZhiLv_CD'] = 1610334, 
    ['SilentHitRate'] = 1610111, 
    ['SkillCD_Test'] = 1610005, 
    ['SleepHitRate'] = 1610107, 
    ['SlowDuration'] = 1610113, 
    ['SlowHitRate'] = 1610112, 
    ['SufferAndDeathScoreCalc'] = 1612314, 
    ['TDamageScoreSumCalc'] = 1612306, 
    ['TDamageScore_AssistCalc'] = 1612308, 
    ['TDamageScore_KillerCalc'] = 1612307, 
    ['TaLuoSchool_Audrey_Shield'] = 1620016, 
    ['TenebrousHitRate'] = 1610136, 
    ['TiedDuration'] = 1610115, 
    ['TiedHitRate'] = 1610108, 
    ['TotalScoreCalc'] = 1612303, 
    ['TunShiZhe_QTEShield'] = 1610336, 
    ['TwelvePlayer_WinPoint_FailCalc'] = 1612331, 
    ['TwelvePlayer_WinPoint_WinCalc'] = 1612330, 
    ['Ulorus_LockedHp_001'] = 1610186, 
    ['Warrior_HurtReduce_86061060'] = 1610339, 
    ['Warrior_HurtReduce_86061061'] = 1610342, 
    ['Warrior_Shield_86061060'] = 1610338, 
    ['Warrior_Shield_86061061'] = 1610341, 
    ['Warrior_Shield_86061080'] = 1610340, 
    ['WeeklyWage_TarotTeam'] = 1620009, 
    ['WinPoint_FailCalc'] = 1612320, 
    ['WinPoint_FailScoreCalc'] = 1612318, 
    ['WinPoint_WinCalc'] = 1612319, 
    ['WinPoint_WinScoreCalc'] = 1612317, 
    ['XGXT_BuffDes'] = 1610325, 
    ['XUEZHIHUA_HurtReduce_Lv1'] = 1610301, 
    ['XUEZHIHUA_HurtReduce_Lv2'] = 1610302, 
    ['XUEZHIHUA_HurtReduce_Lv3'] = 1610303, 
    ['XUEZHIHUA_HurtReduce_Lv4'] = 1610304, 
    ['XUEZHIHUA_HurtReduce_Lv5'] = 1610305, 
    ['XUEZHIHUA_HurtReduce_Lv6'] = 1610306, 
    ['XuanFengLingYu_CD'] = 1610333, 
    ['XueZhiHua_BuffLayer'] = 1600023, 
    ['Xuetu_SkillDes1'] = 1610317, 
    ['Xuetu_SkillDes2'] = 1610318, 
    ['Xuetu_SkillDes3'] = 1610319, 
    ['Xuetu_SkillDes4'] = 1610320, 
    ['Xuetu_SkillDes5'] = 1610321, 
    ['ZhanYiNingJie_AddAtk'] = 1610332, 
    ['Zhanshi_JueJi'] = 1610322, 
    ['Zhanshi_ShuaibaiBuffLv'] = 1610313, 
    ['Zhanshi_SkillDes1'] = 1610315, 
    ['Zhanshi_SkillDes2'] = 1610316, 
}

Enum.EFormulaForClientData = {
    ['Charac_DefShow'] = 1690002, 
    ['Charac_SimpleCo'] = 1690001, 
}

Enum.EFortuityIntConstData = {
    ['FORTUITYLEASTRATE'] = 3, 
    ['FORTUITYTASKENDTIME'] = 5, 
}

Enum.EFriendAddSourceData = {
    ['CHAT'] = 4, 
    ['DATABASE_SYNC'] = 10, 
    ['DECISIVE_ARENA'] = 3, 
    ['DUNGEON'] = 1, 
    ['GUILD'] = 7, 
    ['MOMENTS'] = 12, 
    ['PVPGAME'] = 11, 
    ['SERVER_RECOMMEND'] = 2, 
    ['TEAM'] = 5, 
    ['TEAM_GROUP'] = 6, 
    ['TEAM_PLATFORM'] = 9, 
    ['WORLD'] = 8, 
}

Enum.EFriendAttractionSourceData = {
    ['ACTIVITY'] = 1, 
    ['FIRST_WHISPER_DAILY'] = 5, 
    ['GIFT'] = 3, 
    ['INTERACTIVE_ACTION'] = 2, 
    ['KILL_MONSTER_IN_TEAM'] = 4, 
}

Enum.EFriendImprintData = {
    ['WHISPER'] = 1, 
}

Enum.EFriendRecommandTriggerData = {
    ['CLOSE_COMBAT'] = 2, 
    ['DUNGEON_COMPLETE'] = 4, 
    ['SAME_CITY'] = 3, 
    ['TEAM_LEADER'] = 1, 
}

Enum.EFStatePropData = {
    ['ApprProp1'] = 1022030, 
    ['ApprProp2'] = 1022040, 
    ['ApprProp3'] = 1022050, 
    ['ArbProp1'] = 1022080, 
    ['CurrentMaxHp'] = 1020000, 
    ['EndureValve'] = 1020070, 
    ['FoolProp1'] = 1022100, 
    ['HitLimitCamp'] = 1021060, 
    ['Hp'] = 1020020, 
    ['HpLockValue'] = 1020010, 
    ['IsHealLimit'] = 1021020, 
    ['IsHitLimit'] = 1021000, 
    ['IsLockLimit'] = 1021010, 
    ['IsMustCrit'] = 1021070, 
    ['LockLimitCamp'] = 1021050, 
    ['ProfessionProp1'] = 1022000, 
    ['ProfessionProp2'] = 1022010, 
    ['ProfessionProp3'] = 1022020, 
    ['SeerProp1'] = 1022110, 
    ['Stagger'] = 1020080, 
    ['StaminaConsumeRate'] = 1020040, 
    ['StaminaRecoverRate'] = 1020050, 
    ['StaminaValue'] = 1020030, 
    ['SunProp1'] = 1022060, 
    ['UltimatePoint'] = 1024010, 
    ['UtoProp1'] = 1022070, 
    ['WarProp1'] = 1022090, 
    ['aShield'] = 1023000, 
    ['mBlockLimit'] = 1021040, 
    ['mShield'] = 1023020, 
    ['pBlockLimit'] = 1021030, 
    ['pShield'] = 1023010, 
}

Enum.EFunctionInfoData = {
    ['MODULE_LOCK_ACHIEVEMENT'] = 1241100, 
    ['MODULE_LOCK_AUCTION'] = 1241805, 
    ['MODULE_LOCK_AUTONAVIGATION'] = 1243100, 
    ['MODULE_LOCK_AUTONAVIGATION_MEW'] = 1243101, 
    ['MODULE_LOCK_CASHMARKET'] = 1241804, 
    ['MODULE_LOCK_CE_SCORE'] = 1243501, 
    ['MODULE_LOCK_CE_property'] = 1243502, 
    ['MODULE_LOCK_CHARACTER'] = 1240400, 
    ['MODULE_LOCK_CHAT'] = 1242400, 
    ['MODULE_LOCK_COLLECTIBLES'] = 1241200, 
    ['MODULE_LOCK_COMBAT_3V3'] = 1241601, 
    ['MODULE_LOCK_COMBAT_50V50'] = 1241603, 
    ['MODULE_LOCK_COMBAT_5V5'] = 1241602, 
    ['MODULE_LOCK_CROSS_WORD_PUZZLE'] = 1242800, 
    ['MODULE_LOCK_ClOTH_SHOP'] = 1241827, 
    ['MODULE_LOCK_DEF_EQUIP'] = 1240104, 
    ['MODULE_LOCK_DRUG_SHOP'] = 1241822, 
    ['MODULE_LOCK_DUNGEON'] = 1241500, 
    ['MODULE_LOCK_ELEMENT_MAIN'] = 1240208, 
    ['MODULE_LOCK_ELEMENT_VICE'] = 1240209, 
    ['MODULE_LOCK_EQUIP'] = 1240100, 
    ['MODULE_LOCK_EQUIPMENT_REFORGE_SHOP'] = 1241821, 
    ['MODULE_LOCK_EQUIPMENT_SHOP'] = 1241820, 
    ['MODULE_LOCK_EQUIP_ADVANCE'] = 1240106, 
    ['MODULE_LOCK_EQUIP_ENHANCE'] = 1240107, 
    ['MODULE_LOCK_EQUIP_INFO'] = 1240101, 
    ['MODULE_LOCK_EQUIP_POS_REFORGE'] = 1240103, 
    ['MODULE_LOCK_EQUIP_RANDOM'] = 1240105, 
    ['MODULE_LOCK_EQUIP_REFORGE'] = 1240102, 
    ['MODULE_LOCK_EXCHANGE_HOUSE'] = 1241803, 
    ['MODULE_LOCK_FACTMAINRECAP'] = 1243601, 
    ['MODULE_LOCK_FASHION_SHOP'] = 1240600, 
    ['MODULE_LOCK_FELLOW'] = 1240800, 
    ['MODULE_LOCK_FRIEND'] = 1242500, 
    ['MODULE_LOCK_GACHA'] = 1240700, 
    ['MODULE_LOCK_GETOUT'] = 1243701, 
    ['MODULE_LOCK_GIFT_SHOP'] = 1241828, 
    ['MODULE_LOCK_GUILD'] = 1242700, 
    ['MODULE_LOCK_GUILDLEAGUE'] = 1242704, 
    ['MODULE_LOCK_GUILD_BUILD'] = 1242701, 
    ['MODULE_LOCK_GUILD_PRACTICE'] = 1242702, 
    ['MODULE_LOCK_GUILD_SHOP'] = 1241823, 
    ['MODULE_LOCK_GUILD_WAGES'] = 1242703, 
    ['MODULE_LOCK_HOME'] = 1243200, 
    ['MODULE_LOCK_HUD_SHOP'] = 1241800, 
    ['MODULE_LOCK_ITEMSUBMIT'] = 1242900, 
    ['MODULE_LOCK_LEADMODE'] = 1243000, 
    ['MODULE_LOCK_MAIL'] = 1242600, 
    ['MODULE_LOCK_MENU'] = 1240000, 
    ['MODULE_LOCK_MISTMAINRECAP'] = 1243602, 
    ['MODULE_LOCK_PHARAMCIST'] = 1241001, 
    ['MODULE_LOCK_PHOTOGRAPH'] = 1242100, 
    ['MODULE_LOCK_PLOTCAP'] = 1243600, 
    ['MODULE_LOCK_PVP'] = 1241600, 
    ['MODULE_LOCK_RANKINGLIST'] = 1241300, 
    ['MODULE_LOCK_REPORT'] = 1243702, 
    ['MODULE_LOCK_ROLEMECHANISM_Q'] = 1240210, 
    ['MODULE_LOCK_ROLEPLAY'] = 1241000, 
    ['MODULE_LOCK_SCHEDULE'] = 1242000, 
    ['MODULE_LOCK_SEALED'] = 1240300, 
    ['MODULE_LOCK_SEALED_RANDOM'] = 1240301, 
    ['MODULE_LOCK_SEQUENCE'] = 1240900, 
    ['MODULE_LOCK_SETTING'] = 1241400, 
    ['MODULE_LOCK_SHOP'] = 1241802, 
    ['MODULE_LOCK_SHOPPING_MALL'] = 1241801, 
    ['MODULE_LOCK_SHOP_5'] = 1241824, 
    ['MODULE_LOCK_SHOP_6'] = 1241825, 
    ['MODULE_LOCK_SHOP_GACHA'] = 1241826, 
    ['MODULE_LOCK_SIDEQUESTRECAP'] = 1243603, 
    ['MODULE_LOCK_SKILL'] = 1240200, 
    ['MODULE_LOCK_SKILL_CUSTOM'] = 1240201, 
    ['MODULE_LOCK_SKILL_CUSTOM_TAB1'] = 1240203, 
    ['MODULE_LOCK_SKILL_CUSTOM_TAB2'] = 1240204, 
    ['MODULE_LOCK_SKILL_CUSTOM_TAB3'] = 1240205, 
    ['MODULE_LOCK_SKILL_CUSTOM_TAB4'] = 1240206, 
    ['MODULE_LOCK_SKILL_CUSTOM_TAB5'] = 1240207, 
    ['MODULE_LOCK_SM_BANGJIN'] = 1241810, 
    ['MODULE_LOCK_SM_JINBANG'] = 1241811, 
    ['MODULE_LOCK_SOCIAL_ACTION'] = 1242200, 
    ['MODULE_LOCK_TAROTTEAM'] = 1243400, 
    ['MODULE_LOCK_TAROTTEAM_SHOP'] = 1241829, 
    ['MODULE_LOCK_TASK'] = 1240500, 
    ['MODULE_LOCK_TEAM'] = 1242300, 
    ['MODULE_LOCK_TOWERCLIMB'] = 1241700, 
    ['MOUDLE_LOCK_MOMENT'] = 1243300, 
    ['MOUDLE_LOCK_MOUNT'] = 1243800, 
}

Enum.EGazeTypeMap = {
    ['CROWD_NPC'] = 9, 
    ['CUSTOM_ROLE'] = 8, 
    ['DIALOGUE'] = 6, 
    ['FACE_CLOSE_UP'] = 1, 
    ['NPC_AUTO_GAZE'] = 10, 
    ['PHOTO_CAMERA'] = 4, 
    ['QUEST'] = 5, 
    ['ROLE_CREATE'] = 7, 
    ['ROLE_SHOW'] = 3, 
    ['SCENE_ACTOR'] = 11, 
    ['THIRD_CAMERA'] = 2, 
}

Enum.EGuildConstIntData = {
    ['BADGE_TYPE_BG'] = 2, 
    ['BADGE_TYPE_ICON'] = 1, 
    ['GROUP_WORDNUM_LIMIT'] = 2, 
    ['GUILD_FUND'] = 2000158, 
    ['GUILD_STORE'] = 1250104, 
}

Enum.EGuildLeagueConstIntData = {
    [''] = 0, 
    ['COMMAND_SYSTEM_MAX_GROUP_NUM'] = 5, 
    ['GUILD_BATTLEFIELD_RESOURCE_ITEM'] = 2000289, 
    ['GUILD_COMMAND_BEAM_BASE_MAX_HEIGHT'] = 2500, 
    ['GUILD_COMMAND_BEAM_BASE_MIN_HEIGHT'] = 400, 
    ['GUILD_COMMAND_BEAM_ZOOM'] = 3, 
    ['GUILD_LEAGUE_ACTIVE_OCCUPY_AREA_A_POINT'] = 150, 
    ['GUILD_LEAGUE_ACTIVE_OCCUPY_AREA_B_POINT'] = 200, 
    ['GUILD_LEAGUE_ASSIST_CALCULATE_TIME'] = 30, 
    ['GUILD_LEAGUE_AVATAR_TAKE_LIMIT'] = 500, 
    ['GUILD_LEAGUE_BACK_TO_FIGHT_TIME_CALCULATE_FORMULA'] = 1612325, 
    ['GUILD_LEAGUE_BASIC_RESOURCE_CALCULATE_ADD_NUM'] = 30, 
    ['GUILD_LEAGUE_BASIC_RESOURCE_CALCULATE_PER_SECOND'] = 15, 
    ['GUILD_LEAGUE_BEFORE_OCCUPY_DETECT_AREA_ACTIVE_TIME'] = 33, 
    ['GUILD_LEAGUE_CAMP1_VEHICLE_INSTANCEID'] = 5860009, 
    ['GUILD_LEAGUE_CAMP2_VEHICLE_INSTANCEID'] = 5860010, 
    ['GUILD_LEAGUE_CAMP_A_RESPAWN_POINT'] = 561092123, 
    ['GUILD_LEAGUE_CAMP_B_RESPAWN_POINT'] = 172436250, 
    ['GUILD_LEAGUE_CAN_KEEP_TIME'] = 180, 
    ['GUILD_LEAGUE_ENTER_GUILD_TIME_LIMIT'] = 0, 
    ['GUILD_LEAGUE_ENTRANCE_NPC_ID'] = 7290001, 
    ['GUILD_LEAGUE_ESTABLISH_GUILD_TIME_LIMIT'] = 0, 
    ['GUILD_LEAGUE_GET_REWARD_MIN_JOIN_TIME'] = 180, 
    ['GUILD_LEAGUE_HIGH_ZHANLI_THRESHOLD'] = 0, 
    ['GUILD_LEAGUE_INIT_RESOURCE'] = 500, 
    ['GUILD_LEAGUE_JOIN_LIVEVALUE_THRESHOLD'] = 0, 
    ['GUILD_LEAGUE_JOIN_PLAYER_LEVEL_LIMIT'] = 30, 
    ['GUILD_LEAGUE_KILL_MONSTER_RESOURCE'] = 100, 
    ['GUILD_LEAGUE_LOSE_OBTAIN_WIN_REWARD_TIME_REQUIRE'] = 900, 
    ['GUILD_LEAGUE_MAX_FIX_GROUP_COUNT'] = 10, 
    ['GUILD_LEAGUE_MAX_GROUP_NAME_LENGTH'] = 3, 
    ['GUILD_LEAGUE_MAX_GUILD_COUNT_PER_ZONE'] = 400, 
    ['GUILD_LEAGUE_MAX_PLAYER_NUM'] = 150, 
    ['GUILD_LEAGUE_MONSTER_SKILL_ID'] = 80009140, 
    ['GUILD_LEAGUE_OCCUPY_AREA_ATTACK_INTERVAL'] = 5, 
    ['GUILD_LEAGUE_OCCUPY_AREA_ATTACK_TIME'] = 5, 
    ['GUILD_LEAGUE_OCCUPY_AREA_OPEN_CALCULATE_FORMULA'] = 1612326, 
    ['GUILD_LEAGUE_OCCUPY_AREA_OPEN_COUNT_DOWN'] = 30, 
    ['GUILD_LEAGUE_OCCUPY_AREA_START_TIME'] = 480, 
    ['GUILD_LEAGUE_OCCUPY_DETECT_AREA_MAX_OPEN_ROUND'] = 3, 
    ['GUILD_LEAGUE_OCCUPY_DETECT_AREA_OPEN_INTERVAL'] = 360, 
    ['GUILD_LEAGUE_PLAYER_SUMMARIZE_DISTANCE'] = 2500, 
    ['GUILD_LEAGUE_PLAYER_SUMMARIZE_NUM'] = 5, 
    ['GUILD_LEAGUE_PREPARE_TIME'] = 120, 
    ['GUILD_LEAGUE_RESOURCE_DROP_DESTROY_TIME'] = 300, 
    ['GUILD_LEAGUE_RESOURCE_MAX_LIMIT'] = 10000, 
    ['GUILD_LEAGUE_RESOURCE_STOP_REFRESH_TIME'] = 900, 
    ['GUILD_LEAGUE_SCHOOL_LIMIT_NUM'] = 50, 
    ['GUILD_LEAGUE_START_BID_INTERVAL'] = 180, 
    ['GUILD_LEAGUE_START_HAVE_QUALIFICATION_BID_MAIL_ID'] = 6260033, 
    ['GUILD_LEAGUE_START_NO_QUALIFICATION_BID_MAIL_ID'] = 6260032, 
    ['GUILD_LEAGUE_SUCCESS_OCCUPY_GAIN_RESOURCE'] = 600, 
    ['GUILD_LEAGUE_SUMMON_MONSTER_MIN_HP'] = 20, 
    ['GUILD_LEAGUE_SUMMON_MONSTER_REQUIRE_POINT'] = 300, 
    ['GUILD_LEAGUE_SUPPORT_MONSTER_CAMP_A'] = 7100034, 
    ['GUILD_LEAGUE_SUPPORT_MONSTER_CAMP_B'] = 7100035, 
    ['GUILD_LEAGUE_TAG_NAME_MAX_COUNT'] = 4, 
    ['GUILD_LEAGUE_TELEPORTPOINT_INSTANCEID_CAMP_A'] = 1548860495, 
    ['GUILD_LEAGUE_TELEPORTPOINT_INSTANCEID_CAMP_B'] = 2485231767, 
    ['GUILD_LEAGUE_TESTING_RESPAWN_POINT'] = 3453746067, 
    ['GUILD_LEAGUE_TIMED_RESOURCE_INTERVAL_MIN'] = 1, 
    ['GUILD_LEAGUE_TIMED_RESOURCE_VALUE'] = 1000, 
    ['GUILD_LEAGUE_TOTAL_TIME'] = 1500, 
    ['GUILD_LEAGUE_TOWER_AOI_RADIUS'] = 3000, 
    ['GUILD_LEAGUE_TOWER_BUFF'] = 84001301, 
    ['GUILD_LEAGUE_TOWER_DIFF_FOR_EXTRA_RESOURCE'] = 3, 
    ['GUILD_LEAGUE_TOWER_EXTRA_RESOURCE'] = 1000, 
    ['GUILD_LEAGUE_VEHICLE_POSITION_OFFSET'] = 10, 
    ['MAX_GUILD_COMMANDER_NUM'] = 5, 
    ['MAX_NUMBER_RESERVED_SWSSIONS'] = 3, 
    ['MULTI_PVP_CAMP_A_ID'] = 1400007, 
    ['MULTI_PVP_CAMP_B_ID'] = 1400008, 
}

Enum.EInteractorIntConstData = {
    ['DROP_DESTROY'] = 180, 
    ['DROP_HEIGHT_APPEAR'] = 100, 
    ['DROP_HEIGHT_TOP'] = 500, 
    ['DROP_INTERVAL'] = 100, 
    ['DROP_LOCATION_TYPE'] = 2, 
    ['DROP_NAME'] = 10, 
    ['DROP_PICKUP'] = 500, 
    ['DROP_PICKUP_AUTO'] = 300, 
    ['DROP_PRIORITY_INTERVAL'] = 200, 
    ['DROP_REMINDER_CD'] = 30, 
    ['DROP_SQUARE_LENGTH'] = 75, 
    ['DROP_SQUARE_MAX'] = 9, 
    ['DROP_TIME_ALL'] = 4, 
    ['DROP_TIME_OFFSET'] = 200, 
    ['DROP_TIME_PER'] = 200, 
    ['DROP_TIME_PICKUP'] = 100, 
    ['DROP_TRAIL_BULLET_ID'] = 15, 
    ['MAX_DROP_HEIGHT'] = 200, 
    ['MAX_ROTATE_ANGLE'] = 360, 
    ['MIN_DROP_HEIGHT'] = 200, 
    ['MIN_ROTATE_ANGLE'] = 90, 
    ['PICKUP_PLAYER_SKILL'] = 9001, 
    ['TITLE_HEIGHT'] = 30, 
}

Enum.ELevelMapData = {
    ['C7_City_NPC_P'] = 5200022, 
    ['Gameplay_Test'] = 5209993, 
    ['LV_ActorGallery_P'] = 5209995, 
    ['LV_Arena_P'] = 5200020, 
    ['LV_Arena_P1'] = 5200109, 
    ['LV_AsYouWishTheatre_P'] = 5200089, 
    ['LV_Beckland_P'] = 5200081, 
    ['LV_BlackThorns_Company_P'] = 5200004, 
    ['LV_BlackThorns_Company_broken_P'] = 5200040, 
    ['LV_BlackThorns_Company_broken_P2'] = 5200067, 
    ['LV_Campaign_Factory_LD'] = 5200032, 
    ['LV_Campaign_Ruierbibo_LD'] = 5200029, 
    ['LV_Chaoz_JokerDream_P'] = 5200115, 
    ['LV_CharacterCreation'] = 5209005, 
    ['LV_Character_Ceshi_P'] = 5208001, 
    ['LV_CommanVillage_P'] = 5200052, 
    ['LV_CommonKitchen_P'] = 5200088, 
    ['LV_CommonMemory_P'] = 5200068, 
    ['LV_CustomRole'] = 5209006, 
    ['LV_Divination_club_P'] = 5200009, 
    ['LV_DragonBar_P'] = 5200050, 
    ['LV_Factory_Daily_LD01'] = 5200042, 
    ['LV_Factory_Daily_LD02'] = 5200043, 
    ['LV_Factory_P'] = 5200013, 
    ['LV_Factory_P_Elite'] = 5200034, 
    ['LV_Factory_WeeklyRaid_P'] = 5200056, 
    ['LV_Factory_WeeklyRaid_P_Hero'] = 5200095, 
    ['LV_FactroyTest'] = 5200072, 
    ['LV_Ferlanqi_Flats_P'] = 5200030, 
    ['LV_ForsakenLand_BlackWilderness_P'] = 5200054, 
    ['LV_ForsakenLand_GiantTing_P'] = 5200053, 
    ['LV_GVGArena_P'] = 5200131, 
    ['LV_GoldenSycamoreTheatre_P'] = 5200091, 
    ['LV_GreyMist_Space_P'] = 5200092, 
    ['LV_GuildBattleField_P'] = 5200059, 
    ['LV_GuildBattleField_P1'] = 5200094, 
    ['LV_LTZX_test'] = 5200129, 
    ['LV_LYDesign_test'] = 5200121, 
    ['LV_Laivez_Home_P'] = 5200028, 
    ['LV_LevelGameplayTest'] = 5200017, 
    ['LV_LevelQuestTest_P'] = 5200014, 
    ['LV_Loading'] = 5209004, 
    ['LV_Login'] = 5209003, 
    ['LV_Manor02_P'] = 5200061, 
    ['LV_Manor03_P'] = 5200060, 
    ['LV_MassTest_P'] = 5209992, 
    ['LV_Middle_House_P'] = 5200100, 
    ['LV_Mindisland_P1'] = 5200039, 
    ['LV_Mirror_Space_P'] = 5200047, 
    ['LV_Mirror_Space_P2'] = 5200069, 
    ['LV_Mirror_Space_P3'] = 5200080, 
    ['LV_MythicDungeonDemo_01'] = 5200132, 
    ['LV_MythicDungeonDemo_02'] = 5200133, 
    ['LV_MythicDungeonDemo_03'] = 5200134, 
    ['LV_MythicDungeonDemo_04'] = 5200135, 
    ['LV_MythicDungeonDemo_05'] = 5200136, 
    ['LV_MythicDungeonDemo_06'] = 5200137, 
    ['LV_Octopus_Island_WP_P'] = 5200021, 
    ['LV_PersonalTestLevel_01'] = 5200113, 
    ['LV_PersonalTestLevel_dz'] = 5200116, 
    ['LV_PersonalTestLevel_llq'] = 5200126, 
    ['LV_PersonalTestLevel_mxy'] = 5200114, 
    ['LV_PersonalTestLevel_whq'] = 5200122, 
    ['LV_PersonalTestLevel_xtl'] = 5200120, 
    ['LV_Pirate_School_Audrey_Daily'] = 5200045, 
    ['LV_Pirate_School_Derrick_Daily'] = 5200044, 
    ['LV_Pirate_School_Dungeon'] = 5200124, 
    ['LV_Pirate_School_Dungeon_H'] = 5200125, 
    ['LV_Pirate_School_Mentor_Daily'] = 5200038, 
    ['LV_Pirate_School_Merlin_Daily'] = 5200046, 
    ['LV_Pirate_School_P'] = 5200007, 
    ['LV_Plane'] = 5209999, 
    ['LV_QABattleTest_P'] = 5200018, 
    ['LV_QAComplexTerrainTest_P'] = 5209001, 
    ['LV_QAExpandState_P'] = 5200055, 
    ['LV_QAExpandState_P_01'] = 5200112, 
    ['LV_QAResourceTest_WP_P'] = 5200093, 
    ['LV_Rich_house_P'] = 5200048, 
    ['LV_Rose_School_P'] = 5200001, 
    ['LV_Rose_School_P_Elite'] = 5200035, 
    ['LV_Rose_School_P_FC'] = 5200063, 
    ['LV_Ruierbibo_Escort_LD'] = 5200041, 
    ['LV_Ruierbibo_Factory_Single_P'] = 5200105, 
    ['LV_Ruierbibo_JokerDream_Single_P'] = 5200107, 
    ['LV_Ruierbibo_P'] = 5200016, 
    ['LV_Ruierbibo_P_Elite'] = 5200033, 
    ['LV_Ruierbibo_Port_Single_P'] = 5200106, 
    ['LV_Ruierbibo_WeeklyRaid_JokerDream_P'] = 5200064, 
    ['LV_Ruierbibo_WeeklyRaid_JokerDream_P_02'] = 5200071, 
    ['LV_Ruierbibo_WeeklyRaid_JokerDream_P_Hero'] = 5200097, 
    ['LV_Ruierbibo_WeeklyRaid_JokerDream_Test'] = 5200065, 
    ['LV_Ruierbibo_WeeklyRaid_P'] = 5200057, 
    ['LV_Ruierbibo_WeeklyRaid_P_Hero'] = 5200096, 
    ['LV_Rylbir_Houes_P'] = 5200006, 
    ['LV_SceneActor_Test'] = 5209990, 
    ['LV_SchoolBattle_P'] = 5200078, 
    ['LV_Sefirah_Castle_P'] = 5200027, 
    ['LV_Sewer_Lanevus_P'] = 5201001, 
    ['LV_Sewer_Lanevus_P_02'] = 5200104, 
    ['LV_Showroom'] = 5209007, 
    ['LV_ShuiXian_Villa_P'] = 5200051, 
    ['LV_Starrynight_P'] = 5200128, 
    ['LV_Startplace_Export_P'] = 5200090, 
    ['LV_Startplace_Hzptest'] = 5200127, 
    ['LV_Startplace_LD1'] = 5200049, 
    ['LV_Startplace_P'] = 5209996, 
    ['LV_Startplace_Ragetest'] = 5209998, 
    ['LV_Startplace_ShuzhiTest'] = 5209899, 
    ['LV_Startplace_Strong_Interactive_P'] = 5200098, 
    ['LV_Startplace_Wbtest'] = 5209997, 
    ['LV_Startplace_headtest'] = 5209989, 
    ['LV_Startup'] = 5209002, 
    ['LV_SubconsciousOcean_P'] = 5200108, 
    ['LV_Tiengen_Lake02_P'] = 5200058, 
    ['LV_Tiengen_Lake03_P'] = 5204001, 
    ['LV_Tiengen_Lake03_P_Test'] = 5204002, 
    ['LV_Tiengen_Lake_P1'] = 5200026, 
    ['LV_Tiengen_P'] = 5200002, 
    ['LV_TrueCreatorBattle_P'] = 5200077, 
    ['LV_TrueCreatorBattle_P2'] = 5200101, 
    ['LV_Underground_relic02_P'] = 5200099, 
    ['LV_Underground_relic02_P_01'] = 5200103, 
    ['LV_Underground_relic_P'] = 5200075, 
    ['LV_WYZY_WP_P'] = 5200079, 
    ['LV_WYZY_WP_P_1'] = 5200102, 
    ['LV_WYZY_WP_P_GJ'] = 5200123, 
    ['LV_WYZY_WP_P_Test'] = 5200118, 
    ['LV_WYZY_WP_P_Test_2'] = 5200119, 
    ['Lookdev_VFX_P'] = 5209994, 
    ['Lv_12v12Arena_P'] = 5203002, 
    ['Lv_12v12Arena_P1'] = 5203003, 
    ['Lv_5v5Arena_P'] = 5203001, 
    ['Lv_5v5Arena_P1'] = 5200110, 
    ['Lv_5v5Arena_P2'] = 5200111, 
    ['Lv_5v5Arena_Stress'] = 5203099, 
    ['Server_BigWorld_Test'] = 5209991, 
    ['Suburb_AutumnIsland_P'] = 5200085, 
    ['Suburb_Castle_P'] = 5200130, 
    ['Suburb_SnowMountain_P'] = 5200086, 
    ['Suburb_SnowMountain_P_5200117'] = 5200117, 
}

Enum.EMailConstIntData = {
    ['MAIL_ATTACHMENT_MAX'] = 10, 
    ['MAIL_COLLECT_NUM_MAX'] = 100, 
    ['MAIL_EXPIRE_TIP'] = 24, 
    ['MAIL_LIMIT_MAX'] = 200, 
}

Enum.EMakeMedicineDiceData = {
    [17] = true, 
}

Enum.EMarqueeTextEnum = {
    ['LOUD_SPEAKER_ITEM_ID1'] = 1, 
    ['LOUD_SPEAKER_ITEM_ID2'] = 2, 
    ['LOUD_SPEAKER_ITEM_ID3'] = 3, 
    ['WORLD_CHANNEL_QUIZ_NOTICE'] = 4, 
    ['WORLD_CHANNEL_QUIZ_START'] = 5, 
}

Enum.EMatchTypeData = {
    ['MATCH_TYPE_12V12'] = 5, 
    ['MATCH_TYPE_3V3'] = 3, 
    ['MATCH_TYPE_5V5'] = 4, 
    ['MATCH_TYPE_SINGLE'] = 2, 
    ['MATCH_TYPE_TEAM'] = 1, 
}

Enum.ENewbieGuideAnimType = {
    ['CLICK_ANIM'] = 'Ani_Click_Hand', 
    ['DRAG_LR'] = 'Ani_SlideLR', 
    ['DRAG_UD'] = 'Ani_SlideUD', 
    ['DRAG_Up'] = 'Ani_SlideUp', 
    ['LONG_PRESS_ANIM'] = 'Ani_LongClick_Hand', 
}

Enum.ENewbieGuideAreaType = {
    ['ROUND_AREA'] = '/Game/Arts/UI_2/Blueprint/NewbieGuide/WBP_NewbieGuidCricle_Item.WBP_NewbieGuidCricle_Item_C', 
    ['SQUARE_AREA'] = '/Game/Arts/UI_2/Blueprint/NewbieGuide/WBP_NewbieGuideRect_Item.WBP_NewbieGuideRect_Item_C', 
}

Enum.ENewbieGuideFinishType = {
    ['CLICK_ANY_FINISH'] = 204, 
    ['CLICK_FINISH'] = 201, 
    ['CUSTOM_FINISH'] = 208, 
    ['DRAG_FINISH'] = 203, 
    ['LONG_PRESS_FINISH'] = 202, 
    ['PERIOD_FINISH'] = 205, 
    ['PRESS_KEYBOARD'] = 207, 
    ['TASK_FINISH'] = 206, 
}

Enum.ENewbieGuideTriggerType = {
    ['ACTIVITY_TRIGGER'] = 4, 
    ['FC_TRIGGER'] = 2, 
    ['FUNCTION_TRIGGER'] = 3, 
    ['LEVEL_TRIGGER'] = 6, 
    ['NEWBIE_GUIDE_TRIGGER'] = 7, 
    ['TASK_TRIGGER'] = 1, 
    ['UI_TRIGGER'] = 5, 
}

Enum.ENpcSpiritualVisionType = {
    ['NPC_CanInteract'] = 2, 
    ['NPC_CanNotInteract'] = 1, 
    ['NPC_Enemy'] = 5, 
    ['NPC_Friend'] = 7, 
    ['NPC_GamePlay'] = 6, 
    ['NPC_MainTask'] = 3, 
    ['NPC_SubTask'] = 4, 
}

Enum.ENpcTrapTypeData = {
    ['AnnularTrap'] = 5, 
    ['CircleTrap'] = 2, 
    ['CylinderTrap'] = 4, 
    ['Fan3dTrap'] = 6, 
    ['FanTrap'] = 3, 
    ['RectangularTrap'] = 1, 
}

Enum.ENpcTypeData = {
    ['Monster'] = 0, 
    ['Passerby'] = 2, 
    ['Task'] = 1, 
    ['Vehicle'] = 3, 
}

Enum.EPendulumAnimType = {
    ['EndLoop'] = 1006, 
    ['Fail'] = 1007, 
    ['Fast'] = 1003, 
    ['FastRev'] = 1005, 
    ['Idle'] = 1001, 
    ['Slow'] = 1002, 
    ['SlowRev'] = 1004, 
}

Enum.EPlainFashionType2ID = {
    [0] = {[1002] = 4220065, [1003] = 4220066, [1006] = 4220069, }, 
    [1] = {[1002] = 4220075, [1003] = 4220076, [1005] = 4220077, [1006] = 4220068, }, 
}

Enum.EPlayerClassTypeData = {
    ['ChaosSea'] = 7, 
    ['DPS'] = 4, 
    ['Healer'] = 3, 
    ['Melee'] = 1, 
    ['Ranged'] = 2, 
    ['SefirahCastle'] = 6, 
    ['Tank'] = 5, 
}

Enum.EPlayerInitialConst = {
    ['NormalCameraDirMoveSpeed'] = 450, 
    ['NormalRideFastSpeed'] = 900, 
    ['NormalRideFastToRideSprintSpeedBound'] = 1200, 
    ['NormalRideSlowSpeed'] = 300, 
    ['NormalRideSlowToRideFastSpeedBound'] = 600, 
    ['NormalRideSprintSpeed'] = 1500, 
    ['NormalRunSpeed'] = 638, 
    ['NormalRunToSprintSpeedBound'] = 800, 
    ['NormalSprintSpeed'] = 957, 
    ['NormalSwimSpeed'] = 0, 
    ['NormalWalkSpeed'] = 169, 
    ['NormalWalkToRunSpeedBound'] = 400, 
    ['NormalWateWalkSpeed'] = 1000, 
    ['UltimateRecoverTime'] = 5, 
}

Enum.EPPTypes = {
    ['PP_BLOOM'] = 5, 
    ['PP_BRIGHTEN'] = 12, 
    ['PP_COLOR_ADJUST'] = 7, 
    ['PP_COLOR_ADJUST_RADIAL_BLUR'] = 9, 
    ['PP_CUTOM_MATERIAL'] = 1000, 
    ['PP_DARKEN'] = 4, 
    ['PP_DOF'] = 6, 
    ['PP_PHANTOM'] = 14, 
    ['PP_RADIAL_BLUR'] = 8, 
    ['PP_RADIAL_UV_DISTORT'] = 13, 
    ['PP_RGB_SPLIT'] = 10, 
    ['PP_VIGNETTE'] = 11, 
}

Enum.EPropData = {
    ['AbundanceAnti'] = 1014230, 
    ['AbundanceAnti_N'] = 1014231, 
    ['AbundanceAnti_P'] = 1014232, 
    ['AbundanceAtk'] = 1014200, 
    ['AbundanceAtk_N'] = 1014201, 
    ['AbundanceAtk_P'] = 1014202, 
    ['AbundanceHurtMulti'] = 1014210, 
    ['AbundanceHurtMulti_N'] = 1014211, 
    ['AbundanceHurtReduce'] = 1014220, 
    ['AbundanceHurtReduce_N'] = 1014221, 
    ['AbundanceLevel'] = 1014260, 
    ['AbundanceLevel_N'] = 1014261, 
    ['AbundanceRate'] = 1014250, 
    ['AbundanceRate_N'] = 1014251, 
    ['AdditionalAnti'] = 1017060, 
    ['AdditionalAnti_N'] = 1017061, 
    ['AdditionalAnti_P'] = 1017062, 
    ['AdditionalAtk'] = 1017050, 
    ['AdditionalAtk_N'] = 1017051, 
    ['AdditionalAtk_P'] = 1017052, 
    ['AdditionalIgnore'] = 1017070, 
    ['AdditionalIgnore_N'] = 1017071, 
    ['AdditionalIgnore_P'] = 1017072, 
    ['AggroPercent'] = 1011230, 
    ['AggroPercent_N'] = 1011231, 
    ['AirShield'] = 1017020, 
    ['AirShield_N'] = 1017021, 
    ['AirborneAnti'] = 1015000, 
    ['AirborneAnti_N'] = 1015001, 
    ['AirborneAnti_P'] = 1015002, 
    ['AirborneHitRate'] = 1015020, 
    ['AirborneHitRate_N'] = 1015021, 
    ['AllControlAnti'] = 1016030, 
    ['AllControlAnti_N'] = 1016031, 
    ['AllControlAnti_P'] = 1016032, 
    ['AllControlHitRate'] = 1016050, 
    ['AllControlHitRate_N'] = 1016051, 
    ['AllEleAnti'] = 1016060, 
    ['AllEleAnti_N'] = 1016061, 
    ['AllEleAnti_P'] = 1016062, 
    ['AllEleAtk'] = 1016000, 
    ['AllEleAtk_N'] = 1016001, 
    ['AllEleAtk_P'] = 1016002, 
    ['AllEleHurtMulti'] = 1016010, 
    ['AllEleHurtMulti_N'] = 1016011, 
    ['AllEleHurtReduce'] = 1016020, 
    ['AllEleHurtReduce_N'] = 1016021, 
    ['AllProHurtMulti'] = 1016130, 
    ['AllProHurtMulti_N'] = 1016131, 
    ['AllProHurtMulti_P'] = 1016132, 
    ['AllProHurtReduce'] = 1016140, 
    ['AllProHurtReduce_N'] = 1016141, 
    ['AllProHurtReduce_P'] = 1016142, 
    ['AllRaceHurtMulti'] = 1016150, 
    ['AllRaceHurtMulti_N'] = 1016151, 
    ['AllRaceHurtMulti_P'] = 1016152, 
    ['AllRaceHurtReduce'] = 1016160, 
    ['AllRaceHurtReduce_N'] = 1016161, 
    ['AllRaceHurtReduce_P'] = 1016162, 
    ['ApprProp1'] = 1022030, 
    ['ApprProp2'] = 1022040, 
    ['ApprProp3'] = 1022050, 
    ['ApprenticeHurtMulti'] = 1019050, 
    ['ApprenticeHurtMulti_N'] = 1019051, 
    ['ApprenticeHurtMulti_P'] = 1019052, 
    ['ApprenticeHurtReduce'] = 1019250, 
    ['ApprenticeHurtReduce_N'] = 1019251, 
    ['ApprenticeHurtReduce_P'] = 1019252, 
    ['ArbProp1'] = 1022080, 
    ['ArbiterHurtMulti'] = 1019030, 
    ['ArbiterHurtMulti_N'] = 1019031, 
    ['ArbiterHurtMulti_P'] = 1019032, 
    ['ArbiterHurtReduce'] = 1019230, 
    ['ArbiterHurtReduce_N'] = 1019231, 
    ['ArbiterHurtReduce_P'] = 1019232, 
    ['Atk'] = 1016100, 
    ['AtkRange'] = 1011070, 
    ['AtkRange_N'] = 1011071, 
    ['AtkRange_P'] = 1011072, 
    ['Atk_N'] = 1016101, 
    ['Atk_P'] = 1016102, 
    ['Block'] = 1016090, 
    ['Block_N'] = 1016091, 
    ['Block_P'] = 1016092, 
    ['CalamityAnti'] = 1014430, 
    ['CalamityAnti_N'] = 1014431, 
    ['CalamityAnti_P'] = 1014432, 
    ['CalamityAtk'] = 1014400, 
    ['CalamityAtk_N'] = 1014401, 
    ['CalamityAtk_P'] = 1014402, 
    ['CalamityHurtMulti'] = 1014410, 
    ['CalamityHurtMulti_N'] = 1014411, 
    ['CalamityHurtReduce'] = 1014420, 
    ['CalamityHurtReduce_N'] = 1014421, 
    ['CalamityLevel'] = 1014460, 
    ['CalamityLevel_N'] = 1014461, 
    ['CalamityRate'] = 1014450, 
    ['CalamityRate_N'] = 1014451, 
    ['ChaosAnti'] = 1014030, 
    ['ChaosAnti_N'] = 1014031, 
    ['ChaosAnti_P'] = 1014032, 
    ['ChaosAtk'] = 1014000, 
    ['ChaosAtk_N'] = 1014001, 
    ['ChaosAtk_P'] = 1014002, 
    ['ChaosHurtMulti'] = 1014010, 
    ['ChaosHurtMulti_N'] = 1014011, 
    ['ChaosHurtReduce'] = 1014020, 
    ['ChaosHurtReduce_N'] = 1014021, 
    ['ChaosLevel'] = 1014060, 
    ['ChaosLevel_N'] = 1014061, 
    ['ChaosRate'] = 1014050, 
    ['ChaosRate_N'] = 1014051, 
    ['Con'] = 1010000, 
    ['Con_N'] = 1010001, 
    ['Con_P'] = 1010002, 
    ['CritAbundanceLevel'] = 1014270, 
    ['CritAbundanceLevel_N'] = 1014271, 
    ['CritAnti'] = 1016170, 
    ['CritAnti_N'] = 1016171, 
    ['CritAnti_P'] = 1016172, 
    ['CritCalamityLevel'] = 1014470, 
    ['CritCalamityLevel_N'] = 1014471, 
    ['CritChaosLevel'] = 1014070, 
    ['CritChaosLevel_N'] = 1014071, 
    ['CritDarknessLevel'] = 1014370, 
    ['CritDarknessLevel_N'] = 1014371, 
    ['CritDarknessSteal'] = 1011330, 
    ['CritDarknessSteal_N'] = 1011331, 
    ['CritDarknessSteal_P'] = 1011332, 
    ['CritDef'] = 1016180, 
    ['CritDef_N'] = 1016181, 
    ['CritDef_P'] = 1016182, 
    ['CritDisorderLevel'] = 1014570, 
    ['CritDisorderLevel_N'] = 1014571, 
    ['CritFateAtkMax'] = 1011370, 
    ['CritFateAtkMax_N'] = 1011371, 
    ['CritFateAtkMax_P'] = 1011372, 
    ['CritFateAtkMin'] = 1011360, 
    ['CritFateAtkMin_N'] = 1011361, 
    ['CritFateAtkMin_P'] = 1011362, 
    ['CritFateLevel'] = 1014870, 
    ['CritFateLevel_N'] = 1014871, 
    ['CritKnowledgeLevel'] = 1014770, 
    ['CritKnowledgeLevel_N'] = 1014771, 
    ['CritMysteryLevel'] = 1014170, 
    ['CritMysteryLevel_N'] = 1014171, 
    ['CritTenebrousIgnoreDef'] = 1011350, 
    ['CritTenebrousIgnoreDef_N'] = 1011351, 
    ['CritTenebrousIgnoreDef_P'] = 1011352, 
    ['CritTenebrousLevel'] = 1014670, 
    ['CritTenebrousLevel_N'] = 1014671, 
    ['CurrentMaxHp'] = 1020000, 
    ['DarknessAnti'] = 1014330, 
    ['DarknessAnti_N'] = 1014331, 
    ['DarknessAnti_P'] = 1014332, 
    ['DarknessAtk'] = 1014300, 
    ['DarknessAtk_N'] = 1014301, 
    ['DarknessAtk_P'] = 1014302, 
    ['DarknessHurtMulti'] = 1014310, 
    ['DarknessHurtMulti_N'] = 1014311, 
    ['DarknessHurtReduce'] = 1014320, 
    ['DarknessHurtReduce_N'] = 1014321, 
    ['DarknessLevel'] = 1014360, 
    ['DarknessLevel_N'] = 1014361, 
    ['DarknessRate'] = 1014350, 
    ['DarknessRate_N'] = 1014351, 
    ['DarknessSteal'] = 1011320, 
    ['DarknessSteal_N'] = 1011321, 
    ['DarknessSteal_P'] = 1011322, 
    ['Def'] = 1016080, 
    ['Def_N'] = 1016081, 
    ['Def_P'] = 1016082, 
    ['DeltaBeHealed'] = 1011090, 
    ['DeltaBeHealed_N'] = 1011091, 
    ['DeltaBeHealed_P'] = 1011092, 
    ['DeltaBeHurted'] = 1011250, 
    ['DeltaBeHurted_N'] = 1011251, 
    ['DeltaHeal'] = 1011080, 
    ['DeltaHeal_N'] = 1011081, 
    ['DeltaHeal_P'] = 1011082, 
    ['DeltaHurt'] = 1011240, 
    ['DeltaHurt_N'] = 1011241, 
    ['Dex'] = 1010040, 
    ['Dex_N'] = 1010041, 
    ['Dex_P'] = 1010042, 
    ['DisorderAnti'] = 1014530, 
    ['DisorderAnti_N'] = 1014531, 
    ['DisorderAnti_P'] = 1014532, 
    ['DisorderAtk'] = 1014500, 
    ['DisorderAtk_N'] = 1014501, 
    ['DisorderAtk_P'] = 1014502, 
    ['DisorderHurtMulti'] = 1014510, 
    ['DisorderHurtMulti_N'] = 1014511, 
    ['DisorderHurtReduce'] = 1014520, 
    ['DisorderHurtReduce_N'] = 1014521, 
    ['DisorderLevel'] = 1014560, 
    ['DisorderLevel_N'] = 1014561, 
    ['DisorderRate'] = 1014550, 
    ['DisorderRate_N'] = 1014551, 
    ['DizzyAnti'] = 1015500, 
    ['DizzyAnti_N'] = 1015501, 
    ['DizzyAnti_P'] = 1015502, 
    ['DizzyHitRate'] = 1015520, 
    ['DizzyHitRate_N'] = 1015521, 
    ['DownAnti'] = 1015100, 
    ['DownAnti_N'] = 1015101, 
    ['DownAnti_P'] = 1015102, 
    ['DownHitRate'] = 1015120, 
    ['DownHitRate_N'] = 1015121, 
    ['EndureValve'] = 1020070, 
    ['EnhanceAirborne'] = 1015010, 
    ['EnhanceAirborne_N'] = 1015011, 
    ['EnhanceAirborne_P'] = 1015012, 
    ['EnhanceAllControl'] = 1016040, 
    ['EnhanceAllControl_N'] = 1016041, 
    ['EnhanceAllControl_P'] = 1016042, 
    ['EnhanceDizzy'] = 1015510, 
    ['EnhanceDizzy_N'] = 1015511, 
    ['EnhanceDizzy_P'] = 1015512, 
    ['EnhanceDown'] = 1015110, 
    ['EnhanceDown_N'] = 1015111, 
    ['EnhanceDown_P'] = 1015112, 
    ['EnhanceFear'] = 1015610, 
    ['EnhanceFear_N'] = 1015611, 
    ['EnhanceFear_P'] = 1015612, 
    ['EnhancePull'] = 1015210, 
    ['EnhancePull_N'] = 1015211, 
    ['EnhancePull_P'] = 1015212, 
    ['EnhanceSilence'] = 1015710, 
    ['EnhanceSilence_N'] = 1015711, 
    ['EnhanceSilence_P'] = 1015712, 
    ['EnhanceSleep'] = 1015410, 
    ['EnhanceSleep_N'] = 1015411, 
    ['EnhanceSleep_P'] = 1015412, 
    ['EnhanceSlow'] = 1015810, 
    ['EnhanceSlow_N'] = 1015811, 
    ['EnhanceSlow_P'] = 1015812, 
    ['EnhanceTied'] = 1015310, 
    ['EnhanceTied_N'] = 1015311, 
    ['EnhanceTied_P'] = 1015312, 
    ['FateAnti'] = 1014830, 
    ['FateAnti_N'] = 1014831, 
    ['FateAnti_P'] = 1014832, 
    ['FateAtk'] = 1014800, 
    ['FateAtk_N'] = 1014801, 
    ['FateAtk_P'] = 1014802, 
    ['FateHurtMulti'] = 1014810, 
    ['FateHurtMulti_N'] = 1014811, 
    ['FateHurtReduce'] = 1014820, 
    ['FateHurtReduce_N'] = 1014821, 
    ['FateLevel'] = 1014860, 
    ['FateLevel_N'] = 1014861, 
    ['FateRate'] = 1014850, 
    ['FateRate_N'] = 1014851, 
    ['FearAnti'] = 1015600, 
    ['FearAnti_N'] = 1015601, 
    ['FearAnti_P'] = 1015602, 
    ['FearHitRate'] = 1015620, 
    ['FearHitRate_N'] = 1015621, 
    ['FeatherwitHurtMulti'] = 1019020, 
    ['FeatherwitHurtMulti_N'] = 1019021, 
    ['FeatherwitHurtMulti_P'] = 1019022, 
    ['FeatherwitHurtReduce'] = 1019220, 
    ['FeatherwitHurtReduce_N'] = 1019221, 
    ['FeatherwitHurtReduce_P'] = 1019222, 
    ['FoolProp1'] = 1022100, 
    ['HitLimitCamp'] = 1021060, 
    ['Hp'] = 1020020, 
    ['HpLockValue'] = 1020010, 
    ['HpReg'] = 1011010, 
    ['HpReg_N'] = 1011011, 
    ['HpReg_P'] = 1011012, 
    ['IgnoreAbundanceAnti'] = 1014240, 
    ['IgnoreAbundanceAnti_N'] = 1014241, 
    ['IgnoreAbundanceAnti_P'] = 1014242, 
    ['IgnoreAllEle'] = 1016070, 
    ['IgnoreAllEle_N'] = 1016071, 
    ['IgnoreAllEle_P'] = 1016072, 
    ['IgnoreCalamityAnti'] = 1014440, 
    ['IgnoreCalamityAnti_N'] = 1014441, 
    ['IgnoreCalamityAnti_P'] = 1014442, 
    ['IgnoreChaosAnti'] = 1014040, 
    ['IgnoreChaosAnti_N'] = 1014041, 
    ['IgnoreChaosAnti_P'] = 1014042, 
    ['IgnoreDarknessAnti'] = 1014340, 
    ['IgnoreDarknessAnti_N'] = 1014341, 
    ['IgnoreDarknessAnti_P'] = 1014342, 
    ['IgnoreDisorderAnti'] = 1014540, 
    ['IgnoreDisorderAnti_N'] = 1014541, 
    ['IgnoreDisorderAnti_P'] = 1014542, 
    ['IgnoreFateAnti'] = 1014840, 
    ['IgnoreFateAnti_N'] = 1014841, 
    ['IgnoreFateAnti_P'] = 1014842, 
    ['IgnoreKnowledgeAnti'] = 1014740, 
    ['IgnoreKnowledgeAnti_N'] = 1014741, 
    ['IgnoreKnowledgeAnti_P'] = 1014742, 
    ['IgnoreMysteryAnti'] = 1014140, 
    ['IgnoreMysteryAnti_N'] = 1014141, 
    ['IgnoreMysteryAnti_P'] = 1014142, 
    ['IgnoreTenebrousAnti'] = 1014640, 
    ['IgnoreTenebrousAnti_N'] = 1014641, 
    ['IgnoreTenebrousAnti_P'] = 1014642, 
    ['Int'] = 1010030, 
    ['Int_N'] = 1010031, 
    ['Int_P'] = 1010032, 
    ['IsHealLimit'] = 1021020, 
    ['IsHitLimit'] = 1021000, 
    ['IsLockLimit'] = 1021010, 
    ['IsMustCrit'] = 1021070, 
    ['KnowledgeAnti'] = 1014730, 
    ['KnowledgeAnti_N'] = 1014731, 
    ['KnowledgeAnti_P'] = 1014732, 
    ['KnowledgeAtk'] = 1014700, 
    ['KnowledgeAtk_N'] = 1014701, 
    ['KnowledgeAtk_P'] = 1014702, 
    ['KnowledgeHurtMulti'] = 1014710, 
    ['KnowledgeHurtMulti_N'] = 1014711, 
    ['KnowledgeHurtReduce'] = 1014720, 
    ['KnowledgeHurtReduce_N'] = 1014721, 
    ['KnowledgeLevel'] = 1014760, 
    ['KnowledgeLevel_N'] = 1014761, 
    ['KnowledgeRate'] = 1014750, 
    ['KnowledgeRate_N'] = 1014751, 
    ['LockLimitCamp'] = 1021050, 
    ['LockedMaxHp'] = 1011040, 
    ['LockedMaxHp_N'] = 1011041, 
    ['LockedMaxHp_P'] = 1011042, 
    ['MaxHp'] = 1011000, 
    ['MaxHp_F'] = 1011003, 
    ['MaxHp_N'] = 1011001, 
    ['MaxHp_P'] = 1011002, 
    ['MaxStaminaValue'] = 1011060, 
    ['MaxStaminaValue_F'] = 1011063, 
    ['MaxStaminaValue_N'] = 1011061, 
    ['MaxStaminaValue_P'] = 1011062, 
    ['MysteryAnti'] = 1014130, 
    ['MysteryAnti_N'] = 1014131, 
    ['MysteryAnti_P'] = 1014132, 
    ['MysteryAtk'] = 1014100, 
    ['MysteryAtk_N'] = 1014101, 
    ['MysteryAtk_P'] = 1014102, 
    ['MysteryHurtMulti'] = 1014110, 
    ['MysteryHurtMulti_N'] = 1014111, 
    ['MysteryHurtReduce'] = 1014120, 
    ['MysteryHurtReduce_N'] = 1014121, 
    ['MysteryLevel'] = 1014160, 
    ['MysteryLevel_N'] = 1014161, 
    ['MysteryRate'] = 1014150, 
    ['MysteryRate_N'] = 1014151, 
    ['Pierce'] = 1016120, 
    ['Pierce_N'] = 1016121, 
    ['Pierce_P'] = 1016122, 
    ['Pow'] = 1010010, 
    ['Pow_N'] = 1010011, 
    ['Pow_P'] = 1010012, 
    ['ProHurtMulti9'] = 1019080, 
    ['ProHurtMulti9_N'] = 1019081, 
    ['ProHurtMulti9_P'] = 1019082, 
    ['ProHurtReduce9'] = 1019280, 
    ['ProHurtReduce9_N'] = 1019281, 
    ['ProHurtReduce9_P'] = 1019282, 
    ['ProfessionProp1'] = 1022000, 
    ['ProfessionProp2'] = 1022010, 
    ['ProfessionProp3'] = 1022020, 
    ['PullAnti'] = 1015200, 
    ['PullAnti_N'] = 1015201, 
    ['PullAnti_P'] = 1015202, 
    ['PullHitRate'] = 1015220, 
    ['PullHitRate_N'] = 1015221, 
    ['RaceHurtMulti1'] = 1018000, 
    ['RaceHurtMulti1_N'] = 1018001, 
    ['RaceHurtMulti1_P'] = 1018002, 
    ['RaceHurtMulti2'] = 1018010, 
    ['RaceHurtMulti2_N'] = 1018011, 
    ['RaceHurtMulti2_P'] = 1018012, 
    ['RaceHurtMulti3'] = 1018020, 
    ['RaceHurtMulti3_N'] = 1018021, 
    ['RaceHurtMulti3_P'] = 1018022, 
    ['RaceHurtMulti4'] = 1018030, 
    ['RaceHurtMulti4_N'] = 1018031, 
    ['RaceHurtMulti4_P'] = 1018032, 
    ['RaceHurtMulti5'] = 1018040, 
    ['RaceHurtMulti5_N'] = 1018041, 
    ['RaceHurtMulti5_P'] = 1018042, 
    ['RaceHurtReduce1'] = 1018100, 
    ['RaceHurtReduce1_N'] = 1018101, 
    ['RaceHurtReduce1_P'] = 1018102, 
    ['RaceHurtReduce2'] = 1018110, 
    ['RaceHurtReduce2_N'] = 1018111, 
    ['RaceHurtReduce2_P'] = 1018112, 
    ['RaceHurtReduce3'] = 1018120, 
    ['RaceHurtReduce3_N'] = 1018121, 
    ['RaceHurtReduce3_P'] = 1018122, 
    ['RaceHurtReduce4'] = 1018130, 
    ['RaceHurtReduce4_N'] = 1018131, 
    ['RaceHurtReduce4_P'] = 1018132, 
    ['RaceHurtReduce5'] = 1018140, 
    ['RaceHurtReduce5_N'] = 1018141, 
    ['RaceHurtReduce5_P'] = 1018142, 
    ['SeerProp1'] = 1022110, 
    ['ShieldBreak'] = 1017010, 
    ['ShieldBreak_N'] = 1017011, 
    ['SilenceAnti'] = 1015700, 
    ['SilenceAnti_N'] = 1015701, 
    ['SilenceAnti_P'] = 1015702, 
    ['SilenceHitRate'] = 1015720, 
    ['SilenceHitRate_N'] = 1015721, 
    ['SkillAnti'] = 1017030, 
    ['SkillAnti_N'] = 1017031, 
    ['SkillAnti_P'] = 1017032, 
    ['SkillEnhance'] = 1017040, 
    ['SkillEnhance_N'] = 1017041, 
    ['SkillEnhance_P'] = 1017042, 
    ['SleepAnti'] = 1015400, 
    ['SleepAnti_N'] = 1015401, 
    ['SleepAnti_P'] = 1015402, 
    ['SleepHitRate'] = 1015420, 
    ['SleepHitRate_N'] = 1015421, 
    ['SlowAnti'] = 1015800, 
    ['SlowAnti_N'] = 1015801, 
    ['SlowAnti_P'] = 1015802, 
    ['SlowHitRate'] = 1015820, 
    ['SlowHitRate_N'] = 1015821, 
    ['Speed'] = 1011100, 
    ['Speed_N'] = 1011101, 
    ['Speed_P'] = 1011102, 
    ['Stagger'] = 1020080, 
    ['StaminaConsumeRate'] = 1020040, 
    ['StaminaRecoverRate'] = 1020050, 
    ['StaminaValue'] = 1020030, 
    ['Str'] = 1010020, 
    ['Str_N'] = 1010021, 
    ['Str_P'] = 1010022, 
    ['SunHurtMulti'] = 1019000, 
    ['SunHurtMulti_N'] = 1019001, 
    ['SunHurtMulti_P'] = 1019002, 
    ['SunHurtReduce'] = 1019200, 
    ['SunHurtReduce_N'] = 1019201, 
    ['SunHurtReduce_P'] = 1019202, 
    ['SunProp1'] = 1022060, 
    ['TenebrousAnti'] = 1014630, 
    ['TenebrousAnti_N'] = 1014631, 
    ['TenebrousAnti_P'] = 1014632, 
    ['TenebrousAtk'] = 1014600, 
    ['TenebrousAtk_N'] = 1014601, 
    ['TenebrousAtk_P'] = 1014602, 
    ['TenebrousHurtMulti'] = 1014610, 
    ['TenebrousHurtMulti_N'] = 1014611, 
    ['TenebrousHurtReduce'] = 1014620, 
    ['TenebrousHurtReduce_N'] = 1014621, 
    ['TenebrousIgnoreDef'] = 1011340, 
    ['TenebrousIgnoreDef_N'] = 1011341, 
    ['TenebrousIgnoreDef_P'] = 1011342, 
    ['TenebrousLevel'] = 1014660, 
    ['TenebrousLevel_N'] = 1014661, 
    ['TenebrousRate'] = 1014650, 
    ['TenebrousRate_N'] = 1014651, 
    ['TiedAnti'] = 1015300, 
    ['TiedAnti_N'] = 1015301, 
    ['TiedAnti_P'] = 1015302, 
    ['TiedHitRate'] = 1015320, 
    ['TiedHitRate_N'] = 1015321, 
    ['UltimatePoint'] = 1024010, 
    ['UltimatePointSpeed'] = 1011380, 
    ['UltimatePointSpeed_N'] = 1011381, 
    ['UltimatePointSpeed_P'] = 1011382, 
    ['UtoProp1'] = 1022070, 
    ['VisionaryHurtMulti'] = 1019010, 
    ['VisionaryHurtMulti_N'] = 1019011, 
    ['VisionaryHurtMulti_P'] = 1019012, 
    ['VisionaryHurtReduce'] = 1019210, 
    ['VisionaryHurtReduce_N'] = 1019211, 
    ['VisionaryHurtReduce_P'] = 1019212, 
    ['WarProp1'] = 1022090, 
    ['WarriorHurtMulti'] = 1019040, 
    ['WarriorHurtMulti_N'] = 1019041, 
    ['WarriorHurtMulti_P'] = 1019042, 
    ['WarriorHurtReduce'] = 1019240, 
    ['WarriorHurtReduce_N'] = 1019241, 
    ['WarriorHurtReduce_P'] = 1019242, 
    ['aShield'] = 1023000, 
    ['mAtkMax'] = 1013010, 
    ['mAtkMax_F'] = 1013013, 
    ['mAtkMax_N'] = 1013011, 
    ['mAtkMax_P'] = 1013012, 
    ['mAtkMin'] = 1013000, 
    ['mAtkMin_F'] = 1013003, 
    ['mAtkMin_N'] = 1013001, 
    ['mAtkMin_P'] = 1013002, 
    ['mAtkSpd'] = 1013030, 
    ['mAtkSpd_N'] = 1013031, 
    ['mAtkSpd_P'] = 1013032, 
    ['mBlock'] = 1013150, 
    ['mBlockLimit'] = 1021040, 
    ['mBlock_N'] = 1013151, 
    ['mBlock_P'] = 1013152, 
    ['mCrit'] = 1013080, 
    ['mCritAnti'] = 1013090, 
    ['mCritAnti_N'] = 1013091, 
    ['mCritAnti_P'] = 1013092, 
    ['mCritDef'] = 1013110, 
    ['mCritDef_N'] = 1013111, 
    ['mCritDef_P'] = 1013112, 
    ['mCritHurt'] = 1013100, 
    ['mCritHurt_N'] = 1013101, 
    ['mCritHurt_P'] = 1013102, 
    ['mCrit_N'] = 1013081, 
    ['mCrit_P'] = 1013082, 
    ['mDef'] = 1013040, 
    ['mDef_F'] = 1013043, 
    ['mDef_N'] = 1013041, 
    ['mDef_P'] = 1013042, 
    ['mHurtMulti'] = 1013120, 
    ['mHurtMulti_N'] = 1013121, 
    ['mHurtMulti_P'] = 1013122, 
    ['mHurtReduce'] = 1013130, 
    ['mHurtReduce_N'] = 1013131, 
    ['mHurtReduce_P'] = 1013132, 
    ['mIgnoreDef'] = 1013050, 
    ['mIgnoreDef_N'] = 1013051, 
    ['mIgnoreDef_P'] = 1013052, 
    ['mPierce'] = 1013140, 
    ['mPierce_N'] = 1013141, 
    ['mPierce_P'] = 1013142, 
    ['mShield'] = 1023020, 
    ['pAtkMax'] = 1012010, 
    ['pAtkMax_F'] = 1012013, 
    ['pAtkMax_N'] = 1012011, 
    ['pAtkMax_P'] = 1012012, 
    ['pAtkMin'] = 1012000, 
    ['pAtkMin_F'] = 1012003, 
    ['pAtkMin_N'] = 1012001, 
    ['pAtkMin_P'] = 1012002, 
    ['pAtkSpd'] = 1012030, 
    ['pAtkSpd_N'] = 1012031, 
    ['pAtkSpd_P'] = 1012032, 
    ['pBlock'] = 1012150, 
    ['pBlockLimit'] = 1021030, 
    ['pBlock_N'] = 1012151, 
    ['pBlock_P'] = 1012152, 
    ['pCrit'] = 1012080, 
    ['pCritAnti'] = 1012090, 
    ['pCritAnti_N'] = 1012091, 
    ['pCritAnti_P'] = 1012092, 
    ['pCritDef'] = 1012110, 
    ['pCritDef_N'] = 1012111, 
    ['pCritDef_P'] = 1012112, 
    ['pCritHurt'] = 1012100, 
    ['pCritHurt_N'] = 1012101, 
    ['pCritHurt_P'] = 1012102, 
    ['pCrit_N'] = 1012081, 
    ['pCrit_P'] = 1012082, 
    ['pDef'] = 1012040, 
    ['pDef_F'] = 1012043, 
    ['pDef_N'] = 1012041, 
    ['pDef_P'] = 1012042, 
    ['pHurtMulti'] = 1012120, 
    ['pHurtMulti_N'] = 1012121, 
    ['pHurtMulti_P'] = 1012122, 
    ['pHurtReduce'] = 1012130, 
    ['pHurtReduce_N'] = 1012131, 
    ['pHurtReduce_P'] = 1012132, 
    ['pIgnoreDef'] = 1012050, 
    ['pIgnoreDef_N'] = 1012051, 
    ['pIgnoreDef_P'] = 1012052, 
    ['pPierce'] = 1012140, 
    ['pPierce_N'] = 1012141, 
    ['pPierce_P'] = 1012142, 
    ['pShield'] = 1023010, 
}

Enum.EPVPEntranceConstData = {
    ['PVP_ENTRANCE_DESC_TIPS'] = 6427228, 
    ['PVP_ENTRANCE_SHOP_UIJUMP'] = 1250101, 
}

Enum.EPVPGameModeData = {
    ['CHAMPION_ELIMINATION_3'] = 5500005, 
    ['CHAMPION_ELIMINATION_5'] = 5500006, 
    ['CHAMPION_GROUP_BATTLE'] = 5500004, 
    ['GUILD_TEAM'] = 5500008, 
    ['NEW_TEAM12V12'] = 5500007, 
    ['TEAM12V12'] = 5500003, 
    ['TEAM3V3'] = 5500002, 
    ['TEAM5V5'] = 5500001, 
}

Enum.ERedNameConstFloatData = {
    ['BOUNTY_MULTIPLE'] = 1.5, 
}

Enum.ERedNameConstIntData = {
    ['FOE_NUM_MAX'] = 50, 
    ['GRUDGE_VALUE'] = 3, 
    ['HEREDITARY_FOE_NUM_MAX'] = 50, 
    ['HOSTILE_CLEARDURATION'] = 30, 
    ['HOSTILE_RANGE'] = 40, 
    ['LEVEL_DIFFERENCE'] = 3, 
    ['MIN_RED_BOUNTY'] = 20, 
    ['YELLOW_DURATIONTIME'] = 3600, 
    ['YELLOW_UPDATE_INTERVAL'] = 60, 
}

Enum.ERedPointEnumData = {
    ['REDPOINT_ASSIST_COMBAT'] = 30003, 
    ['REDPOINT_DUNGEON_UNLOCK'] = 10001, 
    ['REDPOINT_EQUIP_BODY_ENHANCE_SLOT'] = 50002, 
    ['REDPOINT_EQUIP_INVENTORY'] = 50001, 
    ['REDPOINT_FASHION_ITEM'] = 70001, 
    ['REDPOINT_FELLOW_LIST'] = 30001, 
    ['REDPOINT_JOIN_COMBAT'] = 30002, 
    ['REDPOINT_ROLEPLAY_IDENTITY'] = 80001, 
    ['REDPOINT_SEALED_SLOT'] = 40001, 
    ['REDPOINT_SKILL_LIST'] = 20001, 
    ['REDPOINT_SOCIALACTION_ITEM'] = 60001, 
}

Enum.EReminderTextData = {
    ['3-888_Mech_End'] = 6406380, 
    ['3-888_Mech_Start'] = 6406379, 
    ['ACCEPT_GUILD_INVITE'] = 6402029, 
    ['ACCEPT_RIDE_MOUNT_IS_FAR'] = 6442079, 
    ['ACCEPT_RIDE_MOUNT_IS_FULL'] = 6442080, 
    ['ACCEPT_RIDE_MOUNT_TARGET_EMPTY'] = 6442081, 
    ['ACCOUNT_BE_REPLACED'] = 6400107, 
    ['ACCOUNT_LOGIN_CHECK_FAIL'] = 6400113, 
    ['ACCOUNT_LOGIN_CHECK_VERSION_ERROR'] = 6400116, 
    ['ACHIEVEMENT_COLLECT_ALREADY'] = 6440323, 
    ['ACHIEVEMENT_COLLECT_LIMIT'] = 6440348, 
    ['ACHIEVEMENT_COLLECT_NOT_EXIST'] = 6440324, 
    ['ACHIEVEMENT_COMMON'] = 6440347, 
    ['ACHIEVEMENT_LEVEL_NO_REWARD'] = 6440325, 
    ['ACHIEVEMENT_LEVEL_REWARD_ALREADY'] = 6440327, 
    ['ACHIEVEMENT_NOT_EXIST'] = 6440330, 
    ['ACHIEVEMENT_NOT_FINISH'] = 6440328, 
    ['ACHIEVEMENT_PARAM_ERROR'] = 6440326, 
    ['ACHIEVEMENT_REWARD_ALREADY'] = 6440329, 
    ['ACTIVITY_END_REMINDER'] = 6403005, 
    ['ACTIVITY_MONEY_WEEK_LIMIT_NOTICE'] = 6401044, 
    ['ACTIVITY_NOT_OPEN'] = 6405402, 
    ['ACTIVITY_OPEN_REMINDER'] = 6403004, 
    ['ACTIVITY_PRE_REMINDER'] = 6403003, 
    ['ACTIVITY_TRACE_FAIL'] = 6403006, 
    ['AGREE_GUILD_APPLY_SUCCESS'] = 6402051, 
    ['ALL_EXIT_DUNGEON_CLOSE'] = 6406074, 
    ['ALREADY_IN_GUILD'] = 6402010, 
    ['AMON1'] = 6406071, 
    ['AMON2'] = 6406072, 
    ['AMON3'] = 6406073, 
    ['ANON_CHAT_NOT_OPEN'] = 6401646, 
    ['ANON_LIKE_LIMIT'] = 6401645, 
    ['APPRENTICE_KICK_OUT'] = 6402054, 
    ['AREAR3V3_UNLOCK'] = 6401702, 
    ['AREA_ICON_NAME'] = 6400215, 
    ['ASK_REVIVE_COOLDOWN'] = 6400775, 
    ['ATKEQUIP_RANDLIMIT'] = 6401722, 
    ['AUCTION_COMPLETE'] = 6406053, 
    ['AUTONAVIGATION_BEGIN'] = 6405928, 
    ['AUTONAVIGATION_CANNOT_ARRIVE'] = 6405940, 
    ['AUTONAVIGATION_LEAD'] = 6405929, 
    ['AUTONAVIGATION_NONE'] = 6405930, 
    ['AUTO_BATTLE_STOP'] = 6441801, 
    ['AUTO_DECOMPOSE_LOCKED'] = 6401061, 
    ['AUTO_DECOMPOSE_OFF'] = 6401063, 
    ['AUTO_DECOMPOSE_ON'] = 6401062, 
    ['AUTO_NAVIGATION_OPENING'] = 6409019, 
    ['AVATAR_IN_TELEPORT_STATUS'] = 6400217, 
    ['BAG_FULL_MAIL_ITEMS'] = 6401008, 
    ['BALL_GAME_END'] = 6441907, 
    ['BALL_GAME_START'] = 6441906, 
    ['BASE_IS_UNLOCK'] = 6405602, 
    ['BASICDANCE_INVITE'] = 6442087, 
    ['BASICDANCE_REFUSE'] = 6442086, 
    ['BATCH_APPLY_GUILDS_IN_CD'] = 6402052, 
    ['BATCH_APPLY_GUILDS_SUCCESS'] = 6402053, 
    ['BATTLE_FORBID_SWITCH_PROFESSION_STATE_DEAD'] = 6442083, 
    ['BATTLE_FORBID_SWITCH_PROFESSION_STATE_IN_BATTLE'] = 6442046, 
    ['BATTLE_START_TIMER'] = 6405700, 
    ['BELOW_CURRENT_PRICE'] = 6406051, 
    ['BELOW_LOWEST_PRICE'] = 6406050, 
    ['BID_GUILD_NOT_OPEN'] = 6405208, 
    ['BID_ITEM_FAIL_BY_ALREADY_BUY_OUT'] = 6405203, 
    ['BID_ITEM_FAIL_BY_ALREADY_IN_HIGHEST_BIDDER'] = 6405204, 
    ['BID_ITEM_FAIL_BY_EXPIRY_TIME'] = 6405206, 
    ['BID_ITEM_FAIL_BY_NOT_OPEN'] = 6405205, 
    ['BID_ITEM_NOT_EXIT'] = 6405201, 
    ['BID_ITEM_PRICE_OUT_OF_DATE'] = 6405202, 
    ['BID_MONEY_LACK_ERR'] = 6405200, 
    ['BID_PREPARE_PERIOD_CLICK'] = 6405209, 
    ['BID_SUCCESS'] = 6405212, 
    ['BID_WORLD_NOT_OPEN'] = 6405207, 
    ['BLACKLIST_BLOCK_GIFT'] = 6401580, 
    ['BLOCK_OTHER_PLAYER'] = 6409029, 
    ['BOSS_CLOWN_SKILLNAME1'] = 6406203, 
    ['BOSS_CLOWN_SKILLNAME2'] = 6406204, 
    ['BOSS_CLOWN_SKILLNAME3'] = 6406205, 
    ['BOSS_CLOWN_SKILLNAME4'] = 6406206, 
    ['BOSS_CLOWN_SKILLNAME5'] = 6406207, 
    ['BOSS_CLOWN_SKILLNAME6'] = 6406208, 
    ['BOSS_CLOWN_SKILLNAME7'] = 6406234, 
    ['BOSS_FIGHT_REVIVE'] = 6400776, 
    ['BOS_2049_SKILLNAME1'] = 6406209, 
    ['BOS_2049_SKILL_zishe_other'] = 6406333, 
    ['BOS_2049_SKILL_zishe_self'] = 6406332, 
    ['BOS_Thug_SKILL_1'] = 6406334, 
    ['BOS_Thug_SKILL_2'] = 6406335, 
    ['BOT_PROFESSION_EXCEED_LIMIT'] = 6441702, 
    ['BOT_PROFESSION_MATCH_FAIL'] = 6441914, 
    ['BOT_PROFESSION_SWITCH_FAIL'] = 6441913, 
    ['BOT_PROFESSION_SWITCH_SUCCESS'] = 6441912, 
    ['BREAK_COCOON'] = 6408100, 
    ['BREAK_STUCK_CD'] = 6401801, 
    ['BREAK_STUCK_NOT_ALLOWED'] = 6401802, 
    ['BUFF_LOCKTARGET_INCONTROL'] = 6406018, 
    ['BUFF_PALYER_INCONTROL'] = 6406017, 
    ['CALL_GHOST'] = 6440321, 
    ['CANCEL_BLOCK_OTHER_PLAYER'] = 6409030, 
    ['CANE_NAVIGATION_FAILED'] = 6403503, 
    ['CANNOT_INTERACT_IN_DEATH'] = 6400907, 
    ['CANNOT_REDIVIINATION'] = 6405401, 
    ['CANNOT_TELEPORT_IN_BATTLE'] = 6400216, 
    ['CANT_CONTACT_ONESELF'] = 6402065, 
    ['CANT_DELETE_MAIL'] = 6401110, 
    ['CAN_NOT_ADD_HERB'] = 6405608, 
    ['CAN_NOT_CREATE_ROLE'] = 6400111, 
    ['CAN_NOT_IMMERSIVE_MODE'] = 6409028, 
    ['CAN_NOT_LEAVE_TEAM_IN_PVP'] = 6405821, 
    ['CAN_NOT_SELECT_ROLE'] = 6400109, 
    ['CAREFUL_SIGNAL'] = 6405721, 
    ['CATCH_MONSTER_S'] = 6440331, 
    ['CATCH_MOUSE'] = 6405806, 
    ['CATCH_MOUSE_1'] = 6405807, 
    ['CHALLENGING_DUNGEON_NOW'] = 6406033, 
    ['CHAMPION_FORBID_MODIFY_NAME'] = 6442017, 
    ['CHAMPION_NOT_IN_TROOP'] = 6441916, 
    ['CHAMPION_PROFESSION_DUPLICATE'] = 6441931, 
    ['CHAMPION_SIGNUP_FAILED_FOR_STAGE'] = 6441927, 
    ['CHAMPION_TROOP_ALREADY_IN_TEAM'] = 6441919, 
    ['CHAMPION_TROOP_CANCEL_SIGNUP_FOR_QUIT'] = 6441926, 
    ['CHAMPION_TROOP_CHANGE_LEADER_LOCKED'] = 6442053, 
    ['CHAMPION_TROOP_CREATED'] = 6441935, 
    ['CHAMPION_TROOP_CREATE_FAILED_FOR_SIGNUP_LEVEL_LIMIT'] = 6441938, 
    ['CHAMPION_TROOP_CREATING'] = 6441918, 
    ['CHAMPION_TROOP_DISBAND'] = 6441923, 
    ['CHAMPION_TROOP_JOIN'] = 6441921, 
    ['CHAMPION_TROOP_JOIN_FAILED_FOR_SIGNUP_LEVEL_LIMIT'] = 6441937, 
    ['CHAMPION_TROOP_JOIN_FAILED_FOR_SIGNUP_SCHOOL_LIMIT'] = 6441936, 
    ['CHAMPION_TROOP_LOCKING'] = 6441915, 
    ['CHAMPION_TROOP_MEMBER_FULL_CANNOT_JOIN'] = 6441929, 
    ['CHAMPION_TROOP_NAME_LENGTH_LIMIT'] = 6441939, 
    ['CHAMPION_TROOP_NOT_EXIST'] = 6441928, 
    ['CHAMPION_TROOP_NOT_SIGN_UP'] = 6441917, 
    ['CHAMPION_TROOP_NUM_TOO_LOW'] = 6441930, 
    ['CHAMPION_TROOP_OPERATION_LIMITED'] = 6442066, 
    ['CHAMPION_TROOP_QUIT'] = 6441922, 
    ['CHAMPION_TROOP_QUIT_FAILED'] = 6442018, 
    ['CHAMPION_TROOP_REQUIRED'] = 6441932, 
    ['CHAMPION_TROOP_SELF_JOIN'] = 6441933, 
    ['CHAMPION_TROOP_SELF_QUIT'] = 6441934, 
    ['CHAMPION_TROOP_TOO_FEW_MEMBER'] = 6442009, 
    ['CHAMPION_TROOP_TOO_MUCH_SAME_SCHOOL'] = 6442010, 
    ['CHANGE_FIGHTMODE_INBATTLE'] = 6403101, 
    ['CHANGE_FIGHTMODE_INGUILD'] = 6403102, 
    ['CHATROOM_APPLY_ALREADY_SEND'] = 6440922, 
    ['CHATROOM_APPLY_LIST_NUM_LIMIT'] = 6440932, 
    ['CHATROOM_APPLY_LIST_OPERATE_NUM_MAX'] = 6440937, 
    ['CHATROOM_APPLY_REJECTED'] = 6440946, 
    ['CHATROOM_APPLY_SEND'] = 6440921, 
    ['CHATROOM_APPLY_SENSITIVE'] = 6440920, 
    ['CHATROOM_BAN_MEMBER'] = 6440909, 
    ['CHATROOM_BAN_SPEAK_LIST_NUM_LIMIT'] = 6440933, 
    ['CHATROOM_BE_BANNED'] = 6440910, 
    ['CHATROOM_BE_OWNER'] = 6440918, 
    ['CHATROOM_BE_UNBANNED'] = 6440912, 
    ['CHATROOM_BLACKLIST_INVALID'] = 6440915, 
    ['CHATROOM_BLACKLIST_MEMBER'] = 6440913, 
    ['CHATROOM_BLACKLIST_NUM_LIMIT'] = 6440934, 
    ['CHATROOM_CANCEL_BAN'] = 6442090, 
    ['CHATROOM_CANCEL_BAN_MEMBER'] = 6440911, 
    ['CHATROOM_CREATE_SUCCESS'] = 6440906, 
    ['CHATROOM_EXIT_ROOM'] = 6440925, 
    ['CHATROOM_GET_MIC_PERMISSION'] = 6440907, 
    ['CHATROOM_GIFT_TARGET_ERROR'] = 6440938, 
    ['CHATROOM_GIVE_GIFT_INFO'] = 6440941, 
    ['CHATROOM_IDENTITY_NAME_EXCEED'] = 6442089, 
    ['CHATROOM_IDENTITY_NUMBER_EXCEED'] = 6442088, 
    ['CHATROOM_INFO_CHANGE_SUCC'] = 6440926, 
    ['CHATROOM_IN_BLACKLIST'] = 6440914, 
    ['CHATROOM_LINK_INVALID'] = 6440903, 
    ['CHATROOM_MIC_NUM_FULL'] = 6440908, 
    ['CHATROOM_MIC_PERMISSION_FAIL'] = 6440936, 
    ['CHATROOM_NAME_DUPLICATE'] = 6440905, 
    ['CHATROOM_NAME_EMPTY'] = 6440931, 
    ['CHATROOM_NAME_INVALID'] = 6440904, 
    ['CHATROOM_NOT_EXIST'] = 6440924, 
    ['CHATROOM_NOT_FIRST'] = 6440919, 
    ['CHATROOM_NO_ROOM_ENTER'] = 6440902, 
    ['CHATROOM_OUT_BLACKLIST'] = 6440916, 
    ['CHATROOM_OWNER_TRANSFER_CD'] = 6440935, 
    ['CHATROOM_PARTY_COLLECT_FAIL'] = 6442030, 
    ['CHATROOM_PARTY_COLLECT_REMOVE'] = 6442020, 
    ['CHATROOM_PARTY_COLLECT_REMOVE_SELECT'] = 6442019, 
    ['CHATROOM_PARTY_COLLECT_SUCC'] = 6442029, 
    ['CHATROOM_PARTY_IDENTITY_MYSELF'] = 6442064, 
    ['CHATROOM_PARTY_IDENTITY_REPEAT'] = 6442028, 
    ['CHATROOM_PARTY_IDENTITY_STATE_UPDATE'] = 6442027, 
    ['CHATROOM_PARTY_PERMANENT_ROOM_MAX'] = 6442022, 
    ['CHATROOM_PARTY_RENEWAL_MAX'] = 6442021, 
    ['CHATROOM_PARTY_RENEWAL_NOITEM'] = 6442045, 
    ['CHATROOM_PARTY_RENEWAL_SUCC'] = 6442023, 
    ['CHATROOM_PARTY_ROOM_DISBAND'] = 6442025, 
    ['CHATROOM_PARTY_ROOM_DISBAND_SUCC'] = 6442026, 
    ['CHATROOM_PARTY_SERVER_ROOM_MAX'] = 6442024, 
    ['CHATROOM_PERMISSION_CANCEL'] = 6440928, 
    ['CHATROOM_PIN_INVALID'] = 6440923, 
    ['CHATROOM_PLAYER_BECOME_MASTER'] = 6440943, 
    ['CHATROOM_PLAYER_ENTER'] = 6440942, 
    ['CHATROOM_PLAYER_LEFT'] = 6440940, 
    ['CHATROOM_ROOM_AUTODESTROY_MSG'] = 6440944, 
    ['CHATROOM_ROOM_FREQUENT_SHARE'] = 6440945, 
    ['CHATROOM_ROOM_FULL'] = 6440900, 
    ['CHATROOM_ROOM_NUM_FULL'] = 6440901, 
    ['CHATROOM_SHARE_SUCCESS'] = 6440939, 
    ['CHATROOM_SPEAKER_LIMIT_EXCEED'] = 6440930, 
    ['CHATROOM_TOO_MANY_APPLY'] = 6440927, 
    ['CHATROOM_TRANSFER_OWNER'] = 6440917, 
    ['CHAT_ADD_WORLD_CHAT_FREE_NUM_NOTIFY'] = 6401612, 
    ['CHAT_ALREADY_ANOY'] = 6440517, 
    ['CHAT_ANOY_INVINCIBLE'] = 6440508, 
    ['CHAT_ANOY_LIKE'] = 6440506, 
    ['CHAT_ANOY_LIKE_LIMIT'] = 6440507, 
    ['CHAT_ANOY_NO_ITEM'] = 6440505, 
    ['CHAT_APPLY_TEAM_FAIL'] = 6401636, 
    ['CHAT_BAN'] = 6401643, 
    ['CHAT_BUBBLE_LOCKED'] = 6442060, 
    ['CHAT_CAN_NOT_REVEAL_SELF'] = 6440515, 
    ['CHAT_CD'] = 6401610, 
    ['CHAT_CHANGE_GROUPSET'] = 6440510, 
    ['CHAT_CHANNEL_LIMIT'] = 6440504, 
    ['CHAT_CHANNEL_UNIFORM'] = 6440503, 
    ['CHAT_CREATE_GROUPSET'] = 6440511, 
    ['CHAT_CUSTOM_IMG_BAN'] = 6401648, 
    ['CHAT_DAILY_SEND_NUM_LIMIT'] = 6401611, 
    ['CHAT_FIGHT_ATK_ACTIVE'] = 6401620, 
    ['CHAT_FIGHT_ATK_PASSIVE'] = 6401624, 
    ['CHAT_FIGHT_BE_DEFEATED'] = 6401627, 
    ['CHAT_FIGHT_CONTROL'] = 6401637, 
    ['CHAT_FIGHT_CONTROL_ACTIVE'] = 6401634, 
    ['CHAT_FIGHT_CONTROL_PASSIVE'] = 6401635, 
    ['CHAT_FIGHT_DEFEAT'] = 6401623, 
    ['CHAT_FIGHT_HPREG'] = 6401625, 
    ['CHAT_FIGHT_NOT_HIT'] = 6401638, 
    ['CHAT_FIGHT_NO_NAME'] = 6401644, 
    ['CHAT_FIGHT_REG'] = 6401621, 
    ['CHAT_FIGHT_RESURRECT_ACTIVE'] = 6401622, 
    ['CHAT_FIGHT_RESURRECT_PASSIVE'] = 6401626, 
    ['CHAT_GET_ITEM'] = 6401632, 
    ['CHAT_GET_ITEM_ALONE'] = 6401631, 
    ['CHAT_GET_MONEY_OR_EXP'] = 6401633, 
    ['CHAT_GROUP_CREATE_MEMBER_MAX'] = 6401606, 
    ['CHAT_GROUP_INVITE_FAILED'] = 6401607, 
    ['CHAT_GROUP_INVITE_RESET'] = 6401608, 
    ['CHAT_IMAGE_CHECK'] = 6440501, 
    ['CHAT_IMAGE_CHECK_CAN_NOT_SEND'] = 6440502, 
    ['CHAT_INPUT_MAX'] = 6401609, 
    ['CHAT_LOUD_SPEAKER_SEND_SUCCESS'] = 6401613, 
    ['CHAT_MAIL_EXPIRE'] = 6401639, 
    ['CHAT_MAIL_EXPIRE_LOT'] = 6401640, 
    ['CHAT_NOT_ALLOW_VOICE'] = 6440514, 
    ['CHAT_NO_CHANGE'] = 6440509, 
    ['CHAT_NO_ENOUGH_ITEM'] = 6440500, 
    ['CHAT_OPERATE_FREQUENT'] = 6401605, 
    ['CHAT_OVERTIME_NO_REVEAL'] = 6440516, 
    ['CHAT_RANGE_SAVE_SUCCESS'] = 6440512, 
    ['CHAT_RECORD_FAILED'] = 6401601, 
    ['CHAT_SENDER_IN_TARGET_BLACK'] = 6401604, 
    ['CHAT_SEND_BLACK_TARGET'] = 6401603, 
    ['CHAT_SEND_BLANK'] = 6401602, 
    ['CHAT_SEND_FAILED_LEVEL_LIMIT'] = 6401614, 
    ['CHAT_SEND_FAILED_MONEY_NOTENOUGH'] = 6401616, 
    ['CHAT_SEND_VOICE_CANCEL'] = 6401617, 
    ['CHAT_SEND_VOICE_NO_PERMISSION'] = 6401628, 
    ['CHAT_SEND_VOICE_SWITCH_WORD'] = 6401618, 
    ['CHAT_SEND_VOICE_TRANSLATE_FAIL'] = 6401619, 
    ['CHAT_SPENT_CURRENCY'] = 6401641, 
    ['CHAT_SPENT_ITEM'] = 6401642, 
    ['CHAT_TOO_FRENQUENTLY'] = 6440513, 
    ['CHECK_STRING_DIRTY_FAILED'] = 6409010, 
    ['CLOSE_GUILD_CHANNEL_VOICE'] = 6402218, 
    ['CLUB_AGREE_APPLICATION'] = 6440823, 
    ['CLUB_EJECT_MEMBER'] = 6440822, 
    ['CLUB_GROUP_FIRST_CREATE'] = 6440828, 
    ['CLUB_GROUP_MEMBER_LIMIT'] = 6440827, 
    ['CLUB_INVITE_MYSELF_TIP'] = 6440829, 
    ['CLUB_MEMBER_CHANGE'] = 6440825, 
    ['CLUB_MEMBER_CHANGE_OUT'] = 6440826, 
    ['CLUB_NO_PERMISSION_TO_GROUP_MEMBER'] = 6440832, 
    ['CLUB_NO_PERMISSION_TO_STAR'] = 6440830, 
    ['CLUB_NO_PERMISSION_TO_SWITCH_MEMBER'] = 6440831, 
    ['CLUB_REJECT_APPLICATION'] = 6440824, 
    ['CLUB_UNABLE_NO_OTHERONE'] = 6440819, 
    ['COIN1_NOTSATISFY'] = 6402329, 
    ['COIN2_NOTSATISFY'] = 6402330, 
    ['COLLECTIBLES_ALL_LEVEL_REWARDED'] = 6440339, 
    ['COLLECTIBLES_DESC_DIRTY'] = 6440349, 
    ['COLLECTIBLES_LEVEL_REWARDED'] = 6440341, 
    ['COLLECTIBLES_UNREACH_LEVEL'] = 6440340, 
    ['COMBAT_DENY_EQUIP'] = 6401064, 
    ['COMBINE_FELLOW_HAVE_EXIST'] = 6401900, 
    ['COMBINE_FULL_FAILED'] = 6401009, 
    ['COMMON_AUTH_FAILED'] = 6409007, 
    ['COMMON_CUSTOM_STRING'] = 6409031, 
    ['COMMON_ERROR_STATE_MACHINE'] = 6409009, 
    ['COMMON_ILLEGAL_NAME'] = 6409008, 
    ['COMMON_MODULE_LOCKED'] = 6409002, 
    ['COMMON_PROTOCOL_NOT_MATCH'] = 6409015, 
    ['COMMON_SERVER_LAUNCH_TIME'] = 6409013, 
    ['COMMON_SERVER_NOT_ALLOW_GM'] = 6409014, 
    ['COMMON_START'] = 6405813, 
    ['COMMON_TOO_FREQUENTLY'] = 6409001, 
    ['CONSECUTIVE_KILL'] = 6405705, 
    ['CONTROL_POINT_ACTIVATED'] = 6405797, 
    ['CORE_OUTPUT'] = 6405707, 
    ['COUNTERATTACK'] = 6405708, 
    ['CRITICAL_CONTROL'] = 6405711, 
    ['CRITICAL_TREATMENT'] = 6405706, 
    ['CROSS_WORD_PUZZLE_EGG_TIPS'] = 6401402, 
    ['CROSS_WORD_PUZZLE_INCORRECT'] = 6401401, 
    ['CURRENCY_TOPLIMIT_THRESHOLD_NOTICE'] = 6401045, 
    ['CUSTOMROLE_DATA_EXPORT_TIP'] = 6407206, 
    ['CUSTOMROLE_DATA_INPORT_FAIL'] = 6407208, 
    ['CUSTOMROLE_DATA_INPORT_SUCCESS'] = 6407207, 
    ['CUSTOMROLE_IMPORT_TIPS'] = 6441904, 
    ['CUSTOMTAG_NUMMAX'] = 6401313, 
    ['CUTPRICE_END'] = 6403502, 
    ['CUT_CANDIDATE_SYS_FRIEND'] = 6402067, 
    ['CYCLE_UPPER_LIMIT'] = 6401002, 
    ['Campaign_1511_treeman_reminder01'] = 6406242, 
    ['Campaign_1511_treeman_skill01'] = 6406243, 
    ['Campaign_1511_treeman_skill02'] = 6406244, 
    ['Campaign_1511_treeman_skill03'] = 6406245, 
    ['Campaign_156_fry_skill01'] = 6406257, 
    ['Campaign_156_reminder01'] = 6406236, 
    ['Campaign_treeman_skill04'] = 6406346, 
    ['Campaign_treeman_skill04_desc1'] = 6406347, 
    ['Campaign_treeman_skill04_desc2'] = 6406355, 
    ['Campaign_treeman_skill04_desc3'] = 6406437, 
    ['Campaign_treeman_skill04_desc4'] = 6406395, 
    ['Campaign_treewoman_skill01'] = 6406405, 
    ['Campaign_treewoman_skill02'] = 6406406, 
    ['ClockBlade_False'] = 6406394, 
    ['DANCE_DOUBLE_EXIT_CHOOSE'] = 6440313, 
    ['DANCE_DOUBLE_START_FAIL_MEMBER_AOI'] = 6440307, 
    ['DANCE_DOUBLE_START_FAIL_MEMBER_FIGHT'] = 6440308, 
    ['DANCE_DOUBLE_START_FAIL_MEMBER_LOCK'] = 6440306, 
    ['DANCE_DOUBLE_START_FAIL_NOT_LEADER'] = 6440304, 
    ['DANCE_DOUBLE_START_FAIL_PLAYER_NUM'] = 6440305, 
    ['DANCE_DOUBLE_START_FAIL_POSITION_LESS'] = 6440309, 
    ['DANCE_DOUBLE_TIMEOUT'] = 6440312, 
    ['DANCE_ENERGY_LESS'] = 6440311, 
    ['DANCE_JOIN_FAIL_FORMATION_FULL'] = 6440315, 
    ['DANCE_LOCK'] = 6440300, 
    ['DANCE_MULTI_START_FAIL_POSITION_LESS'] = 6440310, 
    ['DANCE_SOLO_START_FAIL_MULTI'] = 6440301, 
    ['DANCE_SOLO_START_FAIL_NOT_LEADER'] = 6440303, 
    ['DANCE_START_FAIL_FIGHT'] = 6440302, 
    ['DANCE_TIME_MAX'] = 6440314, 
    ['DANCINGPARTY_ACCEPT_FAIL_PARTNER_LEAVE'] = 6440337, 
    ['DANCINGPARTY_ASSIST_END'] = 6440120, 
    ['DANCINGPARTY_GUILD_REWARD'] = 6440122, 
    ['DANCINGPARTY_INVITE_ACCEPT_FAIL'] = 6440108, 
    ['DANCINGPARTY_INVITE_ALREADY'] = 6440103, 
    ['DANCINGPARTY_INVITE_BEGIN'] = 6440101, 
    ['DANCINGPARTY_INVITE_FAIL_LEAVE'] = 6440105, 
    ['DANCINGPARTY_INVITE_FAIL_MATCH_ALREADY'] = 6440100, 
    ['DANCINGPARTY_INVITE_FAIL_OFFLINE'] = 6440106, 
    ['DANCINGPARTY_INVITE_FAIL_PARTNER'] = 6440104, 
    ['DANCINGPARTY_INVITE_REFUSE'] = 6440109, 
    ['DANCINGPARTY_INVITE_REFUSE_CD'] = 6440110, 
    ['DANCINGPARTY_INVITE_SUCCESS'] = 6440102, 
    ['DANCINGPARTY_INVITE_TIPS'] = 6440107, 
    ['DANCINGPARTY_LOTTERY'] = 6440121, 
    ['DANCINGPARTY_MATCH_BEGIN'] = 6440113, 
    ['DANCINGPARTY_MATCH_WAIT'] = 6440114, 
    ['DANCINGPARTY_MATCH_WAIT_TIPS'] = 6440123, 
    ['DANCINGPARTY_MUSIC'] = 6440115, 
    ['DANCINGPARTY_MUSIC_VARY'] = 6440117, 
    ['DANCINGPARTY_PARTNER_RELIEVE_ACTIVE'] = 6440200, 
    ['DANCINGPARTY_PARTNER_RELIEVE_LEAVE'] = 6440111, 
    ['DANCINGPARTY_PARTNER_RELIEVE_OFFLINE'] = 6440112, 
    ['DANCINGPARTY_REWARD_TIPS'] = 6440116, 
    ['DANCINGPARTY_SOLO'] = 6440118, 
    ['DANCINGPARTY_SOLO_PARTNER'] = 6440119, 
    ['DANGEROUS_SIGNAL'] = 6405716, 
    ['DARK_BOX'] = 6441714, 
    ['DB_VERSION_ERROR'] = 6409032, 
    ['DEBUG_INFO'] = 6400016, 
    ['DECOMPOSITION_CONFIGURATION_ERROR'] = 6401069, 
    ['DEFEQUIP_ADV_NOTOPEN'] = 6401716, 
    ['DEFEQUIP_BASE_NOITEM'] = 6401717, 
    ['DEFEQUIP_POINT_CHANGE'] = 6401715, 
    ['DEFEQUIP_RANDLIMIT'] = 6401723, 
    ['DEFEQUIP_REVERT_NOPOINT'] = 6401714, 
    ['DEMISE_PRESIDENT_SYS_FRIEND'] = 6402070, 
    ['DOG_CATCH_MOUSE'] = 6405811, 
    ['DOMINATE_BATTLEGROUND'] = 6405709, 
    ['DOUBLEPOINTS_END'] = 6405720, 
    ['DOUBLEPOINTS_START'] = 6405702, 
    ['DOUBLEPOINTS_START_PREVIEW'] = 6405701, 
    ['DUNGEON_AUCTION_GIVEUP_FAILED'] = 6400025, 
    ['DUNGEON_AUCTION_MONEY_NOT_ENOUGH'] = 6406077, 
    ['DUNGEON_AUCTION_OFFER_FAILED'] = 6400026, 
    ['DUNGEON_AUCTION_PREOFFER_INVALID'] = 6400024, 
    ['DUNGEON_AUTO_CLOSED'] = 6406030, 
    ['DUNGEON_CAPTAIN_OPEN_DUNGEON'] = 6406005, 
    ['DUNGEON_CHALLENGE_SUCCEED'] = 6406032, 
    ['DUNGEON_FAIL'] = 6406045, 
    ['DUNGEON_GROUP_DROPTIMELIMIT1'] = 6400018, 
    ['DUNGEON_GROUP_DROPTIMELIMIT2'] = 6400019, 
    ['DUNGEON_GROUP_DROPTIMELIMIT3'] = 6400020, 
    ['DUNGEON_GROUP_DROPTIMELIMIT4'] = 6400021, 
    ['DUNGEON_GROUP_DROPTIMELIMIT5'] = 6400022, 
    ['DUNGEON_GROUP_DROPTIMELIMIT6'] = 6400023, 
    ['DUNGEON_GROUP_LIMIT'] = 6406088, 
    ['DUNGEON_LEVEL_NOT_MATCH'] = 6400701, 
    ['DUNGEON_LIKE_REMINDER'] = 6406036, 
    ['DUNGEON_MAX_TIME_FAILED'] = 6406031, 
    ['DUNGEON_NUMBER_NOT_MATCH'] = 6406027, 
    ['DUNGEON_OPEN_TIME'] = 6400779, 
    ['DUNGEON_RESTART_FAILED'] = 6400705, 
    ['DUNGEON_RETURN_TIMEOUT_NOTIFY'] = 6406028, 
    ['DUNGEON_REWARD_MAX'] = 6401102, 
    ['DUNGEON_SINGLE_LIMIT'] = 6406086, 
    ['DUNGEON_STAGE_FAIL'] = 6406047, 
    ['DUNGEON_STAGE_LIMIT'] = 6401103, 
    ['DUNGEON_STAGE_SETTLEMENT'] = 6400704, 
    ['DUNGEON_STAGE_SUCCESS'] = 6406046, 
    ['DUNGEON_SUCCESS'] = 6406044, 
    ['DUNGEON_TEAMATE_IN_DUNGEON'] = 6406006, 
    ['DUNGEON_TEAMMATE_NOT_ONLINE'] = 6400702, 
    ['DUNGEON_TEAM_LIMIT'] = 6406087, 
    ['DUNGEON_TIPS_MULTIPLE_SEND'] = 6401630, 
    ['DUNGEON_TIPS_NO_TEAM'] = 6400778, 
    ['DUNGEON_TIPS_SEND_SUCCESS'] = 6401629, 
    ['DUNGEON_TYPE_NOT_AVAILABLE'] = 6400017, 
    ['DUNGEON_UITEST1'] = 6406099, 
    ['DUNGEON_UITEST2'] = 6406100, 
    ['DUNGEON_UITEST3'] = 6406201, 
    ['DUNGEON_UITEST4'] = 6406202, 
    ['Divination_Ofc_czhy'] = 6406438, 
    ['Divination_Ofc_kqsl'] = 6406440, 
    ['Divination_Ofc_ltzx'] = 6406439, 
    ['ELEMENT_LV_SUCCESS'] = 6402935, 
    ['ELEMENT_SWITCH_ELEMENT_FAIL'] = 6402934, 
    ['ELEMENT_SWITCH_ELEMENT_LOCK'] = 6402933, 
    ['ELEMENT_SWITCH_ELEMENT_SUCCESS'] = 6402932, 
    ['ENEMY_TEAM_CONTROL_POINT'] = 6405795, 
    ['ENTER_FREE_ZONE'] = 6403105, 
    ['ENTER_ORANGE'] = 6403106, 
    ['ENTER_ORANGERED'] = 6403107, 
    ['ENTER_PEACE_ZONE'] = 6403103, 
    ['ENTER_PEACE_ZONE_WITH_CHANGE_MODE'] = 6403104, 
    ['ENTER_PURPLE'] = 6403109, 
    ['ENTER_RED'] = 6403108, 
    ['EQUIP_FAILED_CLASS'] = 6401004, 
    ['EQUIP_FAILED_LEVEL'] = 6401003, 
    ['EQUIP_SELECT_NOEQUIP'] = 6401725, 
    ['EQUIP_SELECT_NOTSATISFY'] = 6401724, 
    ['EQUIP_TAB1_STAGEFAIL'] = 6401711, 
    ['EQUIP_TAB2_NOTHAVE'] = 6401707, 
    ['EQUIP_TAB2_NOWORD'] = 6401705, 
    ['EQUIP_TAB2_SAMEWORD'] = 6401709, 
    ['EQUIP_TAB2_SELECTFIRST'] = 6401706, 
    ['EQUIP_TAB3_ALREADYMAX'] = 6401710, 
    ['EQUIP_TAB3_BANMAX'] = 6401708, 
    ['EQUIP_TAB4_ITEMNOTIMES'] = 6401713, 
    ['EQUIP_TAB4_NOTSELECT'] = 6401712, 
    ['EQUIP_WORD_DOWNLIMIT'] = 6401726, 
    ['EQUIP_WORD_REVERTNONE'] = 6401727, 
    ['EVENT_REMINDER'] = 6440224, 
    ['EXCEEDED_SHARING_USE_TIMES'] = 6401037, 
    ['EXPLORE_AREA'] = 6441717, 
    ['EYECHEST'] = 6405910, 
    ['Eudora_Mech02_desc'] = 6406349, 
    ['Eudora_Mech02_predesc'] = 6406414, 
    ['Eudora_Mech02_title'] = 6406348, 
    ['Eudora_Mech03_desc1'] = 6406351, 
    ['Eudora_Mech03_desc2'] = 6406352, 
    ['Eudora_Mech03_title'] = 6406350, 
    ['Eudora_Skill01_desc'] = 6406411, 
    ['Eudora_Skill01_title'] = 6406408, 
    ['Eudora_Skill02_title'] = 6406409, 
    ['Eudora_Skill03_02_desc'] = 6406413, 
    ['Eudora_Skill03_02_title'] = 6406412, 
    ['Eudora_Skill03_title'] = 6406410, 
    ['FAIL_ITEMSUBMIT_REMINDER'] = 6400903, 
    ['FAIL_TASK_REMINDER'] = 6400902, 
    ['FAIL_USE_ITEM_TASK_AREA'] = 6400906, 
    ['FAIL_USE_ITEM_TASK_TARGET'] = 6400905, 
    ['FASHION_CONFLICT_REMOVE'] = 6407205, 
    ['FASHION_EDIT_ILLEGAL'] = 6407203, 
    ['FASHION_EDIT_SAVE_SUCCESS'] = 6407215, 
    ['FASHION_HAS_CANT_BUY_FASHION'] = 6407214, 
    ['FASHION_NOT_AVAILABLE'] = 6407201, 
    ['FASHION_NOT_EDITABLE'] = 6407202, 
    ['FASHION_SAVE_CONFLICT'] = 6407200, 
    ['FASHION_SAVE_SUCCESS'] = 6407204, 
    ['FASHION_SDK_NOT_OPEN'] = 6407216, 
    ['FASHION_STAIN_LOCKED_CANTFIND'] = 6407209, 
    ['FASHION_STAIN_NOCHANGE'] = 6407212, 
    ['FASHION_STAIN_NOCHOICE'] = 6407213, 
    ['FASHION_STAIN_NOITEM'] = 6407211, 
    ['FASHION_STAIN_SWITCH_NOHISTORY'] = 6407210, 
    ['FASHION_SYSTEM_LOCKED'] = 6405927, 
    ['FATE_GIFT_END'] = 6441101, 
    ['FATE_GIFT_START'] = 6441100, 
    ['FAZHEN_COMPLETE'] = 6440345, 
    ['FAZHEN_INPROCESS'] = 6440344, 
    ['FAZHEN_REPAIR'] = 6440343, 
    ['FEEDBACK_THANKS'] = 6440362, 
    ['FELLOW_CANCLE_ASSIST_COMBAT'] = 6401905, 
    ['FELLOW_CANCLE_JOIN_COMBAT'] = 6401904, 
    ['FELLOW_COMBAT_NOFELLOW'] = 6401919, 
    ['FELLOW_COMBAT_TYPE_ERROR'] = 6401906, 
    ['FELLOW_CONFIG_ERROR'] = 6401901, 
    ['FELLOW_EXP_ITEM_RETURN'] = 6401918, 
    ['FELLOW_FIRST_STAR_UP_FULL'] = 6401910, 
    ['FELLOW_FIRST_STAR_UP_NOT_FULL'] = 6401909, 
    ['FELLOW_FRIEND_ASSIST_DUPLICATE'] = 6404001, 
    ['FELLOW_GACHA_COUNT_UP_MAX'] = 6401917, 
    ['FELLOW_GACHA_SINGLE_FREE_SUCESS'] = 6401916, 
    ['FELLOW_HAVE_FULL_LEVEL'] = 6401911, 
    ['FELLOW_HAVE_IN_TARGET_POS'] = 6401908, 
    ['FELLOW_ITEM_NOT_ENOUGH'] = 6401903, 
    ['FELLOW_ITEM_NOT_PLACE'] = 6401912, 
    ['FELLOW_JOIN_COMBAT_SKILL_IN_CD'] = 6401920, 
    ['FELLOW_LACK_ITEM'] = 6401914, 
    ['FELLOW_NOT_GET'] = 6401907, 
    ['FELLOW_SEVER_ERROR'] = 6401902, 
    ['FELLOW_SKILL_IN_CD'] = 6401915, 
    ['FELLOW_UPPER_BOUND'] = 6401913, 
    ['FENGYINSHI'] = 6405922, 
    ['FENGYINSHI2'] = 6405923, 
    ['FENGYINSHI_1'] = 6405937, 
    ['FHYZ_01'] = 6442011, 
    ['FHYZ_02'] = 6442012, 
    ['FHYZ_03'] = 6442013, 
    ['FHYZ_04'] = 6442014, 
    ['FHYZ_05'] = 6442015, 
    ['FIREND_CLUB_NOT_BOTH_FRIEND'] = 6401548, 
    ['FIRE_BOX'] = 6441715, 
    ['FIRE_NPC_REMINDER'] = 6440219, 
    ['FIRE_NPC_REMINDER2'] = 6440221, 
    ['FIRE_SPIDER_LINE'] = 6405913, 
    ['FIRE_STONE_BIG'] = 6405912, 
    ['FIRE_STONE_SMALL'] = 6405911, 
    ['FIRE_VINE'] = 6441716, 
    ['FIRST_KILL'] = 6405704, 
    ['FLOWER_SHOP_OWNER'] = 6405925, 
    ['FM_EyesRaven_Skill06'] = 6406290, 
    ['FM_Knight_Skill06'] = 6406289, 
    ['FORBIDDEN_MAIL_ROLE'] = 6401107, 
    ['FOUNDER_CUT_DEMISE'] = 6402042, 
    ['FOUR_FOOT1'] = 6405904, 
    ['FOUR_FOOT2'] = 6405905, 
    ['FOUR_FOOT3'] = 6405906, 
    ['FOUR_FOOT4'] = 6405907, 
    ['FREIND_CLUB_DISSOLVE'] = 6401559, 
    ['FREIND_CLUB_EDIT_NOTICE'] = 6401565, 
    ['FRIEND_ADD_MYSELF'] = 6442049, 
    ['FRIEND_ADD_OPERATE_FORBIDDEN'] = 6401517, 
    ['FRIEND_ALREADY_APPLY'] = 6401574, 
    ['FRIEND_APPLY_FAILED'] = 6401501, 
    ['FRIEND_APPLY_MAX'] = 6401504, 
    ['FRIEND_APPROVE_FAILED'] = 6401502, 
    ['FRIEND_APPROVE_FAILED_BLACKLIST'] = 6401576, 
    ['FRIEND_ATTRACTION_ADD'] = 6401542, 
    ['FRIEND_BLACKLIST_APPLY_FAILED'] = 6401510, 
    ['FRIEND_BLACKLIST_APPLY_SUCCESS'] = 6401511, 
    ['FRIEND_BLACKLIST_MAX'] = 6401509, 
    ['FRIEND_CHANNEL_CHAT_FORBIDDEN'] = 6401516, 
    ['FRIEND_CHAT_EMPTY'] = 6401568, 
    ['FRIEND_CHAT_MYSELF'] = 6442050, 
    ['FRIEND_CLICK_TOO_OFTEN'] = 6401577, 
    ['FRIEND_CLUB_BAN_SUCCESS'] = 6401563, 
    ['FRIEND_CLUB_BUILD_LIMIT'] = 6401561, 
    ['FRIEND_CLUB_BUILD_LV_LIMIT'] = 6401545, 
    ['FRIEND_CLUB_BUILD_ONE_MEMBER'] = 6401554, 
    ['FRIEND_CLUB_BUILD_SUCCESS'] = 6401547, 
    ['FRIEND_CLUB_COUNT_LIMIT'] = 6401546, 
    ['FRIEND_CLUB_DISSOVE_SUCCESS'] = 6401562, 
    ['FRIEND_CLUB_EDIT_NAME'] = 6401564, 
    ['FRIEND_CLUB_EDIT_NAME_AND_NOTICE'] = 6401566, 
    ['FRIEND_CLUB_INVITE_MEMBER'] = 6401555, 
    ['FRIEND_CLUB_INVITE_NOTICE'] = 6401556, 
    ['FRIEND_CLUB_KICK_MEMBER'] = 6401557, 
    ['FRIEND_CLUB_LEAVE'] = 6401558, 
    ['FRIEND_CLUB_MEMBER_COUNT_LIMIT'] = 6401550, 
    ['FRIEND_CLUB_MEMBER_LV_LIMIT'] = 6401560, 
    ['FRIEND_CLUB_NOT_ONLINE'] = 6401549, 
    ['FRIEND_DELETE_FAILED'] = 6401507, 
    ['FRIEND_GIVE_FAIL_OFFLINE'] = 6401571, 
    ['FRIEND_GROUP_NAME'] = 6401508, 
    ['FRIEND_GROUP_NAME_SAME'] = 6401572, 
    ['FRIEND_IMPRINT_WHISPER_FIFTEEN'] = 6401543, 
    ['FRIEND_INPUT_MAX'] = 6401569, 
    ['FRIEND_INPUT_SEARCH_EMPTY_WARNING'] = 6401570, 
    ['FRIEND_IN_BAN_TIME'] = 6401575, 
    ['FRIEND_LEVEL_LIMIT'] = 6401567, 
    ['FRIEND_MAX_APPLY_FAILED'] = 6401503, 
    ['FRIEND_MAX_APPROVE_FAILED'] = 6401506, 
    ['FRIEND_MAX_APPROVE_FAILED_OTHER'] = 6401505, 
    ['FRIEND_MGR_OPERATE_FORBIDDEN'] = 6401518, 
    ['FRIEND_RECOMMEND_FORBIDDEN'] = 6401519, 
    ['FRIEND_RENAME_LENGTH_MAX'] = 6401512, 
    ['FRIEND_SYSTEM_ADD_DAY_ATTRACTION_NOTIFY'] = 6401531, 
    ['FRIEND_SYSTEM_ADD_FRIEND_SUCCESS'] = 6401528, 
    ['FRIEND_SYSTEM_ADD_LEVEL_LIMIT'] = 6401522, 
    ['FRIEND_SYSTEM_APPLY_ADD_FRIEND_NOTIFY'] = 6401514, 
    ['FRIEND_SYSTEM_BLACK_LIST_FORBIDDEN'] = 6401540, 
    ['FRIEND_SYSTEM_CANNOT_ADD_MORE_ATTRACTION'] = 6401533, 
    ['FRIEND_SYSTEM_DEL_FRIEND_SUCCESS'] = 6401529, 
    ['FRIEND_SYSTEM_FRIEND_EXISTED'] = 6401525, 
    ['FRIEND_SYSTEM_FRIEND_NUM_UPPER_LIMIT'] = 6401520, 
    ['FRIEND_SYSTEM_GROUP_NAME_MODIFY_SUCCEED'] = 6401538, 
    ['FRIEND_SYSTEM_GROUP_NAME_NULL'] = 6401536, 
    ['FRIEND_SYSTEM_MSG_ATTRACTION'] = 6401532, 
    ['FRIEND_SYSTEM_MSG_ROLE_DELETE'] = 6401534, 
    ['FRIEND_SYSTEM_NO_MORE_APPLICATION'] = 6401573, 
    ['FRIEND_SYSTEM_NO_MORE_BLACK'] = 6401530, 
    ['FRIEND_SYSTEM_NO_MORE_FRIEND'] = 6401541, 
    ['FRIEND_SYSTEM_NO_MORE_GROUP'] = 6401537, 
    ['FRIEND_SYSTEM_PERSONAL_SETTING_MODIFY_SUCCEED'] = 6401539, 
    ['FRIEND_SYSTEM_PLAYER_NOT_EXIST'] = 6401524, 
    ['FRIEND_SYSTEM_PLAYER_NOT_FIND'] = 6401513, 
    ['FRIEND_SYSTEM_PLAYER_REFUSE_INVITE'] = 6401544, 
    ['FRIEND_SYSTEM_PROCESS_APPLICATION_SUCCEED'] = 6401521, 
    ['FRIEND_SYSTEM_REFUSE_ADD_ROBOT'] = 6401523, 
    ['FRIEND_SYSTEM_REMARK_NO_MODIFY'] = 6401535, 
    ['FRIEND_SYSTEM_STARTING'] = 6401579, 
    ['FRIEND_TARGET_SERVER_RESTARTING'] = 6401578, 
    ['FRIEND_TO_BE_FRIEND'] = 6401526, 
    ['FRIEND_TO_BE_FRIEND_FROM_DUNGEON'] = 6401527, 
    ['FRIEND_WHISPER_FORBIDDEN'] = 6401515, 
    ['FRINED_CLUB_BUILD_MULTI_MEMBER'] = 6401553, 
    ['FRINED_CLUB_CROSS_LIMIT'] = 6401552, 
    ['FRINED_CLUB_MEMBER_JOIN_LIMIT'] = 6401551, 
    ['FULL_PICKUP_FAILED'] = 6401010, 
    ['FUNCTION_AREAR3V3_UNLOCK'] = 6401719, 
    ['FUNCTION_AUTONAVIGATION_UNLOCK'] = 6401729, 
    ['FUNCTION_DUNGEON_UNLOCK'] = 6401720, 
    ['FUNCTION_LOCK'] = 6401701, 
    ['FUNCTION_LOCK_LEVEL'] = 6401703, 
    ['FUNCTION_LOCK_TASK'] = 6401704, 
    ['FUNCTION_LOCK_TASK_UNAVAILABLE'] = 6401730, 
    ['FUNCTION_LOCK_TEAM_MEMBER'] = 6401718, 
    ['FUNCTION_NOT_OPEN'] = 6440821, 
    ['FUNCTION_PHARAMCIST_UNLOCK'] = 6405003, 
    ['FUNCTION_PLAYER_LOCK'] = 6401721, 
    ['FUNCTION_ROLEPLAY_UNLOCK'] = 6401728, 
    ['Fight_EnterFight'] = 6400706, 
    ['Fight_LeaveFight'] = 6400707, 
    ['GAMEPLAY_MECHANIC_300010'] = 6406004, 
    ['GAMEPLAY_MECHANIC_300011'] = 6404000, 
    ['GAMEPLAY_MECHANIC_300013'] = 6406007, 
    ['GAMEPLAY_MECHANIC_300014'] = 6406008, 
    ['GAMEPLAY_MECHANIC_300015'] = 6406009, 
    ['GAMEPLAY_MECHANIC_300016'] = 6406010, 
    ['GAMEPLAY_MECHANIC_300017'] = 6406011, 
    ['GAMEPLAY_MECHANIC_300018'] = 6406012, 
    ['GAMEPLAY_MECHANIC_300019'] = 6406013, 
    ['GAMEPLAY_MECHANIC_300020'] = 6406014, 
    ['GAMEPLAY_MECHANIC_300021'] = 6406015, 
    ['GAMEPLAY_MECHANIC_300022'] = 6406016, 
    ['GAMEPLAY_MECHANIC_300025'] = 6406019, 
    ['GAMEPLAY_MECHANIC_300026'] = 6406020, 
    ['GAMEPLAY_MECHANIC_300027'] = 6406021, 
    ['GAMEPLAY_MECHANIC_300028'] = 6406022, 
    ['GAMEPLAY_MECHANIC_300040'] = 6406034, 
    ['GAMEPLAY_MECHANIC_300041'] = 6406035, 
    ['GAMEPLAY_MECHANIC_300043'] = 6406037, 
    ['GAMEPLAY_MECHANIC_300044'] = 6406038, 
    ['GAMEPLAY_MECHANIC_300046'] = 6406040, 
    ['GAMEPLAY_MECHANIC_300047'] = 6406041, 
    ['GAMEPLAY_MECHANIC_300048'] = 6406042, 
    ['GAMEPLAY_MECHANIC_300049'] = 6406043, 
    ['GAMEPLAY_MECHANIC_300054'] = 6406048, 
    ['GAMEPLAY_MECHANIC_300055'] = 6406049, 
    ['GAMEPLAY_MECHANIC_300060'] = 6406054, 
    ['GAMEPLAY_MECHANIC_300061'] = 6406055, 
    ['GAMEPLAY_MECHANIC_300062'] = 6406056, 
    ['GAMEPLAY_MECHANIC_300063'] = 6406057, 
    ['GAMEPLAY_MECHANIC_300064'] = 6406058, 
    ['GAMEPLAY_MECHANIC_300065'] = 6406059, 
    ['GAMEPLAY_MECHANIC_300066'] = 6406060, 
    ['GAMEPLAY_MECHANIC_300067'] = 6406061, 
    ['GAMEPLAY_MECHANIC_300068'] = 6406062, 
    ['GAMEPLAY_MECHANIC_300069'] = 6406063, 
    ['GAMEPLAY_MECHANIC_300070'] = 6406064, 
    ['GAMEPLAY_MECHANIC_300071'] = 6406065, 
    ['GAMEPLAY_MECHANIC_300072'] = 6406066, 
    ['GAMEPLAY_MECHANIC_300073'] = 6406067, 
    ['GAMEPLAY_MECHANIC_300074'] = 6406068, 
    ['GAMEPLAY_MECHANIC_300075'] = 6406069, 
    ['GAMEPLAY_MECHANIC_300076'] = 6406070, 
    ['GAMEPLAY_MECHANIC_6406000'] = 6406000, 
    ['GAMEPLAY_MECHANIC_6406001'] = 6406001, 
    ['GAMEPLAY_MECHANIC_6406002'] = 6406002, 
    ['GAMEPLAY_MECHANIC_6406078'] = 6406078, 
    ['GAMEPLAY_MECHANIC_6406079'] = 6406079, 
    ['GAMEPLAY_MECHANIC_6406080'] = 6406080, 
    ['GAMEPLAY_MECHANIC_6406082'] = 6406082, 
    ['GAMEPLAY_MECHANIC_6406090'] = 6406090, 
    ['GAMEPLAY_MECHANIC_6406091'] = 6406091, 
    ['GAMEPLAY_MECHANIC_6406092'] = 6406092, 
    ['GAMEPLAY_MECHANIC_6406093'] = 6406093, 
    ['GAMEPLAY_MECHANIC_6406094'] = 6406094, 
    ['GAMEPLAY_MECHANIC_6406095'] = 6406095, 
    ['GAMEPLAY_MECHANIC_6406096'] = 6406096, 
    ['GAMEPLAY_MECHANIC_6406097'] = 6406097, 
    ['GAMEPLAY_MECHANIC_6406098'] = 6406098, 
    ['GIFT_RECIPIENTS_LEFT_CHATROOM'] = 6440929, 
    ['GIVE_LEVEL_REWARD_FAILED'] = 6440338, 
    ['GODDESSFORTH_FIRST'] = 6441911, 
    ['GODDESSLEAD_FIRST'] = 6441910, 
    ['GODDESS_ACHIEVEMENT_SHOW'] = 6441908, 
    ['GONGJIANGFANG_1'] = 6405914, 
    ['GONGJIANGFANG_2'] = 6405915, 
    ['GONGJIANGFANG_3'] = 6405916, 
    ['GONGJIANGFANG_4'] = 6405917, 
    ['GOODS_CONFIG_NOT_EXIST'] = 6402603, 
    ['GOT_TEAM_REWARD'] = 6406081, 
    ['GROUP_APPLY_FAIL_ALREADY_DISBAND'] = 6402715, 
    ['GROUP_APPLY_FAIL_ALREADY_FULL'] = 6402716, 
    ['GROUP_APPLY_FAIL_IN_DUNGEON'] = 6402714, 
    ['GROUP_APPLY_FAIL_IN_GROUP'] = 6402719, 
    ['GROUP_APPLY_FAIL_MAP_LIMIT'] = 6402713, 
    ['GROUP_APPLY_FAIL_NOT_MATCH'] = 6402712, 
    ['GROUP_APPLY_TARGET_HAS_GROUP'] = 6402725, 
    ['GROUP_BECOME_GROUP_LEADER'] = 6402727, 
    ['GROUP_BECOME_NEW_TEAMLEADER'] = 6402701, 
    ['GROUP_BE_KICKED_OFF_GROUP'] = 6402702, 
    ['GROUP_CHAT_CAN_NOT_ADD'] = 6401647, 
    ['GROUP_DUNGEON_ENTER_FAIL'] = 6402723, 
    ['GROUP_EXCHANGE_FAIL'] = 6402738, 
    ['GROUP_FOLLOW_FAIL_OFFLINE'] = 6402718, 
    ['GROUP_FOLLOW_FAIL_SCENE'] = 6402717, 
    ['GROUP_FOLLOW_LEADER'] = 6402720, 
    ['GROUP_FOLLOW_MEMBER'] = 6402721, 
    ['GROUP_HAVE_LEAGUE'] = 6402736, 
    ['GROUP_INVITE_FAIL_FULL'] = 6402708, 
    ['GROUP_INVITE_FAIL_IN_DUNGEON'] = 6402711, 
    ['GROUP_INVITE_FAIL_IN_GROUP'] = 6402710, 
    ['GROUP_INVITE_FAIL_OFFLINE'] = 6402709, 
    ['GROUP_LEADER_APPLY_CD'] = 6402739, 
    ['GROUP_LEADER_CAN_NOT_APPLY'] = 6402743, 
    ['GROUP_LEAGUE_FULL'] = 6402737, 
    ['GROUP_MEMBER_ENTER_GROUP'] = 6402728, 
    ['GROUP_MEMBER_EXCHANGE_FAIL'] = 6402705, 
    ['GROUP_MEMBER_EXCHANGE_SUCCESS'] = 6402704, 
    ['GROUP_NUM_EXCEED'] = 6402735, 
    ['GROUP_PLAYER_ENTER_TEAM'] = 6402733, 
    ['GROUP_REFRESH_TARGET_FAIL'] = 6402730, 
    ['GROUP_STOP_ALL_LEADER_FOLLOW'] = 6402731, 
    ['GROUP_SWITCH_FAIL_IN_DUNGEON'] = 6402724, 
    ['GROUP_TARGET_SET_FAIL'] = 6402726, 
    ['GROUP_TEAM_APPLY_FAIL_ALREADY_FULL'] = 6402722, 
    ['GROUP_TEAM_EXCHANGE_FAIL'] = 6402707, 
    ['GROUP_TEAM_EXCHANGE_SUCCESS'] = 6402706, 
    ['GROUP_TEAM_POSITON_CHANGE_FAIL'] = 6402734, 
    ['GROUP_TRANSFER_MEMBER_HAS_LEAVE'] = 6402729, 
    ['GROUP_VOICE_SETTING_LIMIT'] = 6402703, 
    ['GROW_DECAY_INTERACTOR_REMINDER'] = 6405926, 
    ['GROW_DECAY_MONKEY'] = 6441710, 
    ['GUILD_ACTIVITY_ENTER_LIMIT_STATION'] = 6402222, 
    ['GUILD_ACTIVITY_ENTER_TIME_LIMIT'] = 6402219, 
    ['GUILD_ACTIVITY_FUNDS_ADD'] = 6402235, 
    ['GUILD_ALREADY_CANCLED'] = 6402274, 
    ['GUILD_ALREADY_CREATED'] = 6402011, 
    ['GUILD_ALREADY_IN_SCENE'] = 6402192, 
    ['GUILD_ANNOUNCE_WORD_TOO_FEW'] = 6402063, 
    ['GUILD_APPLICATION_CANCEL'] = 6442040, 
    ['GUILD_APPLICATION_REPEAT'] = 6442041, 
    ['GUILD_APPLY_APPRENTICE_FULL'] = 6402018, 
    ['GUILD_APPLY_FAILED_OTHER'] = 6402101, 
    ['GUILD_APPLY_LAPSE'] = 6402024, 
    ['GUILD_APPLY_LEVEL_LIMIT'] = 6402014, 
    ['GUILD_APPLY_MEMBER_FULL'] = 6402017, 
    ['GUILD_APPLY_REASON_NULL'] = 6402015, 
    ['GUILD_APPLY_REPET'] = 6402013, 
    ['GUILD_APPLY_SUCCESS'] = 6402016, 
    ['GUILD_APPRENTICE_FULL'] = 6402022, 
    ['GUILD_AUDIO_COMMANDER_NUM_FULL'] = 6402244, 
    ['GUILD_BABY_ALREADY'] = 6402228, 
    ['GUILD_BABY_APPLIED'] = 6402208, 
    ['GUILD_BABY_APPLY_CD'] = 6402229, 
    ['GUILD_BABY_APPLY_MSG'] = 6402227, 
    ['GUILD_BABY_AUDIO_COMMANDER_NOTIFY'] = 6402199, 
    ['GUILD_BABY_ONLINE'] = 6402198, 
    ['GUILD_BABY_RED_PACKET_TIMES_LIMIT'] = 6402220, 
    ['GUILD_BACKGROUND_IMAGE_RESET'] = 6402292, 
    ['GUILD_BADGE_NOT_UNLOCK'] = 6402287, 
    ['GUILD_BECOME_NEW_LEADER'] = 6402105, 
    ['GUILD_BOSS_END'] = 6402127, 
    ['GUILD_BOSS_OPEN'] = 6402126, 
    ['GUILD_BOSS_OPEN_ALREADY'] = 6402125, 
    ['GUILD_BOSS_OPEN_SUCCESS'] = 6402124, 
    ['GUILD_BUILDINGS_UPGRADE_SUCCESS'] = 6402271, 
    ['GUILD_BUILDING_IN_UPGRADE'] = 6402133, 
    ['GUILD_BUILDING_LEVEL_NOT_ENOUGH'] = 6402156, 
    ['GUILD_BUILDING_MAX_LV'] = 6402137, 
    ['GUILD_BUILDING_UPGRADE_COST'] = 6402239, 
    ['GUILD_BUILD_CANNOT_UPGRADE'] = 6402241, 
    ['GUILD_CANCEL_APPRENTICE'] = 6402250, 
    ['GUILD_CANCEL_CREATE'] = 6402196, 
    ['GUILD_CANCEL_MEMBER'] = 6402089, 
    ['GUILD_CANCEL_RESPONSE'] = 6402061, 
    ['GUILD_CANCEL_ROLE'] = 6402075, 
    ['GUILD_CANCEL_ROLE_BY_SOMEONE'] = 6402097, 
    ['GUILD_CANCEL_ROLE_SYS_FRIEND'] = 6402072, 
    ['GUILD_CANCEL_SENIOR_OFFICIALS'] = 6402090, 
    ['GUILD_CANCEL_SUCCEED'] = 6402197, 
    ['GUILD_CAN_NOT_TELEPORT'] = 6402286, 
    ['GUILD_CHANGE_NAME'] = 6402077, 
    ['GUILD_CHANGE_NAME_MONEY_NOT_ENOUGH'] = 6402080, 
    ['GUILD_CHANGE_NAME_SUCCEED'] = 6402079, 
    ['GUILD_CHANGE_NAME_SYS_FRIEND'] = 6402078, 
    ['GUILD_CHANGE_RIGHTS_SUCCESS'] = 6402081, 
    ['GUILD_CHANGE_ROLE'] = 6402076, 
    ['GUILD_CHANGE_ROLE_BY_SOMEONE'] = 6402098, 
    ['GUILD_CHANGE_ROLE_SUCCEED'] = 6402108, 
    ['GUILD_CHANGE_ROLE_SYS_FRIEND'] = 6402073, 
    ['GUILD_CHOOSE_TARGET'] = 6402111, 
    ['GUILD_CLICK_INTERVAL_TIME'] = 6402086, 
    ['GUILD_CREATE_FAIL'] = 6402047, 
    ['GUILD_CREATE_FINISH'] = 6402012, 
    ['GUILD_CREATE_FINISH_ALL'] = 6402049, 
    ['GUILD_CREATE_LEVEL_LIMIT'] = 6402006, 
    ['GUILD_CREATE_MONEY_LIMIT'] = 6402007, 
    ['GUILD_CREATE_NO_SIGN'] = 6440813, 
    ['GUILD_CREATE_NUM_LIMIT'] = 6402008, 
    ['GUILD_CREATE_PREPARATION'] = 6402112, 
    ['GUILD_CREATE_SELECT_TYPE'] = 6440814, 
    ['GUILD_CREATE_SUCCESS'] = 6402009, 
    ['GUILD_DANCE_ACT_REACH_MAX'] = 6441901, 
    ['GUILD_DANCE_DRINK_REACH_MAX'] = 6441902, 
    ['GUILD_DANCE_HOT_WORLD_TIPS'] = 6441905, 
    ['GUILD_DANCE_RESPONSE_INVALID'] = 6441900, 
    ['GUILD_DAY_KICK_NUM_LIMIT'] = 6402189, 
    ['GUILD_DECLARATION_ILLEGAL'] = 6402005, 
    ['GUILD_DECLARATION_MODIFY'] = 6402083, 
    ['GUILD_DECLARATION_NULL'] = 6402004, 
    ['GUILD_DECLARATION_OPEN'] = 6440800, 
    ['GUILD_DEGRADE_NOTICE'] = 6402195, 
    ['GUILD_DEMISE_TO_APPRENTICE'] = 6402039, 
    ['GUILD_DEMISE_TO_BABY'] = 6402040, 
    ['GUILD_DINNER_ITEM_BUFF_MAX_LIMIT'] = 6402216, 
    ['GUILD_DINNER_ITEM_NOT_IN_SCENE'] = 6402215, 
    ['GUILD_DINNER_ITEM_TIMES_LIMIT'] = 6402214, 
    ['GUILD_DINNER_LOVE_REVIVE'] = 6402221, 
    ['GUILD_DINNER_QUESTION'] = 6402187, 
    ['GUILD_DUNGEON_END'] = 6402123, 
    ['GUILD_DUNGEON_FULL'] = 6402194, 
    ['GUILD_DUNGEON_OPEN'] = 6402122, 
    ['GUILD_DUNGEON_OPEN_ALREADY'] = 6402121, 
    ['GUILD_DUNGEON_OPEN_SUCCESS'] = 6402120, 
    ['GUILD_EXERCISE_UNOPEN_LEVEL_LIMIT'] = 6402243, 
    ['GUILD_FAILURE_TO_BE_INVITED_APPRENTICE'] = 6402110, 
    ['GUILD_FAILURE_TO_BE_INVITED_MEMBER'] = 6402109, 
    ['GUILD_FOUNDER_CHANGE_CANDIDATE'] = 6402059, 
    ['GUILD_FOUNDER_CHANGE_CANDIDATE_NOTICE'] = 6402107, 
    ['GUILD_FOUNDER_CUT_DEMISE'] = 6402106, 
    ['GUILD_FOUNDER_CUT_IMPEACH'] = 6402038, 
    ['GUILD_FOUNDER_FINISH_IMPEACH'] = 6402057, 
    ['GUILD_FOUNDER_START_CANDIDATE'] = 6402058, 
    ['GUILD_FOUNDER_START_IMPEACH'] = 6402037, 
    ['GUILD_FUNDS_ADD'] = 6402210, 
    ['GUILD_FUNDS_ADD_PLAYER'] = 6402209, 
    ['GUILD_FUNDS_ADD_PLAYER_LIMIT'] = 6402253, 
    ['GUILD_FUNDS_COND_NOT_MEET'] = 6402157, 
    ['GUILD_FUNDS_GET_TOP_LIMIT_CHANNEL_NOTICE'] = 6402207, 
    ['GUILD_FUNDS_GET_TOP_LIMIT_NOTICE'] = 6402206, 
    ['GUILD_FUNDS_LIMIT'] = 6402136, 
    ['GUILD_FUNDS_LITTLE'] = 6402237, 
    ['GUILD_FUNDS_LITTLE_FRIEND'] = 6402238, 
    ['GUILD_FUNDS_NOT_ENOUGH'] = 6402158, 
    ['GUILD_FUNDS_REDUCE'] = 6402211, 
    ['GUILD_GROUP_CHANGE_NAME'] = 6440812, 
    ['GUILD_GROUP_FULL'] = 6442056, 
    ['GUILD_GROUP_INVITE_1'] = 6440806, 
    ['GUILD_GROUP_INVITE_2'] = 6440807, 
    ['GUILD_GROUP_INVITE_OBJECT_OFFLINE'] = 6442037, 
    ['GUILD_GROUP_INVITE_SEND_CD'] = 6442035, 
    ['GUILD_GROUP_INVITE_SEND_FAIL'] = 6442036, 
    ['GUILD_GROUP_INVITE_SEND_SUCC'] = 6442034, 
    ['GUILD_GROUP_INVITE_TIPS'] = 6442033, 
    ['GUILD_GROUP_MEMBER_REMOVE_1'] = 6440809, 
    ['GUILD_GROUP_MEMBER_REMOVE_2'] = 6440810, 
    ['GUILD_GROUP_MEMBER_REMOVE_3'] = 6440811, 
    ['GUILD_GROUP_MESSAGE_NULL'] = 6402084, 
    ['GUILD_GROUP_MESSAGE_SUCCEED'] = 6402085, 
    ['GUILD_GROUP_NAME_SUCC'] = 6442038, 
    ['GUILD_GUILD_BADGE_FRAME_CHANGE_NOTIFY'] = 6402233, 
    ['GUILD_HAVE_NOT_YET'] = 6402129, 
    ['GUILD_HAVE_NOT_YET_CANT_APPLY'] = 6402132, 
    ['GUILD_HAVE_NOT_YET_CANT_INVITE'] = 6402131, 
    ['GUILD_INVITE_FAILED_NOTICE'] = 6402247, 
    ['GUILD_INVITE_LAPSE'] = 6402025, 
    ['GUILD_INVITE_LEVEL_LIMIT'] = 6402190, 
    ['GUILD_INVITE_LIMIT'] = 6440808, 
    ['GUILD_INVITE_LIST_REFRESH'] = 6402296, 
    ['GUILD_INVITE_MODULE_LOCK'] = 6402290, 
    ['GUILD_INVITE_SUCCESS'] = 6402295, 
    ['GUILD_INVITE_TARGET_OFFLINE'] = 6402191, 
    ['GUILD_INVITING_MESSAGE_SEND_SUCCEED'] = 6402087, 
    ['GUILD_INVITING_SOMEONE_JOIN_SUCCEED'] = 6402102, 
    ['GUILD_IN_ACTIVITY'] = 6402142, 
    ['GUILD_IN_CLUB_UNABLE_TO_RESPOND'] = 6440818, 
    ['GUILD_IN_LEAGUE_BATTLE_CANNOT_CHANGE_ROLE'] = 6441544, 
    ['GUILD_IN_MERGE_CD'] = 6402143, 
    ['GUILD_IN_MERGE_CONFIRM'] = 6402146, 
    ['GUILD_IN_RESPONSE'] = 6402064, 
    ['GUILD_IN_RESPONSE_1'] = 6440801, 
    ['GUILD_IN_RESPONSE_2'] = 6440802, 
    ['GUILD_IN_RESPONSE_3'] = 6440803, 
    ['GUILD_IN_UNNORMAL_STATUS'] = 6402159, 
    ['GUILD_KICK_MEMBER_LIMIT'] = 6402252, 
    ['GUILD_KICK_SOMEONE_SUCCEED'] = 6402103, 
    ['GUILD_LEAGUE_BAN_QUICK_USE'] = 6442091, 
    ['GUILD_LEAGUE_BEFORE_OCCUPY_DETECT_AREA_ACTIVE'] = 6405787, 
    ['GUILD_LEAGUE_BID_NO_QUALIFICATION_REMINDER'] = 6405779, 
    ['GUILD_LEAGUE_COMMAND_APPOINT'] = 6441518, 
    ['GUILD_LEAGUE_COMMAND_APPOINTED'] = 6441517, 
    ['GUILD_LEAGUE_COMMAND_ATTACK'] = 6405746, 
    ['GUILD_LEAGUE_COMMAND_BUFF'] = 6405744, 
    ['GUILD_LEAGUE_COMMAND_GATHER'] = 6405748, 
    ['GUILD_LEAGUE_COMMAND_INTERACTOR'] = 6405754, 
    ['GUILD_LEAGUE_COMMAND_IN_CD'] = 6441527, 
    ['GUILD_LEAGUE_COMMAND_PERMISSIONS'] = 6441519, 
    ['GUILD_LEAGUE_COMMAND_RETREAT'] = 6405747, 
    ['GUILD_LEAGUE_COMMAND_SKILL1'] = 6405745, 
    ['GUILD_LEAGUE_COMMAND_TOP'] = 6441520, 
    ['GUILD_LEAGUE_COMMAND_USE_MAX'] = 6441525, 
    ['GUILD_LEAGUE_COMMAND_WARNING'] = 6405749, 
    ['GUILD_LEAGUE_IN_BATTLE_AREA_CHANGE_EQUIP_FORBID'] = 6405786, 
    ['GUILD_LEAGUE_JOIN_ACTIVITY_LEVEL_LIMIT'] = 6405788, 
    ['GUILD_LEAGUE_NOT_ALLOW_GUILD_ABOLISH'] = 6405778, 
    ['GUILD_LEAGUE_NOT_ALLOW_INFORMAL_MEMBER_ENTER'] = 6405775, 
    ['GUILD_LEAGUE_NOT_ALLOW_TIME_LIMIT_MEMBER_ENTER'] = 6405777, 
    ['GUILD_LEAGUE_NOT_ENTER_SCENE'] = 6405768, 
    ['GUILD_LEAGUE_NOT_START'] = 6441524, 
    ['GUILD_LEAGUE_NO_AUTHORITY'] = 6441540, 
    ['GUILD_LEAGUE_OCCUPY_AREA_START_COUNTDOWN'] = 6405759, 
    ['GUILD_LEAGUE_PREPARE_COUNT_DOWN'] = 6405763, 
    ['GUILD_LEAGUE_PREPARE_NOT_ALLOW_ENTER'] = 6405765, 
    ['GUILD_LEAGUE_PREPARE_STAGE_WARNING'] = 6441543, 
    ['GUILD_LEAGUE_QUICK_INSTRUCTION_TOWER'] = 6441542, 
    ['GUILD_LEAGUE_REMINDER_FAIL_ENTER_DUNGEON'] = 6405757, 
    ['GUILD_LEAGUE_RESOURCE_MAX_NOTICE'] = 6405743, 
    ['GUILD_LEAGUE_RESOURCE_NOT_ENOUGH'] = 6441526, 
    ['GUILD_LEAGUE_REVIVE_COUNT_DOWN'] = 6405742, 
    ['GUILD_LEAGUE_SCHOOL_LIMIT_NUM_REMINDER'] = 6405776, 
    ['GUILD_LEAGUE_START_COUNT_DOWN'] = 6405741, 
    ['GUILD_LEAGUE_STRATEGY_IN_CD'] = 6441541, 
    ['GUILD_LEAGUE_SUCCESS_OCCUPY_AREA_ALLY'] = 6405756, 
    ['GUILD_LEAGUE_SUCCESS_OCCUPY_AREA_ENEMY'] = 6405755, 
    ['GUILD_LEAGUE_TEXT'] = 6441521, 
    ['GUILD_LEAGUE_TIME_OVER_NOT_ALLOW_ENTER'] = 6405764, 
    ['GUILD_LEVEL_LIMIT_WING_FLY'] = 6402212, 
    ['GUILD_LEVEL_NOT_ENOUGH'] = 6402130, 
    ['GUILD_LEVEL_UP_CONDITION'] = 6402183, 
    ['GUILD_LOCKED'] = 6402294, 
    ['GUILD_MAIL_SEND_SUCCESS'] = 6402288, 
    ['GUILD_MAIN_BUILDING_LV_LIMIT'] = 6402134, 
    ['GUILD_MANAGE_CAN_NOT_ADJUST_MEMBER'] = 6442084, 
    ['GUILD_MASS_MESSAGE_VALID'] = 6402186, 
    ['GUILD_MAX_BABY_RED_PROCKET_LIMIT'] = 6402200, 
    ['GUILD_MAX_NUM_LIMIT'] = 6402113, 
    ['GUILD_MEMBER_FULL'] = 6402021, 
    ['GUILD_MEMBER_LEAVE'] = 6402264, 
    ['GUILD_MERGE_APPLY_CANCEL'] = 6402151, 
    ['GUILD_MERGE_APPLY_OUTTIME'] = 6402150, 
    ['GUILD_MERGE_APPLY_REFUSE'] = 6402152, 
    ['GUILD_MERGE_APPLY_SEND'] = 6402171, 
    ['GUILD_MERGE_INPUT_OTHER_ID'] = 6402176, 
    ['GUILD_MERGE_NAME_ERROR'] = 6402276, 
    ['GUILD_MERGE_NUM_LIMIT'] = 6402153, 
    ['GUILD_MERGE_SUCCESS'] = 6402154, 
    ['GUILD_MERGE_SUCCESS_EVENT'] = 6402155, 
    ['GUILD_MERGE_TGT_REFUSE_APPLY'] = 6402177, 
    ['GUILD_NAME_ILLEGAL'] = 6402003, 
    ['GUILD_NAME_INPUT_MAX'] = 6402254, 
    ['GUILD_NAME_MODIFY_FAILED'] = 6402184, 
    ['GUILD_NAME_NOT_CHINESE'] = 6402259, 
    ['GUILD_NAME_NULL'] = 6402001, 
    ['GUILD_NAME_REPET'] = 6402002, 
    ['GUILD_NAME_WORD_TOO_FEW'] = 6402062, 
    ['GUILD_NEW_MEMBER_WELCOME'] = 6402289, 
    ['GUILD_NOT_FIND'] = 6402104, 
    ['GUILD_NO_GUILD'] = 6402213, 
    ['GUILD_NO_PERMISSION_TIPS'] = 6442032, 
    ['GUILD_NO_RIGHT_APPOINT_ELITE'] = 6402231, 
    ['GUILD_NO_VISTOR_1'] = 6440804, 
    ['GUILD_NO_VISTOR_2'] = 6440805, 
    ['GUILD_NUM_LIMIT'] = 6402000, 
    ['GUILD_OPEN_ACTIVITY_COUNT_LIMIT'] = 6402128, 
    ['GUILD_OPERATE_SUCCEED'] = 6402095, 
    ['GUILD_OPERATION_LEVEL_LIMIT'] = 6402236, 
    ['GUILD_PARTY_ANSWER_CORRECT_REACT'] = 6402280, 
    ['GUILD_PARTY_ANSWER_REWARD'] = 6402285, 
    ['GUILD_PARTY_ANSWER_WRONG_REACT'] = 6402281, 
    ['GUILD_PARTY_BUFF'] = 6402278, 
    ['GUILD_PARTY_RARE_BUFF_SHOW'] = 6402279, 
    ['GUILD_PARTY_REWARD_FUNDS'] = 6402283, 
    ['GUILD_PARTY_REWARD_MONEY'] = 6402282, 
    ['GUILD_PARTY_UNOPENED'] = 6402284, 
    ['GUILD_PERMISSION_NUMBER_MAX'] = 6442039, 
    ['GUILD_PLAYCONFIG_NOT_OPENED'] = 6402269, 
    ['GUILD_POSITION_FULL'] = 6402291, 
    ['GUILD_POSITION_REPEAT'] = 6442031, 
    ['GUILD_POSITION_SELECT_FAIL'] = 6402293, 
    ['GUILD_PRESIDENT_QUIT_TRANSFER'] = 6442055, 
    ['GUILD_REPORT_FAILED_EMPTY'] = 6402277, 
    ['GUILD_REPORT_SUCCESS'] = 6402272, 
    ['GUILD_RESET_RIGHTS_SUCCESS'] = 6402082, 
    ['GUILD_RESIGNATION'] = 6402099, 
    ['GUILD_RESIGN_NEED_VICE_PRESIDENT'] = 6402035, 
    ['GUILD_RESPONSE_OTHER_ALREADY'] = 6402046, 
    ['GUILD_RESPONSE_REPET'] = 6402045, 
    ['GUILD_RESPOSE_FAIL_ABSENT'] = 6402255, 
    ['GUILD_RESPOSE_SUCCESS'] = 6402048, 
    ['GUILD_REWARD_EXPIRED'] = 6402273, 
    ['GUILD_REWARD_LOCK'] = 6402261, 
    ['GUILD_ROLE_ASSIGN_TO_OTHER'] = 6402060, 
    ['GUILD_ROLE_FULL'] = 6402036, 
    ['GUILD_SEARCH_CONTENT_EMPTY'] = 6402185, 
    ['GUILD_SELECT_TARGET'] = 6402088, 
    ['GUILD_SELECT_TARGET_FIRST'] = 6402100, 
    ['GUILD_SEND_FAIL_EMPTY'] = 6402262, 
    ['GUILD_SEND_FAIL_MAX'] = 6402263, 
    ['GUILD_SET_APPRENTICE_HAS_TITLE'] = 6402249, 
    ['GUILD_SET_APPRENTICE_ROLE_LIMIT'] = 6402041, 
    ['GUILD_SET_APPRENTICE_TO_APPRENTICE'] = 6402251, 
    ['GUILD_SET_APPRENTICE_TO_BABY'] = 6402092, 
    ['GUILD_SET_COMMANDER_MUTE_NOTIFY'] = 6402245, 
    ['GUILD_SET_FOUNDER_TO_BABY'] = 6402093, 
    ['GUILD_SET_GUILD_BADGE_FRAME'] = 6402230, 
    ['GUILD_SET_GUILD_BADGE_FRAME_NO_CHANGE'] = 6402232, 
    ['GUILD_SET_MEMBER_OR_APPRENTICE'] = 6402091, 
    ['GUILD_SET_PRESIDENT_TO_BABY'] = 6402094, 
    ['GUILD_SET_ROLE'] = 6402074, 
    ['GUILD_SET_ROLE_BABY_CD'] = 6402201, 
    ['GUILD_SET_ROLE_BY_SOMEONE'] = 6402096, 
    ['GUILD_SET_ROLE_MEMBER'] = 6402115, 
    ['GUILD_SET_ROLE_MEMBER_SYS'] = 6402116, 
    ['GUILD_SET_ROLE_MEMBER_SYS_FRIEND'] = 6402117, 
    ['GUILD_SET_ROLE_MEMBER_SYS_SYS_FRIEND'] = 6402118, 
    ['GUILD_SET_ROLE_REPEAT'] = 6402203, 
    ['GUILD_SET_ROLE_SYS_FRIEND'] = 6402071, 
    ['GUILD_SHOP_CURRENCY_NOT_ENOUGH'] = 6402275, 
    ['GUILD_SIGNATURE_SHOULD_EDIT'] = 6402204, 
    ['GUILD_SIGNATURE_VALID'] = 6402205, 
    ['GUILD_SIGNATURE_WORLD_TOO_FEW'] = 6402202, 
    ['GUILD_SIGNIN_ALREADY_SIGNED'] = 6402268, 
    ['GUILD_SIGNIN_EMPTY'] = 6402267, 
    ['GUILD_SIGNIN_NOT_CHINESE'] = 6402265, 
    ['GUILD_SIGNIN_SUCCESS'] = 6402266, 
    ['GUILD_SKILL_CONTRI_LIMIT'] = 6402164, 
    ['GUILD_SKILL_ENTER_TIME_LIMIT'] = 6402160, 
    ['GUILD_SKILL_FRIEND_NUM_LIMIT'] = 6402162, 
    ['GUILD_SKILL_HISTORY_CONTRI_LIMIT'] = 6402163, 
    ['GUILD_SKILL_LEARN_COST_CONTRI'] = 6402182, 
    ['GUILD_SKILL_LV_LIMIT'] = 6402161, 
    ['GUILD_SKILL_MAX_LV_LIMIT'] = 6402167, 
    ['GUILD_SKILL_MONEY_LIMIT'] = 6402165, 
    ['GUILD_SKILL_NO_SET'] = 6402181, 
    ['GUILD_SKILL_NO_SKILL'] = 6402193, 
    ['GUILD_SKILL_RESET_IN_CD'] = 6402169, 
    ['GUILD_SKILL_RESET_MONEY_LIMIT'] = 6402170, 
    ['GUILD_SKILL_RESET_SUCCESS'] = 6402168, 
    ['GUILD_SKILL_SCHOOL_LEVEL_LIMIT'] = 6402260, 
    ['GUILD_SKILL_SET_FULL'] = 6402179, 
    ['GUILD_SKILL_SET_LIMIT'] = 6402166, 
    ['GUILD_SKILL_SET_SUCCEED'] = 6402180, 
    ['GUILD_STATION_INSIDE_ALREADY'] = 6402270, 
    ['GUILD_STATION_MODIFY_STATUE_MONEY_FEW'] = 6402224, 
    ['GUILD_STATION_MODIFY_STATUE_SUCCESS'] = 6402225, 
    ['GUILD_STATION_MODIFY_STATUE_SUCCESS_NOTITY'] = 6402226, 
    ['GUILD_STATION_STATUE_MODIFY_TIME_LIMIT'] = 6402223, 
    ['GUILD_SUB_BUILDINGS_DEGRADE_NOTICE'] = 6402248, 
    ['GUILD_SUB_BUILDING_NUM_LIMIT'] = 6402135, 
    ['GUILD_TGT_IN_ACTIVITY'] = 6402149, 
    ['GUILD_TGT_IN_MERGE_CD'] = 6402144, 
    ['GUILD_TGT_IN_MERGE_CONFIRM'] = 6402147, 
    ['GUILD_TGT_IN_UNNORMAL_STATUS'] = 6402145, 
    ['GUILD_TGT_PRESIDENT_OFFLINE'] = 6402148, 
    ['GUILD_UNLOCK_COMMANDER_MUTE_NOTIFY'] = 6402246, 
    ['GUILD_UPGRADE_MAIN'] = 6402138, 
    ['GUILD_UPGRADE_PUB'] = 6402139, 
    ['GUILD_UPGRADE_SCHOOL'] = 6402141, 
    ['GUILD_UPGRADE_VAULT'] = 6402140, 
    ['GUILD_VISIT_CLOSED'] = 6440817, 
    ['GUILD_VISIT_LOCKED'] = 6440815, 
    ['GUILD_VISIT_OPENED'] = 6440816, 
    ['GUILD_VOYAGE_GET_CONTRI_LIMIT_NOTICE'] = 6402257, 
    ['GUILD_VOYAGE_GET_LEVEL_LIMIT_NOTICE'] = 6402258, 
    ['GUILD_VOYAGE_GET_TIME_LIMIT_NOTICE'] = 6402256, 
    ['GUILD_WAGE_COUNT_ZERO'] = 6402174, 
    ['GUILD_WAGE_NOT_MEMBER'] = 6402172, 
    ['GUILD_WAGE_RECEIVE'] = 6402173, 
    ['GUILD_WAGE_RECEIVE_SUCCESS'] = 6402175, 
    ['GUILD_WAGE_REFRESH'] = 6402178, 
    ['GUILD_WELCOME_WORD_TOO_FEW'] = 6402234, 
    ['GUILD_WHISPER_TO_ALL_MEMBER'] = 6402188, 
    ['HELP_TRAVELER'] = 6405924, 
    ['HOMEMSG_CREATE_FAILED'] = 6440603, 
    ['HOMEMSG_FURNITURE_ALREADY_UNLOCK'] = 6440608, 
    ['HOMEMSG_FURNITURE_LOCKED'] = 6440612, 
    ['HOMEMSG_FURNITURE_NOT_ENOUGH'] = 6440607, 
    ['HOMEMSG_FURNITURE_NUM_REACH_MAX'] = 6440613, 
    ['HOMEMSG_FURNITURE_UNLOCK_CONDITION_NOT_MATCH'] = 6440610, 
    ['HOMEMSG_HOME_MONEY_NOT_ENOUGH'] = 6440611, 
    ['HOMEMSG_MODULE_LOCKED'] = 6440609, 
    ['HOMEMSG_NOT_HOMELAND_OWNER'] = 6440606, 
    ['HOMEMSG_NOT_IN_HOMELAND'] = 6440604, 
    ['HOMEMSG_NO_HOME'] = 6440602, 
    ['HOMEMSG_OPERATION_FAILED'] = 6440605, 
    ['HOME_ALREADY_IN_HOME'] = 6442057, 
    ['HOME_COIN_EXCEEDS_LIMIT_SELL_FALIED'] = 6442082, 
    ['HOME_FORNITURE_BUY_SUCCESSFUL'] = 6442005, 
    ['HOME_FORNITURE_LOCKED'] = 6440601, 
    ['HOME_FORNITURE_SELL_SUCCESSFUL'] = 6442006, 
    ['HOME_FORNITURE_UNLOCK_SUCCESSFUL'] = 6442007, 
    ['HOME_RENAME_EMPTY'] = 6442067, 
    ['HOME_RENAME_SAME'] = 6442068, 
    ['HOME_RENAME_SUCCESS'] = 6442069, 
    ['HOME_SUB_FORNITURE_LOCK'] = 6442008, 
    ['HUD_FUNCTION_BUTTON_LIMITED'] = 6409027, 
    ['HUD_FUNCTION_BUTTON_REMINDER'] = 6409035, 
    ['HUIYISHI_1'] = 6405918, 
    ['HUIYISHI_2'] = 6405919, 
    ['HUIYISHI_3'] = 6405932, 
    ['HUIYISHI_4'] = 6405933, 
    ['ICE_MONKEY_FIND'] = 6441711, 
    ['ICE_MONKEY_SKILL'] = 6441712, 
    ['INCORRECT_ITEM_SUBMIT'] = 6409023, 
    ['INDIVIDUAL_PVP_DRAW'] = 6405735, 
    ['INDIVIDUAL_PVP_OVER'] = 6405737, 
    ['INDIVIDUAL_PVP_RESULT'] = 6405734, 
    ['INFO_NOTICE'] = 6405717, 
    ['INTERACTOR_EXCLUSIVE'] = 6401307, 
    ['INTERACTOR_GAME_RESULT'] = 6401390, 
    ['INTERACTOR_TIMES_LIMIT'] = 6401308, 
    ['INVAILD_INVITE_INFO'] = 6400395, 
    ['INVITE_FAIL_OF_TEAM_FULL'] = 6400398, 
    ['INVITE_RIDE_MOUNT_IS_FAR'] = 6442075, 
    ['INVITE_RIDE_MOUNT_IS_FULL'] = 6442074, 
    ['INVITE_RIDE_MOUNT_IS_REFUSE'] = 6442078, 
    ['INVITE_RIDE_MOUNT_IS_UNACCEPT'] = 6442076, 
    ['INVITE_RIDE_MOUNT_SEND'] = 6442072, 
    ['INVITE_RIDE_MOUNT_SEND_CD'] = 6442073, 
    ['INVITE_RIDE_MOUNT_TARGET_EMPTY'] = 6442077, 
    ['INV_BOUND_FAILED'] = 6401025, 
    ['INV_FULL'] = 6401033, 
    ['INV_FULL_GET_TEMPITEM'] = 6401056, 
    ['INV_ITEM_CANNOT_ABANDON'] = 6401024, 
    ['INV_ITEM_EXCEED_HOLD_MAX'] = 6401020, 
    ['INV_ITEM_EXPIRED'] = 6401042, 
    ['INV_ITEM_NOT_ENOUGH'] = 6401032, 
    ['INV_LEVEL_LIMIT'] = 6401054, 
    ['INV_MONEY_NOT_ENOUGH'] = 6401043, 
    ['INV_PRO_LIMIT'] = 6401055, 
    ['INV_REQUEST_IN_CD'] = 6401053, 
    ['INV_SLOT_FEW'] = 6401021, 
    ['INV_SLOT_NUMBER_REACH_UPPER_LIMIT'] = 6401029, 
    ['IS_USING_ITEM'] = 6401057, 
    ['ITEM_CANNOT_USE_IN_DEAD'] = 6401031, 
    ['ITEM_DECOMPOSE_FAILURE_INV_SLOT_FEW'] = 6401041, 
    ['ITEM_DECOMPOSE_NO_CHOOSE'] = 6401058, 
    ['ITEM_DECOMPOSE_TIMES_LIMIT_FOREVER'] = 6401039, 
    ['ITEM_DECOMPOSE_TIMES_LIMIT_NOW'] = 6401040, 
    ['ITEM_GET_FELLOW'] = 6401065, 
    ['ITEM_IN_CD'] = 6401035, 
    ['ITEM_IN_SCENE_LIMIT_USE'] = 6401036, 
    ['ITEM_LACK'] = 6409033, 
    ['ITEM_MEDICINE_CAN_NOT_CHOOSE'] = 6401059, 
    ['ITEM_NOT_ENOUGH'] = 6401019, 
    ['ITEM_NOT_IN_USE_TIME'] = 6401034, 
    ['ITEM_REMINDER'] = 6409017, 
    ['ITEM_SPIRIT_ADD_FAILED'] = 6401060, 
    ['ITEM_SYTHESIS_FAILURE_INV_FULL'] = 6401027, 
    ['ITEM_SYTHESIS_FAILURE_MATERIAL_LACK'] = 6401026, 
    ['ITEM_SYTHESIS_SUCCESS'] = 6401028, 
    ['ITEM_TYPE_INCORRECT'] = 6401038, 
    ['JOIN_GUILD_SUCCESS'] = 6402019, 
    ['JOIN_GUILD_SUCCESS_SYS_FRIEND'] = 6402050, 
    ['KICK_GUILD_MANAGER'] = 6402033, 
    ['KILL'] = 6405703, 
    ['KILL_MONSTER'] = 6440342, 
    ['LEAGUE_APPLY_SUCCESS'] = 6402740, 
    ['LEAGUE_CAN_NOT_CHANGE_LEADER'] = 6402742, 
    ['LEAGUE_CREATE_SUCCESS'] = 6402741, 
    ['LEGENDARY'] = 6405710, 
    ['LESS_BLOOD_ITEM'] = 6401030, 
    ['LIGHT_BOX'] = 6441713, 
    ['LIKE_REMINDER'] = 6409018, 
    ['LINE_AREA_NUMBER_FULL'] = 6440320, 
    ['LINE_FIGHTLINE_ENTER'] = 6440319, 
    ['LINE_FREQUENT_OPEQUENT'] = 6440334, 
    ['LINE_NO_ENTER'] = 6440336, 
    ['LINE_NO_EXIST'] = 6440335, 
    ['LINE_NUMBER_FULL'] = 6440317, 
    ['LINE_SAMELINE'] = 6440318, 
    ['LINE_SWITCH_FAIL'] = 6440316, 
    ['LOCATION_ALREADY_EXISTS'] = 6401650, 
    ['LOCK_OVERRANGE'] = 6409003, 
    ['LOGIC_SERVER_ID'] = 6409024, 
    ['LOGIN_ACCOUNT_DB_ERROR'] = 6400106, 
    ['LOGIN_BAN'] = 6400117, 
    ['LOGIN_FAILED'] = 6400103, 
    ['LOGIN_IN_BANTIME'] = 6400120, 
    ['LOGIN_LOAD_ENTITY_FROM_DB_ERROR'] = 6400104, 
    ['LOGIN_QUEUEING_SERVER'] = 6400125, 
    ['LOGIN_QUEUE_LIMITED'] = 6400124, 
    ['LOGIN_QUEUE_TOO_LONG'] = 6400126, 
    ['LOGIN_REGIST_LIMITED'] = 6400123, 
    ['LOGIN_SERVER_NOT_READY'] = 6400105, 
    ['LOST_FOLLOW_REMINDER'] = 6403501, 
    ['MAIL_BAG_FULL'] = 6401104, 
    ['MAIL_BAN'] = 6401113, 
    ['MAIL_COLLECT'] = 6401114, 
    ['MAIL_COLLECT_CANCEL'] = 6401115, 
    ['MAIL_COLLECT_EXPIRE'] = 6401118, 
    ['MAIL_COLLECT_FULL'] = 6401117, 
    ['MAIL_COLLECT_ITEM'] = 6401116, 
    ['MAIL_DELETE_ONE_CLICK'] = 6401119, 
    ['MAIL_NOITEM'] = 6401108, 
    ['MAIL_NOMAIL'] = 6401109, 
    ['MAIL_RECEIVE_ALL_NO_MATCH'] = 6401105, 
    ['MAIL_REMOVE_ALL_NO_MATCH'] = 6401106, 
    ['MALL_BAN'] = 6402503, 
    ['MALL_BUY_GOODS_LOCK_LIMIT'] = 6402501, 
    ['MALL_BUY_GOODS_SOLD_OUT'] = 6402502, 
    ['MALL_EXCHANGE_CHECK_INVALID'] = 6402302, 
    ['MALL_EXCHANGE_INVALID_TYPE'] = 6402301, 
    ['MALL_EXCHANGE_NUM_INVALID'] = 6402300, 
    ['MAP_INVALID_COORDINATE'] = 6440702, 
    ['MAP_LABELUPPERLIMIT'] = 6441516, 
    ['MAP_TAB_WORD_MAX'] = 6441909, 
    ['MAP_TRACE_ALREADY_AT_GOAL'] = 6401309, 
    ['MAP_UNREACHABLE_COORDINATE'] = 6440703, 
    ['MEDICINE_SAVED'] = 6440130, 
    ['MED_POINT_NOT_ENOUGH'] = 6405606, 
    ['MED_PROP_IN_DANGER'] = 6405605, 
    ['MED_PROP_OUT_RANGE'] = 6405604, 
    ['MEISHI1'] = 6405931, 
    ['MOENY_SALE_BUY_MONEY_FAIL_BY_SYSTEM'] = 6402319, 
    ['MOENY_SALE_CANCEL_MONEY_FAIL_BY_CD_LIMIT'] = 6402333, 
    ['MOENY_SALE_CANCEL_MONEY_FAIL_BY_SYSTEM'] = 6402326, 
    ['MOENY_SALE_SELL_MONEY_FAIL_BY_NUM_LIMIT'] = 6402303, 
    ['MOENY_SALE_SELL_MONEY_FAIL_BY_SYSTEM'] = 6402312, 
    ['MOMENTS_COMMENT_NOT_EXIST'] = 6441504, 
    ['MOMENTS_COMMENT_SENSITIVE'] = 6441502, 
    ['MOMENTS_COMMENT_TOO_LONG'] = 6441501, 
    ['MOMENTS_DATA_EXHAUSTED'] = 6441515, 
    ['MOMENTS_DELETED_DYNAMIC'] = 6441529, 
    ['MOMENTS_DYNAMIC_CONTENT_EMPTY'] = 6441507, 
    ['MOMENTS_DYNAMIC_CONTENT_SENSITIVE'] = 6441506, 
    ['MOMENTS_DYNAMIC_CONTENT_TOO_LONG'] = 6441505, 
    ['MOMENTS_DYNAMIC_DELETED'] = 6441512, 
    ['MOMENTS_DYNAMIC_FORWARD'] = 6441513, 
    ['MOMENTS_DYNAMIC_NOT_EXIST'] = 6441509, 
    ['MOMENTS_DYNAMIC_RELEASE'] = 6441514, 
    ['MOMENTS_DYNAMIC_SHARE'] = 6441523, 
    ['MOMENTS_FOLLOW_LIMIT'] = 6441511, 
    ['MOMENTS_FORBIDDEN_COMMENT'] = 6441503, 
    ['MOMENTS_FORBIDDEN_FORWARD'] = 6441508, 
    ['MOMENTS_OPERATE_TOO_FREQUENTLY'] = 6441500, 
    ['MOMENTS_SELECTION_LIMIT'] = 6441531, 
    ['MOMENTS_SENSITIVE_WORD'] = 6441510, 
    ['MOMENTS_SERVER_TIMEOUT'] = 6441530, 
    ['MOMENTS_SHARE_CHOICE'] = 6441528, 
    ['MONEYSELL_SOLD_OUTOFTIME'] = 6402332, 
    ['MONEYSELL_SOLD_SUCCESS'] = 6402331, 
    ['MONEYSELL_SUCCESS'] = 6402328, 
    ['MONEY_NOT_ENOUGH'] = 6402402, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_LACK_MONEY'] = 6402318, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_LIMIT'] = 6402316, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_MAX_LIMIT'] = 6402321, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_NUM_MIN_LIMIT'] = 6402322, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_ODER_CANCEL_FAIL'] = 6402320, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_ODER_COMFIRM_FAIL'] = 6402317, 
    ['MONEY_SALE_BUY_MONEY_FAIL_BY_ODER_REPEAT'] = 6402315, 
    ['MONEY_SALE_CANCEL_MONEY_FAIL_BY_ODER_NOT_EXIT'] = 6402324, 
    ['MONEY_SALE_CANCEL_MONEY_FAIL_BY_ODER_REPEAT'] = 6402323, 
    ['MONEY_SALE_CANCEL_MONEY_FAIL_BY_SELL_NUM_NOT_SAME'] = 6402325, 
    ['MONEY_SALE_NOT_OPEN'] = 6402327, 
    ['MONEY_SALE_OTHER_MAX_RATE_LIMIT'] = 6402314, 
    ['MONEY_SALE_OTHER_MIN_RATE_LIMIT'] = 6402313, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_NUM_MAX_LIMIT'] = 6402306, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_NUM_MIN_LIMIT'] = 6402307, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_ODER_CANCEL_FAIL'] = 6402311, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_ODER_COMFIRM_FAIL'] = 6402310, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_ODER_REPEAT'] = 6402309, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_ORDER_MAX_LIMIT'] = 6402308, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_RATE_MAX_LIMIT'] = 6402304, 
    ['MONEY_SALE_SELL_MONEY_FAIL_BY_RATE_MIN_LIMIT'] = 6402305, 
    ['MONSTER_CATCH'] = 6440333, 
    ['MONSTER_RUN'] = 6440332, 
    ['MOUNT_BAN_MAP'] = 6442071, 
    ['MOYAOSHI_1'] = 6405938, 
    ['MOYAOSHI_2'] = 6405939, 
    ['MYSTERY_EFFECT_ADD_DICE_BUFF'] = 6405613, 
    ['MYSTERY_EFFECT_EXTRA_DICE'] = 6405615, 
    ['MYSTERY_EFFECT_MORE_EXP'] = 6405614, 
    ['MYSTERY_EFFECT_RANDOM_REWARD'] = 6405612, 
    ['MYSTERY_EFFECT_RECYCLE_HERB'] = 6405616, 
    ['NAGETIVE_LINK'] = 6401615, 
    ['NEW_APPEARANCE'] = 6441522, 
    ['NEW_TASK_REMINDER'] = 6400901, 
    ['NONE_GUILD_RIGHT'] = 6402023, 
    ['NOT_ARRIVE_END_POINT'] = 6405607, 
    ['NOT_ENOUGH_HERB'] = 6405600, 
    ['NOT_GET_END_POINT'] = 6405610, 
    ['NOT_IN_SAME_LOGIC'] = 6409022, 
    ['NO_OTHER_CLUB_TO_APPLY'] = 6440820, 
    ['NO_SELECT_HERB'] = 6405601, 
    ['NPC_SHOP_ADD_ITEM_ERROR'] = 6400613, 
    ['NPC_SHOP_BEYOND_LIMIT_COUNT'] = 6400606, 
    ['NPC_SHOP_BUY_CD'] = 6400618, 
    ['NPC_SHOP_BUY_CONDITION_LOCKED'] = 6400617, 
    ['NPC_SHOP_CONDITION_LOCKED'] = 6400615, 
    ['NPC_SHOP_COUNT_TOO_LARGE'] = 6400605, 
    ['NPC_SHOP_HAVE_END_SALE'] = 6400608, 
    ['NPC_SHOP_ITEM_ID_ERROR'] = 6400609, 
    ['NPC_SHOP_LESS_THAN_GOODS_LEVEL'] = 6400604, 
    ['NPC_SHOP_LESS_THAN_OPEN_LEVEL'] = 6400601, 
    ['NPC_SHOP_MONEY_NOT_ENOUGH'] = 6400612, 
    ['NPC_SHOP_NOT_BEGIN_SALE'] = 6400607, 
    ['NPC_SHOP_PROFESSION_NOT_MATCH'] = 6400614, 
    ['NPC_SHOP_PURCHASE_CD'] = 6400616, 
    ['NPC_SHOP_REQ_COUNT_ERROR'] = 6400602, 
    ['NPC_SHOP_REQ_INFO_SHOP_ID_ERROR'] = 6400603, 
    ['NPC_SHOP_SHOW_CONDITION_LOCKED'] = 6400619, 
    ['NPC_SHOP_TOKEN_ID_ERROR'] = 6400610, 
    ['NPC_SHOP_TOKEN_STRUCT_ERROR'] = 6400611, 
    ['OPEN_GUILD_CHANNEL_VOICE'] = 6402217, 
    ['OPERATIONAL_LIMITATION_IN_SINGLE_DUNGEON'] = 6441813, 
    ['OPERATION_FAIL'] = 6404004, 
    ['OPTION_IN_CD'] = 6409005, 
    ['OTHER_ACCEPT_GUILD_INVITE'] = 6402028, 
    ['OTHER_QUIT_GUILD'] = 6402032, 
    ['OTHER_REFUSE_GUILD_INVITE'] = 6402026, 
    ['OUR_TEAM_CONTROL_POINT'] = 6405796, 
    ['OUTPUT_CHECK_FAIL'] = 6440127, 
    ['OUTPUT_CHECK_SUCCESS'] = 6440126, 
    ['OUT_OF_SCENE'] = 6405723, 
    ['OtherEffectQuality_Limit'] = 6401201, 
    ['PEERLESS_PERFORMANCE'] = 6405818, 
    ['PENQUAN_1'] = 6405920, 
    ['PENQUAN_2'] = 6405921, 
    ['PHOTOGRAPH_PROHIBIT_ANIMATION'] = 6404007, 
    ['PHOTOGRAPH_PROHIBIT_FIGHT'] = 6404006, 
    ['PHOTOGRAPH_QUIT_FIGHT'] = 6404008, 
    ['PHOTOGRAPH_QUIT_MATCH_SUCCESS'] = 6404009, 
    ['PHOTOGRAPH_QUIT_STORY'] = 6404010, 
    ['PHOTOGRAPH_SAVE_FAIL'] = 6404012, 
    ['PHOTOGRAPH_SAVE_LOG'] = 6404011, 
    ['PICK_CHEST_MAX'] = 6401011, 
    ['PICK_CHEST_MIN'] = 6401012, 
    ['PICK_CHEST_PRICE_MAX'] = 6405210, 
    ['PICK_CHEST_PRICE_MIN'] = 6405211, 
    ['PICK_DESTORY_INTERRUPT_CAST'] = 6441815, 
    ['PICK_ENTER_CD'] = 6441814, 
    ['PIGGYBANK_MONSTER'] = 6440322, 
    ['PIRATE_SCHOOL_NO_OPEN'] = 6401310, 
    ['PLAYER_GIVEUP_AUCTION'] = 6406052, 
    ['PLAYER_IN_MULTI_PVP'] = 6400431, 
    ['PLAYER_LEVEL_UP'] = 6409016, 
    ['PLOTRECAP_UNLOCK'] = 6442016, 
    ['PLOT_RECAP_CONFIG_NOT_EXIST'] = 6441803, 
    ['PLOT_RECAP_LESS_MAIN_TYPE_TOKEN'] = 6441808, 
    ['PLOT_RECAP_LEVEL_NOT_ENOUGH'] = 6441812, 
    ['PLOT_RECAP_LEVEL_NO_REWARD'] = 6441810, 
    ['PLOT_RECAP_LEVEL_REWARD_ALREADY'] = 6441809, 
    ['PLOT_RECAP_NOT_FINISH'] = 6441804, 
    ['PLOT_RECAP_PARAM_ERROR'] = 6441806, 
    ['PLOT_RECAP_REWARD_HAS_RECEIVED'] = 6441805, 
    ['PLOT_RECAP_WRONG_MAIN_TYPE'] = 6441807, 
    ['PORTRAIT_FRAME_LOCKED'] = 6442059, 
    ['PORTRAIT_LOCKED'] = 6442058, 
    ['PRAISE_SIGNAL'] = 6405714, 
    ['PREPARE_COUNTDOWN'] = 6405719, 
    ['PRODUCT_ID_ERROR'] = 6402604, 
    ['PROPERTY_NOT_ENOUGH'] = 6405609, 
    ['PROTECTION_SIGNAL'] = 6405713, 
    ['PROTOCOL_ERROR'] = 6409004, 
    ['PUT_ON_SAME_SEALED'] = 6402807, 
    ['PVPGAME_ATK_UP'] = 6400415, 
    ['PVPGAME_BALANCEBUFF'] = 6400425, 
    ['PVPGAME_BREAK_MATCH'] = 6400433, 
    ['PVPGAME_ENEMY_PART_KILL_BOSS'] = 6400430, 
    ['PVPGAME_IN_GAME'] = 6400434, 
    ['PVPGAME_MODULE_NOT_UNLOCK'] = 6400426, 
    ['PVPGAME_NEED_EXIT_DUNGEON'] = 6400428, 
    ['PVPGAME_NEED_EXIT_GROUP'] = 6400432, 
    ['PVPGAME_NEED_EXIT_TEAM'] = 6400427, 
    ['PVPGAME_OTHER_MATCH_INPROCESS'] = 6400424, 
    ['PVPGAME_OUR_PART_KILL_BOSS'] = 6400429, 
    ['PVPGAME_RESULT_SEND_LOSE'] = 6400420, 
    ['PVPGAME_RESULT_SEND_TIE'] = 6400421, 
    ['PVPGAME_RESULT_SEND_WIN'] = 6400419, 
    ['PVPGAME_TEAM_FORBIDDEN'] = 6400423, 
    ['PVPGAME_TERMINATION_EARLY'] = 6400435, 
    ['PVPGAME_TIME_OUT_RESULT_LOSE'] = 6400417, 
    ['PVPGAME_TIME_OUT_RESULT_TIE'] = 6400418, 
    ['PVPGAME_TIME_OUT_RESULT_WIN'] = 6400416, 
    ['PVPGAME_WAIT_FOR_WATCH'] = 6400422, 
    ['PVPMATCH_ACTIVITY_CLOSE_REMINDER'] = 6405820, 
    ['PVPMATCH_CANCEL_NOT_IN_QUEUE'] = 6400405, 
    ['PVPMATCH_CANCEL_OFFLINE'] = 6400413, 
    ['PVPMATCH_CANCLE_TEAM_CHANGE'] = 6400402, 
    ['PVPMATCH_CANCLE_TIMEOUT'] = 6400409, 
    ['PVPMATCH_COMFIRM_CANCLE_MATCH'] = 6400412, 
    ['PVPMATCH_COMFIRM_OTHER_CANCLE'] = 6400411, 
    ['PVPMATCH_COMFIRM_TEAM_CANCLE'] = 6400410, 
    ['PVPMATCH_COMFIRM_TIMEOUT_CANCLE'] = 6400414, 
    ['PVPMATCH_FAIL'] = 6400404, 
    ['PVPMATCH_MATCHING'] = 6400401, 
    ['PVPMATCH_NOT_CAPTAIN'] = 6400408, 
    ['PVPMATCH_NOT_IN_ACTIVITY_TIME'] = 6405819, 
    ['PVPMATCH_OFFLINE_IN_DUNGEON'] = 6400406, 
    ['PVPMATCH_SUCESS'] = 6400403, 
    ['PVPMATCH_TOO_MEMBER'] = 6400407, 
    ['PVP_3V3_NOT_NEED_HEAL'] = 6405784, 
    ['PVP_ACCEPT_FAILED_DEATH'] = 6405740, 
    ['PVP_ACCEPT_FAILED_IN'] = 6405725, 
    ['PVP_ACCEPT_FAILED_OFF'] = 6405727, 
    ['PVP_ACCEPT_FAILED_OUT'] = 6405726, 
    ['PVP_ACCEPT_TARGET_DEAD'] = 6405761, 
    ['PVP_CANCEL_READY_CONFIRM_IN_PUNISM_TIME'] = 6405780, 
    ['PVP_CANNOT_BATTLE_WITH_SELF'] = 6405762, 
    ['PVP_CHAMPION_LEADER_TRY_QUIT_TROOP'] = 6442043, 
    ['PVP_CHAMPION_NEW_TROOP_LEADER'] = 6442044, 
    ['PVP_FAILED'] = 6405752, 
    ['PVP_FAILED_CRAZY_INVITE'] = 6405767, 
    ['PVP_FAILED_INVITE'] = 6405758, 
    ['PVP_FAILED_INVITE_CRAZY'] = 6405766, 
    ['PVP_FAILED_TARGET_IN_FIGHT'] = 6405760, 
    ['PVP_INVITE_EXCESS_FAILED'] = 6405750, 
    ['PVP_INVITE_FAILED'] = 6405731, 
    ['PVP_INVITE_FAILED_DEATH'] = 6405739, 
    ['PVP_INVITE_FAILED_FREQUENT'] = 6405729, 
    ['PVP_INVITE_FAILED_IN'] = 6405751, 
    ['PVP_INVITE_FAILED_OFF'] = 6405736, 
    ['PVP_INVITE_FAILED_OUT'] = 6405732, 
    ['PVP_INVITE_FAILED_OVER'] = 6405730, 
    ['PVP_INVITE_FAILED_REPEAT'] = 6405728, 
    ['PVP_INVITE_FAILED_REVIVE'] = 6405738, 
    ['PVP_INVITE_FAILED_UNSAFE'] = 6405733, 
    ['PVP_INVITE_SUCESS'] = 6405724, 
    ['PVP_IN_SCENE_NOT_ALLOW_INVITE_PLAYER'] = 6405791, 
    ['PVP_IN_SCENE_NOT_ALLOW_KICK_PLAYER'] = 6405792, 
    ['PVP_LIKED_BY_OTHERS'] = 6405823, 
    ['PVP_MATCH_IN_PUNISH_TIME'] = 6405773, 
    ['PVP_MATCH_STATE_NOT_ALLOW_MATCH_OTHER'] = 6405790, 
    ['PVP_MATCH_TEAM_MEMBER_IN_PUNISH_TIME'] = 6405774, 
    ['PVP_MODE_SHIELDING_TIPS'] = 6442054, 
    ['PVP_NOT_ALLOW_USE_ITEM'] = 6405785, 
    ['PVP_NOT_READY'] = 6405822, 
    ['PVP_PLAYER_OFFLINE_CANCEL_MATCH'] = 6405782, 
    ['PVP_REVIVIE_TIMES_UP_TO_MAX'] = 6442004, 
    ['PVP_TEAM_ENTRANCE_NOT_ALLOW_OPEN'] = 6400436, 
    ['PVP_TEAM_MEMBER_CANCEL_READY_CONFIRM_IN_PUNISH_TIME'] = 6405781, 
    ['PVP_TEAM_MEMBER_NOT_ALLOW_START_MATCH'] = 6405789, 
    ['PVP_TEAM_MEMBER_NOT_READY'] = 6405793, 
    ['PVP_TEAM_MEMBER_NUM_CHANGE_CANCEL_MATCH'] = 6405783, 
    ['PVP_TEAM_MEMBER_PROFESSION_TOO_MANY'] = 6405824, 
    ['PVP_TEAM_MEMBER_RANK_DIFF_LARGE'] = 6405794, 
    ['Performance_Reminder'] = 6401204, 
    ['QUEST_BIKE_LEAVE'] = 6441811, 
    ['QUICKDECISION_START'] = 6405770, 
    ['QUICKDECISION_START_PREVIEW'] = 6405769, 
    ['QUIT_CUT_CANDIDATE_SYS_FRIEND'] = 6402068, 
    ['QUIT_GUILD_BY_OTHER'] = 6402034, 
    ['QUIT_GUILD_BY_OTHER_SYS_FRIEND'] = 6402119, 
    ['QUIT_GUILD_SUCCESS'] = 6402031, 
    ['QUIT_GUILD_SUCCESS_CHANNEL'] = 6402114, 
    ['QUIT_GUILD_WITHOUT_RESIGN'] = 6402030, 
    ['RBRaid_BlockEnergyBall'] = 6406218, 
    ['RBRaid_BlockEnergyBall2'] = 6406246, 
    ['RBRaid_BossCombatBegin'] = 6406220, 
    ['RBRaid_BossCombatFailed'] = 6406247, 
    ['RBRaid_BossCombatSuccess'] = 6406221, 
    ['RBRaid_EnemyDieReminder'] = 6406212, 
    ['RBRaid_EnemySpawnReminder'] = 6406211, 
    ['RBRaid_EscortReminder'] = 6406210, 
    ['RBRaid_ProtectBegin'] = 6406215, 
    ['RBRaid_ProtectFailed'] = 6406217, 
    ['RBRaid_ProtectSuccess'] = 6406216, 
    ['RBRaid_Protect_2049Born'] = 6406219, 
    ['RBRaid_SpinBegin'] = 6406250, 
    ['RBRaid_SpinFinish'] = 6406251, 
    ['RBRaid_SpinFinish2'] = 6406255, 
    ['RBRaid_SpinJoke'] = 6406254, 
    ['RBRaid_SwitchLevelCountdown'] = 6406213, 
    ['RBRaid_WaveRefreshCountdown'] = 6406214, 
    ['RBRaid_reminder10'] = 6406225, 
    ['RBRaid_reminder11'] = 6406226, 
    ['RBRaid_reminder12'] = 6406227, 
    ['RBRaid_reminder13'] = 6406228, 
    ['RBRaid_reminder14'] = 6406229, 
    ['RBRaid_reminder15'] = 6406230, 
    ['RBRaid_reminder16'] = 6406231, 
    ['RBRaid_reminder17'] = 6406232, 
    ['RBRaid_reminder18'] = 6406233, 
    ['RBRaid_reminder20'] = 6406235, 
    ['RBRaid_reminder21'] = 6406237, 
    ['RBRaid_reminder22'] = 6406238, 
    ['RBRaid_reminder23'] = 6406239, 
    ['RBRaid_reminder24'] = 6406240, 
    ['RBRaid_reminder25'] = 6406241, 
    ['RBRaid_reminder28'] = 6406248, 
    ['RBRaid_reminder29'] = 6406249, 
    ['RBRaid_reminder32'] = 6406252, 
    ['RBRaid_reminder33'] = 6406253, 
    ['RBRaid_reminder36'] = 6406256, 
    ['RBRaid_reminder37'] = 6406336, 
    ['RBRaid_reminder38'] = 6406337, 
    ['RBRaid_reminder39'] = 6406338, 
    ['RBRaid_reminder40'] = 6406339, 
    ['RBRaid_reminder41'] = 6406340, 
    ['RBRaid_reminder42'] = 6406341, 
    ['RBRaid_reminder43'] = 6406342, 
    ['RBRaid_reminder44'] = 6406343, 
    ['RBRaid_reminder45'] = 6406344, 
    ['RBRaid_reminder46'] = 6406345, 
    ['RBRaid_reminder7'] = 6406222, 
    ['RBRaid_reminder8'] = 6406223, 
    ['RBRaid_reminder9'] = 6406224, 
    ['READY_TO_MAKE_MEDICINE'] = 6405611, 
    ['REASONING_JUDGE_FAIL'] = 6442042, 
    ['REASONING_LINK_CLUE_FAIL'] = 6442070, 
    ['RECIPE_DELETED'] = 6405000, 
    ['RECIPE_FULL'] = 6405001, 
    ['RECIPE_HAS_BEEN_LEARNED'] = 6405002, 
    ['RECIPE_LEARNED'] = 6440222, 
    ['RECIPE_REPLACED'] = 6440125, 
    ['RECIPE_SAVED'] = 6440124, 
    ['RECORD_QUERY_IN_CD'] = 6405100, 
    ['RED_PACKET_ALREADY_FINISHED'] = 6440429, 
    ['RED_PACKET_ALREADY_RECEIVED'] = 6440430, 
    ['RED_PACKET_ARGS_ERROR'] = 6440400, 
    ['RED_PACKET_CHANNEL_ERROR'] = 6440403, 
    ['RED_PACKET_CHATROOM_ID_WRONG'] = 6440436, 
    ['RED_PACKET_CLASS_ERROR'] = 6440401, 
    ['RED_PACKET_GIFT_CREDIT_NOT_ENOUGH'] = 6440417, 
    ['RED_PACKET_GOODS_ALREADY_PUT'] = 6440424, 
    ['RED_PACKET_GOODS_CAN_NOT_SEND'] = 6440420, 
    ['RED_PACKET_GOODS_CNT_ERROR'] = 6440418, 
    ['RED_PACKET_GOODS_TYPE_NUM_ERROR'] = 6440419, 
    ['RED_PACKET_GROUP_ID_WRONG'] = 6440433, 
    ['RED_PACKET_GUILD_ID_WRONG'] = 6440432, 
    ['RED_PACKET_MESSAGE_DIRTY'] = 6440421, 
    ['RED_PACKET_MESSAGE_TOO_LONG'] = 6440406, 
    ['RED_PACKET_MONEY_LESS_THAN_MIN'] = 6440412, 
    ['RED_PACKET_MONEY_LESS_THAN_PACK_NUM'] = 6440411, 
    ['RED_PACKET_MONEY_MORE_THAN_MAX'] = 6440413, 
    ['RED_PACKET_MONEY_NOT_ENOUGH'] = 6440414, 
    ['RED_PACKET_NOT_EXIST'] = 6440428, 
    ['RED_PACKET_NOT_IN_CHATROOM'] = 6440435, 
    ['RED_PACKET_NOT_IN_COMMON_GUILD'] = 6440404, 
    ['RED_PACKET_NOT_IN_TEAM'] = 6440405, 
    ['RED_PACKET_NO_MORE_MONEY'] = 6440427, 
    ['RED_PACKET_NO_PASSWORD'] = 6440409, 
    ['RED_PACKET_NO_SECRET_WORD'] = 6440407, 
    ['RED_PACKET_PACK_NUM_LESS_THAN_MIN'] = 6440415, 
    ['RED_PACKET_PACK_NUM_MORE_THAN_MAX'] = 6440416, 
    ['RED_PACKET_PASSWD_DIRTY'] = 6440423, 
    ['RED_PACKET_PASSWD_TOO_LONG'] = 6440410, 
    ['RED_PACKET_PASSWD_WRONG'] = 6440426, 
    ['RED_PACKET_SECRET_WORD_DIRTY'] = 6440422, 
    ['RED_PACKET_SECRET_WORD_SPECIAL'] = 6440434, 
    ['RED_PACKET_SECRET_WORD_TOO_LONG'] = 6440408, 
    ['RED_PACKET_SECRET_WORD_WRONG'] = 6440425, 
    ['RED_PACKET_TEAM_ID_WRONG'] = 6440431, 
    ['RED_PACKET_TYPE_ERROR'] = 6440402, 
    ['REFINE_COMPONENTS_DONE'] = 6405810, 
    ['REFINE_NO_COMPONENTS'] = 6405809, 
    ['REFINE_NO_OPEN'] = 6405803, 
    ['REFINE_REFINE_LUCK'] = 6402814, 
    ['REFINE_UNLOCK_TIP'] = 6405816, 
    ['REFRESH_SHOP_ALREADY_REFRESHED'] = 6402401, 
    ['REFRESH_SHOP_DAY_MANUAL_TIMES_LIMIT'] = 6402400, 
    ['REFUSE_DUNGEON_OPEN_FAILED'] = 6406024, 
    ['REFUSE_GUILD_INVITE'] = 6402027, 
    ['REGION_REACH_MAX_LOGIC_PLAYER_NUM'] = 6400218, 
    ['REMATERIAL_ALREADY_HELP'] = 6405303, 
    ['REMATERIAL_FINISH'] = 6405306, 
    ['REMATERIAL_MAX_HELP_TIME'] = 6405307, 
    ['REMATERIAL_NOT_SAME_GUILD'] = 6405305, 
    ['REMATERIAL_NO_ENOUGH_ITEM'] = 6405302, 
    ['REMATERIAL_NO_ITEM_SOLD'] = 6405301, 
    ['REMATERIAL_SEND_HELP'] = 6405304, 
    ['REMATERIAL_SOMEONE_HELP'] = 6405308, 
    ['REMINDERLTEM_DROP'] = 6405718, 
    ['REMOVE_EXPIRED_ITEMS'] = 6401001, 
    ['REMOVE_OBSERVE_STATUS'] = 6406039, 
    ['REPLACE_ACCOUNT_IN_CD'] = 6400108, 
    ['REPLACE_AVATAR_BOT_FAILURE'] = 6441703, 
    ['REPLACE_AVATAR_BOT_SUC'] = 6441701, 
    ['REPORT_THANKS'] = 6440361, 
    ['RES_PACK_AWARD_GOT'] = 6402240, 
    ['RETREAT_SIGNAL'] = 6405715, 
    ['RETURN_CHECK_FAIL'] = 6440129, 
    ['RETURN_CHECK_SUCCESS'] = 6440128, 
    ['REVIVIED_TIME_UP_TO_MAX'] = 6442047, 
    ['REWARD_MAX'] = 6401101, 
    ['ROLECREATE_LOCK'] = 6400119, 
    ['ROLECREATE_NAME_ENTER_FAIL'] = 6400121, 
    ['ROLECREATE_SEX_LOCK'] = 6400122, 
    ['ROLEPLAY_ADD_FORTUNE'] = 6406134, 
    ['ROLEPLAY_ARBITRATOR_CAN_TRIAL_TIP'] = 6406168, 
    ['ROLEPLAY_ARBITRATOR_ENTER_CHURCH'] = 6406157, 
    ['ROLEPLAY_ARBITRATOR_FIRST_TRIAL'] = 6406167, 
    ['ROLEPLAY_ARBITRATOR_INDENTIFY'] = 6406166, 
    ['ROLEPLAY_ARBITRATOR_LEAVE_CHURCH'] = 6406156, 
    ['ROLEPLAY_ARBITRATOR_TRAIL_FIGHT'] = 6406155, 
    ['ROLEPLAY_ARBITRATOR_TRAIL_OK'] = 6406153, 
    ['ROLEPLAY_ARBITRATOR_TRAIL_RUN'] = 6406154, 
    ['ROLEPLAY_ARBITRATOR_TRIAL_TIP'] = 6406170, 
    ['ROLEPLAY_DICEBATTLE_CANCEL'] = 6406139, 
    ['ROLEPLAY_DICEBATTLE_FAIL'] = 6406119, 
    ['ROLEPLAY_DICEBATTLE_FORTUNE'] = 6406133, 
    ['ROLEPLAY_DICEBATTLE_LOSE'] = 6406114, 
    ['ROLEPLAY_DICEBATTLE_REFUSE'] = 6406115, 
    ['ROLEPLAY_DICEBATTLE_WIN'] = 6406113, 
    ['ROLEPLAY_DICE_RESULT'] = 6401391, 
    ['ROLEPLAY_DIE_NO'] = 6406152, 
    ['ROLEPLAY_FORTUNE_ADD'] = 6406138, 
    ['ROLEPLAY_FORTUNE_BUFFTIME'] = 6406122, 
    ['ROLEPLAY_FORTUNE_CANCEL'] = 6406140, 
    ['ROLEPLAY_FORTUNE_DICEBATTLE'] = 6406137, 
    ['ROLEPLAY_FORTUNE_DIE_NO'] = 6406149, 
    ['ROLEPLAY_FORTUNE_FORTUNE'] = 6406136, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY'] = 6406123, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY_FAIL_CD'] = 6406132, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY_OTHER'] = 6406131, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY_TIME'] = 6406162, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY_TXT1'] = 6406176, 
    ['ROLEPLAY_FORTUNE_OCCUPANCY_TXT2'] = 6406177, 
    ['ROLEPLAY_FORTUNE_REJECT'] = 6406109, 
    ['ROLEPLAY_FORTUNE_SEARCH_NO_PLAYER'] = 6406161, 
    ['ROLEPLAY_FORTUNE_SERVANT_TELEPORT'] = 6406158, 
    ['ROLEPLAY_FORTUNE_SERVANT_TELEPORT_NO'] = 6406159, 
    ['ROLEPLAY_FORTUNE_TELLER_LEAVE'] = 6406145, 
    ['ROLEPLAY_FORTUNE_TELLER_REHECT'] = 6406146, 
    ['ROLEPLAY_FORTUNE_TIPS'] = 6406142, 
    ['ROLEPLAY_HUD_NO'] = 6406178, 
    ['ROLEPLAY_HUD_SLAVE'] = 6406179, 
    ['ROLEPLAY_JOKER_DICE_RESULT'] = 6401406, 
    ['ROLEPLAY_JOKER_EGG_CD'] = 6406108, 
    ['ROLEPLAY_JOKER_FLOWER_CD'] = 6406107, 
    ['ROLEPLAY_JOKER_REWARD'] = 6406163, 
    ['ROLEPLAY_JOKER_REWARD_MAXCOUNT'] = 6406106, 
    ['ROLEPLAY_JOKER_REWARD_ZERO'] = 6406164, 
    ['ROLEPLAY_LEVEL_UP'] = 6401407, 
    ['ROLEPLAY_MONEY_FAILURE'] = 6406105, 
    ['ROLEPLAY_MUTUALEXLIUSION'] = 6406141, 
    ['ROLEPLAY_NO'] = 6406160, 
    ['ROLEPLAY_NOTARGET'] = 6406118, 
    ['ROLEPLAY_NO_SAFE_ENTER'] = 6406101, 
    ['ROLEPLAY_NO_SAFE_QUIT'] = 6406102, 
    ['ROLEPLAY_NO_SEE'] = 6406165, 
    ['ROLEPLAY_PATH_NO'] = 6406171, 
    ['ROLEPLAY_RELEASE_NOSLAVE'] = 6406116, 
    ['ROLEPLAY_REQUEST_FAILURE'] = 6406104, 
    ['ROLEPLAY_ROLESKILL_TIP'] = 6406169, 
    ['ROLEPLAY_SEQUENCE_LOCKED'] = 6401404, 
    ['ROLEPLAY_SEQUENCE_SKILL_LOCKED'] = 6401400, 
    ['ROLEPLAY_SEQUENCE_SKILL_UNLOCK'] = 6401403, 
    ['ROLEPLAY_SERVANT_FORTUNE'] = 6406135, 
    ['ROLEPLAY_SET_FAILURE'] = 6406103, 
    ['ROLEPLAY_SKILL_CONDITION_INVALID'] = 6401393, 
    ['ROLEPLAY_SKILL_COST_NOTENOUGH'] = 6401392, 
    ['ROLEPLAY_SKILL_POINT_NOTENOUGH'] = 6401405, 
    ['ROLEPLAY_SKILL_SUCCESS'] = 6401394, 
    ['ROLEPLAY_SOCIAL_UNLOCK'] = 6401395, 
    ['ROLEPLAY_SPIRIT_ITEM_NOTENOUGH'] = 6401397, 
    ['ROLEPLAY_SPIRIT_ITEM_OVERMAX'] = 6401398, 
    ['ROLEPLAY_SPIRIT_ITEM_SUCCESS'] = 6401399, 
    ['ROLEPLAY_SPIRIT_NOTENOUGH'] = 6401396, 
    ['ROLEPLAY_TARGET_LEFT'] = 6406117, 
    ['ROLEPLAY_TARGET_SKILL'] = 6406127, 
    ['ROLEPLAY_WITCH_ADD_DIE_NO'] = 6406151, 
    ['ROLEPLAY_WITCH_ADD_TIPS'] = 6406144, 
    ['ROLEPLAY_WITCH_CHAIR_ADD'] = 6406121, 
    ['ROLEPLAY_WITCH_CHAIR_ARREST'] = 6406120, 
    ['ROLEPLAY_WITCH_CHAIR_OCCUPANCY'] = 6406126, 
    ['ROLEPLAY_WITCH_CHAIR_REJECT'] = 6406124, 
    ['ROLEPLAY_WITCH_DIE_NO'] = 6406150, 
    ['ROLEPLAY_WITCH_ERROR'] = 6406125, 
    ['ROLEPLAY_WITCH_SELF_INTERACTING'] = 6406111, 
    ['ROLEPLAY_WITCH_SERVANT_TELEPORT'] = 6406148, 
    ['ROLEPLAY_WITCH_SERVANT_TELEPORT_NO'] = 6406147, 
    ['ROLEPLAY_WITCH_SKILL_NOME'] = 6406128, 
    ['ROLEPLAY_WITCH_SLAVE_NO'] = 6406129, 
    ['ROLEPLAY_WITCH_SLAVE_NOHUMANITY'] = 6406130, 
    ['ROLEPLAY_WITCH_SLAVE_NUMMAX'] = 6406110, 
    ['ROLEPLAY_WITCH_TARGET_INTERACTING'] = 6406112, 
    ['ROLEPLAY_WITCH_TIPS'] = 6406143, 
    ['ROLE_CHANGE_EQUIP_BAG_FULL'] = 6400505, 
    ['ROLE_CHANGE_EQUIP_IN_COMBAT'] = 6400504, 
    ['ROLE_CHANGE_EQUIP_IN_DEATH'] = 6400510, 
    ['ROLE_CHANGE_EQUIP_IN_SHOW'] = 6400511, 
    ['ROLE_CREATE_DELETE_ROLE_FAIL'] = 6400112, 
    ['ROLE_CREATE_NAME_BEYOND_LIMIT'] = 6400114, 
    ['ROLE_CREATE_NAME_DUPLICATE'] = 6400102, 
    ['ROLE_CREATE_NAME_EMPTY'] = 6400101, 
    ['ROLE_CREATE_NAME_ILLEGAL'] = 6400115, 
    ['ROLE_CREATE_NAME_NOTPC'] = 6400131, 
    ['ROLE_CREATE_SECONDNAME_BEYOND_LIMIT'] = 6400128, 
    ['ROLE_CREATE_SECONDNAME_EMPTY'] = 6400127, 
    ['ROLE_CREATE_SECONDNAME_ILLEGAL'] = 6400129, 
    ['ROLE_CREATE_SECONDNAME_NOTPC'] = 6400132, 
    ['ROLE_CREATE_SWITCH_ROLE_WAIT'] = 6400130, 
    ['ROLE_EQUIP_SUIT_RENAME_ILLEGAL'] = 6400507, 
    ['ROLE_EQUIP_SUIT_RENAME_LETTER'] = 6400508, 
    ['ROLE_EQUIP_SUIT_RENAME_NONE'] = 6400509, 
    ['ROLE_EQUIP_SUIT_SAVE'] = 6400506, 
    ['ROLE_ID_COPY_SUCCESS'] = 6400503, 
    ['ROLE_STATE_ERROR'] = 6409006, 
    ['RP_FORTUNE_INDESK_NO_SINGLE_FORTUNE'] = 6406175, 
    ['RaidBoss_Amon_RageReminder'] = 6406391, 
    ['RaidBoss_Amon_Skill1Reminder'] = 6406392, 
    ['RaidBoss_Amon_Skill3Reminder'] = 6406387, 
    ['RaidBoss_Amon_Skill4Reminder'] = 6406388, 
    ['RaidBoss_Amon_Skill5Reminder'] = 6406389, 
    ['RaidBoss_Amon_Skill5Reminder3'] = 6406521, 
    ['RaidBoss_Amon_Skill5Reminder4'] = 6406522, 
    ['RaidBoss_Amon_Skill5Reminder5'] = 6406523, 
    ['RaidBoss_Amon_Skill5Reminder6'] = 6406524, 
    ['RaidBoss_Amon_Skill5Reminder7'] = 6406525, 
    ['RaidBoss_Amon_Skill6Reminder'] = 6406390, 
    ['RaidBoss_Amon_Skill6Reminder1'] = 6406393, 
    ['RaidBoss_Amon_Skill8Reminder1'] = 6406396, 
    ['RaidBoss_Amon_Skill8Reminder2'] = 6406397, 
    ['RaidBoss_Amon_Skill8Reminder3'] = 6406398, 
    ['RaidBoss_Amon_Skill8Reminder4'] = 6406399, 
    ['RaidBoss_Amon_Skill9Reminder1'] = 6406526, 
    ['RaidBoss_Amon_Skill9Reminder10'] = 6406535, 
    ['RaidBoss_Amon_Skill9Reminder11'] = 6406536, 
    ['RaidBoss_Amon_Skill9Reminder12'] = 6406537, 
    ['RaidBoss_Amon_Skill9Reminder13'] = 6406538, 
    ['RaidBoss_Amon_Skill9Reminder14'] = 6406539, 
    ['RaidBoss_Amon_Skill9Reminder15'] = 6406540, 
    ['RaidBoss_Amon_Skill9Reminder2'] = 6406527, 
    ['RaidBoss_Amon_Skill9Reminder3'] = 6406528, 
    ['RaidBoss_Amon_Skill9Reminder4'] = 6406529, 
    ['RaidBoss_Amon_Skill9Reminder5'] = 6406530, 
    ['RaidBoss_Amon_Skill9Reminder6'] = 6406531, 
    ['RaidBoss_Amon_Skill9Reminder7'] = 6406532, 
    ['RaidBoss_Amon_Skill9Reminder8'] = 6406533, 
    ['RaidBoss_Amon_Skill9Reminder9'] = 6406534, 
    ['RaidBoss_Amon_SkillName1'] = 6406381, 
    ['RaidBoss_Amon_SkillName2'] = 6406382, 
    ['RaidBoss_Amon_SkillName3'] = 6406383, 
    ['RaidBoss_Amon_SkillName4'] = 6406384, 
    ['RaidBoss_Amon_SkillName5'] = 6406385, 
    ['RaidBoss_Amon_SkillName6'] = 6406386, 
    ['RaidBoss_Cotard_SkillName1'] = 6406400, 
    ['RaidBoss_Cotard_SkillName2'] = 6406401, 
    ['RaidBoss_Cotard_SkillName3'] = 6406402, 
    ['RaidBoss_Cotard_SkillName4'] = 6406403, 
    ['RaidBoss_Cotard_SkillName5'] = 6406404, 
    ['RaidBoss_Goulu_DefenseBreak_Desc'] = 6406407, 
    ['RaidBoss_Goulu_Mechanic01_desc01'] = 6406297, 
    ['RaidBoss_Goulu_Mechanic01_title01'] = 6406296, 
    ['RaidBoss_Goulu_Mechanic02_desc01'] = 6406292, 
    ['RaidBoss_Goulu_Mechanic02_desc02'] = 6406293, 
    ['RaidBoss_Goulu_Mechanic02_desc03'] = 6406294, 
    ['RaidBoss_Goulu_Mechanic02_title01'] = 6406291, 
    ['RaidBoss_Goulu_Mechanic02_title02'] = 6406295, 
    ['RaidBoss_Goulu__Skill01_desc'] = 6406300, 
    ['RaidBoss_Goulu__Skill01_title'] = 6406301, 
    ['RaidBoss_Goulu__Skill02_title'] = 6406302, 
    ['RaidBoss_Goulu__Skill03_title'] = 6406304, 
    ['RaidBoss_Goulu__Skill04_caution1'] = 6406309, 
    ['RaidBoss_Goulu__Skill04_caution2'] = 6406308, 
    ['RaidBoss_Goulu__Skill04_caution3'] = 6406307, 
    ['RaidBoss_Goulu__Skill04_caution4'] = 6406310, 
    ['RaidBoss_Goulu__Skill04_predesc'] = 6406306, 
    ['RaidBoss_Goulu__Skill04_title'] = 6406303, 
    ['RaidBoss_Goulu__Skill06_desc'] = 6406299, 
    ['RaidBoss_Goulu__Skill06_title'] = 6406298, 
    ['RaidBoss_Goulu__Skill07_title'] = 6406305, 
    ['RaidBoss_Goulu__Skill08_desc'] = 6406354, 
    ['RaidBoss_Goulu__Skill08_title'] = 6406353, 
    ['RaidBoss_Milgongen_Skill01_desc'] = 6406316, 
    ['RaidBoss_Milgongen_Skill01_title'] = 6406311, 
    ['RaidBoss_Milgongen_Skill02_title'] = 6406312, 
    ['RaidBoss_Milgongen_Skill03_desc'] = 6406317, 
    ['RaidBoss_Milgongen_Skill03_title'] = 6406313, 
    ['RaidBoss_Milgongen_Skill04_desc'] = 6406318, 
    ['RaidBoss_Milgongen_Skill04_title'] = 6406314, 
    ['RaidBoss_Milgongen_Skill05_desc'] = 6406319, 
    ['RaidBoss_Milgongen_Skill05_title'] = 6406315, 
    ['RaidBoss_Milgongen_Skill06_title'] = 6406328, 
    ['RaidBoss_Milgongen_Skill07_title'] = 6406329, 
    ['RaidBoss_Milgongen_Skill08_title'] = 6406330, 
    ['RaidBoss_Milgongen_Skill09_title'] = 6406331, 
    ['RaidBoss_Milgongen_desc1'] = 6406320, 
    ['RaidBoss_Milgongen_desc10'] = 6406500, 
    ['RaidBoss_Milgongen_desc11'] = 6406501, 
    ['RaidBoss_Milgongen_desc12'] = 6406502, 
    ['RaidBoss_Milgongen_desc13'] = 6406503, 
    ['RaidBoss_Milgongen_desc14'] = 6406504, 
    ['RaidBoss_Milgongen_desc15'] = 6406505, 
    ['RaidBoss_Milgongen_desc16'] = 6406506, 
    ['RaidBoss_Milgongen_desc17'] = 6406507, 
    ['RaidBoss_Milgongen_desc18'] = 6406508, 
    ['RaidBoss_Milgongen_desc19'] = 6406509, 
    ['RaidBoss_Milgongen_desc2'] = 6406321, 
    ['RaidBoss_Milgongen_desc20'] = 6406510, 
    ['RaidBoss_Milgongen_desc21'] = 6406511, 
    ['RaidBoss_Milgongen_desc22'] = 6406512, 
    ['RaidBoss_Milgongen_desc23'] = 6406513, 
    ['RaidBoss_Milgongen_desc24'] = 6406514, 
    ['RaidBoss_Milgongen_desc25'] = 6406515, 
    ['RaidBoss_Milgongen_desc26'] = 6406516, 
    ['RaidBoss_Milgongen_desc27'] = 6406517, 
    ['RaidBoss_Milgongen_desc28'] = 6406518, 
    ['RaidBoss_Milgongen_desc29'] = 6406519, 
    ['RaidBoss_Milgongen_desc3'] = 6406322, 
    ['RaidBoss_Milgongen_desc30'] = 6406520, 
    ['RaidBoss_Milgongen_desc4'] = 6406323, 
    ['RaidBoss_Milgongen_desc5'] = 6406324, 
    ['RaidBoss_Milgongen_desc6'] = 6406325, 
    ['RaidBoss_Milgongen_desc7'] = 6406326, 
    ['RaidBoss_Milgongen_desc8'] = 6406327, 
    ['RaidBoss_Sasriel_AOEReminder'] = 6406373, 
    ['RaidBoss_Sasriel_BallReminder'] = 6406372, 
    ['RaidBoss_Sasriel_BallReminder2'] = 6406367, 
    ['RaidBoss_Sasriel_DarkSeaReminder'] = 6406370, 
    ['RaidBoss_Sasriel_LazerReminder'] = 6406371, 
    ['RaidBoss_Sasriel_Mechanism'] = 6406377, 
    ['RaidBoss_Sasriel_RiverReminder'] = 6406368, 
    ['RaidBoss_Sasriel_Sacrifice'] = 6406376, 
    ['RaidBoss_Sasriel_ShadowChargeReminder'] = 6406369, 
    ['RaidBoss_Sasriel_SkillName1'] = 6406356, 
    ['RaidBoss_Sasriel_SkillName10'] = 6406365, 
    ['RaidBoss_Sasriel_SkillName11'] = 6406366, 
    ['RaidBoss_Sasriel_SkillName19'] = 6406374, 
    ['RaidBoss_Sasriel_SkillName2'] = 6406357, 
    ['RaidBoss_Sasriel_SkillName20'] = 6406375, 
    ['RaidBoss_Sasriel_SkillName3'] = 6406358, 
    ['RaidBoss_Sasriel_SkillName4'] = 6406359, 
    ['RaidBoss_Sasriel_SkillName5'] = 6406360, 
    ['RaidBoss_Sasriel_SkillName6'] = 6406361, 
    ['RaidBoss_Sasriel_SkillName7'] = 6406362, 
    ['RaidBoss_Sasriel_SkillName8'] = 6406363, 
    ['RaidBoss_Sasriel_SkillName9'] = 6406364, 
    ['Rebecca_Strengthen'] = 6406378, 
    ['Restart_Tip'] = 6401203, 
    ['RoseGhost_allattack'] = 6406478, 
    ['RoseGhost_change'] = 6406480, 
    ['RoseGhost_divide'] = 6406479, 
    ['RoseGhost_findcouple'] = 6406477, 
    ['RoseGhost_gather'] = 6406476, 
    ['RoseGhost_shooting'] = 6406542, 
    ['RoseGhost_show'] = 6406541, 
    ['RoseGhost_spreadill'] = 6406543, 
    ['SCENE_BAN_THIS_REVIVE'] = 6400777, 
    ['SCENE_CUSTOM_NAME_EMPTY'] = 6407217, 
    ['SCENE_DATA_RELOAD_FAIL_GM'] = 6441971, 
    ['SCENE_DATA_RELOAD_SUCCESS_GM'] = 6441970, 
    ['SCENE_PROHIBIT_ENTER_LEAGUE'] = 6442085, 
    ['SCENE_RES_PACK_INSTALL'] = 6402242, 
    ['SEALED_BREAKTHROUGH_NO_MATERIAL'] = 6402804, 
    ['SEALED_BREAKTHROUGH_SUCCESS'] = 6402805, 
    ['SEALED_IS_EQUIP'] = 6402808, 
    ['SEALED_LEVEL_MAX'] = 6402806, 
    ['SEALED_MATERIAL_NOT_ENOUGH'] = 6402803, 
    ['SEALED_MONEY_NOT_ENOUGH'] = 6402802, 
    ['SEALED_QUICK_EQUIP_CHECK_FULL_ERRO'] = 6402831, 
    ['SEALED_QUICK_EQUIP_CHECK_SAME_ERRO'] = 6402830, 
    ['SEALED_RANKUP_CHECK_EMPTY_ERRO'] = 6402829, 
    ['SEALED_RANKUP_CHECK_EQUIP'] = 6402823, 
    ['SEALED_RANKUP_CHECK_FILL'] = 6402826, 
    ['SEALED_RANKUP_CHECK_LOCKED'] = 6402824, 
    ['SEALED_RANKUP_CHECK_MAX'] = 6402825, 
    ['SEALED_RANKUP_EXTRA'] = 6402820, 
    ['SEALED_RANKUP_REFINE_ITEM'] = 6402822, 
    ['SEALED_RANKUP_UPGRADE_ITEM'] = 6402821, 
    ['SEALED_REFINE_LACK_ITEM1'] = 6402817, 
    ['SEALED_REFINE_LACK_ITEM2'] = 6402818, 
    ['SEALED_REFINE_RESET_ERRO'] = 6402819, 
    ['SEALED_SAME_NOT_ENOUGH'] = 6402809, 
    ['SEALED_SYSTEM_LOCKED'] = 6402810, 
    ['SEALED_UPGRADE_LACK_ITEM1'] = 6402815, 
    ['SEALED_UPGRADE_LACK_ITEM2'] = 6402816, 
    ['SEALED_UPGRADE_SUCCESS'] = 6402801, 
    ['SECURIY_REACH_LIMIT'] = 6440346, 
    ['SEFIROT_CORE_SLOT_EMPTY'] = 6402813, 
    ['SEFIROT_CORE_SLOT_EMPTY_ERRO'] = 6402827, 
    ['SEFIROT_CORE_SLOT_LOCKED'] = 6402811, 
    ['SEFIROT_CORE_SWITCH_MONEY_NOT_ENOUGH'] = 6402812, 
    ['SEFIROT_ITEM_LOCK'] = 6402832, 
    ['SEFIROT_ITEM_UNLOCK'] = 6402833, 
    ['SELECT_ROLE_ENTER_GAME_FAILE'] = 6400110, 
    ['SEND_MAIL_FOR_BAG_FULL'] = 6401111, 
    ['SEND_MAIL_FOR_BAG_HOLD_MAX_ITEM'] = 6401112, 
    ['SERVER_GM_BAN'] = 6409020, 
    ['SERVER_LEVEL_EXP_LIMIT'] = 6403303, 
    ['SERVER_RPC_ACCESS_LIMIT'] = 6405500, 
    ['SERVER_RPC_ACCESS_LIMIT_FORMAL'] = 6405501, 
    ['SERVER_TIME_NOTIFY'] = 6409026, 
    ['SET_FIRE_SIGNAL'] = 6405712, 
    ['SHADOW'] = 6405908, 
    ['SHADOW2'] = 6405909, 
    ['SHARED_REFRESHED'] = 6405903, 
    ['SHARED_UNREFRESHED'] = 6405902, 
    ['SHERIFF_BATTLE_REMINDER'] = 6405804, 
    ['SHERIFF_BATTLE_REMINDER_QTE'] = 6405808, 
    ['SHERIFF_BATTLE_REMINDER_Round1'] = 6405934, 
    ['SHERIFF_BATTLE_REMINDER_Round2'] = 6405935, 
    ['SHERIFF_BATTLE_REMINDER_Round3'] = 6405936, 
    ['SHERIFF_BATTLE_REMINDER_Yingjiu'] = 6405814, 
    ['SHERIFF_BATTLE_REMINDER_Zhuiji'] = 6405805, 
    ['SHERIFF_LEVEL_NOT_ENOUGH'] = 6405800, 
    ['SHERIFF_RANDOM_LIMIT'] = 6405815, 
    ['SHERIFF_TASK_NUM_LIMITED'] = 6405802, 
    ['SHERIFF_TASK_REPEATED'] = 6405801, 
    ['SHOP_BUY_GOODS_SERVER_LIMIT'] = 6402500, 
    ['SHORT_CUT_SUCCESS'] = 6401066, 
    ['SHUT_DOWN'] = 6405722, 
    ['SKILLWHEEL_BLOCK_SKILL_MODUEL'] = 6441920, 
    ['SKILL_ACTION_UNLOCK_CHECK'] = 6402923, 
    ['SKILL_ACTION_UPGRADE_CHECK'] = 6402924, 
    ['SKILL_CONSUMELACK'] = 6400802, 
    ['SKILL_EQUIP_DISABLED_ERRO'] = 6402908, 
    ['SKILL_EQUIP_LOCKED_ERRO'] = 6402907, 
    ['SKILL_FELLOW_LV_CHECK'] = 6402912, 
    ['SKILL_FIGHTRES_NOT_ENOUGH'] = 6400807, 
    ['SKILL_HUD_AUTOFIGHT_OFF'] = 6402930, 
    ['SKILL_HUD_AUTOFIGHT_ON'] = 6402929, 
    ['SKILL_HUD_PRESET_EMPTY'] = 6402927, 
    ['SKILL_HUD_PRESET_UNLOCK'] = 6402928, 
    ['SKILL_ID_NOT_IN_AI_SKILL_DATA'] = 6403007, 
    ['SKILL_IN_COOL_DOWN'] = 6400804, 
    ['SKILL_IN_DISABLE'] = 6400805, 
    ['SKILL_IN_RELIVE_CONFIRM'] = 6400808, 
    ['SKILL_LVMAX_ERRO'] = 6402913, 
    ['SKILL_LVUP_ERRO'] = 6402909, 
    ['SKILL_MANAGE_CONSUME_LACK'] = 6402905, 
    ['SKILL_MANAGE_PLAN_RENAME'] = 6402903, 
    ['SKILL_MANAGE_PLAN_RENAME_LIMIT'] = 6402904, 
    ['SKILL_MANAGE_PLAN_SWITCH'] = 6402901, 
    ['SKILL_MANAGE_SKILL_SWITCH'] = 6402902, 
    ['SKILL_MANAGE_UNLOCK'] = 6402906, 
    ['SKILL_MENU_UNLOCK'] = 6402925, 
    ['SKILL_PRESET_CHECK_REPLACE'] = 6402931, 
    ['SKILL_PRESET_RENAME_ERRO'] = 6402914, 
    ['SKILL_PRESET_RENAME_OVER_ERRO'] = 6402915, 
    ['SKILL_PRESET_REPLACE'] = 6402918, 
    ['SKILL_PRESET_SAVE_ERRO'] = 6402916, 
    ['SKILL_PRESET_UNLOCK_FELLOW'] = 6402919, 
    ['SKILL_PRESET_UNLOCK_LV'] = 6402921, 
    ['SKILL_PRESET_UNLOCK_SEAL'] = 6402920, 
    ['SKILL_PRESET_USE_ERRO'] = 6402917, 
    ['SKILL_REPLACE_FAILED'] = 6400803, 
    ['SKILL_ROLEPLAY_SKILL_CHECK'] = 6402926, 
    ['SKILL_SLOT_CHECK_EMPTY'] = 6402922, 
    ['SKILL_SLOT_MAXLIMIT_ERRO'] = 6402911, 
    ['SKILL_SLOT_UNMATCH_ERRO'] = 6402910, 
    ['SKILL_TARGETLACK'] = 6400801, 
    ['SKN_SKILL01'] = 6442001, 
    ['SKN_SKILL02'] = 6442002, 
    ['SKN_SKILL03'] = 6442003, 
    ['SM_Derrick_Skill01'] = 6406277, 
    ['SM_Derrick_Skill02'] = 6406278, 
    ['SM_Derrick_Skill03'] = 6406279, 
    ['SM_Derrick_Skill04'] = 6406280, 
    ['SM_Derrick_Skill05'] = 6406281, 
    ['SM_Derrick_Skill06'] = 6406282, 
    ['SM_Merlin_Skill01'] = 6406283, 
    ['SM_Merlin_Skill02'] = 6406284, 
    ['SM_Merlin_Skill03'] = 6406285, 
    ['SM_Merlin_Skill04'] = 6406286, 
    ['SM_Merlin_Skill05'] = 6406287, 
    ['SM_Merlin_Skill06'] = 6406288, 
    ['SM_Xio_Skill01'] = 6406272, 
    ['SM_Xio_Skill02'] = 6406273, 
    ['SM_Xio_Skill03'] = 6406274, 
    ['SM_Xio_Skill04'] = 6406275, 
    ['SM_Xio_Skill05'] = 6406276, 
    ['SOCIALACTION_ACCEPT'] = 6440146, 
    ['SOCIALACTION_CASTER_OFFLINE'] = 6440151, 
    ['SOCIALACTION_COMMON_BLOCK'] = 6440155, 
    ['SOCIALACTION_CONDITON_NOT_FULFILL'] = 6440149, 
    ['SOCIALACTION_DISTANCE_TOO_FAR'] = 6440156, 
    ['SOCIALACTION_FAILURE_DISTANCE'] = 6440145, 
    ['SOCIALACTION_FAILURE_POSITION'] = 6440140, 
    ['SOCIALACTION_HANDLE_TIMEOUT'] = 6440152, 
    ['SOCIALACTION_LOCKED'] = 6440148, 
    ['SOCIALACTION_NOT_CASTING'] = 6440154, 
    ['SOCIALACTION_NOT_IN_SAME_SCENE'] = 6440150, 
    ['SOCIALACTION_NOT_MATCH'] = 6440153, 
    ['SOCIALACTION_NO_PLAYER'] = 6440141, 
    ['SOCIALACTION_NO_TARGET'] = 6440142, 
    ['SOCIALACTION_REFUSE'] = 6440143, 
    ['SOCIALACTION_REFUSE_CD'] = 6440144, 
    ['SOCIALACTION_TARGET_CONDITON_NOT_FULFILL'] = 6440147, 
    ['SOMEONE_ASIGN_GUILD'] = 6402055, 
    ['SOMEONE_CREATE_GUILD'] = 6402043, 
    ['SOMEONE_JOIN_GUILD'] = 6402044, 
    ['SOMEONE_TAKE_OVER_GUILD_ROLE'] = 6402056, 
    ['SOUND_TRACE_REMINDER_1'] = 6440707, 
    ['SOUND_TRACE_REMINDER_2'] = 6440706, 
    ['SOUND_TRACE_REMINDER_3'] = 6440705, 
    ['SPACE_ENTER_DUNGEON_FAILE_AFTER_MIGRATE'] = 6400205, 
    ['SPACE_ENTER_MAP_FAILE_AFTER_MIGRATE'] = 6400206, 
    ['SPACE_IN_TELEPORT_SEAT_QUEUE'] = 6400212, 
    ['SPACE_IS_IN_TEMPORARY_LEAVE'] = 6400214, 
    ['SPACE_MIGRATE_AFTER_MIGRATE_FAILE'] = 6409021, 
    ['SPACE_MIGRATE_DUNGEON_NOT_EXIST'] = 6400208, 
    ['SPACE_MIGRATE_FAILE_LOGIC_FULL'] = 6400207, 
    ['SPACE_MIGRATE_POST_ENTER_EXCEPTION'] = 6400210, 
    ['SPACE_MIGRATE_RETURN_DUNGEON_FAILED'] = 6400209, 
    ['SPACE_NOT_FIND_TARGET_LINE'] = 6400211, 
    ['SPACE_NOT_FIND_VALID_LINE'] = 6400204, 
    ['SPACE_REACH_ENTER_MAP_FAILE'] = 6400203, 
    ['SPACE_REACH_MAX_LINE_PLAYER_NUM'] = 6400201, 
    ['SPACE_REACH_MAX_LOGIC_PLAYER_NUM'] = 6400202, 
    ['SPACE_TARGET_WORLD_CANT_ARRIVE'] = 6400213, 
    ['SPIRITUAL_VISION_ONCD'] = 6409011, 
    ['SPIRITUAL_VISION_ONREVIVE'] = 6409012, 
    ['SPIRIT_NOT_ENOUGH'] = 6405603, 
    ['STAGE_AUTO_RESTART'] = 6406029, 
    ['STALL_BELOW_MIN_PRICE'] = 6403255, 
    ['STALL_BUY_COMPETITION_PREPARE'] = 6403226, 
    ['STALL_BUY_OWN_ITEM_FAIL'] = 6403219, 
    ['STALL_BUY_QUANTITY_SUCCESS'] = 6403225, 
    ['STALL_CAN_NOT_BUY_QUANTITY'] = 6403223, 
    ['STALL_CAN_NOT_GLOBAL_VIEW'] = 6403222, 
    ['STALL_CAN_NOT_SORT'] = 6403224, 
    ['STALL_CELL_UNLOCK_FAIL'] = 6403232, 
    ['STALL_CELL_UNLOCK_SUCCESS'] = 6403231, 
    ['STALL_FAVORITES_OVER_NUM_LIMIT'] = 6403215, 
    ['STALL_FEE_NOT_ENOUGH'] = 6403201, 
    ['STALL_FOLLOW_ITEM_NOT_FOUND'] = 6403212, 
    ['STALL_FOLLOW_ITEM_WITHDRAW'] = 6403211, 
    ['STALL_FORBID_PLAYER_BUY'] = 6403233, 
    ['STALL_FORBID_PLAYER_PUT_ITEM'] = 6403234, 
    ['STALL_FORBID_PLAYER_TAKE_MONEY'] = 6403235, 
    ['STALL_IN_BAN_TIME'] = 6403258, 
    ['STALL_ITEM_BOUND'] = 6403206, 
    ['STALL_ITEM_CAN_ONLY_BUY_CHEAPEST'] = 6403242, 
    ['STALL_ITEM_FOLLOWED'] = 6403213, 
    ['STALL_ITEM_FREEZE'] = 6403207, 
    ['STALL_ITEM_INVALID'] = 6403205, 
    ['STALL_ITEM_NOT_ENOUGH'] = 6403203, 
    ['STALL_ITEM_NUM_NOT_ENOUGH'] = 6403241, 
    ['STALL_ITEM_NUM_ZERO'] = 6403254, 
    ['STALL_ITEM_PRICE_EXCEED_LIMIT'] = 6403209, 
    ['STALL_ITEM_REC_PRICE_CHANGED'] = 6403210, 
    ['STALL_ITEM_RESELL_CHANGE_PRICE'] = 6403240, 
    ['STALL_ITEM_SELL_PRICE_MISMATCH'] = 6403208, 
    ['STALL_ITEM_SINGLE_ITEM_SALE_SWITCH_OFF'] = 6403239, 
    ['STALL_ITEM_SOLD_OUT'] = 6403216, 
    ['STALL_ITEM_SOLD_OUT_OR_CANCLED'] = 6403238, 
    ['STALL_ITEM_SOLD_OUT_REVIEW_OVER'] = 6403228, 
    ['STALL_ITEM_SOLD_OUT_WAIT_FOR_REVIEW'] = 6403227, 
    ['STALL_ITEM_WITHDRAW'] = 6403204, 
    ['STALL_LEVEL_LOWER_LIMIT'] = 6403214, 
    ['STALL_MAX_NUM_FAVOR'] = 6403256, 
    ['STALL_NO_WITHDRAW_REVENUE'] = 6403260, 
    ['STALL_PURCHASE_NOT_ENOUGH_UNBOUND_CASH'] = 6403221, 
    ['STALL_SEARCH_ITEM_FAIL'] = 6403230, 
    ['STALL_SELL_AGAIN_FAIL_OWING_TO_BAG_FULL'] = 6403236, 
    ['STALL_SELL_FAIL'] = 6403217, 
    ['STALL_SELL_NUM_FULL'] = 6403202, 
    ['STALL_SHARE_ITEM_INVALID'] = 6403229, 
    ['STALL_SWITCH_OFF'] = 6403218, 
    ['STALL_TAKE_MONEY_FAIL'] = 6403237, 
    ['STALL_UNAVAILABLE_STALL'] = 6403257, 
    ['STALL_WITHDRAW_FAIL'] = 6403220, 
    ['STALL_WITHDRAW_REVENUE'] = 6403259, 
    ['START_CANDIDATE_SYS_FRIEND'] = 6402066, 
    ['STATECONFLICT_COMMON'] = 6409025, 
    ['STATECONFLICT_COMMON_REMINDER'] = 6409034, 
    ['STROKE'] = 6400013, 
    ['SUBMITITEM_LACK'] = 6441802, 
    ['SWITCH_CLOSE_CUSTOM_TIP'] = 6401202, 
    ['SYSTEM_NOT_OPEN_YET'] = 6404005, 
    ['SYSTEM_TARGET_UNLOCK'] = 6440202, 
    ['SYSTEM_UNLOCK'] = 6440201, 
    ['TAKE_OVER_PRESIDENT_SYS_FRIEND'] = 6402069, 
    ['TARGET_ALREADY_IN_CHAMPION_TROOP'] = 6441924, 
    ['TARGET_IN_OTHER_GUILD'] = 6402020, 
    ['TARGET_REJECT_FOR_JOIN_CHAMPION_TROOP'] = 6441925, 
    ['TAROTTEAM_ACCEPT_APPLICATION'] = 6441613, 
    ['TAROTTEAM_ACCEPT_BE_ALLOWED'] = 6441635, 
    ['TAROTTEAM_ALL_ORDER_HAVE_TO_SET'] = 6441674, 
    ['TAROTTEAM_ALREADY_CREATED_NPC_TIPS'] = 6441667, 
    ['TAROTTEAM_APPLICATION_BE_REFUSE'] = 6441605, 
    ['TAROTTEAM_APPLICATION_EXPIRE'] = 6441611, 
    ['TAROTTEAM_APPLICATION_LIST_CLEAR'] = 6441636, 
    ['TAROTTEAM_APPLICATION_PASS'] = 6441604, 
    ['TAROTTEAM_APPLY_FAILDE_FOR_FULL'] = 6441655, 
    ['TAROTTEAM_APPLY_FAILED_FOR_BE_CAPTAIN'] = 6441637, 
    ['TAROTTEAM_APPLY_FAILED_FOR_BE_IN_TEAM'] = 6441668, 
    ['TAROTTEAM_APPLY_FAILED_FOR_IN_TEAM'] = 6441606, 
    ['TAROTTEAM_APPLY_FAILED_FOR_TARGET_BUILDING'] = 6441627, 
    ['TAROTTEAM_APPLY_FULL'] = 6441602, 
    ['TAROTTEAM_APPLY_IN_CD'] = 6441603, 
    ['TAROTTEAM_APPLY_LIST_FULL'] = 6441670, 
    ['TAROTTEAM_APPLY_SUCC'] = 6441600, 
    ['TAROTTEAM_BUILDING_TEAM'] = 6441612, 
    ['TAROTTEAM_BUILD_BUTTON_INTERCEPTION'] = 6441664, 
    ['TAROTTEAM_BUILD_CANNOT_CLOSE_GROUP_WINDOW'] = 6441673, 
    ['TAROTTEAM_BUILD_CANNOT_QUIT_SCREEN'] = 6441672, 
    ['TAROTTEAM_BUILD_FAILED_FOR_PLAYERS_FAR_AWAY'] = 6441640, 
    ['TAROTTEAM_BUILD_FAILED_FOR_PLAYERS_NOT_ENOUGH'] = 6441642, 
    ['TAROTTEAM_BUILD_FAILED_FOR_PLAYERS_NOT_FOLLOW'] = 6441641, 
    ['TAROTTEAM_BUILD_JUDGE_SET'] = 6441671, 
    ['TAROTTEAM_CANDEL_APPLICATION_SUCC'] = 6441601, 
    ['TAROTTEAM_CAPTAIN_IMPEACH_APPLY'] = 6441652, 
    ['TAROTTEAM_CAPTAIN_IMPEACH_IN_PROCESS'] = 6441676, 
    ['TAROTTEAM_CAPTAIN_IMPEACH_SUCC'] = 6441653, 
    ['TAROTTEAM_CHECK_SUCCESS_TEXT'] = 6441666, 
    ['TAROTTEAM_COMPLETION_CONTRACT'] = 6441656, 
    ['TAROTTEAM_CONFIRM_MEMBER_ORDER'] = 6441643, 
    ['TAROTTEAM_CREATE_TEAMLEADER'] = 6441661, 
    ['TAROTTEAM_CREATE_TEAM_BUSY'] = 6441621, 
    ['TAROTTEAM_CREATE_TEAM_SUCC'] = 6441619, 
    ['TAROTTEAM_DECLARATION_ERROR'] = 6441630, 
    ['TAROTTEAM_DECLARATION_MUST_NOT_EMPTY'] = 6441629, 
    ['TAROTTEAM_DISBAND_CHAT_CLUB'] = 6441609, 
    ['TAROTTEAM_DISBAND_TEAM_BUSY'] = 6441622, 
    ['TAROTTEAM_DISBAND_TEAM_SUCC'] = 6441616, 
    ['TAROTTEAM_FAILED_FOR_DISSOLVE_GROUP_CHAT'] = 6441639, 
    ['TAROTTEAM_FAILED_FOR_EXIT_GROUP_CHAT'] = 6441638, 
    ['TAROTTEAM_GET_ALL_AWARDS'] = 6441677, 
    ['TAROTTEAM_JOIN_CHAT_CLUB'] = 6441607, 
    ['TAROTTEAM_JOIN_TEAM_BUSY'] = 6441623, 
    ['TAROTTEAM_LEAVE_TEMP_TEAM_SUCC'] = 6441628, 
    ['TAROTTEAM_MODULED_LOCKED'] = 6441665, 
    ['TAROTTEAM_NAME_DUPLICATE'] = 6441632, 
    ['TAROTTEAM_NAME_ERROR'] = 6441633, 
    ['TAROTTEAM_NOT_FRIEND'] = 6441618, 
    ['TAROTTEAM_NOT_FRIEND_IN_TEAM'] = 6441675, 
    ['TAROTTEAM_ONLY_CAPTAIN_OPERATION'] = 6441644, 
    ['TAROTTEAM_ORIGIN_PLACE_SELECTION'] = 6441660, 
    ['TAROTTEAM_PLAYER_BUSY'] = 6441626, 
    ['TAROTTEAM_QUIT_CHAT_CLUB'] = 6441608, 
    ['TAROTTEAM_QUIT_FAILED_FOR_TARGET_BUILDING'] = 6441631, 
    ['TAROTTEAM_QUIT_TEAM_BUSY'] = 6441624, 
    ['TAROTTEAM_REJECT_APPLICATION'] = 6441614, 
    ['TAROTTEAM_SQUAD_TAG_SELECTION'] = 6441634, 
    ['TAROTTEAM_TARGET_ALREADY_IN_TEAM'] = 6441610, 
    ['TAROTTEAM_TARGET_TEAM_BE_DISBAND'] = 6441669, 
    ['TAROTTEAM_TEAMLEADER_ORDER'] = 6441662, 
    ['TAROTTEAM_TEAM_BUILD_FAILED_FOR_APPLYING'] = 6441654, 
    ['TAROTTEAM_TEAM_BUSY'] = 6441625, 
    ['TAROTTEAM_TEAM_CREATION_COMPLETE'] = 6441646, 
    ['TAROTTEAM_TEAM_DISBANDED_FOR_EXPIRE'] = 6441620, 
    ['TAROTTEAM_TEAM_IMPEACH_REPEAT'] = 6441659, 
    ['TAROTTEAM_TEAM_IS_FULL'] = 6441615, 
    ['TAROTTEAM_TEAM_MEMBER_ORDER_SUCC'] = 6441651, 
    ['TAROTTEAM_TEAM_NAME_EDIT_CD'] = 6441647, 
    ['TAROTTEAM_TEAM_NAME_INPUT'] = 6441648, 
    ['TAROTTEAM_TEAM_NAME_SUCC'] = 6441649, 
    ['TAROTTEAM_TEAM_SALARY_CANT_GET'] = 6441650, 
    ['TAROTTEAM_TEAM_SALARY_GET_SUCC'] = 6441658, 
    ['TAROTTEAM_TEAM_SALARY_REPEAT'] = 6441657, 
    ['TAROTTEAM_TOO_FEW_MEMBERS'] = 6441617, 
    ['TAROTTEAM_TRANSFER_CAPTAIN_TIPS'] = 6441663, 
    ['TAROTTEAM_WAIT_FOR_COMPLETION_CONTRACT'] = 6441645, 
    ['TASK_AUTO_TRACE_FAIL_TIPS'] = 6442063, 
    ['TASK_FINISHED'] = 6400904, 
    ['TASK_ITEM_NONE'] = 6441003, 
    ['TASK_ITEM_USE_INVALID'] = 6401067, 
    ['TASK_ITEM_VALID'] = 6401068, 
    ['TASK_ONNEWCHAPTER'] = 6400908, 
    ['TASK_OPENDAY'] = 6441001, 
    ['TASK_OPENTIME'] = 6441002, 
    ['TASK_RECEIVED'] = 6441000, 
    ['TEAM_ALREADY_APPLY'] = 6400359, 
    ['TEAM_ALREADY_APPLY_JOIN'] = 6400389, 
    ['TEAM_ALREADY_CAPTAIN'] = 6400335, 
    ['TEAM_ALREADY_INVITE'] = 6400355, 
    ['TEAM_ALREADY_REQ_COMBINE'] = 6400392, 
    ['TEAM_APPLICATION_ALREADY_IN_TEAM'] = 6400327, 
    ['TEAM_APPLICATION_CAPTAIN_CHANGE'] = 6400330, 
    ['TEAM_APPLICATION_INVALID'] = 6400328, 
    ['TEAM_APPLYCAPTAIN_SUCCESS'] = 6400322, 
    ['TEAM_APPLY_COUNTDOWN'] = 6400369, 
    ['TEAM_APPLY_FAIL_GROUP_LIMIT'] = 6400349, 
    ['TEAM_APPLY_JOIN_CD_ONEDAY'] = 6400391, 
    ['TEAM_APPLY_JOIN_REFUSE_CD'] = 6400390, 
    ['TEAM_APPLY_SUCCESS'] = 6400379, 
    ['TEAM_ASSISTANCE'] = 6402732, 
    ['TEAM_BAN_TOWERCLIMB'] = 6400371, 
    ['TEAM_BECOME_TEAM_LEADER'] = 6400306, 
    ['TEAM_BEREAKED'] = 6400316, 
    ['TEAM_CAN_NOT_FOLLOW'] = 6400351, 
    ['TEAM_CAN_NOT_GOTO_TARGET'] = 6400373, 
    ['TEAM_CAPTAIN_IN_PLANE'] = 6400362, 
    ['TEAM_CHANGE_DUNGEON_OPEN_FAILED'] = 6406026, 
    ['TEAM_COLLECT_CANCEL'] = 6400339, 
    ['TEAM_COLLECT_SUCCESS'] = 6400338, 
    ['TEAM_COMBINE_SUCCESS'] = 6400319, 
    ['TEAM_CONVENE_LIMIT'] = 6400388, 
    ['TEAM_CREATE'] = 6400376, 
    ['TEAM_ENTER_TEAM_FAILED'] = 6400304, 
    ['TEAM_EXIT_NOT_ALLOWED'] = 6405798, 
    ['TEAM_FOLLOW_FAILED_BRANCH'] = 6400341, 
    ['TEAM_FOLLOW_FAILED_LEADER'] = 6400310, 
    ['TEAM_FOLLOW_FAILED_LEADER_OFFLINE'] = 6400311, 
    ['TEAM_FOLLOW_FAILED_LEVEL_UNENABLE'] = 6400334, 
    ['TEAM_FOLLOW_FAILED_SCENE'] = 6400309, 
    ['TEAM_FULL'] = 6400317, 
    ['TEAM_FULL_CAN_NOT_MATCH'] = 6400374, 
    ['TEAM_FULL_NOT_COMBINE'] = 6400320, 
    ['TEAM_FULL_RECRUIT'] = 6400343, 
    ['TEAM_FULL_STOPMATCH'] = 6400326, 
    ['TEAM_INVALID_TARGET'] = 6400384, 
    ['TEAM_INVITE_ALREADY'] = 6400396, 
    ['TEAM_INVITE_CD'] = 6400357, 
    ['TEAM_INVITE_CD_ONEDAY'] = 6400358, 
    ['TEAM_INVITE_FAILED'] = 6400301, 
    ['TEAM_INVITE_REFUESED'] = 6400364, 
    ['TEAM_INVITE_SUCCESS'] = 6400378, 
    ['TEAM_IN_CAPTAIN_APPLY'] = 6400337, 
    ['TEAM_IN_SELF_TEAM_CAN_NOT_APPLY'] = 6442051, 
    ['TEAM_IN_TEAM_APPLY'] = 6442052, 
    ['TEAM_JOIN_ANIMATION_LOCKED'] = 6442061, 
    ['TEAM_JOIN_GROUP'] = 6400397, 
    ['TEAM_JOIN_LIMIT'] = 6400366, 
    ['TEAM_KICKED_OUT'] = 6400307, 
    ['TEAM_KICK_MEMBER_IN_BATTLE'] = 6400329, 
    ['TEAM_KICK_NOT_ALLOWED'] = 6405799, 
    ['TEAM_LEADER_APPLY_CD'] = 6400383, 
    ['TEAM_LEADER_TRANSFER_INFORMATION_FAILURE'] = 6400361, 
    ['TEAM_LIST_REFRESH_SUCCESS'] = 6400340, 
    ['TEAM_MATCHING'] = 6400387, 
    ['TEAM_MATCH_MEMBER_IN_DUNGEON'] = 6400333, 
    ['TEAM_MEMBER_CANCEL_NEXT_PVP_MATCH'] = 6405817, 
    ['TEAM_MEMBER_COUNT_MORE_THAN_NEED'] = 6400365, 
    ['TEAM_MEMBER_ENTER_TEAM'] = 6400303, 
    ['TEAM_MEMBER_MORE_THAN_NEED'] = 6400331, 
    ['TEAM_MERGE_FAILED_TIPS'] = 6442048, 
    ['TEAM_NAME_PLATE_LOCKED'] = 6442062, 
    ['TEAM_NOT_IN_BIGWORLD'] = 6400321, 
    ['TEAM_NOT_MATCH'] = 6400325, 
    ['TEAM_NO_MEMBER'] = 6400377, 
    ['TEAM_PATHFINDING_FALSE'] = 6400382, 
    ['TEAM_PLANE_CAN_NOT_FOLLOW'] = 6400375, 
    ['TEAM_PLAYER_ENTER_TEAM'] = 6400302, 
    ['TEAM_PLAYER_INVITE_UNHANDLED'] = 6400356, 
    ['TEAM_PLAYER_IN_DUNGEON'] = 6400315, 
    ['TEAM_PLAYER_IN_TEAM'] = 6400314, 
    ['TEAM_PLAYER_NOT_EXIST'] = 6400313, 
    ['TEAM_PLAYER_NOT_IN_TEAM'] = 6400318, 
    ['TEAM_PLAYER_STRANGER_INVITE'] = 6400342, 
    ['TEAM_POSITION_CHANGE'] = 6400347, 
    ['TEAM_PVP_NOT_OPEN'] = 6405753, 
    ['TEAM_RECRUIT_TIME_LIMIT'] = 6400344, 
    ['TEAM_REFUSE_APPLICATION'] = 6400308, 
    ['TEAM_REQ_COMBINE_CD_ONEDAY'] = 6400394, 
    ['TEAM_REQ_COMBINE_REFUSE_CD'] = 6400393, 
    ['TEAM_SELF_IN_DUNGEON'] = 6400323, 
    ['TEAM_SPEAK_FAIL_TARGET'] = 6400348, 
    ['TEAM_SPEAK_SUCCESS'] = 6400345, 
    ['TEAM_STOP_ALL_MEMBER_FOLLOW'] = 6400370, 
    ['TEAM_STOP_FOLLOW'] = 6400363, 
    ['TEAM_SUPPORT_JOIN_FAIL'] = 6400386, 
    ['TEAM_SUPPORT_JOIN_IN'] = 6400385, 
    ['TEAM_TARGET_CHANGE'] = 6400367, 
    ['TEAM_TARGET_CHANGE_INVALID'] = 6400368, 
    ['TEAM_TARGET_IN_DUNGEON'] = 6400332, 
    ['TEAM_TARGET_NOT_ALLOW_MATCH'] = 6400352, 
    ['TEAM_TARGET_NOT_MATCH'] = 6400324, 
    ['TEAM_TARGET_NOT_SELECTABLE'] = 6400305, 
    ['TEAM_TARGET_SET_CE_INPUT_LIMIT'] = 6400353, 
    ['TEAM_TARGET_SET_FAIL'] = 6400360, 
    ['TEAM_TARGET_UNAVALIABLE'] = 6400372, 
    ['TEAM_TARGET_ZHANLI_LIMIT_OVERSIZE'] = 6400354, 
    ['TEAM_TEAM_FULL'] = 6400312, 
    ['TEAM_TRANSFER_LEADER_FAIL_OFFLINE'] = 6400350, 
    ['TEAM_VOICE_BAN'] = 6400380, 
    ['TEAM_VOICE_OPEN'] = 6400381, 
    ['TEAM_ZHANLI_LIMIT'] = 6400336, 
    ['TELEPORTPOINT_BATTLESTATE_INVALID'] = 6401311, 
    ['TELEPORTPOINT_COSTMONEY_INVALID'] = 6401302, 
    ['TELEPORTPOINT_DIESTATE_INVALID'] = 6401312, 
    ['TELEPORTPOINT_INSIDERROR'] = 6401301, 
    ['TELEPORTPOINT_MONEY_NOTENOUGH'] = 6401303, 
    ['TELEPORTPOINT_PLAYERSTATE_INVALID'] = 6401305, 
    ['TELEPORTPOINT_SUCCESS'] = 6401306, 
    ['TELEPORTPOINT_TELEPORT_INVALID'] = 6401304, 
    ['TELEPORTPOINT_UNLOCK_FINISH'] = 6441903, 
    ['TELEPORT_PLAYER_BATTLEZONE'] = 6406003, 
    ['TEMP_INV_FULL_SELF_NOTICE'] = 6401047, 
    ['TEMP_INV_GET_HOLD_MAX_ITEM'] = 6401023, 
    ['TEMP_INV_GET_ITEM'] = 6401022, 
    ['TEMP_INV_ITEM_INVALID'] = 6401046, 
    ['TEST_AREACHANGE'] = 6400004, 
    ['TEST_CHALLENGE'] = 6400006, 
    ['TEST_COUNTDOWN'] = 6400007, 
    ['TEST_DUNGEON'] = 6400003, 
    ['TEST_DUNGEON_SUCCESS'] = 6400008, 
    ['TEST_FUNCTION_UNLOCK'] = 6400010, 
    ['TEST_LEVELUP_1'] = 6400011, 
    ['TEST_LEVELUP_2'] = 6400012, 
    ['TEST_LIGHT'] = 6400002, 
    ['TEST_MISSION'] = 6400005, 
    ['TEST_NORMAL'] = 6400001, 
    ['TEST_STAGE_SUCCESS'] = 6400009, 
    ['THREE_ITEM_CAP'] = 6401651, 
    ['TODO12'] = 6400703, 
    ['TOOLBAR_SLOT_INVALID'] = 6401016, 
    ['TOPUP_ITEM_NOT_EXIST'] = 6402602, 
    ['TOPUP_NOT_COMPLETE'] = 6402600, 
    ['TOPUP_NOT_ENABLE'] = 6402607, 
    ['TOPUP_PANEL_OPENED'] = 6402605, 
    ['TOPUP_PAYMENT_SUCCESS'] = 6402601, 
    ['TOPUP_TOO_FREQUENTLY'] = 6402606, 
    ['TOWERCLIMB_ADD_FELLOW_IN_BATTLE'] = 6404003, 
    ['TOWERCLIMB_ADD_FELLOW_LOCKED'] = 6404002, 
    ['TOWERCLIMB_BEGINING'] = 6406083, 
    ['TOWERCLIMB_IN_MULTI_TEAM'] = 6406085, 
    ['TOWERCLIMB_LEVEL_NOT_MATCH'] = 6406084, 
    ['TRIGGER_REFRESHED'] = 6405901, 
    ['TRIGGER_UNREFRESHED'] = 6405900, 
    ['TURNTABLE_REMINDER_1'] = 6440708, 
    ['TURNTABLE_REMINDER_2'] = 6440710, 
    ['Test_dz_desc1'] = 6406560, 
    ['Test_mxy_desc1'] = 6406550, 
    ['Test_mxy_desc2'] = 6406551, 
    ['Test_mxy_desc3'] = 6406552, 
    ['The_Moon1'] = 6406075, 
    ['The_Moon2'] = 6406076, 
    ['Triss_Curserose'] = 6406089, 
    ['UGC_FORCE_REFRESH_IN_CD'] = 6403251, 
    ['UGC_STALL_BUY_CNT_ZERO'] = 6403253, 
    ['UGC_STALL_BUY_ITEM_CLOSED_BY_GM'] = 6403248, 
    ['UGC_STALL_BUY_QUANTITY_SUCCESS'] = 6403243, 
    ['UGC_STALL_CLOSED_BY_GM'] = 6403246, 
    ['UGC_STALL_ITEM_BOUND'] = 6403244, 
    ['UGC_STALL_ITEM_SOLD_OUT'] = 6403245, 
    ['UGC_STALL_PUT_ITEM_CLOSED_BY_GM'] = 6403247, 
    ['UGC_STALL_SEARCH_IN_CD'] = 6403252, 
    ['UGC_STALL_TAKE_ITEM_CLOSED_BY_GM'] = 6403249, 
    ['UGC_STALL_TAKE_MONEY_CLOSED_BY_GM'] = 6403250, 
    ['ULTIMATE_SKILL_IN_COOL_DOWN'] = 6400806, 
    ['UNCONFIRMED_DUNGEON_OPEN_FAILED'] = 6406025, 
    ['UNTRADABLE_ITEMS_FULL'] = 6401007, 
    ['USE_AREA_LIMIT'] = 6401070, 
    ['USE_CHAOS_STATE_LIMIT'] = 6401018, 
    ['USE_COOL_DOWN'] = 6401013, 
    ['USE_DEAD_LIMIT'] = 6401017, 
    ['USE_FAILED_CLASS'] = 6401006, 
    ['USE_FAILED_LEVEL'] = 6401005, 
    ['USE_HP_LIMIT'] = 6401015, 
    ['USE_MAP_LIMIT'] = 6401014, 
    ['VERSION_TOO_OLD'] = 6400118, 
    ['VIEW_ROLE_ATTR_NOT_ONLIE'] = 6400501, 
    ['VIEW_ROLE_ATTR_TOO_FRENQUENCY'] = 6400502, 
    ['VOICE_RECORDER_NO_AUDIO_DATA_WARN'] = 6401649, 
    ['WAIT_FOR_CAPTAIN_RESTART'] = 6406023, 
    ['WANTED_END'] = 6405771, 
    ['WANTED_START'] = 6405772, 
    ['WARE_HOUSE_FORBID_STORE'] = 6401048, 
    ['WARE_HOUSE_STORE_FULL'] = 6401050, 
    ['WARE_HOUSE_STORE_HOLD_MAX'] = 6401049, 
    ['WARE_HOUSE_TAKE_FULL'] = 6401052, 
    ['WARE_HOUSE_TAKE_HOLD_MAX'] = 6401051, 
    ['WATERPIPE_REMINDER_1'] = 6440709, 
    ['WATERPIPE_WARNING_LEAVE'] = 6440701, 
    ['WATERPIPE_WARNING_UNRESET'] = 6440704, 
    ['WENXIANSHI_1'] = 6405812, 
    ['WITCH_WitchLie_Dice_DISTANCE'] = 6406172, 
    ['WITCH_WitchLie_Dice_PEOPLE'] = 6406174, 
    ['WITCH_WitchLie_Dice_WITCH'] = 6406173, 
    ['WORLD_BOSS_DEAD'] = 6403013, 
    ['WORLD_BOSS_END_COUNTDOWN'] = 6403012, 
    ['WORLD_BOSS_FINISH'] = 6403008, 
    ['WORLD_BOSS_FIRSTHIT_NOTICE'] = 6403014, 
    ['WORLD_BOSS_GROUP_SCORE_WARN'] = 6403011, 
    ['WORLD_BOSS_KILL_SUCCESS'] = 6403017, 
    ['WORLD_BOSS_LASTHIT_NOTICE'] = 6403015, 
    ['WORLD_BOSS_NEW_LINE_OPEN'] = 6403009, 
    ['WORLD_BOSS_PALYER_SCORE_WARN'] = 6403010, 
    ['WORLD_BOSS_SPAWNED'] = 6403000, 
    ['WORLD_BOSS_SPAWNED_EX'] = 6403001, 
    ['WORLD_BOSS_TEAM_NOT_IN_SAME_SPACE'] = 6403016, 
    ['WORLD_BOSS_WILL_LEAVE_BATTLE'] = 6403002, 
    ['WORLD_QUIZ_SUCCESS'] = 6403400, 
    ['WORLD_QUIZ_SUCCESS_EARLIEST'] = 6403401, 
    ['WORLD_QUIZ_SUCCESS_VOICE'] = 6403402, 
    ['WYZY_SHGJ_caution1'] = 6406603, 
    ['WYZY_SHGJ_remind1'] = 6406601, 
    ['WYZY_SHGJ_remind2'] = 6406602, 
    ['WYZY_SHGJ_remind3'] = 6406604, 
    ['WYZY_SHGJ_skill1'] = 6406590, 
    ['WYZY_SHGJ_skill10'] = 6406599, 
    ['WYZY_SHGJ_skill11'] = 6406600, 
    ['WYZY_SHGJ_skill2'] = 6406591, 
    ['WYZY_SHGJ_skill3'] = 6406592, 
    ['WYZY_SHGJ_skill4'] = 6406593, 
    ['WYZY_SHGJ_skill5'] = 6406594, 
    ['WYZY_SHGJ_skill6'] = 6406595, 
    ['WYZY_SHGJ_skill7'] = 6406596, 
    ['WYZY_SHGJ_skill8'] = 6406597, 
    ['WYZY_SHGJ_skill9'] = 6406598, 
    ['WYZY_XZQS_reminder1'] = 6406486, 
    ['WYZY_XZQS_reminder2'] = 6406487, 
    ['WYZY_XZQS_reminder3'] = 6406488, 
    ['WYZY_XZQS_reminder4'] = 6406489, 
    ['WYZY_XZQS_reminder5'] = 6406490, 
    ['WYZY_XZQS_reminder6'] = 6406491, 
    ['WYZY_XZQS_reminder7'] = 6406492, 
    ['WYZY_XZQS_reminder8'] = 6406493, 
    ['WYZY_XZQS_reminder9'] = 6406494, 
    ['WYZY_XZQS_title1'] = 6406481, 
    ['WYZY_XZQS_title2'] = 6406482, 
    ['WYZY_XZQS_title3'] = 6406483, 
    ['WYZY_XZQS_title4'] = 6406484, 
    ['WYZY_XZQS_title5'] = 6406485, 
    ['WYZY_YHXT_reminder1'] = 6406570, 
    ['WYZY_YHXT_reminder10'] = 6406579, 
    ['WYZY_YHXT_reminder2'] = 6406571, 
    ['WYZY_YHXT_reminder3'] = 6406572, 
    ['WYZY_YHXT_reminder4'] = 6406573, 
    ['WYZY_YHXT_reminder5'] = 6406574, 
    ['WYZY_YHXT_reminder6'] = 6406575, 
    ['WYZY_YHXT_reminder7'] = 6406576, 
    ['WYZY_YHXT_reminder8'] = 6406577, 
    ['WYZY_YHXT_reminder9'] = 6406578, 
    ['WYZY_YHXT_skill1'] = 6406580, 
    ['WYZY_YHXT_skill10'] = 6406589, 
    ['WYZY_YHXT_skill2'] = 6406581, 
    ['WYZY_YHXT_skill3'] = 6406582, 
    ['WYZY_YHXT_skill4'] = 6406583, 
    ['WYZY_YHXT_skill5'] = 6406584, 
    ['WYZY_YHXT_skill6'] = 6406585, 
    ['WYZY_YHXT_skill7'] = 6406586, 
    ['WYZY_YHXT_skill8'] = 6406587, 
    ['WYZY_YHXT_skill9'] = 6406588, 
    ['WYZY_liequan_reminder1'] = 6406461, 
    ['WYZY_liequan_reminder2'] = 6406462, 
    ['WYZY_liequan_reminder3'] = 6406463, 
    ['WYZY_liequan_reminder4'] = 6406464, 
    ['WYZY_liequan_reminder5'] = 6406465, 
    ['WYZY_liequan_title1'] = 6406456, 
    ['WYZY_liequan_title2'] = 6406457, 
    ['WYZY_liequan_title3'] = 6406458, 
    ['WYZY_liequan_title4'] = 6406459, 
    ['WYZY_liequan_title5'] = 6406460, 
    ['WYZY_reminder1'] = 6406441, 
    ['WYZY_reminder10'] = 6406450, 
    ['WYZY_reminder11'] = 6406451, 
    ['WYZY_reminder12'] = 6406452, 
    ['WYZY_reminder13'] = 6406453, 
    ['WYZY_reminder14'] = 6406454, 
    ['WYZY_reminder15'] = 6406455, 
    ['WYZY_reminder2'] = 6406442, 
    ['WYZY_reminder3'] = 6406443, 
    ['WYZY_reminder4'] = 6406444, 
    ['WYZY_reminder5'] = 6406445, 
    ['WYZY_reminder6'] = 6406446, 
    ['WYZY_reminder7'] = 6406447, 
    ['WYZY_reminder8'] = 6406448, 
    ['WYZY_reminder9'] = 6406449, 
    ['WYZY_reminder_test1'] = 6406561, 
    ['WYZY_reminder_test2'] = 6406562, 
    ['WYZY_reminder_test3'] = 6406563, 
    ['WYZY_reminder_test4'] = 6406564, 
    ['WYZY_reminder_test5'] = 6406565, 
    ['WYZY_shenhuaguanjia_reminder1'] = 6406471, 
    ['WYZY_shenhuaguanjia_reminder2'] = 6406472, 
    ['WYZY_shenhuaguanjia_reminder3'] = 6406473, 
    ['WYZY_shenhuaguanjia_reminder4'] = 6406474, 
    ['WYZY_shenhuaguanjia_reminder5'] = 6406475, 
    ['WYZY_shenhuaguanjia_title1'] = 6406466, 
    ['WYZY_shenhuaguanjia_title2'] = 6406467, 
    ['WYZY_shenhuaguanjia_title3'] = 6406468, 
    ['WYZY_shenhuaguanjia_title4'] = 6406469, 
    ['WYZY_shenhuaguanjia_title5'] = 6406470, 
    ['WYZY_zjfr_reminder17'] = 6406431, 
    ['WYZY_zjfr_reminder18'] = 6406432, 
    ['WYZY_zjfr_reminder19'] = 6406433, 
    ['WYZY_zjfr_reminder20'] = 6406434, 
    ['WYZY_zjfr_reminder21'] = 6406435, 
    ['WYZY_zjfr_reminder22'] = 6406436, 
    ['WYZY_zjfr_skill02_title'] = 6406429, 
    ['WYZY_zjfr_skill03_title'] = 6406430, 
    ['WYZY_zjfr_skill06_reminder1'] = 6406415, 
    ['WYZY_zjfr_skill06_reminder2'] = 6406416, 
    ['WYZY_zjfr_skill06_title'] = 6406423, 
    ['WYZY_zjfr_skill07_reminder1'] = 6406417, 
    ['WYZY_zjfr_skill07_title'] = 6406424, 
    ['WYZY_zjfr_skill08_reminder1'] = 6406418, 
    ['WYZY_zjfr_skill08_title'] = 6406425, 
    ['WYZY_zjfr_skill10_reminder1'] = 6406419, 
    ['WYZY_zjfr_skill10_title'] = 6406426, 
    ['WYZY_zjfr_skill13_reminder1'] = 6406421, 
    ['WYZY_zjfr_skill13_title'] = 6406427, 
    ['WYZY_zjfr_skill14_reminder1'] = 6406420, 
    ['WYZY_zjfr_skill14_reminder2'] = 6406422, 
    ['WYZY_zjfr_skill14_title'] = 6406428, 
    ['WorldBoss_Megose_skill01'] = 6406258, 
    ['WorldBoss_Megose_skill02'] = 6406259, 
    ['WorldBoss_Megose_skill03'] = 6406260, 
    ['WorldBoss_Megose_skill04'] = 6406261, 
    ['WorldBoss_Megose_skill05'] = 6406262, 
    ['WorldBoss_Megose_skill06'] = 6406263, 
    ['WorldBoss_Megose_skilldesc01'] = 6406264, 
    ['WorldBoss_Sherron_skill01'] = 6406265, 
    ['WorldBoss_Sherron_skill02'] = 6406266, 
    ['WorldBoss_Sherron_skill02desc'] = 6406271, 
    ['WorldBoss_Sherron_skill03'] = 6406267, 
    ['WorldBoss_Sherron_skill03desc'] = 6406269, 
    ['WorldBoss_Sherron_skill04'] = 6406268, 
    ['WorldBoss_Sherron_skill04desc'] = 6406270, 
    ['XINGXIANG_1'] = 6405941, 
    ['ZHANLI_CHANGE'] = 6407100, 
}

Enum.EReportType = {
    ['Feedback'] = 2, 
    ['Guild'] = 3, 
    ['PVP'] = 7, 
    ['PlayerReport'] = 1, 
    ['Redpacket'] = 4, 
    ['TarotTeam'] = 6, 
    ['TeaRoom'] = 5, 
}

Enum.ERoleMechanismConstData = {
    ['BLACK_DRAGON_SKILLID'] = 86025041, 
    ['FOOL_BLUE_BUFF'] = 82030103, 
    ['FOOL_BLUE_CARD_BUFF'] = 82030101, 
    ['FOOL_BLUE_SKILL'] = 86033020, 
    ['FOOL_CARD_LAPTIME'] = 2, 
    ['FOOL_ENERGY_POINT'] = 100, 
    ['FOOL_ENERGY_SKILL'] = 86033030, 
    ['FOOL_YELLOW_BUFF'] = 82030102, 
    ['FOOL_YELLOW_CARD_BUFF'] = 82030100, 
    ['FOOL_YELLOW_SKILL'] = 86033010, 
    ['INIT_UTOPIAN_STATE'] = 0, 
    ['LEAVE_SLEEP_NEED_HP_PERCENT'] = 0.2, 
    ['MYSTERIES_CLEAR_INTERVAL'] = 30, 
    ['SUN_DEAD_SUNLIGHT_VALUE'] = 0, 
    ['SUN_NEWROLE_SUNLIGHT_VALUE'] = 0, 
    ['SUN_REVIVE_SUNLIGHT_VALUE'] = 0, 
    ['UTOPIAN_FEATHER_SKILL_DISAPPEAR_TIME'] = 1, 
    ['UTOPIAN_REVIVE_SKILLID'] = 86025010, 
    ['UTOPIAN_SELF_REVIVE_SKILLID'] = 86025020, 
    ['WHITE_DRAGON_SKILLID'] = 86025031, 
}

Enum.ERolePlayConstIntData = {
    ['DEFAULT_SOCIAL_SELECT'] = 7320204, 
    ['DICEBATTLE_WAITTIME'] = 1, 
    ['DICE_PROP_TRANS_FORMULAID'] = 0, 
    ['DICE_REDICE_SKILLID'] = 80200101, 
    ['DIVINATION_CHAIR_TEMPLATE_ID'] = 48, 
    ['Divination_Table_DiceID'] = 9006, 
    ['IDENTITY_DEFAULT'] = 7321999, 
    ['JOKER_PLAY_BUBBLE_NUM'] = 3, 
    ['Joker_Hat_DiceID'] = 14, 
    ['Joker_Hat_SocialAction'] = 9005, 
    ['Joker_Juggle_DiceID'] = 12, 
    ['Joker_Juggle_SocialAction'] = 9003, 
    ['Joker_Rider_DiceID'] = 13, 
    ['Joker_Rider_SocialAction'] = 9004, 
    ['PROP_TRANS_RATE'] = 5, 
    ['REJECT_WITCH_CD'] = 600, 
    ['ROLEPLAY_FORTUNE_QUEST_TIME'] = 15, 
    ['ROLEPLAY_FORTUNE_SKILL_ID_ADD'] = 73221001, 
    ['ROLEPLAY_FORTUNE_SPAWN_SKILL_ID_ADD'] = 73221002, 
    ['ROLEPLAY_FORTUNE_UNSPAWN_DESK'] = 73221003, 
    ['ROLEPLAY_WITCH_INVITE_REJECT_TIME'] = 15, 
    ['ROLEPLAY_WITCH_SKILL_ID'] = 73221015, 
    ['ROLEPLAY_WITCH_SKILL_ID_ADD'] = 73221016, 
    ['ROLEPLAY_WITCH_TRAPED_MAX'] = 10, 
    ['ROLEPLAY_WITCH_UNSPAWN_CHAIR'] = 73221012, 
    ['SOCIAL_SKILL_COSTID'] = 2001000, 
    ['SPIRITUAL_POWER_LIMIT'] = 1500, 
    ['SPIRITUAL_POWER_RECOVER_ITEMID'] = 2000002, 
    ['SPIRITUAL_POWER_RECOVER_TIME'] = 3, 
    ['WITCH_CHAIR_LEAVE'] = 73221017, 
    ['WITCH_CHAIR_TEMPLATE_ID'] = 47, 
    ['WITCH_CONTROLLER_SOCIALACTION'] = 9009, 
    ['WITCH_DICE_HUG_BY_FEMALE'] = 9001, 
    ['WITCH_DICE_HUG_BY_MALE'] = 9002, 
    ['WITCH_ESCAPE'] = 73221014, 
    ['WITCH_HUG_BY_FEMALE'] = 9010, 
    ['WITCH_HUG_BY_MALE'] = 9011, 
    ['WITCH_INVITE'] = 73221016, 
    ['WITCH_INVITE_FAIL_TITLE'] = 5, 
    ['WITCH_KITE_DISTANCE'] = 400, 
    ['WITCH_KITE_HEIGHT'] = 300, 
    ['WITCH_KITE_HEIGHT_SECOND'] = 600, 
    ['WITCH_NORMAL_IDENTITY'] = 7321998, 
    ['WITCH_RELEASE'] = 73221013, 
    ['WITCH_TRAPED_SOCIALACTION'] = 9008, 
    ['WITCH_WitchLie_DiceID'] = 9007, 
    ['WITCH_WitchLie_Dice_DISTANCE'] = 1000, 
}

Enum.ERolePlayPropData = {
    ['Agile'] = 7320002, 
    ['Charm'] = 7320005, 
    ['Intelligence'] = 7320003, 
    ['Perception'] = 7320004, 
    ['Physique'] = 7320006, 
    ['Strength'] = 7320001, 
}

Enum.ESceneActorBehaviorType = {
    ['AlchemyTable'] = 5, 
    ['AutomaticFog'] = 14, 
    ['BigRockTreasure'] = 57, 
    ['Block'] = 11, 
    ['BlockOpen'] = 12, 
    ['BookCorridor'] = 70, 
    ['BrokenSteps'] = 62, 
    ['BuffTriggerClick'] = 36, 
    ['CageMonkey'] = 18, 
    ['CanFired'] = 78, 
    ['ConsecrationMonument'] = 6, 
    ['ConsecrationSpirit'] = 7, 
    ['CorruptTree'] = 64, 
    ['CuttableTree'] = 65, 
    ['DrinkWine'] = 69, 
    ['DropDark'] = 40, 
    ['DropItem'] = 53, 
    ['DropLight'] = 39, 
    ['DropMonster'] = 44, 
    ['DropSpider'] = 38, 
    ['EatSnack'] = 68, 
    ['EmptyBehaviorTemplate'] = 16, 
    ['EnvironmentTreasureBox'] = 71, 
    ['EstatePaint'] = 67, 
    ['EstatePortal'] = 59, 
    ['ExtraordinaryPortal'] = 73, 
    ['ExtraordinaryPortalUnlockTreasure'] = 74, 
    ['FateContract'] = 21, 
    ['FateContractEntry'] = 24, 
    ['Firework'] = 43, 
    ['FloatStone'] = 49, 
    ['Fountain'] = 37, 
    ['FractureTable'] = 9, 
    ['Furnace'] = 51, 
    ['GrowDecayPlant'] = 47, 
    ['GuilBless'] = 52, 
    ['HeightFogModifier'] = 19, 
    ['IceFieldNpc'] = 31, 
    ['InteractiveCollection'] = 30, 
    ['InteractiveDoor'] = 29, 
    ['InteractiveFog'] = 15, 
    ['InteractiveStreetlight'] = 32, 
    ['Machine'] = 50, 
    ['MovableBread'] = 23, 
    ['Newspaper'] = 66, 
    ['PaintingScratch'] = 58, 
    ['PaintingVideo'] = 56, 
    ['PlaceItem'] = 77, 
    ['PlanePortal'] = 27, 
    ['PlantSihouette'] = 54, 
    ['PosterTreasure'] = 34, 
    ['PotionTable'] = 25, 
    ['PowderKeg'] = 45, 
    ['PutBread'] = 20, 
    ['PuzzleRitual'] = 76, 
    ['SacrificialAltar'] = 42, 
    ['Safflower'] = 4, 
    ['SealRoomDark'] = 48, 
    ['SequenceLadder'] = 3, 
    ['ShadowMonster'] = 41, 
    ['SitChair'] = 1, 
    ['SitPianoChair'] = 2, 
    ['SitSwing'] = 8, 
    ['SmallRockTreasure'] = 46, 
    ['SpiderWeb'] = 55, 
    ['SpiritAnimal'] = 22, 
    ['SpiritRealm'] = 10, 
    ['StarGraphTreasure'] = 33, 
    ['TowerClimbPortal'] = 26, 
    ['TreasureBoxEye'] = 35, 
    ['TreasureBoxLock'] = 17, 
    ['TreasureBoxUnlock'] = 13, 
    ['Turntable'] = 63, 
    ['TurntablePuzzleTrigger'] = 61, 
    ['WaterPipePuzzleTrigger'] = 60, 
    ['Wonder'] = 75, 
    ['WonderCore'] = 72, 
    ['WorldPortal'] = 28, 
}

Enum.ESceneActorCondition = {
    ['ACTING_ROLE'] = 2, 
    ['HAVE_BUFF'] = 1, 
}

Enum.ESceneCustomTypeEnum = {
    ['Anim'] = 1001, 
    ['Component'] = 1002, 
    ['Scene'] = 1003, 
}

Enum.ESceneDisplayEnum = {
    ['BP_CustomRoleMap_BloodMoon'] = 1004, 
    ['BP_CustomRoleMap_Blue'] = 1009, 
    ['BP_CustomRoleMap_Day'] = 1005, 
    ['BP_CustomRoleMap_Dusk'] = 1006, 
    ['BP_CustomRoleMap_Foggy'] = 1007, 
    ['BP_CustomRoleMap_Night'] = 1008, 
    ['BP_CustomRoleMap_Pink'] = 1010, 
    ['BP_CustomRoleMap_White'] = 1011, 
    ['BP_CustomRoleMap_Yellow'] = 1012, 
    ['BP_SceneCustom_BloodMoon'] = 1024, 
    ['BP_SceneCustom_Day'] = 1025, 
    ['BP_SceneCustom_Dusk'] = 1026, 
    ['BP_SceneCustom_Foggy'] = 1027, 
    ['BP_SceneCustom_Night'] = 1028, 
    ['CreateRoleName'] = 1020, 
    ['CustomRoleMap_Day_Moveable'] = 1013, 
    ['DungeonSelect'] = 1016, 
    ['DungeonSettlement'] = 1015, 
    ['OtherPlayer'] = 1022, 
    ['PVPChampionDisplay'] = 1002, 
    ['RankDisplay'] = 1014, 
    ['SceneCustomSingle'] = 1000, 
    ['ShowRoom2'] = 1003, 
    ['SuitShow'] = 1021, 
    ['TarotTeamDisplay'] = 1019, 
    ['TeamDisplay'] = 1001, 
    ['UIRTCollectiblesBall'] = 1017, 
    ['UIRTMonster'] = 1018, 
    ['UIRTPendulum'] = 1023, 
}

Enum.EScheduleDailyTaskTypeData = {
    ['PVE'] = 3, 
    ['PVP'] = 2, 
    ['SOCIAL'] = 4, 
}

Enum.ESCPlayerShowMode = {
    ['ENEMY_FIRST_MODE'] = 3, 
    ['FRIENDLY_FIRST_MODE'] = 2, 
    ['SMART_MODE'] = 1, 
}

Enum.ESefirotCoreConstIntData = {
    ['HASH_VALUE'] = 100000, 
    ['MAX_LEVEL'] = 6, 
    ['MAX_RANK'] = 5, 
    ['SWITCH_COST'] = 0, 
    ['SWITCH_MONEY_ID'] = 2001000, 
}

Enum.ESequenceIntConstData = {
    ['SEQUENCE_FAILURE_BUFF01'] = 81009107, 
    ['SEQUENCE_FAILURE_BUFF02'] = 81009108, 
    ['SEQUENCE_SUCCESS_BUFF01'] = 81009106, 
}

Enum.ESettingConfigData = {
    ['ADAPT_CURVED_GLASS'] = 4013, 
    ['ADAPT_CURVED_OFFSET_MOBILE'] = 4015, 
    ['ADAPT_CURVED_OFFSET_PC'] = 4014, 
    ['ANTI_ALIASING_MODE_MOBILE'] = 4017, 
    ['ANTI_ALIASING_MODE_PC'] = 4016, 
    ['ANTI_ALIASING_QUALITY'] = 4004, 
    ['AOI_DISPLAY_MODE'] = 5003, 
    ['ATTACK_AUTO_NAVI'] = 7008, 
    ['BATTLE_CAMERA_CORRECT'] = 6006, 
    ['BATTLE_CAMERA_TURN_TO_LOCK'] = 6007, 
    ['BGM'] = 1003, 
    ['BGM_ISACTIVE'] = 1002, 
    ['CAMERA_FOLLOW'] = 6003, 
    ['CAMERA_SENSITIVITY'] = 6001, 
    ['CAMERA_SHAKE'] = 6010, 
    ['CHAT'] = 1007, 
    ['CHAT_ISACTIVE'] = 1006, 
    ['DLSS_QUALITY'] = 4021, 
    ['ENEMY_LOCK_LINE'] = 7017, 
    ['ENEMY_LOCK_POINT'] = 7009, 
    ['ENEMY_LOCK_WINDOW'] = 7010, 
    ['ENEMY_TARGET_LINE'] = 7016, 
    ['FAR_PLANE_DISTANCE'] = 6002, 
    ['FIXED_ROCKER'] = 7001, 
    ['FRAME_GENERATION'] = 4020, 
    ['FRIEND_LOCK_LINE'] = 7018, 
    ['FRIEND_LOCK_POINT'] = 7014, 
    ['FRIEND_LOCK_WINDOW'] = 7015, 
    ['FRIEND_NPC_HP_SHOW'] = 13004, 
    ['FSR_QUALITY'] = 4022, 
    ['FX_FILTER'] = 3003, 
    ['GIFT_KEYS'] = 20002, 
    ['GI_QUALITY'] = 4005, 
    ['GOURPMATE_HP_SHOW'] = 13002, 
    ['GREEN_PROTECTION'] = 7012, 
    ['GUILD_PROTECTION'] = 7013, 
    ['HIDE_ALL_PLAYER_SWITCH'] = 5004, 
    ['HIDE_SKILLBOARD'] = 7002, 
    ['HOSTILE_PROTECTION'] = 7006, 
    ['INTELLIGENT_CAMERA_SENSITIVITY'] = 6009, 
    ['LEVEL_PROTECTION'] = 7005, 
    ['LOCK_DISTANCE'] = 7011, 
    ['LOCK_METHOD'] = 6011, 
    ['NPC_MAXIMUM'] = 5002, 
    ['N_ATTACK_WHEN_CD'] = 7007, 
    ['OPEN_PROTECTION'] = 7004, 
    ['OPERATOR_MODE'] = 6008, 
    ['OTHER_PLAYER_FX_QUALITY'] = 3001, 
    ['OTHER_PLAYER_FX_STRENGTH'] = 3002, 
    ['OVERALL_SCALABILITY_LEVEL'] = 4001, 
    ['PLAYER_MAXIMUM'] = 5001, 
    ['PLAYER_MAXIMUM_PC'] = 5000, 
    ['PLAY_BACKGROUND_SFX'] = 1008, 
    ['PP_QUALITY'] = 4011, 
    ['PVE_CAMERA'] = 6004, 
    ['PVP_CAMERA'] = 6005, 
    ['REFLECTION_QUALITY'] = 4010, 
    ['RESOLUTION_SCALE_LEVEL'] = 4002, 
    ['SCREEN_RESOLUTION_ENUM'] = 4012, 
    ['SELF_HP_SHOW'] = 13001, 
    ['SETTING_FRIEND_TEAM_INVITE'] = 8002, 
    ['SETTING_NEARBY_TEAM_INVITE'] = 8003, 
    ['SETTING_SHOW_CHAT_HONORIFIC'] = 8010, 
    ['SETTING_SHOW_FASHION_LEVEL'] = 8008, 
    ['SETTING_SHOW_FRIEND_APPLY'] = 8006, 
    ['SETTING_SHOW_PRIVATE_CHAT'] = 8007, 
    ['SETTING_SHOW_RANK'] = 8009, 
    ['SETTING_SHOW_TEAM_APPLY'] = 8005, 
    ['SETTING_SHOW_TEAM_INVITE'] = 8004, 
    ['SETTING_STRANGER_TEAM_INVITE'] = 8001, 
    ['SHADING_QUALITY'] = 4007, 
    ['SHADOW_QUALITY'] = 4006, 
    ['SHOW_MEW'] = 2003, 
    ['SHOW_OTHER_HEADER'] = 2002, 
    ['SHOW_SELF_HEADER'] = 2000, 
    ['SHOW_SKILL_TAG'] = 7003, 
    ['SHOW_TEAMMATE_HEADER'] = 2001, 
    ['SKILL_RELEASE_CANCEL_DISTANCE'] = 7019, 
    ['SKILL_RELEASE_CANCEL_MODE'] = 7021, 
    ['SKILL_RELEASE_CANCEL_TIME'] = 7020, 
    ['SOUND_EFFECT'] = 1005, 
    ['SOUND_EFFECT_ISACTIVE'] = 1004, 
    ['SUPER_RESOLUTION_MODE'] = 4019, 
    ['SUPER_RESOLUTION_QUALITY'] = 4018, 
    ['SUPER_RESOLUTION_SAVED_MODE'] = 4024, 
    ['TEAMMATE_HP_SHOW'] = 13003, 
    ['TEXTURE_QUALITY'] = 4008, 
    ['TSR_QUALITY'] = 4023, 
    ['USER_TERMS_CONDITIONS'] = 20001, 
    ['VIEW_DISTANCE_QUALITY'] = 4003, 
    ['VISUAL_EFFECT_QUALITY'] = 4009, 
    ['VOLUME'] = 1001, 
    ['VOLUME_ISACTIVE'] = 1000, 
}

Enum.ESettingConstData = {
    ['ADAPT_CURVED_GLASS'] = 'ADAPT_CURVED_GLASS', 
    ['ADAPT_CURVED_OFFSET_MOBILE'] = 'ADAPT_CURVED_OFFSET_MOBILE', 
    ['ADAPT_CURVED_OFFSET_PC'] = 'ADAPT_CURVED_OFFSET_PC', 
    ['ANTI_ALIASING_MODE_MOBILE'] = 'ANTI_ALIASING_MODE_MOBILE', 
    ['ANTI_ALIASING_MODE_PC'] = 'ANTI_ALIASING_MODE_PC', 
    ['ANTI_ALIASING_QUALITY'] = 'ANTI_ALIASING_QUALITY', 
    ['AOI_DISPLAY_MODE'] = 'AOI_DISPLAY_MODE', 
    ['ATTACK_AUTO_NAVI'] = 'ATTACK_AUTO_NAVI', 
    ['BATTLE_CAMERA_CORRECT'] = 'BATTLE_CAMERA_CORRECT', 
    ['BATTLE_CAMERA_TURN_TO_LOCK'] = 'BATTLE_CAMERA_TURN_TO_LOCK', 
    ['BGM'] = 'BGM', 
    ['BGM_ISACTIVE'] = 'BGM_ISACTIVE', 
    ['CAMERA_FOLLOW'] = 'CAMERA_FOLLOW', 
    ['CAMERA_SENSITIVITY'] = 'CAMERA_SENSITIVITY', 
    ['CAMERA_SHAKE'] = 'CAMERA_SHAKE', 
    ['CHAT'] = 'CHAT', 
    ['CHAT_ISACTIVE'] = 'CHAT_ISACTIVE', 
    ['DLSS_QUALITY'] = 'DLSS_QUALITY', 
    ['ENEMY_LOCK_LINE'] = 'ENEMY_LOCK_LINE', 
    ['ENEMY_LOCK_POINT'] = 'ENEMY_LOCK_POINT', 
    ['ENEMY_LOCK_WINDOW'] = 'ENEMY_LOCK_WINDOW', 
    ['ENEMY_TARGET_LINE'] = 'ENEMY_TARGET_LINE', 
    ['FAR_PLANE_DISTANCE'] = 'FAR_PLANE_DISTANCE', 
    ['FIXED_ROCKER'] = 'FIXED_ROCKER', 
    ['FRAME_GENERATION'] = 'FRAME_GENERATION', 
    ['FRIEND_LOCK_LINE'] = 'FRIEND_LOCK_LINE', 
    ['FRIEND_LOCK_POINT'] = 'FRIEND_LOCK_POINT', 
    ['FRIEND_LOCK_WINDOW'] = 'FRIEND_LOCK_WINDOW', 
    ['FRIEND_NPC_HP_SHOW'] = 'FRIEND_NPC_HP_SHOW', 
    ['FSR_QUALITY'] = 'FSR_QUALITY', 
    ['FX_FILTER'] = 'FX_FILTER', 
    ['GIFT_KEYS'] = 'GIFT_KEYS', 
    ['GI_QUALITY'] = 'GI_QUALITY', 
    ['GOURPMATE_HP_SHOW'] = 'GOURPMATE_HP_SHOW', 
    ['GREEN_PROTECTION'] = 'GREEN_PROTECTION', 
    ['GUILD_PROTECTION'] = 'GUILD_PROTECTION', 
    ['HIDE_ALL_PLAYER_SWITCH'] = 'HIDE_ALL_PLAYER_SWITCH', 
    ['HIDE_SKILLBOARD'] = 'HIDE_SKILLBOARD', 
    ['HOSTILE_PROTECTION'] = 'HOSTILE_PROTECTION', 
    ['INTELLIGENT_CAMERA_SENSITIVITY'] = 'INTELLIGENT_CAMERA_SENSITIVITY', 
    ['LEVEL_PROTECTION'] = 'LEVEL_PROTECTION', 
    ['LOCK_DISTANCE'] = 'LOCK_DISTANCE', 
    ['LOCK_METHOD'] = 'LOCK_METHOD', 
    ['NPC_MAXIMUM'] = 'NPC_MAXIMUM', 
    ['N_ATTACK_WHEN_CD'] = 'N_ATTACK_WHEN_CD', 
    ['OPEN_PROTECTION'] = 'OPEN_PROTECTION', 
    ['OPERATOR_MODE'] = 'OPERATOR_MODE', 
    ['OTHER_PLAYER_FX_QUALITY'] = 'OTHER_PLAYER_FX_QUALITY', 
    ['OTHER_PLAYER_FX_STRENGTH'] = 'OTHER_PLAYER_FX_STRENGTH', 
    ['OVERALL_SCALABILITY_LEVEL'] = 'OVERALL_SCALABILITY_LEVEL', 
    ['PLAYER_MAXIMUM'] = 'PLAYER_MAXIMUM', 
    ['PLAYER_MAXIMUM_PC'] = 'PLAYER_MAXIMUM_PC', 
    ['PLAY_BACKGROUND_SFX'] = 'PLAY_BACKGROUND_SFX', 
    ['PP_QUALITY'] = 'PP_QUALITY', 
    ['PVE_CAMERA'] = 'PVE_CAMERA', 
    ['PVP_CAMERA'] = 'PVP_CAMERA', 
    ['REFLECTION_QUALITY'] = 'REFLECTION_QUALITY', 
    ['RESOLUTION_SCALE_LEVEL'] = 'RESOLUTION_SCALE_LEVEL', 
    ['SCREEN_RESOLUTION_ENUM'] = 'SCREEN_RESOLUTION_ENUM', 
    ['SELF_HP_SHOW'] = 'SELF_HP_SHOW', 
    ['SETTING_FRIEND_TEAM_INVITE'] = 'SETTING_FRIEND_TEAM_INVITE', 
    ['SETTING_NEARBY_TEAM_INVITE'] = 'SETTING_NEARBY_TEAM_INVITE', 
    ['SETTING_SHOW_CHAT_HONORIFIC'] = 'SETTING_SHOW_CHAT_HONORIFIC', 
    ['SETTING_SHOW_FASHION_LEVEL'] = 'SETTING_SHOW_FASHION_LEVEL', 
    ['SETTING_SHOW_FRIEND_APPLY'] = 'SETTING_SHOW_FRIEND_APPLY', 
    ['SETTING_SHOW_PRIVATE_CHAT'] = 'SETTING_SHOW_PRIVATE_CHAT', 
    ['SETTING_SHOW_RANK'] = 'SETTING_SHOW_RANK', 
    ['SETTING_SHOW_TEAM_APPLY'] = 'SETTING_SHOW_TEAM_APPLY', 
    ['SETTING_SHOW_TEAM_INVITE'] = 'SETTING_SHOW_TEAM_INVITE', 
    ['SETTING_STRANGER_TEAM_INVITE'] = 'SETTING_STRANGER_TEAM_INVITE', 
    ['SHADING_QUALITY'] = 'SHADING_QUALITY', 
    ['SHADOW_QUALITY'] = 'SHADOW_QUALITY', 
    ['SHOW_MEW'] = 'SHOW_MEW', 
    ['SHOW_OTHER_HEADER'] = 'SHOW_OTHER_HEADER', 
    ['SHOW_SELF_HEADER'] = 'SHOW_SELF_HEADER', 
    ['SHOW_SKILL_TAG'] = 'SHOW_SKILL_TAG', 
    ['SHOW_TEAMMATE_HEADER'] = 'SHOW_TEAMMATE_HEADER', 
    ['SKILL_RELEASE_CANCEL_DISTANCE'] = 'SKILL_RELEASE_CANCEL_DISTANCE', 
    ['SKILL_RELEASE_CANCEL_MODE'] = 'SKILL_RELEASE_CANCEL_MODE', 
    ['SKILL_RELEASE_CANCEL_TIME'] = 'SKILL_RELEASE_CANCEL_TIME', 
    ['SOUND_EFFECT'] = 'SOUND_EFFECT', 
    ['SOUND_EFFECT_ISACTIVE'] = 'SOUND_EFFECT_ISACTIVE', 
    ['SUPER_RESOLUTION_MODE'] = 'SUPER_RESOLUTION_MODE', 
    ['SUPER_RESOLUTION_QUALITY'] = 'SUPER_RESOLUTION_QUALITY', 
    ['SUPER_RESOLUTION_SAVED_MODE'] = 'SUPER_RESOLUTION_SAVED_MODE', 
    ['TEAMMATE_HP_SHOW'] = 'TEAMMATE_HP_SHOW', 
    ['TEXTURE_QUALITY'] = 'TEXTURE_QUALITY', 
    ['TSR_QUALITY'] = 'TSR_QUALITY', 
    ['USER_TERMS_CONDITIONS'] = 'USER_TERMS_CONDITIONS', 
    ['VIEW_DISTANCE_QUALITY'] = 'VIEW_DISTANCE_QUALITY', 
    ['VISUAL_EFFECT_QUALITY'] = 'VISUAL_EFFECT_QUALITY', 
    ['VOLUME'] = 'VOLUME', 
    ['VOLUME_ISACTIVE'] = 'VOLUME_ISACTIVE', 
}

Enum.ESettingDataEnum = {
    ['ANTI_ALIASING_MODE_MOBILE'] = 'ANTI_ALIASING_MODE_MOBILE', 
    ['ANTI_ALIASING_MODE_PC'] = 'ANTI_ALIASING_MODE_PC', 
    ['AOI_DISPLAY_MODE'] = 'AOI_DISPLAY_MODE', 
    ['AdaptCurvedOffsetMobile'] = 'ADAPT_CURVED_OFFSET_MOBILE', 
    ['AdaptCurvedOffsetPC'] = 'ADAPT_CURVED_OFFSET_PC', 
    ['AntiAliasingQuality'] = 'ANTI_ALIASING_QUALITY', 
    ['AttackAutoNav'] = 'ATTACK_AUTO_NAVI', 
    ['BGMIsActive'] = 'BGM_ISACTIVE', 
    ['BGMValue'] = 'BGM', 
    ['CAMERA_SHAKE'] = 'CAMERA_SHAKE', 
    ['CameraFollow'] = 'CAMERA_FOLLOW', 
    ['ChatIsActive'] = 'CHAT_ISACTIVE', 
    ['ChatValue'] = 'CHAT', 
    ['DLSS_QUALITY'] = 'DLSS_QUALITY', 
    ['ENEMY_LOCK_LINE'] = 'ENEMY_LOCK_LINE', 
    ['ENEMY_TARGET_LINE'] = 'ENEMY_TARGET_LINE', 
    ['EnemyLockPoint'] = 'ENEMY_LOCK_POINT', 
    ['EnemyLockWindow'] = 'ENEMY_LOCK_WINDOW', 
    ['FRAME_GENERATION'] = 'FRAME_GENERATION', 
    ['FRIEND_LOCK_LINE'] = 'FRIEND_LOCK_LINE', 
    ['FSR_QUALITY'] = 'FSR_QUALITY', 
    ['FX_FILTER'] = 'FX_FILTER', 
    ['FarPlane'] = 'FAR_PLANE_DISTANCE', 
    ['FixRocker'] = 'FIXED_ROCKER', 
    ['FriendInvitation'] = 'SETTING_FRIEND_TEAM_INVITE', 
    ['FriendLockPoint'] = 'FRIEND_LOCK_POINT', 
    ['FriendLockWindow'] = 'FRIEND_LOCK_WINDOW', 
    ['FriendlyNpcHpShow'] = 'FRIEND_NPC_HP_SHOW', 
    ['GiftKey'] = 'GIFT_KEYS', 
    ['GlobalIlluminationQuality'] = 'GI_QUALITY', 
    ['GreenProtection'] = 'GREEN_PROTECTION', 
    ['GroupmateHpShow'] = 'GOURPMATE_HP_SHOW', 
    ['GuildProtection'] = 'GUILD_PROTECTION', 
    ['HIDE_ALL_PLAYER_SWITCH'] = 'HIDE_ALL_PLAYER_SWITCH', 
    ['HideSkillBoard'] = 'HIDE_SKILLBOARD', 
    ['HostileProtection'] = 'HOSTILE_PROTECTION', 
    ['IntelligentCameraSensitivity'] = 'INTELLIGENT_CAMERA_SENSITIVITY', 
    ['LOCK_METHOD'] = 'LOCK_METHOD', 
    ['LevelProtection'] = 'LEVEL_PROTECTION', 
    ['NAttackCD'] = 'N_ATTACK_WHEN_CD', 
    ['NPCMaximum'] = 'NPC_MAXIMUM', 
    ['NearbyInvitation'] = 'SETTING_NEARBY_TEAM_INVITE', 
    ['OpenProtection'] = 'OPEN_PROTECTION', 
    ['OperatorMode'] = 'OPERATOR_MODE', 
    ['OtherEffectIntensity'] = 'OTHER_PLAYER_FX_STRENGTH', 
    ['OtherEffectQuality'] = 'OTHER_PLAYER_FX_QUALITY', 
    ['OtherHeader'] = 'SHOW_OTHER_HEADER', 
    ['OtherInvitation'] = 'SETTING_STRANGER_TEAM_INVITE', 
    ['OverallScalabilityLevel'] = 'OVERALL_SCALABILITY_LEVEL', 
    ['PVECamera'] = 'PVE_CAMERA', 
    ['PVPCamera'] = 'PVP_CAMERA', 
    ['PlayBackGroundSFX'] = 'PLAY_BACKGROUND_SFX', 
    ['PlayerMaximum'] = 'PLAYER_MAXIMUM', 
    ['PlayerMaximumPC'] = 'PLAYER_MAXIMUM_PC', 
    ['PostProcessingQuality'] = 'PP_QUALITY', 
    ['ReflectionQuality'] = 'REFLECTION_QUALITY', 
    ['ResolutionScaleValue'] = 'RESOLUTION_SCALE_LEVEL', 
    ['SETTING_SHOW_CHAT_HONORIFIC'] = 'SETTING_SHOW_CHAT_HONORIFIC', 
    ['SETTING_SHOW_FASHION_LEVEL'] = 'SETTING_SHOW_FASHION_LEVEL', 
    ['SETTING_SHOW_RANK'] = 'SETTING_SHOW_RANK', 
    ['SKILL_RELEASE_CANCEL_DISTANCE'] = 'SKILL_RELEASE_CANCEL_DISTANCE', 
    ['SKILL_RELEASE_CANCEL_MODE'] = 'SKILL_RELEASE_CANCEL_MODE', 
    ['SKILL_RELEASE_CANCEL_TIME'] = 'SKILL_RELEASE_CANCEL_TIME', 
    ['SUPER_RESOLUTION_MODE'] = 'SUPER_RESOLUTION_MODE', 
    ['SUPER_RESOLUTION_QUALITY'] = 'SUPER_RESOLUTION_QUALITY', 
    ['SUPER_RESOLUTION_SAVED_MODE'] = 'SUPER_RESOLUTION_SAVED_MODE', 
    ['ScreenResolutionEnum'] = 'SCREEN_RESOLUTION_ENUM', 
    ['SelfHPShow'] = 'SELF_HP_SHOW', 
    ['SelfHeader'] = 'SHOW_SELF_HEADER', 
    ['Sensitivity'] = 'CAMERA_SENSITIVITY', 
    ['ShadingQuality'] = 'SHADING_QUALITY', 
    ['ShadowQuality'] = 'SHADOW_QUALITY', 
    ['ShowFriendApply'] = 'SETTING_SHOW_FRIEND_APPLY', 
    ['ShowLockDistance'] = 'LOCK_DISTANCE', 
    ['ShowMew'] = 'SHOW_MEW', 
    ['ShowPrivateChat'] = 'SETTING_SHOW_PRIVATE_CHAT', 
    ['ShowProtocol'] = 'USER_TERMS_CONDITIONS', 
    ['ShowSkillTag'] = 'SHOW_SKILL_TAG', 
    ['ShowTeamApply'] = 'SETTING_SHOW_TEAM_APPLY', 
    ['ShowTeamInvite'] = 'SETTING_SHOW_TEAM_INVITE', 
    ['SoundEffectIsActive'] = 'SOUND_EFFECT_ISACTIVE', 
    ['SoundEffectValue'] = 'SOUND_EFFECT', 
    ['TSR_QUALITY'] = 'TSR_QUALITY', 
    ['TeamHeader'] = 'SHOW_TEAMMATE_HEADER', 
    ['TeammateHpShow'] = 'TEAMMATE_HP_SHOW', 
    ['TextureQuality'] = 'TEXTURE_QUALITY', 
    ['ViewDistanceQuality'] = 'VIEW_DISTANCE_QUALITY', 
    ['VisualEffectQuality'] = 'VISUAL_EFFECT_QUALITY', 
    ['VolumeIsActive'] = 'VOLUME_ISACTIVE', 
    ['VolumeValue'] = 'VOLUME', 
    ['bAdaptCurvedGlass'] = 'ADAPT_CURVED_GLASS', 
    ['bEnableBattleCameraCorrect'] = 'BATTLE_CAMERA_CORRECT', 
    ['bEnableBattleCameraTurnToLock'] = 'BATTLE_CAMERA_TURN_TO_LOCK', 
}

Enum.ESettingDefaultValueEnum = {
    [1000] = 1, 
    [1001] = 100, 
    [1002] = 1, 
    [1003] = 100, 
    [1004] = 1, 
    [1005] = 100, 
    [1006] = 1, 
    [1007] = 100, 
    [1008] = 0, 
    [2000] = 1, 
    [2001] = 1, 
    [2002] = 1, 
    [2003] = 1, 
    [3003] = 0, 
    [4013] = 0, 
    [4014] = 0, 
    [4015] = 0, 
    [5000] = 10, 
    [5001] = 10, 
    [5002] = 20, 
    [5003] = 1, 
    [5004] = 0, 
    [6001] = 100, 
    [6002] = 12, 
    [6003] = 0, 
    [6004] = 0, 
    [6005] = 0, 
    [6006] = 0, 
    [6007] = 0, 
    [6008] = 2, 
    [6009] = 100, 
    [6010] = 100, 
    [6011] = 1, 
    [7001] = 0, 
    [7002] = 1, 
    [7003] = 1, 
    [7004] = 1, 
    [7005] = 25, 
    [7006] = 1, 
    [7007] = 1, 
    [7008] = 1, 
    [7009] = 1, 
    [7010] = 1, 
    [7011] = 1, 
    [7012] = 1, 
    [7013] = 1, 
    [7014] = 1, 
    [7015] = 1, 
    [7016] = 0, 
    [7017] = 1, 
    [7018] = 1, 
    [7019] = 200, 
    [7020] = 200, 
    [7021] = 0, 
    [8001] = 1, 
    [8002] = 1, 
    [8003] = 1, 
    [8004] = 1, 
    [8005] = 1, 
    [8006] = 1, 
    [8007] = 1, 
    [8008] = 1, 
    [8009] = 1, 
    [8010] = 1, 
    [13001] = 1, 
    [13002] = 1, 
    [13003] = 1, 
    [13004] = 1, 
}

Enum.ESettingWidgetTypeData = {
    ['BUTTON'] = 6, 
    ['DROPDOWNLIST'] = 5, 
    ['IMAGE_OPTION'] = 9, 
    ['OPTION'] = 8, 
    ['SLIDER'] = 3, 
    ['SWITCH'] = 2, 
    ['SWITCH_DOUBLE'] = 7, 
    ['SWITCH_SLIDER'] = 4, 
    ['TITLE'] = 1, 
}

Enum.ESkillCustomizerFLoatConstData = {
    ['SKILL_PENALTY_CD_DEFAULT_a'] = 0.1, 
    ['SKILL_PENALTY_CD_DEFAULT_b'] = 1, 
    ['SKILL_PENALTY_CD_DEFAULT_c'] = 0.2, 
    ['SKILL_PENALTY_CD_SCHEME_a'] = 0.1, 
    ['SKILL_PENALTY_CD_SCHEME_b'] = 1, 
    ['SKILL_PENALTY_CD_SCHEME_d'] = 1, 
}

Enum.ESkillCustomizerIntConstData = {
    ['DISCOVERYSKILL_INVISIBLE'] = 1, 
    ['EXTRAORDINARYSKILL_INVISIBLE'] = 1, 
    ['EXTRAORDINARYSKILL_LEVEL_LOCK'] = 30, 
    ['FELLOWSKILL_INVISIBLE'] = 1, 
    ['IDENTITY_INVISIBLE'] = 1, 
    ['NORMALSKILL_INVISIBLE'] = 1, 
    ['PRESET_INVISIBLE'] = 1, 
    ['SCHEME_MAX'] = 10, 
    ['SWITCH_AUGUST'] = 1, 
}

Enum.ESkillTypeData = {
    ['DiscoverySkill'] = 4, 
    ['ExtraordinarySkill'] = 8, 
    ['FellowSkill'] = 7, 
    ['IdentityFightSkill'] = 5, 
    ['IdentityPlaySkill'] = 6, 
    ['NormalAttack'] = 1, 
    ['Other'] = 10, 
    ['SealedSkill_2D'] = 16, 
    ['SequenceSkill'] = 2, 
    ['SpecialSkill'] = 3, 
}

Enum.EStallConstIntData = {
    ['BID_CLOSE_ENTER_DELAY'] = 10, 
    ['BID_REFRESH_SECOND'] = 15, 
    ['BID_REWARD_MAIL_ID'] = 6260020, 
    ['EXCHANGE_HOUSE_MAX_SYSTEM_NUM'] = 1000, 
    ['EXCHANGE_HOUSE_PRICE_ADD'] = 110, 
    ['GM_RETURN_STALL_ITEM_MAIL'] = 20009, 
    ['MAX_SELL_RECORD_TIME'] = 30, 
    ['OVERTIME_OFFSHELF_MAIL'] = 20010, 
    ['PUT_STALL_ITEM_RANDOM_DELAY'] = 0, 
    ['STALL_BUY_COMPETITION_PREPARE_TIME'] = 180, 
    ['STALL_BUY_COMPETITION_ROLL_TIME'] = 5000, 
    ['STALL_DOUBLE_CHECK_PRICE'] = 500000, 
    ['STALL_FAVORITES_NUM_LIMIT'] = 30, 
    ['STALL_FEE_LIMIT_FREE_PRICE'] = 100000, 
    ['STALL_FEE_LIMIT_NOT_FREE_PRICE'] = 50000, 
    ['STALL_FEE_MONEY_TYPE'] = 2001000, 
    ['STALL_FEE_RATE'] = 1, 
    ['STALL_ITEM_FREEZE_TIME'] = 336, 
    ['STALL_LOWEST_LEVEL_MAX'] = 9999999, 
    ['STALL_LOWEST_LEVEL_MIN'] = 1, 
    ['STALL_MAX_NUM_FAVOR'] = 30, 
    ['STALL_MAX_NUM_PURCHASE'] = 1000, 
    ['STALL_MAX_RATIO_BULK'] = 400, 
    ['STALL_MIN_RATIO_BULK'] = 50, 
    ['STALL_NOTICE_END_TEST'] = 5, 
    ['STALL_OVERTIME_OFFSHELF_TIME'] = 7, 
    ['STALL_PRICE_UPPER_LIMIT'] = 99999999999, 
    ['STALL_PUBLICITY_TIME'] = 6, 
    ['STALL_RANDOM_MAX_VALUE'] = 125, 
    ['STALL_RANDOM_MIN_VALUE'] = 75, 
    ['STALL_REC_PRICE_CASH_MARKET'] = 1612004, 
    ['STALL_REC_PRICE_REFRESH_TIME'] = 10, 
    ['STALL_SELLING_TIME_CHARGE'] = 24, 
    ['STALL_SELLING_TIME_FREE'] = 24, 
    ['STALL_SYSTEM_BUY_WAIT_TIME_MAX'] = 5, 
    ['STALL_SYSTEM_BUY_WAIT_TIME_MIN'] = 3, 
    ['STALL_SYSTEM_CONTROL_PARAMETER_DIMENSION'] = 5, 
    ['STALL_TRANSACTION_RECORD_NUM_LIMIT'] = 20, 
    ['STALL_TRANSACTION_REVIEW_TIME'] = 24, 
    ['STALL_TRANSACTION_TAX_RATE'] = 10, 
    ['STALL_TURNOVER_CHECK_TIME'] = 2, 
    ['TRADE_DIRECT_AUDIT_TIME'] = 24, 
    ['TRADE_DIRECT_TAX'] = 2, 
    ['UGC_STALL_FEE_LIMIT'] = 50000, 
    ['UGC_STALL_FEE_MONEY_TYPE'] = 2001000, 
    ['UGC_STALL_FEE_RATE'] = 8, 
    ['UGC_STALL_TRANSACTION_TAX_RATE'] = 10, 
}

Enum.EStaminaConstData = {
    ['IN_BATTLE_DODGE_COST'] = 18, 
    ['IN_BATTLE_JUMP_COST'] = 15, 
    ['IN_BATTLE_RECOVER_TIME'] = 1, 
    ['IN_BATTLE_RECOVER_VALUE'] = 16, 
    ['OUT_BATTLE_DODGE_COST'] = 12, 
    ['OUT_BATTLE_JUMP_COST'] = 10, 
    ['OUT_BATTLE_RECOVER_TIME'] = 1, 
    ['OUT_BATTLE_RECOVER_VALUE'] = 16, 
    ['OUT_BATTLE_RIDE_DODGE_COST'] = 16, 
    ['OUT_BATTLE_SPRINT_COST'] = 2, 
    ['RecoverValueInDodgeLimitState'] = 8, 
    ['STAMINA_DISAPPEAR_TIME'] = 0.5, 
    ['STAMINA_THRESHOLD_ONE'] = 60, 
    ['STAMINA_THRESHOLD_TWO'] = 20, 
}

Enum.EStateConflictAction = {
    ['AutoFarming'] = 38, 
    ['AutoSkill'] = 39, 
    ['BossMechanism'] = 47, 
    ['CastCombatSkill'] = 7, 
    ['CommonInteractEnterArea'] = 42, 
    ['ContinuousInteract'] = 23, 
    ['Control'] = 22, 
    ['CutScene'] = 29, 
    ['DialogControl'] = 43, 
    ['Die'] = 21, 
    ['EnterHome'] = 32, 
    ['EquipChange'] = 26, 
    ['FashionSystem'] = 8, 
    ['Follow'] = 4, 
    ['GetoffMount'] = 49, 
    ['InBattle'] = 20, 
    ['InMount'] = 31, 
    ['InOtherMount'] = 51, 
    ['InPVEScene'] = 28, 
    ['InPVPScene'] = 27, 
    ['InVehicle'] = 13, 
    ['InWaterWalk'] = 34, 
    ['Interact'] = 19, 
    ['InteractSpell'] = 25, 
    ['Jump'] = 6, 
    ['Match'] = 10, 
    ['Morph'] = 16, 
    ['Move'] = 5, 
    ['Navigate'] = 18, 
    ['Observer'] = 41, 
    ['Photograph'] = 44, 
    ['QuestControl'] = 40, 
    ['Seat'] = 35, 
    ['SetAnimation'] = 36, 
    ['SetUpperAnimation'] = 37, 
    ['SocialActionSystem'] = 9, 
    ['StartDance'] = 48, 
    ['StrongControl'] = 45, 
    ['SummonControl'] = 24, 
    ['TarotTeamBuildingProcess'] = 30, 
    ['Teleport'] = 2, 
    ['TeleportSpell'] = 3, 
    ['UnStucking'] = 52, 
    ['UseItemSpell'] = 33, 
}

Enum.EStateConflictState = {
    ['AutoFarming'] = 38, 
    ['AutoSkill'] = 39, 
    ['BossMechanism'] = 47, 
    ['CastCombatSkill'] = 7, 
    ['ContinuousInteract'] = 23, 
    ['Control'] = 22, 
    ['CutScene'] = 29, 
    ['Dancing'] = 50, 
    ['DialogControl'] = 43, 
    ['Die'] = 21, 
    ['EnterHome'] = 32, 
    ['FashionSystem'] = 8, 
    ['Follow'] = 4, 
    ['InBattle'] = 20, 
    ['InMount'] = 31, 
    ['InOtherMount'] = 51, 
    ['InPVEScene'] = 28, 
    ['InPVPFighting'] = 49, 
    ['InPVPPrepare'] = 48, 
    ['InVehicle'] = 13, 
    ['InWaterWalk'] = 34, 
    ['InteractSpell'] = 25, 
    ['Jump'] = 6, 
    ['Match'] = 10, 
    ['Morph'] = 16, 
    ['Move'] = 5, 
    ['Navigate'] = 18, 
    ['Observer'] = 41, 
    ['Photograph'] = 44, 
    ['QuestControl'] = 40, 
    ['Seat'] = 35, 
    ['SetAnimation'] = 36, 
    ['SetUpperAnimation'] = 37, 
    ['SocialActionSystem'] = 9, 
    ['StrongControl'] = 45, 
    ['TarotTeamBuildingProcess'] = 30, 
    ['TeleportSpell'] = 3, 
    ['UnStucking'] = 52, 
    ['UseItemSpell'] = 33, 
}

Enum.EStatisticsConstData = {
    ['BUFF_SCALE_NUM'] = 0.5, 
    ['MIN_BATTLE_TIME_LEN'] = 0.1, 
}

Enum.ETagTypeConfigData = {
    ['AUDIO_TRACE'] = 118, 
    ['CITY_NAME'] = 1, 
    ['CUSTOM_TAG'] = 115, 
    ['ENTRANCE'] = 7, 
    ['EVENT_TRACE'] = 12, 
    ['EXIT'] = 8, 
    ['FIRSTSUBWAY'] = 11, 
    ['FUNCTION_NPC'] = 6, 
    ['GUILD_LEAGUE'] = 114, 
    ['GUILD_LEAGUE_ALTAR'] = 124, 
    ['GUILD_LEAGUE_BATTLE_TAG'] = 125, 
    ['GUILD_LEAGUE_MONSTER'] = 126, 
    ['LANDMARK_NAME'] = 3, 
    ['MINIMAP_THREE_COINCIDENCE'] = 122, 
    ['MINIMAP_TRACE'] = 117, 
    ['MINIMAP_TRACE_ORDINARY'] = 120, 
    ['MINIMAP_TWO_COINCIDENCE'] = 121, 
    ['NORMAL_TRACE'] = 112, 
    ['NPC'] = 5, 
    ['PLAYER_SELF'] = 100, 
    ['PLAYER_TEAMMATE'] = 101, 
    ['REGION_NAME'] = 2, 
    ['SPIRIT_TRACE'] = 116, 
    ['SPIRIT_VISION'] = 119, 
    ['STELE'] = 10, 
    ['SUBWAY'] = 4, 
    ['TASK_TRACE'] = 110, 
    ['TASK_TRACE_CIRCLE'] = 111, 
    ['TASK_WAITING_TAKE'] = 123, 
    ['TG_LANDMARK'] = 13, 
    ['WORLD_BOSS'] = 113, 
}

Enum.ETaskConstData = {
    ['FAIL_ACTION__FINISH_QUEST_AUTO'] = 2, 
    ['FAIL_ACTION__FINISH_QUEST_MANUAL'] = 3, 
    ['FAIL_ACTION__RESET_QUEST_AUTO'] = 0, 
    ['FAIL_ACTION__RESET_QUEST_MANUAL'] = 1, 
    ['FINISH_TYPE__AUTO'] = 0, 
    ['FINISH_TYPE__MANUAL'] = 2, 
    ['FINISH_TYPE__NPC_TALK'] = 1, 
    ['PLANE_ENTER_TYPE__AUTO'] = 1, 
    ['PLANE_ENTER_TYPE__INTERACTOR'] = 2, 
    ['PLANE_EXIT_TYPE__AUTO'] = 0, 
    ['PLANE_EXIT_TYPE__ENTER_PLANE_POS'] = 3, 
    ['PLANE_EXIT_TYPE__POSITION'] = 2, 
    ['PLANE_EXIT_TYPE__TREIGGER'] = 1, 
    ['QUEST_FAIL_TYPE__ALL_PLAYER_DEACTIVE'] = 503, 
    ['QUEST_FAIL_TYPE__NPC_DEAD'] = 502, 
    ['QUEST_FAIL_TYPE__PLAYER_DEAD'] = 501, 
    ['QUEST_FAIL_TYPE__PLAYER_LEAVE'] = 505, 
    ['QUEST_FAIL_TYPE__QTE_FAILED'] = 506, 
    ['QUEST_FAIL_TYPE__QUEST_TIMEOUT'] = 504, 
    ['RECEIVE_TYPE__AUTO'] = 0, 
    ['RECEIVE_TYPE__ENTER_SPACE'] = 5, 
    ['RECEIVE_TYPE__ITEM'] = 3, 
    ['RECEIVE_TYPE__MANUAL'] = 2, 
    ['RECEIVE_TYPE__NPC_TALK'] = 1, 
    ['RECEIVE_TYPE__QUEST_JUMP'] = 4, 
    ['TARGET_TYPE__BUY_ITEMS'] = 113, 
    ['TARGET_TYPE__CANE_SKILL_INSTANCEID'] = 157, 
    ['TARGET_TYPE__CANE_SKILL_POSITION'] = 159, 
    ['TARGET_TYPE__CANE_SKILL_TEMPLATEID'] = 158, 
    ['TARGET_TYPE__CAST_SKILL'] = 30, 
    ['TARGET_TYPE__CAST_SKILL_BY_INSTANCEID'] = 131, 
    ['TARGET_TYPE__CAST_SKILL_BY_POS'] = 133, 
    ['TARGET_TYPE__CAST_SKILL_BY_SKILLID'] = 134, 
    ['TARGET_TYPE__CAST_SKILL_BY_TEMPLATEID'] = 132, 
    ['TARGET_TYPE__CAST_SKILL_BY_TRIGGER'] = 135, 
    ['TARGET_TYPE__CHANGE_NAME_SUCC'] = 166, 
    ['TARGET_TYPE__CHAT'] = 17, 
    ['TARGET_TYPE__CHAT_NUMBER'] = 126, 
    ['TARGET_TYPE__CLOSE_LETTER'] = 33, 
    ['TARGET_TYPE__COLLECT_ITEM'] = 103, 
    ['TARGET_TYPE__COLLECT_ITEMS'] = 26, 
    ['TARGET_TYPE__COLLECT_ITEM_RANDOM'] = 104, 
    ['TARGET_TYPE__COLLECT_ITEM_TEMPLATEID'] = 152, 
    ['TARGET_TYPE__COMPLETE_DUNGEON'] = 136, 
    ['TARGET_TYPE__CUSTOM_EVENT'] = 29, 
    ['TARGET_TYPE__CUT_MONSTER_HP'] = 22, 
    ['TARGET_TYPE__CUT_MONSTER_HP_BY_INSTANCEID'] = 122, 
    ['TARGET_TYPE__CUT_MONSTER_HP_BY_TEMPLATEID'] = 123, 
    ['TARGET_TYPE__DOOR'] = 161, 
    ['TARGET_TYPE__DUNGEON_COMPLETE'] = 7, 
    ['TARGET_TYPE__DUNGEON_COMPLETE_IN_GUILD'] = 115, 
    ['TARGET_TYPE__DUNGEON_COMPLETE_WITH_GUILD'] = 18, 
    ['TARGET_TYPE__ENTER_SPACE_CLIENT_LOADED'] = 160, 
    ['TARGET_TYPE__EQUIP'] = 109, 
    ['TARGET_TYPE__EQUIP_EQUIPMENT'] = 24, 
    ['TARGET_TYPE__ESCORT'] = 6, 
    ['TARGET_TYPE__FIND_CORRECT_NPC'] = 28, 
    ['TARGET_TYPE__FIND_NPC'] = 140, 
    ['TARGET_TYPE__FINISHED_QUEST'] = 138, 
    ['TARGET_TYPE__FINISHED_RING'] = 137, 
    ['TARGET_TYPE__FINISH_ALL_SUBTARGETS'] = 153, 
    ['TARGET_TYPE__FINISH_EXTRAORDINARY_EVENT'] = 165, 
    ['TARGET_TYPE__FINISH_GUILD_MATERIAL_QUEST'] = 110, 
    ['TARGET_TYPE__FINISH_GUILD_MATERIAL_TASK'] = 25, 
    ['TARGET_TYPE__FINISH_QTE'] = 31, 
    ['TARGET_TYPE__FINISH_QTE_ASHBUTTON'] = 146, 
    ['TARGET_TYPE__FINISH_QTE_BUTTON'] = 148, 
    ['TARGET_TYPE__FINISH_QTE_SLIDE'] = 147, 
    ['TARGET_TYPE__FINISH_RING'] = 16, 
    ['TARGET_TYPE__FINISH_SUBTARGET_COUNT'] = 154, 
    ['TARGET_TYPE__FINISH_SUBTARGET_LIST'] = 155, 
    ['TARGET_TYPE__GET_ITEMS'] = 139, 
    ['TARGET_TYPE__GOTO_LEVELMAP'] = 23, 
    ['TARGET_TYPE__GO_TO_LEVELMAP'] = 108, 
    ['TARGET_TYPE__GO_TO_POS'] = 107, 
    ['TARGET_TYPE__GO_TO_TRIGGER'] = 106, 
    ['TARGET_TYPE__INTERACTION'] = 9, 
    ['TARGET_TYPE__INTERACTION_COMPLETED'] = 141, 
    ['TARGET_TYPE__INTERACTION_PLANE'] = 15, 
    ['TARGET_TYPE__INTERACTION_PLANE_ID'] = 142, 
    ['TARGET_TYPE__INTERACTION_PLANE_POS'] = 143, 
    ['TARGET_TYPE__ITEM_IN_BAG'] = 10, 
    ['TARGET_TYPE__ITEM_SUBMIT'] = 124, 
    ['TARGET_TYPE__ITEM_SUBMIT_BRANCH'] = 168, 
    ['TARGET_TYPE__ITEM_SUBMIT_DIRECT'] = 167, 
    ['TARGET_TYPE__ITEM_TO_BAG'] = 27, 
    ['TARGET_TYPE__JIGSAW_PUZZLE_SUCC'] = 37, 
    ['TARGET_TYPE__JIGSAW_PUZZLE_SUCCES'] = 119, 
    ['TARGET_TYPE__LETTER_CLOSE'] = 114, 
    ['TARGET_TYPE__LEVEL'] = 105, 
    ['TARGET_TYPE__LEVELUP'] = 8, 
    ['TARGET_TYPE__LOCATION'] = 5, 
    ['TARGET_TYPE__MISTERY_SUCC'] = 117, 
    ['TARGET_TYPE__MONSTER_KILL'] = 1, 
    ['TARGET_TYPE__MONSTER_KILL_COUNT'] = 2, 
    ['TARGET_TYPE__MONSTER_KILL_INSTANCEID'] = 127, 
    ['TARGET_TYPE__MONSTER_KILL_TEMPLATEID'] = 128, 
    ['TARGET_TYPE__NPCTALK_ENTER_PLANE'] = 116, 
    ['TARGET_TYPE__NPC_ASK_PRICE_SUCC'] = 120, 
    ['TARGET_TYPE__NPC_DIALOGUE'] = 144, 
    ['TARGET_TYPE__NPC_SEQUENCE_DIALOGUE'] = 169, 
    ['TARGET_TYPE__NPC_TALK'] = 3, 
    ['TARGET_TYPE__NULL'] = 112, 
    ['TARGET_TYPE__OPEN_SPIRITUALVISION'] = 151, 
    ['TARGET_TYPE__OPEN_UI_JUMP'] = 36, 
    ['TARGET_TYPE__PAINTING_STRATCH_SUCC'] = 164, 
    ['TARGET_TYPE__PLAY_CUTSCENE'] = 21, 
    ['TARGET_TYPE__PLAY_DIALOGUE'] = 20, 
    ['TARGET_TYPE__POTION_MAKE_SUCCESS'] = 163, 
    ['TARGET_TYPE__RECEIVE_CUSTOM_EVENT'] = 125, 
    ['TARGET_TYPE__SEQUENCE_SAN_CHECK_SUCC'] = 121, 
    ['TARGET_TYPE__SERVER_ENTER_PLANE'] = 999, 
    ['TARGET_TYPE__SHAPE_TRIGGER_LOCATION'] = 38, 
    ['TARGET_TYPE__SHOP_BUY'] = 13, 
    ['TARGET_TYPE__SIT_ANY_CHAIR'] = 156, 
    ['TARGET_TYPE__SIT_CHAIR'] = 149, 
    ['TARGET_TYPE__SIT_DOWN_CHAIR'] = 34, 
    ['TARGET_TYPE__START_CUTSCENE'] = 101, 
    ['TARGET_TYPE__START_DIALOGUE'] = 102, 
    ['TARGET_TYPE__START_SPIRITUALVISION'] = 32, 
    ['TARGET_TYPE__STAR_MISTERY_SUCC'] = 35, 
    ['TARGET_TYPE__SUBMIT_ITEM'] = 11, 
    ['TARGET_TYPE__TALK_ENTER_PLANE'] = 19, 
    ['TARGET_TYPE__TALK_NPC'] = 145, 
    ['TARGET_TYPE__UI_JUMP'] = 118, 
    ['TARGET_TYPE__USE_ITEM'] = 150, 
    ['TARGET_TYPE__USE_ITEM_AT_LOCATION'] = 12, 
    ['TARGET_TYPE__USE_ITEM_AT_POSITION'] = 129, 
    ['TARGET_TYPE__USE_ITEM_BY_INSTANCEID'] = 130, 
    ['TARGET_TYPE__WAITTIME'] = 111, 
    ['TARGET_TYPE__WAIT_TIME'] = 14, 
    ['TARGET_TYPE__WEAR_FASHION'] = 162, 
    ['TASK_FAIL_TYPE__ALL_PLAYER_DEACTIVE'] = 3, 
    ['TASK_FAIL_TYPE__NPC_DEAD'] = 2, 
    ['TASK_FAIL_TYPE__PLAYER_DEAD'] = 1, 
    ['TASK_FAIL_TYPE__PLAYER_LEAVE'] = 5, 
    ['TASK_FAIL_TYPE__QTE_FAILED'] = 6, 
    ['TASK_FAIL_TYPE__QUEST_TIMEOUT'] = 4, 
    ['TASK_MINITYPE__DUNGEON'] = 3, 
    ['TASK_MINITYPE__GUILDSINGLE'] = 5, 
    ['TASK_MINITYPE__MAIN'] = 0, 
    ['TASK_MINITYPE__REPEAT'] = 4, 
    ['TASK_MINITYPE__ROLEPLAYSHERIFFTASK'] = 6, 
    ['TASK_MINITYPE__ROLE_PLAY'] = 2, 
    ['TASK_MINITYPE__SUBTASK'] = 1, 
    ['TASK_MINITYPE__SYSTEM_TASK'] = 7, 
    ['TASK_STATUS__ABANDONED'] = 5, 
    ['TASK_STATUS__ACCEPTED'] = 2, 
    ['TASK_STATUS__ACCOMPLISHED'] = 3, 
    ['TASK_STATUS__AVAILABLE'] = 1, 
    ['TASK_STATUS__FAILED'] = 4, 
    ['TASK_STATUS__FINISHED'] = 6, 
    ['TASK_STATUS__FINISH_ACTION'] = 7, 
    ['TASK_TYPE__REPEATABLE'] = 1, 
    ['TASK_TYPE__REPEATABLE_WEEK'] = 2, 
    ['TASK_TYPE__UNREPEATABLE'] = 0, 
    ['TRIGGER_EVENT_TYPE__ADD_BUFF'] = 15, 
    ['TRIGGER_EVENT_TYPE__ADD_BUFF_BY_INSTANCEID'] = 132, 
    ['TRIGGER_EVENT_TYPE__ANIMATION_STOP'] = 164, 
    ['TRIGGER_EVENT_TYPE__CAMERA_END'] = 137, 
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKATINS'] = 19, 
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKATLOC'] = 18, 
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_INS'] = 139, 
    ['TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_POS'] = 138, 
    ['TRIGGER_EVENT_TYPE__CAMERA_MOVE'] = 136, 
    ['TRIGGER_EVENT_TYPE__CANE_SKILL_TO_INSTANCEID'] = 181, 
    ['TRIGGER_EVENT_TYPE__CANE_SKILL_TO_POSITION'] = 182, 
    ['TRIGGER_EVENT_TYPE__CHANGE_CLIMATE'] = 168, 
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE'] = 57, 
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_INSTANCEID'] = 126, 
    ['TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_TEMPLATEID'] = 125, 
    ['TRIGGER_EVENT_TYPE__CHANGE_INTERACTOR_STATE'] = 127, 
    ['TRIGGER_EVENT_TYPE__CHANGE_MINE_STATE'] = 88, 
    ['TRIGGER_EVENT_TYPE__CHANGE_SCENE_TIME'] = 49, 
    ['TRIGGER_EVENT_TYPE__CLEAR_QUEST_MARK_DEFINE'] = 146, 
    ['TRIGGER_EVENT_TYPE__CLEAR_TASK_MARK_DEFINE'] = 83, 
    ['TRIGGER_EVENT_TYPE__CLOSE_LETTER'] = 119, 
    ['TRIGGER_EVENT_TYPE__CLOSE_SPIRITUAL_VISION'] = 143, 
    ['TRIGGER_EVENT_TYPE__CREATE_BY_CLUSTER'] = 151, 
    ['TRIGGER_EVENT_TYPE__CREATE_BY_INSTANCEID'] = 149, 
    ['TRIGGER_EVENT_TYPE__DELETE_CAMERA_POST_PROCESS_EFFECT'] = 163, 
    ['TRIGGER_EVENT_TYPE__DELETE_ENTITY_EFFECT'] = 161, 
    ['TRIGGER_EVENT_TYPE__DELETE_SCENE_EFFECT'] = 159, 
    ['TRIGGER_EVENT_TYPE__DESTORY_COLLECT'] = 25, 
    ['TRIGGER_EVENT_TYPE__DESTROY_BY_CLUSTER'] = 152, 
    ['TRIGGER_EVENT_TYPE__DESTROY_BY_INSTANCEID'] = 150, 
    ['TRIGGER_EVENT_TYPE__EDIT_PLANE_CLIMATE'] = 20, 
    ['TRIGGER_EVENT_TYPE__END_CAMERA'] = 74, 
    ['TRIGGER_EVENT_TYPE__ENTER_PLANE'] = 179, 
    ['TRIGGER_EVENT_TYPE__ENTER_PLANE_POS'] = 141, 
    ['TRIGGER_EVENT_TYPE__ENTER_QUEST_CONTROL'] = 40, 
    ['TRIGGER_EVENT_TYPE__FLOWCHART_ACTION'] = 170, 
    ['TRIGGER_EVENT_TYPE__FLOW_CHART'] = 44, 
    ['TRIGGER_EVENT_TYPE__FOG'] = 70, 
    ['TRIGGER_EVENT_TYPE__FOG_CONTROL'] = 140, 
    ['TRIGGER_EVENT_TYPE__FUNCTION_UNLOCKED'] = 11, 
    ['TRIGGER_EVENT_TYPE__GAZE_BACK'] = 38, 
    ['TRIGGER_EVENT_TYPE__GAZE_TO_BACK'] = 131, 
    ['TRIGGER_EVENT_TYPE__GAZE_TO_LOC'] = 130, 
    ['TRIGGER_EVENT_TYPE__GAZE_TO_SPAWNER'] = 129, 
    ['TRIGGER_EVENT_TYPE__JUMP_UI'] = 23, 
    ['TRIGGER_EVENT_TYPE__KILL_INSTANCEID'] = 147, 
    ['TRIGGER_EVENT_TYPE__KILL_NPC'] = 56, 
    ['TRIGGER_EVENT_TYPE__KILL_TEMPLATEID'] = 148, 
    ['TRIGGER_EVENT_TYPE__LEAVE_QUEST_CONTROL'] = 41, 
    ['TRIGGER_EVENT_TYPE__MONSTER_BACK_TO_NPC'] = 58, 
    ['TRIGGER_EVENT_TYPE__MONSTER_TO_NPC'] = 157, 
    ['TRIGGER_EVENT_TYPE__MORPH_START'] = 21, 
    ['TRIGGER_EVENT_TYPE__MORPH_STOP'] = 22, 
    ['TRIGGER_EVENT_TYPE__MOVE_CAMERA'] = 69, 
    ['TRIGGER_EVENT_TYPE__MOVE_TO_POS'] = 31, 
    ['TRIGGER_EVENT_TYPE__MOVE_TO_TRIGGER'] = 32, 
    ['TRIGGER_EVENT_TYPE__NPC_TOMONSTER'] = 156, 
    ['TRIGGER_EVENT_TYPE__NPC_TO_MONSTER'] = 55, 
    ['TRIGGER_EVENT_TYPE__OPEN_CROSS_WORD_PUZZLE'] = 112, 
    ['TRIGGER_EVENT_TYPE__OPEN_JIGSAW_PUZZLE'] = 114, 
    ['TRIGGER_EVENT_TYPE__OPEN_LETTER'] = 113, 
    ['TRIGGER_EVENT_TYPE__OPEN_STAR_MISTERY'] = 85, 
    ['TRIGGER_EVENT_TYPE__PLANE_ENTER'] = 7, 
    ['TRIGGER_EVENT_TYPE__PLANE_LEAVE'] = 8, 
    ['TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_POS'] = 104, 
    ['TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_TRRIGER'] = 106, 
    ['TRIGGER_EVENT_TYPE__PLAY_2DAUDIO'] = 115, 
    ['TRIGGER_EVENT_TYPE__PLAY_3DAUDIO'] = 117, 
    ['TRIGGER_EVENT_TYPE__PLAY_ANIMATION'] = 39, 
    ['TRIGGER_EVENT_TYPE__PLAY_ANIMATION_BY_INSTANCEID'] = 101, 
    ['TRIGGER_EVENT_TYPE__PLAY_ASIDETALK'] = 16, 
    ['TRIGGER_EVENT_TYPE__PLAY_ASIDE_TALK'] = 103, 
    ['TRIGGER_EVENT_TYPE__PLAY_AUDIO'] = 86, 
    ['TRIGGER_EVENT_TYPE__PLAY_BLACKBG'] = 43, 
    ['TRIGGER_EVENT_TYPE__PLAY_BLACK_BG'] = 102, 
    ['TRIGGER_EVENT_TYPE__PLAY_BUBBLE'] = 42, 
    ['TRIGGER_EVENT_TYPE__PLAY_CAMERA_POST_PROCESS_EFFECT'] = 79, 
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTEREND'] = 60, 
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTERSTART'] = 59, 
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTER_END'] = 109, 
    ['TRIGGER_EVENT_TYPE__PLAY_CHAPTER_START'] = 108, 
    ['TRIGGER_EVENT_TYPE__PLAY_CUTSCENE'] = 14, 
    ['TRIGGER_EVENT_TYPE__PLAY_DIALOGUE'] = 9, 
    ['TRIGGER_EVENT_TYPE__PLAY_END_QUEST'] = 111, 
    ['TRIGGER_EVENT_TYPE__PLAY_ENTITY_EFFECT'] = 77, 
    ['TRIGGER_EVENT_TYPE__PLAY_MUSIC_SOUND'] = 47, 
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG'] = 171, 
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_NPC'] = 172, 
    ['TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_POSITION'] = 173, 
    ['TRIGGER_EVENT_TYPE__PLAY_SCENE_EFFECT'] = 75, 
    ['TRIGGER_EVENT_TYPE__PLAY_STARTTASK'] = 26, 
    ['TRIGGER_EVENT_TYPE__PLAY_START_QUEST'] = 110, 
    ['TRIGGER_EVENT_TYPE__QTE_ASHBUTTON'] = 66, 
    ['TRIGGER_EVENT_TYPE__QTE_BUTTON'] = 68, 
    ['TRIGGER_EVENT_TYPE__QTE_SLIDE'] = 67, 
    ['TRIGGER_EVENT_TYPE__REMOVE_BUFF'] = 17, 
    ['TRIGGER_EVENT_TYPE__REMOVE_BUFF_BY_INSTANCEID'] = 133, 
    ['TRIGGER_EVENT_TYPE__REMOVE_CAMERA_POST_PROCESS_EFFECT'] = 80, 
    ['TRIGGER_EVENT_TYPE__REMOVE_ENTITY_EFFECT'] = 78, 
    ['TRIGGER_EVENT_TYPE__REMOVE_QUEST_ITEM'] = 166, 
    ['TRIGGER_EVENT_TYPE__REMOVE_SCENE_EFFECT'] = 76, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ANG'] = 33, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ANGLE'] = 153, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_ENTITY'] = 34, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_INSTANCE'] = 154, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_LOC'] = 35, 
    ['TRIGGER_EVENT_TYPE__ROTATE_TO_POS'] = 155, 
    ['TRIGGER_EVENT_TYPE__SAY'] = 165, 
    ['TRIGGER_EVENT_TYPE__SEND_AI_AND_SPACE_MESSAGE'] = 72, 
    ['TRIGGER_EVENT_TYPE__SEND_AI_MESSAGE'] = 71, 
    ['TRIGGER_EVENT_TYPE__SEND_CUSTOM_EVENT'] = 62, 
    ['TRIGGER_EVENT_TYPE__SEND_FASHION'] = 178, 
    ['TRIGGER_EVENT_TYPE__SEND_MAIL'] = 167, 
    ['TRIGGER_EVENT_TYPE__SEND_QUEST_ITEM'] = 123, 
    ['TRIGGER_EVENT_TYPE__SEND_SPACE_MESSAGE'] = 73, 
    ['TRIGGER_EVENT_TYPE__SEND_TASK_ITEM'] = 12, 
    ['TRIGGER_EVENT_TYPE__SEND_TASK_ITEM_SINGLE'] = 64, 
    ['TRIGGER_EVENT_TYPE__SEND_TO_AI_AND_SPACE_MESSAGE'] = 121, 
    ['TRIGGER_EVENT_TYPE__SEND_TO_AI_MESSAGE'] = 120, 
    ['TRIGGER_EVENT_TYPE__SEND_TO_SPACE_MESSAGE'] = 122, 
    ['TRIGGER_EVENT_TYPE__SET_ANIMATION'] = 176, 
    ['TRIGGER_EVENT_TYPE__SET_QUEST_MARK_DEFINE'] = 145, 
    ['TRIGGER_EVENT_TYPE__SET_SCENE_TIME'] = 169, 
    ['TRIGGER_EVENT_TYPE__SET_TASK_MARK_DEFINE'] = 82, 
    ['TRIGGER_EVENT_TYPE__SPAWN_COLLECT'] = 24, 
    ['TRIGGER_EVENT_TYPE__SPAWN_DESTROY'] = 13, 
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE'] = 4, 
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE_POS'] = 65, 
    ['TRIGGER_EVENT_TYPE__SPAWN_MINE_SINGLE'] = 63, 
    ['TRIGGER_EVENT_TYPE__SPAWN_MONSTER'] = 1, 
    ['TRIGGER_EVENT_TYPE__SPAWN_NPC'] = 2, 
    ['TRIGGER_EVENT_TYPE__SPAWN_PLANE_ENTRANCE'] = 5, 
    ['TRIGGER_EVENT_TYPE__SPAWN_PLANE_EXIT'] = 6, 
    ['TRIGGER_EVENT_TYPE__START_BEHAVIOR_TREE'] = 45, 
    ['TRIGGER_EVENT_TYPE__START_BUBBLE'] = 144, 
    ['TRIGGER_EVENT_TYPE__START_CAMERA_POST_PROCESS_EFFECT'] = 162, 
    ['TRIGGER_EVENT_TYPE__START_CROSS_WORD_PUZZLE'] = 52, 
    ['TRIGGER_EVENT_TYPE__START_ENTITY_EFFECT'] = 160, 
    ['TRIGGER_EVENT_TYPE__START_JIGSAW_PUZZLE'] = 54, 
    ['TRIGGER_EVENT_TYPE__START_JUMP_UI'] = 124, 
    ['TRIGGER_EVENT_TYPE__START_LETTER'] = 53, 
    ['TRIGGER_EVENT_TYPE__START_MORPH'] = 134, 
    ['TRIGGER_EVENT_TYPE__START_MOVING_PATH'] = 50, 
    ['TRIGGER_EVENT_TYPE__START_PAINTING_STRATCH'] = 180, 
    ['TRIGGER_EVENT_TYPE__START_SCENE_EFFECT'] = 158, 
    ['TRIGGER_EVENT_TYPE__START_SPIRITUALVISION'] = 61, 
    ['TRIGGER_EVENT_TYPE__START_SPIRITUAL_VISION'] = 142, 
    ['TRIGGER_EVENT_TYPE__STOP_2DAUDIO'] = 116, 
    ['TRIGGER_EVENT_TYPE__STOP_3DAUDIO'] = 118, 
    ['TRIGGER_EVENT_TYPE__STOP_ANIMATION'] = 81, 
    ['TRIGGER_EVENT_TYPE__STOP_AUDIO'] = 87, 
    ['TRIGGER_EVENT_TYPE__STOP_AUTO_PATH'] = 175, 
    ['TRIGGER_EVENT_TYPE__STOP_BEHAVIOR_TREE'] = 46, 
    ['TRIGGER_EVENT_TYPE__STOP_CANE_SKILL'] = 183, 
    ['TRIGGER_EVENT_TYPE__STOP_MORPH'] = 135, 
    ['TRIGGER_EVENT_TYPE__STOP_MOVING'] = 51, 
    ['TRIGGER_EVENT_TYPE__STOP_MUSIC_SOUND'] = 48, 
    ['TRIGGER_EVENT_TYPE__STOP_NOCAMERA_DIALOG'] = 174, 
    ['TRIGGER_EVENT_TYPE__TELEPORT'] = 10, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_HOME'] = 177, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_POS'] = 29, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE'] = 84, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_POS'] = 105, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_TRIGGER'] = 107, 
    ['TRIGGER_EVENT_TYPE__TRANSMIT_TRIGGER'] = 30, 
    ['TRIGGER_EVENT_TYPE__TRIGGER_UPDATE'] = 3, 
    ['TRIGGER_EVENT_TYPE__UNLOCKED_FUNCTION'] = 128, 
    ['TRIGGER_EVENT_TYPE__WAIT_TIME'] = 28, 
    ['TRIGGER_ON_TYPE__RING_COMPLETED'] = 8, 
    ['TRIGGER_ON_TYPE__RING_REVEIVED'] = 7, 
    ['TRIGGER_ON_TYPE__TASK_COMPLETED'] = 2, 
    ['TRIGGER_ON_TYPE__TASK_FAILED'] = 5, 
    ['TRIGGER_ON_TYPE__TASK_FINISHED'] = 3, 
    ['TRIGGER_ON_TYPE__TASK_GIVEN_UP'] = 4, 
    ['TRIGGER_ON_TYPE__TASK_RECEIVED'] = 1, 
    ['TRIGGER_ON_TYPE__TASK_TARGET_COMPLETED'] = 6, 
}

Enum.EThirdCameraModes = {
    [100001] = true, 
    [200001] = true, 
    [200003] = true, 
    [200004] = true, 
}

Enum.ETipsData = {
    ['ACHIEVEMENTTAB_DESC'] = 6427208, 
    ['AGL'] = 6421010, 
    ['AP_PEN'] = 6421031, 
    ['AR_PEN'] = 6421030, 
    ['ATK'] = 6421012, 
    ['ATK_RANGE'] = 6421015, 
    ['ATK_SPEED'] = 6421014, 
    ['AUTO_CORRECT_CAMERA'] = 6423010, 
    ['BOSS_ENH'] = 6421107, 
    ['BOSS_ENH_PER'] = 6421108, 
    ['BOSS_RES'] = 6421117, 
    ['BOSS_RES_PER'] = 6421118, 
    ['BRIEF_AGL'] = 6421206, 
    ['BRIEF_BLOCK'] = 6421217, 
    ['BRIEF_CRITANTI'] = 6421218, 
    ['BRIEF_CRITDEF'] = 6421219, 
    ['BRIEF_DEF'] = 6421216, 
    ['BRIEF_ELEMENT'] = 6421211, 
    ['BRIEF_HP'] = 6421201, 
    ['BRIEF_INT'] = 6421205, 
    ['BRIEF_MATK'] = 6421212, 
    ['BRIEF_MCRIT'] = 6421214, 
    ['BRIEF_MCRITHURT'] = 6421215, 
    ['BRIEF_MPIERCE'] = 6421213, 
    ['BRIEF_PATK'] = 6421207, 
    ['BRIEF_PCRIT'] = 6421209, 
    ['BRIEF_PCRITHURT'] = 6421210, 
    ['BRIEF_PHY'] = 6421202, 
    ['BRIEF_PPIERCE'] = 6421208, 
    ['BRIEF_STR'] = 6421204, 
    ['BRIEF_WIL'] = 6421203, 
    ['BULK_PURCHASE'] = 6422004, 
    ['CAREER_DMG_UP'] = 6421028, 
    ['CAREER_DW'] = 6421042, 
    ['CASHMARKET_TIPS'] = 6420034, 
    ['CDB'] = 6421020, 
    ['CHAMPION_MATCH_DESC'] = 6427248, 
    ['CHATROOM_AMBIENT_SOUND'] = 6427231, 
    ['CHATROOM_ANNOUNMENT'] = 6427219, 
    ['CHATROOM_HELP_TIPS'] = 6427234, 
    ['CHATROOM_ORDER_MIC_GUIDE'] = 6427221, 
    ['CHATROOM_PARTY_TYPE_GUIDE'] = 6427253, 
    ['CHATROOM_USER_GUIDE'] = 6427220, 
    ['CHATROOM_USER_GUIDE_BEGINNER'] = 6427251, 
    ['CHATROOM_USER_GUIDE_CONCERT'] = 6427250, 
    ['CHATROOM_USER_GUIDE_COURT'] = 6427261, 
    ['CHATROOM_USER_GUIDE_DATE'] = 6427249, 
    ['CHATROOM_USER_GUIDE_READ'] = 6427252, 
    ['CHAT_ANOY_DESC'] = 6427223, 
    ['CHAT_LOUDSPEAKER'] = 6424001, 
    ['CHOOSE_FASHION_DESC'] = 6427216, 
    ['CLASS1_ENH'] = 6421099, 
    ['CLASS1_ENH_PER'] = 6421100, 
    ['CLASS1_RES'] = 6421109, 
    ['CLASS1_RES_PER'] = 6421110, 
    ['CLASS2_ENH'] = 6421101, 
    ['CLASS2_ENH_PER'] = 6421102, 
    ['CLASS2_RES'] = 6421111, 
    ['CLASS2_RES_PER'] = 6421112, 
    ['CLASS3_ENH'] = 6421103, 
    ['CLASS3_ENH_PER'] = 6421104, 
    ['CLASS3_RES'] = 6421113, 
    ['CLASS3_RES_PER'] = 6421114, 
    ['CLASS4_ENH'] = 6421105, 
    ['CLASS4_ENH_PER'] = 6421106, 
    ['CLASS4_RES'] = 6421115, 
    ['CLASS4_RES_PER'] = 6421116, 
    ['CLASS5_ENH'] = 6421119, 
    ['CLASS5_ENH_PER'] = 6421120, 
    ['CLASS5_RES'] = 6421123, 
    ['CLASS5_RES_PER'] = 6421124, 
    ['CLASS6_ENH'] = 6421121, 
    ['CLASS6_ENH_PER'] = 6421122, 
    ['CLASS6_RES'] = 6421125, 
    ['CLASS6_RES_PER'] = 6421126, 
    ['CLUB_ACTIVITY_DESC'] = 6427225, 
    ['CLUB_BUILDINGUP_DESC'] = 6427226, 
    ['CLUB_FUNDS_DESC'] = 6427224, 
    ['CLUB_POSITION_ADJUSTMENT'] = 6427229, 
    ['COLLECTIBLESMAIN_DESC'] = 6427207, 
    ['CRIT'] = 6421018, 
    ['CRIT_DMG_RES'] = 6421038, 
    ['CRIT_RES'] = 6421036, 
    ['CTL1_ENH'] = 6421072, 
    ['CTL1_HTR'] = 6421073, 
    ['CTL1_RES'] = 6421090, 
    ['CTL2_ENH'] = 6421074, 
    ['CTL2_HTR'] = 6421075, 
    ['CTL2_RES'] = 6421091, 
    ['CTL3_ENH'] = 6421076, 
    ['CTL3_HTR'] = 6421077, 
    ['CTL3_RES'] = 6421092, 
    ['CTL4_ENH'] = 6421078, 
    ['CTL4_HTR'] = 6421079, 
    ['CTL4_RES'] = 6421093, 
    ['CTL5_ENH'] = 6421080, 
    ['CTL5_HTR'] = 6421081, 
    ['CTL5_RES'] = 6421094, 
    ['CTL6_ENH'] = 6421082, 
    ['CTL6_HTR'] = 6421083, 
    ['CTL6_RES'] = 6421095, 
    ['CTL7_ENH'] = 6421084, 
    ['CTL7_HTR'] = 6421085, 
    ['CTL7_RES'] = 6421096, 
    ['CTL8_ENH'] = 6421086, 
    ['CTL8_HTR'] = 6421087, 
    ['CTL8_RES'] = 6421097, 
    ['CTL9_ENH'] = 6421088, 
    ['CTL9_HTR'] = 6421089, 
    ['CTL9_RES'] = 6421098, 
    ['DEF'] = 6421032, 
    ['DEFEQUIP_ADVANCE_TIPS'] = 6420029, 
    ['DEX'] = 6421016, 
    ['DIFFICULTY_TOWER_CLIMB_DESC'] = 6426013, 
    ['DMG_BONUS'] = 6421022, 
    ['DUNGEON_ASSIGNMENT'] = 6420010, 
    ['DUNGEON_AUCTION'] = 6420011, 
    ['DUNGEON_DAILY'] = 6420012, 
    ['DUNGEON_DAILY_NEWS'] = 6420032, 
    ['DUNGEON_TEST'] = 6420014, 
    ['DUNGEON_TOWER_CLIMB_DESC'] = 6426012, 
    ['DUNGEON_WEEKLY'] = 6420013, 
    ['ELE1_DB'] = 6421025, 
    ['ELE1_DMG'] = 6421024, 
    ['ELE1_DW'] = 6421041, 
    ['ELE1_IGN'] = 6421046, 
    ['ELE1_RES'] = 6421063, 
    ['ELE2_DMG'] = 6421047, 
    ['ELE2_IGN'] = 6421048, 
    ['ELE2_RES'] = 6421064, 
    ['ELE3_DMG'] = 6421049, 
    ['ELE3_IGN'] = 6421050, 
    ['ELE3_RES'] = 6421065, 
    ['ELE4_DMG'] = 6421051, 
    ['ELE4_IGN'] = 6421052, 
    ['ELE4_RES'] = 6421066, 
    ['ELE5_DMG'] = 6421053, 
    ['ELE5_IGN'] = 6421054, 
    ['ELE5_RES'] = 6421067, 
    ['ELE6_DMG'] = 6421055, 
    ['ELE6_IGN'] = 6421056, 
    ['ELE6_RES'] = 6421068, 
    ['ELE7_DMG'] = 6421057, 
    ['ELE7_IGN'] = 6421058, 
    ['ELE7_RES'] = 6421069, 
    ['ELE8_DMG'] = 6421059, 
    ['ELE8_IGN'] = 6421060, 
    ['ELE8_RES'] = 6421070, 
    ['ELE9_DMG'] = 6421061, 
    ['ELE9_IGN'] = 6421062, 
    ['ELE9_RES'] = 6421071, 
    ['ELEMENT_ELEMENT_TYPE_TIP'] = 6427239, 
    ['ELEMENT_LV_TIP'] = 6427237, 
    ['ELEMENT_MAIN_TIP'] = 6427236, 
    ['ELEMENT_REWARD_TIP'] = 6427244, 
    ['ELEMENT_TREE_TIP'] = 6427238, 
    ['ELE_DMG'] = 6421045, 
    ['ELE_RES'] = 6421040, 
    ['ENHANCE_CTRL'] = 6421027, 
    ['EQUIPENHANCE_TIPS'] = 6420027, 
    ['EQUIP_ADVANCE'] = 6420028, 
    ['EQUIP_BASICTIP'] = 6420035, 
    ['EQUIP_ENHANCESUITE'] = 6420036, 
    ['EQUIP_ENHANCESUITE_FULL'] = 6420039, 
    ['EQUIP_ENHANCESUITE_NONE'] = 6420038, 
    ['EQUIP_FIXED'] = 6420026, 
    ['EQUIP_REFORGE'] = 6420006, 
    ['EQUIP_WORD'] = 6420037, 
    ['EXPLORE_PROGRESS_DESC'] = 6427205, 
    ['FASHION_DYEING_TIP'] = 6427240, 
    ['FASHION_PACKET_DESC'] = 6427215, 
    ['FELLOW_CE_ADDITION_TIPS'] = 6420023, 
    ['FELLOW_STAR_UP_1PROP'] = 6420017, 
    ['FELLOW_STAR_UP_2PROP'] = 6420018, 
    ['FELLOW_SYSTEM_TIPS'] = 6420019, 
    ['FELLOW_TOWER_CLIMB_DESC'] = 6426011, 
    ['FRIEND'] = 6424002, 
    ['FRIEND_RANGE_SETTING_DESC'] = 6427217, 
    ['GACHA_SHOP_REFRESH_TIPS'] = 6420022, 
    ['GACHA_SHOP_TIPS'] = 6420021, 
    ['GACHA_SYSTEM_TIPS'] = 6420020, 
    ['GOD_WAY_ACT_RULE'] = 6426023, 
    ['GOLD_RED_PACKET_DESC'] = 6427214, 
    ['GUILD'] = 6424003, 
    ['GUILD_LEAGUE_ACTIVITY_DESC'] = 6427222, 
    ['GUILD_LEAGUE_MORALE_TIPS'] = 6427274, 
    ['GUILD_PARTY'] = 6424007, 
    ['GUILD_PERMISSION_DIRECTOR'] = 6427256, 
    ['GUILD_PERMISSION_GROUP'] = 6427257, 
    ['GUILD_PERMISSION_MEMBER'] = 6427259, 
    ['GUILD_PERMISSION_PRESIDENT'] = 6427254, 
    ['GUILD_PERMISSION_STAR'] = 6427258, 
    ['GUILD_PERMISSION_SUBSTITUTE'] = 6427260, 
    ['GUILD_PERMISSION_VICE_PRESIDENT'] = 6427255, 
    ['GUILD_PRACTICE'] = 6424006, 
    ['GUILD_REMATERIAL_DESC'] = 6426016, 
    ['GUILD_TARGET'] = 6424005, 
    ['HONORIFIC_DESC'] = 6427233, 
    ['HP'] = 6421001, 
    ['HP_REGEN'] = 6421002, 
    ['IGNORE_ELE1_RES'] = 6421026, 
    ['IMAGE_QUAITY'] = 6423002, 
    ['INT'] = 6421009, 
    ['LOGIN_CIRCUIT_BREAKER_TIP'] = 6427242, 
    ['LOGIN_QUEUE_TIP'] = 6427241, 
    ['LUCK'] = 6421011, 
    ['LUCKY_RED_PACKET_DESC'] = 6427211, 
    ['MAGIC_MIRROR'] = 6422001, 
    ['MAIL_COLLECT_DESC'] = 6420008, 
    ['MAKE_MEDICINE_MAIN_UI'] = 6427101, 
    ['MAKE_MEDICINE_OPERATION_UI'] = 6427102, 
    ['MANOR_CURRENCY_1'] = 6427272, 
    ['MANOR_CURRENCY_2'] = 6427273, 
    ['MANOR_TOTAL_SCORE'] = 6427271, 
    ['MATK'] = 6421013, 
    ['MATK_SPEED'] = 6421044, 
    ['MCDB'] = 6421021, 
    ['MCRIT'] = 6421019, 
    ['MDEX'] = 6421017, 
    ['MISS'] = 6421034, 
    ['MIX_POTION'] = 6426024, 
    ['M_CRIT_DMG_RES'] = 6421039, 
    ['M_CRIT_RES'] = 6421037, 
    ['M_DMG_BONUS'] = 6421023, 
    ['M_MISS'] = 6421035, 
    ['OPERATOR_MODE'] = 6423011, 
    ['PASSWORD_RED_PACKET_DESC'] = 6427213, 
    ['PHY'] = 6421006, 
    ['PLAYCARD_FASHION_TIPS'] = 6427218, 
    ['PLOT_RECAP_TIPS'] = 6427243, 
    ['PROTECTSCREEN_ASSOCIATION'] = 6423008, 
    ['PROTECTSCREEN_GREEN'] = 6423006, 
    ['PROTECTSCREEN_LEVEL'] = 6423005, 
    ['PROTECTSCREEN_NONHOSTILITY'] = 6423007, 
    ['PUZZLE_TIPS'] = 6426019, 
    ['PVP_12V12_DESC'] = 6427246, 
    ['PVP_3V3_Award'] = 6427245, 
    ['PVP_3V3_DESC'] = 6427203, 
    ['PVP_3V3_WIN_SCORE_DESC'] = 6427204, 
    ['PVP_CHAMPION_DESC'] = 6427247, 
    ['PVP_ENTRANCE_TIPS'] = 6427228, 
    ['PVP_PROTECT_SCORE_DESC'] = 6427202, 
    ['RANKINGLIST'] = 6426015, 
    ['RED_NAME_TIPS'] = 6426020, 
    ['RED_PACKET_SYSTEM_DESC'] = 6427210, 
    ['RES'] = 6421033, 
    ['ROLE_CREATE'] = 6420016, 
    ['ROLE_DISPLAY'] = 6420001, 
    ['ROLE_PLAY_DESC'] = 6427003, 
    ['ROLE_PLAY_IDENTITY_DESC'] = 6427004, 
    ['ROLE_PLAY_SECURITY_DESC'] = 6427206, 
    ['ROLE_PLAY_SKILL_DESC'] = 6427005, 
    ['ROLE_SCORE'] = 6420009, 
    ['ROLE_SELECT'] = 6420015, 
    ['RUN_SPEED'] = 6421004, 
    ['SCHEDULE_DESC'] = 6426018, 
    ['SCREEN_NPC_NUM'] = 6423004, 
    ['SCREEN_PLAYER_NUM'] = 6423003, 
    ['SEALED_CE_TIPS'] = 6420031, 
    ['SEALED_REFINE_LUCK_TIPS'] = 6420046, 
    ['SEALED_REFINE_TIPS'] = 6420045, 
    ['SEALED_RESPON_TIPS_DOWN'] = 6420043, 
    ['SEALED_RESPON_TIPS_UP'] = 6420044, 
    ['SEALED_SYSTEM_CE_TIPS'] = 6420041, 
    ['SEALED_SYSTEM_RESPON_TIPS'] = 6420042, 
    ['SEALED_SYSTEM_TIPS'] = 6420024, 
    ['SEALED_TIPS'] = 6420040, 
    ['SECRET_WORD_RED_PACKET_DESC'] = 6427212, 
    ['SEFIROT_CORE_TIPS'] = 6420025, 
    ['SEQUENCE_PROMOTION'] = 6426017, 
    ['SEQUENCE_PROMOTION2'] = 6426021, 
    ['SEQUENCE_PROMOTION3'] = 6426022, 
    ['SERVER_LEVEL'] = 6425005, 
    ['SERVER_LEVEL_WITHOUT_SEQUENCE'] = 6427209, 
    ['SETTING'] = 6423001, 
    ['SHOPPINGMALL'] = 6422002, 
    ['SKILL_CE_TIPS'] = 6420047, 
    ['SKILL_CUSTOMIZER'] = 6420002, 
    ['SKILL_DESC_COMMON1'] = 6426007, 
    ['SKILL_DESC_PRO1_SKILL1'] = 6426001, 
    ['SKILL_DESC_PRO1_SKILL2'] = 6426002, 
    ['SKILL_DESC_PRO1_SKILL3'] = 6426004, 
    ['SKILL_DESC_PRO1_SKILL4'] = 6426005, 
    ['SKILL_DESC_PRO2_SKILL1'] = 6426006, 
    ['SKILL_FELLOW_TIPS'] = 6420049, 
    ['SKILL_PRESET_TIPS'] = 6420048, 
    ['SPECIES_DMG_UP'] = 6421029, 
    ['SPECIES_DW'] = 6421043, 
    ['SPRINT_SPEED'] = 6421005, 
    ['STAR_MYSTERY_TIPS'] = 6420033, 
    ['STR'] = 6421008, 
    ['TAROT_TEAM_HELP_TIPS'] = 6427235, 
    ['TASK'] = 6420005, 
    ['TEAM'] = 6420007, 
    ['TEAM_GROUP'] = 6424004, 
    ['TEAM_SUPPORT_TIPS'] = 6427227, 
    ['TIPS_3V3'] = 6420003, 
    ['TITLE_DESC'] = 6427232, 
    ['TOPUP'] = 6422003, 
    ['TURN_TO_TARGET_WHEN_LOCK'] = 6423009, 
    ['WALK_SPEED'] = 6421003, 
    ['WANTED_DEGREE'] = 6425001, 
    ['WIL'] = 6421007, 
    ['WORLD_BOSS_DESC'] = 6427201, 
    ['WORLD_PVP_FIGHT_TIPS'] = 6427230, 
    ['WORLD_QUIZ'] = 6426014, 
}

Enum.ETriggerTypeData = {
    ['ACCEPT_TASK'] = 74, 
    ['ACHIEVEMENT_FINISHED'] = 57, 
    ['ACTIVE_TASK'] = 6, 
    ['ARBITRATOR_TRIAL'] = 36, 
    ['AUTO_SKILL'] = 45, 
    ['BODY_ENFORCE'] = 25, 
    ['BODY_ENHANCE_SUIT_COUNT'] = 27, 
    ['BREAK_SEALED_COUNT'] = 15, 
    ['CHAT'] = 4, 
    ['CHAT_DETAIL'] = 49, 
    ['CHECK_DUNGEON_BUFF'] = 79, 
    ['CLASS'] = 34, 
    ['COMPLETE_INTERACTOR_EXPLORE'] = 72, 
    ['COMPLETE_TASK'] = 5, 
    ['DESTINY_POINTS'] = 24, 
    ['DUNGEON_COMPLETE'] = 2, 
    ['DUNGEON_TYPE_COMPLETE'] = 21, 
    ['EQUIPPED_DANGER_SEALED_COUNT'] = 14, 
    ['EQUIPPED_SEALED_COUNT'] = 12, 
    ['EQUIP_QUALITY_EQUIPMENT'] = 18, 
    ['EXPLORE_BOX_COUNT'] = 42, 
    ['EXPLORE_PUZZLE'] = 44, 
    ['EXPLORE_SOUL_COUNT'] = 41, 
    ['EXPLORE_STELE_LEVEL'] = 40, 
    ['EXPLORE_STELE_UNLOCK'] = 39, 
    ['EXPLORE_TYPE_COMPLETE'] = 43, 
    ['FIRST_LEVEL_AREA_EXPLORATION_PROGRESS'] = 75, 
    ['FRIEND_COUNT'] = 37, 
    ['GAME_SEASON'] = 81, 
    ['GAME_SEASON_VERSION'] = 82, 
    ['GAME_SEASON_YEAR'] = 80, 
    ['GUILD_SHOP_LEVEL'] = 33, 
    ['GUILD_SIGN'] = 23, 
    ['GUILD_STUDY_COUNT'] = 26, 
    ['HAS_BUFF'] = 73, 
    ['INSTANCE_STATE_BEHAVIOR'] = 76, 
    ['INTERACTOR_TASK_ACTIVE'] = 61, 
    ['INTERACT_CON_SUCCESS'] = 50, 
    ['INTERACT_NPC'] = 47, 
    ['IS_RING_ACCEPTED'] = 7, 
    ['IS_RING_FINISHED'] = 8, 
    ['ITEM_GET'] = 54, 
    ['ITEM_USE'] = 53, 
    ['JOIN_GUILD'] = 20, 
    ['JOIN_TEAM_ARENA'] = 58, 
    ['LEVELUP'] = 3, 
    ['MONSTER_KILL'] = 55, 
    ['NPC_CUT_PRICE_EXCEED'] = 69, 
    ['NPC_CUT_PRICE_INSUFFICIENT'] = 70, 
    ['PARTNER_BREAK'] = 11, 
    ['PARTNER_COUNT'] = 9, 
    ['PARTNER_LEVEL'] = 10, 
    ['PERSISTENT_GAMEPLAY_MAP'] = 62, 
    ['PLAYER_KILL'] = 56, 
    ['REENFORCE_EQUIP_COUNT'] = 35, 
    ['REENFORCE_SEALED_COUNT'] = 13, 
    ['ROLEPLAY_ATTR_LEVEL'] = 31, 
    ['ROLEPLAY_IDENTITY_LEVEL'] = 28, 
    ['ROLEPLAY_TALENT_NODE_LEVEL'] = 29, 
    ['SEALED_RESONANCE'] = 16, 
    ['SEALED_UPGRADE'] = 17, 
    ['SEQUENCE_DIGEST'] = 38, 
    ['SEQUENCE_STAGE'] = 52, 
    ['SHOP_SOLDOUT'] = 32, 
    ['SKILL_LEVEL_COUNT'] = 22, 
    ['SOLVE_PUZZLE_COUNT'] = 19, 
    ['SPIRITUAL_VISION'] = 63, 
    ['STATISTIC'] = 1, 
    ['SWITCH_SKILL'] = 46, 
    ['TAROTTEAM_MEMBER_COUNT'] = 77, 
    ['TAROTTEAM_MEMBER_COUNT_EX'] = 78, 
    ['TEAM_ARENA_COMMON_BEAR'] = 68, 
    ['TEAM_ARENA_COMMON_DAMAGE'] = 67, 
    ['TEAM_ARENA_COMMON_ENTER'] = 64, 
    ['TEAM_ARENA_COMMON_KILL'] = 66, 
    ['TEAM_ARENA_COMMON_WIN'] = 65, 
    ['TEAM_ARENA_KILL_PLAYER'] = 60, 
    ['TEAM_MEMBER_COUNT'] = 71, 
    ['WIN_TEAM_ARENA'] = 59, 
    ['WITCH_INVITE'] = 51, 
    ['WORLD_CHANNEL_QUIZ'] = 30, 
}

Enum.ETurntablePuzzleItemType = {
    ['Empty'] = 0, 
    ['Flower'] = 1, 
    ['Other'] = 2, 
    ['Reset'] = 3, 
    ['Turntable'] = 4, 
    ['TurntableCrossArea'] = 5, 
}

Enum.EUIAudioEvent = {
    ['Play_UI_Battle_Lock'] = 'Play_UI_Battle_Lock', 
    ['Play_UI_Card_Draw'] = 'Play_UI_Card_Draw', 
    ['Play_UI_Card_Flip'] = 'Play_UI_Card_Flip', 
    ['Play_UI_Card_Legend'] = 'Play_UI_Card_Legend', 
    ['Play_UI_Card_Myth'] = 'Play_UI_Card_Myth', 
    ['Play_UI_Card_Rare'] = 'Play_UI_Card_Rare', 
    ['Play_UI_Card_Summarize'] = 'Play_UI_Card_Summarize', 
    ['Play_UI_Character_Chosen_Arbiter'] = 'Play_UI_Character_Chosen_Arbiter', 
    ['Play_UI_Character_Chosen_Fool'] = 'Play_UI_Character_Chosen_Fool', 
    ['Play_UI_Character_Chosen_Sun'] = 'Play_UI_Character_Chosen_Sun', 
    ['Play_UI_Character_Chosen_Visionary'] = 'Play_UI_Character_Chosen_Visionary', 
    ['Play_UI_Character_Confirm'] = 'Play_UI_Character_Confirm', 
    ['Play_UI_Character_Done'] = 'Play_UI_Character_Done', 
    ['Play_UI_Character_SmokeSwitch'] = 'Play_UI_Character_SmokeSwitch', 
    ['Play_UI_Character_face'] = 'Play_UI_Character_face', 
    ['Play_UI_Common'] = 'Play_UI_Common', 
    ['Play_UI_Common_Bag'] = 'Play_UI_Common_Bag', 
    ['Play_UI_Common_ButtonBackArrow'] = 'Play_UI_Common_ButtonBackArrow', 
    ['Play_UI_Common_Button_Close'] = 'Play_UI_Common_Button_Close', 
    ['Play_UI_Common_Character'] = 'Play_UI_Common_Character', 
    ['Play_UI_Common_Expand'] = 'Play_UI_Common_Expand', 
    ['Play_UI_Common_Mannual'] = 'Play_UI_Common_Mannual', 
    ['Play_UI_Common_Mission'] = 'Play_UI_Common_Mission', 
    ['Play_UI_Common_RedPacket_Get'] = 'Play_UI_Common_RedPacket_Get', 
    ['Play_UI_Common_RedPacket_Lose'] = 'Play_UI_Common_RedPacket_Lose', 
    ['Play_UI_Common_Rollup'] = 'Play_UI_Common_Rollup', 
    ['Play_UI_Common_Schedule'] = 'Play_UI_Common_Schedule', 
    ['Play_UI_Common_Store'] = 'Play_UI_Common_Store', 
    ['Play_UI_Common_Tab'] = 'Play_UI_Common_Tab', 
    ['Play_UI_Common_Task'] = 'Play_UI_Common_Task', 
    ['Play_UI_Common_lvl2'] = 'Play_UI_Common_lvl2', 
    ['Play_UI_Cutscean_QTE_Blue_Loop'] = 'Play_UI_Cutscean_QTE_Blue_Loop', 
    ['Play_UI_Cutscean_QTE_Loop'] = 'Play_UI_Cutscean_QTE_Loop', 
    ['Play_UI_Cutscean_QTE_Red_Loop'] = 'Play_UI_Cutscean_QTE_Red_Loop', 
    ['Play_UI_Fuben_Enter'] = 'Play_UI_Fuben_Enter', 
    ['Play_UI_Fuben_Select'] = 'Play_UI_Fuben_Select', 
    ['Play_UI_Mail_Cat_Click'] = 'Play_UI_Mail_Cat_Click', 
    ['Play_UI_Mail_Cat_DoubleClick'] = 'Play_UI_Mail_Cat_DoubleClick', 
    ['Play_UI_Mail_Cat_In'] = 'Play_UI_Mail_Cat_In', 
    ['Play_UI_Map_Newspace'] = 'Play_UI_Map_Newspace', 
    ['Play_UI_Map_Position'] = 'Play_UI_Map_Position', 
    ['Play_UI_Map_Switch'] = 'Play_UI_Map_Switch', 
    ['Play_UI_Map_World'] = 'Play_UI_Map_World', 
    ['Play_UI_Map_World_Close'] = 'Play_UI_Map_World_Close', 
    ['Play_UI_Map_ZoomIn'] = 'Play_UI_Map_ZoomIn', 
    ['Play_UI_Map_ZoomOut'] = 'Play_UI_Map_ZoomOut', 
    ['Play_UI_Map_open'] = 'Play_UI_Map_open', 
    ['Play_UI_Menu_Achieve'] = 'Play_UI_Menu_Achieve', 
    ['Play_UI_Menu_Club'] = 'Play_UI_Menu_Club', 
    ['Play_UI_Menu_Dungeon'] = 'Play_UI_Menu_Dungeon', 
    ['Play_UI_Menu_Dungeon_Background_Thunder_A'] = 'Play_UI_Menu_Dungeon_Background_Thunder_A', 
    ['Play_UI_Menu_Dungeon_Background_Thunder_B'] = 'Play_UI_Menu_Dungeon_Background_Thunder_B', 
    ['Play_UI_Menu_Equip'] = 'Play_UI_Menu_Equip', 
    ['Play_UI_Menu_Friend'] = 'Play_UI_Menu_Friend', 
    ['Play_UI_Menu_Merchandise'] = 'Play_UI_Menu_Merchandise', 
    ['Play_UI_Menu_PVP'] = 'Play_UI_Menu_PVP', 
    ['Play_UI_Menu_Pata'] = 'Play_UI_Menu_Pata', 
    ['Play_UI_Menu_Perform'] = 'Play_UI_Menu_Perform', 
    ['Play_UI_Menu_Perform_CardChosen'] = 'Play_UI_Menu_Perform_CardChosen', 
    ['Play_UI_Menu_Perform_CardSlide'] = 'Play_UI_Menu_Perform_CardSlide', 
    ['Play_UI_Menu_Perform_WearMask'] = 'Play_UI_Menu_Perform_WearMask', 
    ['Play_UI_Menu_Seal'] = 'Play_UI_Menu_Seal', 
    ['Play_UI_Menu_Sequence'] = 'Play_UI_Menu_Sequence', 
    ['Play_UI_Menu_Sequence_FadeIn'] = 'Play_UI_Menu_Sequence_FadeIn', 
    ['Play_UI_Menu_Sequence_FadeOut'] = 'Play_UI_Menu_Sequence_FadeOut', 
    ['Play_UI_Menu_Setting'] = 'Play_UI_Menu_Setting', 
    ['Play_UI_Menu_Skill'] = 'Play_UI_Menu_Skill', 
    ['Play_UI_Menu_TarrotTeam'] = 'Play_UI_Menu_TarrotTeam', 
    ['Play_UI_Menu_Troops'] = 'Play_UI_Menu_Troops', 
    ['Play_UI_Messenger_Cat_Annoying'] = 'Play_UI_Messenger_Cat_Annoying', 
    ['Play_UI_Messenger_Cat_Idea'] = 'Play_UI_Messenger_Cat_Idea', 
    ['Play_UI_MiniGame_Book_Close'] = 'Play_UI_MiniGame_Book_Close', 
    ['Play_UI_MiniGame_Book_Open'] = 'Play_UI_MiniGame_Book_Open', 
    ['Play_UI_MiniGame_Book_PageTurn'] = 'Play_UI_MiniGame_Book_PageTurn', 
    ['Play_UI_MiniGame_CrossWord_Fail'] = 'Play_UI_MiniGame_CrossWord_Fail', 
    ['Play_UI_MiniGame_CrossWord_Success'] = 'Play_UI_MiniGame_CrossWord_Success', 
    ['Play_UI_MiniGame_CrossWord_Tab'] = 'Play_UI_MiniGame_CrossWord_Tab', 
    ['Play_UI_MiniGame_Paint_Fail'] = 'Play_UI_MiniGame_Paint_Fail', 
    ['Play_UI_MiniGame_Paint_Success'] = 'Play_UI_MiniGame_Paint_Success', 
    ['Play_UI_MiniGame_Paint_Trigger'] = 'Play_UI_MiniGame_Paint_Trigger', 
    ['Play_UI_MiniGame_Paint_Trigger_Final'] = 'Play_UI_MiniGame_Paint_Trigger_Final', 
    ['Play_UI_MiniGame_Sequence_Bar_Loop'] = 'Play_UI_MiniGame_Sequence_Bar_Loop', 
    ['Play_UI_MiniGame_Sequence_Drug_Boil'] = 'Play_UI_MiniGame_Sequence_Drug_Boil', 
    ['Play_UI_MiniGame_Sequence_Drug_Explosion'] = 'Play_UI_MiniGame_Sequence_Drug_Explosion', 
    ['Play_UI_MiniGame_Sequence_Drug_Loop'] = 'Play_UI_MiniGame_Sequence_Drug_Loop', 
    ['Play_UI_MiniGame_Sequence_Drug_Pour'] = 'Play_UI_MiniGame_Sequence_Drug_Pour', 
    ['Play_UI_MiniGame_Sequence_Drug_Stir_Loop'] = 'Play_UI_MiniGame_Sequence_Drug_Stir_Loop', 
    ['Play_UI_MiniGame_Sequence_Whisper_Loop_01'] = 'Play_UI_MiniGame_Sequence_Whisper_Loop_01', 
    ['Play_UI_MiniGame_Sequence_Whisper_Loop_02'] = 'Play_UI_MiniGame_Sequence_Whisper_Loop_02', 
    ['Play_UI_MiniGame_Sequence_Whisper_Loop_03'] = 'Play_UI_MiniGame_Sequence_Whisper_Loop_03', 
    ['Play_UI_MiniGame_Sequence_Whisper_Loop_04'] = 'Play_UI_MiniGame_Sequence_Whisper_Loop_04', 
    ['Play_UI_Paper_Switch'] = 'Play_UI_Paper_Switch', 
    ['Play_UI_Store_Coin'] = 'Play_UI_Store_Coin', 
    ['Play_UI_Union_Answer_Chose'] = 'Play_UI_Union_Answer_Chose', 
    ['Stop_UI_Cutscean_QTE_Blue_Loop'] = 'Stop_UI_Cutscean_QTE_Blue_Loop', 
    ['Stop_UI_Cutscean_QTE_Loop'] = 'Stop_UI_Cutscean_QTE_Loop', 
    ['Stop_UI_Cutscean_QTE_Red_Loop'] = 'Stop_UI_Cutscean_QTE_Red_Loop', 
    ['Stop_UI_MiniGame_Sequence_Bar_Loop'] = 'Stop_UI_MiniGame_Sequence_Bar_Loop', 
    ['Stop_UI_MiniGame_Sequence_Drug_Loop'] = 'Stop_UI_MiniGame_Sequence_Drug_Loop', 
    ['Stop_UI_MiniGame_Sequence_Drug_Stir_Loop'] = 'Stop_UI_MiniGame_Sequence_Drug_Stir_Loop', 
    ['Stop_UI_MiniGame_Sequence_Whisper_Loop'] = 'Stop_UI_MiniGame_Sequence_Whisper_Loop', 
}

Enum.EWorldChannelQuizConstIntData = {
    ['PREVIEW_DURATION_TIME'] = 60, 
    ['REWARD_A_MAX_QUANTITY'] = 3, 
}

Enum.EWorldChannelQuizConstStringData = {
    ['QUIZ_FAIL_TEXT'] = 3, 
    ['QUIZ_START_REMINDER'] = 1, 
    ['QUIZ_SUCCESS_TEXT'] = 2, 
}

Enum.EWorldChannelQuizRewardData = {
    ['Right'] = 2, 
    ['Top'] = 1, 
    ['Voice'] = 3, 
}

Enum.HomeType = {
    ['HOME_MANOR'] = 5200060, 
    ['HOME_TEST'] = 5200061, 
}

Enum.PostProcessType = {
    ['Bloom'] = 11, 
    ['Brighten'] = 101, 
    ['ChromaticAberration'] = 15, 
    ['Clip'] = 109, 
    ['ColorAdjust'] = 102, 
    ['ColorAdjustRadialBlur'] = 103, 
    ['Darken'] = 13, 
    ['DepthOfField'] = 12, 
    ['Fog'] = 51, 
    ['GI_IndirectLight'] = 14, 
    ['LowLife'] = 110, 
    ['Phantom'] = 52, 
    ['RGBSplit'] = 105, 
    ['RadialBlur'] = 104, 
    ['RadialUVDistort'] = 108, 
    ['Sketch'] = 53, 
    ['Vignette'] = 107, 
}

Enum.StrongControl = {
    [2] = true, 
    [10] = true, 
    [11] = true, 
}

Enum.TriggerModuleType = {
    ['Achievement'] = 10, 
    ['Collectibles'] = 11, 
    ['CommonInteractor'] = 19, 
    ['DiceCheckBonus'] = 20, 
    ['ElementTalentTree'] = 17, 
    ['EquipBodyEnhance'] = 14, 
    ['HomeFurniture'] = 12, 
    ['Newbie'] = 1, 
    ['None'] = 0, 
    ['PlotRecap'] = 16, 
    ['RelicsExtraEffect'] = 21, 
    ['RolePlayIdentity'] = 2, 
    ['RolePlayPharmacist'] = 13, 
    ['RolePlayTalent'] = 3, 
    ['Schedule'] = 6, 
    ['Sequence'] = 4, 
    ['Shop'] = 9, 
    ['SkillList'] = 8, 
    ['SocialAction'] = 7, 
    ['Stall'] = 5, 
    ['TarotTeam'] = 15, 
    ['WorldActivity'] = 18, 
}

Enum.WeakControl = {
    [7] = true, 
    [15] = true, 
    [17] = true, 
}

Enum.WorldActivityType = {
}
