--
-- 表名: DungeonData后处理
--

local TopData = {
    EnterSpaceStartBattleReset = {
        [5100001] = {[5150003] = true, }, 
        [5100013] = {[5150012] = true, [5150013] = true, }, 
        [5100038] = {[5150013] = true, }, 
        [5100039] = {[5150013] = true, }, 
    },
    StageEndReset = {
        [5100032] = {[5150026] = true, }, 
        [5100033] = {[5150028] = true, [5150029] = true, }, 
        [5100035] = {[5150034] = true, [5150035] = true, }, 
        [5100042] = {[5150041] = true, [5150042] = true, }, 
        [5100043] = {[5150044] = true, [5150045] = true, }, 
        [5100045] = {[5150050] = true, [5150051] = true, }, 
        [5100046] = {[5150034] = true, [5150035] = true, }, 
        [5100047] = {[5150050] = true, [5150051] = true, }, 
    },
    StageEntSpaceReset = {
        [5100001] = {[5150001] = true, [5150002] = true, }, 
        [5100013] = {[5150011] = true, }, 
        [5100032] = {[5150026] = true, }, 
        [5100033] = {[5150028] = true, }, 
        [5100035] = {[5150034] = true, }, 
        [5100038] = {[5150011] = true, [5150012] = true, }, 
        [5100039] = {[5150011] = true, [5150012] = true, }, 
        [5100040] = {[5150038] = true, [5150039] = true, [5150040] = true, }, 
        [5100042] = {[5150041] = true, }, 
        [5100043] = {[5150044] = true, }, 
        [5100044] = {[5150047] = true, [5150048] = true, [5150049] = true, }, 
        [5100045] = {[5150050] = true, }, 
        [5100046] = {[5150034] = true, }, 
        [5100047] = {[5150050] = true, }, 
        [5100048] = {[5150038] = true, [5150039] = true, [5150040] = true, }, 
        [5100049] = {[5150038] = true, [5150039] = true, [5150040] = true, }, 
    },
    StageStartBattleReset = {
        [5100001] = {[5150002] = true, }, 
        [5100013] = {[5150011] = true, [5150012] = true, }, 
        [5100038] = {[5150012] = true, }, 
        [5100039] = {[5150012] = true, }, 
    },
    bDungeonLastStages = {
        [5150003] = 5150003, 
        [5150013] = 5150013, 
        [5150027] = 5150027, 
        [5150030] = 5150030, 
        [5150036] = 5150036, 
        [5150040] = 5150040, 
        [5150043] = 5150043, 
        [5150046] = 5150046, 
        [5150049] = 5150049, 
        [5150052] = 5150052, 
    },
    bLevelDungeonMaxLvlLimitMap = {
        [5100001] = 0, 
        [5100013] = 0, 
        [5100019] = 99, 
        [5100032] = 0, 
        [5100033] = 0, 
        [5100035] = 0, 
        [5100037] = 0, 
        [5100038] = 0, 
        [5100039] = 0, 
        [5100040] = 0, 
        [5100042] = 99, 
        [5100043] = 99, 
        [5100044] = 0, 
        [5100045] = 99, 
        [5100046] = 0, 
        [5100047] = 99, 
        [5100048] = 0, 
        [5100049] = 0, 
        [5100050] = 0, 
        [5100099] = 0, 
        [5101001] = 0, 
        [5102001] = 0, 
    },
    bLevelDungeonMinLvlLimitMap = {
        [5100001] = 33, 
        [5100013] = 45, 
        [5100019] = 1, 
        [5100032] = 50, 
        [5100033] = 55, 
        [5100035] = 28, 
        [5100037] = 1, 
        [5100038] = 45, 
        [5100039] = 45, 
        [5100040] = 39, 
        [5100042] = 1, 
        [5100043] = 1, 
        [5100044] = 39, 
        [5100045] = 1, 
        [5100046] = 28, 
        [5100047] = 1, 
        [5100048] = 39, 
        [5100049] = 39, 
        [5100050] = 28, 
        [5100099] = 99, 
        [5101001] = 25, 
        [5102001] = 1, 
    },
    data = {
        [5100001] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823552'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100001, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 33, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54083570370304'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 420, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150001, 5150002, 5150003}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'(31430,6800,135)', '-1', '-1'}, 
            ['StageRewards'] = {{[5150001] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150002] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150003] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150001] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150002] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150003] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 0, 
            ['WorldID'] = {5200016, 5200013}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100013] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Steven.UI_Dungeon_Img_Steven', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100013, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 45, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642587136'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150011, 5150012, 5150013}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 2, 
            ['WorldID'] = {5200001}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100019] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss01.UI_Dungeon_Img_DungeonBoss01', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_32574642589952'),
            ['DisplayNumber'] = 20, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {}, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 99999, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 30, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100019, 
            ['MaxLvlLimit'] = 99, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642589952'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBossBanner01.UI_Dungeon_Img_DungeonBossBanner01', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 0, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 0, 
            ['WorldID'] = {5200018}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100032] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140825088'),
            ['DisplayNumber'] = 2, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{1}, {1, 2}, }, 
            ['DungeonEndTimes'] = {1766577600, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100032, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 50, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724905984'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 420, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 2, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 1, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {1}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 1766577600, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150026, 5150027}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, }, 
            ['StageRewardPosition'] = {'-1', '-1'}, 
            ['StageRewards'] = {{[5150026] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150027] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150026] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150027] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 0, 
            ['WorldID'] = {5200034}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100033] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 2, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100033, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 55, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724906240'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150028, 5150029, 5150030}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150028] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150029] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150030] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150028] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150029] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150030] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 3, 
            ['WorldID'] = {5200035}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100035] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss03.UI_Dungeon_Img_DungeonBoss03', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG03.UI_Dungeon_Img_DungeonBG03', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140825856'),
            ['DisplayNumber'] = 2, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 3600, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100035, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 28, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_36353676952576'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBossBanner03.UI_Dungeon_Img_DungeonBossBanner03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 420, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150034, 5150035, 5150036}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{[5150034] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150035] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150036] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150034] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150035] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150036] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 1, 
            ['WorldID'] = {5200038}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100037] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140826368'),
            ['DisplayNumber'] = 3, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {}, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 99999, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100037, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642596608'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 0, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 2, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 0, 
            ['WorldID'] = {5200049}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100038] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100038, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 45, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724907776'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150011, 5150012, 5150013}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {3}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 3, 
            ['WorldID'] = {5200001}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100039] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Steven.UI_Dungeon_Img_Steven', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100039, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 45, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642600192'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150011, 5150012, 5150013}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150011] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150012] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150013] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 3, 
            ['WorldID'] = {5200063}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100040] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossSide/RayBieber01.RayBieber01', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823552'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {1766577600, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100040, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 39, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54083570370304'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossHalf/RayBieber02.RayBieber02', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 4500, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 1766577600, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150038, 5150039, 5150040}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'(31430,6800,135)', '-1', '-1'}, 
            ['StageRewards'] = {{[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300003, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 2, 
            ['WorldID'] = {5200057, 5200056, 5200064}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100042] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossSide/Cotard01.Cotard01', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100042, 
            ['MaxLvlLimit'] = 99, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597888'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossHalf/Cotard02.Cotard02', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150041, 5150042, 5150043}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150041] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150042] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150043] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150041] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150042] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150043] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300006, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 3, 
            ['WorldID'] = {5200054}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100043] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossSide/Sasriel01.Sasriel01', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140827648'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100043, 
            ['MaxLvlLimit'] = 99, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642597632'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossHalf/Sasriel02.Sasriel02', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150044, 5150045, 5150046}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1', '-1'}, 
            ['StageRewards'] = {{[5150044] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150045] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150046] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150044] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150045] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150046] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300007, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 3, 
            ['WorldID'] = {5200053}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100044] = {
            ['AllowSingleMode'] = false, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossSide/RayBieber01.RayBieber01', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140828160'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {1766577600, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100044, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 39, 
            ['MinPlayerLimit'] = 4, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724909056'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/ConfigIcon/Character/BossHalf/RayBieber02.RayBieber02', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 4500, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 1766577600, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150047, 5150048, 5150049}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'(31430,6800,135)', '-1', '-1'}, 
            ['StageRewards'] = {{[5150047] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150048] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150049] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150047] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150048] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150049] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300003, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 2, 
            ['WorldID'] = {5200096, 5200095, 5200097}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100045] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140828416'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100045, 
            ['MaxLvlLimit'] = 99, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642603776'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150050, 5150051, 5150052}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1'}, 
            ['StageRewards'] = {{[5150050] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150051] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150052] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150050] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150051] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150052] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300008, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 3, 
            ['WorldID'] = {5200079}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100046] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss03.UI_Dungeon_Img_DungeonBoss03', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG03.UI_Dungeon_Img_DungeonBG03', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140825856'),
            ['DisplayNumber'] = 2, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 3600, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100046, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 28, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642616320'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBossBanner03.UI_Dungeon_Img_DungeonBossBanner03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 420, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {5150034, 5150035, 5150036}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{[5150034] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150035] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150036] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150034] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150035] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150036] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 1, 
            ['WorldID'] = {5204002}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100047] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140828416'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100047, 
            ['MaxLvlLimit'] = 99, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_32574642608640'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150050, 5150051, 5150052}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'-1', '-1'}, 
            ['StageRewards'] = {{[5150050] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150051] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150052] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150050] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150051] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150052] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300009, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 3, 
            ['WorldID'] = {5200102}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100048] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823552'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {1766577600, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100048, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 39, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_34983850814720'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 4500, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 1766577600, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150038, 5150039, 5150040}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'(31430,6800,135)', '-1', '-1'}, 
            ['StageRewards'] = {{[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300003, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 2, 
            ['WorldID'] = {5200124}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100049] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber02.UI_Dungeon_Img_RayBieber02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG01.UI_Dungeon_Img_DungeonBG01', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823552'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_Rielbieber.FBXZ_Rielbieber', 
            ['DungeonBuffs'] = {{3}, {1, 2}, }, 
            ['DungeonEndTimes'] = {1766577600, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {{3011035, 1, 1}, {3012034, 1, -1}, }, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {1735041600, 1735041600}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_RayBieber.UI_Dungeon_Img_RayBieber', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900003, 
            ['FirstFinishDrop'] = {{3014034, 1, 1}, {3014035, 1, -1}, {3015034, 1, 1}, }, 
            ['FirstFinishTeam'] = 1, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100049, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 39, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724910336'),
            ['NiagaraAssets'] = {'/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C001.NS_Rielbieber_Show_Smoke_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C002.NS_Rielbieber_Show_Smoke_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C003.NS_Rielbieber_Show_Smoke_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Smoke_C004.NS_Rielbieber_Show_Smoke_C004', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C001.NS_Rielbieber_Show_Glow_Fire_C001', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C002.NS_Rielbieber_Show_Glow_Fire_C002', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Glow_Fire_C003.NS_Rielbieber_Show_Glow_Fire_C003', '/Game/Arts/Effects/CinematicsFX/Rielbieber_Show/NS_Rielbieber_Show_Dirt.NS_Rielbieber_Show_Dirt'}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 4500, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonBuffId'] = {3}, 
            ['SingleDungeonPersonalReward'] = {{3004035, 1, 1}, {3005034, 1, -1}, {3005035, 1, 1}, }, 
            ['SingleDungeonRewardDisplay'] = {{3012035, 1, -1}, {3013034, 1, 1}, {3013035, 1, -1}, }, 
            ['SingleEndTime'] = 1766577600, 
            ['SingleStagePersonalReward'] = {{3000034, 1, 1}, {3000035, 1, -1}, {3001034, 1, 1}, }, 
            ['SingleStartTime'] = 1735041600, 
            ['StageList'] = {5150038, 5150039, 5150040}, 
            ['StagePersonalReward'] = {{3001035, 1, 1}, {3002034, 1, -1}, {3002035, 1, 1}, }, 
            ['StageRewardPosition'] = {'(31430,6800,135)', '-1', '-1'}, 
            ['StageRewards'] = {{[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, {[5150038] = {['DropBossPosition'] = false, ['SpecialDropPosition'] = {31430, 6800, 135}, }, [5150039] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, [5150040] = {['DropBossPosition'] = true, ['SpecialDropPosition'] = {0, 0, 0}, }, }, }, 
            ['StageTeamReward'] = {{3003034, 1, 1}, {3003035, 1, -1}, {3004034, 1, 1}, }, 
            ['StartTime'] = 0, 
            ['Target'] = 5300003, 
            ['TeamCanEnter'] = true, 
            ['TeamDungeonBuffId'] = {1, 2}, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 1735041600, 
            ['Type'] = 2, 
            ['WorldID'] = {5200125}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100050] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss03.UI_Dungeon_Img_DungeonBoss03', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG03.UI_Dungeon_Img_DungeonBG03', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140825856'),
            ['DisplayNumber'] = 2, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 3600, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['MarkId'] = 5100050, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 12, 
            ['MinLvlLimit'] = 28, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724910592'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBossBanner03.UI_Dungeon_Img_DungeonBossBanner03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 420, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 30, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 3, 
            ['WorldID'] = {5200123}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5100099] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBG02.UI_Dungeon_Img_DungeonBG02', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 2, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140823808'),
            ['DisplayNumber'] = 1, 
            ['DisplaySequence'] = '/Game/Arts/Cinematics/Dungeon/DungeonInterface/FBXZ_yuanhun.FBXZ_yuanhun', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {}, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Dungeon01.UI_Dungeon_Img_Dungeon01', 
            ['EnterPreviewBoss'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_Steven.UI_Dungeon_Img_Steven', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 7900020, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 2, 
            ['GroupCanEnter'] = true, 
            ['MarkId'] = 5100099, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 99, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54632789776384'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 1, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven03.UI_Dungeon_Img_Steven03', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 640, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_SeletedDomain01.UI_Dungeon_Img_SeletedDomain01', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2, 3}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 5, 
            ['WorldID'] = {5200049}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5101001] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss01.UI_Dungeon_Img_DungeonBoss01;/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss02.UI_Dungeon_Img_DungeonBoss02;/Game/Arts/UI_2/Resource/Dungeon/NotAtlas/UI_Dungeon_Img_DungeonBoss04.UI_Dungeon_Img_DungeonBoss04', 
            ['BackGroundPath'] = '', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_14297140826624'),
            ['DisplayNumber'] = 0, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {{3010034, 1, -1}, {3010035, 1, 1}, {3011034, 1, 1}, }, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 3600, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/UI_Dungeon_Img_DungeonBg.UI_Dungeon_Img_DungeonBg', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 0, 
            ['MarkId'] = 5101001, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 6, 
            ['MinLvlLimit'] = 25, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_345744869120'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 0, 
            ['PagePath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_RayBieber03.UI_Dungeon_Img_RayBieber03', 
            ['RandomDungeon'] = {5200041, 5200042, 5200043}, 
            ['RecommendPower'] = 300, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 4, 
            ['WorldID'] = {}, 
            ['bAllowTemporaryLeave'] = true, 
        },
        [5102001] = {
            ['AllowSingleMode'] = true, 
            ['AtmoPath'] = '/Game/Arts/UI_2/Resource/Dungeon_2/NotAtlas/Boss/UI_Dungeon_Img_Steven02.UI_Dungeon_Img_Steven02', 
            ['BackGroundPath'] = '', 
            ['BackToInitial'] = true, 
            ['DLvlRule'] = 1, 
            ['Desc'] = Game.TableDataManager:GetLangStr('str_32574642609152'),
            ['DisplayNumber'] = 0, 
            ['DisplaySequence'] = '', 
            ['DungeonBuffs'] = {{}, {}, }, 
            ['DungeonEndTimes'] = {0, 0}, 
            ['DungeonPersonalReward'] = {}, 
            ['DungeonRewardDisplay'] = {}, 
            ['DungeonRewardPosition'] = '-1', 
            ['DungeonStartTimes'] = {0, 0}, 
            ['DungeonTeamReward'] = {}, 
            ['DurationLimit'] = 7200, 
            ['EndTime'] = 2000000000, 
            ['EnterPreviewBg'] = '', 
            ['EnterPreviewBoss'] = '', 
            ['ExitCountdown'] = 300, 
            ['ExitID'] = 0, 
            ['ExitPosition'] = {}, 
            ['ExitScope'] = 0, 
            ['FacadeControlID'] = 0, 
            ['FirstFinishDrop'] = {}, 
            ['FirstFinishTeam'] = 0, 
            ['GameModeID'] = 0, 
            ['GameModeType'] = 0, 
            ['MarkId'] = 5102001, 
            ['MaxLvlLimit'] = 0, 
            ['MaxPlayerLimit'] = 1, 
            ['MinLvlLimit'] = 1, 
            ['MinPlayerLimit'] = 1, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_14294724910848'),
            ['NiagaraAssets'] = {}, 
            ['Page'] = 0, 
            ['PagePath'] = '', 
            ['RandomDungeon'] = {}, 
            ['RecommendPower'] = 0, 
            ['RewardDisplay'] = 0, 
            ['RewardDisplayType'] = 1, 
            ['SelectedPagePath'] = '', 
            ['SettleCountdown'] = 0, 
            ['SettlementInterruptStage'] = 0, 
            ['SingleCanEnter'] = true, 
            ['SingleDungeonPersonalReward'] = {}, 
            ['SingleDungeonRewardDisplay'] = {}, 
            ['SingleEndTime'] = 0, 
            ['SingleStagePersonalReward'] = {}, 
            ['SingleStartTime'] = 0, 
            ['StageList'] = {}, 
            ['StagePersonalReward'] = {}, 
            ['StageRewardPosition'] = {'-1'}, 
            ['StageRewards'] = {{}, {}, }, 
            ['StageTeamReward'] = {}, 
            ['StartTime'] = 0, 
            ['Target'] = 0, 
            ['TeamCanEnter'] = true, 
            ['TeamEndTime'] = 0, 
            ['TeamLimit'] = {1, 2}, 
            ['TeamStartTime'] = 0, 
            ['Type'] = 0, 
            ['WorldID'] = {5200099}, 
            ['bAllowTemporaryLeave'] = true, 
        },
    }
}
return TopData