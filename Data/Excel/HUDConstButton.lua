--
-- 表名: $HUDConstButton_HUD功能按钮.xlsx  页名：$HUDConstButton_HUD按钮
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_25564450669056'),
			["ButtonNumber"] = 1,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B04_Sprite.UI_HUD_Icon_B04_Sprite",
			["ActiveIcon"] = "",
		},
		[2] = {
			["ID"] = 2,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_29412741351168'),
			["ButtonNumber"] = 2,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B02_Sprite.UI_HUD_Icon_B02_Sprite",
			["ActiveIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B01_Sprite.UI_HUD_Icon_B01_Sprite",
		},
		[3] = {
			["ID"] = 3,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_29412741351424'),
			["ButtonNumber"] = 3,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B05_Sprite.UI_HUD_Icon_B05_Sprite",
			["ActiveIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B06_Sprite.UI_HUD_Icon_B06_Sprite",
		},
		[4] = {
			["ID"] = 4,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_29412741351680'),
			["ButtonNumber"] = 4,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B03_Sprite.UI_HUD_Icon_B03_Sprite",
			["ActiveIcon"] = "",
		},
		[5] = {
			["ID"] = 5,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_33604629433856'),
			["ButtonNumber"] = 5,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B03_Sprite.UI_HUD_Icon_B03_Sprite",
			["ActiveIcon"] = "",
		},
		[6] = {
			["ID"] = 6,
			["FunctionName"] = Game.TableDataManager:GetLangStr('str_44599745711104'),
			["ButtonNumber"] = 6,
			["ButtonIcon"] = "/Game/Arts/UI_2/Resource/HUD_2/Atlas/HUD_Common/Sprite01/UI_HUD_Icon_B08_Sprite.UI_HUD_Icon_B08_Sprite",
			["ActiveIcon"] = "",
		},
	},
}

return TopData
