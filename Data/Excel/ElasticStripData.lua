--
-- 表名: $UITips_界面提醒.xlsx  页名：$ElasticStrip_左侧弹条
--

local TopData = {
	data = {
		[10000] = {
			["ID"] = 10000,
			["Enum"] = "NewPrivateChat",
			["Content"] = "",
			["AcceptIcon"] = 2,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Message_Sprite.UI_Tips_Icon_Message_Sprite",
		},
		[20000] = {
			["ID"] = 20000,
			["Enum"] = "FriendInvite",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115427584'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_AddFriend_Sprite.UI_Tips_Icon_AddFriend_Sprite",
		},
		[20001] = {
			["ID"] = 20001,
			["Enum"] = "ReceivedJoinTeamApplication",
			["Content"] = "%s",
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20002] = {
			["ID"] = 20002,
			["Enum"] = "ReceivedTeamInvitation",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115428096'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20003] = {
			["ID"] = 20003,
			["Enum"] = "ReceivedCombineTeamApplication",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115428352'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20004] = {
			["ID"] = 20004,
			["Enum"] = "ReceivedJoinGroupApplication",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115428608'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20005] = {
			["ID"] = 20005,
			["Enum"] = "ReceivedGroupInvitation",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115428864'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20006] = {
			["ID"] = 20006,
			["Enum"] = "InviteOthersInTeam",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115429120'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[20007] = {
			["ID"] = 20007,
			["Enum"] = "InviteOthersInGroup",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115429376'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[20008] = {
			["ID"] = 20008,
			["Enum"] = "LeagueApply",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115429632'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[20009] = {
			["ID"] = 20009,
			["Enum"] = "GroupTeamMember",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115429888'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[20010] = {
			["ID"] = 20010,
			["Enum"] = "ApplyTeamLeader",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115430144'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[20011] = {
			["ID"] = 20011,
			["Enum"] = "ApplyGroupLeader",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115430400'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[20012] = {
			["ID"] = 20012,
			["Enum"] = "ReceivedCombineGroupApplication",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115430656'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[30000] = {
			["ID"] = 30000,
			["Enum"] = "SendSocialAction",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115430912'),
			["AcceptIcon"] = 0,
			["IsIgnore"] = 0,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 3,
			["CoupleStripType"] = 3,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[30001] = {
			["ID"] = 30001,
			["Enum"] = "ReceivedSocialAction",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431168'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 0,
			["CoupleStripType"] = 0,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[60000] = {
			["ID"] = 60000,
			["Enum"] = "GuildDanceInvite",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431424'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 2,
			["SwearStripType"] = 2,
			["CoupleStripType"] = 2,
			["CountDown"] = 10,
			["TimeUPSelect"] = 2,
			["IdentificationIcon"] = "",
		},
		[130000] = {
			["ID"] = 130000,
			["Enum"] = "ApplyIndividualPVP",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431680'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 10,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[140000] = {
			["ID"] = 140000,
			["Enum"] = "RoleplayFortuneInvite",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431936'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[150000] = {
			["ID"] = 150000,
			["Enum"] = "WitchChairApplication",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115432192'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[160000] = {
			["ID"] = 160000,
			["Enum"] = "WitchInvite",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431168'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 1,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[70000] = {
			["ID"] = 70000,
			["Enum"] = "InviteToChampionTroop",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115432704'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "/Game/Arts/UI_2/Resource/Tips_2/Atlas/Sprite01/UI_Tips_Icon_Team_Sprite.UI_Tips_Icon_Team_Sprite",
		},
		[80000] = {
			["ID"] = 80000,
			["Enum"] = "InviteToRideTogether",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115432960'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 0,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
		[60001] = {
			["ID"] = 60001,
			["Enum"] = "DanceInvite",
			["Content"] = Game.TableDataManager:GetLangStr('str_61299115431424'),
			["AcceptIcon"] = 1,
			["IsIgnore"] = 2,
			["UIJumpID"] = 0,
			["DialogType"] = 1,
			["SwearStripType"] = 1,
			["CoupleStripType"] = 1,
			["CountDown"] = 30,
			["TimeUPSelect"] = 0,
			["IdentificationIcon"] = "",
		},
	},
}

return TopData
