--
-- 表名: GuildPuzzleData后处理
--

local TopData = {
    canShuffleQuest = {101, 102, 201, 202, 203, 301
    },
    data = {
        [101] = {
            ['ID'] = 101, 
            ['Option1'] = Game.TableDataManager:GetLangStr('str_27901718169088'),
            ['Option2'] = Game.TableDataManager:GetLangStr('str_27901986604544'),
            ['Option3'] = Game.TableDataManager:GetLangStr('str_27902255040000'),
            ['Option4'] = Game.TableDataManager:GetLangStr('str_27902523475456'),
            ['QuestType'] = 1, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912862720'),
        },
        [102] = {
            ['ID'] = 102, 
            ['Option1'] = Game.TableDataManager:GetLangStr('str_27901718169344'),
            ['Option2'] = Game.TableDataManager:GetLangStr('str_27901986604800'),
            ['Option3'] = Game.TableDataManager:GetLangStr('str_27902255040256'),
            ['Option4'] = Game.TableDataManager:GetLangStr('str_27902523475712'),
            ['QuestType'] = 1, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912862976'),
        },
        [201] = {
            ['ID'] = 201, 
            ['Parameter'] = 6, 
            ['QuestType'] = 2, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912863232'),
            ['Type'] = '特定职位', 
        },
        [202] = {
            ['ID'] = 202, 
            ['Parameter'] = 3, 
            ['QuestType'] = 2, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912863488'),
            ['Type'] = '特定职位', 
        },
        [203] = {
            ['ID'] = 203, 
            ['QuestType'] = 3, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912863744'),
            ['Type'] = '历史公会贡献', 
        },
        [301] = {
            ['ID'] = 301, 
            ['Option1'] = Game.TableDataManager:GetLangStr('str_27901718170368'),
            ['Option2'] = Game.TableDataManager:GetLangStr('str_27901986605824'),
            ['Option3'] = Game.TableDataManager:GetLangStr('str_27902255041280'),
            ['Option4'] = Game.TableDataManager:GetLangStr('str_27902523476736'),
            ['QuestType'] = 4, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_27900912864000'),
        },
    }
}
return TopData