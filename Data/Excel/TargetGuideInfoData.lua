--
-- 表名: TargetGuideInfoData后处理
--

local TopData = {
    data = {
        [10000] = {{['Des'] = Game.TableDataManager:GetLangStr('str_12371653297664'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 10000, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'DanceEnterTeam', ['Type'] = 8, }, {['Des'] = Game.TableDataManager:GetLangStr('str_12371653297920'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 10000, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'DanceFlower', ['Type'] = 9, }, {['Des'] = Game.TableDataManager:GetLangStr('str_12371653298176'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 10000, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'DanceTea', ['Type'] = 10, }, 
        },
        [51001] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765211136'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51001, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765211392'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 3, ['TemplateID'] = {[7102196] = true, [7102197] = true, }, }, ['StepId'] = 2, ['TargetGuidId'] = 51001, ['TargetParam'] = {7102196, 7102197}, ['TargetTotalValue'] = 3, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765211648'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[1] = true, [2] = true, [2400001] = true, [2400002] = true, }, }, ['StepId'] = 3, ['TargetGuidId'] = 51001, ['TargetParam'] = {1, 2, 2400001, 2400002}, ['TargetTotalValue'] = 1, ['TargetType'] = 'Collect', ['Type'] = 3, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765211904'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 2, ['InstanceID'] = {['123456'] = true, ['1234567'] = true, }, }, ['StepId'] = 4, ['TargetGuidId'] = 51001, ['TargetParam'] = {123456, 1234567}, ['TargetTotalValue'] = 2, ['TargetType'] = 'Interact', ['Type'] = 6, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765212160'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -4915, ['Y'] = 661, ['Z'] = 192, }, ['Radius'] = 500, }, ['StepId'] = 5, ['TargetGuidId'] = 51001, ['TargetParam'] = {-4915, 661, 192, 500}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765212416'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['TimeCount'] = 20, }, ['StepId'] = 6, ['TargetGuidId'] = 51001, ['TargetParam'] = {20}, ['TargetTotalValue'] = 0, ['TargetType'] = 'CountDown', ['Type'] = 11, }, 
        },
        [51002] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765212672'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = true, ['Params'] = {['NpcID'] = 7242025, ['TalkID'] = 9040813, }, ['StepId'] = 1, ['TargetGuidId'] = 51002, ['TargetParam'] = {9040813, 7242025}, ['TargetTotalValue'] = 1, ['TargetType'] = 'TalkToNpc', ['Type'] = 4, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765211136'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51002, ['TargetParam'] = {}, ['TargetTotalValue'] = 1, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51003] = {
            [3] = {['Des'] = Game.TableDataManager:GetLangStr('str_55458765213184'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['DialogID'] = 100003, }, ['StepId'] = 3, ['TargetGuidId'] = 51003, ['TargetParam'] = {100003}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Dialogue', ['Type'] = 5, }, 
        },
        [51004] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765213440'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = true, ['Params'] = {['TimeCount'] = 20, }, ['StepId'] = 1, ['TargetGuidId'] = 51004, ['TargetParam'] = {20}, ['TargetTotalValue'] = 0, ['TargetType'] = 'CountDown', ['Type'] = 11, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765213696'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765213952'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765214208'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765214464'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765213696'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765214464'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 7, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765215232'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 8, ['TargetGuidId'] = 51004, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765215488'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 33480, ['Y'] = 7700, ['Z'] = 100, }, ['Radius'] = 300, }, ['StepId'] = 9, ['TargetGuidId'] = 51004, ['TargetParam'] = {33480, 7700, 100, 300}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, 
        },
        [51005] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765215744'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51005, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765216000'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51005, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51006] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765216256'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765216512'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765216768'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765216512'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765217280'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765217536'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51006, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51007] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765217792'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['NpcID'] = 7242108, ['TalkID'] = 9041129, }, ['StepId'] = 1, ['TargetGuidId'] = 51007, ['TargetParam'] = {9041129, 7242108}, ['TargetTotalValue'] = 0, ['TargetType'] = 'TalkToNpc', ['Type'] = 4, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218048'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218304'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7109801] = true, }, }, ['StepId'] = 3, ['TargetGuidId'] = 51007, ['TargetParam'] = {7109801}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218560'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102400] = true, }, }, ['StepId'] = 4, ['TargetGuidId'] = 51007, ['TargetParam'] = {7102400}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218816'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7103206] = true, }, }, ['StepId'] = 5, ['TargetGuidId'] = 51007, ['TargetParam'] = {7103206}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765219072'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765219328'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 7, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765219584'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 8, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765219840'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 6, ['TemplateID'] = {[7102934] = true, }, }, ['StepId'] = 9, ['TargetGuidId'] = 51007, ['TargetParam'] = {7102934}, ['TargetTotalValue'] = 6, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765220096'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {['Position'] = {['X'] = -3573, ['Y'] = -2505, ['Z'] = 20, }, ['Radius'] = 500, }, ['StepId'] = 10, ['TargetGuidId'] = 51007, ['TargetParam'] = {-3573, -2505, 20, 500}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218560'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102939] = true, }, }, ['StepId'] = 11, ['TargetGuidId'] = 51007, ['TargetParam'] = {7102939}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765220608'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 12, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765220864'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 13, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765221120'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 14, ['TargetGuidId'] = 51007, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765221376'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[9102932] = true, }, }, ['StepId'] = 15, ['TargetGuidId'] = 51007, ['TargetParam'] = {9102932}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765221632'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 4, ['TemplateID'] = {[7102933] = true, }, }, ['StepId'] = 16, ['TargetGuidId'] = 51007, ['TargetParam'] = {7102933}, ['TargetTotalValue'] = 4, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, 
        },
        [51008] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765221888'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 53670, ['Y'] = 101680, ['Z'] = -18850, }, ['Radius'] = 3000, }, ['StepId'] = 1, ['TargetGuidId'] = 51008, ['TargetParam'] = {53670, 101680, -18850, 3000}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765222144'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 0, ['TemplateID'] = {[2400440] = true, }, }, ['StepId'] = 2, ['TargetGuidId'] = 51008, ['TargetParam'] = {2400440}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Collect', ['Type'] = 3, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765222400'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51008, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765222656'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 49290, ['Y'] = 80980, ['Z'] = -18480, }, ['Radius'] = 1500, }, ['StepId'] = 4, ['TargetGuidId'] = 51008, ['TargetParam'] = {49290, 80980, -18480, 1500}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765222656'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 58780, ['Y'] = 77420, ['Z'] = -18120, }, ['Radius'] = 3000, }, ['StepId'] = 5, ['TargetGuidId'] = 51008, ['TargetParam'] = {58780, 77420, -18120, 3000}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765223168'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51008, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765223424'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 66980, ['Y'] = 62370, ['Z'] = -15240, }, ['Radius'] = 2000, }, ['StepId'] = 7, ['TargetGuidId'] = 51008, ['TargetParam'] = {66980, 62370, -15240, 2000}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765223424'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 61570, ['Y'] = 51450, ['Z'] = -11470, }, ['Radius'] = 2000, }, ['StepId'] = 8, ['TargetGuidId'] = 51008, ['TargetParam'] = {61570, 51450, -11470, 2000}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765223424'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 49930, ['Y'] = 51270, ['Z'] = -11250, }, ['Radius'] = 1000, }, ['StepId'] = 9, ['TargetGuidId'] = 51008, ['TargetParam'] = {49930, 51270, -11250, 1000}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765224192'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 10, ['TargetGuidId'] = 51008, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765224448'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 11, ['TargetGuidId'] = 51008, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51009] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765224704'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51009, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51010] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765225728'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 0, ['TemplateID'] = {[1290132278] = true, }, }, ['StepId'] = 1, ['TargetGuidId'] = 51010, ['TargetParam'] = {1290132278}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Collect', ['Type'] = 3, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765225984'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 0, ['TemplateID'] = {[1522902463] = true, }, }, ['StepId'] = 2, ['TargetGuidId'] = 51010, ['TargetParam'] = {1522902463}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Collect', ['Type'] = 3, }, 
        },
        [51011] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765226240'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 0, ['TemplateID'] = {[2400440] = true, }, }, ['StepId'] = 1, ['TargetGuidId'] = 51011, ['TargetParam'] = {2400440}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Collect', ['Type'] = 3, }, 
        },
        [51012] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765226240'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 0, ['TemplateID'] = {[2400440] = true, }, }, ['StepId'] = 1, ['TargetGuidId'] = 51012, ['TargetParam'] = {2400440}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Collect', ['Type'] = 3, }, 
        },
        [51013] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765224960'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51013, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51014] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765225216'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51014, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51015] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765225472'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51015, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51016] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765226752'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -3120, ['Y'] = 631, ['Z'] = 200, }, ['Radius'] = 300, }, ['StepId'] = 1, ['TargetGuidId'] = 51016, ['TargetParam'] = {-3120, 631, 200, 300}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227008'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51016, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227264'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -3120, ['Y'] = 631, ['Z'] = 200, }, ['Radius'] = 300, }, ['StepId'] = 3, ['TargetGuidId'] = 51016, ['TargetParam'] = {-3120, 631, 200, 300}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227520'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51016, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227776'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51016, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51017] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765228032'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -17359, ['Y'] = -33154, ['Z'] = 1610, }, ['Radius'] = 0, }, ['StepId'] = 1, ['TargetGuidId'] = 51017, ['TargetParam'] = {-17359, -33154, 1610, 0}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765218816'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102936] = true, }, }, ['StepId'] = 2, ['TargetGuidId'] = 51017, ['TargetParam'] = {7102936}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765228544'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51017, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765228800'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 7, ['TemplateID'] = {[7102934] = true, [7102935] = true, }, }, ['StepId'] = 4, ['TargetGuidId'] = 51017, ['TargetParam'] = {7102934, 7102935}, ['TargetTotalValue'] = 7, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765229056'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51017, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765229312'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102933] = true, }, }, ['StepId'] = 6, ['TargetGuidId'] = 51017, ['TargetParam'] = {7102933}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765229568'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 7, ['TargetGuidId'] = 51017, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765229824'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102936] = true, }, }, ['StepId'] = 8, ['TargetGuidId'] = 51017, ['TargetParam'] = {7102936}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765230080'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 9, ['TargetGuidId'] = 51017, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765230336'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Count'] = 1, ['TemplateID'] = {[7102938] = true, }, }, ['StepId'] = 10, ['TargetGuidId'] = 51017, ['TargetParam'] = {7102938}, ['TargetTotalValue'] = 1, ['TargetType'] = 'KillMonster', ['Type'] = 2, }, 
        },
        [51018] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765230592'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 9920, ['Y'] = 60790, ['Z'] = -5780, }, ['Radius'] = 300, }, ['StepId'] = 1, ['TargetGuidId'] = 51018, ['TargetParam'] = {9920, 60790, -5780, 300}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765230848'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51018, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227264'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -3120, ['Y'] = 631, ['Z'] = 200, }, ['Radius'] = 300, }, ['StepId'] = 3, ['TargetGuidId'] = 51018, ['TargetParam'] = {-3120, 631, 200, 300}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227520'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51018, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765227776'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51018, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51019] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765231872'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765232128'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765232384'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765232640'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_59236994366208'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765233152'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765233408'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 7, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765233664'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 8, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765233920'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = true, ['Params'] = {}, ['StepId'] = 9, ['TargetGuidId'] = 51019, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51020] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765234176'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = -1540, ['Y'] = -720, ['Z'] = 60, }, ['Radius'] = 200, }, ['StepId'] = 1, ['TargetGuidId'] = 51020, ['TargetParam'] = {-1540, -720, 60, 200}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765234432'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51020, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765234688'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 2000, ['Y'] = -870, ['Z'] = 0, }, ['Radius'] = 200, }, ['StepId'] = 3, ['TargetGuidId'] = 51020, ['TargetParam'] = {2000, -870, 0, 200}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765234944'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51020, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51021] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765235200'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51021, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765235456'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51021, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765235712'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51021, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765235968'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51021, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51030] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765236224'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765236480'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765236736'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765236992'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765237248'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765237504'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765237760'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 7, ['TargetGuidId'] = 51030, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51031] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765238016'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 0, ['Y'] = -3663, ['Z'] = 191, }, }, ['StepId'] = 1, ['TargetGuidId'] = 51031, ['TargetParam'] = {0, -3663, 191}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765238272'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51031, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765238528'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['Position'] = {['X'] = 89, ['Y'] = -1212, ['Z'] = 191, }, }, ['StepId'] = 3, ['TargetGuidId'] = 51031, ['TargetParam'] = {89, -1212, 191}, ['TargetTotalValue'] = 0, ['TargetType'] = 'Position', ['Type'] = 7, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765238784'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51031, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765239040'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51031, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51032] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765239296'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51032, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_9488656515840'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51032, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765214464'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51032, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765240064'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51032, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765240320'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51032, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51033] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765240576'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {['TimeCount'] = 10, }, ['StepId'] = 1, ['TargetGuidId'] = 51033, ['TargetParam'] = {10}, ['TargetTotalValue'] = 0, ['TargetType'] = 'CountDown', ['Type'] = 11, }, 
        },
        [51034] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765240832'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_59243705394688'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765241344'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_59243705394176'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765241856'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765242112'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 6, ['TargetGuidId'] = 51034, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51035] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765242368'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51035, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51036] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765242624'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 1, ['TargetGuidId'] = 51036, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765242880'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 2, ['TargetGuidId'] = 51036, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765243136'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 3, ['TargetGuidId'] = 51036, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_55458765243392'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 4, ['TargetGuidId'] = 51036, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, {['Des'] = Game.TableDataManager:GetLangStr('str_54632789641984'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = false, ['IsHide'] = false, ['Params'] = {}, ['StepId'] = 5, ['TargetGuidId'] = 51036, ['TargetParam'] = {}, ['TargetTotalValue'] = 0, ['TargetType'] = 'NoParam', ['Type'] = 1, }, 
        },
        [51037] = {{['Des'] = Game.TableDataManager:GetLangStr('str_55458765243904'),['GuideType'] = 1, ['IsAutoActiveNextStep'] = true, ['IsHide'] = false, ['Params'] = {['TimeCount'] = 30, }, ['StepId'] = 1, ['TargetGuidId'] = 51037, ['TargetParam'] = {30}, ['TargetTotalValue'] = 0, ['TargetType'] = 'CountDown', ['Type'] = 11, }, 
        },
    }
}
return TopData