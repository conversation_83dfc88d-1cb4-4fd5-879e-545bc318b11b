--
-- 表名: MoneyExchangeData(后处理)
--

local TopData = {
    moneyExchangeRateTable = {
        ['2001002;2001004'] = {['ExchangeRate'] = 1, ['SrcConsume'] = 180, }, 
        ['2001002;2001005'] = {['ExchangeRate'] = 1, ['SrcConsume'] = 180, }, 
        ['2001002;2001008'] = {['ExchangeRate'] = 1, ['SrcConsume'] = 180, }, 
        ['2001002;2001009'] = {['ExchangeRate'] = 1, ['SrcConsume'] = 180, }, 
        ['2001003;2001002'] = {['ExchangeRate'] = 1, ['SrcConsume'] = 1, }, 
    },
    data = {
        [2001000] = {
            ['ExchangeMoneyNum'] = {}, 
            ['ExchangeMoneyType'] = {}, 
            ['ExchangeTargetNum'] = {}, 
            ['GoodsList'] = {2300000, 2300001}, 
            ['ID'] = 2001000, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277301760'),
        },
        [2001001] = {
            ['ExchangeMoneyNum'] = {}, 
            ['ExchangeMoneyType'] = {}, 
            ['ExchangeTargetNum'] = {}, 
            ['GoodsList'] = {2300101}, 
            ['ID'] = 2001001, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277302016'),
        },
        [2001002] = {
            ['ExchangeMoneyNum'] = {1}, 
            ['ExchangeMoneyType'] = {2001003}, 
            ['ExchangeTargetNum'] = {1}, 
            ['ID'] = 2001002, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277302272'),
        },
        [2001004] = {
            ['ExchangeMoneyNum'] = {180}, 
            ['ExchangeMoneyType'] = {2001002}, 
            ['ExchangeTargetNum'] = {1}, 
            ['ID'] = 2001004, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277302784'),
        },
        [2001005] = {
            ['ExchangeMoneyNum'] = {180}, 
            ['ExchangeMoneyType'] = {2001002}, 
            ['ExchangeTargetNum'] = {1}, 
            ['ID'] = 2001005, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277303040'),
        },
        [2001008] = {
            ['ExchangeMoneyNum'] = {180}, 
            ['ExchangeMoneyType'] = {2001002}, 
            ['ExchangeTargetNum'] = {1}, 
            ['ID'] = 2001008, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277303296'),
        },
        [2001009] = {
            ['ExchangeMoneyNum'] = {180}, 
            ['ExchangeMoneyType'] = {2001002}, 
            ['ExchangeTargetNum'] = {1}, 
            ['ID'] = 2001009, 
            ['Title'] = Game.TableDataManager:GetLangStr('str_31750277303552'),
        },
    }
}
return TopData