--
-- 表名: $CustomRole_捏脸表.xlsx  页名：$SliderData_滑条数据
--

local TopData = {
	data = {
		[1101] = {
			["Key"] = 1101,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628974593'), Game.TableDataManager:GetLangStr('str_11133628974594'), Game.TableDataManager:GetLangStr('str_11133628974595'), Game.TableDataManager:GetLangStr('str_11133628974596')},
			["WidgetStyle"] = 1,
			["SliderType"] = "Preset",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "",
			["PresetIDs"] = {7730601, 7730602, 7730604, 7730603},
		},
		[1102] = {
			["Key"] = 1102,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628974849'), Game.TableDataManager:GetLangStr('str_11133628974850')},
			["WidgetStyle"] = 1,
			["SliderType"] = "Curve",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "/Game/Blueprint/CustomRole/CT_Test1.CT_Test1",
			["PresetIDs"] = {},
		},
		[1103] = {
			["Key"] = 1103,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628975105'), Game.TableDataManager:GetLangStr('str_11133628975106'), Game.TableDataManager:GetLangStr('str_11133628975107'), Game.TableDataManager:GetLangStr('str_11133628975108')},
			["WidgetStyle"] = 1,
			["SliderType"] = "Preset",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "",
			["PresetIDs"] = {7730805, 7730806, 7730807, 7730808},
		},
		[1104] = {
			["Key"] = 1104,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628975361'), Game.TableDataManager:GetLangStr('str_11133628974595'), Game.TableDataManager:GetLangStr('str_11133628974596'), Game.TableDataManager:GetLangStr('str_11133628975364')},
			["WidgetStyle"] = 1,
			["SliderType"] = "Preset",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "",
			["PresetIDs"] = {7730701, 7730702, 7730704, 7730703},
		},
		[1105] = {
			["Key"] = 1105,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628975617'), Game.TableDataManager:GetLangStr('str_11133628975618'), Game.TableDataManager:GetLangStr('str_11133628975619'), Game.TableDataManager:GetLangStr('str_11133628975620')},
			["WidgetStyle"] = 1,
			["SliderType"] = "Preset",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "",
			["PresetIDs"] = {7730901, 7730902, 7730904, 7730903},
		},
		[1301] = {
			["Key"] = 1301,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628976385'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Body_Height",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1302] = {
			["Key"] = 1302,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628976641'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Curve",
			["DataName"] = "",
			["DataRange"] = {},
			["CurvePath"] = "/Game/Blueprint/CustomRole/CT_Body.CT_Body",
			["PresetIDs"] = {},
		},
		[1303] = {
			["Key"] = 1303,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628976897'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chest",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1304] = {
			["Key"] = 1304,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628977153'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chest_Circumference",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1305] = {
			["Key"] = 1305,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628977409'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Shoulder_Width",
			["DataRange"] = {0.6, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1306] = {
			["Key"] = 1306,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628977665'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Waist",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1307] = {
			["Key"] = 1307,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628977921'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Hip",
			["DataRange"] = {0.2, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1401] = {
			["Key"] = 1401,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628978177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Head_Size",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1402] = {
			["Key"] = 1402,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628978433'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Neck_Length",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1403] = {
			["Key"] = 1403,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628978689'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Neck_Thickness",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1501] = {
			["Key"] = 1501,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628978945'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Trapezius_Muscle_Size",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1502] = {
			["Key"] = 1502,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628979201'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Arm-Length",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1503] = {
			["Key"] = 1503,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628979457'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Arm_Circumference",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1504] = {
			["Key"] = 1504,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628979713'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Arm_Circumference",
			["DataRange"] = {0, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1505] = {
			["Key"] = 1505,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628979969'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Palm_Size",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1506] = {
			["Key"] = 1506,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628980225'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Finger_Thickness",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1551] = {
			["Key"] = 1551,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628980481'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Leg_Total_Length",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1552] = {
			["Key"] = 1552,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628980737'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Thigh_Circumference",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1553] = {
			["Key"] = 1553,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628980993'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Calf_Circumference",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1554] = {
			["Key"] = 1554,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628981249'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Foot_Sole_Size",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1601] = {
			["Key"] = 1601,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628981505'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Alpha",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1602] = {
			["Key"] = 1602,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628981761'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Color_Intensity",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1650] = {
			["Key"] = 1650,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982017'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Base_Alpha",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1651] = {
			["Key"] = 1651,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982273'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Roughness",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1652] = {
			["Key"] = 1652,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Horizontal_Scale",
			["DataRange"] = {0.1, 0.4},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1653] = {
			["Key"] = 1653,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Vertical_Scale",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1654] = {
			["Key"] = 1654,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983041'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Horizontal_Shift",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1655] = {
			["Key"] = 1655,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Vertical_Shift",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1656] = {
			["Key"] = 1656,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyebrow_Rotation",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1701] = {
			["Key"] = 1701,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Freckle_Alpha",
			["DataRange"] = {0.1, 0.3},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1702] = {
			["Key"] = 1702,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Freckle_Size",
			["DataRange"] = {0.05, 0.1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1703] = {
			["Key"] = 1703,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984321'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Freckle_Density",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1801] = {
			["Key"] = 1801,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeshadow_Alpha",
			["DataRange"] = {0, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1802] = {
			["Key"] = 1802,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeshadow_Roughness",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1821] = {
			["Key"] = 1821,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeshadow_Second_Alpha",
			["DataRange"] = {0, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1822] = {
			["Key"] = 1822,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeshadow_Second_Roughness",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1851] = {
			["Key"] = 1851,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628985601'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Glitter_Roughness",
			["DataRange"] = {0.1, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1852] = {
			["Key"] = 1852,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628985857'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye Decal Metallic",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1853] = {
			["Key"] = 1853,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Decal_Alpha",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1901] = {
			["Key"] = 1901,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lip_Makeup_Alpha",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1902] = {
			["Key"] = 1902,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lip_Makeup_Roughness",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[1903] = {
			["Key"] = 1903,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628986881'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "LipSpecularColorValue",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2001] = {
			["Key"] = 2001,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lip_Paillette_Range_Switch",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2002] = {
			["Key"] = 2002,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984321'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lip_Paillette_Density",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2003] = {
			["Key"] = 2003,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628987649'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lip_Paillette_Size",
			["DataRange"] = {0.15, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2101] = {
			["Key"] = 2101,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Blush_Alpha",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2102] = {
			["Key"] = 2102,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982273'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Blush_Roughness",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2103] = {
			["Key"] = 2103,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628988417'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Blush_Level_Input_Min",
			["DataRange"] = {0, 0.3},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2104] = {
			["Key"] = 2104,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628988673'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Blush_Level_Input_Max",
			["DataRange"] = {0.7, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2105] = {
			["Key"] = 2105,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628988929'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Face_Blush_Offset_U",
			["DataRange"] = {0.45, 0.55},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2106] = {
			["Key"] = 2106,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Face_Blush_Offset_V",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2107] = {
			["Key"] = 2107,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Face_Blush_Rotation",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2201] = {
			["Key"] = 2201,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "FaceShadowAlpha",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2301] = {
			["Key"] = 2301,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628989953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyelash_Length",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2302] = {
			["Key"] = 2302,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628990209'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "TiltStrength",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2303] = {
			["Key"] = 2303,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628990465'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "CurlIntensity",
			["DataRange"] = {0, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2304] = {
			["Key"] = 2304,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628990721'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "thickness",
			["DataRange"] = {0.5, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2401] = {
			["Key"] = 2401,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_54632789346048'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Brightness",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2402] = {
			["Key"] = 2402,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_10721043701760'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Aging_Intensity_Switch",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2501] = {
			["Key"] = 2501,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628991489'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_Shadow_Alpha",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2502] = {
			["Key"] = 2502,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628991745'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_ColorMaskRange",
			["DataRange"] = {0, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2503] = {
			["Key"] = 2503,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628992001'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_Alpha",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2504] = {
			["Key"] = 2504,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628992257'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_Range",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2505] = {
			["Key"] = 2505,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628992513'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_RangeSmooth",
			["DataRange"] = {0.1, 0.4},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2506] = {
			["Key"] = 2506,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628985601'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Stubble_Roughness_Bias",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2801] = {
			["Key"] = 2801,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Forehead_Height",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2802] = {
			["Key"] = 2802,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Forehead_Angle",
			["DataRange"] = {0, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2803] = {
			["Key"] = 2803,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Forehead_Thickness",
			["DataRange"] = {0.1, 0.95},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2901] = {
			["Key"] = 2901,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Horizontal_Direction",
			["DataRange"] = {0.3, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2902] = {
			["Key"] = 2902,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994049'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Protrusion",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2903] = {
			["Key"] = 2903,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Vertical_Direction",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2904] = {
			["Key"] = 2904,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Angle",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2905] = {
			["Key"] = 2905,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Height",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2906] = {
			["Key"] = 2906,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_RotY",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[2907] = {
			["Key"] = 2907,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Size",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3001] = {
			["Key"] = 3001,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Horizontal_Direction",
			["DataRange"] = {0.45, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3002] = {
			["Key"] = 3002,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994049'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Angle",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3003] = {
			["Key"] = 3003,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Vertical_Direction",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3004] = {
			["Key"] = 3004,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Protrusion",
			["DataRange"] = {0.45, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3005] = {
			["Key"] = 3005,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Height",
			["DataRange"] = {0.1, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3006] = {
			["Key"] = 3006,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_RotY",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3007] = {
			["Key"] = 3007,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628997121'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Size",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3101] = {
			["Key"] = 3101,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Horizontal_Direction",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3102] = {
			["Key"] = 3102,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994049'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Depth",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3103] = {
			["Key"] = 3103,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Vertical_Direction",
			["DataRange"] = {0.1, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3104] = {
			["Key"] = 3104,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Angle",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3105] = {
			["Key"] = 3105,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Width",
			["DataRange"] = {0.3, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3106] = {
			["Key"] = 3106,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jaw_Protrusion",
			["DataRange"] = {0.15, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3201] = {
			["Key"] = 3201,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chin_Height",
			["DataRange"] = {0.2, 0.92},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3202] = {
			["Key"] = 3202,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chin_Width",
			["DataRange"] = {0, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3203] = {
			["Key"] = 3203,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chin_Thickness",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3204] = {
			["Key"] = 3204,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chin_Depth",
			["DataRange"] = {0.2, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3205] = {
			["Key"] = 3205,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628999937'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Chin_Angle",
			["DataRange"] = {0.3, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3301] = {
			["Key"] = 3301,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629000193'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Horizontal_Direction",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3302] = {
			["Key"] = 3302,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629000449'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Vertical_Direction",
			["DataRange"] = {0.35, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3303] = {
			["Key"] = 3303,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Tilt",
			["DataRange"] = {0, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3304] = {
			["Key"] = 3304,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Depth",
			["DataRange"] = {0.25, 0.55},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3305] = {
			["Key"] = 3305,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Size",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3401] = {
			["Key"] = 3401,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Horizontal_Direction",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3402] = {
			["Key"] = 3402,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629001729'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Angle",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3403] = {
			["Key"] = 3403,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Vertical_Direction",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3404] = {
			["Key"] = 3404,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Depth",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3405] = {
			["Key"] = 3405,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Tilt",
			["DataRange"] = {0.3, 0.55},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3406] = {
			["Key"] = 3406,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629002753'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Inner_Eye_Corner_Width",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3501] = {
			["Key"] = 3501,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Horizontal_Direction",
			["DataRange"] = {0.85, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3502] = {
			["Key"] = 3502,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629001729'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Tilt",
			["DataRange"] = {0.35, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3503] = {
			["Key"] = 3503,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Vertical_Direction",
			["DataRange"] = {0, 0.4},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3504] = {
			["Key"] = 3504,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Depth",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3505] = {
			["Key"] = 3505,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Angle",
			["DataRange"] = {0.5, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3506] = {
			["Key"] = 3506,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629002753'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Outer_Eye_Corner_Width",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3601] = {
			["Key"] = 3601,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lid_Depth",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3602] = {
			["Key"] = 3602,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lid_Angle",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3603] = {
			["Key"] = 3603,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lid_Tilt",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3604] = {
			["Key"] = 3604,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lid_Height",
			["DataRange"] = {0.35, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3605] = {
			["Key"] = 3605,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lid_Length",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3701] = {
			["Key"] = 3701,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Protrusion",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3702] = {
			["Key"] = 3702,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Depth",
			["DataRange"] = {0, 0.2},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3703] = {
			["Key"] = 3703,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629006337'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Angle",
			["DataRange"] = {0.25, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3704] = {
			["Key"] = 3704,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Tilt",
			["DataRange"] = {0.15, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3705] = {
			["Key"] = 3705,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629002753'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Height",
			["DataRange"] = {0.45, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3706] = {
			["Key"] = 3706,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lid_Length",
			["DataRange"] = {0.25, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3651] = {
			["Key"] = 3651,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629007361'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_PosX",
			["DataRange"] = {0.1, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3652] = {
			["Key"] = 3652,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629007617'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_PosY",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3653] = {
			["Key"] = 3653,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629007873'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_PosZ",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3654] = {
			["Key"] = 3654,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629008129'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_RotX",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3655] = {
			["Key"] = 3655,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629008385'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_RotY",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3656] = {
			["Key"] = 3656,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629008641'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_RotZ",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3657] = {
			["Key"] = 3657,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629008897'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_SclX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3658] = {
			["Key"] = 3658,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629009153'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Inside_SclZ",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3659] = {
			["Key"] = 3659,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629009409'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_PosX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3660] = {
			["Key"] = 3660,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629009665'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_PosY",
			["DataRange"] = {0.5, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3661] = {
			["Key"] = 3661,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629009921'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_PosZ",
			["DataRange"] = {0.1, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3662] = {
			["Key"] = 3662,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629010177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_RotX",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3663] = {
			["Key"] = 3663,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629010433'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_RotY",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3664] = {
			["Key"] = 3664,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629010689'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_RotZ",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3665] = {
			["Key"] = 3665,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629010945'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_SclX",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3666] = {
			["Key"] = 3666,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629011201'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Middle_SclZ",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3667] = {
			["Key"] = 3667,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629011457'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_PosX",
			["DataRange"] = {0.5, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3668] = {
			["Key"] = 3668,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629011713'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_PosY",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3669] = {
			["Key"] = 3669,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629011969'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_PosZ",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3670] = {
			["Key"] = 3670,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629012225'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_RotX",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3671] = {
			["Key"] = 3671,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629012481'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_RotY",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3672] = {
			["Key"] = 3672,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629012737'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_RotZ",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3673] = {
			["Key"] = 3673,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629012993'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_SclX",
			["DataRange"] = {0.25, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3674] = {
			["Key"] = 3674,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629013249'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Outside_SclZ",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3751] = {
			["Key"] = 3751,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629013505'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_PosX",
			["DataRange"] = {0.2, 0.95},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3752] = {
			["Key"] = 3752,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629013761'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_PosY",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3753] = {
			["Key"] = 3753,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629014017'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_PosZ",
			["DataRange"] = {0.1, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3754] = {
			["Key"] = 3754,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629014273'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_RotX",
			["DataRange"] = {0.35, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3755] = {
			["Key"] = 3755,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629014529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_RotY",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3756] = {
			["Key"] = 3756,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629014785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_RotZ",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3757] = {
			["Key"] = 3757,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629015041'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_SclX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3758] = {
			["Key"] = 3758,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629015297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Inside_SclZ",
			["DataRange"] = {0.2, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3759] = {
			["Key"] = 3759,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629015553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_PosX",
			["DataRange"] = {0.25, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3760] = {
			["Key"] = 3760,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629015809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_PosY",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3761] = {
			["Key"] = 3761,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629016065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_PosZ",
			["DataRange"] = {0.25, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3762] = {
			["Key"] = 3762,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629016321'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_RotX",
			["DataRange"] = {0.35, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3763] = {
			["Key"] = 3763,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629016577'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_RotY",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3764] = {
			["Key"] = 3764,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629016833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_RotZ",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3765] = {
			["Key"] = 3765,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629017089'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_SclX",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3766] = {
			["Key"] = 3766,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629017345'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Middle_SclZ",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3767] = {
			["Key"] = 3767,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629017601'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_PosX",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3768] = {
			["Key"] = 3768,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629017857'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_PosY",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3769] = {
			["Key"] = 3769,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629018113'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_PosZ",
			["DataRange"] = {0.35, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3770] = {
			["Key"] = 3770,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629018369'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_RotX",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3771] = {
			["Key"] = 3771,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629018625'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_RotY",
			["DataRange"] = {0.35, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3772] = {
			["Key"] = 3772,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629018881'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_RotZ",
			["DataRange"] = {0.35, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3773] = {
			["Key"] = 3773,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629019137'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_SclX",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3774] = {
			["Key"] = 3774,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629019393'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Outside_SclZ",
			["DataRange"] = {0.25, 0.95},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3801] = {
			["Key"] = 3801,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Horizontal_Direction",
			["DataRange"] = {0.35, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3802] = {
			["Key"] = 3802,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994049'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Tilt",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3803] = {
			["Key"] = 3803,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Vertical_Direction",
			["DataRange"] = {0, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3804] = {
			["Key"] = 3804,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Angle",
			["DataRange"] = {0.5, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3805] = {
			["Key"] = 3805,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Depth",
			["DataRange"] = {0.35, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3806] = {
			["Key"] = 3806,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Protrusion",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3807] = {
			["Key"] = 3807,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629021185'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Bone_Scale",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3901] = {
			["Key"] = 3901,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Height",
			["DataRange"] = {0.2, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3902] = {
			["Key"] = 3902,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Depth",
			["DataRange"] = {0.35, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3903] = {
			["Key"] = 3903,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629021953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Angle",
			["DataRange"] = {0.2, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[3904] = {
			["Key"] = 3904,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Width",
			["DataRange"] = {0.15, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4101] = {
			["Key"] = 4101,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Mouth_Height",
			["DataRange"] = {0.45, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4102] = {
			["Key"] = 4102,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Mouth_Depth",
			["DataRange"] = {0, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4103] = {
			["Key"] = 4103,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Mouth_Angle",
			["DataRange"] = {0.1, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4104] = {
			["Key"] = 4104,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Mouth_Width",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4105] = {
			["Key"] = 4105,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Mouth_Height_Scl",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4201] = {
			["Key"] = 4201,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629023745'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Horizontal_Direction",
			["DataRange"] = {0, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4202] = {
			["Key"] = 4202,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629024001'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Angle",
			["DataRange"] = {0.4, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4203] = {
			["Key"] = 4203,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Vertical_Direction",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4204] = {
			["Key"] = 4204,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Scale_Z",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4205] = {
			["Key"] = 4205,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Depth",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4206] = {
			["Key"] = 4206,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Tilt",
			["DataRange"] = {0.3, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4207] = {
			["Key"] = 4207,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629025281'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Rotation",
			["DataRange"] = {0.1, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4208] = {
			["Key"] = 4208,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629025537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_End_Scale_X",
			["DataRange"] = {0.4, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4301] = {
			["Key"] = 4301,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Horizontal_Direction",
			["DataRange"] = {0.3, 0.95},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4302] = {
			["Key"] = 4302,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Vertical_Direction",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4303] = {
			["Key"] = 4303,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Depth",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4304] = {
			["Key"] = 4304,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629026561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Angle",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4305] = {
			["Key"] = 4305,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Tilt",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4306] = {
			["Key"] = 4306,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Rotation",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4307] = {
			["Key"] = 4307,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629021185'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Scale_X",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4308] = {
			["Key"] = 4308,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629027585'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Middle_Scale_Z",
			["DataRange"] = {0.35, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4401] = {
			["Key"] = 4401,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993793'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Horizontal_Direction",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4402] = {
			["Key"] = 4402,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994049'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Angle",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4403] = {
			["Key"] = 4403,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Vertical_Direction",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4404] = {
			["Key"] = 4404,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Rotation",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4405] = {
			["Key"] = 4405,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Depth",
			["DataRange"] = {0.2, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4406] = {
			["Key"] = 4406,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Tilt",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4407] = {
			["Key"] = 4407,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629021185'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Scale_X",
			["DataRange"] = {0.35, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4408] = {
			["Key"] = 4408,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629027585'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Tail_Scale_Z",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4501] = {
			["Key"] = 4501,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Start_Height",
			["DataRange"] = {0.25, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4502] = {
			["Key"] = 4502,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Start_Depth",
			["DataRange"] = {0.345, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4503] = {
			["Key"] = 4503,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629030401'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Start_Angle",
			["DataRange"] = {0.3, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4504] = {
			["Key"] = 4504,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Start_Width",
			["DataRange"] = {0, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4601] = {
			["Key"] = 4601,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Height",
			["DataRange"] = {0.3, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4602] = {
			["Key"] = 4602,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Depth",
			["DataRange"] = {0, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4603] = {
			["Key"] = 4603,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629030401'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Angle",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4604] = {
			["Key"] = 4604,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Bridge_Width",
			["DataRange"] = {0, 0.4},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4701] = {
			["Key"] = 4701,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_Height",
			["DataRange"] = {0.2, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4702] = {
			["Key"] = 4702,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_Depth",
			["DataRange"] = {0, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4703] = {
			["Key"] = 4703,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629032449'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_Angle",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4704] = {
			["Key"] = 4704,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_SclX",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4705] = {
			["Key"] = 4705,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628989953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_SclY",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4706] = {
			["Key"] = 4706,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629033217'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nasal_Spine_SclZ",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4801] = {
			["Key"] = 4801,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Horizontal_Direction",
			["DataRange"] = {0.4, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4802] = {
			["Key"] = 4802,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Vertical_Direction",
			["DataRange"] = {0.1, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4803] = {
			["Key"] = 4803,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Depth",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4804] = {
			["Key"] = 4804,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629006337'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Angle",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4805] = {
			["Key"] = 4805,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Incline",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4806] = {
			["Key"] = 4806,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Rotation",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4807] = {
			["Key"] = 4807,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Thickness",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4808] = {
			["Key"] = 4808,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628989953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nostril_Height",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4901] = {
			["Key"] = 4901,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Horizontal_Direction",
			["DataRange"] = {0.1, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4902] = {
			["Key"] = 4902,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629026561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Angle",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4903] = {
			["Key"] = 4903,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Vertical_Direction",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4904] = {
			["Key"] = 4904,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994561'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Incline",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4905] = {
			["Key"] = 4905,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Depth",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[4906] = {
			["Key"] = 4906,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Nose_Side_Rotation",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5101] = {
			["Key"] = 5101,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Horizontal_Direction",
			["DataRange"] = {0.2, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5102] = {
			["Key"] = 5102,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Vertical_Direction",
			["DataRange"] = {0.35, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5103] = {
			["Key"] = 5103,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Depth",
			["DataRange"] = {0.3, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5104] = {
			["Key"] = 5104,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Angle",
			["DataRange"] = {0.1, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5105] = {
			["Key"] = 5105,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629038081'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Tilt",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5106] = {
			["Key"] = 5106,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Rotation",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5107] = {
			["Key"] = 5107,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Corner_of_Mouth_Scale",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5201] = {
			["Key"] = 5201,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Horizontal_Direction",
			["DataRange"] = {0, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5202] = {
			["Key"] = 5202,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Vertical_Direction",
			["DataRange"] = {0.3, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5203] = {
			["Key"] = 5203,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Depth",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5204] = {
			["Key"] = 5204,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Angle",
			["DataRange"] = {0.25, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5205] = {
			["Key"] = 5205,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Tilt",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5206] = {
			["Key"] = 5206,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Size",
			["DataRange"] = {0.15, 0.55},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5207] = {
			["Key"] = 5207,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Width",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5208] = {
			["Key"] = 5208,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629040641'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Up_Teeth_Depth",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5301] = {
			["Key"] = 5301,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Horizontal_Direction",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5302] = {
			["Key"] = 5302,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Vertical_Direction",
			["DataRange"] = {0.3, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5303] = {
			["Key"] = 5303,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Depth",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5304] = {
			["Key"] = 5304,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Size",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5306] = {
			["Key"] = 5306,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Angle",
			["DataRange"] = {0.45, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5307] = {
			["Key"] = 5307,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Width",
			["DataRange"] = {0.25, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5308] = {
			["Key"] = 5308,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Tilt",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5309] = {
			["Key"] = 5309,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042689'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Down_Teeth_Depth",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5401] = {
			["Key"] = 5401,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeliner_Alpha",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5402] = {
			["Key"] = 5402,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982273'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eyeliner_Roughness",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5451] = {
			["Key"] = 5451,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629043457'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Size",
			["DataRange"] = {0.25, 0.35},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5452] = {
			["Key"] = 5452,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629043713'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Pupil_Size",
			["DataRange"] = {0.5, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5453] = {
			["Key"] = 5453,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629043969'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Edge_Softness",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5454] = {
			["Key"] = 5454,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629044225'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EmissionIntensity_Iris",
			["DataRange"] = {0, 0.1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5455] = {
			["Key"] = 5455,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629044481'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Glitter_Density_Switch",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5456] = {
			["Key"] = 5456,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629044737'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Glitter_Brightness",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5457] = {
			["Key"] = 5457,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629044993'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Glitter_Size",
			["DataRange"] = {0.1, 0.3},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5458] = {
			["Key"] = 5458,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629045249'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Iris_Glitter_Speed",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5501] = {
			["Key"] = 5501,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Horizontal_Direction",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5502] = {
			["Key"] = 5502,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Vertical_Direction",
			["DataRange"] = {0.3, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5503] = {
			["Key"] = 5503,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Depth",
			["DataRange"] = {0.3, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5504] = {
			["Key"] = 5504,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Angle",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5505] = {
			["Key"] = 5505,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Tilt",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5506] = {
			["Key"] = 5506,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628995073'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_RotY",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5507] = {
			["Key"] = 5507,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Size",
			["DataRange"] = {0, 0.35},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5601] = {
			["Key"] = 5601,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Top_Horizontal_Direction",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5602] = {
			["Key"] = 5602,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Top_Vertical_Direction",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5603] = {
			["Key"] = 5603,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Top_Depth",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5604] = {
			["Key"] = 5604,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Top_Size",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5701] = {
			["Key"] = 5701,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Pinnacle_Horizontal_Direction",
			["DataRange"] = {0.6, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5702] = {
			["Key"] = 5702,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Pinnacle_Vertical_Direction",
			["DataRange"] = {0.55, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5703] = {
			["Key"] = 5703,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Pinnacle_Depth",
			["DataRange"] = {0, 0.3},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5704] = {
			["Key"] = 5704,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Pinnacle_Size",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5801] = {
			["Key"] = 5801,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Outside_Horizontal_Direction",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5802] = {
			["Key"] = 5802,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Outside_Vertical_Direction",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5803] = {
			["Key"] = 5803,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Outside_Depth",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5804] = {
			["Key"] = 5804,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Outside_Size",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5901] = {
			["Key"] = 5901,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Lobule_Horizontal_Direction",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5902] = {
			["Key"] = 5902,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Lobule_Vertical_Direction",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5903] = {
			["Key"] = 5903,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Lobule_Depth",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[5904] = {
			["Key"] = 5904,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Ear_Lobule_Size",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6001] = {
			["Key"] = 6001,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Horizontal_Direction",
			["DataRange"] = {0.45, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6002] = {
			["Key"] = 6002,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Vertical_Direction",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6003] = {
			["Key"] = 6003,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Protrusion",
			["DataRange"] = {0.4, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6004] = {
			["Key"] = 6004,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Angle",
			["DataRange"] = {0.35, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6005] = {
			["Key"] = 6005,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Height",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6006] = {
			["Key"] = 6006,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Size",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6101] = {
			["Key"] = 6101,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_Horizontal_Direction",
			["DataRange"] = {0.3, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6102] = {
			["Key"] = 6102,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_Vertical_Direction",
			["DataRange"] = {0, 0.85},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6103] = {
			["Key"] = 6103,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_Depth",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6104] = {
			["Key"] = 6104,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_Angle",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6105] = {
			["Key"] = 6105,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_Protrusion",
			["DataRange"] = {0.45, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6106] = {
			["Key"] = 6106,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Jawline_RotY",
			["DataRange"] = {0.35, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6201] = {
			["Key"] = 6201,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Double_Chin_Height",
			["DataRange"] = {0, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6202] = {
			["Key"] = 6202,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Double_Chin_Depth",
			["DataRange"] = {0.25, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6203] = {
			["Key"] = 6203,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628993537'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Double_Chin_Angle",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6204] = {
			["Key"] = 6204,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629055233'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Double_Chin_Size",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6301] = {
			["Key"] = 6301,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_PosX",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6302] = {
			["Key"] = 6302,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_PosY",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6303] = {
			["Key"] = 6303,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_PosZ",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6304] = {
			["Key"] = 6304,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629022977'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_RotX",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6305] = {
			["Key"] = 6305,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629042177'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_RotY",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6306] = {
			["Key"] = 6306,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983553'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_RotZ",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6307] = {
			["Key"] = 6307,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629038081'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Wrinkle_SclA",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6401] = {
			["Key"] = 6401,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Center_Vertical_Direction",
			["DataRange"] = {0.1, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6402] = {
			["Key"] = 6402,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Brow_Center_Depth",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6501] = {
			["Key"] = 6501,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Ball_Horizontal_Direction",
			["DataRange"] = {0.45, 0.55},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6502] = {
			["Key"] = 6502,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye_Ball_Vertical_Direction",
			["DataRange"] = {0.425, 0.575},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6601] = {
			["Key"] = 6601,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Middle_Height",
			["DataRange"] = {0.1, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6602] = {
			["Key"] = 6602,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Middle_Depth",
			["DataRange"] = {0.25, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6603] = {
			["Key"] = 6603,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Middle_Width",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6604] = {
			["Key"] = 6604,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Middle_Thickness",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6701] = {
			["Key"] = 6701,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Middle_Height",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6702] = {
			["Key"] = 6702,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628994817'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Middle_Depth",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6703] = {
			["Key"] = 6703,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984065'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Middle_Thickness",
			["DataRange"] = {0.3, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6704] = {
			["Key"] = 6704,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982529'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Middle_Width",
			["DataRange"] = {0.4, 0.9},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6801] = {
			["Key"] = 6801,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629060353'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_PosX",
			["DataRange"] = {0.4, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6802] = {
			["Key"] = 6802,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629060609'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_PosY",
			["DataRange"] = {0.15, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6803] = {
			["Key"] = 6803,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629060865'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_PosZ",
			["DataRange"] = {0.35, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6804] = {
			["Key"] = 6804,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629061121'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_RotX",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6805] = {
			["Key"] = 6805,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629061377'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_RotY",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6806] = {
			["Key"] = 6806,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629061633'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_RotZ",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6807] = {
			["Key"] = 6807,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629061889'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_SclX",
			["DataRange"] = {0, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6808] = {
			["Key"] = 6808,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629062145'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Inside_SclZ",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6809] = {
			["Key"] = 6809,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629062401'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_PosX",
			["DataRange"] = {0.3, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6810] = {
			["Key"] = 6810,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629062657'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_PosY",
			["DataRange"] = {0.1, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6811] = {
			["Key"] = 6811,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629062913'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_PosZ",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6812] = {
			["Key"] = 6812,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629063169'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_RotX",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6813] = {
			["Key"] = 6813,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629063425'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_RotY",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6814] = {
			["Key"] = 6814,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629063681'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_RotZ",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6815] = {
			["Key"] = 6815,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629063937'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_SclX",
			["DataRange"] = {0.3, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6816] = {
			["Key"] = 6816,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629064193'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Upper_Lip_Outside_SclZ",
			["DataRange"] = {0.2, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6901] = {
			["Key"] = 6901,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629064449'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_PosX",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6902] = {
			["Key"] = 6902,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629064705'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_PosY",
			["DataRange"] = {0.3, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6903] = {
			["Key"] = 6903,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629064961'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_PosZ",
			["DataRange"] = {0.4, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6904] = {
			["Key"] = 6904,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629065217'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_RotX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6905] = {
			["Key"] = 6905,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629065473'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_RotY",
			["DataRange"] = {0.4, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6906] = {
			["Key"] = 6906,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629065729'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_RotZ",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6907] = {
			["Key"] = 6907,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629065985'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_SclX",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6908] = {
			["Key"] = 6908,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629066241'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Inside_SclZ",
			["DataRange"] = {0.1, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6909] = {
			["Key"] = 6909,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629066497'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_PosX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6910] = {
			["Key"] = 6910,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629066753'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_PosY",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6911] = {
			["Key"] = 6911,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629067009'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_PosZ",
			["DataRange"] = {0, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6912] = {
			["Key"] = 6912,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629067265'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_RotX",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6913] = {
			["Key"] = 6913,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629067521'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_RotY",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6914] = {
			["Key"] = 6914,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629067777'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_RotZ",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6915] = {
			["Key"] = 6915,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629068033'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_SclX",
			["DataRange"] = {0, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[6916] = {
			["Key"] = 6916,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629068289'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Lower_Lip_Outside_SclZ",
			["DataRange"] = {0, 0.95},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7001] = {
			["Key"] = 7001,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629068545'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "High_Precision_Eye_Normal_Intensity",
			["DataRange"] = {0.1, 0.3},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7002] = {
			["Key"] = 7002,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EyeLids_ColorIntensity",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7101] = {
			["Key"] = 7101,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EyeShadow_Paillette_Range_Switch",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7102] = {
			["Key"] = 7102,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984321'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EyeShadow_Paillette_Density",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7103] = {
			["Key"] = 7103,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628987649'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EyeShadow_Paillette_Size",
			["DataRange"] = {0.1, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7201] = {
			["Key"] = 7201,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628987649'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Metal Cap Size",
			["DataRange"] = {0.2, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7202] = {
			["Key"] = 7202,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629068545'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Matcap_Intensity",
			["DataRange"] = {0.02, 0.1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7203] = {
			["Key"] = 7203,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629070337'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Specular",
			["DataRange"] = {0, 0.5},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7204] = {
			["Key"] = 7204,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629070593'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Pupil_Highlight_Intensity",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7205] = {
			["Key"] = 7205,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629070849'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Pupil_Highlight_Size",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7301] = {
			["Key"] = 7301,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629071105'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EmissionIntensity_Sclera",
			["DataRange"] = {0, 0.05},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7302] = {
			["Key"] = 7302,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629071361'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Eye AO Intensity",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7401] = {
			["Key"] = 7401,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Alpha",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7402] = {
			["Key"] = 7402,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629071873'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Normal_Intensity",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7403] = {
			["Key"] = 7403,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628988929'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Offset_U",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7404] = {
			["Key"] = 7404,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Offset_V",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7405] = {
			["Key"] = 7405,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628989953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Scale_U",
			["DataRange"] = {0.02, 0.06},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7406] = {
			["Key"] = 7406,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Scale_V",
			["DataRange"] = {0.02, 0.06},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7407] = {
			["Key"] = 7407,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629073153'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Rotation",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7408] = {
			["Key"] = 7408,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin Decal Roughness",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7409] = {
			["Key"] = 7409,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629073665'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin Decal Metallic",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7501] = {
			["Key"] = 7501,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983809'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Alpha2",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7502] = {
			["Key"] = 7502,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629071873'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Normal_Intensity2",
			["DataRange"] = {0.2, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7503] = {
			["Key"] = 7503,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628988929'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Offset_U2",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7504] = {
			["Key"] = 7504,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628983297'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Offset_V2",
			["DataRange"] = {0.25, 0.75},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7505] = {
			["Key"] = 7505,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628989953'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Scale_U2",
			["DataRange"] = {0.02, 0.06},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7506] = {
			["Key"] = 7506,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628982785'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Scale_V2",
			["DataRange"] = {0.02, 0.06},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7507] = {
			["Key"] = 7507,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629073153'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin_Decal_Rotation2",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7508] = {
			["Key"] = 7508,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133628984833'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin Decal Roughness2",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7509] = {
			["Key"] = 7509,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629073665'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Skin Decal Metallic2",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7601] = {
			["Key"] = 7601,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629068545'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "HiRes_Normal_EyeBag_Intensity",
			["DataRange"] = {0.1, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7602] = {
			["Key"] = 7602,
			["DataType"] = "MakeUp",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629076481'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "EyeBag_ColorIntensity",
			["DataRange"] = {0, 1},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7701] = {
			["Key"] = 7701,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629076737'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Forehead_Height",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7702] = {
			["Key"] = 7702,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629076993'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Temporal_Size",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7703] = {
			["Key"] = 7703,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629077249'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheekbone_Size",
			["DataRange"] = {0.2, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7704] = {
			["Key"] = 7704,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629077505'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Cheek_Size",
			["DataRange"] = {0.2, 0.8},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7705] = {
			["Key"] = 7705,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629077761'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Vertical_Dimension",
			["DataRange"] = {0.4, 0.7},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7706] = {
			["Key"] = 7706,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629078017'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Half_Face_Size",
			["DataRange"] = {0.35, 0.65},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
		[7707] = {
			["Key"] = 7707,
			["DataType"] = "Face",
			["WidgetTitle"] = {Game.TableDataManager:GetLangStr('str_11133629078273'), "100"},
			["WidgetStyle"] = 0,
			["SliderType"] = "Param",
			["DataName"] = "Half_Face_Depth",
			["DataRange"] = {0.35, 0.6},
			["CurvePath"] = "",
			["PresetIDs"] = {},
		},
	},
}

return TopData
