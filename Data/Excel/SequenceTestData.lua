--
-- 表名: $Player_角色.xlsx  页名：$SequenceTest_序列测试
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["TestText"] = Game.TableDataManager:GetLangStr('str_40957613442560'),
			["TestSeclect"] = {},
			["PushCamera"] = false,
		},
		[2] = {
			["ID"] = 2,
			["TestText"] = Game.TableDataManager:GetLangStr('str_40957613442816'),
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881878273'), Game.TableDataManager:GetLangStr('str_40957881878274'), Game.TableDataManager:GetLangStr('str_40957881878275')},
			["PushCamera"] = true,
		},
		[3] = {
			["ID"] = 3,
			["TestText"] = "",
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881878529'), Game.TableDataManager:GetLangStr('str_40957881878530'), Game.TableDataManager:GetLangStr('str_40957881878531')},
			["PushCamera"] = true,
		},
		[4] = {
			["ID"] = 4,
			["TestText"] = Game.TableDataManager:GetLangStr('str_40957613443328'),
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881878785'), Game.TableDataManager:GetLangStr('str_40957881878786'), Game.TableDataManager:GetLangStr('str_40957881878787')},
			["PushCamera"] = true,
		},
		[5] = {
			["ID"] = 5,
			["TestText"] = "",
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881879041'), Game.TableDataManager:GetLangStr('str_40957881879042'), Game.TableDataManager:GetLangStr('str_40957881879043')},
			["PushCamera"] = true,
		},
		[6] = {
			["ID"] = 6,
			["TestText"] = Game.TableDataManager:GetLangStr('str_40957613443840'),
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881879297'), Game.TableDataManager:GetLangStr('str_40957881879298'), Game.TableDataManager:GetLangStr('str_40957881879299')},
			["PushCamera"] = true,
		},
		[7] = {
			["ID"] = 7,
			["TestText"] = "",
			["TestSeclect"] = {Game.TableDataManager:GetLangStr('str_40957881879553'), Game.TableDataManager:GetLangStr('str_40957881879554'), Game.TableDataManager:GetLangStr('str_40957881879555')},
			["PushCamera"] = false,
		},
		[8] = {
			["ID"] = 8,
			["TestText"] = Game.TableDataManager:GetLangStr('str_40957613444352'),
			["TestSeclect"] = {},
			["PushCamera"] = true,
		},
	},
}

return TopData
