--
-- 表名: $Manor_庄园.xlsx  页名：$Component
--

local TopData = {
	data = {
		[4300000] = {
			["ID"] = 4300000,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543675904'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2101,
			["GroupSubId"] = 3101,
			["FrameValue"] = 1,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 63, 600},
			["ModelPartComponent"] = {"/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_bot.SM_ManorNew_Wall01_bot", "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01_top.SM_ManorNew_Wall01_top"},
			["CombineComponent"] = "{(4300006),(300,0,0)}{(4300006),(-300,0,0)}",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033336832'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Wall01.SM_ManorNew_Wall01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 30,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300001] = {
			["ID"] = 4300001,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543676160'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2101,
			["GroupSubId"] = 3102,
			["FrameValue"] = 2,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 63, 600},
			["ModelPartComponent"] = {"/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_bot.SM_ManorNew_Window01_bot", "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01_top.SM_ManorNew_Window01_top"},
			["CombineComponent"] = "{(4300006),(300,0,0)}{(4300006),(-300,0,0)}",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033337088'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Window01.SM_ManorNew_Window01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300002] = {
			["ID"] = 4300002,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543676416'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2101,
			["GroupSubId"] = 3103,
			["FrameValue"] = 3,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 70, 600},
			["ModelPartComponent"] = {"/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_bot.SM_ManorNew_Door01_bot", "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01_top.SM_ManorNew_Door01_top"},
			["CombineComponent"] = "{(4300006),(300,0,0)}{(4300006),(-300,0,0)}",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033337344'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Door01.SM_ManorNew_Door01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300003] = {
			["ID"] = 4300003,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543676672'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 1,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543676672'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot01.SM_ManorNew_Pillar01_bot01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300004] = {
			["ID"] = 4300004,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543676928'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 2,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543676928'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot02.SM_ManorNew_Pillar01_bot02",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300005] = {
			["ID"] = 4300005,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543677184'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 3,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543677184'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_flair.SM_ManorNew_Pillar01_flair",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300006] = {
			["ID"] = 4300006,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543677440'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 4,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {200, 200, 600},
			["ModelPartComponent"] = {"/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_bot.SM_ManorNew_Pillar01_bot", "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top"},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033338368'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300007] = {
			["ID"] = 4300007,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543677696'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 5,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {131, 131, 600},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543677696'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300008] = {
			["ID"] = 4300008,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543677952'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 6,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543677952'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_top.SM_ManorNew_Pillar01_top",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300009] = {
			["ID"] = 4300009,
			["FurnitureName"] = "",
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 0,
			["GroupSubId"] = 0,
			["FrameValue"] = 7,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = "",
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01_mid.SM_ManorNew_Pillar01_mid",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300010] = {
			["ID"] = 4300010,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543678464'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2102,
			["GroupSubId"] = 0,
			["FrameValue"] = 8,
			["CanShow"] = false,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543678464'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Pillar01.SM_ManorNew_Pillar01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300011] = {
			["ID"] = 4300011,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543678720'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2107,
			["GroupSubId"] = 0,
			["FrameValue"] = 1,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {95, 95, 280},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543678720'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing02.SM_ManorNew_RoofRailing02",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300012] = {
			["ID"] = 4300012,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543678976'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2106,
			["GroupSubId"] = 0,
			["FrameValue"] = 1,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 60, 151},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543678976'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_RoofRailing01.SM_ManorNew_RoofRailing01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300013] = {
			["ID"] = 4300013,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543679232'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2103,
			["GroupSubId"] = 0,
			["FrameValue"] = 11,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 48},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 600,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033340160'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Floor01.SM_ManorNew_Floor01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300014] = {
			["ID"] = 4300014,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543679488'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 46,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 437},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543679488'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type01.SM_ManorNew_Type01",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300015] = {
			["ID"] = 4300015,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543679744'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 1,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 437},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543679744'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type02.SM_ManorNew_Type02",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300016] = {
			["ID"] = 4300016,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543680000'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 5,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 437},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543680000'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type03.SM_ManorNew_Type03",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300017] = {
			["ID"] = 4300017,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543680256'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 23,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543680256'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type04.SM_ManorNew_Type04",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300018] = {
			["ID"] = 4300018,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543680512'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 11,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543680512'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type05.SM_ManorNew_Type05",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300019] = {
			["ID"] = 4300019,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543680768'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 15,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 345},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543680768'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type06.SM_ManorNew_Type06",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300020] = {
			["ID"] = 4300020,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543681024'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 9,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 345},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543681024'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type07.SM_ManorNew_Type07",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300021] = {
			["ID"] = 4300021,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543681280'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 31,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 345},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543681280'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type08.SM_ManorNew_Type08",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300022] = {
			["ID"] = 4300022,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543681536'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 19,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543681536'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type09.SM_ManorNew_Type09",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300023] = {
			["ID"] = 4300023,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543681792'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 27,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543681792'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type10.SM_ManorNew_Type10",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300024] = {
			["ID"] = 4300024,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543682048'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 32,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543682048'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type11.SM_ManorNew_Type11",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300025] = {
			["ID"] = 4300025,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543682304'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 40,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543682304'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type12.SM_ManorNew_Type12",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300026] = {
			["ID"] = 4300026,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543682560'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 36,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543682560'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type13.SM_ManorNew_Type13",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300027] = {
			["ID"] = 4300027,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543682816'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 99,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 58},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543682816'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type14.SM_ManorNew_Type14",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300028] = {
			["ID"] = 4300028,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543683072'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2104,
			["GroupSubId"] = 0,
			["FrameValue"] = 42,
			["CanShow"] = true,
			["CollisionType"] = 1,
			["AreaSize"] = 0,
			["Volume"] = {600, 600, 644},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543683072'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Type15.SM_ManorNew_Type15",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300029] = {
			["ID"] = 4300029,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543683328'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2105,
			["GroupSubId"] = 3504,
			["FrameValue"] = 1,
			["CanShow"] = false,
			["CollisionType"] = 2,
			["AreaSize"] = 0,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34360543683328'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Double01_Stair.SM_ManorNew_Double01_Stair",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300030] = {
			["ID"] = 4300030,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543683584'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_ZBSMY_M.UI_Item_Icon_ZBSMY_M",
			["TypeId"] = 1001,
			["GroupId"] = 2105,
			["GroupSubId"] = 3501,
			["FrameValue"] = 2,
			["CanShow"] = true,
			["CollisionType"] = 2,
			["AreaSize"] = 2,
			["Volume"] = {1200, 600, 822},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033344512'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Single01_Stair.SM_ManorNew_Single01_Stair",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 10,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300031] = {
			["ID"] = 4300031,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543683840'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_163_M.UI_Item_Icon_163_M",
			["TypeId"] = 1001,
			["GroupId"] = 2105,
			["GroupSubId"] = 3502,
			["FrameValue"] = 3,
			["CanShow"] = true,
			["CollisionType"] = 2,
			["AreaSize"] = 3,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033344768'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn01_Stair.SM_ManorNew_Turn01_Stair",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
		[4300032] = {
			["ID"] = 4300032,
			["FurnitureName"] = Game.TableDataManager:GetLangStr('str_34360543684096'),
			["icon"] = "/Game/Arts/UI_2/Resource/Item/NotAtlas/Prop/Medium/UI_Item_Icon_Megaphone_Affection_M.UI_Item_Icon_Megaphone_Affection_M",
			["TypeId"] = 1001,
			["GroupId"] = 2105,
			["GroupSubId"] = 3503,
			["FrameValue"] = 4,
			["CanShow"] = true,
			["CollisionType"] = 2,
			["AreaSize"] = 3,
			["Volume"] = {},
			["ModelPartComponent"] = {},
			["CombineComponent"] = "",
			["MinMoveGrid"] = 0,
			["FurnitureDes"] = Game.TableDataManager:GetLangStr('str_34364033345024'),
			["quality"] = 0,
			["model"] = "/Game/Arts/Environment/Mesh/Building/SM_Manor/Mesh/SM_ManorNew_6x6/SM_ManorNew_Turn02_Stair.SM_ManorNew_Turn02_Stair",
			["FurnitureScore"] = 100,
			["BuyPrice"] = 1000,
			["SellPrice"] = 500,
			["UnlockLevel"] = 1,
			["UnlockPrice"] = 1000,
			["UnlockItem"] = 0,
			["UnlockWorkshp"] = 0,
		},
	},
}

return TopData
