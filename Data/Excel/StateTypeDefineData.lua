--
-- 表名: $StateConflict_状态冲突表.xlsx  页名：$StateTypeDefine_状态定义表
--

local TopData = {
	data = {
		[3] = {
			["ID"] = 3,
			["StateEnum"] = "TeleportSpell",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618795008'),
			["NoReminder"] = false,
		},
		[4] = {
			["ID"] = 4,
			["StateEnum"] = "Follow",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618795264'),
			["NoReminder"] = false,
		},
		[5] = {
			["ID"] = 5,
			["StateEnum"] = "Move",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618795520'),
			["NoReminder"] = false,
		},
		[6] = {
			["ID"] = 6,
			["StateEnum"] = "Jump",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618795776'),
			["NoReminder"] = false,
		},
		[7] = {
			["ID"] = 7,
			["StateEnum"] = "CastCombatSkill",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618796032'),
			["NoReminder"] = false,
		},
		[8] = {
			["ID"] = 8,
			["StateEnum"] = "FashionSystem",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618796288'),
			["NoReminder"] = false,
		},
		[9] = {
			["ID"] = 9,
			["StateEnum"] = "SocialActionSystem",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618796544'),
			["NoReminder"] = true,
		},
		[10] = {
			["ID"] = 10,
			["StateEnum"] = "Match",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618796800'),
			["NoReminder"] = false,
		},
		[13] = {
			["ID"] = 13,
			["StateEnum"] = "InVehicle",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618797056'),
			["NoReminder"] = false,
		},
		[16] = {
			["ID"] = 16,
			["StateEnum"] = "Morph",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618797312'),
			["NoReminder"] = false,
		},
		[18] = {
			["ID"] = 18,
			["StateEnum"] = "Navigate",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618797568'),
			["NoReminder"] = true,
		},
		[20] = {
			["ID"] = 20,
			["StateEnum"] = "InBattle",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618797824'),
			["NoReminder"] = false,
		},
		[21] = {
			["ID"] = 21,
			["StateEnum"] = "Die",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618798080'),
			["NoReminder"] = false,
		},
		[22] = {
			["ID"] = 22,
			["StateEnum"] = "Control",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618798336'),
			["NoReminder"] = false,
		},
		[23] = {
			["ID"] = 23,
			["StateEnum"] = "ContinuousInteract",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618798592'),
			["NoReminder"] = false,
		},
		[25] = {
			["ID"] = 25,
			["StateEnum"] = "InteractSpell",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618798848'),
			["NoReminder"] = false,
		},
		[28] = {
			["ID"] = 28,
			["StateEnum"] = "InPVEScene",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618799104'),
			["NoReminder"] = true,
		},
		[29] = {
			["ID"] = 29,
			["StateEnum"] = "CutScene",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618799360'),
			["NoReminder"] = true,
		},
		[30] = {
			["ID"] = 30,
			["StateEnum"] = "TarotTeamBuildingProcess",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618799616'),
			["NoReminder"] = true,
		},
		[31] = {
			["ID"] = 31,
			["StateEnum"] = "InMount",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618799872'),
			["NoReminder"] = false,
		},
		[32] = {
			["ID"] = 32,
			["StateEnum"] = "EnterHome",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618800128'),
			["NoReminder"] = false,
		},
		[33] = {
			["ID"] = 33,
			["StateEnum"] = "UseItemSpell",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618800384'),
			["NoReminder"] = false,
		},
		[34] = {
			["ID"] = 34,
			["StateEnum"] = "InWaterWalk",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618800640'),
			["NoReminder"] = false,
		},
		[35] = {
			["ID"] = 35,
			["StateEnum"] = "Seat",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618798592'),
			["NoReminder"] = true,
		},
		[36] = {
			["ID"] = 36,
			["StateEnum"] = "SetAnimation",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618801152'),
			["NoReminder"] = true,
		},
		[37] = {
			["ID"] = 37,
			["StateEnum"] = "SetUpperAnimation",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618801408'),
			["NoReminder"] = true,
		},
		[38] = {
			["ID"] = 38,
			["StateEnum"] = "AutoFarming",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618801664'),
			["NoReminder"] = false,
		},
		[39] = {
			["ID"] = 39,
			["StateEnum"] = "AutoSkill",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618801920'),
			["NoReminder"] = false,
		},
		[40] = {
			["ID"] = 40,
			["StateEnum"] = "QuestControl",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618802176'),
			["NoReminder"] = true,
		},
		[41] = {
			["ID"] = 41,
			["StateEnum"] = "Observer",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618802432'),
			["NoReminder"] = true,
		},
		[43] = {
			["ID"] = 43,
			["StateEnum"] = "DialogControl",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618802688'),
			["NoReminder"] = false,
		},
		[44] = {
			["ID"] = 44,
			["StateEnum"] = "Photograph",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618802944'),
			["NoReminder"] = false,
		},
		[45] = {
			["ID"] = 45,
			["StateEnum"] = "StrongControl",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618803200'),
			["NoReminder"] = false,
		},
		[47] = {
			["ID"] = 47,
			["StateEnum"] = "BossMechanism",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618803456'),
			["NoReminder"] = false,
		},
		[48] = {
			["ID"] = 48,
			["StateEnum"] = "InPVPPrepare",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618803712'),
			["NoReminder"] = false,
		},
		[49] = {
			["ID"] = 49,
			["StateEnum"] = "InPVPFighting",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618803968'),
			["NoReminder"] = true,
		},
		[50] = {
			["ID"] = 50,
			["StateEnum"] = "Dancing",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618804224'),
			["NoReminder"] = false,
		},
		[51] = {
			["ID"] = 51,
			["StateEnum"] = "InOtherMount",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618804480'),
			["NoReminder"] = false,
		},
		[52] = {
			["ID"] = 52,
			["StateEnum"] = "UnStucking",
			["StateName"] = Game.TableDataManager:GetLangStr('str_54495618804736'),
			["NoReminder"] = true,
		},
	},
}

return TopData
