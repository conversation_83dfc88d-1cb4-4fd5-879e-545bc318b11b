--
-- 表名: $Chat_聊天表.xlsx  页名：$ChatSystem_消息文本
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["Enum"] = "TEAM_JOIN",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144503808'),
			["GuildNotice"] = 0,
		},
		[2] = {
			["Id"] = 2,
			["Enum"] = "TEAM_LEAVE",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144504064'),
			["GuildNotice"] = 0,
		},
		[3] = {
			["Id"] = 3,
			["Enum"] = "TEAM_CAPTAIN",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144504320'),
			["GuildNotice"] = 0,
		},
		[4] = {
			["Id"] = 4,
			["Enum"] = "AUCTION_GIVEUP",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144504576'),
			["GuildNotice"] = 0,
		},
		[5] = {
			["Id"] = 5,
			["Enum"] = "AUCTION_BID",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144504832'),
			["GuildNotice"] = 0,
		},
		[6] = {
			["Id"] = 6,
			["Enum"] = "AUCTION_COMPLETE",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144505088'),
			["GuildNotice"] = 0,
		},
		[7] = {
			["Id"] = 7,
			["Enum"] = "ALLOCATION_THROW",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144505344'),
			["GuildNotice"] = 0,
		},
		[8] = {
			["Id"] = 8,
			["Enum"] = "ALLOCATION_COMPLETE_NEED",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144505600'),
			["GuildNotice"] = 0,
		},
		[9] = {
			["Id"] = 9,
			["Enum"] = "TEAM_TARGET",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144505856'),
			["GuildNotice"] = 0,
		},
		[10] = {
			["Id"] = 10,
			["Enum"] = "GET_EXP",
			["TabName"] = "8",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144506112'),
			["GuildNotice"] = 0,
		},
		[11] = {
			["Id"] = 11,
			["Enum"] = "GET_MONEY",
			["TabName"] = "8",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144506368'),
			["GuildNotice"] = 0,
		},
		[12] = {
			["Id"] = 12,
			["Enum"] = "GET_ITEM",
			["TabName"] = "8",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144506624'),
			["GuildNotice"] = 0,
		},
		[13] = {
			["Id"] = 13,
			["Enum"] = "CHAT_GROUP_INVITE",
			["TabName"] = "10",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144506880'),
			["GuildNotice"] = 0,
		},
		[14] = {
			["Id"] = 14,
			["Enum"] = "CHAT_GROUP_LEAVE",
			["TabName"] = "10",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144507136'),
			["GuildNotice"] = 0,
		},
		[16] = {
			["Id"] = 16,
			["Enum"] = "GIFT_SHOW",
			["TabName"] = "8.1",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144507392'),
			["GuildNotice"] = 0,
		},
		[17] = {
			["Id"] = 17,
			["Enum"] = "FIGHT_INTO",
			["TabName"] = "8.2",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144507648'),
			["GuildNotice"] = 0,
		},
		[18] = {
			["Id"] = 18,
			["Enum"] = "FIGHT_LEAVE",
			["TabName"] = "8.2",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144507904'),
			["GuildNotice"] = 0,
		},
		[19] = {
			["Id"] = 19,
			["Enum"] = "FIGHT_ATK_ACTIVE",
			["TabName"] = "8.2",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144508160'),
			["GuildNotice"] = 0,
		},
		[20] = {
			["Id"] = 20,
			["Enum"] = "FIGHT_REG",
			["TabName"] = "8.2",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144508416'),
			["GuildNotice"] = 0,
		},
		[21] = {
			["Id"] = 21,
			["Enum"] = "FIGHT_ATK_PASSIVE",
			["TabName"] = "8.3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144508672'),
			["GuildNotice"] = 0,
		},
		[22] = {
			["Id"] = 22,
			["Enum"] = "FIGHT_HPREG",
			["TabName"] = "8.3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144508928'),
			["GuildNotice"] = 0,
		},
		[23] = {
			["Id"] = 23,
			["Enum"] = "FIGHT_BUFF_GET",
			["TabName"] = "8.3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144509184'),
			["GuildNotice"] = 0,
		},
		[24] = {
			["Id"] = 24,
			["Enum"] = "FIGHT_BUFF_DISAPPEAR",
			["TabName"] = "8.3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144509440'),
			["GuildNotice"] = 0,
		},
		[25] = {
			["Id"] = 25,
			["Enum"] = "GUILD_MASCOT",
			["TabName"] = "9",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144509696'),
			["GuildNotice"] = 0,
		},
		[26] = {
			["Id"] = 26,
			["Enum"] = "FIGHT_ACT",
			["TabName"] = "8.2",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144509952'),
			["GuildNotice"] = 0,
		},
		[27] = {
			["Id"] = 27,
			["Enum"] = "OTHER_QUIT_GUILD",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144510208'),
			["GuildNotice"] = 1,
		},
		[28] = {
			["Id"] = 28,
			["Enum"] = "SOMEONE_CREATE_GUILD",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144510464'),
			["GuildNotice"] = 1,
		},
		[29] = {
			["Id"] = 29,
			["Enum"] = "SOMEONE_JOIN_GUILD",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144510720'),
			["GuildNotice"] = 1,
		},
		[30] = {
			["Id"] = 30,
			["Enum"] = "SOMEONE_ASIGN_GUILD",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144510976'),
			["GuildNotice"] = 1,
		},
		[31] = {
			["Id"] = 31,
			["Enum"] = "SOMEONE_TAKE_OVER_GUILD_ROLE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144511232'),
			["GuildNotice"] = 1,
		},
		[32] = {
			["Id"] = 32,
			["Enum"] = "GUILD_FOUNDER_FINISH_IMPEACH",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144511488'),
			["GuildNotice"] = 1,
		},
		[33] = {
			["Id"] = 33,
			["Enum"] = "GUILD_FOUNDER_START_CANDIDATE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144511744'),
			["GuildNotice"] = 1,
		},
		[34] = {
			["Id"] = 34,
			["Enum"] = "GUILD_FOUNDER_CHANGE_CANDIDATE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144512000'),
			["GuildNotice"] = 1,
		},
		[35] = {
			["Id"] = 35,
			["Enum"] = "GUILD_ROLE_ASSIGN_TO_OTHER",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144512256'),
			["GuildNotice"] = 1,
		},
		[36] = {
			["Id"] = 36,
			["Enum"] = "QUIT_CUT_CANDIDATE_SYS_FRIEND",
			["TabName"] = "9",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144512512'),
			["GuildNotice"] = 0,
		},
		[37] = {
			["Id"] = 37,
			["Enum"] = "GUILD_SET_ROLE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144512768'),
			["GuildNotice"] = 1,
		},
		[38] = {
			["Id"] = 38,
			["Enum"] = "GUILD_CANCEL_ROLE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144513024'),
			["GuildNotice"] = 1,
		},
		[39] = {
			["Id"] = 39,
			["Enum"] = "GUILD_CHANGE_ROLE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144513280'),
			["GuildNotice"] = 1,
		},
		[40] = {
			["Id"] = 40,
			["Enum"] = "GUILD_CHANGE_NAME",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144513536'),
			["GuildNotice"] = 1,
		},
		[41] = {
			["Id"] = 41,
			["Enum"] = "GUILD_CHANGE_NAME_SYS_FRIEND",
			["TabName"] = "9",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144513792'),
			["GuildNotice"] = 0,
		},
		[42] = {
			["Id"] = 42,
			["Enum"] = "GUILD_DECLARATION_MODIFY",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144514048'),
			["GuildNotice"] = 1,
		},
		[43] = {
			["Id"] = 43,
			["Enum"] = "GUILD_MERGE_SUCCESS_EVENT",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144514304'),
			["GuildNotice"] = 1,
		},
		[44] = {
			["Id"] = 44,
			["Enum"] = "GUILD_DEGRADE_NOTICE",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144514560'),
			["GuildNotice"] = 1,
		},
		[45] = {
			["Id"] = 45,
			["Enum"] = "GUILD_GUILD_BADGE_FRAME_CHANGE_NOTIFY",
			["TabName"] = "3",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144514816'),
			["GuildNotice"] = 1,
		},
		[46] = {
			["Id"] = 46,
			["Enum"] = "FRIEND_ATTRACTION_LEVEL_NOTICE",
			["TabName"] = "9",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144515072'),
			["GuildNotice"] = 0,
		},
		[47] = {
			["Id"] = 47,
			["Enum"] = "AUCTION_ALL_GIVEUP",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144515328'),
			["GuildNotice"] = 0,
		},
		[48] = {
			["Id"] = 48,
			["Enum"] = "ALLOCATION_COMPLETE_GREED",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144515584'),
			["GuildNotice"] = 0,
		},
		[49] = {
			["Id"] = 49,
			["Enum"] = "PVP_INDIVIDUAL_RESULT",
			["TabName"] = "1",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144515840'),
			["GuildNotice"] = 0,
		},
		[50] = {
			["Id"] = 50,
			["Enum"] = "ALLOCATION_ALL_GIVEUP",
			["TabName"] = "5",
			["Content"] = Game.TableDataManager:GetLangStr('str_4949144516096'),
			["GuildNotice"] = 0,
		},
	},
}

return TopData
