--
-- 表名: FightPropModeData(后处理)
--

local TopData = {
    fightPropModeSetMap = {
        ['AllControlAnti_N'] = 1016031, 
        ['AllControlAnti_P'] = 1016032, 
        ['AllControlHitRate_N'] = 1016051, 
        ['AllEleAnti_N'] = 1016061, 
        ['AllEleAnti_P'] = 1016062, 
        ['AllEleAtk_N'] = 1016001, 
        ['AllEleAtk_P'] = 1016002, 
        ['AllEleHurtMulti_N'] = 1016011, 
        ['AllEleHurtReduce_N'] = 1016021, 
        ['AllProHurtMulti_N'] = 1016131, 
        ['AllProHurtMulti_P'] = 1016132, 
        ['AllProHurtReduce_N'] = 1016141, 
        ['AllProHurtReduce_P'] = 1016142, 
        ['AllRaceHurtMulti_N'] = 1016151, 
        ['AllRaceHurtMulti_P'] = 1016152, 
        ['AllRaceHurtReduce_N'] = 1016161, 
        ['AllRaceHurtReduce_P'] = 1016162, 
        ['Atk_N'] = 1016101, 
        ['Atk_P'] = 1016102, 
        ['Block_N'] = 1016091, 
        ['Block_P'] = 1016092, 
        ['CritAnti_N'] = 1016171, 
        ['CritAnti_P'] = 1016172, 
        ['CritDef_N'] = 1016181, 
        ['CritDef_P'] = 1016182, 
        ['Def_N'] = 1016081, 
        ['Def_P'] = 1016082, 
        ['EnhanceAllControl_N'] = 1016041, 
        ['EnhanceAllControl_P'] = 1016042, 
        ['IgnoreAllEle_N'] = 1016071, 
        ['IgnoreAllEle_P'] = 1016072, 
        ['Pierce_N'] = 1016121, 
        ['Pierce_P'] = 1016122, 
    },
    fightPropSetMap = {
        ['AllControlAnti'] = true, 
        ['AllControlHitRate'] = true, 
        ['AllEleAnti'] = true, 
        ['AllEleAtk'] = true, 
        ['AllEleHurtMulti'] = true, 
        ['AllEleHurtReduce'] = true, 
        ['AllProHurtMulti'] = true, 
        ['AllProHurtReduce'] = true, 
        ['AllRaceHurtMulti'] = true, 
        ['AllRaceHurtReduce'] = true, 
        ['Atk'] = true, 
        ['Block'] = true, 
        ['CritAnti'] = true, 
        ['CritDef'] = true, 
        ['Def'] = true, 
        ['EnhanceAllControl'] = true, 
        ['IgnoreAllEle'] = true, 
        ['Pierce'] = true, 
    },
    propChangeModeMap = {
        ['AbundanceAnti_N'] = 1014231, 
        ['AbundanceAnti_P'] = 1014232, 
        ['AbundanceAtk_N'] = 1014201, 
        ['AbundanceAtk_P'] = 1014202, 
        ['AbundanceHurtMulti_N'] = 1014211, 
        ['AbundanceHurtReduce_N'] = 1014221, 
        ['AbundanceLevel_N'] = 1014261, 
        ['AbundanceRate_N'] = 1014251, 
        ['AdditionalAnti_N'] = 1017061, 
        ['AdditionalAnti_P'] = 1017062, 
        ['AdditionalAtk_N'] = 1017051, 
        ['AdditionalAtk_P'] = 1017052, 
        ['AdditionalIgnore_N'] = 1017071, 
        ['AdditionalIgnore_P'] = 1017072, 
        ['AggroPercent_N'] = 1011231, 
        ['AirShield_N'] = 1017021, 
        ['AirborneAnti_N'] = 1015001, 
        ['AirborneAnti_P'] = 1015002, 
        ['AirborneHitRate_N'] = 1015021, 
        ['AllControlAnti_N'] = 1016031, 
        ['AllControlAnti_P'] = 1016032, 
        ['AllControlHitRate_N'] = 1016051, 
        ['AllEleAnti_N'] = 1016061, 
        ['AllEleAnti_P'] = 1016062, 
        ['AllEleAtk_N'] = 1016001, 
        ['AllEleAtk_P'] = 1016002, 
        ['AllEleHurtMulti_N'] = 1016011, 
        ['AllEleHurtReduce_N'] = 1016021, 
        ['AllProHurtMulti_N'] = 1016131, 
        ['AllProHurtMulti_P'] = 1016132, 
        ['AllProHurtReduce_N'] = 1016141, 
        ['AllProHurtReduce_P'] = 1016142, 
        ['AllRaceHurtMulti_N'] = 1016151, 
        ['AllRaceHurtMulti_P'] = 1016152, 
        ['AllRaceHurtReduce_N'] = 1016161, 
        ['AllRaceHurtReduce_P'] = 1016162, 
        ['ApprenticeHurtMulti_N'] = 1019051, 
        ['ApprenticeHurtMulti_P'] = 1019052, 
        ['ApprenticeHurtReduce_N'] = 1019251, 
        ['ApprenticeHurtReduce_P'] = 1019252, 
        ['ArbiterHurtMulti_N'] = 1019031, 
        ['ArbiterHurtMulti_P'] = 1019032, 
        ['ArbiterHurtReduce_N'] = 1019231, 
        ['ArbiterHurtReduce_P'] = 1019232, 
        ['AtkRange_N'] = 1011071, 
        ['AtkRange_P'] = 1011072, 
        ['Atk_N'] = 1016101, 
        ['Atk_P'] = 1016102, 
        ['Block_N'] = 1016091, 
        ['Block_P'] = 1016092, 
        ['CalamityAnti_N'] = 1014431, 
        ['CalamityAnti_P'] = 1014432, 
        ['CalamityAtk_N'] = 1014401, 
        ['CalamityAtk_P'] = 1014402, 
        ['CalamityHurtMulti_N'] = 1014411, 
        ['CalamityHurtReduce_N'] = 1014421, 
        ['CalamityLevel_N'] = 1014461, 
        ['CalamityRate_N'] = 1014451, 
        ['ChaosAnti_N'] = 1014031, 
        ['ChaosAnti_P'] = 1014032, 
        ['ChaosAtk_N'] = 1014001, 
        ['ChaosAtk_P'] = 1014002, 
        ['ChaosHurtMulti_N'] = 1014011, 
        ['ChaosHurtReduce_N'] = 1014021, 
        ['ChaosLevel_N'] = 1014061, 
        ['ChaosRate_N'] = 1014051, 
        ['Con_N'] = 1010001, 
        ['Con_P'] = 1010002, 
        ['CritAbundanceLevel_N'] = 1014271, 
        ['CritAnti_N'] = 1016171, 
        ['CritAnti_P'] = 1016172, 
        ['CritCalamityLevel_N'] = 1014471, 
        ['CritChaosLevel_N'] = 1014071, 
        ['CritDarknessLevel_N'] = 1014371, 
        ['CritDarknessSteal_N'] = 1011331, 
        ['CritDarknessSteal_P'] = 1011332, 
        ['CritDef_N'] = 1016181, 
        ['CritDef_P'] = 1016182, 
        ['CritDisorderLevel_N'] = 1014571, 
        ['CritFateAtkMax_N'] = 1011371, 
        ['CritFateAtkMax_P'] = 1011372, 
        ['CritFateAtkMin_N'] = 1011361, 
        ['CritFateAtkMin_P'] = 1011362, 
        ['CritFateLevel_N'] = 1014871, 
        ['CritKnowledgeLevel_N'] = 1014771, 
        ['CritMysteryLevel_N'] = 1014171, 
        ['CritTenebrousIgnoreDef_N'] = 1011351, 
        ['CritTenebrousIgnoreDef_P'] = 1011352, 
        ['CritTenebrousLevel_N'] = 1014671, 
        ['DarknessAnti_N'] = 1014331, 
        ['DarknessAnti_P'] = 1014332, 
        ['DarknessAtk_N'] = 1014301, 
        ['DarknessAtk_P'] = 1014302, 
        ['DarknessHurtMulti_N'] = 1014311, 
        ['DarknessHurtReduce_N'] = 1014321, 
        ['DarknessLevel_N'] = 1014361, 
        ['DarknessRate_N'] = 1014351, 
        ['DarknessSteal_N'] = 1011321, 
        ['DarknessSteal_P'] = 1011322, 
        ['Def_N'] = 1016081, 
        ['Def_P'] = 1016082, 
        ['DeltaBeHealed_N'] = 1011091, 
        ['DeltaBeHealed_P'] = 1011092, 
        ['DeltaBeHurted_N'] = 1011251, 
        ['DeltaHeal_N'] = 1011081, 
        ['DeltaHeal_P'] = 1011082, 
        ['DeltaHurt_N'] = 1011241, 
        ['Dex_N'] = 1010041, 
        ['Dex_P'] = 1010042, 
        ['DisorderAnti_N'] = 1014531, 
        ['DisorderAnti_P'] = 1014532, 
        ['DisorderAtk_N'] = 1014501, 
        ['DisorderAtk_P'] = 1014502, 
        ['DisorderHurtMulti_N'] = 1014511, 
        ['DisorderHurtReduce_N'] = 1014521, 
        ['DisorderLevel_N'] = 1014561, 
        ['DisorderRate_N'] = 1014551, 
        ['DizzyAnti_N'] = 1015501, 
        ['DizzyAnti_P'] = 1015502, 
        ['DizzyHitRate_N'] = 1015521, 
        ['DownAnti_N'] = 1015101, 
        ['DownAnti_P'] = 1015102, 
        ['DownHitRate_N'] = 1015121, 
        ['EnhanceAirborne_N'] = 1015011, 
        ['EnhanceAirborne_P'] = 1015012, 
        ['EnhanceAllControl_N'] = 1016041, 
        ['EnhanceAllControl_P'] = 1016042, 
        ['EnhanceDizzy_N'] = 1015511, 
        ['EnhanceDizzy_P'] = 1015512, 
        ['EnhanceDown_N'] = 1015111, 
        ['EnhanceDown_P'] = 1015112, 
        ['EnhanceFear_N'] = 1015611, 
        ['EnhanceFear_P'] = 1015612, 
        ['EnhancePull_N'] = 1015211, 
        ['EnhancePull_P'] = 1015212, 
        ['EnhanceSilence_N'] = 1015711, 
        ['EnhanceSilence_P'] = 1015712, 
        ['EnhanceSleep_N'] = 1015411, 
        ['EnhanceSleep_P'] = 1015412, 
        ['EnhanceSlow_N'] = 1015811, 
        ['EnhanceSlow_P'] = 1015812, 
        ['EnhanceTied_N'] = 1015311, 
        ['EnhanceTied_P'] = 1015312, 
        ['FateAnti_N'] = 1014831, 
        ['FateAnti_P'] = 1014832, 
        ['FateAtk_N'] = 1014801, 
        ['FateAtk_P'] = 1014802, 
        ['FateHurtMulti_N'] = 1014811, 
        ['FateHurtReduce_N'] = 1014821, 
        ['FateLevel_N'] = 1014861, 
        ['FateRate_N'] = 1014851, 
        ['FearAnti_N'] = 1015601, 
        ['FearAnti_P'] = 1015602, 
        ['FearHitRate_N'] = 1015621, 
        ['FeatherwitHurtMulti_N'] = 1019021, 
        ['FeatherwitHurtMulti_P'] = 1019022, 
        ['FeatherwitHurtReduce_N'] = 1019221, 
        ['FeatherwitHurtReduce_P'] = 1019222, 
        ['HpReg_N'] = 1011011, 
        ['HpReg_P'] = 1011012, 
        ['IgnoreAbundanceAnti_N'] = 1014241, 
        ['IgnoreAbundanceAnti_P'] = 1014242, 
        ['IgnoreAllEle_N'] = 1016071, 
        ['IgnoreAllEle_P'] = 1016072, 
        ['IgnoreCalamityAnti_N'] = 1014441, 
        ['IgnoreCalamityAnti_P'] = 1014442, 
        ['IgnoreChaosAnti_N'] = 1014041, 
        ['IgnoreChaosAnti_P'] = 1014042, 
        ['IgnoreDarknessAnti_N'] = 1014341, 
        ['IgnoreDarknessAnti_P'] = 1014342, 
        ['IgnoreDisorderAnti_N'] = 1014541, 
        ['IgnoreDisorderAnti_P'] = 1014542, 
        ['IgnoreFateAnti_N'] = 1014841, 
        ['IgnoreFateAnti_P'] = 1014842, 
        ['IgnoreKnowledgeAnti_N'] = 1014741, 
        ['IgnoreKnowledgeAnti_P'] = 1014742, 
        ['IgnoreMysteryAnti_N'] = 1014141, 
        ['IgnoreMysteryAnti_P'] = 1014142, 
        ['IgnoreTenebrousAnti_N'] = 1014641, 
        ['IgnoreTenebrousAnti_P'] = 1014642, 
        ['Int_N'] = 1010031, 
        ['Int_P'] = 1010032, 
        ['KnowledgeAnti_N'] = 1014731, 
        ['KnowledgeAnti_P'] = 1014732, 
        ['KnowledgeAtk_N'] = 1014701, 
        ['KnowledgeAtk_P'] = 1014702, 
        ['KnowledgeHurtMulti_N'] = 1014711, 
        ['KnowledgeHurtReduce_N'] = 1014721, 
        ['KnowledgeLevel_N'] = 1014761, 
        ['KnowledgeRate_N'] = 1014751, 
        ['LockedMaxHp_N'] = 1011041, 
        ['LockedMaxHp_P'] = 1011042, 
        ['MaxHp_F'] = 1011003, 
        ['MaxHp_N'] = 1011001, 
        ['MaxHp_P'] = 1011002, 
        ['MaxStaminaValue_F'] = 1011063, 
        ['MaxStaminaValue_N'] = 1011061, 
        ['MaxStaminaValue_P'] = 1011062, 
        ['MysteryAnti_N'] = 1014131, 
        ['MysteryAnti_P'] = 1014132, 
        ['MysteryAtk_N'] = 1014101, 
        ['MysteryAtk_P'] = 1014102, 
        ['MysteryHurtMulti_N'] = 1014111, 
        ['MysteryHurtReduce_N'] = 1014121, 
        ['MysteryLevel_N'] = 1014161, 
        ['MysteryRate_N'] = 1014151, 
        ['Pierce_N'] = 1016121, 
        ['Pierce_P'] = 1016122, 
        ['Pow_N'] = 1010011, 
        ['Pow_P'] = 1010012, 
        ['ProHurtMulti9_N'] = 1019081, 
        ['ProHurtMulti9_P'] = 1019082, 
        ['ProHurtReduce9_N'] = 1019281, 
        ['ProHurtReduce9_P'] = 1019282, 
        ['PullAnti_N'] = 1015201, 
        ['PullAnti_P'] = 1015202, 
        ['PullHitRate_N'] = 1015221, 
        ['RaceHurtMulti1_N'] = 1018001, 
        ['RaceHurtMulti1_P'] = 1018002, 
        ['RaceHurtMulti2_N'] = 1018011, 
        ['RaceHurtMulti2_P'] = 1018012, 
        ['RaceHurtMulti3_N'] = 1018021, 
        ['RaceHurtMulti3_P'] = 1018022, 
        ['RaceHurtMulti4_N'] = 1018031, 
        ['RaceHurtMulti4_P'] = 1018032, 
        ['RaceHurtMulti5_N'] = 1018041, 
        ['RaceHurtMulti5_P'] = 1018042, 
        ['RaceHurtReduce1_N'] = 1018101, 
        ['RaceHurtReduce1_P'] = 1018102, 
        ['RaceHurtReduce2_N'] = 1018111, 
        ['RaceHurtReduce2_P'] = 1018112, 
        ['RaceHurtReduce3_N'] = 1018121, 
        ['RaceHurtReduce3_P'] = 1018122, 
        ['RaceHurtReduce4_N'] = 1018131, 
        ['RaceHurtReduce4_P'] = 1018132, 
        ['RaceHurtReduce5_N'] = 1018141, 
        ['RaceHurtReduce5_P'] = 1018142, 
        ['ShieldBreak_N'] = 1017011, 
        ['SilenceAnti_N'] = 1015701, 
        ['SilenceAnti_P'] = 1015702, 
        ['SilenceHitRate_N'] = 1015721, 
        ['SkillAnti_N'] = 1017031, 
        ['SkillAnti_P'] = 1017032, 
        ['SkillEnhance_N'] = 1017041, 
        ['SkillEnhance_P'] = 1017042, 
        ['SleepAnti_N'] = 1015401, 
        ['SleepAnti_P'] = 1015402, 
        ['SleepHitRate_N'] = 1015421, 
        ['SlowAnti_N'] = 1015801, 
        ['SlowAnti_P'] = 1015802, 
        ['SlowHitRate_N'] = 1015821, 
        ['Speed_N'] = 1011101, 
        ['Speed_P'] = 1011102, 
        ['Str_N'] = 1010021, 
        ['Str_P'] = 1010022, 
        ['SunHurtMulti_N'] = 1019001, 
        ['SunHurtMulti_P'] = 1019002, 
        ['SunHurtReduce_N'] = 1019201, 
        ['SunHurtReduce_P'] = 1019202, 
        ['TenebrousAnti_N'] = 1014631, 
        ['TenebrousAnti_P'] = 1014632, 
        ['TenebrousAtk_N'] = 1014601, 
        ['TenebrousAtk_P'] = 1014602, 
        ['TenebrousHurtMulti_N'] = 1014611, 
        ['TenebrousHurtReduce_N'] = 1014621, 
        ['TenebrousIgnoreDef_N'] = 1011341, 
        ['TenebrousIgnoreDef_P'] = 1011342, 
        ['TenebrousLevel_N'] = 1014661, 
        ['TenebrousRate_N'] = 1014651, 
        ['TiedAnti_N'] = 1015301, 
        ['TiedAnti_P'] = 1015302, 
        ['TiedHitRate_N'] = 1015321, 
        ['UltimatePointSpeed_N'] = 1011381, 
        ['UltimatePointSpeed_P'] = 1011382, 
        ['VisionaryHurtMulti_N'] = 1019011, 
        ['VisionaryHurtMulti_P'] = 1019012, 
        ['VisionaryHurtReduce_N'] = 1019211, 
        ['VisionaryHurtReduce_P'] = 1019212, 
        ['WarriorHurtMulti_N'] = 1019041, 
        ['WarriorHurtMulti_P'] = 1019042, 
        ['WarriorHurtReduce_N'] = 1019241, 
        ['WarriorHurtReduce_P'] = 1019242, 
        ['mAtkMax_F'] = 1013013, 
        ['mAtkMax_N'] = 1013011, 
        ['mAtkMax_P'] = 1013012, 
        ['mAtkMin_F'] = 1013003, 
        ['mAtkMin_N'] = 1013001, 
        ['mAtkMin_P'] = 1013002, 
        ['mAtkSpd_N'] = 1013031, 
        ['mAtkSpd_P'] = 1013032, 
        ['mBlock_N'] = 1013151, 
        ['mBlock_P'] = 1013152, 
        ['mCritAnti_N'] = 1013091, 
        ['mCritAnti_P'] = 1013092, 
        ['mCritDef_N'] = 1013111, 
        ['mCritDef_P'] = 1013112, 
        ['mCritHurt_N'] = 1013101, 
        ['mCritHurt_P'] = 1013102, 
        ['mCrit_N'] = 1013081, 
        ['mCrit_P'] = 1013082, 
        ['mDef_F'] = 1013043, 
        ['mDef_N'] = 1013041, 
        ['mDef_P'] = 1013042, 
        ['mHurtMulti_N'] = 1013121, 
        ['mHurtMulti_P'] = 1013122, 
        ['mHurtReduce_N'] = 1013131, 
        ['mHurtReduce_P'] = 1013132, 
        ['mIgnoreDef_N'] = 1013051, 
        ['mIgnoreDef_P'] = 1013052, 
        ['mPierce_N'] = 1013141, 
        ['mPierce_P'] = 1013142, 
        ['pAtkMax_F'] = 1012013, 
        ['pAtkMax_N'] = 1012011, 
        ['pAtkMax_P'] = 1012012, 
        ['pAtkMin_F'] = 1012003, 
        ['pAtkMin_N'] = 1012001, 
        ['pAtkMin_P'] = 1012002, 
        ['pAtkSpd_N'] = 1012031, 
        ['pAtkSpd_P'] = 1012032, 
        ['pBlock_N'] = 1012151, 
        ['pBlock_P'] = 1012152, 
        ['pCritAnti_N'] = 1012091, 
        ['pCritAnti_P'] = 1012092, 
        ['pCritDef_N'] = 1012111, 
        ['pCritDef_P'] = 1012112, 
        ['pCritHurt_N'] = 1012101, 
        ['pCritHurt_P'] = 1012102, 
        ['pCrit_N'] = 1012081, 
        ['pCrit_P'] = 1012082, 
        ['pDef_F'] = 1012043, 
        ['pDef_N'] = 1012041, 
        ['pDef_P'] = 1012042, 
        ['pHurtMulti_N'] = 1012121, 
        ['pHurtMulti_P'] = 1012122, 
        ['pHurtReduce_N'] = 1012131, 
        ['pHurtReduce_P'] = 1012132, 
        ['pIgnoreDef_N'] = 1012051, 
        ['pIgnoreDef_P'] = 1012052, 
        ['pPierce_N'] = 1012141, 
        ['pPierce_P'] = 1012142, 
    },
    propMode2PropList = {
        ['AbundanceAnti_N'] = {'AbundanceAnti', 'N', 'AbundanceAnti_N', 'AbundanceAnti_P', 'AbundanceAnti_F'}, 
        ['AbundanceAnti_P'] = {'AbundanceAnti', 'P', 'AbundanceAnti_N', 'AbundanceAnti_P', 'AbundanceAnti_F'}, 
        ['AbundanceAtk_N'] = {'AbundanceAtk', 'N', 'AbundanceAtk_N', 'AbundanceAtk_P', 'AbundanceAtk_F'}, 
        ['AbundanceAtk_P'] = {'AbundanceAtk', 'P', 'AbundanceAtk_N', 'AbundanceAtk_P', 'AbundanceAtk_F'}, 
        ['AbundanceHurtMulti_N'] = {'AbundanceHurtMulti', 'N', 'AbundanceHurtMulti_N', 'AbundanceHurtMulti_P', 'AbundanceHurtMulti_F'}, 
        ['AbundanceHurtReduce_N'] = {'AbundanceHurtReduce', 'N', 'AbundanceHurtReduce_N', 'AbundanceHurtReduce_P', 'AbundanceHurtReduce_F'}, 
        ['AbundanceLevel_N'] = {'AbundanceLevel', 'N', 'AbundanceLevel_N', 'AbundanceLevel_P', 'AbundanceLevel_F'}, 
        ['AbundanceRate_N'] = {'AbundanceRate', 'N', 'AbundanceRate_N', 'AbundanceRate_P', 'AbundanceRate_F'}, 
        ['AdditionalAnti_N'] = {'AdditionalAnti', 'N', 'AdditionalAnti_N', 'AdditionalAnti_P', 'AdditionalAnti_F'}, 
        ['AdditionalAnti_P'] = {'AdditionalAnti', 'P', 'AdditionalAnti_N', 'AdditionalAnti_P', 'AdditionalAnti_F'}, 
        ['AdditionalAtk_N'] = {'AdditionalAtk', 'N', 'AdditionalAtk_N', 'AdditionalAtk_P', 'AdditionalAtk_F'}, 
        ['AdditionalAtk_P'] = {'AdditionalAtk', 'P', 'AdditionalAtk_N', 'AdditionalAtk_P', 'AdditionalAtk_F'}, 
        ['AdditionalIgnore_N'] = {'AdditionalIgnore', 'N', 'AdditionalIgnore_N', 'AdditionalIgnore_P', 'AdditionalIgnore_F'}, 
        ['AdditionalIgnore_P'] = {'AdditionalIgnore', 'P', 'AdditionalIgnore_N', 'AdditionalIgnore_P', 'AdditionalIgnore_F'}, 
        ['AggroPercent_N'] = {'AggroPercent', 'N', 'AggroPercent_N', 'AggroPercent_P', 'AggroPercent_F'}, 
        ['AirShield_N'] = {'AirShield', 'N', 'AirShield_N', 'AirShield_P', 'AirShield_F'}, 
        ['AirborneAnti_N'] = {'AirborneAnti', 'N', 'AirborneAnti_N', 'AirborneAnti_P', 'AirborneAnti_F'}, 
        ['AirborneAnti_P'] = {'AirborneAnti', 'P', 'AirborneAnti_N', 'AirborneAnti_P', 'AirborneAnti_F'}, 
        ['AirborneHitRate_N'] = {'AirborneHitRate', 'N', 'AirborneHitRate_N', 'AirborneHitRate_P', 'AirborneHitRate_F'}, 
        ['AllControlAnti_N'] = {'AllControlAnti', 'N', 'AllControlAnti_N', 'AllControlAnti_P', 'AllControlAnti_F'}, 
        ['AllControlAnti_P'] = {'AllControlAnti', 'P', 'AllControlAnti_N', 'AllControlAnti_P', 'AllControlAnti_F'}, 
        ['AllControlHitRate_N'] = {'AllControlHitRate', 'N', 'AllControlHitRate_N', 'AllControlHitRate_P', 'AllControlHitRate_F'}, 
        ['AllEleAnti_N'] = {'AllEleAnti', 'N', 'AllEleAnti_N', 'AllEleAnti_P', 'AllEleAnti_F'}, 
        ['AllEleAnti_P'] = {'AllEleAnti', 'P', 'AllEleAnti_N', 'AllEleAnti_P', 'AllEleAnti_F'}, 
        ['AllEleAtk_N'] = {'AllEleAtk', 'N', 'AllEleAtk_N', 'AllEleAtk_P', 'AllEleAtk_F'}, 
        ['AllEleAtk_P'] = {'AllEleAtk', 'P', 'AllEleAtk_N', 'AllEleAtk_P', 'AllEleAtk_F'}, 
        ['AllEleHurtMulti_N'] = {'AllEleHurtMulti', 'N', 'AllEleHurtMulti_N', 'AllEleHurtMulti_P', 'AllEleHurtMulti_F'}, 
        ['AllEleHurtReduce_N'] = {'AllEleHurtReduce', 'N', 'AllEleHurtReduce_N', 'AllEleHurtReduce_P', 'AllEleHurtReduce_F'}, 
        ['AllProHurtMulti_N'] = {'AllProHurtMulti', 'N', 'AllProHurtMulti_N', 'AllProHurtMulti_P', 'AllProHurtMulti_F'}, 
        ['AllProHurtMulti_P'] = {'AllProHurtMulti', 'P', 'AllProHurtMulti_N', 'AllProHurtMulti_P', 'AllProHurtMulti_F'}, 
        ['AllProHurtReduce_N'] = {'AllProHurtReduce', 'N', 'AllProHurtReduce_N', 'AllProHurtReduce_P', 'AllProHurtReduce_F'}, 
        ['AllProHurtReduce_P'] = {'AllProHurtReduce', 'P', 'AllProHurtReduce_N', 'AllProHurtReduce_P', 'AllProHurtReduce_F'}, 
        ['AllRaceHurtMulti_N'] = {'AllRaceHurtMulti', 'N', 'AllRaceHurtMulti_N', 'AllRaceHurtMulti_P', 'AllRaceHurtMulti_F'}, 
        ['AllRaceHurtMulti_P'] = {'AllRaceHurtMulti', 'P', 'AllRaceHurtMulti_N', 'AllRaceHurtMulti_P', 'AllRaceHurtMulti_F'}, 
        ['AllRaceHurtReduce_N'] = {'AllRaceHurtReduce', 'N', 'AllRaceHurtReduce_N', 'AllRaceHurtReduce_P', 'AllRaceHurtReduce_F'}, 
        ['AllRaceHurtReduce_P'] = {'AllRaceHurtReduce', 'P', 'AllRaceHurtReduce_N', 'AllRaceHurtReduce_P', 'AllRaceHurtReduce_F'}, 
        ['ApprenticeHurtMulti_N'] = {'ApprenticeHurtMulti', 'N', 'ApprenticeHurtMulti_N', 'ApprenticeHurtMulti_P', 'ApprenticeHurtMulti_F'}, 
        ['ApprenticeHurtMulti_P'] = {'ApprenticeHurtMulti', 'P', 'ApprenticeHurtMulti_N', 'ApprenticeHurtMulti_P', 'ApprenticeHurtMulti_F'}, 
        ['ApprenticeHurtReduce_N'] = {'ApprenticeHurtReduce', 'N', 'ApprenticeHurtReduce_N', 'ApprenticeHurtReduce_P', 'ApprenticeHurtReduce_F'}, 
        ['ApprenticeHurtReduce_P'] = {'ApprenticeHurtReduce', 'P', 'ApprenticeHurtReduce_N', 'ApprenticeHurtReduce_P', 'ApprenticeHurtReduce_F'}, 
        ['ArbiterHurtMulti_N'] = {'ArbiterHurtMulti', 'N', 'ArbiterHurtMulti_N', 'ArbiterHurtMulti_P', 'ArbiterHurtMulti_F'}, 
        ['ArbiterHurtMulti_P'] = {'ArbiterHurtMulti', 'P', 'ArbiterHurtMulti_N', 'ArbiterHurtMulti_P', 'ArbiterHurtMulti_F'}, 
        ['ArbiterHurtReduce_N'] = {'ArbiterHurtReduce', 'N', 'ArbiterHurtReduce_N', 'ArbiterHurtReduce_P', 'ArbiterHurtReduce_F'}, 
        ['ArbiterHurtReduce_P'] = {'ArbiterHurtReduce', 'P', 'ArbiterHurtReduce_N', 'ArbiterHurtReduce_P', 'ArbiterHurtReduce_F'}, 
        ['AtkRange_N'] = {'AtkRange', 'N', 'AtkRange_N', 'AtkRange_P', 'AtkRange_F'}, 
        ['AtkRange_P'] = {'AtkRange', 'P', 'AtkRange_N', 'AtkRange_P', 'AtkRange_F'}, 
        ['Atk_N'] = {'Atk', 'N', 'Atk_N', 'Atk_P', 'Atk_F'}, 
        ['Atk_P'] = {'Atk', 'P', 'Atk_N', 'Atk_P', 'Atk_F'}, 
        ['Block_N'] = {'Block', 'N', 'Block_N', 'Block_P', 'Block_F'}, 
        ['Block_P'] = {'Block', 'P', 'Block_N', 'Block_P', 'Block_F'}, 
        ['CalamityAnti_N'] = {'CalamityAnti', 'N', 'CalamityAnti_N', 'CalamityAnti_P', 'CalamityAnti_F'}, 
        ['CalamityAnti_P'] = {'CalamityAnti', 'P', 'CalamityAnti_N', 'CalamityAnti_P', 'CalamityAnti_F'}, 
        ['CalamityAtk_N'] = {'CalamityAtk', 'N', 'CalamityAtk_N', 'CalamityAtk_P', 'CalamityAtk_F'}, 
        ['CalamityAtk_P'] = {'CalamityAtk', 'P', 'CalamityAtk_N', 'CalamityAtk_P', 'CalamityAtk_F'}, 
        ['CalamityHurtMulti_N'] = {'CalamityHurtMulti', 'N', 'CalamityHurtMulti_N', 'CalamityHurtMulti_P', 'CalamityHurtMulti_F'}, 
        ['CalamityHurtReduce_N'] = {'CalamityHurtReduce', 'N', 'CalamityHurtReduce_N', 'CalamityHurtReduce_P', 'CalamityHurtReduce_F'}, 
        ['CalamityLevel_N'] = {'CalamityLevel', 'N', 'CalamityLevel_N', 'CalamityLevel_P', 'CalamityLevel_F'}, 
        ['CalamityRate_N'] = {'CalamityRate', 'N', 'CalamityRate_N', 'CalamityRate_P', 'CalamityRate_F'}, 
        ['ChaosAnti_N'] = {'ChaosAnti', 'N', 'ChaosAnti_N', 'ChaosAnti_P', 'ChaosAnti_F'}, 
        ['ChaosAnti_P'] = {'ChaosAnti', 'P', 'ChaosAnti_N', 'ChaosAnti_P', 'ChaosAnti_F'}, 
        ['ChaosAtk_N'] = {'ChaosAtk', 'N', 'ChaosAtk_N', 'ChaosAtk_P', 'ChaosAtk_F'}, 
        ['ChaosAtk_P'] = {'ChaosAtk', 'P', 'ChaosAtk_N', 'ChaosAtk_P', 'ChaosAtk_F'}, 
        ['ChaosHurtMulti_N'] = {'ChaosHurtMulti', 'N', 'ChaosHurtMulti_N', 'ChaosHurtMulti_P', 'ChaosHurtMulti_F'}, 
        ['ChaosHurtReduce_N'] = {'ChaosHurtReduce', 'N', 'ChaosHurtReduce_N', 'ChaosHurtReduce_P', 'ChaosHurtReduce_F'}, 
        ['ChaosLevel_N'] = {'ChaosLevel', 'N', 'ChaosLevel_N', 'ChaosLevel_P', 'ChaosLevel_F'}, 
        ['ChaosRate_N'] = {'ChaosRate', 'N', 'ChaosRate_N', 'ChaosRate_P', 'ChaosRate_F'}, 
        ['Con_N'] = {'Con', 'N', 'Con_N', 'Con_P', 'Con_F'}, 
        ['Con_P'] = {'Con', 'P', 'Con_N', 'Con_P', 'Con_F'}, 
        ['CritAbundanceLevel_N'] = {'CritAbundanceLevel', 'N', 'CritAbundanceLevel_N', 'CritAbundanceLevel_P', 'CritAbundanceLevel_F'}, 
        ['CritAnti_N'] = {'CritAnti', 'N', 'CritAnti_N', 'CritAnti_P', 'CritAnti_F'}, 
        ['CritAnti_P'] = {'CritAnti', 'P', 'CritAnti_N', 'CritAnti_P', 'CritAnti_F'}, 
        ['CritCalamityLevel_N'] = {'CritCalamityLevel', 'N', 'CritCalamityLevel_N', 'CritCalamityLevel_P', 'CritCalamityLevel_F'}, 
        ['CritChaosLevel_N'] = {'CritChaosLevel', 'N', 'CritChaosLevel_N', 'CritChaosLevel_P', 'CritChaosLevel_F'}, 
        ['CritDarknessLevel_N'] = {'CritDarknessLevel', 'N', 'CritDarknessLevel_N', 'CritDarknessLevel_P', 'CritDarknessLevel_F'}, 
        ['CritDarknessSteal_N'] = {'CritDarknessSteal', 'N', 'CritDarknessSteal_N', 'CritDarknessSteal_P', 'CritDarknessSteal_F'}, 
        ['CritDarknessSteal_P'] = {'CritDarknessSteal', 'P', 'CritDarknessSteal_N', 'CritDarknessSteal_P', 'CritDarknessSteal_F'}, 
        ['CritDef_N'] = {'CritDef', 'N', 'CritDef_N', 'CritDef_P', 'CritDef_F'}, 
        ['CritDef_P'] = {'CritDef', 'P', 'CritDef_N', 'CritDef_P', 'CritDef_F'}, 
        ['CritDisorderLevel_N'] = {'CritDisorderLevel', 'N', 'CritDisorderLevel_N', 'CritDisorderLevel_P', 'CritDisorderLevel_F'}, 
        ['CritFateAtkMax_N'] = {'CritFateAtkMax', 'N', 'CritFateAtkMax_N', 'CritFateAtkMax_P', 'CritFateAtkMax_F'}, 
        ['CritFateAtkMax_P'] = {'CritFateAtkMax', 'P', 'CritFateAtkMax_N', 'CritFateAtkMax_P', 'CritFateAtkMax_F'}, 
        ['CritFateAtkMin_N'] = {'CritFateAtkMin', 'N', 'CritFateAtkMin_N', 'CritFateAtkMin_P', 'CritFateAtkMin_F'}, 
        ['CritFateAtkMin_P'] = {'CritFateAtkMin', 'P', 'CritFateAtkMin_N', 'CritFateAtkMin_P', 'CritFateAtkMin_F'}, 
        ['CritFateLevel_N'] = {'CritFateLevel', 'N', 'CritFateLevel_N', 'CritFateLevel_P', 'CritFateLevel_F'}, 
        ['CritKnowledgeLevel_N'] = {'CritKnowledgeLevel', 'N', 'CritKnowledgeLevel_N', 'CritKnowledgeLevel_P', 'CritKnowledgeLevel_F'}, 
        ['CritMysteryLevel_N'] = {'CritMysteryLevel', 'N', 'CritMysteryLevel_N', 'CritMysteryLevel_P', 'CritMysteryLevel_F'}, 
        ['CritTenebrousIgnoreDef_N'] = {'CritTenebrousIgnoreDef', 'N', 'CritTenebrousIgnoreDef_N', 'CritTenebrousIgnoreDef_P', 'CritTenebrousIgnoreDef_F'}, 
        ['CritTenebrousIgnoreDef_P'] = {'CritTenebrousIgnoreDef', 'P', 'CritTenebrousIgnoreDef_N', 'CritTenebrousIgnoreDef_P', 'CritTenebrousIgnoreDef_F'}, 
        ['CritTenebrousLevel_N'] = {'CritTenebrousLevel', 'N', 'CritTenebrousLevel_N', 'CritTenebrousLevel_P', 'CritTenebrousLevel_F'}, 
        ['DarknessAnti_N'] = {'DarknessAnti', 'N', 'DarknessAnti_N', 'DarknessAnti_P', 'DarknessAnti_F'}, 
        ['DarknessAnti_P'] = {'DarknessAnti', 'P', 'DarknessAnti_N', 'DarknessAnti_P', 'DarknessAnti_F'}, 
        ['DarknessAtk_N'] = {'DarknessAtk', 'N', 'DarknessAtk_N', 'DarknessAtk_P', 'DarknessAtk_F'}, 
        ['DarknessAtk_P'] = {'DarknessAtk', 'P', 'DarknessAtk_N', 'DarknessAtk_P', 'DarknessAtk_F'}, 
        ['DarknessHurtMulti_N'] = {'DarknessHurtMulti', 'N', 'DarknessHurtMulti_N', 'DarknessHurtMulti_P', 'DarknessHurtMulti_F'}, 
        ['DarknessHurtReduce_N'] = {'DarknessHurtReduce', 'N', 'DarknessHurtReduce_N', 'DarknessHurtReduce_P', 'DarknessHurtReduce_F'}, 
        ['DarknessLevel_N'] = {'DarknessLevel', 'N', 'DarknessLevel_N', 'DarknessLevel_P', 'DarknessLevel_F'}, 
        ['DarknessRate_N'] = {'DarknessRate', 'N', 'DarknessRate_N', 'DarknessRate_P', 'DarknessRate_F'}, 
        ['DarknessSteal_N'] = {'DarknessSteal', 'N', 'DarknessSteal_N', 'DarknessSteal_P', 'DarknessSteal_F'}, 
        ['DarknessSteal_P'] = {'DarknessSteal', 'P', 'DarknessSteal_N', 'DarknessSteal_P', 'DarknessSteal_F'}, 
        ['Def_N'] = {'Def', 'N', 'Def_N', 'Def_P', 'Def_F'}, 
        ['Def_P'] = {'Def', 'P', 'Def_N', 'Def_P', 'Def_F'}, 
        ['DeltaBeHealed_N'] = {'DeltaBeHealed', 'N', 'DeltaBeHealed_N', 'DeltaBeHealed_P', 'DeltaBeHealed_F'}, 
        ['DeltaBeHealed_P'] = {'DeltaBeHealed', 'P', 'DeltaBeHealed_N', 'DeltaBeHealed_P', 'DeltaBeHealed_F'}, 
        ['DeltaBeHurted_N'] = {'DeltaBeHurted', 'N', 'DeltaBeHurted_N', 'DeltaBeHurted_P', 'DeltaBeHurted_F'}, 
        ['DeltaHeal_N'] = {'DeltaHeal', 'N', 'DeltaHeal_N', 'DeltaHeal_P', 'DeltaHeal_F'}, 
        ['DeltaHeal_P'] = {'DeltaHeal', 'P', 'DeltaHeal_N', 'DeltaHeal_P', 'DeltaHeal_F'}, 
        ['DeltaHurt_N'] = {'DeltaHurt', 'N', 'DeltaHurt_N', 'DeltaHurt_P', 'DeltaHurt_F'}, 
        ['Dex_N'] = {'Dex', 'N', 'Dex_N', 'Dex_P', 'Dex_F'}, 
        ['Dex_P'] = {'Dex', 'P', 'Dex_N', 'Dex_P', 'Dex_F'}, 
        ['DisorderAnti_N'] = {'DisorderAnti', 'N', 'DisorderAnti_N', 'DisorderAnti_P', 'DisorderAnti_F'}, 
        ['DisorderAnti_P'] = {'DisorderAnti', 'P', 'DisorderAnti_N', 'DisorderAnti_P', 'DisorderAnti_F'}, 
        ['DisorderAtk_N'] = {'DisorderAtk', 'N', 'DisorderAtk_N', 'DisorderAtk_P', 'DisorderAtk_F'}, 
        ['DisorderAtk_P'] = {'DisorderAtk', 'P', 'DisorderAtk_N', 'DisorderAtk_P', 'DisorderAtk_F'}, 
        ['DisorderHurtMulti_N'] = {'DisorderHurtMulti', 'N', 'DisorderHurtMulti_N', 'DisorderHurtMulti_P', 'DisorderHurtMulti_F'}, 
        ['DisorderHurtReduce_N'] = {'DisorderHurtReduce', 'N', 'DisorderHurtReduce_N', 'DisorderHurtReduce_P', 'DisorderHurtReduce_F'}, 
        ['DisorderLevel_N'] = {'DisorderLevel', 'N', 'DisorderLevel_N', 'DisorderLevel_P', 'DisorderLevel_F'}, 
        ['DisorderRate_N'] = {'DisorderRate', 'N', 'DisorderRate_N', 'DisorderRate_P', 'DisorderRate_F'}, 
        ['DizzyAnti_N'] = {'DizzyAnti', 'N', 'DizzyAnti_N', 'DizzyAnti_P', 'DizzyAnti_F'}, 
        ['DizzyAnti_P'] = {'DizzyAnti', 'P', 'DizzyAnti_N', 'DizzyAnti_P', 'DizzyAnti_F'}, 
        ['DizzyHitRate_N'] = {'DizzyHitRate', 'N', 'DizzyHitRate_N', 'DizzyHitRate_P', 'DizzyHitRate_F'}, 
        ['DownAnti_N'] = {'DownAnti', 'N', 'DownAnti_N', 'DownAnti_P', 'DownAnti_F'}, 
        ['DownAnti_P'] = {'DownAnti', 'P', 'DownAnti_N', 'DownAnti_P', 'DownAnti_F'}, 
        ['DownHitRate_N'] = {'DownHitRate', 'N', 'DownHitRate_N', 'DownHitRate_P', 'DownHitRate_F'}, 
        ['EnhanceAirborne_N'] = {'EnhanceAirborne', 'N', 'EnhanceAirborne_N', 'EnhanceAirborne_P', 'EnhanceAirborne_F'}, 
        ['EnhanceAirborne_P'] = {'EnhanceAirborne', 'P', 'EnhanceAirborne_N', 'EnhanceAirborne_P', 'EnhanceAirborne_F'}, 
        ['EnhanceAllControl_N'] = {'EnhanceAllControl', 'N', 'EnhanceAllControl_N', 'EnhanceAllControl_P', 'EnhanceAllControl_F'}, 
        ['EnhanceAllControl_P'] = {'EnhanceAllControl', 'P', 'EnhanceAllControl_N', 'EnhanceAllControl_P', 'EnhanceAllControl_F'}, 
        ['EnhanceDizzy_N'] = {'EnhanceDizzy', 'N', 'EnhanceDizzy_N', 'EnhanceDizzy_P', 'EnhanceDizzy_F'}, 
        ['EnhanceDizzy_P'] = {'EnhanceDizzy', 'P', 'EnhanceDizzy_N', 'EnhanceDizzy_P', 'EnhanceDizzy_F'}, 
        ['EnhanceDown_N'] = {'EnhanceDown', 'N', 'EnhanceDown_N', 'EnhanceDown_P', 'EnhanceDown_F'}, 
        ['EnhanceDown_P'] = {'EnhanceDown', 'P', 'EnhanceDown_N', 'EnhanceDown_P', 'EnhanceDown_F'}, 
        ['EnhanceFear_N'] = {'EnhanceFear', 'N', 'EnhanceFear_N', 'EnhanceFear_P', 'EnhanceFear_F'}, 
        ['EnhanceFear_P'] = {'EnhanceFear', 'P', 'EnhanceFear_N', 'EnhanceFear_P', 'EnhanceFear_F'}, 
        ['EnhancePull_N'] = {'EnhancePull', 'N', 'EnhancePull_N', 'EnhancePull_P', 'EnhancePull_F'}, 
        ['EnhancePull_P'] = {'EnhancePull', 'P', 'EnhancePull_N', 'EnhancePull_P', 'EnhancePull_F'}, 
        ['EnhanceSilence_N'] = {'EnhanceSilence', 'N', 'EnhanceSilence_N', 'EnhanceSilence_P', 'EnhanceSilence_F'}, 
        ['EnhanceSilence_P'] = {'EnhanceSilence', 'P', 'EnhanceSilence_N', 'EnhanceSilence_P', 'EnhanceSilence_F'}, 
        ['EnhanceSleep_N'] = {'EnhanceSleep', 'N', 'EnhanceSleep_N', 'EnhanceSleep_P', 'EnhanceSleep_F'}, 
        ['EnhanceSleep_P'] = {'EnhanceSleep', 'P', 'EnhanceSleep_N', 'EnhanceSleep_P', 'EnhanceSleep_F'}, 
        ['EnhanceSlow_N'] = {'EnhanceSlow', 'N', 'EnhanceSlow_N', 'EnhanceSlow_P', 'EnhanceSlow_F'}, 
        ['EnhanceSlow_P'] = {'EnhanceSlow', 'P', 'EnhanceSlow_N', 'EnhanceSlow_P', 'EnhanceSlow_F'}, 
        ['EnhanceTied_N'] = {'EnhanceTied', 'N', 'EnhanceTied_N', 'EnhanceTied_P', 'EnhanceTied_F'}, 
        ['EnhanceTied_P'] = {'EnhanceTied', 'P', 'EnhanceTied_N', 'EnhanceTied_P', 'EnhanceTied_F'}, 
        ['FateAnti_N'] = {'FateAnti', 'N', 'FateAnti_N', 'FateAnti_P', 'FateAnti_F'}, 
        ['FateAnti_P'] = {'FateAnti', 'P', 'FateAnti_N', 'FateAnti_P', 'FateAnti_F'}, 
        ['FateAtk_N'] = {'FateAtk', 'N', 'FateAtk_N', 'FateAtk_P', 'FateAtk_F'}, 
        ['FateAtk_P'] = {'FateAtk', 'P', 'FateAtk_N', 'FateAtk_P', 'FateAtk_F'}, 
        ['FateHurtMulti_N'] = {'FateHurtMulti', 'N', 'FateHurtMulti_N', 'FateHurtMulti_P', 'FateHurtMulti_F'}, 
        ['FateHurtReduce_N'] = {'FateHurtReduce', 'N', 'FateHurtReduce_N', 'FateHurtReduce_P', 'FateHurtReduce_F'}, 
        ['FateLevel_N'] = {'FateLevel', 'N', 'FateLevel_N', 'FateLevel_P', 'FateLevel_F'}, 
        ['FateRate_N'] = {'FateRate', 'N', 'FateRate_N', 'FateRate_P', 'FateRate_F'}, 
        ['FearAnti_N'] = {'FearAnti', 'N', 'FearAnti_N', 'FearAnti_P', 'FearAnti_F'}, 
        ['FearAnti_P'] = {'FearAnti', 'P', 'FearAnti_N', 'FearAnti_P', 'FearAnti_F'}, 
        ['FearHitRate_N'] = {'FearHitRate', 'N', 'FearHitRate_N', 'FearHitRate_P', 'FearHitRate_F'}, 
        ['FeatherwitHurtMulti_N'] = {'FeatherwitHurtMulti', 'N', 'FeatherwitHurtMulti_N', 'FeatherwitHurtMulti_P', 'FeatherwitHurtMulti_F'}, 
        ['FeatherwitHurtMulti_P'] = {'FeatherwitHurtMulti', 'P', 'FeatherwitHurtMulti_N', 'FeatherwitHurtMulti_P', 'FeatherwitHurtMulti_F'}, 
        ['FeatherwitHurtReduce_N'] = {'FeatherwitHurtReduce', 'N', 'FeatherwitHurtReduce_N', 'FeatherwitHurtReduce_P', 'FeatherwitHurtReduce_F'}, 
        ['FeatherwitHurtReduce_P'] = {'FeatherwitHurtReduce', 'P', 'FeatherwitHurtReduce_N', 'FeatherwitHurtReduce_P', 'FeatherwitHurtReduce_F'}, 
        ['HpReg_N'] = {'HpReg', 'N', 'HpReg_N', 'HpReg_P', 'HpReg_F'}, 
        ['HpReg_P'] = {'HpReg', 'P', 'HpReg_N', 'HpReg_P', 'HpReg_F'}, 
        ['IgnoreAbundanceAnti_N'] = {'IgnoreAbundanceAnti', 'N', 'IgnoreAbundanceAnti_N', 'IgnoreAbundanceAnti_P', 'IgnoreAbundanceAnti_F'}, 
        ['IgnoreAbundanceAnti_P'] = {'IgnoreAbundanceAnti', 'P', 'IgnoreAbundanceAnti_N', 'IgnoreAbundanceAnti_P', 'IgnoreAbundanceAnti_F'}, 
        ['IgnoreAllEle_N'] = {'IgnoreAllEle', 'N', 'IgnoreAllEle_N', 'IgnoreAllEle_P', 'IgnoreAllEle_F'}, 
        ['IgnoreAllEle_P'] = {'IgnoreAllEle', 'P', 'IgnoreAllEle_N', 'IgnoreAllEle_P', 'IgnoreAllEle_F'}, 
        ['IgnoreCalamityAnti_N'] = {'IgnoreCalamityAnti', 'N', 'IgnoreCalamityAnti_N', 'IgnoreCalamityAnti_P', 'IgnoreCalamityAnti_F'}, 
        ['IgnoreCalamityAnti_P'] = {'IgnoreCalamityAnti', 'P', 'IgnoreCalamityAnti_N', 'IgnoreCalamityAnti_P', 'IgnoreCalamityAnti_F'}, 
        ['IgnoreChaosAnti_N'] = {'IgnoreChaosAnti', 'N', 'IgnoreChaosAnti_N', 'IgnoreChaosAnti_P', 'IgnoreChaosAnti_F'}, 
        ['IgnoreChaosAnti_P'] = {'IgnoreChaosAnti', 'P', 'IgnoreChaosAnti_N', 'IgnoreChaosAnti_P', 'IgnoreChaosAnti_F'}, 
        ['IgnoreDarknessAnti_N'] = {'IgnoreDarknessAnti', 'N', 'IgnoreDarknessAnti_N', 'IgnoreDarknessAnti_P', 'IgnoreDarknessAnti_F'}, 
        ['IgnoreDarknessAnti_P'] = {'IgnoreDarknessAnti', 'P', 'IgnoreDarknessAnti_N', 'IgnoreDarknessAnti_P', 'IgnoreDarknessAnti_F'}, 
        ['IgnoreDisorderAnti_N'] = {'IgnoreDisorderAnti', 'N', 'IgnoreDisorderAnti_N', 'IgnoreDisorderAnti_P', 'IgnoreDisorderAnti_F'}, 
        ['IgnoreDisorderAnti_P'] = {'IgnoreDisorderAnti', 'P', 'IgnoreDisorderAnti_N', 'IgnoreDisorderAnti_P', 'IgnoreDisorderAnti_F'}, 
        ['IgnoreFateAnti_N'] = {'IgnoreFateAnti', 'N', 'IgnoreFateAnti_N', 'IgnoreFateAnti_P', 'IgnoreFateAnti_F'}, 
        ['IgnoreFateAnti_P'] = {'IgnoreFateAnti', 'P', 'IgnoreFateAnti_N', 'IgnoreFateAnti_P', 'IgnoreFateAnti_F'}, 
        ['IgnoreKnowledgeAnti_N'] = {'IgnoreKnowledgeAnti', 'N', 'IgnoreKnowledgeAnti_N', 'IgnoreKnowledgeAnti_P', 'IgnoreKnowledgeAnti_F'}, 
        ['IgnoreKnowledgeAnti_P'] = {'IgnoreKnowledgeAnti', 'P', 'IgnoreKnowledgeAnti_N', 'IgnoreKnowledgeAnti_P', 'IgnoreKnowledgeAnti_F'}, 
        ['IgnoreMysteryAnti_N'] = {'IgnoreMysteryAnti', 'N', 'IgnoreMysteryAnti_N', 'IgnoreMysteryAnti_P', 'IgnoreMysteryAnti_F'}, 
        ['IgnoreMysteryAnti_P'] = {'IgnoreMysteryAnti', 'P', 'IgnoreMysteryAnti_N', 'IgnoreMysteryAnti_P', 'IgnoreMysteryAnti_F'}, 
        ['IgnoreTenebrousAnti_N'] = {'IgnoreTenebrousAnti', 'N', 'IgnoreTenebrousAnti_N', 'IgnoreTenebrousAnti_P', 'IgnoreTenebrousAnti_F'}, 
        ['IgnoreTenebrousAnti_P'] = {'IgnoreTenebrousAnti', 'P', 'IgnoreTenebrousAnti_N', 'IgnoreTenebrousAnti_P', 'IgnoreTenebrousAnti_F'}, 
        ['Int_N'] = {'Int', 'N', 'Int_N', 'Int_P', 'Int_F'}, 
        ['Int_P'] = {'Int', 'P', 'Int_N', 'Int_P', 'Int_F'}, 
        ['KnowledgeAnti_N'] = {'KnowledgeAnti', 'N', 'KnowledgeAnti_N', 'KnowledgeAnti_P', 'KnowledgeAnti_F'}, 
        ['KnowledgeAnti_P'] = {'KnowledgeAnti', 'P', 'KnowledgeAnti_N', 'KnowledgeAnti_P', 'KnowledgeAnti_F'}, 
        ['KnowledgeAtk_N'] = {'KnowledgeAtk', 'N', 'KnowledgeAtk_N', 'KnowledgeAtk_P', 'KnowledgeAtk_F'}, 
        ['KnowledgeAtk_P'] = {'KnowledgeAtk', 'P', 'KnowledgeAtk_N', 'KnowledgeAtk_P', 'KnowledgeAtk_F'}, 
        ['KnowledgeHurtMulti_N'] = {'KnowledgeHurtMulti', 'N', 'KnowledgeHurtMulti_N', 'KnowledgeHurtMulti_P', 'KnowledgeHurtMulti_F'}, 
        ['KnowledgeHurtReduce_N'] = {'KnowledgeHurtReduce', 'N', 'KnowledgeHurtReduce_N', 'KnowledgeHurtReduce_P', 'KnowledgeHurtReduce_F'}, 
        ['KnowledgeLevel_N'] = {'KnowledgeLevel', 'N', 'KnowledgeLevel_N', 'KnowledgeLevel_P', 'KnowledgeLevel_F'}, 
        ['KnowledgeRate_N'] = {'KnowledgeRate', 'N', 'KnowledgeRate_N', 'KnowledgeRate_P', 'KnowledgeRate_F'}, 
        ['LockedMaxHp_N'] = {'LockedMaxHp', 'N', 'LockedMaxHp_N', 'LockedMaxHp_P', 'LockedMaxHp_F'}, 
        ['LockedMaxHp_P'] = {'LockedMaxHp', 'P', 'LockedMaxHp_N', 'LockedMaxHp_P', 'LockedMaxHp_F'}, 
        ['MaxHp_F'] = {'MaxHp', 'F', 'MaxHp_N', 'MaxHp_P', 'MaxHp_F'}, 
        ['MaxHp_N'] = {'MaxHp', 'N', 'MaxHp_N', 'MaxHp_P', 'MaxHp_F'}, 
        ['MaxHp_P'] = {'MaxHp', 'P', 'MaxHp_N', 'MaxHp_P', 'MaxHp_F'}, 
        ['MaxStaminaValue_F'] = {'MaxStaminaValue', 'F', 'MaxStaminaValue_N', 'MaxStaminaValue_P', 'MaxStaminaValue_F'}, 
        ['MaxStaminaValue_N'] = {'MaxStaminaValue', 'N', 'MaxStaminaValue_N', 'MaxStaminaValue_P', 'MaxStaminaValue_F'}, 
        ['MaxStaminaValue_P'] = {'MaxStaminaValue', 'P', 'MaxStaminaValue_N', 'MaxStaminaValue_P', 'MaxStaminaValue_F'}, 
        ['MysteryAnti_N'] = {'MysteryAnti', 'N', 'MysteryAnti_N', 'MysteryAnti_P', 'MysteryAnti_F'}, 
        ['MysteryAnti_P'] = {'MysteryAnti', 'P', 'MysteryAnti_N', 'MysteryAnti_P', 'MysteryAnti_F'}, 
        ['MysteryAtk_N'] = {'MysteryAtk', 'N', 'MysteryAtk_N', 'MysteryAtk_P', 'MysteryAtk_F'}, 
        ['MysteryAtk_P'] = {'MysteryAtk', 'P', 'MysteryAtk_N', 'MysteryAtk_P', 'MysteryAtk_F'}, 
        ['MysteryHurtMulti_N'] = {'MysteryHurtMulti', 'N', 'MysteryHurtMulti_N', 'MysteryHurtMulti_P', 'MysteryHurtMulti_F'}, 
        ['MysteryHurtReduce_N'] = {'MysteryHurtReduce', 'N', 'MysteryHurtReduce_N', 'MysteryHurtReduce_P', 'MysteryHurtReduce_F'}, 
        ['MysteryLevel_N'] = {'MysteryLevel', 'N', 'MysteryLevel_N', 'MysteryLevel_P', 'MysteryLevel_F'}, 
        ['MysteryRate_N'] = {'MysteryRate', 'N', 'MysteryRate_N', 'MysteryRate_P', 'MysteryRate_F'}, 
        ['Pierce_N'] = {'Pierce', 'N', 'Pierce_N', 'Pierce_P', 'Pierce_F'}, 
        ['Pierce_P'] = {'Pierce', 'P', 'Pierce_N', 'Pierce_P', 'Pierce_F'}, 
        ['Pow_N'] = {'Pow', 'N', 'Pow_N', 'Pow_P', 'Pow_F'}, 
        ['Pow_P'] = {'Pow', 'P', 'Pow_N', 'Pow_P', 'Pow_F'}, 
        ['ProHurtMulti9_N'] = {'ProHurtMulti9', 'N', 'ProHurtMulti9_N', 'ProHurtMulti9_P', 'ProHurtMulti9_F'}, 
        ['ProHurtMulti9_P'] = {'ProHurtMulti9', 'P', 'ProHurtMulti9_N', 'ProHurtMulti9_P', 'ProHurtMulti9_F'}, 
        ['ProHurtReduce9_N'] = {'ProHurtReduce9', 'N', 'ProHurtReduce9_N', 'ProHurtReduce9_P', 'ProHurtReduce9_F'}, 
        ['ProHurtReduce9_P'] = {'ProHurtReduce9', 'P', 'ProHurtReduce9_N', 'ProHurtReduce9_P', 'ProHurtReduce9_F'}, 
        ['PullAnti_N'] = {'PullAnti', 'N', 'PullAnti_N', 'PullAnti_P', 'PullAnti_F'}, 
        ['PullAnti_P'] = {'PullAnti', 'P', 'PullAnti_N', 'PullAnti_P', 'PullAnti_F'}, 
        ['PullHitRate_N'] = {'PullHitRate', 'N', 'PullHitRate_N', 'PullHitRate_P', 'PullHitRate_F'}, 
        ['RaceHurtMulti1_N'] = {'RaceHurtMulti1', 'N', 'RaceHurtMulti1_N', 'RaceHurtMulti1_P', 'RaceHurtMulti1_F'}, 
        ['RaceHurtMulti1_P'] = {'RaceHurtMulti1', 'P', 'RaceHurtMulti1_N', 'RaceHurtMulti1_P', 'RaceHurtMulti1_F'}, 
        ['RaceHurtMulti2_N'] = {'RaceHurtMulti2', 'N', 'RaceHurtMulti2_N', 'RaceHurtMulti2_P', 'RaceHurtMulti2_F'}, 
        ['RaceHurtMulti2_P'] = {'RaceHurtMulti2', 'P', 'RaceHurtMulti2_N', 'RaceHurtMulti2_P', 'RaceHurtMulti2_F'}, 
        ['RaceHurtMulti3_N'] = {'RaceHurtMulti3', 'N', 'RaceHurtMulti3_N', 'RaceHurtMulti3_P', 'RaceHurtMulti3_F'}, 
        ['RaceHurtMulti3_P'] = {'RaceHurtMulti3', 'P', 'RaceHurtMulti3_N', 'RaceHurtMulti3_P', 'RaceHurtMulti3_F'}, 
        ['RaceHurtMulti4_N'] = {'RaceHurtMulti4', 'N', 'RaceHurtMulti4_N', 'RaceHurtMulti4_P', 'RaceHurtMulti4_F'}, 
        ['RaceHurtMulti4_P'] = {'RaceHurtMulti4', 'P', 'RaceHurtMulti4_N', 'RaceHurtMulti4_P', 'RaceHurtMulti4_F'}, 
        ['RaceHurtMulti5_N'] = {'RaceHurtMulti5', 'N', 'RaceHurtMulti5_N', 'RaceHurtMulti5_P', 'RaceHurtMulti5_F'}, 
        ['RaceHurtMulti5_P'] = {'RaceHurtMulti5', 'P', 'RaceHurtMulti5_N', 'RaceHurtMulti5_P', 'RaceHurtMulti5_F'}, 
        ['RaceHurtReduce1_N'] = {'RaceHurtReduce1', 'N', 'RaceHurtReduce1_N', 'RaceHurtReduce1_P', 'RaceHurtReduce1_F'}, 
        ['RaceHurtReduce1_P'] = {'RaceHurtReduce1', 'P', 'RaceHurtReduce1_N', 'RaceHurtReduce1_P', 'RaceHurtReduce1_F'}, 
        ['RaceHurtReduce2_N'] = {'RaceHurtReduce2', 'N', 'RaceHurtReduce2_N', 'RaceHurtReduce2_P', 'RaceHurtReduce2_F'}, 
        ['RaceHurtReduce2_P'] = {'RaceHurtReduce2', 'P', 'RaceHurtReduce2_N', 'RaceHurtReduce2_P', 'RaceHurtReduce2_F'}, 
        ['RaceHurtReduce3_N'] = {'RaceHurtReduce3', 'N', 'RaceHurtReduce3_N', 'RaceHurtReduce3_P', 'RaceHurtReduce3_F'}, 
        ['RaceHurtReduce3_P'] = {'RaceHurtReduce3', 'P', 'RaceHurtReduce3_N', 'RaceHurtReduce3_P', 'RaceHurtReduce3_F'}, 
        ['RaceHurtReduce4_N'] = {'RaceHurtReduce4', 'N', 'RaceHurtReduce4_N', 'RaceHurtReduce4_P', 'RaceHurtReduce4_F'}, 
        ['RaceHurtReduce4_P'] = {'RaceHurtReduce4', 'P', 'RaceHurtReduce4_N', 'RaceHurtReduce4_P', 'RaceHurtReduce4_F'}, 
        ['RaceHurtReduce5_N'] = {'RaceHurtReduce5', 'N', 'RaceHurtReduce5_N', 'RaceHurtReduce5_P', 'RaceHurtReduce5_F'}, 
        ['RaceHurtReduce5_P'] = {'RaceHurtReduce5', 'P', 'RaceHurtReduce5_N', 'RaceHurtReduce5_P', 'RaceHurtReduce5_F'}, 
        ['ShieldBreak_N'] = {'ShieldBreak', 'N', 'ShieldBreak_N', 'ShieldBreak_P', 'ShieldBreak_F'}, 
        ['SilenceAnti_N'] = {'SilenceAnti', 'N', 'SilenceAnti_N', 'SilenceAnti_P', 'SilenceAnti_F'}, 
        ['SilenceAnti_P'] = {'SilenceAnti', 'P', 'SilenceAnti_N', 'SilenceAnti_P', 'SilenceAnti_F'}, 
        ['SilenceHitRate_N'] = {'SilenceHitRate', 'N', 'SilenceHitRate_N', 'SilenceHitRate_P', 'SilenceHitRate_F'}, 
        ['SkillAnti_N'] = {'SkillAnti', 'N', 'SkillAnti_N', 'SkillAnti_P', 'SkillAnti_F'}, 
        ['SkillAnti_P'] = {'SkillAnti', 'P', 'SkillAnti_N', 'SkillAnti_P', 'SkillAnti_F'}, 
        ['SkillEnhance_N'] = {'SkillEnhance', 'N', 'SkillEnhance_N', 'SkillEnhance_P', 'SkillEnhance_F'}, 
        ['SkillEnhance_P'] = {'SkillEnhance', 'P', 'SkillEnhance_N', 'SkillEnhance_P', 'SkillEnhance_F'}, 
        ['SleepAnti_N'] = {'SleepAnti', 'N', 'SleepAnti_N', 'SleepAnti_P', 'SleepAnti_F'}, 
        ['SleepAnti_P'] = {'SleepAnti', 'P', 'SleepAnti_N', 'SleepAnti_P', 'SleepAnti_F'}, 
        ['SleepHitRate_N'] = {'SleepHitRate', 'N', 'SleepHitRate_N', 'SleepHitRate_P', 'SleepHitRate_F'}, 
        ['SlowAnti_N'] = {'SlowAnti', 'N', 'SlowAnti_N', 'SlowAnti_P', 'SlowAnti_F'}, 
        ['SlowAnti_P'] = {'SlowAnti', 'P', 'SlowAnti_N', 'SlowAnti_P', 'SlowAnti_F'}, 
        ['SlowHitRate_N'] = {'SlowHitRate', 'N', 'SlowHitRate_N', 'SlowHitRate_P', 'SlowHitRate_F'}, 
        ['Speed_N'] = {'Speed', 'N', 'Speed_N', 'Speed_P', 'Speed_F'}, 
        ['Speed_P'] = {'Speed', 'P', 'Speed_N', 'Speed_P', 'Speed_F'}, 
        ['Str_N'] = {'Str', 'N', 'Str_N', 'Str_P', 'Str_F'}, 
        ['Str_P'] = {'Str', 'P', 'Str_N', 'Str_P', 'Str_F'}, 
        ['SunHurtMulti_N'] = {'SunHurtMulti', 'N', 'SunHurtMulti_N', 'SunHurtMulti_P', 'SunHurtMulti_F'}, 
        ['SunHurtMulti_P'] = {'SunHurtMulti', 'P', 'SunHurtMulti_N', 'SunHurtMulti_P', 'SunHurtMulti_F'}, 
        ['SunHurtReduce_N'] = {'SunHurtReduce', 'N', 'SunHurtReduce_N', 'SunHurtReduce_P', 'SunHurtReduce_F'}, 
        ['SunHurtReduce_P'] = {'SunHurtReduce', 'P', 'SunHurtReduce_N', 'SunHurtReduce_P', 'SunHurtReduce_F'}, 
        ['TenebrousAnti_N'] = {'TenebrousAnti', 'N', 'TenebrousAnti_N', 'TenebrousAnti_P', 'TenebrousAnti_F'}, 
        ['TenebrousAnti_P'] = {'TenebrousAnti', 'P', 'TenebrousAnti_N', 'TenebrousAnti_P', 'TenebrousAnti_F'}, 
        ['TenebrousAtk_N'] = {'TenebrousAtk', 'N', 'TenebrousAtk_N', 'TenebrousAtk_P', 'TenebrousAtk_F'}, 
        ['TenebrousAtk_P'] = {'TenebrousAtk', 'P', 'TenebrousAtk_N', 'TenebrousAtk_P', 'TenebrousAtk_F'}, 
        ['TenebrousHurtMulti_N'] = {'TenebrousHurtMulti', 'N', 'TenebrousHurtMulti_N', 'TenebrousHurtMulti_P', 'TenebrousHurtMulti_F'}, 
        ['TenebrousHurtReduce_N'] = {'TenebrousHurtReduce', 'N', 'TenebrousHurtReduce_N', 'TenebrousHurtReduce_P', 'TenebrousHurtReduce_F'}, 
        ['TenebrousIgnoreDef_N'] = {'TenebrousIgnoreDef', 'N', 'TenebrousIgnoreDef_N', 'TenebrousIgnoreDef_P', 'TenebrousIgnoreDef_F'}, 
        ['TenebrousIgnoreDef_P'] = {'TenebrousIgnoreDef', 'P', 'TenebrousIgnoreDef_N', 'TenebrousIgnoreDef_P', 'TenebrousIgnoreDef_F'}, 
        ['TenebrousLevel_N'] = {'TenebrousLevel', 'N', 'TenebrousLevel_N', 'TenebrousLevel_P', 'TenebrousLevel_F'}, 
        ['TenebrousRate_N'] = {'TenebrousRate', 'N', 'TenebrousRate_N', 'TenebrousRate_P', 'TenebrousRate_F'}, 
        ['TiedAnti_N'] = {'TiedAnti', 'N', 'TiedAnti_N', 'TiedAnti_P', 'TiedAnti_F'}, 
        ['TiedAnti_P'] = {'TiedAnti', 'P', 'TiedAnti_N', 'TiedAnti_P', 'TiedAnti_F'}, 
        ['TiedHitRate_N'] = {'TiedHitRate', 'N', 'TiedHitRate_N', 'TiedHitRate_P', 'TiedHitRate_F'}, 
        ['UltimatePointSpeed_N'] = {'UltimatePointSpeed', 'N', 'UltimatePointSpeed_N', 'UltimatePointSpeed_P', 'UltimatePointSpeed_F'}, 
        ['UltimatePointSpeed_P'] = {'UltimatePointSpeed', 'P', 'UltimatePointSpeed_N', 'UltimatePointSpeed_P', 'UltimatePointSpeed_F'}, 
        ['VisionaryHurtMulti_N'] = {'VisionaryHurtMulti', 'N', 'VisionaryHurtMulti_N', 'VisionaryHurtMulti_P', 'VisionaryHurtMulti_F'}, 
        ['VisionaryHurtMulti_P'] = {'VisionaryHurtMulti', 'P', 'VisionaryHurtMulti_N', 'VisionaryHurtMulti_P', 'VisionaryHurtMulti_F'}, 
        ['VisionaryHurtReduce_N'] = {'VisionaryHurtReduce', 'N', 'VisionaryHurtReduce_N', 'VisionaryHurtReduce_P', 'VisionaryHurtReduce_F'}, 
        ['VisionaryHurtReduce_P'] = {'VisionaryHurtReduce', 'P', 'VisionaryHurtReduce_N', 'VisionaryHurtReduce_P', 'VisionaryHurtReduce_F'}, 
        ['WarriorHurtMulti_N'] = {'WarriorHurtMulti', 'N', 'WarriorHurtMulti_N', 'WarriorHurtMulti_P', 'WarriorHurtMulti_F'}, 
        ['WarriorHurtMulti_P'] = {'WarriorHurtMulti', 'P', 'WarriorHurtMulti_N', 'WarriorHurtMulti_P', 'WarriorHurtMulti_F'}, 
        ['WarriorHurtReduce_N'] = {'WarriorHurtReduce', 'N', 'WarriorHurtReduce_N', 'WarriorHurtReduce_P', 'WarriorHurtReduce_F'}, 
        ['WarriorHurtReduce_P'] = {'WarriorHurtReduce', 'P', 'WarriorHurtReduce_N', 'WarriorHurtReduce_P', 'WarriorHurtReduce_F'}, 
        ['mAtkMax_F'] = {'mAtkMax', 'F', 'mAtkMax_N', 'mAtkMax_P', 'mAtkMax_F'}, 
        ['mAtkMax_N'] = {'mAtkMax', 'N', 'mAtkMax_N', 'mAtkMax_P', 'mAtkMax_F'}, 
        ['mAtkMax_P'] = {'mAtkMax', 'P', 'mAtkMax_N', 'mAtkMax_P', 'mAtkMax_F'}, 
        ['mAtkMin_F'] = {'mAtkMin', 'F', 'mAtkMin_N', 'mAtkMin_P', 'mAtkMin_F'}, 
        ['mAtkMin_N'] = {'mAtkMin', 'N', 'mAtkMin_N', 'mAtkMin_P', 'mAtkMin_F'}, 
        ['mAtkMin_P'] = {'mAtkMin', 'P', 'mAtkMin_N', 'mAtkMin_P', 'mAtkMin_F'}, 
        ['mAtkSpd_N'] = {'mAtkSpd', 'N', 'mAtkSpd_N', 'mAtkSpd_P', 'mAtkSpd_F'}, 
        ['mAtkSpd_P'] = {'mAtkSpd', 'P', 'mAtkSpd_N', 'mAtkSpd_P', 'mAtkSpd_F'}, 
        ['mBlock_N'] = {'mBlock', 'N', 'mBlock_N', 'mBlock_P', 'mBlock_F'}, 
        ['mBlock_P'] = {'mBlock', 'P', 'mBlock_N', 'mBlock_P', 'mBlock_F'}, 
        ['mCritAnti_N'] = {'mCritAnti', 'N', 'mCritAnti_N', 'mCritAnti_P', 'mCritAnti_F'}, 
        ['mCritAnti_P'] = {'mCritAnti', 'P', 'mCritAnti_N', 'mCritAnti_P', 'mCritAnti_F'}, 
        ['mCritDef_N'] = {'mCritDef', 'N', 'mCritDef_N', 'mCritDef_P', 'mCritDef_F'}, 
        ['mCritDef_P'] = {'mCritDef', 'P', 'mCritDef_N', 'mCritDef_P', 'mCritDef_F'}, 
        ['mCritHurt_N'] = {'mCritHurt', 'N', 'mCritHurt_N', 'mCritHurt_P', 'mCritHurt_F'}, 
        ['mCritHurt_P'] = {'mCritHurt', 'P', 'mCritHurt_N', 'mCritHurt_P', 'mCritHurt_F'}, 
        ['mCrit_N'] = {'mCrit', 'N', 'mCrit_N', 'mCrit_P', 'mCrit_F'}, 
        ['mCrit_P'] = {'mCrit', 'P', 'mCrit_N', 'mCrit_P', 'mCrit_F'}, 
        ['mDef_F'] = {'mDef', 'F', 'mDef_N', 'mDef_P', 'mDef_F'}, 
        ['mDef_N'] = {'mDef', 'N', 'mDef_N', 'mDef_P', 'mDef_F'}, 
        ['mDef_P'] = {'mDef', 'P', 'mDef_N', 'mDef_P', 'mDef_F'}, 
        ['mHurtMulti_N'] = {'mHurtMulti', 'N', 'mHurtMulti_N', 'mHurtMulti_P', 'mHurtMulti_F'}, 
        ['mHurtMulti_P'] = {'mHurtMulti', 'P', 'mHurtMulti_N', 'mHurtMulti_P', 'mHurtMulti_F'}, 
        ['mHurtReduce_N'] = {'mHurtReduce', 'N', 'mHurtReduce_N', 'mHurtReduce_P', 'mHurtReduce_F'}, 
        ['mHurtReduce_P'] = {'mHurtReduce', 'P', 'mHurtReduce_N', 'mHurtReduce_P', 'mHurtReduce_F'}, 
        ['mIgnoreDef_N'] = {'mIgnoreDef', 'N', 'mIgnoreDef_N', 'mIgnoreDef_P', 'mIgnoreDef_F'}, 
        ['mIgnoreDef_P'] = {'mIgnoreDef', 'P', 'mIgnoreDef_N', 'mIgnoreDef_P', 'mIgnoreDef_F'}, 
        ['mPierce_N'] = {'mPierce', 'N', 'mPierce_N', 'mPierce_P', 'mPierce_F'}, 
        ['mPierce_P'] = {'mPierce', 'P', 'mPierce_N', 'mPierce_P', 'mPierce_F'}, 
        ['pAtkMax_F'] = {'pAtkMax', 'F', 'pAtkMax_N', 'pAtkMax_P', 'pAtkMax_F'}, 
        ['pAtkMax_N'] = {'pAtkMax', 'N', 'pAtkMax_N', 'pAtkMax_P', 'pAtkMax_F'}, 
        ['pAtkMax_P'] = {'pAtkMax', 'P', 'pAtkMax_N', 'pAtkMax_P', 'pAtkMax_F'}, 
        ['pAtkMin_F'] = {'pAtkMin', 'F', 'pAtkMin_N', 'pAtkMin_P', 'pAtkMin_F'}, 
        ['pAtkMin_N'] = {'pAtkMin', 'N', 'pAtkMin_N', 'pAtkMin_P', 'pAtkMin_F'}, 
        ['pAtkMin_P'] = {'pAtkMin', 'P', 'pAtkMin_N', 'pAtkMin_P', 'pAtkMin_F'}, 
        ['pAtkSpd_N'] = {'pAtkSpd', 'N', 'pAtkSpd_N', 'pAtkSpd_P', 'pAtkSpd_F'}, 
        ['pAtkSpd_P'] = {'pAtkSpd', 'P', 'pAtkSpd_N', 'pAtkSpd_P', 'pAtkSpd_F'}, 
        ['pBlock_N'] = {'pBlock', 'N', 'pBlock_N', 'pBlock_P', 'pBlock_F'}, 
        ['pBlock_P'] = {'pBlock', 'P', 'pBlock_N', 'pBlock_P', 'pBlock_F'}, 
        ['pCritAnti_N'] = {'pCritAnti', 'N', 'pCritAnti_N', 'pCritAnti_P', 'pCritAnti_F'}, 
        ['pCritAnti_P'] = {'pCritAnti', 'P', 'pCritAnti_N', 'pCritAnti_P', 'pCritAnti_F'}, 
        ['pCritDef_N'] = {'pCritDef', 'N', 'pCritDef_N', 'pCritDef_P', 'pCritDef_F'}, 
        ['pCritDef_P'] = {'pCritDef', 'P', 'pCritDef_N', 'pCritDef_P', 'pCritDef_F'}, 
        ['pCritHurt_N'] = {'pCritHurt', 'N', 'pCritHurt_N', 'pCritHurt_P', 'pCritHurt_F'}, 
        ['pCritHurt_P'] = {'pCritHurt', 'P', 'pCritHurt_N', 'pCritHurt_P', 'pCritHurt_F'}, 
        ['pCrit_N'] = {'pCrit', 'N', 'pCrit_N', 'pCrit_P', 'pCrit_F'}, 
        ['pCrit_P'] = {'pCrit', 'P', 'pCrit_N', 'pCrit_P', 'pCrit_F'}, 
        ['pDef_F'] = {'pDef', 'F', 'pDef_N', 'pDef_P', 'pDef_F'}, 
        ['pDef_N'] = {'pDef', 'N', 'pDef_N', 'pDef_P', 'pDef_F'}, 
        ['pDef_P'] = {'pDef', 'P', 'pDef_N', 'pDef_P', 'pDef_F'}, 
        ['pHurtMulti_N'] = {'pHurtMulti', 'N', 'pHurtMulti_N', 'pHurtMulti_P', 'pHurtMulti_F'}, 
        ['pHurtMulti_P'] = {'pHurtMulti', 'P', 'pHurtMulti_N', 'pHurtMulti_P', 'pHurtMulti_F'}, 
        ['pHurtReduce_N'] = {'pHurtReduce', 'N', 'pHurtReduce_N', 'pHurtReduce_P', 'pHurtReduce_F'}, 
        ['pHurtReduce_P'] = {'pHurtReduce', 'P', 'pHurtReduce_N', 'pHurtReduce_P', 'pHurtReduce_F'}, 
        ['pIgnoreDef_N'] = {'pIgnoreDef', 'N', 'pIgnoreDef_N', 'pIgnoreDef_P', 'pIgnoreDef_F'}, 
        ['pIgnoreDef_P'] = {'pIgnoreDef', 'P', 'pIgnoreDef_N', 'pIgnoreDef_P', 'pIgnoreDef_F'}, 
        ['pPierce_N'] = {'pPierce', 'N', 'pPierce_N', 'pPierce_P', 'pPierce_F'}, 
        ['pPierce_P'] = {'pPierce', 'P', 'pPierce_N', 'pPierce_P', 'pPierce_F'}, 
    },
    propModeList = {'IgnoreTenebrousAnti_N', 'SkillEnhance_P', 'EnhanceSleep_P', 'Con_P', 'ArbiterHurtReduce_P', 'FeatherwitHurtMulti_P', 'SkillEnhance_N', 'CritAnti_N', 'DeltaHurt_N', 'FateHurtReduce_N', 'AllEleHurtReduce_N', 'IgnoreKnowledgeAnti_P', 'TenebrousAtk_P', 'AllRaceHurtReduce_N', 'CritFateAtkMin_N', 'CritCalamityLevel_N', 'ChaosAnti_N', 'CritChaosLevel_N', 'Def_N', 'VisionaryHurtReduce_N', 'Atk_P', 'CritTenebrousLevel_N', 'IgnoreAbundanceAnti_N', 'EnhanceAirborne_P', 'pAtkMax_F', 'IgnoreAllEle_P', 'AllControlAnti_N', 'MaxStaminaValue_N', 'Def_P', 'Dex_P', 'CritDarknessSteal_N', 'RaceHurtMulti1_P', 'WarriorHurtReduce_N', 'AllEleAnti_N', 'pAtkMin_P', 'DisorderAtk_N', 'pAtkMin_F', 'mCrit_N', 'DisorderHurtMulti_N', 'pCrit_N', 'pBlock_P', 'CritFateLevel_N', 'mBlock_P', 'ProHurtMulti9_P', 'pPierce_P', 'DisorderAnti_P', 'ShieldBreak_N', 'KnowledgeAtk_P', 'ApprenticeHurtMulti_N', 'KnowledgeLevel_N', 'mPierce_P', 'TiedHitRate_N', 'RaceHurtMulti3_P', 'VisionaryHurtMulti_N', 'IgnoreAbundanceAnti_P', 'TenebrousAnti_N', 'DarknessAnti_N', 'mAtkMax_N', 'AllEleAtk_P', 'AllProHurtReduce_P', 'CritDarknessSteal_P', 'pAtkMax_N', 'KnowledgeAtk_N', 'IgnoreCalamityAnti_P', 'EnhanceDown_P', 'IgnoreFateAnti_P', 'TenebrousHurtMulti_N', 'AirborneAnti_P', 'RaceHurtReduce2_N', 'Atk_N', 'WarriorHurtMulti_N', 'DeltaHeal_N', 'RaceHurtReduce3_P', 'Str_P', 'IgnoreDisorderAnti_P', 'AllRaceHurtMulti_N', 'FateAnti_P', 'PullAnti_P', 'FeatherwitHurtReduce_N', 'MysteryAnti_N', 'MysteryLevel_N', 'AbundanceHurtMulti_N', 'MysteryAtk_P', 'EnhanceDown_N', 'AllControlAnti_P', 'EnhanceTied_N', 'RaceHurtMulti5_N', 'RaceHurtReduce1_P', 'SleepHitRate_N', 'CalamityAtk_P', 'SlowHitRate_N', 'mHurtMulti_P', 'RaceHurtMulti2_P', 'pHurtMulti_P', 'AtkRange_P', 'mCritHurt_N', 'pCritHurt_N', 'pHurtMulti_N', 'CritFateAtkMin_P', 'EnhanceDizzy_P', 'mHurtMulti_N', 'AbundanceAnti_N', 'KnowledgeAnti_P', 'TenebrousIgnoreDef_N', 'CalamityHurtMulti_N', 'mPierce_N', 'DarknessRate_N', 'PullHitRate_N', 'mCritAnti_P', 'pPierce_N', 'AllRaceHurtMulti_P', 'AirborneHitRate_N', 'DarknessHurtReduce_N', 'AbundanceAnti_P', 'pCritAnti_P', 'RaceHurtMulti2_N', 'MaxHp_F', 'EnhanceAllControl_N', 'RaceHurtMulti5_P', 'Str_N', 'CalamityHurtReduce_N', 'TiedAnti_N', 'EnhanceFear_N', 'SilenceAnti_P', 'FateLevel_N', 'AtkRange_N', 'Int_N', 'DeltaBeHealed_N', 'mHurtReduce_P', 'AbundanceAtk_N', 'MysteryHurtReduce_N', 'pHurtReduce_P', 'AdditionalAnti_P', 'AllControlHitRate_N', 'SlowAnti_N', 'AdditionalAtk_N', 'ArbiterHurtMulti_P', 'IgnoreMysteryAnti_N', 'ChaosAtk_P', 'pCritDef_N', 'mCritDef_N', 'AllEleAtk_N', 'IgnoreMysteryAnti_P', 'RaceHurtReduce4_N', 'AbundanceAtk_P', 'CalamityRate_N', 'RaceHurtMulti1_N', 'DisorderLevel_N', 'EnhanceAirborne_N', 'mBlock_N', 'TenebrousHurtReduce_N', 'CalamityAnti_N', 'pBlock_N', 'ProHurtReduce9_P', 'Speed_N', 'KnowledgeAnti_N', 'AllProHurtReduce_N', 'Block_N', 'IgnoreDarknessAnti_P', 'CritFateAtkMax_P', 'SleepAnti_P', 'DisorderRate_N', 'RaceHurtReduce1_N', 'ChaosHurtMulti_N', 'CalamityAtk_N', 'TenebrousLevel_N', 'MaxStaminaValue_F', 'IgnoreChaosAnti_N', 'AllEleAnti_P', 'IgnoreTenebrousAnti_P', 'EnhancePull_N', 'DarknessSteal_N', 'ChaosHurtReduce_N', 'SilenceHitRate_N', 'mAtkMax_F', 'AirborneAnti_N', 'IgnoreKnowledgeAnti_N', 'ArbiterHurtReduce_N', 'ArbiterHurtMulti_N', 'mAtkMax_P', 'ChaosAnti_P', 'AllProHurtMulti_P', 'AllRaceHurtReduce_P', 'AirShield_N', 'EnhancePull_P', 'ChaosAtk_N', 'mHurtReduce_N', 'IgnoreAllEle_N', 'DizzyAnti_N', 'EnhanceTied_P', 'FearAnti_N', 'mAtkMin_F', 'KnowledgeRate_N', 'TiedAnti_P', 'mAtkMin_P', 'SunHurtMulti_N', 'FateRate_N', 'AbundanceLevel_N', 'FateAtk_P', 'mDef_P', 'SilenceAnti_N', 'CritAnti_P', 'pDef_P', 'MysteryRate_N', 'DisorderAnti_N', 'Pierce_N', 'EnhanceSilence_P', 'pHurtReduce_N', 'LockedMaxHp_N', 'EnhanceSlow_P', 'FateAnti_N', 'TenebrousAtk_N', 'SkillAnti_P', 'FeatherwitHurtMulti_N', 'CritAbundanceLevel_N', 'CritDarknessLevel_N', 'TenebrousAnti_P', 'SlowAnti_P', 'Pierce_P', 'DisorderHurtReduce_N', 'AllProHurtMulti_N', 'pIgnoreDef_N', 'mAtkSpd_N', 'DeltaBeHealed_P', 'IgnoreDisorderAnti_N', 'mDef_N', 'AdditionalIgnore_P', 'pAtkSpd_N', 'PullAnti_N', 'EnhanceFear_P', 'FateAtk_N', 'DizzyAnti_P', 'CritTenebrousIgnoreDef_N', 'CritKnowledgeLevel_N', 'LockedMaxHp_P', 'CritDef_P', 'CritDef_N', 'mCritHurt_P', 'RaceHurtReduce2_P', 'Pow_N', 'pCritHurt_P', 'ChaosRate_N', 'EnhanceSlow_N', 'EnhanceAllControl_P', 'DarknessLevel_N', 'CalamityAnti_P', 'CritMysteryLevel_N', 'RaceHurtReduce4_P', 'IgnoreChaosAnti_P', 'DarknessSteal_P', 'RaceHurtReduce5_N', 'DarknessAnti_P', 'FearAnti_P', 'RaceHurtReduce5_P', 'DarknessAtk_N', 'AbundanceRate_N', 'AdditionalAnti_N', 'mIgnoreDef_P', 'pIgnoreDef_P', 'AbundanceHurtReduce_N', 'mAtkMin_N', 'DownHitRate_N', 'SunHurtReduce_N', 'WarriorHurtReduce_P', 'VisionaryHurtReduce_P', 'WarriorHurtMulti_P', 'DeltaBeHurted_N', 'DarknessAtk_P', 'DisorderAtk_P', 'ProHurtReduce9_N', 'KnowledgeHurtReduce_N', 'ChaosLevel_N', 'FeatherwitHurtReduce_P', 'RaceHurtMulti3_N', 'AdditionalAtk_P', 'pAtkMin_N', 'DeltaHeal_P', 'EnhanceDizzy_N', 'DizzyHitRate_N', 'pCrit_P', 'MaxHp_N', 'ProHurtMulti9_N', 'ApprenticeHurtReduce_N', 'ApprenticeHurtReduce_P', 'Block_P', 'CritTenebrousIgnoreDef_P', 'RaceHurtReduce3_N', 'AdditionalIgnore_N', 'SunHurtReduce_P', 'RaceHurtMulti4_N', 'mAtkSpd_P', 'Pow_P', 'CritDisorderLevel_N', 'AllEleHurtMulti_N', 'IgnoreFateAnti_N', 'pDef_F', 'ApprenticeHurtMulti_P', 'mCritDef_P', 'TenebrousRate_N', 'SkillAnti_N', 'Speed_P', 'VisionaryHurtMulti_P', 'DownAnti_N', 'CalamityLevel_N', 'KnowledgeHurtMulti_N', 'FateHurtMulti_N', 'DownAnti_P', 'RaceHurtMulti4_P', 'MysteryAnti_P', 'AggroPercent_N', 'CritFateAtkMax_N', 'MysteryAtk_N', 'UltimatePointSpeed_P', 'UltimatePointSpeed_N', 'MaxHp_P', 'IgnoreCalamityAnti_N', 'pCritDef_P', 'pCritAnti_N', 'mCritAnti_N', 'SunHurtMulti_P', 'FearHitRate_N', 'SleepAnti_N', 'MaxStaminaValue_P', 'Con_N', 'EnhanceSilence_N', 'MysteryHurtMulti_N', 'Int_P', 'pAtkMax_P', 'mIgnoreDef_N', 'Dex_N', 'HpReg_P', 'mCrit_P', 'TenebrousIgnoreDef_P', 'mDef_F', 'DarknessHurtMulti_N', 'pDef_N', 'pAtkSpd_P', 'EnhanceSleep_N', 'HpReg_N', 'IgnoreDarknessAnti_N'
    },
    data = {
        [1010001] = {
            ['ID'] = 1010001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Con', 
            ['PropMode'] = 'Con_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134860800'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329554432'),
            ['ShowType'] = 0, 
        },
        [1010002] = {
            ['ID'] = 1010002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Con', 
            ['PropMode'] = 'Con_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134861056'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329554432'),
            ['ShowType'] = 1, 
        },
        [1010011] = {
            ['ID'] = 1010011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Pow', 
            ['PropMode'] = 'Pow_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134861312'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329554944'),
            ['ShowType'] = 0, 
        },
        [1010012] = {
            ['ID'] = 1010012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Pow', 
            ['PropMode'] = 'Pow_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134861568'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329554944'),
            ['ShowType'] = 1, 
        },
        [1010021] = {
            ['ID'] = 1010021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Str', 
            ['PropMode'] = 'Str_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134861824'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_46043391592192'),
            ['ShowType'] = 0, 
        },
        [1010022] = {
            ['ID'] = 1010022, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Str', 
            ['PropMode'] = 'Str_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134862080'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_46043391592192'),
            ['ShowType'] = 1, 
        },
        [1010031] = {
            ['ID'] = 1010031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Int', 
            ['PropMode'] = 'Int_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134862336'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329555968'),
            ['ShowType'] = 0, 
        },
        [1010032] = {
            ['ID'] = 1010032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Int', 
            ['PropMode'] = 'Int_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134862592'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329555968'),
            ['ShowType'] = 1, 
        },
        [1010041] = {
            ['ID'] = 1010041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Dex', 
            ['PropMode'] = 'Dex_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134862848'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329556480'),
            ['ShowType'] = 0, 
        },
        [1010042] = {
            ['ID'] = 1010042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Dex', 
            ['PropMode'] = 'Dex_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134863104'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329556480'),
            ['ShowType'] = 1, 
        },
        [1011001] = {
            ['ID'] = 1011001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MaxHp', 
            ['PropMode'] = 'MaxHp_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134863360'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329556992'),
            ['ShowType'] = 0, 
        },
        [1011002] = {
            ['ID'] = 1011002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'MaxHp', 
            ['PropMode'] = 'MaxHp_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134863616'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329556992'),
            ['ShowType'] = 1, 
        },
        [1011003] = {
            ['ID'] = 1011003, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'MaxHp', 
            ['PropMode'] = 'MaxHp_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134863872'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329556992'),
            ['ShowType'] = 0, 
        },
        [1011011] = {
            ['ID'] = 1011011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'HpReg', 
            ['PropMode'] = 'HpReg_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134864128'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329557760'),
            ['ShowType'] = 2, 
        },
        [1011012] = {
            ['ID'] = 1011012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'HpReg', 
            ['PropMode'] = 'HpReg_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134864384'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329557760'),
            ['ShowType'] = 1, 
        },
        [1011041] = {
            ['ID'] = 1011041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'LockedMaxHp', 
            ['PropMode'] = 'LockedMaxHp_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134864640'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329558272'),
            ['ShowType'] = 0, 
        },
        [1011042] = {
            ['ID'] = 1011042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'LockedMaxHp', 
            ['PropMode'] = 'LockedMaxHp_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134864896'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329558272'),
            ['ShowType'] = 1, 
        },
        [1011061] = {
            ['ID'] = 1011061, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MaxStaminaValue', 
            ['PropMode'] = 'MaxStaminaValue_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134865152'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329558784'),
            ['ShowType'] = 0, 
        },
        [1011062] = {
            ['ID'] = 1011062, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'MaxStaminaValue', 
            ['PropMode'] = 'MaxStaminaValue_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134865408'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329558784'),
            ['ShowType'] = 0, 
        },
        [1011063] = {
            ['ID'] = 1011063, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'MaxStaminaValue', 
            ['PropMode'] = 'MaxStaminaValue_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134865664'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329558784'),
            ['ShowType'] = 0, 
        },
        [1011071] = {
            ['ID'] = 1011071, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AtkRange', 
            ['PropMode'] = 'AtkRange_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134865920'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329559552'),
            ['ShowType'] = 2, 
        },
        [1011072] = {
            ['ID'] = 1011072, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AtkRange', 
            ['PropMode'] = 'AtkRange_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134866176'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329559552'),
            ['ShowType'] = 1, 
        },
        [1011081] = {
            ['ID'] = 1011081, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DeltaHeal', 
            ['PropMode'] = 'DeltaHeal_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134866432'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329560064'),
            ['ShowType'] = 0, 
        },
        [1011082] = {
            ['ID'] = 1011082, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DeltaHeal', 
            ['PropMode'] = 'DeltaHeal_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134866688'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329560064'),
            ['ShowType'] = 1, 
        },
        [1011091] = {
            ['ID'] = 1011091, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DeltaBeHealed', 
            ['PropMode'] = 'DeltaBeHealed_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134866944'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329560576'),
            ['ShowType'] = 0, 
        },
        [1011092] = {
            ['ID'] = 1011092, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DeltaBeHealed', 
            ['PropMode'] = 'DeltaBeHealed_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134867200'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329560576'),
            ['ShowType'] = 1, 
        },
        [1011101] = {
            ['ID'] = 1011101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Speed', 
            ['PropMode'] = 'Speed_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134867456'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329561088'),
            ['ShowType'] = 2, 
        },
        [1011102] = {
            ['ID'] = 1011102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Speed', 
            ['PropMode'] = 'Speed_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134867712'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329561088'),
            ['ShowType'] = 1, 
        },
        [1011231] = {
            ['ID'] = 1011231, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AggroPercent', 
            ['PropMode'] = 'AggroPercent_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134867968'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329561600'),
            ['ShowType'] = 0, 
        },
        [1011241] = {
            ['ID'] = 1011241, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DeltaHurt', 
            ['PropMode'] = 'DeltaHurt_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134868224'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329561856'),
            ['ShowType'] = 0, 
        },
        [1011251] = {
            ['ID'] = 1011251, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DeltaBeHurted', 
            ['PropMode'] = 'DeltaBeHurted_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134868480'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329562112'),
            ['ShowType'] = 0, 
        },
        [1011321] = {
            ['ID'] = 1011321, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessSteal', 
            ['PropMode'] = 'DarknessSteal_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134868736'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329562368'),
            ['ShowType'] = 0, 
        },
        [1011322] = {
            ['ID'] = 1011322, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DarknessSteal', 
            ['PropMode'] = 'DarknessSteal_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134868992'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329562368'),
            ['ShowType'] = 1, 
        },
        [1011331] = {
            ['ID'] = 1011331, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritDarknessSteal', 
            ['PropMode'] = 'CritDarknessSteal_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134869248'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329562880'),
            ['ShowType'] = 0, 
        },
        [1011332] = {
            ['ID'] = 1011332, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritDarknessSteal', 
            ['PropMode'] = 'CritDarknessSteal_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134869504'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329562880'),
            ['ShowType'] = 1, 
        },
        [1011341] = {
            ['ID'] = 1011341, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousIgnoreDef', 
            ['PropMode'] = 'TenebrousIgnoreDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134869760'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329563392'),
            ['ShowType'] = 0, 
        },
        [1011342] = {
            ['ID'] = 1011342, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'TenebrousIgnoreDef', 
            ['PropMode'] = 'TenebrousIgnoreDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134870016'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329563392'),
            ['ShowType'] = 1, 
        },
        [1011351] = {
            ['ID'] = 1011351, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritTenebrousIgnoreDef', 
            ['PropMode'] = 'CritTenebrousIgnoreDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134870272'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329563904'),
            ['ShowType'] = 0, 
        },
        [1011352] = {
            ['ID'] = 1011352, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritTenebrousIgnoreDef', 
            ['PropMode'] = 'CritTenebrousIgnoreDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134870528'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329563904'),
            ['ShowType'] = 1, 
        },
        [1011361] = {
            ['ID'] = 1011361, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritFateAtkMin', 
            ['PropMode'] = 'CritFateAtkMin_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134870784'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329564416'),
            ['ShowType'] = 0, 
        },
        [1011362] = {
            ['ID'] = 1011362, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritFateAtkMin', 
            ['PropMode'] = 'CritFateAtkMin_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134871040'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329564416'),
            ['ShowType'] = 1, 
        },
        [1011371] = {
            ['ID'] = 1011371, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritFateAtkMax', 
            ['PropMode'] = 'CritFateAtkMax_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134871296'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329564928'),
            ['ShowType'] = 0, 
        },
        [1011372] = {
            ['ID'] = 1011372, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritFateAtkMax', 
            ['PropMode'] = 'CritFateAtkMax_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134871552'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329564928'),
            ['ShowType'] = 1, 
        },
        [1011381] = {
            ['ID'] = 1011381, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'UltimatePointSpeed', 
            ['PropMode'] = 'UltimatePointSpeed_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134871808'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329565440'),
            ['ShowType'] = 2, 
        },
        [1011382] = {
            ['ID'] = 1011382, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'UltimatePointSpeed', 
            ['PropMode'] = 'UltimatePointSpeed_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134872064'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329565440'),
            ['ShowType'] = 1, 
        },
        [1012001] = {
            ['ID'] = 1012001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pAtkMin', 
            ['PropMode'] = 'pAtkMin_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134872320'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802816'),
            ['ShowType'] = 0, 
        },
        [1012002] = {
            ['ID'] = 1012002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pAtkMin', 
            ['PropMode'] = 'pAtkMin_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134872576'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802816'),
            ['ShowType'] = 1, 
        },
        [1012003] = {
            ['ID'] = 1012003, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'pAtkMin', 
            ['PropMode'] = 'pAtkMin_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134872832'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802816'),
            ['ShowType'] = 0, 
        },
        [1012011] = {
            ['ID'] = 1012011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pAtkMax', 
            ['PropMode'] = 'pAtkMax_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134873088'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368801792'),
            ['ShowType'] = 0, 
        },
        [1012012] = {
            ['ID'] = 1012012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pAtkMax', 
            ['PropMode'] = 'pAtkMax_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134873344'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368801792'),
            ['ShowType'] = 1, 
        },
        [1012013] = {
            ['ID'] = 1012013, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'pAtkMax', 
            ['PropMode'] = 'pAtkMax_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134873600'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368801792'),
            ['ShowType'] = 0, 
        },
        [1012031] = {
            ['ID'] = 1012031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pAtkSpd', 
            ['PropMode'] = 'pAtkSpd_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134873856'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329567488'),
            ['ShowType'] = 2, 
        },
        [1012032] = {
            ['ID'] = 1012032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pAtkSpd', 
            ['PropMode'] = 'pAtkSpd_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134874112'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329567488'),
            ['ShowType'] = 1, 
        },
        [1012041] = {
            ['ID'] = 1012041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pDef', 
            ['PropMode'] = 'pDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134874368'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789314816'),
            ['ShowType'] = 0, 
        },
        [1012042] = {
            ['ID'] = 1012042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pDef', 
            ['PropMode'] = 'pDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134874624'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789314816'),
            ['ShowType'] = 1, 
        },
        [1012043] = {
            ['ID'] = 1012043, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'pDef', 
            ['PropMode'] = 'pDef_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134874880'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789314816'),
            ['ShowType'] = 0, 
        },
        [1012051] = {
            ['ID'] = 1012051, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pIgnoreDef', 
            ['PropMode'] = 'pIgnoreDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134875136'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210380288'),
            ['ShowType'] = 0, 
        },
        [1012052] = {
            ['ID'] = 1012052, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pIgnoreDef', 
            ['PropMode'] = 'pIgnoreDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134875392'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210380288'),
            ['ShowType'] = 1, 
        },
        [1012081] = {
            ['ID'] = 1012081, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pCrit', 
            ['PropMode'] = 'pCrit_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134875648'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992498178'),
            ['ShowType'] = 0, 
        },
        [1012082] = {
            ['ID'] = 1012082, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pCrit', 
            ['PropMode'] = 'pCrit_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134875904'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992498178'),
            ['ShowType'] = 1, 
        },
        [1012091] = {
            ['ID'] = 1012091, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pCritAnti', 
            ['PropMode'] = 'pCritAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134876160'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368807936'),
            ['ShowType'] = 0, 
        },
        [1012092] = {
            ['ID'] = 1012092, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pCritAnti', 
            ['PropMode'] = 'pCritAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134876416'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368807936'),
            ['ShowType'] = 1, 
        },
        [1012101] = {
            ['ID'] = 1012101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pCritHurt', 
            ['PropMode'] = 'pCritHurt_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134876672'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377216'),
            ['ShowType'] = 1, 
        },
        [1012102] = {
            ['ID'] = 1012102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pCritHurt', 
            ['PropMode'] = 'pCritHurt_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134876928'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377216'),
            ['ShowType'] = 1, 
        },
        [1012111] = {
            ['ID'] = 1012111, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pCritDef', 
            ['PropMode'] = 'pCritDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134877184'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368808960'),
            ['ShowType'] = 1, 
        },
        [1012112] = {
            ['ID'] = 1012112, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pCritDef', 
            ['PropMode'] = 'pCritDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134877440'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368808960'),
            ['ShowType'] = 1, 
        },
        [1012121] = {
            ['ID'] = 1012121, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pHurtMulti', 
            ['PropMode'] = 'pHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134877696'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329571328'),
            ['ShowType'] = 1, 
        },
        [1012122] = {
            ['ID'] = 1012122, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pHurtMulti', 
            ['PropMode'] = 'pHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134877952'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329571328'),
            ['ShowType'] = 1, 
        },
        [1012131] = {
            ['ID'] = 1012131, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pHurtReduce', 
            ['PropMode'] = 'pHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134878208'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329571840'),
            ['ShowType'] = 1, 
        },
        [1012132] = {
            ['ID'] = 1012132, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pHurtReduce', 
            ['PropMode'] = 'pHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134878464'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329571840'),
            ['ShowType'] = 1, 
        },
        [1012141] = {
            ['ID'] = 1012141, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pPierce', 
            ['PropMode'] = 'pPierce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134878720'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992493570'),
            ['ShowType'] = 0, 
        },
        [1012142] = {
            ['ID'] = 1012142, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pPierce', 
            ['PropMode'] = 'pPierce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134878976'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992493570'),
            ['ShowType'] = 1, 
        },
        [1012151] = {
            ['ID'] = 1012151, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'pBlock', 
            ['PropMode'] = 'pBlock_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134879232'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329572864'),
            ['ShowType'] = 0, 
        },
        [1012152] = {
            ['ID'] = 1012152, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'pBlock', 
            ['PropMode'] = 'pBlock_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134879488'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329572864'),
            ['ShowType'] = 1, 
        },
        [1013001] = {
            ['ID'] = 1013001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mAtkMin', 
            ['PropMode'] = 'mAtkMin_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134879744'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368803072'),
            ['ShowType'] = 0, 
        },
        [1013002] = {
            ['ID'] = 1013002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mAtkMin', 
            ['PropMode'] = 'mAtkMin_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134880000'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368803072'),
            ['ShowType'] = 1, 
        },
        [1013003] = {
            ['ID'] = 1013003, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'mAtkMin', 
            ['PropMode'] = 'mAtkMin_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134880256'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368803072'),
            ['ShowType'] = 0, 
        },
        [1013011] = {
            ['ID'] = 1013011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mAtkMax', 
            ['PropMode'] = 'mAtkMax_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134880512'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802048'),
            ['ShowType'] = 0, 
        },
        [1013012] = {
            ['ID'] = 1013012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mAtkMax', 
            ['PropMode'] = 'mAtkMax_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134880768'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802048'),
            ['ShowType'] = 1, 
        },
        [1013013] = {
            ['ID'] = 1013013, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'mAtkMax', 
            ['PropMode'] = 'mAtkMax_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134881024'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368802048'),
            ['ShowType'] = 0, 
        },
        [1013031] = {
            ['ID'] = 1013031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mAtkSpd', 
            ['PropMode'] = 'mAtkSpd_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134881280'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329574912'),
            ['ShowType'] = 2, 
        },
        [1013032] = {
            ['ID'] = 1013032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mAtkSpd', 
            ['PropMode'] = 'mAtkSpd_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134881536'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329574912'),
            ['ShowType'] = 1, 
        },
        [1013041] = {
            ['ID'] = 1013041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mDef', 
            ['PropMode'] = 'mDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134881792'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789315072'),
            ['ShowType'] = 0, 
        },
        [1013042] = {
            ['ID'] = 1013042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mDef', 
            ['PropMode'] = 'mDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134882048'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789315072'),
            ['ShowType'] = 1, 
        },
        [1013043] = {
            ['ID'] = 1013043, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Fixed', 
            ['Prop'] = 'mDef', 
            ['PropMode'] = 'mDef_F', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134882304'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789315072'),
            ['ShowType'] = 0, 
        },
        [1013051] = {
            ['ID'] = 1013051, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mIgnoreDef', 
            ['PropMode'] = 'mIgnoreDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134882560'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210380544'),
            ['ShowType'] = 0, 
        },
        [1013052] = {
            ['ID'] = 1013052, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mIgnoreDef', 
            ['PropMode'] = 'mIgnoreDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134882816'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210380544'),
            ['ShowType'] = 1, 
        },
        [1013081] = {
            ['ID'] = 1013081, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mCrit', 
            ['PropMode'] = 'mCrit_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134883072'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992498434'),
            ['ShowType'] = 0, 
        },
        [1013082] = {
            ['ID'] = 1013082, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mCrit', 
            ['PropMode'] = 'mCrit_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134883328'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992498434'),
            ['ShowType'] = 1, 
        },
        [1013091] = {
            ['ID'] = 1013091, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mCritAnti', 
            ['PropMode'] = 'mCritAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134883584'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368808192'),
            ['ShowType'] = 0, 
        },
        [1013092] = {
            ['ID'] = 1013092, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mCritAnti', 
            ['PropMode'] = 'mCritAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134883840'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368808192'),
            ['ShowType'] = 1, 
        },
        [1013101] = {
            ['ID'] = 1013101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mCritHurt', 
            ['PropMode'] = 'mCritHurt_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134884096'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377472'),
            ['ShowType'] = 1, 
        },
        [1013102] = {
            ['ID'] = 1013102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mCritHurt', 
            ['PropMode'] = 'mCritHurt_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134884352'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377472'),
            ['ShowType'] = 1, 
        },
        [1013111] = {
            ['ID'] = 1013111, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mCritDef', 
            ['PropMode'] = 'mCritDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134884608'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368809216'),
            ['ShowType'] = 1, 
        },
        [1013112] = {
            ['ID'] = 1013112, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mCritDef', 
            ['PropMode'] = 'mCritDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134884864'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368809216'),
            ['ShowType'] = 1, 
        },
        [1013121] = {
            ['ID'] = 1013121, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mHurtMulti', 
            ['PropMode'] = 'mHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134885120'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329578752'),
            ['ShowType'] = 1, 
        },
        [1013122] = {
            ['ID'] = 1013122, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mHurtMulti', 
            ['PropMode'] = 'mHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134885376'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329578752'),
            ['ShowType'] = 1, 
        },
        [1013131] = {
            ['ID'] = 1013131, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mHurtReduce', 
            ['PropMode'] = 'mHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134885632'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329579264'),
            ['ShowType'] = 1, 
        },
        [1013132] = {
            ['ID'] = 1013132, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mHurtReduce', 
            ['PropMode'] = 'mHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134885888'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329579264'),
            ['ShowType'] = 1, 
        },
        [1013141] = {
            ['ID'] = 1013141, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mPierce', 
            ['PropMode'] = 'mPierce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134886144'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992494082'),
            ['ShowType'] = 0, 
        },
        [1013142] = {
            ['ID'] = 1013142, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mPierce', 
            ['PropMode'] = 'mPierce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134886400'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_17319992494082'),
            ['ShowType'] = 1, 
        },
        [1013151] = {
            ['ID'] = 1013151, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'mBlock', 
            ['PropMode'] = 'mBlock_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134886656'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329580288'),
            ['ShowType'] = 0, 
        },
        [1013152] = {
            ['ID'] = 1013152, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'mBlock', 
            ['PropMode'] = 'mBlock_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134886912'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329580288'),
            ['ShowType'] = 1, 
        },
        [1014001] = {
            ['ID'] = 1014001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosAtk', 
            ['PropMode'] = 'ChaosAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134887168'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210370816'),
            ['ShowType'] = 0, 
        },
        [1014002] = {
            ['ID'] = 1014002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ChaosAtk', 
            ['PropMode'] = 'ChaosAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134887424'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210370816'),
            ['ShowType'] = 1, 
        },
        [1014011] = {
            ['ID'] = 1014011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosHurtMulti', 
            ['PropMode'] = 'ChaosHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134887680'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329581312'),
            ['ShowType'] = 1, 
        },
        [1014021] = {
            ['ID'] = 1014021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosHurtReduce', 
            ['PropMode'] = 'ChaosHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134887936'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329581568'),
            ['ShowType'] = 1, 
        },
        [1014031] = {
            ['ID'] = 1014031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosAnti', 
            ['PropMode'] = 'ChaosAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134888192'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329581824'),
            ['ShowType'] = 0, 
        },
        [1014032] = {
            ['ID'] = 1014032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ChaosAnti', 
            ['PropMode'] = 'ChaosAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134888448'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329581824'),
            ['ShowType'] = 1, 
        },
        [1014041] = {
            ['ID'] = 1014041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreChaosAnti', 
            ['PropMode'] = 'IgnoreChaosAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134888704'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329582336'),
            ['ShowType'] = 0, 
        },
        [1014042] = {
            ['ID'] = 1014042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreChaosAnti', 
            ['PropMode'] = 'IgnoreChaosAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134888960'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329582336'),
            ['ShowType'] = 1, 
        },
        [1014051] = {
            ['ID'] = 1014051, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosRate', 
            ['PropMode'] = 'ChaosRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134889216'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329582848'),
            ['ShowType'] = 1, 
        },
        [1014061] = {
            ['ID'] = 1014061, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ChaosLevel', 
            ['PropMode'] = 'ChaosLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134889472'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329583104'),
            ['ShowType'] = 0, 
        },
        [1014071] = {
            ['ID'] = 1014071, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritChaosLevel', 
            ['PropMode'] = 'CritChaosLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134889728'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329583360'),
            ['ShowType'] = 0, 
        },
        [1014101] = {
            ['ID'] = 1014101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryAtk', 
            ['PropMode'] = 'MysteryAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134889984'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371072'),
            ['ShowType'] = 0, 
        },
        [1014102] = {
            ['ID'] = 1014102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'MysteryAtk', 
            ['PropMode'] = 'MysteryAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134890240'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371072'),
            ['ShowType'] = 1, 
        },
        [1014111] = {
            ['ID'] = 1014111, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryHurtMulti', 
            ['PropMode'] = 'MysteryHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134890496'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329584128'),
            ['ShowType'] = 1, 
        },
        [1014121] = {
            ['ID'] = 1014121, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryHurtReduce', 
            ['PropMode'] = 'MysteryHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134890752'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329584384'),
            ['ShowType'] = 1, 
        },
        [1014131] = {
            ['ID'] = 1014131, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryAnti', 
            ['PropMode'] = 'MysteryAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134891008'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329584640'),
            ['ShowType'] = 0, 
        },
        [1014132] = {
            ['ID'] = 1014132, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'MysteryAnti', 
            ['PropMode'] = 'MysteryAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134891264'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329584640'),
            ['ShowType'] = 1, 
        },
        [1014141] = {
            ['ID'] = 1014141, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreMysteryAnti', 
            ['PropMode'] = 'IgnoreMysteryAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134891520'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329585152'),
            ['ShowType'] = 0, 
        },
        [1014142] = {
            ['ID'] = 1014142, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreMysteryAnti', 
            ['PropMode'] = 'IgnoreMysteryAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134891776'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329585152'),
            ['ShowType'] = 1, 
        },
        [1014151] = {
            ['ID'] = 1014151, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryRate', 
            ['PropMode'] = 'MysteryRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134892032'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329585664'),
            ['ShowType'] = 1, 
        },
        [1014161] = {
            ['ID'] = 1014161, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'MysteryLevel', 
            ['PropMode'] = 'MysteryLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134892288'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329585920'),
            ['ShowType'] = 0, 
        },
        [1014171] = {
            ['ID'] = 1014171, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritMysteryLevel', 
            ['PropMode'] = 'CritMysteryLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134892544'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329586176'),
            ['ShowType'] = 0, 
        },
        [1014201] = {
            ['ID'] = 1014201, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceAtk', 
            ['PropMode'] = 'AbundanceAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134892800'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371328'),
            ['ShowType'] = 0, 
        },
        [1014202] = {
            ['ID'] = 1014202, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AbundanceAtk', 
            ['PropMode'] = 'AbundanceAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134893056'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371328'),
            ['ShowType'] = 1, 
        },
        [1014211] = {
            ['ID'] = 1014211, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceHurtMulti', 
            ['PropMode'] = 'AbundanceHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134893312'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329586944'),
            ['ShowType'] = 1, 
        },
        [1014221] = {
            ['ID'] = 1014221, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceHurtReduce', 
            ['PropMode'] = 'AbundanceHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134893568'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329587200'),
            ['ShowType'] = 1, 
        },
        [1014231] = {
            ['ID'] = 1014231, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceAnti', 
            ['PropMode'] = 'AbundanceAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134893824'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329587456'),
            ['ShowType'] = 0, 
        },
        [1014232] = {
            ['ID'] = 1014232, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AbundanceAnti', 
            ['PropMode'] = 'AbundanceAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134894080'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329587456'),
            ['ShowType'] = 1, 
        },
        [1014241] = {
            ['ID'] = 1014241, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreAbundanceAnti', 
            ['PropMode'] = 'IgnoreAbundanceAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134894336'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329587968'),
            ['ShowType'] = 0, 
        },
        [1014242] = {
            ['ID'] = 1014242, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreAbundanceAnti', 
            ['PropMode'] = 'IgnoreAbundanceAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134894592'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329587968'),
            ['ShowType'] = 1, 
        },
        [1014251] = {
            ['ID'] = 1014251, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceRate', 
            ['PropMode'] = 'AbundanceRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134894848'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329588480'),
            ['ShowType'] = 1, 
        },
        [1014261] = {
            ['ID'] = 1014261, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AbundanceLevel', 
            ['PropMode'] = 'AbundanceLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134895104'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329588736'),
            ['ShowType'] = 0, 
        },
        [1014271] = {
            ['ID'] = 1014271, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritAbundanceLevel', 
            ['PropMode'] = 'CritAbundanceLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134895360'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329588992'),
            ['ShowType'] = 0, 
        },
        [1014301] = {
            ['ID'] = 1014301, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessAtk', 
            ['PropMode'] = 'DarknessAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134895616'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371584'),
            ['ShowType'] = 0, 
        },
        [1014302] = {
            ['ID'] = 1014302, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DarknessAtk', 
            ['PropMode'] = 'DarknessAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134895872'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371584'),
            ['ShowType'] = 1, 
        },
        [1014311] = {
            ['ID'] = 1014311, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessHurtMulti', 
            ['PropMode'] = 'DarknessHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134896128'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329589760'),
            ['ShowType'] = 1, 
        },
        [1014321] = {
            ['ID'] = 1014321, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessHurtReduce', 
            ['PropMode'] = 'DarknessHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134896384'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329590016'),
            ['ShowType'] = 1, 
        },
        [1014331] = {
            ['ID'] = 1014331, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessAnti', 
            ['PropMode'] = 'DarknessAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134896640'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329590272'),
            ['ShowType'] = 0, 
        },
        [1014332] = {
            ['ID'] = 1014332, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DarknessAnti', 
            ['PropMode'] = 'DarknessAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134896896'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329590272'),
            ['ShowType'] = 1, 
        },
        [1014341] = {
            ['ID'] = 1014341, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreDarknessAnti', 
            ['PropMode'] = 'IgnoreDarknessAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134897152'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329590784'),
            ['ShowType'] = 0, 
        },
        [1014342] = {
            ['ID'] = 1014342, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreDarknessAnti', 
            ['PropMode'] = 'IgnoreDarknessAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134897408'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329590784'),
            ['ShowType'] = 1, 
        },
        [1014351] = {
            ['ID'] = 1014351, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessRate', 
            ['PropMode'] = 'DarknessRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134897664'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329591296'),
            ['ShowType'] = 1, 
        },
        [1014361] = {
            ['ID'] = 1014361, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DarknessLevel', 
            ['PropMode'] = 'DarknessLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134897920'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329591552'),
            ['ShowType'] = 0, 
        },
        [1014371] = {
            ['ID'] = 1014371, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritDarknessLevel', 
            ['PropMode'] = 'CritDarknessLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134898176'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329591808'),
            ['ShowType'] = 0, 
        },
        [1014401] = {
            ['ID'] = 1014401, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityAtk', 
            ['PropMode'] = 'CalamityAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134898432'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371840'),
            ['ShowType'] = 0, 
        },
        [1014402] = {
            ['ID'] = 1014402, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CalamityAtk', 
            ['PropMode'] = 'CalamityAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134898688'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210371840'),
            ['ShowType'] = 1, 
        },
        [1014411] = {
            ['ID'] = 1014411, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityHurtMulti', 
            ['PropMode'] = 'CalamityHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134898944'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329592576'),
            ['ShowType'] = 1, 
        },
        [1014421] = {
            ['ID'] = 1014421, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityHurtReduce', 
            ['PropMode'] = 'CalamityHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134899200'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329592832'),
            ['ShowType'] = 1, 
        },
        [1014431] = {
            ['ID'] = 1014431, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityAnti', 
            ['PropMode'] = 'CalamityAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134899456'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329593088'),
            ['ShowType'] = 0, 
        },
        [1014432] = {
            ['ID'] = 1014432, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CalamityAnti', 
            ['PropMode'] = 'CalamityAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134899712'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329593088'),
            ['ShowType'] = 1, 
        },
        [1014441] = {
            ['ID'] = 1014441, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreCalamityAnti', 
            ['PropMode'] = 'IgnoreCalamityAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134899968'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329593600'),
            ['ShowType'] = 0, 
        },
        [1014442] = {
            ['ID'] = 1014442, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreCalamityAnti', 
            ['PropMode'] = 'IgnoreCalamityAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134900224'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329593600'),
            ['ShowType'] = 1, 
        },
        [1014451] = {
            ['ID'] = 1014451, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityRate', 
            ['PropMode'] = 'CalamityRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134900480'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329594112'),
            ['ShowType'] = 1, 
        },
        [1014461] = {
            ['ID'] = 1014461, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CalamityLevel', 
            ['PropMode'] = 'CalamityLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134900736'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329594368'),
            ['ShowType'] = 0, 
        },
        [1014471] = {
            ['ID'] = 1014471, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritCalamityLevel', 
            ['PropMode'] = 'CritCalamityLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134900992'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329594624'),
            ['ShowType'] = 0, 
        },
        [1014501] = {
            ['ID'] = 1014501, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderAtk', 
            ['PropMode'] = 'DisorderAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134901248'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372096'),
            ['ShowType'] = 0, 
        },
        [1014502] = {
            ['ID'] = 1014502, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DisorderAtk', 
            ['PropMode'] = 'DisorderAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134901504'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372096'),
            ['ShowType'] = 1, 
        },
        [1014511] = {
            ['ID'] = 1014511, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderHurtMulti', 
            ['PropMode'] = 'DisorderHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134901760'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329595392'),
            ['ShowType'] = 1, 
        },
        [1014521] = {
            ['ID'] = 1014521, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderHurtReduce', 
            ['PropMode'] = 'DisorderHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134902016'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329595648'),
            ['ShowType'] = 1, 
        },
        [1014531] = {
            ['ID'] = 1014531, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderAnti', 
            ['PropMode'] = 'DisorderAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134902272'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329595904'),
            ['ShowType'] = 0, 
        },
        [1014532] = {
            ['ID'] = 1014532, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DisorderAnti', 
            ['PropMode'] = 'DisorderAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134902528'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329595904'),
            ['ShowType'] = 1, 
        },
        [1014541] = {
            ['ID'] = 1014541, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreDisorderAnti', 
            ['PropMode'] = 'IgnoreDisorderAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134902784'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329596416'),
            ['ShowType'] = 0, 
        },
        [1014542] = {
            ['ID'] = 1014542, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreDisorderAnti', 
            ['PropMode'] = 'IgnoreDisorderAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134903040'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329596416'),
            ['ShowType'] = 1, 
        },
        [1014551] = {
            ['ID'] = 1014551, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderRate', 
            ['PropMode'] = 'DisorderRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134903296'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329596928'),
            ['ShowType'] = 1, 
        },
        [1014561] = {
            ['ID'] = 1014561, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DisorderLevel', 
            ['PropMode'] = 'DisorderLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134903552'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329597184'),
            ['ShowType'] = 0, 
        },
        [1014571] = {
            ['ID'] = 1014571, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritDisorderLevel', 
            ['PropMode'] = 'CritDisorderLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134903808'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329597440'),
            ['ShowType'] = 0, 
        },
        [1014601] = {
            ['ID'] = 1014601, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousAtk', 
            ['PropMode'] = 'TenebrousAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134904064'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372352'),
            ['ShowType'] = 0, 
        },
        [1014602] = {
            ['ID'] = 1014602, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'TenebrousAtk', 
            ['PropMode'] = 'TenebrousAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134904320'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372352'),
            ['ShowType'] = 1, 
        },
        [1014611] = {
            ['ID'] = 1014611, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousHurtMulti', 
            ['PropMode'] = 'TenebrousHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134904576'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329598208'),
            ['ShowType'] = 1, 
        },
        [1014621] = {
            ['ID'] = 1014621, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousHurtReduce', 
            ['PropMode'] = 'TenebrousHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134904832'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329598464'),
            ['ShowType'] = 1, 
        },
        [1014631] = {
            ['ID'] = 1014631, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousAnti', 
            ['PropMode'] = 'TenebrousAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134905088'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329598720'),
            ['ShowType'] = 0, 
        },
        [1014632] = {
            ['ID'] = 1014632, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'TenebrousAnti', 
            ['PropMode'] = 'TenebrousAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134905344'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329598720'),
            ['ShowType'] = 1, 
        },
        [1014641] = {
            ['ID'] = 1014641, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreTenebrousAnti', 
            ['PropMode'] = 'IgnoreTenebrousAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134905600'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329599232'),
            ['ShowType'] = 0, 
        },
        [1014642] = {
            ['ID'] = 1014642, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreTenebrousAnti', 
            ['PropMode'] = 'IgnoreTenebrousAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134905856'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329599232'),
            ['ShowType'] = 1, 
        },
        [1014651] = {
            ['ID'] = 1014651, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousRate', 
            ['PropMode'] = 'TenebrousRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134906112'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329599744'),
            ['ShowType'] = 1, 
        },
        [1014661] = {
            ['ID'] = 1014661, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TenebrousLevel', 
            ['PropMode'] = 'TenebrousLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134906368'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329600000'),
            ['ShowType'] = 0, 
        },
        [1014671] = {
            ['ID'] = 1014671, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritTenebrousLevel', 
            ['PropMode'] = 'CritTenebrousLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134906624'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329600256'),
            ['ShowType'] = 0, 
        },
        [1014701] = {
            ['ID'] = 1014701, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeAtk', 
            ['PropMode'] = 'KnowledgeAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134906880'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372608'),
            ['ShowType'] = 0, 
        },
        [1014702] = {
            ['ID'] = 1014702, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'KnowledgeAtk', 
            ['PropMode'] = 'KnowledgeAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134907136'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372608'),
            ['ShowType'] = 1, 
        },
        [1014711] = {
            ['ID'] = 1014711, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeHurtMulti', 
            ['PropMode'] = 'KnowledgeHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134907392'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329601024'),
            ['ShowType'] = 1, 
        },
        [1014721] = {
            ['ID'] = 1014721, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeHurtReduce', 
            ['PropMode'] = 'KnowledgeHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134907648'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329601280'),
            ['ShowType'] = 1, 
        },
        [1014731] = {
            ['ID'] = 1014731, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeAnti', 
            ['PropMode'] = 'KnowledgeAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134907904'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329601536'),
            ['ShowType'] = 0, 
        },
        [1014732] = {
            ['ID'] = 1014732, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'KnowledgeAnti', 
            ['PropMode'] = 'KnowledgeAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134908160'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329601536'),
            ['ShowType'] = 1, 
        },
        [1014741] = {
            ['ID'] = 1014741, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreKnowledgeAnti', 
            ['PropMode'] = 'IgnoreKnowledgeAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134908416'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329602048'),
            ['ShowType'] = 0, 
        },
        [1014742] = {
            ['ID'] = 1014742, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreKnowledgeAnti', 
            ['PropMode'] = 'IgnoreKnowledgeAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134908672'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329602048'),
            ['ShowType'] = 1, 
        },
        [1014751] = {
            ['ID'] = 1014751, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeRate', 
            ['PropMode'] = 'KnowledgeRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134908928'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329602560'),
            ['ShowType'] = 1, 
        },
        [1014761] = {
            ['ID'] = 1014761, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'KnowledgeLevel', 
            ['PropMode'] = 'KnowledgeLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134909184'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329602816'),
            ['ShowType'] = 0, 
        },
        [1014771] = {
            ['ID'] = 1014771, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritKnowledgeLevel', 
            ['PropMode'] = 'CritKnowledgeLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134909440'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329603072'),
            ['ShowType'] = 0, 
        },
        [1014801] = {
            ['ID'] = 1014801, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateAtk', 
            ['PropMode'] = 'FateAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134909696'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372864'),
            ['ShowType'] = 0, 
        },
        [1014802] = {
            ['ID'] = 1014802, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'FateAtk', 
            ['PropMode'] = 'FateAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134909952'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210372864'),
            ['ShowType'] = 1, 
        },
        [1014811] = {
            ['ID'] = 1014811, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateHurtMulti', 
            ['PropMode'] = 'FateHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134910208'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329603840'),
            ['ShowType'] = 1, 
        },
        [1014821] = {
            ['ID'] = 1014821, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateHurtReduce', 
            ['PropMode'] = 'FateHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134910464'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329604096'),
            ['ShowType'] = 1, 
        },
        [1014831] = {
            ['ID'] = 1014831, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateAnti', 
            ['PropMode'] = 'FateAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134910720'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329604352'),
            ['ShowType'] = 0, 
        },
        [1014832] = {
            ['ID'] = 1014832, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'FateAnti', 
            ['PropMode'] = 'FateAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134910976'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329604352'),
            ['ShowType'] = 1, 
        },
        [1014841] = {
            ['ID'] = 1014841, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreFateAnti', 
            ['PropMode'] = 'IgnoreFateAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134911232'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329604864'),
            ['ShowType'] = 0, 
        },
        [1014842] = {
            ['ID'] = 1014842, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreFateAnti', 
            ['PropMode'] = 'IgnoreFateAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134911488'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329604864'),
            ['ShowType'] = 1, 
        },
        [1014851] = {
            ['ID'] = 1014851, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateRate', 
            ['PropMode'] = 'FateRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134911744'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329605376'),
            ['ShowType'] = 1, 
        },
        [1014861] = {
            ['ID'] = 1014861, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FateLevel', 
            ['PropMode'] = 'FateLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134912000'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329605632'),
            ['ShowType'] = 0, 
        },
        [1014871] = {
            ['ID'] = 1014871, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritFateLevel', 
            ['PropMode'] = 'CritFateLevel_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134912256'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329605888'),
            ['ShowType'] = 0, 
        },
        [1015001] = {
            ['ID'] = 1015001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AirborneAnti', 
            ['PropMode'] = 'AirborneAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134912512'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329606144'),
            ['ShowType'] = 0, 
        },
        [1015002] = {
            ['ID'] = 1015002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AirborneAnti', 
            ['PropMode'] = 'AirborneAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134912768'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329606144'),
            ['ShowType'] = 1, 
        },
        [1015011] = {
            ['ID'] = 1015011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceAirborne', 
            ['PropMode'] = 'EnhanceAirborne_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134913024'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821248'),
            ['ShowType'] = 0, 
        },
        [1015012] = {
            ['ID'] = 1015012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceAirborne', 
            ['PropMode'] = 'EnhanceAirborne_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134913280'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821248'),
            ['ShowType'] = 1, 
        },
        [1015021] = {
            ['ID'] = 1015021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AirborneHitRate', 
            ['PropMode'] = 'AirborneHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134913536'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329607168'),
            ['ShowType'] = 1, 
        },
        [1015101] = {
            ['ID'] = 1015101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DownAnti', 
            ['PropMode'] = 'DownAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134913792'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329607424'),
            ['ShowType'] = 0, 
        },
        [1015102] = {
            ['ID'] = 1015102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DownAnti', 
            ['PropMode'] = 'DownAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134914048'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329607424'),
            ['ShowType'] = 1, 
        },
        [1015111] = {
            ['ID'] = 1015111, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceDown', 
            ['PropMode'] = 'EnhanceDown_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134914304'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821504'),
            ['ShowType'] = 0, 
        },
        [1015112] = {
            ['ID'] = 1015112, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceDown', 
            ['PropMode'] = 'EnhanceDown_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134914560'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821504'),
            ['ShowType'] = 1, 
        },
        [1015121] = {
            ['ID'] = 1015121, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DownHitRate', 
            ['PropMode'] = 'DownHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134914816'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329608448'),
            ['ShowType'] = 1, 
        },
        [1015201] = {
            ['ID'] = 1015201, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'PullAnti', 
            ['PropMode'] = 'PullAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134915072'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329608704'),
            ['ShowType'] = 0, 
        },
        [1015202] = {
            ['ID'] = 1015202, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'PullAnti', 
            ['PropMode'] = 'PullAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134915328'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329608704'),
            ['ShowType'] = 1, 
        },
        [1015211] = {
            ['ID'] = 1015211, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhancePull', 
            ['PropMode'] = 'EnhancePull_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134915584'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821760'),
            ['ShowType'] = 0, 
        },
        [1015212] = {
            ['ID'] = 1015212, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhancePull', 
            ['PropMode'] = 'EnhancePull_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134915840'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368821760'),
            ['ShowType'] = 1, 
        },
        [1015221] = {
            ['ID'] = 1015221, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'PullHitRate', 
            ['PropMode'] = 'PullHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134916096'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329609728'),
            ['ShowType'] = 1, 
        },
        [1015301] = {
            ['ID'] = 1015301, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TiedAnti', 
            ['PropMode'] = 'TiedAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134916352'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329609984'),
            ['ShowType'] = 0, 
        },
        [1015302] = {
            ['ID'] = 1015302, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'TiedAnti', 
            ['PropMode'] = 'TiedAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134916608'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329609984'),
            ['ShowType'] = 1, 
        },
        [1015311] = {
            ['ID'] = 1015311, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceTied', 
            ['PropMode'] = 'EnhanceTied_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134916864'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822016'),
            ['ShowType'] = 0, 
        },
        [1015312] = {
            ['ID'] = 1015312, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceTied', 
            ['PropMode'] = 'EnhanceTied_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134917120'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822016'),
            ['ShowType'] = 1, 
        },
        [1015321] = {
            ['ID'] = 1015321, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'TiedHitRate', 
            ['PropMode'] = 'TiedHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134917376'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329611008'),
            ['ShowType'] = 1, 
        },
        [1015401] = {
            ['ID'] = 1015401, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SleepAnti', 
            ['PropMode'] = 'SleepAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134917632'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329611264'),
            ['ShowType'] = 0, 
        },
        [1015402] = {
            ['ID'] = 1015402, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SleepAnti', 
            ['PropMode'] = 'SleepAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134917888'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329611264'),
            ['ShowType'] = 1, 
        },
        [1015411] = {
            ['ID'] = 1015411, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceSleep', 
            ['PropMode'] = 'EnhanceSleep_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134918144'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822272'),
            ['ShowType'] = 0, 
        },
        [1015412] = {
            ['ID'] = 1015412, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceSleep', 
            ['PropMode'] = 'EnhanceSleep_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134918400'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822272'),
            ['ShowType'] = 1, 
        },
        [1015421] = {
            ['ID'] = 1015421, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SleepHitRate', 
            ['PropMode'] = 'SleepHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134918656'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329612288'),
            ['ShowType'] = 1, 
        },
        [1015501] = {
            ['ID'] = 1015501, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DizzyAnti', 
            ['PropMode'] = 'DizzyAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134918912'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329612544'),
            ['ShowType'] = 0, 
        },
        [1015502] = {
            ['ID'] = 1015502, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'DizzyAnti', 
            ['PropMode'] = 'DizzyAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134919168'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329612544'),
            ['ShowType'] = 1, 
        },
        [1015511] = {
            ['ID'] = 1015511, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceDizzy', 
            ['PropMode'] = 'EnhanceDizzy_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134919424'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822528'),
            ['ShowType'] = 0, 
        },
        [1015512] = {
            ['ID'] = 1015512, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceDizzy', 
            ['PropMode'] = 'EnhanceDizzy_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134919680'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822528'),
            ['ShowType'] = 1, 
        },
        [1015521] = {
            ['ID'] = 1015521, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'DizzyHitRate', 
            ['PropMode'] = 'DizzyHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134919936'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329613568'),
            ['ShowType'] = 1, 
        },
        [1015601] = {
            ['ID'] = 1015601, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FearAnti', 
            ['PropMode'] = 'FearAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134920192'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329613824'),
            ['ShowType'] = 0, 
        },
        [1015602] = {
            ['ID'] = 1015602, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'FearAnti', 
            ['PropMode'] = 'FearAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134920448'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329613824'),
            ['ShowType'] = 1, 
        },
        [1015611] = {
            ['ID'] = 1015611, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceFear', 
            ['PropMode'] = 'EnhanceFear_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134920704'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822784'),
            ['ShowType'] = 0, 
        },
        [1015612] = {
            ['ID'] = 1015612, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceFear', 
            ['PropMode'] = 'EnhanceFear_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134920960'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368822784'),
            ['ShowType'] = 1, 
        },
        [1015621] = {
            ['ID'] = 1015621, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FearHitRate', 
            ['PropMode'] = 'FearHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134921216'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329614848'),
            ['ShowType'] = 1, 
        },
        [1015701] = {
            ['ID'] = 1015701, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SilenceAnti', 
            ['PropMode'] = 'SilenceAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134921472'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329615104'),
            ['ShowType'] = 0, 
        },
        [1015702] = {
            ['ID'] = 1015702, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SilenceAnti', 
            ['PropMode'] = 'SilenceAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134921728'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329615104'),
            ['ShowType'] = 1, 
        },
        [1015711] = {
            ['ID'] = 1015711, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceSilence', 
            ['PropMode'] = 'EnhanceSilence_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134921984'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823040'),
            ['ShowType'] = 0, 
        },
        [1015712] = {
            ['ID'] = 1015712, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceSilence', 
            ['PropMode'] = 'EnhanceSilence_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134922240'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823040'),
            ['ShowType'] = 1, 
        },
        [1015721] = {
            ['ID'] = 1015721, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SilenceHitRate', 
            ['PropMode'] = 'SilenceHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134922496'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329616128'),
            ['ShowType'] = 1, 
        },
        [1015801] = {
            ['ID'] = 1015801, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SlowAnti', 
            ['PropMode'] = 'SlowAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134922752'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329616384'),
            ['ShowType'] = 0, 
        },
        [1015802] = {
            ['ID'] = 1015802, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SlowAnti', 
            ['PropMode'] = 'SlowAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134923008'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329616384'),
            ['ShowType'] = 1, 
        },
        [1015811] = {
            ['ID'] = 1015811, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceSlow', 
            ['PropMode'] = 'EnhanceSlow_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134923264'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823296'),
            ['ShowType'] = 0, 
        },
        [1015812] = {
            ['ID'] = 1015812, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceSlow', 
            ['PropMode'] = 'EnhanceSlow_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134923520'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823296'),
            ['ShowType'] = 1, 
        },
        [1015821] = {
            ['ID'] = 1015821, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SlowHitRate', 
            ['PropMode'] = 'SlowHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134923776'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329617408'),
            ['ShowType'] = 1, 
        },
        [1016001] = {
            ['ID'] = 1016001, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllEleAtk', 
            ['PropMode'] = 'AllEleAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134924032'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329617664'),
            ['ShowType'] = 0, 
        },
        [1016002] = {
            ['ID'] = 1016002, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllEleAtk', 
            ['PropMode'] = 'AllEleAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134924288'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329617664'),
            ['ShowType'] = 1, 
        },
        [1016011] = {
            ['ID'] = 1016011, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllEleHurtMulti', 
            ['PropMode'] = 'AllEleHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134924544'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329618176'),
            ['ShowType'] = 1, 
        },
        [1016021] = {
            ['ID'] = 1016021, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllEleHurtReduce', 
            ['PropMode'] = 'AllEleHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134924800'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329618432'),
            ['ShowType'] = 1, 
        },
        [1016031] = {
            ['ID'] = 1016031, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllControlAnti', 
            ['PropMode'] = 'AllControlAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134925056'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823808'),
            ['ShowType'] = 0, 
        },
        [1016032] = {
            ['ID'] = 1016032, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllControlAnti', 
            ['PropMode'] = 'AllControlAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134925312'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823808'),
            ['ShowType'] = 1, 
        },
        [1016041] = {
            ['ID'] = 1016041, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'EnhanceAllControl', 
            ['PropMode'] = 'EnhanceAllControl_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134925568'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19242258810368'),
            ['ShowType'] = 0, 
        },
        [1016042] = {
            ['ID'] = 1016042, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'EnhanceAllControl', 
            ['PropMode'] = 'EnhanceAllControl_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134925824'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19242258810368'),
            ['ShowType'] = 1, 
        },
        [1016051] = {
            ['ID'] = 1016051, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllControlHitRate', 
            ['PropMode'] = 'AllControlHitRate_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134926080'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_19036368823552'),
            ['ShowType'] = 1, 
        },
        [1016061] = {
            ['ID'] = 1016061, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllEleAnti', 
            ['PropMode'] = 'AllEleAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134926336'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329619968'),
            ['ShowType'] = 0, 
        },
        [1016062] = {
            ['ID'] = 1016062, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllEleAnti', 
            ['PropMode'] = 'AllEleAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134926592'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329619968'),
            ['ShowType'] = 1, 
        },
        [1016071] = {
            ['ID'] = 1016071, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'IgnoreAllEle', 
            ['PropMode'] = 'IgnoreAllEle_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134926848'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329620480'),
            ['ShowType'] = 0, 
        },
        [1016072] = {
            ['ID'] = 1016072, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'IgnoreAllEle', 
            ['PropMode'] = 'IgnoreAllEle_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134927104'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24190329620480'),
            ['ShowType'] = 1, 
        },
        [1016081] = {
            ['ID'] = 1016081, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Def', 
            ['PropMode'] = 'Def_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134927360'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789318144'),
            ['ShowType'] = 0, 
        },
        [1016082] = {
            ['ID'] = 1016082, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Def', 
            ['PropMode'] = 'Def_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134927616'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789318144'),
            ['ShowType'] = 1, 
        },
        [1016091] = {
            ['ID'] = 1016091, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Block', 
            ['PropMode'] = 'Block_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134927872'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210374144'),
            ['ShowType'] = 0, 
        },
        [1016092] = {
            ['ID'] = 1016092, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Block', 
            ['PropMode'] = 'Block_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134928128'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210374144'),
            ['ShowType'] = 1, 
        },
        [1016101] = {
            ['ID'] = 1016101, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Atk', 
            ['PropMode'] = 'Atk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134928384'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789317888'),
            ['ShowType'] = 0, 
        },
        [1016102] = {
            ['ID'] = 1016102, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Atk', 
            ['PropMode'] = 'Atk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134928640'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_54632789317888'),
            ['ShowType'] = 1, 
        },
        [1016121] = {
            ['ID'] = 1016121, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'Pierce', 
            ['PropMode'] = 'Pierce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134928896'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210373888'),
            ['ShowType'] = 0, 
        },
        [1016122] = {
            ['ID'] = 1016122, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'Pierce', 
            ['PropMode'] = 'Pierce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134929152'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210373888'),
            ['ShowType'] = 1, 
        },
        [1016131] = {
            ['ID'] = 1016131, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllProHurtMulti', 
            ['PropMode'] = 'AllProHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134929408'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890641920'),
            ['ShowType'] = 0, 
        },
        [1016132] = {
            ['ID'] = 1016132, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllProHurtMulti', 
            ['PropMode'] = 'AllProHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134929664'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890641920'),
            ['ShowType'] = 1, 
        },
        [1016141] = {
            ['ID'] = 1016141, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllProHurtReduce', 
            ['PropMode'] = 'AllProHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134929920'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642176'),
            ['ShowType'] = 0, 
        },
        [1016142] = {
            ['ID'] = 1016142, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllProHurtReduce', 
            ['PropMode'] = 'AllProHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134930176'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642176'),
            ['ShowType'] = 1, 
        },
        [1016151] = {
            ['ID'] = 1016151, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllRaceHurtMulti', 
            ['PropMode'] = 'AllRaceHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134930432'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642432'),
            ['ShowType'] = 0, 
        },
        [1016152] = {
            ['ID'] = 1016152, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllRaceHurtMulti', 
            ['PropMode'] = 'AllRaceHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134930688'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642432'),
            ['ShowType'] = 1, 
        },
        [1016161] = {
            ['ID'] = 1016161, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AllRaceHurtReduce', 
            ['PropMode'] = 'AllRaceHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134930944'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642688'),
            ['ShowType'] = 0, 
        },
        [1016162] = {
            ['ID'] = 1016162, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AllRaceHurtReduce', 
            ['PropMode'] = 'AllRaceHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134931200'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890642688'),
            ['ShowType'] = 1, 
        },
        [1016171] = {
            ['ID'] = 1016171, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritAnti', 
            ['PropMode'] = 'CritAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134931456'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210376960'),
            ['ShowType'] = 0, 
        },
        [1016172] = {
            ['ID'] = 1016172, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritAnti', 
            ['PropMode'] = 'CritAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134931712'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210376960'),
            ['ShowType'] = 0, 
        },
        [1016181] = {
            ['ID'] = 1016181, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'CritDef', 
            ['PropMode'] = 'CritDef_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134931968'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377984'),
            ['ShowType'] = 1, 
        },
        [1016182] = {
            ['ID'] = 1016182, 
            ['IsPropSet'] = true, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'CritDef', 
            ['PropMode'] = 'CritDef_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134932224'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_18830210377984'),
            ['ShowType'] = 1, 
        },
        [1017011] = {
            ['ID'] = 1017011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ShieldBreak', 
            ['PropMode'] = 'ShieldBreak_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134932480'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890643456'),
            ['ShowType'] = 0, 
        },
        [1017021] = {
            ['ID'] = 1017021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AirShield', 
            ['PropMode'] = 'AirShield_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134932736'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890643712'),
            ['ShowType'] = 0, 
        },
        [1017031] = {
            ['ID'] = 1017031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SkillAnti', 
            ['PropMode'] = 'SkillAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134932992'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890643968'),
            ['ShowType'] = 0, 
        },
        [1017032] = {
            ['ID'] = 1017032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SkillAnti', 
            ['PropMode'] = 'SkillAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134933248'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890643968'),
            ['ShowType'] = 0, 
        },
        [1017041] = {
            ['ID'] = 1017041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SkillEnhance', 
            ['PropMode'] = 'SkillEnhance_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134933504'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644224'),
            ['ShowType'] = 0, 
        },
        [1017042] = {
            ['ID'] = 1017042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SkillEnhance', 
            ['PropMode'] = 'SkillEnhance_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134933760'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644224'),
            ['ShowType'] = 0, 
        },
        [1017051] = {
            ['ID'] = 1017051, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AdditionalAtk', 
            ['PropMode'] = 'AdditionalAtk_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134934016'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644480'),
            ['ShowType'] = 0, 
        },
        [1017052] = {
            ['ID'] = 1017052, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AdditionalAtk', 
            ['PropMode'] = 'AdditionalAtk_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134934272'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644480'),
            ['ShowType'] = 0, 
        },
        [1017061] = {
            ['ID'] = 1017061, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AdditionalAnti', 
            ['PropMode'] = 'AdditionalAnti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134934528'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644736'),
            ['ShowType'] = 0, 
        },
        [1017062] = {
            ['ID'] = 1017062, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AdditionalAnti', 
            ['PropMode'] = 'AdditionalAnti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134934784'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644736'),
            ['ShowType'] = 0, 
        },
        [1017071] = {
            ['ID'] = 1017071, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'AdditionalIgnore', 
            ['PropMode'] = 'AdditionalIgnore_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134935040'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644992'),
            ['ShowType'] = 0, 
        },
        [1017072] = {
            ['ID'] = 1017072, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'AdditionalIgnore', 
            ['PropMode'] = 'AdditionalIgnore_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134935296'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24052890644992'),
            ['ShowType'] = 0, 
        },
        [1018001] = {
            ['ID'] = 1018001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtMulti1', 
            ['PropMode'] = 'RaceHurtMulti1_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134935552'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610077696'),
            ['ShowType'] = 0, 
        },
        [1018002] = {
            ['ID'] = 1018002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtMulti1', 
            ['PropMode'] = 'RaceHurtMulti1_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134935808'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610077696'),
            ['ShowType'] = 1, 
        },
        [1018011] = {
            ['ID'] = 1018011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtMulti2', 
            ['PropMode'] = 'RaceHurtMulti2_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134936064'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610077952'),
            ['ShowType'] = 0, 
        },
        [1018012] = {
            ['ID'] = 1018012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtMulti2', 
            ['PropMode'] = 'RaceHurtMulti2_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134936320'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610077952'),
            ['ShowType'] = 1, 
        },
        [1018021] = {
            ['ID'] = 1018021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtMulti3', 
            ['PropMode'] = 'RaceHurtMulti3_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134936576'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078208'),
            ['ShowType'] = 0, 
        },
        [1018022] = {
            ['ID'] = 1018022, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtMulti3', 
            ['PropMode'] = 'RaceHurtMulti3_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134936832'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078208'),
            ['ShowType'] = 1, 
        },
        [1018031] = {
            ['ID'] = 1018031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtMulti4', 
            ['PropMode'] = 'RaceHurtMulti4_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134937088'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078464'),
            ['ShowType'] = 0, 
        },
        [1018032] = {
            ['ID'] = 1018032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtMulti4', 
            ['PropMode'] = 'RaceHurtMulti4_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134937344'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078464'),
            ['ShowType'] = 1, 
        },
        [1018041] = {
            ['ID'] = 1018041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtMulti5', 
            ['PropMode'] = 'RaceHurtMulti5_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134937600'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078720'),
            ['ShowType'] = 0, 
        },
        [1018042] = {
            ['ID'] = 1018042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtMulti5', 
            ['PropMode'] = 'RaceHurtMulti5_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134937856'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078720'),
            ['ShowType'] = 1, 
        },
        [1018101] = {
            ['ID'] = 1018101, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtReduce1', 
            ['PropMode'] = 'RaceHurtReduce1_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134938112'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078976'),
            ['ShowType'] = 0, 
        },
        [1018102] = {
            ['ID'] = 1018102, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtReduce1', 
            ['PropMode'] = 'RaceHurtReduce1_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134938368'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610078976'),
            ['ShowType'] = 1, 
        },
        [1018111] = {
            ['ID'] = 1018111, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtReduce2', 
            ['PropMode'] = 'RaceHurtReduce2_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134938624'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079232'),
            ['ShowType'] = 0, 
        },
        [1018112] = {
            ['ID'] = 1018112, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtReduce2', 
            ['PropMode'] = 'RaceHurtReduce2_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134938880'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079232'),
            ['ShowType'] = 1, 
        },
        [1018121] = {
            ['ID'] = 1018121, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtReduce3', 
            ['PropMode'] = 'RaceHurtReduce3_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134939136'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079488'),
            ['ShowType'] = 0, 
        },
        [1018122] = {
            ['ID'] = 1018122, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtReduce3', 
            ['PropMode'] = 'RaceHurtReduce3_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134939392'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079488'),
            ['ShowType'] = 1, 
        },
        [1018131] = {
            ['ID'] = 1018131, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtReduce4', 
            ['PropMode'] = 'RaceHurtReduce4_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134939648'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079744'),
            ['ShowType'] = 0, 
        },
        [1018132] = {
            ['ID'] = 1018132, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtReduce4', 
            ['PropMode'] = 'RaceHurtReduce4_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134939904'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610079744'),
            ['ShowType'] = 1, 
        },
        [1018141] = {
            ['ID'] = 1018141, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'RaceHurtReduce5', 
            ['PropMode'] = 'RaceHurtReduce5_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134940160'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080000'),
            ['ShowType'] = 0, 
        },
        [1018142] = {
            ['ID'] = 1018142, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'RaceHurtReduce5', 
            ['PropMode'] = 'RaceHurtReduce5_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134940416'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080000'),
            ['ShowType'] = 1, 
        },
        [1019001] = {
            ['ID'] = 1019001, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SunHurtMulti', 
            ['PropMode'] = 'SunHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134940672'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080256'),
            ['ShowType'] = 0, 
        },
        [1019002] = {
            ['ID'] = 1019002, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SunHurtMulti', 
            ['PropMode'] = 'SunHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134940928'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080256'),
            ['ShowType'] = 1, 
        },
        [1019011] = {
            ['ID'] = 1019011, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'VisionaryHurtMulti', 
            ['PropMode'] = 'VisionaryHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134941184'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080512'),
            ['ShowType'] = 0, 
        },
        [1019012] = {
            ['ID'] = 1019012, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'VisionaryHurtMulti', 
            ['PropMode'] = 'VisionaryHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134941440'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080512'),
            ['ShowType'] = 1, 
        },
        [1019021] = {
            ['ID'] = 1019021, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FeatherwitHurtMulti', 
            ['PropMode'] = 'FeatherwitHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134941696'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080768'),
            ['ShowType'] = 0, 
        },
        [1019022] = {
            ['ID'] = 1019022, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'FeatherwitHurtMulti', 
            ['PropMode'] = 'FeatherwitHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134941952'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610080768'),
            ['ShowType'] = 1, 
        },
        [1019031] = {
            ['ID'] = 1019031, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ArbiterHurtMulti', 
            ['PropMode'] = 'ArbiterHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134942208'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081024'),
            ['ShowType'] = 0, 
        },
        [1019032] = {
            ['ID'] = 1019032, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ArbiterHurtMulti', 
            ['PropMode'] = 'ArbiterHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134942464'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081024'),
            ['ShowType'] = 1, 
        },
        [1019041] = {
            ['ID'] = 1019041, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'WarriorHurtMulti', 
            ['PropMode'] = 'WarriorHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134942720'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081280'),
            ['ShowType'] = 0, 
        },
        [1019042] = {
            ['ID'] = 1019042, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'WarriorHurtMulti', 
            ['PropMode'] = 'WarriorHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134942976'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081280'),
            ['ShowType'] = 1, 
        },
        [1019051] = {
            ['ID'] = 1019051, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ApprenticeHurtMulti', 
            ['PropMode'] = 'ApprenticeHurtMulti_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134943232'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081536'),
            ['ShowType'] = 0, 
        },
        [1019052] = {
            ['ID'] = 1019052, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ApprenticeHurtMulti', 
            ['PropMode'] = 'ApprenticeHurtMulti_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134943488'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610081536'),
            ['ShowType'] = 1, 
        },
        [1019081] = {
            ['ID'] = 1019081, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ProHurtMulti9', 
            ['PropMode'] = 'ProHurtMulti9_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134944768'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610082304'),
            ['ShowType'] = 0, 
        },
        [1019082] = {
            ['ID'] = 1019082, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ProHurtMulti9', 
            ['PropMode'] = 'ProHurtMulti9_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134945024'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610082304'),
            ['ShowType'] = 1, 
        },
        [1019201] = {
            ['ID'] = 1019201, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'SunHurtReduce', 
            ['PropMode'] = 'SunHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134945792'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610082816'),
            ['ShowType'] = 0, 
        },
        [1019202] = {
            ['ID'] = 1019202, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'SunHurtReduce', 
            ['PropMode'] = 'SunHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134946048'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610082816'),
            ['ShowType'] = 1, 
        },
        [1019211] = {
            ['ID'] = 1019211, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'VisionaryHurtReduce', 
            ['PropMode'] = 'VisionaryHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134946304'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083072'),
            ['ShowType'] = 0, 
        },
        [1019212] = {
            ['ID'] = 1019212, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'VisionaryHurtReduce', 
            ['PropMode'] = 'VisionaryHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134946560'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083072'),
            ['ShowType'] = 1, 
        },
        [1019221] = {
            ['ID'] = 1019221, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'FeatherwitHurtReduce', 
            ['PropMode'] = 'FeatherwitHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134946816'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083328'),
            ['ShowType'] = 0, 
        },
        [1019222] = {
            ['ID'] = 1019222, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'FeatherwitHurtReduce', 
            ['PropMode'] = 'FeatherwitHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134947072'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083328'),
            ['ShowType'] = 1, 
        },
        [1019231] = {
            ['ID'] = 1019231, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ArbiterHurtReduce', 
            ['PropMode'] = 'ArbiterHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134947328'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083584'),
            ['ShowType'] = 0, 
        },
        [1019232] = {
            ['ID'] = 1019232, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ArbiterHurtReduce', 
            ['PropMode'] = 'ArbiterHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134947584'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083584'),
            ['ShowType'] = 1, 
        },
        [1019241] = {
            ['ID'] = 1019241, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'WarriorHurtReduce', 
            ['PropMode'] = 'WarriorHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134947840'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083840'),
            ['ShowType'] = 0, 
        },
        [1019242] = {
            ['ID'] = 1019242, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'WarriorHurtReduce', 
            ['PropMode'] = 'WarriorHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134948096'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610083840'),
            ['ShowType'] = 1, 
        },
        [1019251] = {
            ['ID'] = 1019251, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ApprenticeHurtReduce', 
            ['PropMode'] = 'ApprenticeHurtReduce_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134948352'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610084096'),
            ['ShowType'] = 0, 
        },
        [1019252] = {
            ['ID'] = 1019252, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ApprenticeHurtReduce', 
            ['PropMode'] = 'ApprenticeHurtReduce_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134948608'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610084096'),
            ['ShowType'] = 1, 
        },
        [1019281] = {
            ['ID'] = 1019281, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Number', 
            ['Prop'] = 'ProHurtReduce9', 
            ['PropMode'] = 'ProHurtReduce9_N', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134949888'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610084864'),
            ['ShowType'] = 0, 
        },
        [1019282] = {
            ['ID'] = 1019282, 
            ['IsPropSet'] = false, 
            ['Mode'] = 'Percent', 
            ['Prop'] = 'ProHurtReduce9', 
            ['PropMode'] = 'ProHurtReduce9_P', 
            ['PropModeName'] = Game.TableDataManager:GetLangStr('str_24191134950144'),
            ['PropName'] = Game.TableDataManager:GetLangStr('str_24121610084864'),
            ['ShowType'] = 1, 
        },
    }
}
return TopData