--
-- 表名: PlotRecapCategoryLevelData后处理
--

local TopData = {
    data = {
        [0] = {
            ['ContainData'] = {}, 
            ['Key'] = 0, 
            ['Level'] = 6, 
            ['LevelName'] = {Game.TableDataManager:GetLangStr('str_70598526465'),Game.TableDataManager:GetLangStr('str_70598526466'),Game.TableDataManager:GetLangStr('str_70598526467'),Game.TableDataManager:GetLangStr('str_70598526468'),Game.TableDataManager:GetLangStr('str_70598526469'),Game.TableDataManager:GetLangStr('str_70598526470'),}, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_41232491349504'),
            ['RewardList'] = {{2001000, 10, 1}, {2001001, 20, -1}, {2001000, 30, -1}, {2001001, 40, -1}, {2001000, 50, 1}, }, 
            ['SubTitleName'] = {}, 
            ['TabNameModuleLock'] = '', 
            ['TokenList'] = {1000, 3000, 5000, 8000, 13000}, 
            ['bTabVisible'] = true, 
        },
        [1] = {
            ['ContainData'] = {'FactMainRecap'}, 
            ['Key'] = 1, 
            ['Level'] = 6, 
            ['LevelName'] = {Game.TableDataManager:GetLangStr('str_70598526465'),Game.TableDataManager:GetLangStr('str_70598526466'),Game.TableDataManager:GetLangStr('str_70598526467'),Game.TableDataManager:GetLangStr('str_70598526468'),Game.TableDataManager:GetLangStr('str_70598526469'),Game.TableDataManager:GetLangStr('str_70598526470'),}, 
            ['Name'] = 'PlOTRECAP_TAB1_NAME', 
            ['RewardList'] = {{2001000, 10, 1}, {2001001, 20, -1}, {2001000, 30, -1}, {2001001, 40, -1}, {2001000, 50, 1}, }, 
            ['SubTitleName'] = {}, 
            ['TabNameModuleLock'] = 'MODULE_LOCK_FACTMAINRECAP', 
            ['TokenList'] = {100, 300, 500, 800, 1300}, 
            ['bTabVisible'] = true, 
        },
        [2] = {
            ['ContainData'] = {'MistMainRecap'}, 
            ['Key'] = 2, 
            ['Level'] = 6, 
            ['LevelName'] = {Game.TableDataManager:GetLangStr('str_70598526465'),Game.TableDataManager:GetLangStr('str_70598526466'),Game.TableDataManager:GetLangStr('str_70598526467'),Game.TableDataManager:GetLangStr('str_70598526468'),Game.TableDataManager:GetLangStr('str_70598526469'),Game.TableDataManager:GetLangStr('str_70598526470'),}, 
            ['Name'] = 'PlOTRECAP_TAB2_NAME', 
            ['RewardList'] = {{2001000, 10, 1}, {2001001, 20, -1}, {2001000, 30, -1}, {2001001, 40, -1}, {2001000, 50, 1}, }, 
            ['SubTitleName'] = {}, 
            ['TabNameModuleLock'] = 'MODULE_LOCK_MISTMAINRECAP', 
            ['TokenList'] = {100, 300, 500, 800, 1300}, 
            ['bTabVisible'] = true, 
        },
        [3] = {
            ['ContainData'] = {'FactSideRecap', 'MistSideRecap', 'SideQuestRecap'}, 
            ['Key'] = 3, 
            ['Level'] = 6, 
            ['LevelName'] = {Game.TableDataManager:GetLangStr('str_70598526465'),Game.TableDataManager:GetLangStr('str_70598526466'),Game.TableDataManager:GetLangStr('str_70598526467'),Game.TableDataManager:GetLangStr('str_70598526468'),Game.TableDataManager:GetLangStr('str_70598526469'),Game.TableDataManager:GetLangStr('str_70598526470'),}, 
            ['Name'] = 'PlOTRECAP_TAB3_NAME', 
            ['RewardList'] = {{2001000, 10, 1}, {2001001, 20, -1}, {2001000, 30, -1}, {2001001, 40, -1}, {2001000, 50, 1}, }, 
            ['SubTitleName'] = {'PlOTRECAP_TAB3_TITLE1', 'PlOTRECAP_TAB3_TITLE2', 'PlOTRECAP_TAB3_TITLE3'}, 
            ['TabNameModuleLock'] = 'MODULE_LOCK_SIDEQUESTRECAP', 
            ['TokenList'] = {100, 300, 500, 800, 1300}, 
            ['bTabVisible'] = true, 
        },
    }
}
return TopData