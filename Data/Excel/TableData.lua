local TableData = {}

function TableData.mergeTable(table, chunk_table)
    for key, value in pairs(chunk_table) do
        table[key] = value
    end
    return table
end

---@return _URLDataRow
function TableData.GetURLDataRow(key, priority)
	return Game.TableDataManager:GetRow("URLData", key, priority)
end


---@return _URLDataRow[]
function TableData.GetURLDataTable(priority)
	return Game.TableDataManager:GetData("URLData", priority)
end


---@return _LevelMapDataRow
function TableData.GetLevelMapDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelMapData", key, priority)
end


---@return _LevelMapDataRow[]
function TableData.GetLevelMapDataTable(priority)
	return Game.TableDataManager:GetData("LevelMapData", priority)
end


---@return _LevelMapData_PlaneRow
function TableData.GetLevelMapData_PlaneRow(key, priority)
	return Game.TableDataManager:GetRow("LevelMapData_Plane", key, priority)
end


---@return _LevelMapData_PlaneRow[]
function TableData.GetLevelMapData_PlaneTable(priority)
	return Game.TableDataManager:GetData("LevelMapData_Plane", priority)
end


---@return _EnvironmentThemeDataRow
function TableData.GetEnvironmentThemeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EnvironmentThemeData", key, priority)
end


---@return _EnvironmentThemeDataRow[]
function TableData.GetEnvironmentThemeDataTable(priority)
	return Game.TableDataManager:GetData("EnvironmentThemeData", priority)
end


---@return _GMCommandTypeRow
function TableData.GetGMCommandTypeRow(key, priority)
	return Game.TableDataManager:GetRow("GMCommandType", key, priority)
end


---@return _GMCommandTypeRow[]
function TableData.GetGMCommandTypeTable(priority)
	return Game.TableDataManager:GetData("GMCommandType", priority)
end


---@return _GMCommandsDataRow
function TableData.GetGMCommandsDataRow(key, priority)
	return Game.TableDataManager:GetRow("GMCommandsData", key, priority)
end


---@return _GMCommandsDataRow[]
function TableData.GetGMCommandsDataTable(priority)
	return Game.TableDataManager:GetData("GMCommandsData", priority)
end


---@return _IntegratedCommandDataRow
function TableData.GetIntegratedCommandDataRow(key, priority)
	return Game.TableDataManager:GetRow("IntegratedCommandData", key, priority)
end


---@return _IntegratedCommandDataRow[]
function TableData.GetIntegratedCommandDataTable(priority)
	return Game.TableDataManager:GetData("IntegratedCommandData", priority)
end


---@return _GMCustomDataRow
function TableData.GetGMCustomDataRow(key, priority)
	return Game.TableDataManager:GetRow("GMCustomData", key, priority)
end


---@return _GMCustomDataRow[]
function TableData.GetGMCustomDataTable(priority)
	return Game.TableDataManager:GetData("GMCustomData", priority)
end


---@return _IntegratedCustomDataRow
function TableData.GetIntegratedCustomDataRow(key, priority)
	return Game.TableDataManager:GetRow("IntegratedCustomData", key, priority)
end


---@return _IntegratedCustomDataRow[]
function TableData.GetIntegratedCustomDataTable(priority)
	return Game.TableDataManager:GetData("IntegratedCustomData", priority)
end


---@return _DungeonDataRow
function TableData.GetDungeonDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonData", key, priority)
end


---@return _DungeonDataRow[]
function TableData.GetDungeonDataTable(priority)
	return Game.TableDataManager:GetData("DungeonData", priority)
end


---@return _DungeonEnterDataRow
function TableData.GetDungeonEnterDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonEnterData", key, priority)
end


---@return _DungeonEnterDataRow[]
function TableData.GetDungeonEnterDataTable(priority)
	return Game.TableDataManager:GetData("DungeonEnterData", priority)
end


---@return _DungeonStageRankingDataRow
function TableData.GetDungeonStageRankingDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonStageRankingData", key, priority)
end


---@return _DungeonStageRankingDataRow[]
function TableData.GetDungeonStageRankingDataTable(priority)
	return Game.TableDataManager:GetData("DungeonStageRankingData", priority)
end


---@return _DungeonBuffDataRow
function TableData.GetDungeonBuffDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonBuffData", key, priority)
end


---@return _DungeonBuffDataRow[]
function TableData.GetDungeonBuffDataTable(priority)
	return Game.TableDataManager:GetData("DungeonBuffData", priority)
end


---@return _DungeonTypeDataRow
function TableData.GetDungeonTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonTypeData", key, priority)
end


---@return _DungeonTypeDataRow[]
function TableData.GetDungeonTypeDataTable(priority)
	return Game.TableDataManager:GetData("DungeonTypeData", priority)
end


---@return _SeasonDungeonDataRow
function TableData.GetSeasonDungeonDataRow(key, priority)
	return Game.TableDataManager:GetRow("SeasonDungeonData", key, priority)
end


---@return _SeasonDungeonDataRow[]
function TableData.GetSeasonDungeonDataTable(priority)
	return Game.TableDataManager:GetData("SeasonDungeonData", priority)
end


---@return _DungeonSettlementDataRow
function TableData.GetDungeonSettlementDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonSettlementData", key, priority)
end


---@return _DungeonSettlementDataRow[]
function TableData.GetDungeonSettlementDataTable(priority)
	return Game.TableDataManager:GetData("DungeonSettlementData", priority)
end


---@return _TingenHistoryDataRow
function TableData.GetTingenHistoryDataRow(key, priority)
	return Game.TableDataManager:GetRow("TingenHistoryData", key, priority)
end


---@return _TingenHistoryDataRow[]
function TableData.GetTingenHistoryDataTable(priority)
	return Game.TableDataManager:GetData("TingenHistoryData", priority)
end


---@return _DungeonVersionDataRow
function TableData.GetDungeonVersionDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonVersionData", key, priority)
end


---@return _DungeonVersionDataRow[]
function TableData.GetDungeonVersionDataTable(priority)
	return Game.TableDataManager:GetData("DungeonVersionData", priority)
end


---@return _DungeonRewardDataRow
function TableData.GetDungeonRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("DungeonRewardData", key, priority)
end


---@return _DungeonRewardDataRow[]
function TableData.GetDungeonRewardDataTable(priority)
	return Game.TableDataManager:GetData("DungeonRewardData", priority)
end


---@return _GuildPuzzleDataRow
function TableData.GetGuildPuzzleDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildPuzzleData", key, priority)
end


---@return _GuildPuzzleDataRow[]
function TableData.GetGuildPuzzleDataTable(priority)
	return Game.TableDataManager:GetData("GuildPuzzleData", priority)
end


---@return _GuildPartyRewardDataRow
function TableData.GetGuildPartyRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildPartyRewardData", key, priority)
end


---@return _GuildPartyRewardDataRow[]
function TableData.GetGuildPartyRewardDataTable(priority)
	return Game.TableDataManager:GetData("GuildPartyRewardData", priority)
end


---@return _GuildAnswerRegionDataRow
function TableData.GetGuildAnswerRegionDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildAnswerRegionData", key, priority)
end


---@return _GuildAnswerRegionDataRow[]
function TableData.GetGuildAnswerRegionDataTable(priority)
	return Game.TableDataManager:GetData("GuildAnswerRegionData", priority)
end


---@return _ManorItemMainTypeDataRow
function TableData.GetManorItemMainTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorItemMainTypeData", key, priority)
end


---@return _ManorItemMainTypeDataRow[]
function TableData.GetManorItemMainTypeDataTable(priority)
	return Game.TableDataManager:GetData("ManorItemMainTypeData", priority)
end


---@return _ManorItemSubTypeDataRow
function TableData.GetManorItemSubTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorItemSubTypeData", key, priority)
end


---@return _ManorItemSubTypeDataRow[]
function TableData.GetManorItemSubTypeDataTable(priority)
	return Game.TableDataManager:GetData("ManorItemSubTypeData", priority)
end


---@return _ManorCantPlaceDataRow
function TableData.GetManorCantPlaceDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorCantPlaceData", key, priority)
end


---@return _ManorCantPlaceDataRow[]
function TableData.GetManorCantPlaceDataTable(priority)
	return Game.TableDataManager:GetData("ManorCantPlaceData", priority)
end


---@return _ManorHighLightEffectDataRow
function TableData.GetManorHighLightEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorHighLightEffectData", key, priority)
end


---@return _ManorHighLightEffectDataRow[]
function TableData.GetManorHighLightEffectDataTable(priority)
	return Game.TableDataManager:GetData("ManorHighLightEffectData", priority)
end


---@return _ManorCantDeleteDataRow
function TableData.GetManorCantDeleteDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorCantDeleteData", key, priority)
end


---@return _ManorCantDeleteDataRow[]
function TableData.GetManorCantDeleteDataTable(priority)
	return Game.TableDataManager:GetData("ManorCantDeleteData", priority)
end


---@return _ManorItemSubSubTypeDataRow
function TableData.GetManorItemSubSubTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorItemSubSubTypeData", key, priority)
end


---@return _ManorItemSubSubTypeDataRow[]
function TableData.GetManorItemSubSubTypeDataTable(priority)
	return Game.TableDataManager:GetData("ManorItemSubSubTypeData", priority)
end


---@return _ManorItemDataRow
function TableData.GetManorItemDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorItemData", key, priority)
end


---@return _ManorItemDataRow[]
function TableData.GetManorItemDataTable(priority)
	return Game.TableDataManager:GetData("ManorItemData", priority)
end


---@return _ManorWorkshopDataRow
function TableData.GetManorWorkshopDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorWorkshopData", key, priority)
end


---@return _ManorWorkshopDataRow[]
function TableData.GetManorWorkshopDataTable(priority)
	return Game.TableDataManager:GetData("ManorWorkshopData", priority)
end


---@return _ManorSettingRow
function TableData.GetManorSettingRow(key, priority)
	return Game.TableDataManager:GetRow("ManorSetting", key, priority)
end


---@return _ManorSettingRow[]
function TableData.GetManorSettingTable(priority)
	return Game.TableDataManager:GetData("ManorSetting", priority)
end


---@return _ManorComponentDataRow
function TableData.GetManorComponentDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorComponentData", key, priority)
end


---@return _ManorComponentDataRow[]
function TableData.GetManorComponentDataTable(priority)
	return Game.TableDataManager:GetData("ManorComponentData", priority)
end


---@return _ManorConstDataRow
function TableData.GetManorConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorConstData", key, priority)
end


---@return _ManorConstDataRow[]
function TableData.GetManorConstDataTable(priority)
	return Game.TableDataManager:GetData("ManorConstData", priority)
end


---@return _ManorLevelUpDataRow
function TableData.GetManorLevelUpDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorLevelUpData", key, priority)
end


---@return _ManorLevelUpDataRow[]
function TableData.GetManorLevelUpDataTable(priority)
	return Game.TableDataManager:GetData("ManorLevelUpData", priority)
end


---@return _WorkshopLevelUpDataRow
function TableData.GetWorkshopLevelUpDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorkshopLevelUpData", key, priority)
end


---@return _WorkshopLevelUpDataRow[]
function TableData.GetWorkshopLevelUpDataTable(priority)
	return Game.TableDataManager:GetData("WorkshopLevelUpData", priority)
end


---@return _ManorRingDataRow
function TableData.GetManorRingDataRow(key, priority)
	return Game.TableDataManager:GetRow("ManorRingData", key, priority)
end


---@return _ManorRingDataRow[]
function TableData.GetManorRingDataTable(priority)
	return Game.TableDataManager:GetData("ManorRingData", priority)
end


---@return _WorkShopDataRow
function TableData.GetWorkShopDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorkShopData", key, priority)
end


---@return _WorkShopDataRow[]
function TableData.GetWorkShopDataTable(priority)
	return Game.TableDataManager:GetData("WorkShopData", priority)
end


---@return _WorkshopItemProduceDataRow
function TableData.GetWorkshopItemProduceDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorkshopItemProduceData", key, priority)
end


---@return _WorkshopItemProduceDataRow[]
function TableData.GetWorkshopItemProduceDataTable(priority)
	return Game.TableDataManager:GetData("WorkshopItemProduceData", priority)
end


---@return _WorkshopEmployeeDataRow
function TableData.GetWorkshopEmployeeDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorkshopEmployeeData", key, priority)
end


---@return _WorkshopEmployeeDataRow[]
function TableData.GetWorkshopEmployeeDataTable(priority)
	return Game.TableDataManager:GetData("WorkshopEmployeeData", priority)
end


---@return _NickNameLibDataRow
function TableData.GetNickNameLibDataRow(key, priority)
	return Game.TableDataManager:GetRow("NickNameLibData", key, priority)
end


---@return _NickNameLibDataRow[]
function TableData.GetNickNameLibDataTable(priority)
	return Game.TableDataManager:GetData("NickNameLibData", priority)
end


---@return _NicknameRandomPatternDataRow
function TableData.GetNicknameRandomPatternDataRow(key, priority)
	return Game.TableDataManager:GetRow("NicknameRandomPatternData", key, priority)
end


---@return _NicknameRandomPatternDataRow[]
function TableData.GetNicknameRandomPatternDataTable(priority)
	return Game.TableDataManager:GetData("NicknameRandomPatternData", priority)
end


---@return _SpecialNickNameDataRow
function TableData.GetSpecialNickNameDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpecialNickNameData", key, priority)
end


---@return _SpecialNickNameDataRow[]
function TableData.GetSpecialNickNameDataTable(priority)
	return Game.TableDataManager:GetData("SpecialNickNameData", priority)
end


---@return _LegalTextDataRow
function TableData.GetLegalTextDataRow(key, priority)
	return Game.TableDataManager:GetRow("LegalTextData", key, priority)
end


---@return _LegalTextDataRow[]
function TableData.GetLegalTextDataTable(priority)
	return Game.TableDataManager:GetData("LegalTextData", priority)
end


---@return _MonsterDataRow
function TableData.GetMonsterDataRow(key, priority)
	return Game.TableDataManager:GetRow("MonsterData", key, priority)
end


---@return _MonsterDataRow[]
function TableData.GetMonsterDataTable(priority)
	return Game.TableDataManager:GetData("MonsterData", priority)
end


---@return _ConstDataRow
function TableData.GetConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ConstData", key, priority)
end


---@return _ConstDataRow[]
function TableData.GetConstDataTable(priority)
	return Game.TableDataManager:GetData("ConstData", priority)
end


---@return _LevelPropInfoDataRow
function TableData.GetLevelPropInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelPropInfoData", key, priority)
end


---@return _LevelPropInfoDataRow[]
function TableData.GetLevelPropInfoDataTable(priority)
	return Game.TableDataManager:GetData("LevelPropInfoData", priority)
end


---@return _LevelConfigDataRow
function TableData.GetLevelConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelConfigData", key, priority)
end


---@return _LevelConfigDataRow[]
function TableData.GetLevelConfigDataTable(priority)
	return Game.TableDataManager:GetData("LevelConfigData", priority)
end


---@return _EquipmentConstDataRow
function TableData.GetEquipmentConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentConstData", key, priority)
end


---@return _EquipmentConstDataRow[]
function TableData.GetEquipmentConstDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentConstData", priority)
end


---@return _EquipmentTypeDataRow
function TableData.GetEquipmentTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentTypeData", key, priority)
end


---@return _EquipmentTypeDataRow[]
function TableData.GetEquipmentTypeDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentTypeData", priority)
end


---@return _EquipmentSlotDataRow
function TableData.GetEquipmentSlotDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentSlotData", key, priority)
end


---@return _EquipmentSlotDataRow[]
function TableData.GetEquipmentSlotDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentSlotData", priority)
end


---@return _EquipmentDataRow
function TableData.GetEquipmentDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentData", key, priority)
end


---@return _EquipmentDataRow[]
function TableData.GetEquipmentDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentData", priority)
end


---@return _EquipmentSuitDataRow
function TableData.GetEquipmentSuitDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentSuitData", key, priority)
end


---@return _EquipmentSuitDataRow[]
function TableData.GetEquipmentSuitDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentSuitData", priority)
end


---@return _EquipmentUniqueDataRow
function TableData.GetEquipmentUniqueDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentUniqueData", key, priority)
end


---@return _EquipmentUniqueDataRow[]
function TableData.GetEquipmentUniqueDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentUniqueData", priority)
end


---@return _EquipmentWordRarityDataRow
function TableData.GetEquipmentWordRarityDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordRarityData", key, priority)
end


---@return _EquipmentWordRarityDataRow[]
function TableData.GetEquipmentWordRarityDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordRarityData", priority)
end


---@return _EquipmentWordGroupTypeNameDataRow
function TableData.GetEquipmentWordGroupTypeNameDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordGroupTypeNameData", key, priority)
end


---@return _EquipmentWordGroupTypeNameDataRow[]
function TableData.GetEquipmentWordGroupTypeNameDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordGroupTypeNameData", priority)
end


---@return _EquipmentWordSetDataRow
function TableData.GetEquipmentWordSetDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordSetData", key, priority)
end


---@return _EquipmentWordSetDataRow[]
function TableData.GetEquipmentWordSetDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordSetData", priority)
end


---@return _EquipmentWordRandomWordDataRow
function TableData.GetEquipmentWordRandomWordDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordRandomWordData", key, priority)
end


---@return _EquipmentWordRandomWordDataRow[]
function TableData.GetEquipmentWordRandomWordDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordRandomWordData", priority)
end


---@return _EquipmentWordRandomGroupDataRow
function TableData.GetEquipmentWordRandomGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordRandomGroupData", key, priority)
end


---@return _EquipmentWordRandomGroupDataRow[]
function TableData.GetEquipmentWordRandomGroupDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordRandomGroupData", priority)
end


---@return _EquipmentWordInitRandomGroupDataRow
function TableData.GetEquipmentWordInitRandomGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordInitRandomGroupData", key, priority)
end


---@return _EquipmentWordInitRandomGroupDataRow[]
function TableData.GetEquipmentWordInitRandomGroupDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordInitRandomGroupData", priority)
end


---@return _EquipmentWordRandomClassDataRow
function TableData.GetEquipmentWordRandomClassDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentWordRandomClassData", key, priority)
end


---@return _EquipmentWordRandomClassDataRow[]
function TableData.GetEquipmentWordRandomClassDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentWordRandomClassData", priority)
end


---@return _EquipRandomPropGuaranteeDataRow
function TableData.GetEquipRandomPropGuaranteeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipRandomPropGuaranteeData", key, priority)
end


---@return _EquipRandomPropGuaranteeDataRow[]
function TableData.GetEquipRandomPropGuaranteeDataTable(priority)
	return Game.TableDataManager:GetData("EquipRandomPropGuaranteeData", priority)
end


---@return _EquipRandomGroupGuaranteeDataRow
function TableData.GetEquipRandomGroupGuaranteeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipRandomGroupGuaranteeData", key, priority)
end


---@return _EquipRandomGroupGuaranteeDataRow[]
function TableData.GetEquipRandomGroupGuaranteeDataTable(priority)
	return Game.TableDataManager:GetData("EquipRandomGroupGuaranteeData", priority)
end


---@return _EquipmentSpiritualityConvergenceDataRow
function TableData.GetEquipmentSpiritualityConvergenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentSpiritualityConvergenceData", key, priority)
end


---@return _EquipmentSpiritualityConvergenceDataRow[]
function TableData.GetEquipmentSpiritualityConvergenceDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentSpiritualityConvergenceData", priority)
end


---@return _EquipmentGrowConstDataRow
function TableData.GetEquipmentGrowConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowConstData", key, priority)
end


---@return _EquipmentGrowConstDataRow[]
function TableData.GetEquipmentGrowConstDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowConstData", priority)
end


---@return _EquipmentGrowNewConstDataRow
function TableData.GetEquipmentGrowNewConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowNewConstData", key, priority)
end


---@return _EquipmentGrowNewConstDataRow[]
function TableData.GetEquipmentGrowNewConstDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowNewConstData", priority)
end


---@return _EquipmentGrowBodyConfigDataRow
function TableData.GetEquipmentGrowBodyConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowBodyConfigData", key, priority)
end


---@return _EquipmentGrowBodyConfigDataRow[]
function TableData.GetEquipmentGrowBodyConfigDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowBodyConfigData", priority)
end


---@return _EquipmentGrowBodyEnhanceDataRow
function TableData.GetEquipmentGrowBodyEnhanceDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowBodyEnhanceData", key, priority)
end


---@return _EquipmentGrowBodyEnhanceDataRow[]
function TableData.GetEquipmentGrowBodyEnhanceDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowBodyEnhanceData", priority)
end


---@return _EquipmentGrowEnhancePropDataRow
function TableData.GetEquipmentGrowEnhancePropDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowEnhancePropData", key, priority)
end


---@return _EquipmentGrowEnhancePropDataRow[]
function TableData.GetEquipmentGrowEnhancePropDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowEnhancePropData", priority)
end


---@return _EquipmentGrowBodySuitDataRow
function TableData.GetEquipmentGrowBodySuitDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowBodySuitData", key, priority)
end


---@return _EquipmentGrowBodySuitDataRow[]
function TableData.GetEquipmentGrowBodySuitDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowBodySuitData", priority)
end


---@return _EquipmentGrowSeasonConsumeDataRow
function TableData.GetEquipmentGrowSeasonConsumeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipmentGrowSeasonConsumeData", key, priority)
end


---@return _EquipmentGrowSeasonConsumeDataRow[]
function TableData.GetEquipmentGrowSeasonConsumeDataTable(priority)
	return Game.TableDataManager:GetData("EquipmentGrowSeasonConsumeData", priority)
end


---@return _EquipRandomReformUnlockDataRow
function TableData.GetEquipRandomReformUnlockDataRow(key, priority)
	return Game.TableDataManager:GetRow("EquipRandomReformUnlockData", key, priority)
end


---@return _EquipRandomReformUnlockDataRow[]
function TableData.GetEquipRandomReformUnlockDataTable(priority)
	return Game.TableDataManager:GetData("EquipRandomReformUnlockData", priority)
end


---@return _SkillTabDataRow
function TableData.GetSkillTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillTabData", key, priority)
end


---@return _SkillTabDataRow[]
function TableData.GetSkillTabDataTable(priority)
	return Game.TableDataManager:GetData("SkillTabData", priority)
end


---@return _RoleSkillUnlockDataRow
function TableData.GetRoleSkillUnlockDataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleSkillUnlockData", key, priority)
end


---@return _RoleSkillUnlockDataRow[]
function TableData.GetRoleSkillUnlockDataTable(priority)
	return Game.TableDataManager:GetData("RoleSkillUnlockData", priority)
end


---@return _FellowSkillUnlockDataRow
function TableData.GetFellowSkillUnlockDataRow(key, priority)
	return Game.TableDataManager:GetRow("FellowSkillUnlockData", key, priority)
end


---@return _FellowSkillUnlockDataRow[]
function TableData.GetFellowSkillUnlockDataTable(priority)
	return Game.TableDataManager:GetData("FellowSkillUnlockData", priority)
end


---@return _SkillTypeDataRow
function TableData.GetSkillTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillTypeData", key, priority)
end


---@return _SkillTypeDataRow[]
function TableData.GetSkillTypeDataTable(priority)
	return Game.TableDataManager:GetData("SkillTypeData", priority)
end


---@return _SkillLvlUpConsumeDataRow
function TableData.GetSkillLvlUpConsumeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillLvlUpConsumeData", key, priority)
end


---@return _SkillLvlUpConsumeDataRow[]
function TableData.GetSkillLvlUpConsumeDataTable(priority)
	return Game.TableDataManager:GetData("SkillLvlUpConsumeData", priority)
end


---@return _SkillLevelUpDataRow
function TableData.GetSkillLevelUpDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillLevelUpData", key, priority)
end


---@return _SkillLevelUpDataRow[]
function TableData.GetSkillLevelUpDataTable(priority)
	return Game.TableDataManager:GetData("SkillLevelUpData", priority)
end


---@return _SkillPresetDataRow
function TableData.GetSkillPresetDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillPresetData", key, priority)
end


---@return _SkillPresetDataRow[]
function TableData.GetSkillPresetDataTable(priority)
	return Game.TableDataManager:GetData("SkillPresetData", priority)
end


---@return _SkillUpgradeGroupDataRow
function TableData.GetSkillUpgradeGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillUpgradeGroupData", key, priority)
end


---@return _SkillUpgradeGroupDataRow[]
function TableData.GetSkillUpgradeGroupDataTable(priority)
	return Game.TableDataManager:GetData("SkillUpgradeGroupData", priority)
end


---@return _SkillCustomizerIntConstDataRow
function TableData.GetSkillCustomizerIntConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillCustomizerIntConstData", key, priority)
end


---@return _SkillCustomizerIntConstDataRow[]
function TableData.GetSkillCustomizerIntConstDataTable(priority)
	return Game.TableDataManager:GetData("SkillCustomizerIntConstData", priority)
end


---@return _SkillCustomizerFloatConstDataRow
function TableData.GetSkillCustomizerFloatConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillCustomizerFloatConstData", key, priority)
end


---@return _SkillCustomizerFloatConstDataRow[]
function TableData.GetSkillCustomizerFloatConstDataTable(priority)
	return Game.TableDataManager:GetData("SkillCustomizerFloatConstData", priority)
end


---@return _SwitchSkillGroupDataRow
function TableData.GetSwitchSkillGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("SwitchSkillGroupData", key, priority)
end


---@return _SwitchSkillGroupDataRow[]
function TableData.GetSwitchSkillGroupDataTable(priority)
	return Game.TableDataManager:GetData("SwitchSkillGroupData", priority)
end


---@return _ProfessionStateDataRow
function TableData.GetProfessionStateDataRow(key, priority)
	return Game.TableDataManager:GetRow("ProfessionStateData", key, priority)
end


---@return _ProfessionStateDataRow[]
function TableData.GetProfessionStateDataTable(priority)
	return Game.TableDataManager:GetData("ProfessionStateData", priority)
end


---@return _SkillTraitsDataRow
function TableData.GetSkillTraitsDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillTraitsData", key, priority)
end


---@return _SkillTraitsDataRow[]
function TableData.GetSkillTraitsDataTable(priority)
	return Game.TableDataManager:GetData("SkillTraitsData", priority)
end


---@return _SkillTagDataRow
function TableData.GetSkillTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillTagData", key, priority)
end


---@return _SkillTagDataRow[]
function TableData.GetSkillTagDataTable(priority)
	return Game.TableDataManager:GetData("SkillTagData", priority)
end


---@return _HitActionDefineDataRow
function TableData.GetHitActionDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("HitActionDefineData", key, priority)
end


---@return _HitActionDefineDataRow[]
function TableData.GetHitActionDefineDataTable(priority)
	return Game.TableDataManager:GetData("HitActionDefineData", priority)
end


---@return _HitConstantDataRow
function TableData.GetHitConstantDataRow(key, priority)
	return Game.TableDataManager:GetRow("HitConstantData", key, priority)
end


---@return _HitConstantDataRow[]
function TableData.GetHitConstantDataTable(priority)
	return Game.TableDataManager:GetData("HitConstantData", priority)
end


---@return _BuffControlDefineDataRow
function TableData.GetBuffControlDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("BuffControlDefineData", key, priority)
end


---@return _BuffControlDefineDataRow[]
function TableData.GetBuffControlDefineDataTable(priority)
	return Game.TableDataManager:GetData("BuffControlDefineData", priority)
end


---@return _HitAnimlibParamsDataRow
function TableData.GetHitAnimlibParamsDataRow(key, priority)
	return Game.TableDataManager:GetRow("HitAnimlibParamsData", key, priority)
end


---@return _HitAnimlibParamsDataRow[]
function TableData.GetHitAnimlibParamsDataTable(priority)
	return Game.TableDataManager:GetData("HitAnimlibParamsData", priority)
end


---@return _AttackAnimlibDataRow
function TableData.GetAttackAnimlibDataRow(key, priority)
	return Game.TableDataManager:GetRow("AttackAnimlibData", key, priority)
end


---@return _AttackAnimlibDataRow[]
function TableData.GetAttackAnimlibDataTable(priority)
	return Game.TableDataManager:GetData("AttackAnimlibData", priority)
end


---@return _ControlReminderDataRow
function TableData.GetControlReminderDataRow(key, priority)
	return Game.TableDataManager:GetRow("ControlReminderData", key, priority)
end


---@return _ControlReminderDataRow[]
function TableData.GetControlReminderDataTable(priority)
	return Game.TableDataManager:GetData("ControlReminderData", priority)
end


---@return _HitStateConflictDataRow
function TableData.GetHitStateConflictDataRow(key, priority)
	return Game.TableDataManager:GetRow("HitStateConflictData", key, priority)
end


---@return _HitStateConflictDataRow[]
function TableData.GetHitStateConflictDataTable(priority)
	return Game.TableDataManager:GetData("HitStateConflictData", priority)
end


---@return _HitFallBackDataRow
function TableData.GetHitFallBackDataRow(key, priority)
	return Game.TableDataManager:GetRow("HitFallBackData", key, priority)
end


---@return _HitFallBackDataRow[]
function TableData.GetHitFallBackDataTable(priority)
	return Game.TableDataManager:GetData("HitFallBackData", priority)
end


---@return _AttackTypeDataRow
function TableData.GetAttackTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("AttackTypeData", key, priority)
end


---@return _AttackTypeDataRow[]
function TableData.GetAttackTypeDataTable(priority)
	return Game.TableDataManager:GetData("AttackTypeData", priority)
end


---@return _AttackTypeSettingDataRow
function TableData.GetAttackTypeSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("AttackTypeSettingData", key, priority)
end


---@return _AttackTypeSettingDataRow[]
function TableData.GetAttackTypeSettingDataTable(priority)
	return Game.TableDataManager:GetData("AttackTypeSettingData", priority)
end


---@return _AttackSlomoDataRow
function TableData.GetAttackSlomoDataRow(key, priority)
	return Game.TableDataManager:GetRow("AttackSlomoData", key, priority)
end


---@return _AttackSlomoDataRow[]
function TableData.GetAttackSlomoDataTable(priority)
	return Game.TableDataManager:GetData("AttackSlomoData", priority)
end


---@return _CampDefineDataRow
function TableData.GetCampDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("CampDefineData", key, priority)
end


---@return _CampDefineDataRow[]
function TableData.GetCampDefineDataTable(priority)
	return Game.TableDataManager:GetData("CampDefineData", priority)
end


---@return _CampRelationDataRow
function TableData.GetCampRelationDataRow(key, priority)
	return Game.TableDataManager:GetRow("CampRelationData", key, priority)
end


---@return _CampRelationDataRow[]
function TableData.GetCampRelationDataTable(priority)
	return Game.TableDataManager:GetData("CampRelationData", priority)
end


---@return _CampEnumDataRow
function TableData.GetCampEnumDataRow(key, priority)
	return Game.TableDataManager:GetRow("CampEnumData", key, priority)
end


---@return _CampEnumDataRow[]
function TableData.GetCampEnumDataTable(priority)
	return Game.TableDataManager:GetData("CampEnumData", priority)
end


---@return _CampConstIntDataRow
function TableData.GetCampConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("CampConstIntData", key, priority)
end


---@return _CampConstIntDataRow[]
function TableData.GetCampConstIntDataTable(priority)
	return Game.TableDataManager:GetData("CampConstIntData", priority)
end


---@return _LoadingPresetDataRow
function TableData.GetLoadingPresetDataRow(key, priority)
	return Game.TableDataManager:GetRow("LoadingPresetData", key, priority)
end


---@return _LoadingPresetDataRow[]
function TableData.GetLoadingPresetDataTable(priority)
	return Game.TableDataManager:GetData("LoadingPresetData", priority)
end


---@return _LoadingImgsDataRow
function TableData.GetLoadingImgsDataRow(key, priority)
	return Game.TableDataManager:GetRow("LoadingImgsData", key, priority)
end


---@return _LoadingImgsDataRow[]
function TableData.GetLoadingImgsDataTable(priority)
	return Game.TableDataManager:GetData("LoadingImgsData", priority)
end


---@return _LoadingTipsDataRow
function TableData.GetLoadingTipsDataRow(key, priority)
	return Game.TableDataManager:GetRow("LoadingTipsData", key, priority)
end


---@return _LoadingTipsDataRow[]
function TableData.GetLoadingTipsDataTable(priority)
	return Game.TableDataManager:GetData("LoadingTipsData", priority)
end


---@return _NpcMonsterTemplateDataRow
function TableData.GetNpcMonsterTemplateDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcMonsterTemplateData", key, priority)
end


---@return _NpcMonsterTemplateDataRow[]
function TableData.GetNpcMonsterTemplateDataTable(priority)
	return Game.TableDataManager:GetData("NpcMonsterTemplateData", priority)
end


---@return _NpcInfoDataRow
function TableData.GetNpcInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcInfoData", key, priority)
end


---@return _NpcInfoDataRow[]
function TableData.GetNpcInfoDataTable(priority)
	return Game.TableDataManager:GetData("NpcInfoData", priority)
end


---@return _TalkDataRow
function TableData.GetTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalkData", key, priority)
end


---@return _TalkDataRow[]
function TableData.GetTalkDataTable(priority)
	return Game.TableDataManager:GetData("TalkData", priority)
end


---@return _TalkGroupDataRow
function TableData.GetTalkGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalkGroupData", key, priority)
end


---@return _TalkGroupDataRow[]
function TableData.GetTalkGroupDataTable(priority)
	return Game.TableDataManager:GetData("TalkGroupData", priority)
end


---@return _NpcTypeDataRow
function TableData.GetNpcTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcTypeData", key, priority)
end


---@return _NpcTypeDataRow[]
function TableData.GetNpcTypeDataTable(priority)
	return Game.TableDataManager:GetData("NpcTypeData", priority)
end


---@return _GossipGroupDataRow
function TableData.GetGossipGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("GossipGroupData", key, priority)
end


---@return _GossipGroupDataRow[]
function TableData.GetGossipGroupDataTable(priority)
	return Game.TableDataManager:GetData("GossipGroupData", priority)
end


---@return _GossipDataRow
function TableData.GetGossipDataRow(key, priority)
	return Game.TableDataManager:GetRow("GossipData", key, priority)
end


---@return _GossipDataRow[]
function TableData.GetGossipDataTable(priority)
	return Game.TableDataManager:GetData("GossipData", priority)
end


---@return _AsideTalkDataRow
function TableData.GetAsideTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("AsideTalkData", key, priority)
end


---@return _AsideTalkDataRow[]
function TableData.GetAsideTalkDataTable(priority)
	return Game.TableDataManager:GetData("AsideTalkData", priority)
end


---@return _BubbleDataRow
function TableData.GetBubbleDataRow(key, priority)
	return Game.TableDataManager:GetRow("BubbleData", key, priority)
end


---@return _BubbleDataRow[]
function TableData.GetBubbleDataTable(priority)
	return Game.TableDataManager:GetData("BubbleData", priority)
end


---@return _DialogueOptionTextRow
function TableData.GetDialogueOptionTextRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueOptionText", key, priority)
end


---@return _DialogueOptionTextRow[]
function TableData.GetDialogueOptionTextTable(priority)
	return Game.TableDataManager:GetData("DialogueOptionText", priority)
end


---@return _DialogueTalkDataRow
function TableData.GetDialogueTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueTalkData", key, priority)
end


---@return _DialogueTalkDataRow[]
function TableData.GetDialogueTalkDataTable(priority)
	return Game.TableDataManager:GetData("DialogueTalkData", priority)
end


---@return _DialogueAssetDataRow
function TableData.GetDialogueAssetDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueAssetData", key, priority)
end


---@return _DialogueAssetDataRow[]
function TableData.GetDialogueAssetDataTable(priority)
	return Game.TableDataManager:GetData("DialogueAssetData", priority)
end


---@return _DialogueBlackScreenTextRow
function TableData.GetDialogueBlackScreenTextRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueBlackScreenText", key, priority)
end


---@return _DialogueBlackScreenTextRow[]
function TableData.GetDialogueBlackScreenTextTable(priority)
	return Game.TableDataManager:GetData("DialogueBlackScreenText", priority)
end


---@return _DialogueActorDescriptionDataRow
function TableData.GetDialogueActorDescriptionDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueActorDescriptionData", key, priority)
end


---@return _DialogueActorDescriptionDataRow[]
function TableData.GetDialogueActorDescriptionDataTable(priority)
	return Game.TableDataManager:GetData("DialogueActorDescriptionData", priority)
end


---@return _PasserbyNpcDataRow
function TableData.GetPasserbyNpcDataRow(key, priority)
	return Game.TableDataManager:GetRow("PasserbyNpcData", key, priority)
end


---@return _PasserbyNpcDataRow[]
function TableData.GetPasserbyNpcDataTable(priority)
	return Game.TableDataManager:GetData("PasserbyNpcData", priority)
end


---@return _VehicleNpcDataRow
function TableData.GetVehicleNpcDataRow(key, priority)
	return Game.TableDataManager:GetRow("VehicleNpcData", key, priority)
end


---@return _VehicleNpcDataRow[]
function TableData.GetVehicleNpcDataTable(priority)
	return Game.TableDataManager:GetData("VehicleNpcData", priority)
end


---@return _CommonEffectDataRow
function TableData.GetCommonEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonEffectData", key, priority)
end


---@return _CommonEffectDataRow[]
function TableData.GetCommonEffectDataTable(priority)
	return Game.TableDataManager:GetData("CommonEffectData", priority)
end


---@return _TaskChapterRewardDataRow
function TableData.GetTaskChapterRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskChapterRewardData", key, priority)
end


---@return _TaskChapterRewardDataRow[]
function TableData.GetTaskChapterRewardDataTable(priority)
	return Game.TableDataManager:GetData("TaskChapterRewardData", priority)
end


---@return _TargetGuideInfoDataRow
function TableData.GetTargetGuideInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("TargetGuideInfoData", key, priority)
end


---@return _TargetGuideInfoDataRow[]
function TableData.GetTargetGuideInfoDataTable(priority)
	return Game.TableDataManager:GetData("TargetGuideInfoData", priority)
end


---@return _GuildDanceGuideDataRow
function TableData.GetGuildDanceGuideDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildDanceGuideData", key, priority)
end


---@return _GuildDanceGuideDataRow[]
function TableData.GetGuildDanceGuideDataTable(priority)
	return Game.TableDataManager:GetData("GuildDanceGuideData", priority)
end


---@return _FortuityDataRow
function TableData.GetFortuityDataRow(key, priority)
	return Game.TableDataManager:GetRow("FortuityData", key, priority)
end


---@return _FortuityDataRow[]
function TableData.GetFortuityDataTable(priority)
	return Game.TableDataManager:GetData("FortuityData", priority)
end


---@return _FortuityIntConstDataRow
function TableData.GetFortuityIntConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("FortuityIntConstData", key, priority)
end


---@return _FortuityIntConstDataRow[]
function TableData.GetFortuityIntConstDataTable(priority)
	return Game.TableDataManager:GetData("FortuityIntConstData", priority)
end


---@return _TaskRewardDataRow
function TableData.GetTaskRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskRewardData", key, priority)
end


---@return _TaskRewardDataRow[]
function TableData.GetTaskRewardDataTable(priority)
	return Game.TableDataManager:GetData("TaskRewardData", priority)
end


---@return _TaskConstDataRow
function TableData.GetTaskConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskConstData", key, priority)
end


---@return _TaskConstDataRow[]
function TableData.GetTaskConstDataTable(priority)
	return Game.TableDataManager:GetData("TaskConstData", priority)
end


---@return _TaskFilterDataRow
function TableData.GetTaskFilterDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskFilterData", key, priority)
end


---@return _TaskFilterDataRow[]
function TableData.GetTaskFilterDataTable(priority)
	return Game.TableDataManager:GetData("TaskFilterData", priority)
end


---@return _TaskMiniTypeDataRow
function TableData.GetTaskMiniTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskMiniTypeData", key, priority)
end


---@return _TaskMiniTypeDataRow[]
function TableData.GetTaskMiniTypeDataTable(priority)
	return Game.TableDataManager:GetData("TaskMiniTypeData", priority)
end


---@return _MainChapterPerformDataRow
function TableData.GetMainChapterPerformDataRow(key, priority)
	return Game.TableDataManager:GetRow("MainChapterPerformData", key, priority)
end


---@return _MainChapterPerformDataRow[]
function TableData.GetMainChapterPerformDataTable(priority)
	return Game.TableDataManager:GetData("MainChapterPerformData", priority)
end


---@return _NpcHideDataRow
function TableData.GetNpcHideDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcHideData", key, priority)
end


---@return _NpcHideDataRow[]
function TableData.GetNpcHideDataTable(priority)
	return Game.TableDataManager:GetData("NpcHideData", priority)
end


---@return _TaskCollectHideDataRow
function TableData.GetTaskCollectHideDataRow(key, priority)
	return Game.TableDataManager:GetRow("TaskCollectHideData", key, priority)
end


---@return _TaskCollectHideDataRow[]
function TableData.GetTaskCollectHideDataTable(priority)
	return Game.TableDataManager:GetData("TaskCollectHideData", priority)
end


---@return _PlayerCreateDataRow
function TableData.GetPlayerCreateDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerCreateData", key, priority)
end


---@return _PlayerCreateDataRow[]
function TableData.GetPlayerCreateDataTable(priority)
	return Game.TableDataManager:GetData("PlayerCreateData", priority)
end


---@return _PlayerSocialDisplayDataRow
function TableData.GetPlayerSocialDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerSocialDisplayData", key, priority)
end


---@return _PlayerSocialDisplayDataRow[]
function TableData.GetPlayerSocialDisplayDataTable(priority)
	return Game.TableDataManager:GetData("PlayerSocialDisplayData", priority)
end


---@return _PlayerBattleDataRow
function TableData.GetPlayerBattleDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerBattleData", key, priority)
end


---@return _PlayerBattleDataRow[]
function TableData.GetPlayerBattleDataTable(priority)
	return Game.TableDataManager:GetData("PlayerBattleData", priority)
end


---@return _PlayerPropTransDataRow
function TableData.GetPlayerPropTransDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerPropTransData", key, priority)
end


---@return _PlayerPropTransDataRow[]
function TableData.GetPlayerPropTransDataTable(priority)
	return Game.TableDataManager:GetData("PlayerPropTransData", priority)
end


---@return _PlayerInitialConstDataRow
function TableData.GetPlayerInitialConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerInitialConstData", key, priority)
end


---@return _PlayerInitialConstDataRow[]
function TableData.GetPlayerInitialConstDataTable(priority)
	return Game.TableDataManager:GetData("PlayerInitialConstData", priority)
end


---@return _RoleCreateQandADataRow
function TableData.GetRoleCreateQandADataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleCreateQandAData", key, priority)
end


---@return _RoleCreateQandADataRow[]
function TableData.GetRoleCreateQandADataTable(priority)
	return Game.TableDataManager:GetData("RoleCreateQandAData", priority)
end


---@return _RoleCreateItemConfigDataRow
function TableData.GetRoleCreateItemConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleCreateItemConfigData", key, priority)
end


---@return _RoleCreateItemConfigDataRow[]
function TableData.GetRoleCreateItemConfigDataTable(priority)
	return Game.TableDataManager:GetData("RoleCreateItemConfigData", priority)
end


---@return _RoleCreatePropConfigDataRow
function TableData.GetRoleCreatePropConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleCreatePropConfigData", key, priority)
end


---@return _RoleCreatePropConfigDataRow[]
function TableData.GetRoleCreatePropConfigDataTable(priority)
	return Game.TableDataManager:GetData("RoleCreatePropConfigData", priority)
end


---@return _RoleCreateParamConstDataRow
function TableData.GetRoleCreateParamConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleCreateParamConstData", key, priority)
end


---@return _RoleCreateParamConstDataRow[]
function TableData.GetRoleCreateParamConstDataTable(priority)
	return Game.TableDataManager:GetData("RoleCreateParamConstData", priority)
end


---@return _CEShowDataRow
function TableData.GetCEShowDataRow(key, priority)
	return Game.TableDataManager:GetRow("CEShowData", key, priority)
end


---@return _CEShowDataRow[]
function TableData.GetCEShowDataTable(priority)
	return Game.TableDataManager:GetData("CEShowData", priority)
end


---@return _FontSettingDataRow
function TableData.GetFontSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("FontSettingData", key, priority)
end


---@return _FontSettingDataRow[]
function TableData.GetFontSettingDataTable(priority)
	return Game.TableDataManager:GetData("FontSettingData", priority)
end


---@return _ConstSettingDataRow
function TableData.GetConstSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("ConstSettingData", key, priority)
end


---@return _ConstSettingDataRow[]
function TableData.GetConstSettingDataTable(priority)
	return Game.TableDataManager:GetData("ConstSettingData", priority)
end


---@return _NPCInteractDataRow
function TableData.GetNPCInteractDataRow(key, priority)
	return Game.TableDataManager:GetRow("NPCInteractData", key, priority)
end


---@return _NPCInteractDataRow[]
function TableData.GetNPCInteractDataTable(priority)
	return Game.TableDataManager:GetData("NPCInteractData", priority)
end


---@return _NPCSPInteractDataRow
function TableData.GetNPCSPInteractDataRow(key, priority)
	return Game.TableDataManager:GetRow("NPCSPInteractData", key, priority)
end


---@return _NPCSPInteractDataRow[]
function TableData.GetNPCSPInteractDataTable(priority)
	return Game.TableDataManager:GetData("NPCSPInteractData", priority)
end


---@return _PhyBriefDataRow
function TableData.GetPhyBriefDataRow(key, priority)
	return Game.TableDataManager:GetRow("PhyBriefData", key, priority)
end


---@return _PhyBriefDataRow[]
function TableData.GetPhyBriefDataTable(priority)
	return Game.TableDataManager:GetData("PhyBriefData", priority)
end


---@return _PhyDetailDataRow
function TableData.GetPhyDetailDataRow(key, priority)
	return Game.TableDataManager:GetRow("PhyDetailData", key, priority)
end


---@return _PhyDetailDataRow[]
function TableData.GetPhyDetailDataTable(priority)
	return Game.TableDataManager:GetData("PhyDetailData", priority)
end


---@return _MagBriefDataRow
function TableData.GetMagBriefDataRow(key, priority)
	return Game.TableDataManager:GetRow("MagBriefData", key, priority)
end


---@return _MagBriefDataRow[]
function TableData.GetMagBriefDataTable(priority)
	return Game.TableDataManager:GetData("MagBriefData", priority)
end


---@return _MagDetailDataRow
function TableData.GetMagDetailDataRow(key, priority)
	return Game.TableDataManager:GetRow("MagDetailData", key, priority)
end


---@return _MagDetailDataRow[]
function TableData.GetMagDetailDataTable(priority)
	return Game.TableDataManager:GetData("MagDetailData", priority)
end


---@return _CommonBriefDataRow
function TableData.GetCommonBriefDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonBriefData", key, priority)
end


---@return _CommonBriefDataRow[]
function TableData.GetCommonBriefDataTable(priority)
	return Game.TableDataManager:GetData("CommonBriefData", priority)
end


---@return _PropTitleDataRow
function TableData.GetPropTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("PropTitleData", key, priority)
end


---@return _PropTitleDataRow[]
function TableData.GetPropTitleDataTable(priority)
	return Game.TableDataManager:GetData("PropTitleData", priority)
end


---@return _ShowCurrencyDataRow
function TableData.GetShowCurrencyDataRow(key, priority)
	return Game.TableDataManager:GetRow("ShowCurrencyData", key, priority)
end


---@return _ShowCurrencyDataRow[]
function TableData.GetShowCurrencyDataTable(priority)
	return Game.TableDataManager:GetData("ShowCurrencyData", priority)
end


---@return _CutPriceDataRow
function TableData.GetCutPriceDataRow(key, priority)
	return Game.TableDataManager:GetRow("CutPriceData", key, priority)
end


---@return _CutPriceDataRow[]
function TableData.GetCutPriceDataTable(priority)
	return Game.TableDataManager:GetData("CutPriceData", priority)
end


---@return _CutPriceSettingDataRow
function TableData.GetCutPriceSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("CutPriceSettingData", key, priority)
end


---@return _CutPriceSettingDataRow[]
function TableData.GetCutPriceSettingDataTable(priority)
	return Game.TableDataManager:GetData("CutPriceSettingData", priority)
end


---@return _MoodSettingDataRow
function TableData.GetMoodSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("MoodSettingData", key, priority)
end


---@return _MoodSettingDataRow[]
function TableData.GetMoodSettingDataTable(priority)
	return Game.TableDataManager:GetData("MoodSettingData", priority)
end


---@return _RarityDataRow
function TableData.GetRarityDataRow(key, priority)
	return Game.TableDataManager:GetRow("RarityData", key, priority)
end


---@return _RarityDataRow[]
function TableData.GetRarityDataTable(priority)
	return Game.TableDataManager:GetData("RarityData", priority)
end


---@return _SystemMailDataRow
function TableData.GetSystemMailDataRow(key, priority)
	return Game.TableDataManager:GetRow("SystemMailData", key, priority)
end


---@return _SystemMailDataRow[]
function TableData.GetSystemMailDataTable(priority)
	return Game.TableDataManager:GetData("SystemMailData", priority)
end


---@return _MailSenderDataRow
function TableData.GetMailSenderDataRow(key, priority)
	return Game.TableDataManager:GetRow("MailSenderData", key, priority)
end


---@return _MailSenderDataRow[]
function TableData.GetMailSenderDataTable(priority)
	return Game.TableDataManager:GetData("MailSenderData", priority)
end


---@return _MailConstIntDataRow
function TableData.GetMailConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("MailConstIntData", key, priority)
end


---@return _MailConstIntDataRow[]
function TableData.GetMailConstIntDataTable(priority)
	return Game.TableDataManager:GetData("MailConstIntData", priority)
end


---@return _LetterTextDataRow
function TableData.GetLetterTextDataRow(key, priority)
	return Game.TableDataManager:GetRow("LetterTextData", key, priority)
end


---@return _LetterTextDataRow[]
function TableData.GetLetterTextDataTable(priority)
	return Game.TableDataManager:GetData("LetterTextData", priority)
end


---@return _BookContentDataRow
function TableData.GetBookContentDataRow(key, priority)
	return Game.TableDataManager:GetRow("BookContentData", key, priority)
end


---@return _BookContentDataRow[]
function TableData.GetBookContentDataTable(priority)
	return Game.TableDataManager:GetData("BookContentData", priority)
end


---@return _WordMapConstDataRow
function TableData.GetWordMapConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("WordMapConstData", key, priority)
end


---@return _WordMapConstDataRow[]
function TableData.GetWordMapConstDataTable(priority)
	return Game.TableDataManager:GetData("WordMapConstData", priority)
end


---@return _SceneActorPaintingScratchDataRow
function TableData.GetSceneActorPaintingScratchDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorPaintingScratchData", key, priority)
end


---@return _SceneActorPaintingScratchDataRow[]
function TableData.GetSceneActorPaintingScratchDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorPaintingScratchData", priority)
end


---@return _NewspaperDataRow
function TableData.GetNewspaperDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewspaperData", key, priority)
end


---@return _NewspaperDataRow[]
function TableData.GetNewspaperDataTable(priority)
	return Game.TableDataManager:GetData("NewspaperData", priority)
end


---@return _OfferDataRow
function TableData.GetOfferDataRow(key, priority)
	return Game.TableDataManager:GetRow("OfferData", key, priority)
end


---@return _OfferDataRow[]
function TableData.GetOfferDataTable(priority)
	return Game.TableDataManager:GetData("OfferData", priority)
end


---@return _StoryRolePlayDataRow
function TableData.GetStoryRolePlayDataRow(key, priority)
	return Game.TableDataManager:GetRow("StoryRolePlayData", key, priority)
end


---@return _StoryRolePlayDataRow[]
function TableData.GetStoryRolePlayDataTable(priority)
	return Game.TableDataManager:GetData("StoryRolePlayData", priority)
end


---@return _TextTipsDataRow
function TableData.GetTextTipsDataRow(key, priority)
	return Game.TableDataManager:GetRow("TextTipsData", key, priority)
end


---@return _TextTipsDataRow[]
function TableData.GetTextTipsDataTable(priority)
	return Game.TableDataManager:GetData("TextTipsData", priority)
end


---@return _RussellDiaryDataRow
function TableData.GetRussellDiaryDataRow(key, priority)
	return Game.TableDataManager:GetRow("RussellDiaryData", key, priority)
end


---@return _RussellDiaryDataRow[]
function TableData.GetRussellDiaryDataTable(priority)
	return Game.TableDataManager:GetData("RussellDiaryData", priority)
end


---@return _PoliceArchiveDataRow
function TableData.GetPoliceArchiveDataRow(key, priority)
	return Game.TableDataManager:GetRow("PoliceArchiveData", key, priority)
end


---@return _PoliceArchiveDataRow[]
function TableData.GetPoliceArchiveDataTable(priority)
	return Game.TableDataManager:GetData("PoliceArchiveData", priority)
end


---@return _DropInteractorRow
function TableData.GetDropInteractorRow(key, priority)
	return Game.TableDataManager:GetRow("DropInteractor", key, priority)
end


---@return _DropInteractorRow[]
function TableData.GetDropInteractorTable(priority)
	return Game.TableDataManager:GetData("DropInteractor", priority)
end


---@return _InteractorIntConstDataRow
function TableData.GetInteractorIntConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("InteractorIntConstData", key, priority)
end


---@return _InteractorIntConstDataRow[]
function TableData.GetInteractorIntConstDataTable(priority)
	return Game.TableDataManager:GetData("InteractorIntConstData", priority)
end


---@return _InteractorUITemplateRow
function TableData.GetInteractorUITemplateRow(key, priority)
	return Game.TableDataManager:GetRow("InteractorUITemplate", key, priority)
end


---@return _InteractorUITemplateRow[]
function TableData.GetInteractorUITemplateTable(priority)
	return Game.TableDataManager:GetData("InteractorUITemplate", priority)
end


---@return _SceneActorBehaviorDataRow
function TableData.GetSceneActorBehaviorDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorBehaviorData", key, priority)
end


---@return _SceneActorBehaviorDataRow[]
function TableData.GetSceneActorBehaviorDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorBehaviorData", priority)
end


---@return _SceneActorTriggerTypeDataRow
function TableData.GetSceneActorTriggerTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorTriggerTypeData", key, priority)
end


---@return _SceneActorTriggerTypeDataRow[]
function TableData.GetSceneActorTriggerTypeDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorTriggerTypeData", priority)
end


---@return _SceneActorConditionEnumDataRow
function TableData.GetSceneActorConditionEnumDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorConditionEnumData", key, priority)
end


---@return _SceneActorConditionEnumDataRow[]
function TableData.GetSceneActorConditionEnumDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorConditionEnumData", priority)
end


---@return _SceneActorConditionTypeDataRow
function TableData.GetSceneActorConditionTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorConditionTypeData", key, priority)
end


---@return _SceneActorConditionTypeDataRow[]
function TableData.GetSceneActorConditionTypeDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorConditionTypeData", priority)
end


---@return _SceneActorActionTypeDataRow
function TableData.GetSceneActorActionTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorActionTypeData", key, priority)
end


---@return _SceneActorActionTypeDataRow[]
function TableData.GetSceneActorActionTypeDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorActionTypeData", priority)
end


---@return _SceneActorElementSpreadActionDataRow
function TableData.GetSceneActorElementSpreadActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorElementSpreadActionData", key, priority)
end


---@return _SceneActorElementSpreadActionDataRow[]
function TableData.GetSceneActorElementSpreadActionDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorElementSpreadActionData", priority)
end


---@return _SceneActorElementAttachTriggerDataRow
function TableData.GetSceneActorElementAttachTriggerDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorElementAttachTriggerData", key, priority)
end


---@return _SceneActorElementAttachTriggerDataRow[]
function TableData.GetSceneActorElementAttachTriggerDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorElementAttachTriggerData", priority)
end


---@return _SceneActor_ConsecrationSpiritDataRow
function TableData.GetSceneActor_ConsecrationSpiritDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActor_ConsecrationSpiritData", key, priority)
end


---@return _SceneActor_ConsecrationSpiritDataRow[]
function TableData.GetSceneActor_ConsecrationSpiritDataTable(priority)
	return Game.TableDataManager:GetData("SceneActor_ConsecrationSpiritData", priority)
end


---@return _SceneSimulatePhysicsActorDataRow
function TableData.GetSceneSimulatePhysicsActorDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneSimulatePhysicsActorData", key, priority)
end


---@return _SceneSimulatePhysicsActorDataRow[]
function TableData.GetSceneSimulatePhysicsActorDataTable(priority)
	return Game.TableDataManager:GetData("SceneSimulatePhysicsActorData", priority)
end


---@return _SceneActor_ConsecrationMonumentDataRow
function TableData.GetSceneActor_ConsecrationMonumentDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActor_ConsecrationMonumentData", key, priority)
end


---@return _SceneActor_ConsecrationMonumentDataRow[]
function TableData.GetSceneActor_ConsecrationMonumentDataTable(priority)
	return Game.TableDataManager:GetData("SceneActor_ConsecrationMonumentData", priority)
end


---@return _SceneActor_InteractivePortalDataRow
function TableData.GetSceneActor_InteractivePortalDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActor_InteractivePortalData", key, priority)
end


---@return _SceneActor_InteractivePortalDataRow[]
function TableData.GetSceneActor_InteractivePortalDataTable(priority)
	return Game.TableDataManager:GetData("SceneActor_InteractivePortalData", priority)
end


---@return _SceneActor_WindmillDataRow
function TableData.GetSceneActor_WindmillDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActor_WindmillData", key, priority)
end


---@return _SceneActor_WindmillDataRow[]
function TableData.GetSceneActor_WindmillDataTable(priority)
	return Game.TableDataManager:GetData("SceneActor_WindmillData", priority)
end


---@return _SceneActorTaskCollectDataRow
function TableData.GetSceneActorTaskCollectDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorTaskCollectData", key, priority)
end


---@return _SceneActorTaskCollectDataRow[]
function TableData.GetSceneActorTaskCollectDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorTaskCollectData", priority)
end


---@return _SceneActorPlanePortalDataRow
function TableData.GetSceneActorPlanePortalDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorPlanePortalData", key, priority)
end


---@return _SceneActorPlanePortalDataRow[]
function TableData.GetSceneActorPlanePortalDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorPlanePortalData", priority)
end


---@return _ContinuousInteractiveChairDataRow
function TableData.GetContinuousInteractiveChairDataRow(key, priority)
	return Game.TableDataManager:GetRow("ContinuousInteractiveChairData", key, priority)
end


---@return _ContinuousInteractiveChairDataRow[]
function TableData.GetContinuousInteractiveChairDataTable(priority)
	return Game.TableDataManager:GetData("ContinuousInteractiveChairData", priority)
end


---@return _ContinuousInteractiveSwingDataRow
function TableData.GetContinuousInteractiveSwingDataRow(key, priority)
	return Game.TableDataManager:GetRow("ContinuousInteractiveSwingData", key, priority)
end


---@return _ContinuousInteractiveSwingDataRow[]
function TableData.GetContinuousInteractiveSwingDataTable(priority)
	return Game.TableDataManager:GetData("ContinuousInteractiveSwingData", priority)
end


---@return _ContinuousInteractiveDoorDataRow
function TableData.GetContinuousInteractiveDoorDataRow(key, priority)
	return Game.TableDataManager:GetRow("ContinuousInteractiveDoorData", key, priority)
end


---@return _ContinuousInteractiveDoorDataRow[]
function TableData.GetContinuousInteractiveDoorDataTable(priority)
	return Game.TableDataManager:GetData("ContinuousInteractiveDoorData", priority)
end


---@return _SceneActorTreasureBoxDataRow
function TableData.GetSceneActorTreasureBoxDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorTreasureBoxData", key, priority)
end


---@return _SceneActorTreasureBoxDataRow[]
function TableData.GetSceneActorTreasureBoxDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorTreasureBoxData", priority)
end


---@return _SceneActorIceFieldNpcDataRow
function TableData.GetSceneActorIceFieldNpcDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorIceFieldNpcData", key, priority)
end


---@return _SceneActorIceFieldNpcDataRow[]
function TableData.GetSceneActorIceFieldNpcDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorIceFieldNpcData", priority)
end


---@return _SceneActorClockDataRow
function TableData.GetSceneActorClockDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorClockData", key, priority)
end


---@return _SceneActorClockDataRow[]
function TableData.GetSceneActorClockDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorClockData", priority)
end


---@return _FogTriggerDataRow
function TableData.GetFogTriggerDataRow(key, priority)
	return Game.TableDataManager:GetRow("FogTriggerData", key, priority)
end


---@return _FogTriggerDataRow[]
function TableData.GetFogTriggerDataTable(priority)
	return Game.TableDataManager:GetData("FogTriggerData", priority)
end


---@return _FateContractEntryDataRow
function TableData.GetFateContractEntryDataRow(key, priority)
	return Game.TableDataManager:GetRow("FateContractEntryData", key, priority)
end


---@return _FateContractEntryDataRow[]
function TableData.GetFateContractEntryDataTable(priority)
	return Game.TableDataManager:GetData("FateContractEntryData", priority)
end


---@return _InteractiveStreetLightDataRow
function TableData.GetInteractiveStreetLightDataRow(key, priority)
	return Game.TableDataManager:GetRow("InteractiveStreetLightData", key, priority)
end


---@return _InteractiveStreetLightDataRow[]
function TableData.GetInteractiveStreetLightDataTable(priority)
	return Game.TableDataManager:GetData("InteractiveStreetLightData", priority)
end


---@return _SceneActorFountainDataRow
function TableData.GetSceneActorFountainDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorFountainData", key, priority)
end


---@return _SceneActorFountainDataRow[]
function TableData.GetSceneActorFountainDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorFountainData", priority)
end


---@return _SceneActorElementTriggerDataRow
function TableData.GetSceneActorElementTriggerDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorElementTriggerData", key, priority)
end


---@return _SceneActorElementTriggerDataRow[]
function TableData.GetSceneActorElementTriggerDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorElementTriggerData", priority)
end


---@return _SceneActorMovablePlatformDataRow
function TableData.GetSceneActorMovablePlatformDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorMovablePlatformData", key, priority)
end


---@return _SceneActorMovablePlatformDataRow[]
function TableData.GetSceneActorMovablePlatformDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorMovablePlatformData", priority)
end


---@return _SceneActorGrowDecayPlantDataRow
function TableData.GetSceneActorGrowDecayPlantDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorGrowDecayPlantData", key, priority)
end


---@return _SceneActorGrowDecayPlantDataRow[]
function TableData.GetSceneActorGrowDecayPlantDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorGrowDecayPlantData", priority)
end


---@return _SceneActorCuttableTreeDataRow
function TableData.GetSceneActorCuttableTreeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorCuttableTreeData", key, priority)
end


---@return _SceneActorCuttableTreeDataRow[]
function TableData.GetSceneActorCuttableTreeDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorCuttableTreeData", priority)
end


---@return _SceneActorExtraordinaryEventDataRow
function TableData.GetSceneActorExtraordinaryEventDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneActorExtraordinaryEventData", key, priority)
end


---@return _SceneActorExtraordinaryEventDataRow[]
function TableData.GetSceneActorExtraordinaryEventDataTable(priority)
	return Game.TableDataManager:GetData("SceneActorExtraordinaryEventData", priority)
end


---@return _TargetDataRow
function TableData.GetTargetDataRow(key, priority)
	return Game.TableDataManager:GetRow("TargetData", key, priority)
end


---@return _TargetDataRow[]
function TableData.GetTargetDataTable(priority)
	return Game.TableDataManager:GetData("TargetData", priority)
end


---@return _TitleDataRow
function TableData.GetTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("TitleData", key, priority)
end


---@return _TitleDataRow[]
function TableData.GetTitleDataTable(priority)
	return Game.TableDataManager:GetData("TitleData", priority)
end


---@return _TargetChannelDataRow
function TableData.GetTargetChannelDataRow(key, priority)
	return Game.TableDataManager:GetRow("TargetChannelData", key, priority)
end


---@return _TargetChannelDataRow[]
function TableData.GetTargetChannelDataTable(priority)
	return Game.TableDataManager:GetData("TargetChannelData", priority)
end


---@return _FunctionInfoDataRow
function TableData.GetFunctionInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("FunctionInfoData", key, priority)
end


---@return _FunctionInfoDataRow[]
function TableData.GetFunctionInfoDataTable(priority)
	return Game.TableDataManager:GetData("FunctionInfoData", priority)
end


---@return _StageDataRow
function TableData.GetStageDataRow(key, priority)
	return Game.TableDataManager:GetRow("StageData", key, priority)
end


---@return _StageDataRow[]
function TableData.GetStageDataTable(priority)
	return Game.TableDataManager:GetData("StageData", priority)
end


---@return _StageTipDataRow
function TableData.GetStageTipDataRow(key, priority)
	return Game.TableDataManager:GetRow("StageTipData", key, priority)
end


---@return _StageTipDataRow[]
function TableData.GetStageTipDataTable(priority)
	return Game.TableDataManager:GetData("StageTipData", priority)
end


---@return _CutsceneDataRow
function TableData.GetCutsceneDataRow(key, priority)
	return Game.TableDataManager:GetRow("CutsceneData", key, priority)
end


---@return _CutsceneDataRow[]
function TableData.GetCutsceneDataTable(priority)
	return Game.TableDataManager:GetData("CutsceneData", priority)
end


---@return _LevelSequenceDataRow
function TableData.GetLevelSequenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelSequenceData", key, priority)
end


---@return _LevelSequenceDataRow[]
function TableData.GetLevelSequenceDataTable(priority)
	return Game.TableDataManager:GetData("LevelSequenceData", priority)
end


---@return _TemplateSequenceDataRow
function TableData.GetTemplateSequenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("TemplateSequenceData", key, priority)
end


---@return _TemplateSequenceDataRow[]
function TableData.GetTemplateSequenceDataTable(priority)
	return Game.TableDataManager:GetData("TemplateSequenceData", priority)
end


---@return _CutSceneDialogDataRow
function TableData.GetCutSceneDialogDataRow(key, priority)
	return Game.TableDataManager:GetRow("CutSceneDialogData", key, priority)
end


---@return _CutSceneDialogDataRow[]
function TableData.GetCutSceneDialogDataTable(priority)
	return Game.TableDataManager:GetData("CutSceneDialogData", priority)
end


---@return _DilaogueSeuqenceDataRow
function TableData.GetDilaogueSeuqenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("DilaogueSeuqenceData", key, priority)
end


---@return _DilaogueSeuqenceDataRow[]
function TableData.GetDilaogueSeuqenceDataTable(priority)
	return Game.TableDataManager:GetData("DilaogueSeuqenceData", priority)
end


---@return _TipsDataRow
function TableData.GetTipsDataRow(key, priority)
	return Game.TableDataManager:GetRow("TipsData", key, priority)
end


---@return _TipsDataRow[]
function TableData.GetTipsDataTable(priority)
	return Game.TableDataManager:GetData("TipsData", priority)
end


---@return _DialogPopUpDataRow
function TableData.GetDialogPopUpDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogPopUpData", key, priority)
end


---@return _DialogPopUpDataRow[]
function TableData.GetDialogPopUpDataTable(priority)
	return Game.TableDataManager:GetData("DialogPopUpData", priority)
end


---@return _ReminderTextDataRow
function TableData.GetReminderTextDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReminderTextData", key, priority)
end


---@return _ReminderTextDataRow[]
function TableData.GetReminderTextDataTable(priority)
	return Game.TableDataManager:GetData("ReminderTextData", priority)
end


---@return _ReminderParamDataRow
function TableData.GetReminderParamDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReminderParamData", key, priority)
end


---@return _ReminderParamDataRow[]
function TableData.GetReminderParamDataTable(priority)
	return Game.TableDataManager:GetData("ReminderParamData", priority)
end


---@return _ReminderTypeDataRow
function TableData.GetReminderTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReminderTypeData", key, priority)
end


---@return _ReminderTypeDataRow[]
function TableData.GetReminderTypeDataTable(priority)
	return Game.TableDataManager:GetData("ReminderTypeData", priority)
end


---@return _ReminderQueueDataRow
function TableData.GetReminderQueueDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReminderQueueData", key, priority)
end


---@return _ReminderQueueDataRow[]
function TableData.GetReminderQueueDataTable(priority)
	return Game.TableDataManager:GetData("ReminderQueueData", priority)
end


---@return _CountTipsDataRow
function TableData.GetCountTipsDataRow(key, priority)
	return Game.TableDataManager:GetRow("CountTipsData", key, priority)
end


---@return _CountTipsDataRow[]
function TableData.GetCountTipsDataTable(priority)
	return Game.TableDataManager:GetData("CountTipsData", priority)
end


---@return _ElasticStripDataRow
function TableData.GetElasticStripDataRow(key, priority)
	return Game.TableDataManager:GetRow("ElasticStripData", key, priority)
end


---@return _ElasticStripDataRow[]
function TableData.GetElasticStripDataTable(priority)
	return Game.TableDataManager:GetData("ElasticStripData", priority)
end


---@return _RedPointEnumDataRow
function TableData.GetRedPointEnumDataRow(key, priority)
	return Game.TableDataManager:GetRow("RedPointEnumData", key, priority)
end


---@return _RedPointEnumDataRow[]
function TableData.GetRedPointEnumDataTable(priority)
	return Game.TableDataManager:GetData("RedPointEnumData", priority)
end


---@return _StringConstDataRow
function TableData.GetStringConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("StringConstData", key, priority)
end


---@return _StringConstDataRow[]
function TableData.GetStringConstDataTable(priority)
	return Game.TableDataManager:GetData("StringConstData", priority)
end


---@return _StringLuaDataRow
function TableData.GetStringLuaDataRow(key, priority)
	return Game.TableDataManager:GetRow("StringLuaData", key, priority)
end


---@return _StringLuaDataRow[]
function TableData.GetStringLuaDataTable(priority)
	return Game.TableDataManager:GetData("StringLuaData", priority)
end


---@return _NpcShopDataRow
function TableData.GetNpcShopDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcShopData", key, priority)
end


---@return _NpcShopDataRow[]
function TableData.GetNpcShopDataTable(priority)
	return Game.TableDataManager:GetData("NpcShopData", priority)
end


---@return _ShopTabDataRow
function TableData.GetShopTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("ShopTabData", key, priority)
end


---@return _ShopTabDataRow[]
function TableData.GetShopTabDataTable(priority)
	return Game.TableDataManager:GetData("ShopTabData", priority)
end


---@return _MapCustomTagDataRow
function TableData.GetMapCustomTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapCustomTagData", key, priority)
end


---@return _MapCustomTagDataRow[]
function TableData.GetMapCustomTagDataTable(priority)
	return Game.TableDataManager:GetData("MapCustomTagData", priority)
end


---@return _MapSearchTabDataRow
function TableData.GetMapSearchTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapSearchTabData", key, priority)
end


---@return _MapSearchTabDataRow[]
function TableData.GetMapSearchTabDataTable(priority)
	return Game.TableDataManager:GetData("MapSearchTabData", priority)
end


---@return _MapTagClassDataRow
function TableData.GetMapTagClassDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapTagClassData", key, priority)
end


---@return _MapTagClassDataRow[]
function TableData.GetMapTagClassDataTable(priority)
	return Game.TableDataManager:GetData("MapTagClassData", priority)
end


---@return _GuildConstIntDataRow
function TableData.GetGuildConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildConstIntData", key, priority)
end


---@return _GuildConstIntDataRow[]
function TableData.GetGuildConstIntDataTable(priority)
	return Game.TableDataManager:GetData("GuildConstIntData", priority)
end


---@return _StarGraphDataRow
function TableData.GetStarGraphDataRow(key, priority)
	return Game.TableDataManager:GetRow("StarGraphData", key, priority)
end


---@return _StarGraphDataRow[]
function TableData.GetStarGraphDataTable(priority)
	return Game.TableDataManager:GetData("StarGraphData", priority)
end


---@return _StarMisteryDataRow
function TableData.GetStarMisteryDataRow(key, priority)
	return Game.TableDataManager:GetRow("StarMisteryData", key, priority)
end


---@return _StarMisteryDataRow[]
function TableData.GetStarMisteryDataTable(priority)
	return Game.TableDataManager:GetData("StarMisteryData", priority)
end


---@return _MapTagDataRow
function TableData.GetMapTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapTagData", key, priority)
end


---@return _MapTagDataRow[]
function TableData.GetMapTagDataTable(priority)
	return Game.TableDataManager:GetData("MapTagData", priority)
end


---@return _MapTagLibDataRow
function TableData.GetMapTagLibDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapTagLibData", key, priority)
end


---@return _MapTagLibDataRow[]
function TableData.GetMapTagLibDataTable(priority)
	return Game.TableDataManager:GetData("MapTagLibData", priority)
end


---@return _MapDataRow
function TableData.GetMapDataRow(key, priority)
	return Game.TableDataManager:GetRow("MapData", key, priority)
end


---@return _MapDataRow[]
function TableData.GetMapDataTable(priority)
	return Game.TableDataManager:GetData("MapData", priority)
end


---@return _TagTypeConfigDataRow
function TableData.GetTagTypeConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("TagTypeConfigData", key, priority)
end


---@return _TagTypeConfigDataRow[]
function TableData.GetTagTypeConfigDataTable(priority)
	return Game.TableDataManager:GetData("TagTypeConfigData", priority)
end


---@return _GoodsServerLimitDataRow
function TableData.GetGoodsServerLimitDataRow(key, priority)
	return Game.TableDataManager:GetRow("GoodsServerLimitData", key, priority)
end


---@return _GoodsServerLimitDataRow[]
function TableData.GetGoodsServerLimitDataTable(priority)
	return Game.TableDataManager:GetData("GoodsServerLimitData", priority)
end


---@return _MallShopDataRow
function TableData.GetMallShopDataRow(key, priority)
	return Game.TableDataManager:GetRow("MallShopData", key, priority)
end


---@return _MallShopDataRow[]
function TableData.GetMallShopDataTable(priority)
	return Game.TableDataManager:GetData("MallShopData", priority)
end


---@return _MallGoodsDataRow
function TableData.GetMallGoodsDataRow(key, priority)
	return Game.TableDataManager:GetRow("MallGoodsData", key, priority)
end


---@return _MallGoodsDataRow[]
function TableData.GetMallGoodsDataTable(priority)
	return Game.TableDataManager:GetData("MallGoodsData", priority)
end


---@return _CashMarketConstIntDataRow
function TableData.GetCashMarketConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("CashMarketConstIntData", key, priority)
end


---@return _CashMarketConstIntDataRow[]
function TableData.GetCashMarketConstIntDataTable(priority)
	return Game.TableDataManager:GetData("CashMarketConstIntData", priority)
end


---@return _DailyStandardDataRow
function TableData.GetDailyStandardDataRow(key, priority)
	return Game.TableDataManager:GetRow("DailyStandardData", key, priority)
end


---@return _DailyStandardDataRow[]
function TableData.GetDailyStandardDataTable(priority)
	return Game.TableDataManager:GetData("DailyStandardData", priority)
end


---@return _ItemIdToGoodIdDataRow
function TableData.GetItemIdToGoodIdDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemIdToGoodIdData", key, priority)
end


---@return _ItemIdToGoodIdDataRow[]
function TableData.GetItemIdToGoodIdDataTable(priority)
	return Game.TableDataManager:GetData("ItemIdToGoodIdData", priority)
end


---@return _NpcGoodsDataRow
function TableData.GetNpcGoodsDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcGoodsData", key, priority)
end


---@return _NpcGoodsDataRow[]
function TableData.GetNpcGoodsDataTable(priority)
	return Game.TableDataManager:GetData("NpcGoodsData", priority)
end


---@return _CrossWordPuzzleDataRow
function TableData.GetCrossWordPuzzleDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrossWordPuzzleData", key, priority)
end


---@return _CrossWordPuzzleDataRow[]
function TableData.GetCrossWordPuzzleDataTable(priority)
	return Game.TableDataManager:GetData("CrossWordPuzzleData", priority)
end


---@return _KnowledgeTipsDataRow
function TableData.GetKnowledgeTipsDataRow(key, priority)
	return Game.TableDataManager:GetRow("KnowledgeTipsData", key, priority)
end


---@return _KnowledgeTipsDataRow[]
function TableData.GetKnowledgeTipsDataTable(priority)
	return Game.TableDataManager:GetData("KnowledgeTipsData", priority)
end


---@return _JigsawPuzzleDataRow
function TableData.GetJigsawPuzzleDataRow(key, priority)
	return Game.TableDataManager:GetRow("JigsawPuzzleData", key, priority)
end


---@return _JigsawPuzzleDataRow[]
function TableData.GetJigsawPuzzleDataTable(priority)
	return Game.TableDataManager:GetData("JigsawPuzzleData", priority)
end


---@return _JigsawPuzzleColorDataRow
function TableData.GetJigsawPuzzleColorDataRow(key, priority)
	return Game.TableDataManager:GetRow("JigsawPuzzleColorData", key, priority)
end


---@return _JigsawPuzzleColorDataRow[]
function TableData.GetJigsawPuzzleColorDataTable(priority)
	return Game.TableDataManager:GetData("JigsawPuzzleColorData", priority)
end


---@return _OverHeadInfoSpecializedOffsetDataRow
function TableData.GetOverHeadInfoSpecializedOffsetDataRow(key, priority)
	return Game.TableDataManager:GetRow("OverHeadInfoSpecializedOffsetData", key, priority)
end


---@return _OverHeadInfoSpecializedOffsetDataRow[]
function TableData.GetOverHeadInfoSpecializedOffsetDataTable(priority)
	return Game.TableDataManager:GetData("OverHeadInfoSpecializedOffsetData", priority)
end


---@return _OverHeadBuffDisplayDataRow
function TableData.GetOverHeadBuffDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("OverHeadBuffDisplayData", key, priority)
end


---@return _OverHeadBuffDisplayDataRow[]
function TableData.GetOverHeadBuffDisplayDataTable(priority)
	return Game.TableDataManager:GetData("OverHeadBuffDisplayData", priority)
end


---@return _OverHeadCountDownDisplayDataRow
function TableData.GetOverHeadCountDownDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("OverHeadCountDownDisplayData", key, priority)
end


---@return _OverHeadCountDownDisplayDataRow[]
function TableData.GetOverHeadCountDownDisplayDataTable(priority)
	return Game.TableDataManager:GetData("OverHeadCountDownDisplayData", priority)
end


---@return _OverHeadProgressDisplayDataRow
function TableData.GetOverHeadProgressDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("OverHeadProgressDisplayData", key, priority)
end


---@return _OverHeadProgressDisplayDataRow[]
function TableData.GetOverHeadProgressDisplayDataTable(priority)
	return Game.TableDataManager:GetData("OverHeadProgressDisplayData", priority)
end


---@return _ItemSubmitNewDataRow
function TableData.GetItemSubmitNewDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemSubmitNewData", key, priority)
end


---@return _ItemSubmitNewDataRow[]
function TableData.GetItemSubmitNewDataTable(priority)
	return Game.TableDataManager:GetData("ItemSubmitNewData", priority)
end


---@return _HelperFormulaDataRow
function TableData.GetHelperFormulaDataRow(key, priority)
	return Game.TableDataManager:GetRow("HelperFormulaData", key, priority)
end


---@return _HelperFormulaDataRow[]
function TableData.GetHelperFormulaDataTable(priority)
	return Game.TableDataManager:GetData("HelperFormulaData", priority)
end


---@return _FightActionDataRow
function TableData.GetFightActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightActionData", key, priority)
end


---@return _FightActionDataRow[]
function TableData.GetFightActionDataTable(priority)
	return Game.TableDataManager:GetData("FightActionData", priority)
end


---@return _CounterAttackDataRow
function TableData.GetCounterAttackDataRow(key, priority)
	return Game.TableDataManager:GetRow("CounterAttackData", key, priority)
end


---@return _CounterAttackDataRow[]
function TableData.GetCounterAttackDataTable(priority)
	return Game.TableDataManager:GetData("CounterAttackData", priority)
end


---@return _FStatePropDataRow
function TableData.GetFStatePropDataRow(key, priority)
	return Game.TableDataManager:GetRow("FStatePropData", key, priority)
end


---@return _FStatePropDataRow[]
function TableData.GetFStatePropDataTable(priority)
	return Game.TableDataManager:GetData("FStatePropData", priority)
end


---@return _FightPropDataRow
function TableData.GetFightPropDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightPropData", key, priority)
end


---@return _FightPropDataRow[]
function TableData.GetFightPropDataTable(priority)
	return Game.TableDataManager:GetData("FightPropData", priority)
end


---@return _FightPropGroupDataRow
function TableData.GetFightPropGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightPropGroupData", key, priority)
end


---@return _FightPropGroupDataRow[]
function TableData.GetFightPropGroupDataTable(priority)
	return Game.TableDataManager:GetData("FightPropGroupData", priority)
end


---@return _FightPropModeDataRow
function TableData.GetFightPropModeDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightPropModeData", key, priority)
end


---@return _FightPropModeDataRow[]
function TableData.GetFightPropModeDataTable(priority)
	return Game.TableDataManager:GetData("FightPropModeData", priority)
end


---@return _FightPropModeSetDataRow
function TableData.GetFightPropModeSetDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightPropModeSetData", key, priority)
end


---@return _FightPropModeSetDataRow[]
function TableData.GetFightPropModeSetDataTable(priority)
	return Game.TableDataManager:GetData("FightPropModeSetData", priority)
end


---@return _FormulaDataRow
function TableData.GetFormulaDataRow(key, priority)
	return Game.TableDataManager:GetRow("FormulaData", key, priority)
end


---@return _FormulaDataRow[]
function TableData.GetFormulaDataTable(priority)
	return Game.TableDataManager:GetData("FormulaData", priority)
end


---@return _FormulaForClientDataRow
function TableData.GetFormulaForClientDataRow(key, priority)
	return Game.TableDataManager:GetRow("FormulaForClientData", key, priority)
end


---@return _FormulaForClientDataRow[]
function TableData.GetFormulaForClientDataTable(priority)
	return Game.TableDataManager:GetData("FormulaForClientData", priority)
end


---@return _DeathReviveDataRow
function TableData.GetDeathReviveDataRow(key, priority)
	return Game.TableDataManager:GetRow("DeathReviveData", key, priority)
end


---@return _DeathReviveDataRow[]
function TableData.GetDeathReviveDataTable(priority)
	return Game.TableDataManager:GetData("DeathReviveData", priority)
end


---@return _ReviveDataRow
function TableData.GetReviveDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReviveData", key, priority)
end


---@return _ReviveDataRow[]
function TableData.GetReviveDataTable(priority)
	return Game.TableDataManager:GetData("ReviveData", priority)
end


---@return _FightPropBalanceRuleDataRow
function TableData.GetFightPropBalanceRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("FightPropBalanceRuleData", key, priority)
end


---@return _FightPropBalanceRuleDataRow[]
function TableData.GetFightPropBalanceRuleDataTable(priority)
	return Game.TableDataManager:GetData("FightPropBalanceRuleData", priority)
end


---@return _SkillBalanceRuleDataRow
function TableData.GetSkillBalanceRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillBalanceRuleData", key, priority)
end


---@return _SkillBalanceRuleDataRow[]
function TableData.GetSkillBalanceRuleDataTable(priority)
	return Game.TableDataManager:GetData("SkillBalanceRuleData", priority)
end


---@return _PVPBattleBotPropDataRow
function TableData.GetPVPBattleBotPropDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPBattleBotPropData", key, priority)
end


---@return _PVPBattleBotPropDataRow[]
function TableData.GetPVPBattleBotPropDataTable(priority)
	return Game.TableDataManager:GetData("PVPBattleBotPropData", priority)
end


---@return _PVPPlayerPropScaleRuleDataRow
function TableData.GetPVPPlayerPropScaleRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPPlayerPropScaleRuleData", key, priority)
end


---@return _PVPPlayerPropScaleRuleDataRow[]
function TableData.GetPVPPlayerPropScaleRuleDataTable(priority)
	return Game.TableDataManager:GetData("PVPPlayerPropScaleRuleData", priority)
end


---@return _ItemNewDataRow
function TableData.GetItemNewDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemNewData", key, priority)
end


---@return _ItemNewDataRow[]
function TableData.GetItemNewDataTable(priority)
	return Game.TableDataManager:GetData("ItemNewData", priority)
end


---@return _ItemRMBDataRow
function TableData.GetItemRMBDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemRMBData", key, priority)
end


---@return _ItemRMBDataRow[]
function TableData.GetItemRMBDataTable(priority)
	return Game.TableDataManager:GetData("ItemRMBData", priority)
end


---@return _ItemGiftDataRow
function TableData.GetItemGiftDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemGiftData", key, priority)
end


---@return _ItemGiftDataRow[]
function TableData.GetItemGiftDataTable(priority)
	return Game.TableDataManager:GetData("ItemGiftData", priority)
end


---@return _ItemTaskDataRow
function TableData.GetItemTaskDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemTaskData", key, priority)
end


---@return _ItemTaskDataRow[]
function TableData.GetItemTaskDataTable(priority)
	return Game.TableDataManager:GetData("ItemTaskData", priority)
end


---@return _ItemLifeDataRow
function TableData.GetItemLifeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemLifeData", key, priority)
end


---@return _ItemLifeDataRow[]
function TableData.GetItemLifeDataTable(priority)
	return Game.TableDataManager:GetData("ItemLifeData", priority)
end


---@return _ItemPriorOrderDataRow
function TableData.GetItemPriorOrderDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemPriorOrderData", key, priority)
end


---@return _ItemPriorOrderDataRow[]
function TableData.GetItemPriorOrderDataTable(priority)
	return Game.TableDataManager:GetData("ItemPriorOrderData", priority)
end


---@return _CDGroupDataRow
function TableData.GetCDGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("CDGroupData", key, priority)
end


---@return _CDGroupDataRow[]
function TableData.GetCDGroupDataTable(priority)
	return Game.TableDataManager:GetData("CDGroupData", priority)
end


---@return _SharingUseGroupDataRow
function TableData.GetSharingUseGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("SharingUseGroupData", key, priority)
end


---@return _SharingUseGroupDataRow[]
function TableData.GetSharingUseGroupDataTable(priority)
	return Game.TableDataManager:GetData("SharingUseGroupData", priority)
end


---@return _RawItemOperationTypeDataRow
function TableData.GetRawItemOperationTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("RawItemOperationTypeData", key, priority)
end


---@return _RawItemOperationTypeDataRow[]
function TableData.GetRawItemOperationTypeDataTable(priority)
	return Game.TableDataManager:GetData("RawItemOperationTypeData", priority)
end


---@return _MoneyWeekLimitDataRow
function TableData.GetMoneyWeekLimitDataRow(key, priority)
	return Game.TableDataManager:GetRow("MoneyWeekLimitData", key, priority)
end


---@return _MoneyWeekLimitDataRow[]
function TableData.GetMoneyWeekLimitDataTable(priority)
	return Game.TableDataManager:GetData("MoneyWeekLimitData", priority)
end


---@return _MoneyExchangeDataRow
function TableData.GetMoneyExchangeDataRow(key, priority)
	return Game.TableDataManager:GetRow("MoneyExchangeData", key, priority)
end


---@return _MoneyExchangeDataRow[]
function TableData.GetMoneyExchangeDataTable(priority)
	return Game.TableDataManager:GetData("MoneyExchangeData", priority)
end


---@return _AchievePathDataRow
function TableData.GetAchievePathDataRow(key, priority)
	return Game.TableDataManager:GetRow("AchievePathData", key, priority)
end


---@return _AchievePathDataRow[]
function TableData.GetAchievePathDataTable(priority)
	return Game.TableDataManager:GetData("AchievePathData", priority)
end


---@return _BtnTypeDataRow
function TableData.GetBtnTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("BtnTypeData", key, priority)
end


---@return _BtnTypeDataRow[]
function TableData.GetBtnTypeDataTable(priority)
	return Game.TableDataManager:GetData("BtnTypeData", priority)
end


---@return _WareHousePageDataRow
function TableData.GetWareHousePageDataRow(key, priority)
	return Game.TableDataManager:GetRow("WareHousePageData", key, priority)
end


---@return _WareHousePageDataRow[]
function TableData.GetWareHousePageDataTable(priority)
	return Game.TableDataManager:GetData("WareHousePageData", priority)
end


---@return _WareHouseSlotDataRow
function TableData.GetWareHouseSlotDataRow(key, priority)
	return Game.TableDataManager:GetRow("WareHouseSlotData", key, priority)
end


---@return _WareHouseSlotDataRow[]
function TableData.GetWareHouseSlotDataTable(priority)
	return Game.TableDataManager:GetData("WareHouseSlotData", priority)
end


---@return _DecomposeItemDataRow
function TableData.GetDecomposeItemDataRow(key, priority)
	return Game.TableDataManager:GetRow("DecomposeItemData", key, priority)
end


---@return _DecomposeItemDataRow[]
function TableData.GetDecomposeItemDataTable(priority)
	return Game.TableDataManager:GetData("DecomposeItemData", priority)
end


---@return _DecomposeItemLimitDataRow
function TableData.GetDecomposeItemLimitDataRow(key, priority)
	return Game.TableDataManager:GetRow("DecomposeItemLimitData", key, priority)
end


---@return _DecomposeItemLimitDataRow[]
function TableData.GetDecomposeItemLimitDataTable(priority)
	return Game.TableDataManager:GetData("DecomposeItemLimitData", priority)
end


---@return _DecomposeEquipDataRow
function TableData.GetDecomposeEquipDataRow(key, priority)
	return Game.TableDataManager:GetRow("DecomposeEquipData", key, priority)
end


---@return _DecomposeEquipDataRow[]
function TableData.GetDecomposeEquipDataTable(priority)
	return Game.TableDataManager:GetData("DecomposeEquipData", priority)
end


---@return _ItemSynthesisDataRow
function TableData.GetItemSynthesisDataRow(key, priority)
	return Game.TableDataManager:GetRow("ItemSynthesisData", key, priority)
end


---@return _ItemSynthesisDataRow[]
function TableData.GetItemSynthesisDataTable(priority)
	return Game.TableDataManager:GetData("ItemSynthesisData", priority)
end


---@return _InventoryDataRow
function TableData.GetInventoryDataRow(key, priority)
	return Game.TableDataManager:GetRow("InventoryData", key, priority)
end


---@return _InventoryDataRow[]
function TableData.GetInventoryDataTable(priority)
	return Game.TableDataManager:GetData("InventoryData", priority)
end


---@return _InventorySlotDataRow
function TableData.GetInventorySlotDataRow(key, priority)
	return Game.TableDataManager:GetRow("InventorySlotData", key, priority)
end


---@return _InventorySlotDataRow[]
function TableData.GetInventorySlotDataTable(priority)
	return Game.TableDataManager:GetData("InventorySlotData", priority)
end


---@return _DecomposeDataRow
function TableData.GetDecomposeDataRow(key, priority)
	return Game.TableDataManager:GetRow("DecomposeData", key, priority)
end


---@return _DecomposeDataRow[]
function TableData.GetDecomposeDataTable(priority)
	return Game.TableDataManager:GetData("DecomposeData", priority)
end


---@return _FilterDataRow
function TableData.GetFilterDataRow(key, priority)
	return Game.TableDataManager:GetRow("FilterData", key, priority)
end


---@return _FilterDataRow[]
function TableData.GetFilterDataTable(priority)
	return Game.TableDataManager:GetData("FilterData", priority)
end


---@return _SortTabDataRow
function TableData.GetSortTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("SortTabData", key, priority)
end


---@return _SortTabDataRow[]
function TableData.GetSortTabDataTable(priority)
	return Game.TableDataManager:GetData("SortTabData", priority)
end


---@return _AutoDecomposeDataRow
function TableData.GetAutoDecomposeDataRow(key, priority)
	return Game.TableDataManager:GetRow("AutoDecomposeData", key, priority)
end


---@return _AutoDecomposeDataRow[]
function TableData.GetAutoDecomposeDataTable(priority)
	return Game.TableDataManager:GetData("AutoDecomposeData", priority)
end


---@return _ChatChannelDataRow
function TableData.GetChatChannelDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatChannelData", key, priority)
end


---@return _ChatChannelDataRow[]
function TableData.GetChatChannelDataTable(priority)
	return Game.TableDataManager:GetData("ChatChannelData", priority)
end


---@return _ChatStickerDataRow
function TableData.GetChatStickerDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatStickerData", key, priority)
end


---@return _ChatStickerDataRow[]
function TableData.GetChatStickerDataTable(priority)
	return Game.TableDataManager:GetData("ChatStickerData", priority)
end


---@return _ChatSystemDataRow
function TableData.GetChatSystemDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatSystemData", key, priority)
end


---@return _ChatSystemDataRow[]
function TableData.GetChatSystemDataTable(priority)
	return Game.TableDataManager:GetData("ChatSystemData", priority)
end


---@return _ChatConstDataRow
function TableData.GetChatConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatConstData", key, priority)
end


---@return _ChatConstDataRow[]
function TableData.GetChatConstDataTable(priority)
	return Game.TableDataManager:GetData("ChatConstData", priority)
end


---@return _AnonymousHeadDataRow
function TableData.GetAnonymousHeadDataRow(key, priority)
	return Game.TableDataManager:GetRow("AnonymousHeadData", key, priority)
end


---@return _AnonymousHeadDataRow[]
function TableData.GetAnonymousHeadDataTable(priority)
	return Game.TableDataManager:GetData("AnonymousHeadData", priority)
end


---@return _AnonymousNameDataRow
function TableData.GetAnonymousNameDataRow(key, priority)
	return Game.TableDataManager:GetRow("AnonymousNameData", key, priority)
end


---@return _AnonymousNameDataRow[]
function TableData.GetAnonymousNameDataTable(priority)
	return Game.TableDataManager:GetData("AnonymousNameData", priority)
end


---@return _AnonymousTagDataRow
function TableData.GetAnonymousTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("AnonymousTagData", key, priority)
end


---@return _AnonymousTagDataRow[]
function TableData.GetAnonymousTagDataTable(priority)
	return Game.TableDataManager:GetData("AnonymousTagData", priority)
end


---@return _ChatRoomConstDataRow
function TableData.GetChatRoomConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatRoomConstData", key, priority)
end


---@return _ChatRoomConstDataRow[]
function TableData.GetChatRoomConstDataTable(priority)
	return Game.TableDataManager:GetData("ChatRoomConstData", priority)
end


---@return _ChatRoomStringConstDataRow
function TableData.GetChatRoomStringConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatRoomStringConstData", key, priority)
end


---@return _ChatRoomStringConstDataRow[]
function TableData.GetChatRoomStringConstDataTable(priority)
	return Game.TableDataManager:GetData("ChatRoomStringConstData", priority)
end


---@return _ChatRoomTypeDataRow
function TableData.GetChatRoomTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatRoomTypeData", key, priority)
end


---@return _ChatRoomTypeDataRow[]
function TableData.GetChatRoomTypeDataTable(priority)
	return Game.TableDataManager:GetData("ChatRoomTypeData", priority)
end


---@return _ChatRoomIdentityDataRow
function TableData.GetChatRoomIdentityDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatRoomIdentityData", key, priority)
end


---@return _ChatRoomIdentityDataRow[]
function TableData.GetChatRoomIdentityDataTable(priority)
	return Game.TableDataManager:GetData("ChatRoomIdentityData", priority)
end


---@return _ChatRoomIdentityStateDataRow
function TableData.GetChatRoomIdentityStateDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChatRoomIdentityStateData", key, priority)
end


---@return _ChatRoomIdentityStateDataRow[]
function TableData.GetChatRoomIdentityStateDataTable(priority)
	return Game.TableDataManager:GetData("ChatRoomIdentityStateData", priority)
end


---@return _SettingTabDataRow
function TableData.GetSettingTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("SettingTabData", key, priority)
end


---@return _SettingTabDataRow[]
function TableData.GetSettingTabDataTable(priority)
	return Game.TableDataManager:GetData("SettingTabData", priority)
end


---@return _SettingDataRow
function TableData.GetSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("SettingData", key, priority)
end


---@return _SettingDataRow[]
function TableData.GetSettingDataTable(priority)
	return Game.TableDataManager:GetData("SettingData", priority)
end


---@return _SettingWidgetTypeDataRow
function TableData.GetSettingWidgetTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SettingWidgetTypeData", key, priority)
end


---@return _SettingWidgetTypeDataRow[]
function TableData.GetSettingWidgetTypeDataTable(priority)
	return Game.TableDataManager:GetData("SettingWidgetTypeData", priority)
end


---@return _SettingConfigDataRow
function TableData.GetSettingConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("SettingConfigData", key, priority)
end


---@return _SettingConfigDataRow[]
function TableData.GetSettingConfigDataTable(priority)
	return Game.TableDataManager:GetData("SettingConfigData", priority)
end


---@return _SettingRestartCVarDataRow
function TableData.GetSettingRestartCVarDataRow(key, priority)
	return Game.TableDataManager:GetRow("SettingRestartCVarData", key, priority)
end


---@return _SettingRestartCVarDataRow[]
function TableData.GetSettingRestartCVarDataTable(priority)
	return Game.TableDataManager:GetData("SettingRestartCVarData", priority)
end


---@return _MenuDataRow
function TableData.GetMenuDataRow(key, priority)
	return Game.TableDataManager:GetRow("MenuData", key, priority)
end


---@return _MenuDataRow[]
function TableData.GetMenuDataTable(priority)
	return Game.TableDataManager:GetData("MenuData", priority)
end


---@return _FriendRecommandTriggerDataRow
function TableData.GetFriendRecommandTriggerDataRow(key, priority)
	return Game.TableDataManager:GetRow("FriendRecommandTriggerData", key, priority)
end


---@return _FriendRecommandTriggerDataRow[]
function TableData.GetFriendRecommandTriggerDataTable(priority)
	return Game.TableDataManager:GetData("FriendRecommandTriggerData", priority)
end


---@return _FriendAttractionDataRow
function TableData.GetFriendAttractionDataRow(key, priority)
	return Game.TableDataManager:GetRow("FriendAttractionData", key, priority)
end


---@return _FriendAttractionDataRow[]
function TableData.GetFriendAttractionDataTable(priority)
	return Game.TableDataManager:GetData("FriendAttractionData", priority)
end


---@return _FriendAttractionSourceDataRow
function TableData.GetFriendAttractionSourceDataRow(key, priority)
	return Game.TableDataManager:GetRow("FriendAttractionSourceData", key, priority)
end


---@return _FriendAttractionSourceDataRow[]
function TableData.GetFriendAttractionSourceDataTable(priority)
	return Game.TableDataManager:GetData("FriendAttractionSourceData", priority)
end


---@return _FriendImprintDataRow
function TableData.GetFriendImprintDataRow(key, priority)
	return Game.TableDataManager:GetRow("FriendImprintData", key, priority)
end


---@return _FriendImprintDataRow[]
function TableData.GetFriendImprintDataTable(priority)
	return Game.TableDataManager:GetData("FriendImprintData", priority)
end


---@return _FriendAddSourceDataRow
function TableData.GetFriendAddSourceDataRow(key, priority)
	return Game.TableDataManager:GetRow("FriendAddSourceData", key, priority)
end


---@return _FriendAddSourceDataRow[]
function TableData.GetFriendAddSourceDataTable(priority)
	return Game.TableDataManager:GetData("FriendAddSourceData", priority)
end


---@return _GiftDataRow
function TableData.GetGiftDataRow(key, priority)
	return Game.TableDataManager:GetRow("GiftData", key, priority)
end


---@return _GiftDataRow[]
function TableData.GetGiftDataTable(priority)
	return Game.TableDataManager:GetData("GiftData", priority)
end


---@return _GuildRightDataRow
function TableData.GetGuildRightDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildRightData", key, priority)
end


---@return _GuildRightDataRow[]
function TableData.GetGuildRightDataTable(priority)
	return Game.TableDataManager:GetData("GuildRightData", priority)
end


---@return _GuildUpgradeDataRow
function TableData.GetGuildUpgradeDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildUpgradeData", key, priority)
end


---@return _GuildUpgradeDataRow[]
function TableData.GetGuildUpgradeDataTable(priority)
	return Game.TableDataManager:GetData("GuildUpgradeData", priority)
end


---@return _GuildChatTitleDataRow
function TableData.GetGuildChatTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildChatTitleData", key, priority)
end


---@return _GuildChatTitleDataRow[]
function TableData.GetGuildChatTitleDataTable(priority)
	return Game.TableDataManager:GetData("GuildChatTitleData", priority)
end


---@return _GuildBadgeFrameDataRow
function TableData.GetGuildBadgeFrameDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildBadgeFrameData", key, priority)
end


---@return _GuildBadgeFrameDataRow[]
function TableData.GetGuildBadgeFrameDataTable(priority)
	return Game.TableDataManager:GetData("GuildBadgeFrameData", priority)
end


---@return _GuildBuildingDataRow
function TableData.GetGuildBuildingDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildBuildingData", key, priority)
end


---@return _GuildBuildingDataRow[]
function TableData.GetGuildBuildingDataTable(priority)
	return Game.TableDataManager:GetData("GuildBuildingData", priority)
end


---@return _GuildMaintenanceDataRow
function TableData.GetGuildMaintenanceDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildMaintenanceData", key, priority)
end


---@return _GuildMaintenanceDataRow[]
function TableData.GetGuildMaintenanceDataTable(priority)
	return Game.TableDataManager:GetData("GuildMaintenanceData", priority)
end


---@return _GuildFuncDataRow
function TableData.GetGuildFuncDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildFuncData", key, priority)
end


---@return _GuildFuncDataRow[]
function TableData.GetGuildFuncDataTable(priority)
	return Game.TableDataManager:GetData("GuildFuncData", priority)
end


---@return _GuildContributionDataRow
function TableData.GetGuildContributionDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildContributionData", key, priority)
end


---@return _GuildContributionDataRow[]
function TableData.GetGuildContributionDataTable(priority)
	return Game.TableDataManager:GetData("GuildContributionData", priority)
end


---@return _GuildExercisePropDataRow
function TableData.GetGuildExercisePropDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildExercisePropData", key, priority)
end


---@return _GuildExercisePropDataRow[]
function TableData.GetGuildExercisePropDataTable(priority)
	return Game.TableDataManager:GetData("GuildExercisePropData", priority)
end


---@return _GuildExerciseTypeDataRow
function TableData.GetGuildExerciseTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildExerciseTypeData", key, priority)
end


---@return _GuildExerciseTypeDataRow[]
function TableData.GetGuildExerciseTypeDataTable(priority)
	return Game.TableDataManager:GetData("GuildExerciseTypeData", priority)
end


---@return _GuildChatWelcomeDataRow
function TableData.GetGuildChatWelcomeDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildChatWelcomeData", key, priority)
end


---@return _GuildChatWelcomeDataRow[]
function TableData.GetGuildChatWelcomeDataTable(priority)
	return Game.TableDataManager:GetData("GuildChatWelcomeData", priority)
end


---@return _GuildClubDecDataRow
function TableData.GetGuildClubDecDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildClubDecData", key, priority)
end


---@return _GuildClubDecDataRow[]
function TableData.GetGuildClubDecDataTable(priority)
	return Game.TableDataManager:GetData("GuildClubDecData", priority)
end


---@return _GuildClubActivityDataRow
function TableData.GetGuildClubActivityDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildClubActivityData", key, priority)
end


---@return _GuildClubActivityDataRow[]
function TableData.GetGuildClubActivityDataTable(priority)
	return Game.TableDataManager:GetData("GuildClubActivityData", priority)
end


---@return _DancingPartyStageDataRow
function TableData.GetDancingPartyStageDataRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartyStageData", key, priority)
end


---@return _DancingPartyStageDataRow[]
function TableData.GetDancingPartyStageDataTable(priority)
	return Game.TableDataManager:GetData("DancingPartyStageData", priority)
end


---@return _DancingPartyQTEDataRow
function TableData.GetDancingPartyQTEDataRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartyQTEData", key, priority)
end


---@return _DancingPartyQTEDataRow[]
function TableData.GetDancingPartyQTEDataTable(priority)
	return Game.TableDataManager:GetData("DancingPartyQTEData", priority)
end


---@return _DancingPartyEvaluateLevelDataRow
function TableData.GetDancingPartyEvaluateLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartyEvaluateLevelData", key, priority)
end


---@return _DancingPartyEvaluateLevelDataRow[]
function TableData.GetDancingPartyEvaluateLevelDataTable(priority)
	return Game.TableDataManager:GetData("DancingPartyEvaluateLevelData", priority)
end


---@return _DancingPartySettingDataRow
function TableData.GetDancingPartySettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartySettingData", key, priority)
end


---@return _DancingPartySettingDataRow[]
function TableData.GetDancingPartySettingDataTable(priority)
	return Game.TableDataManager:GetData("DancingPartySettingData", priority)
end


---@return _DancingPartyHotRewardRow
function TableData.GetDancingPartyHotRewardRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartyHotReward", key, priority)
end


---@return _DancingPartyHotRewardRow[]
function TableData.GetDancingPartyHotRewardTable(priority)
	return Game.TableDataManager:GetData("DancingPartyHotReward", priority)
end


---@return _DancingPartyHotTaskRow
function TableData.GetDancingPartyHotTaskRow(key, priority)
	return Game.TableDataManager:GetRow("DancingPartyHotTask", key, priority)
end


---@return _DancingPartyHotTaskRow[]
function TableData.GetDancingPartyHotTaskTable(priority)
	return Game.TableDataManager:GetData("DancingPartyHotTask", priority)
end


---@return _WorldBossDataRow
function TableData.GetWorldBossDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldBossData", key, priority)
end


---@return _WorldBossDataRow[]
function TableData.GetWorldBossDataTable(priority)
	return Game.TableDataManager:GetData("WorldBossData", priority)
end


---@return _WorldBossSettingDataRow
function TableData.GetWorldBossSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldBossSettingData", key, priority)
end


---@return _WorldBossSettingDataRow[]
function TableData.GetWorldBossSettingDataTable(priority)
	return Game.TableDataManager:GetData("WorldBossSettingData", priority)
end


---@return _QTEAshButtonDataRow
function TableData.GetQTEAshButtonDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTEAshButtonData", key, priority)
end


---@return _QTEAshButtonDataRow[]
function TableData.GetQTEAshButtonDataTable(priority)
	return Game.TableDataManager:GetData("QTEAshButtonData", priority)
end


---@return _QTESequenceButtonDataRow
function TableData.GetQTESequenceButtonDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTESequenceButtonData", key, priority)
end


---@return _QTESequenceButtonDataRow[]
function TableData.GetQTESequenceButtonDataTable(priority)
	return Game.TableDataManager:GetData("QTESequenceButtonData", priority)
end


---@return _QTETrueFalseButtonDataRow
function TableData.GetQTETrueFalseButtonDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTETrueFalseButtonData", key, priority)
end


---@return _QTETrueFalseButtonDataRow[]
function TableData.GetQTETrueFalseButtonDataTable(priority)
	return Game.TableDataManager:GetData("QTETrueFalseButtonData", priority)
end


---@return _QTETimesButtonDataRow
function TableData.GetQTETimesButtonDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTETimesButtonData", key, priority)
end


---@return _QTETimesButtonDataRow[]
function TableData.GetQTETimesButtonDataTable(priority)
	return Game.TableDataManager:GetData("QTETimesButtonData", priority)
end


---@return _QTEStringConstDataRow
function TableData.GetQTEStringConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTEStringConstData", key, priority)
end


---@return _QTEStringConstDataRow[]
function TableData.GetQTEStringConstDataTable(priority)
	return Game.TableDataManager:GetData("QTEStringConstData", priority)
end


---@return _QTECutSceneSelectDataRow
function TableData.GetQTECutSceneSelectDataRow(key, priority)
	return Game.TableDataManager:GetRow("QTECutSceneSelectData", key, priority)
end


---@return _QTECutSceneSelectDataRow[]
function TableData.GetQTECutSceneSelectDataTable(priority)
	return Game.TableDataManager:GetData("QTECutSceneSelectData", priority)
end


---@return _ArtAssetIconDataRow
function TableData.GetArtAssetIconDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArtAssetIconData", key, priority)
end


---@return _ArtAssetIconDataRow[]
function TableData.GetArtAssetIconDataTable(priority)
	return Game.TableDataManager:GetData("ArtAssetIconData", priority)
end


---@return _ElementSingleEffectDataRow
function TableData.GetElementSingleEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("ElementSingleEffectData", key, priority)
end


---@return _ElementSingleEffectDataRow[]
function TableData.GetElementSingleEffectDataTable(priority)
	return Game.TableDataManager:GetData("ElementSingleEffectData", priority)
end


---@return _ElementEffectsDataRow
function TableData.GetElementEffectsDataRow(key, priority)
	return Game.TableDataManager:GetRow("ElementEffectsData", key, priority)
end


---@return _ElementEffectsDataRow[]
function TableData.GetElementEffectsDataTable(priority)
	return Game.TableDataManager:GetData("ElementEffectsData", priority)
end


---@return _EleEffectConstDataRow
function TableData.GetEleEffectConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleEffectConstData", key, priority)
end


---@return _EleEffectConstDataRow[]
function TableData.GetEleEffectConstDataTable(priority)
	return Game.TableDataManager:GetData("EleEffectConstData", priority)
end


---@return _ElementTypeDefDataRow
function TableData.GetElementTypeDefDataRow(key, priority)
	return Game.TableDataManager:GetRow("ElementTypeDefData", key, priority)
end


---@return _ElementTypeDefDataRow[]
function TableData.GetElementTypeDefDataTable(priority)
	return Game.TableDataManager:GetData("ElementTypeDefData", priority)
end


---@return _UIJumpDataRow
function TableData.GetUIJumpDataRow(key, priority)
	return Game.TableDataManager:GetRow("UIJumpData", key, priority)
end


---@return _UIJumpDataRow[]
function TableData.GetUIJumpDataTable(priority)
	return Game.TableDataManager:GetData("UIJumpData", priority)
end


---@return _TopupDataRow
function TableData.GetTopupDataRow(key, priority)
	return Game.TableDataManager:GetRow("TopupData", key, priority)
end


---@return _TopupDataRow[]
function TableData.GetTopupDataTable(priority)
	return Game.TableDataManager:GetData("TopupData", priority)
end


---@return _SefirotCoreEffectDataRow
function TableData.GetSefirotCoreEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("SefirotCoreEffectData", key, priority)
end


---@return _SefirotCoreEffectDataRow[]
function TableData.GetSefirotCoreEffectDataTable(priority)
	return Game.TableDataManager:GetData("SefirotCoreEffectData", priority)
end


---@return _SefirotCoreWordDataRow
function TableData.GetSefirotCoreWordDataRow(key, priority)
	return Game.TableDataManager:GetRow("SefirotCoreWordData", key, priority)
end


---@return _SefirotCoreWordDataRow[]
function TableData.GetSefirotCoreWordDataTable(priority)
	return Game.TableDataManager:GetData("SefirotCoreWordData", priority)
end


---@return _SefirotCoreConstIntDataRow
function TableData.GetSefirotCoreConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("SefirotCoreConstIntData", key, priority)
end


---@return _SefirotCoreConstIntDataRow[]
function TableData.GetSefirotCoreConstIntDataTable(priority)
	return Game.TableDataManager:GetData("SefirotCoreConstIntData", priority)
end


---@return _SefirotCoreStringDataRow
function TableData.GetSefirotCoreStringDataRow(key, priority)
	return Game.TableDataManager:GetRow("SefirotCoreStringData", key, priority)
end


---@return _SefirotCoreStringDataRow[]
function TableData.GetSefirotCoreStringDataTable(priority)
	return Game.TableDataManager:GetData("SefirotCoreStringData", priority)
end


---@return _RegionClimateDataRow
function TableData.GetRegionClimateDataRow(key, priority)
	return Game.TableDataManager:GetRow("RegionClimateData", key, priority)
end


---@return _RegionClimateDataRow[]
function TableData.GetRegionClimateDataTable(priority)
	return Game.TableDataManager:GetData("RegionClimateData", priority)
end


---@return _ClimateSettingDataRow
function TableData.GetClimateSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("ClimateSettingData", key, priority)
end


---@return _ClimateSettingDataRow[]
function TableData.GetClimateSettingDataTable(priority)
	return Game.TableDataManager:GetData("ClimateSettingData", priority)
end


---@return _GameTimeDataRow
function TableData.GetGameTimeDataRow(key, priority)
	return Game.TableDataManager:GetRow("GameTimeData", key, priority)
end


---@return _GameTimeDataRow[]
function TableData.GetGameTimeDataTable(priority)
	return Game.TableDataManager:GetData("GameTimeData", priority)
end


---@return _TimeSettingDataRow
function TableData.GetTimeSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("TimeSettingData", key, priority)
end


---@return _TimeSettingDataRow[]
function TableData.GetTimeSettingDataTable(priority)
	return Game.TableDataManager:GetData("TimeSettingData", priority)
end


---@return _ClimateConstDataRow
function TableData.GetClimateConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ClimateConstData", key, priority)
end


---@return _ClimateConstDataRow[]
function TableData.GetClimateConstDataTable(priority)
	return Game.TableDataManager:GetData("ClimateConstData", priority)
end


---@return _ClimatePhaseDataRow
function TableData.GetClimatePhaseDataRow(key, priority)
	return Game.TableDataManager:GetRow("ClimatePhaseData", key, priority)
end


---@return _ClimatePhaseDataRow[]
function TableData.GetClimatePhaseDataTable(priority)
	return Game.TableDataManager:GetData("ClimatePhaseData", priority)
end


---@return _CrowdNpcConfigDataRow
function TableData.GetCrowdNpcConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrowdNpcConfigData", key, priority)
end


---@return _CrowdNpcConfigDataRow[]
function TableData.GetCrowdNpcConfigDataTable(priority)
	return Game.TableDataManager:GetData("CrowdNpcConfigData", priority)
end


---@return _CrowdClimateSettingDataRow
function TableData.GetCrowdClimateSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrowdClimateSettingData", key, priority)
end


---@return _CrowdClimateSettingDataRow[]
function TableData.GetCrowdClimateSettingDataTable(priority)
	return Game.TableDataManager:GetData("CrowdClimateSettingData", priority)
end


---@return _CrowdNpcReactingDataRow
function TableData.GetCrowdNpcReactingDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrowdNpcReactingData", key, priority)
end


---@return _CrowdNpcReactingDataRow[]
function TableData.GetCrowdNpcReactingDataTable(priority)
	return Game.TableDataManager:GetData("CrowdNpcReactingData", priority)
end


---@return _CrowdNPCConstDataRow
function TableData.GetCrowdNPCConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrowdNPCConstData", key, priority)
end


---@return _CrowdNPCConstDataRow[]
function TableData.GetCrowdNPCConstDataTable(priority)
	return Game.TableDataManager:GetData("CrowdNPCConstData", priority)
end


---@return _CrowdNPCPerformanceSettingDataRow
function TableData.GetCrowdNPCPerformanceSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrowdNPCPerformanceSettingData", key, priority)
end


---@return _CrowdNPCPerformanceSettingDataRow[]
function TableData.GetCrowdNPCPerformanceSettingDataTable(priority)
	return Game.TableDataManager:GetData("CrowdNPCPerformanceSettingData", priority)
end


---@return _NewsTickerDataRow
function TableData.GetNewsTickerDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewsTickerData", key, priority)
end


---@return _NewsTickerDataRow[]
function TableData.GetNewsTickerDataTable(priority)
	return Game.TableDataManager:GetData("NewsTickerData", priority)
end


---@return _MarqueeTypeDataRow
function TableData.GetMarqueeTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("MarqueeTypeData", key, priority)
end


---@return _MarqueeTypeDataRow[]
function TableData.GetMarqueeTypeDataTable(priority)
	return Game.TableDataManager:GetData("MarqueeTypeData", priority)
end


---@return _MarqueeQueueTypeDataRow
function TableData.GetMarqueeQueueTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("MarqueeQueueTypeData", key, priority)
end


---@return _MarqueeQueueTypeDataRow[]
function TableData.GetMarqueeQueueTypeDataTable(priority)
	return Game.TableDataManager:GetData("MarqueeQueueTypeData", priority)
end


---@return _SpiritualVisionDataRow
function TableData.GetSpiritualVisionDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpiritualVisionData", key, priority)
end


---@return _SpiritualVisionDataRow[]
function TableData.GetSpiritualVisionDataTable(priority)
	return Game.TableDataManager:GetData("SpiritualVisionData", priority)
end


---@return _SpiritualVisionShaderDataRow
function TableData.GetSpiritualVisionShaderDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpiritualVisionShaderData", key, priority)
end


---@return _SpiritualVisionShaderDataRow[]
function TableData.GetSpiritualVisionShaderDataTable(priority)
	return Game.TableDataManager:GetData("SpiritualVisionShaderData", priority)
end


---@return _SpiritualVisionModelDataRow
function TableData.GetSpiritualVisionModelDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpiritualVisionModelData", key, priority)
end


---@return _SpiritualVisionModelDataRow[]
function TableData.GetSpiritualVisionModelDataTable(priority)
	return Game.TableDataManager:GetData("SpiritualVisionModelData", priority)
end


---@return _SpiritualSceneActorDataRow
function TableData.GetSpiritualSceneActorDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpiritualSceneActorData", key, priority)
end


---@return _SpiritualSceneActorDataRow[]
function TableData.GetSpiritualSceneActorDataTable(priority)
	return Game.TableDataManager:GetData("SpiritualSceneActorData", priority)
end


---@return _InvisibleHandInteractDataRow
function TableData.GetInvisibleHandInteractDataRow(key, priority)
	return Game.TableDataManager:GetRow("InvisibleHandInteractData", key, priority)
end


---@return _InvisibleHandInteractDataRow[]
function TableData.GetInvisibleHandInteractDataTable(priority)
	return Game.TableDataManager:GetData("InvisibleHandInteractData", priority)
end


---@return _SequenceDataRow
function TableData.GetSequenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("SequenceData", key, priority)
end


---@return _SequenceDataRow[]
function TableData.GetSequenceDataTable(priority)
	return Game.TableDataManager:GetData("SequenceData", priority)
end


---@return _DigestionDataRow
function TableData.GetDigestionDataRow(key, priority)
	return Game.TableDataManager:GetRow("DigestionData", key, priority)
end


---@return _DigestionDataRow[]
function TableData.GetDigestionDataTable(priority)
	return Game.TableDataManager:GetData("DigestionData", priority)
end


---@return _DigestionConditionDataRow
function TableData.GetDigestionConditionDataRow(key, priority)
	return Game.TableDataManager:GetRow("DigestionConditionData", key, priority)
end


---@return _DigestionConditionDataRow[]
function TableData.GetDigestionConditionDataTable(priority)
	return Game.TableDataManager:GetData("DigestionConditionData", priority)
end


---@return _DigestionConditionTypeDataRow
function TableData.GetDigestionConditionTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("DigestionConditionTypeData", key, priority)
end


---@return _DigestionConditionTypeDataRow[]
function TableData.GetDigestionConditionTypeDataTable(priority)
	return Game.TableDataManager:GetData("DigestionConditionTypeData", priority)
end


---@return _ProfessionSequenceDataRow
function TableData.GetProfessionSequenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("ProfessionSequenceData", key, priority)
end


---@return _ProfessionSequenceDataRow[]
function TableData.GetProfessionSequenceDataTable(priority)
	return Game.TableDataManager:GetData("ProfessionSequenceData", priority)
end


---@return _SequenceRecipeDataRow
function TableData.GetSequenceRecipeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SequenceRecipeData", key, priority)
end


---@return _SequenceRecipeDataRow[]
function TableData.GetSequenceRecipeDataTable(priority)
	return Game.TableDataManager:GetData("SequenceRecipeData", priority)
end


---@return _SequenceRecipeMaterialDataRow
function TableData.GetSequenceRecipeMaterialDataRow(key, priority)
	return Game.TableDataManager:GetRow("SequenceRecipeMaterialData", key, priority)
end


---@return _SequenceRecipeMaterialDataRow[]
function TableData.GetSequenceRecipeMaterialDataTable(priority)
	return Game.TableDataManager:GetData("SequenceRecipeMaterialData", priority)
end


---@return _ActRuleDataRow
function TableData.GetActRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("ActRuleData", key, priority)
end


---@return _ActRuleDataRow[]
function TableData.GetActRuleDataTable(priority)
	return Game.TableDataManager:GetData("ActRuleData", priority)
end


---@return _CrazyTalkDataRow
function TableData.GetCrazyTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrazyTalkData", key, priority)
end


---@return _CrazyTalkDataRow[]
function TableData.GetCrazyTalkDataTable(priority)
	return Game.TableDataManager:GetData("CrazyTalkData", priority)
end


---@return _VerticleTalkDataRow
function TableData.GetVerticleTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("VerticleTalkData", key, priority)
end


---@return _VerticleTalkDataRow[]
function TableData.GetVerticleTalkDataTable(priority)
	return Game.TableDataManager:GetData("VerticleTalkData", priority)
end


---@return _IntConstSequenceDataRow
function TableData.GetIntConstSequenceDataRow(key, priority)
	return Game.TableDataManager:GetRow("IntConstSequenceData", key, priority)
end


---@return _IntConstSequenceDataRow[]
function TableData.GetIntConstSequenceDataTable(priority)
	return Game.TableDataManager:GetData("IntConstSequenceData", priority)
end


---@return _ScheduleDailyTaskDataRow
function TableData.GetScheduleDailyTaskDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleDailyTaskData", key, priority)
end


---@return _ScheduleDailyTaskDataRow[]
function TableData.GetScheduleDailyTaskDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleDailyTaskData", priority)
end


---@return _ScheduleDailyTaskTypeDataRow
function TableData.GetScheduleDailyTaskTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleDailyTaskTypeData", key, priority)
end


---@return _ScheduleDailyTaskTypeDataRow[]
function TableData.GetScheduleDailyTaskTypeDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleDailyTaskTypeData", priority)
end


---@return _ScheduleRevelationDataRow
function TableData.GetScheduleRevelationDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleRevelationData", key, priority)
end


---@return _ScheduleRevelationDataRow[]
function TableData.GetScheduleRevelationDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleRevelationData", priority)
end


---@return _ScheduleStageRewardDataRow
function TableData.GetScheduleStageRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleStageRewardData", key, priority)
end


---@return _ScheduleStageRewardDataRow[]
function TableData.GetScheduleStageRewardDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleStageRewardData", priority)
end


---@return _ScheduleConstDataRow
function TableData.GetScheduleConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleConstData", key, priority)
end


---@return _ScheduleConstDataRow[]
function TableData.GetScheduleConstDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleConstData", priority)
end


---@return _ScheduleTabDataRow
function TableData.GetScheduleTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScheduleTabData", key, priority)
end


---@return _ScheduleTabDataRow[]
function TableData.GetScheduleTabDataTable(priority)
	return Game.TableDataManager:GetData("ScheduleTabData", priority)
end


---@return _NewbieGuideGroupDataRow
function TableData.GetNewbieGuideGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewbieGuideGroupData", key, priority)
end


---@return _NewbieGuideGroupDataRow[]
function TableData.GetNewbieGuideGroupDataTable(priority)
	return Game.TableDataManager:GetData("NewbieGuideGroupData", priority)
end


---@return _NewbieGuideOperationDataRow
function TableData.GetNewbieGuideOperationDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewbieGuideOperationData", key, priority)
end


---@return _NewbieGuideOperationDataRow[]
function TableData.GetNewbieGuideOperationDataTable(priority)
	return Game.TableDataManager:GetData("NewbieGuideOperationData", priority)
end


---@return _NewbieGuideImgTextDataRow
function TableData.GetNewbieGuideImgTextDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewbieGuideImgTextData", key, priority)
end


---@return _NewbieGuideImgTextDataRow[]
function TableData.GetNewbieGuideImgTextDataTable(priority)
	return Game.TableDataManager:GetData("NewbieGuideImgTextData", priority)
end


---@return _KeyMappingDataRow
function TableData.GetKeyMappingDataRow(key, priority)
	return Game.TableDataManager:GetRow("KeyMappingData", key, priority)
end


---@return _KeyMappingDataRow[]
function TableData.GetKeyMappingDataTable(priority)
	return Game.TableDataManager:GetData("KeyMappingData", priority)
end


---@return _BossMechanismCoolDownDataRow
function TableData.GetBossMechanismCoolDownDataRow(key, priority)
	return Game.TableDataManager:GetRow("BossMechanismCoolDownData", key, priority)
end


---@return _BossMechanismCoolDownDataRow[]
function TableData.GetBossMechanismCoolDownDataTable(priority)
	return Game.TableDataManager:GetData("BossMechanismCoolDownData", priority)
end


---@return _ActivityDataRow
function TableData.GetActivityDataRow(key, priority)
	return Game.TableDataManager:GetRow("ActivityData", key, priority)
end


---@return _ActivityDataRow[]
function TableData.GetActivityDataTable(priority)
	return Game.TableDataManager:GetData("ActivityData", priority)
end


---@return _ActivityTypeDataRow
function TableData.GetActivityTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ActivityTypeData", key, priority)
end


---@return _ActivityTypeDataRow[]
function TableData.GetActivityTypeDataTable(priority)
	return Game.TableDataManager:GetData("ActivityTypeData", priority)
end


---@return _ActivityTabDataRow
function TableData.GetActivityTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("ActivityTabData", key, priority)
end


---@return _ActivityTabDataRow[]
function TableData.GetActivityTabDataTable(priority)
	return Game.TableDataManager:GetData("ActivityTabData", priority)
end


---@return _RedNameStageDataRow
function TableData.GetRedNameStageDataRow(key, priority)
	return Game.TableDataManager:GetRow("RedNameStageData", key, priority)
end


---@return _RedNameStageDataRow[]
function TableData.GetRedNameStageDataTable(priority)
	return Game.TableDataManager:GetData("RedNameStageData", priority)
end


---@return _RedNameConstIntDataRow
function TableData.GetRedNameConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("RedNameConstIntData", key, priority)
end


---@return _RedNameConstIntDataRow[]
function TableData.GetRedNameConstIntDataTable(priority)
	return Game.TableDataManager:GetData("RedNameConstIntData", priority)
end


---@return _CrimeConstFloatDataRow
function TableData.GetCrimeConstFloatDataRow(key, priority)
	return Game.TableDataManager:GetRow("CrimeConstFloatData", key, priority)
end


---@return _CrimeConstFloatDataRow[]
function TableData.GetCrimeConstFloatDataTable(priority)
	return Game.TableDataManager:GetData("CrimeConstFloatData", priority)
end


---@return _BattleModeDataRow
function TableData.GetBattleModeDataRow(key, priority)
	return Game.TableDataManager:GetRow("BattleModeData", key, priority)
end


---@return _BattleModeDataRow[]
function TableData.GetBattleModeDataTable(priority)
	return Game.TableDataManager:GetData("BattleModeData", priority)
end


---@return _RedNameBountyEventDataRow
function TableData.GetRedNameBountyEventDataRow(key, priority)
	return Game.TableDataManager:GetRow("RedNameBountyEventData", key, priority)
end


---@return _RedNameBountyEventDataRow[]
function TableData.GetRedNameBountyEventDataTable(priority)
	return Game.TableDataManager:GetData("RedNameBountyEventData", priority)
end


---@return _CameraConstDataRow
function TableData.GetCameraConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraConstData", key, priority)
end


---@return _CameraConstDataRow[]
function TableData.GetCameraConstDataTable(priority)
	return Game.TableDataManager:GetData("CameraConstData", priority)
end


---@return _ArmNxNDataRow
function TableData.GetArmNxNDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArmNxNData", key, priority)
end


---@return _ArmNxNDataRow[]
function TableData.GetArmNxNDataTable(priority)
	return Game.TableDataManager:GetData("ArmNxNData", priority)
end


---@return _DirNxNDataRow
function TableData.GetDirNxNDataRow(key, priority)
	return Game.TableDataManager:GetRow("DirNxNData", key, priority)
end


---@return _DirNxNDataRow[]
function TableData.GetDirNxNDataTable(priority)
	return Game.TableDataManager:GetData("DirNxNData", priority)
end


---@return _EaseNxNDataRow
function TableData.GetEaseNxNDataRow(key, priority)
	return Game.TableDataManager:GetRow("EaseNxNData", key, priority)
end


---@return _EaseNxNDataRow[]
function TableData.GetEaseNxNDataTable(priority)
	return Game.TableDataManager:GetData("EaseNxNData", priority)
end


---@return _RouteNxNDataRow
function TableData.GetRouteNxNDataRow(key, priority)
	return Game.TableDataManager:GetRow("RouteNxNData", key, priority)
end


---@return _RouteNxNDataRow[]
function TableData.GetRouteNxNDataTable(priority)
	return Game.TableDataManager:GetData("RouteNxNData", priority)
end


---@return _CameraCurveConfigDataRow
function TableData.GetCameraCurveConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraCurveConfigData", key, priority)
end


---@return _CameraCurveConfigDataRow[]
function TableData.GetCameraCurveConfigDataTable(priority)
	return Game.TableDataManager:GetData("CameraCurveConfigData", priority)
end


---@return _CameraModesConfigDataRow
function TableData.GetCameraModesConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraModesConfigData", key, priority)
end


---@return _CameraModesConfigDataRow[]
function TableData.GetCameraModesConfigDataTable(priority)
	return Game.TableDataManager:GetData("CameraModesConfigData", priority)
end


---@return _CameraModeModifierConfigDataRow
function TableData.GetCameraModeModifierConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraModeModifierConfigData", key, priority)
end


---@return _CameraModeModifierConfigDataRow[]
function TableData.GetCameraModeModifierConfigDataTable(priority)
	return Game.TableDataManager:GetData("CameraModeModifierConfigData", priority)
end


---@return _CameraModifierPriorityConfigDataRow
function TableData.GetCameraModifierPriorityConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraModifierPriorityConfigData", key, priority)
end


---@return _CameraModifierPriorityConfigDataRow[]
function TableData.GetCameraModifierPriorityConfigDataTable(priority)
	return Game.TableDataManager:GetData("CameraModifierPriorityConfigData", priority)
end


---@return _CameraShakeConfigDataRow
function TableData.GetCameraShakeConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("CameraShakeConfigData", key, priority)
end


---@return _CameraShakeConfigDataRow[]
function TableData.GetCameraShakeConfigDataTable(priority)
	return Game.TableDataManager:GetData("CameraShakeConfigData", priority)
end


---@return _LevelCameraConfigDataRow
function TableData.GetLevelCameraConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelCameraConfigData", key, priority)
end


---@return _LevelCameraConfigDataRow[]
function TableData.GetLevelCameraConfigDataTable(priority)
	return Game.TableDataManager:GetData("LevelCameraConfigData", priority)
end


---@return _StallTabInfoRow
function TableData.GetStallTabInfoRow(key, priority)
	return Game.TableDataManager:GetRow("StallTabInfo", key, priority)
end


---@return _StallTabInfoRow[]
function TableData.GetStallTabInfoTable(priority)
	return Game.TableDataManager:GetData("StallTabInfo", priority)
end


---@return _StallTabDataRow
function TableData.GetStallTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("StallTabData", key, priority)
end


---@return _StallTabDataRow[]
function TableData.GetStallTabDataTable(priority)
	return Game.TableDataManager:GetData("StallTabData", priority)
end


---@return _StallDataRow
function TableData.GetStallDataRow(key, priority)
	return Game.TableDataManager:GetRow("StallData", key, priority)
end


---@return _StallDataRow[]
function TableData.GetStallDataTable(priority)
	return Game.TableDataManager:GetData("StallData", priority)
end


---@return _StallMarketInterventionDataRow
function TableData.GetStallMarketInterventionDataRow(key, priority)
	return Game.TableDataManager:GetRow("StallMarketInterventionData", key, priority)
end


---@return _StallMarketInterventionDataRow[]
function TableData.GetStallMarketInterventionDataTable(priority)
	return Game.TableDataManager:GetData("StallMarketInterventionData", priority)
end


---@return _StallEquipAttribSearchDataRow
function TableData.GetStallEquipAttribSearchDataRow(key, priority)
	return Game.TableDataManager:GetRow("StallEquipAttribSearchData", key, priority)
end


---@return _StallEquipAttribSearchDataRow[]
function TableData.GetStallEquipAttribSearchDataTable(priority)
	return Game.TableDataManager:GetData("StallEquipAttribSearchData", priority)
end


---@return _StallCellUnlockDataRow
function TableData.GetStallCellUnlockDataRow(key, priority)
	return Game.TableDataManager:GetRow("StallCellUnlockData", key, priority)
end


---@return _StallCellUnlockDataRow[]
function TableData.GetStallCellUnlockDataTable(priority)
	return Game.TableDataManager:GetData("StallCellUnlockData", priority)
end


---@return _ExchangeHouseQualityDataRow
function TableData.GetExchangeHouseQualityDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExchangeHouseQualityData", key, priority)
end


---@return _ExchangeHouseQualityDataRow[]
function TableData.GetExchangeHouseQualityDataTable(priority)
	return Game.TableDataManager:GetData("ExchangeHouseQualityData", priority)
end


---@return _ExchangeHouseConstIntDataRow
function TableData.GetExchangeHouseConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExchangeHouseConstIntData", key, priority)
end


---@return _ExchangeHouseConstIntDataRow[]
function TableData.GetExchangeHouseConstIntDataTable(priority)
	return Game.TableDataManager:GetData("ExchangeHouseConstIntData", priority)
end


---@return _ServerLevelDataRow
function TableData.GetServerLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("ServerLevelData", key, priority)
end


---@return _ServerLevelDataRow[]
function TableData.GetServerLevelDataTable(priority)
	return Game.TableDataManager:GetData("ServerLevelData", priority)
end


---@return _GameSeasonDataRow
function TableData.GetGameSeasonDataRow(key, priority)
	return Game.TableDataManager:GetRow("GameSeasonData", key, priority)
end


---@return _GameSeasonDataRow[]
function TableData.GetGameSeasonDataTable(priority)
	return Game.TableDataManager:GetData("GameSeasonData", priority)
end


---@return _GameSeasonVersionDataRow
function TableData.GetGameSeasonVersionDataRow(key, priority)
	return Game.TableDataManager:GetRow("GameSeasonVersionData", key, priority)
end


---@return _GameSeasonVersionDataRow[]
function TableData.GetGameSeasonVersionDataTable(priority)
	return Game.TableDataManager:GetData("GameSeasonVersionData", priority)
end


---@return _RankRegionDataRow
function TableData.GetRankRegionDataRow(key, priority)
	return Game.TableDataManager:GetRow("RankRegionData", key, priority)
end


---@return _RankRegionDataRow[]
function TableData.GetRankRegionDataTable(priority)
	return Game.TableDataManager:GetData("RankRegionData", priority)
end


---@return _WorldChannelQuizBankDataRow
function TableData.GetWorldChannelQuizBankDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldChannelQuizBankData", key, priority)
end


---@return _WorldChannelQuizBankDataRow[]
function TableData.GetWorldChannelQuizBankDataTable(priority)
	return Game.TableDataManager:GetData("WorldChannelQuizBankData", priority)
end


---@return _WorldChannelQuizConstIntDataRow
function TableData.GetWorldChannelQuizConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldChannelQuizConstIntData", key, priority)
end


---@return _WorldChannelQuizConstIntDataRow[]
function TableData.GetWorldChannelQuizConstIntDataTable(priority)
	return Game.TableDataManager:GetData("WorldChannelQuizConstIntData", priority)
end


---@return _WorldChannelQuizConstStringDataRow
function TableData.GetWorldChannelQuizConstStringDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldChannelQuizConstStringData", key, priority)
end


---@return _WorldChannelQuizConstStringDataRow[]
function TableData.GetWorldChannelQuizConstStringDataTable(priority)
	return Game.TableDataManager:GetData("WorldChannelQuizConstStringData", priority)
end


---@return _AreaDataRow
function TableData.GetAreaDataRow(key, priority)
	return Game.TableDataManager:GetRow("AreaData", key, priority)
end


---@return _AreaDataRow[]
function TableData.GetAreaDataTable(priority)
	return Game.TableDataManager:GetData("AreaData", priority)
end


---@return _AppearanceOverrideDataRow
function TableData.GetAppearanceOverrideDataRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceOverrideData", key, priority)
end


---@return _AppearanceOverrideDataRow[]
function TableData.GetAppearanceOverrideDataTable(priority)
	return Game.TableDataManager:GetData("AppearanceOverrideData", priority)
end


---@return _MorphDataRow
function TableData.GetMorphDataRow(key, priority)
	return Game.TableDataManager:GetRow("MorphData", key, priority)
end


---@return _MorphDataRow[]
function TableData.GetMorphDataTable(priority)
	return Game.TableDataManager:GetData("MorphData", priority)
end


---@return _MorphPlayDataRow
function TableData.GetMorphPlayDataRow(key, priority)
	return Game.TableDataManager:GetRow("MorphPlayData", key, priority)
end


---@return _MorphPlayDataRow[]
function TableData.GetMorphPlayDataTable(priority)
	return Game.TableDataManager:GetData("MorphPlayData", priority)
end


---@return _RolePlayPropertyDataRow
function TableData.GetRolePlayPropertyDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayPropertyData", key, priority)
end


---@return _RolePlayPropertyDataRow[]
function TableData.GetRolePlayPropertyDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayPropertyData", priority)
end


---@return _RolePlayPropertyLevelDataRow
function TableData.GetRolePlayPropertyLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayPropertyLevelData", key, priority)
end


---@return _RolePlayPropertyLevelDataRow[]
function TableData.GetRolePlayPropertyLevelDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayPropertyLevelData", priority)
end


---@return _RolePlayTalentTreeDataRow
function TableData.GetRolePlayTalentTreeDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayTalentTreeData", key, priority)
end


---@return _RolePlayTalentTreeDataRow[]
function TableData.GetRolePlayTalentTreeDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayTalentTreeData", priority)
end


---@return _RolePlayIdentityDataRow
function TableData.GetRolePlayIdentityDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayIdentityData", key, priority)
end


---@return _RolePlayIdentityDataRow[]
function TableData.GetRolePlayIdentityDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayIdentityData", priority)
end


---@return _RolePlaySourceDataRow
function TableData.GetRolePlaySourceDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlaySourceData", key, priority)
end


---@return _RolePlaySourceDataRow[]
function TableData.GetRolePlaySourceDataTable(priority)
	return Game.TableDataManager:GetData("RolePlaySourceData", priority)
end


---@return _RolePlayEffectDataRow
function TableData.GetRolePlayEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayEffectData", key, priority)
end


---@return _RolePlayEffectDataRow[]
function TableData.GetRolePlayEffectDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayEffectData", priority)
end


---@return _RolePlayConstDataRow
function TableData.GetRolePlayConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("RolePlayConstData", key, priority)
end


---@return _RolePlayConstDataRow[]
function TableData.GetRolePlayConstDataTable(priority)
	return Game.TableDataManager:GetData("RolePlayConstData", priority)
end


---@return _RPGameScriptDataRow
function TableData.GetRPGameScriptDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameScriptData", key, priority)
end


---@return _RPGameScriptDataRow[]
function TableData.GetRPGameScriptDataTable(priority)
	return Game.TableDataManager:GetData("RPGameScriptData", priority)
end


---@return _RPGameChapterDataRow
function TableData.GetRPGameChapterDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameChapterData", key, priority)
end


---@return _RPGameChapterDataRow[]
function TableData.GetRPGameChapterDataTable(priority)
	return Game.TableDataManager:GetData("RPGameChapterData", priority)
end


---@return _RPGameIdentityDataRow
function TableData.GetRPGameIdentityDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameIdentityData", key, priority)
end


---@return _RPGameIdentityDataRow[]
function TableData.GetRPGameIdentityDataTable(priority)
	return Game.TableDataManager:GetData("RPGameIdentityData", priority)
end


---@return _RPGameMapDataRow
function TableData.GetRPGameMapDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameMapData", key, priority)
end


---@return _RPGameMapDataRow[]
function TableData.GetRPGameMapDataTable(priority)
	return Game.TableDataManager:GetData("RPGameMapData", priority)
end


---@return _RPGameMoneyDataRow
function TableData.GetRPGameMoneyDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameMoneyData", key, priority)
end


---@return _RPGameMoneyDataRow[]
function TableData.GetRPGameMoneyDataTable(priority)
	return Game.TableDataManager:GetData("RPGameMoneyData", priority)
end


---@return _RPGameItemDataRow
function TableData.GetRPGameItemDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameItemData", key, priority)
end


---@return _RPGameItemDataRow[]
function TableData.GetRPGameItemDataTable(priority)
	return Game.TableDataManager:GetData("RPGameItemData", priority)
end


---@return _RPGameEffectDataRow
function TableData.GetRPGameEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameEffectData", key, priority)
end


---@return _RPGameEffectDataRow[]
function TableData.GetRPGameEffectDataTable(priority)
	return Game.TableDataManager:GetData("RPGameEffectData", priority)
end


---@return _RPGameShopDataRow
function TableData.GetRPGameShopDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameShopData", key, priority)
end


---@return _RPGameShopDataRow[]
function TableData.GetRPGameShopDataTable(priority)
	return Game.TableDataManager:GetData("RPGameShopData", priority)
end


---@return _RPGameEventPoolDataRow
function TableData.GetRPGameEventPoolDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameEventPoolData", key, priority)
end


---@return _RPGameEventPoolDataRow[]
function TableData.GetRPGameEventPoolDataTable(priority)
	return Game.TableDataManager:GetData("RPGameEventPoolData", priority)
end


---@return _RPGameEventDataRow
function TableData.GetRPGameEventDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameEventData", key, priority)
end


---@return _RPGameEventDataRow[]
function TableData.GetRPGameEventDataTable(priority)
	return Game.TableDataManager:GetData("RPGameEventData", priority)
end


---@return _RPGameDialogueDataRow
function TableData.GetRPGameDialogueDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameDialogueData", key, priority)
end


---@return _RPGameDialogueDataRow[]
function TableData.GetRPGameDialogueDataTable(priority)
	return Game.TableDataManager:GetData("RPGameDialogueData", priority)
end


---@return _RPGameDialogueOptionsDataRow
function TableData.GetRPGameDialogueOptionsDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameDialogueOptionsData", key, priority)
end


---@return _RPGameDialogueOptionsDataRow[]
function TableData.GetRPGameDialogueOptionsDataTable(priority)
	return Game.TableDataManager:GetData("RPGameDialogueOptionsData", priority)
end


---@return _RPGameTargetDataRow
function TableData.GetRPGameTargetDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameTargetData", key, priority)
end


---@return _RPGameTargetDataRow[]
function TableData.GetRPGameTargetDataTable(priority)
	return Game.TableDataManager:GetData("RPGameTargetData", priority)
end


---@return _RPGameConstDataRow
function TableData.GetRPGameConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("RPGameConstData", key, priority)
end


---@return _RPGameConstDataRow[]
function TableData.GetRPGameConstDataTable(priority)
	return Game.TableDataManager:GetData("RPGameConstData", priority)
end


---@return _PVPGameModeDataRow
function TableData.GetPVPGameModeDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPGameModeData", key, priority)
end


---@return _PVPGameModeDataRow[]
function TableData.GetPVPGameModeDataTable(priority)
	return Game.TableDataManager:GetData("PVPGameModeData", priority)
end


---@return _PVPRoundConfigDataRow
function TableData.GetPVPRoundConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPRoundConfigData", key, priority)
end


---@return _PVPRoundConfigDataRow[]
function TableData.GetPVPRoundConfigDataTable(priority)
	return Game.TableDataManager:GetData("PVPRoundConfigData", priority)
end


---@return _PVPCombatStatisticsTypeDataRow
function TableData.GetPVPCombatStatisticsTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPCombatStatisticsTypeData", key, priority)
end


---@return _PVPCombatStatisticsTypeDataRow[]
function TableData.GetPVPCombatStatisticsTypeDataTable(priority)
	return Game.TableDataManager:GetData("PVPCombatStatisticsTypeData", priority)
end


---@return _BattleNoticeDataRow
function TableData.GetBattleNoticeDataRow(key, priority)
	return Game.TableDataManager:GetRow("BattleNoticeData", key, priority)
end


---@return _BattleNoticeDataRow[]
function TableData.GetBattleNoticeDataTable(priority)
	return Game.TableDataManager:GetData("BattleNoticeData", priority)
end


---@return _5V5SettingDataRow
function TableData.Get5V5SettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("5V5SettingData", key, priority)
end


---@return _5V5SettingDataRow[]
function TableData.Get5V5SettingDataTable(priority)
	return Game.TableDataManager:GetData("5V5SettingData", priority)
end


---@return _3V3SettingDataRow
function TableData.Get3V3SettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("3V3SettingData", key, priority)
end


---@return _3V3SettingDataRow[]
function TableData.Get3V3SettingDataTable(priority)
	return Game.TableDataManager:GetData("3V3SettingData", priority)
end


---@return _1V1SettingDataRow
function TableData.Get1V1SettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("1V1SettingData", key, priority)
end


---@return _1V1SettingDataRow[]
function TableData.Get1V1SettingDataTable(priority)
	return Game.TableDataManager:GetData("1V1SettingData", priority)
end


---@return _12V12SettingDataRow
function TableData.Get12V12SettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("12V12SettingData", key, priority)
end


---@return _12V12SettingDataRow[]
function TableData.Get12V12SettingDataTable(priority)
	return Game.TableDataManager:GetData("12V12SettingData", priority)
end


---@return _PVPBattleOrderDataRow
function TableData.GetPVPBattleOrderDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPBattleOrderData", key, priority)
end


---@return _PVPBattleOrderDataRow[]
function TableData.GetPVPBattleOrderDataTable(priority)
	return Game.TableDataManager:GetData("PVPBattleOrderData", priority)
end


---@return _PVPBattleTitleDataRow
function TableData.GetPVPBattleTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPBattleTitleData", key, priority)
end


---@return _PVPBattleTitleDataRow[]
function TableData.GetPVPBattleTitleDataTable(priority)
	return Game.TableDataManager:GetData("PVPBattleTitleData", priority)
end


---@return _PVPTabDataRow
function TableData.GetPVPTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPTabData", key, priority)
end


---@return _PVPTabDataRow[]
function TableData.GetPVPTabDataTable(priority)
	return Game.TableDataManager:GetData("PVPTabData", priority)
end


---@return _3V3RankDataRow
function TableData.Get3V3RankDataRow(key, priority)
	return Game.TableDataManager:GetRow("3V3RankData", key, priority)
end


---@return _3V3RankDataRow[]
function TableData.Get3V3RankDataTable(priority)
	return Game.TableDataManager:GetData("3V3RankData", priority)
end


---@return _12V12RankDataRow
function TableData.Get12V12RankDataRow(key, priority)
	return Game.TableDataManager:GetRow("12V12RankData", key, priority)
end


---@return _12V12RankDataRow[]
function TableData.Get12V12RankDataTable(priority)
	return Game.TableDataManager:GetData("12V12RankData", priority)
end


---@return _PVPSeasonDataRow
function TableData.GetPVPSeasonDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPSeasonData", key, priority)
end


---@return _PVPSeasonDataRow[]
function TableData.GetPVPSeasonDataTable(priority)
	return Game.TableDataManager:GetData("PVPSeasonData", priority)
end


---@return _ReadyCountDownImageDataRow
function TableData.GetReadyCountDownImageDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReadyCountDownImageData", key, priority)
end


---@return _ReadyCountDownImageDataRow[]
function TableData.GetReadyCountDownImageDataTable(priority)
	return Game.TableDataManager:GetData("ReadyCountDownImageData", priority)
end


---@return _3V3ConstDataRow
function TableData.Get3V3ConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("3V3ConstData", key, priority)
end


---@return _3V3ConstDataRow[]
function TableData.Get3V3ConstDataTable(priority)
	return Game.TableDataManager:GetData("3V3ConstData", priority)
end


---@return _5V5RankDataRow
function TableData.Get5V5RankDataRow(key, priority)
	return Game.TableDataManager:GetRow("5V5RankData", key, priority)
end


---@return _5V5RankDataRow[]
function TableData.Get5V5RankDataTable(priority)
	return Game.TableDataManager:GetData("5V5RankData", priority)
end


---@return _5V5ConstDataRow
function TableData.Get5V5ConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("5V5ConstData", key, priority)
end


---@return _5V5ConstDataRow[]
function TableData.Get5V5ConstDataTable(priority)
	return Game.TableDataManager:GetData("5V5ConstData", priority)
end


---@return _3V3SeasonConfigDataRow
function TableData.Get3V3SeasonConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("3V3SeasonConfigData", key, priority)
end


---@return _3V3SeasonConfigDataRow[]
function TableData.Get3V3SeasonConfigDataTable(priority)
	return Game.TableDataManager:GetData("3V3SeasonConfigData", priority)
end


---@return _5V5SeasonConfigDataRow
function TableData.Get5V5SeasonConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("5V5SeasonConfigData", key, priority)
end


---@return _5V5SeasonConfigDataRow[]
function TableData.Get5V5SeasonConfigDataTable(priority)
	return Game.TableDataManager:GetData("5V5SeasonConfigData", priority)
end


---@return _12V12ConstDataRow
function TableData.Get12V12ConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("12V12ConstData", key, priority)
end


---@return _12V12ConstDataRow[]
function TableData.Get12V12ConstDataTable(priority)
	return Game.TableDataManager:GetData("12V12ConstData", priority)
end


---@return _12V12SeasonConfigDataRow
function TableData.Get12V12SeasonConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("12V12SeasonConfigData", key, priority)
end


---@return _12V12SeasonConfigDataRow[]
function TableData.Get12V12SeasonConfigDataTable(priority)
	return Game.TableDataManager:GetData("12V12SeasonConfigData", priority)
end


---@return _PVPEntranceInfoDataRow
function TableData.GetPVPEntranceInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPEntranceInfoData", key, priority)
end


---@return _PVPEntranceInfoDataRow[]
function TableData.GetPVPEntranceInfoDataTable(priority)
	return Game.TableDataManager:GetData("PVPEntranceInfoData", priority)
end


---@return _PVPEntranceConstDataRow
function TableData.GetPVPEntranceConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPEntranceConstData", key, priority)
end


---@return _PVPEntranceConstDataRow[]
function TableData.GetPVPEntranceConstDataTable(priority)
	return Game.TableDataManager:GetData("PVPEntranceConstData", priority)
end


---@return _PVPTypeDataRow
function TableData.GetPVPTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("PVPTypeData", key, priority)
end


---@return _PVPTypeDataRow[]
function TableData.GetPVPTypeDataTable(priority)
	return Game.TableDataManager:GetData("PVPTypeData", priority)
end


---@return _ReportDataRow
function TableData.GetReportDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReportData", key, priority)
end


---@return _ReportDataRow[]
function TableData.GetReportDataTable(priority)
	return Game.TableDataManager:GetData("ReportData", priority)
end


---@return _SystemActionEnumRow
function TableData.GetSystemActionEnumRow(key, priority)
	return Game.TableDataManager:GetRow("SystemActionEnum", key, priority)
end


---@return _SystemActionEnumRow[]
function TableData.GetSystemActionEnumTable(priority)
	return Game.TableDataManager:GetData("SystemActionEnum", priority)
end


---@return _DropSystemActionEnumRow
function TableData.GetDropSystemActionEnumRow(key, priority)
	return Game.TableDataManager:GetRow("DropSystemActionEnum", key, priority)
end


---@return _DropSystemActionEnumRow[]
function TableData.GetDropSystemActionEnumTable(priority)
	return Game.TableDataManager:GetData("DropSystemActionEnum", priority)
end


---@return _StaminaConstDataRow
function TableData.GetStaminaConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("StaminaConstData", key, priority)
end


---@return _StaminaConstDataRow[]
function TableData.GetStaminaConstDataTable(priority)
	return Game.TableDataManager:GetData("StaminaConstData", priority)
end


---@return _AdditionalSkillConstDataRow
function TableData.GetAdditionalSkillConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("AdditionalSkillConstData", key, priority)
end


---@return _AdditionalSkillConstDataRow[]
function TableData.GetAdditionalSkillConstDataTable(priority)
	return Game.TableDataManager:GetData("AdditionalSkillConstData", priority)
end


---@return _BidDataRow
function TableData.GetBidDataRow(key, priority)
	return Game.TableDataManager:GetRow("BidData", key, priority)
end


---@return _BidDataRow[]
function TableData.GetBidDataTable(priority)
	return Game.TableDataManager:GetData("BidData", priority)
end


---@return _BidItemDataRow
function TableData.GetBidItemDataRow(key, priority)
	return Game.TableDataManager:GetRow("BidItemData", key, priority)
end


---@return _BidItemDataRow[]
function TableData.GetBidItemDataTable(priority)
	return Game.TableDataManager:GetData("BidItemData", priority)
end


---@return _GuildMaterialTaskItemWeightDataRow
function TableData.GetGuildMaterialTaskItemWeightDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildMaterialTaskItemWeightData", key, priority)
end


---@return _GuildMaterialTaskItemWeightDataRow[]
function TableData.GetGuildMaterialTaskItemWeightDataTable(priority)
	return Game.TableDataManager:GetData("GuildMaterialTaskItemWeightData", priority)
end


---@return _GuildMaterialTaskRarityWeightDataRow
function TableData.GetGuildMaterialTaskRarityWeightDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildMaterialTaskRarityWeightData", key, priority)
end


---@return _GuildMaterialTaskRarityWeightDataRow[]
function TableData.GetGuildMaterialTaskRarityWeightDataTable(priority)
	return Game.TableDataManager:GetData("GuildMaterialTaskRarityWeightData", priority)
end


---@return _GuildMaterialTaskFinishRewardDataRow
function TableData.GetGuildMaterialTaskFinishRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildMaterialTaskFinishRewardData", key, priority)
end


---@return _GuildMaterialTaskFinishRewardDataRow[]
function TableData.GetGuildMaterialTaskFinishRewardDataTable(priority)
	return Game.TableDataManager:GetData("GuildMaterialTaskFinishRewardData", priority)
end


---@return _GuildMaterialTaskHelpDialogueDataRow
function TableData.GetGuildMaterialTaskHelpDialogueDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildMaterialTaskHelpDialogueData", key, priority)
end


---@return _GuildMaterialTaskHelpDialogueDataRow[]
function TableData.GetGuildMaterialTaskHelpDialogueDataTable(priority)
	return Game.TableDataManager:GetData("GuildMaterialTaskHelpDialogueData", priority)
end


---@return _TimeChessDataRow
function TableData.GetTimeChessDataRow(key, priority)
	return Game.TableDataManager:GetRow("TimeChessData", key, priority)
end


---@return _TimeChessDataRow[]
function TableData.GetTimeChessDataTable(priority)
	return Game.TableDataManager:GetData("TimeChessData", priority)
end


---@return _PlayerBattleFacadeControlDataRow
function TableData.GetPlayerBattleFacadeControlDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerBattleFacadeControlData", key, priority)
end


---@return _PlayerBattleFacadeControlDataRow[]
function TableData.GetPlayerBattleFacadeControlDataTable(priority)
	return Game.TableDataManager:GetData("PlayerBattleFacadeControlData", priority)
end


---@return _DisplayRoleFacadeControlDataRow
function TableData.GetDisplayRoleFacadeControlDataRow(key, priority)
	return Game.TableDataManager:GetRow("DisplayRoleFacadeControlData", key, priority)
end


---@return _DisplayRoleFacadeControlDataRow[]
function TableData.GetDisplayRoleFacadeControlDataTable(priority)
	return Game.TableDataManager:GetData("DisplayRoleFacadeControlData", priority)
end


---@return _NPCFacadeControlDataRow
function TableData.GetNPCFacadeControlDataRow(key, priority)
	return Game.TableDataManager:GetRow("NPCFacadeControlData", key, priority)
end


---@return _NPCFacadeControlDataRow[]
function TableData.GetNPCFacadeControlDataTable(priority)
	return Game.TableDataManager:GetData("NPCFacadeControlData", priority)
end


---@return _MonsterFacadeControlDataRow
function TableData.GetMonsterFacadeControlDataRow(key, priority)
	return Game.TableDataManager:GetRow("MonsterFacadeControlData", key, priority)
end


---@return _MonsterFacadeControlDataRow[]
function TableData.GetMonsterFacadeControlDataTable(priority)
	return Game.TableDataManager:GetData("MonsterFacadeControlData", priority)
end


---@return _ModelMaterialDataRow
function TableData.GetModelMaterialDataRow(key, priority)
	return Game.TableDataManager:GetRow("ModelMaterialData", key, priority)
end


---@return _ModelMaterialDataRow[]
function TableData.GetModelMaterialDataTable(priority)
	return Game.TableDataManager:GetData("ModelMaterialData", priority)
end


---@return _RoleMechanismConstDataRow
function TableData.GetRoleMechanismConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("RoleMechanismConstData", key, priority)
end


---@return _RoleMechanismConstDataRow[]
function TableData.GetRoleMechanismConstDataTable(priority)
	return Game.TableDataManager:GetData("RoleMechanismConstData", priority)
end


---@return _ProfessionSkillDataRow
function TableData.GetProfessionSkillDataRow(key, priority)
	return Game.TableDataManager:GetRow("ProfessionSkillData", key, priority)
end


---@return _ProfessionSkillDataRow[]
function TableData.GetProfessionSkillDataTable(priority)
	return Game.TableDataManager:GetData("ProfessionSkillData", priority)
end


---@return _ExploreElementDataRow
function TableData.GetExploreElementDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreElementData", key, priority)
end


---@return _ExploreElementDataRow[]
function TableData.GetExploreElementDataTable(priority)
	return Game.TableDataManager:GetData("ExploreElementData", priority)
end


---@return _ExploreElementEventDataRow
function TableData.GetExploreElementEventDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreElementEventData", key, priority)
end


---@return _ExploreElementEventDataRow[]
function TableData.GetExploreElementEventDataTable(priority)
	return Game.TableDataManager:GetData("ExploreElementEventData", priority)
end


---@return _GameDropDataRow
function TableData.GetGameDropDataRow(key, priority)
	return Game.TableDataManager:GetRow("GameDropData", key, priority)
end


---@return _GameDropDataRow[]
function TableData.GetGameDropDataTable(priority)
	return Game.TableDataManager:GetData("GameDropData", priority)
end


---@return _PlayerInteractMenuInfoDataRow
function TableData.GetPlayerInteractMenuInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerInteractMenuInfoData", key, priority)
end


---@return _PlayerInteractMenuInfoDataRow[]
function TableData.GetPlayerInteractMenuInfoDataTable(priority)
	return Game.TableDataManager:GetData("PlayerInteractMenuInfoData", priority)
end


---@return _PlayerInteractUnitDataRow
function TableData.GetPlayerInteractUnitDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerInteractUnitData", key, priority)
end


---@return _PlayerInteractUnitDataRow[]
function TableData.GetPlayerInteractUnitDataTable(priority)
	return Game.TableDataManager:GetData("PlayerInteractUnitData", priority)
end


---@return _DialogueAutoCameraOnePDataRow
function TableData.GetDialogueAutoCameraOnePDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueAutoCameraOnePData", key, priority)
end


---@return _DialogueAutoCameraOnePDataRow[]
function TableData.GetDialogueAutoCameraOnePDataTable(priority)
	return Game.TableDataManager:GetData("DialogueAutoCameraOnePData", priority)
end


---@return _DialogueAutoCameraTwoPTypeOneDataRow
function TableData.GetDialogueAutoCameraTwoPTypeOneDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueAutoCameraTwoPTypeOneData", key, priority)
end


---@return _DialogueAutoCameraTwoPTypeOneDataRow[]
function TableData.GetDialogueAutoCameraTwoPTypeOneDataTable(priority)
	return Game.TableDataManager:GetData("DialogueAutoCameraTwoPTypeOneData", priority)
end


---@return _DialogueAutoCameraTwoPTypeTwoDataRow
function TableData.GetDialogueAutoCameraTwoPTypeTwoDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueAutoCameraTwoPTypeTwoData", key, priority)
end


---@return _DialogueAutoCameraTwoPTypeTwoDataRow[]
function TableData.GetDialogueAutoCameraTwoPTypeTwoDataTable(priority)
	return Game.TableDataManager:GetData("DialogueAutoCameraTwoPTypeTwoData", priority)
end


---@return _DialogueInitialDataRow
function TableData.GetDialogueInitialDataRow(key, priority)
	return Game.TableDataManager:GetRow("DialogueInitialData", key, priority)
end


---@return _DialogueInitialDataRow[]
function TableData.GetDialogueInitialDataTable(priority)
	return Game.TableDataManager:GetData("DialogueInitialData", priority)
end


---@return _IconConfigDataRow
function TableData.GetIconConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("IconConfigData", key, priority)
end


---@return _IconConfigDataRow[]
function TableData.GetIconConfigDataTable(priority)
	return Game.TableDataManager:GetData("IconConfigData", priority)
end


---@return _IconSetDataRow
function TableData.GetIconSetDataRow(key, priority)
	return Game.TableDataManager:GetRow("IconSetData", key, priority)
end


---@return _IconSetDataRow[]
function TableData.GetIconSetDataTable(priority)
	return Game.TableDataManager:GetData("IconSetData", priority)
end


---@return _AudioConstDataRow
function TableData.GetAudioConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("AudioConstData", key, priority)
end


---@return _AudioConstDataRow[]
function TableData.GetAudioConstDataTable(priority)
	return Game.TableDataManager:GetData("AudioConstData", priority)
end


---@return _LevelMapAudioDataRow
function TableData.GetLevelMapAudioDataRow(key, priority)
	return Game.TableDataManager:GetRow("LevelMapAudioData", key, priority)
end


---@return _LevelMapAudioDataRow[]
function TableData.GetLevelMapAudioDataTable(priority)
	return Game.TableDataManager:GetData("LevelMapAudioData", priority)
end


---@return _UIAudioDataRow
function TableData.GetUIAudioDataRow(key, priority)
	return Game.TableDataManager:GetRow("UIAudioData", key, priority)
end


---@return _UIAudioDataRow[]
function TableData.GetUIAudioDataTable(priority)
	return Game.TableDataManager:GetData("UIAudioData", priority)
end


---@return _AutoLoadBankDataRow
function TableData.GetAutoLoadBankDataRow(key, priority)
	return Game.TableDataManager:GetRow("AutoLoadBankData", key, priority)
end


---@return _AutoLoadBankDataRow[]
function TableData.GetAutoLoadBankDataTable(priority)
	return Game.TableDataManager:GetData("AutoLoadBankData", priority)
end


---@return _SceneFieldAudioDataRow
function TableData.GetSceneFieldAudioDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneFieldAudioData", key, priority)
end


---@return _SceneFieldAudioDataRow[]
function TableData.GetSceneFieldAudioDataTable(priority)
	return Game.TableDataManager:GetData("SceneFieldAudioData", priority)
end


---@return _AkAudioEventDataRow
function TableData.GetAkAudioEventDataRow(key, priority)
	return Game.TableDataManager:GetRow("AkAudioEventData", key, priority)
end


---@return _AkAudioEventDataRow[]
function TableData.GetAkAudioEventDataTable(priority)
	return Game.TableDataManager:GetData("AkAudioEventData", priority)
end


---@return _AkAudioBankDataRow
function TableData.GetAkAudioBankDataRow(key, priority)
	return Game.TableDataManager:GetRow("AkAudioBankData", key, priority)
end


---@return _AkAudioBankDataRow[]
function TableData.GetAkAudioBankDataTable(priority)
	return Game.TableDataManager:GetData("AkAudioBankData", priority)
end


---@return _TerrainPhysicalMaterialDataRow
function TableData.GetTerrainPhysicalMaterialDataRow(key, priority)
	return Game.TableDataManager:GetRow("TerrainPhysicalMaterialData", key, priority)
end


---@return _TerrainPhysicalMaterialDataRow[]
function TableData.GetTerrainPhysicalMaterialDataTable(priority)
	return Game.TableDataManager:GetData("TerrainPhysicalMaterialData", priority)
end


---@return _TerrainMaterialMaskDataRow
function TableData.GetTerrainMaterialMaskDataRow(key, priority)
	return Game.TableDataManager:GetRow("TerrainMaterialMaskData", key, priority)
end


---@return _TerrainMaterialMaskDataRow[]
function TableData.GetTerrainMaterialMaskDataTable(priority)
	return Game.TableDataManager:GetData("TerrainMaterialMaskData", priority)
end


---@return _SequenceTestDataRow
function TableData.GetSequenceTestDataRow(key, priority)
	return Game.TableDataManager:GetRow("SequenceTestData", key, priority)
end


---@return _SequenceTestDataRow[]
function TableData.GetSequenceTestDataTable(priority)
	return Game.TableDataManager:GetData("SequenceTestData", priority)
end


---@return _GVGAreaInfoDataRow
function TableData.GetGVGAreaInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("GVGAreaInfoData", key, priority)
end


---@return _GVGAreaInfoDataRow[]
function TableData.GetGVGAreaInfoDataTable(priority)
	return Game.TableDataManager:GetData("GVGAreaInfoData", priority)
end


---@return _GVGConstDataRow
function TableData.GetGVGConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("GVGConstData", key, priority)
end


---@return _GVGConstDataRow[]
function TableData.GetGVGConstDataTable(priority)
	return Game.TableDataManager:GetData("GVGConstData", priority)
end


---@return _GuildLeagueTowerInfoDataRow
function TableData.GetGuildLeagueTowerInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueTowerInfoData", key, priority)
end


---@return _GuildLeagueTowerInfoDataRow[]
function TableData.GetGuildLeagueTowerInfoDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueTowerInfoData", priority)
end


---@return _GuildLeagueSeasonDataRow
function TableData.GetGuildLeagueSeasonDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueSeasonData", key, priority)
end


---@return _GuildLeagueSeasonDataRow[]
function TableData.GetGuildLeagueSeasonDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueSeasonData", priority)
end


---@return _GuildLeagueRankRewardDataRow
function TableData.GetGuildLeagueRankRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueRankRewardData", key, priority)
end


---@return _GuildLeagueRankRewardDataRow[]
function TableData.GetGuildLeagueRankRewardDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueRankRewardData", priority)
end


---@return _GuildLeagueTagInfoDataRow
function TableData.GetGuildLeagueTagInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueTagInfoData", key, priority)
end


---@return _GuildLeagueTagInfoDataRow[]
function TableData.GetGuildLeagueTagInfoDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueTagInfoData", priority)
end


---@return _GuildLeagueMoraleLevelDataRow
function TableData.GetGuildLeagueMoraleLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueMoraleLevelData", key, priority)
end


---@return _GuildLeagueMoraleLevelDataRow[]
function TableData.GetGuildLeagueMoraleLevelDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueMoraleLevelData", priority)
end


---@return _GuildLeagueConstDataRow
function TableData.GetGuildLeagueConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueConstData", key, priority)
end


---@return _GuildLeagueConstDataRow[]
function TableData.GetGuildLeagueConstDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueConstData", priority)
end


---@return _GuildLeagueConstIntDataRow
function TableData.GetGuildLeagueConstIntDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueConstIntData", key, priority)
end


---@return _GuildLeagueConstIntDataRow[]
function TableData.GetGuildLeagueConstIntDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueConstIntData", priority)
end


---@return _GuildLeagueCommandSystemDataRow
function TableData.GetGuildLeagueCommandSystemDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueCommandSystemData", key, priority)
end


---@return _GuildLeagueCommandSystemDataRow[]
function TableData.GetGuildLeagueCommandSystemDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueCommandSystemData", priority)
end


---@return _GuildLeagueCommonCommandDataRow
function TableData.GetGuildLeagueCommonCommandDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueCommonCommandData", key, priority)
end


---@return _GuildLeagueCommonCommandDataRow[]
function TableData.GetGuildLeagueCommonCommandDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueCommonCommandData", priority)
end


---@return _GuildLeagueTitleDataRow
function TableData.GetGuildLeagueTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueTitleData", key, priority)
end


---@return _GuildLeagueTitleDataRow[]
function TableData.GetGuildLeagueTitleDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueTitleData", priority)
end


---@return _GuildLeagueDivisionNameDataRow
function TableData.GetGuildLeagueDivisionNameDataRow(key, priority)
	return Game.TableDataManager:GetRow("GuildLeagueDivisionNameData", key, priority)
end


---@return _GuildLeagueDivisionNameDataRow[]
function TableData.GetGuildLeagueDivisionNameDataTable(priority)
	return Game.TableDataManager:GetData("GuildLeagueDivisionNameData", priority)
end


---@return _ParallelBehaviorConstraintRulesDataRow
function TableData.GetParallelBehaviorConstraintRulesDataRow(key, priority)
	return Game.TableDataManager:GetRow("ParallelBehaviorConstraintRulesData", key, priority)
end


---@return _ParallelBehaviorConstraintRulesDataRow[]
function TableData.GetParallelBehaviorConstraintRulesDataTable(priority)
	return Game.TableDataManager:GetData("ParallelBehaviorConstraintRulesData", priority)
end


---@return _OccupyDetectAreaTypeDataRow
function TableData.GetOccupyDetectAreaTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("OccupyDetectAreaTypeData", key, priority)
end


---@return _OccupyDetectAreaTypeDataRow[]
function TableData.GetOccupyDetectAreaTypeDataTable(priority)
	return Game.TableDataManager:GetData("OccupyDetectAreaTypeData", priority)
end


---@return _HomeCameraDataRow
function TableData.GetHomeCameraDataRow(key, priority)
	return Game.TableDataManager:GetRow("HomeCameraData", key, priority)
end


---@return _HomeCameraDataRow[]
function TableData.GetHomeCameraDataTable(priority)
	return Game.TableDataManager:GetData("HomeCameraData", priority)
end


---@return _HomeConfigDataRow
function TableData.GetHomeConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("HomeConfigData", key, priority)
end


---@return _HomeConfigDataRow[]
function TableData.GetHomeConfigDataTable(priority)
	return Game.TableDataManager:GetData("HomeConfigData", priority)
end


---@return _DLCPriorityDataRow
function TableData.GetDLCPriorityDataRow(key, priority)
	return Game.TableDataManager:GetRow("DLCPriorityData", key, priority)
end


---@return _DLCPriorityDataRow[]
function TableData.GetDLCPriorityDataTable(priority)
	return Game.TableDataManager:GetData("DLCPriorityData", priority)
end


---@return _DLCGroupDataRow
function TableData.GetDLCGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("DLCGroupData", key, priority)
end


---@return _DLCGroupDataRow[]
function TableData.GetDLCGroupDataTable(priority)
	return Game.TableDataManager:GetData("DLCGroupData", priority)
end


---@return _DLCPakChunkDataRow
function TableData.GetDLCPakChunkDataRow(key, priority)
	return Game.TableDataManager:GetRow("DLCPakChunkData", key, priority)
end


---@return _DLCPakChunkDataRow[]
function TableData.GetDLCPakChunkDataTable(priority)
	return Game.TableDataManager:GetData("DLCPakChunkData", priority)
end


---@return _AvatarInteractDefineDataRow
function TableData.GetAvatarInteractDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("AvatarInteractDefineData", key, priority)
end


---@return _AvatarInteractDefineDataRow[]
function TableData.GetAvatarInteractDefineDataTable(priority)
	return Game.TableDataManager:GetData("AvatarInteractDefineData", priority)
end


---@return _BattleInteractDefineDataRow
function TableData.GetBattleInteractDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("BattleInteractDefineData", key, priority)
end


---@return _BattleInteractDefineDataRow[]
function TableData.GetBattleInteractDefineDataTable(priority)
	return Game.TableDataManager:GetData("BattleInteractDefineData", priority)
end


---@return _RankAllListDataRow
function TableData.GetRankAllListDataRow(key, priority)
	return Game.TableDataManager:GetRow("RankAllListData", key, priority)
end


---@return _RankAllListDataRow[]
function TableData.GetRankAllListDataTable(priority)
	return Game.TableDataManager:GetData("RankAllListData", priority)
end


---@return _RankListDisplayDataRow
function TableData.GetRankListDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("RankListDisplayData", key, priority)
end


---@return _RankListDisplayDataRow[]
function TableData.GetRankListDisplayDataTable(priority)
	return Game.TableDataManager:GetData("RankListDisplayData", priority)
end


---@return _FashionConstCostDataRow
function TableData.GetFashionConstCostDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionConstCostData", key, priority)
end


---@return _FashionConstCostDataRow[]
function TableData.GetFashionConstCostDataTable(priority)
	return Game.TableDataManager:GetData("FashionConstCostData", priority)
end


---@return _AroundAccConfigDataRow
function TableData.GetAroundAccConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("AroundAccConfigData", key, priority)
end


---@return _AroundAccConfigDataRow[]
function TableData.GetAroundAccConfigDataTable(priority)
	return Game.TableDataManager:GetData("AroundAccConfigData", priority)
end


---@return _DyeingConfigDataRow
function TableData.GetDyeingConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("DyeingConfigData", key, priority)
end


---@return _DyeingConfigDataRow[]
function TableData.GetDyeingConfigDataTable(priority)
	return Game.TableDataManager:GetData("DyeingConfigData", priority)
end


---@return _FashionMountDataRow
function TableData.GetFashionMountDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionMountData", key, priority)
end


---@return _FashionMountDataRow[]
function TableData.GetFashionMountDataTable(priority)
	return Game.TableDataManager:GetData("FashionMountData", priority)
end


---@return _FashionMountSubTypeDataRow
function TableData.GetFashionMountSubTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionMountSubTypeData", key, priority)
end


---@return _FashionMountSubTypeDataRow[]
function TableData.GetFashionMountSubTypeDataTable(priority)
	return Game.TableDataManager:GetData("FashionMountSubTypeData", priority)
end


---@return _FashionMountConstDataRow
function TableData.GetFashionMountConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionMountConstData", key, priority)
end


---@return _FashionMountConstDataRow[]
function TableData.GetFashionMountConstDataTable(priority)
	return Game.TableDataManager:GetData("FashionMountConstData", priority)
end


---@return _CustomRoleTabDataRow
function TableData.GetCustomRoleTabDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleTabData", key, priority)
end


---@return _CustomRoleTabDataRow[]
function TableData.GetCustomRoleTabDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleTabData", priority)
end


---@return _CustomRoleTabWidgetDataRow
function TableData.GetCustomRoleTabWidgetDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleTabWidgetData", key, priority)
end


---@return _CustomRoleTabWidgetDataRow[]
function TableData.GetCustomRoleTabWidgetDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleTabWidgetData", priority)
end


---@return _CustomRoleSettingDataRow
function TableData.GetCustomRoleSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleSettingData", key, priority)
end


---@return _CustomRoleSettingDataRow[]
function TableData.GetCustomRoleSettingDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleSettingData", priority)
end


---@return _CustomRoleCameraDataRow
function TableData.GetCustomRoleCameraDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleCameraData", key, priority)
end


---@return _CustomRoleCameraDataRow[]
function TableData.GetCustomRoleCameraDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleCameraData", priority)
end


---@return _CustomRoleStageDataRow
function TableData.GetCustomRoleStageDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleStageData", key, priority)
end


---@return _CustomRoleStageDataRow[]
function TableData.GetCustomRoleStageDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleStageData", priority)
end


---@return _CustomRoleSwitchDataRow
function TableData.GetCustomRoleSwitchDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleSwitchData", key, priority)
end


---@return _CustomRoleSwitchDataRow[]
function TableData.GetCustomRoleSwitchDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleSwitchData", priority)
end


---@return _CustomRoleSliderDataRow
function TableData.GetCustomRoleSliderDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleSliderData", key, priority)
end


---@return _CustomRoleSliderDataRow[]
function TableData.GetCustomRoleSliderDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleSliderData", priority)
end


---@return _CustomRoleListDataRow
function TableData.GetCustomRoleListDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleListData", key, priority)
end


---@return _CustomRoleListDataRow[]
function TableData.GetCustomRoleListDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleListData", priority)
end


---@return _CustomRoleAssessmentDataRow
function TableData.GetCustomRoleAssessmentDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleAssessmentData", key, priority)
end


---@return _CustomRoleAssessmentDataRow[]
function TableData.GetCustomRoleAssessmentDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleAssessmentData", priority)
end


---@return _CustomRoleColorBoardDataRow
function TableData.GetCustomRoleColorBoardDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleColorBoardData", key, priority)
end


---@return _CustomRoleColorBoardDataRow[]
function TableData.GetCustomRoleColorBoardDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleColorBoardData", priority)
end


---@return _FashionMakeUpDataRow
function TableData.GetFashionMakeUpDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionMakeUpData", key, priority)
end


---@return _FashionMakeUpDataRow[]
function TableData.GetFashionMakeUpDataTable(priority)
	return Game.TableDataManager:GetData("FashionMakeUpData", priority)
end


---@return _FashionMakeUpEditSlotDataRow
function TableData.GetFashionMakeUpEditSlotDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionMakeUpEditSlotData", key, priority)
end


---@return _FashionMakeUpEditSlotDataRow[]
function TableData.GetFashionMakeUpEditSlotDataTable(priority)
	return Game.TableDataManager:GetData("FashionMakeUpEditSlotData", priority)
end


---@return _CustomRoleGroupSetDataRow
function TableData.GetCustomRoleGroupSetDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomRoleGroupSetData", key, priority)
end


---@return _CustomRoleGroupSetDataRow[]
function TableData.GetCustomRoleGroupSetDataTable(priority)
	return Game.TableDataManager:GetData("CustomRoleGroupSetData", priority)
end


---@return _FashionFaceEditSlotDataRow
function TableData.GetFashionFaceEditSlotDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionFaceEditSlotData", key, priority)
end


---@return _FashionFaceEditSlotDataRow[]
function TableData.GetFashionFaceEditSlotDataTable(priority)
	return Game.TableDataManager:GetData("FashionFaceEditSlotData", priority)
end


---@return _SocialActionDataRow
function TableData.GetSocialActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("SocialActionData", key, priority)
end


---@return _SocialActionDataRow[]
function TableData.GetSocialActionDataTable(priority)
	return Game.TableDataManager:GetData("SocialActionData", priority)
end


---@return _PerformanceActionDataRow
function TableData.GetPerformanceActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("PerformanceActionData", key, priority)
end


---@return _PerformanceActionDataRow[]
function TableData.GetPerformanceActionDataTable(priority)
	return Game.TableDataManager:GetData("PerformanceActionData", priority)
end


---@return _PerformanceUpperBlendDataRow
function TableData.GetPerformanceUpperBlendDataRow(key, priority)
	return Game.TableDataManager:GetRow("PerformanceUpperBlendData", key, priority)
end


---@return _PerformanceUpperBlendDataRow[]
function TableData.GetPerformanceUpperBlendDataTable(priority)
	return Game.TableDataManager:GetData("PerformanceUpperBlendData", priority)
end


---@return _SocialActionClassDataRow
function TableData.GetSocialActionClassDataRow(key, priority)
	return Game.TableDataManager:GetRow("SocialActionClassData", key, priority)
end


---@return _SocialActionClassDataRow[]
function TableData.GetSocialActionClassDataTable(priority)
	return Game.TableDataManager:GetData("SocialActionClassData", priority)
end


---@return _SocialActionTagDataRow
function TableData.GetSocialActionTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("SocialActionTagData", key, priority)
end


---@return _SocialActionTagDataRow[]
function TableData.GetSocialActionTagDataTable(priority)
	return Game.TableDataManager:GetData("SocialActionTagData", priority)
end


---@return _BuffDataNewRow
function TableData.GetBuffDataNewRow(key, priority)
	return Game.TableDataManager:GetRow("BuffDataNew", key, priority)
end


---@return _BuffDataNewRow[]
function TableData.GetBuffDataNewTable(priority)
	return Game.TableDataManager:GetData("BuffDataNew", priority)
end


---@return _PassiveSkillDataRow
function TableData.GetPassiveSkillDataRow(key, priority)
	return Game.TableDataManager:GetRow("PassiveSkillData", key, priority)
end


---@return _PassiveSkillDataRow[]
function TableData.GetPassiveSkillDataTable(priority)
	return Game.TableDataManager:GetData("PassiveSkillData", priority)
end


---@return _CombatParameterDataRow
function TableData.GetCombatParameterDataRow(key, priority)
	return Game.TableDataManager:GetRow("CombatParameterData", key, priority)
end


---@return _CombatParameterDataRow[]
function TableData.GetCombatParameterDataTable(priority)
	return Game.TableDataManager:GetData("CombatParameterData", priority)
end


---@return _TargetSelectionRuleDataRow
function TableData.GetTargetSelectionRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("TargetSelectionRuleData", key, priority)
end


---@return _TargetSelectionRuleDataRow[]
function TableData.GetTargetSelectionRuleDataTable(priority)
	return Game.TableDataManager:GetData("TargetSelectionRuleData", priority)
end


---@return _AuraDataRow
function TableData.GetAuraDataRow(key, priority)
	return Game.TableDataManager:GetRow("AuraData", key, priority)
end


---@return _AuraDataRow[]
function TableData.GetAuraDataTable(priority)
	return Game.TableDataManager:GetData("AuraData", priority)
end


---@return _AuraAppearRow
function TableData.GetAuraAppearRow(key, priority)
	return Game.TableDataManager:GetRow("AuraAppear", key, priority)
end


---@return _AuraAppearRow[]
function TableData.GetAuraAppearTable(priority)
	return Game.TableDataManager:GetData("AuraAppear", priority)
end


---@return _TrapDataRow
function TableData.GetTrapDataRow(key, priority)
	return Game.TableDataManager:GetRow("TrapData", key, priority)
end


---@return _TrapDataRow[]
function TableData.GetTrapDataTable(priority)
	return Game.TableDataManager:GetData("TrapData", priority)
end


---@return _TrapAppearRow
function TableData.GetTrapAppearRow(key, priority)
	return Game.TableDataManager:GetRow("TrapAppear", key, priority)
end


---@return _TrapAppearRow[]
function TableData.GetTrapAppearTable(priority)
	return Game.TableDataManager:GetData("TrapAppear", priority)
end


---@return _SpellFieldDataRow
function TableData.GetSpellFieldDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpellFieldData", key, priority)
end


---@return _SpellFieldDataRow[]
function TableData.GetSpellFieldDataTable(priority)
	return Game.TableDataManager:GetData("SpellFieldData", priority)
end


---@return _SpellFieldAppearRow
function TableData.GetSpellFieldAppearRow(key, priority)
	return Game.TableDataManager:GetRow("SpellFieldAppear", key, priority)
end


---@return _SpellFieldAppearRow[]
function TableData.GetSpellFieldAppearTable(priority)
	return Game.TableDataManager:GetData("SpellFieldAppear", priority)
end


---@return _SpellAgentDataRow
function TableData.GetSpellAgentDataRow(key, priority)
	return Game.TableDataManager:GetRow("SpellAgentData", key, priority)
end


---@return _SpellAgentDataRow[]
function TableData.GetSpellAgentDataTable(priority)
	return Game.TableDataManager:GetData("SpellAgentData", priority)
end


---@return _ExtraHurtMultiDataRow
function TableData.GetExtraHurtMultiDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExtraHurtMultiData", key, priority)
end


---@return _ExtraHurtMultiDataRow[]
function TableData.GetExtraHurtMultiDataTable(priority)
	return Game.TableDataManager:GetData("ExtraHurtMultiData", priority)
end


---@return _ServerDataRow
function TableData.GetServerDataRow(key, priority)
	return Game.TableDataManager:GetRow("ServerData", key, priority)
end


---@return _ServerDataRow[]
function TableData.GetServerDataTable(priority)
	return Game.TableDataManager:GetData("ServerData", priority)
end


---@return _NiagaraPrioritySmallScaleBattleRow
function TableData.GetNiagaraPrioritySmallScaleBattleRow(key, priority)
	return Game.TableDataManager:GetRow("NiagaraPrioritySmallScaleBattle", key, priority)
end


---@return _NiagaraPrioritySmallScaleBattleRow[]
function TableData.GetNiagaraPrioritySmallScaleBattleTable(priority)
	return Game.TableDataManager:GetData("NiagaraPrioritySmallScaleBattle", priority)
end


---@return _NiagaraPriorityMediumScaleBattleRow
function TableData.GetNiagaraPriorityMediumScaleBattleRow(key, priority)
	return Game.TableDataManager:GetRow("NiagaraPriorityMediumScaleBattle", key, priority)
end


---@return _NiagaraPriorityMediumScaleBattleRow[]
function TableData.GetNiagaraPriorityMediumScaleBattleTable(priority)
	return Game.TableDataManager:GetData("NiagaraPriorityMediumScaleBattle", priority)
end


---@return _NiagaraPriorityLargeScaleBattleRow
function TableData.GetNiagaraPriorityLargeScaleBattleRow(key, priority)
	return Game.TableDataManager:GetRow("NiagaraPriorityLargeScaleBattle", key, priority)
end


---@return _NiagaraPriorityLargeScaleBattleRow[]
function TableData.GetNiagaraPriorityLargeScaleBattleTable(priority)
	return Game.TableDataManager:GetData("NiagaraPriorityLargeScaleBattle", priority)
end


---@return _StatisticsBuffAndSkillFilterDataRow
function TableData.GetStatisticsBuffAndSkillFilterDataRow(key, priority)
	return Game.TableDataManager:GetRow("StatisticsBuffAndSkillFilterData", key, priority)
end


---@return _StatisticsBuffAndSkillFilterDataRow[]
function TableData.GetStatisticsBuffAndSkillFilterDataTable(priority)
	return Game.TableDataManager:GetData("StatisticsBuffAndSkillFilterData", priority)
end


---@return _GazeConfigDataRow
function TableData.GetGazeConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("GazeConfigData", key, priority)
end


---@return _GazeConfigDataRow[]
function TableData.GetGazeConfigDataTable(priority)
	return Game.TableDataManager:GetData("GazeConfigData", priority)
end


---@return _SkillDataNewRow
function TableData.GetSkillDataNewRow(key, priority)
	return Game.TableDataManager:GetRow("SkillDataNew", key, priority)
end


---@return _SkillDataNewRow[]
function TableData.GetSkillDataNewTable(priority)
	return Game.TableDataManager:GetData("SkillDataNew", priority)
end


---@return _ExploreTypeDataRow
function TableData.GetExploreTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreTypeData", key, priority)
end


---@return _ExploreTypeDataRow[]
function TableData.GetExploreTypeDataTable(priority)
	return Game.TableDataManager:GetData("ExploreTypeData", priority)
end


---@return _ExploreItemDataRow
function TableData.GetExploreItemDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreItemData", key, priority)
end


---@return _ExploreItemDataRow[]
function TableData.GetExploreItemDataTable(priority)
	return Game.TableDataManager:GetData("ExploreItemData", priority)
end


---@return _ExploreFirstLevelAreaDataRow
function TableData.GetExploreFirstLevelAreaDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreFirstLevelAreaData", key, priority)
end


---@return _ExploreFirstLevelAreaDataRow[]
function TableData.GetExploreFirstLevelAreaDataTable(priority)
	return Game.TableDataManager:GetData("ExploreFirstLevelAreaData", priority)
end


---@return _ExploreSecondLevelAreaDataRow
function TableData.GetExploreSecondLevelAreaDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreSecondLevelAreaData", key, priority)
end


---@return _ExploreSecondLevelAreaDataRow[]
function TableData.GetExploreSecondLevelAreaDataTable(priority)
	return Game.TableDataManager:GetData("ExploreSecondLevelAreaData", priority)
end


---@return _ExploreSoulDataRow
function TableData.GetExploreSoulDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreSoulData", key, priority)
end


---@return _ExploreSoulDataRow[]
function TableData.GetExploreSoulDataTable(priority)
	return Game.TableDataManager:GetData("ExploreSoulData", priority)
end


---@return _ExploreSteleDataRow
function TableData.GetExploreSteleDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreSteleData", key, priority)
end


---@return _ExploreSteleDataRow[]
function TableData.GetExploreSteleDataTable(priority)
	return Game.TableDataManager:GetData("ExploreSteleData", priority)
end


---@return _SceneDisplayDataRow
function TableData.GetSceneDisplayDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneDisplayData", key, priority)
end


---@return _SceneDisplayDataRow[]
function TableData.GetSceneDisplayDataTable(priority)
	return Game.TableDataManager:GetData("SceneDisplayData", priority)
end


---@return _UICameraConfigDataRow
function TableData.GetUICameraConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("UICameraConfigData", key, priority)
end


---@return _UICameraConfigDataRow[]
function TableData.GetUICameraConfigDataTable(priority)
	return Game.TableDataManager:GetData("UICameraConfigData", priority)
end


---@return _SceneCustomTypeDataRow
function TableData.GetSceneCustomTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneCustomTypeData", key, priority)
end


---@return _SceneCustomTypeDataRow[]
function TableData.GetSceneCustomTypeDataTable(priority)
	return Game.TableDataManager:GetData("SceneCustomTypeData", priority)
end


---@return _SceneCustomDefaultListDataRow
function TableData.GetSceneCustomDefaultListDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneCustomDefaultListData", key, priority)
end


---@return _SceneCustomDefaultListDataRow[]
function TableData.GetSceneCustomDefaultListDataTable(priority)
	return Game.TableDataManager:GetData("SceneCustomDefaultListData", priority)
end


---@return _SceneCustomConstDataRow
function TableData.GetSceneCustomConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("SceneCustomConstData", key, priority)
end


---@return _SceneCustomConstDataRow[]
function TableData.GetSceneCustomConstDataTable(priority)
	return Game.TableDataManager:GetData("SceneCustomConstData", priority)
end


---@return _FashionDataRow
function TableData.GetFashionDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionData", key, priority)
end


---@return _FashionDataRow[]
function TableData.GetFashionDataTable(priority)
	return Game.TableDataManager:GetData("FashionData", priority)
end


---@return _AppearanceTypeRow
function TableData.GetAppearanceTypeRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceType", key, priority)
end


---@return _AppearanceTypeRow[]
function TableData.GetAppearanceTypeTable(priority)
	return Game.TableDataManager:GetData("AppearanceType", priority)
end


---@return _AppearanceSubTypeRow
function TableData.GetAppearanceSubTypeRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceSubType", key, priority)
end


---@return _AppearanceSubTypeRow[]
function TableData.GetAppearanceSubTypeTable(priority)
	return Game.TableDataManager:GetData("AppearanceSubType", priority)
end


---@return _AppearanceItemSubTypeRow
function TableData.GetAppearanceItemSubTypeRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceItemSubType", key, priority)
end


---@return _AppearanceItemSubTypeRow[]
function TableData.GetAppearanceItemSubTypeTable(priority)
	return Game.TableDataManager:GetData("AppearanceItemSubType", priority)
end


---@return _AppearanceTabTypeRow
function TableData.GetAppearanceTabTypeRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceTabType", key, priority)
end


---@return _AppearanceTabTypeRow[]
function TableData.GetAppearanceTabTypeTable(priority)
	return Game.TableDataManager:GetData("AppearanceTabType", priority)
end


---@return _FashionAccConfigDataRow
function TableData.GetFashionAccConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionAccConfigData", key, priority)
end


---@return _FashionAccConfigDataRow[]
function TableData.GetFashionAccConfigDataTable(priority)
	return Game.TableDataManager:GetData("FashionAccConfigData", priority)
end


---@return _FashionLevelDataRow
function TableData.GetFashionLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionLevelData", key, priority)
end


---@return _FashionLevelDataRow[]
function TableData.GetFashionLevelDataTable(priority)
	return Game.TableDataManager:GetData("FashionLevelData", priority)
end


---@return _FashionTagDataRow
function TableData.GetFashionTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("FashionTagData", key, priority)
end


---@return _FashionTagDataRow[]
function TableData.GetFashionTagDataTable(priority)
	return Game.TableDataManager:GetData("FashionTagData", priority)
end


---@return _AccEditConfigDataRow
function TableData.GetAccEditConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("AccEditConfigData", key, priority)
end


---@return _AccEditConfigDataRow[]
function TableData.GetAccEditConfigDataTable(priority)
	return Game.TableDataManager:GetData("AccEditConfigData", priority)
end


---@return _AppearanceSystemConstDataRow
function TableData.GetAppearanceSystemConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("AppearanceSystemConstData", key, priority)
end


---@return _AppearanceSystemConstDataRow[]
function TableData.GetAppearanceSystemConstDataTable(priority)
	return Game.TableDataManager:GetData("AppearanceSystemConstData", priority)
end


---@return _NewBulletDataRow
function TableData.GetNewBulletDataRow(key, priority)
	return Game.TableDataManager:GetRow("NewBulletData", key, priority)
end


---@return _NewBulletDataRow[]
function TableData.GetNewBulletDataTable(priority)
	return Game.TableDataManager:GetData("NewBulletData", priority)
end


---@return _NpcTrapDefineDataRow
function TableData.GetNpcTrapDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcTrapDefineData", key, priority)
end


---@return _NpcTrapDefineDataRow[]
function TableData.GetNpcTrapDefineDataTable(priority)
	return Game.TableDataManager:GetData("NpcTrapDefineData", priority)
end


---@return _NpcTrapTypeDataRow
function TableData.GetNpcTrapTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcTrapTypeData", key, priority)
end


---@return _NpcTrapTypeDataRow[]
function TableData.GetNpcTrapTypeDataTable(priority)
	return Game.TableDataManager:GetData("NpcTrapTypeData", priority)
end


---@return _NpcTrapGroupDataRow
function TableData.GetNpcTrapGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("NpcTrapGroupData", key, priority)
end


---@return _NpcTrapGroupDataRow[]
function TableData.GetNpcTrapGroupDataTable(priority)
	return Game.TableDataManager:GetData("NpcTrapGroupData", priority)
end


---@return _LocationEffectDataRow
function TableData.GetLocationEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocationEffectData", key, priority)
end


---@return _LocationEffectDataRow[]
function TableData.GetLocationEffectDataTable(priority)
	return Game.TableDataManager:GetData("LocationEffectData", priority)
end


---@return _AttachEffectDataRow
function TableData.GetAttachEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("AttachEffectData", key, priority)
end


---@return _AttachEffectDataRow[]
function TableData.GetAttachEffectDataTable(priority)
	return Game.TableDataManager:GetData("AttachEffectData", priority)
end


---@return _PPEffectsDataRow
function TableData.GetPPEffectsDataRow(key, priority)
	return Game.TableDataManager:GetRow("PPEffectsData", key, priority)
end


---@return _PPEffectsDataRow[]
function TableData.GetPPEffectsDataTable(priority)
	return Game.TableDataManager:GetData("PPEffectsData", priority)
end


---@return _DissolveEffectDataRow
function TableData.GetDissolveEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("DissolveEffectData", key, priority)
end


---@return _DissolveEffectDataRow[]
function TableData.GetDissolveEffectDataTable(priority)
	return Game.TableDataManager:GetData("DissolveEffectData", priority)
end


---@return _SystemActionNiagaraDataRow
function TableData.GetSystemActionNiagaraDataRow(key, priority)
	return Game.TableDataManager:GetRow("SystemActionNiagaraData", key, priority)
end


---@return _SystemActionNiagaraDataRow[]
function TableData.GetSystemActionNiagaraDataTable(priority)
	return Game.TableDataManager:GetData("SystemActionNiagaraData", priority)
end


---@return _MaterialParamDataRow
function TableData.GetMaterialParamDataRow(key, priority)
	return Game.TableDataManager:GetRow("MaterialParamData", key, priority)
end


---@return _MaterialParamDataRow[]
function TableData.GetMaterialParamDataTable(priority)
	return Game.TableDataManager:GetData("MaterialParamData", priority)
end


---@return _PP_DarkenDataRow
function TableData.GetPP_DarkenDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_DarkenData", key, priority)
end


---@return _PP_DarkenDataRow[]
function TableData.GetPP_DarkenDataTable(priority)
	return Game.TableDataManager:GetData("PP_DarkenData", priority)
end


---@return _PPTypesDataRow
function TableData.GetPPTypesDataRow(key, priority)
	return Game.TableDataManager:GetRow("PPTypesData", key, priority)
end


---@return _PPTypesDataRow[]
function TableData.GetPPTypesDataTable(priority)
	return Game.TableDataManager:GetData("PPTypesData", priority)
end


---@return _PP_BloomDataRow
function TableData.GetPP_BloomDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_BloomData", key, priority)
end


---@return _PP_BloomDataRow[]
function TableData.GetPP_BloomDataTable(priority)
	return Game.TableDataManager:GetData("PP_BloomData", priority)
end


---@return _PP_DOFDataRow
function TableData.GetPP_DOFDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_DOFData", key, priority)
end


---@return _PP_DOFDataRow[]
function TableData.GetPP_DOFDataTable(priority)
	return Game.TableDataManager:GetData("PP_DOFData", priority)
end


---@return _PP_ColorAdjustDataRow
function TableData.GetPP_ColorAdjustDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_ColorAdjustData", key, priority)
end


---@return _PP_ColorAdjustDataRow[]
function TableData.GetPP_ColorAdjustDataTable(priority)
	return Game.TableDataManager:GetData("PP_ColorAdjustData", priority)
end


---@return _PP_RadialBlurDataRow
function TableData.GetPP_RadialBlurDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_RadialBlurData", key, priority)
end


---@return _PP_RadialBlurDataRow[]
function TableData.GetPP_RadialBlurDataTable(priority)
	return Game.TableDataManager:GetData("PP_RadialBlurData", priority)
end


---@return _PP_ColorAdjustRadialBlurDataRow
function TableData.GetPP_ColorAdjustRadialBlurDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_ColorAdjustRadialBlurData", key, priority)
end


---@return _PP_ColorAdjustRadialBlurDataRow[]
function TableData.GetPP_ColorAdjustRadialBlurDataTable(priority)
	return Game.TableDataManager:GetData("PP_ColorAdjustRadialBlurData", priority)
end


---@return _PP_RGBSplitDataRow
function TableData.GetPP_RGBSplitDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_RGBSplitData", key, priority)
end


---@return _PP_RGBSplitDataRow[]
function TableData.GetPP_RGBSplitDataTable(priority)
	return Game.TableDataManager:GetData("PP_RGBSplitData", priority)
end


---@return _PP_VignetteDataRow
function TableData.GetPP_VignetteDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_VignetteData", key, priority)
end


---@return _PP_VignetteDataRow[]
function TableData.GetPP_VignetteDataTable(priority)
	return Game.TableDataManager:GetData("PP_VignetteData", priority)
end


---@return _PP_BrightenDataRow
function TableData.GetPP_BrightenDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_BrightenData", key, priority)
end


---@return _PP_BrightenDataRow[]
function TableData.GetPP_BrightenDataTable(priority)
	return Game.TableDataManager:GetData("PP_BrightenData", priority)
end


---@return _PP_PhantomDataRow
function TableData.GetPP_PhantomDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_PhantomData", key, priority)
end


---@return _PP_PhantomDataRow[]
function TableData.GetPP_PhantomDataTable(priority)
	return Game.TableDataManager:GetData("PP_PhantomData", priority)
end


---@return _PP_RadialUVDistortDataRow
function TableData.GetPP_RadialUVDistortDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_RadialUVDistortData", key, priority)
end


---@return _PP_RadialUVDistortDataRow[]
function TableData.GetPP_RadialUVDistortDataTable(priority)
	return Game.TableDataManager:GetData("PP_RadialUVDistortData", priority)
end


---@return _PP_SingleMaterialDataRow
function TableData.GetPP_SingleMaterialDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_SingleMaterialData", key, priority)
end


---@return _PP_SingleMaterialDataRow[]
function TableData.GetPP_SingleMaterialDataTable(priority)
	return Game.TableDataManager:GetData("PP_SingleMaterialData", priority)
end


---@return _PP_SingleMaterialParamsDataRow
function TableData.GetPP_SingleMaterialParamsDataRow(key, priority)
	return Game.TableDataManager:GetRow("PP_SingleMaterialParamsData", key, priority)
end


---@return _PP_SingleMaterialParamsDataRow[]
function TableData.GetPP_SingleMaterialParamsDataTable(priority)
	return Game.TableDataManager:GetData("PP_SingleMaterialParamsData", priority)
end


---@return _PostProcessTypeDataRow
function TableData.GetPostProcessTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("PostProcessTypeData", key, priority)
end


---@return _PostProcessTypeDataRow[]
function TableData.GetPostProcessTypeDataTable(priority)
	return Game.TableDataManager:GetData("PostProcessTypeData", priority)
end


---@return _ScreenEffectDataRow
function TableData.GetScreenEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("ScreenEffectData", key, priority)
end


---@return _ScreenEffectDataRow[]
function TableData.GetScreenEffectDataTable(priority)
	return Game.TableDataManager:GetData("ScreenEffectData", priority)
end


---@return _EffectAnimNotifyCDDataRow
function TableData.GetEffectAnimNotifyCDDataRow(key, priority)
	return Game.TableDataManager:GetRow("EffectAnimNotifyCDData", key, priority)
end


---@return _EffectAnimNotifyCDDataRow[]
function TableData.GetEffectAnimNotifyCDDataTable(priority)
	return Game.TableDataManager:GetData("EffectAnimNotifyCDData", priority)
end


---@return _PhotoDataRow
function TableData.GetPhotoDataRow(key, priority)
	return Game.TableDataManager:GetRow("PhotoData", key, priority)
end


---@return _PhotoDataRow[]
function TableData.GetPhotoDataTable(priority)
	return Game.TableDataManager:GetData("PhotoData", priority)
end


---@return _TakePhotoDataRow
function TableData.GetTakePhotoDataRow(key, priority)
	return Game.TableDataManager:GetRow("TakePhotoData", key, priority)
end


---@return _TakePhotoDataRow[]
function TableData.GetTakePhotoDataTable(priority)
	return Game.TableDataManager:GetData("TakePhotoData", priority)
end


---@return _FateContractDataRow
function TableData.GetFateContractDataRow(key, priority)
	return Game.TableDataManager:GetRow("FateContractData", key, priority)
end


---@return _FateContractDataRow[]
function TableData.GetFateContractDataTable(priority)
	return Game.TableDataManager:GetData("FateContractData", priority)
end


---@return _FateContractNiagaraDataRow
function TableData.GetFateContractNiagaraDataRow(key, priority)
	return Game.TableDataManager:GetRow("FateContractNiagaraData", key, priority)
end


---@return _FateContractNiagaraDataRow[]
function TableData.GetFateContractNiagaraDataTable(priority)
	return Game.TableDataManager:GetData("FateContractNiagaraData", priority)
end


---@return _StateConflictDataRow
function TableData.GetStateConflictDataRow(key, priority)
	return Game.TableDataManager:GetRow("StateConflictData", key, priority)
end


---@return _StateConflictDataRow[]
function TableData.GetStateConflictDataTable(priority)
	return Game.TableDataManager:GetData("StateConflictData", priority)
end


---@return _StateTypeDefineDataRow
function TableData.GetStateTypeDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("StateTypeDefineData", key, priority)
end


---@return _StateTypeDefineDataRow[]
function TableData.GetStateTypeDefineDataTable(priority)
	return Game.TableDataManager:GetData("StateTypeDefineData", priority)
end


---@return _ActionTypeDefineDataRow
function TableData.GetActionTypeDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("ActionTypeDefineData", key, priority)
end


---@return _ActionTypeDefineDataRow[]
function TableData.GetActionTypeDefineDataTable(priority)
	return Game.TableDataManager:GetData("ActionTypeDefineData", priority)
end


---@return _LocoAnimStateDefineDataRow
function TableData.GetLocoAnimStateDefineDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoAnimStateDefineData", key, priority)
end


---@return _LocoAnimStateDefineDataRow[]
function TableData.GetLocoAnimStateDefineDataTable(priority)
	return Game.TableDataManager:GetData("LocoAnimStateDefineData", priority)
end


---@return _LocoAnimStateOverrideDataRow
function TableData.GetLocoAnimStateOverrideDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoAnimStateOverrideData", key, priority)
end


---@return _LocoAnimStateOverrideDataRow[]
function TableData.GetLocoAnimStateOverrideDataTable(priority)
	return Game.TableDataManager:GetData("LocoAnimStateOverrideData", priority)
end


---@return _LocoGroupStateDataRow
function TableData.GetLocoGroupStateDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoGroupStateData", key, priority)
end


---@return _LocoGroupStateDataRow[]
function TableData.GetLocoGroupStateDataTable(priority)
	return Game.TableDataManager:GetData("LocoGroupStateData", priority)
end


---@return _LocoControlConstDataRow
function TableData.GetLocoControlConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoControlConstData", key, priority)
end


---@return _LocoControlConstDataRow[]
function TableData.GetLocoControlConstDataTable(priority)
	return Game.TableDataManager:GetData("LocoControlConstData", priority)
end


---@return _LocoControlDefaultTemplateDataRow
function TableData.GetLocoControlDefaultTemplateDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoControlDefaultTemplateData", key, priority)
end


---@return _LocoControlDefaultTemplateDataRow[]
function TableData.GetLocoControlDefaultTemplateDataTable(priority)
	return Game.TableDataManager:GetData("LocoControlDefaultTemplateData", priority)
end


---@return _LocoControlUpperControlLogicDataRow
function TableData.GetLocoControlUpperControlLogicDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocoControlUpperControlLogicData", key, priority)
end


---@return _LocoControlUpperControlLogicDataRow[]
function TableData.GetLocoControlUpperControlLogicDataTable(priority)
	return Game.TableDataManager:GetData("LocoControlUpperControlLogicData", priority)
end


---@return _SkillWheelDataRow
function TableData.GetSkillWheelDataRow(key, priority)
	return Game.TableDataManager:GetRow("SkillWheelData", key, priority)
end


---@return _SkillWheelDataRow[]
function TableData.GetSkillWheelDataTable(priority)
	return Game.TableDataManager:GetData("SkillWheelData", priority)
end


---@return _TeleportDataRow
function TableData.GetTeleportDataRow(key, priority)
	return Game.TableDataManager:GetRow("TeleportData", key, priority)
end


---@return _TeleportDataRow[]
function TableData.GetTeleportDataTable(priority)
	return Game.TableDataManager:GetData("TeleportData", priority)
end


---@return _SCPlayerShowPriorityDataRow
function TableData.GetSCPlayerShowPriorityDataRow(key, priority)
	return Game.TableDataManager:GetRow("SCPlayerShowPriorityData", key, priority)
end


---@return _SCPlayerShowPriorityDataRow[]
function TableData.GetSCPlayerShowPriorityDataTable(priority)
	return Game.TableDataManager:GetData("SCPlayerShowPriorityData", priority)
end


---@return _ModelDisplayPriorityDataRow
function TableData.GetModelDisplayPriorityDataRow(key, priority)
	return Game.TableDataManager:GetRow("ModelDisplayPriorityData", key, priority)
end


---@return _ModelDisplayPriorityDataRow[]
function TableData.GetModelDisplayPriorityDataTable(priority)
	return Game.TableDataManager:GetData("ModelDisplayPriorityData", priority)
end


---@return _AchievementTypeRow
function TableData.GetAchievementTypeRow(key, priority)
	return Game.TableDataManager:GetRow("AchievementType", key, priority)
end


---@return _AchievementTypeRow[]
function TableData.GetAchievementTypeTable(priority)
	return Game.TableDataManager:GetData("AchievementType", priority)
end


---@return _AchievementSubtypeRow
function TableData.GetAchievementSubtypeRow(key, priority)
	return Game.TableDataManager:GetRow("AchievementSubtype", key, priority)
end


---@return _AchievementSubtypeRow[]
function TableData.GetAchievementSubtypeTable(priority)
	return Game.TableDataManager:GetData("AchievementSubtype", priority)
end


---@return _AchievementsRow
function TableData.GetAchievementsRow(key, priority)
	return Game.TableDataManager:GetRow("Achievements", key, priority)
end


---@return _AchievementsRow[]
function TableData.GetAchievementsTable(priority)
	return Game.TableDataManager:GetData("Achievements", priority)
end


---@return _AchievementMenuDataRow
function TableData.GetAchievementMenuDataRow(key, priority)
	return Game.TableDataManager:GetRow("AchievementMenuData", key, priority)
end


---@return _AchievementMenuDataRow[]
function TableData.GetAchievementMenuDataTable(priority)
	return Game.TableDataManager:GetData("AchievementMenuData", priority)
end


---@return _AchievementSettingDataRow
function TableData.GetAchievementSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("AchievementSettingData", key, priority)
end


---@return _AchievementSettingDataRow[]
function TableData.GetAchievementSettingDataTable(priority)
	return Game.TableDataManager:GetData("AchievementSettingData", priority)
end


---@return _CollectiblesTypeRow
function TableData.GetCollectiblesTypeRow(key, priority)
	return Game.TableDataManager:GetRow("CollectiblesType", key, priority)
end


---@return _CollectiblesTypeRow[]
function TableData.GetCollectiblesTypeTable(priority)
	return Game.TableDataManager:GetData("CollectiblesType", priority)
end


---@return _CollectiblesSubTypeRow
function TableData.GetCollectiblesSubTypeRow(key, priority)
	return Game.TableDataManager:GetRow("CollectiblesSubType", key, priority)
end


---@return _CollectiblesSubTypeRow[]
function TableData.GetCollectiblesSubTypeTable(priority)
	return Game.TableDataManager:GetData("CollectiblesSubType", priority)
end


---@return _CollectiblesSettingRow
function TableData.GetCollectiblesSettingRow(key, priority)
	return Game.TableDataManager:GetRow("CollectiblesSetting", key, priority)
end


---@return _CollectiblesSettingRow[]
function TableData.GetCollectiblesSettingTable(priority)
	return Game.TableDataManager:GetData("CollectiblesSetting", priority)
end


---@return _HUDConstButtonRow
function TableData.GetHUDConstButtonRow(key, priority)
	return Game.TableDataManager:GetRow("HUDConstButton", key, priority)
end


---@return _HUDConstButtonRow[]
function TableData.GetHUDConstButtonTable(priority)
	return Game.TableDataManager:GetData("HUDConstButton", priority)
end


---@return _SealedRiskRow
function TableData.GetSealedRiskRow(key, priority)
	return Game.TableDataManager:GetRow("SealedRisk", key, priority)
end


---@return _SealedRiskRow[]
function TableData.GetSealedRiskTable(priority)
	return Game.TableDataManager:GetData("SealedRisk", priority)
end


---@return _HUDUIConfigRow
function TableData.GetHUDUIConfigRow(key, priority)
	return Game.TableDataManager:GetRow("HUDUIConfig", key, priority)
end


---@return _HUDUIConfigRow[]
function TableData.GetHUDUIConfigTable(priority)
	return Game.TableDataManager:GetData("HUDUIConfig", priority)
end


---@return _MountConfigDataRow
function TableData.GetMountConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("MountConfigData", key, priority)
end


---@return _MountConfigDataRow[]
function TableData.GetMountConfigDataTable(priority)
	return Game.TableDataManager:GetData("MountConfigData", priority)
end


---@return _TurntablePuzzleDataRow
function TableData.GetTurntablePuzzleDataRow(key, priority)
	return Game.TableDataManager:GetRow("TurntablePuzzleData", key, priority)
end


---@return _TurntablePuzzleDataRow[]
function TableData.GetTurntablePuzzleDataTable(priority)
	return Game.TableDataManager:GetData("TurntablePuzzleData", priority)
end


---@return _TurntableMapDataRow
function TableData.GetTurntableMapDataRow(key, priority)
	return Game.TableDataManager:GetRow("TurntableMapData", key, priority)
end


---@return _TurntableMapDataRow[]
function TableData.GetTurntableMapDataTable(priority)
	return Game.TableDataManager:GetData("TurntableMapData", priority)
end


---@return _TurntableItemTypeDataRow
function TableData.GetTurntableItemTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("TurntableItemTypeData", key, priority)
end


---@return _TurntableItemTypeDataRow[]
function TableData.GetTurntableItemTypeDataTable(priority)
	return Game.TableDataManager:GetData("TurntableItemTypeData", priority)
end


---@return _MomentsStringConstDataRow
function TableData.GetMomentsStringConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("MomentsStringConstData", key, priority)
end


---@return _MomentsStringConstDataRow[]
function TableData.GetMomentsStringConstDataTable(priority)
	return Game.TableDataManager:GetData("MomentsStringConstData", priority)
end


---@return _MomentsConstDataRow
function TableData.GetMomentsConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("MomentsConstData", key, priority)
end


---@return _MomentsConstDataRow[]
function TableData.GetMomentsConstDataTable(priority)
	return Game.TableDataManager:GetData("MomentsConstData", priority)
end


---@return _NPCMomentsConstDataRow
function TableData.GetNPCMomentsConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("NPCMomentsConstData", key, priority)
end


---@return _NPCMomentsConstDataRow[]
function TableData.GetNPCMomentsConstDataTable(priority)
	return Game.TableDataManager:GetData("NPCMomentsConstData", priority)
end


---@return _OfficalTopicDataRow
function TableData.GetOfficalTopicDataRow(key, priority)
	return Game.TableDataManager:GetRow("OfficalTopicData", key, priority)
end


---@return _OfficalTopicDataRow[]
function TableData.GetOfficalTopicDataTable(priority)
	return Game.TableDataManager:GetData("OfficalTopicData", priority)
end


---@return _TarotTeamSettingDataRow
function TableData.GetTarotTeamSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamSettingData", key, priority)
end


---@return _TarotTeamSettingDataRow[]
function TableData.GetTarotTeamSettingDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamSettingData", priority)
end


---@return _TarotTeamRewardDataRow
function TableData.GetTarotTeamRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamRewardData", key, priority)
end


---@return _TarotTeamRewardDataRow[]
function TableData.GetTarotTeamRewardDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamRewardData", priority)
end


---@return _TarotTeamTaskDataRow
function TableData.GetTarotTeamTaskDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamTaskData", key, priority)
end


---@return _TarotTeamTaskDataRow[]
function TableData.GetTarotTeamTaskDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamTaskData", priority)
end


---@return _TarotTeamTagDataRow
function TableData.GetTarotTeamTagDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamTagData", key, priority)
end


---@return _TarotTeamTagDataRow[]
function TableData.GetTarotTeamTagDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamTagData", priority)
end


---@return _TarotTeamLocationDataRow
function TableData.GetTarotTeamLocationDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamLocationData", key, priority)
end


---@return _TarotTeamLocationDataRow[]
function TableData.GetTarotTeamLocationDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamLocationData", priority)
end


---@return _TarotTeamStringConstDataRow
function TableData.GetTarotTeamStringConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamStringConstData", key, priority)
end


---@return _TarotTeamStringConstDataRow[]
function TableData.GetTarotTeamStringConstDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamStringConstData", priority)
end


---@return _TarotTeamJobTitleDataRow
function TableData.GetTarotTeamJobTitleDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotTeamJobTitleData", key, priority)
end


---@return _TarotTeamJobTitleDataRow[]
function TableData.GetTarotTeamJobTitleDataTable(priority)
	return Game.TableDataManager:GetData("TarotTeamJobTitleData", priority)
end


---@return _BattleBotDungeonDataRow
function TableData.GetBattleBotDungeonDataRow(key, priority)
	return Game.TableDataManager:GetRow("BattleBotDungeonData", key, priority)
end


---@return _BattleBotDungeonDataRow[]
function TableData.GetBattleBotDungeonDataTable(priority)
	return Game.TableDataManager:GetData("BattleBotDungeonData", priority)
end


---@return _BattleBotTemplateDataRow
function TableData.GetBattleBotTemplateDataRow(key, priority)
	return Game.TableDataManager:GetRow("BattleBotTemplateData", key, priority)
end


---@return _BattleBotTemplateDataRow[]
function TableData.GetBattleBotTemplateDataTable(priority)
	return Game.TableDataManager:GetData("BattleBotTemplateData", priority)
end


---@return _AutoBattleSettingDataRow
function TableData.GetAutoBattleSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("AutoBattleSettingData", key, priority)
end


---@return _AutoBattleSettingDataRow[]
function TableData.GetAutoBattleSettingDataTable(priority)
	return Game.TableDataManager:GetData("AutoBattleSettingData", priority)
end


---@return _AutoBattleModeDataRow
function TableData.GetAutoBattleModeDataRow(key, priority)
	return Game.TableDataManager:GetRow("AutoBattleModeData", key, priority)
end


---@return _AutoBattleModeDataRow[]
function TableData.GetAutoBattleModeDataTable(priority)
	return Game.TableDataManager:GetData("AutoBattleModeData", priority)
end


---@return _TalentConstDataRow
function TableData.GetTalentConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalentConstData", key, priority)
end


---@return _TalentConstDataRow[]
function TableData.GetTalentConstDataTable(priority)
	return Game.TableDataManager:GetData("TalentConstData", priority)
end


---@return _TalentTreeDataRow
function TableData.GetTalentTreeDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalentTreeData", key, priority)
end


---@return _TalentTreeDataRow[]
function TableData.GetTalentTreeDataTable(priority)
	return Game.TableDataManager:GetData("TalentTreeData", priority)
end


---@return _TalentNodeDataRow
function TableData.GetTalentNodeDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalentNodeData", key, priority)
end


---@return _TalentNodeDataRow[]
function TableData.GetTalentNodeDataTable(priority)
	return Game.TableDataManager:GetData("TalentNodeData", priority)
end


---@return _TalentBattleEffectDataRow
function TableData.GetTalentBattleEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("TalentBattleEffectData", key, priority)
end


---@return _TalentBattleEffectDataRow[]
function TableData.GetTalentBattleEffectDataTable(priority)
	return Game.TableDataManager:GetData("TalentBattleEffectData", priority)
end


---@return _EleTalentTreeNodeDataRow
function TableData.GetEleTalentTreeNodeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentTreeNodeData", key, priority)
end


---@return _EleTalentTreeNodeDataRow[]
function TableData.GetEleTalentTreeNodeDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentTreeNodeData", priority)
end


---@return _EleTalentTreeDataRow
function TableData.GetEleTalentTreeDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentTreeData", key, priority)
end


---@return _EleTalentTreeDataRow[]
function TableData.GetEleTalentTreeDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentTreeData", priority)
end


---@return _EleTalentLvDataRow
function TableData.GetEleTalentLvDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentLvData", key, priority)
end


---@return _EleTalentLvDataRow[]
function TableData.GetEleTalentLvDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentLvData", priority)
end


---@return _EleTalentConstDataRow
function TableData.GetEleTalentConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentConstData", key, priority)
end


---@return _EleTalentConstDataRow[]
function TableData.GetEleTalentConstDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentConstData", priority)
end


---@return _EleTalentSequenceInfoDataRow
function TableData.GetEleTalentSequenceInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentSequenceInfoData", key, priority)
end


---@return _EleTalentSequenceInfoDataRow[]
function TableData.GetEleTalentSequenceInfoDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentSequenceInfoData", priority)
end


---@return _EleTalentTriggerTextDataRow
function TableData.GetEleTalentTriggerTextDataRow(key, priority)
	return Game.TableDataManager:GetRow("EleTalentTriggerTextData", key, priority)
end


---@return _EleTalentTriggerTextDataRow[]
function TableData.GetEleTalentTriggerTextDataTable(priority)
	return Game.TableDataManager:GetData("EleTalentTriggerTextData", priority)
end


---@return _WeaponAnimationOverridesDataRow
function TableData.GetWeaponAnimationOverridesDataRow(key, priority)
	return Game.TableDataManager:GetRow("WeaponAnimationOverridesData", key, priority)
end


---@return _WeaponAnimationOverridesDataRow[]
function TableData.GetWeaponAnimationOverridesDataTable(priority)
	return Game.TableDataManager:GetData("WeaponAnimationOverridesData", priority)
end


---@return _PlayerAskArrodesDataRow
function TableData.GetPlayerAskArrodesDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlayerAskArrodesData", key, priority)
end


---@return _PlayerAskArrodesDataRow[]
function TableData.GetPlayerAskArrodesDataTable(priority)
	return Game.TableDataManager:GetData("PlayerAskArrodesData", priority)
end


---@return _ArrodesAskPlayerDataRow
function TableData.GetArrodesAskPlayerDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesAskPlayerData", key, priority)
end


---@return _ArrodesAskPlayerDataRow[]
function TableData.GetArrodesAskPlayerDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesAskPlayerData", priority)
end


---@return _ArrodesDialogueOptionDataRow
function TableData.GetArrodesDialogueOptionDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesDialogueOptionData", key, priority)
end


---@return _ArrodesDialogueOptionDataRow[]
function TableData.GetArrodesDialogueOptionDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesDialogueOptionData", priority)
end


---@return _ArrodesDialogueTalkDataRow
function TableData.GetArrodesDialogueTalkDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesDialogueTalkData", key, priority)
end


---@return _ArrodesDialogueTalkDataRow[]
function TableData.GetArrodesDialogueTalkDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesDialogueTalkData", priority)
end


---@return _ArrodesDialogueActionsDataRow
function TableData.GetArrodesDialogueActionsDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesDialogueActionsData", key, priority)
end


---@return _ArrodesDialogueActionsDataRow[]
function TableData.GetArrodesDialogueActionsDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesDialogueActionsData", priority)
end


---@return _ArrodesEmojiDataRow
function TableData.GetArrodesEmojiDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesEmojiData", key, priority)
end


---@return _ArrodesEmojiDataRow[]
function TableData.GetArrodesEmojiDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesEmojiData", priority)
end


---@return _ArrodesPlayAnimationDataRow
function TableData.GetArrodesPlayAnimationDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesPlayAnimationData", key, priority)
end


---@return _ArrodesPlayAnimationDataRow[]
function TableData.GetArrodesPlayAnimationDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesPlayAnimationData", priority)
end


---@return _ArrodesSoundDataRow
function TableData.GetArrodesSoundDataRow(key, priority)
	return Game.TableDataManager:GetRow("ArrodesSoundData", key, priority)
end


---@return _ArrodesSoundDataRow[]
function TableData.GetArrodesSoundDataTable(priority)
	return Game.TableDataManager:GetData("ArrodesSoundData", priority)
end


---@return _WaterWaveParamDataRow
function TableData.GetWaterWaveParamDataRow(key, priority)
	return Game.TableDataManager:GetRow("WaterWaveParamData", key, priority)
end


---@return _WaterWaveParamDataRow[]
function TableData.GetWaterWaveParamDataTable(priority)
	return Game.TableDataManager:GetData("WaterWaveParamData", priority)
end


---@return _LocalWindParamDataRow
function TableData.GetLocalWindParamDataRow(key, priority)
	return Game.TableDataManager:GetRow("LocalWindParamData", key, priority)
end


---@return _LocalWindParamDataRow[]
function TableData.GetLocalWindParamDataTable(priority)
	return Game.TableDataManager:GetData("LocalWindParamData", priority)
end


---@return _BulletWaterWaveParamDataRow
function TableData.GetBulletWaterWaveParamDataRow(key, priority)
	return Game.TableDataManager:GetRow("BulletWaterWaveParamData", key, priority)
end


---@return _BulletWaterWaveParamDataRow[]
function TableData.GetBulletWaterWaveParamDataTable(priority)
	return Game.TableDataManager:GetData("BulletWaterWaveParamData", priority)
end


---@return _WaterWaveMotorTextureDataRow
function TableData.GetWaterWaveMotorTextureDataRow(key, priority)
	return Game.TableDataManager:GetRow("WaterWaveMotorTextureData", key, priority)
end


---@return _WaterWaveMotorTextureDataRow[]
function TableData.GetWaterWaveMotorTextureDataTable(priority)
	return Game.TableDataManager:GetData("WaterWaveMotorTextureData", priority)
end


---@return _PlotRecapSettingConstDataRow
function TableData.GetPlotRecapSettingConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlotRecapSettingConstData", key, priority)
end


---@return _PlotRecapSettingConstDataRow[]
function TableData.GetPlotRecapSettingConstDataTable(priority)
	return Game.TableDataManager:GetData("PlotRecapSettingConstData", priority)
end


---@return _PlotRecapCategoryLevelDataRow
function TableData.GetPlotRecapCategoryLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlotRecapCategoryLevelData", key, priority)
end


---@return _PlotRecapCategoryLevelDataRow[]
function TableData.GetPlotRecapCategoryLevelDataTable(priority)
	return Game.TableDataManager:GetData("PlotRecapCategoryLevelData", priority)
end


---@return _PlotRecapDataRow
function TableData.GetPlotRecapDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlotRecapData", key, priority)
end


---@return _PlotRecapDataRow[]
function TableData.GetPlotRecapDataTable(priority)
	return Game.TableDataManager:GetData("PlotRecapData", priority)
end


---@return _PlotRecapSideQuestDetailDataRow
function TableData.GetPlotRecapSideQuestDetailDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlotRecapSideQuestDetailData", key, priority)
end


---@return _PlotRecapSideQuestDetailDataRow[]
function TableData.GetPlotRecapSideQuestDetailDataTable(priority)
	return Game.TableDataManager:GetData("PlotRecapSideQuestDetailData", priority)
end


---@return _PlotRecapMistMainRecapSubDataRow
function TableData.GetPlotRecapMistMainRecapSubDataRow(key, priority)
	return Game.TableDataManager:GetRow("PlotRecapMistMainRecapSubData", key, priority)
end


---@return _PlotRecapMistMainRecapSubDataRow[]
function TableData.GetPlotRecapMistMainRecapSubDataTable(priority)
	return Game.TableDataManager:GetData("PlotRecapMistMainRecapSubData", priority)
end


---@return _TitleInfoDataRow
function TableData.GetTitleInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("TitleInfoData", key, priority)
end


---@return _TitleInfoDataRow[]
function TableData.GetTitleInfoDataTable(priority)
	return Game.TableDataManager:GetData("TitleInfoData", priority)
end


---@return _HonorificInfoDataRow
function TableData.GetHonorificInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("HonorificInfoData", key, priority)
end


---@return _HonorificInfoDataRow[]
function TableData.GetHonorificInfoDataTable(priority)
	return Game.TableDataManager:GetData("HonorificInfoData", priority)
end


---@return _CommonInteractorDataRow
function TableData.GetCommonInteractorDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorData", key, priority)
end


---@return _CommonInteractorDataRow[]
function TableData.GetCommonInteractorDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorData", priority)
end


---@return _CommonInteractorStateDataRow
function TableData.GetCommonInteractorStateDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorStateData", key, priority)
end


---@return _CommonInteractorStateDataRow[]
function TableData.GetCommonInteractorStateDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorStateData", priority)
end


---@return _CommonInteractorEventTypeDataRow
function TableData.GetCommonInteractorEventTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorEventTypeData", key, priority)
end


---@return _CommonInteractorEventTypeDataRow[]
function TableData.GetCommonInteractorEventTypeDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorEventTypeData", priority)
end


---@return _CommonInteractorActionDataRow
function TableData.GetCommonInteractorActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorActionData", key, priority)
end


---@return _CommonInteractorActionDataRow[]
function TableData.GetCommonInteractorActionDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorActionData", priority)
end


---@return _CommonInteractorModelDataRow
function TableData.GetCommonInteractorModelDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorModelData", key, priority)
end


---@return _CommonInteractorModelDataRow[]
function TableData.GetCommonInteractorModelDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorModelData", priority)
end


---@return _CommonInteractorAnimDataRow
function TableData.GetCommonInteractorAnimDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorAnimData", key, priority)
end


---@return _CommonInteractorAnimDataRow[]
function TableData.GetCommonInteractorAnimDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorAnimData", priority)
end


---@return _CommonInteractorCameraShakeDataRow
function TableData.GetCommonInteractorCameraShakeDataRow(key, priority)
	return Game.TableDataManager:GetRow("CommonInteractorCameraShakeData", key, priority)
end


---@return _CommonInteractorCameraShakeDataRow[]
function TableData.GetCommonInteractorCameraShakeDataTable(priority)
	return Game.TableDataManager:GetData("CommonInteractorCameraShakeData", priority)
end


---@return _CustomizedGameplayTypeDataRow
function TableData.GetCustomizedGameplayTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomizedGameplayTypeData", key, priority)
end


---@return _CustomizedGameplayTypeDataRow[]
function TableData.GetCustomizedGameplayTypeDataTable(priority)
	return Game.TableDataManager:GetData("CustomizedGameplayTypeData", priority)
end


---@return _CustomizedGameplay2DTypeDataRow
function TableData.GetCustomizedGameplay2DTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("CustomizedGameplay2DTypeData", key, priority)
end


---@return _CustomizedGameplay2DTypeDataRow[]
function TableData.GetCustomizedGameplay2DTypeDataTable(priority)
	return Game.TableDataManager:GetData("CustomizedGameplay2DTypeData", priority)
end


---@return _HiddenFloorGameplayPropDataRow
function TableData.GetHiddenFloorGameplayPropDataRow(key, priority)
	return Game.TableDataManager:GetRow("HiddenFloorGameplayPropData", key, priority)
end


---@return _HiddenFloorGameplayPropDataRow[]
function TableData.GetHiddenFloorGameplayPropDataTable(priority)
	return Game.TableDataManager:GetData("HiddenFloorGameplayPropData", priority)
end


---@return _MirrorReflectionGameplayPropDataRow
function TableData.GetMirrorReflectionGameplayPropDataRow(key, priority)
	return Game.TableDataManager:GetRow("MirrorReflectionGameplayPropData", key, priority)
end


---@return _MirrorReflectionGameplayPropDataRow[]
function TableData.GetMirrorReflectionGameplayPropDataTable(priority)
	return Game.TableDataManager:GetData("MirrorReflectionGameplayPropData", priority)
end


---@return _SingleLineDrawGameplayPropDataRow
function TableData.GetSingleLineDrawGameplayPropDataRow(key, priority)
	return Game.TableDataManager:GetRow("SingleLineDrawGameplayPropData", key, priority)
end


---@return _SingleLineDrawGameplayPropDataRow[]
function TableData.GetSingleLineDrawGameplayPropDataTable(priority)
	return Game.TableDataManager:GetData("SingleLineDrawGameplayPropData", priority)
end


---@return _WorldActivityDataRow
function TableData.GetWorldActivityDataRow(key, priority)
	return Game.TableDataManager:GetRow("WorldActivityData", key, priority)
end


---@return _WorldActivityDataRow[]
function TableData.GetWorldActivityDataTable(priority)
	return Game.TableDataManager:GetData("WorldActivityData", priority)
end


---@return _TraceGeneralDataRow
function TableData.GetTraceGeneralDataRow(key, priority)
	return Game.TableDataManager:GetRow("TraceGeneralData", key, priority)
end


---@return _TraceGeneralDataRow[]
function TableData.GetTraceGeneralDataTable(priority)
	return Game.TableDataManager:GetData("TraceGeneralData", priority)
end


---@return _TraceTypeInfoDataRow
function TableData.GetTraceTypeInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("TraceTypeInfoData", key, priority)
end


---@return _TraceTypeInfoDataRow[]
function TableData.GetTraceTypeInfoDataTable(priority)
	return Game.TableDataManager:GetData("TraceTypeInfoData", priority)
end


---@return _GoddessLeadDataRow
function TableData.GetGoddessLeadDataRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessLeadData", key, priority)
end


---@return _GoddessLeadDataRow[]
function TableData.GetGoddessLeadDataTable(priority)
	return Game.TableDataManager:GetData("GoddessLeadData", priority)
end


---@return _GoddessFirstRow
function TableData.GetGoddessFirstRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessFirst", key, priority)
end


---@return _GoddessFirstRow[]
function TableData.GetGoddessFirstTable(priority)
	return Game.TableDataManager:GetData("GoddessFirst", priority)
end


---@return _GoddessSecondRow
function TableData.GetGoddessSecondRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessSecond", key, priority)
end


---@return _GoddessSecondRow[]
function TableData.GetGoddessSecondTable(priority)
	return Game.TableDataManager:GetData("GoddessSecond", priority)
end


---@return _GoddessThirdRow
function TableData.GetGoddessThirdRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessThird", key, priority)
end


---@return _GoddessThirdRow[]
function TableData.GetGoddessThirdTable(priority)
	return Game.TableDataManager:GetData("GoddessThird", priority)
end


---@return _GoddessFourthRow
function TableData.GetGoddessFourthRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessFourth", key, priority)
end


---@return _GoddessFourthRow[]
function TableData.GetGoddessFourthTable(priority)
	return Game.TableDataManager:GetData("GoddessFourth", priority)
end


---@return _GoddessFifthRow
function TableData.GetGoddessFifthRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessFifth", key, priority)
end


---@return _GoddessFifthRow[]
function TableData.GetGoddessFifthTable(priority)
	return Game.TableDataManager:GetData("GoddessFifth", priority)
end


---@return _GoddessSixthRow
function TableData.GetGoddessSixthRow(key, priority)
	return Game.TableDataManager:GetRow("GoddessSixth", key, priority)
end


---@return _GoddessSixthRow[]
function TableData.GetGoddessSixthTable(priority)
	return Game.TableDataManager:GetData("GoddessSixth", priority)
end


---@return _NarrativeEPCategoryDataRow
function TableData.GetNarrativeEPCategoryDataRow(key, priority)
	return Game.TableDataManager:GetRow("NarrativeEPCategoryData", key, priority)
end


---@return _NarrativeEPCategoryDataRow[]
function TableData.GetNarrativeEPCategoryDataTable(priority)
	return Game.TableDataManager:GetData("NarrativeEPCategoryData", priority)
end


---@return _ChampionSettingDataRow
function TableData.GetChampionSettingDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChampionSettingData", key, priority)
end


---@return _ChampionSettingDataRow[]
function TableData.GetChampionSettingDataTable(priority)
	return Game.TableDataManager:GetData("ChampionSettingData", priority)
end


---@return _ChampionScheduleDataRow
function TableData.GetChampionScheduleDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChampionScheduleData", key, priority)
end


---@return _ChampionScheduleDataRow[]
function TableData.GetChampionScheduleDataTable(priority)
	return Game.TableDataManager:GetData("ChampionScheduleData", priority)
end


---@return _ChampionDivisionDataRow
function TableData.GetChampionDivisionDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChampionDivisionData", key, priority)
end


---@return _ChampionDivisionDataRow[]
function TableData.GetChampionDivisionDataTable(priority)
	return Game.TableDataManager:GetData("ChampionDivisionData", priority)
end


---@return _ChampionEliminationScheduleDataRow
function TableData.GetChampionEliminationScheduleDataRow(key, priority)
	return Game.TableDataManager:GetRow("ChampionEliminationScheduleData", key, priority)
end


---@return _ChampionEliminationScheduleDataRow[]
function TableData.GetChampionEliminationScheduleDataTable(priority)
	return Game.TableDataManager:GetData("ChampionEliminationScheduleData", priority)
end


---@return _ReasoningClueDataRow
function TableData.GetReasoningClueDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReasoningClueData", key, priority)
end


---@return _ReasoningClueDataRow[]
function TableData.GetReasoningClueDataTable(priority)
	return Game.TableDataManager:GetData("ReasoningClueData", priority)
end


---@return _ReasoningLayerDataRow
function TableData.GetReasoningLayerDataRow(key, priority)
	return Game.TableDataManager:GetRow("ReasoningLayerData", key, priority)
end


---@return _ReasoningLayerDataRow[]
function TableData.GetReasoningLayerDataTable(priority)
	return Game.TableDataManager:GetData("ReasoningLayerData", priority)
end


---@return _SealedInfoDataRow
function TableData.GetSealedInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("SealedInfoData", key, priority)
end


---@return _SealedInfoDataRow[]
function TableData.GetSealedInfoDataTable(priority)
	return Game.TableDataManager:GetData("SealedInfoData", priority)
end


---@return _SealedExtraEffectDataRow
function TableData.GetSealedExtraEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("SealedExtraEffectData", key, priority)
end


---@return _SealedExtraEffectDataRow[]
function TableData.GetSealedExtraEffectDataTable(priority)
	return Game.TableDataManager:GetData("SealedExtraEffectData", priority)
end


---@return _SealedInfoAttrDataRow
function TableData.GetSealedInfoAttrDataRow(key, priority)
	return Game.TableDataManager:GetRow("SealedInfoAttrData", key, priority)
end


---@return _SealedInfoAttrDataRow[]
function TableData.GetSealedInfoAttrDataTable(priority)
	return Game.TableDataManager:GetData("SealedInfoAttrData", priority)
end


---@return _XtraMatInfoDataRow
function TableData.GetXtraMatInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatInfoData", key, priority)
end


---@return _XtraMatInfoDataRow[]
function TableData.GetXtraMatInfoDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatInfoData", priority)
end


---@return _XtraMatNameRuleDataRow
function TableData.GetXtraMatNameRuleDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatNameRuleData", key, priority)
end


---@return _XtraMatNameRuleDataRow[]
function TableData.GetXtraMatNameRuleDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatNameRuleData", priority)
end


---@return _XtraMatTCDataRow
function TableData.GetXtraMatTCDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatTCData", key, priority)
end


---@return _XtraMatTCDataRow[]
function TableData.GetXtraMatTCDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatTCData", priority)
end


---@return _RelicsConstDataRow
function TableData.GetRelicsConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("RelicsConstData", key, priority)
end


---@return _RelicsConstDataRow[]
function TableData.GetRelicsConstDataTable(priority)
	return Game.TableDataManager:GetData("RelicsConstData", priority)
end


---@return _XtraMatRandomWordDataRow
function TableData.GetXtraMatRandomWordDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatRandomWordData", key, priority)
end


---@return _XtraMatRandomWordDataRow[]
function TableData.GetXtraMatRandomWordDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatRandomWordData", priority)
end


---@return _XtraMatRandomGroupDataRow
function TableData.GetXtraMatRandomGroupDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatRandomGroupData", key, priority)
end


---@return _XtraMatRandomGroupDataRow[]
function TableData.GetXtraMatRandomGroupDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatRandomGroupData", priority)
end


---@return _XtraMatRandomClassDataRow
function TableData.GetXtraMatRandomClassDataRow(key, priority)
	return Game.TableDataManager:GetRow("XtraMatRandomClassData", key, priority)
end


---@return _XtraMatRandomClassDataRow[]
function TableData.GetXtraMatRandomClassDataTable(priority)
	return Game.TableDataManager:GetData("XtraMatRandomClassData", priority)
end


---@return _FuseInfoDataRow
function TableData.GetFuseInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("FuseInfoData", key, priority)
end


---@return _FuseInfoDataRow[]
function TableData.GetFuseInfoDataTable(priority)
	return Game.TableDataManager:GetData("FuseInfoData", priority)
end


---@return _FuseProgressInfoDataRow
function TableData.GetFuseProgressInfoDataRow(key, priority)
	return Game.TableDataManager:GetRow("FuseProgressInfoData", key, priority)
end


---@return _FuseProgressInfoDataRow[]
function TableData.GetFuseProgressInfoDataTable(priority)
	return Game.TableDataManager:GetData("FuseProgressInfoData", priority)
end


---@return _BasicDanceMusicDataRow
function TableData.GetBasicDanceMusicDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceMusicData", key, priority)
end


---@return _BasicDanceMusicDataRow[]
function TableData.GetBasicDanceMusicDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceMusicData", priority)
end


---@return _BasicDanceStageDataRow
function TableData.GetBasicDanceStageDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceStageData", key, priority)
end


---@return _BasicDanceStageDataRow[]
function TableData.GetBasicDanceStageDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceStageData", priority)
end


---@return _BasicDanceFormationDataRow
function TableData.GetBasicDanceFormationDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceFormationData", key, priority)
end


---@return _BasicDanceFormationDataRow[]
function TableData.GetBasicDanceFormationDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceFormationData", priority)
end


---@return _BasicDanceConstDataRow
function TableData.GetBasicDanceConstDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceConstData", key, priority)
end


---@return _BasicDanceConstDataRow[]
function TableData.GetBasicDanceConstDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceConstData", priority)
end


---@return _BasicDanceQTEDataRow
function TableData.GetBasicDanceQTEDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceQTEData", key, priority)
end


---@return _BasicDanceQTEDataRow[]
function TableData.GetBasicDanceQTEDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceQTEData", priority)
end


---@return _BasicDanceEvaluateLevelDataRow
function TableData.GetBasicDanceEvaluateLevelDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceEvaluateLevelData", key, priority)
end


---@return _BasicDanceEvaluateLevelDataRow[]
function TableData.GetBasicDanceEvaluateLevelDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceEvaluateLevelData", priority)
end


---@return _BasicDanceSoloActionDataRow
function TableData.GetBasicDanceSoloActionDataRow(key, priority)
	return Game.TableDataManager:GetRow("BasicDanceSoloActionData", key, priority)
end


---@return _BasicDanceSoloActionDataRow[]
function TableData.GetBasicDanceSoloActionDataTable(priority)
	return Game.TableDataManager:GetData("BasicDanceSoloActionData", priority)
end


---@return _InterfaceAppearancePortraitDataRow
function TableData.GetInterfaceAppearancePortraitDataRow(key, priority)
	return Game.TableDataManager:GetRow("InterfaceAppearancePortraitData", key, priority)
end


---@return _InterfaceAppearancePortraitDataRow[]
function TableData.GetInterfaceAppearancePortraitDataTable(priority)
	return Game.TableDataManager:GetData("InterfaceAppearancePortraitData", priority)
end


---@return _InterfaceAppearancePortraitFrameDataRow
function TableData.GetInterfaceAppearancePortraitFrameDataRow(key, priority)
	return Game.TableDataManager:GetRow("InterfaceAppearancePortraitFrameData", key, priority)
end


---@return _InterfaceAppearancePortraitFrameDataRow[]
function TableData.GetInterfaceAppearancePortraitFrameDataTable(priority)
	return Game.TableDataManager:GetData("InterfaceAppearancePortraitFrameData", priority)
end


---@return _InterfaceAppearanceChatBubbleDataRow
function TableData.GetInterfaceAppearanceChatBubbleDataRow(key, priority)
	return Game.TableDataManager:GetRow("InterfaceAppearanceChatBubbleData", key, priority)
end


---@return _InterfaceAppearanceChatBubbleDataRow[]
function TableData.GetInterfaceAppearanceChatBubbleDataTable(priority)
	return Game.TableDataManager:GetData("InterfaceAppearanceChatBubbleData", priority)
end


---@return _InterfaceAppearanceTeamJoinAnimationDataRow
function TableData.GetInterfaceAppearanceTeamJoinAnimationDataRow(key, priority)
	return Game.TableDataManager:GetRow("InterfaceAppearanceTeamJoinAnimationData", key, priority)
end


---@return _InterfaceAppearanceTeamJoinAnimationDataRow[]
function TableData.GetInterfaceAppearanceTeamJoinAnimationDataTable(priority)
	return Game.TableDataManager:GetData("InterfaceAppearanceTeamJoinAnimationData", priority)
end


---@return _InterfaceAppearanceTeamNameplateDataRow
function TableData.GetInterfaceAppearanceTeamNameplateDataRow(key, priority)
	return Game.TableDataManager:GetRow("InterfaceAppearanceTeamNameplateData", key, priority)
end


---@return _InterfaceAppearanceTeamNameplateDataRow[]
function TableData.GetInterfaceAppearanceTeamNameplateDataTable(priority)
	return Game.TableDataManager:GetData("InterfaceAppearanceTeamNameplateData", priority)
end


---@return _EstatePortalDataRow
function TableData.GetEstatePortalDataRow(key, priority)
	return Game.TableDataManager:GetRow("EstatePortalData", key, priority)
end


---@return _EstatePortalDataRow[]
function TableData.GetEstatePortalDataTable(priority)
	return Game.TableDataManager:GetData("EstatePortalData", priority)
end


---@return _EstatePortalCameraDataRow
function TableData.GetEstatePortalCameraDataRow(key, priority)
	return Game.TableDataManager:GetRow("EstatePortalCameraData", key, priority)
end


---@return _EstatePortalCameraDataRow[]
function TableData.GetEstatePortalCameraDataTable(priority)
	return Game.TableDataManager:GetData("EstatePortalCameraData", priority)
end


---@return _DiceCheckDataRow
function TableData.GetDiceCheckDataRow(key, priority)
	return Game.TableDataManager:GetRow("DiceCheckData", key, priority)
end


---@return _DiceCheckDataRow[]
function TableData.GetDiceCheckDataTable(priority)
	return Game.TableDataManager:GetData("DiceCheckData", priority)
end


---@return _DiceCheckBonusDataRow
function TableData.GetDiceCheckBonusDataRow(key, priority)
	return Game.TableDataManager:GetRow("DiceCheckBonusData", key, priority)
end


---@return _DiceCheckBonusDataRow[]
function TableData.GetDiceCheckBonusDataTable(priority)
	return Game.TableDataManager:GetData("DiceCheckBonusData", priority)
end


---@return _DiceCheckTypeDataRow
function TableData.GetDiceCheckTypeDataRow(key, priority)
	return Game.TableDataManager:GetRow("DiceCheckTypeData", key, priority)
end


---@return _DiceCheckTypeDataRow[]
function TableData.GetDiceCheckTypeDataTable(priority)
	return Game.TableDataManager:GetData("DiceCheckTypeData", priority)
end


---@return _PendulumDivineDataRow
function TableData.GetPendulumDivineDataRow(key, priority)
	return Game.TableDataManager:GetRow("PendulumDivineData", key, priority)
end


---@return _PendulumDivineDataRow[]
function TableData.GetPendulumDivineDataTable(priority)
	return Game.TableDataManager:GetData("PendulumDivineData", priority)
end


---@return _PendulumAnimConfigDataRow
function TableData.GetPendulumAnimConfigDataRow(key, priority)
	return Game.TableDataManager:GetRow("PendulumAnimConfigData", key, priority)
end


---@return _PendulumAnimConfigDataRow[]
function TableData.GetPendulumAnimConfigDataTable(priority)
	return Game.TableDataManager:GetData("PendulumAnimConfigData", priority)
end


---@return _TarotDivineDataRow
function TableData.GetTarotDivineDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotDivineData", key, priority)
end


---@return _TarotDivineDataRow[]
function TableData.GetTarotDivineDataTable(priority)
	return Game.TableDataManager:GetData("TarotDivineData", priority)
end


---@return _TarotDivineAnalysisDataRow
function TableData.GetTarotDivineAnalysisDataRow(key, priority)
	return Game.TableDataManager:GetRow("TarotDivineAnalysisData", key, priority)
end


---@return _TarotDivineAnalysisDataRow[]
function TableData.GetTarotDivineAnalysisDataTable(priority)
	return Game.TableDataManager:GetData("TarotDivineAnalysisData", priority)
end


---@return _ExploreRewardDataRow
function TableData.GetExploreRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExploreRewardData", key, priority)
end


---@return _ExploreRewardDataRow[]
function TableData.GetExploreRewardDataTable(priority)
	return Game.TableDataManager:GetData("ExploreRewardData", priority)
end


---@return _ExplorationProgressRewardDataRow
function TableData.GetExplorationProgressRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("ExplorationProgressRewardData", key, priority)
end


---@return _ExplorationProgressRewardDataRow[]
function TableData.GetExplorationProgressRewardDataTable(priority)
	return Game.TableDataManager:GetData("ExplorationProgressRewardData", priority)
end


---@return _PossessionEffectDataRow
function TableData.GetPossessionEffectDataRow(key, priority)
	return Game.TableDataManager:GetRow("PossessionEffectData", key, priority)
end


---@return _PossessionEffectDataRow[]
function TableData.GetPossessionEffectDataTable(priority)
	return Game.TableDataManager:GetData("PossessionEffectData", priority)
end


---@return _BoxManPlayCfgRow
function TableData.GetBoxManPlayCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManPlayCfg", key, priority)
end


---@return _BoxManPlayCfgRow[]
function TableData.GetBoxManPlayCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManPlayCfg", priority)
end


---@return _BoxManLevelCfgRow
function TableData.GetBoxManLevelCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManLevelCfg", key, priority)
end


---@return _BoxManLevelCfgRow[]
function TableData.GetBoxManLevelCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManLevelCfg", priority)
end


---@return _BoxManItemCfgRow
function TableData.GetBoxManItemCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManItemCfg", key, priority)
end


---@return _BoxManItemCfgRow[]
function TableData.GetBoxManItemCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManItemCfg", priority)
end


---@return _BoxManGridCfgRow
function TableData.GetBoxManGridCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManGridCfg", key, priority)
end


---@return _BoxManGridCfgRow[]
function TableData.GetBoxManGridCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManGridCfg", priority)
end


---@return _BoxManGridBPCfgRow
function TableData.GetBoxManGridBPCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManGridBPCfg", key, priority)
end


---@return _BoxManGridBPCfgRow[]
function TableData.GetBoxManGridBPCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManGridBPCfg", priority)
end


---@return _BoxManItemBPCfgRow
function TableData.GetBoxManItemBPCfgRow(key, priority)
	return Game.TableDataManager:GetRow("BoxManItemBPCfg", key, priority)
end


---@return _BoxManItemBPCfgRow[]
function TableData.GetBoxManItemBPCfgTable(priority)
	return Game.TableDataManager:GetData("BoxManItemBPCfg", priority)
end


---@return _WonderShopDataRow
function TableData.GetWonderShopDataRow(key, priority)
	return Game.TableDataManager:GetRow("WonderShopData", key, priority)
end


---@return _WonderShopDataRow[]
function TableData.GetWonderShopDataTable(priority)
	return Game.TableDataManager:GetData("WonderShopData", priority)
end


---@return _WonderShopRewardDataRow
function TableData.GetWonderShopRewardDataRow(key, priority)
	return Game.TableDataManager:GetRow("WonderShopRewardData", key, priority)
end


---@return _WonderShopRewardDataRow[]
function TableData.GetWonderShopRewardDataTable(priority)
	return Game.TableDataManager:GetData("WonderShopRewardData", priority)
end


---@return _WonderShopStringDataRow
function TableData.GetWonderShopStringDataRow(key, priority)
	return Game.TableDataManager:GetRow("WonderShopStringData", key, priority)
end


---@return _WonderShopStringDataRow[]
function TableData.GetWonderShopStringDataTable(priority)
	return Game.TableDataManager:GetData("WonderShopStringData", priority)
end

function TableData.Get_ParentAchievementDict()
    return Game.TableDataManager:GetAttr("Achievements", "ParentAchievementDict")
end

function TableData.Get_ActivityTypeDetailIDMapData()
    return Game.TableDataManager:GetAttr("ActivityData", "ActivityTypeDetailIDMapData")
end

function TableData.Get_SCPlayerShowPriorityMap()
    return Game.TableDataManager:GetAttr("SCPlayerShowPriorityData", "SCPlayerShowPriorityMap")
end

function TableData.Get_ModelClipShowPriorityMap()
    return Game.TableDataManager:GetAttr("ModelDisplayPriorityData", "ModelClipShowPriorityMap")
end

function TableData.Get_AttackLogicParamsNew()
    return Game.TableDataManager:GetAttr("AttackTypeData", "AttackLogicParamsNew")
end

---@return StateControlProtectionData
function TableData.GetStateControlProtectionDataRow(key, priority)
    return Game.TableDataManager:GetRow("StateControlProtectionData", key, priority)
end

---@return StateControlProtectionData[]
function TableData.GetStateControlProtectionDataTable(priority)
    return Game.TableDataManager:GetData("StateControlProtectionData", priority)
end

function TableData.Get_StateControlProtectionMap()
    return Game.TableDataManager:GetAttr("StateControlProtectionData", "StateControlProtectionMap")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

function TableData.Get_BasicDanceSongIDList()
    return Game.TableDataManager:GetAttr("BasicDanceMusicData", "BasicDanceSongIDList")
end

function TableData.Get_BasicDanceStageIDList()
    return Game.TableDataManager:GetAttr("BasicDanceStageData", "BasicDanceStageIDList")
end

function TableData.Get_BasicDanceFormationIDList()
    return Game.TableDataManager:GetAttr("BasicDanceFormationData", "BasicDanceFormationIDList")
end

function TableData.Get_ConditionsMap()
    return Game.TableDataManager:GetAttr("BubbleData", "ConditionsMap")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

---@return BuffGroupData
function TableData.GetBuffGroupDataRow(key, priority)
    return Game.TableDataManager:GetRow("BuffGroupData", key, priority)
end

---@return BuffGroupData[]
function TableData.GetBuffGroupDataTable(priority)
    return Game.TableDataManager:GetData("BuffGroupData", priority)
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

---@return CameraMode2ModeConfigData
function TableData.GetCameraMode2ModeConfigDataRow(key, priority)
    return Game.TableDataManager:GetRow("CameraMode2ModeConfigData", key, priority)
end

---@return CameraMode2ModeConfigData[]
function TableData.GetCameraMode2ModeConfigDataTable(priority)
    return Game.TableDataManager:GetData("CameraMode2ModeConfigData", priority)
end

function TableData.Get_ChatRoomType2Identity()
    return Game.TableDataManager:GetAttr("ChatRoomIdentityData", "ChatRoomType2Identity")
end

function TableData.Get_Identity2DefaultState()
    return Game.TableDataManager:GetAttr("ChatRoomIdentityStateData", "Identity2DefaultState")
end

function TableData.Get_LevelBelongRegionMap()
    return Game.TableDataManager:GetAttr("RegionClimateData", "LevelBelongRegionMap")
end

---@return Collectibles
function TableData.GetCollectiblesRow(key, priority)
    return Game.TableDataManager:GetRow("Collectibles", key, priority)
end

---@return Collectibles[]
function TableData.GetCollectiblesTable(priority)
    return Game.TableDataManager:GetData("Collectibles", priority)
end

function TableData.Get_SoundPathKeyData()
    return Game.TableDataManager:GetAttr("CommonEffectData", "SoundPathKeyData")
end

function TableData.Get_ButtonActionList()
    return Game.TableDataManager:GetAttr("CommonInteractorActionData", "ButtonActionList")
end

function TableData.Get_StageEndReset()
    return Game.TableDataManager:GetAttr("DungeonData", "StageEndReset")
end

function TableData.Get_bLevelDungeonMaxLvlLimitMap()
    return Game.TableDataManager:GetAttr("DungeonData", "bLevelDungeonMaxLvlLimitMap")
end

function TableData.Get_StageStartBattleReset()
    return Game.TableDataManager:GetAttr("DungeonData", "StageStartBattleReset")
end

function TableData.Get_bLevelDungeonMinLvlLimitMap()
    return Game.TableDataManager:GetAttr("DungeonData", "bLevelDungeonMinLvlLimitMap")
end

function TableData.Get_StageEntSpaceReset()
    return Game.TableDataManager:GetAttr("DungeonData", "StageEntSpaceReset")
end

function TableData.Get_bDungeonLastStages()
    return Game.TableDataManager:GetAttr("DungeonData", "bDungeonLastStages")
end

function TableData.Get_EnterSpaceStartBattleReset()
    return Game.TableDataManager:GetAttr("DungeonData", "EnterSpaceStartBattleReset")
end

function TableData.Get_dungeonID2StageID2RankID()
    return Game.TableDataManager:GetAttr("DungeonStageRankingData", "dungeonID2StageID2RankID")
end

function TableData.Get_DungeonID2DungeonEnterID()
    return Game.TableDataManager:GetAttr("DungeonEnterData", "DungeonID2DungeonEnterID")
end

function TableData.Get_GroupPriorityLimit()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "GroupPriorityLimit")
end

function TableData.Get_DungeonType2StageIDs()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "DungeonType2StageIDs")
end

function TableData.Get_GroupPriority2DungeonID()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "GroupPriority2DungeonID")
end

function TableData.Get_ReversedGroup2PriorityList()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "ReversedGroup2PriorityList")
end

function TableData.Get_Group2MinPriority()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "Group2MinPriority")
end

function TableData.Get_SortedGroup2PriorityList()
    return Game.TableDataManager:GetAttr("DungeonRewardData", "SortedGroup2PriorityList")
end

function TableData.Get_SkillToElementMap()
    return Game.TableDataManager:GetAttr("ElementEffectsData", "SkillToElementMap")
end

function TableData.Get_BuffToElementMap()
    return Game.TableDataManager:GetAttr("ElementEffectsData", "BuffToElementMap")
end

function TableData.Get_ElementName2IDMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "ElementName2IDMap")
end

function TableData.Get_eleLevelPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleLevelPropMap")
end

function TableData.Get_eleHurtMultiPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleHurtMultiPropMap")
end

function TableData.Get_eleCritLevelPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleCritLevelPropMap")
end

function TableData.Get_eleIgnoreAntiPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleIgnoreAntiPropMap")
end

function TableData.Get_eleAntiPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleAntiPropMap")
end

function TableData.Get_eleHurtReducePropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "eleHurtReducePropMap")
end

function TableData.Get_elementAtkPropMap()
    return Game.TableDataManager:GetAttr("ElementTypeDefData", "elementAtkPropMap")
end

function TableData.Get_EquipmentWordSetLevelToID()
    return Game.TableDataManager:GetAttr("EquipmentWordSetData", "EquipmentWordSetLevelToID")
end

function TableData.Get_EquipmentWordGroupTypeNameSlotClassToRow()
    return Game.TableDataManager:GetAttr("EquipmentWordGroupTypeNameData", "EquipmentWordGroupTypeNameSlotClassToRow")
end

function TableData.Get_ProfessionTCRaritySlotEquipNos()
    return Game.TableDataManager:GetAttr("EquipmentData", "ProfessionTCRaritySlotEquipNos")
end

function TableData.Get_EquipmentGrowBodyConfigBatchSlotToID()
    return Game.TableDataManager:GetAttr("EquipmentGrowBodyConfigData", "EquipmentGrowBodyConfigBatchSlotToID")
end

function TableData.Get_EquipSeason2EnhanceID()
    return Game.TableDataManager:GetAttr("EquipmentGrowBodyEnhanceData", "EquipSeason2EnhanceID")
end

function TableData.Get_EquipmentBodySuitSeasonTypeLevel2ID()
    return Game.TableDataManager:GetAttr("EquipmentGrowBodySuitData", "EquipmentBodySuitSeasonTypeLevel2ID")
end

function TableData.Get_EquipmentWordGroupTypeNameSlotClassToRow()
    return Game.TableDataManager:GetAttr("EquipmentWordGroupTypeNameData", "EquipmentWordGroupTypeNameSlotClassToRow")
end

function TableData.Get_EquipmentRandomWordGroupSpace()
    return Game.TableDataManager:GetAttr("EquipmentWordRandomGroupData", "EquipmentRandomWordGroupSpace")
end

function TableData.Get_EquipmentRandomWordID2MemeryKey()
    return Game.TableDataManager:GetAttr("EquipmentWordRandomWordData", "EquipmentRandomWordID2MemeryKey")
end

function TableData.Get_EquipmentWordRandomWordGroupSetToMinMaxWordID()
    return Game.TableDataManager:GetAttr("EquipmentWordRandomWordData", "EquipmentWordRandomWordGroupSetToMinMaxWordID")
end

function TableData.Get_EquipRandomWordID2ClassType()
    return Game.TableDataManager:GetAttr("EquipmentWordRandomClassData", "EquipRandomWordID2ClassType")
end

function TableData.Get_EquipmentWordRandomClassGroupToRow()
    return Game.TableDataManager:GetAttr("EquipmentWordRandomClassData", "EquipmentWordRandomClassGroupToRow")
end

function TableData.Get_SlotSeasonID2SpiritualityConvergenceID()
    return Game.TableDataManager:GetAttr("EquipmentSpiritualityConvergenceData", "SlotSeasonID2SpiritualityConvergenceID")
end

function TableData.Get_ExploreFirstToSecondLevelArea()
    return Game.TableDataManager:GetAttr("ExploreSecondLevelAreaData", "ExploreFirstToSecondLevelArea")
end

function TableData.Get_ExploreArea2CollectType2ID()
    return Game.TableDataManager:GetAttr("ExploreRewardData", "ExploreArea2CollectType2ID")
end

function TableData.Get_ExploreInstanceRewardMap()
    return Game.TableDataManager:GetAttr("ExploreRewardData", "ExploreInstanceRewardMap")
end

function TableData.Get_ExploreRingRewardMap()
    return Game.TableDataManager:GetAttr("ExploreRewardData", "ExploreRingRewardMap")
end

function TableData.Get_AbandonedExploreInfo()
    return Game.TableDataManager:GetAttr("ExploreRewardData", "AbandonedExploreInfo")
end

function TableData.Get_ExploreRewardSumMap()
    return Game.TableDataManager:GetAttr("ExploreRewardData", "ExploreRewardSumMap")
end

---@return FacadeControlData
function TableData.GetFacadeControlDataRow(key, priority)
    return Game.TableDataManager:GetRow("FacadeControlData", key, priority)
end

---@return FacadeControlData[]
function TableData.GetFacadeControlDataTable(priority)
    return Game.TableDataManager:GetData("FacadeControlData", priority)
end

---@return FashionID2SubTypeData
function TableData.GetFashionID2SubTypeDataRow(key, priority)
    return Game.TableDataManager:GetRow("FashionID2SubTypeData", key, priority)
end

---@return FashionID2SubTypeData[]
function TableData.GetFashionID2SubTypeDataTable(priority)
    return Game.TableDataManager:GetData("FashionID2SubTypeData", priority)
end

function TableData.Get_actionName2ActionIdMap()
    return Game.TableDataManager:GetAttr("FightActionData", "actionName2ActionIdMap")
end

function TableData.Get_elementReplacementActionMap()
    return Game.TableDataManager:GetAttr("FightActionData", "elementReplacementActionMap")
end

function TableData.Get_autoCalcFStatePropSet()
    return Game.TableDataManager:GetAttr("FStatePropData", "autoCalcFStatePropSet")
end

function TableData.Get_statePropPropRangeMap()
    return Game.TableDataManager:GetAttr("FStatePropData", "statePropPropRangeMap")
end

function TableData.Get_statePropRangePropChgEffectMap()
    return Game.TableDataManager:GetAttr("FStatePropData", "statePropRangePropChgEffectMap")
end

function TableData.Get_fStatePropMap()
    return Game.TableDataManager:GetAttr("FStatePropData", "fStatePropMap")
end

function TableData.Get_rangePropChgEffectMap()
    return Game.TableDataManager:GetAttr("FightPropData", "rangePropChgEffectMap")
end

function TableData.Get_primaryPropSet()
    return Game.TableDataManager:GetAttr("FightPropData", "primaryPropSet")
end

function TableData.Get_propRangeMap()
    return Game.TableDataManager:GetAttr("FightPropData", "propRangeMap")
end

function TableData.Get_regPropMap()
    return Game.TableDataManager:GetAttr("FightPropData", "regPropMap")
end

function TableData.Get_fightPropGroupSubKey2PropMap()
    return Game.TableDataManager:GetAttr("FightPropGroupData", "fightPropGroupSubKey2PropMap")
end

function TableData.Get_propMode2PropList()
    return Game.TableDataManager:GetAttr("FightPropModeData", "propMode2PropList")
end

function TableData.Get_fightPropSetMap()
    return Game.TableDataManager:GetAttr("FightPropModeData", "fightPropSetMap")
end

function TableData.Get_propChangeModeMap()
    return Game.TableDataManager:GetAttr("FightPropModeData", "propChangeModeMap")
end

function TableData.Get_fightPropModeSetMap()
    return Game.TableDataManager:GetAttr("FightPropModeData", "fightPropModeSetMap")
end

function TableData.Get_propModeList()
    return Game.TableDataManager:GetAttr("FightPropModeData", "propModeList")
end

---@return PropData
function TableData.GetPropDataRow(key, priority)
    return Game.TableDataManager:GetRow("PropData", key, priority)
end

---@return PropData[]
function TableData.GetPropDataTable(priority)
    return Game.TableDataManager:GetData("PropData", priority)
end

function TableData.Get_SkillBalanceTypes()
    return Game.TableDataManager:GetAttr("SkillBalanceRuleData", "SkillBalanceTypes")
end

function TableData.Get_SkillBalanceRules()
    return Game.TableDataManager:GetAttr("SkillBalanceRuleData", "SkillBalanceRules")
end

function TableData.Get_SkillTypeUnlockMap()
    return Game.TableDataManager:GetAttr("FightPropBalanceRuleData", "SkillTypeUnlockMap")
end

function TableData.Get_FriendImprintDayTypeData()
    return Game.TableDataManager:GetAttr("FriendImprintData", "FriendImprintDayTypeData")
end

function TableData.Get_RobotUnlockFunction()
    return Game.TableDataManager:GetAttr("FunctionInfoData", "RobotUnlockFunction")
end

function TableData.Get_FunctionEnumToLockData()
    return Game.TableDataManager:GetAttr("FunctionInfoData", "FunctionEnumToLockData")
end

function TableData.Get_FunctionLevelToLockData()
    return Game.TableDataManager:GetAttr("FunctionInfoData", "FunctionLevelToLockData")
end

function TableData.Get_QuestRingToLockData()
    return Game.TableDataManager:GetAttr("FunctionInfoData", "QuestRingToLockData")
end

function TableData.Get_topContribTitle()
    return Game.TableDataManager:GetAttr("GuildChatTitleData", "topContribTitle")
end

function TableData.Get_guildChatTitles()
    return Game.TableDataManager:GetAttr("GuildChatTitleData", "guildChatTitles")
end

function TableData.Get_mostZhanLiTitle()
    return Game.TableDataManager:GetAttr("GuildChatTitleData", "mostZhanLiTitle")
end

function TableData.Get_GuildExercisePropMapData()
    return Game.TableDataManager:GetAttr("GuildExercisePropData", "GuildExercisePropMapData")
end

function TableData.Get_MainTowerInfoByCamp()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "MainTowerInfoByCamp")
end

function TableData.Get_MainTowerLevel()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "MainTowerLevel")
end

function TableData.Get_TowerInfoByInstanceID()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "TowerInfoByInstanceID")
end

function TableData.Get_TowerID2MonsterID()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "TowerID2MonsterID")
end

function TableData.Get_MonsterID2Camp()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "MonsterID2Camp")
end

function TableData.Get_TotalTowerNumberByCamp()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "TotalTowerNumberByCamp")
end

function TableData.Get_TowerInfoByCamp()
    return Game.TableDataManager:GetAttr("GuildLeagueTowerInfoData", "TowerInfoByCamp")
end

function TableData.Get_GuildLeagueWeekCount()
    return Game.TableDataManager:GetAttr("GuildLeagueSeasonData", "GuildLeagueWeekCount")
end

function TableData.Get_MaxGroupIndex()
    return Game.TableDataManager:GetAttr("GuildLeagueRankRewardData", "MaxGroupIndex")
end

function TableData.Get_MinGroupIndex()
    return Game.TableDataManager:GetAttr("GuildLeagueRankRewardData", "MinGroupIndex")
end

function TableData.Get_canShuffleQuest()
    return Game.TableDataManager:GetAttr("GuildPuzzleData", "canShuffleQuest")
end

function TableData.Get_rewardBuffProbs()
    return Game.TableDataManager:GetAttr("GuildPartyRewardData", "rewardBuffProbs")
end

function TableData.Get_rewardBuffs()
    return Game.TableDataManager:GetAttr("GuildPartyRewardData", "rewardBuffs")
end

function TableData.Get_RarityMap()
    return Game.TableDataManager:GetAttr("GuildMaterialTaskItemWeightData", "RarityMap")
end

function TableData.Get_TaskNumMap()
    return Game.TableDataManager:GetAttr("GuildMaterialTaskFinishRewardData", "TaskNumMap")
end

function TableData.Get_workshopLevelMapByType()
    return Game.TableDataManager:GetAttr("WorkshopLevelUpData", "workshopLevelMapByType")
end

function TableData.Get_TaskItemRecycleList()
    return Game.TableDataManager:GetAttr("ItemNewData", "TaskItemRecycleList")
end

function TableData.Get_useItemSkillList()
    return Game.TableDataManager:GetAttr("ItemNewData", "useItemSkillList")
end

function TableData.Get_TaskItemDestroyList()
    return Game.TableDataManager:GetAttr("ItemNewData", "TaskItemDestroyList")
end

---@return ItemOperationTypeData
function TableData.GetItemOperationTypeDataRow(key, priority)
    return Game.TableDataManager:GetRow("ItemOperationTypeData", key, priority)
end

---@return ItemOperationTypeData[]
function TableData.GetItemOperationTypeDataTable(priority)
    return Game.TableDataManager:GetData("ItemOperationTypeData", priority)
end

---@return MedicineData
function TableData.GetMedicineDataRow(key, priority)
    return Game.TableDataManager:GetRow("MedicineData", key, priority)
end

---@return MedicineData[]
function TableData.GetMedicineDataTable(priority)
    return Game.TableDataManager:GetData("MedicineData", priority)
end

function TableData.Get_moneyExchangeRateTable()
    return Game.TableDataManager:GetAttr("MoneyExchangeData", "moneyExchangeRateTable")
end

---@return DecomposeItemByRefreshTypeLimitData
function TableData.GetDecomposeItemByRefreshTypeLimitDataRow(key, priority)
    return Game.TableDataManager:GetRow("DecomposeItemByRefreshTypeLimitData", key, priority)
end

---@return DecomposeItemByRefreshTypeLimitData[]
function TableData.GetDecomposeItemByRefreshTypeLimitDataTable(priority)
    return Game.TableDataManager:GetData("DecomposeItemByRefreshTypeLimitData", priority)
end

function TableData.Get_DecomposeEquipKeyToID()
    return Game.TableDataManager:GetAttr("DecomposeEquipData", "DecomposeEquipKeyToID")
end

---@return ItemSynthesisReverseData
function TableData.GetItemSynthesisReverseDataRow(key, priority)
    return Game.TableDataManager:GetRow("ItemSynthesisReverseData", key, priority)
end

---@return ItemSynthesisReverseData[]
function TableData.GetItemSynthesisReverseDataTable(priority)
    return Game.TableDataManager:GetData("ItemSynthesisReverseData", priority)
end

function TableData.Get_KeyActionNameToID()
    return Game.TableDataManager:GetAttr("KeyMappingData", "KeyActionNameToID")
end

function TableData.Get_MapSearchableTags()
    return Game.TableDataManager:GetAttr("MapTagData", "MapSearchableTags")
end

function TableData.Get_LinkInsID2TagIDMap()
    return Game.TableDataManager:GetAttr("MapTagData", "LinkInsID2TagIDMap")
end

function TableData.Get_MapIDToTagIDs()
    return Game.TableDataManager:GetAttr("MapTagData", "MapIDToTagIDs")
end

function TableData.Get_PlaneID2MapDataMap()
    return Game.TableDataManager:GetAttr("MapData", "PlaneID2MapDataMap")
end

function TableData.Get_LevelLayerID2MapDataMap()
    return Game.TableDataManager:GetAttr("MapData", "LevelLayerID2MapDataMap")
end

function TableData.Get_PuzzleIDList()
    return Game.TableDataManager:GetAttr("CrossWordPuzzleData", "PuzzleIDList")
end

function TableData.Get_propScaleMap()
    return Game.TableDataManager:GetAttr("MonsterData", "propScaleMap")
end

function TableData.Get_skillId2SkillPos()
    return Game.TableDataManager:GetAttr("MorphData", "skillId2SkillPos")
end

function TableData.Get_whitePlaneMorphMap()
    return Game.TableDataManager:GetAttr("MorphData", "whitePlaneMorphMap")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

function TableData.Get_NickNameLibGroupMap()
    return Game.TableDataManager:GetAttr("NickNameLibData", "NickNameLibGroupMap")
end

function TableData.Get_NicknameRandomPatternMap()
    return Game.TableDataManager:GetAttr("NicknameRandomPatternData", "NicknameRandomPatternMap")
end

function TableData.Get_SpecialNickNameMap()
    return Game.TableDataManager:GetAttr("SpecialNickNameData", "SpecialNickNameMap")
end

function TableData.Get_NpcHideMaxLevel()
    return Game.TableDataManager:GetAttr("NpcHideData", "NpcHideMaxLevel")
end

function TableData.Get_NpcHideMinLevel()
    return Game.TableDataManager:GetAttr("NpcHideData", "NpcHideMinLevel")
end

function TableData.Get_TaskRingIDMark()
    return Game.TableDataManager:GetAttr("NpcHideData", "TaskRingIDMark")
end

function TableData.Get_TaskIDMark()
    return Game.TableDataManager:GetAttr("NpcHideData", "TaskIDMark")
end

function TableData.Get_GossipMap()
    return Game.TableDataManager:GetAttr("NpcInfoData", "GossipMap")
end

function TableData.Get_trapExcelID2WarningUpperBound()
    return Game.TableDataManager:GetAttr("NpcTrapGroupData", "trapExcelID2WarningUpperBound")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

function TableData.Get_propTargetTransMap()
    return Game.TableDataManager:GetAttr("PlayerPropTransData", "propTargetTransMap")
end

function TableData.Get_propSrcTransMap()
    return Game.TableDataManager:GetAttr("PlayerPropTransData", "propSrcTransMap")
end

function TableData.Get_RecapType2IdMap()
    return Game.TableDataManager:GetAttr("PlotRecapData", "RecapType2IdMap")
end

function TableData.Get_parent2childrenMap()
    return Game.TableDataManager:GetAttr("PlotRecapData", "parent2childrenMap")
end

function TableData.Get_AshButtonCaseMap()
    return Game.TableDataManager:GetAttr("QTEAshButtonData", "AshButtonCaseMap")
end

function TableData.Get_RankProvinceIndex()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankProvinceIndex")
end

function TableData.Get_RankIgnoreCities()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankIgnoreCities")
end

function TableData.Get_RankClientCities()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankClientCities")
end

function TableData.Get_RankCountryIndex()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankCountryIndex")
end

function TableData.Get_RankClientProvinces()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankClientProvinces")
end

function TableData.Get_RankIgnoreProvinces()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankIgnoreProvinces")
end

function TableData.Get_RankRegions()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankRegions")
end

function TableData.Get_RankCityIndex()
    return Game.TableDataManager:GetAttr("RankRegionData", "RankCityIndex")
end

function TableData.Get_RANK_PROP()
    return Game.TableDataManager:GetAttr("RankListDisplayData", "RANK_PROP")
end

function TableData.Get_TableRankId2ServerRankId()
    return Game.TableDataManager:GetAttr("RankAllListData", "TableRankId2ServerRankId")
end

function TableData.Get_ServerRankId2TableRankId()
    return Game.TableDataManager:GetAttr("RankAllListData", "ServerRankId2TableRankId")
end

function TableData.Get_MusicID2RankID()
    return Game.TableDataManager:GetAttr("RankAllListData", "MusicID2RankID")
end

function TableData.Get_RankID2musicID()
    return Game.TableDataManager:GetAttr("RankAllListData", "RankID2musicID")
end

function TableData.Get_PrePairClues2NextClue()
    return Game.TableDataManager:GetAttr("ReasoningClueData", "PrePairClues2NextClue")
end

function TableData.Get_Group2ReasoningLayerIds()
    return Game.TableDataManager:GetAttr("ReasoningLayerData", "Group2ReasoningLayerIds")
end

function TableData.Get_RedPointConfigToEnumMap()
    return Game.TableDataManager:GetAttr("RedPointEnumData", "RedPointConfigToEnumMap")
end

function TableData.Get_Source2IdentityLevelList()
    return Game.TableDataManager:GetAttr("RolePlayIdentityData", "Source2IdentityLevelList")
end

function TableData.Get_RolePlayAttr2MaxLevel()
    return Game.TableDataManager:GetAttr("RolePlayPropertyLevelData", "RolePlayAttr2MaxLevel")
end

function TableData.Get_RolePlayToken2Attr()
    return Game.TableDataManager:GetAttr("RolePlayPropertyData", "RolePlayToken2Attr")
end

function TableData.Get_skillId2UniqueIdMap()
    return Game.TableDataManager:GetAttr("RoleSkillUnlockData", "skillId2UniqueIdMap")
end

function TableData.Get_roleClassSkillListMap()
    return Game.TableDataManager:GetAttr("RoleSkillUnlockData", "roleClassSkillListMap")
end

function TableData.Get_newRoleClassSkillListMap()
    return Game.TableDataManager:GetAttr("FellowSkillUnlockData", "newRoleClassSkillListMap")
end

function TableData.Get_newSkillId2UniqueIdMap()
    return Game.TableDataManager:GetAttr("FellowSkillUnlockData", "newSkillId2UniqueIdMap")
end

function TableData.Get_skillLvlUpRowNameByLvl()
    return Game.TableDataManager:GetAttr("SkillLevelUpData", "skillLvlUpRowNameByLvl")
end

function TableData.Get_classInitSkillSchemeMap()
    return Game.TableDataManager:GetAttr("SkillPresetData", "classInitSkillSchemeMap")
end

function TableData.Get_professionSkillPresetList()
    return Game.TableDataManager:GetAttr("SkillPresetData", "professionSkillPresetList")
end

function TableData.Get_professionStateSkillMap()
    return Game.TableDataManager:GetAttr("SwitchSkillGroupData", "professionStateSkillMap")
end

function TableData.Get_professionskillSwitchMap()
    return Game.TableDataManager:GetAttr("SwitchSkillGroupData", "professionskillSwitchMap")
end

---@return SceneCustomListData
function TableData.GetSceneCustomListDataRow(key, priority)
    return Game.TableDataManager:GetRow("SceneCustomListData", key, priority)
end

---@return SceneCustomListData[]
function TableData.GetSceneCustomListDataTable(priority)
    return Game.TableDataManager:GetData("SceneCustomListData", priority)
end

function TableData.Get_ScheduleMaxStageId()
    return Game.TableDataManager:GetAttr("ScheduleStageRewardData", "ScheduleMaxStageId")
end

function TableData.Get_ScheduleMaxStagePoints()
    return Game.TableDataManager:GetAttr("ScheduleStageRewardData", "ScheduleMaxStagePoints")
end

function TableData.Get_ConditionId2TarotTaskId()
    return Game.TableDataManager:GetAttr("ScheduleDailyTaskData", "ConditionId2TarotTaskId")
end

function TableData.Get_ScheduleTaskType2IdMapData()
    return Game.TableDataManager:GetAttr("ScheduleDailyTaskData", "ScheduleTaskType2IdMapData")
end

function TableData.Get_SeasonGroupMap()
    return Game.TableDataManager:GetAttr("SealedInfoData", "SeasonGroupMap")
end

function TableData.Get_GroupCondMap()
    return Game.TableDataManager:GetAttr("SealedInfoAttrData", "GroupCondMap")
end

function TableData.Get_TCPropsCountWeights()
    return Game.TableDataManager:GetAttr("XtraMatTCData", "TCPropsCountWeights")
end

function TableData.Get_GroupToClassMap()
    return Game.TableDataManager:GetAttr("XtraMatRandomClassData", "GroupToClassMap")
end

function TableData.Get_XTraMatFuseWeights()
    return Game.TableDataManager:GetAttr("XtraMatRandomClassData", "XTraMatFuseWeights")
end

function TableData.Get_XTraMatSpecialWordWeights()
    return Game.TableDataManager:GetAttr("XtraMatRandomClassData", "XTraMatSpecialWordWeights")
end

function TableData.Get_XTraMatWordRandomWeights()
    return Game.TableDataManager:GetAttr("XtraMatRandomClassData", "XTraMatWordRandomWeights")
end

function TableData.Get_SpecialWordList()
    return Game.TableDataManager:GetAttr("XtraMatRandomClassData", "SpecialWordList")
end

function TableData.Get_maxVal()
    return Game.TableDataManager:GetAttr("FuseProgressInfoData", "maxVal")
end

function TableData.Get_FuseRewardCollectConfig()
    return Game.TableDataManager:GetAttr("FuseProgressInfoData", "FuseRewardCollectConfig")
end

function TableData.Get_toBigRewardMap()
    return Game.TableDataManager:GetAttr("FuseProgressInfoData", "toBigRewardMap")
end

function TableData.Get_ActRuleMap()
    return Game.TableDataManager:GetAttr("DigestionData", "ActRuleMap")
end

function TableData.Get_ConditionId2DigestionIdMap()
    return Game.TableDataManager:GetAttr("DigestionData", "ConditionId2DigestionIdMap")
end

function TableData.Get_SequenceName2ClassIDMap()
    return Game.TableDataManager:GetAttr("SequenceData", "SequenceName2ClassIDMap")
end

function TableData.Get_ClassIDToActRuleMap()
    return Game.TableDataManager:GetAttr("SequenceData", "ClassIDToActRuleMap")
end

function TableData.Get_ActRuleIDToClassSeqIdMap()
    return Game.TableDataManager:GetAttr("SequenceData", "ActRuleIDToClassSeqIdMap")
end

function TableData.Get_RingID2SequenceIdMap()
    return Game.TableDataManager:GetAttr("SequenceData", "RingID2SequenceIdMap")
end

function TableData.Get_SeqIDIndexMap()
    return Game.TableDataManager:GetAttr("SequenceData", "SeqIDIndexMap")
end

function TableData.Get_DigestionId2SequenceIdMap()
    return Game.TableDataManager:GetAttr("SequenceData", "DigestionId2SequenceIdMap")
end

function TableData.Get_ServerLaunchTime2ServerLevel()
    return Game.TableDataManager:GetAttr("ServerLevelData", "ServerLaunchTime2ServerLevel")
end

function TableData.Get_ServerLaunchLevelMax()
    return Game.TableDataManager:GetAttr("ServerLevelData", "ServerLaunchLevelMax")
end

function TableData.Get_ServerLaunchDayMax()
    return Game.TableDataManager:GetAttr("ServerLevelData", "ServerLaunchDayMax")
end

function TableData.Get_TotalNum()
    return Game.TableDataManager:GetAttr("NpcGoodsData", "TotalNum")
end

---@return ItemIdToGoodIdData
function TableData.GetItemIdToGoodIdDataRow(key, priority)
    return Game.TableDataManager:GetRow("ItemIdToGoodIdData", key, priority)
end

---@return ItemIdToGoodIdData[]
function TableData.GetItemIdToGoodIdDataTable(priority)
    return Game.TableDataManager:GetData("ItemIdToGoodIdData", priority)
end

function TableData.Get_TotalNum()
    return Game.TableDataManager:GetAttr("NpcShopData", "TotalNum")
end

function TableData.Get_parentSkill2SubSkillListNew()
    return Game.TableDataManager:GetAttr("SkillDataNew", "parentSkill2SubSkillListNew")
end

---@return SkillDataEditor
function TableData.GetSkillDataEditorRow(key, priority)
    return Game.TableDataManager:GetRow("SkillDataEditor", key, priority)
end

---@return SkillDataEditor[]
function TableData.GetSkillDataEditorTable(priority)
    return Game.TableDataManager:GetData("SkillDataEditor", priority)
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

function TableData.Get_StallTypeData()
    return Game.TableDataManager:GetAttr("StallData", "StallTypeData")
end

function TableData.Get_StallTabLevelData()
    return Game.TableDataManager:GetAttr("StallTabData", "StallTabLevelData")
end

function TableData.Get_StateID2BlockMap()
    return Game.TableDataManager:GetAttr("StateConflictData", "StateID2BlockMap")
end

function TableData.Get_StateConflictMap()
    return Game.TableDataManager:GetAttr("StateConflictData", "StateConflictMap")
end

function TableData.Get_StateConflictReminderData()
    return Game.TableDataManager:GetAttr("StateConflictData", "StateConflictReminderData")
end

---@return SystemActionNameIDMap
function TableData.GetSystemActionNameIDMapRow(key, priority)
    return Game.TableDataManager:GetRow("SystemActionNameIDMap", key, priority)
end

---@return SystemActionNameIDMap[]
function TableData.GetSystemActionNameIDMapTable(priority)
    return Game.TableDataManager:GetData("SystemActionNameIDMap", priority)
end

function TableData.Get_TrickIDToMarkID()
    return Game.TableDataManager:GetAttr("TargetData", "TrickIDToMarkID")
end

function TableData.Get_TaskActionTypeExecClient()
    return Game.TableDataManager:GetAttr("TaskConstData", "TaskActionTypeExecClient")
end

function TableData.Get_OpenTimeDateMap()
    return Game.TableDataManager:GetAttr("TaskChapterRewardData", "OpenTimeDateMap")
end

function TableData.Get_ShowTimeDateMap()
    return Game.TableDataManager:GetAttr("TaskChapterRewardData", "ShowTimeDateMap")
end

function TableData.Get_EndTimeDateMap()
    return Game.TableDataManager:GetAttr("TaskChapterRewardData", "EndTimeDateMap")
end

function TableData.Get_ConteolQuestMap()
    return Game.TableDataManager:GetAttr("TaskChapterRewardData", "ConteolQuestMap")
end

function TableData.Get_TaskIDMarkForCollect()
    return Game.TableDataManager:GetAttr("TaskCollectHideData", "TaskIDMarkForCollect")
end

function TableData.Get_TaskRingIDMarkForCollect()
    return Game.TableDataManager:GetAttr("TaskCollectHideData", "TaskRingIDMarkForCollect")
end

function TableData.Get_Team33MinRankID()
    return Game.TableDataManager:GetAttr("3V3RankData", "Team33MinRankID")
end

function TableData.Get_Team3v3RankIdToStarMax()
    return Game.TableDataManager:GetAttr("3V3RankData", "Team3v3RankIdToStarMax")
end

function TableData.Get_Max3v3RankPoints()
    return Game.TableDataManager:GetAttr("3V3RankData", "Max3v3RankPoints")
end

function TableData.Get_Team55MinRankID()
    return Game.TableDataManager:GetAttr("5V5RankData", "Team55MinRankID")
end

function TableData.Get_Max5v5RankPoints()
    return Game.TableDataManager:GetAttr("5V5RankData", "Max5v5RankPoints")
end

function TableData.Get_Max12v12RankPoints()
    return Game.TableDataManager:GetAttr("12V12RankData", "Max12v12RankPoints")
end

function TableData.Get_Team12v12RankIdToStarMax()
    return Game.TableDataManager:GetAttr("12V12RankData", "Team12v12RankIdToStarMax")
end

function TableData.Get_Team1212MinRankID()
    return Game.TableDataManager:GetAttr("12V12RankData", "Team1212MinRankID")
end

function TableData.Get_NpcToTeleportMap()
    return Game.TableDataManager:GetAttr("TeleportData", "NpcToTeleportMap")
end

function TableData.Get_MostTitleInfos()
    return Game.TableDataManager:GetAttr("TitleData", "MostTitleInfos")
end

function TableData.Get_MVPTitleInfos()
    return Game.TableDataManager:GetAttr("TitleData", "MVPTitleInfos")
end

function TableData.Get_GameModeTitleInfos()
    return Game.TableDataManager:GetAttr("TitleData", "GameModeTitleInfos")
end

function TableData.Get_TopupData()
    return Game.TableDataManager:GetAttr("TopupData", "TopupData")
end

function TableData.Get_buffLvlRelatedDataNew()
    return Game.TableDataManager:GetAttr("BuffDataNew", "buffLvlRelatedDataNew")
end

function TableData.Get_buff2GroupInfo()
    return Game.TableDataManager:GetAttr("BuffGroupData", "buff2GroupInfo")
end

---@return TriggerCustomData
function TableData.GetTriggerCustomDataRow(key, priority)
    return Game.TableDataManager:GetRow("TriggerCustomData", key, priority)
end

---@return TriggerCustomData[]
function TableData.GetTriggerCustomDataTable(priority)
    return Game.TableDataManager:GetData("TriggerCustomData", priority)
end

function TableData.Get_TriggerCustomTargetDict()
    return Game.TableDataManager:GetAttr("TriggerCustomData", "TriggerCustomTargetDict")
end

---@return TriggerTypeSpecialData
function TableData.GetTriggerTypeSpecialDataRow(key, priority)
    return Game.TableDataManager:GetRow("TriggerTypeSpecialData", key, priority)
end

---@return TriggerTypeSpecialData[]
function TableData.GetTriggerTypeSpecialDataTable(priority)
    return Game.TableDataManager:GetData("TriggerTypeSpecialData", priority)
end

function TableData.Get_CustomID2WorldActivityID()
    return Game.TableDataManager:GetAttr("WorldActivityData", "CustomID2WorldActivityID")
end

return TableData
