--
-- 表名: TaskConstData后处理
--

local TopData = {
    TaskActionTypeExecClient = {
        [9] = true, 
        [14] = true, 
        [16] = true, 
        [18] = true, 
        [19] = true, 
        [23] = true, 
        [26] = true, 
        [36] = true, 
        [37] = true, 
        [38] = true, 
        [39] = true, 
        [40] = true, 
        [41] = true, 
        [42] = true, 
        [43] = true, 
        [47] = true, 
        [48] = true, 
        [52] = true, 
        [53] = true, 
        [54] = true, 
        [59] = true, 
        [60] = true, 
        [66] = true, 
        [67] = true, 
        [68] = true, 
        [69] = true, 
        [74] = true, 
        [81] = true, 
        [85] = true, 
        [102] = true, 
        [103] = true, 
        [108] = true, 
        [109] = true, 
        [110] = true, 
        [111] = true, 
        [112] = true, 
        [113] = true, 
        [114] = true, 
        [119] = true, 
        [144] = true, 
        [171] = true, 
        [172] = true, 
        [173] = true, 
        [174] = true, 
    },
    data = {
        [1] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__AUTO', 
            ['Const'] = 'AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959978496'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 1, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [2] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__NPC_TALK', 
            ['Const'] = 'NPC_TALK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959978752'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 2, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [3] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__MANUAL', 
            ['Const'] = 'MANUAL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959979008'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 3, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [4] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__ITEM', 
            ['Const'] = 'ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959979264'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 4, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [5] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__QUEST_JUMP', 
            ['Const'] = 'QUEST_JUMP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959979520'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 5, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [6] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'RECEIVE_TYPE__ENTER_SPACE', 
            ['Const'] = 'ENTER_SPACE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959979776'),
            ['Group'] = 'RECEIVE_TYPE', 
            ['ID'] = 6, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [7] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_RECEIVED', 
            ['Const'] = 'TASK_RECEIVED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959980032'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 7, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [8] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_COMPLETED', 
            ['Const'] = 'TASK_COMPLETED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959980288'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 8, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [9] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_FINISHED', 
            ['Const'] = 'TASK_FINISHED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959980544'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 9, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [10] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_GIVEN_UP', 
            ['Const'] = 'TASK_GIVEN_UP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959980800'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 10, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [11] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_FAILED', 
            ['Const'] = 'TASK_FAILED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959981056'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 11, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [12] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__TASK_TARGET_COMPLETED', 
            ['Const'] = 'TASK_TARGET_COMPLETED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959981312'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 12, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [13] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__RING_REVEIVED', 
            ['Const'] = 'RING_REVEIVED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959981568'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 13, 
            ['ShowCount'] = false, 
            ['Value'] = 7, 
        },
        [14] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_ON_TYPE__RING_COMPLETED', 
            ['Const'] = 'RING_COMPLETED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959981824'),
            ['Group'] = 'TRIGGER_ON_TYPE', 
            ['ID'] = 14, 
            ['ShowCount'] = false, 
            ['Value'] = 8, 
        },
        [15] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_MONSTER', 
            ['Const'] = 'SPAWN_MONSTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959982080'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 15, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [16] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_NPC', 
            ['Const'] = 'SPAWN_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959982336'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 16, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [17] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRIGGER_UPDATE', 
            ['Const'] = 'TRIGGER_UPDATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959982592'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 17, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [18] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_MINE', 
            ['Const'] = 'SPAWN_MINE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959982848'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 18, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [19] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_PLANE_ENTRANCE', 
            ['Const'] = 'SPAWN_PLANE_ENTRANCE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959983104'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 19, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [20] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_PLANE_EXIT', 
            ['Const'] = 'SPAWN_PLANE_EXIT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959983360'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 20, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [21] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLANE_ENTER', 
            ['Const'] = 'PLANE_ENTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959983616'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 21, 
            ['ShowCount'] = false, 
            ['Value'] = 7, 
        },
        [22] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLANE_LEAVE', 
            ['Const'] = 'PLANE_LEAVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959983872'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 22, 
            ['ShowCount'] = false, 
            ['Value'] = 8, 
        },
        [23] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_DIALOGUE', 
            ['Const'] = 'PLAY_DIALOGUE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959984128'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 23, 
            ['ShowCount'] = false, 
            ['Value'] = 9, 
        },
        [24] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TELEPORT', 
            ['Const'] = 'TELEPORT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959984384'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 24, 
            ['ShowCount'] = false, 
            ['Value'] = 10, 
        },
        [25] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__FUNCTION_UNLOCKED', 
            ['Const'] = 'FUNCTION_UNLOCKED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959984640'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 25, 
            ['ShowCount'] = false, 
            ['Value'] = 11, 
        },
        [26] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_TASK_ITEM', 
            ['Const'] = 'SEND_TASK_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959984896'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 26, 
            ['ShowCount'] = false, 
            ['Value'] = 12, 
        },
        [27] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_DESTROY', 
            ['Const'] = 'SPAWN_DESTROY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985152'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 27, 
            ['ShowCount'] = false, 
            ['Value'] = 13, 
        },
        [28] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CUTSCENE', 
            ['Const'] = 'PLAY_CUTSCENE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985408'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 28, 
            ['ShowCount'] = false, 
            ['Value'] = 14, 
        },
        [29] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ADD_BUFF', 
            ['Const'] = 'ADD_BUFF', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985664'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 29, 
            ['ShowCount'] = false, 
            ['Value'] = 15, 
        },
        [30] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_ASIDETALK', 
            ['Const'] = 'PLAY_ASIDETALK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985920'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 30, 
            ['ShowCount'] = false, 
            ['Value'] = 16, 
        },
        [31] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_BUFF', 
            ['Const'] = 'REMOVE_BUFF', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959986176'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 31, 
            ['ShowCount'] = false, 
            ['Value'] = 17, 
        },
        [32] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_LOOKATLOC', 
            ['Const'] = 'CAMERA_LOOKATLOC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959986432'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 32, 
            ['ShowCount'] = false, 
            ['Value'] = 18, 
        },
        [33] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_LOOKATINS', 
            ['Const'] = 'CAMERA_LOOKATINS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959986688'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 33, 
            ['ShowCount'] = false, 
            ['Value'] = 19, 
        },
        [34] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__EDIT_PLANE_CLIMATE', 
            ['Const'] = 'EDIT_PLANE_CLIMATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959986944'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 34, 
            ['ShowCount'] = false, 
            ['Value'] = 20, 
        },
        [35] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MORPH_START', 
            ['Const'] = 'MORPH_START', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987200'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 35, 
            ['ShowCount'] = false, 
            ['Value'] = 21, 
        },
        [36] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MORPH_STOP', 
            ['Const'] = 'MORPH_STOP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987456'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 36, 
            ['ShowCount'] = false, 
            ['Value'] = 22, 
        },
        [37] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__JUMP_UI', 
            ['Const'] = 'JUMP_UI', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987712'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 37, 
            ['ShowCount'] = false, 
            ['Value'] = 23, 
        },
        [38] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_COLLECT', 
            ['Const'] = 'SPAWN_COLLECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987968'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 38, 
            ['ShowCount'] = false, 
            ['Value'] = 24, 
        },
        [39] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DESTORY_COLLECT', 
            ['Const'] = 'DESTORY_COLLECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988224'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 39, 
            ['ShowCount'] = false, 
            ['Value'] = 25, 
        },
        [40] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_STARTTASK', 
            ['Const'] = 'PLAY_STARTTASK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988480'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 40, 
            ['ShowCount'] = false, 
            ['Value'] = 26, 
        },
        [42] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__WAIT_TIME', 
            ['Const'] = 'WAIT_TIME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988736'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 42, 
            ['ShowCount'] = false, 
            ['Value'] = 28, 
        },
        [43] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_POS', 
            ['Const'] = 'TRANSMIT_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988992'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 43, 
            ['ShowCount'] = false, 
            ['Value'] = 29, 
        },
        [44] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_TRIGGER', 
            ['Const'] = 'TRANSMIT_TRIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959989248'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 44, 
            ['ShowCount'] = false, 
            ['Value'] = 30, 
        },
        [45] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MOVE_TO_POS', 
            ['Const'] = 'MOVE_TO_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959989504'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 45, 
            ['ShowCount'] = false, 
            ['Value'] = 31, 
        },
        [46] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MOVE_TO_TRIGGER', 
            ['Const'] = 'MOVE_TO_TRIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959989760'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 46, 
            ['ShowCount'] = false, 
            ['Value'] = 32, 
        },
        [47] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_ANG', 
            ['Const'] = 'ROTATE_TO_ANG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990016'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 47, 
            ['ShowCount'] = false, 
            ['Value'] = 33, 
        },
        [48] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_ENTITY', 
            ['Const'] = 'ROTATE_TO_ENTITY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990272'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 48, 
            ['ShowCount'] = false, 
            ['Value'] = 34, 
        },
        [49] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_LOC', 
            ['Const'] = 'ROTATE_TO_LOC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990528'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 49, 
            ['ShowCount'] = false, 
            ['Value'] = 35, 
        },
        [50] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_TO_SPAWNER', 
            ['Const'] = 'GAZE_TO_SPAWNER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990784'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 50, 
            ['ShowCount'] = false, 
            ['Value'] = 36, 
        },
        [51] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_TO_LOC', 
            ['Const'] = 'GAZE_TO_LOC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991040'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 51, 
            ['ShowCount'] = false, 
            ['Value'] = 37, 
        },
        [52] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_BACK', 
            ['Const'] = 'GAZE_BACK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991296'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 52, 
            ['ShowCount'] = false, 
            ['Value'] = 38, 
        },
        [53] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_ANIMATION', 
            ['Const'] = 'PLAY_ANIMATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991552'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 53, 
            ['ShowCount'] = false, 
            ['Value'] = 39, 
        },
        [54] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ENTER_QUEST_CONTROL', 
            ['Const'] = 'ENTER_QUEST_CONTROL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991808'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 54, 
            ['ShowCount'] = false, 
            ['Value'] = 40, 
        },
        [55] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__LEAVE_QUEST_CONTROL', 
            ['Const'] = 'LEAVE_QUEST_CONTROL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959992064'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 55, 
            ['ShowCount'] = false, 
            ['Value'] = 41, 
        },
        [56] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_BUBBLE', 
            ['Const'] = 'PLAY_BUBBLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959992320'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 56, 
            ['ShowCount'] = false, 
            ['Value'] = 42, 
        },
        [57] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_BLACKBG', 
            ['Const'] = 'PLAY_BLACKBG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959992576'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 57, 
            ['ShowCount'] = false, 
            ['Value'] = 43, 
        },
        [58] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__FLOW_CHART', 
            ['Const'] = 'FLOW_CHART', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959992832'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 58, 
            ['ShowCount'] = false, 
            ['Value'] = 44, 
        },
        [59] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_BEHAVIOR_TREE', 
            ['Const'] = 'START_BEHAVIOR_TREE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959993088'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 59, 
            ['ShowCount'] = false, 
            ['Value'] = 45, 
        },
        [60] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_BEHAVIOR_TREE', 
            ['Const'] = 'STOP_BEHAVIOR_TREE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959993344'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 60, 
            ['ShowCount'] = false, 
            ['Value'] = 46, 
        },
        [61] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_MUSIC_SOUND', 
            ['Const'] = 'PLAY_MUSIC_SOUND', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959993600'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 61, 
            ['ShowCount'] = false, 
            ['Value'] = 47, 
        },
        [62] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_MUSIC_SOUND', 
            ['Const'] = 'STOP_MUSIC_SOUND', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959993856'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 62, 
            ['ShowCount'] = false, 
            ['Value'] = 48, 
        },
        [63] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_SCENE_TIME', 
            ['Const'] = 'CHANGE_SCENE_TIME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959994112'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 63, 
            ['ShowCount'] = false, 
            ['Value'] = 49, 
        },
        [64] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_MOVING_PATH', 
            ['Const'] = 'START_MOVING_PATH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959994368'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 64, 
            ['ShowCount'] = false, 
            ['Value'] = 50, 
        },
        [65] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_MOVING', 
            ['Const'] = 'STOP_MOVING', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959994624'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 65, 
            ['ShowCount'] = false, 
            ['Value'] = 51, 
        },
        [66] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_CROSS_WORD_PUZZLE', 
            ['Const'] = 'START_CROSS_WORD_PUZZLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959994880'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 66, 
            ['ShowCount'] = false, 
            ['Value'] = 52, 
        },
        [67] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_LETTER', 
            ['Const'] = 'START_LETTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995136'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 67, 
            ['ShowCount'] = false, 
            ['Value'] = 53, 
        },
        [68] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_JIGSAW_PUZZLE', 
            ['Const'] = 'START_JIGSAW_PUZZLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995392'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 68, 
            ['ShowCount'] = false, 
            ['Value'] = 54, 
        },
        [69] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__NPC_TO_MONSTER', 
            ['Const'] = 'NPC_TO_MONSTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995648'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 69, 
            ['ShowCount'] = false, 
            ['Value'] = 55, 
        },
        [70] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__KILL_NPC', 
            ['Const'] = 'KILL_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995904'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 70, 
            ['ShowCount'] = false, 
            ['Value'] = 56, 
        },
        [71] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE', 
            ['Const'] = 'CHANGE_COLLECT_STATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996160'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 71, 
            ['ShowCount'] = false, 
            ['Value'] = 57, 
        },
        [72] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MONSTER_BACK_TO_NPC', 
            ['Const'] = 'MONSTER_BACK_TO_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996416'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 72, 
            ['ShowCount'] = false, 
            ['Value'] = 58, 
        },
        [73] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CHAPTERSTART', 
            ['Const'] = 'PLAY_CHAPTERSTART', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996672'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 73, 
            ['ShowCount'] = false, 
            ['Value'] = 59, 
        },
        [74] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CHAPTEREND', 
            ['Const'] = 'PLAY_CHAPTEREND', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996928'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 74, 
            ['ShowCount'] = false, 
            ['Value'] = 60, 
        },
        [75] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_SPIRITUALVISION', 
            ['Const'] = 'START_SPIRITUALVISION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607174912'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 75, 
            ['ShowCount'] = false, 
            ['Value'] = 61, 
        },
        [76] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_CUSTOM_EVENT', 
            ['Const'] = 'SEND_CUSTOM_EVENT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959997440'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 76, 
            ['ShowCount'] = false, 
            ['Value'] = 62, 
        },
        [77] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_MINE_SINGLE', 
            ['Const'] = 'SPAWN_MINE_SINGLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959997696'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 77, 
            ['ShowCount'] = false, 
            ['Value'] = 63, 
        },
        [78] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_TASK_ITEM_SINGLE', 
            ['Const'] = 'SEND_TASK_ITEM_SINGLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959997952'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 78, 
            ['ShowCount'] = false, 
            ['Value'] = 64, 
        },
        [79] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SPAWN_MINE_POS', 
            ['Const'] = 'SPAWN_MINE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959998208'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 79, 
            ['ShowCount'] = false, 
            ['Value'] = 65, 
        },
        [80] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__QTE_ASHBUTTON', 
            ['Const'] = 'QTE_ASHBUTTON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959998464'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 80, 
            ['ShowCount'] = false, 
            ['Value'] = 66, 
        },
        [81] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__QTE_SLIDE', 
            ['Const'] = 'QTE_SLIDE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959998720'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 81, 
            ['ShowCount'] = false, 
            ['Value'] = 67, 
        },
        [82] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__QTE_BUTTON', 
            ['Const'] = 'QTE_BUTTON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959998976'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 82, 
            ['ShowCount'] = false, 
            ['Value'] = 68, 
        },
        [83] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MOVE_CAMERA', 
            ['Const'] = 'MOVE_CAMERA', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959999232'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 83, 
            ['ShowCount'] = false, 
            ['Value'] = 69, 
        },
        [84] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__FOG', 
            ['Const'] = 'FOG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959999488'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 84, 
            ['ShowCount'] = false, 
            ['Value'] = 70, 
        },
        [85] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_AI_MESSAGE', 
            ['Const'] = 'SEND_AI_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959999744'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 85, 
            ['ShowCount'] = false, 
            ['Value'] = 71, 
        },
        [86] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_AI_AND_SPACE_MESSAGE', 
            ['Const'] = 'SEND_AI_AND_SPACE_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000000'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 86, 
            ['ShowCount'] = false, 
            ['Value'] = 72, 
        },
        [87] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_SPACE_MESSAGE', 
            ['Const'] = 'SEND_SPACE_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000256'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 87, 
            ['ShowCount'] = false, 
            ['Value'] = 73, 
        },
        [88] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__END_CAMERA', 
            ['Const'] = 'END_CAMERA', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000512'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 88, 
            ['ShowCount'] = false, 
            ['Value'] = 74, 
        },
        [89] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_SCENE_EFFECT', 
            ['Const'] = 'PLAY_SCENE_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000768'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 89, 
            ['ShowCount'] = false, 
            ['Value'] = 75, 
        },
        [90] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_SCENE_EFFECT', 
            ['Const'] = 'REMOVE_SCENE_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001024'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 90, 
            ['ShowCount'] = false, 
            ['Value'] = 76, 
        },
        [91] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_ENTITY_EFFECT', 
            ['Const'] = 'PLAY_ENTITY_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001280'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 91, 
            ['ShowCount'] = false, 
            ['Value'] = 77, 
        },
        [92] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_ENTITY_EFFECT', 
            ['Const'] = 'REMOVE_ENTITY_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001536'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 92, 
            ['ShowCount'] = false, 
            ['Value'] = 78, 
        },
        [93] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CAMERA_POST_PROCESS_EFFECT', 
            ['Const'] = 'PLAY_CAMERA_POST_PROCESS_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001792'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 93, 
            ['ShowCount'] = false, 
            ['Value'] = 79, 
        },
        [94] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_CAMERA_POST_PROCESS_EFFECT', 
            ['Const'] = 'REMOVE_CAMERA_POST_PROCESS_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002048'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 94, 
            ['ShowCount'] = false, 
            ['Value'] = 80, 
        },
        [95] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_ANIMATION', 
            ['Const'] = 'STOP_ANIMATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002304'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 95, 
            ['ShowCount'] = false, 
            ['Value'] = 81, 
        },
        [96] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SET_TASK_MARK_DEFINE', 
            ['Const'] = 'SET_TASK_MARK_DEFINE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002560'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 96, 
            ['ShowCount'] = false, 
            ['Value'] = 82, 
        },
        [97] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CLEAR_TASK_MARK_DEFINE', 
            ['Const'] = 'CLEAR_TASK_MARK_DEFINE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002816'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 97, 
            ['ShowCount'] = false, 
            ['Value'] = 83, 
        },
        [98] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE', 
            ['Const'] = 'TRANSMIT_SAME_SCENE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960003072'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 98, 
            ['ShowCount'] = false, 
            ['Value'] = 84, 
        },
        [99] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__OPEN_STAR_MISTERY', 
            ['Const'] = 'OPEN_STAR_MISTERY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960003328'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 99, 
            ['ShowCount'] = false, 
            ['Value'] = 85, 
        },
        [100] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_AUDIO', 
            ['Const'] = 'PLAY_AUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960003584'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 100, 
            ['ShowCount'] = false, 
            ['Value'] = 86, 
        },
        [101] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_AUDIO', 
            ['Const'] = 'STOP_AUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960003840'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 101, 
            ['ShowCount'] = false, 
            ['Value'] = 87, 
        },
        [102] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_MINE_STATE', 
            ['Const'] = 'CHANGE_MINE_STATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960004096'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 102, 
            ['ShowCount'] = false, 
            ['Value'] = 88, 
        },
        [103] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__MONSTER_KILL', 
            ['Const'] = 'MONSTER_KILL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960004352'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 103, 
            ['ShowCount'] = true, 
            ['Value'] = 1, 
        },
        [104] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__MONSTER_KILL_COUNT', 
            ['Const'] = 'MONSTER_KILL_COUNT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960004352'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 104, 
            ['ShowCount'] = true, 
            ['Value'] = 2, 
        },
        [105] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPC_TALK', 
            ['Const'] = 'NPC_TALK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960004864'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 105, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [106] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__LOCATION', 
            ['Const'] = 'LOCATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960005120'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 106, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [107] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ESCORT', 
            ['Const'] = 'ESCORT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960005376'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 107, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [108] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__DUNGEON_COMPLETE', 
            ['Const'] = 'DUNGEON_COMPLETE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960005632'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 108, 
            ['ShowCount'] = false, 
            ['Value'] = 7, 
        },
        [109] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__LEVELUP', 
            ['Const'] = 'LEVELUP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960005888'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 109, 
            ['ShowCount'] = false, 
            ['Value'] = 8, 
        },
        [110] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__INTERACTION', 
            ['Const'] = 'INTERACTION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960006144'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 110, 
            ['ShowCount'] = true, 
            ['Value'] = 9, 
        },
        [111] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ITEM_IN_BAG', 
            ['Const'] = 'ITEM_IN_BAG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960006400'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 111, 
            ['ShowCount'] = false, 
            ['Value'] = 10, 
        },
        [112] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SUBMIT_ITEM', 
            ['Const'] = 'SUBMIT_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960006656'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 112, 
            ['ShowCount'] = false, 
            ['Value'] = 11, 
        },
        [113] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__USE_ITEM_AT_LOCATION', 
            ['Const'] = 'USE_ITEM_AT_LOCATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960006912'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 113, 
            ['ShowCount'] = true, 
            ['Value'] = 12, 
        },
        [114] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SHOP_BUY', 
            ['Const'] = 'SHOP_BUY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960007168'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 114, 
            ['ShowCount'] = false, 
            ['Value'] = 13, 
        },
        [115] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__WAIT_TIME', 
            ['Const'] = 'WAIT_TIME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988736'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 115, 
            ['ShowCount'] = false, 
            ['Value'] = 14, 
        },
        [116] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__INTERACTION_PLANE', 
            ['Const'] = 'INTERACTION_PLANE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960007680'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 116, 
            ['ShowCount'] = false, 
            ['Value'] = 15, 
        },
        [117] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_RING', 
            ['Const'] = 'FINISH_RING', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960007936'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 117, 
            ['ShowCount'] = false, 
            ['Value'] = 16, 
        },
        [118] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CHAT', 
            ['Const'] = 'CHAT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960008192'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 118, 
            ['ShowCount'] = false, 
            ['Value'] = 17, 
        },
        [119] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__DUNGEON_COMPLETE_WITH_GUILD', 
            ['Const'] = 'DUNGEON_COMPLETE_WITH_GUILD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960008448'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 119, 
            ['ShowCount'] = false, 
            ['Value'] = 18, 
        },
        [120] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__TALK_ENTER_PLANE', 
            ['Const'] = 'TALK_ENTER_PLANE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960008704'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 120, 
            ['ShowCount'] = false, 
            ['Value'] = 19, 
        },
        [121] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__PLAY_DIALOGUE', 
            ['Const'] = 'PLAY_DIALOGUE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960008960'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 121, 
            ['ShowCount'] = false, 
            ['Value'] = 20, 
        },
        [122] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__PLAY_CUTSCENE', 
            ['Const'] = 'PLAY_CUTSCENE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985408'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 122, 
            ['ShowCount'] = false, 
            ['Value'] = 21, 
        },
        [123] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CUT_MONSTER_HP', 
            ['Const'] = 'CUT_MONSTER_HP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960009472'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 123, 
            ['ShowCount'] = false, 
            ['Value'] = 22, 
        },
        [124] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__GOTO_LEVELMAP', 
            ['Const'] = 'GOTO_LEVELMAP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960009728'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 124, 
            ['ShowCount'] = false, 
            ['Value'] = 23, 
        },
        [125] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__EQUIP_EQUIPMENT', 
            ['Const'] = 'EQUIP_EQUIPMENT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960009984'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 125, 
            ['ShowCount'] = false, 
            ['Value'] = 24, 
        },
        [126] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_GUILD_MATERIAL_TASK', 
            ['Const'] = 'FINISH_GUILD_MATERIAL_TASK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960010240'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 126, 
            ['ShowCount'] = false, 
            ['Value'] = 25, 
        },
        [127] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__COLLECT_ITEMS', 
            ['Const'] = 'COLLECT_ITEMS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960010496'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 127, 
            ['ShowCount'] = false, 
            ['Value'] = 26, 
        },
        [128] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ITEM_TO_BAG', 
            ['Const'] = 'ITEM_TO_BAG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960010752'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 128, 
            ['ShowCount'] = false, 
            ['Value'] = 27, 
        },
        [129] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FIND_CORRECT_NPC', 
            ['Const'] = 'FIND_CORRECT_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960011008'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 129, 
            ['ShowCount'] = false, 
            ['Value'] = 28, 
        },
        [130] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CUSTOM_EVENT', 
            ['Const'] = 'CUSTOM_EVENT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960011264'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 130, 
            ['ShowCount'] = true, 
            ['Value'] = 29, 
        },
        [131] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL', 
            ['Const'] = 'CAST_SKILL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_54495618796032'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 131, 
            ['ShowCount'] = true, 
            ['Value'] = 30, 
        },
        [132] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_QTE', 
            ['Const'] = 'FINISH_QTE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960011776'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 132, 
            ['ShowCount'] = false, 
            ['Value'] = 31, 
        },
        [133] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__START_SPIRITUALVISION', 
            ['Const'] = 'START_SPIRITUALVISION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607174912'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 133, 
            ['ShowCount'] = false, 
            ['Value'] = 32, 
        },
        [134] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CLOSE_LETTER', 
            ['Const'] = 'CLOSE_LETTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960012288'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 134, 
            ['ShowCount'] = false, 
            ['Value'] = 33, 
        },
        [135] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SIT_DOWN_CHAIR', 
            ['Const'] = 'SIT_DOWN_CHAIR', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960012544'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 135, 
            ['ShowCount'] = false, 
            ['Value'] = 34, 
        },
        [136] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__STAR_MISTERY_SUCC', 
            ['Const'] = 'STAR_MISTERY_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960012800'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 136, 
            ['ShowCount'] = false, 
            ['Value'] = 35, 
        },
        [137] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__OPEN_UI_JUMP', 
            ['Const'] = 'OPEN_UI_JUMP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987712'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 137, 
            ['ShowCount'] = false, 
            ['Value'] = 36, 
        },
        [138] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__JIGSAW_PUZZLE_SUCC', 
            ['Const'] = 'JIGSAW_PUZZLE_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960013312'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 138, 
            ['ShowCount'] = false, 
            ['Value'] = 37, 
        },
        [139] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SHAPE_TRIGGER_LOCATION', 
            ['Const'] = 'SHAPE_TRIGGER_LOCATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960013568'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 139, 
            ['ShowCount'] = false, 
            ['Value'] = 38, 
        },
        [140] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPC_ASK_PRICE_SUCC', 
            ['Const'] = 'NPC_ASK_PRICE_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960013824'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 140, 
            ['ShowCount'] = false, 
            ['Value'] = 39, 
        },
        [141] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SEQUENCE_SAN_CHECK_SUCC', 
            ['Const'] = 'SEQUENCE_SAN_CHECK_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960014080'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 141, 
            ['ShowCount'] = false, 
            ['Value'] = 40, 
        },
        [142] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SERVER_ENTER_PLANE', 
            ['Const'] = 'SERVER_ENTER_PLANE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960014336'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 142, 
            ['ShowCount'] = false, 
            ['Value'] = 999, 
        },
        [143] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FINISH_TYPE__AUTO', 
            ['Const'] = 'AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960014592'),
            ['Group'] = 'FINISH_TYPE', 
            ['ID'] = 143, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [144] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FINISH_TYPE__NPC_TALK', 
            ['Const'] = 'NPC_TALK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960014848'),
            ['Group'] = 'FINISH_TYPE', 
            ['ID'] = 144, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [145] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FINISH_TYPE__MANUAL', 
            ['Const'] = 'MANUAL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960015104'),
            ['Group'] = 'FINISH_TYPE', 
            ['ID'] = 145, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [146] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__MAIN', 
            ['Const'] = 'MAIN', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_54632789640960'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 146, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [147] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__SUBTASK', 
            ['Const'] = 'SUBTASK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_54632789641216'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 147, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [148] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__ROLE_PLAY', 
            ['Const'] = 'ROLE_PLAY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960015872'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 148, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [149] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__DUNGEON', 
            ['Const'] = 'DUNGEON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960016128'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 149, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [150] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__REPEAT', 
            ['Const'] = 'REPEAT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960016384'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 150, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [151] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__GUILDSINGLE', 
            ['Const'] = 'GUILDSINGLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960016640'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 151, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [152] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__ROLEPLAYSHERIFFTASK', 
            ['Const'] = 'ROLEPLAYSHERIFFTASK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960016896'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 152, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [153] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_MINITYPE__SYSTEM_TASK', 
            ['Const'] = 'SYSTEM_TASK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960017152'),
            ['Group'] = 'TASK_MINITYPE', 
            ['ID'] = 153, 
            ['ShowCount'] = false, 
            ['Value'] = 7, 
        },
        [154] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_TYPE__UNREPEATABLE', 
            ['Const'] = 'UNREPEATABLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960017408'),
            ['Group'] = 'TASK_TYPE', 
            ['ID'] = 154, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [155] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_TYPE__REPEATABLE', 
            ['Const'] = 'REPEATABLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960016384'),
            ['Group'] = 'TASK_TYPE', 
            ['ID'] = 155, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [156] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_TYPE__REPEATABLE_WEEK', 
            ['Const'] = 'REPEATABLE_WEEK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960017920'),
            ['Group'] = 'TASK_TYPE', 
            ['ID'] = 156, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [157] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__AVAILABLE', 
            ['Const'] = 'AVAILABLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960018176'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 157, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [158] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__ACCEPTED', 
            ['Const'] = 'ACCEPTED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960018432'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 158, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [159] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__ACCOMPLISHED', 
            ['Const'] = 'ACCOMPLISHED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_54632789352960'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 159, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [160] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__FAILED', 
            ['Const'] = 'FAILED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960018944'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 160, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [161] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__ABANDONED', 
            ['Const'] = 'ABANDONED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960019200'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 161, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [162] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__FINISHED', 
            ['Const'] = 'FINISHED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960019456'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 162, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [163] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_STATUS__FINISH_ACTION', 
            ['Const'] = 'FINISH_ACTION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960019712'),
            ['Group'] = 'TASK_STATUS', 
            ['ID'] = 163, 
            ['ShowCount'] = false, 
            ['Value'] = 7, 
        },
        [164] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__PLAYER_DEAD', 
            ['Const'] = 'PLAYER_DEAD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960019968'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 164, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [165] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__NPC_DEAD', 
            ['Const'] = 'NPC_DEAD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020224'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 165, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [166] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__ALL_PLAYER_DEACTIVE', 
            ['Const'] = 'ALL_PLAYER_DEACTIVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020480'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 166, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [167] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__QUEST_TIMEOUT', 
            ['Const'] = 'QUEST_TIMEOUT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020736'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 167, 
            ['ShowCount'] = false, 
            ['Value'] = 4, 
        },
        [168] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__PLAYER_LEAVE', 
            ['Const'] = 'PLAYER_LEAVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020992'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 168, 
            ['ShowCount'] = false, 
            ['Value'] = 5, 
        },
        [169] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TASK_FAIL_TYPE__QTE_FAILED', 
            ['Const'] = 'QTE_FAILED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960021248'),
            ['Group'] = 'TASK_FAIL_TYPE', 
            ['ID'] = 169, 
            ['ShowCount'] = false, 
            ['Value'] = 6, 
        },
        [170] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_ENTER_TYPE__AUTO', 
            ['Const'] = 'AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960021504'),
            ['Group'] = 'PLANE_ENTER_TYPE', 
            ['ID'] = 170, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [171] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_ENTER_TYPE__INTERACTOR', 
            ['Const'] = 'INTERACTOR', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_25839328569856'),
            ['Group'] = 'PLANE_ENTER_TYPE', 
            ['ID'] = 171, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [172] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_EXIT_TYPE__AUTO', 
            ['Const'] = 'AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960022016'),
            ['Group'] = 'PLANE_EXIT_TYPE', 
            ['ID'] = 172, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [173] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_EXIT_TYPE__TREIGGER', 
            ['Const'] = 'TREIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960022272'),
            ['Group'] = 'PLANE_EXIT_TYPE', 
            ['ID'] = 173, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [174] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_EXIT_TYPE__POSITION', 
            ['Const'] = 'POSITION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960022528'),
            ['Group'] = 'PLANE_EXIT_TYPE', 
            ['ID'] = 174, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [175] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'PLANE_EXIT_TYPE__ENTER_PLANE_POS', 
            ['Const'] = 'ENTER_PLANE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960022784'),
            ['Group'] = 'PLANE_EXIT_TYPE', 
            ['ID'] = 175, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [176] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FAIL_ACTION__RESET_QUEST_AUTO', 
            ['Const'] = 'RESET_QUEST_AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960023040'),
            ['Group'] = 'FAIL_ACTION', 
            ['ID'] = 176, 
            ['ShowCount'] = false, 
            ['Value'] = 0, 
        },
        [177] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FAIL_ACTION__RESET_QUEST_MANUAL', 
            ['Const'] = 'RESET_QUEST_MANUAL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960023296'),
            ['Group'] = 'FAIL_ACTION', 
            ['ID'] = 177, 
            ['ShowCount'] = false, 
            ['Value'] = 1, 
        },
        [178] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FAIL_ACTION__FINISH_QUEST_AUTO', 
            ['Const'] = 'FINISH_QUEST_AUTO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960023552'),
            ['Group'] = 'FAIL_ACTION', 
            ['ID'] = 178, 
            ['ShowCount'] = false, 
            ['Value'] = 2, 
        },
        [179] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'FAIL_ACTION__FINISH_QUEST_MANUAL', 
            ['Const'] = 'FINISH_QUEST_MANUAL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960023808'),
            ['Group'] = 'FAIL_ACTION', 
            ['ID'] = 179, 
            ['ShowCount'] = false, 
            ['Value'] = 3, 
        },
        [180] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_ANIMATION_BY_INSTANCEID', 
            ['Const'] = 'PLAY_ANIMATION_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960024064'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 180, 
            ['ShowCount'] = false, 
            ['Value'] = 101, 
        },
        [181] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_BLACK_BG', 
            ['Const'] = 'PLAY_BLACK_BG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959992576'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 181, 
            ['ShowCount'] = false, 
            ['Value'] = 102, 
        },
        [182] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_ASIDE_TALK', 
            ['Const'] = 'PLAY_ASIDE_TALK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959985920'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 182, 
            ['ShowCount'] = false, 
            ['Value'] = 103, 
        },
        [183] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_POS', 
            ['Const'] = 'PLAYER_TRANSMIT_OTHER_SCENE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960024832'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 183, 
            ['ShowCount'] = false, 
            ['Value'] = 104, 
        },
        [184] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_POS', 
            ['Const'] = 'TRANSMIT_SAME_SCENE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960025088'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 184, 
            ['ShowCount'] = false, 
            ['Value'] = 105, 
        },
        [185] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAYER_TRANSMIT_OTHER_SCENE_TRRIGER', 
            ['Const'] = 'PLAYER_TRANSMIT_OTHER_SCENE_TRRIGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960025344'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 185, 
            ['ShowCount'] = false, 
            ['Value'] = 106, 
        },
        [186] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_SAME_SCENE_TRIGGER', 
            ['Const'] = 'TRANSMIT_SAME_SCENE_TRIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960025600'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 186, 
            ['ShowCount'] = false, 
            ['Value'] = 107, 
        },
        [187] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CHAPTER_START', 
            ['Const'] = 'PLAY_CHAPTER_START', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996672'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 187, 
            ['ShowCount'] = false, 
            ['Value'] = 108, 
        },
        [188] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_CHAPTER_END', 
            ['Const'] = 'PLAY_CHAPTER_END', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996928'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 188, 
            ['ShowCount'] = false, 
            ['Value'] = 109, 
        },
        [189] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_START_QUEST', 
            ['Const'] = 'PLAY_START_QUEST', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988480'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 189, 
            ['ShowCount'] = false, 
            ['Value'] = 110, 
        },
        [190] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_END_QUEST', 
            ['Const'] = 'PLAY_END_QUEST', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960026624'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 190, 
            ['ShowCount'] = false, 
            ['Value'] = 111, 
        },
        [191] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__OPEN_CROSS_WORD_PUZZLE', 
            ['Const'] = 'OPEN_CROSS_WORD_PUZZLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959994880'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 191, 
            ['ShowCount'] = false, 
            ['Value'] = 112, 
        },
        [192] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__OPEN_LETTER', 
            ['Const'] = 'OPEN_LETTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995136'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 192, 
            ['ShowCount'] = false, 
            ['Value'] = 113, 
        },
        [193] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__OPEN_JIGSAW_PUZZLE', 
            ['Const'] = 'OPEN_JIGSAW_PUZZLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959995392'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 193, 
            ['ShowCount'] = false, 
            ['Value'] = 114, 
        },
        [194] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_2DAUDIO', 
            ['Const'] = 'PLAY_2DAUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960027648'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 194, 
            ['ShowCount'] = false, 
            ['Value'] = 115, 
        },
        [195] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_2DAUDIO', 
            ['Const'] = 'STOP_2DAUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960027904'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 195, 
            ['ShowCount'] = false, 
            ['Value'] = 116, 
        },
        [196] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_3DAUDIO', 
            ['Const'] = 'PLAY_3DAUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960028160'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 196, 
            ['ShowCount'] = false, 
            ['Value'] = 117, 
        },
        [197] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_3DAUDIO', 
            ['Const'] = 'STOP_3DAUDIO', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960028416'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 197, 
            ['ShowCount'] = false, 
            ['Value'] = 118, 
        },
        [198] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CLOSE_LETTER', 
            ['Const'] = 'CLOSE_LETTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960012288'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 198, 
            ['ShowCount'] = false, 
            ['Value'] = 119, 
        },
        [199] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_TO_AI_MESSAGE', 
            ['Const'] = 'SEND_TO_AI_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959999744'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 199, 
            ['ShowCount'] = false, 
            ['Value'] = 120, 
        },
        [200] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_TO_AI_AND_SPACE_MESSAGE', 
            ['Const'] = 'SEND_TO_AI_AND_SPACE_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000000'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 200, 
            ['ShowCount'] = false, 
            ['Value'] = 121, 
        },
        [201] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_TO_SPACE_MESSAGE', 
            ['Const'] = 'SEND_TO_SPACE_MESSAGE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000256'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 201, 
            ['ShowCount'] = false, 
            ['Value'] = 122, 
        },
        [202] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_QUEST_ITEM', 
            ['Const'] = 'SEND_QUEST_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960029696'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 202, 
            ['ShowCount'] = false, 
            ['Value'] = 123, 
        },
        [203] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_JUMP_UI', 
            ['Const'] = 'START_JUMP_UI', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987712'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 203, 
            ['ShowCount'] = false, 
            ['Value'] = 124, 
        },
        [204] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_TEMPLATEID', 
            ['Const'] = 'CHANGE_COLLECT_STATE_BY_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960030208'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 204, 
            ['ShowCount'] = false, 
            ['Value'] = 125, 
        },
        [205] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_COLLECT_STATE_BY_INSTANCEID', 
            ['Const'] = 'CHANGE_COLLECT_STATE_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960030464'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 205, 
            ['ShowCount'] = false, 
            ['Value'] = 126, 
        },
        [206] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_INTERACTOR_STATE', 
            ['Const'] = 'CHANGE_INTERACTOR_STATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960004096'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 206, 
            ['ShowCount'] = false, 
            ['Value'] = 127, 
        },
        [207] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__UNLOCKED_FUNCTION', 
            ['Const'] = 'UNLOCKED_FUNCTION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959984640'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 207, 
            ['ShowCount'] = false, 
            ['Value'] = 128, 
        },
        [208] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_TO_SPAWNER', 
            ['Const'] = 'GAZE_TO_SPAWNER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990784'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 208, 
            ['ShowCount'] = false, 
            ['Value'] = 129, 
        },
        [209] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_TO_LOC', 
            ['Const'] = 'GAZE_TO_LOC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991040'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 209, 
            ['ShowCount'] = false, 
            ['Value'] = 130, 
        },
        [210] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__GAZE_TO_BACK', 
            ['Const'] = 'GAZE_TO_BACK', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959991296'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 210, 
            ['ShowCount'] = false, 
            ['Value'] = 131, 
        },
        [211] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ADD_BUFF_BY_INSTANCEID', 
            ['Const'] = 'ADD_BUFF_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960032000'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 211, 
            ['ShowCount'] = false, 
            ['Value'] = 132, 
        },
        [212] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_BUFF_BY_INSTANCEID', 
            ['Const'] = 'REMOVE_BUFF_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960032256'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 212, 
            ['ShowCount'] = false, 
            ['Value'] = 133, 
        },
        [213] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_MORPH', 
            ['Const'] = 'START_MORPH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987200'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 213, 
            ['ShowCount'] = false, 
            ['Value'] = 134, 
        },
        [214] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_MORPH', 
            ['Const'] = 'STOP_MORPH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959987456'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 214, 
            ['ShowCount'] = false, 
            ['Value'] = 135, 
        },
        [215] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_MOVE', 
            ['Const'] = 'CAMERA_MOVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960033024'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 215, 
            ['ShowCount'] = false, 
            ['Value'] = 136, 
        },
        [216] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_END', 
            ['Const'] = 'CAMERA_END', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960033280'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 216, 
            ['ShowCount'] = false, 
            ['Value'] = 137, 
        },
        [217] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_POS', 
            ['Const'] = 'CAMERA_LOOKAT_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960033536'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 217, 
            ['ShowCount'] = false, 
            ['Value'] = 138, 
        },
        [218] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CAMERA_LOOKAT_INS', 
            ['Const'] = 'CAMERA_LOOKAT_INS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960033792'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 218, 
            ['ShowCount'] = false, 
            ['Value'] = 139, 
        },
        [219] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__FOG_CONTROL', 
            ['Const'] = 'FOG_CONTROL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959999488'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 219, 
            ['ShowCount'] = false, 
            ['Value'] = 140, 
        },
        [220] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ENTER_PLANE_POS', 
            ['Const'] = 'ENTER_PLANE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960034304'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 220, 
            ['ShowCount'] = false, 
            ['Value'] = 141, 
        },
        [221] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_SPIRITUAL_VISION', 
            ['Const'] = 'START_SPIRITUAL_VISION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607174912'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 221, 
            ['ShowCount'] = false, 
            ['Value'] = 142, 
        },
        [222] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CLOSE_SPIRITUAL_VISION', 
            ['Const'] = 'CLOSE_SPIRITUAL_VISION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607195904'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 222, 
            ['ShowCount'] = false, 
            ['Value'] = 143, 
        },
        [223] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_BUBBLE', 
            ['Const'] = 'START_BUBBLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960035072'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 223, 
            ['ShowCount'] = false, 
            ['Value'] = 144, 
        },
        [224] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SET_QUEST_MARK_DEFINE', 
            ['Const'] = 'SET_QUEST_MARK_DEFINE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002560'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 224, 
            ['ShowCount'] = false, 
            ['Value'] = 145, 
        },
        [225] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CLEAR_QUEST_MARK_DEFINE', 
            ['Const'] = 'CLEAR_QUEST_MARK_DEFINE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002816'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 225, 
            ['ShowCount'] = false, 
            ['Value'] = 146, 
        },
        [226] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__KILL_INSTANCEID', 
            ['Const'] = 'KILL_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960035840'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 226, 
            ['ShowCount'] = false, 
            ['Value'] = 147, 
        },
        [227] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__KILL_TEMPLATEID', 
            ['Const'] = 'KILL_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960036096'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 227, 
            ['ShowCount'] = false, 
            ['Value'] = 148, 
        },
        [228] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CREATE_BY_INSTANCEID', 
            ['Const'] = 'CREATE_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960036352'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 228, 
            ['ShowCount'] = false, 
            ['Value'] = 149, 
        },
        [229] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DESTROY_BY_INSTANCEID', 
            ['Const'] = 'DESTROY_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960036608'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 229, 
            ['ShowCount'] = false, 
            ['Value'] = 150, 
        },
        [230] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CREATE_BY_CLUSTER', 
            ['Const'] = 'CREATE_BY_CLUSTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960036864'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 230, 
            ['ShowCount'] = false, 
            ['Value'] = 151, 
        },
        [231] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DESTROY_BY_CLUSTER', 
            ['Const'] = 'DESTROY_BY_CLUSTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960037120'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 231, 
            ['ShowCount'] = false, 
            ['Value'] = 152, 
        },
        [232] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_ANGLE', 
            ['Const'] = 'ROTATE_TO_ANGLE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990016'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 232, 
            ['ShowCount'] = false, 
            ['Value'] = 153, 
        },
        [233] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_INSTANCE', 
            ['Const'] = 'ROTATE_TO_INSTANCE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990272'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 233, 
            ['ShowCount'] = false, 
            ['Value'] = 154, 
        },
        [234] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ROTATE_TO_POS', 
            ['Const'] = 'ROTATE_TO_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959990528'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 234, 
            ['ShowCount'] = false, 
            ['Value'] = 155, 
        },
        [235] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__NPC_TOMONSTER', 
            ['Const'] = 'NPC_TOMONSTER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960038144'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 235, 
            ['ShowCount'] = false, 
            ['Value'] = 156, 
        },
        [236] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__MONSTER_TO_NPC', 
            ['Const'] = 'MONSTER_TO_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959996416'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 236, 
            ['ShowCount'] = false, 
            ['Value'] = 157, 
        },
        [237] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_SCENE_EFFECT', 
            ['Const'] = 'START_SCENE_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960000768'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 237, 
            ['ShowCount'] = false, 
            ['Value'] = 158, 
        },
        [238] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DELETE_SCENE_EFFECT', 
            ['Const'] = 'DELETE_SCENE_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001024'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 238, 
            ['ShowCount'] = false, 
            ['Value'] = 159, 
        },
        [239] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_ENTITY_EFFECT', 
            ['Const'] = 'START_ENTITY_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001280'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 239, 
            ['ShowCount'] = false, 
            ['Value'] = 160, 
        },
        [240] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DELETE_ENTITY_EFFECT', 
            ['Const'] = 'DELETE_ENTITY_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001536'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 240, 
            ['ShowCount'] = false, 
            ['Value'] = 161, 
        },
        [241] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_CAMERA_POST_PROCESS_EFFECT', 
            ['Const'] = 'START_CAMERA_POST_PROCESS_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960001792'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 241, 
            ['ShowCount'] = false, 
            ['Value'] = 162, 
        },
        [242] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__DELETE_CAMERA_POST_PROCESS_EFFECT', 
            ['Const'] = 'DELETE_CAMERA_POST_PROCESS_EFFECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002048'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 242, 
            ['ShowCount'] = false, 
            ['Value'] = 163, 
        },
        [243] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ANIMATION_STOP', 
            ['Const'] = 'ANIMATION_STOP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960002304'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 243, 
            ['ShowCount'] = false, 
            ['Value'] = 164, 
        },
        [244] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SAY', 
            ['Const'] = 'SAY', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960040448'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 244, 
            ['ShowCount'] = false, 
            ['Value'] = 165, 
        },
        [245] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__REMOVE_QUEST_ITEM', 
            ['Const'] = 'REMOVE_QUEST_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960040704'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 245, 
            ['ShowCount'] = false, 
            ['Value'] = 166, 
        },
        [246] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_MAIL', 
            ['Const'] = 'SEND_MAIL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960040960'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 246, 
            ['ShowCount'] = false, 
            ['Value'] = 167, 
        },
        [247] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CHANGE_CLIMATE', 
            ['Const'] = 'CHANGE_CLIMATE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960041216'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 247, 
            ['ShowCount'] = false, 
            ['Value'] = 168, 
        },
        [248] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SET_SCENE_TIME', 
            ['Const'] = 'SET_SCENE_TIME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960041472'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 248, 
            ['ShowCount'] = false, 
            ['Value'] = 169, 
        },
        [249] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__FLOWCHART_ACTION', 
            ['Const'] = 'FLOWCHART_ACTION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960041728'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 249, 
            ['ShowCount'] = false, 
            ['Value'] = 170, 
        },
        [250] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG', 
            ['Const'] = 'PLAY_NOCAMERA_DIALOG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960041984'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 250, 
            ['ShowCount'] = false, 
            ['Value'] = 171, 
        },
        [251] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_NPC', 
            ['Const'] = 'PLAY_NOCAMERA_DIALOG_BY_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960042240'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 251, 
            ['ShowCount'] = false, 
            ['Value'] = 172, 
        },
        [252] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__PLAY_NOCAMERA_DIALOG_BY_POSITION', 
            ['Const'] = 'PLAY_NOCAMERA_DIALOG_BY_POSITION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960042496'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 252, 
            ['ShowCount'] = false, 
            ['Value'] = 173, 
        },
        [253] = {
            ['ClientExec'] = true, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_NOCAMERA_DIALOG', 
            ['Const'] = 'STOP_NOCAMERA_DIALOG', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960042752'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 253, 
            ['ShowCount'] = false, 
            ['Value'] = 174, 
        },
        [254] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_AUTO_PATH', 
            ['Const'] = 'STOP_AUTO_PATH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960043008'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 254, 
            ['ShowCount'] = false, 
            ['Value'] = 175, 
        },
        [255] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SET_ANIMATION', 
            ['Const'] = 'SET_ANIMATION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960043264'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 255, 
            ['ShowCount'] = false, 
            ['Value'] = 176, 
        },
        [256] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__TRANSMIT_HOME', 
            ['Const'] = 'TRANSMIT_HOME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960043520'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 256, 
            ['ShowCount'] = false, 
            ['Value'] = 177, 
        },
        [257] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__SEND_FASHION', 
            ['Const'] = 'SEND_FASHION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960043776'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 257, 
            ['ShowCount'] = false, 
            ['Value'] = 178, 
        },
        [258] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__ENTER_PLANE', 
            ['Const'] = 'ENTER_PLANE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607202048'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 258, 
            ['ShowCount'] = false, 
            ['Value'] = 179, 
        },
        [259] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__START_PAINTING_STRATCH', 
            ['Const'] = 'START_PAINTING_STRATCH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960044288'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 259, 
            ['ShowCount'] = false, 
            ['Value'] = 180, 
        },
        [260] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CANE_SKILL_TO_INSTANCEID', 
            ['Const'] = 'CANE_SKILL_TO_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960044544'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 260, 
            ['ShowCount'] = false, 
            ['Value'] = 181, 
        },
        [261] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__CANE_SKILL_TO_POSITION', 
            ['Const'] = 'CANE_SKILL_TO_POSITION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960044800'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 261, 
            ['ShowCount'] = false, 
            ['Value'] = 182, 
        },
        [262] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TRIGGER_EVENT_TYPE__STOP_CANE_SKILL', 
            ['Const'] = 'STOP_CANE_SKILL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960045056'),
            ['Group'] = 'TRIGGER_EVENT_TYPE', 
            ['ID'] = 262, 
            ['ShowCount'] = false, 
            ['Value'] = 183, 
        },
        [263] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__START_CUTSCENE', 
            ['Const'] = 'START_CUTSCENE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960045312'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 263, 
            ['ShowCount'] = false, 
            ['Value'] = 101, 
        },
        [264] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__START_DIALOGUE', 
            ['Const'] = 'START_DIALOGUE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960008960'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 264, 
            ['ShowCount'] = false, 
            ['Value'] = 102, 
        },
        [265] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__COLLECT_ITEM', 
            ['Const'] = 'COLLECT_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960010496'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 265, 
            ['ShowCount'] = false, 
            ['Value'] = 103, 
        },
        [266] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__COLLECT_ITEM_RANDOM', 
            ['Const'] = 'COLLECT_ITEM_RANDOM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960046080'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 266, 
            ['ShowCount'] = false, 
            ['Value'] = 104, 
        },
        [267] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__LEVEL', 
            ['Const'] = 'LEVEL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960046336'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 267, 
            ['ShowCount'] = false, 
            ['Value'] = 105, 
        },
        [268] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__GO_TO_TRIGGER', 
            ['Const'] = 'GO_TO_TRIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960046592'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 268, 
            ['ShowCount'] = false, 
            ['Value'] = 106, 
        },
        [269] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__GO_TO_POS', 
            ['Const'] = 'GO_TO_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960046848'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 269, 
            ['ShowCount'] = false, 
            ['Value'] = 107, 
        },
        [270] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__GO_TO_LEVELMAP', 
            ['Const'] = 'GO_TO_LEVELMAP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960047104'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 270, 
            ['ShowCount'] = false, 
            ['Value'] = 108, 
        },
        [271] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__EQUIP', 
            ['Const'] = 'EQUIP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960009984'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 271, 
            ['ShowCount'] = false, 
            ['Value'] = 109, 
        },
        [272] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_GUILD_MATERIAL_QUEST', 
            ['Const'] = 'FINISH_GUILD_MATERIAL_QUEST', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960047616'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 272, 
            ['ShowCount'] = false, 
            ['Value'] = 110, 
        },
        [273] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__WAITTIME', 
            ['Const'] = 'WAITTIME', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145959988736'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 273, 
            ['ShowCount'] = false, 
            ['Value'] = 111, 
        },
        [274] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NULL', 
            ['Const'] = 'NULL', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960048128'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 274, 
            ['ShowCount'] = false, 
            ['Value'] = 112, 
        },
        [275] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__BUY_ITEMS', 
            ['Const'] = 'BUY_ITEMS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960048384'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 275, 
            ['ShowCount'] = false, 
            ['Value'] = 113, 
        },
        [276] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__LETTER_CLOSE', 
            ['Const'] = 'LETTER_CLOSE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960048640'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 276, 
            ['ShowCount'] = false, 
            ['Value'] = 114, 
        },
        [277] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__DUNGEON_COMPLETE_IN_GUILD', 
            ['Const'] = 'DUNGEON_COMPLETE_IN_GUILD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960048896'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 277, 
            ['ShowCount'] = false, 
            ['Value'] = 115, 
        },
        [278] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPCTALK_ENTER_PLANE', 
            ['Const'] = 'NPCTALK_ENTER_PLANE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960049152'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 278, 
            ['ShowCount'] = false, 
            ['Value'] = 116, 
        },
        [279] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__MISTERY_SUCC', 
            ['Const'] = 'MISTERY_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960012800'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 279, 
            ['ShowCount'] = false, 
            ['Value'] = 117, 
        },
        [280] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__UI_JUMP', 
            ['Const'] = 'UI_JUMP', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960049664'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 280, 
            ['ShowCount'] = false, 
            ['Value'] = 118, 
        },
        [281] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__JIGSAW_PUZZLE_SUCCES', 
            ['Const'] = 'JIGSAW_PUZZLE_SUCCES', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960049920'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 281, 
            ['ShowCount'] = false, 
            ['Value'] = 119, 
        },
        [282] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPC_ASK_PRICE_SUCC', 
            ['Const'] = 'NPC_ASK_PRICE_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960050176'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 282, 
            ['ShowCount'] = false, 
            ['Value'] = 120, 
        },
        [283] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SEQUENCE_SAN_CHECK_SUCC', 
            ['Const'] = 'SEQUENCE_SAN_CHECK_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960050432'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 283, 
            ['ShowCount'] = false, 
            ['Value'] = 121, 
        },
        [284] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CUT_MONSTER_HP_BY_INSTANCEID', 
            ['Const'] = 'CUT_MONSTER_HP_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960050688'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 284, 
            ['ShowCount'] = false, 
            ['Value'] = 122, 
        },
        [285] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CUT_MONSTER_HP_BY_TEMPLATEID', 
            ['Const'] = 'CUT_MONSTER_HP_BY_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960050944'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 285, 
            ['ShowCount'] = false, 
            ['Value'] = 123, 
        },
        [286] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ITEM_SUBMIT', 
            ['Const'] = 'ITEM_SUBMIT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960051200'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 286, 
            ['ShowCount'] = false, 
            ['Value'] = 124, 
        },
        [287] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__RECEIVE_CUSTOM_EVENT', 
            ['Const'] = 'RECEIVE_CUSTOM_EVENT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960051456'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 287, 
            ['ShowCount'] = false, 
            ['Value'] = 125, 
        },
        [288] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CHAT_NUMBER', 
            ['Const'] = 'CHAT_NUMBER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960051712'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 288, 
            ['ShowCount'] = false, 
            ['Value'] = 126, 
        },
        [289] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__MONSTER_KILL_INSTANCEID', 
            ['Const'] = 'MONSTER_KILL_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960051968'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 289, 
            ['ShowCount'] = false, 
            ['Value'] = 127, 
        },
        [290] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__MONSTER_KILL_TEMPLATEID', 
            ['Const'] = 'MONSTER_KILL_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960052224'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 290, 
            ['ShowCount'] = false, 
            ['Value'] = 128, 
        },
        [291] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__USE_ITEM_AT_POSITION', 
            ['Const'] = 'USE_ITEM_AT_POSITION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960052480'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 291, 
            ['ShowCount'] = false, 
            ['Value'] = 129, 
        },
        [292] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__USE_ITEM_BY_INSTANCEID', 
            ['Const'] = 'USE_ITEM_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960052736'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 292, 
            ['ShowCount'] = false, 
            ['Value'] = 130, 
        },
        [293] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL_BY_INSTANCEID', 
            ['Const'] = 'CAST_SKILL_BY_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960052992'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 293, 
            ['ShowCount'] = false, 
            ['Value'] = 131, 
        },
        [294] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL_BY_TEMPLATEID', 
            ['Const'] = 'CAST_SKILL_BY_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960053248'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 294, 
            ['ShowCount'] = false, 
            ['Value'] = 132, 
        },
        [295] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL_BY_POS', 
            ['Const'] = 'CAST_SKILL_BY_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960053504'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 295, 
            ['ShowCount'] = false, 
            ['Value'] = 133, 
        },
        [296] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL_BY_SKILLID', 
            ['Const'] = 'CAST_SKILL_BY_SKILLID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960053760'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 296, 
            ['ShowCount'] = false, 
            ['Value'] = 134, 
        },
        [297] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CAST_SKILL_BY_TRIGGER', 
            ['Const'] = 'CAST_SKILL_BY_TRIGGER', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960054016'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 297, 
            ['ShowCount'] = false, 
            ['Value'] = 135, 
        },
        [298] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__COMPLETE_DUNGEON', 
            ['Const'] = 'COMPLETE_DUNGEON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960005632'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 298, 
            ['ShowCount'] = false, 
            ['Value'] = 136, 
        },
        [299] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISHED_RING', 
            ['Const'] = 'FINISHED_RING', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_25908585004032'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 299, 
            ['ShowCount'] = false, 
            ['Value'] = 137, 
        },
        [300] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISHED_QUEST', 
            ['Const'] = 'FINISHED_QUEST', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960054784'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 300, 
            ['ShowCount'] = false, 
            ['Value'] = 138, 
        },
        [301] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__GET_ITEMS', 
            ['Const'] = 'GET_ITEMS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960055040'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 301, 
            ['ShowCount'] = false, 
            ['Value'] = 139, 
        },
        [302] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FIND_NPC', 
            ['Const'] = 'FIND_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960055296'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 302, 
            ['ShowCount'] = false, 
            ['Value'] = 140, 
        },
        [303] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__INTERACTION_COMPLETED', 
            ['Const'] = 'INTERACTION_COMPLETED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960055552'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 303, 
            ['ShowCount'] = false, 
            ['Value'] = 141, 
        },
        [304] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__INTERACTION_PLANE_ID', 
            ['Const'] = 'INTERACTION_PLANE_ID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960055808'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 304, 
            ['ShowCount'] = false, 
            ['Value'] = 142, 
        },
        [305] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__INTERACTION_PLANE_POS', 
            ['Const'] = 'INTERACTION_PLANE_POS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960056064'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 305, 
            ['ShowCount'] = false, 
            ['Value'] = 143, 
        },
        [306] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPC_DIALOGUE', 
            ['Const'] = 'NPC_DIALOGUE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960056320'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 306, 
            ['ShowCount'] = false, 
            ['Value'] = 144, 
        },
        [307] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__TALK_NPC', 
            ['Const'] = 'TALK_NPC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960056576'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 307, 
            ['ShowCount'] = false, 
            ['Value'] = 145, 
        },
        [308] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_QTE_ASHBUTTON', 
            ['Const'] = 'FINISH_QTE_ASHBUTTON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960056832'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 308, 
            ['ShowCount'] = false, 
            ['Value'] = 146, 
        },
        [309] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_QTE_SLIDE', 
            ['Const'] = 'FINISH_QTE_SLIDE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960057088'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 309, 
            ['ShowCount'] = false, 
            ['Value'] = 147, 
        },
        [310] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_QTE_BUTTON', 
            ['Const'] = 'FINISH_QTE_BUTTON', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960057344'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 310, 
            ['ShowCount'] = false, 
            ['Value'] = 148, 
        },
        [311] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SIT_CHAIR', 
            ['Const'] = 'SIT_CHAIR', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960057600'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 311, 
            ['ShowCount'] = false, 
            ['Value'] = 149, 
        },
        [312] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__USE_ITEM', 
            ['Const'] = 'USE_ITEM', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960057856'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 312, 
            ['ShowCount'] = false, 
            ['Value'] = 150, 
        },
        [313] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__OPEN_SPIRITUALVISION', 
            ['Const'] = 'OPEN_SPIRITUALVISION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_29756607174912'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 313, 
            ['ShowCount'] = false, 
            ['Value'] = 151, 
        },
        [314] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__COLLECT_ITEM_TEMPLATEID', 
            ['Const'] = 'COLLECT_ITEM_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960058368'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 314, 
            ['ShowCount'] = false, 
            ['Value'] = 152, 
        },
        [315] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_ALL_SUBTARGETS', 
            ['Const'] = 'FINISH_ALL_SUBTARGETS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960058624'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 315, 
            ['ShowCount'] = false, 
            ['Value'] = 153, 
        },
        [316] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_SUBTARGET_COUNT', 
            ['Const'] = 'FINISH_SUBTARGET_COUNT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960058880'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 316, 
            ['ShowCount'] = false, 
            ['Value'] = 154, 
        },
        [317] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_SUBTARGET_LIST', 
            ['Const'] = 'FINISH_SUBTARGET_LIST', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960059136'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 317, 
            ['ShowCount'] = false, 
            ['Value'] = 155, 
        },
        [318] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__SIT_ANY_CHAIR', 
            ['Const'] = 'SIT_ANY_CHAIR', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960059392'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 318, 
            ['ShowCount'] = false, 
            ['Value'] = 156, 
        },
        [319] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CANE_SKILL_INSTANCEID', 
            ['Const'] = 'CANE_SKILL_INSTANCEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960059648'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 319, 
            ['ShowCount'] = false, 
            ['Value'] = 157, 
        },
        [320] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CANE_SKILL_TEMPLATEID', 
            ['Const'] = 'CANE_SKILL_TEMPLATEID', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960059904'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 320, 
            ['ShowCount'] = false, 
            ['Value'] = 158, 
        },
        [321] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CANE_SKILL_POSITION', 
            ['Const'] = 'CANE_SKILL_POSITION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960060160'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 321, 
            ['ShowCount'] = false, 
            ['Value'] = 159, 
        },
        [322] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ENTER_SPACE_CLIENT_LOADED', 
            ['Const'] = 'ENTER_SPACE_CLIENT_LOADED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960060416'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 322, 
            ['ShowCount'] = false, 
            ['Value'] = 160, 
        },
        [323] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__DOOR', 
            ['Const'] = 'DOOR', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960060672'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 323, 
            ['ShowCount'] = false, 
            ['Value'] = 161, 
        },
        [324] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__WEAR_FASHION', 
            ['Const'] = 'WEAR_FASHION', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960060928'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 324, 
            ['ShowCount'] = false, 
            ['Value'] = 162, 
        },
        [325] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__POTION_MAKE_SUCCESS', 
            ['Const'] = 'POTION_MAKE_SUCCESS', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960061184'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 325, 
            ['ShowCount'] = false, 
            ['Value'] = 163, 
        },
        [326] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__PAINTING_STRATCH_SUCC', 
            ['Const'] = 'PAINTING_STRATCH_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960061440'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 326, 
            ['ShowCount'] = false, 
            ['Value'] = 164, 
        },
        [327] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__PLAYER_DEAD', 
            ['Const'] = 'PLAYER_DEAD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960019968'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 327, 
            ['ShowCount'] = false, 
            ['Value'] = 501, 
        },
        [328] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__NPC_DEAD', 
            ['Const'] = 'NPC_DEAD', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020224'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 328, 
            ['ShowCount'] = false, 
            ['Value'] = 502, 
        },
        [329] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__ALL_PLAYER_DEACTIVE', 
            ['Const'] = 'ALL_PLAYER_DEACTIVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020480'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 329, 
            ['ShowCount'] = false, 
            ['Value'] = 503, 
        },
        [330] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__QUEST_TIMEOUT', 
            ['Const'] = 'QUEST_TIMEOUT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020736'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 330, 
            ['ShowCount'] = false, 
            ['Value'] = 504, 
        },
        [331] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__PLAYER_LEAVE', 
            ['Const'] = 'PLAYER_LEAVE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960020992'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 331, 
            ['ShowCount'] = false, 
            ['Value'] = 505, 
        },
        [332] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'QUEST_FAIL_TYPE__QTE_FAILED', 
            ['Const'] = 'QTE_FAILED', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960021248'),
            ['Group'] = 'QUEST_FAIL_TYPE', 
            ['ID'] = 332, 
            ['ShowCount'] = false, 
            ['Value'] = 506, 
        },
        [333] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__FINISH_EXTRAORDINARY_EVENT', 
            ['Const'] = 'FINISH_EXTRAORDINARY_EVENT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960063232'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 333, 
            ['ShowCount'] = false, 
            ['Value'] = 165, 
        },
        [334] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__CHANGE_NAME_SUCC', 
            ['Const'] = 'CHANGE_NAME_SUCC', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960063488'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 334, 
            ['ShowCount'] = false, 
            ['Value'] = 166, 
        },
        [335] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ITEM_SUBMIT_DIRECT', 
            ['Const'] = 'ITEM_SUBMIT_DIRECT', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960063744'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 335, 
            ['ShowCount'] = false, 
            ['Value'] = 167, 
        },
        [336] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__ITEM_SUBMIT_BRANCH', 
            ['Const'] = 'ITEM_SUBMIT_BRANCH', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960064000'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 336, 
            ['ShowCount'] = false, 
            ['Value'] = 168, 
        },
        [337] = {
            ['ClientExec'] = false, 
            ['CombineEnum'] = 'TARGET_TYPE__NPC_SEQUENCE_DIALOGUE', 
            ['Const'] = 'NPC_SEQUENCE_DIALOGUE', 
            ['EnumDesc'] = Game.TableDataManager:GetLangStr('str_56145960064256'),
            ['Group'] = 'TARGET_TYPE', 
            ['ID'] = 337, 
            ['ShowCount'] = false, 
            ['Value'] = 169, 
        },
    }
}
return TopData