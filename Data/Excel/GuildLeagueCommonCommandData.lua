--
-- 表名: $GuildLeague_公会联赛表.xlsx  页名：$GuildLeagueCommonCommand_基础指令表
--

local TopData = {
	data = {
		[1001] = {
			["Id"] = 1001,
			["Icon"] = "UI_Guild_Icon_GuildBattle01",
			["CommandCD"] = 1,
			["Reminder"] = Game.TableDataManager:GetLangStr('str_61025580153856'),
			["ReminderMyCampTower"] = Game.TableDataManager:GetLangStr('str_28658169284096'),
			["ReminderEnemyCampTower"] = Game.TableDataManager:GetLangStr('str_28658437719552'),
			["ReminderMonster"] = Game.TableDataManager:GetLangStr('str_28658706155008'),
		},
		[1002] = {
			["Id"] = 1002,
			["Icon"] = "UI_Guild_Icon_GuildBattle02",
			["CommandCD"] = 1,
			["Reminder"] = Game.TableDataManager:GetLangStr('str_61025580154112'),
			["ReminderMyCampTower"] = Game.TableDataManager:GetLangStr('str_28658169284352'),
			["ReminderEnemyCampTower"] = Game.TableDataManager:GetLangStr('str_28658437719808'),
			["ReminderMonster"] = Game.TableDataManager:GetLangStr('str_28658706155264'),
		},
		[1003] = {
			["Id"] = 1003,
			["Icon"] = "UI_Guild_Icon_GuildBattle04",
			["CommandCD"] = 1,
			["Reminder"] = Game.TableDataManager:GetLangStr('str_61025580154368'),
			["ReminderMyCampTower"] = Game.TableDataManager:GetLangStr('str_28658169284608'),
			["ReminderEnemyCampTower"] = Game.TableDataManager:GetLangStr('str_28658437720064'),
			["ReminderMonster"] = Game.TableDataManager:GetLangStr('str_28658706155520'),
		},
		[1004] = {
			["Id"] = 1004,
			["Icon"] = "UI_Guild_Icon_GuildBattle03",
			["CommandCD"] = 1,
			["Reminder"] = Game.TableDataManager:GetLangStr('str_61025580154624'),
			["ReminderMyCampTower"] = Game.TableDataManager:GetLangStr('str_28658169284864'),
			["ReminderEnemyCampTower"] = Game.TableDataManager:GetLangStr('str_28658437720320'),
			["ReminderMonster"] = Game.TableDataManager:GetLangStr('str_28658706155776'),
		},
	},
}

return TopData
