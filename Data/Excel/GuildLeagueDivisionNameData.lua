--
-- 表名: $GuildLeague_公会联赛表.xlsx  页名：$GuildLeagueDivisionName_赛区名称表
--

local TopData = {
	data = {
		[1] = {
			["Id"] = 1,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34983850824960'),
		},
		[2] = {
			["Id"] = 2,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34984119240192'),
		},
		[3] = {
			["Id"] = 3,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657344'),
		},
		[4] = {
			["Id"] = 4,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657600'),
		},
		[5] = {
			["Id"] = 5,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657856'),
		},
		[6] = {
			["Id"] = 6,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658112'),
		},
		[7] = {
			["Id"] = 7,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_5154766133504'),
		},
		[8] = {
			["Id"] = 8,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658624'),
		},
		[9] = {
			["Id"] = 9,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658880'),
		},
		[10] = {
			["Id"] = 10,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34983850824960'),
		},
		[11] = {
			["Id"] = 11,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34984119240192'),
		},
		[12] = {
			["Id"] = 12,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657344'),
		},
		[13] = {
			["Id"] = 13,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657600'),
		},
		[14] = {
			["Id"] = 14,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185657856'),
		},
		[15] = {
			["Id"] = 15,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658112'),
		},
		[16] = {
			["Id"] = 16,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_5154766133504'),
		},
		[17] = {
			["Id"] = 17,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658624'),
		},
		[18] = {
			["Id"] = 18,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_43088185658880'),
		},
		[19] = {
			["Id"] = 19,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34983850824960'),
		},
		[20] = {
			["Id"] = 20,
			["DivisionName"] = Game.TableDataManager:GetLangStr('str_34984119240192'),
		},
	},
}

return TopData
