--
-- 表名: $FellowShowProperty.xlsx  页名：$MFellowPropDetail
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Title"] = 1,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789317376'),
			["CalculateType"] = 2,
			["ShowProperty"] = {"MaxHp"},
			["ShowType"] = 0,
			["TipsID"] = 6421001,
		},
		[2] = {
			["ID"] = 2,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992494081'),
			["CalculateType"] = 1,
			["ShowProperty"] = {"mAtkMin", "mAtkMax"},
			["ShowType"] = 0,
			["TipsID"] = 6421013,
		},
		[3] = {
			["ID"] = 3,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992494082'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mPierce"},
			["ShowType"] = 0,
			["TipsID"] = 6421017,
		},
		[4] = {
			["ID"] = 4,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_17319992498434'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCrit"},
			["ShowType"] = 0,
			["TipsID"] = 6421019,
		},
		[5] = {
			["ID"] = 5,
			["Title"] = 2,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_18830210377472'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritHurt"},
			["ShowType"] = 1,
			["TipsID"] = 6421021,
		},
		[6] = {
			["ID"] = 6,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789314816'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pDef"},
			["ShowType"] = 0,
			["TipsID"] = 6421032,
		},
		[7] = {
			["ID"] = 7,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_54632789315072'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mDef"},
			["ShowType"] = 0,
			["TipsID"] = 6421033,
		},
		[8] = {
			["ID"] = 8,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329572864'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pBlock"},
			["ShowType"] = 0,
			["TipsID"] = 6421034,
		},
		[9] = {
			["ID"] = 9,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329580288'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mBlock"},
			["ShowType"] = 0,
			["TipsID"] = 6421035,
		},
		[10] = {
			["ID"] = 10,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368807936'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCritAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421036,
		},
		[11] = {
			["ID"] = 11,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368808192'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421037,
		},
		[12] = {
			["ID"] = 12,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368808960'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"pCritDef"},
			["ShowType"] = 1,
			["TipsID"] = 6421038,
		},
		[13] = {
			["ID"] = 13,
			["Title"] = 3,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368809216'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"mCritDef"},
			["ShowType"] = 1,
			["TipsID"] = 6421039,
		},
		[14] = {
			["ID"] = 14,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403225856'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ChaosAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421045,
		},
		[15] = {
			["ID"] = 15,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403226112'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreChaosAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421046,
		},
		[16] = {
			["ID"] = 16,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403226368'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"MysteryAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421047,
		},
		[17] = {
			["ID"] = 17,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403226624'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreMysteryAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421048,
		},
		[18] = {
			["ID"] = 18,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403226880'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AbundanceAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421049,
		},
		[19] = {
			["ID"] = 19,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403227136'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreAbundanceAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421050,
		},
		[20] = {
			["ID"] = 20,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403227392'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DarknessAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421051,
		},
		[21] = {
			["ID"] = 21,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403227648'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreDarknessAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421052,
		},
		[22] = {
			["ID"] = 22,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403227904'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"CalamityAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421053,
		},
		[23] = {
			["ID"] = 23,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403228160'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreCalamityAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421054,
		},
		[24] = {
			["ID"] = 24,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403228416'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DisorderAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421055,
		},
		[25] = {
			["ID"] = 25,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403228672'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreDisorderAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421056,
		},
		[26] = {
			["ID"] = 26,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403228928'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TenebrousAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421057,
		},
		[27] = {
			["ID"] = 27,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403229184'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreTenebrousAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421058,
		},
		[28] = {
			["ID"] = 28,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403229440'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"KnowledgeAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421059,
		},
		[29] = {
			["ID"] = 29,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403229696'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreKnowledgeAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421060,
		},
		[30] = {
			["ID"] = 30,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403229952'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FateAtk"},
			["ShowType"] = 0,
			["TipsID"] = 6421061,
		},
		[31] = {
			["ID"] = 31,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403230208'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"IgnoreFateAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421062,
		},
		[32] = {
			["ID"] = 32,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403230464'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"ChaosAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421063,
		},
		[33] = {
			["ID"] = 33,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403230720'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"MysteryAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421064,
		},
		[34] = {
			["ID"] = 34,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403230976'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AbundanceAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421065,
		},
		[35] = {
			["ID"] = 35,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403231232'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DarknessAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421066,
		},
		[36] = {
			["ID"] = 36,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403231488'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"CalamityAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421067,
		},
		[37] = {
			["ID"] = 37,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403231744'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DisorderAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421068,
		},
		[38] = {
			["ID"] = 38,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403232000'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TenebrousAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421069,
		},
		[39] = {
			["ID"] = 39,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403232256'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"KnowledgeAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421070,
		},
		[40] = {
			["ID"] = 40,
			["Title"] = 4,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_23503403232512'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FateAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421071,
		},
		[41] = {
			["ID"] = 41,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821248'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceAirborne"},
			["ShowType"] = 0,
			["TipsID"] = 6421072,
		},
		[42] = {
			["ID"] = 42,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329607168'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AirborneHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421073,
		},
		[43] = {
			["ID"] = 43,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821504'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceDown"},
			["ShowType"] = 0,
			["TipsID"] = 6421074,
		},
		[44] = {
			["ID"] = 44,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329608448'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DownHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421075,
		},
		[45] = {
			["ID"] = 45,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368821760'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhancePull"},
			["ShowType"] = 0,
			["TipsID"] = 6421076,
		},
		[46] = {
			["ID"] = 46,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329609728'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"PullHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421077,
		},
		[47] = {
			["ID"] = 47,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822016'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceTied"},
			["ShowType"] = 0,
			["TipsID"] = 6421078,
		},
		[48] = {
			["ID"] = 48,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329611008'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TiedHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421079,
		},
		[49] = {
			["ID"] = 49,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822272'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSleep"},
			["ShowType"] = 0,
			["TipsID"] = 6421080,
		},
		[50] = {
			["ID"] = 50,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329612288'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SleepHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421081,
		},
		[51] = {
			["ID"] = 51,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822528'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceDizzy"},
			["ShowType"] = 0,
			["TipsID"] = 6421082,
		},
		[52] = {
			["ID"] = 52,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329613568'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DizzyHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421083,
		},
		[53] = {
			["ID"] = 53,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368822784'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceFear"},
			["ShowType"] = 0,
			["TipsID"] = 6421084,
		},
		[54] = {
			["ID"] = 54,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329614848'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FearHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421085,
		},
		[55] = {
			["ID"] = 55,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368823040'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSilence"},
			["ShowType"] = 0,
			["TipsID"] = 6421086,
		},
		[56] = {
			["ID"] = 56,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329616128'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SilenceHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421087,
		},
		[57] = {
			["ID"] = 57,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_19036368823296'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"EnhanceSlow"},
			["ShowType"] = 0,
			["TipsID"] = 6421088,
		},
		[58] = {
			["ID"] = 58,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329617408'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SlowHitRate"},
			["ShowType"] = 1,
			["TipsID"] = 6421089,
		},
		[59] = {
			["ID"] = 59,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329606144'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"AirborneAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421090,
		},
		[60] = {
			["ID"] = 60,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329607424'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DownAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421091,
		},
		[61] = {
			["ID"] = 61,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329608704'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"PullAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421092,
		},
		[62] = {
			["ID"] = 62,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329609984'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"TiedAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421093,
		},
		[63] = {
			["ID"] = 63,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329611264'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SleepAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421094,
		},
		[64] = {
			["ID"] = 64,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329612544'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"DizzyAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421095,
		},
		[65] = {
			["ID"] = 65,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329613824'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"FearAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421096,
		},
		[66] = {
			["ID"] = 66,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329615104'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SilenceAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421097,
		},
		[67] = {
			["ID"] = 67,
			["Title"] = 5,
			["ShowPropertyName"] = Game.TableDataManager:GetLangStr('str_24190329616384'),
			["CalculateType"] = 0,
			["ShowProperty"] = {"SlowAnti"},
			["ShowType"] = 0,
			["TipsID"] = 6421098,
		},
	},
}

return TopData
