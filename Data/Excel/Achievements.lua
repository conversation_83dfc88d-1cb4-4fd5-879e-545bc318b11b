--
-- 表名: Achievements后处理
--

local TopData = {
    ParentAchievementDict = {
        [6221001] = 6221002, 
        [6224000] = 6224001, 
        [6224001] = 6224002, 
        [6224002] = 6224003, 
        [6224003] = 6224004, 
        [6224004] = 6224005, 
    },
    data = {
        [6220000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_57795227420160'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16220000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {100}, ['FuncName'] = 'NPC_CUT_PRICE_EXCEED', ['FuncParamInfos'] = {['targetRatio'] = 100, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'NPC_CUT_PRICE_EXCEED(100)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_208574350848'),
            ['Hide'] = false, 
            ['ID'] = 6220000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_208305915392'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 101, 
            ['Token'] = 20, 
            ['Type'] = 1, 
            ['beShowProgress'] = true, 
        },
        [6220001] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_57795227420160'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16220001, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {50}, ['FuncName'] = 'NPC_CUT_PRICE_INSUFFICIENT', ['FuncParamInfos'] = {['targetRatio'] = 50, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'NPC_CUT_PRICE_INSUFFICIENT(50)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_208574351104'),
            ['Hide'] = false, 
            ['ID'] = 6220001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_208305915648'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 101, 
            ['Token'] = 20, 
            ['Type'] = 1, 
            ['beShowProgress'] = true, 
        },
        [6221000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_276756956672'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16221000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {2005525}, ['FuncName'] = 'ITEM_USE', ['FuncParamInfos'] = {['targetId'] = 2005525, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'ITEM_USE(2005525)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_277293827584'),
            ['Hide'] = true, 
            ['ID'] = 6221000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_277025392128'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 201, 
            ['Token'] = 20, 
            ['Type'] = 2, 
            ['beShowProgress'] = true, 
        },
        [6221001] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781133312'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16221001, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {2005525}, ['FuncName'] = 'ITEM_USE', ['FuncParamInfos'] = {['targetId'] = 2005525, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'ITEM_USE(2005525)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_277293827840'),
            ['Hide'] = false, 
            ['ID'] = 6221001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_277025392384'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 202, 
            ['Token'] = 20, 
            ['Type'] = 2, 
            ['beShowProgress'] = true, 
        },
        [6221002] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781133312'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16221002, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {2005525}, ['FuncName'] = 'ITEM_USE', ['FuncParamInfos'] = {['targetId'] = 2005525, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'ITEM_USE(2005525)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_277293828096'),
            ['Hide'] = false, 
            ['ID'] = 6221002, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_277025392640'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6221001, 
            ['Subclass_ID'] = 202, 
            ['Token'] = 20, 
            ['Type'] = 2, 
            ['beShowProgress'] = true, 
        },
        [6221003] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781133312'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16221003, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {2005525}, ['FuncName'] = 'ITEM_USE', ['FuncParamInfos'] = {['targetId'] = 2005525, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'ITEM_USE(2005525)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_277293828352'),
            ['Hide'] = false, 
            ['ID'] = 6221003, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_277025392896'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 202, 
            ['Token'] = 20, 
            ['Type'] = 2, 
            ['beShowProgress'] = true, 
        },
        [6222000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_345476433408'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16222000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {5100040}, ['FuncName'] = 'DUNGEON_COMPLETE', ['FuncParamInfos'] = {['targetId'] = 5100040, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'DUNGEON_COMPLETE(5100040)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_346013304320'),
            ['Hide'] = false, 
            ['ID'] = 6222000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_345744868864'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 20, 
            ['Type'] = 3, 
            ['beShowProgress'] = true, 
        },
        [6222001] = {
            ['Achievement_Name'] = '', 
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590010, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_346013304576'),
            ['Hide'] = false, 
            ['ID'] = 6222001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_345744869120'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 20, 
            ['Type'] = 3, 
            ['beShowProgress'] = true, 
        },
        [6222002] = {
            ['Achievement_Name'] = '', 
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590011, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_346013304832'),
            ['Hide'] = false, 
            ['ID'] = 6222002, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_54083570370304'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 20, 
            ['Type'] = 3, 
            ['beShowProgress'] = true, 
        },
        [6222003] = {
            ['Achievement_Name'] = '', 
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590012, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_346013305088'),
            ['Hide'] = false, 
            ['ID'] = 6222003, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_345744869632'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 301, 
            ['Token'] = 20, 
            ['Type'] = 3, 
            ['beShowProgress'] = true, 
        },
        [6223000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16223000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 30, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=30', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732781056'),
            ['Hide'] = false, 
            ['ID'] = 6223000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464345600'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223001] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590016, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_55801020419072'),
            ['Hide'] = false, 
            ['ID'] = 6223001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464345856'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223004] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590026, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732782080'),
            ['Hide'] = false, 
            ['ID'] = 6223004, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464346624'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223005] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590027, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732782336'),
            ['Hide'] = false, 
            ['ID'] = 6223005, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464346880'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223006] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590028, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732782592'),
            ['Hide'] = false, 
            ['ID'] = 6223006, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464347136'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223007] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590029, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732782848'),
            ['Hide'] = false, 
            ['ID'] = 6223007, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464347392'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6223008] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134592'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590030, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_414732783104'),
            ['Hide'] = false, 
            ['ID'] = 6223008, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_414464347648'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 401, 
            ['Token'] = 20, 
            ['Type'] = 4, 
            ['beShowProgress'] = true, 
        },
        [6224000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16224000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 5, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=5', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_51609132337920'),
            ['Hide'] = false, 
            ['ID'] = 6224000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183822336'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224001] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16224001, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 10, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=10', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_483452258048'),
            ['Hide'] = false, 
            ['ID'] = 6224001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183822592'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6224000, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224002] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16224002, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 15, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=15', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_483452258304'),
            ['Hide'] = false, 
            ['ID'] = 6224002, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183822848'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6224001, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224003] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace02.UI_Achievement_Icon_Replace02', 
            ['Condition'] = 16224003, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 20, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=20', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_483452258560'),
            ['Hide'] = false, 
            ['ID'] = 6224003, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183823104'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6224002, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224004] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace03.UI_Achievement_Icon_Replace03', 
            ['Condition'] = 16224004, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 25, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=25', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_51609132339712'),
            ['Hide'] = false, 
            ['ID'] = 6224004, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183823360'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6224003, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224005] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace04.UI_Achievement_Icon_Replace04', 
            ['Condition'] = 16224005, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 30, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=30', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_483452259072'),
            ['Hide'] = false, 
            ['ID'] = 6224005, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183823616'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 6224004, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 10000, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6224006] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_138781134848'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16224006, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 55, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {}, ['FuncName'] = 'LEVELUP', ['FuncParamInfos'] = {}, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'LEVELUP()>=55', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_483452259328'),
            ['Hide'] = false, 
            ['ID'] = 6224006, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_483183823872'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 501, 
            ['Token'] = 20, 
            ['Type'] = 5, 
            ['beShowProgress'] = true, 
        },
        [6225001] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_34983850824960'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16225001, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {7}, ['FuncName'] = 'EXPLORE_TYPE_COMPLETE', ['FuncParamInfos'] = {['targetId'] = 7, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'EXPLORE_TYPE_COMPLETE(7)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_552171734784'),
            ['Hide'] = false, 
            ['ID'] = 6225001, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_551903299328'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 602, 
            ['Token'] = 20, 
            ['Type'] = 6, 
            ['beShowProgress'] = true, 
        },
        [6225002] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_34983850824960'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16225002, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 1, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {5200002}, ['FuncName'] = 'INTERACT_CON_SUCCESS', ['FuncParamInfos'] = {['targetId'] = 5200002, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'INTERACT_CON_SUCCESS(5200002)>=1', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_552171735040'),
            ['Hide'] = false, 
            ['ID'] = 6225002, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_551903299584'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 601, 
            ['Token'] = 20, 
            ['Type'] = 6, 
            ['beShowProgress'] = true, 
        },
        [6225003] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_34983850824960'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 6590013, 
            ['ConditionAst'] = {}, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_552171735296'),
            ['Hide'] = false, 
            ['ID'] = 6225003, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_551903299840'),
            ['Priority'] = 4, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 601, 
            ['Token'] = 20, 
            ['Type'] = 6, 
            ['beShowProgress'] = true, 
        },
        [6226000] = {
            ['Achievement_Name'] = Game.TableDataManager:GetLangStr('str_620354340352'),
            ['Achievement_Picture'] = '/Game/Arts/UI_2/Resource/Achieve/Atlas/Texture01/UI_Achievement_Icon_Replace01.UI_Achievement_Icon_Replace01', 
            ['Condition'] = 16226000, 
            ['ConditionAst'] = {['ConditionCmp'] = '>=', ['ConditionCmpTarget'] = 2, ['ConditionFuncInfo'] = {['Caller'] = '', ['FuncArgInfos'] = {5}, ['FuncName'] = 'EQUIPPED_DANGER_SEALED_COUNT', ['FuncParamInfos'] = {['dangerLevel'] = 5, }, }, ['ConditionOp'] = 'None', ['IsFFunc'] = false, ['RawInput'] = 'EQUIPPED_DANGER_SEALED_COUNT(5)>=2', }, 
            ['ConditionNum'] = 1, 
            ['Describe'] = Game.TableDataManager:GetLangStr('str_620891211264'),
            ['Hide'] = false, 
            ['ID'] = 6226000, 
            ['IsImport'] = false, 
            ['Name'] = Game.TableDataManager:GetLangStr('str_620622775808'),
            ['Priority'] = 0, 
            ['Reward'] = {{2001000, 1000, 1}, }, 
            ['Sub_achievement_number'] = 0, 
            ['Subclass_ID'] = 701, 
            ['Token'] = 20, 
            ['Type'] = 7, 
            ['beShowProgress'] = true, 
        },
    }
}
return TopData