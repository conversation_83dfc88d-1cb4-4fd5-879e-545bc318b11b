--
-- 表名: $Task_任务.xlsx  页名：$TaskFilter_任务页签名
--

local TopData = {
	data = {
		[1] = {
			["ID"] = 1,
			["Name"] = Game.TableDataManager:GetLangStr('str_1100585371136'),
			["Icon"] = "/Game/Arts/UI_2/Resource/Common_2/Atlas/Sprite01/UI_Com_Icon_All_Sprite.UI_Com_Icon_All_Sprite",
			["Filter"] = {0, 1, 2, 4, 6, 7},
			["UnReceiveTaskMap"] = "",
			["InProgressTaskMap"] = "",
			["UnReceiveTask"] = "",
			["InProgressTask"] = "",
		},
		[2] = {
			["ID"] = 2,
			["Name"] = Game.TableDataManager:GetLangStr('str_57795227420160'),
			["Icon"] = "/Game/Arts/UI_2/Resource/Common_2/Atlas/Sprite01/UI_Com_Icon_Main_Sprite.UI_Com_Icon_Main_Sprite",
			["Filter"] = {0},
			["UnReceiveTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/UnReceive_Main.UnReceive_Main",
			["InProgressTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/InProgress_Main.InProgress_Main",
			["UnReceiveTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/UnReceive_Main.UnReceive_Main",
			["InProgressTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/InProgress_Main.InProgress_Main",
		},
		[3] = {
			["ID"] = 3,
			["Name"] = Game.TableDataManager:GetLangStr('str_138781132544'),
			["Icon"] = "/Game/Arts/UI_2/Resource/Common_2/Atlas/Sprite01/UI_Com_Icon_Branch_Sprite.UI_Com_Icon_Branch_Sprite",
			["Filter"] = {1},
			["UnReceiveTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/UnReceive_Branch.UnReceive_Branch",
			["InProgressTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/InProgress_Branch.InProgress_Branch",
			["UnReceiveTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/UnReceive_Branch.UnReceive_Branch",
			["InProgressTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/InProgress_Branch.InProgress_Branch",
		},
		[4] = {
			["ID"] = 4,
			["Name"] = Game.TableDataManager:GetLangStr('str_58207544304640'),
			["Icon"] = "/Game/Arts/UI_2/Resource/Common_2/Atlas/Sprite01/UI_Com_Icon_City_Sprite.UI_Com_Icon_City_Sprite",
			["Filter"] = {2},
			["UnReceiveTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/UnReceive_City.UnReceive_City",
			["InProgressTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/InProgress_City.InProgress_City",
			["UnReceiveTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/UnReceive_City.UnReceive_City",
			["InProgressTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/InProgress_City.InProgress_City",
		},
		[5] = {
			["ID"] = 5,
			["Name"] = Game.TableDataManager:GetLangStr('str_33673348910592'),
			["Icon"] = "/Game/Arts/UI_2/Resource/Common_2/Atlas/Sprite01/UI_Com_Icon_Daily_Sprite.UI_Com_Icon_Daily_Sprite",
			["Filter"] = {4, 6, 7},
			["UnReceiveTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/UnReceive_System.UnReceive_System",
			["InProgressTaskMap"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Map/MapTrace/InProgress_System.InProgress_System",
			["UnReceiveTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/UnReceive_System.UnReceive_System",
			["InProgressTask"] = "/Game/Arts/UI_2/Resource/ConfigIcon/Task/TaskTrace/InProgress_System.InProgress_System",
		},
	},
}

return TopData
