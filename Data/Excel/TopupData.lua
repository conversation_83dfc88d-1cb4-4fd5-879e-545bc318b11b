--
-- 表名: TopupData后处理
--

local TopData = {
    TopupData = {
        ['1981001'] = {['AndroidProductId'] = '1981001', ['DisplayNumber'] = 4, ['DisplayPrice'] = '198', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 1980, ['ID'] = 1981001, ['IosProductId'] = 'com.ios.gmzz.cz1981001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418304'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982848'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin04.UI_Recharge_Img_Coin04', ['TopupId'] = 2001003, ['TopupQuantity'] = 1980, }, 
        ['301001'] = {['AndroidProductId'] = '301001', ['DisplayNumber'] = 2, ['DisplayPrice'] = '30', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 300, ['ID'] = 301001, ['IosProductId'] = 'com.ios.gmzz.cz301001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417792'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982336'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin02.UI_Recharge_Img_Coin02', ['TopupId'] = 2001003, ['TopupQuantity'] = 300, }, 
        ['3281001'] = {['AndroidProductId'] = '3281001', ['DisplayNumber'] = 5, ['DisplayPrice'] = '328', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 3280, ['ID'] = 3281001, ['IosProductId'] = 'com.ios.gmzz.cz3281001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418560'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983104'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin05.UI_Recharge_Img_Coin05', ['TopupId'] = 2001003, ['TopupQuantity'] = 3280, }, 
        ['61001'] = {['AndroidProductId'] = '61001', ['DisplayNumber'] = 1, ['DisplayPrice'] = '6', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 60, ['ID'] = 61001, ['IosProductId'] = 'com.ios.gmzz.cz61001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417536'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982080'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin01.UI_Recharge_Img_Coin01', ['TopupId'] = 2001003, ['TopupQuantity'] = 60, }, 
        ['6481001'] = {['AndroidProductId'] = '6481001', ['DisplayNumber'] = 6, ['DisplayPrice'] = '648', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 6480, ['ID'] = 6481001, ['IosProductId'] = 'com.ios.gmzz.cz6481001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418816'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983360'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin06.UI_Recharge_Img_Coin06', ['TopupId'] = 2001003, ['TopupQuantity'] = 6480, }, 
        ['981001'] = {['AndroidProductId'] = '981001', ['DisplayNumber'] = 3, ['DisplayPrice'] = '98', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 980, ['ID'] = 981001, ['IosProductId'] = 'com.ios.gmzz.cz981001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418048'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982592'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin03.UI_Recharge_Img_Coin03', ['TopupId'] = 2001003, ['TopupQuantity'] = 980, }, 
        ['com.ios.gmzz.cz1981001'] = {['AndroidProductId'] = '1981001', ['DisplayNumber'] = 4, ['DisplayPrice'] = '198', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 1980, ['ID'] = 1981001, ['IosProductId'] = 'com.ios.gmzz.cz1981001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418304'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982848'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin04.UI_Recharge_Img_Coin04', ['TopupId'] = 2001003, ['TopupQuantity'] = 1980, }, 
        ['com.ios.gmzz.cz301001'] = {['AndroidProductId'] = '301001', ['DisplayNumber'] = 2, ['DisplayPrice'] = '30', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 300, ['ID'] = 301001, ['IosProductId'] = 'com.ios.gmzz.cz301001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417792'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982336'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin02.UI_Recharge_Img_Coin02', ['TopupId'] = 2001003, ['TopupQuantity'] = 300, }, 
        ['com.ios.gmzz.cz3281001'] = {['AndroidProductId'] = '3281001', ['DisplayNumber'] = 5, ['DisplayPrice'] = '328', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 3280, ['ID'] = 3281001, ['IosProductId'] = 'com.ios.gmzz.cz3281001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418560'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983104'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin05.UI_Recharge_Img_Coin05', ['TopupId'] = 2001003, ['TopupQuantity'] = 3280, }, 
        ['com.ios.gmzz.cz61001'] = {['AndroidProductId'] = '61001', ['DisplayNumber'] = 1, ['DisplayPrice'] = '6', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 60, ['ID'] = 61001, ['IosProductId'] = 'com.ios.gmzz.cz61001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417536'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982080'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin01.UI_Recharge_Img_Coin01', ['TopupId'] = 2001003, ['TopupQuantity'] = 60, }, 
        ['com.ios.gmzz.cz6481001'] = {['AndroidProductId'] = '6481001', ['DisplayNumber'] = 6, ['DisplayPrice'] = '648', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 6480, ['ID'] = 6481001, ['IosProductId'] = 'com.ios.gmzz.cz6481001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418816'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983360'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin06.UI_Recharge_Img_Coin06', ['TopupId'] = 2001003, ['TopupQuantity'] = 6480, }, 
        ['com.ios.gmzz.cz981001'] = {['AndroidProductId'] = '981001', ['DisplayNumber'] = 3, ['DisplayPrice'] = '98', ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),['FirstGiftId'] = 2001002, ['FirstGiftQuantity'] = 980, ['ID'] = 981001, ['IosProductId'] = 'com.ios.gmzz.cz981001', ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418048'),['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982592'),['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin03.UI_Recharge_Img_Coin03', ['TopupId'] = 2001003, ['TopupQuantity'] = 980, }, 
    },
    data = {
        [61001] = {
            ['AndroidProductId'] = '61001', 
            ['DisplayNumber'] = 1, 
            ['DisplayPrice'] = '6', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 60, 
            ['ID'] = 61001, 
            ['IosProductId'] = 'com.ios.gmzz.cz61001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417536'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982080'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin01.UI_Recharge_Img_Coin01', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 60, 
        },
        [301001] = {
            ['AndroidProductId'] = '301001', 
            ['DisplayNumber'] = 2, 
            ['DisplayPrice'] = '30', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 300, 
            ['ID'] = 301001, 
            ['IosProductId'] = 'com.ios.gmzz.cz301001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324417792'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982336'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin02.UI_Recharge_Img_Coin02', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 300, 
        },
        [981001] = {
            ['AndroidProductId'] = '981001', 
            ['DisplayNumber'] = 3, 
            ['DisplayPrice'] = '98', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 980, 
            ['ID'] = 981001, 
            ['IosProductId'] = 'com.ios.gmzz.cz981001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418048'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982592'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin03.UI_Recharge_Img_Coin03', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 980, 
        },
        [1981001] = {
            ['AndroidProductId'] = '1981001', 
            ['DisplayNumber'] = 4, 
            ['DisplayPrice'] = '198', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 1980, 
            ['ID'] = 1981001, 
            ['IosProductId'] = 'com.ios.gmzz.cz1981001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418304'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055982848'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin04.UI_Recharge_Img_Coin04', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 1980, 
        },
        [3281001] = {
            ['AndroidProductId'] = '3281001', 
            ['DisplayNumber'] = 5, 
            ['DisplayPrice'] = '328', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 3280, 
            ['ID'] = 3281001, 
            ['IosProductId'] = 'com.ios.gmzz.cz3281001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418560'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983104'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin05.UI_Recharge_Img_Coin05', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 3280, 
        },
        [6481001] = {
            ['AndroidProductId'] = '6481001', 
            ['DisplayNumber'] = 6, 
            ['DisplayPrice'] = '648', 
            ['DisplaySymbol'] = Game.TableDataManager:GetLangStr('str_59994250675712'),
            ['FirstGiftId'] = 2001002, 
            ['FirstGiftQuantity'] = 6480, 
            ['ID'] = 6481001, 
            ['IosProductId'] = 'com.ios.gmzz.cz6481001', 
            ['ProductDes'] = Game.TableDataManager:GetLangStr('str_59995324418816'),
            ['ProductName'] = Game.TableDataManager:GetLangStr('str_59995055983360'),
            ['TopupIconPath'] = '/Game/Arts/UI_2/Resource/Recharge/NotAtlas/UI_Recharge_Img_Coin06.UI_Recharge_Img_Coin06', 
            ['TopupId'] = 2001003, 
            ['TopupQuantity'] = 6480, 
        },
    }
}
return TopData